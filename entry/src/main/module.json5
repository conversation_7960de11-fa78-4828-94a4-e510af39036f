{
  "module": {
    "name": "entry",
    "type": "entry",
    "description": "$string:module_desc",
    "mainElement": "EntryAbility",
    "deviceTypes": [
      "phone"
    ],
    "deliveryWithInstall": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "EntryAbility",
        "srcEntry": "./ets/entryability/EntryAbility.ets",
        "description": "$string:EntryAbility_desc",
        "icon": "$media:layered_image",
        "label": "$string:EntryAbility_label",
        "startWindowIcon": "$media:startIcon",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          {
            "entities": [
              "entity.system.home",
            ],
            "actions": [
              "action.system.home",
              "wxentity.action.open"
            ]
          },
          {
            "actions": [
              "action.jjcf"
            ],
            "uris": [
              {
                // scheme必选，可以自定义，以link为例，需要替换为实际的scheme
                "scheme": "jjcf",
                // host必选，配置待匹配的域名
                "host": "",
              },
              {
                // scheme必选，可以自定义，以link为例，需要替换为实际的scheme
                "scheme": "https://tfrp6.share2dlink.com/",
                // host必选，配置待匹配的域名
                "host": "https://tfrp6.share2dlink.com/",
              },
            ]
          }
        ]
      }
    ],
    "querySchemes": [
      'thinkiveOpen', // 思迪开户demo
      'psbcmbank', // 邮政储蓄银行
      'com.icbc.iphoneclient', // 中国工商银行
      'bankabc', // 中国农业银行
      'BOCMBCIphone', // 中国银行
      'ccbmobilebank', // 中国建设银行
      'bocom', // 中国交通银行
      'citic', // 中信银行
      'com.cebbank.ebank', // 光大银行
      'com.hx.hxbank', // 华夏银行
      'com.cmbc.cn.iphone', // 民生银行
      'cgb', // 广东发展银行
      'cmbmobilebank', // 招商银行
      'cibmb', // 兴业银行
      'spdbbank', // 浦发银行
      'paebqw', // 平安银行
      'wxcc0b1c78c5bebdb5', // 渤海银行
      'jshbank', // 晋商银行
      'comeToLiaoNing', // 山西省农村信用社联合社
      "https",
      "qqopenapi",
      "weixin",
      "wxopensdk"
    ],
//    "extensionAbilities": [
//      {
//        "name": "EntryBackupAbility",
//        "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets",
//        "type": "backup",
//        "exported": false,
//        "metadata": [
//          {
//            "name": "ohos.extension.backup",
//            "resource": "$profile:backup_config"
//          }
//        ],
//      }
//    ],
    "requestPermissions": [
      {
        // 允许订阅应用网络信息
        "name": "ohos.permission.GET_NETWORK_INFO",
      },
      {
        // 网络访问
        "name": "ohos.permission.INTERNET"
      },
      {
        //WIF信息权限
        "name": "ohos.permission.GET_WIFI_INFO"
      },
      {
        //允许应用存储持久化的数据，该数据直到设备恢复出厂设置或重装系统才会被清除
        "name": "ohos.permission.STORE_PERSISTENT_DATA"
      },
      {
        //允许应用将窗口设置为隐私窗口，禁止截屏录屏
        "name": "ohos.permission.PRIVACY_WINDOW",
      },
      {
        "name": "ohos.permission.CAMERA",
        // 相机
        "reason": "$string:ENTRYABILITY_CAMERA_REQUEST_REASON",
        "usedScene": {
          "abilities": [
            "TKOpenAbility"
          ],
          "when": "inuse"
        }
      },
      {
        "name": "ohos.permission.ACCESS_BIOMETRIC",
        // 生物识别
        "reason": "$string:BIOMETRIC_REQUEST_REASON",
        "usedScene": {
          "abilities": [
            "EntryAbility"
          ],
          "when": "inuse"
        }
      },
    ]
  }
}