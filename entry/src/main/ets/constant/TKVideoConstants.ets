/**
 * 视频见证相关常量定义
 */

/**
 * 视频见证通过消息体
 * 系统消息码:10000
 */
export const WITNESS_SUCCESS: string = "SYS:10000";

/**
 * 坐席端浏览器关闭刷新
 * 系统消息码:10001
 */
export const SEAT_REFRESH_IE: string = "SYS:10001";

/**
 * 客户端主动退出
 * 系统消息码:10002
 */
export const USER_LEAVE_ROOM: string = "SYS:10002";

/**
 * 驳回原因相关常量
 */

/**
 * 没有公安头像
 * 系统消息码:20001
 */
export const REJECT_REASON1: string = "SYS:20001";

/**
 * 身份证正面不合规
 * 系统消息码:20002
 */
export const REJECT_REASON2: string = "SYS:20002";

/**
 * 身份证反面不合规
 * 系统消息码:20003
 */
export const REJECT_REASON3: string = "SYS:20003";

/**
 * 身份证正反面都不合规
 * 系统消息码:20004
 */
export const REJECT_REASON4: string = "SYS:20004";

/**
 * 客户不是本人
 * 系统消息码:20005
 */
export const REJECT_REASON5: string = "SYS:20005";

/**
 * 客户环境不符合要求
 * 系统消息码:20006
 */
export const REJECT_REASON6: string = "SYS:20006";

/**
 * 驳回重新排队
 * 系统消息码:30001
 */
export const REJECT_QUEUE: string = "SYS:30001";

/**
 * 视频消息相关常量
 */

/**
 * 普通文本消息
 * 用户消息码:0
 */
export const VIDEO_TEXT_MSG: string = "USR:0:";

/**
 * 富文本显示，支持HTML
 * 用户消息码:1000
 */
export const VIDEO_CMD_MSG_RTF: string = "USR:1000:";

/**
 * 显示提示框
 * 用户消息码:1001
 */
export const VIDEO_CMD_MSG_NOTICE_DIALOG: string = "USR:1001:";

/**
 * 视频指令消息
 * 用户消息码:1002
 */
export const VIDEO_CMD_MSG_TOAST: string = "USR:1002:";

/**
 * 确认信息弹窗
 * 用户消息码:1006
 */
export const VIDEO_CMD_MSG_CONFIRM_DIALOG: string = "USR:1006:";

/**
 * 阅读协议弹窗
 * 用户消息码:1007
 */
export const VIDEO_CMD_MSG_READ_DIALOG: string = "USR:1007:";

/**
 * 确认信息弹窗回调
 * 返回消息码:1006
 */
export const VIDEO_RET_MSG_CONFIRM_DIALOG: string = "RET:1006:";

/**
 * 阅读协议弹窗回调
 * 返回消息码:1007
 */
export const VIDEO_RET_MSG_READ_DIALOG: string = "RET:1007:";

/**
 * 视频结果相关常量
 */

/**
 * 用户主动退出视频见证
 * 应用消息码:10001
 */
export const VIDEO_RES_USER_EXIT_ROOM: string = "app:10001";

/**
 * 用户连接视频失败(连接、登录、进入房间)
 * 应用消息码:10002
 */
export const VIDEO_RES_USER_CON_FAIL: string = "app:10002";

/**
 * 坐席连接视频失败(等待20秒超时)
 * 应用消息码:10003
 */
export const VIDEO_RES_STAFF_CON_FAIL: string = "app:10003";

/**
 * 坐席主动退出视频见证
 * 应用消息码:10004
 */
export const VIDEO_RES_STAFF_EXIT_ROOM: string = "app:10004";

/**
 * 错误码相关常量
 */

/**
 * 用户主动退出
 * 错误码:-1
 */
export const ERROR_CODE_USER_CANCEL: number = -1;

/**
 * 排队接口错误
 * 错误码:-2
 */
export const ERROR_CODE_QUEUE_ERROR: number = -2;

/**
 * 连接视频服务器失败
 * 错误码:-3
 */
export const ERROR_CODE_CONNECT_SERVER_ERROR: number = -3;

/**
 * 进入房间失败
 * 错误码:-4
 */
export const ERROR_CODE_ENTERROOM_ERROR: number = -4;

/**
 * 客服连接视频超时
 * 错误码:-5
 */
export const ERROR_CODE_SERVICE_ENTERROOM_TIMEOUT: number = -5;

/**
 * 客户连接异常
 * 错误码:-6
 */
export const ERROR_CODE_LOCAL_CONNECT_ERROR: number = -6;

/**
 * 坐席连接异常
 * 错误码:-7
 */
export const ERROR_CODE_SERVICE_CONNECT_ERROR: number = -7;

/**
 * 网络异常
 * 错误码:-9
 */
export const ERROR_CODE_NETWORK_ERROR: number = -9;

/**
 * 无坐席
 * 错误码:-10
 */
export const ERROR_CODE_NO_STAFF_ONLINE: number = -10;

/**
 * 排队人数超限客户选择走单向
 * 错误码:-11
 */
export const ERROR_CODE_QUEUE_LIMIT: number = -11;

/**
 * 未登录
 * 错误码:-999
 */
export const ERROR_CODE_NOT_LOGIN: number = -999;
