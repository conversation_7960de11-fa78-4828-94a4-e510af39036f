/**
 * 开户模块常量定义
 */
export namespace TKOpenConstant {

  /** SDK中断通知 */
  export const TK_INTERRUPTSDK_NOTIFICATION = 'tkInterruptSDKNotification'
  /** 开户模块名称 */
  export const TK_OPEN_MODULE_NAME = 'open'
  /** 开户模块退出处理 */
  export const TK_OPEN_HANDLE_MODULE = 'exit_open'
  /** H5刷新通知 */
  export const TK_OPEN_REFRESH_H5 = 'refreash_h5'
  /** 非现场账户信息 */
  export const TK_FXC_ACCOUNT_INFO = 'fxcAccountInfo'
  /** 单向视频信息 */
  export const TK_ONE_VIDEO_INFO = 'oneVideoInfo'
  /** 窗口隐私模式通知 */
  export const TK_WINDOW_PRIVACY_MODE_NOTIFICATION = 'tkWindowPrivacyModeNotification'
  /** 开户模块退出通知 */
  export const TK_OPEN_EXIT_NOTIFICATION = 'tkOpenExitNotification'

  // 系统主题相关常量
  /** 系统亮度模式变化通知H5功能号 */
  export const TK_SYSTEM_COLOR_MODE_FUNC_NO = '50126'
  /** 浅色主题配置名称 */
  export const TK_LIGHT_THEME_NAME = 'theme1'
  /** 深色主题配置名称 */
  export const TK_DARK_THEME_NAME = 'theme2'

  // 中泰定制需求
  /** 中泰用户登录通知 */
  export const TK_TZT_USER_LOGIN_NOTIFICATION = 'TZTNotifi_UserLogin'
  /** 开户模块未登录通知 */
  export const TK_OPEN_NOT_LOGIN_NOTIFICATION = 'tkOpenNotLoginNotification'
}

/**
 * 导出开户模块常量
 */
export default TKOpenConstant