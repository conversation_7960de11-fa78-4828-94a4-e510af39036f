import {
  TKBarName,
  TKBasePlugin,
  TKDialogHelper,
  TKLog,
  TKNoPermissionTip,
  TKNormalDialogOption,
  TKPermission,
  TKPermissionHelper, TKResultVO, TKRouterHelper } from '@thinkive/tk-harmony-base';
import { abilityAccessCtrl, common } from '@kit.AbilityKit';
import { promptAction } from '@kit.ArkUI';
import ClickUtils from '../../utils/TKClickUtils';

import('./pages/TKOpenBankCardScannerPage') // 引入共享包中的命名路由页面

/**
 * 插件: 银行卡扫描
 * 用于扫描银行卡并识别卡号等信息
 */
export class TKPlugin60304 extends TKBasePlugin {
  /**
   * 插件调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info("[思迪60304插件]:开始调用银行卡扫描插件")
    let resultVO: TKResultVO = new TKResultVO();

    // 防止短时间内重复调用
    if (ClickUtils.clickTooFast('60304', 2000)) {
      TKLog.info("[思迪60304插件]:重复调用")
      resultVO.errorNo = 1;
      resultVO.errorInfo = "重复调用,请稍后再试"
      return resultVO;
    }

    // 请求相机权限
    TKPermissionHelper.requestPermissionFromUser(
      TKPermission.CAMERA,
      getContext(this) as common.UIAbilityContext)
      .then((grantStatus) => {
        TKLog.debug(`[思迪60304插件]:相机权限状态:${grantStatus}`)
        for (let i = 0; i < 2; i++) {
          if (i == 0 && grantStatus != abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
            TKLog.warn("[思迪60304插件]:未获得相机权限")
            // promptAction.showToast({message:TKNoPermissionTip.CAMERA_TIP})
            TKDialogHelper.showAlertDialog({
              title: "温馨提示",
              message: TKNoPermissionTip.CAMERA_TIP,
              confirmText: "去授权",
              cancelText: "暂不授权",
              confirm: () => {
                TKPermissionHelper.toAppSetting();
              }
            } as TKNormalDialogOption);
            return
          }
          continue;
        }
        TKLog.info("[思迪60304插件]:已获得相机权限,开始跳转扫描页面")
        this.goPick(param)
      })

    return resultVO
  }

  /**
   * 跳转到银行卡扫描页面
   * @param content 页面参数
   */
  goPick(content: Record<string, Object>) {
    TKLog.debug(`[思迪60304插件]:跳转参数:${JSON.stringify(content)}`)

    TKRouterHelper.pushNamedRoute({
      name: 'TKOpenBankCardScannerPage',
      params: content,
      pageStyle: {
        layoutFullScreen: true,
        systemBarProperties: {
          statusBarContentColor: '#ffffff',
          statusBarColor: '#********'
        },
        systemBarEnable:  [TKBarName.status, TKBarName.navigationIndicator],
      },
      onResult: (data) => {
        data.funcNo = '60305'
        data.errorNo = data.error_no  // 兼容部分项目
        data.errorInfo = data.error_info // 兼容部分项目

        // 晋金财富定制逻辑
        if (content.paramExt) data.paramExt = content.paramExt
        if (data.bankOriginalImage) data.base64 = data.bankOriginalImage

        TKLog.debug(`[思迪60304插件]:扫描结果:${JSON.stringify(data)}`)
        this.harmonyCallPluginCallBack(data)
      },
      navComponentOrPathStack: this.navComponentOrPathStack
    })
  }
}