import router from '@ohos.router';
import { BusinessError, Callback } from '@ohos.base';
import { TKFileUtils } from '../../../utils/TKFileUtils';
import { TKBankCardScanModel } from '../vos/TKBankCardScanModel';
import {
  T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON>,
  TKPhotoSelectOptions,
  T<PERSON><PERSON><PERSON><PERSON>elper,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TKWindowHelper } from '@thinkive/tk-harmony-base'
import { CameraPosition, CameraPreviewSize } from '../../../components/camera/common/constants/TKCameraConstants';
import TKCameraService from '../../../components/camera/TKCameraService';
import TKImageUtil from '../../../utils/TKImageUtil';
import TKStringUtil from '../../../utils/TKStringUtil';
import { display } from '@kit.ArkUI';
import ClickUtils from '../../../utils/TKClickUtils';
import { BankCardScanner, RecognizeResult } from '../services/BankCardScanner';
import { TKBankCardScannerManger } from '../services/impl/TKBankCardScannerManger';
import { photoAccessHelper } from '@kit.MediaLibraryKit';
import { TKPhotoPreviewAttribute } from '../../../pages/common/TKPhotoPreviewAttribute';
import { TKCommonDialogAttribute } from '../../../components/TKCommonDialogAttribute';
import { drawing } from '@kit.ArkGraphics2D';
import { TKFoldManager } from '../../../utils/TKFoldManager';

@Entry({ routeName: 'TKOpenBankCardScannerPage' })
@Component
struct TKOpenBankCardScannerPage {

  // 是否显示相册按钮
  @State isAlbum: boolean = false;
  // 是否显示拍照按钮
  @State isTake: boolean = false;
  // 取景框图片资源
  @State cardBoxImg?: Resource = $r("app.media.tk_open_plugin_60037_bank_photograph");
  // 摄像头分辨率
  @State cameraPreviewSize: CameraPreviewSize = { width: 1280, height: 960 }
  // 示例图片资源
  @State sampleImg: Resource = $r("app.media.tk_open_plugin_common_front_example_icon");
  // 状态提示文本
  @State statusResource?: string | null = null;
  // 折叠屏展开边距
  @State isFoldSpace: number = 0
  // 是否是相册模式
  @State isSelectPhotoAction: boolean = false
  // 是否是拍照模式
  @State isTakeModule: boolean = false
  // 扫码线移动位置
  @State scanLineTranslateX: number = 0
  // 回调结果Map
  callbackResult = new Map<string, string>()
  // XComponent控制器
  mXComponentController: XComponentController = new XComponentController;
  // 页面参数
  params: TKBankCardScanModel = new TKBankCardScanModel
  // 页面旋转角度（折叠屏时不做横屏）
  @State pageAngle: number = 90

  // 样式属性
  @State styleAttribute: TKPhotoPreviewAttribute = new TKPhotoPreviewAttribute();

  // 窗口宽度
  @State windowWidth: number = 0
  // 窗口高度  
  @State windowHeight: number = 0
  // 银行卡扫描器实例
  private idCardScanner: BankCardScanner | undefined;
  // 是否开启闪光灯
  @State cameraService: TKCameraService = TKCameraService.getInstance()

  build() {
    Stack() {
      // 相机预览视图
      PreviewView({ mXComponentController: this.mXComponentController, cameraPreviewSize: this.cameraPreviewSize })
        .aspectRatio(this.cameraPreviewSize.height / this.cameraPreviewSize.width)
        .height('100%')// 根据父控件宽高比进行裁剪
        .visibility(this.idCardScanner?.isUseSDKCramera() ? Visibility.None : Visibility.Visible)

      Row() {
        //页面左半边区域（包括返回和相册）
        Column() {
          // 闪光灯
          Column() {
            Button() {
              Image($r(this.cameraService.getIsFlash() ? "app.media.tk_JJCF_scan_flash_cloae_icon" : "app.media.tk_JJCF_scan_flash_icon"))
                .padding(11)
                .objectFit(ImageFit.Contain)
                .rotate({
                  angle: 90
                })
            }
            .width(48)
            .height(48)
            .backgroundColor('#00000000')
            .margin({ top: 10 })
            .onClick(() => {
              TKLog.debug('[思迪身份证OCR页面]:切换闪光灯')
              this.cameraService.handleFlash()
            })
            .visibility(this.isTake ? Visibility.Visible : Visibility.Hidden)
          }
        }
        .backgroundColor(0x99000000)
        .width(this.pageAngle == 90 ?'20%':'17%')
        .height('100%')
        .padding({ top: this.isFoldSpace, bottom: this.isFoldSpace })
        .justifyContent(FlexAlign.SpaceBetween)

        //页面中间区域（包括取景框和文案）
        Column() {
          RelativeContainer() {
            Text() {
              Span('请勿抖动')
                .fontColor($r('sys.color.white'))
            }
            .fontSize(14)
            .fontFamily('PingFang SC')
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .height(30 + this.isFoldSpace)
            .padding({ top: this.isFoldSpace })
            .textAlign(TextAlign.Center)
            .backgroundColor(0x99000000)
            .alignRules({
              'top': { 'anchor': '__container__', 'align': VerticalAlign.Top },
              'left': { 'anchor': '__container__', 'align': HorizontalAlign.Start }
            })
            .width('100%')
            .id("id_take_photo_top")

            Image(this.cardBoxImg)
              .width('100%')
              .objectFit(ImageFit.Fill)
              .backgroundColor('#0000')
              .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB("#FFFFFF"), drawing.BlendMode.SRC_IN))
              .alignRules({
                'top': { 'anchor': 'id_take_photo_top', 'align': VerticalAlign.Bottom },
                'bottom': { 'anchor': 'id_take_photo_bottom', 'align': VerticalAlign.Top }
              })
              .id("id_take_photo_middle")
              .width('100%')

            Column()
              .alignRules({
                'bottom': { 'anchor': '__container__', 'align': VerticalAlign.Bottom },
                'left': { 'anchor': 'id_take_photo_top', 'align': HorizontalAlign.Start }
              })
              .width('100%')
              .height(30 + this.isFoldSpace)
              .padding({ bottom: this.isFoldSpace })
              .backgroundColor(0x99000000)
              .id("id_take_photo_bottom")

            /*扫描线*/
            Image($r("app.media.tk_open_plugin_60014_scan_line"))
              .translate({ x: this.scanLineTranslateX })
              .alignRules({
                'top': { 'anchor': 'id_take_photo_top', 'align': VerticalAlign.Bottom },
                'bottom': { 'anchor': 'id_take_photo_bottom', 'align': VerticalAlign.Top }
              })
              .id("id_take_photo_scan_line")
              .animation({
                duration: 2000,
                curve: Curve.Linear,
                delay: 0,
                iterations: -1, // 设置-1表示动画无限循环
                playMode: PlayMode.Normal
              })
              .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB(this.styleAttribute.fontColor ?? "#2F85FF"), drawing.BlendMode.SRC_IN))
              .visibility(this.isTakeModule ? Visibility.None : Visibility.Visible)

            Text(this.statusResource)
              .visibility(this.statusResource ? Visibility.Visible : Visibility.Hidden)
              .fontColor($r('sys.color.white'))
              .padding({ left: 15, top: 10, right: 15, bottom: 10})
              .fontSize(18)
              .textAlign(TextAlign.Center)
              .backgroundColor('rgba(253, 77, 67, 0.9)')
              .borderRadius(5)
              .alignRules({
                'center': { 'anchor': '__container__', 'align': VerticalAlign.Center },
                'middle': { 'anchor': '__container__', 'align': HorizontalAlign.Center },
              })
              .id("id_take_photo_type_error_tip")

          }.width('100%')
        }
        .height('100%')
        .width(this.pageAngle == 90 ?'60%':'66%')
        .alignItems(HorizontalAlign.Center)
        .justifyContent(FlexAlign.SpaceBetween)

        //页面右边区域（包括拍照按钮）
        Column() {

          // 返回按钮区域
          Column() {
            Button() {
              // 返回按钮图标
              Image($r("app.media.tk_JJCF_scan_white_back_icon"))
                .padding(12)
                .objectFit(ImageFit.Contain)
            }
            .width(48)
            .height(48)
            .backgroundColor('#00000000')
            .margin({ top: 10 })
            .onClick(() => {
              TKLog.debug('[思迪身份证OCR页面]:点击返回按钮')
              this.backPageAction()
            })

          }.width('100%')
        }
        .backgroundColor(0x99000000)
        .width(this.pageAngle == 90 ?'20%':'17%')
        .padding({ top: this.isFoldSpace, bottom: this.isFoldSpace })
        .height('100%')
        .justifyContent(FlexAlign.SpaceBetween)

      }
      .height(this.pageAngle == 90 ? this.windowWidth : '100%')
      .rotate({angle:this.pageAngle})
      .width(this.pageAngle == 90 ? this.windowHeight:  '100%')
    }
  }

  // 页面即将显示时调用
  aboutToAppear() {
    TKLog.info(`[思迪银行卡OCR页面]:aboutToAppear`)
    
    if (TKRouterHelper.getParams(this)) {
      this.params = TKRouterHelper.getParams(this) as Object as TKBankCardScanModel; // 获取传递过来的参数对象
    }

    if (this.params.isAlbum) {
      this.isAlbum = this.params.isAlbum == "1"
    }

    if (this.params.isTake) {
      this.isTake = this.params.isTake == "1"
    }

    if (this.params.mainColor) {
      this.styleAttribute.fontColor = this.params.mainColor
    }

    this.idCardScanner =  TKBankCardScannerManger.getCardScanner("")
    this.idCardScanner.setListener({
      onInit: () => {
        TKLog.info(`[思迪银行卡OCR页面]:扫描器初始化完成`)
        this.idCardScanner?.start()
      },
      onFailed: (errorNo: number, errorInfo: string) => {
        TKLog.error(`[思迪银行卡OCR页面]:扫描失败: errorNo = ${errorNo}, errorInfo = ${errorInfo}`)
        this.postWitnessResultToH5(errorNo + '', errorInfo)
      },
      onCompleted: (result: RecognizeResult) => {
        TKLog.info(`[思迪银行卡OCR页面]:扫描完成: ${JSON.stringify(result)}`)
        this.returnToH5(result)
      },
      onScannerTips: (errorInfo: string) => {
        TKLog.info(`[思迪银行卡OCR页面]:扫描提示: ${errorInfo}`)
        this.statusResource = errorInfo
        setTimeout(() => {
          this.statusResource = undefined
        }, 1500)
      }
    })

    this.idCardScanner.init(getContext(this))

    this.setWindowOrientation(false)
    //折叠屏适配
    this.initFold()
  }

  // 初始化折叠屏相关配置
  initFold() {
    TKLog.info(`[思迪银行卡OCR页面]:初始化折叠屏配置`)
    if (!display.isFoldable()) {
      return
    }
    //如果是折叠屏手机
    if (display.getFoldDisplayMode() == display.FoldDisplayMode.FOLD_DISPLAY_MODE_FULL) {
      this.cameraPreviewSize = { width: 1088, height: 1088 }
      let displayClass: display.Display = display.getDefaultDisplaySync()
      this.isFoldSpace = px2vp(displayClass.width / 4 + (displayClass.height - displayClass.width) / 2)
      this.pageAngle = 0
    }
    let that = this;
    TKFoldManager.getInstance().setListener({
      foldDisplayMain(): void {
        TKLog.info(`[思迪银行卡OCR页面]:折叠屏-折叠显示`)
        that.isFoldSpace = 0
        that.cameraPreviewSize = { width: 1280, height: 960 }
        that.pageAngle = 90
        that.onPageShow()
      },
      foldDisplayFull(): void {
        TKLog.info(`[思迪银行卡OCR页面]:折叠屏-非折叠显示`)
        let displayClass: display.Display = display.getDefaultDisplaySync()
        that.isFoldSpace = px2vp(displayClass.width / 4 + (displayClass.height - displayClass.width) / 2)
        that.cameraPreviewSize = { width: 1088, height: 1088 }
        that.pageAngle = 0
        that.onPageShow()
      },
    })
  }

  /**
   * 直接返回图片给H5
   */
  async returnToH5(result: RecognizeResult) {
    TKLog.info(`[思迪银行卡OCR页面]:返回识别结果给H5`)
    if (result.cardNumberPath && result.originalImagePath) {
      this.idCardScanner?.stop();
      try {
        const cardNumberContent = await TKFileUtils.readContentUint8Array(result.cardNumberPath);
        const bankImage = cardNumberContent ? TKStringUtil.uint8ArrayToBase64(cardNumberContent) : "";

        const originalImageContent = await TKFileUtils.readContentUint8Array(result.originalImagePath);
        const originalImage = originalImageContent ? TKStringUtil.uint8ArrayToBase64(originalImageContent) : "";

        let cardNumber = result.cardNumber ?? ''
        cardNumber = TKStringHelper.replace(cardNumber, ' ', '') // 去掉空格
        this.callbackResult.set("cardNumber", cardNumber); //银行卡号

        this.callbackResult.set("bankID", result.bankID ?? ''); //银行标识ID
        this.callbackResult.set("bankName", result.bankName ?? ''); //银行名称
        this.callbackResult.set("cardType", result.cardType ?? ''); //卡类型
        this.callbackResult.set("validDate", result.validDate ?? ''); //有效期

        this.callbackResult.set("bankOriginalImage", originalImage ? 'data:image/jpg;base64,' + originalImage : ''); //拍照或者相册的银行卡原图
        this.callbackResult.set("bankImage", bankImage ? 'data:image/jpg;base64,' + bankImage : ''); //银行卡号
        this.callbackResult.set("error_no", "0");
        TKRouterHelper.back({result: TKDataHelper.mapToJson(this.callbackResult), navComponentOrPathStack: this})
      } catch (e) {
        TKLog.error(`[思迪银行卡OCR页面]:处理识别结果异常: ${e}`)
        e.printStackTrace();
        TKRouterHelper.back({navComponentOrPathStack: this})
      }
    }
  }

  // 页面显示时调用
  onPageShow() {
    TKLog.info(`[思迪银行卡OCR页面]:onPageShow`)
    let displayClass: display.Display = display.getDefaultDisplaySync()
    this.windowWidth = px2vp(displayClass.width)
    this.windowHeight = px2vp(displayClass.height)
    this.scanLineTranslateX = this.windowHeight * 0.6 - 12
    TKLog.debug(`[思迪银行卡OCR页面]:窗口尺寸: width=${this.windowWidth}, height=${this.windowHeight}`)

    if (!this.idCardScanner?.isUseSDKCramera()) {
      let that = this;
      setTimeout(() => {
        this.cameraService
          .init(getContext(), this.mXComponentController.getXComponentSurfaceId(), CameraPosition.BACK,
            this.cameraPreviewSize);

        this.cameraService.addLifecycleListener({
          onPreviewFrame(byteBuffer: ArrayBuffer): void {
            TKLog.debug(`[思迪银行卡OCR页面]:处理预览帧`)
            try {
              that.idCardScanner?.inputData(byteBuffer, that.cameraPreviewSize.width, that.cameraPreviewSize.height)
              TKLog.debug(`[思迪银行卡OCR页面]:预览帧处理完成`)
            } catch (exception) {
              TKLog.error(`[思迪银行卡OCR页面]:预览帧处理异常: ${exception}`)
            }
          }
        });
        //设置检测区域
        that.idCardScanner?.setDetectorRect(that.cameraPreviewSize);
      }, 500)
    }
  }

  // 页面隐藏时调用
  onPageHide(): void {
    TKLog.info(`[思迪银行卡OCR页面]:onPageHide`)
    this.cameraService.destroy()
  }

  // 页面即将销毁时调用
  aboutToDisappear(): void {
    TKLog.info(`[思迪银行卡OCR页面]:aboutToDisappear`)
    this.idCardScanner?.release()
    this.idCardScanner = undefined
    TKFoldManager.release()
    this.setWindowOrientation(true)
  }

  // 返回按钮点击事件
  backPageAction() {
    TKLog.info(`[思迪银行卡OCR页面]:点击返回按钮`)
    this.idCardScanner?.stop()
    this.postWitnessResultToH5("-1", "用户取消")
  }

  /**
   * 相册选择
   */
  openPhotoAlbumAction() {
    TKLog.info(`[思迪银行卡OCR页面]:打开相册`)

    let recommendationType:photoAccessHelper.RecommendationType = photoAccessHelper.RecommendationType.BANK_CARD
    let recommendOptions: photoAccessHelper.RecommendationOptions = {
      recommendationType: recommendationType
    }
    let options: TKPhotoSelectOptions = {
      // MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE,  //可选择的媒体文件类型，若无此参数，则默认为图片和视频类型。
      maxSelectNumber: 1,
      // isPhotoTakingSupported = true, //支持拍照。
      // isEditSupported = true, //支持编辑照片。
      // isSearchSupported = true, //支持搜索。
      recommendationOptions: recommendOptions,
      // preselectedUris?: Array<string>; //预选择图片的uri数据。
    }
    TKPickerHelper.selectPhoto(options).then(async (uris) => {
      if (uris.length > 0) {
        let uri = uris[0]
        //先压缩分辨率在压缩质量
        let pixelMap = await TKImageUtil.createPixelMapByUri(uri)
        if (pixelMap) {
          TKLog.info(`[思迪银行卡OCR页面]:识别相册图片`)
          this.idCardScanner?.recognizePixelMap(pixelMap)
        }
      }
    })
  }

  /**
   * 拍照识别
   */
  takePhotoAction() {
    TKLog.info(`[思迪银行卡OCR页面]:拍照识别`)
    try {
      if (ClickUtils.clickTooFast('takeAction', 2000)) {
        return;
      }
      //先停止扫描
      this.idCardScanner?.stop();
      this.idCardScanner?.takePhone(this.mXComponentController.getXComponentSurfaceId(),this.pageAngle)
    } catch (err) {
      TKLog.error(`[思迪银行卡OCR页面]:拍照失败: ${err.message}`)
      TKLog.error(`Failed to create pixelMap.code is ${err.code},message is ${err.message}`);
    }
  }

  // 页面转场动画
  pageTransition() {
    PageTransitionEnter({ type: RouteType.None, duration: 0 })
    PageTransitionExit({ type: RouteType.None, duration: 0 })
  }

  // 返回键处理
  onBackPress(): boolean | void {
    this.backPageAction()
    return true
  }

  // 向H5发送结果
  postWitnessResultToH5(error_no: string, error_info: string) {
    let backResult = new Map<string, string>()
    if (error_no) {
      backResult.set("error_no", error_no);
    }
    if (error_info) {
      backResult.set("error_info", error_info);
    }
    TKRouterHelper.back({result: TKDataHelper.mapToJson(backResult), navComponentOrPathStack: this})
    TKLog.info(`[思迪银行卡OCR页面]:回调结果: ${JSON.stringify(Array.from(backResult))}`);
  }

  // 上一个窗口是否全屏
  isPreWinFull?: boolean = undefined

  /**
   * 设置屏幕方向
   * @param isPortrait 是否竖屏
   */
  setWindowOrientation(isPortrait: boolean) {
    TKLog.info(`[思迪银行卡OCR页面]:设置${isPortrait ? '竖屏' : '横屏'}`)
    let that = this

    TKWindowHelper.getLastWindow(getContext(this)).then((win) => {
      if (that.isPreWinFull == undefined) {
        that.isPreWinFull = win.getWindowProperties().isLayoutFullScreen
      }

      if (that.isPreWinFull) {
        return
      }
    }).catch((error: BusinessError) => {
      TKLog.error(`[思迪银行卡OCR页面]:设置屏幕方向失败: ${error}`)
    });
  }
}

// 预览视图组件
@Component
struct PreviewView {
  // XComponent控制器
  public mXComponentController?: XComponentController;
  // 相机预览尺寸
  public cameraPreviewSize?: CameraPreviewSize
  // 是否已加载
  public isLoad: boolean = false;

  build() {
    // 创建XComponent
    XComponent({
      id: '',
      type: 'surface',
      controller: this.mXComponentController
    })
      .onLoad(() => {
        if (!this.isLoad) {
          this.isLoad = true
          // 设置Surface宽高，预览尺寸设置参考前面 previewProfilesArray 获取的当前设备所支持的预览分辨率大小去设置
          // 预览流与录像输出流的分辨率的宽高比要保持一致
          TKLog.debug(`[思迪银行卡OCR页面]:XComponent加载完成`)
          // 设置Surface宽高
          this.mXComponentController?.setXComponentSurfaceSize({
            surfaceWidth: this.cameraPreviewSize?.width,
            surfaceHeight: this.cameraPreviewSize?.height
          });
        }
      }).onDestroy(() => {
      TKLog.debug(`[思迪银行卡OCR页面]:XComponent销毁`)
    })
  }
}