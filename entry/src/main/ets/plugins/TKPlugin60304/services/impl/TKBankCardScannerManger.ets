import { BankCardScanner } from '../BankCardScanner';
import TKJJCFIntsigBankCardScanner from './TKJJCFIntsigBankCardScanner';

/**
 * 银行卡扫描管理类
 * 用于获取不同类型的银行卡扫描器实例
 */
export class TKBankCardScannerManger {

  /**
   * 获取银行卡扫描器实例
   * @param scannerType 扫描器类型,目前仅支持银行卡扫描
   * @returns BankCardScanner 扫描器实例
   */
  static getCardScanner(scannerType: string): BankCardScanner {
    
    // 合合银行卡扫描器
    return new TKJJCFIntsigBankCardScanner();
  }
}
