
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TKWindowHelper } from '@thinkive/tk-harmony-base';
import { ItemListBean, RecognizeResultData, RecognizerType, VpuMoreCardPicPreKV } from 'hh_card_recognize';
import { BankCardScanner, OnRecognizeListener, RecognizeResult, DetectorRect } from '../BankCardScanner';
import { BusinessError } from '@ohos.base';
import { TKFileUtils } from '../../../../utils/TKFileUtils';
import image from '@ohos.multimedia.image';
import { CameraPreviewSize } from '../../../../components/camera/common/constants/TKCameraConstants';
import  ClickUtils  from '../../../../utils/TKClickUtils';
import { DetectBorderResult } from 'hh_card_recognize/src/main/ets/components/mainpage/DetectBorderResult';

/**
 * 合合银行卡扫码实现类
 * 用于实现银行卡号码识别、银行标识等功能
 */
export default class TKJJCFIntsigBankCardScanner implements BankCardScanner {

  // 合合SDK授权key
  private appkey: string = 'newAuth2472fe1657d82d7e'  // 测试授权


  // 边距阈值(单位:像素)
  private BORDER: number = 40;

  // 识别结果监听器
  private listener?: OnRecognizeListener;

  // 文件存储目录
  private filesDir?: string;

  // 是否开始识别标志
  private _start: boolean = false;

  // 连续匹配次数计数
  private continue_match_time: number = 0;

  // 检测区域范围
  private detectorRect?: DetectorRect;

  // 相机预览尺寸
  cameraPreviewSize: CameraPreviewSize = { width: 1280, height: 960 }

  init(context: Context, _params?: Record<string, Object>): void {
    TKLog.debug(`[思迪合合银行卡OCR工具]:初始化`)

    if (TKStringHelper.isEmpty(this.appkey)) {
      this.listener?.onFailed? this.listener?.onFailed(-2, '授权失败') : undefined
      return
    }

    // 设置文件存储目录
    this.filesDir = context.filesDir;
    TKFileUtils.mkdir(this.filesDir)

    // 初始化SDK
    try {
      VpuMoreCardPicPreKV.initICCardRecognizer(this.appkey).then((status) => {

        TKLog.debug(`[思迪合合银行卡OCR工具]:初始化完成: status=${status}`)

        if (status == 0) {
          // 设置识别类型为银行卡
          VpuMoreCardPicPreKV.setRecognizerType(RecognizerType.bank_card);
          TKLog.debug(`[思迪合合银行卡OCR工具]:设置识别类型: RecognizerType=${RecognizerType.bank_card}`)
          this.listener?.onInit ? this.listener?.onInit() : undefined
        } else {
          this.listener?.onFailed? this.listener?.onFailed(-2, '授权失败' + `(${status})`) : undefined
        }
      }).catch((e: BusinessError) => {
        TKLog.debug(`[思迪合合银行卡OCR工具]:初始化失败: e=${JSON.stringify(e)}`)
        this.listener?.onFailed ? this.listener?.onFailed(-2, '授权失败') : undefined
      });
    } catch (error) {
      TKLog.error(`[思迪合合银行卡OCR工具]:初始化异常: e=${JSON.stringify(error)}`)
      this.listener?.onFailed ? this.listener?.onFailed(-2, '授权失败') : undefined
    }
  }

  /**
   * 设置检测区域
   * @param cameraPreviewSize 相机预览尺寸
   */
  setDetectorRect(cameraPreviewSize:CameraPreviewSize): void {
    this.cameraPreviewSize = cameraPreviewSize
    //设置检测区域为手机屏幕可视范围
    TKWindowHelper.getLastWindow(getContext(this)).then((win) => {
      let windowWidth = win.getWindowProperties().windowRect.width;
      let windowHeight = win.getWindowProperties().windowRect.height;
      // 计算预览宽高比
      let r = this.cameraPreviewSize?.width / windowHeight;
      let preViewH = windowWidth * r;

      // 计算检测区域坐标
      let left: number = 0;
      let top: number = (this.cameraPreviewSize?.height - preViewH) / 2;
      let width: number = this.cameraPreviewSize?.width;
      let height: number = preViewH;

      this.detectorRect = { left: left, top: top, right: width + left, bottom: height + top};
      TKLog.debug(`[思迪合合银行卡OCR工具]:设置检测区域: rect=${JSON.stringify(this.detectorRect)}`)
    })
  }

  setListener(listener?: OnRecognizeListener): void {
    this.listener = listener;
  }

  start(): void {
    this._start = true;
    TKLog.debug(`[思迪合合银行卡OCR工具]:开始识别`)
  }

  stop(): void {
    this._start = false;
    TKLog.debug(`[思迪合合银行卡OCR工具]:停止识别`)
  }

  isUseSDKCramera(): boolean {
    return false
  }

  /**
   * 拍摄银行卡照片并识别
   * @param surfaceID 预览界面ID
   * @param _rotate 旋转角度
   */
  async takePhone(surfaceID: string, _rotate:number) {
    // 创建预览图像
    let recHeight: number = this.cameraPreviewSize?.height;
    let pixelMap: PixelMap = await this.createPixelMap(surfaceID, 0, 0, this.cameraPreviewSize?.width, recHeight)

    // 识别图像
    let result = await VpuMoreCardPicPreKV.recognizeCardPixelMap(pixelMap, this.filesDir, this.BORDER);
    TKLog.debug(`[思迪合合银行卡OCR工具]:识别图片: result=${result}`)

    if (result && result.length > 0) {
      let obj = this._checkResult(result)
      TKLog.debug(`[思迪合合银行卡OCR工具]:识别图片: obj=${JSON.stringify(obj)}`)
      if (obj) {
        this.listener?.onCompleted ? this.listener?.onCompleted(obj) : undefined
      }else{
        this.listener?.onScannerTips ? this.listener?.onScannerTips('识别失败，请重试') : undefined
      }
      return
    }else {
      this.listener?.onScannerTips ? this.listener?.onScannerTips('识别失败，请重试') : undefined
    }
  }

  /**
   * 从预览界面创建图像
   */
  async createPixelMap(surfaceId: string, x: number, y: number, width: number, height: number): Promise<PixelMap> {
    let region: image.Region = {
      x: x,
      y: y,
      size: { width: width, height: height }
    };
    return image.createPixelMapFromSurface(surfaceId, region)
  }

  /**
   * 输入图像数据进行识别
   * @param data 图像数据buffer
   * @param width 图像宽度
   * @param height 图像高度
   */
  inputData(data: ArrayBuffer, width: number, height: number): void {
    if (!this._start || !data || data.byteLength <= 0) {
      return
    }
    if (ClickUtils.clickTooFast('inputDataAction', 300)) {
      return;
    }
    TKLog.debug(`[思迪合合银行卡OCR工具]:传入图片: data=${data.byteLength}, width=${width}, height=${height}`)

    //1、检测证件位置
    const border = new Array<number>(8);
    let code: DetectBorderResult = VpuMoreCardPicPreKV.detectBorderYUV(data, width, height);
    TKLog.debug(`[思迪合合银行卡OCR工具]:检测图片: code=${code}`)

    // code大于0检测到边
    if (code.result >= 0) {
      // 2、过滤太贴边数据
      let isDif: boolean = true;
      let dif: number = this.BORDER; //最小边距
      for (let i = 0; i < border.length; i++) {
        if(this.detectorRect){ //设置检测范围
          if (i % 2 == 0){ //x 水平方向范围
            if (border[i] < this.detectorRect.left + dif || border[i] > this.detectorRect.right - dif) {
              isDif = false;
              break
            }
          } else { //y 竖直方向范围
            if (border[i] < this.detectorRect.top + dif || border[i] > this.detectorRect.bottom - dif) {
              isDif = false;
              break
            }
          }
        } else {  //没有设置检测范围，按照原图宽高检测
          if (border[i] < dif || border[i] + dif > (i % 2 == 0 ? width : height)) {
            isDif = false;
            break
          }
        }
      }
      TKLog.debug(`[思迪合合银行卡OCR工具]:边距检测通过: isDif=${isDif}， border=${JSON.stringify(border)}`)
      //todo 还可以根据border校验远近
      if (!isDif) {
        this.continue_match_time = 0; //检测不通过重新计数
      } else if (++this.continue_match_time >= 3) { //连续3次检测通过，降低灵敏度，优化图片模糊问题
        this.continue_match_time = 0;
        let expand_pix: number = this.BORDER;
        //3、识别信息
        VpuMoreCardPicPreKV.recognizeCardYUV(data, width, height, this.filesDir, expand_pix).then((result) => {
          TKLog.debug(`[思迪合合银行卡OCR工具]:识别图片: result=${result}`)
          let obj = this._checkResult(result)
          TKLog.debug(`[思迪合合银行卡OCR工具]:识别图片: obj=${JSON.stringify(obj)}`)
          if (obj) {
            this.listener?.onCompleted? this.listener?.onCompleted(obj) : undefined
            return
          }
        });
      }
    }
  }

  /**
   * 识别PixelMap格式图片
   * @param pixelMap 图片数据
   */
  async recognizePixelMap(pixelMap: image.PixelMap) {
    let result = await VpuMoreCardPicPreKV.recognizeCardPixelMap(pixelMap, this.filesDir, this.BORDER);
    TKLog.debug(`[思迪合合银行卡OCR工具]:识别图片: result=${result}`)
    if (result && result.length > 0) {
      let obj = this._checkResult(result)
      TKLog.debug(`[思迪合合银行卡OCR工具]:识别图片: obj=${JSON.stringify(obj)}`)
      if (obj) {
        this.listener?.onCompleted ? this.listener?.onCompleted(obj) : undefined
      }else{
        this.listener?.onScannerTips ? this.listener?.onScannerTips('识别失败，请重试') : undefined
      }
      return
    } else {
      this.listener?.onScannerTips ? this.listener?.onScannerTips('识别失败，请重试') : undefined
    }
  }

  /**
   * 检查识别结果并转换为RecognizeResult对象
   * @param result 识别结果JSON字符串
   * @returns RecognizeResult对象或undefined
   */
  private _checkResult(result: string): RecognizeResult | undefined {
    let recognizeResultData: RecognizeResultData = JSON.parse(result);
    let item_list: Array<ItemListBean> = recognizeResultData.item_list;
    let sum = item_list.length;

    if(sum > 0){
      let obj: RecognizeResult = new RecognizeResult();
      obj.originalImagePath = recognizeResultData.cropImagePath; //裁剪图
      for (let i = 0; i < sum; i++) {
        let itemListBean: ItemListBean = item_list[i];
        let key: string = itemListBean.key;
        let value: string = itemListBean.value;
        let description: string = itemListBean.description;
        TKLog.debug(`[思迪合合银行卡OCR工具]:checkResult: description=${description}, key=${key}, value=${value}`)
        if(value){
          if (key == 'card_number') { //卡号
            obj.cardNumber = value;
          } else  if (key == 'card_issuer') { //发卡机构
            obj.bankName = value;
          } else  if (key == 'card_issuer_code') { //发卡机构号
            obj.bankID = value;
          } else  if (key == 'card_type') { //卡片类型
            obj.cardType = value;
          } else  if (key == 'validate') { //有效期
            obj.validDate = value;
          } else  if (key == 'bank_number_path') { //卡号路径
            obj.cardNumberPath = value;
          }
        }
      }
      //卡号、卡号路径、原图必须有
      if (obj.cardNumber && obj.cardNumberPath && obj.originalImagePath) {
        return obj;
      }
    }
    return undefined;
  }

  release(): void {
    this.stop();
    TKLog.debug(`[思迪合合银行卡OCR工具]:释放资源`)
    this.listener = undefined;
    //释放模型资源
    VpuMoreCardPicPreKV.releaseMemory();
  }
}