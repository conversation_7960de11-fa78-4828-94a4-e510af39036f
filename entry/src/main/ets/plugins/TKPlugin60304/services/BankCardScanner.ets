import { image } from '@kit.ImageKit';
import { CameraPreviewSize } from '../../../components/camera/common/constants/TKCameraConstants';

/**
 * 银行卡扫描器接口定义
 * 用于实现银行卡号码识别、银行标识等功能
 */
export interface BankCardScanner {

  /**
   * 初始化扫描器
   * @param context 上下文对象
   * @param params 初始化参数
   */
  init(context: Context, params?: Record<string, Object>): void;

  /**
   * 设置识别结果监听器
   * @param listener 监听器对象
   */
  setListener(listener?: OnRecognizeListener): void;

  /**
   * 设置检测区域范围
   * @param cameraPreviewSize 相机预览尺寸
   */
  setDetectorRect(cameraPreviewSize: CameraPreviewSize): void;

  /**
   * 开始银行卡扫描检测
   */
  start(): void;

  /**
   * 是否使用自定义摄像头类 易道摄像头由易道sdk控制
   * @returns true:使用SDK相机 false:使用自定义相机
   */
  isUseSDKCramera(): boolean;

  /**
   * 拍摄银行卡照片
   * @param surfaceID 预览界面ID
   * @param rotate 旋转角度
   */
  takePhone(surfaceID: string, rotate: number): void;

  /**
   * 停止银行卡扫描检测
   */
  stop(): void;

  /**
   * 输入图像数据进行识别
   * @param data 图像数据buffer
   * @param width 图像宽度
   * @param height 图像高度
   */
  inputData(data: ArrayBuffer, width: number, height: number): void;

  /**
   * 识别PixelMap格式图片
   * @param pixelMap 图片数据
   */
  recognizePixelMap(pixelMap: image.PixelMap): void;

  /**
   * 释放扫描器资源
   */
  release(): void;
}

/**
 * 银行卡识别结果类
 */
export class RecognizeResult {
  // 银行卡号码
  cardNumber?: string;

  // 银行机构标识ID
  bankID?: string;

  // 发卡银行名称
  bankName?: string;

  // 银行卡类型(储蓄卡/信用卡)
  cardType?: string;

  // 银行卡有效期
  validDate?: string;

  // 银行卡原始图片路径
  originalImagePath?: string;

  // 银行卡号码图片路径
  cardNumberPath?: string;
}

/**
 * 检测区域矩形接口
 */
export interface DetectorRect {
  // 左边界坐标
  left: number;

  // 上边界坐标  
  top: number;

  // 右边界坐标
  right: number;

  // 下边界坐标
  bottom: number;
}

/**
 * 识别结果监听器类
 */
export class OnRecognizeListener {

  /**
   * 初始化完成
   */
  public onInit? : () => void
  
  /**
   * 检测失败
   * @param errorNo 错误码
   * @param errorInfo 错误信息
   */
  public onFailed? : (errorNo: number, errorInfo: string) => void

  /**
   * 检测提示
   * @param errorInfo 质检提示
   */
  public onScannerTips? : (errorInfo: string) => void

  /**
   * 检测完成
   * @param result 检测结果
   */
  public onCompleted? : (result: RecognizeResult) => void
}