import { TKBasePlugin, TKLog, TKResultVO } from '@thinkive/tk-harmony-base';
import ClickUtils from '../../utils/TKClickUtils';
import { TKBiometricAuthManager, TKBiometricAuthType } from '../../utils/TKBiometricAuthManager';

/**
 * 插件: 异步指纹数据同步
 * 功能号: 80321
 * 描述: 同步指纹数据状态，对应Android版本的Message80321
 *
 */
export class TKPlugin80321 extends TKBasePlugin {
  
  /**
   * 插件调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info('[思迪80321插件]:开始执行异步指纹数据同步');
    
    let resultVO: TKResultVO = new TKResultVO();
    
    // 防止重复调用 - 2秒内不允许重复调用
    if (ClickUtils.clickTooFast('80321', 2000)) {
      TKLog.info('[思迪80321插件]:重复调用，忽略本次请求');
      resultVO.errorNo = 1;
      resultVO.errorInfo = '重复调用';
      return resultVO;
    }
    
    TKLog.debug(`[思迪80321插件]:传入参数: ${JSON.stringify(param)}`);
    
    // 执行指纹数据同步
    this.syncFingerprintData();
    
    // 同步返回结果
    TKLog.info('[思迪80321插件]:异步指纹数据同步执行完成');
    return resultVO;
  }
  
  /**
   * 同步指纹数据
   * 对应Android版本的syncPrint()方法
   */
  private async syncFingerprintData(): Promise<void> {
    TKLog.info('[思迪80321插件]:开始同步指纹数据状态');
    
    try {
      // 获取生物识别认证管理器实例
      const authManager = TKBiometricAuthManager.getInstance();

      // 检查指纹支持状态
      const isSupported: boolean = await authManager.isAuthTypeSupported(TKBiometricAuthType.FINGERPRINT);
      TKLog.info(`[思迪80321插件]:指纹支持状态: ${isSupported}`);

      if (isSupported) {
        // 在HarmonyOS中，我们可以通过重新检查认证状态来实现类似Android的updateFingerData功能
        // 这里主要是刷新内部缓存状态
        // 清除可能的缓存，确保获取最新状态
        authManager.clearCache();
        TKLog.info('[思迪80321插件]:指纹数据状态已同步');
      } else {
        TKLog.warn('[思迪80321插件]:当前设备不支持指纹认证');
      }

      // 构建同步结果信息
      const syncResult:Record<string, Object> = {
        "supported": isSupported,
        "enrolled": isSupported, // 在HarmonyOS中，支持即表示可用
        "timestamp": Date.now()
      } as Record<string, Object>;
      
      TKLog.info(`[思迪80321插件]:指纹数据同步完成: ${JSON.stringify(syncResult)}`);
      
      // 注意：Android版本的Message80321没有回调H5，这里保持一致
      // 如果需要通知H5同步结果，可以取消下面的注释
      /*
      this.harmonyCallPluginCallBack({
        funcNo: '80322', // 可以定义一个新的功能号用于同步结果回调
        syncResult: syncResult,
        errorNo: '0',
        errorInfo: '同步成功'
      });
      */
      
    } catch (error) {
      TKLog.error(`[思迪80321插件]:指纹数据同步异常: ${JSON.stringify(error)}`);
      
      // 同样，如果需要错误回调，可以取消注释
      /*
      this.harmonyCallPluginCallBack({
        funcNo: '80322',
        errorNo: '-1',
        errorInfo: '同步失败'
      });
      */
    }
  }
}
