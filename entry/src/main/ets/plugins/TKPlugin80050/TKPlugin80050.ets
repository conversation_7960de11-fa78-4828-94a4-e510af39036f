import { TKBasePlugin, T<PERSON><PERSON>og, TKResultVO, TKStringHelper } from '@thinkive/tk-harmony-base';
import { bundleManager, common, OpenLinkOptions } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';


/**
 * 插件: 打开外链
 */
export class TKPlugin80050 extends TKBasePlugin {
  /**
   * 插件调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info("[思迪80050插件]:开始调用打开外链插件")

    let resultVO: TKResultVO = new TKResultVO();

    const scheme = param.scheme.toString() as string;
    const paramExt = param.paramExt.toString() as string; // 传入的参数
    const downloadLink = param.downloadLink.toString() as string;

    if (TKStringHelper.isEmpty(downloadLink)) {
      resultVO.errorNo = -8005001;
      resultVO.errorInfo = "downloadLink不能为空";
      return resultVO;
    }

    if (scheme) {
      const url = `${scheme}://${paramExt}`;

      if (bundleManager.canOpenLink(url)) {
        openURL(url);
      } else {
        if (bundleManager.canOpenLink(downloadLink)) {
          openURL(downloadLink);
        }
      }
    } else {
      if (bundleManager.canOpenLink(downloadLink)) {
        openURL(downloadLink);
      }
    }

    return resultVO;
  }
}

function openURL(url: string) {
  let context: common.UIAbilityContext = getContext() as common.UIAbilityContext;
  let openLinkOptions: OpenLinkOptions = {
    appLinkingOnly: false
  };

  try {
    context.openLink(url, openLinkOptions)
      .then(() => {
        TKLog.info(`[思迪80050插件]:打开外链(${url})成功`)
      }).catch((err: BusinessError) => {
      TKLog.info(`[思迪80050插件]:打开外链(${url})失败，Code is ${err.code}, message is ${err.message}`)
    });
  } catch (err) {
    TKLog.info(`[思迪80050插件]:打开外链(${url})异常，Code is ${err.code}, message is ${err.message}`)
  }
}