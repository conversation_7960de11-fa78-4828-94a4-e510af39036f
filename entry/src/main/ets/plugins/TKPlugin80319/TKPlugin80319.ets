import { TKBasePlugin, TKLog, TKResultVO } from '@thinkive/tk-harmony-base';
import ClickUtils from '../../utils/TKClickUtils';
import { TKBiometricAuthManager, TKBiometricAuthType, TKBiometricAuthCallback, TKBiometricAuthResult } from '../../utils/TKBiometricAuthManager';

/**
 * 插件: 同步指纹验证
 * 功能号: 80319
 * 描述: 执行指纹验证并同步返回结果，对应Android版本的Message80319
 *
 */
export class TKPlugin80319 extends TKBasePlugin {
  
  /**
   * 插件调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info('[思迪80319插件]:开始执行同步指纹验证');
    
    let resultVO: TKResultVO = new TKResultVO();
    
    // 防止重复调用 - 2秒内不允许重复调用
    if (ClickUtils.clickTooFast('80319', 2000)) {
      TKLog.info('[思迪80319插件]:重复调用，忽略本次请求');
      resultVO.errorNo = 1;
      resultVO.errorInfo = '重复调用';
      return resultVO;
    }
    
    TKLog.debug(`[思迪80319插件]:传入参数: ${JSON.stringify(param)}`);
    
    // 执行指纹验证
    this.performFingerprintAuth();
    
    // 同步返回结果 - 实际结果通过异步回调返回
    TKLog.info('[思迪80319插件]:同步指纹验证请求已提交');
    return resultVO;
  }
  
  /**
   * 执行指纹验证
   * 对应Android版本的check()方法
   */
  private async performFingerprintAuth(): Promise<void> {
    TKLog.info('[思迪80319插件]:开始执行指纹认证流程');
    
    try {
      // 获取生物识别认证管理器实例
      const authManager = TKBiometricAuthManager.getInstance();

      // 检查指纹支持状态
      const isSupported: boolean = await authManager.isAuthTypeSupported(TKBiometricAuthType.FINGERPRINT);

      if (!isSupported) {
        // 设备不支持指纹 - 对应Android的DEVICE_UNSUPPORTED
        TKLog.warn('[思迪80319插件]:设备不支持指纹认证');
        this.sendResultToH5('设备不支持指纹', '-1');
        return;
      }

      // HarmonyOS中，如果支持指纹认证，通常表示已录入指纹
      // 具体的录入状态检查会在认证过程中处理
      TKLog.info('[思迪80319插件]:设备支持指纹认证，开始认证流程');
      
      // 创建认证回调
      const callback: TKBiometricAuthCallback = {
        onSuccess: (result: TKBiometricAuthResult) => {
          TKLog.info(`[思迪80319插件]:指纹认证成功: ${JSON.stringify(result)}`);
          this.sendResultToH5('验证成功', '0');
        },
        
        onFailed: (result: TKBiometricAuthResult) => {
          TKLog.warn(`[思迪80319插件]:指纹认证失败: ${JSON.stringify(result)}`);

          // 根据错误码映射到对应的错误信息
          let errorInfo: string = '指纹无法识别';
          let errorNo: string = '-4';

          // 根据具体错误码进行映射
          switch (result.errorCode) {
            case -1: // 设备不支持
              errorInfo = '设备不支持指纹';
              errorNo = '-1';
              break;
            case -2: // 未设置锁屏
              errorInfo = '未录制屏幕保护';
              errorNo = '-2';
              break;
            case -3: // 未录入指纹
              errorInfo = '未录入指纹';
              errorNo = '-3';
              break;
            case -5: // 指纹数据变化
              errorInfo = '指纹数据发生变化';
              errorNo = '-5';
              break;
            default:
              errorInfo = result.errorMessage || '指纹无法识别';
              errorNo = '-4';
              break;
          }

          this.sendResultToH5(errorInfo, errorNo);
        },
        
        onCancel: () => {
          TKLog.info('[思迪80319插件]:用户取消指纹认证');
          this.sendResultToH5('取消指纹登录', '-6');
        }
      };
      
      // 执行指纹认证
      TKLog.info('[思迪80319插件]:开始调用指纹认证');
      await authManager.authenticate(TKBiometricAuthType.FINGERPRINT, callback);
      
    } catch (error) {
      TKLog.error(`[思迪80319插件]:指纹认证异常: ${JSON.stringify(error)}`);
      this.sendResultToH5('指纹认证异常', '-4');
    }
  }
  
  /**
   * 向H5发送认证结果
   * 对应Android版本的send80319Msg方法
   * @param errorInfo 错误信息
   * @param errorNo 错误码
   */
  private sendResultToH5(errorInfo: string, errorNo: string): void {
    TKLog.info(`[思迪80319插件]:向H5发送认证结果: errorInfo=${errorInfo}, errorNo=${errorNo}`);

    // 通过harmonyCallPluginCallBack回调H5，保持与Android版本一致的格式
    this.harmonyCallPluginCallBack({
      funcNo: '80320', // 固定回调功能号，与Android版本保持一致
      errorInfo: errorInfo,
      errorNo: errorNo
    });
  }
}
