export class TKScanPhotoModel {
  // 服务端接口参数
  requestParam: string = ""

  // 服务端接口地址
  url: string = ""

  // 拍摄的证件类型(4:身份证人像面,5:身份证国徽面)
  imgType: string = ""

  // 证件照压缩大小(单位:KB)
  compressSize: string = ""

  // 模块名称
  moduleName: string = ""

  // 扩展参数(JSON格式数据)
  extParams?: JSON

  // 证件识别类型(默认服务器识别,1:本地识别,2:服务器识别)
  ocrType: string = ""

  // 服务器接入类型(1:http,2:https)
  serverAccessType: string = ""

  // 大头照是否展示头像框和提示语(0:不展示,1:展示)
  isShowFaceGizmos: string = ""

  // 图片内容上传的key
  fileUploadKey: string = ""

  // 动作类型(photo:拍照,album:相册)
  action: string = ""

  // 是否显示相册按钮(0:不显示,1:显示)
  isAlbum: string = ""

  // 是否显示拍照按钮(0:不显示,1:显示)
  isTake: string = ""

  // 水印图片名称
  watermarkImgName: string = ""

  // 调整图片和按钮主色调(十六进制颜色值)
  mainColor: string = ""

  // 是否需要水印(0:不需要,1:需要)
  isNeedWatermark: string = ""

  // 水印图片base64数据
  watermarkImgBase64: string = ""

  // 证件照图片上传方式(0:原生上传,1:H5上传)
  isUpload: string = ""

  // 是否身份证复印件(0:否,1:是)
  isCopies: string = ""

  // 扫描结果是否需要确认页面(0:不需要,1:需要)
  isNeedOcrAffirmView: string = ""

  // 照片结果展示是否需要显示示例部分(0:不需要,1:需要)
  isNeedSample: string = ""

  // 是否在调用插件前展示原生权限介绍页面(0:不需要,1:需要)
  isNeedTKAuthorIntroduce: string = ""

  // 连拍的时候正反面结果是否一起返回(0:否,1:是)
  isNeedFullResults: string = ""

  // 原生接口请求是否走微服务(0:否,1:是)
  isRestFull: string = ""

  // 请求签名的APPId(微服务加密加签:default)
  requestSignAppId: string = ""

  // 请求签名的Key(加密后的)
  requestSignKey: string = ""

  // 身份证正面预览图base64数据
  previewSamplePicFront: string = ""

  // 身份证反面预览图base64数据
  previewSamplePicBack: string = ""

  //=============业务参数==========================
  // 是否是拍银行卡(60037插件使用)
  isTakeBank: boolean = false

  //裁边留白宽度
  cardBorderWidth: string = ""
}