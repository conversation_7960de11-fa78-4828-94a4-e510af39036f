import router from '@ohos.router';
import { BusinessError, Callback } from '@ohos.base';
import { TKFileUtils } from '../../../utils/TKFileUtils';
import { TKScanPhotoModel } from '../vos/TKScanPhotoModel';
import {
  T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
  T<PERSON><PERSON><PERSON>oSelectOptions,
  T<PERSON><PERSON><PERSON><PERSON>el<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TKWindowHelper } from '@thinkive/tk-harmony-base';
import { TKOpenPhotoPreview } from '../../../pages/common/TKOpenPhotoPreview';
import { CameraPosition, CameraPreviewSize } from '../../../components/camera/common/constants/TKCameraConstants';
import TKCameraService from '../../../components/camera/TKCameraService';
import TKImageUtil from '../../../utils/TKImageUtil';
import TKStringUtil from '../../../utils/TKStringUtil';
import { display } from '@kit.ArkUI';
import ClickUtils from '../../../utils/TKClickUtils';
import { TKIDCardScanner, RecognizeResult } from '../services/TKIDCardScanner';
import { TKCardScannerManger } from '../services/impl/TKCardScannerManger';
import { TKCommonAlertDialog } from '../../../components/TKCommonAlertDialog';
import { TKOpenNetService } from '../../../services/TKOpenNetService';
import { image } from '@kit.ImageKit';
import { util } from '@kit.ArkTS';
import { photoAccessHelper } from '@kit.MediaLibraryKit';
import { webview } from '@kit.ArkWeb';
import { TKPhotoPreviewAttribute } from '../../../pages/common/TKPhotoPreviewAttribute';
import { drawing } from '@kit.ArkGraphics2D';
import { TKCommonDialogAttribute } from '../../../components/TKCommonDialogAttribute';
import { TKFoldManager } from '../../../utils/TKFoldManager';
import { camera } from '@kit.CameraKit';

@Entry({ routeName: 'TKJJCFOpenIDScannerPhotoPage' })
@Component
struct TKJJCFOpenIDScannerPhotoPage {
  // 是否显示相册按钮
  @State isAlbum: boolean = true;
  // 是否显示拍照按钮
  @State isTake: boolean = true;
  // 图片类型 4:人像面 5:国徽面
  @State imgType: string = '4';
  // 图片类型名称
  @State imgTypeName?: string = '';
  // 证件框图片资源
  @State cardBoxImg?: ResourceStr = $r("app.media.tk_open_plugin_60013_cardBox");
  // 摄像头预览分辨率
  @State cameraPreviewSize: CameraPreviewSize = { width: 1280, height: 960 }
  // 图片base64数据
  @State base64: string = ""
  // 是否显示预览界面
  @State isShowPrew: boolean = false
  // 是否需要示例图
  @State isNeedSample: boolean = false
  // 示例图资源
  @State sampleImg: ResourceStr = $r("app.media.tk_open_plugin_common_front_example_icon");
  // 折叠屏展开边距
  @State isFoldSpace: number = 0
  // 是否是相册选择模式
  @State isSelectPhotoAction: boolean = false
  // 是否是拍照模式
  @State isTakeModule: boolean = false
  // 扫描线移动位置
  @State scanLineTranslateX: number = 0
  // 旋转角度
  @State rotateAngle: number = 0
  // 是否显示进度条
  @State isShowProgress: boolean = false
  // 页面旋转角度（折叠屏时不做横屏）
  @State pageAngle: number = 90

  // 回调结果Map
  callbackResult = new Map<string, Object>()
  // XComponent控制器
  mXComponentController: XComponentController = new XComponentController;

  // 预览界面样式属性
  @State styleAttribute: TKPhotoPreviewAttribute = new TKPhotoPreviewAttribute();

  // 对话框样式属性
  @State styleDialogAttribute: TKCommonDialogAttribute = new TKCommonDialogAttribute();

  // 页面参数
  params: TKScanPhotoModel = new TKScanPhotoModel
  // 需求的拍照类型数组｛4,5,3 ; 4,5 ; 4 ; 5 ; 3｝
  imageTypes: string[] = [];
  // 当前处理的照片类型索引
  currentImageIndex: number = -1;
  // webview控制器
  @State webController: webview.WebviewController = new webview.WebviewController();

  // 对话框标题
  @State title: string = '证件识别不全，请检查！';
  // 对话框内容
  @State content: string = '1、存在曝光或阴影。2、图片模糊';
  // 对话框控制器
  commDialog?: CustomDialogController = new CustomDialogController({
    builder: TKCommonAlertDialog({
      content: this.content,
      title: this.title,
      leftTxt: "重新扫描",
      rightTxt: "手动拍照", 
      isHor: this.pageAngle == 90 ? true : false,
      styleDialogAttribute:this.styleDialogAttribute,
      leftClick: (_: string) => {
        TKLog.debug('[思迪身份证OCR页面]:点击重新扫描')
        this.isShowPrew = false
        this.isTakeModule = false
        this.idCardScanner?.start()
        // this.postDelayedTimeout()
      },
      rightClik: (_: string) => {
        TKLog.debug('[思迪身份证OCR页面]:点击手动拍照')
        this.isShowPrew = false
        this.isTakeModule = true
        this.idCardScanner?.stop()
        this.stopTimeOut();
      },
    }),
    alignment: DialogAlignment.Center,
    cornerRadius: 8,
    autoCancel: false,
    customStyle: true,
  })

  // 窗口宽度
  @State windowWidth: number = 0
  // 窗口高度
  @State windowHeight: number = 0
  // 身份证扫描器
  private idCardScanner: TKIDCardScanner | undefined  = TKCardScannerManger.getCardScanner("");
  // OCR识别结果
  private ocrResult?: RecognizeResult
  // 图片类型错误提示
  @State imageTypeErrorTip?: string = undefined;
  // 状态资源
  @State statusResource?: string | null = null;
  // 是否超时
  @State isTimeOut:boolean = false
  // 超时时间 30s
  timeout: number = 30 * 1000;
  // 超时定时器ID
  timeOutIndex: number = -1
  // 文件路径
  filePath: string = ''
  // 开户服务
  private openAccountService = new TKOpenNetService()
  // 是否开启闪光灯
  @State cameraService: TKCameraService = TKCameraService.getInstance()

  build() {
    Stack() {
      // 相机预览组件
      PreviewView({ mXComponentController: this.mXComponentController, cameraPreviewSize: this.cameraPreviewSize })
        .aspectRatio(this.cameraPreviewSize.height / this.cameraPreviewSize.width)
        .height('100%')// 根据父控件宽高比进行裁剪
        .visibility(this.idCardScanner?.isUseSDKCramera() ? Visibility.None : Visibility.Visible)

      Row() {
        // 页面左半边区域（包括返回和相册）
        Column() {

          // 闪光灯
          Column() {
            Button() {
              Image($r(this.cameraService.getIsFlash() ? "app.media.tk_JJCF_scan_flash_cloae_icon" : "app.media.tk_JJCF_scan_flash_icon"))
                .padding(11)
                .objectFit(ImageFit.Contain)
                .rotate({
                  angle: 90
                })
            }
            .width(48)
            .height(48)
            .backgroundColor('#00000000')
            .margin({ top: 10 })
            .onClick(() => {
              TKLog.debug('[思迪身份证OCR页面]:切换闪光灯')
              this.cameraService.handleFlash()
            })
            .visibility(this.isTake ? Visibility.Visible : Visibility.Hidden)
          }
        }
        .backgroundColor(0x99000000)
        .width(this.pageAngle == 90 ?'20%':'17%')
        .height('100%')
        .padding({ top: this.isFoldSpace, bottom: this.isFoldSpace })
        .justifyContent(FlexAlign.SpaceBetween)

        // 页面中间区域（包括取景框和文案）
        Column() {
          RelativeContainer() {
            // 顶部提示文字
            Text() {
              Span('请勿抖动')
                .fontColor($r('sys.color.white'))
            }
            .fontSize(14)
            .fontFamily('PingFang SC')
            .fontWeight(FontWeight.Medium)
            .width('100%')
            .height(30 + this.isFoldSpace)
            .padding({ top: this.isFoldSpace })
            .textAlign(TextAlign.Center)
            .backgroundColor(0x99000000)
            .alignRules({
              'top': { 'anchor': '__container__', 'align': VerticalAlign.Top },
              'left': { 'anchor': '__container__', 'align': HorizontalAlign.Start }
            })
            .width('100%')
            .id("id_take_photo_top")

            // 身份证取景框
            Image(this.cardBoxImg)
              .width('100%')
              .objectFit(ImageFit.Fill)
              .backgroundColor('#0000')
              .alignRules({
                'top': { 'anchor': 'id_take_photo_top', 'align': VerticalAlign.Bottom },
                'bottom': { 'anchor': 'id_take_photo_bottom', 'align': VerticalAlign.Top }
              })
              .id("id_take_photo_middle")
              .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB("#FFFFFF"), drawing.BlendMode.SRC_IN))
              .width('100%')

            // 底部区域
            Column()
              .alignRules({
                'bottom': { 'anchor': '__container__', 'align': VerticalAlign.Bottom },
                'left': { 'anchor': 'id_take_photo_top', 'align': HorizontalAlign.Start }
              })
              .width('100%')
              .height(30 + this.isFoldSpace)
              .padding({ bottom: this.isFoldSpace })
              .backgroundColor(0x99000000)
              .id("id_take_photo_bottom")

            // 扫描线动画
            Image($r("app.media.tk_open_plugin_60014_scan_line"))
              .translate({ x: this.scanLineTranslateX })
              .alignRules({
                'top': { 'anchor': 'id_take_photo_top', 'align': VerticalAlign.Bottom },
                'bottom': { 'anchor': 'id_take_photo_bottom', 'align': VerticalAlign.Top }
              })
              .id("id_take_photo_scan_line")
              .animation({
                duration: 2000,
                curve: Curve.Linear,
                delay: 0,
                iterations: -1, // 设置-1表示动画无限循环
                playMode: PlayMode.Normal
              })
              .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB(this.styleAttribute.fontColor ? this.styleAttribute.fontColor : "#2F85FF"), drawing.BlendMode.SRC_IN))
              .visibility(this.isTakeModule ? Visibility.None : Visibility.Visible)

            // 状态提示文字
            Text(this.statusResource)
              .visibility(this.statusResource ? Visibility.Visible : Visibility.Hidden)
              .fontColor($r('sys.color.white'))
              .padding({ left: 15, top: 10, right: 15, bottom: 10})
              .fontSize(18)
              .textAlign(TextAlign.Center)
              .backgroundColor('rgba(253, 77, 67, 0.9)')
              .borderRadius(5)
              .alignRules({
                'center': { 'anchor': '__container__', 'align': VerticalAlign.Center },
                'middle': { 'anchor': '__container__', 'align': HorizontalAlign.Center },
              })
              .id("id_take_photo_type_error_tip_text")

            // 图片类型错误提示
            Column() {
              Image($r("app.media.tk_open_plugin_60014_cardtype_error_ic"))
                .width(80)
                .height(80)
                .objectFit(ImageFit.Contain)

              Text(this.imageTypeErrorTip)
                .fontSize(14)
                .fontFamily('PingFang SC')
                .fontColor("#fff")
                .margin({top: 10})
                .textAlign(TextAlign.Center)
            }
            .alignRules({
              'center': { 'anchor': '__container__', 'align': VerticalAlign.Center },
              'middle': { 'anchor': '__container__', 'align': HorizontalAlign.Center },
            })
            .backgroundColor('#********')
            .width(160)
            .height(160)
            .borderRadius(8)
            .alignItems(HorizontalAlign.Center)
            .justifyContent(FlexAlign.Center)
            .visibility(this.imageTypeErrorTip ? Visibility.Visible : Visibility.Hidden)
            .id("id_take_photo_type_error_tip")

            // 超时提示弹窗
            Column() {
              Text("长时间扫描不成功？")
                .fontSize(18)
                .fontFamily('PingFang SC')
                .fontWeight(FontWeight.Bold)
                .fontColor("#333333")
                .margin({ top: 20 })

              Row() {
                Column() {
                  Image($r("app.media.tk_open_plugin_60014_dialog_error_icon_1"))
                    .width(60).height(60)

                  Text('边框完整')
                    .fontSize(14)
                    .fontColor('#666666')
                }.layoutWeight(1)

                Column() {
                  Image($r("app.media.tk_open_plugin_60014_dialog_error_icon_2"))
                    .width(60).height(60)

                  Text('拿稳手机')
                    .fontColor('#666666')
                    .fontSize(14)
                }.layoutWeight(1)

                Column() {
                  Image($r("app.media.tk_open_plugin_60014_dialog_error_icon_3"))
                    .width(60).height(60)

                  Text('光线合适')
                    .fontColor('#666666')
                    .fontSize(14)
                }.layoutWeight(1)

              }.margin({top:12,left:12,right:12})

              // 提示文案
              Web({ src: '', controller: this.webController })
                .zoomAccess(false)
                .hitTestBehavior(HitTestMode.None)
                .height(40)
                .margin({top:15,left:10,right:10})
                .onControllerAttached(() => {
                  TKLog.debug('[思迪身份证OCR页面]:加载超时提示文案')
                  this.webController.loadData(
                    "<html><p  style=\"color: #666666;font-size: 40px;text-align: center\" >\n" +
                      "如果长时间扫描未成功，可选择手动拍照模式。<font color= '#FFA900'>(把身份证放在深色背景上更容易识别)</font>" +
                      "</p></html>",
                    "text/html",
                    "UTF-8",
                    " ",
                    " "
                  );
                })

              // 操作按钮
              Row() {
                Column() {
                  Text("手动拍照")
                    .fontSize(16)
                    .width(120)
                    .height(40)
                    .borderRadius(30)
                    .fontColor("#1061FF")
                    .backgroundColor("#111061FF")
                    .textAlign(TextAlign.Center)
                    .align(Alignment.Center)
                    .onClick(() => {
                      TKLog.debug('[思迪身份证OCR页面]:点击手动拍照')
                      this.isTakeModule = true
                      this.idCardScanner?.stop()
                      this.stopTimeOut();
                    })
                }.margin({ right: 5 })

                Column() {
                  Text("再扫一次")
                    .fontColor($r('sys.color.white'))
                    .fontSize(16)
                    .width(120)
                    .height(40)
                    .backgroundColor(0x5A92FF)
                    .linearGradient({
                      direction: GradientDirection.Right,
                      colors: [[0x1061FF, 0.3], [0x5A92FF, 1]]
                    })
                    .borderRadius(30)
                    .textAlign(TextAlign.Center)
                    .align(Alignment.Center)
                    .onClick(() => {
                      TKLog.debug('[思迪身份证OCR页面]:点击再扫一次')
                      // this.postDelayedTimeout()
                      this.idCardScanner?.start()
                    })
                }.margin({ left: 10 })
              }.height(40)
              .margin({top:20})
            }
            .alignRules({
              'center': { 'anchor': '__container__', 'align': VerticalAlign.Center },
              'middle': { 'anchor': '__container__', 'align': HorizontalAlign.Center },
            })
            .width(288)
            .height(270)
            .backgroundColor($r('sys.color.white'))
            .borderRadius(8)
            .visibility(this.isTimeOut ? Visibility.Visible : Visibility.Hidden)
            .id("id_take_photo_time_out")

          }.width('100%')

        }
        .height('100%')
        .width(this.pageAngle == 90 ?'60%':'66%')
        .alignItems(HorizontalAlign.Center)
        .justifyContent(FlexAlign.SpaceBetween)

        // 页面右边区域（包括拍照按钮）
        Column() {

          // 返回按钮区域
          Column() {
            Button() {
              // 返回按钮图标
              Image($r("app.media.tk_JJCF_scan_white_back_icon"))
                .padding(12)
                .objectFit(ImageFit.Contain)
            }
            .width(48)
            .height(48)
            .backgroundColor('#00000000')
            .margin({ top: 10 })
            .onClick(() => {
              TKLog.debug('[思迪身份证OCR页面]:点击返回按钮')
              this.backPageAction()
            })

          }.width('100%')
        }
        .backgroundColor(0x99000000)
        .width(this.pageAngle == 90 ?'20%':'17%')
        .padding({ top: this.isFoldSpace, bottom: this.isFoldSpace })
        .height('100%')
        .justifyContent(FlexAlign.SpaceBetween)

      }
      .height(this.pageAngle == 90 ? this.windowWidth : '100%')
      .rotate({angle:this.pageAngle})
      .width(this.pageAngle == 90 ? this.windowHeight:  '100%')
      .visibility(this.isShowPrew ? Visibility.Hidden : Visibility.Visible)

      // // 照片预览组件
      // TKOpenPhotoPreview({
      //   leftBtn: this.isSelectPhotoAction ? "重选" : "重拍",
      //   showImgStr: this.base64,
      //   imgType: this.imgType,
      //   styleAttribute:this.styleAttribute,
      //   isNeedSample: this.isNeedSample,
      //   sampleImg: this.sampleImg,
      //   rotateAngle:this.rotateAngle,
      //   isFoldSpace: this.isFoldSpace,
      //   isShowProgress:this.isShowProgress,
      //   leftClick: () => {
      //     TKLog.debug('[思迪身份证OCR页面]:点击重拍/重选')
      //     this.onReload()
      //   },
      //   rightClik: () => {
      //     TKLog.debug('[思迪身份证OCR页面]:点击确认')
      //     this.commit()
      //   },
      // })
      //   .height(this.pageAngle == 90 ? this.windowWidth : '100%')
      //   .rotate({angle:this.pageAngle})
      //   .width(this.pageAngle == 90 ? this.windowHeight: '100%')
      //   .visibility(this.isShowPrew ? Visibility.Visible : Visibility.Hidden)
    }
  }

  aboutToAppear() {
    TKLog.debug(`[思迪身份证OCR页面]:aboutToAppear`)

    // 获取路由参数
    if (TKRouterHelper.getParams(this)) {
      this.params = TKRouterHelper.getParams(this) as Object as TKScanPhotoModel; // 获取传递过来的参数对象
    }

    // 处理图片类型参数
    // if (this.params.imgType) {
    //   this.dispatchImageType(this.params.imgType);
    // }

    // 是否为银行卡
    if (this.params.isTakeBank) {
      this.imgType = ''
    }

    // 是否显示相册按钮
    if (this.params.isAlbum) {
      this.isAlbum = this.params.isAlbum == "1"
      TKLog.debug(`[思迪身份证OCR页面]:isAlbum = ${this.isAlbum}`)
    }

    // 是否显示拍照按钮
    if (this.params.isTake) {
      this.isTake = this.params.isTake == "1"
      TKLog.debug(`[思迪身份证OCR页面]:isTake = ${this.isTake}`)
    }

    // 是否需要示例图
    if (this.params.isNeedSample) {
      this.isNeedSample = this.params.isNeedSample == "1"
      TKLog.debug(`[思迪身份证OCR页面]:isNeedSample = ${this.isNeedSample}`)
    }

    // 设置主题色
    if (this.params.mainColor) {
      this.styleAttribute.fontColor = this.params.mainColor
      this.styleDialogAttribute.fontColor = this.params.mainColor
      TKLog.debug(`[思迪身份证OCR页面]:mainColor = ${this.params.mainColor}`)
    }

    // 获取下一个图片类型并刷新UI
    // this.nextImageType()
    // this.refreshImageType(this.imgType);

    // 处理不同的拍摄模式
    if (this.params.action == "phone") {
      TKLog.debug(`[思迪身份证OCR页面]:进入相册选择模式`)
      //跳转相册选择
      this.openPhotoAlbumAction()
      if (this.params.isNeedOcrAffirmView == "0") {
        this.isShowPrew = false
      } else {
        this.isShowPrew = true
      }
    } else {
      TKLog.debug(`[思迪身份证OCR页面]:进入扫描模式`)
      // 初始化身份证扫描器
      this.idCardScanner = TKCardScannerManger.getCardScanner("")
      this.idCardScanner.setListener({
        onInit: () => {
          TKLog.debug(`[思迪身份证OCR页面]:扫描器初始化成功`)
          this.idCardScanner?.start()
          // this.postDelayedTimeout()  // 晋金财富不需要
        },
        onFailed: (errorNo: number, errorInfo: string) => {
          TKLog.error(`[思迪身份证OCR页面]:扫描失败:errorNo = ${errorNo}, errorInfo = ${errorInfo}`)
          this.postWitnessResultToH5(errorNo + '', errorInfo)
        },
        onScannerTips: (errorInfo: string) => {
          TKLog.debug(`[思迪身份证OCR页面]:扫描提示:${errorInfo}`)
          this.statusResource = errorInfo
          setTimeout(() => {
            this.statusResource = undefined
          }, 1500)
        },
        onScannerPreTips: (errorInfo: string) => {
          TKLog.debug(`[思迪身份证OCR页面]:预扫描提示:${errorInfo}`)
          // this.isShowProgress = false
          // this.content = errorInfo
          // this.commDialog?.open()
        },
        onTakePhoneSuccess:  (pix: PixelMap,base64:string) => {
          TKLog.debug(`[思迪身份证OCR页面]:拍照成功`)
          if (pix) {
            //识别图片
            this.base64 = base64
            this.isShowProgress = true
            // this.isShowPrew = true // 晋金财富不需要预览

            setTimeout(() => {
              //base64在预览页面的显示没有那么快先加个延时
              this.idCardScanner?.recognizePixelMap(pix,this.imgType)
            }, 500)
          }
        },
        onCompleted: async (result: RecognizeResult) => {
          TKLog.info("[思迪身份证OCR页面]:识别完成:" + JSON.stringify(result))
          // // 验证身份证正反面
          // if (this.imgType == "4" && !result.isFront) {
          //   if (this.imageTypeErrorTip) return;
          //   this.imageTypeErrorTip = '请识别身份证人像面'
          //   setTimeout(()=>{
          //     this.imageTypeErrorTip = undefined
          //   }, 1000)
          //   return;
          // }
          // if (this.imgType == "5" && result.isFront) {
          //   if (this.imageTypeErrorTip) return;
          //   this.imageTypeErrorTip = '请识别身份证国徽面'
          //   setTimeout(()=>{
          //     this.imageTypeErrorTip = undefined
          //   }, 1000)
          //   return;
          // }

          // 处理裁剪后的图片
          if (result.cropImagePath) {
            this.idCardScanner?.stop();
            this.stopTimeOut();
            const content = await TKFileUtils.readContentUint8Array(result.cropImagePath);
            let picSize: string = this.params.compressSize ? this.params.compressSize : '200'
            if (content) {
              let size: number = Number.parseInt(picSize)
              let buf = await TKImageUtil.qualityCompress(content.buffer as ArrayBuffer, size)
              if (buf) {
                const base64 = content ? TKStringUtil.uint8ArrayToBase64(new Uint8Array(buf)) : "";
                this.preview(base64, result);

                this.filePath = getContext(this).cacheDir + "/ocr";
                TKFileUtils.save(buf.buffer, this.filePath, "idimage.jpg").then((path: string) => {
                  this.filePath = path
                  TKLog.debug(`[思迪身份证OCR页面]:图片保存成功:${path}`)
                }).catch((err: BusinessError) => {
                  TKLog.error(`[思迪身份证OCR页面]:图片保存失败:${err.message}`)
                  this.filePath = ''
                })
              }
            }
          }
          this.isShowProgress = false
        }
      })
      this.idCardScanner.init(getContext(this),(this.params as Object as Record<string, Object>))
    }
    //折叠屏适配
    this.initFold()
  }

  /**
   * 初始化折叠屏相关配置
   */
  initFold() {
    if (!display.isFoldable()) {
      return
    }
    //如果是折叠屏手机
    if (display.getFoldDisplayMode() == display.FoldDisplayMode.FOLD_DISPLAY_MODE_FULL) {
      this.cameraPreviewSize = { width: 1088, height: 1088 }
      let displayClass: display.Display = display.getDefaultDisplaySync()
      this.isFoldSpace = px2vp(displayClass.width / 4 + (displayClass.height - displayClass.width) / 2)
      this.pageAngle = 0
    }

    let that = this;
    TKFoldManager.getInstance().setListener({
      foldDisplayMain(): void {
        TKLog.debug(`[思迪身份证OCR页面]:折叠屏-折叠显示`)
        that.isFoldSpace = 0
        that.cameraPreviewSize = { width: 1280, height: 960 }
        that.pageAngle = 90
        that.onPageShow()
      },
      foldDisplayFull(): void {
        TKLog.debug(`[思迪身份证OCR页面]:折叠屏-非折叠显示`)
        let displayClass: display.Display = display.getDefaultDisplaySync()
        that.isFoldSpace = px2vp(displayClass.width / 4 + (displayClass.height - displayClass.width) / 2)
        that.cameraPreviewSize = { width: 1088, height: 1088 }
        that.pageAngle = 0
        that.onPageShow()
      },
    })
  }

  /**
   * 更新图片类型页面
   *
   * @param imageType 照片类型
   */
  // refreshImageType(imageType: string) {
  //   TKLog.debug(`[思迪身份证OCR页面]:更新图片类型:${imageType}`)
  //   if (this.imageTypes.length <= 0 || this.imageTypes.length > 2) {
  //     this.postWitnessResultToH5("-2", "照片类型数据异常")
  //   } else {
  //     if (imageType == "4") {
  //       this.imgTypeName = '身份证人像面';
  //       this.cardBoxImg = $r("app.media.tk_open_plugin_60013_cardBox")
  //       this.sampleImg = TKStringHelper.isNotEmpty(this.params.previewSamplePicFront) ? 'data:image/jpg;base64,' + this.params.previewSamplePicFront : $r("app.media.tk_open_plugin_common_front_example_icon")
  //     } else if (imageType == "5") {
  //       this.imgTypeName = '身份证国徽面';
  //       this.sampleImg = TKStringHelper.isNotEmpty(this.params.previewSamplePicBack) ? 'data:image/jpg;base64,' + this.params.previewSamplePicBack : $r("app.media.tk_open_plugin_common_reverse_example_icon");
  //       this.cardBoxImg = $r("app.media.tk_open_plugin_60013_cardBox")
  //     }
  //   }
  // }

  // /**
  //  * 确认拍照结果
  //  */
  // commit() {
  //   TKLog.debug(`[思迪身份证OCR页面]:确认拍照结果`)
  //   if ("0" == this.params.isUpload) {
  //     //直接返回识别结果给H5
  //     this.returnToH5()
  //   } else {
  //     //本地上传后返回识别结果给H5
  //     this.uploadImg();
  //   }
  // }

  // /**
  //  * 上传图片到服务器
  //  */
  // uploadImg(){
  //   if (!this.params.url || !this.filePath) {
  //     this.postWitnessResultToH5("-2", "图片路径异常,请重试")
  //     return;
  //   }
  //   TKLog.debug(`[思迪身份证OCR页面]:开始上传图片`)
  //   // 展示动画 正在上传身份证照片,请稍后...
  //   this.isShowProgress = true
  //   //上传的固定路径 internal://cache  对应路径   context.cacheDir
  //   let  mParameters:Record<string, string> = {};
  //
  //   if (this.params.requestParam) {
  //     let params: Array<string> = this.params.requestParam.split("&");
  //     if (params) {
  //       for (let i = 0; i < params.length; i++) {
  //         let keyValue: Array<string> = params[i].split("=");
  //         mParameters[keyValue[0]] = keyValue[1]
  //       }
  //     }
  //   }
  //
  //   if ("4" == this.imgType) {
  //     mParameters.image_type = "idfrontimg";
  //   } else {
  //     mParameters.image_type = "idbackimg";
  //   }
  //
  //   mParameters.fileUploadKey = "file_data";
  //
  //   this.openAccountService.uploadFile(this.params.url,  mParameters.fileUploadKey , this.filePath, undefined,undefined, mParameters,
  //     (uploadData, errorNo, msg) => {
  //       TKLog.info("[思迪身份证OCR页面]:上传完成:" + JSON.stringify(uploadData))
  //       if (errorNo == 0) {
  //         if (uploadData) {
  //           if ("4" == this.imgType) {
  //             this.callbackResult.set("frontFilePath", uploadData.filepath as string);
  //             this.callbackResult.set("frontSecret", uploadData.secret as string);
  //           } else {
  //             this.callbackResult.set("backFilePath", uploadData.filepath as string);
  //             this.callbackResult.set("backSecret", uploadData.secret as string);
  //           }
  //         }
  //         this.returnToH5()
  //       } else {
  //         //自动上传失败，关闭页面
  //         this.isShowProgress = false
  //         this.content = msg
  //         this.commDialog?.open()
  //         TKLog.error(`[思迪身份证OCR页面]:上传失败:${msg}`)
  //         if (!this.params.isNeedOcrAffirmView) {
  //           this.postWitnessResultToH5("-2", "上传失败")
  //         }
  //       }
  //     })
  // }

  // /**
  //  * 下一个图片类型
  //  * @return true 继续下一个类型, false 没有下一个类型了
  //  */
  // nextImageType() {
  //   // 检查图片类型数组是否为空
  //   if (this.imageTypes.length < 1) {
  //     TKLog.debug('[思迪身份证OCR页面]:图片类型数组为空')
  //     return false;
  //   }
  //
  //   // 初始化或增加当前索引
  //   if (this.currentImageIndex == -1) {
  //     this.currentImageIndex = 0;
  //   } else if (this.currentImageIndex >= this.imageTypes.length - 1) {
  //     TKLog.debug('[思迪身份证OCR页面]:已经是最后一个类型')
  //     return false;
  //   } else {
  //     ++this.currentImageIndex;
  //   }
  //
  //   // 设置当前图片类型
  //   this.imgType = this.imageTypes[this.currentImageIndex];
  //   TKLog.debug(`[思迪身份证OCR页面]:切换到下一个图片类型:${this.imgType}`)
  //   return true;
  // }

  /**
   * 根据type分发照片动作
   * @param imageType 照片类型字符串,多个类型用逗号分隔
   */
  dispatchImageType(imageType: string) {
    TKLog.debug(`[思迪身份证OCR页面]:分发照片类型:${imageType}`)
    this.imageTypes = imageType.split(",");
    // 检查类型数量是否合法(目前只支持1-2张照片)
    if (this.imageTypes.length <= 0 || this.imageTypes.length > 2) {
      TKLog.error('[思迪身份证OCR页面]:照片类型数量异常')
      this.postWitnessResultToH5("-2", "照片类型数据异常")
    }
  }

  /**
   * 重拍或者拍下一张重置页面元素状态
   */
  onReload() {
    if (this.isSelectPhotoAction) {
      TKLog.debug('[思迪身份证OCR页面]:重新选择照片')
      this.openPhotoAlbumAction()
      return;
    }
    TKLog.debug('[思迪身份证OCR页面]:重新拍摄')

    // this.refreshImageType(this.imgType);

    this.isShowPrew = false
    if (!this.isTakeModule) {
      this.idCardScanner?.start()
      // this.postDelayedTimeout()
    }
  }

  /**
   * 直接返回图片给H5
   */
  returnToH5() {
    try {
      // 根据图片类型设置不同的返回数据
      if ("4" == this.imgType) {
        TKLog.debug('[思迪身份证OCR页面]:处理身份证人像面数据')
        this.callbackResult.set("frontBase64", this.base64);
        if (this.ocrResult) {
          // 处理身份证号(去掉空格)
          let idNo = this.ocrResult.idNo ?? ''
          idNo = TKStringHelper.replace(idNo, ' ', '')
          this.callbackResult.set("idNo", idNo);

          // 设置其他身份信息
          this.callbackResult.set("custName", this.ocrResult.name ?? '');
          this.callbackResult.set("native", this.ocrResult.address ?? '');
          this.callbackResult.set("birthday", this.ocrResult.birthday ?? '');
          this.callbackResult.set("ethnicName", this.ocrResult.nationality ?? '');
          this.callbackResult.set("userSex", this.ocrResult.sex ?? '');
        }
      } else if ("5" == this.imgType) {
        TKLog.debug('[思迪身份证OCR页面]:处理身份证国徽面数据')
        this.callbackResult.set("backBase64", this.base64);
        if (this.ocrResult) {
          this.callbackResult.set("policeOrg", this.ocrResult.issueAuthority ?? '');
          // 处理有效期
          if (this.ocrResult.validateDate) {
            let split = this.ocrResult.validateDate?.split("-");
            this.callbackResult.set("idbeginDate", TKStringHelper.replace(split[0], '\\.', '-'));
            this.callbackResult.set("idendDate", TKStringHelper.replace(split[1], '\\.', '-'));
          }
        }
      } else {
        TKLog.debug('[思迪身份证OCR页面]:处理其他类型数据')
        this.callbackResult.set("base64", this.base64);
      }

      // 设置成功状态
      this.callbackResult.set("error_no", "0");
      if (this.ocrResult) {
        this.callbackResult.set("isFront", this.ocrResult.isFront ? true : false);
      }

      // 检查是否还有下一个类型需要拍摄
      // if (!this.nextImageType()) {
        TKLog.info(`[思迪身份证OCR页面]:所有照片已完成,返回结果:" + JSON.stringify(this.callbackResult)`)
        TKRouterHelper.back({result: TKDataHelper.mapToJson(this.callbackResult), navComponentOrPathStack: this})
      // } else {
      //   TKLog.debug('[思迪身份证OCR页面]:继续拍摄下一张')
      //   this.onReload();
      // }
    } catch (e) {
      TKLog.error('[思迪身份证OCR页面]:返回数据异常:' + e)
      e.printStackTrace();
    }
  }

  // 页面显示时的生命周期函数
  onPageShow(){
    TKLog.debug("[思迪身份证OCR页面]:页面显示")
    this.rotateAngle = 360

    // 获取窗口尺寸
    let displayClass: display.Display = display.getDefaultDisplaySync()
    this.windowWidth = px2vp(displayClass.width)
    this.windowHeight = px2vp(displayClass.height)
    this.scanLineTranslateX = this.windowHeight * 0.6 - 12
    TKLog.debug("[思迪身份证OCR页面]:窗口尺寸 width:" + this.windowWidth + " height:" + this.windowHeight)

    // 如果不使用SDK相机,则初始化自定义相机
    if (!this.idCardScanner?.isUseSDKCramera()) {
      let that = this;
      setTimeout(() => {
        TKLog.debug("[思迪身份证OCR页面]:初始化相机")

        // 初始化相机服务
        this.cameraService
          .init(getContext(), that.mXComponentController.getXComponentSurfaceId(), CameraPosition.BACK, that.cameraPreviewSize);

        // 添加预览帧监听
        this.cameraService.addLifecycleListener({
          onPreviewFrame(byteBuffer: ArrayBuffer): void {
            try {
              that.idCardScanner?.inputData(byteBuffer, that.cameraPreviewSize.width, that.cameraPreviewSize.height)
            } catch (exception) {
              TKLog.error("[思迪身份证OCR页面]:预览帧处理异常:" + exception)
            }
          }
        });

        // 设置检测区域
        that.idCardScanner?.setDetectorRect(that.cameraPreviewSize);
      }, 200)
    }
  }

  /**
   * 设置超时检测
   */
  postDelayedTimeout() {
    TKLog.debug('[思迪身份证OCR页面]:开始超时检测')
    clearTimeout(this.timeOutIndex)
    this.isTimeOut = false
    this.timeOutIndex = setTimeout(() => {
      TKLog.debug('[思迪身份证OCR页面]:扫描超时')
      this.isTimeOut = true
      this.idCardScanner?.stop()
    }, this.timeout)
  }

  /**
   * 页面隐藏时的生命周期函数
   */
  onPageHide(): void {
    TKLog.debug("[思迪身份证OCR页面]:onPageHide")

    if (!this.idCardScanner?.isUseSDKCramera()) {
      this.cameraService.destroy()
    }
  }

  /**
   * 页面销毁时的生命周期函数
   */
  aboutToDisappear(): void {
    TKLog.debug("[思迪身份证OCR页面]:aboutToDisappear")
    try {
      this.idCardScanner?.release()
      this.idCardScanner = undefined
      this.commDialog?.close()
      this.commDialog = undefined
      TKFoldManager.release()
    } catch (exception) {
      TKLog.error('[思迪身份证OCR页面]:页面销毁异常:' + JSON.stringify(exception));
    }
  }

  /**
   * 打开相册选择照片
   */
  openPhotoAlbumAction() {
    TKLog.info(`[思迪身份证OCR页面]:打开相册`)

    // 设置推荐类型为身份证
    let recommendationType:photoAccessHelper.RecommendationType = photoAccessHelper.RecommendationType.ID_CARD
    let recommendOptions: photoAccessHelper.RecommendationOptions = {
      recommendationType: recommendationType
    }

    // 相册选择配置
    let options: TKPhotoSelectOptions = {
      // MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE,  //可选择的媒体文件类型，若无此参数，则默认为图片和视频类型。
      maxSelectNumber: 1,
      // isPhotoTakingSupported = true, //支持拍照。
      // isEditSupported = true, //支持编辑照片。
      // isSearchSupported = true, //支持搜索。
      recommendationOptions: recommendOptions,
      // preselectedUris?: Array<string>; //预选择图片的uri数据。
    }

    // 选择照片
    TKPickerHelper.selectPhoto(options).then(async (uris) => {
      if (uris.length > 0) {
        let uri = uris[0]
        TKLog.debug(`[思迪身份证OCR页面]:选择照片URI:${uri}`)

        //先压缩分辨率再压缩质量
        let pixelMap = await TKImageUtil.createPixelMapByUri(uri)
        if (pixelMap) {
          this.isSelectPhotoAction = true;

          //识别图片
          let imagePackerApi = image.createImagePacker();
          let packOpts: image.PackingOption = { format: "image/jpeg", quality: 98 };
          imagePackerApi.packing(pixelMap, packOpts).then((data_) => {
            TKLog.debug('[思迪身份证OCR页面]:照片压缩完成')
            this.base64 = "data:image/jpg;base64" + "," + new util.Base64Helper().encodeToStringSync(new Uint8Array(data_))
            this.isShowProgress = true
            // this.isShowPrew = true // 晋金财富不需要预览

            setTimeout(() => {
              this.idCardScanner?.recognizePixelMap(pixelMap,this.imgType)
            }, 500)
          }).catch((error: BusinessError) => {
            TKLog.error("[思迪身份证OCR页面]:照片压缩失败:" + error)
          });
        }
      } else {
        if (this.params.action == "phone") {
          this.postWitnessResultToH5("-1", "用户取消")
        }
      }
    })
  }

  /**
   * 返回按钮点击事件
   */
  backPageAction() {
    TKLog.info(`[思迪身份证OCR页面]:点击返回`)
    this.idCardScanner?.stop()
    this.stopTimeOut();
    this.postWitnessResultToH5("-1", "用户取消")
  }

  /**
   * 拍照按钮点击事件
   */
  takePhotoAction() {
    TKLog.info(`[思迪身份证OCR页面]:点击拍照按钮`)
    try {
      // 防止快速点击
      if (ClickUtils.clickTooFast('takeAction', 1000)) {
        TKLog.debug('[思迪身份证OCR页面]:点击过快,忽略本次点击')
        return;
      }
      //先停止扫描
      this.idCardScanner?.stop();
      this.stopTimeOut();
      this.idCardScanner?.takePhone(this.mXComponentController.getXComponentSurfaceId(),this.pageAngle)
    } catch (err) {
      TKLog.error(`[思迪身份证OCR页面]:拍照失败:${err.message}`);
    }
  }

  /**
   * 停止超时检测
   */
  private stopTimeOut() {
    TKLog.debug('[思迪身份证OCR页面]:停止超时检测')
    clearTimeout(this.timeOutIndex);
    this.isTimeOut = false;
  }

  /**
   * 显示预览页面
   * @param picStr 图片base64字符串
   * @param result OCR识别结果
   */
  preview(picStr: string, result?: RecognizeResult) {
    TKLog.debug(`[思迪身份证OCR页面]:显示预览页`)
    this.base64 = "data:image/jpg;base64" + "," + picStr
    this.ocrResult = result;

    // 晋金财富不需要预览
    // if (this.params.isNeedOcrAffirmView == "0") {
      TKLog.debug('[思迪身份证OCR页面]:不需要预览,直接返回')

      this.imgType = this.ocrResult?.isFront ? '4' : '5'
      this.returnToH5()
    // } else {
    //   this.isShowPrew = true
    // }
  }

  /**
   * 页面过渡动画
   */
  pageTransition() {
    PageTransitionEnter({ type: RouteType.None, duration: 0 })
    PageTransitionExit({ type: RouteType.None, duration: 0 })
  }

  /**
   * 物理返回键处理
   */
  onBackPress(): boolean | void {
    TKLog.debug('[思迪身份证OCR页面]:监听到物理返回键')
    this.backPageAction()
    return true
  }

  /**
   * 向H5发送结果
   * @param error_no 错误码
   * @param error_info 错误信息
   */
  postWitnessResultToH5(error_no: string, error_info: string) {
    let backResult = new Map<string, string>()
    if (error_no) {
      backResult.set("error_no", error_no);
    }
    if (error_info) {
      backResult.set("error_info", error_info);
    }
    TKRouterHelper.back({result: TKDataHelper.mapToJson(backResult), navComponentOrPathStack: this})
    TKLog.info(`[思迪身份证OCR页面]:回调结果:${JSON.stringify(Array.from(backResult))}`);
  }
}

/**
 * 预览视图组件
 */
@Component
struct PreviewView {
  // XComponent控制器
  public mXComponentController?: XComponentController;
  // 相机预览尺寸
  public cameraPreviewSize?: CameraPreviewSize
  // 是否已加载标志
  public isLoad: boolean = false;

  build() {
    // 创建XComponent
    XComponent({
      id: '',
      type: 'surface',
      controller: this.mXComponentController
    })
      .onLoad(() => {
        if (!this.isLoad) {
          this.isLoad = true
          // 设置Surface宽高，预览尺寸设置参考前面 previewProfilesArray 获取的当前设备所支持的预览分辨率大小去设置
          // 预览流与录像输出流的分辨率的宽高比要保持一致
          TKLog.debug(`[思迪身份证OCR页面]:XComponent加载完成`)
          // 设置Surface宽高
          this.mXComponentController?.setXComponentSurfaceSize({
            surfaceWidth: this.cameraPreviewSize?.width,
            surfaceHeight: this.cameraPreviewSize?.height
          });
        }
      }).onDestroy(() => {
      TKLog.debug(`[思迪身份证OCR页面]:XComponent销毁`)
    })
  }
}
