import { image } from '@kit.ImageKit';
import { CameraPreviewSize } from '../../../components/camera/common/constants/TKCameraConstants';

export interface TKIDCardScanner {

  /**
   * 初始化
   * @param context
   * @param params
   */
  init(context: Context, params?: Record<string, Object>): void;

  /**
   * 设置检测事件代理
   * @param listener
   */
  setListener(listener?: OnRecognizeListener): void;

  /**
   * 设置检测区域
   * @param listener
   */
  setDetectorRect(cameraPreviewSize:CameraPreviewSize):void;
  /**
   * 开始检测
   * @param listener
   */
  start(): void;


  /**
   * 识别图片
   * @param pixelMap 数据源
   * @param imgType 证件类型
   */
  recognizePixelMap(pixelMap: image.PixelMap, imgType: string): void;

  /**
   * 是否使用自定义摄像头类 易道摄像头由易道sdk控制
   */
  isUseSDKCramera(): boolean;


  /**
   * 拍照
   * @param surfaceID
   * @param rotate
   */
  takePhone(surfaceID: string,rotate:number):void
  /**
   * 停止检测
   * @param listener
   */
  stop(): void;

  /**
   * 输入数据源
   * @param data 数据源
   * @param width 画面宽
   * @param height 画面高
   */
  inputData(data: ArrayBuffer, width: number, height: number): void;

  /**
   * 释放资源
   */
  release(): void;
}

export class RecognizeResult {
  /**
   * 身份证号码
   */
  idNo?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 地址
   */
  address?: string;
  /**
   * 出生
   */
  birthday?: string;
  /**
   * 民族
   */
  nationality?: string;
  /**
   * 性别（男|女）
   */
  sex?: string;
  /**
   * 头像路径
   */
  avatarPath?: string;
  /**
   * 身份证号码路径
   */
  idNoPath?: string;
  /**
   * 裁剪图片路径
   */
  cropImagePath?: string;
  /**
   * 原图路径
   */
  originalImagePath?: string;
  /**
   * 是否人像面
   */
  isFront?: boolean;
  /**
   * 发证机关
   */
  issueAuthority?: string;
  /**
   * 有效期限（2000.12.12-2020.12.12 | 2000.12.12-长期）
   */
  validateDate?: string;
}

export interface DetectorRect {
  left: number;
  top: number;
  right: number;
  bottom: number;
}

export class OnRecognizeListener {

  /**
   * 初始化完成
   */
  public onInit? : () => void
  /**
   * 检测失败
   * @param errorNo 错误码
   * @param errorInfo 错误信息
   */
  public onFailed? : (errorNo: number, errorInfo: string) => void

  /**
   * 扫描页面检测提示
   * @param errorInfo 质检提示
   */
  public onScannerTips? : (errorInfo: string) => void

  /**
   * 预览页面检测提示
   * @param errorInfo 质检提示
   */
  public onScannerPreTips? : (errorInfo: string) => void

  /**
   * 拍照成功回调
   * @param PixelMap 拍照成功
   */
  public onTakePhoneSuccess? : (pix: PixelMap,base64:string) => void

  /**
   * 检测完成
   * @param result 检测结果
   */
  public onCompleted? : (result: RecognizeResult) => void
}