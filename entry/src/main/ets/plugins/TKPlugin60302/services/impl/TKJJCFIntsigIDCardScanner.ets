/**
 * 合合身份证扫码实现类
 * 实现身份证OCR识别功能
 */
import { T<PERSON><PERSON><PERSON>, TKStringHelper, TKWindowHelper } from '@thinkive/tk-harmony-base';
import { ItemListBean, RecognizeResultData, RecognizerType, VpuMoreCardPicPreKV } from 'hh_card_recognize';
import { TKIDCardScanner, OnRecognizeListener, RecognizeResult, DetectorRect } from '../TKIDCardScanner';
import { BusinessError } from '@ohos.base';
import { TKFileUtils } from '../../../../utils/TKFileUtils';
import { image } from '@kit.ImageKit';
import { CameraPreviewSize } from '../../../../components/camera/common/constants/TKCameraConstants';
import  ClickUtils  from '../../../../utils/TKClickUtils';
import { util } from '@kit.ArkTS';
import { DetectBorderResult } from 'hh_card_recognize/src/main/ets/components/mainpage/DetectBorderResult';

export default class TKIntsigIDCardScanner implements TKIDCardScanner {

  // SDK授权key
  private appkey: string = 'newAuth2472fe1657d82d7e'  // 测试授权
  
  // 边框最小边距
  private BORDER: number = 60;

  // OCR识别回调监听器
  private listener?: OnRecognizeListener;
  
  // 文件存储目录
  private filesDir?: string;
  
  // 是否开始识别标志
  private _start: boolean = false;
  
  // 连续匹配次数
  private continue_match_time: number = 0;
  
  // 检测区域
  private detectorRect?: DetectorRect;
  
  // 相机预览尺寸
  cameraPreviewSize: CameraPreviewSize = { width: 1280, height: 960 }
  //h5入参
  params?: Record<string, Object>;

  /**
   * 初始化OCR识别
   * @param context 上下文
   * @param _params 初始化参数
   */
  init(context: Context, _params?: Record<string, Object>): void {
    TKLog.debug(`[思迪合合OCR工具]:初始化`)

    if (TKStringHelper.isEmpty(this.appkey)) {
      this.listener?.onFailed? this.listener?.onFailed(-2, '授权失败') : undefined
      return
    }

    //对应固定的图片上传地址 internal://cache
    this.filesDir = context.cacheDir + "/ocr";
    this.params=_params;
    TKFileUtils.mkdir(this.filesDir)

    try {
      VpuMoreCardPicPreKV.initICCardRecognizer(this.appkey).then((status) => {
        TKLog.debug(`[思迪合合OCR工具]:初始化完成: status=${status}`)

        if (status == 0) {
          VpuMoreCardPicPreKV.setRecognizerType(RecognizerType.id_card);
          TKLog.debug(`[思迪合合OCR工具]:设置识别类型: RecognizerType=${RecognizerType.id_card}`)
          this.listener?.onInit? this.listener?.onInit() : undefined
        } else {
          this.listener?.onFailed? this.listener?.onFailed(-2, '授权失败' + `(${status})`) : undefined
        }
      }).catch((e: BusinessError) => {
        TKLog.error(`[思迪合合OCR工具]:初始化失败: e=${JSON.stringify(e)}`)
        this.listener?.onFailed? this.listener?.onFailed(-2, '授权失败') : undefined
      });
    } catch (error) {
      TKLog.error(`[思迪合合OCR工具]:初始化异常: e=${JSON.stringify(error)}`)
      this.listener?.onFailed? this.listener?.onFailed(-2, '授权失败') : undefined
    }
  }

  /**
   * 拍照
   * @param surfaceID 预览Surface ID
   * @param rotate 是否需要旋转
   */
  async takePhone(surfaceID: string,rotate:number) {
    try {
      TKLog.debug(`[思迪合合OCR工具]:开始拍照: surfaceID=${surfaceID}, rotate=${rotate}`)
      let pixelMap: PixelMap = await this.createPixelMap(surfaceID, 0, 0, this.cameraPreviewSize?.width, this.cameraPreviewSize?.height)

      if (pixelMap) {
        if (!rotate) {
          pixelMap.rotateSync(90)
        }
        let imagePackerApi = image.createImagePacker();
        let packOpts: image.PackingOption = { format: "image/jpeg", quality: 98 };
        imagePackerApi.packing(pixelMap, packOpts).then((data_) => {
         let base64:string = "data:image/jpg;base64" + "," + new util.Base64Helper().encodeToStringSync(new Uint8Array(data_))
         TKLog.debug(`[思迪合合OCR工具]:拍照成功`)
         this.listener?.onTakePhoneSuccess ? this.listener?.onTakePhoneSuccess(pixelMap,base64) : undefined
        }).catch((error: BusinessError) => {
          TKLog.error(`[思迪合合OCR工具]:图片打包失败: ${error}`)
        });
      } else {
        TKLog.warn(`[思迪合合OCR工具]:拍照失败`)
        this.listener?.onScannerTips ? this.listener?.onScannerTips('拍照失败，请重试') : undefined
      }
    } catch (err) {
      TKLog.error(`[思迪合合OCR工具]:创建PixelMap失败: ${err.message}`)
    }
  }

  /**
   * 从Surface创建PixelMap
   */
   createPixelMap(surfaceId: string, x: number, y: number, width: number, height: number): Promise<PixelMap> {
    let region: image.Region = {
      x: x,
      y: y,
      size: { width: width, height: height }
    };
    return image.createPixelMapFromSurface(surfaceId, region)
  }

  /**
   * 设置检测区域
   * @param cameraPreviewSize 相机预览尺寸
   */
  setDetectorRect(cameraPreviewSize:CameraPreviewSize): void {
   this.cameraPreviewSize = cameraPreviewSize
    //设置设置检测区域为手机屏幕可视范围
    TKWindowHelper.getLastWindow(getContext(this)).then((win) => {
      let windowWidth = win.getWindowProperties().windowRect.width;
      let windowHeight = win.getWindowProperties().windowRect.height;
      let r = this.cameraPreviewSize?.width / windowHeight;
      let preViewH = windowWidth * r;
      let left: number = 0;
      let top: number = (this.cameraPreviewSize?.height - preViewH) / 2;
      let width: number = this.cameraPreviewSize?.width;
      let height: number = preViewH;
      this.detectorRect = { left: left, top: top, right: width + left, bottom: height + top};
      TKLog.debug(`[思迪合合OCR工具]:设置检测区域: rect=${JSON.stringify(this.detectorRect)}`)
    })
  }

  /**
   * 设置识别监听器
   */
  setListener(listener?: OnRecognizeListener): void {
    this.listener = listener;
  }

  /**
   * 开始识别
   */
  start(): void {
    this._start = true;
    TKLog.debug(`[思迪合合OCR工具]:开始识别`)
  }

  /**
   * 是否使用SDK相机
   */
  isUseSDKCramera(): boolean {
    return false
  }

  /**
   * 停止识别
   */
  stop(): void {
    this._start = false;
    TKLog.debug(`[思迪合合OCR工具]:停止识别`)
  }

  /**
   * 识别PixelMap图片
   * @param pixelMap 图片数据
   * @param imgType 图片类型 4:人像面 5:国徽面
   */
   recognizePixelMap(pixelMap: image.PixelMap, imgType: string) {
     VpuMoreCardPicPreKV.recognizeCardPixelMap(pixelMap, this.filesDir, this.BORDER).then((result: string) => {
       TKLog.debug(`[思迪合合OCR工具]:识别图片: result=${result}`)
       if (result && result.length > 0) {
         let obj = this._checkResult(result)
         TKLog.debug(`[思迪合合OCR工具]:识别图片: obj=${JSON.stringify(obj)}`)
         if (obj) {
           // if (imgType == "4" && !obj.isFront) {
           //   this.listener?.onScannerPreTips ? this.listener?.onScannerPreTips('请识别身份证人像面') : undefined
           // } else if (imgType == "5" && obj.isFront) {
           //   this.listener?.onScannerPreTips ? this.listener?.onScannerPreTips('请识别身份证国徽面') : undefined
           // } else {
             this.listener?.onCompleted ? this.listener?.onCompleted(obj) : undefined
           // }
         } else {
           this.listener?.onScannerPreTips ? this.listener?.onScannerPreTips('未识别到身份证图像') : undefined
         }
         return
       } else {
         this.listener?.onScannerPreTips ? this.listener?.onScannerPreTips('未识别到身份证图像') : undefined
       }
     });
  }

  /**
   * 输入图像数据进行识别
   * @param data YUV图像数据
   * @param width 图像宽度
   * @param height 图像高度
   */
  inputData(data: ArrayBuffer, width: number, height: number): void {
    if (!this._start || !data || data.byteLength <= 0) {
      return
    }

    if (ClickUtils.clickTooFast('inputDataAction', 300)) {
      return;
    }

    //1、检测证件位置
    const border = new Array<number>(8);
    let code: DetectBorderResult = VpuMoreCardPicPreKV.detectBorderYUV(data, width, height);
    
    // code大于0检测到边
    if (code.result >= 0) {
      // 2、过滤太贴边数据
      let isDif: boolean = true;
      let dif: number = this.BORDER; //最小边距
      for (let i = 0; i < border.length; i++) {
        if (this.detectorRect) { //设置检测范围
          if (i % 2 == 0) { //x 水平方向范围
            if (border[i] < this.detectorRect.left + dif || border[i] > this.detectorRect.right - dif) {
              isDif = false;
              break
            }
          } else { //y 竖直方向范围
            if (border[i] < this.detectorRect.top + dif || border[i] > this.detectorRect.bottom - dif) {
              isDif = false;
              break
            }
          }
        } else { //没有设置检测范围，按照原图宽高检测
          if (border[i] < dif || border[i] + dif > (i % 2 == 0 ? width : height)) {
            isDif = false;
            break
          }
        }
      }
      TKLog.debug(`[思迪合合OCR工具]:过滤太贴边数据: isDif=${isDif}， border=${JSON.stringify(border)}`)
      //连续3次检测通过，降低灵敏度，优化图片模糊问题
      if (!isDif) {
        this.continue_match_time = 0; //检测不通过重新计数
      } else if (++this.continue_match_time >= 3) { 
        this.continue_match_time = 0;
        let expand_pix: number = this.BORDER;
        //裁边支持h5控制
        if (TKStringHelper.isNotEmpty(this.params?.cardBorderWidth as  string)) {
          expand_pix=Number.parseInt(this.params?.cardBorderWidth as  string)
        }
        
        //3、识别信息
        VpuMoreCardPicPreKV.recognizeCardYUV(data, width, height, this.filesDir, expand_pix).then((result) => {
          TKLog.debug(`[思迪合合OCR工具]:识别图片: result=${result}`)
          let obj = this._checkResult(result)
          TKLog.debug(`[思迪合合OCR工具]:识别图片: obj=${JSON.stringify(obj)}`)
          if (obj) {
            this.listener?.onCompleted ? this.listener?.onCompleted(obj) : undefined
            return
          } else {
            this.listener?.onScannerPreTips ? this.listener?.onScannerPreTips('未识别到身份证图像') : undefined
          }
        });
      }
    }
  }

  /**
   * 检查识别结果
   * @param result 识别结果JSON字符串
   * @returns 解析后的识别结果对象
   */
  private _checkResult(result: string): RecognizeResult | undefined {
    let recognizeResultData: RecognizeResultData = JSON.parse(result);
    let item_list: Array<ItemListBean> = recognizeResultData.item_list;
    let sum = item_list.length;

    if(sum > 0){
      let obj: RecognizeResult = new RecognizeResult();
      obj.originalImagePath = recognizeResultData.originalImagePath;
      obj.cropImagePath = recognizeResultData.cropImagePath;
      for (let i = 0; i < sum; i++) {
        let itemListBean: ItemListBean = item_list[i];
        let key: string = itemListBean.key;
        let value: string = itemListBean.value;
        let description: string = itemListBean.description;
        TKLog.debug(`[思迪合合OCR工具]:checkResult: description=${description}, key=${key}, value=${value}`)
        if(value){
          if (key == 'name') { //姓名
            obj.name = value;
          } else  if (key == 'sex') { //性别
            obj.sex = value;
          } else  if (key == 'nationality') { //民族
            obj.nationality = value;
          } else  if (key == 'address') { //地址
            obj.address = value;
          } else  if (key == 'id_number') { //身份证号码
            obj.idNo = value;
          } else  if (key == 'birth') { //出生
            obj.birthday = value;
          } else  if (key == 'avatar_path') { //头像路径
            obj.avatarPath = value;
          } else  if (key == 'id_number_path') { //身份证号码路径
            obj.idNoPath = value;
          } else  if (key == 'issue_authority') { //签发机构
            obj.issueAuthority = value;
          } else  if (key == 'validate_date') { //有效期限
            obj.validateDate = value;
          } else  if (key == 'isFront') { //是否人像面
            obj.isFront = value.toString() == 'true'
          } else  if (key == 'head_portrait') { //是否人像面
            obj.isFront = true
          }
        }
      }
      //身份证正面识别8项信息，身份证反面识别2项信息
      if (obj.isFront && obj.name && obj.sex && obj.nationality && obj.address && obj.idNo && obj.birthday && obj.cropImagePath && obj.originalImagePath) {
        return obj;
      } else if(!obj.isFront && obj.issueAuthority && obj.validateDate && obj.cropImagePath && obj.originalImagePath) {
        return obj;
      }
    }
    return undefined;
  }

  /**
   * 释放资源
   */
  release(): void {
    this.stop();
    TKLog.debug(`[思迪合合OCR工具]:释放资源`)
    this.listener = undefined;
    //释放模型资源
    VpuMoreCardPicPreKV.releaseMemory();
  }
}
