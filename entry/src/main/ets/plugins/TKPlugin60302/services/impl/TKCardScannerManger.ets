import { TKIDCardScanner } from '../TKIDCardScanner';
import { TKLog } from '@thinkive/tk-harmony-base';
import TKJJCFIntsigIDCardScanner from './TKJJCFIntsigIDCardScanner';

/**
 * 身份证扫描管理类
 * 用于获取不同类型的身份证扫描器实例
 */
export class TKCardScannerManger {

  /**
   * 获取身份证扫描器实例
   * @param scannerType 扫描器类型 0:思迪扫描器 1:易道扫描器
   * @returns 身份证扫描器实例
   */
  static getCardScanner(scannerType: string): TKIDCardScanner {
    TKLog.debug(`[身份证扫描管理]:获取扫描器实例,类型:${scannerType}`);

    TKLog.debug("[身份证扫描管理]:使用思迪扫描器");
    return new TKJJCFIntsigIDCardScanner();
  }
}
