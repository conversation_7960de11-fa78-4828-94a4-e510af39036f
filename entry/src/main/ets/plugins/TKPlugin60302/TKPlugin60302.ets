import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON>per,
  TKLoadingDialogOption,
  T<PERSON><PERSON><PERSON>, TK<PERSON>apHelper,
  TKNoPermissionTip,
  TKNormalDialogOption,
  TKPermission,
  TKPermissionHelper,
  TKPermissionsRequestResult,
  TKPhotoSelectOptions,
  TKPickerHelper,
  TKResultVO, T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  T<PERSON><PERSON><PERSON>el<PERSON> } from '@thinkive/tk-harmony-base'
import { abilityAccessCtrl, common } from '@kit.AbilityKit';
import { promptAction } from '@kit.ArkUI';
import ClickUtils from '../../utils/TKClickUtils';
import { BusinessError } from '@kit.BasicServicesKit';
import { photoAccessHelper } from '@kit.MediaLibraryKit';
import TKImageUtil from '../../utils/TKImageUtil';
import { image } from '@kit.ImageKit';
import { util } from '@kit.ArkTS';
import { RecognizeR<PERSON>ult, TKIDCardScanner } from './services/TKIDCardScanner';
import { TKCardScannerManger } from './services/impl/TKCardScannerManger';
import { TKFileUtils } from '../../utils/TKFileUtils';
import TKStringUtil from '../../utils/TKStringUtil';
import { TKCommonAlertDialog } from '../../components/TKCommonAlertDialog';

import('./pages/TKJJCFOpenIDScannerPhotoPage') // 引入共享包中的命名路由页面

/**
 * 插件: 身份证扫描
 * 用于扫描身份证并返回结果
 */
export class TKPlugin60302 extends TKBasePlugin {
  // 身份证扫描器
  private idCardScanner: TKIDCardScanner = TKCardScannerManger.getCardScanner("");
  // 识别弹窗
  private dialog: TKComponentContent<TKLoadingDialogOption> = TKDialogHelper.createLoadingDialog({
    tip: '识别中...'
  })

  /**
   * 服务调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info("[思迪60302插件]:开始调用身份证扫描插件")
    let resultVO: TKResultVO = new TKResultVO();

    // 防止重复点击
    if (ClickUtils.clickTooFast('60302', 2000)) {
      TKLog.info("[TKPlugin60302]:重复调用" )
      resultVO.errorNo = 1;
      resultVO.errorInfo = "重复调用"
      return resultVO;
    }
    TKLog.debug(`[思迪60302插件]:传入参数: ${JSON.stringify(param)}`)

    if (TKMapHelper.getString(param, 'ocrType') == '2') {
      this.initScanner(param)

    } else {

      // 请求相机权限
      TKPermissionHelper.requestPermissionFromUser(
        TKPermission.CAMERA,
        getContext(this) as common.UIAbilityContext)
        .then((grantStatus) => {
          for (let i = 0; i < 2; i++) {
            if (i == 0 && grantStatus != abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
              TKLog.error("[思迪60302插件]:相机权限未授予")
              // promptAction.showToast({message:TKNoPermissionTip.CAMERA_TIP})
              // 没有权限
              TKDialogHelper.showAlertDialog({
                title: "温馨提示",
                message: TKNoPermissionTip.CAMERA_TIP,
                confirmText: "去授权",
                cancelText: "暂不授权",
                confirm: () => {
                  TKPermissionHelper.toAppSetting();
                }
              } as TKNormalDialogOption);
              return
            }
            continue;
          }
          TKLog.info("[思迪60302插件]:相机权限已授予,开始跳转扫描页面")
          this.goPick(param)
        })
    }

    return resultVO
  }

  /**
   * 跳转到身份证扫描页面
   * @param content 页面参数
   */
  goPick(content: Record<string, Object>) {
    TKLog.debug(`[思迪60302插件]:跳转参数: ${JSON.stringify(content)}`)

    TKRouterHelper.pushNamedRoute({
      name: 'TKJJCFOpenIDScannerPhotoPage',
      params: content,
      pageStyle: {
        layoutFullScreen: true,
        systemBarProperties: {
          statusBarContentColor: '#ffffff',
          statusBarColor: '#00000000'
        },
        systemBarEnable:  [TKBarName.status, TKBarName.navigationIndicator],
      },
      onResult: (data) => {
        data.funcNo = '60303'
        data.errorNo = data.error_no  // 兼容部分项目
        data.errorInfo = data.error_info // 兼容部分项目

        // 晋金财富定制逻辑
        if (content.paramExt) data.paramExt = content.paramExt

        data.flag = data.isFront ? '1' : '0'
        if (data.ethnicName) data.nation = data.ethnicName
        if (data.idNo) data.IDNo = data.idNo
        if (data.native) data.address = data.native
        if (data.userSex) data.sex = data.userSex
        if (data.custName) data.name = data.custName
        if (data.frontBase64) data.base64 = data.frontBase64

        if (data.backBase64) data.base64 = data.backBase64
        if (data.policeOrg) data.authority = data.policeOrg
        if (data.idbeginDate && data.idendDate) data.validity = TKStringHelper.replace(data.idbeginDate as string, '\\-', '.')+ '-' + TKStringHelper.replace(data.idendDate as string, '\\-', '.')

        if (data.base64) data.base64 = TKStringHelper.replace(data.base64 as string, 'data:image/jpg;base64,', '')

        TKLog.info(`[思迪60302插件]:扫描完成,返回结果: ${JSON.stringify(data)}`)
        this.harmonyCallPluginCallBack(data)
      },
      navComponentOrPathStack: this.navComponentOrPathStack
    })
  }

  /**
   * 打开相册选择照片
   */
  openPhotoAlbumAction(param: Record<string, Object>) {
    TKLog.info(`[思迪身份证OCR页面]:打开相册`)

    // 设置推荐类型为身份证
    let recommendationType:photoAccessHelper.RecommendationType = photoAccessHelper.RecommendationType.ID_CARD
    let recommendOptions: photoAccessHelper.RecommendationOptions = {
      recommendationType: recommendationType
    }

    // 相册选择配置
    let options: TKPhotoSelectOptions = {
      // MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE,  //可选择的媒体文件类型，若无此参数，则默认为图片和视频类型。
      maxSelectNumber: 1,
      // isPhotoTakingSupported = true, //支持拍照。
      // isEditSupported = true, //支持编辑照片。
      // isSearchSupported = true, //支持搜索。
      recommendationOptions: recommendOptions,
      // preselectedUris?: Array<string>; //预选择图片的uri数据。
    }

    // 选择照片
    TKPickerHelper.selectPhoto(options).then(async (uris) => {
      if (uris.length > 0) {
        let uri = uris[0]
        TKLog.debug(`[思迪身份证OCR页面]:选择照片URI:${uri}`)

        this.showLoading()

        //先压缩分辨率再压缩质量
        let pixelMap = await TKImageUtil.createPixelMapByUri(uri)
        if (pixelMap) {

          //识别图片
          let imagePackerApi = image.createImagePacker();
          let packOpts: image.PackingOption = { format: "image/jpeg", quality: 98 };
          imagePackerApi.packing(pixelMap, packOpts).then((data_) => {
            TKLog.debug('[思迪身份证OCR页面]:照片压缩完成')

            // setTimeout(() => {
              this.idCardScanner?.recognizePixelMap(pixelMap, TKMapHelper.getString(param, 'imgType')) // 图片类型 4:人像面 5:国徽面
            // }, 500)
          }).catch((error: BusinessError) => {
            TKLog.error("[思迪身份证OCR页面]:照片压缩失败:" + error)
            this.hideLoading()
          });
        } else {
          TKLog.error("[思迪身份证OCR页面]:照片压缩失败(没有pixelMap)")
          this.hideLoading()
        }
      } else {

        this.postWitnessResultToH5('-1', "用户取消")
      }
    })
  }

  initScanner(param: Record<string, Object>) {
    // 初始化身份证扫描器
    this.idCardScanner = TKCardScannerManger.getCardScanner("")
    this.idCardScanner.setListener({
      onInit: () => {
        TKLog.debug(`[思迪身份证OCR页面]:扫描器初始化成功`)
        this.idCardScanner?.start()

        // 相册识别
        this.openPhotoAlbumAction(param)
      },
      onFailed: (errorNo: number, errorInfo: string) => {
        TKLog.error(`[思迪身份证OCR页面]:扫描失败:errorNo = ${errorNo}, errorInfo = ${errorInfo}`)
        this.postWitnessResultToH5(errorNo + '', errorInfo)
      },
      onScannerPreTips: (errorInfo: string) => {
        TKLog.debug(`[思迪身份证OCR页面]:预扫描提示:${errorInfo}`)

        this.idCardScanner?.stop();
        this.idCardScanner?.release();

        this.postWitnessResultToH5('-1', `${errorInfo}`)
        this.hideLoading()
      },
      onCompleted: async (result: RecognizeResult) => {
        TKLog.info("[思迪身份证OCR页面]:识别完成:" + JSON.stringify(result))

        // 处理裁剪后的图片
        if (result.cropImagePath) {
          this.idCardScanner?.stop();
          this.idCardScanner?.release();

          const content = await TKFileUtils.readContentUint8Array(result.cropImagePath);
          let picSize: string = TKStringHelper.isNotEmpty(TKMapHelper.getString(param, 'compressSize')) ? TKMapHelper.getString(param, 'compressSize') : '200'
          if (content) {
            let size: number = Number.parseInt(picSize)
            let buf = await TKImageUtil.qualityCompress(content.buffer as ArrayBuffer, size)
            if (buf) {
              const base64 = content ? TKStringUtil.uint8ArrayToBase64(new Uint8Array(buf)) : "";

              let data = {} as Record<string, Object>
              data.funcNo = '60303'
              data.errorNo = '0'
              data.errorInfo = '识别成功'
              data.errorNo = data.error_no  // 兼容部分项目
              data.errorInfo = data.error_info // 兼容部分项目

              // 晋金财富定制逻辑
              if (param.paramExt) data.paramExt = param.paramExt

              data.flag = result.isFront ? '1' : '0'
              if (result.nationality) data.nation = result.nationality
              if (result.idNo) data.IDNo = result.idNo
              if (result.address) data.address = result.address
              if (result.sex) data.sex = result.sex
              if (result.name) data.name = result.name
              if (base64) data.base64 = base64

              if (result.issueAuthority) data.authority = result.issueAuthority
              if (result.validateDate) data.validity = result.validateDate

              TKLog.info(`[思迪60302插件]:扫描完成,返回结果: ${JSON.stringify(data)}`)
              this.harmonyCallPluginCallBack(data)
            } else {
              TKLog.info(`[思迪60302插件]:图片压缩失败`)
            }
          }
        } else {
          this.postWitnessResultToH5('-1', '识别失败(图片无效)')
        }

        this.hideLoading()
      }
    })
    this.idCardScanner.init(getContext(this),(param as Record<string, Object>))
  }

  /**
   * 向H5发送结果
   * @param error_no 错误码
   * @param error_info 错误信息
   */
  postWitnessResultToH5(error_no: string, error_info: string) {
    this.hideLoading()

    let data = {} as Record<string, string>

    if (error_no) {
      data.error_no = error_no
    }
    if (error_info) {
      data.error_info = error_info
    }

    data.funcNo = '60303'
    data.errorNo = data.error_no  // 兼容部分项目
    data.errorInfo = data.error_info // 兼容部分项目

    TKLog.info(`[思迪60302插件]:扫描完成,返回结果: ${JSON.stringify(data)}`)
    this.harmonyCallPluginCallBack(data)
  }

  showLoading() {
    this.dialog = TKDialogHelper.createLoadingDialog({
      tip: '识别中...'
    })
    this.dialog.open()
  }

  hideLoading() {
    this.dialog.close()
  }
}