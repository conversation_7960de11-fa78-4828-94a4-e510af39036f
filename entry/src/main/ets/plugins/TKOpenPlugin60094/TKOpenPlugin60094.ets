import { TKBasePlugin, TKLog, TKResultVO} from '@thinkive/tk-harmony-base'
import { emitter } from '@kit.BasicServicesKit';
import { TKOpenConstant } from  '../../constant/TKOpenConstant';


/**
 * 插件: H5通知开户SDK外部模块事件
 * 主要功能:
 * 1. 接收H5传入的参数
 * 2. 发送事件通知给外部模块
 * 3. 同步返回处理结果
 */
export class TKOpenPlugin60094 extends TKBasePlugin {

  /**
   * 插件调用入口
   * @param param 调用参数,将作为事件内容传递给外部模块
   * @returns TKResultVO 返回结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.debug('[思迪60094插件]开始处理H5通知事件')
    let resultVO: TKResultVO = new TKResultVO()

    // 获取事件名称(隐私模式通知)
    let externalRadioName:string = TKOpenConstant.TK_WINDOW_PRIVACY_MODE_NOTIFICATION;
    TKLog.debug(`[思迪60094插件]事件名称为:${externalRadioName}`)

    // 构建事件数据对象
    let eventData: emitter.EventData = {
      data: {
        params: param
      }
    };
    TKLog.debug(`[思迪60094插件]事件参数为:${JSON.stringify(param)}`)

    // 发送事件名为externalRadioName的事件，事件内容为eventData
    emitter.emit(externalRadioName, eventData);
    TKLog.debug(`[思迪60094插件]事件发送成功`)

    // 同步返回结果
    return resultVO
  }

}