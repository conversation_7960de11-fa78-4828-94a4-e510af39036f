import { TKBasePlugin, TKLog, TKPasteboardHelper, TKResultVO } from '@thinkive/tk-harmony-base';
import { BusinessError, pasteboard } from '@kit.BasicServicesKit';


/**
 * 插件: 读取系统剪贴板内容
 * 主要功能:
 * 1. 防重复调用,2秒内不允许重复调用
 * 2. 读取系统剪贴板内容
 * 3. 异步回调剪贴板内容给H5
 */
export class TKPlugin80302 extends TKBasePlugin {
  /**
   * 插件调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.debug('[思迪80302插件]开始读取剪贴板内容')
    let resultVO: TKResultVO = new TKResultVO()

    // 读取系统剪贴板内容，使用Promise异步回调
    let text = TKPasteboardHelper.readDataContent()
    TKLog.debug('[思迪80302插件]获取到的text = ' + text);

    // 构建返回结果对象
    let resultMap: Record<string, Object> = {};
    resultMap.content = text // 剪贴板内容
    resultMap.copydata = text// 适配晋金财富
    resultMap.errorInfo = '调用成功'// 适配晋金财富
    resultVO.results = resultMap

    // 构建回调结果对象
    // resultVO.error_no = '0'
    // resultVO.error_info = '调用成功'
    // resultVO.content = text // 剪贴板内容
    // resultVO.results = [{
    //   'copydata' : text
    // } as Record<string, string>] // 适配晋金财富
    // resultVO.errorNo = resultVO.error_no  // 兼容部分项目
    // resultVO.errorInfo = resultVO.error_info // 兼容部分项目

    // 回调结果给H5
    TKLog.debug('[思迪80302插件]回调结果:' + JSON.stringify(resultVO))

    // 同步返回结果
    return resultVO
  }
}