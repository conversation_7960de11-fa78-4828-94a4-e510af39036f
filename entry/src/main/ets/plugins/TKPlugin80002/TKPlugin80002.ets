import { TKBasePlugin, TKLog, TKResultVO } from '@thinkive/tk-harmony-base';


/**
 * 插件: 银行卡扫描
 * 用于扫描银行卡并识别卡号等信息
 */
export class TKPlugin80002 extends TKBasePlugin {
  /**
   * 插件调用入口
   * @param param 调用参数
   * @returns 调用结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info("[思迪80002插件]:开始调用银行卡扫描插件")

    let resultVO: TKResultVO = new TKResultVO();

    // 防止短时间内重复调用
    if (!param) {
      TKLog.info("[思迪80002插件]:参数为空")
      resultVO.errorNo = -8000201;
      resultVO.errorInfo = "参数不能为空"
      return resultVO
    }

    this.harmonyCallPluginCallBack({
      funcNo: '80003',
      param: param ?? {} as Record<string, Object>
    })

    return resultVO
  }
}