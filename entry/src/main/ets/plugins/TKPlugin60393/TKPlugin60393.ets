import { TKBasePlugin, T<PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TKR<PERSON>ultVO } from '@thinkive/tk-harmony-base';
import ClickUtils from '../../utils/TKClickUtils';
import { BusinessError } from '@kit.BasicServicesKit';


/**
 * 插件: 保存图片到相册
 * 功能: 将base64格式的图片保存到系统相册中
 */
export class TKPlugin60393 extends TKBasePlugin {
  /**
   * 服务调用入口
   * @param param 传入的参数对象
   * @returns TKResultVO 返回结果对象
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    TKLog.info('[思迪60393插件]:开始执行保存图片到相册')

    // 返回结果对象
    let resultVO: TKResultVO = new TKResultVO();

    // 防止重复点击
    if (ClickUtils.clickTooFast('60393', 2000)) {
      TKLog.info("[思迪60393插件]::重复调用,忽略本次请求")
      resultVO.errorNo = 1;
      resultVO.errorInfo = "重复调用"
      return resultVO;
    }

    // 获取传入的base64参数
    let base64: string = param.base64Image as string
    // base64 = '123,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'

    // 校验base64参数是否为空
    if (!base64 || base64.length == 0) {
      resultVO.errorNo = -6039301;
      resultVO.errorInfo = 'base64不能为空!'
      return resultVO;
    }

    // 校验base64格式是否正确
    let arr: string[] = base64.split(",")
    if (arr.length < 2) {
      resultVO.errorNo = -6039302;
      resultVO.errorInfo = 'base64数据异常!'
      return resultVO;
    }

    // 请求写入相册权限
    // API 11
    // TKLog.debug('[思迪60393插件]:请求写入相册权限')
    // TKPermissionHelper.requestPermissionFromUser(
    //   TKPermission.WRITE_IMAGEVIDEO,
    //   getContext(this) as common.UIAbilityContext).then(status => {
    //
    //   if (status == abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
    //     TKLog.info('[思迪60393插件]:获得写入相册权限,开始保存图片')
    //     // 获取base64图片内容
    //     let imageBase64 = arr[1]
    //     // 保存图片到相册
    //     TKPickerHelper.savePhotoToGallery(imageBase64);
    //   } else {
    //     TKLog.warn('[思迪60393插件]:无相册权限,提示用户开启')
    //     promptAction.showToast({ message: '无法访问相册,请在 设置-隐私-照片 打开权限.' });
    //   }
    // }).catch((error: BusinessError) => {
    //   TKLog.error(`[思迪60393插件]:保存图片失败, code为${error.code}, msg为${error.message}`)
    // })

    // API 12
    let imageBase64 = arr[1]
    // savePhotoToGallery(imageBase64)
    TKPickerHelper.savePhotoToGallery(imageBase64)
      .then(() => {
        this.harmonyCallPluginCallBack({
          funcNo: '60393',
          error_no: '0',
          error_info: ``,
        })
      })
      .catch((error: BusinessError) => {
      this.harmonyCallPluginCallBack({
        funcNo: '60393',
        error_no: '-3',
        error_info: `图片保存失败(${error.code})`,
      })
    })

    // 同步返回结果
    TKLog.info('[思迪60393插件]:保存图片到相册执行完成')
    return resultVO
  }
}