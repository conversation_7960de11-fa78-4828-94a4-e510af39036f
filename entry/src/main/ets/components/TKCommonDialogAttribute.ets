import { TKStyleAttribute, TKThemeManager } from '@thinkive/tk-harmony-base';

/**
 * 通用弹窗属性类
 * 用于配置弹窗的样式和内容属性
 */
export class TKCommonDialogAttribute extends TKStyleAttribute {
  /**
   * 主题配置名称
   */
  themeConfigName: string = 'TKOpenConfig';

  /**
   * 主题颜色,从主题管理器获取
   */
  themeColor: string = TKThemeManager.shareInstance().getCssRulesetByClassName(this.themeConfigName).fontColor as string;

  /**
   * 字体颜色,默认使用主题颜色
   */
  fontColor?: string = this.themeColor;

  /**
   * 弹窗图标资源
   */
  image?: Resource = TKThemeManager.shareInstance().getCssRulesetByClassName(this.themeConfigName).image;

  /**
   * 弹窗内容文本
   */
  content?: string = "您正在退出排队，确认要退出排队吗？";

  /**
   * 弹窗标题文本
   */
  title?: string = "确定退出排队吗？";

  /**
   * 是否显示标题
   */
  isShowTitle?: Boolean = true;

  /**
   * 左侧按钮文本
   */
  leftTxt?: string = "确认退出";

  /**
   * 是否显示左侧按钮
   */
  isShowLeftBtn?: Boolean = true;

  /**
   * 右侧按钮文本
   */
  rightTxt?: string = "继续等待";

  /**
   * 是否显示右侧按钮
   */
  isShowRightBtn?: Boolean = true;

  /**
   * 是否显示标题图标
   */
  isNeedTitleLogo?: Boolean = false;

  /**
   * 弹窗类型,用于区分不同的回调事件
   */
  dialogType: string = "";

  /**
   * 是否横屏显示弹窗
   */
  isHor?: Boolean = false;
}