/**
 * 通用弹窗组件
 * 支持自定义标题、内容、按钮文案和样式等
 */
import { TKCommonDialogAttribute } from './TKCommonDialogAttribute'

@Preview
@CustomDialog
@Component
export struct TKCommonAlertDialog {
  // 弹窗内容
  @State content?: string = "您正在退出排队，确认要退出排队吗？"
  // 弹窗标题
  @State title?: string = "确定退出排队吗？"
  // 是否显示标题
  @State isShowTitle?: Boolean = true
  // 左侧按钮文案
  @State leftTxt?: string = "确认退出"
  // 是否显示左侧按钮
  @State isShowLeftBtn?: Boolean = true
  // 右侧按钮文案
  @State rightTxt?: string = "继续等待"
  // 是否显示右侧按钮
  @State isShowRightBtn?: Boolean = true
  // 是否显示标题图标
  @State isNeedTitleLogo?: Boolean = false
  // 弹窗类型,用于区分回调事件
  @State dialogType: string = ""
  // 是否横屏显示
  @State isHor?: Boolean = false
  // 弹窗样式属性
  @State styleDialogAttribute:TKCommonDialogAttribute = new TKCommonDialogAttribute
  // 弹窗控制器
  controller?: CustomDialogController
  // 左侧按钮点击回调
  leftClick?: (dialogType : string) => void
  // 右侧按钮点击回调
  rightClik?: (dialogType : string) => void

  build() {
    Stack() {
      Column() {
        // 标题区域
        Text(){
          // 标题图标
          ImageSpan($r("app.media.tk_open_plugin_60026_point_warning"))
            .width(16)
            .height(16)
            .objectFit(ImageFit.Contain)
            .verticalAlign(ImageSpanAlignment.CENTER)
            .margin({right:5})
            .visibility(this.isNeedTitleLogo ? Visibility.Visible : Visibility.None)

          // 标题文本
          Span(this.title)
            .fontColor('#333333')
        }
          .fontSize(18)
          .fontColor("#333333")
          .visibility(this.isShowTitle ? Visibility.Visible : Visibility.None)

        // 内容区域
        Scroll() {
          Text(this.content)
            .fontColor("#666666")
            .fontSize(14)
            .margin({ top: 10, bottom: 10, left: 20, right: 20 })
            .constraintSize({ maxHeight: 200 })
        }

        // 按钮区域
        Row() {
          // 左侧按钮
          Column() {
            Stack() {
              Text(this.leftTxt)
                .fontSize(16)
                .width(120)
                .height(40)
                .borderRadius(30)
                .fontColor(this.styleDialogAttribute.fontColor ?? "#1061FF")
                .textAlign(TextAlign.Center)
                .align(Alignment.Center)
              Column()
                .backgroundColor(this.styleDialogAttribute.fontColor ?? "#1061FF")
                .opacity(0.1)
                .width(120)
                .height(40)
                .borderRadius(30)
            }
            .borderRadius(30)
            .onClick(() => {
              if (this.leftClick) {
                this.leftClick(this.dialogType)
              }
              this.controller?.close()
            })
          }
          .layoutWeight(1)
          .margin({ right: 5 })
          .visibility(this.isShowLeftBtn ? Visibility.Visible : Visibility.None)

          // 右侧按钮
          Column() {
            Text(this.rightTxt)
              .fontColor($r('sys.color.white'))
              .fontSize(16)
              .width(120)
              .backgroundColor(this.styleDialogAttribute.fontColor ?? 0x5A92FF)
              .linearGradient({
                direction: GradientDirection.Right,
                colors: [[this.styleDialogAttribute.fontColor ?? 0x1061FF, 0.3], [this.styleDialogAttribute.fontColor ?? 0x5A92FF, 1]]
              })
              .height(40)
              .borderRadius(30)
              .textAlign(TextAlign.Center)
              .align(Alignment.Center)
              .onClick(() => {
                if (this.rightClik) this.rightClik(this.dialogType)
                this.controller?.close()
              })
          }
          .layoutWeight(1)
          .margin({ left: 5 })
          .visibility(this.isShowRightBtn ? Visibility.Visible : Visibility.None)
        }
        .margin({ top: 10, left: 24, right: 24 })
        .height(40)
      }
      .padding({ top: 20, bottom: 20 })
    }
    .width(300)
    .borderRadius(8)
    .rotate({ angle: this.isHor ? 90 : 0 })
    .backgroundColor($r('sys.color.white'))
  }
}
