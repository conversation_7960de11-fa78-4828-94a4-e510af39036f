/**
 * 相机生命周期接口
 * 用于管理相机预览、拍照、录像等功能的回调
 */
export interface TKCameraLifecycle {
  /**
   * 相机预览开始时的回调
   * 在相机成功打开并开始预览时触发
   */
  onPreviewStart?(): void

  /**
   * 相机预览帧数据回调
   * @param byteBuffer 预览帧的原始数据buffer，可用于图像处理
   */
  onPreviewFrame?(byteBuffer: ArrayBuffer): void

  /**
   * 相机预览停止时的回调
   * 在相机预览结束时触发
   */
  onPreviewStop?(): void

  /**
   * 开始录制视频时的回调
   * 在调用开始录制视频接口后触发
   */
  onStartVideoRecord?(): void

  /**
   * 停止录制视频时的回调
   * @param videoPath 录制完成的视频文件路径
   */
  onStopVideoRecord?(videoPath: string): void

  /**
   * 拍照完成时的回调
   * @param data 拍照生成的图片数据buffer
   */
  onTakePhoto?(data: ArrayBuffer): void

  /**
   * 音频数据回调
   * @param data 采集到的音频数据buffer
   */
  onReadAudioData?(data: ArrayBuffer): void

  /**
   * 相机销毁时的回调
   * 用于清理资源和执行必要的销毁操作
   */
  onDestroy?(): void
}