import { TKLog } from '@thinkive/tk-harmony-base';

/**
 * 监听器管理基类
 * 用于管理特定类型监听器的添加、移除等操作
 */
export abstract class TKListenerManager<T> {
  /**
   * 存储监听器的数组
   */
  protected listeners: T[];

  /**
   * 构造函数
   * 初始化监听器数组
   */
  constructor() {
    this.listeners = new Array<T>();
  }

  /**
   * 添加监听器
   * @param listener 要添加的监听器实例
   */
  public addListener(listener: T): void {
    if (!listener) {
      TKLog.warn('[监听管理者]:添加的监听者为空');
      return;
    }

    if (this.listeners.indexOf(listener) >= 0) {
      TKLog.warn('[监听管理者]:监听者已存在');
      return;
    }

    TKLog.debug(`[监听管理者]:添加监听者 ${listener}`);
    this.listeners.push(listener);
  }

  /**
   * 移除指定监听器
   * @param listener 要移除的监听器实例
   */
  public removeListener(listener: T): void {
    if (!listener) {
      TKLog.warn('[监听管理者]:要移除的监听者为空');
      return;
    }

    const index = this.listeners.indexOf(listener);
    if (index < 0) {
      TKLog.warn('[监听管理者]:要移除的监听者不存在');
      return;
    }

    TKLog.debug(`[监听管理者]:移除监听者 ${listener}`);
    this.listeners.splice(index, 1);
  }

  /**
   * 移除所有监听器
   */
  public removeAllListener(): void {
    TKLog.debug(`[监听管理者]:移除所有监听者,当前数量:${this.listeners.length}`);
    this.listeners = [];
  }

  /**
   * 获取当前监听器数量
   * @returns 监听器数量
   */
  public getListenerCount(): number {
    return this.listeners.length;
  }
}