import { TKListenerManager } from './TKListenerManger';
import { TKCameraLifecycle } from './TKCameraLifecycle';
import { TKLog } from '@thinkive/tk-harmony-base';

/**
 * 相机生命周期代理类
 * 用于管理相机生命周期监听器,转发相机事件到所有监听器
 */
export default class TKCameraLifecycleProxy extends TKListenerManager<TKCameraLifecycle> {

  /**
   * 预览开始回调
   * 通知所有监听器相机预览已开始
   */
  onPreviewStart():void{
    TKLog.debug(`[相机生命周期]:相机预览开始,监听器数量:${this.listeners.length}`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onPreviewStart){
        lifecycle.onPreviewStart();
      }
    })
  }

  /**
   * 预览帧回调
   * 将预览帧数据转发给所有监听器
   * @param byteBuffer 预览帧数据
   */
  onPreviewFrame(byteBuffer: ArrayBuffer):void{
    // TKLog.debug(`[相机生命周期]:收到预览帧数据,数据大小:${byteBuffer.byteLength}字节`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onPreviewFrame){
        lifecycle.onPreviewFrame(byteBuffer);
      }
    })
  }

  /**
   * 预览停止回调
   * 通知所有监听器相机预览已停止
   */
  onPreviewStop():void{
    TKLog.debug(`[相机生命周期]:相机预览停止,监听器数量:${this.listeners.length}`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onPreviewStop){
        lifecycle.onPreviewStop();
      }
    })
  }

  /**
   * 开始录制视频回调
   * 通知所有监听器视频录制已开始
   */
  onStartVideoRecord():void{
    TKLog.debug(`[相机生命周期]:视频录制开始,监听器数量:${this.listeners.length}`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onStartVideoRecord){
        lifecycle.onStartVideoRecord();
      }
    })
  }

  /**
   * 停止录制视频回调
   * 通知所有监听器视频录制已停止,并传递录制文件路径
   * @param videoPath 录制视频的保存路径
   */
  onStopVideoRecord(videoPath:string | undefined):void{
    TKLog.debug(`[相机生命周期]:视频录制结束,视频路径:${videoPath},监听器数量:${this.listeners.length}`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onStopVideoRecord){
        lifecycle.onStopVideoRecord(videoPath);
      }
    })
  }

  /**
   * 拍照完成回调
   * 将拍照数据转发给所有监听器
   * @param data 拍照生成的图片数据
   */
  onTakePhoto(data: ArrayBuffer):void{
    TKLog.debug(`[相机生命周期]:拍照完成,照片大小:${data.byteLength}字节,监听器数量:${this.listeners.length}`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onTakePhoto){
        lifecycle.onTakePhoto(data);
      }
    })
  }

  /**
   * 音频数据回调
   * 将采集到的音频数据转发给所有监听器
   * @param data 音频数据
   */
  onReadAudioData(data: ArrayBuffer):void{
    // TKLog.debug(`[相机生命周期]:收到音频数据,数据大小:${data.byteLength}字节`)

    this.listeners.forEach((lifecycle)=>{
      if(lifecycle.onReadAudioData){
        lifecycle.onReadAudioData(data);
      }
    })
  }

  /**
   * 销毁回调
   * 通知所有监听器执行销毁操作
   */
  onDestroy():void{
    TKLog.debug(`[相机生命周期]:相机生命周期代理销毁,监听器数量:${this.listeners.length}`)

    this.listeners.forEach(async (lifecycle)=>{
      if(lifecycle.onDestroy){
        lifecycle.onDestroy();
      }
    })
  }
}