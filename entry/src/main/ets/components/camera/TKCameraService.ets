import camera from '@ohos.multimedia.camera';
import {
  CameraPosition,
  CameraPreviewSize,
  CaptureMode,
  CameraOutputOption
} from './common/constants/TKCameraConstants';
import image from '@ohos.multimedia.image';
import { AsyncCallback, BusinessError } from '@ohos.base';
import TKCameraLifecycleProxy from './lifecycle/TKCameraLifecycleProxy';
import { TKCameraLifecycle } from './lifecycle/TKCameraLifecycle';
import { TKLog } from '@thinkive/tk-harmony-base'
import TKImageUtil from '../../utils/TKImageUtil';
import { media } from '@kit.MediaKit';
import fs from '@ohos.file.fs';
import { display } from '@kit.ArkUI';
import audio from '@ohos.multimedia.audio';

/**
 * 摄像头服务类
 * 支持拍照、单向视频录制、活体检测抽帧
 */
export default class TKCameraService {
  // 单例实例
  private static instance: TKCameraService
  // 应用上下文
  private context?: Context;
  // 预览Surface ID
  private surfaceId?: string;
  // 摄像头位置
  private cameraPosition: CameraPosition = CameraPosition.BACK;
  // 相机管理器
  private cameraManager?: camera.CameraManager;
  // 相机输出能力
  private cameraOutputCapability?: camera.CameraOutputCapability;
  // 是否正在预览
  private isSessionPreview: boolean = false;
  // 捕获会话
  private captureSession?: camera.CaptureSession;
  // 相机生命周期代理
  private cameraLifecycleProxy?: TKCameraLifecycleProxy
  // 相机输入
  private cameraInput?: camera.CameraInput
  // 组件预览输出
  private componentSurfacePreviewOutput?: camera.PreviewOutput;
  // 图像接收预览输出
  private imageReceiverPreviewOutput?: camera.PreviewOutput;
  // 照片输出
  private photoOutput: camera.PhotoOutput | undefined = undefined;
  // 视频输出
  private videoOutput: camera.VideoOutput | undefined = undefined;
  // 音视频录制器
  private avRecorder: media.AVRecorder | undefined = undefined;
  // 预览尺寸
  private previewSize?: CameraPreviewSize;
  // 输出选项
  private outputOption?: CameraOutputOption = new CameraOutputOption();
  // 输出文件路径
  private outputFilePath?: string;
  // 异步回调
  private callback?: AsyncCallback<void>;
  // 图像接收器
  private mReceiver: image.ImageReceiver | undefined = undefined;
  // 音频采集选项
  private audioCapturerOptions: audio.AudioCapturerOptions | undefined = undefined;
  // 音频采集器
  private audioCapturer: audio.AudioCapturer | undefined = undefined;
  // 是否开启闪光灯
  private isFlashlight: boolean = false

  /**
   * 私有构造函数,初始化相机生命周期代理
   */
  private constructor() {
    TKLog.debug("[思迪相机服务]:构造函数初始化")
    this.cameraLifecycleProxy = new TKCameraLifecycleProxy()
  }

  getCaptureSession():camera.CaptureSession | undefined {
    return this.captureSession
  }

  /**
   * 获取TKCameraService单例实例
   * @returns TKCameraService实例
   */
  public static getInstance() {
    TKLog.debug("[思迪相机服务]:获取单例实例")
    if (TKCameraService.instance == null) {
      TKCameraService.instance = new TKCameraService()
    }
    return TKCameraService.instance
  }

  /**
   * 初始化相机服务
   * @param context 应用上下文
   * @param surfaceId 预览Surface ID
   * @param capturePosition 摄像头位置
   * @param previewSize 预览尺寸
   * @param outputOption 输出选项
   */
  public init(context?: Context, surfaceId?: string, capturePosition?: CameraPosition, previewSize?: CameraPreviewSize, outputOption?: CameraOutputOption) {
    TKLog.debug("[思迪相机服务]:初始化相机")
    this.context = context;
    this.surfaceId = surfaceId ?? this.surfaceId;
    this.cameraPosition = capturePosition ?? this.cameraPosition;
    this.cameraManager = camera.getCameraManager(context);
    this.previewSize = previewSize
    this.outputOption = outputOption ?? this.outputOption
    let camerasInfo = this.cameraManager.getSupportedCameras();
    TKLog.debug(`[思迪相机服务]:获取到${camerasInfo.length}个相机`)
    if (this.cameraPosition == CameraPosition.FRONT) {
      //折叠屏展开兼容
      let foldStatus = display.getFoldStatus()
      TKLog.debug(`[思迪相机服务]:当前折叠状态:${foldStatus}`)
      this.cameraPosition = CameraPosition.FRONT
    }
    this.configureCamera(this.cameraPosition, camerasInfo);
  }

  /**
   * 配置相机参数
   * @param cameraPosition 摄像头位置
   * @param camerasInfo 相机信息数组
   */
  private async configureCamera(cameraPosition: CameraPosition, camerasInfo: Array<camera.CameraDevice>) {
    TKLog.debug("[思迪相机服务]:设置相机参数")
    let targetCameraIndex = camerasInfo.findIndex((checkCamera) => {
      return checkCamera.cameraPosition.valueOf() === cameraPosition.valueOf();
    });
    if (!this.outputOption) {
      throw new Error("outputOption == null || undefined");
    }
    if (targetCameraIndex >= 0 && this.cameraManager) {
      this.cameraPosition = cameraPosition;
      let cameraDevice = camerasInfo[targetCameraIndex];
      TKLog.debug(`[思迪相机服务]:使用相机位置:${cameraDevice.cameraPosition}`)

      this.cameraOutputCapability = this.cameraManager.getSupportedOutputCapability(cameraDevice);
      if (this.cameraOutputCapability) {
        let previewProfiles: Array<camera.Profile> = this.cameraOutputCapability.previewProfiles;
        let previewProfileIndex = previewProfiles.findIndex((profile) => {
          return profile.size.width === this.previewSize?.width && profile.size.height === this.previewSize?.height;
        })
        let previewProfile = previewProfiles[previewProfileIndex];
        TKLog.debug(`[思迪相机服务]:预览配置:${JSON.stringify(previewProfile)}`)
        //创建预览输出流，预览流为XComponent组件提供的surfaceId
        this.componentSurfacePreviewOutput = this.cameraManager.createPreviewOutput(previewProfile, this.surfaceId);

        //创建双录预览输出流
        if (this.outputOption.outputType.indexOf(CaptureMode.PREVIEW_FRAME) > -1) {
          await this.createPreviewOutput(previewProfile);
        }

        // 创建拍照输出流
        if (this.outputOption.outputType.indexOf(CaptureMode.PHOTO) > -1) {
          await this.createPhotoOutput(previewProfile);
        }
        // 创建视频输出流
        if (this.outputOption.outputType.indexOf(CaptureMode.VIDEO) > -1) {
          await this.createVideoOutput();
        }

        //创建相机输入流
        this.cameraInput = this.cameraManager.createCameraInput(cameraDevice);
        TKLog.debug("[思迪相机服务]:准备打开相机输入")
        await this.cameraInput?.open()
        TKLog.debug("[思迪相机服务]:结束打开相机输入")
        this.captureSession = this.cameraManager.createCaptureSession();
        this.captureSession.beginConfig();
        this.captureSession.addInput(this.cameraInput);
        this.captureSession.addOutput(this.componentSurfacePreviewOutput);
        //添加双录预览输出流
        this.imageReceiverPreviewOutput ? this.captureSession.addOutput(this.imageReceiverPreviewOutput) : undefined;
        //添加拍照输出流
        this.photoOutput ? this.captureSession.addOutput(this.photoOutput) : undefined;
        //添加视频输出流
        this.videoOutput ? this.captureSession.addOutput(this.videoOutput) : undefined;
        await this.captureSession.commitConfig()
        await this.startPreview()
      }
    }
  }

  /**
   * 创建双录预览输出流
   * @param previewProfile 预览配置
   */
  private async createPreviewOutput(previewProfile: camera.Profile) {
    TKLog.debug("[思迪相机服务]:创建双录预览输出流")
    this.mReceiver = image.createImageReceiver(
      previewProfile.size.width,
      previewProfile.size.height,
      image.ImageFormat.JPEG,
      8
    );
    let receivingSurfaceId = await this.mReceiver.getReceivingSurfaceId();
    this.imageReceiverPreviewOutput = this.cameraManager?.createPreviewOutput(previewProfile, receivingSurfaceId);

    this.callback = async () => {
      if (this.mReceiver) {
        let imageData: undefined | image.Image = await this.mReceiver?.readNextImage();
        let imageJPEGComponent = await imageData?.getComponent(image.ComponentType.JPEG); //返回NV21格式
        if (imageJPEGComponent && imageJPEGComponent.byteBuffer && imageJPEGComponent.byteBuffer.byteLength > 0) {
          let tempByteBuffer:ArrayBuffer = imageJPEGComponent.byteBuffer.slice(0, imageJPEGComponent.byteBuffer.byteLength);
          // TKLog.debug(`[思迪相机服务]:预览帧大小:${tempByteBuffer.byteLength}`)
          this.cameraLifecycleProxy?.onPreviewFrame(tempByteBuffer);
        }
        await imageData?.release();
      }
    }
    this.mReceiver.on('imageArrival', this.callback);
  }

  /**
   * 创建拍照输出流
   * @param previewProfile 预览配置
   */
  private async createPhotoOutput(_: camera.Profile) {
    TKLog.debug("[思迪相机服务]:创建拍照输出流")
  }

  /**
   * 创建视频输出流
   */
  private async createVideoOutput() {
    TKLog.debug("[思迪相机服务]:创建视频输出流")

    let videoProfiles: Array<camera.VideoProfile> | undefined = this.cameraOutputCapability?.videoProfiles;
    if (!videoProfiles) {
      TKLog.error("createOutput videoProfilesArray == null || undefined");
      throw new Error("createOutput videoProfilesArray == null || undefined");
    }
    // videoProfile的宽高需要与AVRecorderProfile的宽高保持一致，并且需要使用AVRecorderProfile锁支持的宽高
    let videoSize: camera.Size = {
      width: this.previewSize ? this.previewSize?.width : 640,
      height: this.previewSize ? this.previewSize?.height : 480
    }
    let videoProfile: undefined | camera.VideoProfile = videoProfiles.find((profile: camera.VideoProfile) => {
      return profile.size.width === videoSize?.width && profile.size.height === videoSize?.height;
    });
    if (!videoProfile) {
      TKLog.error("[思迪相机服务]:无法找到videoProfile")
      throw new Error("createOutput videoProfilesArray == null || undefined");
    }
    // 配置参数以实际硬件设备支持的范围为准
    let aVRecorderProfile: media.AVRecorderProfile = {
      audioBitrate: 48000,
      audioChannels: 2,
      audioCodec: media.CodecMimeType.AUDIO_AAC,
      audioSampleRate: 48000,
      fileFormat: media.ContainerFormatType.CFT_MPEG_4,
      videoBitrate: videoSize.width * videoSize.height * 3, //中码率(宽X高X3) //2000000
      videoCodec: media.CodecMimeType.VIDEO_AVC,
      videoFrameWidth: videoSize.width,
      videoFrameHeight: videoSize.height,
      videoFrameRate: 30
    };

    let outputUri: string | undefined = this.outputOption?.outputUri; //获取调用方传入的保存uri
    if (!outputUri) {
      //如果调用方没有传入uri，内部生成uri，并在onStopVideoRecord返回最终录制的文件路径
      let fileName = `video_${Date.now().toString()}`;
      let filesDir = this.context?.filesDir + "/media";
      this.outputFilePath = filesDir + "/" + fileName + ".mp4"; //eg. /data/storage/el2/base/haps/entry/files/media/video_1707379245649.mp4
      if (!fs.accessSync(filesDir)) {
        try {
          fs.mkdirSync(filesDir)
        } catch (error) {
          let err = error as BusinessError;
          TKLog.error(`[思迪相机服务]:创建文件夹异常，code = ${err.code}, msg = ${err.message}`)
        }
      }
      let file: fs.File = fs.openSync(this.outputFilePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
      outputUri = `fd://${file.fd.toString()}`; // fd://59
    }

    let aVRecorderConfig: media.AVRecorderConfig = {
      audioSourceType: media.AudioSourceType.AUDIO_SOURCE_TYPE_MIC,
      videoSourceType: media.VideoSourceType.VIDEO_SOURCE_TYPE_SURFACE_YUV,
      profile: aVRecorderProfile,
      url: outputUri, // 文件需先由调用者创建，赋予读写权限，将文件fd传给此参数，eg.fd://45--file:///data/media/01.mp4
      rotation: this.outputOption?.rotation // 合理值0、90、180、270，非合理值prepare接口将报错
    };
    try {
      this.avRecorder = await media.createAVRecorder();
    } catch (error) {
      let err = error as BusinessError;
      TKLog.error(`[思迪相机服务]:createAVRecorder调用异常，code = ${err.code}, msg = ${err.message}`)
      throw new Error(`createAVRecorder call failed. error code: ${err.code}`);
    }
    try {
      await this.avRecorder.prepare(aVRecorderConfig);
    } catch (error) {
      let err = error as BusinessError;
      TKLog.error(`[思迪相机服务]:视频录制prepare异常，code = ${err.code}, msg = ${err.message}`)
      throw new Error(`prepare call failed. error code: ${err.code}`);
    }

    let videoSurfaceId: string | undefined = undefined; // 该surfaceID用于传递给相机接口创造videoOutput
    try {
      videoSurfaceId = await this.avRecorder.getInputSurface();
    } catch (error) {
      let err = error as BusinessError;
      TKLog.error(`[思迪相机服务]:getInputSurface异常，code = ${err.code}, msg = ${err.message}`)
      throw new Error(`getInputSurface call failed. error code: ${err.code}`);
    }
    // 创建VideoOutput对象
    try {
      this.videoOutput = this.cameraManager?.createVideoOutput(videoProfile, videoSurfaceId);
    } catch (error) {
      let err = error as BusinessError;
      TKLog.error(`[思迪相机服务]:createVideoOutput异常，code = ${err.code}, msg = ${err.message}`)
      throw new Error(`Failed to create the videoOutput instance. error: ${JSON.stringify(err)}`);
    }
    // 监听视频输出错误信息
    this.videoOutput?.on('error', (error: BusinessError) => {
      TKLog.error(`[思迪相机服务]:监听视频输出错误信息异常，code = ${error.code}, msg = ${error.message}`)
    });


    let audioStreamInfo: audio.AudioStreamInfo = {
      samplingRate: audio.AudioSamplingRate.SAMPLE_RATE_16000, // 采样率
      channels: audio.AudioChannel.CHANNEL_1, // 通道
      sampleFormat: audio.AudioSampleFormat.SAMPLE_FORMAT_S16LE, // 采样格式
      encodingType: audio.AudioEncodingType.ENCODING_TYPE_RAW // 编码格式
    }
    let audioCapturerInfo: audio.AudioCapturerInfo = {
      source: audio.SourceType.SOURCE_TYPE_MIC, // 音源类型
      capturerFlags: 0 // 音频采集器标志
    }
    this.audioCapturerOptions= {
      streamInfo: audioStreamInfo,
      capturerInfo: audioCapturerInfo
    }
  }

  /**
   * 初始化音频采集器
   */
  private initAudioCapturer() {
    TKLog.debug("[思迪相机服务]:initAudioCapturer:")
    if (!this.audioCapturer){
      TKLog.debug("[思迪相机服务]:createAudioCapturer:")
      audio.createAudioCapturer(this.audioCapturerOptions, (err, capturer) => { // 创建AudioCapturer实例
        if (err) {
          TKLog.error(`[思迪相机服务]:createAudioCapturer::code is ${err.code}, message is ${err.message}`)
          return;
        }
        this.audioCapturer = capturer;
        this.startAudioCapturer();
      });
    } else {
      this.startAudioCapturer();
    }
  }

  /**
   * 开始音频采集
   */
  private startAudioCapturer() {
    TKLog.debug(`[思迪相机服务]:startAudioCapturer::`)
    if (this.audioCapturer !== undefined) {
      let stateGroup = [audio.AudioState.STATE_PREPARED, audio.AudioState.STATE_PAUSED, audio.AudioState.STATE_STOPPED];
      if (stateGroup.indexOf((this.audioCapturer as audio.AudioCapturer).state.valueOf()) === -1) { // 当且仅当状态为STATE_PREPARED、STATE_PAUSED和STATE_STOPPED之一时才能启动采集
        TKLog.error(`start failed`);
        return;
      }

      if (this.audioCapturer !== undefined) {
        (this.audioCapturer as audio.AudioCapturer).on('readData', (buffer: ArrayBuffer) => {
          // TKLog.debug("[思迪相机服务]:audioCapturer::readData()，byteLength = " + (buffer ? buffer.byteLength : 'undefined'))
          if (buffer && buffer.byteLength > 0) {
            this.cameraLifecycleProxy?.onReadAudioData(buffer);
          }
        })
      }

      // 启动采集
      (this.audioCapturer as audio.AudioCapturer).start((err: BusinessError) => {
        if (err) {
          TKLog.error(`[思迪相机服务]:Capturer start failed.`)
        } else {
          TKLog.debug(`[思迪相机服务]:Capturer start success.`)
        }
      });
    }
  }

  /**
   * 停止音频采集
   */
  private stopAudioCapturer() {
    if (this.audioCapturer !== undefined) {
      // 只有采集器状态为STATE_RUNNING或STATE_PAUSED的时候才可以停止
      if ((this.audioCapturer as audio.AudioCapturer).state.valueOf() !== audio.AudioState.STATE_RUNNING && (this.audioCapturer as audio.AudioCapturer).state.valueOf() !== audio.AudioState.STATE_PAUSED) {
        TKLog.error(`[思迪相机服务]:Capturer is not running or paused.`)
        return;
      }

      (this.audioCapturer as audio.AudioCapturer).off('readData');

      //停止采集
      (this.audioCapturer as audio.AudioCapturer).stop((err: BusinessError) => {
        if (err) {
          TKLog.error(`[思迪相机服务]:Capturer stop failed. code = ${err.code}, msg = ${err.message}`)
        } else {
          TKLog.debug(`[思迪相机服务]:Capturer stop success.`)
        }
      });
    }
  }

  /**
   * 释放音频采集器资源
   */
  private releaseAudioCapturer() {
    if (this.audioCapturer !== undefined) {
      // 采集器状态不是STATE_RELEASED或STATE_NEW状态，才能release
      if ((this.audioCapturer as audio.AudioCapturer).state.valueOf() === audio.AudioState.STATE_RELEASED || (this.audioCapturer as audio.AudioCapturer).state.valueOf() === audio.AudioState.STATE_NEW) {
        TKLog.info('Capturer already released');
        return;
      }

      //释放资源
      (this.audioCapturer as audio.AudioCapturer).release((err: BusinessError) => {
        if (err) {
          TKLog.error(`[思迪相机服务]:Capturer release failed.`)
        } else {
          TKLog.debug(`[思迪相机服务]:Capturer release success.`)
        }
      });
      this.audioCapturer = undefined;
    }
  }

  /**
   * 开始视频录制
   */
  public async startVideoRecord() {
    // 启动录像输出流
    this.videoOutput?.start((err: BusinessError) => {
      if (err) {
        TKLog.error(`[思迪相机服务]:启动录像输出流异常，code = ${err.code}, msg = ${err.message}`)
        return;
      }
      TKLog.info(`[思迪相机服务]:启动录像输出流成功`)
    });
    // 开始录像
    try {
      await this.avRecorder?.start();
      this.cameraLifecycleProxy?.onStartVideoRecord();
    } catch (error) {
      let err = error as BusinessError;
      TKLog.error(`[思迪相机服务]:启动avRecorder异常，code = ${err.code}, msg = ${err.message}`)
    }
    //初始化麦克采集器
    this.initAudioCapturer();
  }

  /**
   * 停止视频录制
   */
  public async stopVideoRecord() {
    // 停止录像输出流
    this.videoOutput?.stop((err: BusinessError) => {
      if (err) {
        TKLog.error(`[思迪相机服务]:停止录像输出流异常，code = ${err.code}, msg = ${err.message}`)
        return;
      }
      TKLog.info(`[思迪相机服务]:停止录像输出流成功`)
    });
    // 停止录像
    try {
      await this.avRecorder?.stop();
      this.cameraLifecycleProxy?.onStopVideoRecord(this.outputFilePath);
    } catch (error) {
      let err = error as BusinessError;
      TKLog.error(`[思迪相机服务]:停止avRecorder异常，code = ${err.code}, msg = ${err.message}`)
    }
    // 停止音频采集
    if (this.audioCapturer) {
      this.stopAudioCapturer()
      this.releaseAudioCapturer();
    }
  }

  /**
   * 开始预览
   */
  private async startPreview() {
    TKLog.debug("[思迪相机服务]:开始预览")
    this.isSessionPreview = true;
    await this.captureSession?.start()
    this.cameraLifecycleProxy?.onPreviewStart();
  }

  /**
   * 获取当前画面图片
   * @returns 图片数据
   */
  public async takePhone(): Promise<Uint8Array | undefined> {
    if (this.surfaceId && this.previewSize) {
      TKLog.debug("[思迪相机服务]:拍照")
      return TKImageUtil.getJpgUint8ArrayBySurfaceId(this.surfaceId, this.previewSize?.width, this.previewSize?.height, 100, this.outputOption?.rotation)
    }
    return undefined
  }

  /**
   * 添加生命周期监听器
   * @param listener 监听器
   */
  public addLifecycleListener(listener: TKCameraLifecycle) {
    TKLog.debug("[思迪相机服务]:添加监听")
    this.cameraLifecycleProxy?.addListener(listener)
  }

  /**
   * 移除生命周期监听器
   * @param listener 监听器
   */
  public removeLifecycleListener(listener: TKCameraLifecycle) {
    TKLog.debug("[思迪相机服务]:移除监听者")
    this.cameraLifecycleProxy?.removeListener(listener)
  }

  /**
   * 释放资源
   */
  private async release() {
    if (this.isSessionPreview) {
      TKLog.debug("[思迪相机服务]:释放资源")
      await this.captureSession?.stop();
      try {
        this.mReceiver?.release(this.callback);
      } catch (exception) {
        TKLog.error('[思迪相机服务]:ImageReceiver 释放异常，Code: ' + JSON.stringify(exception))
      }
      await this.componentSurfacePreviewOutput?.release()
      await this.imageReceiverPreviewOutput?.release()
      // 释放录像输出流
      this.videoOutput?.release();
      // 释放录像输出流
      this.photoOutput?.release();
      this.cameraInput?.close()
      this.avRecorder?.release()
      // 释放会话
      await this.captureSession?.release();
      // 会话置空
      this.captureSession = undefined;
      this.isSessionPreview = false;
      this.cameraLifecycleProxy?.onPreviewStop();
    }
  }

  /**
   * 销毁相机服务
   */
  public async destroy() {
    TKLog.debug("[思迪相机服务]:销毁")
    await this.release();
    this.cameraLifecycleProxy?.onDestroy();
    this.cameraLifecycleProxy?.removeAllListener();
    this.cameraLifecycleProxy?.onDestroy();
  }

  handleFlash() {
    try {
      this.isFlashlight = this.getFlashMode() == camera.FlashMode.FLASH_MODE_ALWAYS_OPEN;
      if (!this.isFlashlight) {
        this.setFlashMode(camera.FlashMode.FLASH_MODE_ALWAYS_OPEN)
      } else {
        this.setFlashMode(camera.FlashMode.FLASH_MODE_CLOSE)
      }

      this.isFlashlight = this.getFlashMode() == camera.FlashMode.FLASH_MODE_OPEN || this.getFlashMode() == camera.FlashMode.FLASH_MODE_ALWAYS_OPEN;
    } catch (error) {
      TKLog.info(`[思迪身份证OCR页面]:handleFlash异常:${JSON.stringify(error)}`);
    }
  }

  setFlashMode(flashMode: camera.FlashMode): void {
    try {
      if (this.captureSession != undefined) {
        this.captureSession.setFlashMode(flashMode);
      }

    } catch (error) {
      // 失败返回错误码error.code并处理
      let err = error as BusinessError;
      TKLog.info(`[思迪身份证OCR页面]:setFlashMode异常:error code: ${err.code}`);
    }
  }

  getFlashMode(): camera.FlashMode | undefined {
    try {
      if (this.captureSession != undefined) {
        return this.captureSession.getFlashMode();
      }

    } catch (error) {
      // 失败返回错误码error.code并处理
      let err = error as BusinessError;
      TKLog.info(`[思迪身份证OCR页面]:getFlashMode异常:error code: ${err.code}`);
    }
    return undefined
  }

  getIsFlash(): boolean {
    return this.isFlashlight
  }
}