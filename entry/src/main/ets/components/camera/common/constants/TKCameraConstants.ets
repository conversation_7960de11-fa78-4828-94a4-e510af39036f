/**
 * 相机位置枚举
 */
export enum CameraPosition {
  FRONT = 2, // 前置摄像头
  BACK = 1, // 后置摄像头
}

/**
 * 相机预览画面宽高比枚举
 */
export enum CameraPreviewAspectRatio {
  ASPECT_RATIO_1_1 = 1 / 1, // 1:1
  ASPECT_RATIO_4_3 = 4 / 3, // 4:3
  ASPECT_RATIO_16_9 = 16 / 9, // 16:9
}

/**
 * 相机图像格式枚举
 */
export enum CameraImageFormat {
  CAMERA_PHOTO_FORMAT = 2000, // 照片格式
  CAMERA_VIDEO_FORMAT = 1003, // 视频格式
}

/**
 * 相机捕获模式枚举
 */
export enum CaptureMode {
  PREVIEW_FRAME, // 预览帧
  PHOTO, // 拍照
  VIDEO, // 录像
}

/**
 * 相机预览尺寸类
 */
export class CameraPreviewSize{
  constructor() {
  }
  width:number = 0 // 预览宽度
  height:number = 0 // 预览高度
}

/**
 * 相机输出选项类
 */
export class CameraOutputOption{
  constructor() {
  }
  outputType:CaptureMode[] = [CaptureMode.PREVIEW_FRAME]; // 输出类型,默认为预览帧
  outputUri?:string; // 输出文件URI
  rotation?:number = 270; // 旋转角度,默认270度
}