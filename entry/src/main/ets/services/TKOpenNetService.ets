import {
  TKCommonService,
  TKContentType,
  TKFileHelper,
  TKLoadInfoVO,
  TKLog,
  TKMapHelper,
  TKObjectHelper,
  TKReqParamVO,
  TKResultVO,
  TKStringHelper,
  TKSystemHelper,
  TKWebHelper
} from '@thinkive/tk-harmony-base'


/**
 * 网络请求回调函数类型定义
 * @param result 请求结果
 * @param errorNo 错误码
 * @param errorInfo 错误信息
 */
export type openNetServiceBaseCallback<T> = (result: T, errorNo: number, errorInfo: string) => void

/**
 * 思迪开户网络请求服务类
 */
export class TKOpenNetService {
  // 通用扩展参数（json数据）
  extParams?: Record<string, Object | undefined>
  // 通用请求头
  requestHeaders?: Record<string, Object | undefined>
  // 通用请求参数
  requestParams?: Record<string, Object | undefined>
  // 是否对参数做url编码
  isURLEncode: boolean = false
  // 请求超时时间(单位:秒)
  timeOut: number = 10
  // 网络请求服务
  commonService: TKCommonService = new TKCommonService()
  // 是否显示等待
  isShowWait: boolean = false

  /**
   * 创建基础请求参数对象
   * @param url 请求URL
   * @param method 请求方法
   * @param params 请求参数
   * @returns 请求参数对象
   */
  createOpenBaseReqParamVo(url: string,
    method: string,
    params: Record<string, Object | undefined> | undefined): TKReqParamVO {

    TKLog.info(`[思迪开户网络工具]开始创建请求参数,url:${url},method:${method}`)

    let commonService: TKCommonService = new TKCommonService()
    let reqParamVO: TKReqParamVO = commonService.createReqParamVO()
    reqParamVO.isFilterRepeatRequest = false;
    reqParamVO.httpMethod = method
    reqParamVO.url = url
    reqParamVO.timeOut = this.timeOut
    reqParamVO.isShowWait = this.isShowWait
    reqParamVO.waitTip = '请稍后...'

    // 合并请求头
    if (this.requestHeaders) {
      reqParamVO.headerFieldDic = TKMapHelper.merge(reqParamVO.headerFieldDic, this.requestHeaders)
      TKLog.info(`[思迪开户网络工具]合并请求头:${JSON.stringify(reqParamVO.headerFieldDic)}`)
    }

    // 判断是否为RESTful接口
    if (Number(TKSystemHelper.getConfig('networkRequest.isRestFull')) == 1) {
      reqParamVO.isRestFull = true
      reqParamVO.contentType = TKContentType.WWW_FORM
      TKLog.info(`[思迪开户网络工具]设置为RESTful接口`)
    }

    // 合并请求参数
    if (this.extParams) {
      reqParamVO.reqParam = this.extParams
    }
    if (this.requestParams) {
      reqParamVO.reqParam = TKMapHelper.merge(reqParamVO.reqParam, this.requestParams)
    }
    if (params) {
      reqParamVO.reqParam = TKMapHelper.merge(reqParamVO.reqParam, params)
    }

    // 处理RESTful相关配置
    if (reqParamVO.reqParam.isRestFull && TKStringHelper.isNotEmpty(reqParamVO.reqParam.isRestFull as string)) {
      if (Number(reqParamVO.reqParam.isRestFull) == 0) {
        reqParamVO.isRestFull = false
        reqParamVO.contentType = TKContentType.NONE
      } else {
        reqParamVO.isRestFull = true
        reqParamVO.contentType = TKContentType.WWW_FORM
      }

      // 处理签名相关配置
      if (reqParamVO.reqParam.requestSignAppId &&
      TKStringHelper.isNotEmpty(reqParamVO.reqParam.requestSignAppId as string)) {
        reqParamVO.isURLSign = true
        reqParamVO.signAppId = reqParamVO.reqParam.requestSignAppId as string
      }

      if (reqParamVO.reqParam.requestSignKey &&
      TKStringHelper.isNotEmpty(reqParamVO.reqParam.requestSignKey as string)) {
        reqParamVO.isURLSign = true
        reqParamVO.signKey = reqParamVO.reqParam.requestSignKey as string
      } else {
        reqParamVO.isURLSign = false
      }
    }

    TKLog.info(`[思迪开户网络工具]创建请求参数完成:${JSON.stringify(reqParamVO)}`)
    return reqParamVO
  }

  /**
   * 发送基础网络请求
   * @param url 请求URL
   * @param method 请求方法
   * @param params 请求参数
   * @param callBack 回调函数
   */
  sendBaseRequest(url: string,
    method: string,
    params: Record<string, Object | undefined> | undefined,
    callBack?: openNetServiceBaseCallback<TKResultVO>): void {

    TKLog.info(`[思迪开户网络工具]发送基础请求,url:${url},method:${method},params:${JSON.stringify(params)}`)
    let reqParamVO: TKReqParamVO = this.createOpenBaseReqParamVo(url, method, params)

    this.commonService.serviceInvoke(reqParamVO, (resultVO: TKResultVO) => {
      TKLog.info(`[思迪开户网络工具]网络请求结果:${TKObjectHelper.toJsonStr(resultVO)}`);
      callBack ? callBack(resultVO, resultVO.errorNo, resultVO.errorInfo) : undefined
    })
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param callBack 回调函数
   */
  sendGetRequest(url: string,
    params: Record<string, Object | undefined> | undefined,
    callBack?: openNetServiceBaseCallback<TKResultVO>): void {

    TKLog.info(`[思迪开户网络工具]发送GET请求,url:${url},params:${JSON.stringify(params)}`)
    this.sendBaseRequest(url, 'get', params, callBack)
    return undefined
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param params 请求参数
   * @param callBack 回调函数
   */
  sendPostRequest(url: string,
    params: Record<string, Object | undefined> | undefined,
    callBack?: openNetServiceBaseCallback<TKResultVO>): void {
    TKLog.info(`[思迪开户网络工具]发送POST请求,url:${url},params:${JSON.stringify(params)}`)
    this.sendBaseRequest(url, 'post', params, callBack)
  }

  /**
   * 上传单个文件
   * @param url 上传URL
   * @param filename 文件名
   * @param filePath 文件路径
   * @param fileArray 文件数据
   * @param fileType 文件类型
   * @param params 额外参数
   * @param callBack 回调函数
   * @param progressCallBack 进度回调
   */
  async uploadFile(url: string,
    filename: string,
    filePath?: string,
    fileArray?: Uint8Array,
    fileType?: string,
    params?: Record<string, string>,
    callBack?: openNetServiceBaseCallback<Record<string, Object>>,
    progressCallBack?: openNetServiceBaseCallback<number>) {

    TKLog.info(`[思迪开户网络工具]开始上传单个文件,url:${url},filename:${filename}`)
    let param: Record<string, Object> = {}

    // 设置上传的文件名。【@@F】是传给底层库的标识符
    let fileKey: string = filename + '@@F'
    let fileData: Uint8Array = new Uint8Array();
    // 设置上传的数据流或者文件路径

    if (filePath) {
      fileData = TKFileHelper.readFile(filePath)
      TKLog.info(`[思迪开户网络工具]从文件路径读取数据,filePath:${filePath}`)
    }

    if (fileArray) {
      fileData = fileArray
      TKLog.info(`[思迪开户网络工具]使用传入的文件数据`)
    }

    param[fileKey] = fileData
    // 设置文件类型
    if (filePath) {
      param.type = param.file_extension = TKWebHelper.getUrlPathSuffix(filePath) // param.file_extension是底层库需要字段
    }
    if (fileType) {
      param.type = param.file_extension = fileType
    }

    // 设置额外参数
    if (params) {
      param = TKMapHelper.merge(param, params)
    }

    // 生成上传参数模型
    let reqParamVO: TKReqParamVO = this.createOpenBaseReqParamVo(url, 'post', param)
    reqParamVO.isUpload = true
    reqParamVO.timeOut = 30

    reqParamVO.uploadBlock = (loadInfoVO: TKLoadInfoVO) => {
      progressCallBack ? progressCallBack(loadInfoVO.progress, 0, '') : undefined
      TKLog.info(`[思迪开户网络工具]上传进度:bytesTotal=${loadInfoVO.bytesTotal}, bytesLoaded=${loadInfoVO.bytesLoaded}, progress=${loadInfoVO.progress}`);
    }

    // 发送请求
    this.commonService = new TKCommonService()
    TKLog.info(`[思迪开户网络工具]开始上传`);
    this.commonService.serviceInvoke(reqParamVO, (result: TKResultVO) => {
      TKLog.info(`[思迪开户网络工具]上传结果:${TKObjectHelper.toJsonStr(result.results)}`);

      let results: Array<Record<string, Object>> = result.results as Array<Record<string, Object>>
      if (result.errorNo == 0 && results && results.length > 0) {
        let uploadDic = results[0]
        if (uploadDic != undefined) {

          // 正常回调
          callBack ? callBack(uploadDic, result.errorNo, result.errorInfo) : undefined
          return
        }
      }

      // 错误回调
      callBack ? callBack({}, result.errorNo, result.errorInfo) : undefined
    })
  }

  /**
   * 上传多个文件
   * @param url 上传URL
   * @param filename 文件名
   * @param filePath 文件路径
   * @param fileArray 文件数据
   * @param fileType 文件类型
   * @param params 额外参数
   * @param callBack 回调函数
   * @param progressCallBack 进度回调
   */
  async uploadFiles(url: string,
    filename: string,
    filePath?: string,
    fileArray?: Uint8Array,
    fileType?: string,
    params?: Record<string, string>,
    callBack?: openNetServiceBaseCallback<Record<string, Object>>,
    progressCallBack?: openNetServiceBaseCallback<number>) {

    TKLog.info(`[思迪开户网络工具]开始上传多个文件,url:${url},filename:${filename}`)
    let param: Record<string, Object> = {}

    // 设置上传的文件名。【@@F】是传给底层库的标识符
    let fileKey: string = filename

    let fileData: Uint8Array | undefined = undefined

    if (fileArray) {
      fileData = fileArray
      param[fileKey] = fileData
      TKLog.info(`[思迪开户网络工具]使用传入的文件数据`)
    }

    // 设置上传的数据流或者文件路径
    if (filePath) {
      param.type = param.file_extension = TKWebHelper.getUrlPathSuffix(filePath) // param.file_extension是底层库需要字段

      fileKey = filename + '@@F'
      param[fileKey] = filePath
      TKLog.info(`[思迪开户网络工具]使用文件路径:${filePath}`)
    }

    // 设置文件类型
    if (fileType) {
      param.type = param.file_extension = fileType
    }

    // 设置额外参数
    if (params) {
      param = TKMapHelper.merge(param, params)
    }

    // 生成上传参数模型
    let reqParamVO: TKReqParamVO = this.createOpenBaseReqParamVo(url, 'post', param)
    reqParamVO.isUpload = true
    reqParamVO.timeOut = 30

    reqParamVO.uploadBlock = (loadInfoVO: TKLoadInfoVO) => {
      progressCallBack ? progressCallBack(loadInfoVO.progress, 0, '') : undefined
      TKLog.info(`[思迪开户网络工具]上传进度:bytesTotal=${loadInfoVO.bytesTotal}, bytesLoaded=${loadInfoVO.bytesLoaded}, progress=${loadInfoVO.progress}`);
    }

    // 发送请求
    this.commonService = new TKCommonService()
    TKLog.info(`[思迪开户网络工具]开始上传`);
    this.commonService.serviceInvoke(reqParamVO, (result: TKResultVO) => {

      TKLog.info(`[思迪开户网络工具]服务器返回的原始上传结果:${TKObjectHelper.toJsonStr(result.originData)}`);

      let originData: Record<string, Object> = result.originData as Record<string, Object>
      if (originData && typeof originData.results != 'string') {

        // 正常回调
        callBack ? callBack(result.originData as Record<string, Object>, result.errorNo, result.errorInfo) : undefined
        return
      }

      // 错误回调
      callBack ? callBack({},
        result.errorNo != 0 ? result.errorNo : -1,
        (TKStringHelper.isBlank(result.errorInfo) && (originData.results == 'string')) ? (originData.results as string) :
        result.errorInfo)
        : undefined
    })
  }

  /**
   * 获取jwt token
   * @param url 请求URL
   * @param callBack 回调函数
   */
  getToken(url: string,
    callBack?: openNetServiceBaseCallback<string>): void {

    TKLog.info(`[思迪开户网络工具]开始获取token,url:${url}`)
    this.sendPostRequest(url, undefined, (result, errorNo, errorInfo) => {

      let results: Array<Record<string, string>> = result.results as Array<Record<string, string>>
      if (errorNo == 0 && results && results.length > 0) {
        let tokenDic = results[0]
        if (tokenDic != undefined) {
          const token: string | undefined = tokenDic.authorization
          if (token != undefined) {
            TKLog.info(`[思迪开户网络工具]获取token成功:${token}`)
            // 正常回调
            callBack ? callBack(token, errorNo, errorInfo) : null
            return
          }
        }
      }

      TKLog.error(`[思迪开户网络工具]获取token失败,errorNo:${errorNo},errorInfo:${errorInfo}`)
      // 错误回调
      callBack ? callBack('', errorNo, errorInfo) : null
    })
  }
}
