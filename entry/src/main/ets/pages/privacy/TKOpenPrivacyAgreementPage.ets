import { router, window } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';
import {
  TK<PERSON><PERSON>xt<PERSON>elper,
  T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TK<PERSON>asswordGenerator,
  TKCacheManager
} from '@thinkive/tk-harmony-base';


@Entry
@Component
struct TKOpenPrivacyAgreementPage {
  @State isShowTip: boolean = false; //是否显示温馨提示

  // URL配置区域 - 加密存储
  private static readonly ENCRYPTED_PRIVACY_POLICY_URL: string = "+Rwl8bsTO/tJx0k3Kaozx2YJqqt6BoOk6gL8z5mPfQjVNoihqSUuYC4SixSbIrQan8e2AJ4x0T0p6KURocbvHTrVCCtC3r9xCEXatXs8x0w=";
  private static readonly ENCRYPTED_USER_AGREEMENT_URL: string = "+Rwl8bsTO/tJx0k3Kaozx20bZDq/r1YCWO/1fQlxenjSHHALcHjIDb2oTCPVuO08glAE98Ya0BXHDehBZDtyDLKUdZbhye0w0G+2/l5ZMbo=";

  /**
   * 获取加密URL的方法（用于生成上述常量值）
   * 使用方法：
   * 1. 调用 TKPasswordGenerator.shareInstance().generatorPassword() 获取密码
   * 2. 调用 TKAesHelper.stringWithAesEncrypt(原始URL, 密码) 获取加密URL
   * 3. 将加密后的URL替换上述占位符
   */
  private static async generateEncryptedUrl(originalUrl: string): Promise<string> {
    return await TKAesHelper.stringWithAesEncrypt(originalUrl, TKPasswordGenerator.shareInstance().generatorPassword());
  }

  aboutToAppear() {
    this.setWindowStatus(false); //隐藏屏幕状态栏
  }

  aboutToDisappear(): void {

  }

  setWindowStatus(showNavBar: boolean) {

    window.getLastWindow(getContext(this), (_: BusinessError, win) => {
      if (!win) {
        return
      }

      // 设置沉浸式
      TKWindowHelper.setWindowLayoutFullScreen(!showNavBar, win)
    })
  }

  /**
   <AUTHOR> 2024年06月11日13:19:15
   @取消事件
   */
  cancelAction() {
    this.isShowTip = true;
  }

  /**
   <AUTHOR> 2024年06月11日13:24:20
   @确定事件
   */
  okAction() {
    //保存用户本地缓存
    TKCacheManager.shareInstance().saveFileCacheData("agreePolicyFlag", "1");

    this.setWindowStatus(true); //开启屏幕状态栏

    router.replaceUrl({
      url: 'pages/Index',
      // url: 'pages/demo',
    })
  }

  /**
   <AUTHOR> 2024年06月11日13:24:53
   @退出
   */
  exitAction() {
    getContext(this).getApplicationContext().killAllProcesses()
  }

  async  openWebUrl(encryptedUrl: string, title: string) {
    try {
      let finalUrl: string;

      // 解密URL
      finalUrl = await TKAesHelper.stringWithAesDecrypt(encryptedUrl, TKPasswordGenerator.shareInstance().generatorPassword());
      TKLog.info(`[隐私协议页面]:解密URL成功，标题: ${title}`);

      // 启动系统浏览器
      TKSystemHelper.openBrowser(finalUrl);
    } catch (error) {
      TKLog.error(`[隐私协议页面]:URL处理失败: ${error}`);
      // 如果解密失败，可以考虑使用备用处理方式
    }
  }

  /**
   <AUTHOR> 2024年06月11日13:25:10
   @知晓
   */
  knowAction() {
    this.isShowTip = false;
  }

  build() {
    RelativeContainer() {
      Image($r("app.media.startIcon"))
        .width('100%')
        .objectFit(ImageFit.Fill)
        .backgroundColor('#0000')
        .height("100%")
        .width('100%')
        .id("tk_open_privacy_launch")

      Column() {
        Column() {
          Text("温馨提示")
            .fontSize(22)
            .fontFamily('PingFang SC')
            .fontColor("#000000")
            .margin({ top: 10 })
            .textAlign(TextAlign.Center)

          Line()
            .width('100%')
            .height(2)
            .backgroundColor('#E53C3C')
            .margin({ top: 8 })

          Text() {
            Span("      根据监管规定，为了更好地保护您的权益，请认真阅读")

            Span("《晋金财富隐私权政策》")
              .fontColor("#319EF2")
              .onClick(() => {
                this.openWebUrl(TKOpenPrivacyAgreementPage.ENCRYPTED_PRIVACY_POLICY_URL, '《晋金财富隐私权政策》');
              })

            Span("《用户协议》")
              .fontColor("#319EF2")
              .onClick(() => {
                this.openWebUrl(TKOpenPrivacyAgreementPage.ENCRYPTED_USER_AGREEMENT_URL, '《用户协议》');
              })

            Span(" 的全部内容，我们通过该政策向您说明我们会如何收集、使用、保护、对外提供您的信息以及您享有的权利。\n      为了保障移动互联网应用的业务安全风控，将收集您硬件系列号、唯一设备识别码、MAC地址。\n      为方便您的查阅，可在APP“更多-隐私保护指引”中查看完整版隐私政策内容。")
          }
          .fontSize(16)
          .fontFamily('PingFang SC')
          .fontColor("#000000")
          .lineHeight(26)
          .margin({ top: 5, left: 10, right: 10 })
          .textAlign(TextAlign.Start)

          Row() {
            Column() {
              Text("不同意")
                .fontSize(16)
                .width(120)
                .height(40)
                .borderRadius(7)
                .borderWidth(1)
                .borderColor('#E53C3C')
                .fontColor('#E53C3C')
                .backgroundColor("#FCECEB")
                .textAlign(TextAlign.Center)
                .align(Alignment.Center)
                .onClick(() => {
                  this.cancelAction();
                })
            }.layoutWeight(1).margin({ right: 5 })


            Column() {
              Text("同意并使用")
                .fontColor("#ffffff")
                .fontSize(16)
                .width(120)
                .backgroundColor('#E53C3C')
                .height(40)
                .borderRadius(7)
                .textAlign(TextAlign.Center)
                .align(Alignment.Center)
                .onClick(() => {
                  this.okAction();
                })

            }.layoutWeight(1).margin({ left: 5 })
            .margin({ left: 24, right: 24 }).height(40)
          }.margin({ top: 24, bottom: 24 })
        }
        .backgroundColor('#ffffff')
        .width('85%')
        .id("tk_open_privacy_tip_bg")
        .borderRadius(8)
        .visibility(this.isShowTip ? Visibility.None : Visibility.Visible)

        Column() {
          Text("温馨提示")
            .fontSize(22)
            .fontFamily('PingFang SC')
            .fontColor("#000000")
            .margin({ top: 10 })
            .textAlign(TextAlign.Center)

          Line()
            .width('100%')
            .height(2)
            .backgroundColor('#E53C3C')
            .margin({ top: 8 })

          Text() {
            Span("      我们非常重视对您个人信息的保护，承诺严格按照")

            Span("《晋金财富隐私权政策》")
              .fontColor("#319EF2")
              .onClick(() => {
                this.openWebUrl(TKOpenPrivacyAgreementPage.ENCRYPTED_PRIVACY_POLICY_URL, '《晋金财富隐私权政策》');
              })

            Span("《用户协议》")
              .fontColor("#319EF2")
              .onClick(() => {
                this.openWebUrl(TKOpenPrivacyAgreementPage.ENCRYPTED_USER_AGREEMENT_URL, '《用户协议》');
              })

            Span("保护及处理您的信息。如果您不同意该政策，很遗憾我们将无法为你提供服务。")
          }
          .fontSize(16)
          .fontFamily('PingFang SC')
          .fontColor("#000000")
          .lineHeight(26)
          .margin({ top: 5, left: 10, right: 10 })
          .textAlign(TextAlign.Start)

          Row() {
            Column() {
              Text("退出")
                .fontSize(16)
                .width(120)
                .height(40)
                .borderRadius(7)
                .borderWidth(1)
                .borderColor('#E53C3C')
                .fontColor('#E53C3C')
                .backgroundColor("#FCECEB")
                .textAlign(TextAlign.Center)
                .align(Alignment.Center)
                .onClick(() => {
                  this.exitAction();
                })
            }.layoutWeight(1).margin({ right: 5 })


            Column() {
              Text("同意并使用")
                .fontColor("#ffffff")
                .fontSize(16)
                .width(120)
                .backgroundColor('#E53C3C')
                .height(40)
                .borderRadius(7)
                .textAlign(TextAlign.Center)
                .align(Alignment.Center)
                .onClick(() => {
                  this.knowAction();
                })

            }.layoutWeight(1).margin({ left: 5 })
            .margin({ left: 24, right: 24 }).height(40)
          }.margin({ top: 24, bottom: 24 })
        }
        .backgroundColor('#ffffff')
        .width('85%')
        .id("tk_open_privacy_tip2_bg")
        .borderRadius(8)
        .visibility(this.isShowTip ? Visibility.Visible : Visibility.None)
      }
      .justifyContent(FlexAlign.Center)
      .backgroundColor('rgba(0, 0, 0, 0.4)')
      .height("100%")
      .width('100%')
      .id("tk_open_privacy_bg")
    }
  }
}