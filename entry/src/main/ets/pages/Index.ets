import {
  T<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TKApplicationS<PERSON><PERSON><PERSON>e<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ase<PERSON><PERSON>el<PERSON>,
  T<PERSON><PERSON>ottomMenu,
  T<PERSON><PERSON>acheManager,
  T<PERSON><PERSON><PERSON>ogHelper,
  TKJSProxyController,
  TKJSProxyControllerManager,
  TKLog,
  TKMapHelper,
  TKMenuDialogOption,
  TKNoPermissionTip,
  TKNotification,
  TKNotificationCenter,
  TKPdfPage,
  TKPermission,
  TKPermissionHelper,
  TK<PERSON>luginInvokeCenter,
  TKStringHelper,
  T<PERSON>ystemHelper,
  TKThemeManager,
  TKWebAttrEventController,
  TKWebPage,
  TKWebPageAttribute,
  TKWebPageStyleAttribute,
  TKWindowHelper
} from '@thinkive/tk-harmony-base'
import { promptAction } from '@kit.ArkUI'
import { BusinessError, emitter } from '@kit.BasicServicesKit'
import { abilityAccessCtrl, common, ConfigurationConstant } from '@kit.AbilityKit'
import TKO<PERSON>Constant from '../constant/TKOpenConstant'
import TKBiometricAuthManager, { TKBiometricAuthType, TKBiometricAuthCallback, TKBiometricAuthResult } from '../utils/TKBiometricAuthManager'
import * as TKPlugin from '../services/TKJJCFPluginIndex';
import { TKShare } from '@thinkive/tk-harmony-share'

@Entry
@Component
struct Index {
  // 页面URL
  @State url: string = 'www/m/mall/index.html'
  // 页面属性配置
  @State attribute: TKWebPageAttribute = {
    isLayoutFullScreen: false,
    // statusBarEnable: this.openEntryVO.routerPageStatusBarEnable,
    // titleBarEnable: this.openEntryVO.routerPageTitleBarEnable,
    // bottomBarEnable: this.openEntryVO.routerPageBottomBarEnable,
    // backPressEnable: this.openEntryVO.routerPageBackPressEnable,
    isH5GoBack: true,
    // isSupportSwipingBack: this.openEntryVO.isSupportSwipingBack,
    // onOpenModule: (data?: Record<string, Object>) => {
    //   return this.onOpenModule(data)
    // }
  }
  // 页面样式配置
  @State styleAttribute: TKWebPageStyleAttribute = {
    // loadingDialogGif: $r("app.media.tk_open_loading"),
    // statusStyle: this.openEntryVO.routerPagestatusBarStyle
    progressColor: ''
  }
  // 返回按键状态变化标志
  @Provide onBackPressChanged: boolean = false
  // 返回按键过滤标志
  @Provide onBackPressFilter: boolean = true
  // JS代理控制器
  private jSProxyController: TKJSProxyController = new TKJSProxyController();
  // 当前系统颜色模式，监听AppStorage变化
  @StorageLink('currentColorMode') @Watch('onColorModeChanged') currentColorMode: ConfigurationConstant.ColorMode = ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET;

  // 页面创建时
  aboutToAppear() {

    if (TKAppEngine.shareInstance().isRunning()) {
      TKLog.debug(`[思迪晋金财富入口页面]：思迪鸿蒙框架已运行，注册插件`) // 框架初始化之前，需要使用该方式打印

      this.initTKSDK()
    } else {
      TKAppEngine.shareInstance().start({
        context: getContext() as common.UIAbilityContext,
        finishCallBack: () => {
          //框架初始化完成
          TKLog.info(`[思迪晋金财富入口页面]：思迪鸿蒙框架初始化成功`)

          this.initTKSDK()
        }
      });
    }

    this.subscribe()

    // 初始化系统亮度模式适配
    this.initSystemColorModeAdaptation()
  }

  initTKSDK() {
    TKShare.updatePolicy(true)
    TKPluginInvokeCenter.shareInstance().registerPlugin(TKPlugin);
  }

  /**
   * 返回按键处理
   */
  onBackPress(): boolean | void {
    TKLog.debug('[思迪晋金财富入口页面]:onBackPress')

    this.onBackPressChanged = !this.onBackPressChanged;
    return this.onBackPressFilter
  }

  /**
   * 订阅事件
   */
  private subscribe() {

    // 收到TK_WINDOW_PRIVACY_MODE_NOTIFICATION的事件后执行该回调
    this.subscribeWindowPrivacy()

    // 收到NOTE_ACTION_PDF的事件后执行该回调
    this.subscribeUndoPrivacy()

    // 收到NOTE_DOWNLOAD_PDF的事件后执行该回调
    // this.subscribeDownloadPdf()

    // 收到note_action_pdf的事件后执行该回调
    this.subscribeNoteActionPdf()

    // 收到NOTE_DOWNLOAD_PDF的事件后执行该回调
    this.subscribeEnterForeground()

    TKLog.debug('[思迪晋金财富入口页面]：开始监听消息');
  }

  private subscribeWindowPrivacy() {

    // 执行该回调
    let callback = (eventData: emitter.EventData): void => {
      TKLog.info(`[思迪晋金财富入口页面]：接收消息回调:${JSON.stringify(eventData.data)}`)

      let data: Record<string, Object> = eventData.data as Record<string, Object>
      let params: Record<string, Object> = data.params as Record<string, Object>

      let isPrivacyMode: boolean = false;

      if (params && params.isInterceptScreenshot && Number.parseInt(params.isInterceptScreenshot as string) == 1) {
        isPrivacyMode = true;
      }

      TKWindowHelper.getLastWindow(getContext()).then((window) => {
        let promise = window.setWindowPrivacyMode(isPrivacyMode); // 设置防截屏录屏
        promise.then(() => {
          TKLog.info(`[思迪晋金财富入口页面]：设置防截屏和截图模式成功，isPrivacyMode=${isPrivacyMode}`)
        }).catch((err: BusinessError) => {
          TKLog.error(`[思迪晋金财富入口页面]：设置防截屏和截图模式失败，isPrivacyMode=${isPrivacyMode}，code = ${err.code},  message= ${err.message}`)
        })
      })
    };

    // 订阅
    emitter.on(TKOpenConstant.TK_WINDOW_PRIVACY_MODE_NOTIFICATION, callback);
  }

  private subscribeUndoPrivacy() {

    // 执行该回调
    TKNotificationCenter.defaultCenter.addObserver(this, (note) => {
      TKLog.info(`[思迪晋金财富入口页面]：接收消息回调:${JSON.stringify(note)}`)

      this.undoPrivacy(note)

    }, 'undoPrivacy')
  }

  private subscribeNoteActionPdf() {

    // 执行该回调
    TKNotificationCenter.defaultCenter.addObserver(this, (note) => {
      TKLog.info(`[思迪晋金财富入口页面]：接收消息回调:${JSON.stringify(note)}`)

      this.showChoiceSheet(note)
    }, TKPdfPage.NOTE_ACTION_PDF)
  }

  private showChoiceSheet(note: TKNotification) {

    let userInfo = note.userInfo
    if (userInfo) {
      let action = TKMapHelper.getString(userInfo, 'action')
      if (TKStringHelper.isNotEmpty(action)) {
        if (action === 'privacyMenu') {
          TKDialogHelper.showActionSheet({
            // mainColor: "#34a0fb",
            bottomMenu: [{ tag: "0", name: "下载" }, { tag: "1", name: "撤销" }],
            onItemClick: (item: TKBottomMenu) => {
              // 下载
              if (item.tag == "0") {
                this.downloadPdf(note);
              } else { // 撤销

                this.undoPrivacy(note);
              }
            },
            onCancel: () => {
            }
          } as TKMenuDialogOption);
        }
      }
    }
  }

  // private subscribeDownloadPdf() {
  //
  //   // 执行该回调
  //   let callback = (eventData: emitter.EventData): void => {
  //     TKLog.info(`[思迪晋金财富入口页面]：接收消息回调:${JSON.stringify(eventData.data)}`)
  //   };
  //
  //   // 订阅
  //   emitter.on(TKPdfPage.NOTE_DOWNLOAD_PDF, callback);
  // }


  private subscribeEnterForeground() {

    // // 执行该回调
    // let callback = (eventData: emitter.EventData): void => {
    //   TKLog.info(`[思迪晋金财富入口页面]：接收消息回调:${JSON.stringify(eventData.data)}`)
    // };
    //
    // // 订阅
    // emitter.on(TKPdfPage.NOTE_DOWNLOAD_PDF, callback);

    TKNotificationCenter.defaultCenter.addObserver(this, () => {

      let param:Record<string, Object> = {
        "funcNo": "80305"
      } as Record<string, Object>
      TKJSProxyControllerManager.shareInstance().currentJSProxyController()?.sendPlugMsgToH5(param)
    }, TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND)
  }

  /**
   * 地理位置权限申请处理
   */
  onGeolocationShow(event?: OnGeolocationShowEvent) {
    if (event) {
      TKLog.debug('[思迪晋金财富入口页面]:开始申请地理位置权限')
      TKPermissionHelper.requestPermissionsFromUser(
        [TKPermission.APPROXIMATELY_LOCATION, TKPermission.LOCATION],
        getContext(this) as common.UIAbilityContext)
        .then((result) => {
          let geolocation = event.geolocation as JsGeolocation
          let allow = result.isGrant

          if (allow == false) {
            TKLog.debug('[思迪晋金财富入口页面]:地理位置权限申请被拒绝')
            promptAction.showToast({ message: TKNoPermissionTip.LOCATION_TIP })
          }
          geolocation.invoke(event.origin as string, allow, false); // 允许此站点地理位置权限请求
        })
        .catch((err: BusinessError) => {
          TKLog.error(`[思迪晋金财富入口页面]:地理位置权限申请异常,code=${err.code},message=${err.message}`)
          promptAction.showToast({ message: TKNoPermissionTip.LOCATION_TIP })

          let geolocation = event.geolocation as JsGeolocation
          geolocation.invoke(event.origin as string, false, false); // 不允许此站点地理位置权限请求
        })
    } else {
      TKLog.debug('[思迪晋金财富入口页面]:h5定位传入的event为空')
    }
  }

  /**
   * 文件选择处理
   */
  onShowFileSelector(_event?: OnShowFileSelectorEvent): boolean {
    return false
  }

  /**
   * 插件事件处理
   */
  onPluginEvent(eventId: string, eventParam?: Record<string, Object>) {
    TKLog.info(`[思迪晋金财富入口页面]:onPluginEvent, eventId = ${eventId}, eventParam = ${JSON.stringify(eventParam)}`)
  }

  /**
   * 下载pdf
   */
  downloadPdf(note: TKNotification) {

    // 目前简单处理，打开系统浏览器让用户自行下载
    let userInfo = note.userInfo
    if (userInfo) {
      let param = TKMapHelper.getObject(userInfo, 'param') as Record<string, Object>
      if (TKStringHelper.isNotEmpty(param.downloadUrl as string)) {
        TKSystemHelper.openBrowser(param.downloadUrl as string)
      }
    }
  }

  private undoPrivacy(note: TKNotification) {
    let userInfo = note.userInfo
    if (userInfo) {
      let param = TKMapHelper.getObject(userInfo, 'param') as Record<string, Object>
      if (param) {
        TKDialogHelper.showAlertDialog(
          {
            title: '温馨提示',
            cancelText: '取消',
            confirmText: '确定',
            message: param.promtpText as string,
            confirm: () => {
              // 保存结果
              TKCacheManager.shareInstance().saveFileCacheData("agreePolicyFlag" , "0");

              setTimeout(() => {
                // 关闭应用
                getContext().getApplicationContext().killAllProcesses()
              }, 100)
            }
          }
        )
      }
    }
  }

  build() {
    Row() {
      Column() {
        // Button('调试按钮')
        //   .onClick(() => {
        // this.plugin60302()
        // this.plugin60304()
        // this.plugin80002()
        // this.plugin50273()
        // this.plugin50277()
        // this.plugin80050()
        // this.plugin50261()
        // this.plugin50264()
        // this.plugin50210()
        // this.plugin50240()
        // this.plugin50115()
        // this.plugin50230()
        // this.plugin50231()
        // this.plugin80302()
        // this.plugin60076()
        // })

        // Column() {
        //   Button('检查生物识别支持')
        //     .onClick(() => {
        //       this.checkBiometricSupport()
        //     })
        //     .margin({ bottom: 10 })
        //
        //   Button('测试指纹认证')
        //     .onClick(() => {
        //       this.testFingerprintAuth()
        //     })
        //     .margin({ bottom: 10 })
        //
        //   Button('测试人脸认证')
        //     .onClick(() => {
        //       this.testFaceAuth()
        //     })
        //     .margin({ bottom: 10 })
        //
        //   Button('测试复合认证')
        //     .onClick(() => {
        //       this.testCompositeAuth()
        //     })
        //     .margin({ bottom: 10 })
        //
        //   Button('测试80319同步指纹验证')
        //     .onClick(() => {
        //       this.test80319FingerprintAuth()
        //     })
        //     .margin({ bottom: 10 })
        //
        //   Button('测试80321异步指纹同步')
        //     .onClick(() => {
        //       this.test80321FingerprintSync()
        //     })
        //     .margin({ bottom: 10 })
        // }
        // .padding(20)
        // .visibility(Visibility.Visible) // 显示测试按钮

        TKWebPage({
          url: this.url,
          attribute: this.attribute,
          styleAttribute: this.styleAttribute,
          jSProxyController: this.jSProxyController,
          webAttrEventController: TKWebAttrEventController.builder()
            .geolocationAccess(true)
          // .onGeolocationShow((event) => {
          //   this.onGeolocationShow(event)
          // })
          // .onShowFileSelector((event) => {
          //   return this.onShowFileSelector(event)
          // })
          .onPluginEvent((eventId: string, eventParam?: Record<string, Object>) => {
            this.onPluginEvent(eventId, eventParam)
          })
        })
      }
      .width('100%')
    }
    .height('100%')
  }

  /**
   * 初始化系统亮度模式适配
   * 页面初始化时调用，设置初始主题并监听变化
   */
  private initSystemColorModeAdaptation(): void {
    TKLog.info('[思迪晋金财富入口页面]：开始初始化系统亮度模式适配');

    // 应用当前系统亮度模式对应的主题
    this.applySystemColorModeTheme(this.currentColorMode);

    // 监听系统亮度模式变化
    this.watchSystemColorModeChange();

    TKLog.info('[思迪晋金财富入口页面]：系统亮度模式适配初始化完成');
  }

  /**
   * 系统颜色模式变化回调
   * 当@StorageLink监听的currentColorMode发生变化时自动调用
   */
  onColorModeChanged(): void {
    TKLog.info(`[思迪晋金财富入口页面]：检测到系统亮度模式变化 colorMode = ${this.currentColorMode}`);

    // 应用新的主题
    this.applySystemColorModeTheme(this.currentColorMode);
  }

  /**
   * 监听系统亮度模式变化
   * 当AppStorage中的currentColorMode发生变化时，自动更新主题
   */
  private watchSystemColorModeChange(): void {
    // @Watch装饰器已经自动监听AppStorage变化
    // 当currentColorMode变化时，会自动调用onColorModeChanged方法
    TKLog.info('[思迪晋金财富入口页面]：已设置系统亮度模式变化监听');
  }

  /**
   * 应用系统亮度模式对应的主题
   * @param colorMode 当前系统颜色模式
   */
  private applySystemColorModeTheme(colorMode: ConfigurationConstant.ColorMode): void {
    try {
      let themeName: string;
      let modeText: string;

      // 根据系统亮度模式选择对应主题
      switch (colorMode) {
        case ConfigurationConstant.ColorMode.COLOR_MODE_DARK:
          themeName = TKOpenConstant.TK_DARK_THEME_NAME; // theme2
          modeText = '深色模式';
          break;
        case ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT:
          themeName = TKOpenConstant.TK_LIGHT_THEME_NAME; // theme1
          modeText = '浅色模式';
          break;
        case ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET:
        default:
          // 跟随系统时，默认使用浅色主题
          themeName = TKOpenConstant.TK_LIGHT_THEME_NAME; // theme1
          modeText = '跟随系统（默认浅色）';
          break;
      }

      TKLog.info(`[思迪晋金财富入口页面]：应用${modeText}主题 - ${themeName}`);

      // TODO: 实现主题切换逻辑
      // 目前先通过H5通知的方式让H5页面处理主题切换
      // 后续可以根据TKThemeManager的实际API进行调整
      TKLog.info(`[思迪晋金财富入口页面]：准备切换到${themeName}主题`);

      // 通知H5页面主题变化
      this.notifyH5ColorModeChange(colorMode, themeName);

      TKLog.info(`[思迪晋金财富入口页面]：${modeText}主题应用成功`);

    } catch (error) {
      TKLog.error(`[思迪晋金财富入口页面]：应用主题失败 ${JSON.stringify(error)}`);
    }
  }

  /**
   * 通知H5页面系统亮度模式变化
   * @param colorMode 当前系统颜色模式
   * @param themeName 当前主题名称
   */
  private notifyH5ColorModeChange(colorMode: ConfigurationConstant.ColorMode, themeName: string): void {
    try {
      // 构建通知H5的参数
      let param: Record<string, Object> = {
        "funcNo": TKOpenConstant.TK_SYSTEM_COLOR_MODE_FUNC_NO, // 50126
        "colorMode": colorMode,
        "themeName": themeName,
        "isDarkMode": colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK,
        "isLightMode": colorMode === ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT,
        "timestamp": Date.now()
      } as Record<string, Object>;

      TKLog.info(`[思迪晋金财富入口页面]：准备通知H5页面主题变化 ${JSON.stringify(param)}`);

      // 通过JSProxy发送消息给H5
      TKJSProxyControllerManager.shareInstance().currentJSProxyController()?.sendPlugMsgToH5(param);

      TKLog.info(`[思迪晋金财富入口页面]：已通知H5页面主题变化`);

    } catch (error) {
      TKLog.error(`[思迪晋金财富入口页面]：通知H5页面主题变化失败 ${JSON.stringify(error)}`);
    }
  }

  plugin60302() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "isUpload": "0", //原生插件直接调用不走思迪上传
      "action": "pai", //pai:拍照界面传
      "imgType": "5", //身份证正反面：4：身份证正面，5：身份证反面；
      "isNeedSample": "1", //照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
      //因为合合OCR不支持本地图片识别，所以隐藏相册和拍照按钮，只用扫描，不然会出现没有识别结果只有图片情况
      "isAlbum": "1", //是否显示相册按钮；0：不显示  1：显示（默认显示）
      "isTake": "1", //是否显示拍照按钮0：不显示  1：显示（默认显示）
      "timeOut": "10000", //扫描超时时长；单位秒，默认30秒；为了避免超时提示出现去拍照情况，这里时间设置长点
      "ocrType": "1", //扫描超时时长；单位秒，默认30秒；为了避免超时提示出现去拍照情况，这里时间设置长点
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "60302",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          let errorNo = data.error_no //0：成功 -1: 用户取消 -2：授权失败
          if (data.frontBase64) {
            //身份证人像面识别结果
            let frontBase64 = data.frontBase64 //身份证正面base64，有固定前前缀(data:image/jpg;base64,)
            let ethnicName = data.ethnicName //民族
            let userSex = data.userSex //性别
            let native = data.native //住址
            let idNo = data.idNo //身份证号
            let birthday = data.birthday //出生年月日：1986年6月24日
            let custName = data.custName //姓名
          } else if (data.backBase64) {
            //身份证国徽面识别结果
            let backBase64 = data.backBase64 //身份证反面base64，有固定前缀(data:image/jpg;base64,)
            let idbeginDate = data.idbeginDate //有效日期开始日：2021-09-30
            let idendDate = data.idendDate //有效日期结束日：2041-09-30
            let policeOrg = data.policeOrg //签发机关
          }
          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin60304() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "isUpload": "0", //原生插件直接调用不走思迪上传
      "action": "pai", //pai:拍照界面传
      "imgType": "4", //身份证正反面：4：身份证正面，5：身份证反面；
      "isNeedSample": "1", //照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
      //因为合合OCR不支持本地图片识别，所以隐藏相册和拍照按钮，只用扫描，不然会出现没有识别结果只有图片情况
      "isAlbum": "1", //是否显示相册按钮；0：不显示  1：显示（默认显示）
      "isTake": "1", //是否显示拍照按钮0：不显示  1：显示（默认显示）
      "timeOut": "10000", //扫描超时时长；单位秒，默认30秒；为了避免超时提示出现去拍照情况，这里时间设置长点
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "60304",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          let errorNo = data.error_no //0：成功 -1: 用户取消 -2：授权失败
          if (data.frontBase64) {
            //身份证人像面识别结果
            let frontBase64 = data.frontBase64 //身份证正面base64，有固定前前缀(data:image/jpg;base64,)
            let ethnicName = data.ethnicName //民族
            let userSex = data.userSex //性别
            let native = data.native //住址
            let idNo = data.idNo //身份证号
            let birthday = data.birthday //出生年月日：1986年6月24日
            let custName = data.custName //姓名
          } else if (data.backBase64) {
            //身份证国徽面识别结果
            let backBase64 = data.backBase64 //身份证反面base64，有固定前缀(data:image/jpg;base64,)
            let idbeginDate = data.idbeginDate //有效日期开始日：2021-09-30
            let idendDate = data.idendDate //有效日期结束日：2041-09-30
            let policeOrg = data.policeOrg //签发机关
          }
          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50277() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "moduleName": "mall", //原生插件直接调用不走思迪上传
      "fileName": "headerImg", //pai:拍照界面传
      "compress": "0.5", //身份证正反面：4：身份证正面，5：身份证反面；
      "width": "1600", //照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
      //因为合合OCR不支持本地图片识别，所以隐藏相册和拍照按钮，只用扫描，不然会出现没有识别结果只有图片情况
      "cutFlag": "0",
      "mode": 1,
      "webControllerName": "TKH5|bd268c52-41c9-43cd-a2d8-77388de89dda"
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "50277",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          let errorNo = data.error_no //0：成功 -1: 用户取消 -2：授权失败
          if (data.frontBase64) {
            //身份证人像面识别结果
            let frontBase64 = data.frontBase64 //身份证正面base64，有固定前前缀(data:image/jpg;base64,)
            let ethnicName = data.ethnicName //民族
            let userSex = data.userSex //性别
            let native = data.native //住址
            let idNo = data.idNo //身份证号
            let birthday = data.birthday //出生年月日：1986年6月24日
            let custName = data.custName //姓名
          } else if (data.backBase64) {
            //身份证国徽面识别结果
            let backBase64 = data.backBase64 //身份证反面base64，有固定前缀(data:image/jpg;base64,)
            let idbeginDate = data.idbeginDate //有效日期开始日：2021-09-30
            let idendDate = data.idendDate //有效日期结束日：2041-09-30
            let policeOrg = data.policeOrg //签发机关
          }
          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50273() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "moduleName": "mall", //原生插件直接调用不走思迪上传
      "fileName": "headerImg", //pai:拍照界面传
      // "height": '500',
      // "compress": "0.8", //身份证正反面：4：身份证正面，5：身份证反面；
      // "width": "500", //照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
      //因为合合OCR不支持本地图片识别，所以隐藏相册和拍照按钮，只用扫描，不然会出现没有识别结果只有图片情况
      "cutFlag": "0",
      "mode": 2,
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "50273",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          let base64Image = data.base64Image as string
          let buffer = TKBase64Helper.dataWithBase64Decode(base64Image)
          let length = buffer.length

          let errorNo = data.error_no //0：成功 -1: 用户取消 -2：授权失败
          if (data.frontBase64) {
            //身份证人像面识别结果
            let frontBase64 = data.frontBase64 //身份证正面base64，有固定前前缀(data:image/jpg;base64,)
            let ethnicName = data.ethnicName //民族
            let userSex = data.userSex //性别
            let native = data.native //住址
            let idNo = data.idNo //身份证号
            let birthday = data.birthday //出生年月日：1986年6月24日
            let custName = data.custName //姓名
          } else if (data.backBase64) {
            //身份证国徽面识别结果
            let backBase64 = data.backBase64 //身份证反面base64，有固定前缀(data:image/jpg;base64,)
            let idbeginDate = data.idbeginDate //有效日期开始日：2021-09-30
            let idendDate = data.idendDate //有效日期结束日：2041-09-30
            let policeOrg = data.policeOrg //签发机关
          }
          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin80002() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "moduleName": "mall", //原生插件直接调用不走思迪上传
      "fileName": "headerImg", //pai:拍照界面传
      "compress": "0.5", //身份证正反面：4：身份证正面，5：身份证反面；
      "width": "1600", //照片结果展示是否需要显示示例部分:1：需要示例显示；其他不需要（默认不需要）
      //因为合合OCR不支持本地图片识别，所以隐藏相册和拍照按钮，只用扫描，不然会出现没有识别结果只有图片情况
      "cutFlag": "0",
      "mode": 2,
      "webControllerName": "TKH5|bd268c52-41c9-43cd-a2d8-77388de89dda"
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "80002",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin80050() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "scheme": "thinkiveopen",
      "paramExt": "m/mall/index.html",
      "downloadLink": "0.5",
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "80050",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50261() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "style": "1",
      "account": "***********",
      "errorNum": "5",
      "lockSenconds": "3",
      "userImage": "",
      "isShow": "0",
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "50261",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50264() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      "style": "1",
      "flag": "1",
      "account": "***********",
      "isCanBack": "1",
      "position": "1",
      "errorNum": "5",
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "50264",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50210() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {
      // "pageId":"1",
      // "eleId":"ddd",
      // "keyboardType":"60",
      // "moduleName": "mall",

      "moduleName": "mall",
      "funcNo": "50210",
      "pageId": "safety_updateTradersPassword",
      "eleId": "oldPwd",
      // "doneLable": "确定",
      "keyboardType": "4",
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "50210",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50240() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {

      // "statusColor": "#2F4D80",
      // "title": "内容查看",
      // "titleColor": "#ffffff",
      // "url": "https://jjdxcs.xintongfund.com/oss/fund_filesystem/agreement/20200427093708.pdf",

      "url": "https://jjdxcs.xintongfund.com/m/mall/index.html#!/privacyAgreement/index.html",
      "rightBtnTxt": "tk_plugin_50240_privacyMenu",
      "rightBtnMode": "1",
      "rightBtnAction": "privacyMenu",
      "rightBtnActionParam": {
        "promtpText": "撤销提示",
        "downloadUrl": "https://m.xintongfund.com/fund_filesystem/agreement/20210709094515.pdf"
      } as Record<string, string>
    } as Record<string, Object>

    // TKAppEngine.shareInstance().callPlugin(
    //   {
    //     funcNo: "50240",
    //     param: param,
    //     moduleName: "TKOpen",
    //     callBackFunc: (data) => {
    //       //回调结果
    //
    //       TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
    //     }
    //   })

    TKPluginInvokeCenter.shareInstance().callPlugin({
      funcNo: "50240",
      param: param,
      moduleName: "TKOpen",
      isH5: true,
      callBackFunc: (data) => {
        //回调结果

        TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
      }
    })
  }

  plugin50115() {

    // 打开身份证扫描示例
    let param: Record<string, Object> = {

      "btnColor": "#3f4e59",
      "btnMode": "2",
      "funcNo": "50115",
      "isShowShareBtn": "0",
      "isSupportSwipingBack": "1",
      "isTKH5": "0",
      "leftBtnColor": "#3f4e59",
      "moduleName": "mall",
      "navbarImage": "tk_nav_bg",
      "rightBtnColor": "#FB0005",
      "title": "直播间",
      "titleColor": "#3f4e59",
      "url": "https://h5webtest.jravity.com/frontPage?roomId=test_20230818154939_jvKE&uid=0002880&nick=*波",
    }

    TKAppEngine.shareInstance().callPlugin(
      {
        funcNo: "50115",
        param: param,
        moduleName: "TKOpen",
        callBackFunc: (data) => {
          //回调结果

          TKLog.debug('插件调用:calback数据回调:%{public}s', JSON.stringify(data))
        }
      })
  }

  plugin50230() {

    TKAppEngine.shareInstance().callPlugin({
      funcNo: '50230',
      moduleName: 'home',
      param: {
        // 'shareTypeList': '23,22,6,24,1',
        'shareTypeList': '22',
        'imageShare':  '1',
        'title': '标题',
        'content': '内容',
        'description': '描述',
        'imgUrl': "https://p3-pc.douyinpic.com/img/316630006a5c196dd9b6b~c5_300x300.jpeg?from=**********",
        'link': 'https://www.baidu.com'
      }
    })
  }

  plugin50231() {

    TKAppEngine.shareInstance().callPlugin({
      funcNo: '50231',
      moduleName: 'home',
      param: {
        // 'shareType': '22',  // 1-新浪微博 24-QQ好友 22-微信好友
        // 'imageShare': '1',
        // 'title': '标题',
        // 'content': '内容',
        // 'description': '描述',
        // 'imgUrl': "https://p3-pc.douyinpic.com/img/316630006a5c196dd9b6b~c5_300x300.jpeg?from=**********",
        // 'link': 'https://www.baidu.com'

        'content': "你好色彩",
        'description': "晋金财富",
        'funcNo': "50231",
        'imgUrl': "https://m.xintongfund.com/m/mall/images/120.png",
        'link': "https://www.baidu.com",
        'path': "pages/index/userRegistered/userRegistered?mobile=***********",
        'shareContentType': "10",
        'shareType': "22",
        'title': "晋金财富",
        'type': "0",
        'userName': "gh_396429a6778b",
        'webpageUrl': "https://www.baidu.com"
      }
    })
  }

  plugin80302() {

    TKAppEngine.shareInstance().callPlugin({
      funcNo: '80302',
      moduleName: 'home',
      param: {
      },
      callBackFunc: (data) => {
        TKLog.debug('80302插件调用:calback数据回调:%{public}s', JSON.stringify(data))
      }
    })

  }

  plugin60076() {

    TKAppEngine.shareInstance().callPlugin({
      funcNo: '60076',
      moduleName: 'home',
      param: {
        // 'funcNo': "60076",
        // 'path': "pages/index/userRegistered/userRegistered?mobile=***********",
        // 'miniprogramType': "0",
        // 'userName': "gh_396429a6778b",
        // 'webpageUrl': "https://www.baidu.com",
        // 'extData': "",
        // 'appld': ""

        'funcNo': "60076",
        // 'path': "pages/home/<USER>",
        'path' : 'pages/account/wealthAdvisor/wealthAdvisor?mobile=BxqiCCcS+x8A2BfLL4NpSg==',
        'miniprogramType': "2",
        // 'userName': "gh_212a2f9d51d8",
        'userName': "gh_396429a6778b",
        // 'webpageUrl': "https://www.baidu.com",
        'extData': "",
        'appld': "",
        'universalLink': ""
      },
      callBackFunc: (data) => {
        TKLog.debug('80302插件调用:calback数据回调:%{public}s', JSON.stringify(data))
      }
    })

  }

  /**
   * 测试指纹认证
   */
  async testFingerprintAuth() {
    TKLog.info('[思迪晋金财富入口页面]:开始测试指纹认证');

    const callback: TKBiometricAuthCallback = {
      onSuccess: (result: TKBiometricAuthResult) => {
        TKLog.info(`[思迪晋金财富入口页面]:指纹认证成功: ${JSON.stringify(result)}`);
        promptAction.showToast({ message: '指纹认证成功！' });
      },
      onFailed: (result: TKBiometricAuthResult) => {
        TKLog.warn(`[思迪晋金财富入口页面]:指纹认证失败: ${JSON.stringify(result)}`);
        promptAction.showToast({ message: `指纹认证失败: ${result.errorMessage}` });
      },
      onCancel: () => {
        TKLog.info(`[思迪晋金财富入口页面]:用户取消指纹认证`);
        promptAction.showToast({ message: '用户取消指纹认证' });
      }
    };

    TKBiometricAuthManager.authenticate(TKBiometricAuthType.FINGERPRINT, callback);
  }

  /**
   * 测试人脸认证
   */
  async testFaceAuth() {
    TKLog.info('[思迪晋金财富入口页面]:开始测试人脸认证');

    const callback: TKBiometricAuthCallback = {
      onSuccess: (result: TKBiometricAuthResult) => {
        TKLog.info(`[思迪晋金财富入口页面]:人脸认证成功: ${JSON.stringify(result)}`);
        promptAction.showToast({ message: '人脸认证成功！' });
      },
      onFailed: (result: TKBiometricAuthResult) => {
        TKLog.warn(`[思迪晋金财富入口页面]:人脸认证失败: ${JSON.stringify(result)}`);
        promptAction.showToast({ message: `人脸认证失败: ${result.errorMessage}` });
      },
      onCancel: () => {
        TKLog.info(`[思迪晋金财富入口页面]:用户取消人脸认证`);
        promptAction.showToast({ message: '用户取消人脸认证' });
      }
    };

    TKBiometricAuthManager.authenticate(TKBiometricAuthType.FACE, callback);
  }

  /**
   * 测试复合认证
   */
  testCompositeAuth() {
    TKLog.info('[思迪晋金财富入口页面]:开始测试复合认证');

    const callback: TKBiometricAuthCallback = {
      onSuccess: (result: TKBiometricAuthResult) => {
        TKLog.info(`[思迪晋金财富入口页面]:复合认证成功: ${JSON.stringify(result)}`);
        promptAction.showToast({ message: '复合认证成功！' });
      },
      onFailed: (result: TKBiometricAuthResult) => {
        TKLog.warn(`[思迪晋金财富入口页面]:复合认证失败: ${JSON.stringify(result)}`);
        promptAction.showToast({ message: `复合认证失败: ${result.errorMessage}` });
      },
      onCancel: () => {
        TKLog.info(`[思迪晋金财富入口页面]:用户取消复合认证`);
        promptAction.showToast({ message: '用户取消复合认证' });
      }
    };

    TKBiometricAuthManager.authenticate(TKBiometricAuthType.COMPOSITE, callback);
  }

  /**
   * 检查生物识别支持状态
   */
  async checkBiometricSupport() {
    TKLog.info('[思迪晋金财富入口页面]:开始检查生物识别支持状态');

    try {
      const supportedTypes = await TKBiometricAuthManager.getSupportedAuthTypes();
      TKLog.info(`[思迪晋金财富入口页面]:支持的认证类型: ${JSON.stringify(supportedTypes)}`);

      let userMessage = '';
      if (supportedTypes.length === 0) {
        userMessage = '当前设备不支持生物识别，或需要先录入指纹/人脸信息\n请前往"设置 > 生物识别与密码"进行配置';
      } else {
        const typeNames = supportedTypes.map(type => {
          switch(type) {
            case TKBiometricAuthType.FINGERPRINT: return '指纹';
            case TKBiometricAuthType.FACE: return '人脸';
            case TKBiometricAuthType.COMPOSITE: return '复合认证';
            default: return `类型${type}`;
          }
        });
        userMessage = `✅ 设备支持: ${typeNames.join('、')}认证`;
      }

      promptAction.showToast({
        message: userMessage,
        duration: 3000
      });
    } catch (error) {
      TKLog.error(`[思迪晋金财富入口页面]:检查生物识别支持状态失败: ${JSON.stringify(error)}`);
      promptAction.showToast({ message: '检查生物识别支持状态失败' });
    }
  }

  /**
   * 测试80319同步指纹验证插件
   */
  test80319FingerprintAuth() {
    TKLog.info('[思迪晋金财富入口页面]:开始测试80319同步指纹验证插件');

    // 构建测试参数
    let param = {
      "funcNo": '80319',
      "testMode": true, // 标识这是测试调用
      "timestamp": Date.now()
    } as Record<string, Object>;

    // 调用插件
    TKPluginInvokeCenter.shareInstance().callPlugin({
      funcNo: '80319',
      param: param,
      moduleName: 'TKJJCFPlugin',
      isH5: false,
      callBackFunc: (data: Record<string, Object>) => {
        // 处理回调结果
        TKLog.info(`[思迪晋金财富入口页面]:80319插件回调结果: ${JSON.stringify(data)}`);

        if (data.errorNo === '0') {
          promptAction.showToast({ message: '80319指纹验证成功！' });
        } else {
          promptAction.showToast({ message: `80319指纹验证失败: ${data.errorInfo}` });
        }
      }
    });

    promptAction.showToast({ message: '80319同步指纹验证插件已调用' });
  }

  /**
   * 测试80321异步指纹数据同步插件
   */
  test80321FingerprintSync() {
    TKLog.info('[思迪晋金财富入口页面]:开始测试80321异步指纹数据同步插件');

    // 构建测试参数
    let param = {
      "funcNo": '80321',
      "testMode": true, // 标识这是测试调用
      "timestamp": Date.now()
    } as Record<string, Object>;

    // 调用插件
    TKPluginInvokeCenter.shareInstance().callPlugin({
      funcNo: '80321',
      param: param,
      moduleName: 'TKJJCFPlugin',
      isH5: false,
      callBackFunc: (data: Record<string, Object>) => {
        // 处理回调结果（注意：80321插件通常不会有回调）
        TKLog.info(`[思迪晋金财富入口页面]:80321插件回调结果: ${JSON.stringify(data)}`);

        if (data && data.errorNo) {
          if (data.errorNo === '0') {
            promptAction.showToast({ message: '80321指纹数据同步成功！' });
          } else {
            promptAction.showToast({ message: `80321指纹数据同步失败: ${data.errorInfo}` });
          }
        }
      }
    });

    promptAction.showToast({ message: '80321异步指纹数据同步插件已调用' });
  }
}