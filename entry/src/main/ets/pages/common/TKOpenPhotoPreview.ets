import { webview } from '@kit.ArkWeb';
import { TKPhotoPreviewAttribute } from './TKPhotoPreviewAttribute';
import { TKLog } from '@thinkive/tk-harmony-base';

/**
 * 照片预览组件
 * 用于显示拍照后的预览界面,包含示例图和操作按钮
 */
@Component
export struct TKOpenPhotoPreview {
  // 是否需要显示示例图
  @Prop isNeedSample: boolean = true;
  // 图片类型,默认为4
  @Prop imgType?: string = "4";
  // 示例图资源
  @Prop sampleImg: ResourceStr = $r("app.media.tk_open_plugin_common_front_example_icon");
  // 显示的图片字符串
  @Prop showImgStr: string = "证件识别不全";
  // 左侧按钮文本
  @Prop leftBtn: string = '重拍';
  // 右侧按钮文本
  @Prop rightBtn: string = '提交';
  // 标题文本
  @Prop tvSampleTitle: string = '请您核对照片';
  // 示例图提示文本
  @Prop tvSamplePicTip: string = '请参考样例，确保身份证照片';
  // 折叠屏展开边距
  @Prop isFoldSpace: number = 0
  // 旋转角度
  @Prop rotateAngle: number = 360
  // 是否显示进度
  @Prop isShowProgress: boolean = false
  // Web控制器
  @State webController: webview.WebviewController = new webview.WebviewController();
  // 样式属性
  @Prop styleAttribute: TKPhotoPreviewAttribute = new TKPhotoPreviewAttribute

  // 左侧按钮点击回调
  leftClick?: () => void
  // 右侧按钮点击回调
  rightClik?: () => void

  build() {

    Stack() {
      Column() {
        //除开按钮外的上半部分布局
        Row() {
          //上半部分的左边部分（包括提示语和拍照的照片）
          Column() {
            //提示语
            Web({ src: '', controller: this.webController })
              .backgroundColor('#232323')
              .zoomAccess(false)
              .hitTestBehavior(HitTestMode.None)
              .width(400)
              .height('12%')
              .onControllerAttached(() => {
                TKLog.debug('[思迪照片预览组件]:Web控制器已附加')
                this.webController.loadData(
                  "<html><p  style=\"color: #FFFFFF;font-size: 35px;text-align: center; padding-top: 10px;font-weight: bold\" >\n" +
                  this.tvSampleTitle +
                  "</p></html>",
                  "text/html",
                  "UTF-8",
                  " ",
                  " "
                );
              })

            // 照片预览
            Image(this.showImgStr)
              .width('80%')
              .height('88%')
              .objectFit(ImageFit.Contain)
          }
          .width(this.isNeedSample ? '60%' : '100%')
          .height('100%')

          //上半部分的右边部分（包括示例图和示例提示语）
          Column() {
            // 示例图
            Image(this.sampleImg)
              .width(200)
              .height(126)
              .objectFit(ImageFit.Contain)
            //示例图提示语
            Text() {
              Span(this.tvSamplePicTip)
                .fontColor($r('sys.color.white'))
                .opacity(0.6)

              Span('\n文字清晰、边角完整。')
                .fontColor('#FFA900')
                .opacity(0.6)
            }
            .fontSize(16)
            .fontFamily('PingFang SC')
            .fontWeight(FontWeight.Medium)
            .width('80%')
            .margin({ top: 20 })
            .height(48)
            .textAlign(TextAlign.Start)
            .fontColor($r('sys.color.white'))

          }
          .height('100%')
          .width('40%')
          .margin({ top: '10%' })
          .alignItems(HorizontalAlign.Start)
          .visibility(this.isNeedSample ? Visibility.Visible : Visibility.Hidden)

        }
        .height('80%')
        .width('100%')

        //页面下半部分（重拍，提交按钮）
        Column() {
          Row() {
            // 重拍按钮
            Button() {
              Text(this.leftBtn)
                .fontSize(16)
                .fontColor($r('sys.color.white'))
            }
            .width(214)
            .height(44)
            .backgroundColor('#0000')
            .borderWidth(2)
            .borderColor($r('sys.color.white'))
            .borderRadius(22)
            .onClick(() => {
              TKLog.debug('[思迪照片预览组件]:点击重拍按钮')
              if (this.leftClick) this.leftClick()
            })

            // 提交按钮
            Button() {
              Text(this.rightBtn)
                .fontSize(16)
                .fontColor($r('sys.color.white'))
            }
            .width(214)
            .height(44)
            .backgroundColor(this.styleAttribute.fontColor ?? "#2F85FF")
            .borderRadius(22)
            .margin({ left: 20 })
            .onClick(() => {
              TKLog.debug('[思迪照片预览组件]:点击提交按钮')
              if (this.rightClik) this.rightClik()
            })
          }
          .height('100%')
          .width('100%')
          .justifyContent(FlexAlign.Center)
        }
        .height('20%')
        .width('100%')
        .alignItems(HorizontalAlign.Center)

      }
      .backgroundColor('#232323')
      .padding({ top: this.isFoldSpace, bottom: this.isFoldSpace })

      // 加载进度提示
      Column() {
        Image($r("app.media.tk_open_base_preview_fast_forward"))
          .width(40)
          .height(40)
          .rotate({ angle: this.rotateAngle })
          .animation({
            duration: 2000,
            curve: Curve.Linear,
            delay: 0,
            iterations: -1, // 设置-1表示动画无限循环
            playMode: PlayMode.Normal
          })

        Text('正在努力识别, 请稍等...')
          .fontColor('#fff')
          .margin({ top: 20 })
      }
      .justifyContent(FlexAlign.Center)
      .width('100%')
      .height('100%')
      .visibility(this.isShowProgress ? Visibility.Visible : Visibility.Hidden)
    }
  }
}