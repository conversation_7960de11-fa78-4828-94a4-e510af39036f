import { TKStyleAttribute, TKThemeManager } from '@thinkive/tk-harmony-base';
import { TKLog } from '@thinkive/tk-harmony-base';

/**
 * 图片预览属性类
 * 用于配置图片预览界面的主题样式属性
 */
export class TKPhotoPreviewAttribute extends TKStyleAttribute {
  // 主题配置名称
  themeConfigName: string = 'TKOpenConfig';

  // 获取主题颜色
  themeColor: string = TKThemeManager.shareInstance().getCssRulesetByClassName(this.themeConfigName).fontColor as string;

  // 字体颜色,默认使用主题色
  fontColor?: string = this.themeColor;

  // 图片资源
  image?: Resource = TKThemeManager.shareInstance().getCssRulesetByClassName(this.themeConfigName).image;

  constructor() {
    super();
    TKLog.info(`[TKPhotoPreviewAttribute] 初始化图片预览属性, themeColor: ${this.themeColor}`);
  }

  /**
   * 更新主题配置
   * @param configName 主题配置名称
   */
  updateThemeConfig(configName: string) {
    this.themeConfigName = configName;
    this.themeColor = TKThemeManager.shareInstance().getCssRulesetByClassName(this.themeConfigName).fontColor as string;
    this.fontColor = this.themeColor;
    this.image = TKThemeManager.shareInstance().getCssRulesetByClassName(this.themeConfigName).image;
    TKLog.info(`[TKPhotoPreviewAttribute] 更新主题配置, configName: ${configName}, themeColor: ${this.themeColor}`);
  }
}