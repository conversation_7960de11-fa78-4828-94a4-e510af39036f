import { TKLog } from '@thinkive/tk-harmony-base'
import { liveViewManager } from '@kit.LiveViewKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { Want, wantAgent } from '@kit.AbilityKit';

export default class TKOpenLiveViewManager {
  private isEnabled: boolean = false;
  private static instance: TKOpenLiveViewManager
  private sequenceId: number=1;

  public static getInstance() {
    if (TKOpenLiveViewManager.instance == null) {
      TKOpenLiveViewManager.instance = new TKOpenLiveViewManager()
    }
    return TKOpenLiveViewManager.instance
  }

  //检查实况窗是否有权限
  public checkLiveViewManagerStatus() {
    let  that=this;
    try {
      liveViewManager.isLiveViewEnabled().then((isEnabled: boolean) => {
        TKLog.info(`[思迪实况窗工具]获取实况窗可用状态成功，是否可用：${isEnabled}`)
        if (isEnabled) {
          // this.startLiveView();
          that.isEnabled=true;
          // that.isLiveViewExists=false;
        }
      }).catch((err: BusinessError) => {
        TKLog.error(`[思迪实况窗工具]获取实况窗可用状态失败，code = ${err.code}, msg = ${err.message}`)
      });
    } catch (err) {
      TKLog.error(`[思迪实况窗工具]获取实况窗可用状态异常，code = ${err.code}, msg = ${err.message}`)

    }
  }

  /**
   * 打开排队样式实况窗，已经打开的就update
   * @param currentPosition 当前位置
   * @param waitingTime 预估等待时间
   * @param currentProgress 当前进度0-100
   * @param highlightTextColor高亮文本颜色（#FF固定开头后跟十六进制值；例如：#FF007DFF）
   */
  public async  startQueueLiveView(currentPosition?:string,waitingTime?:string,currentProgress?:number,highlightTextColor?:string): Promise<void> {
    try {

      TKLog.info(`[思迪实况窗工具]准备开启或更新实况窗`)

      let location="10";
      if (currentPosition) {
        location=currentPosition;
      }
      let estimatedTime="10"
      if (waitingTime) {
        estimatedTime=waitingTime;
      }

      let capsuleTitle="排队中";
      let capsuleIcon="tk_open_liveview_capsule3.png";
      let layoutDataNodeIcons=["tk_open_liveview_node1.png", "tk_open_liveview_node3.png", "tk_open_liveview_node4.png"]
      if (currentProgress){
        if (currentProgress>50) {
           capsuleTitle="即将接通";
           capsuleIcon="tk_open_liveview_capsule1.png";
          // let layoutDataNodeIcons=["tk_open_liveview_node1.png", "tk_open_liveview_node2.png", "tk_open_liveview_node4.png"]

        }
      }

      // 获取当前时间的时间戳（秒级）
      this.sequenceId= Math.floor(Date.now() / 1000);

      // 定义创建的liveVIew
      let liveView: liveViewManager.LiveView = {
        id: 9527,
        event: "DELIVERY",
        sequence: this.sequenceId,
        isMute: false,
        liveViewData: {
          primary: {
            title: "排队中",
            content: [
              { text: "当前排在第 " },
              { text: location, textColor:highlightTextColor?highlightTextColor: "#FF007DFF" },
              { text: "位 | 预计" },
              { text: estimatedTime, textColor:highlightTextColor?highlightTextColor: "#FF007DFF"},
              { text: "分钟" }
            ], // 所有文本仅能设置为一种颜色，不设置textColor时，默认展示#FF000000
            keepTime: 1,
            clickAction:await this.buildWantAgent(getContext(this).applicationInfo.name),

            layoutData: {
              layoutType: liveViewManager.LayoutType.LAYOUT_TYPE_PROGRESS,
              progress: currentProgress?currentProgress:10,
              color: "#FF007DFF",
              backgroundColor: "#FF000000",
              indicatorType: liveViewManager.IndicatorType.INDICATOR_TYPE_UP,
              indicatorIcon: "tk_open_liveview_indicator.png", // 进度条指示器图标，取值为“/resource/rawfile”路径下的文件名
              lineType: liveViewManager.LineType.LINE_TYPE_NORMAL_SOLID_LINE,
              nodeIcons: layoutDataNodeIcons// 进度条每个节点图标，取值为“/resource/rawfile”路径下的文件名
            },

          },
          // 实况胶囊相关参数
          capsule: {
            type: liveViewManager.CapsuleType.CAPSULE_TYPE_TEXT,
            status: 1,
            icon: capsuleIcon,
            backgroundColor: "#FF007DFF",
            title: capsuleTitle,
            content: "..."
          }
        ,
          //
          // external: {
          //   title:"排队中",
          //   content: [
          //     { text: "当前排在第 " },
          //     { text: "10 ", textColor: "#FF007DFF" },
          //     { text: "位 | 预计" },
          //     { text: "10 ", textColor: "#FF007DFF" },
          //     { text: "分钟" }
          //   ], // 所有文本仅能设置为一种颜色，不设置textColor时，默认展示#FF000000
          //   backgroundColor:$r('sys.color.black')
          // }
        }
      };



        liveViewManager.startLiveView(liveView).then((liveViewResult: liveViewManager.LiveViewResult) => {
          TKLog.info(`[思迪实况窗工具]开启实况窗成功，result = ${JSON.stringify(liveViewResult)}`)
        }).catch((err: BusinessError) => {
          TKLog.error(`[思迪实况窗工具]开启实况窗失败，code = ${err.code}, msg = ${err.message}`)

          if (err.code==1003500006) {
            liveViewManager.updateLiveView(liveView).then((liveViewResult: liveViewManager.LiveViewResult) => {
              TKLog.info(`[思迪实况窗工具]更新实况窗成功，result = ${JSON.stringify(liveViewResult)}`)
            }).catch((err: BusinessError) => {
              TKLog.error(`[思迪实况窗工具]更新实况窗失败，code = ${err.code}, msg = ${err.message}`)
            });
          }
        });
      // }


    } catch (err) {
      TKLog.error(`[思迪实况窗工具]开启或更新实况窗异常，code = ${err.code}, msg = ${err.message}`)
    }
  }

  /**
   * 关闭排队样式实况窗
   */
  public async  stopQueueLiveView(): Promise<void> {
    try {

      TKLog.info(`[思迪实况窗工具]准备关闭实况窗`)

      // 获取当前时间的时间戳（秒级）
      this.sequenceId= Math.floor(Date.now() / 1000);

      // 定义创建的liveVIew
      let liveView: liveViewManager.LiveView = {
        id: 9527,
        event: "DELIVERY",
        sequence: this.sequenceId,
        isMute: false,
        liveViewData: {
          primary: {
            title: "排队中",
            content: [
              { text: "当前排在第 " },
              { text: "0", textColor: "#FF007DFF" },
              { text: "位 | 预计" },
              { text: "0", textColor:"#FF007DFF"},
              { text: "分钟" }
            ], // 所有文本仅能设置为一种颜色，不设置textColor时，默认展示#FF000000
            keepTime: 1,
            clickAction:await this.buildWantAgent(getContext(this).applicationInfo.name),

            layoutData: {
              layoutType: liveViewManager.LayoutType.LAYOUT_TYPE_PROGRESS,
              progress: 0,
              color: "#FF007DFF",
              backgroundColor: "#FF000000",
              indicatorType: liveViewManager.IndicatorType.INDICATOR_TYPE_UP,
              indicatorIcon: "tk_open_liveview_indicator.png", // 进度条指示器图标，取值为“/resource/rawfile”路径下的文件名
              lineType: liveViewManager.LineType.LINE_TYPE_NORMAL_SOLID_LINE,
              nodeIcons: ["tk_open_liveview_node1.png", "tk_open_liveview_node2.png", "tk_open_liveview_node4.png"] // 进度条每个节点图标，取值为“/resource/rawfile”路径下的文件名
            },

          },
          // 实况胶囊相关参数
          capsule: {
            type: liveViewManager.CapsuleType.CAPSULE_TYPE_TEXT,
            status: 1,
            icon: "tk_open_liveview_capsule1.png",
            backgroundColor: "#FF007DFF",
            title: "排队中",
            content: "..."
          }
        ,
          //
          external: {
            title:"排队中",
            content: [
              { text: "当前排在第 " },
              { text: "10 ", textColor: "#FF007DFF" },
              { text: "位 | 预计" },
              { text: "10 ", textColor: "#FF007DFF" },
              { text: "分钟" }
            ], // 所有文本仅能设置为一种颜色，不设置textColor时，默认展示#FF000000
            backgroundColor:"#000000"
          }
        }
      };

      liveViewManager.stopLiveView(liveView).then((liveViewResult: liveViewManager.LiveViewResult) => {
        TKLog.info(`[思迪实况窗工具]关闭实况窗成功，result = ${JSON.stringify(liveViewResult)}`)
      }).catch((err: BusinessError) => {
        TKLog.error(`[思迪实况窗工具]关闭实况窗失败，code = ${err.code}, msg = ${err.message}`)
      });


    } catch (err) {
      TKLog.error(`[思迪实况窗工具]关闭实况窗异常，code = ${err.code}, msg = ${err.message}`)
    }
  }



  async  buildWantAgent(appBundleName:string): Promise<Want> {
    const wantAgentInfo: wantAgent.WantAgentInfo = {
      wants: [
        {
          bundleName: appBundleName,
          abilityName: 'EntryAbility',
        } as Want
      ],
      operationType: wantAgent.OperationType.START_ABILITIES,
      requestCode: 0,
      wantAgentFlags: [wantAgent.WantAgentFlags.UPDATE_PRESENT_FLAG]
    };
    try {
      const agent = await wantAgent.getWantAgent(wantAgentInfo);
      return agent;
    } catch (e) {
      const err: BusinessError = e as BusinessError;
      TKLog.error(`[思迪实况窗工具]获取wantAgent异常，code = ${err.code}, msg = ${err.message}`)
      throw e as Error;
    }
  }
}


