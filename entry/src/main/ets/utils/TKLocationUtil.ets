import { BusinessError } from '@kit.BasicServicesKit';
import { TKLog } from '@thinkive/tk-harmony-base';
import { geoLocationManager } from '@kit.LocationKit';

export enum TKLocationError {
  unavailable = 3301000, // Location service is unavailable.
  off         = 3301100, // The location switch is off.
  Fail        = 3301200, // Failed to obtain the geographical location.
  ReverseFail = 3301300, //Reverse geocoding query failed.
}

export class TKLocationUtil {

   static getCurrentLocation(): Promise<geoLocationManager.Location> {

     const requestInfo: geoLocationManager.LocationRequest = {
       'priority': geoLocationManager.LocationRequestPriority.FIRST_FIX,
       'scenario': geoLocationManager.LocationRequestScenario.UNSET,
       'timeInterval': 1,
       'distanceInterval': 0,
       'maxAccuracy': 0
     };

     return new Promise<geoLocationManager.Location>((resolve, reject) => {
       TKLog.debug(`[思迪位置工具]:开始获取定位，定位条件为${JSON.stringify(requestInfo)}`)

       try {
           geoLocationManager.getCurrentLocation(requestInfo).then(location => {

             TKLog.debug(`[思迪位置工具]:定位成功，地址为${JSON.stringify(location)}`)
             resolve(location)
           }).catch((error: BusinessError) => {
             TKLog.debug(`[思迪位置工具]:定位失败，code为${error.code}, msg为${error.message}`)
             reject(error)
           })
       } catch (error) {
         TKLog.debug(`[思迪位置工具]:定位失败，code为${error.code}, msg为${error.message}`)
         reject(error)
       }
     })
  }

  static getAddressesFromLocation(location: geoLocationManager.Location): Promise<geoLocationManager.GeoAddress> {
    return new Promise<geoLocationManager.GeoAddress>((resolve, reject) => {
      TKLog.debug(`[思迪位置工具]:开始逆地理编码`)
      try {
        let isAvailable = geoLocationManager.isGeocoderAvailable();
        if (isAvailable) {
          let reverseGeocodeRequest: geoLocationManager.ReverseGeoCodeRequest = {
            "latitude": location.latitude,
            "longitude": location.longitude,
            "maxItems": 1
          };
          // try {
            geoLocationManager.getAddressesFromLocation(reverseGeocodeRequest, (error, data) => {
              if (error) {
                TKLog.debug(`[思迪位置工具]:逆地理编码失败，code为${error.code}, msg为${error.message}`)
                reject(error)
              } else {
                let geoAddress = data[0]
                TKLog.debug(`[思迪位置工具]:逆地理编码成功，geoAddress${JSON.stringify(data)}`)
                resolve(geoAddress)
              }
            });
          // } catch (error) {
          //   TKLog.debug(`[思迪位置工具]:逆地理编码失败，code为${error.code}, msg为${error}`)
          //   reject(error)
          // }
        }
      } catch (error) {
        TKLog.debug(`[思迪位置工具]:逆地理编码失败，code为${error.code}, msg为${error.message}`)
        reject(error)
      }
    })
  }
}
