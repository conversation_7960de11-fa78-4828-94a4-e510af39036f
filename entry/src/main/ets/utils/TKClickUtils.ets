import { systemDateTime } from '@kit.BasicServicesKit';
import { TKLog } from '@thinkive/tk-harmony-base';

export class ClickUtils {
  constructor() {
  }

  private tagCache: Map<String, number> = new Map<String, number>();
  // /**
  //  * 按钮过快点击检测的判断时间间隔
  //  */
  // private INPUT_TIME_DIVIDER: number = 2000;

  /**
   * @return 是否单击的过快了
   * @param tag
   */
  clickTooFast(tag: string, limit: number = 2000): boolean {
    let lastTime = this.tagCache.get(tag);
    let curClickTime = systemDateTime.getTime()
    TKLog.info("当前时间" + curClickTime)
    let fast: boolean = false;
    if (lastTime != null) {
      fast = (curClickTime - lastTime) < limit;
    }
    if (lastTime == null || !fast) {
      this.tagCache.set(tag, curClickTime);
    }
    return fast;
  }
}

export default new ClickUtils();