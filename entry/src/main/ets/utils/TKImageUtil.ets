import { image } from '@kit.ImageKit';
import { TKLog } from '@thinkive/tk-harmony-base';
import { BusinessError } from '@kit.BasicServicesKit';
import fs from '@ohos.file.fs';

export class TKImageUtil {
  constructor() {
  }

  /**
   * 通过surfaceId获取一张JPG格式图片
   * @param surfaceId 一般位XComponentController的surfaceId
   * @param height 图片高
   * @param width 图片宽
   * @param quality 压缩质量，取值[0-100]，默认100不压缩
   * @returns
   */
  async getJpgUint8ArrayBySurfaceId(surfaceId: string, width: number, height: number, quality: number = 100, rotate?: number): Promise<Uint8Array | undefined> {
    let pixelMap: PixelMap = await this.createPixelMap(surfaceId, width, height);
    return this.getJpgUint8ArrayByPixelMap(pixelMap, quality, rotate);
  }

  /**
   * 通过PixelMap获取JPG格式图片
   * @param pixelMap
   * @param quality
   * @param rotate
   * @returns
   */
  async getJpgUint8ArrayByPixelMap(pixelMap: PixelMap, quality: number = 100, rotate?: number): Promise<Uint8Array | undefined> {
    let imagePackerApi = image.createImagePacker();
    let packOpts : image.PackingOption = { format:"image/jpeg", quality: quality };
    let data : ArrayBuffer | undefined

    rotate && rotate != 0 ? await pixelMap.rotate(rotate) : undefined

    await imagePackerApi.packing(pixelMap, packOpts).then((data_) => {
      data = data_
    }).catch((error : BusinessError) => {
      TKLog.info("getJpgUint8ArrayBySurfaceId:error::" + error)
    });
    return data? new Uint8Array(data) : undefined;
  }

  /**
   * 通过surfaceId获取一张位图
   * @param surfaceId 从XComponent组件获取的surfaceId。
   * @param width 图片宽
   * @param height 图片高
   * @returns
   */
  async createPixelMap(surfaceId: string, width: number, height: number): Promise<PixelMap> {
    let region: image.Region = {
      x: 0,
      y: 0,
      size: { width: width, height: height }
    };
    return image.createPixelMapFromSurface(surfaceId, region)
  }

  async createPixelMapByJPG(jpg: ArrayBuffer, width: number, height: number): Promise<PixelMap> {
    let region: image.InitializationOptions = {
      size: { width: width, height: height },
      pixelFormat: image.PixelMapFormat.RGBA_8888
    };
    return image.createPixelMap(jpg, region)
  }

  async createPixelMapByUri(uri: string): Promise<PixelMap> {
    TKLog.info("createPixelMapByUri:uri::" + uri)
    let f = fs.openSync(uri, fs.OpenMode.READ_ONLY);

    let imageSource: image.ImageSource = image.createImageSource(f.fd);
    let opts: image.DecodingOptions = {
      editable: true,
      desiredPixelFormat: image.PixelMapFormat.RGBA_8888 // opencv默认BGRA，所以这里要设置成BGRA和opencv保持一致
    }
    let chooseImage: PixelMap = await imageSource.createPixelMap(opts);
    return chooseImage;
  }


   async getSmallBitmap(array: ArrayBuffer, width: number, height: number): Promise<ArrayBuffer | undefined> {
    let data: ArrayBuffer | undefined
    let source: image.ImageSource = image.createImageSource(array);

    let info: image.ImageInfo = await source.getImageInfo();

    let pixelMap: PixelMap = await source.createPixelMap();

    let scaleNum: number = this.calculateInSampleSize(info.size.height, info.size.width, width, height);

    await pixelMap.scale(scaleNum, scaleNum);

    let imagePackerApi = image.createImagePacker();
    let packOpts: image.PackingOption = { format: "image/jpeg", quality: 98 };
    await imagePackerApi.packing(pixelMap, packOpts).then((data_) => {
      data = data_;
    }).catch((error: BusinessError) => {
      TKLog.info("getJpgUint8ArrayBySurfaceId:error::" + error);
    });
    return data ? data : undefined;
  }

  /**
   * 计算图片的缩放值
   * 如果图片的原始高度或者宽度大与我们期望的宽度和高度，我们需要计算出缩放比例的数值。否则就不缩放。
   * heightRatio是图片原始高度与压缩后高度的倍数，
   * widthRatio是图片原始宽度与压缩后宽度的倍数。
   * inSampleSize就是缩放值 ，取heightRatio与widthRatio中最小的值。
   * inSampleSize为1表示宽度和高度不缩放，为2表示压缩后的宽度与高度为原来的1/2(图片为原1/4)。
   *
   * @param options
   * @param reqWidth
   * @param reqHeight
   * @return
   */
  calculateInSampleSize(outHeight: number, outWidth: number, reqWidth: number, reqHeight: number): number {
    let inSampleSize: number = 1;
    if (outHeight > reqHeight || outWidth > reqWidth) {
      let heightRatio: number = reqHeight / outHeight;
      let widthRatio: number = reqWidth / outWidth;
      inSampleSize = heightRatio < widthRatio ? widthRatio : heightRatio;
    }
    return inSampleSize;
  }


  /**
   * Bitmap质量压缩并存储
   *
   * @param bitmap   源图
   * @param IMG_SIZE 图片阈值
   */
  async qualityCompress(imageSource:ArrayBuffer, IMG_SIZE: number = 200): Promise<Uint8Array | undefined> {
    /**
     * 如果bitmap进行比例压缩后大小仍然大于200kb,则逐级进行质量压缩
     */
    let quality: number = 100;

    let data: ArrayBuffer | undefined
    try {

      let imageSourceApi: image.ImageSource = image.createImageSource(imageSource);

      data = await this.getJpgUint8ArrayByImageSource(imageSourceApi, quality)
      TKLog.debug("质量压缩前:" + (data ? data.byteLength : 0) / 1024 + " kb ");

      while ((data ? data.byteLength : 0) > IMG_SIZE * 1024 && quality > 1) {

        TKLog.debug("达到阈值,触发质量压缩");
        if (quality > 15) {
          let step: number = this.getStep((data ? data.byteLength : 0), IMG_SIZE * 1024);
          quality -= step;
        } else {
          quality -= 2;
        }
        TKLog.debug("质量压缩前:" + (data ? data.byteLength : 0) / 1024 + " kb ===" + quality);

        data = await this.getJpgUint8ArrayByImageSource(imageSourceApi, quality)
        TKLog.debug("质量压缩后:" + (data ? data.byteLength : 0) / 1024 + " kb ");
      }
    } catch (e) {
      e.printStackTrace();
    }
    return data ? new Uint8Array(data) : undefined
  }

  async getJpgUint8ArrayByImageSource(imageSource:  image.ImageSource, quality: number = 100): Promise<ArrayBuffer | undefined> {
    let imagePackerApi = image.createImagePacker();
    let packOpts : image.PackingOption = { format:"image/jpeg", quality: quality };
    let data : ArrayBuffer | undefined

    await imagePackerApi.packing(imageSource, packOpts).then((data_) => {
      data = data_
      TKLog.debug("质量压缩中:" + (data_ ? data_.byteLength : 0) / 1024 + " kb ===" + quality);
    }).catch((error : BusinessError) => {
      TKLog.info("getJpgUint8ArrayBySurfaceId:error::" + error)
    });
    return data? data : undefined;
  }

  /**
   * 压缩步长
   * @param srcLength
   * @param target
   * @return
   */
  getStep(srcLength: number, target: number): number {
    if (srcLength - target > target) { //一倍以上
      return 15;
    } else if (srcLength - target > target / 2) { //一半以上
      return 5;
    } else { //一半以内
      return 2;
    }
  }

  /**
   * NV21图片转JPG格式图片
   * @param source 原图（NV21数据）
   * @param width 原图宽
   * @param height 原图高
   * @param quality 压缩大小
   * @returns JPG格式图片
   */
  async getJpgUint8ArrayByNV21(source: ArrayBuffer, width: number, height: number, quality: number = 100, rotate?: number): Promise<Uint8Array | undefined> {
    let sourceOptions : image.SourceOptions = { sourceDensity: 120, sourceSize: {width: width, height: height}, sourcePixelFormat: image.PixelMapFormat.NV21 };
    const imageSource : image.ImageSource = image.createImageSource(source, sourceOptions);
    let pixelMap: PixelMap = await imageSource.createPixelMap()

    return this.getJpgUint8ArrayByPixelMap(pixelMap, quality, rotate);

    //以下代码能转jpg,但旋转角度有问题
    // const imagePacker : image.ImagePacker = image.createImagePacker();
    // let packOpts: image.PackingOption = { format: "image/jpeg", quality: quality };
    // let jpg: ArrayBuffer = await imagePacker.packing(imageSource, packOpts);
    //
    // if (rotate && rotate != 0 ) {
    //   let pixelMap: PixelMap = await this.createPixelMapByJPG(jpg, width, height)
    //   return this.getJpgUint8ArrayByPixelMap(pixelMap, 100, rotate)
    // } else {
    //   return new Uint8Array(jpg);
    // }
  }

  /**
   * RGB图片转JPG格式图片
   * @param source 原图（NV21数据）
   * @param width 原图宽
   * @param height 原图高
   * @param quality 压缩大小
   * @returns JPG格式图片
   */
  async getJpgUint8ArrayByRGB(source: ArrayBuffer, width: number, height: number, quality: number = 100, rotate?: number): Promise<Uint8Array | undefined> {
    let sourceOptions : image.SourceOptions = { sourceDensity: 120, sourceSize: {width: width, height: height}, sourcePixelFormat: image.PixelMapFormat.RGB_888 };
    const imageSource : image.ImageSource = image.createImageSource(source, sourceOptions);
    let pixelMap: PixelMap | undefined
    imageSource.createPixelMap().then((data_) => {
      pixelMap = data_
    }).catch((error : BusinessError) => {
      TKLog.info("getJpgUint8ArrayByRGB:error::" + error)
    });

    return pixelMap ? this.getJpgUint8ArrayByPixelMap(pixelMap, quality, rotate) : undefined;

  }


}

export default new TKImageUtil();