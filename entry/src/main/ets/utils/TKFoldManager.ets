import { display } from '@kit.ArkUI'
import { TKLog } from '@thinkive/tk-harmony-base'
import { TKFoldListener } from '../Interface/TKFoldListener'

// TKFoldManager类用于管理折叠屏的状态和监听器
export class TKFoldManager {
  // 单例实例
  private static instance: TKFoldManager 
  // 显示模式回调
  private static displayCallback: Callback<display.FoldDisplayMode> 
  // 状态回调
  private static statusCallback: Callback<display.FoldStatus> 

  // 获取单例实例
  public static getInstance() {
    if (TKFoldManager.instance == null) {
      TKFoldManager.instance = new TKFoldManager() // 创建新实例
    }
    TKFoldManager.initLister() // 初始化监听器
    return TKFoldManager.instance
  }

  // 初始化监听器
  static initLister() {
    try {
      // 设置显示模式回调
      TKFoldManager.displayCallback = (data: display.FoldDisplayMode) => {

        if (data == display.FoldDisplayMode.FOLD_DISPLAY_MODE_FULL) {
          TKLog.info(`[思迪折叠屏管理者]: FOLD_DISPLAY_MODE_FULL`);

          // 针对X5 全展开时UI更新回调
          TKFoldManager.listener?.foldDisplayFull ? TKFoldManager.listener?.foldDisplayFull() : undefined

        } else if (data == display.FoldDisplayMode.FOLD_DISPLAY_MODE_MAIN) {
          TKLog.info(`[思迪折叠屏管理者]: FOLD_DISPLAY_MODE_MAIN`);

          // 针对X5 折叠时UI更新回调
          // 针对packet2 全展开UI更新回调（待验证）
          TKFoldManager.listener?.foldDisplayMain ? TKFoldManager.listener?.foldDisplayMain() : undefined // 折叠时调用

        } else if (data == display.FoldDisplayMode.FOLD_DISPLAY_MODE_SUB) {
          TKLog.info(`[思迪折叠屏管理者]: FOLD_DISPLAY_MODE_SUB`);
          // 针对packet2 半折叠UI更新更新回调（待验证）

        } else if (data == display.FoldDisplayMode.FOLD_DISPLAY_MODE_COORDINATION) {
          TKLog.info(`[思迪折叠屏管理者]: FOLD_DISPLAY_MODE_COORDINATION`);

        }  else {
          TKLog.info(`[思迪折叠屏管理者]: FOLD_DISPLAY_MODE_UNKNOWN`);

        }
      };

      // 设置状态回调
      TKFoldManager.statusCallback = (data: display.FoldStatus) => {
        if (data == display.FoldStatus.FOLD_STATUS_EXPANDED) {
          TKLog.info(`[思迪折叠屏管理者]: Fold status=Expanded`); // 展开
          TKFoldManager.listener?.onFullOpen ? TKFoldManager.listener?.onFullOpen() : undefined // 全展开状态调用

        } else if (data == display.FoldStatus.FOLD_STATUS_HALF_FOLDED) {
          TKLog.info(`[思迪折叠屏管理者]: Fold status=HALF_FOLDED`); // 半折叠
          TKFoldManager.listener?.onHalfOpen ? TKFoldManager.listener?.onHalfOpen() : undefined // 半折叠状态调用

        } else if (data == display.FoldStatus.FOLD_STATUS_FOLDED) {
          TKLog.info(`[思迪折叠屏管理者]: Fold status=FOLDED`); // 折叠
          TKFoldManager.listener?.onFoldOpen ? TKFoldManager.listener?.onFoldOpen() : undefined // 折叠状态调用

        } else {
          TKLog.info(`[思迪折叠屏管理者]: Fold status=UNKNOWN`); // 未知
        }
      };
      // 注册折叠屏状态监听
      display.on('foldDisplayModeChange', TKFoldManager.displayCallback);
      display.on('foldStatusChange', TKFoldManager.statusCallback);
    } catch (exception) {
      TKLog.error(`[思迪折叠屏管理者]: Failed to register callback. Code=${JSON.stringify(exception)}`); // 打印错误信息
    }
  }

  static listener?: TKFoldListener | undefined = undefined; // 监听器

  // 设置监听器
  public setListener(listener: TKFoldListener) {
    TKFoldManager.listener = listener; // 保存监听器
  }

  // 释放监听器
  static release() {
    try {
      // 取消折叠屏状态监听
      if (TKFoldManager.displayCallback) {
        display.off('foldDisplayModeChange', TKFoldManager.displayCallback);
      }
      if (TKFoldManager.statusCallback) {
        display.off('foldStatusChange', TKFoldManager.statusCallback);
      }
    } catch (exception) {
      TKLog.error(`[思迪折叠屏管理者]: Failed to unregister callback. Code=${JSON.stringify(exception)}`); // 打印错误信息
    }
  }
}
