/**
 * 生物识别认证基类
 * 提供通用的认证功能和资源管理
 *
 */
import { TKLog } from '@thinkive/tk-harmony-base';
import { userAuth } from '@kit.UserAuthenticationKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKBiometricAuthResult, TKBiometricAuthCallback, TKBiometricAuthType } from './TKBiometricTypes';

/**
 * 认证基类
 * 提供通用的认证流程和资源管理
 */
export abstract class TKBaseAuth {
  protected userAuthInstance?: userAuth.UserAuthInstance;
  protected authParam?: userAuth.AuthParam;
  protected widgetParam?: userAuth.WidgetParam;
  protected authType: TKBiometricAuthType;
  protected authTypeName: string;
  protected lastErrorCode?: number;
  protected lastErrorMessage?: string;

  // AuthTrustLevel 配置获取函数，由门面类注入
  protected authTrustLevelProvider?: () => userAuth.AuthTrustLevel;

  constructor(authType: TKBiometricAuthType, authTypeName: string) {
    this.authType = authType;
    this.authTypeName = authTypeName;
    TKLog.debug(`[${this.authTypeName}认证]:初始化${this.authTypeName}认证类`);
  }

  /**
   * 设置 AuthTrustLevel 提供者函数
   * 由门面类调用，用于注入配置获取能力
   * @param provider AuthTrustLevel 提供者函数
   */
  public setAuthTrustLevelProvider(provider: () => userAuth.AuthTrustLevel): void {
    this.authTrustLevelProvider = provider;
    TKLog.debug(`[${this.authTypeName}认证]:AuthTrustLevel 提供者已设置`);
  }

  /**
   * 获取当前认证类型的 AuthTrustLevel
   * 优先使用门面类提供的配置，如果没有则使用默认值
   * @returns 信任级别
   */
  protected getAuthTrustLevel(): userAuth.AuthTrustLevel {
    if (this.authTrustLevelProvider) {
      return this.authTrustLevelProvider();
    }

    // 如果没有提供者，使用默认配置 ATL3（统一安全级别）
    return userAuth.AuthTrustLevel.ATL3;
  }

  /**
   * 检查设备是否支持该认证类型
   * @returns Promise<boolean> 是否支持
   */
  public async isSupported(): Promise<boolean> {
    try {
      userAuth.getAvailableStatus(
        this.getUserAuthType(),
        userAuth.AuthTrustLevel.ATL1
      );

      // 如果没有抛出异常，说明支持该认证方式且已录入信息
      TKLog.info(`[${this.authTypeName}认证]:设备${this.authTypeName}认证支持状态: true`);
      return true;
    } catch (error) {
      return this.handleSupportCheckError(error as BusinessError);
    }
  }

  /**
   * 处理支持状态检查的错误
   * @param error 业务错误
   * @returns boolean 是否支持
   */
  protected handleSupportCheckError(error: BusinessError): boolean {
    const errorCode = error.code;

    switch (errorCode) {
      case 12500010: // 指定类型的凭据未录入
        TKLog.warn(`[${this.authTypeName}认证]:设备支持${this.authTypeName}硬件但用户未录入${this.authTypeName}信息，错误码: ${errorCode}`);
        TKLog.info(`[${this.authTypeName}认证]:用户提示 - 请前往"设置 > 生物识别与密码"录入${this.authTypeName}信息`);
        this.lastErrorCode = 12500010;
        this.lastErrorMessage = `设备支持${this.authTypeName}硬件，但您尚未录入${this.authTypeName}信息。请前往"设置 > 生物识别与密码"进行录入。`;
        return false;
      case 12500005: // 认证类型不支持
        TKLog.info(`[${this.authTypeName}认证]:设备不支持${this.authTypeName}认证，错误码: ${errorCode}`);
        TKLog.info(`[${this.authTypeName}认证]:用户提示 - 当前设备不支持${this.authTypeName}识别功能`);
        this.lastErrorCode = 12500005;
        this.lastErrorMessage = `当前设备不支持${this.authTypeName}识别功能`;
        return false;
      case 12500002: // 通用错误
        TKLog.warn(`[${this.authTypeName}认证]:${this.authTypeName}认证参数无效或系统错误，错误码: ${errorCode}`);
        TKLog.info(`[${this.authTypeName}认证]:用户提示 - ${this.authTypeName}认证服务暂时不可用，请稍后重试`);
        this.lastErrorCode = 12500002;
        this.lastErrorMessage = `${this.authTypeName}认证服务暂时不可用，请稍后重试`;
        return false;
      default:
        TKLog.error(`[${this.authTypeName}认证]:检查${this.authTypeName}认证支持状态异常，错误码: ${errorCode}, 详情: ${JSON.stringify(error)}`);
        TKLog.info(`[${this.authTypeName}认证]:用户提示 - ${this.authTypeName}认证功能异常，错误码: ${errorCode}`);
        this.lastErrorCode = errorCode;
        this.lastErrorMessage = `${this.authTypeName}认证功能异常，错误码: ${errorCode}`;
        return false;
    }
  }

  /**
   * 执行认证
   * @param callback 认证回调
   */
  public async authenticate(callback: TKBiometricAuthCallback): Promise<void> {
    TKLog.info(`[${this.authTypeName}认证]:开始执行${this.authTypeName}认证`);
    
    try {
      // 检查设备支持状态
      const isSupported = await this.isSupported();
      if (!isSupported) {
        const errorResult: TKBiometricAuthResult = {
          success: false,
          authType: this.authType,
          errorCode: this.lastErrorCode || userAuth.UserAuthResultCode.NOT_ENROLLED,
          errorMessage: this.lastErrorMessage || `设备不支持${this.authTypeName}认证或未录入${this.authTypeName}信息`
        };
        callback.onFailed?.(errorResult);
        return;
      }

      // 准备认证参数
      await this.prepareAuthParams();
      
      // 获取认证实例
      this.userAuthInstance = userAuth.getUserAuthInstance(this.authParam!, this.widgetParam!);
      
      // 订阅认证结果
      this.userAuthInstance.on('result', {
        onResult: (result: userAuth.UserAuthResult) => {
          TKLog.info(`[${this.authTypeName}认证]:认证结果: ${JSON.stringify(result)}`);
          this.handleAuthResult(result, callback);
          // 取消订阅
          this.userAuthInstance?.off('result');
        }
      });

      // 开始认证
      this.userAuthInstance.start();
      TKLog.info(`[${this.authTypeName}认证]:${this.authTypeName}认证已启动`);
      
    } catch (error) {
      TKLog.error(`[${this.authTypeName}认证]:${this.authTypeName}认证执行异常: ${JSON.stringify(error)}`);
      const errorResult: TKBiometricAuthResult = {
        success: false,
        authType: this.authType,
        errorCode: -1,
        errorMessage: `${this.authTypeName}认证执行异常: ${error}`
      };
      callback.onFailed?.(errorResult);
    }
  }

  /**
   * 取消认证
   */
  public cancel(): void {
    try {
      if (this.userAuthInstance) {
        this.userAuthInstance.cancel();
        this.userAuthInstance.off('result');
        this.userAuthInstance = undefined;
        TKLog.info(`[${this.authTypeName}认证]:${this.authTypeName}认证已取消`);
      }
    } catch (error) {
      TKLog.error(`[${this.authTypeName}认证]:取消${this.authTypeName}认证异常: ${JSON.stringify(error)}`);
    }
  }

  /**
   * 准备认证参数（子类实现）
   */
  protected abstract prepareAuthParams(): Promise<void>;

  /**
   * 获取用户认证类型（子类实现）
   */
  protected abstract getUserAuthType(): userAuth.UserAuthType;

  /**
   * 处理认证结果
   * @param result 认证结果
   * @param callback 回调函数
   */
  protected handleAuthResult(result: userAuth.UserAuthResult, callback: TKBiometricAuthCallback): void {
    if (result.result === userAuth.UserAuthResultCode.SUCCESS) {
      // 认证成功
      const successResult: TKBiometricAuthResult = {
        success: true,
        authType: this.authType,
        errorCode: 0,
        token: result.token
      };
      callback.onSuccess?.(successResult);
    } else {
      // 认证失败
      const errorResult: TKBiometricAuthResult = {
        success: false,
        authType: this.authType,
        errorCode: result.result,
        errorMessage: this.getErrorMessage(result.result)
      };
      
      if (result.result === userAuth.UserAuthResultCode.CANCELED) {
        callback.onCancel?.();
      } else {
        callback.onFailed?.(errorResult);
      }
    }
  }

  /**
   * 获取错误信息
   * @param errorCode 错误码
   * @returns 错误信息
   */
  protected getErrorMessage(errorCode: number): string {
    switch (errorCode) {
      case userAuth.UserAuthResultCode.FAIL:
        return `${this.authTypeName}认证失败`;
      case userAuth.UserAuthResultCode.GENERAL_ERROR:
        return `${this.authTypeName}认证通用错误`;
      case userAuth.UserAuthResultCode.CANCELED:
        return `${this.authTypeName}认证被取消`;
      case userAuth.UserAuthResultCode.TIMEOUT:
        return `${this.authTypeName}认证超时`;
      case userAuth.UserAuthResultCode.TYPE_NOT_SUPPORT:
        return `设备不支持${this.authTypeName}认证`;
      case userAuth.UserAuthResultCode.TRUST_LEVEL_NOT_SUPPORT:
        return `${this.authTypeName}认证信任级别不支持`;
      case userAuth.UserAuthResultCode.BUSY:
        return `${this.authTypeName}认证服务忙碌`;
      // case userAuth.UserAuthResultCode.INVALID_PARAMETERS: // 此错误码不存在
      //   return `${this.authTypeName}认证参数无效`;
      case userAuth.UserAuthResultCode.LOCKED:
        return `${this.authTypeName}认证已锁定`;
      case userAuth.UserAuthResultCode.NOT_ENROLLED:
        return `未录入${this.authTypeName}信息`;
      case 401:
        return `${this.authTypeName}认证参数检查失败，请检查认证配置`;
      case 801:
        return `当前设备不支持${this.authTypeName}认证功能`;
      case 12500001:
        return `${this.authTypeName}认证失败`;
      case 12500003:
        return `${this.authTypeName}认证被取消`;
      case 12500004:
        return `${this.authTypeName}认证操作超时`;
      case 12500007:
        return `${this.authTypeName}认证服务繁忙，请稍后重试`;
      case 12500009:
        return `${this.authTypeName}认证被锁定`;
      case 12500011:
        return `${this.authTypeName}认证被用户取消`;
      default:
        return `${this.authTypeName}认证未知错误: ${errorCode}`;
    }
  }
}
