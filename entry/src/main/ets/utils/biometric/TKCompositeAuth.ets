/**
 * 复合认证实现类
 * 仅支持API 18+的真正复合认证功能
 * API 18以下系统将提示升级系统版本
 *
 */
import { userAuth } from '@kit.UserAuthenticationKit';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { deviceInfo } from '@kit.BasicServicesKit';
import { TKBiometricAuthType } from './TKBiometricTypes';
import { TKBaseAuth } from './TKBaseAuth';

/**
 * 复合认证类
 * 专用于API 18+的真正复合认证功能
 */
export class TKCompositeAuth extends TKBaseAuth {

  constructor() {
    super(TKBiometricAuthType.COMPOSITE, '复合生物识别');
  }

  /**
   * 获取用户认证类型
   */
  protected getUserAuthType(): userAuth.UserAuthType {
    // 复合认证需要返回多种类型，这里返回指纹作为主要类型
    // 实际的复合认证在prepareAuthParams中配置
    return userAuth.UserAuthType.FINGERPRINT;
  }

  /**
   * 检查设备是否支持复合认证（重写基类方法）
   * 仅支持API 18+，低版本直接返回false
   * @returns Promise<boolean> 是否支持复合认证
   */
  public async isSupported(): Promise<boolean> {

    try {
      // 检查API级别
      const currentApiLevel = deviceInfo.sdkApiVersion;

      if (currentApiLevel < 18) {
        // API 18以下不支持复合认证
        return false;
      }

      // API 18+：检查真正的复合认证支持
      return await this.checkMultiAuthSupport();
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查多重认证支持（API 18+）
   */
  private async checkMultiAuthSupport(): Promise<boolean> {
    let fingerprintSupported = false;
    let faceSupported = false;

    // 获取当前配置的信任级别
    const trustLevel = this.getAuthTrustLevel();

    try {
      userAuth.getAvailableStatus(
        userAuth.UserAuthType.FINGERPRINT,
        trustLevel
      );
      fingerprintSupported = true;
    } catch (error) {
      // 指纹不支持或未录入
    }

    try {
      userAuth.getAvailableStatus(
        userAuth.UserAuthType.FACE,
        trustLevel
      );
      faceSupported = true;
    } catch (error) {
      // 人脸不支持或未录入
    }

    // 多重认证需要至少支持一种生物识别方式
    return fingerprintSupported || faceSupported;
  }



  /**
   * 准备认证参数（重写基类方法）
   * 仅支持API 18+的复合认证
   */
  protected async prepareAuthParams(): Promise<void> {
    try {

      // 检查API级别
      const currentApiLevel = deviceInfo.sdkApiVersion;

      if (currentApiLevel < 18) {
        throw new Error('系统版本过低，复合认证功能需要API 18+，请升级系统版本');
      }

      // API 18+：使用真正的复合认证
      await this.prepareMultiAuth();

    } catch (error) {
      throw new Error(`复合生物识别认证参数准备失败: ${JSON.stringify(error)}`);
    }
  }



  /**
   * 准备多重认证（API 18+）
   */
  private async prepareMultiAuth(): Promise<void> {
    // 生成挑战值
    const rand = cryptoFramework.createRandom();
    const challengeLen = 32;
    const randData: Uint8Array = rand?.generateRandomSync(challengeLen)?.data;

    // 设置认证复用参数
    const reuseUnlockResult: userAuth.ReuseUnlockResult = {
      reuseMode: userAuth.ReuseMode.AUTH_TYPE_RELEVANT,
      reuseDuration: userAuth.MAX_ALLOWABLE_REUSE_DURATION,
    };

    // 获取支持的认证类型列表
    const supportedAuthTypes: userAuth.UserAuthType[] = [];

    // 获取当前配置的信任级别
    const trustLevel = this.getAuthTrustLevel();

    try {
      userAuth.getAvailableStatus(
        userAuth.UserAuthType.FINGERPRINT,
        trustLevel
      );
      supportedAuthTypes.push(userAuth.UserAuthType.FINGERPRINT);
    } catch (error) {
      // 指纹不支持或未录入
    }

    try {
      userAuth.getAvailableStatus(
        userAuth.UserAuthType.FACE,
        trustLevel
      );
      supportedAuthTypes.push(userAuth.UserAuthType.FACE);
    } catch (error) {
      // 人脸不支持或未录入
    }

    if (supportedAuthTypes.length === 0) {
      throw new Error('设备未录入任何生物识别信息，请先录入指纹或人脸信息');
    }

    // 配置认证参数 - API 18+支持多种认证类型，使用门面类提供的 AuthTrustLevel 配置
    this.authParam = {
      challenge: randData,
      authType: supportedAuthTypes,
      authTrustLevel: trustLevel, // 使用从门面类获取的配置
      reuseUnlockResult: reuseUnlockResult,
    };

    // 配置认证界面参数
    const authTypeNames = supportedAuthTypes.map(type => {
      switch(type) {
        case userAuth.UserAuthType.FINGERPRINT: return '指纹';
        case userAuth.UserAuthType.FACE: return '人脸';
        default: return '生物识别';
      }
    });

    this.widgetParam = {
      title: `${authTypeNames.join('或')}验证`,
      navigationButtonText: '取消'
    };
  }
}
