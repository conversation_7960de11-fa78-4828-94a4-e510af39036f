/**
 * 生物识别相关类型定义
 *
 */

/**
 * 生物识别认证类型枚举
 */
export enum TKBiometricAuthType {
  /** 指纹认证 */
  FINGERPRINT = 'fingerprint',
  /** 人脸认证 */
  FACE = 'face',
  /** 复合认证 (支持多种生物识别方式) */
  COMPOSITE = 'composite'
}

/**
 * 生物识别认证结果接口
 */
export interface TKBiometricAuthResult {
  /** 认证是否成功 */
  success: boolean;
  /** 认证类型 */
  authType: TKBiometricAuthType;
  /** 错误码 (成功时为0) */
  errorCode?: number;
  /** 错误信息 */
  errorMessage?: string;
  /** 认证令牌 (成功时返回) */
  token?: Uint8Array;
}

/**
 * 生物识别认证回调接口
 */
export interface TKBiometricAuthCallback {
  /** 认证成功回调 */
  onSuccess?: (result: TKBiometricAuthResult) => void;
  /** 认证失败回调 */
  onFailed?: (result: TKBiometricAuthResult) => void;
  /** 用户取消回调 */
  onCancel?: () => void;
}
