/**
 * 指纹认证实现类
 * 专门处理指纹识别认证功能
 *
 */
import { userAuth } from '@kit.UserAuthenticationKit';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKBiometricAuthType } from './TKBiometricTypes';
import { TKBaseAuth } from './TKBaseAuth';

/**
 * 指纹认证类
 * 实现指纹识别的具体功能
 */
export class TKFingerprintAuth extends TKBaseAuth {

  constructor() {
    super(TKBiometricAuthType.FINGERPRINT, '指纹');
  }

  /**
   * 获取用户认证类型
   */
  protected getUserAuthType(): userAuth.UserAuthType {
    return userAuth.UserAuthType.FINGERPRINT;
  }

  /**
   * 准备认证参数
   */
  protected async prepareAuthParams(): Promise<void> {
    try {
      // 生成挑战值
      const rand = cryptoFramework.createRandom();
      const challengeLen = 32; // 32字节挑战值
      const randData: Uint8Array = rand?.generateRandomSync(challengeLen)?.data;

      // 设置认证复用参数
      const reuseUnlockResult: userAuth.ReuseUnlockResult = {
        reuseMode: userAuth.ReuseMode.AUTH_TYPE_RELEVANT,
        reuseDuration: userAuth.MAX_ALLOWABLE_REUSE_DURATION,
      };

      // 配置认证参数 - 使用门面类提供的 AuthTrustLevel 配置
      this.authParam = {
        challenge: randData,
        authType: [userAuth.UserAuthType.FINGERPRINT],
        authTrustLevel: this.getAuthTrustLevel(), // 从门面类获取配置
        reuseUnlockResult: reuseUnlockResult,
      };

      // 配置认证界面参数
      this.widgetParam = {
        title: '指纹验证',
        navigationButtonText: '取消'
      };

    } catch (error) {
      throw new Error(`准备认证参数异常: ${JSON.stringify(error)}`);
    }
  }
}
