
import { buffer, util } from '@kit.ArkTS';

class TKStringUtil {
  private base64Helper: util.Base64Helper = new util.Base64Helper();

  constructor() {
  }

  /**
   * Base64 to uint8Array.
   *
   * @param content Base64 data.
   * @returns Uint8Array data.
   */
  base64ToUint8Array(content: string): Uint8Array {
    let uint8ArrayData: Uint8Array = this.base64Helper.decodeSync(content);
    return uint8ArrayData;
  }

  /**
   * Convert uint8Array to base64.
   *
   * @param content Uint8Array data.
   * @returns Base64 data.
   */
  uint8ArrayToBase64(content: Uint8Array): string {
    let base64Data: string = this.base64Helper.encodeToStringSync(content);
    return base64Data;
  }

  /**
   * Convert a character string to a uint8Array byte stream.
   *
   * @param content to be converted.
   * @returns Uint8Array data.
   */
  stringToUint8Array(content: string): Uint8Array {
    let arr:number[] = [];
    for (let i = 0, j = content.length; i < j; ++i) {
      arr.push(content.charCodeAt(i));
    }
    return new Uint8Array(arr);
  }

  /**
   * Encodes this String into a sequence of bytes using the platform's default charset, storing the result into a new byte array.
   * @param content
   * @returns
   */
  stringEncodeToUint8Array(content: string, encoding?: buffer.BufferEncoding): Uint8Array {
    return new Uint8Array(buffer.from(content, encoding).buffer);
  }

  /**
   * Encodes this String into a sequence of bytes using the platform's default charset, storing the result into a new byte array.
   * @param content
   * @returns
   */
  uint8ArrayDecodeToString(content: Uint8Array, encoding?: buffer.BufferEncoding): string {
    return buffer.from(content.buffer).toString(encoding)
  }

  /**
   * Converting uint8Array bytes to stream strings.
   *
   * @param content Uint8Array data.
   * @returns Converted string.
   */
  uint8ArrayToString(content: Uint8Array): string {
    let arrayString = '';
    for (let i = 0; i < content.length; i++) {
      arrayString += String.fromCharCode(content[i]);
    }
    return arrayString;
  }
}

export default new TKStringUtil();