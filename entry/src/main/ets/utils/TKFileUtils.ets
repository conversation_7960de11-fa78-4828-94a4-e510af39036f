import { BusinessError } from '@ohos.base';
import fs from '@ohos.file.fs';
import common from '@ohos.app.ability.common';
import { TKLog } from '@thinkive/tk-harmony-base';
import TKStringUtil from './TKStringUtil';
import TKImageUtil from './TKImageUtil';

export class TKFileUtils {

  static copyToFile(context: common.Context, sourcePath?: string): string {
    let destFilePath: string = "";

    TKLog.debug(`[思迪文件工具]:开始复制文件`)
    if (sourcePath) {
      let filesDir = context.filesDir;
      destFilePath = filesDir + "/" + sourcePath;
      let resourceManager = context.resourceManager;
      if (!fs.accessSync(destFilePath)) {
        let modelArray: Uint8Array | undefined = resourceManager.getRawFileContentSync(sourcePath)
        if (sourcePath.indexOf("/") != -1) {
          let dirs = sourcePath.split("/")
          for (let i = 0; i < dirs.length - 1; i++) {
            filesDir += "/" + dirs[i]
            if (!fs.accessSync(filesDir)) {
              try {
                fs.mkdirSync(filesDir)
                TKLog.debug(`[思迪文件工具]:创建文件夹成功，地址为${filesDir}`)
              } catch (error) {
                TKLog.debug(`[思迪文件工具]:创建文件夹失败，code为${error.code}, msg为${error}`)
              }
            }
          }
        }
        if (modelArray) {
          try {
            let file = fs.openSync(destFilePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
            fs.writeSync(file.fd, modelArray.buffer);
            fs.closeSync(file)
            TKLog.debug(`[思迪文件工具]:写入文件成功`)
          } catch (error) {
            TKLog.debug(`[思迪文件工具]:写入文件失败，code为${error.code}, msg为${error}`)
          }
        }
      }
    }

    destFilePath = destFilePath != undefined ? destFilePath : ''

    TKLog.debug(`[思迪文件工具]:复制文件完毕，地址为${destFilePath}`)
    return destFilePath;
  }

  static async copyToFileAsync(context: common.Context, sourcePath?: string): Promise<string> {
    let destFilePath: string = "";

    TKLog.debug(`[思迪文件工具]:开始复制文件`)
    if (sourcePath) {
      let filesDir = context.filesDir;
      destFilePath = filesDir + "/" + sourcePath;
      let resourceManager = context.resourceManager;
      if (!fs.accessSync(destFilePath)) {
        let modelArray: Uint8Array | undefined = await resourceManager.getRawFileContent(sourcePath)
        if (sourcePath.indexOf("/") != -1) {
          let dirs = sourcePath.split("/")
          for (let i = 0; i < dirs.length - 1; i++) {
            filesDir += "/" + dirs[i]
            if (!fs.accessSync(filesDir)) {
              try {
                fs.mkdirSync(filesDir)
                TKLog.debug(`[思迪文件工具]:创建文件夹成功，地址为${filesDir}`)
              } catch (error) {
                TKLog.debug(`[思迪文件工具]:创建文件夹失败，code为${error.code}, msg为${error}`)
              }
            }
          }
        }
        if (modelArray) {
          try {
            let file = fs.openSync(destFilePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
            fs.writeSync(file.fd, modelArray.buffer);
            fs.closeSync(file)
            TKLog.debug(`[思迪文件工具]:写入文件成功`)
          } catch (error) {
            TKLog.debug(`[思迪文件工具]:写入文件失败，code为${error.code}, msg为${error}`)
          }
        }
      }
    }

    destFilePath = destFilePath != undefined ? destFilePath : ''

    TKLog.debug(`[思迪文件工具]:复制文件完毕，地址为${destFilePath}`)
    return destFilePath;
  }


  public static copyToPath(srcPath : string,destPath : string){
    try {
      let photoSave = fs.openSync(destPath,fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY)
      let photoSelect = fs.openSync(srcPath, fs.OpenMode.READ_ONLY)

      fs.copyFileSync(photoSelect.fd,photoSave.fd)
      fs.close(photoSave)
      fs.close(photoSelect)
    } catch (error) {
      TKLog.debug(`[思迪文件工具]:创建文件夹失败，code为${error.code}, msg为${error}`)
    }
  }



  static async readContent(path: string, picSize?: string): Promise<string> {
    return new Promise<string>(async (resolve, reject) => {
      try {
        TKLog.debug(`[思迪文件工具]:开始读取文件，路径${path}`)
        let file = fs.openSync(path, fs.OpenMode.READ_ONLY);
        let stat = await fs.stat(file.fd)
        TKLog.debug(`[思迪文件工具]:文件大小:${stat.size}`);
        let buffer = new ArrayBuffer(stat.size);
        let readLen = fs.readSync(file.fd, buffer);
        TKLog.debug(`[思迪文件工具]:实际读取大小:${readLen}`);
        fs.closeSync(file);

        let utf8: Uint8Array | undefined = new Uint8Array(buffer);

        // let utf8 = new Uint8Array(buffer);
        // return TKStringUtil.uint8ArrayToString(utf8)

        if (picSize) {
          if (Number.parseInt(picSize)) {
            utf8 = await TKImageUtil.qualityCompress(buffer, Number.parseInt(picSize))
          }
        }

        let result = TKStringUtil.uint8ArrayToBase64(utf8 ? utf8 : new Uint8Array)
        TKLog.debug(`[思迪文件工具]:读取文件完毕,base64=${result}`);
        resolve(result);

      } catch (error) {
        TKLog.debug(`[思迪文件工具]:读取文件失败，code为${error.code}, msg为${error}`)

        reject(error)
      }
    })
  }

  static async readContentUint8Array(path: string): Promise<Uint8Array | undefined> {
    try {
      let file = fs.openSync(path, fs.OpenMode.READ_ONLY);
      let stat = await fs.stat(file.fd)
      TKLog.info(`FileUtils:${path} stat:::${stat.size}`)
      let buffer = new ArrayBuffer(stat.size);
      let readLen = fs.readSync(file.fd, buffer);
      TKLog.info( `FileUtils:${path} readSync:::${readLen}`);
      fs.closeSync(file);
      let utf8 = new Uint8Array(buffer);
      return utf8
    } catch (error) {
      let e: BusinessError = error as BusinessError;
      TKLog.info( `FileUtils:${path} readContent:::${e.message}`);
    }
    return undefined
  }

  static async readContentForBuffer(path: string): Promise<ArrayBuffer> {
    return new Promise<ArrayBuffer>(async (resolve, reject) => {
      try {
        TKLog.debug(`[思迪文件工具]:开始读取文件，路径${path}`)
        let file = fs.openSync(path, fs.OpenMode.READ_ONLY);
        let stat = await fs.stat(file.fd)
        TKLog.debug(`[思迪文件工具]:文件大小:${stat.size}`);
        let buffer = new ArrayBuffer(stat.size);
        let readLen = fs.readSync(file.fd, buffer);
        TKLog.debug(`[思迪文件工具]:实际读取大小:${readLen}`);
        fs.closeSync(file);

        resolve(buffer);

      } catch (error) {
        TKLog.debug(`[思迪文件工具]:读取文件失败，code为${error.code}, msg为${error}`)

        reject(error)
      }
    })
  }

  static getName(path: string): string {
    if (path.indexOf("/") != -1) {
      let dirs = path.split("/")
      return dirs[dirs.length -1]
    }
    return path;
  }

  static async save(buffer:ArrayBuffer|string, destFilePath: string, name: string): Promise<string> {
    TKLog.debug(`[思迪文件工具]:开始保存文件，路径为${destFilePath}`)
    await TKFileUtils.mkdir(destFilePath);
    if (buffer) {
      try {
        let file = fs.openSync(destFilePath + "/" + name, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        let length = fs.writeSync(file.fd, buffer);
        TKLog.debug(`[思迪文件工具]:保存文件成功，路径为${destFilePath}，文件大小:${length}`)
        fs.closeSync(file)
      } catch (error) {
        TKLog.debug(`[思迪文件工具]:保存文件失败，code为${error.code}, msg为${error}`)
      }
    }

    TKLog.debug(`[思迪文件工具]:保存文件结束，路径为${destFilePath}`)
    return destFilePath + "/" + name;
  }

  static async append(buffer: ArrayBuffer | string, destFilePath: string, name: string): Promise<string> {
    TKLog.debug(`[思迪文件工具]:开始追加文件，路径为${destFilePath}`)
    await TKFileUtils.mkdir(destFilePath);
    if (buffer) {
      try {
        let file = fs.openSync(destFilePath + "/" + name, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE | fs.OpenMode.APPEND);
        let length = fs.writeSync(file.fd, buffer);
        fs.closeSync(file)
        TKLog.debug(`[思迪文件工具]:追加文件成功，路径为${destFilePath}，文件大小:${length}`)
      } catch (error) {
        TKLog.debug(`[思迪文件工具]:追加文件失败，code为${error.code}, msg为${error}`)
      }
    }
    TKLog.debug(`[思迪文件工具]:追加文件结束，路径为${destFilePath}`)
    return destFilePath;
  }

  static async mkdir(path: string): Promise<string> {
    TKLog.debug(`[思迪文件工具]:开始创建文件夹，路径为${path}`)
    let filesDir = "";
    if (!fs.accessSync(path)) {
      let dirs = path.split("/")
      for (let i = 0; i < dirs.length; i++) {
        if (i == 0) {
          filesDir = dirs[0]
        } else {
          filesDir += "/" + dirs[i]
          if (!fs.accessSync(filesDir)) {
            try {
              fs.mkdirSync(filesDir)
              TKLog.debug(`[思迪文件工具]:创建文件夹成功，路径为${filesDir}`)
            } catch (error) {
              TKLog.debug(`[思迪文件工具]:创建文件夹失败，路径为${filesDir}`)
            }
          }
        }
      }
    }

    TKLog.debug(`[思迪文件工具]:创建文件夹结束，路径为${filesDir}`)
    return filesDir;
  }
}