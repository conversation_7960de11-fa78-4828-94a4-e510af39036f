import { AbilityConstant, ConfigurationConstant, UIAbility, Want, Configuration } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { TKAppEngine, TKCacheManager, TKLog } from '@thinkive/tk-harmony-base';
import { TKCommonService } from '@thinkive/tk-harmony-base/Index';
import { TKShare } from '@thinkive/tk-harmony-share';


export default class EntryAbility extends UIAbility {

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onCreate');

    // 初始化系统亮度模式状态
    this.initSystemColorMode();

    TKShare.handleWant(want, this.context)

    TKAppEngine.shareInstance().start({
      context: this.context,
      finishCallBack: () => {
        TKShare.builtInitial(this.context)

        //框架初始化完成
        TKLog.info(`[思迪开户入口]：思迪鸿蒙框架初始化成功`)
      }
    });

    TKLog.info(`[思迪开户入口]：onCreate want = ${JSON.stringify(want)}, launchParam = ${JSON.stringify(launchParam)}`)
  }

  onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    TKLog.info(`[思迪开户入口]：onNewWant want = ${JSON.stringify(want)}, launchParam = ${JSON.stringify(launchParam)}`)

    TKShare.handleWant(want, this.context)
  }

  handleOpenUrl(uri: string): void {
    const targetScheme = "jjcf://";
    if (uri.startsWith(targetScheme)) {
      // 晋金所响应外链的逻辑
      // 示例jjcf://paramExt(Jason String)
      // jjcf://m/mall/index.html?page_type=8&busi_id=xingzhi21983084&goPageIndex=undefined&img=&group_id=&activity_id=
      // const decodeURL = decodeURIComponent(uri);`
      // const urlString = decodeURL.replace(/ /g, "");
      // const paramExt = TKStringHelper.subString(urlString, targetScheme.length, urlString.length); // jjcf://共7个字符串
      //
      // // 传递给h5
      // this.gotoH5ActivityWithActivityName(null, paramExt);
    }
  }

  gotoH5ActivityWithActivityName(activityName: string | null, pramaStr: string): void {
    // const mmDrawerCtrl = this.navigationController.topViewController as TKH5MainViewController;
    // if (UIApplication.sharedApplication.applicationState === UIApplicationState.Active &&
    //   mmDrawerCtrl instanceof TKH5MainViewController && mmDrawerCtrl.isH5LoadFinish) {
    //   const dic: Record<string, any> = {};
    //
    //   dic["paramExt"] = pramaStr;
    //   dic["funcNo"] = "80004";
    //   // const service = new TKCommonService();
    //   // service.iosCallJs("mall", dic);
    // } else {
    //   setTimeout(() => {
    //     this.gotoH5ActivityWithActivityName(activityName, pramaStr);
    //   }, 1000);
    // }
  }

  onContinue(wantParam: Record<string, Object>): AbilityConstant.OnContinueResult | Promise<AbilityConstant.OnContinueResult> {
    TKLog.info(`[思迪开户入口]：onContinue`)

    return AbilityConstant.OnContinueResult.AGREE
  }


  onDestroy(): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageCreate');

    AppStorage.setOrCreate("HHSDK_WindowStage", this.context);

    let agreePolicyFlag: string = TKCacheManager.shareInstance().getFileCacheData("agreePolicyFlag") ?? '0'
    let pageName = "pages/Index";
    // agreePolicyFlag = '0'
    if (agreePolicyFlag != "1") {
      pageName = "pages/privacy/TKOpenPrivacyAgreementPage";
    }
    windowStage.loadContent(pageName, (err) => {
      if (err.code) {
        hilog.error(0x0000, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'testTag', 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onBackground');
  }

  /**
   * 系统配置变化回调
   * 监听系统亮度模式变化并更新AppStorage
   */
  onConfigurationUpdate(newConfig: Configuration): void {
    TKLog.info(`[思迪开户入口]：系统配置变化 colorMode = ${newConfig.colorMode}`);

    // 更新AppStorage中的当前颜色模式，供全局访问
    AppStorage.setOrCreate('currentColorMode', newConfig.colorMode);

    // 记录模式变化日志
    const modeText = this.getColorModeText(newConfig.colorMode ?? ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    TKLog.info(`[思迪开户入口]：系统亮度模式已切换为 ${modeText}`);
  }

  /**
   * 初始化系统颜色模式状态
   * 在应用启动时获取当前系统颜色模式并存储到AppStorage
   */
  private initSystemColorMode(): void {
    try {
      const config = this.context.config;
      const currentColorMode = config.colorMode;

      // 存储当前颜色模式到AppStorage
      AppStorage.setOrCreate('currentColorMode', currentColorMode);

      const modeText = this.getColorModeText(currentColorMode ?? ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
      TKLog.info(`[思迪开户入口]：初始化系统亮度模式 ${modeText}`);
    } catch (error) {
      TKLog.error(`[思迪开户入口]：获取系统颜色模式失败 ${JSON.stringify(error)}`);
      // 设置默认值
      AppStorage.setOrCreate('currentColorMode', ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    }
  }

  /**
   * 获取颜色模式的文本描述
   */
  private getColorModeText(colorMode: ConfigurationConstant.ColorMode): string {
    switch (colorMode) {
      case ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT:
        return '浅色模式';
      case ConfigurationConstant.ColorMode.COLOR_MODE_DARK:
        return '深色模式';
      case ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET:
      default:
        return '跟随系统';
    }
  }
}