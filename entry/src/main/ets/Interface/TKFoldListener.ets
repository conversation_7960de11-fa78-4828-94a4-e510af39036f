/**
 * 折叠屏状态监听接口
 */
export interface TKFoldListener {
  /**
   * X5折叠、pocket2展开状态UI显示属性更新回调
   * 当X5完全折叠时触发，pocket2不是完全折叠时触发（待验证）
   */
  foldDisplayMain: () => void;

  /**
   * 针对X5完全展开时UI显示属性更新回调
   * 当X5不是完全折叠时触发
   */
  foldDisplayFull: () => void;

  /**
   * 半开状态回调
   * 当设备处于半开状态时触发
   * 可选实现
   */
  onHalfOpen?: () => void;

  /**
   * 完全展开状态回调
   * 当设备处于完全展开状态时触发
   * 可选实现
   */
  onFullOpen?: () => void;

  /**
   * 完全折叠状态回调
   * 当设备处于完全折叠状态时触发
   * 可选实现
   */
  onFoldOpen?: () => void;
}