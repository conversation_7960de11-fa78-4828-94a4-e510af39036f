<?xml version="1.0" encoding="UTF-8"?>
<!--系统配置-->
<system>

    <catalog name="system" description="系统配置">
        <item name="isDevelop" value="0" description="是否开发环境(0:否,1:是)，默认是0"/>
        <item name="isLinstenGateway" value="0"
              description="是否对BusConfig.xml的服务器进行启动检测(0:否,1:是)，默认是0"/>
        <item name="isStartHqPush" value="0" description="是否开启行情推送通道服务(0:否,1:是)，默认是0"/>
        <item name="isUseServerTestSpeed" value="0" description="是否启动Server.xml的Http的测速(0:否,1:是)，默认是0"/>
        <item name="pluginPath" value="SystemPlugin.xml|TKH5SDKPlugin.xml|TKJJCFPlugin.xml" description="系统插件配置文件地址,多个用|分割"/>0
        <item name="isPluginBackspaceCallH5" value="1" description="是否插件返回取消后需要回调H5，默认是1"/>
    </catalog>

    <catalog name="keyborad" description="键盘配置">
        <item name="path" value="KeyBoard.xml" description="系统键盘配置文件地址,多个用|分割" />
        <item name="theme" value="0" description="原生键盘主题类型(0:浅色,1:深色)，默认是0" />
        <item name="isNoCutScreen" value="0" description="原生键盘是否支持防止截屏(0:不处理，1：处理)，默认0，处理的时候键盘会去掉点击背景色" />
    </catalog>

    <catalog name="networkRequest" description="网络请求相关配置">
        <item name="requestChannelId" value="" description="请求渠道ID"/>
        <item name="isShowErrorPrefix" value="1" description="是否对网络异常提示显示前缀(0:否,1:是)默认是1"/>
        <item name="isRequestURLEncode" value="1" description="是否对请求入参进行URL编码(0:否,1:是)默认是1"/>
        <item name="isRequestURLSign" value="0" description="是否对请求入参进行签名(0:否,1:是)默认是0"/>
        <item name="requestSignKey" value="nPeaYkREFgwhlz6/UN582NYDd3ySKKTSM4jTiMrZtmYeT9CVhloh0e0kmq2PagBU"
              description="请求签名的Key"/>
        <item name="requestSignAppId" value="00000001" description="请求签名的APPId"/>
        <item name="isRequestURLEncrypt" value="0" description="是否对请求入参进行加密(0:否,1:是)默认是0"/>
        <item name="requestEncryptMode" value="aes" description="加密的类型"/>
        <item name="requestEncryptKey" value="nPeaYkREFgwhlz6/UN582NYDd3ySKKTSM4jTiMrZtmYeT9CVhloh0e0kmq2PagBU"
              description="请求加密的Key"/>
        <item name="isAutoLoginForReConnected" value="1" description="是否断线重连自动检测登录(0:否,1:是)默认是1"/>
        <item name="isRestFull" value="0" description="是否走微服务RestFull接口" />
    </catalog>

    <catalog name="networkRequestSign" description="请求签名加密相关配置">
        <item name="trade"
              value=""
              description="格式为:trade模块的商户号|签名Key|加密Key"/>
    </catalog>

    <catalog name="networkErrorInfo" description="请求常见的错误的提示信息配置">
        <item name="-100000" value="亲，网络很不给力哦~" description="请求断网错误"/>
        <item name="-100001" value="亲，服务器不能正常访问~" description="请求服务器异常"/>
        <item name="-100002" value="亲，请求超时，请稍后重试~" description="请求超时错误"/>
        <item name="-100003" value="亲，请求已中断，请稍后重试~" description="请求中断错误"/>
        <item name="-100004" value="亲，请求地址不能正常访问~" description="请求地址错误"/>
        <item name="-100005" value="亲，DNS域名解析失败~" description="DNS域名解析错误"/>
        <item name="-100006" value="亲，您正在通话中~" description="通话中请求数据错误"/>
    </catalog>

    <catalog name="webViewPool" description="webView链接池">
        <item name="htmlLoadMode" value="0" description="html的加载模式(0:从安全沙箱加载,1:从安装包加载)，默认是0"/>
        <item name="isCheckH5" value="0"
              description="是否检测H5加载完成（1:检测，只有H5通知原生加载完成以后,才可以回调H5的方法 0:不检查,直接回调H5）默认是0，不检测"/>
        <item name="isShowLoading" value="0" description="是否显示WebView加载过渡效果(0:否,1:是)默认是0"/>
        <item name="disableScheme" value="" description="应用openURL的Scheme,多个用|分割"/>
        <item name="userAgent" value="" description="浏览器扩展头"/>
        <item name="inspectable" value="1" description="是否开启WK调试(0:否,1:是)默认是0"/>
        <item name="isCheckSecurityDomain" value="0" description="是否开启安全域名校验(0:否,1:是)默认是0"/>
        <item name="securityDomain" value=""
              description="安全域名,多个用|分割"/>
        <item name="illegalDomainUrl" value="www/index.html" description="非法域名的重定向地址"/>
    </catalog>

    <catalog name="update" description="版本管理配置">
        <item name="isOpen" value="0" description="是否启动检测版本更新(0:否,1:是)，默认是0"/>
        <item name="mode" value="1" description="更新模式0:全量更新,1:增量更新"/>
<!--        <item name="versionCode" value="1" description="版本内部序号"/>-->
        <item name="saveName" value="www.zip" description="下载H5压缩包名称"/>
        <item name="password" value="yn6dwAEVH2NepNOkm755bA==" description="解压H5压缩包得密码" />
        <item name="isEncrypt" value="2" description="H5下载压缩包是有密码(0:否,1:是,2:是加密，同时配置的password字段是加密后的密码)" />
        <item name="checkUrl" value="" description="版本自动检测的服务器地址" />
        <item name="reqMode" value="0"
              description="请求模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)"/>
        <item name="updateTip" value="" description="更新过程提示语"/>
        <item name="errorTip" value="" description="更新错误提示语"/>
        <item name="successTip" value="" description="更新成功提示语"/>
        <item name="isShowUpdateTip" value="1" description="是否显示更新相关弹层进度提示"/>
        <item name="isReloadWebView" value="1" description="是否更新完成后进行webview的刷新动作"/>
        <item name="timeOut" value="0" description="下载超时时间，单位秒，默认是0，代表不限制"/>
        <item name="backgroundTaskEnabled" value="1" description="设置是否支持后台任务模式(0:否,1:是)默认1"/>
        <item name="isCancelOnNetworkException" value="1" description="网络异常时是否自动取消下载任务(0:否,1:是)默认0"/>
    </catalog>

    <catalog name="log" description="日志的配置">
        <item name="logLevel" value="all" description="off|error|warn|info|debug|all"/>
    </catalog>

    <catalog name="theme" description="主题配置">
        <item name="currentTheme" value="theme1" description="默认主题名称" />
        <item name="isSaveLastTheme" value="1" description="覆盖安装时，是否缓存上次的主题(0:不缓存,1:缓存)，默认是0" />
        <item name="updateUrl" value="" description="版本自动检测的服务器地址,http/https格式:http://地址:端口/servlet/json?key=value&key=value, socket格式：socket://busconfig.xml中的serverID?companyId=THINKIVE&systemId=MALL&key=value, server.xml格式：server://server.xml中的serverID?key=value&key=value" />
        <item name="theme1" value="theme1.css|sysWhiteTheme.css" description="主题1样式文件地址;ios的浅色模式" />
        <item name="theme2" value="theme2.css|sysBlackTheme.css" description="主题2样式文件地址;ios的暗黑模式" />
    </catalog>

</system>