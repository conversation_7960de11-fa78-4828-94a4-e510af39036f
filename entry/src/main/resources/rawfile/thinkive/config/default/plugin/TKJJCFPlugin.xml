<?xml version="1.0" encoding="UTF-8"?>
<!--H5调用原生综合SDK插件-->
<plugins>
  <item name="60302" value="TKPlugin60302" description="身份证OCR识别"></item>
  <item name="60304" value="TKPlugin60304" description="银行卡OCR识别"></item>
  <item name="60393" value="TKPlugin60393" description="保存图片"></item>
  <item name="60076" value="TKPlugin60076" description="打开微信小程序"></item>
  <item name='60094' value="TKOpenPlugin60094" description="控制是否截屏录屏做提示"></item>

  <!--    晋金财富专用插件-->
  <item name="80001" value="TKPlugin80001" description="清空cookie"></item>
  <item name="80002" value="TKPlugin80002" description="用于h5不同模块的消息传递，调用80003插件"></item>
  <item name="80010" value="TKPlugin80010" description="活体加人脸识别，80013返回结果"></item>
  <item name="80020" value="TKPlugin80020" description="阿里云上传图片调用"></item>
  <item name="80031" value="TKPlugin80031" description="查询app角标值"></item>
  <item name="80032" value="TKPlugin80032" description="设置app角标值"></item>
  <item name="80040" value="TKPlugin80040" description="预览带签名的PDF"></item>
  <item name="80050" value="TKPlugin80050" description="唤起银行APP"></item>
  <item name="80302" value="TKPlugin80302" description="获取剪切板的内容"></item>
  <item name="80303" value="TKPlugin80303" description="唤起建行APP支付"></item>
  <item name="60395" value="TKPlugin60395" description="H5预览PDF原生提供分享下载"></item>
  <item name="80309" value="TKPlugin80309" description="设置七鱼用户信息"></item>
  <item name="80311" value="TKPlugin80311" description="注销七鱼用户信息"></item>
  <item name="80313" value="TKPlugin80313" description="调起七鱼客服页面"></item>
  <item name="80315" value="TKPlugin80315" description="获取七鱼所有未读消息数"></item>
  <item name="80323" value="TKPlugin80323" description="获取当前手机是否正在录制"></item>

  <!--    指纹验证相关插件 -->
  <item name="80319" value="TKPlugin80319" description="同步指纹验证"></item>
  <item name="80321" value="TKPlugin80321" description="异步指纹数据同步"></item>

</plugins>
