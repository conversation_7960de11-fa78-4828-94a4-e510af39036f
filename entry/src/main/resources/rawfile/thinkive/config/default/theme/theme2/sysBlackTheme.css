<!-------------手势密码------------>

.TKGesturePasswordView
{
    background-color: #000000;
}

.TKGesturePasswordViewNavBar
{
    -ios-bar-tint-color: #000000;
    -ios-bar-title-color: #FFFFFF;
}

.TKGesturePasswordViewTopLabel
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

.TKGesturePasswordViewButtomLabel
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

.TKGesturePasswordTentacleView
{
    background-color: clear;
    color: #FFFFFF;
}

.TKGesturePasswordLittleTentacleView
{
    background-color: clear;
    color: #FFFFFF;
}

<!-------------PDF------------>

.TKPdfView
{
    background-color: #FFFFFF;
}

.TKPdfViewNavBar
{
    -ios-bar-tint-color: #000000;
    -ios-bar-title-color: #FFFFFF;
}

.TKPdfViewTimer
{
    background-color: clear;
    color: #FFFFFF;
}

.TKPdfViewBackBtn
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

.TKPdfViewReadBtn
{
    background-color: orange;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

<!-------------二维码----------->

.TKQRCoderViewNavBar
{
    -ios-bar-tint-color: #000000;
    -ios-bar-title-color: #FFFFFF;
}

.TKQRCoderViewBackBtn
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

<!-------------图片上传----------->

.TKImageCropperViewNavBar
{
    -ios-bar-tint-color: #000000;
    -ios-bar-title-color: #FFFFFF;
}

.TKImageCropperViewBackBtn
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}
