<!-------------手势密码----------->

.TKGesturePasswordView
{
    background-color: #FFFFFF;
}

.TKGesturePasswordViewNavBar
{
    -ios-bar-tint-color: #FFFFFF;
    -ios-bar-title-color: #333333;
}

.TKGesturePasswordViewTopLabel
{
    background-color: clear;
    color: #333333;
    -ios-tint-color: #333333;
}

.TKGesturePasswordViewButtomLabel
{
    background-color: clear;
    color: #6b5955;
    -ios-tint-color: #6b5955;
}

.TKGesturePasswordTentacleView
{
    background-color: clear;
    color: #CCCCCC;
}

.TKGesturePasswordLittleTentacleView
{
    background-color: clear;
    color: #757575;
}

<!-------------PDF----------->

.TKPdfView
{
    background-color: #FFFFFF;
}

.TKPdfViewNavBar
{
    -ios-bar-tint-color: #3E6193;
    -ios-bar-title-color: #FFFFFF;
}

.TKPdfViewTimer
{
    background-color: clear;
    color: #FFFFFF;
}

.TKPdfViewBackBtn
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

.TKPdfViewReadBtn
{
    background-color: orange;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

<!-------------二维码----------->

.TKQRCoderViewNavBar
{
    -ios-bar-tint-color: #3E6193;
    -ios-bar-title-color: #FFFFFF;
}

.TKQRCoderViewBackBtn
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}

<!-------------图片上传----------->

.TKImageCropperViewNavBar
{
    -ios-bar-tint-color: #3E6193;
    -ios-bar-title-color: #FFFFFF;
}

.TKImageCropperViewBackBtn
{
    background-color: clear;
    color: #FFFFFF;
    -ios-tint-color: #FFFFFF;
}
