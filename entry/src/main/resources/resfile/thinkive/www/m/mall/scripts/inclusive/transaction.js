// 晋金普惠--交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageUrl = "inclusive/transaction",
        _pageId = "#inclusive_transaction ";
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var cur_page = 1;
    var startTime = "";
    var endTime = "";
    var isEnd = false;
    var num_per_page = "15";
    var tools = require("../common/tools");
    var userInfo = "";
    var ut = require("../common/userUtil");
    var profit_obj = {
        "12901": "红利再投",
        "12902": "分红到晋金宝",
        "12903": "分红到晋金宝",

    }
    var expire_obj = {
        "13901": "自动滚入下一期",
        "13902": "自动赎回到银行卡",
        "13903": "自动赎回到晋金宝",
    }

    function init() {
        cur_page = 1;
        userInfo = ut.getUserInf();
        $(_pageId + ".olay").remove();
        //初始化时间筛选列表及时间框显示(默认七天)
        selectDate(_pageId + '#startTime', 0, _pageId);
        selectDate(_pageId + '#endTime', 1, _pageId);
        //用于接收详情页带回的参数，找回跳转前的筛选结果
        if (appUtils.getSStorageInfo("qry_condition")) {
            var qry_condition = appUtils.getSStorageInfo("qry_condition");
            appUtils.clearSStorage("qry_condition");
            $(_pageId + " #query_type li a").removeClass("active");
            if (qry_condition.busi_type) {
                $(_pageId + "#query_type [data-value=" + qry_condition.busi_type + "]").addClass("active");
            } else {
                $(_pageId + " #query_type li a").eq(0).addClass("active");
            }
            $('#startTime').attr('time', qry_condition.start_time);
            $('#endTime').attr('time', qry_condition.end_time);
            $(_pageId + " #query_date li a").removeClass("active");
            if (qry_condition.date_value) {
                $(_pageId + "#query_date [data-value=" + qry_condition.date_value + "]").addClass("active");
            } else {
                $(_pageId + " #query_date li a").eq(0).addClass("active");
            }
        } else {
            resetInputDate();
            initBusiType();
        }
        getUsertransaction(false);
    }


    function bindPageEvent() {
        //返回键
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");

        //跳转详情
        appUtils.preBindEvent($(_pageId + " #concent"), ".trade_box", function (e) {
            var trans_serno = $(this).attr("trans_serno");
            var busi_code_e = $(this).attr("busi_code_e");
            var sub_busi_code_e = $(this).attr("sub_busi_code_e");
            appUtils.setSStorageInfo("judgeTransactionDetails", true);
            //跳转详情
            
            judge_busi_code_e(busi_code_e, trans_serno, sub_busi_code_e);
        }, "click");

        //打开筛选层
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " .record_filter").show();
            $(_pageId + " #filter_layer").show();
            var recordHeight = $(_pageId + " .record_filter").innerHeight();
            var topHeight = recordHeight + 44 + "px";
            $(_pageId + " .pop_layer").css("top", topHeight);
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " .pop_layer"), function () {
            $(_pageId + " .record_filter").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //快捷键选择时间
        appUtils.bindEvent($(_pageId + " #query_date li a"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).addClass("active");
            var data_value = $(this).attr("data-value");
            var endTime = new Date();
            var startTime = new Date();
            if (data_value == "30") {
                startTime.setMonth(endTime.getMonth() - 1);
            } else if (data_value == "90") {
                startTime.setMonth(endTime.getMonth() - 3);
            } else if (data_value == "180") {
                startTime.setMonth(endTime.getMonth() - 6);
            } else {
                startTime = "";
                endTime = "";
            }
            setInputDate("endTime", endTime);
            setInputDate("startTime", startTime);
        });

        //确定/重置按钮绑定事件
        appUtils.bindEvent($(_pageId + " .record_filter #query-button a"), function () {
            var dataType = $(this).attr("data-value");
            if (dataType === "reset") {
                //重置按钮
                resetInputDate();
            } else if (dataType === "confirm") {
                cur_page = 1;
                startTime = $(_pageId + " #startTime").attr("time");
                endTime = $(_pageId + " #endTime").attr("time");
                getUsertransaction(false);
                $(_pageId + " .record_filter").hide();
                $(_pageId + " .pop_layer").hide();
            }
        });

    }

    function initBusiType() {
        //交易类型（1.现金宝；2.公募；3.私募）
        var param = {
            "trans_type": "2",
        }
        service.reqFun102059(param, function (resultVo) {
            var str = "<li><a class='active' data-value=''>所有</a></li>";
            if (resultVo.error_no == "0") {
                var dataList = resultVo.results;
                for (var i = 0; i < dataList.length; i++) {
                    str += "<li><a data-value='" + dataList[i].busi_type + "'>" + dataList[i].busiName + "</a></li>"
                }
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }
            $(_pageId + " #query_type").html(str);
            //快捷键选择类型
            appUtils.bindEvent($(_pageId + " #query_type li a"), function () {
                $(_pageId + " #query_type li a").removeClass("active");
                $(this).addClass("active");
            });
        })
    }

    //获取交易记录
    function getUsertransaction(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        endTime = $(_pageId + '#endTime').attr('time');
        endTime = endTime.replace(/-/g, "");
        startTime = $(_pageId + '#startTime').attr('time');
        startTime = startTime.replace(/-/g, "");
        var param = {
            "cur_page": cur_page,
            "num_per_page": num_per_page,
            "start_date": startTime,
            "end_date": endTime,
            "busi_type": $(_pageId + " #query_type li .active").attr("data-value"),
            "cnl_trans_no": userInfo.fncTransAcctNo,
        };

        var trsFundCode = appUtils.getSStorageInfo("trsFundCode");
        var trsAckDate = appUtils.getSStorageInfo("trsAckDate");
        if (trsFundCode) {
            param.fund_code = trsFundCode;
        }
        if (trsAckDate) {
            param.ack_date = trsAckDate;
        }
        $(_pageId + "[data-name=digest]").html(tools.fundDataDict(param.busi_type, "sub_busi_name"));
        $(_pageId + "[data-name=order_state]").html(tools.fundDataDict(param.busi_type, "pub_trans_status_name"));
        var callback = function (resultVo) {
            if (resultVo.error_no == "0") {
                var detailParams = resultVo.results[0].detailParams;
                var htmls = "";
                if (detailParams && detailParams.length > 0) {
                    for (var i = 0; i < detailParams.length; i++) {
                        var sub_busi_code_e = detailParams[i].sub_busi_code_e;
                        if (sub_busi_code_e == "12901" || sub_busi_code_e == "12902" || sub_busi_code_e == "12903") {
                            //修改分红方式列表
                            htmls += dividendList(detailParams[i]);
                        } else if (sub_busi_code_e == "13901" || sub_busi_code_e == "13902" || sub_busi_code_e == "13903") {
                            //修改到期方式列表
                            htmls += expireList(detailParams[i]);
                        } else {
                            //申购列表
                            htmls += buyList(detailParams[i]);
                        }
                    }
                    if (isAppendFlag) {
                        $(_pageId + " #concent").append(htmls);
                    } else {
                        $(_pageId + " #concent").html(htmls);
                    }
                } else if (!detailParams) {
                    isEnd = true;
                    if (!isAppendFlag) {
                        $(_pageId + " #concent").html("");
                    }
                    $(_pageId + " .new_none").show();
                }
                if (detailParams && detailParams.length < num_per_page) {
                    isEnd = true;
                    if (!isAppendFlag && detailParams.length == 0) { //第一页 && 数据为空
                        $(_pageId + " #concent").html("");
                    }
                    $(_pageId + " .new_none").show();
                }
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }

            $(_pageId + " .visc_pullUpIcon").hide();
            $(_pageId + " .visc_pullUpDiv").hide();
            pageScrollInit();
        }
        service.reqFun102052(param, callback);
    }

    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {

            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    getUsertransaction(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getUsertransaction(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        if (isEnd) {//可能有当前页为1总页数为0的情况
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }

        //ios上拉拖动卡死问题解决
        var pageTouchTimer = null;
//		var contentHeight = $(_pageId + " #wrapper_w1").height()-570;
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }


    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", "").val("");
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        //快捷类型筛选
        $(_pageId + " #query_type li a").removeClass("active").eq(0).addClass("active");
        //快捷时间筛选
        $(_pageId + " #query_date li a").removeClass("active").eq(0).addClass("active");
        $(_pageId + '#startTime').attr("time", "").val("");
        $(_pageId + '#endTime').attr("time", "").val("");
    }

    //修改分红方式列表
    function dividendList(data) {
        var sub_busi_code = data.sub_busi_code;
        var product_name = data.prod_sname;
        var crt_date = data.crt_date;
        var crt_time = data.crt_time;
        var full_date = crt_date + crt_time;
        full_date = tools.ftime(full_date);
        var state = tools.fundDataDict(data.trans_status, "pub_updateDivide_status_name");
        var sub_busi_code_e = data.sub_busi_code_e;
        var sub_busi_code_name = profit_obj[sub_busi_code_e];
        var prod_type3 = data.prod_type3;
        if (prod_type3 == "30") {
            product_name = "晋金宝" + require("gconfig").global.holding_days + "天";
        }
        var html = '<div class="trade_box" trans_serno=' + data.trans_serno + " busi_code_e=" + data.busi_code_e + ' sub_busi_code_e="' + data.sub_busi_code_e + '">' +
            '<div class="fundInfo">' +
            '<p>' + sub_busi_code + '</p>' +
            '<p class="fund_name">' + product_name + '</p>' +
            '<p class="date">' + full_date + '</p>' +
            '</div>' +
            '<div class="method">' +
            '<div class="two_item">' +
            '<p class="transFontSize">' + sub_busi_code_name + '</p>' +
            '<p style="margin-top: 0.06rem;">' + state + '</p>' +
            '</div>' +
            '</div>' +
            '</div>';

        return html;
    }

    //修改到期方式列表
    function expireList(data) {
        var sub_busi_code = data.sub_busi_code;
        var product_name = data.prod_sname;
        var crt_date = data.crt_date;
        var crt_time = data.crt_time;
        var full_date = crt_date + crt_time;
        full_date = tools.ftime(full_date);
        var state = tools.fundDataDict(data.trans_status, "pub_updateExpire_status_name");
        var sub_busi_code_e = data.sub_busi_code_e;
        var sub_busi_code_name = expire_obj[sub_busi_code_e];
        var prod_type3 = data.prod_type3;
        if (prod_type3 == "30") {
            product_name = "晋金宝" + require("gconfig").global.holding_days + "天";
        }
        var html = '<div class="trade_box" trans_serno=' + data.trans_serno + " busi_code_e=" + data.busi_code_e + ' sub_busi_code_e="' + data.sub_busi_code_e + '">' +
            '<div class="fundInfo">' +
            '<p>' + sub_busi_code + '</p>' +
            '<p class="fund_name">' + product_name + '</p>' +
            '<p class="date">' + full_date + '</p>' +
            '</div>' +
            '<div class="method">' +
            '<div class="two_item">' +
            '<p class="transFontSize">' + sub_busi_code_name + '</p>' +
            '<p style="margin-top: 0.06rem;">' + state + '</p>' +
            '</div>' +
            '</div>' +
            '</div>';

        return html;
    }

    //申购列表
    function buyList(data) {
        var sub_busi_code = data.sub_busi_code;
        var product_name = data.prod_sname;
        var prod_type3 = data.prod_type3;
        if (prod_type3 == "30") {
            product_name = "晋金宝" + require("gconfig").global.holding_days + "天";
        }
        var crt_date = data.crt_date;
        var crt_time = data.crt_time;
        var full_date = crt_date + crt_time;
        full_date = tools.ftime(full_date);
        if (data.sub_busi_code_e == "14301" || data.sub_busi_code_e == "14201" || data.sub_busi_code_e == "14202" || data.sub_busi_code_e == '14401' || data.sub_busi_code_e == "14501") { //分红展示 ack_amt 红利再投
            var money = data.ack_vol;//份额
        } else if (data.sub_busi_code_e == "14302" || data.sub_busi_code_e == "14303") { //分红展示 分红到卡 分红到宝
            var money = data.ack_amt; //金额
        } else {
            var money = data.app_amt;
        }
        var unit = "";
        if (data.sub_busi_code_e == "12404" || data.sub_busi_code_e == "12402" || data.sub_busi_code_e == "14301" || data.sub_busi_code_e == "14201" || data.sub_busi_code_e == "14202" ||data.sub_busi_code_e == "12406" || data.sub_busi_code_e == "12408" || data.sub_busi_code_e == '14401' || data.sub_busi_code_e == "14501") { //赎回、分红：红利再投展示份，强制赎回14201
            unit = "份";
        } else {
            unit = "元";
        }
        money = tools.fmoney(money) + unit;
        if (data.sub_busi_code_e == "12202") { //申购交易状态
            var state = tools.fundDataDict(data.trans_status, "pub_purchase_status_name");
        } else if (data.sub_busi_code_e == "12404" || data.sub_busi_code_e == "12402" || data.sub_busi_code_e == "12406" || data.sub_busi_code_e == "12408") { //赎回交易状态
            var state = tools.fundDataDict(data.trans_status, "pub_redeem_status_name");
        } else if (data.sub_busi_code_e == "15002" || data.sub_busi_code_e == "15003") { //兑付到宝交易状态（私募）
            var state = tools.fundDataDict(data.trans_status, "pub_cashJJB_status_name");
        } else if (data.sub_busi_code_e == "14302" || data.sub_busi_code_e == "14303" || data.sub_busi_code_e == "14201" || data.sub_busi_code_e == "14202") { //分红到宝交易状态、强制赎回到宝
            var state = tools.fundDataDict(data.trans_status, "pub_profitJJB_status_name");
        } else {
            var state = tools.fundDataDict(data.trans_status, "pub_trans_status_name");
        }
        var html = '<div class="trade_box" trans_serno=' + data.trans_serno + " busi_code_e=" + data.busi_code_e + ' sub_busi_code_e="' + data.sub_busi_code_e + '">' +
            '<div class="fundInfo">' +
            '<p>' + sub_busi_code + '</p>' +
            '<p class="fund_name">' + product_name + '</p>' +
            '<p class="date">' + full_date + '</p>' +
            '</div>' +
            '<div class="method right_icon">' +
            '<div class="two_item">' +
            '<p style="font-size: 16px;">' + money + '</p>' +
            '<p style="margin-top: 0.06rem;">' + state + '</p>' +
            '</div>' +
            '</div>' +
            '</div>';
        return html;
    }

    //跳转详情
    function judge_busi_code_e(busi_code_e, trans_serno, sub_busi_code_e) {
        var obj = {
            "12202": "inclusive/transactionDetailsBuy", //申购
            "12302": "inclusive/transactionDetailsAppointmentBuy", //预约申购
            "12402": "inclusive/transactionDetailsSell", // 主动赎回到卡
            "12404": "inclusive/transactionDetailsSell", //主动赎回到宝
            "12406": "inclusive/transactionDetailsSell", //到期自动赎回到宝
            "12408": "inclusive/transactionDetailsSell", //到期自动赎回到卡
            "14201": "inclusive/transactionDetailsForeclosure", //强制赎回 产品赎回（宝）
            "14202": "inclusive/transactionDetailsForeclosure", //强制赎回 产品赎回卡
            "14301": "inclusive/transactionDetailsProfit", //分红 红利再投
            "14302": "inclusive/transactionDetailsProfit", //分红 分红到宝
            "14303": "inclusive/transactionDetailsProfit", //分红 分红到宝
            "14401":"inclusive/additionSubtraction", //强增
            "14501":"inclusive/additionSubtraction" //强减
        }
        var param = {
            date_value: $(_pageId + " #query_date li a.active").attr("data-value"),
            startTime: $(_pageId + " #startTime").val(),
            endTime: $(_pageId + " #endTime").val(),
            busi_type: $(_pageId + " #query_type li a.active").attr("data-value")
        }
        appUtils.setSStorageInfo("qry_condition", param); //保留此次筛选条件
        if (obj[sub_busi_code_e], trans_serno) {
            appUtils.pageInit(_pageUrl, obj[sub_busi_code_e], {
                "trans_serno": trans_serno,
                "sub_busi_code_e": sub_busi_code_e
            });
        }

    }

    //返回页面
    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        startTime = "";
        endTime = "";
        cur_page = 1;
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .record_filter").hide();
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        $(_pageId + " .new_none").hide();
        isEnd = false;
        $(_pageId + " #concent").html("");
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
