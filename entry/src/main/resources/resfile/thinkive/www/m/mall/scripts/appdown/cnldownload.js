// 下载页面
define(function (require, exports, module) {
    var gconfig = require("gconfig"),
        global = gconfig.global,
        service = require("mobileService"),
        _pageId = "#appdown_cnldownload ",
        appUtils = require("appUtils"),
        layerUtils = require("layerUtils");
    var flag; // 是否重复点击
    var is_weixin = false;
    var isIphone = false;
    var isAndroid = false;
    var downloadUrl = "";
    require("../common/clipboard.min.js");
    var bannerTimer;
    var hIscroll =null;
    var tools = require("../common/tools");
    var channelCode;
    
    function init() {
	
        flag = true;
        var ua = navigator.userAgent.toLowerCase();
        if (ua.indexOf('iphone') > 0) {						//需对所有 iOS 系统 UA 信息进行判断
            isIphone = true;
        }
        if (ua.match(/MicroMessenger/i) == "micromessenger" || ua.match(/QQ\/[0-9]/i)) {
            is_weixin = true;
         }
        if (ua.indexOf('android') > 0) {				//需对所有 Android 系统 UA 信息进行判断
            isAndroid = true;
        }
        if (isIphone && is_weixin)              //IPone 手机打开微信
        {
            $(_pageId + " #iphone_weixin").show();
            return;
        }

        if (isAndroid && is_weixin) {
            $(_pageId + " #android_weixin").show();
            return;
        }

        //初始化复制功能相关内容
        copyContent("updateBtn");
        channelCode = appUtils.getPageParam("ename");
        var queryParam = {"ename":channelCode};
        service.reqFun1100009(queryParam, function (data) {
            if (data.error_no == 0) {
                if (data.results.length != 0) {
                    var uuid = data.results[0].UUID;
//                  navigator.clipboard.writeText(uuid);
                    if(uuid.trim()){
                        $(_pageId + " #updateBtn").attr("data-clipboard-text", uuid);
                        
                    }
                }
            }
        });
	
	//是手机浏览器打开,展示页面
	pageShow();
	
	//是ios浏览器
        if (isIphone) {
            downloadUrl =  $(_pageId + " .uploadIosSrc").attr("upload_ios_url"); //下载地址
        }
        
        //是安卓浏览器
        if (isAndroid) {
            initAndroidInfo();
        }
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #updateBtn"), function () {
            if(!flag) return;
            flag = false;
            if(isIphone){
        	window.location =downloadUrl;
        	return;
            }
            $("iframe").remove();
            $("body").append("<iframe src='" + downloadUrl+ "' style='display: none'></iframe>");
            
//            if($(_pageId + " #copyContent").attr("data-clipboard-text").trim()){
//                document.getElementById("copyContent").click();
//            }
            setTimeout(function(){
                $(_pageId + " #updateBtn").text("下载中");
                $(_pageId + " #updateBarInfo").show();
                flag = true;
            }, 2000)
        })
    }

    function destroy() {
        $(_pageId + " #updateBarInfo").hide();
        $("iframe").remove();
        downloadUrl = "";
        if(hIscroll){
	    hIscroll.destroy();
	    hIscroll = null;
	}
	clearInterval(bannerTimer);
    }

    // 初始化页面展示
    function pageShow(){
	$(_pageId + " #android_download").show();
	$(_pageId + " #header").show();
	$(_pageId + " #updateBtn").text("立即下载");
        if(hIscroll){//banner轮播
	    hIscroll.destroy();
	    hIscroll = null;
	    clearInterval(bannerTimer);
	}
        banner(channelCode);
    }
    
    // 初始化安卓信息
    function initAndroidInfo() {
        var queryParam = {
    	    "channel": "1",
        };
        service.reqFun102071(queryParam, function (data) {
    	if (data.error_no == 0) {
    	    if (data.results.length != 0) {
    		downloadUrl =  data.results[0].downloadUrl; //下载地址
    	    }
    	}
        }, {"isShowWait": false});
    }
    
    //banner
    function banner(channelCode) {
        service.reqFun102105({channel_code:channelCode}, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                banner_id = results.banner_id;
                //banner轮播
                tools.guanggao({_pageId: _pageId, group_id: banner_id});
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    
    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            // console.log("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            // console.log("复制失败，请重试");
        });
    }
    var userIndex = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
// 暴露对外的接口
    module.exports = userIndex;
})
;