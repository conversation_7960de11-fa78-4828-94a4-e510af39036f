// 上传身份证 -- 操作指南
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#guide_uploadGuide";
    var external = require("external");

    function init() {
    }

    function bindPageEvent() {

        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .start_icon"), function () {
            var setParam = {
                "funcNo": "50275",
                "title": "晋金财富身份证识别步骤",
                "mediaUrl": require("gconfig").global.oss_url + "fund_filesystem/video/yysp.mp4", //视频文件的地址
            };
            external.callMessage(setParam);
        });

    }

    function destroy() {
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var guide_uploadGuide = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = guide_uploadGuide;
});
