// 风险测评
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#combProduct_combRiskQuestion";
    _page_code = "combProduct/combRiskQuestion";
    var common = require("common");
    var tools = require("../common/tools");
    var ut = require("../common/userUtil");
    var custTgAssess = {};
    function init() {
        $(_pageId + " input").removeAttr("checked");
        initRisk();
        //页面埋点初始化
        tools.initPagePointData();
    }
    function initRisk() {
        service.reqFun101089({}, function (data) {
            if (data.error_no == "0") {
                if (data.results.length > 0) {
                    var str = "";
                    $(_pageId + " #RiskQuestionAnswer").html("");
                    for (var i = 0; i < data.results.length; i++) {
                        var question_code = data.results[i].question_code;//题目编号
                        var question_name = data.results[i].question_name;//风险测评题目名称
                        var option_cont_a = data.results[i].option_cont_a;//a
                        var option_cont_b = data.results[i].option_cont_b;//b
                        var option_cont_c = data.results[i].option_cont_c;//c
                        var option_cont_d = data.results[i].option_cont_d;//d
                        var option_cont_e = data.results[i].option_cont_e;//e
                        var option_cont_f = data.results[i].option_cont_f;//f
                        var option_cont_g = data.results[i].option_cont_g;//g
                        var option_cont_h = data.results[i].option_cont_h;//h
                        var option_cont_i = data.results[i].option_cont_i;//i
                        var option_cont_j = data.results[i].option_cont_j;//j
                        str += "<div  id='" + (i + 1) + "' class='risk_question'>" +
                            "<strong class='m_font_size14'>" + (i + 1) + "." + question_name + "</strong>";
                        if (option_cont_a) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='A' id='radio_" + (i + 1) + "'><label>" + option_cont_a + "</label></div>";
                        }
                        if (option_cont_b) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='B' id='radio_" + (i + 1) + "'><label>" + option_cont_b + "</label></div>";
                        }
                        if (option_cont_c) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='C' id='radio_" + (i + 1) + "'><label>" + option_cont_c + "</label></div>";
                        }
                        if (option_cont_d) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='D' id='radio_" + (i + 1) + "'><label>" + option_cont_d + "</label></div>";
                        }
                        if (option_cont_e) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='E' id='radio_" + (i + 1) + "'><label>" + option_cont_e + "</label></div>";
                        }
                        if (option_cont_f) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='F' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_g) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='G' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_h) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='H' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_i) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='I' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_j) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='J' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        str += "</div>";
                    }
                    $(_pageId + " #RiskQuestionAnswer").append(str);
                    $(_pageId + " #RiskQuestionAnswer div").css('width', '100%');
                    $(_pageId + " #RiskQuestionAnswer input").css('display', 'none');
                    $(_pageId + " .risk_question").css({ 'padding': '0.2rem 0', "border-bottom": "1px solid #CECECE" });
                    $(_pageId + " .risk_question strong").css({ 'margin': '0 0.15rem', 'display': 'block', 'margin-bottom': '0.1rem' });
                    $(_pageId + " .risk_question div").css({ 'padding': '0 0.15rem 0.04rem 0.15rem' });
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }

    function bindPageEvent() {
        appUtils.preBindEvent($(_pageId + " #RiskQuestionAnswer"), ".riskQuesAns", function () {
            var checkedS = $(this).parent(".risk_question").attr("id");
            tools.recordEventData('1','riskQuesAns_' + checkedS,'答题');
            var inputs = $(_pageId + " #" + checkedS).children("div").children("input");
            var checked = $(this).children("input").prop("checked");
            inputs.removeAttr("checked");
            $(this).children("input").attr("checked", "checked");
        }, 'click');

        //提交按钮
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            var answer = "";
            var answerNum = 0;
            var length = $(_pageId + " .risk_question").length;
            for (var i = 1; i < length + 1; i++) {
                //获得每题中是否被选择
                var $isSelect = $(_pageId + " #" + i).children("div").children("input:radio:checked");
                if ($isSelect.length > 0) {
                    $isSelect[0].value;
                    answer += $isSelect[0].value;
                    if (i == '1') {
                        custTgAssess['investmentTermDesc'] = $isSelect.parent().find("label").html()
                        custTgAssess['investmentTerm'] = $isSelect[0].value;
                    } else if (i == '2') {
                        custTgAssess['investmentObjectivesDesc'] = $isSelect.parent().find("label").html()
                        custTgAssess['investmentObjectives'] = $isSelect[0].value;
                    }
                    answerNum++;
                    if (answerNum != length) {
                        answer += ",";
                    }
                } else {
                    layerUtils.iMsg(-1, "第" + i + "题您未选择,请核对后提交");
                    break;
                }
            }
            // console.log(answer);
            if (answerNum == length) {
                var param101088 = {
                    tg_cust_question_result: answer,
                    cust_type: "1"
                };
                submintAnswer(param101088);
            }
            // appUtils.pageInit(_page_code, "combProduct/combProdList");
        });

        //点击返回
        appUtils.bindEvent(_pageId + " #getBack", function () {
            pageBack();
        });
    }

    function submintAnswer(param101008) {
        service.reqFun101088(param101008, function (data) {
            if (data.error_no == "0") {
                // let info = 
                let info = ut.getUserInf()
                custTgAssess.invalidFlag = '0';
                info.custTgAssess = custTgAssess;
                ut.saveUserInf(info);
                appUtils.pageInit("combProduct/combRiskQuestion", "combProduct/combProdList");
            } else {
                layerUtils.iMsg(-1, data.error_info);
            }
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #RiskQuestionAnswer").html("");
    }

    var riskQuestion = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = riskQuestion;
});
