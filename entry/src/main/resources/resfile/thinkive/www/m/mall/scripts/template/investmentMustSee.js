// 投资必看
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageId = "#template_investmentMustSee ",
        _page_code = "template/investmentMustSee";
    var ut = require("../common/userUtil");
    var high_player;//首页视频
    var global = gconfig.global;
    var essay_id;//文章id
    var catalog_id;//栏目id
    var video_id = '';
    var videoInfo;
    var _base64;
    let cover_path;//存视频封面
    let article_id,pageTitleName;
    var userAuthenticationStatus;
    require("../common/html2canvas.min");
    async function init() {
        if(ut.getUserInf()){
            var userStatus = await getUserAuthenticationStatus();
            userAuthenticationStatus = userStatus[0].state //获取用户认证状态
        }
        // catalog_id = '1';
        //页面埋点初始化
        tools.initPagePointData();
        //获取投资必看模板
        let templateId = '202';//测试
        // let templateId = '124';//准生产
        let res = await getTemplate({templateId:templateId});
        $(_pageId + " .investmentMustSee").html(res)
        let imgUrl = $(_pageId + " .saveImg").attr("url");
        // $(_pageId + " .saveImg img").attr("src", global.oss_url + imgUrl);
        if(imgUrl){
            url_base(global.oss_url + imgUrl)
        }
        
        video_id = $(_pageId + " .videoShow").attr("video_id");
        catalog_id = $(_pageId + " .videoShow").attr("catalog_id");
        //获取视频信息
        getVideoInfo(video_id)
        bindPageEvent();
        //获取产品解读列表
        getProductInterpretationList();
        $(_pageId + " article")[0].scrollTop = 0;
    }
    function url_base(url) {
        let requestData = {
            img_url: url
        }
        service.reqFun102119(requestData, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .saveImg img").attr("src", base64Str);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //获取视频信息
    async function getVideoInfo(video_id) {
        service.reqFun102222({video_id:video_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let res = datas.results[0]
            videoInfo = res;
            // console.log(res,111)background: url(./images/index_poster.png) center center no-repeat;
            //渲染视频封面
            // $(_pageId + " .videoShow").attr("video_id");
            cover_path = global.oss_url + res.cover_path;
            $(_pageId + " .cover_path").css({
                "background": `url(${global.oss_url + res.cover_path}) center center no-repeat`,
                "background-size": "cover"
            })
        })
    }
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    function getProductInterpretationList(){
        service.reqFun181006({catalog_id:catalog_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            list = datas.results[0];
            let catalog_idList = catalog_id.split(',');
            catalog_idList.map((item,index) => {
                let keyId = item*1;
                let html = ``;
                if(!list  || !list[keyId] || !list[keyId].length) return $(_pageId + " .productReed .list" +keyId).html(`<p style="padding:0.2rem 0;text-align: center;">暂无数据</p>`);
                list[keyId].forEach((items, index) => {
                    items.essay_id = items.article_id;
                    html += `
                        <li class="flex vertical_center ${index == 0 ? 'active_bg' : ''}" qualified_investor_visible="${items.qualified_investor_visible}" operationType="1" operationId="articleDetails" operationName="文章详情"  contentType="14" essay_id="${items.article_id}" catalog_id="${items.catalog_id}">
                           <em style="display:none">${JSON.stringify(items)}</em>
                            <p class="flex_1 m_font_size16">${items.title}</p>
                            <img class="${items.img_url ? '' : 'display_none'}" style="width:0.94rem;height:0.62rem" src="${global.oss_url + items.img_url}" alt="">
                        </li>
                    `
                })
                $(_pageId + " .productReed .list" +keyId).html(html);
            })
        })
    }
    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    //绑定事件
    function bindPageEvent() {
        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    $(_pageId + " .qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    appUtils.pageInit(_page_code, "highVersion/articleDetails",{essay_id:essay_id, catalog_id: catalog_id});
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
         // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            $(_pageId + " .qualifiedInvestor").hide();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "login_userRegistered";
            tools.saveAlbum(_page_code,param)
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转文章详情
        appUtils.preBindEvent($(_pageId + " .productReed .list"), "li", function (e) {
            pageTitleName = $(this).find("p").text();
            catalog_id = $(this).attr("catalog_id");
            essay_id = $(this).attr("essay_id");
            let qualified_investor_visible = $(this).attr("qualified_investor_visible");//是否需要验证合格投资者
            //tools.recordEventData('1','articleDetails','文章详情',{essayId:essay_id});
            if(qualified_investor_visible == '1'){
                if (!common.loginInter(_page_code)) return;            
            } 
            if(userAuthenticationStatus == '0' && qualified_investor_visible == '1' ){
                if (!ut.hasBindCard(_page_code)) return;
                return $(_pageId + " .qualifiedInvestor").show();
            }
            appUtils.pageInit(_page_code, "highVersion/articleDetails",{essay_id: essay_id, catalog_id: catalog_id});
        }, 'click');
        //跳转文章详情
        appUtils.bindEvent($(_pageId + " .about"), function () {
            article_id = $(this).attr("catalog_id");
            // pageTitleName = $(this).find("p").text();
            // let qualified_investor_visible = $(this).attr("qualified_investor_visible");//是否需要验证合格投资者
            tools.recordEventData('1','articleDetails','文章详情',{essayId:article_id});
            // if(userAuthenticationStatus == '0' && qualified_investor_visible == '1'){
            //     return $(_pageId + " .qualifiedInvestor").show();
            // }
            appUtils.pageInit(_page_code, "highVersion/articleDetails",{essay_id:article_id});
        }, 'click');
        //跳转更多 产品解读
        appUtils.bindEvent($(_pageId + " .moreProduct"), function () {
            appUtils.setSStorageInfo("catalog_id", $(this).attr("id"));
            // tools.recordEventData('1','moreProduct','更多');
            appUtils.setSStorageInfo("productInterpretationName",$(this).attr("operationname"))
            appUtils.pageInit(_page_code, "template/productInterpretation");
        });
        //点击播放
        appUtils.bindEvent($(_pageId + " .videoShow p"), function () {
            //tools.recordEventData('1','videoShow','点击播放按钮');
            let html = `<video id="userIndex_new_example_video" style="width:100%;height:100%" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
            webkit-playsinline="true" playsinline="true" autoplay height="100%" controls preload="auto" poster=""
            data-setup="{}">
            </video>`
            $(_pageId + " #new_example_div").html(html);
            //初始化视频
            high_player = videojs('userIndex_new_example_video', {
            }, function onPlayerReady() {
                //结束和暂时时清除定时器，并向后台发送数据
                this.on('ended', function () {
                    // window.clearInterval(time1);
                });
                this.on('pause', function () {
                    // window.clearInterval(time1);
                });
                this.on('waiting', function () {
                    // window.clearInterval(time1);
                })
            });
            high_player.reset();
            high_player.src({ src: global.video_oss_url + videoInfo.video_path, type: 'video/mp4' })
            high_player.load(global.video_oss_url + videoInfo.video_path)
            $(_pageId + " video").attr("poster", cover_path)
            $(_pageId + " #showVideo").show()
        });
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            //tools.recordEventData('1','close_video','关闭视频弹窗');
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        });
        //保存图片
        appUtils.bindEvent($(_pageId + " .save_picture"), function () {
            //tools.recordEventData('1','save_picture','保存图片');
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#template_investmentMustSee");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " .saveImg img")
            html2canvas(dom, {
                scale: 4,
            }).then(function (canvas) {
                var base64 = canvas.toDataURL("image/png");
                _base64 = base64;
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "60393";
                param["base64Image"] = base64;
                param["isJsCallBack"] = "1";
                param["flowNo"] = new Date().getTime() + "";
                require("external").callMessage(param, function () {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert("保存成功");
                    $(_pageId + " article")[0].scrollTop = 500;
                });
            });

         });
    }
    function destroyVideo(){
        if(high_player){
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        }
    }

    function destroy() {
        $(_pageId + " .qualifiedInvestor").hide();
        article_id = '';
        userAuthenticationStatus = '';
        tools.recordEventData('4','destroy','页面销毁');
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
       //原生返回判断是否有banner跳转
        // if(high_player){
        //     high_player.pause();
        //     setTimeout(function() {
        //         high_player.dispose();
        //         high_player = '';
        //     }, 0);
        // }
        //原生返回判断视频弹窗是否打开,如果打开关闭弹窗
        if($(_pageId + " #showVideo").is(":visible")){
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            return $(_pageId + " #showVideo").hide();
        }
        $(_pageId + " #showVideo").hide();
        $(_pageId + " .loginDig").hide();
        appUtils.pageBack();
    }

    var template_investmentMustSee = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "destroyVideo":destroyVideo
    };
    // 暴露对外的接口
    module.exports = template_investmentMustSee;
});
