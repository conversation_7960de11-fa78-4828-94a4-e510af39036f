// 财富顾问
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageCode = "account/wealthAdvisor",
        validatorUtil = require("validatorUtil"),
        _pageId = "#account_wealthAdvisor ";
    var service = require("mobileService");
    var tools = require("../common/tools");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var phone;
    function init() {
        geTwealthAdvisorInfo();
    }
    //获取财富顾问
    function geTwealthAdvisorInfo(){
        service.reqFun112019({}, function (data) {
            if(data.error_no != '0' && data.results[0]) return;
            let info = data.results[0];
            let beforUrl = gconfig.global.oss_url
            phone = info.phone ? info.phone : '';
            $(_pageId + " .name").text(info.custmanager_alias ? info.custmanager_alias : info.custmanager_name);
            $(_pageId + " .fullname").text(info.fullname ? info.fullname : '');
            $(_pageId + " .dept_name").text(info.dept_name ? info.dept_name : '');
            $(_pageId + " .position").text(info.position ? info.position : '');
            $(_pageId + " .remark").text(info.qr_code_desc ? info.qr_code_desc  : '');
            $(_pageId + " .phone").text(info.phone ? info.phone : '');
            $(_pageId + " .qr_code_url").attr('src',beforUrl + info.qr_code_url);
            if(info.background_image_url) $(_pageId + " .wealthAdvisor").css({'background-image': "url('"+beforUrl + info.background_image_url +"')",});

            //控制显隐
            if(info.fullname) $(_pageId + " .fullname").show();
            if(info.dept_name) $(_pageId + " .dept_name").show();
            if(info.position) $(_pageId + " .position").show();
            if(info.qr_code_desc) $(_pageId + " .remark").show();
            if(info.phone) $(_pageId + " .phone_info").show();
            if(info.qr_code_url) $(_pageId + " .header_portrait").show();
            $(_pageId + " .wealthAdvisor").show();
        })
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.bindEvent($(_pageId + " .phone"), function () {
            //拨打电话
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = phone;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
    }
    function destroy() {
        $(_pageId + " .wealthAdvisor").hide();
        $(_pageId + " .fullname").hide();
        $(_pageId + " .dept_name").hide();
        $(_pageId + " .position").hide();
        $(_pageId + " .header_portrait").hide();
        $(_pageId + " .phone_info").hide();
        $(_pageId + " .remark").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var wealthAdvisor = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = wealthAdvisor;
});
