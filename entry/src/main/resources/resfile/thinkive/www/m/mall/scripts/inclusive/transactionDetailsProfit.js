// 交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#inclusive_transactionDetailsProfit ";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");

    function init() {
        var param = appUtils.getPageParam();
        //分红方式交易详情查询
        if (param.sub_busi_code_e == "14301") {
            reqFun102068(param);
            $(_pageId + " .dividend_vol_text").html("分红");
        } else {
            $(_pageId + " .dividend_vol_text").html("分红");
            reqFun102074(param);

        }
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    //分红： 红利再投
    function reqFun102068(param) {
        service.reqFun102068(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);
            //交易状态
            var trans_status = results.trans_status;
            var trans_status_name = tools.fundDataDict(trans_status, "pub_trans_status_name");
            //产品名称
            var prod_name = results.prod_name;
            //产品代码
            var prod_code = "(" + results.prod_code + ")";
            //分红份额
            var dividend_vol = tools.fmoney(results.dividend_vol);
            dividend_vol = dividend_vol + "份";
            //交易流水号
            var trans_serno = results.trans_serno;
            //分红日期
            var dividend_date = results.dividend_date;
            if (dividend_date != "--") {
                dividend_date = tools.ftime(dividend_date.substring(4, 8));
            }
            //分红方式
            var dividend_method = results.dividend_method;
            var dividend_method_name = tools.fundDataDict(dividend_method, "dividend_method");
            var bonus_arrive = "";
            $(_pageId + " #bonus_arrive_box").hide();
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #prod_code").html(prod_code);
            $(_pageId + " .dividend_vol").html(dividend_vol);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " .dividend_date").html(dividend_date);
            $(_pageId + " #dividend_method").html(dividend_method_name);
        });
    }

    //分红： 分红到宝
    function reqFun102074(param) {
        service.reqFun102074(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);
            //交易状态
            var trans_status = results.trans_status;
            var trans_status_name = tools.fundDataDict(trans_status, "pub_profitJJB_status_name");
            //产品名称
            var prod_name = results.prod_name;
            //产品代码
            var prod_code = "(" + results.prod_code + ")";
            //分红份额
            var dividend_amt = tools.fmoney(results.dividend_amt);
            dividend_amt = dividend_amt + "元";
            //交易流水号
            var trans_serno = results.trans_serno;
            //分红日期
            var dividend_date = results.dividend_date;
            if (dividend_date != "--") {
                dividend_date = tools.ftime(dividend_date.substring(4, 8));
            }
			//到账日期
			var to_account_date = results.to_account_date;
			if (to_account_date != "--") {
				to_account_date = tools.ftime(to_account_date.substring(4, 8));
			}
			//分红方式
            var dividend_method = results.dividend_method;
            var dividend_method_name = tools.fundDataDict(dividend_method, "dividend_method");
            $(_pageId + " #bonus_arrive").html(dividend_method == '14302' ? '银行卡' : '晋金宝');
            $(_pageId + " .remarkType").html(dividend_method == '14302' ? '资金到账，回款到银行卡' : '资金到账，回款到晋金宝')
            $(_pageId + " #bonus_arrive_box").show();
            $(_pageId + " .apponit_box").show();
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #prod_code").html(prod_code);
            $(_pageId + " .dividend_vol").html(dividend_amt);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " .dividend_date").html(dividend_date);
			$(_pageId + " #dividend_method").html(dividend_method_name);
            $(_pageId + " #to_account_date").html(to_account_date);
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " .dividend_vol").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " .dividend_date").html("--");
        $(_pageId + " #dividend_method").html("--");
        $(_pageId + " #bonus_arrive").html("");
        $(_pageId + " #bonus_arrive_box").hide();
        $(_pageId + " .apponit_box").hide();
        $(_pageId + " .dividend_vol_text").html("");
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
