<!DOCTYPE html>
<html><!--  manifest="app.manifest" -->
<head>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<meta content="yes" name="apple-mobile-web-app-capable" />
	<meta name="format-detection" content="telephone=no">
	<title>晋金财富</title>
	<meta http-equiv="Cache-Control" content="no-store" />
	<meta http-equiv="Pragma" content="no-cache" />
	<meta http-equiv="Expires" content="0" />
	<link rel="shortcut icon" href="favicon.ico"/>
</head>
<body><!-- style="width:320px;margin:0 auto;" -->

<div id="afui"><!-- appframework扩展储备 -->
	<div id="header" style="position: relative;"></div><!-- 公用头部，待扩展 -->
	<div id="content" style="position: relative;"></div><!-- 内容区域，所有业务模块的page/panel放在这里  -->
	<div id="footer" style="position: relative;"></div><!-- 公用底部，待扩展 -->
	<nav id="leftNav"></nav><!-- 左抽屉 ，待扩展 -->
	<aside id="rightNav"></aside><!-- 右抽屉，待扩展 -->
</div>

<!-- 上线的版本号，更新前更改，开发若存在缓存问题手动改变该值 -->
<script>var _sysVersion = "1.5.2",_shellVersion = "1.5.2";</script>

<!-- 生产版本 -->
<script type="text/javascript" src="../1.2.3/hSea.min.js?v=1.5.2"></script>

<!--vconsole调试-->
<!--<script type="text/javascript" src="./js/vconsole.min.js"></script>
<script>
    window.vConsole = new window.VConsole({
        defaultPlugins: ['system', 'network', 'element', 'storage'], // 可以在此设定要默认加载的面板
        maxLogNumber: 1000
    });
</script>-->
<!-- pc浏览器调试版本 -->
<!-- <script type="text/javascript" src="../framework-base/base/seajs/sea.js"></script>
<script type="text/javascript" src="../framework-base/base/startup.js"></script> -->
</body>
</html>