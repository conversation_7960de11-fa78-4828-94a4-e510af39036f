// 投顾产品持仓模板
define(function (require, exports, module) {
    require('../common/vue.min');
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "combProduct/combHoldHeightDetail",
        _pageId = "#combProduct_combHoldHeightDetail";
    require("chartsUtils");
    var ut = require("../common/userUtil");
    var validatorUtil = require("validatorUtil");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    // require("../common/html2canvas.min");
    let combPublicHoldHeightDetail; //new 一个 vue 实例
    let productInfo;
    let prodType;
    let financial_prod_type;
    let busi_code;
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '1',
                fund_code: productInfo.fund_code
            }
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res.template_content)
            })
        })
    }
    async function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");  //缓存的列表详情
        productInfo.fund_code = productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code;
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        financial_prod_type = appUtils.getSStorageInfo("financial_prod_type");
        appUtils.setSStorageInfo("isSeriesComb", '0'); //默认非投顾系列产品
        let html = await setTemplate() //拿到模板数据
        $(".main_combHoldHeightDetail").html(html)   //渲染模板
        combPublicHoldHeightDetail = new Vue({
            el: '#main_combHoldHeightDetail',
            data() {
                return {
                    oss_url: global.oss_url,
                    button_flag: false,//底部按钮
                    detail: {},
                    buy_flag: "",    //购买按钮
                    sell_flag: "",   //卖出按钮
                    flag_data: "",
                    new_htmls: "",
                    prodMsg: [],
                    investmentNum: '',//定投个数
                    investorInfo: {},// 投资者信息
                    isShowAssetDetails: false, // 是否展示资产明细
                    targetProfit:'0', //是否是目标盈产品
                    childrenInfo:{},//子女信息
                }
            },
            //视图 渲染前
            async created() {
                prodType = $(_pageId + " .public_top").attr("prodType");
                appUtils.setSStorageInfo("prodType", prodType);
                //获取持仓详情
                appUtils.setSStorageInfo("productInfo", { ...productInfo, ...this.detail }); //产品信息
                this.detail = await this.getDetails()
                // this.reqFun102178(this.detail);
                // this.detail.prod_sub_type2 = '200';
                if(this.detail.checkdate && this.detail.checkdate != '--'){  //是否存在昨日收益更新时间
                    let formattedDate = `${'(' + this.detail.checkdate.slice(4, 6)}-${this.detail.checkdate.slice(6) + ')'}`;//处理日期数据
                    this.detail.checkdate = formattedDate;
                }else{
                    this.detail.checkdate = ''
                }
                this.detail.comb_code = this.detail.fund_code;
                //买入在途
                if (this.detail.fund_in_way_vol > 0) {
                    $(_pageId + " #fund_way_vol_box").show();
                    $(_pageId + " #fund_way_vol").html(tools.changeTwoDecimal_f(this.detail.fund_in_way_vol));
                } else {
                    $(_pageId + " #fund_way_vol_box").hide();
                }
                //卖出在途
                if (this.detail.fund_out_way_vol > 0) {
                    $(_pageId + " #fund_out_way_vol_box").show();
                    $(_pageId + " #fund_out_way_vol").html(tools.changeTwoDecimal_f(this.detail.fund_out_way_vol));
                } else {
                    $(_pageId + " #fund_out_way_vol_box").hide();
                }
                // 调仓在途
                if (this.detail.fund_change_way_vol > 0) {
                    $(_pageId + " #fund_change_way_vol_box").show();
                    $(_pageId + " #fund_change_way_vol").html(tools.changeTwoDecimal_f(this.detail.fund_change_way_vol));
                } else {
                    $(_pageId + " #fund_change_way_vol_box").hide();
                }
                // 产品资讯
                if (this.detail.prodMsg && this.detail.prodMsg.length) {
                    this.prodMsg = this.detail.prodMsg;
                }
                // this.detail.fixed_investment_list = "1";
                // this.detail.holding_yield = "1"
                this.detail.sum_fund_amt = tools.changeTwoDecimal_f(this.detail.sum_fund_amt);    //资产
                this.detail.hold_income = tools.changeTwoDecimal_f(this.detail.hold_income);  //持仓收益
                this.detail.last_income = tools.changeTwoDecimal_f(this.detail.last_income);    //昨日收益
                this.detail.accumulated_income = tools.changeTwoDecimal_f(this.detail.accumulated_income);  //累计收益
                //this.detail.fixed_investment_list = this.detail.fixed_investment_list * 1; // 是否展示定投
                // 获取购买状态
                this.reqFun102168();
                // 获取定投数量
                this.getInvestmentNum()
                this.getInvestorInfo();
                //获取子女信息
                this.getChildrenInfo()
                if (this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length > 1) {
                    this.dealSwiper(this.prodMsg);
                }
                if (this.detail.zcmxflag == 0) {
                    this.isShowAssetDetails = false
                } else {
                    this.isShowAssetDetails = true
                }
            },

            //渲染完成后
            mounted() {
                var appletEnterImg = require("gconfig").global.oss_url + $(_pageId + " #applet_enter").html();
                $(_pageId + " #applet_enter_img").attr("src", appletEnterImg);
                this.targetProfit = $(_pageId + " .public_top").attr("targetProfit") ? $(_pageId + " .public_top").attr("targetProfit") : '0';
                //缓存当前是否为投顾系列产品
                appUtils.setSStorageInfo("isSeriesComb", $(_pageId + " .header_top").attr("isSeriesComb") ? $(_pageId + " .header_top").attr("isSeriesComb") : '0');
                
                
            },
            //计算属性
            computed: {
                timeResult: () => {
                    return (time, num) => {
                        if (!time) return '--'
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
            },
            //绑定事件
            methods: {
                //陪伴服务 文章列表
                escortService(){
                    tools.recordEventData('1','escortService','陪伴服务');
                    this.detail.fund_code
                    let escortService_data = {
                        prod_id:this.detail.fund_code,
                        location:'2'
                    }
                    appUtils.setSStorageInfo("escortService_data", escortService_data);
                    appUtils.pageInit(_pageCode, "template/escortService");
                },
                //修改子女信息
                edit_info(){
                    tools.recordEventData('1','edit_info','修改子女信息');
                    //去修改页面
                    this.childrenInfo.comb_code = productInfo.fund_code;
                    appUtils.pageInit(_pageCode, "combProduct/editChildInfo", this.childrenInfo);
                },
                //获取子女信息
                getChildrenInfo(){
                    let data = {
                        comb_code: productInfo.fund_code,
                    }
                    service.reqFun102200(data, (res) => {
                        if (res.error_no == '0') {
                            let results = res.results[0]
                            this.childrenInfo = results ? results : {} //缓存子女信息
                            if(this.childrenInfo && this.childrenInfo.plan_type=="4"){
                                $(_pageId + " .saveShow").show();    
                                this.$set(this.detail, 'fixed_investment_list', 0);
                                // this.detail.fixed_investment_list = false                         
                            }else{ 
                                $(_pageId + " .saveShow").hide();
                                this.$set(this.detail, 'fixed_investment_list', this.detail.fixed_investment_list * 1);
                            }
                            // console.log(this.detail,222)
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //获取定投数量
                getInvestmentNum() {
                    let userInfo = ut.getUserInf()
                    let data = {
                        custno: userInfo.custNo,
                        fundcode: productInfo.fund_code,
                        virfundcode: productInfo.vir_fundcode,
                    }
                    service.reqFun106041(data, (res) => {
                        if (res.error_no == '0') {
                            let results = res.results[0]
                            this.investmentNum = results.amount + '个'
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },

                // 获取定投投资者相关信息
                getInvestorInfo() {
                    service.reqFun102187({
                        comb_code: productInfo.fund_code
                    }, (res) => {
                        if (res.error_no == '0') {
                            let results = res.results[0]
                            this.investorInfo = results;
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                seeDetails(str) {   //查看交易明细
                    tools.recordEventData('1','seeDetails','交易明细');
                    appUtils.setSStorageInfo("trsFundCode", this.detail.fund_code);
                    appUtils.setSStorageInfo("is_transit", '1');
                    appUtils.setSStorageInfo("busi_code", str == 'buy' ? '122' : '124');
                    appUtils.pageInit(_pageCode, "combProduct/combTransaction");
                },

                // //点击定投
                fixedInvestment() {
                    tools.recordEventData('1','fixedInvestment','定投');
                    //跳转新增定投页面
                    appUtils.setSStorageInfo("fund_code", this.detail.fund_code);
                    if (!common.loginInter(_pageCode)) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode)
                    common.changeCardInter(() => {
                        appUtils.setSStorageInfo("isAdvisoryInvestment", '1');
                        appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
                    });
                    // common.changeCardInter(function () {
                    //     if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    //         layerUtils.iConfirm("您还未进行风险测评", function () {
                    //             appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                    //         }, function () {
                    //         }, "去测评", "取消");
                    //         return;
                    //     }
                    //     appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
                    // });
                    // appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment");
                },
                //获取投顾持仓详情
                async getDetails() {
                    return new Promise(async (resolve) => {
                        let data = {
                            fund_code: productInfo.fund_code,
                            if_period: productInfo.if_period || "",
                            vir_fundcode: productInfo.vir_fundcode || "",
                            financial_prod_type: financial_prod_type
                        }
                        service.reqFun101937(data, async (data) => {
                            if (data.error_no == '0') {
                             	appUtils.setSStorageInfo("financial_prod_type", data.results[0].financial_prod_type);
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                // 买入卖出状态
                reqFun102168() {
                    service.reqFun102168({
                        comb_code: productInfo.fund_code
                    }, async (data) => {
                        if (data.error_no == '0') {
                            this.button_flag = true;
                            let results = data.results[0];
                            if (!results || results.length == 0) {
                                this.buy_flag = this.sell_flag = "no_active"
                                return;
                            }
                            if (results.buy_status != '1' && results.buy_status != '2') this.buy_flag = "no_active";
                            if (results.sell_status != '1' || parseFloat(this.detail.allredscale) == '0') this.sell_flag = "no_active";
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },

                dealSwiper(data) {
                    // console
                    $(_pageId + " #scroller_index2").html('');
                    let list = data;
                    swipeInstance = null
                    swipeInstance && swipeInstance.destroy(false); //如果存在swiper对象，清空
                    let str = ""; //轮播内容
                    let pagination = ""; //轮播导航
                    for (var i = 0; i < list.length; i++) {
                        let item = list[i];
                        // pagination += "<li></li>";
                        str += `
                            <li class="msg_list swiper-slide" data="${item.id}">
                                <div>
                                    <div style="padding: 0.2rem 0.2rem;">${item.msg_title}</div>
                                </div>
                            </li>
                        `;
                    }
                    var autoplays, loop;
                    if (list.length == 1) {
                        $(_pageId + " #scroller_index2").html(str).addClass("swiper-no-swiping");
                        $(_pageId + " .swiper-pagination").addClass("swiper-no-swiping");
                        autoplays = false;
                        loop = false;
                        pagination = "";
                    } else {
                        $(_pageId + " #scroller_index2").html(str).removeClass("swiper-no-swiping");
                        $(_pageId + " .swiper-pagination").removeClass("swiper-no-swiping");
                        autoplays = 5000;
                        loop = true;
                        pagination = ".swiper-pagination";
                    }
                    // $(_pageId + " .swiper-pagination").html(pagination);
                    $(_pageId + " #scroller_index2").html(str);
                    $(_pageId + " #scroller_index2 .banner_list").show();
                    $(_pageId + " .banner_box").show();
                    setTimeout(() => {
                        swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                            pagination: pagination,
                            autoplay: false,
                            paginationElement: "li",
                            bulletActiveClass: "check",
                            // el: '.swiper-pagination',
                            autoplayDisableOnInteraction: false,
                            observer: true, // 启动动态检查器(OB/观众/观看者)
                            observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                            loop: loop,
                            onImagesReady: function () {
                                if (callback) callback();
                            },
                            beforeDestroy: function () {
                                swipeInstance = null
                            }
                        });
                    }, 0)
                },
                //跳转定投计划页面
                investmentPlan() {
                    tools.recordEventData('1','investmentPlan','定投计划');
                    appUtils.setSStorageInfo("singlePlan", '1');
                    appUtils.setSStorageInfo("fixed_investment_list", this.detail.fixed_investment_list);
                    let param = {
                        fixed_investment_list: this.detail.fixed_investment_list
                    }
                    appUtils.setSStorageInfo("isAdvisoryInvestment", '1');
                    appUtils.pageInit(_pageCode, "fixedInvestment/investmentList", param);
                },
                //跳转产品详情
                PageToProductDetails() {
                    tools.recordEventData('1','PageToProductDetails','产品详情');
                    // appUtils.setSStorageInfo("productInfo", this.detail);
                    appUtils.pageInit(_pageCode, "combProduct/combProdDetail");
                },
                //查看交易记录
                seeTransactionRecords() {
                    tools.recordEventData('1','seeTransactionRecords','查看交易记录');
                    appUtils.setSStorageInfo("trsFundCode", this.detail.fund_code);
                    appUtils.setSStorageInfo("is_transit", '');
                    appUtils.setSStorageInfo("busi_code", '');
                    appUtils.pageInit(_pageCode, "combProduct/combTransaction");
                },
                // 查看资讯
                readProdMsg() {
                    tools.recordEventData('1','readProdMsg','查看资讯');
                    if (this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length && this.prodMsg.length <= 1) {
                        var param = this.prodMsg[0];
                        appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);
                    }
                },
                // 改版查看资讯
                seeProdMsg(item){
                    tools.recordEventData('1','readProdMsg','查看资讯',{fundMsgId:item.id});
                    if(item && item.html_content) item.html_content = encodeURIComponent(item.html_content);
                    appUtils.pageInit(_pageCode, "template/prodMsgDetail", item);
                },
                // 查看收益明细
                seeReturnsDetailed() {
                    tools.recordEventData('1','seeReturnsDetailed','查看收益明细');
                    appUtils.setSStorageInfo("trsFundCode", this.detail.fund_code);
                    appUtils.pageInit(_pageCode, "combProduct/revenueDetails");
                },
                // 投顾卖出
                combSell() {
                    tools.recordEventData('1','combSell','投顾卖出');
                    if (this.sell_flag) return;
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode);

                    let sellingtips = this.detail.sellingtips
                    if(sellingtips){
                        layerUtils.iConfirm(sellingtips,  ()=> {
                            this.page_to_sell()
                        }, function () {
                        }, "继续卖出", "坚持持有",);
                    }else{
                        this.page_to_sell()
                    }
                },
                //进入卖出页面
                page_to_sell(){
                    
                    common.changeCardInter(() => {
                        appUtils.setSStorageInfo("fund_code", this.detail.fund_code);
                        appUtils.setSStorageInfo("holdObj", { ...productInfo, ...this.detail });
                        appUtils.setSStorageInfo("targetProfit",this.targetProfit);
                        appUtils.setSStorageInfo("plan_type", this.childrenInfo.plan_type);
                        appUtils.pageInit(_pageCode, "combProduct/combProdSell");
                    });
                },
                // 投顾买入
                combBuy() {
                    tools.recordEventData('1','combBuy','投顾买入');
                    if (this.buy_flag) return;
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode)
                    common.changeCardInter(() => {
                        appUtils.setSStorageInfo("comb_code", this.detail.fund_code);
                        appUtils.setSStorageInfo("productInfo", this.detail);
                        appUtils.setSStorageInfo("plan_type", this.childrenInfo.plan_type);
                        appUtils.pageInit(_pageCode, "combProduct/combProdBuy");
                    });
                },

                // 查看服务协议
                seeServiceAgreement() {
                    tools.recordEventData('1','seeServiceAgreement','查看服务协议');
                    service.reqFun102016({
                        fund_code: this.detail.fund_code,
                        agreement_type: "prod",
                    }, function (data) {
                        if (data.error_no != 0) {
                            layerUtils.iAlert(data.error_info);
                            return
                        }
                        var res = data.results;
                        appUtils.setSStorageInfo("agreements", res);
                        appUtils.pageInit(_pageCode, "combProduct/combServiceAgreement");
                    })
                },

                seeAssetDetails() {
                    tools.recordEventData('1','seeAssetDetails','资产明细');
                    appUtils.pageInit(_pageCode, "combProduct/combAssetDetails");
                    return;
                },

                //跳转宝宝账单
                accountBook() {
                    tools.recordEventData('1','accountBook','宝宝账单');
                    // appUtils.setSStorageInfo("productInfo", this.detail);
                    appUtils.pageInit(_pageCode, "combProduct/combAccounting");
                },

                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                }

            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_pageCode)
        });
        //bannear 点击链接
        // appUtils.preBindEvent($(_pageId + " #scroller_index3"), ".msg_list", function (e) {
        //     // e.stopPropagation();
        //     // e.preventDefault();
        //     var msgInfo = JSON.parse($(this).find(".msgInfo").text());
        //     var param = msgInfo;
        //     tools.recordEventData('1','msg_list','资讯详情');
        //     appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);

        // }, 'click');
    }

    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #scroller_index3").html("");
        $(_pageId + " .main_combHoldHeightDetail").html("")
        $(_pageId + " .saveShow").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    let combHoldHeightDetailModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };

    // 暴露对外的接口
    module.exports = combHoldHeightDetailModule;
});