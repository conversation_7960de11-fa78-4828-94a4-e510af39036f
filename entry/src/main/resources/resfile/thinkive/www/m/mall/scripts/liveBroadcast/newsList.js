//消息列表页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
    layerUtils = require("layerUtils"),
    gconfig = require("gconfig"),
    des = require("des"),
    common = require("common"),
    tools = require("../common/tools"),
    validatorUtil = require("validatorUtil"),
    service = require("mobileService"),
    serviceConstants = require("constants"),
    ut = require("../common/userUtil");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    VIscroll = require("vIscroll");
    vIscroll = {"scroll": null, "_init": false};
    var global = gconfig.global;
    var _pageId = "#liveBroadcast_newsList ";
    var _pageCode = "liveBroadcast/newsList";
    let newsData,cur_page,indexParam,title;
    var isEnd = false;
    let nameType = '';//标题
    function init() {
        vIscroll = {"scroll": null, "_init": false};
        cur_page = 1;
        indexParam = appUtils.getPageParam();
        // console.log(indexParam)
        nameType = indexParam ? indexParam.jump_page_prodid : '';
        newsData =  sessionStorage.newsData ? JSON.parse(sessionStorage.newsData) : {};
        tools.initPagePointData({pageName:newsData.name});
        if(!indexParam || !indexParam.jump_page_prodid){    //渲染顶部标题栏
            $(_pageId + " .newTitle").text(newsData.name);
        }
        setData(false);
        
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    function returnName(TypeList){
        TypeList.map(item=>{
            if(item.code == nameType){
                title = item.name;
            }
        })
        $(_pageId + " .newTitle").text(title);
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击直接进入详情页
        appUtils.preBindEvent($(_pageId + " .list"), ".frame", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let invest_teach_id = $(this).attr("id");
            // let html_content = $(this).attr("html_content");
            // sessionStorage.newsDetatil = html_content;
            if(indexParam && indexParam.jump_page_prodid){
                newsData = {
                    code:indexParam.jump_page_prodid,
                    name:title
                }
                sessionStorage.newsData = JSON.stringify(newsData);
            }
            appUtils.pageInit(_pageCode, "liveBroadcast/newsDetatil", {invest_teach_id:invest_teach_id,title:newsData.name});
        }, 'click');
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        isEnd = false;
        $(_pageId + " .list").html("");
        cur_page = 1;
    };
    //初始化渲染
    function setData(flag){
        let requuestData = {
            type:(indexParam && indexParam.jump_page_prodid) ? indexParam.jump_page_prodid : newsData.code,
            current:cur_page + ''
        };
        service.reqFun112002(requuestData, (data) => {
            isEnd = false;
            if (data.error_no == '0') {
                // if(!data.results[0] || !data.results[0].data || !data.results[0].data[0]) return $(_pageId + " .list").html('<p class="m_padding_10_10 m_center">暂无数据</p>');
                var totalPages = data.results[0].pageData.totalPages; //总页数
                var res = data.results[0].pageData.data;
                let TypeList = data.results[0].TypeList;
                if(nameType && nameType != '') returnName(TypeList)
                let html = "";
                if(!res) res = [];
                res.map(item=>{
                    let bg_img = global.oss_url + item.img_url;
                    html += `
                    <ul  id=${item.id} operationType="1" operationId="frame_${item.id}" operationName="${item.title}" class="frame bg_fff flex m_padding_10_10 m_marginBottom_10">
                        <li class="m_width_70 main_flxe vertical_center">
                            ${item.title}
                        </li>
                        <li class="m_width_30">
                            <img class="m_width_100" src="${bg_img}">
                        </li>
                    </ul>
                    `
                })
                // $(_pageId + " .list").html(html)
                if (totalPages == cur_page) {
                    isEnd = true;
                    html += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && res.length == 0) {
                    isEnd = true;
                    html = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                if (flag) {
                    $(_pageId + " .list").append(html);
                } else {
                    $(_pageId + " .list").html(html);
                }
                // $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    endTime = "";
                    setData(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        setData(true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var liveBroadcast_broadcastList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_broadcastList;
});
