// 晋金高端撤单详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "highEnd/cancelOrder",
        _pageId = "#highEnd_cancelOrder ";
    var cancelOrderParam;
    var jymm1, jymm;
    var monkeywords = require("../common/moneykeywords");

    function init() {
        cancelOrderParam = appUtils.getSStorageInfo("cancelOrderParam");
        if(cancelOrderParam.prod_sub_type2 == "93"){
        	 $(_pageId + " .yuanhui_show").show();
        	 $(_pageId + " .ordinary_show").hide();
        }else{
        	 $(_pageId + " .yuanhui_show").hide();
        	 $(_pageId + " .ordinary_show").show();
        }
        $(_pageId + " .prod_sname").text(cancelOrderParam.fund_name);
        $(_pageId + " .fund_vol").text(tools.fmoney(cancelOrderParam.cost_money||cancelOrderParam.app_amt) + "元");
        $(_pageId + " .closed_length").text(cancelOrderParam.closed_length);
        $(_pageId + " .crt_date").text(tools.ftime(cancelOrderParam.crt_date));
        $(_pageId + " .due_date").text(tools.ftime(cancelOrderParam.due_date));
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //申请撤单
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_cancelOrder";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //交易密码确定
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            var param = {
                fund_code: cancelOrderParam.fund_code,
                trans_pwd: jymm1, //交易密码
                order_no: cancelOrderParam.app_order_no|| cancelOrderParam.order_no
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                cd(param);
            }, {isLastReq: false});
        });
    }


    //调用撤单接口
    function cd(param) {
        service.reqFun106017(param, function (data) {
            if (data.error_no == 0) {
                appUtils.pageInit(_page_code, "highEnd/cancelOrderResult");
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        jymm1 = "";
        $(_pageId + " .prod_sname").text("");
        $(_pageId + " .fund_vol").text("");
        $(_pageId + " .closed_length").text("");
        $(_pageId + " .establish_date").text("");
        $(_pageId + " .due_date").text("");
        $(_pageId + " .yuanhui_show").hide();
   	    $(_pageId + " .ordinary_show").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }


    var thcancelOrder = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thcancelOrder;
});
