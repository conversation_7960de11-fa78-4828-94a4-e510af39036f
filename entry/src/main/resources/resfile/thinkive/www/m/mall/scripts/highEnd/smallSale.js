define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#highEnd_smallSale ";
    var _pageCode = "highEnd/smallSale";
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");

    var _fund_code;
    var _redem_method = "1";
    var jymm;
    var _available_vol; //卖出份额展示
    var _holdmin;
    var _redeem_min;
    var highEndHoldDetail;

    function init() {
        highEndHoldDetail = appUtils.getSStorageInfo("productInfo")
        //页面埋点初始化
        tools.initPagePointData();
        _fund_code = highEndHoldDetail.fund_code
        if(highEndHoldDetail.prod_sub_type2 == "100"){
            _available_vol = parseFloat(highEndHoldDetail.available_vol); //可用份额
            $(_pageId + " .saleInfo").html("可赎回份额" + highEndHoldDetail.available_vol + "份");
        }else{
            _available_vol = parseFloat(highEndHoldDetail.available_vol); //可用份额
            $(_pageId + " .saleInfo").html("可赎回份额" + _available_vol + "份");
        }
        //获取交易时间
        reqFun102008();
        //获取最低持有份额，产品详情
        reqFun102043();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            // console.log(highEndHoldDetail)
            if(highEndHoldDetail.prod_sub_type2 == "100") return appUtils.pageInit(_pageCode, "template/heighEndProduct");
            appUtils.pageInit(_pageCode, "highEnd/smallgatherDetail");
        });
        //全部卖出
        appUtils.bindEvent($(_pageId + " #all_buy"), function () {
            $(_pageId + " #inputspanid>span").html(tools.fmoney(_available_vol)).css({"color": "#000000"});
            $(_pageId + " #czje").val(_available_vol);
        });
        
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_smallSale";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击到账方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _redem_method = $(this).attr("redem_method");
        });
        //点击下一步
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            var czjeVal = $(_pageId + " #czje").val();
            czjeVal = czjeVal.replace(/,/g, "");
            if (czjeVal <= 0 || !czjeVal) {
                layerUtils.iAlert("请输入卖出份额");
                return;
            }

            if (czjeVal > _available_vol) {
                layerUtils.iAlert("超过可用份额");
                return;
            }

            if (czjeVal < parseFloat(_redeem_min)) {
                layerUtils.iAlert("最低赎回" + _redeem_min + "份");
                return;
            }

           /* var surplus = _available_vol - czjeVal;
            if (parseFloat(surplus) < parseFloat(_holdmin) && surplus != "0") { //剩余金额 < 最低持有金额 && 剩余金额 != 0
                layerUtils.iConfirm("剩余份额低于产品最低持有份额" + _holdmin, function () {
                    $(_pageId + " #inputspanid>span").html("");
                    $(_pageId + " #czje").val("");
                    return;
                }, function () {
                    $(_pageId + " #inputspanid>span").html(tools.fmoney(_available_vol));
                    $(_pageId + " #czje").val(_available_vol);
                }, "取消", "全部赎回");
                return;
            }*/

            setRechargeInfo();
            //显示 输入交易密码
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();

            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_smallSale";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");

            var param = {
                fund_code: _fund_code,	//基金代码
                trans_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                redem_method: _redem_method,  //赎回到宝:1,赎回到卡：2.
                vir_fundcode:highEndHoldDetail.vir_fundcode ? highEndHoldDetail.vir_fundcode : ''
            };
            if(highEndHoldDetail.due_date) {
                param.due_date = highEndHoldDetail.due_date
            }
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                trade(param);
            }, {isLastReq: false});
        });
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function trade(param) {
        service.reqFun106010(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "highEnd/smallSaleResult", data.results[0]);
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                if (curVal == ".") {
                    curVal = "";
                }
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    if (moneys > _available_vol) {
                        $(_pageId + " #czje").val(tools.fmoney(_available_vol + ""));
                    } else {
                        moneys = tools.fmoney(moneys);
                        $(_pageId + " #czje").val(moneys);
                    }
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                }
                if (curVal > _available_vol) {
                    $(_pageId + " #czje").val(tools.fmoney(_available_vol + ""));
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //获取最低持有份额,产品详情
    function reqFun102043() {
        let param = {
            fund_code: _fund_code,
        }
        let results
        if(highEndHoldDetail.prod_sub_type2 != "100"){
            service.reqFun102043(param, function (data) {
                if (data.error_no == 0) {
                    results = data.results[0];
                    handleData(results)
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }else{
            service.reqFun102108(param, function (data) {
                if (data.error_no == 0) {
                    results = data.results[0];
                    results.prod_sub_type2 = "100"
                    handleData(results)
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }
        
    }
    //处理数据
    function handleData(results){
        if (!results) {
            return;
        }
        $(_pageId + " #prod_sname").text(results.prod_sname);
        $(_pageId + " .header_inner h1").text(results.prod_sname);
        //最低持有份额
        _holdmin = results.holdmin;
        //最低赎回份额
        // console.log(results.prod_sub_type2,222)
        if(results.prod_sub_type2 == "100" && results.sold_tip_show == "1"){
            layerUtils.iAlert(results.sold_tip);
        }
        _redeem_min = results.redeem_min;
        if(results.prod_sub_type2 != "100") layerUtils.iAlert("<span style='display: block;'>卖出后剩余金额必须大于" + _holdmin / 10000 + "万元，否则剩余部分将强制赎回</span>");
    }
    //获取交易时间
    function reqFun102008() {
        var param = {
            fund_code: _fund_code,
            type: "8"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);

                var dzDate = results.dzDate;
                if (dzDate != "--") {
                    dzDate = tools.FormatDateText(dzDate.substring(4));
                }

                //确认日期
                $(_pageId + " #dzDate").html(dzDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        //赎回份额
        var trans_amt = $(_pageId + " #czje").val();
        if (_redem_method == "1") {
            $(_pageId + " #payMethod").text("晋金宝");
        } else {
            $(_pageId + " #payMethod").text("银行卡");
        }
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html(tools.fmoney(trans_amt));
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " #dzDate").html("--");
        jymm = "";
        _redem_method = "1";
        $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
        $(_pageId + " .modify_box .item").eq(0).children(".icon").addClass("active");
        $(_pageId + " #czje").val("");
        $(_pageId + " #inputspanid span").text("请输入卖出份额").css({color: "rgb(153, 153, 153)"});
        $(_pageId + " #payMethod").text("--");
        $(_pageId + " #recharge_name").html("--");
        $(_pageId + " #recharge_money").html("--");
        monkeywords.destroy();
        _holdmin = "";
        _redeem_min = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
