// 产品-交易记录 中惠存
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#bank_prodTransSD2 ",
    	_pageUrl = "bank/prodTransSD2";
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var startTime = "";
    var endTime = "";
    var tools = require("../common/tools");
    var productInfo;
    function init() {
        resetInputDate();
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + ".olay").remove();
        selectDate(_pageId + '#startTime', 0, _pageId);
        selectDate(_pageId + '#endTime', 1, _pageId);
        if (appUtils.getSStorageInfo("qry_condition")) {
            var qry_condition = appUtils.getSStorageInfo("qry_condition");
            appUtils.clearSStorage("qry_condition");
            $(_pageId + " #query_type li a").removeClass("active");
            if (qry_condition.bus_type) {
                $(_pageId + "#query_type [bus_type=" + qry_condition.bus_type + "]").addClass("active");
            } else {
                $(_pageId + " #query_type li a").eq(0).addClass("active");
            }
            $('#startTime').attr('time', qry_condition.startTime).val(qry_condition.startTime);
            $('#endTime').attr('time', qry_condition.endTime).val(qry_condition.endTime);
            $(_pageId + " #query_date li a").removeClass("active");
            if (qry_condition.date_value) {
                $(_pageId + "#query_date [data-value=" + qry_condition.date_value + "]").addClass("active");
            } else {
                $(_pageId + " #query_date li a").eq(0).addClass("active");
            }
        } else {
            resetInputDate();
        }
        getUsertransaction();
    }

    function bindPageEvent() {
        //筛选日期
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " #jymx_filter").show();
            $(_pageId + " #filter_layer").show();
        });
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId + " #query_date li"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).find("a").addClass("active");
            var data_value = $(this).find("a").attr("data-value");
            setInputDate("endTime", new Date());
            var start_date = new Date();//开始日期
            if (data_value == "90") {
                start_date.setMonth(start_date.getMonth() - 3);
            } else if (data_value == "360") {
                start_date.setFullYear(start_date.getFullYear() - 1);
            } else {
                start_date = "";
                endTime = "";
                setInputDate("endTime", "");
            }
            setInputDate("startTime", start_date);
        });
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId + " #query_type li"), function () {
            $(_pageId + " #query_type li a").removeClass("active");
            $(this).find("a").addClass("active");

        });
        //重置
        appUtils.bindEvent($(_pageId + " #reset"), function () {
            resetInputDate();
        });
        //确定
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
            endTime = "";
            getUsertransaction();
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " #filter_layer"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
        });
        //点击取消时间控件
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");
        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function getUsertransaction() {
        $(_pageId + " .new_none").hide();
        endTime = $(_pageId + '#endTime').attr('time').replace(/-/g, "");
        startTime = $(_pageId + '#startTime').attr('time').replace(/-/g, "");
        var bus_type = $(_pageId + " #query_type .active").attr("bus_type");
        var bus_type_name = {
            "240": "购买",
            "241": "提前支取",
            "242": "到期系统自动兑付",
            "244": "到期支取",
            "245": "周期付息",
        }
        var param = {
            bank_channel_code: productInfo.bank_channel_code,
            str_date: startTime,
            end_date: endTime,
            bus_type: bus_type, //240-购买   241-提前支取 242-到期系统自动兑付  244-到期支取 245-周期付息 N
            order_no: productInfo.order_no,
            brnd_sris: productInfo.brnd_sris
        };

        var gettransActionCallBack = function (data) {
            if (data.error_no == "0") {
                var results = data.results[0].trans_info_list;
                if (data.tol_num == "0") {
                    $(_pageId + " .trade_history").html('<div class="nodata">暂无数据</div>');
                    return;
                }
                var str = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        //购买日期
                        var bus_type = results[i].bus_type;//交易类型
                        var totAmt = results[i].totAmt; //总金额
                        var trans_amt = results[i].trans_amt; //交易金额
                        var txn_amt = results[i].txn_amt; //交易本金
                        var txn_lnt = results[i].txn_lnt; //交易利息
                        var rgs_date = results[i].rgs_date; //登记日期
                        var rgs_time = results[i].rgs_time; //登记时间
                        var remark = results[i].remark;
                        if(bus_type == "517"){
                        	money = tools.fmoney(trans_amt);
                        }else{
                        	money = tools.fmoney(totAmt);
                        }
                       /* if(bus_type == "241" || bus_type == "242" || bus_type =="244") {
                            remark = "<div style='width: 100%' class='g_fontSize12 text_gray'>备注：本金：" + tools.fmoney(txn_amt) + "，利息：" + tools.fmoney(txn_lnt) + "</div>"
                        }*/
                        str += '<div class="trade_box">' + 
                        	'<div class="fundInfo">' +'<p style="display:none" class="info">' + JSON.stringify(results[i]) + 
                            '</p><p class="g_fontSize16">' + bus_type_name[bus_type] + '</p>' +
                            '<p class="date g_fontSize14 text_gray">' + tools.ftime(rgs_date) + " " +tools.ftime(rgs_time) + '</p>' +
                            '</div>' +
                            '<div class="result" style="margin-right:4%;">' +
                            '<p>' + money + '元</p>' +
                            '<p>交易成功</p>' +
                            '</div>' +
                            '<div class="jjsss" style="position: absolute;right: 2%;top: 0.4rem;"></div>' +
                            '</div>';
                    }
                }
                if (results && results.length == "0" || !results) {
                    str += '<div class="nodata">暂无数据</div>'
                }

                $(_pageId + " .my_finance").hide();
                $(_pageId + " #v_container_productList").show();
                $(_pageId + " .trade_history").html(str);
                $(_pageId + " .trade_history").show();
              
              //详情
                appUtils.bindEvent($(_pageId + " .trade_box"), function () {
                	var info = JSON.parse($(this).find(".info").text());
                    var param = {
                        date_value: $(_pageId + " #query_date li a.active").attr("data-value"),
                        startTime: $(_pageId + " #startTime").val(),
                        endTime: $(_pageId + " #endTime").val(),
                        bus_type: $(_pageId + " #query_type li a.active").attr("bus_type")
                    }
                    appUtils.setSStorageInfo("qry_condition", param); //保留此次筛选条件
                	  var trsObj = {
                      		busiCode: info.bus_type,
                      		crtDate: info.rgs_date,
                      		crtTime: info.rgs_time,
                      		totAmt: info.totAmt,
                      		transAmt: info.trans_amt,
                      		remark: info.remark,
                      		transStatus: "3"
                          }
                    appUtils.setSStorageInfo("getTransation", trsObj);
                    appUtils.pageInit(_pageUrl, "bank/transactiondetail");
                }, "click");

            } else {
                layerUtils.iAlert(data.error_info);
            }
        };
        service.reqFun151109(param, gettransActionCallBack);
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        page = 1;
        $(_pageId + " #start_date").val("查询日期");
        $(_pageId + " .my_finance").hide();
        $(_pageId + " .trade_history").hide();
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        endTime = "";
        resetInputDate();
    }

    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", date);
            $(_pageId + " #" + id).val(date);
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        //快捷筛选选中
        $(_pageId + " #jymx_filter ul li a").removeClass("active");
        $(_pageId + " #query_type li").eq(0).find("a").addClass("active");
        $(_pageId + " #query_date li").eq(0).find("a").addClass("active");
        //开始时间框设置
        $(_pageId + '#startTime').attr("time", "");
        $(_pageId + '#startTime').val("");
        $(_pageId + '#endTime').attr("time", "");
        $(_pageId + '#endTime').val("");
    }

    var transaction = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = transaction;
});
