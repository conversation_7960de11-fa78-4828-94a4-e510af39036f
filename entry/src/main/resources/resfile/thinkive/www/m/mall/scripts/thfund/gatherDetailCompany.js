// 基金公司
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),

        _pageCode = "thfund/gatherDetailCompany",
        _pageId = "#thfund_gatherDetailCompany";
    var ut = require("../common/userUtil");
    var common = require("common");
    var _fund_code = "";

    function init() {
        _fund_code = appUtils.getSStorageInfo("fund_code");
        //查询产品信息
        reqFun102028();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //查询产品信息
    function reqFun102028() {
        var param = {
            fund_code: _fund_code
        }
        service.reqFun102028(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                //数据处理 空 和 --
                results = tools.FormatNull(results);
                tools.initFundBtn(results, _pageId);

                var establish_date = results.establish_date;
                if (establish_date != "--") {
                    establish_date = tools.ftime(establish_date);
                }

                $(_pageId + " #prod_name").html(results.prod_name);
                $(_pageId + " #prod_sname").html(results.prod_sname);
                $(_pageId + " #fund_code").html(results.fund_code);
                $(_pageId + " #fund_type_name").html(results.fund_type_name);
                $(_pageId + " #establish_date").html(establish_date);
                $(_pageId + " #mgrcomp_name").html(results.mgrcomp_name);
                $(_pageId + " #trustee_name").html(results.trustee_name);
                $(_pageId + " #business_scope").html(results.invest_scope);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_sname").html("--");
        $(_pageId + " #fund_code").html("--");
        $(_pageId + " #fund_type_name").html("--");
        $(_pageId + " #establish_date").html("--");
        $(_pageId + " #mgrcomp_name").html("--");
        $(_pageId + " #trustee_name").html("--");
        $(_pageId + " #business_scope").html("--");
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
        $(_pageId + " #buy_state").html("--");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailCompany = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailCompany;
});
