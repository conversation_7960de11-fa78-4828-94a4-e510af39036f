<!-- 理财计算器 -->
<div class="page" id="scene_financialCalculator" data-pageTitle="理财计算器" style="-webkit-overflow-scrolling: touch;">
    <section id="product" class="main fixed" data-page="home" style="padding-top: 0rem; padding-bottom: 0rem;">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" class="icon_back icon_gray" style="z-index: 99" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center pageTitle">理财计算器</h1>
            </div>
        </header>
        <article style="padding-bottom: 0rem;">
            <!-- 白色背景容器 -->
            <div class="calculator-container" style="background: white; padding-left: 20px; padding-right: 20px; padding-top: 1px; padding-bottom: 20px; margin-top: 15px;">

                <!-- 首投金额 -->
                <ul class="flex" style="margin-top: 10px;margin-bottom: 10px">
                    <li class="color_000">首投金额</li>
                    <li class="m_text_999 right" id="inputspanidAmt" operationType="1" operationId="inputspanidAmt" operationName="弹出数字键盘">
                        <span class="unable" text=''></span>
                    </li>
                    <input id="czje_Amt" style="display: none;" readonly="readonly">
                </ul>

                <div class="divider" style="border-top: 1px solid #ddd; margin: -2px -20px;"></div>

                <!-- 每月定投 -->
                <ul class="flex" style="margin-top: 10px;margin-bottom: 10px">
                    <li class="color_000">每月定投</li>
                    <li class="m_text_999 right" id="inputspanidInvestAmt" operationType="1" operationId="inputspanidInvestAmt" operationName="弹出数字键盘">
                        <span class="unable cursor-show" text=''></span>
                    </li>
                    <input id="czje_InvestAmt" style="display: none;" readonly="readonly">
                </ul>

                <div class="divider" style="border-top: 1px solid #ddd; margin: -2px -20px;"></div>

                <!-- 年收益率标签 -->
                <div class="slider-container" style="margin: 16px 0;">
                    <label for="annualRate" style="width: 100%; display: block; font-size: 14px; color: black;">年收益率</label>
                </div>

                <!-- 滑动条 -->
                <div class="slider-wrap" style="width: 100%; display: block; margin-top: 16px;">
                    <input type="range" id="annualRate" min="2" max="10" value="5" step="1" class="slider">
                    <span id="annualRateValue" class="annual-rate-value" style="display: block; text-align: center; margin-top: 5px;">5%</span>
                </div>
            </div>

            <!-- 计算按钮 -->
            <div class="calculate-button-container" style="padding: 0 20px; margin-top: 20px; text-align: center;">
                <button id="calculateButton" style="width: 65%; height: 30px; background-color: #e5443c; color: white; font-size: 14px; border: none; border-radius: 22px;margin-bottom: 0.2rem;">计算年度收益</button>
            </div>

            <!-- 计算结果表格 -->
            <div id="resultContainer" style="display: none;">
                <!-- 计算结果字段提示 -->
                <!--<div id="resultFieldDescription" class="result-field-description" style="margin: 15px auto 10px; width: 85%; max-width: 400px; text-align: left; font-size: 14px; color: black;">
                    <strong>计算结果</strong>
                </div>-->
                <table id="resultTable" class="result-table">
                    <thead>
                        <tr class="default-row">
                            <td>时间</td>
                            <td>累计投入</td>
                            <td>预计收益</td>
                        </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
            </div>
        </article>
    </section>
</div>