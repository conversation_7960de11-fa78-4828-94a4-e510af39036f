// 大集合买入卖出规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),

        _pageCode = "thfund/gatherDetailRule",
        _pageId = "#thfund_gatherDetailRule";
    var productInfo;
    var ut = require("../common/userUtil");
    var common = require("common");

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        if(productInfo.prod_sub_type2 == "200"){
            $(_pageId + " .gather").hide();
            $(_pageId + " .buy_rule").html(productInfo.open_redeem_rule);
            $(_pageId + " .rule_box").hide();
            if (productInfo.buy_state == "1") { // 购买
                $(_pageId + " .method_name").html("买入");
                $(_pageId + " .purchase_box").show();
            } else if (productInfo.buy_state == "2") { // 预约
                $(_pageId + " .method_name").html("预约");
                $(_pageId + " .apponit_box").show();
            } else if (productInfo.buy_state == "3") { // 敬请期待
                $(_pageId + " .comingsoon_box").show();
                $(_pageId + " .method_name").html("买入");
            } else if (productInfo.buy_state == "4" || productInfo.buy_state == "5") { //封闭中||  售罄
                $(_pageId + " .method_name").html("买入");
                $(_pageId + " .soldout_box").show();
            }
            $(_pageId + "  .issue_end_time").html(tools.FormatDateText(productInfo.issue_end_time.substr(4, 4)) || "-月-日");
            $(_pageId + "  .purconfirm_days").html(tools.FormatDateText(productInfo.confirm_date.substr(4, 4)) || "-月-日");
            $(_pageId + "  .redconfirm_days").html(tools.FormatDateText(productInfo.due_date.substr(4, 4)) || "-月-日");
        }else{
            $(_pageId + " .buy_rule").hide();
            $(_pageId + " .gather").show();
            //获取详情
            reqFun102028();
        }

        tools.initFundBtn(productInfo, _pageId);
        getRateInfo();
        //查询产品赎回提示 
        reqFun102047(productInfo.fund_code);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.preBindEvent($(_pageId + " .highFinancialRate"),".rule_tip", function () {
            layerUtils.iAlert("客户维护费指基金管理人与基金销售机构通过基金销售协议约定，依据销售机构销售基金的保有量，从基金管理费中列支一定比例，用以向基金销售机构支付客户服务及销售活动中产生的相关费用。");
        });
        //点击买入规则
        appUtils.bindEvent($(_pageId + " #payRule"), function () {
            if(productInfo.prod_sub_type2 == "200"){
                $(_pageId + " .gather").hide();
                $(_pageId + " .buy_rule").html(productInfo.open_redeem_rule);
            }else{
                $(_pageId + " .buy_rule").hide();
                $(_pageId + " .gather").show();
            }
            $(_pageId + " #payRule").addClass("active");
            $(_pageId + " #enchashmentRule").removeClass("active");

            $(_pageId + " #payRuleBox").show();
            $(_pageId + " #enchashmentRuleBox").hide();
        });
        //点击到期规则
        appUtils.bindEvent($(_pageId + " #enchashmentRule"), function () {
            if(productInfo.prod_sub_type2 == "200"){
                $(_pageId + " .gather").hide();
                $(_pageId + " .sold_rule").html(productInfo.redeem_rules);
            }else{
                $(_pageId + " .sold_rule").hide();
                $(_pageId + " .gather").show();
            }
            $(_pageId + " #payRule").removeClass("active");
            $(_pageId + " #enchashmentRule").addClass("active");

            $(_pageId + " #enchashmentRuleBox").show();
            $(_pageId + " #payRuleBox").hide();
        });
    }


    //获取详情
    function reqFun102028() {
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102028(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);
                //起购
                var threshold_amount = results.threshold_amount;
                //追加
                var addition_amt = results.addition_amt;
                //投资上限
                var max_amt = results.max_amt;
                var BuyRules = '2、' +
                    '<sapn id="rules_threshold_amount">' + threshold_amount + '</sapn>元起购 ，' +
                    '<span id="addition_amt">' + addition_amt + '</span>元递增 。';
                $(_pageId + " #BuyRules").html(BuyRules);

                var buy_state = results.buy_state;
                $(_pageId + " .rule_box").hide();
                $(_pageId + " .deadline_box").show();
                if (buy_state == "1") { // 购买
                    $(_pageId + " .method_name").html("买入");
                    $(_pageId + " .purchase_box").show();
                } else if (buy_state == "2") { // 预约
                    $(_pageId + " .method_name").html("预约");
                    $(_pageId + " .apponit_box").show();
                } else if (buy_state == "3") { // 敬请期待
                    $(_pageId + " .comingsoon_box").show();
                    $(_pageId + " .method_name").html("买入");
                } else if (buy_state == "4" || buy_state == "5") { //封闭中||  售罄
                    $(_pageId + " .method_name").html("买入");
                    $(_pageId + " .soldout_box").show();
                }

                //交易规则
                var issue_end_time = results.issue_end_time;
                var purconfirm_days = results.purconfirm_days;
                var due_date = results.due_date; //预计到期
                $(_pageId + "  .issue_end_time").html(tools.FormatDateText(issue_end_time.substr(4, 4)) || "-月-日");
                $(_pageId + "  .purconfirm_days").html(tools.FormatDateText(purconfirm_days.substr(4, 4)) || "-月-日");
                $(_pageId + "  .redconfirm_days").html(tools.FormatDateText(due_date.substr(4, 4)) || "-月-日");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getRateInfo() {

        // 产品详情查询--费率查询
        service.reqFun102003({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                var purchaseRateStr = "";
                var operateRateStr = "";
                var redeemRateStr = "";
                if (result.purchaseRate && result.purchaseRate.length > 0) {
                    purchaseRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.purchaseRate[0].chgrate_type_desc + '</h1><div><p>适用金额</p>' +
                        '<p>申购费率</p></div>';
                    for (var i = 0; i < result.purchaseRate.length; i++) {
                        var fcitem_tval = result.purchaseRate[i].fcitem_tval;
                        if (fcitem_tval == "-1") {
                            fcitem_tval = "以上";
                        } else {
                            fcitem_tval = '--' + fcitem_tval + result.purchaseRate[i].fcitem_tvunit;
                        }
                        purchaseRateStr +=
                            '<div><p>' + result.purchaseRate[i].fcitem_lval + result.purchaseRate[i].fcitem_lvunit + fcitem_tval + '</p>' +
                            '<p>' + result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit + '</p></div>';
                    }
                    purchaseRateStr += "</div>"
                }
                if (result.operateRate && result.operateRate.length > 0) {
                    operateRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.operateRate[0].chgrate_type_desc + '</h1><div>';
                    for (var i = 0; i < result.operateRate.length; i++) {
                        if(result.operateRate[i].chgrate_type == "11"){
                            if(result.operateRate[i].commission_per_p && result.operateRate[i].commission_per_p > 0){
                                var str = '<p style="height:0.6rem;line-height: 0.6rem;">' + result.operateRate[i].chgrate_item_desc + '</p>' +
                                    '<p style="height: auto;line-height: 0.3rem;"><em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                    '<em class="proportion" >·客户维护费占' + (+result.operateRate[i].commission_per_p).toFixed(2)/1 + '%<img src="../mall/images/rule_tip.png" class="rule_tip"></em></p></div><div>';
                            }else{
                                var str = '<p style="">' + result.operateRate[i].chgrate_item_desc + '</p>' +
                                    '<p style=""><em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                    '</p></div><div>';
                            }

                            result.operateRate.splice(i,1);
                        }
                        var chgrate_tval = (+result.operateRate[i].chgrate_tval).toFixed(4);
                        operateRateStr += '<p>' + result.operateRate[i].chgrate_item_desc + '</p>' +
                            '<p>' + chgrate_tval + result.operateRate[i].chgrate_unit + '</p></div><div>';

                    }
                    operateRateStr += str + "</div>"
                }
                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.redeemRate[0].chgrate_type_desc + '</h1><div><p>适用期限</p>' +
                        '<p>赎回费率</p></div>';
                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;
                        if (fcitem_tval == "-1") {
                            fcitem_tval = "以上";
                        } else {
                            fcitem_tval = '--' + fcitem_tval + result.redeemRate[i].fcitem_tvunit;
                        }
                        redeemRateStr += '<div>' +
                            '<p>' + result.redeemRate[i].fcitem_lval + result.redeemRate[i].fcitem_lvunit + fcitem_tval + '</p>' +
                            '<p>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</p> </div>';
                    }
                    redeemRateStr += "</div>";
                }
                $(_pageId + " #payRuleBox .highFinancialRate").html(purchaseRateStr + operateRateStr);
                $(_pageId + " #enchashmentRuleBox .highFinancialRate").html(redeemRateStr);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    //查询产品赎回提示 
    function reqFun102047(fund_code) {
        var param = {
            fund_code: fund_code,
        }
        service.reqFun102047(param, function (data) {
            if (data.error_no == 0) {
                if (!data.results || data.results.length == 0) {
                    return;
                }
                var results = data.results[0];

                var redeem_desc = results.redeem_desc;
                if (redeem_desc) {
                    $(_pageId + " #redeem_desc_parent").show();
                    $(_pageId + " #redeem_desc").html(redeem_desc);
                } else {
                    $(_pageId + " #redeem_desc_parent").hide();
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        $(_pageId + " #buy_state").html("--");
        $(_pageId + " #payRule").addClass("active");
        $(_pageId + " #enchashmentRule").removeClass("active");
        $(_pageId + " #rules_threshold_amount").html("--");
        $(_pageId + " #payRuleBox").show();
        $(_pageId + " #enchashmentRuleBox").hide();
        $(_pageId + " .highFinancialRate").html("");
        $(_pageId + " .issue_end_time").html("-月-日");
        $(_pageId + " .purconfirm_days").html("-月-日");
        $(_pageId + " .redconfirm_days").html("-月-日");
        $(_pageId + " .rule_box ").hide();
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
        $(_pageId + " #redeem_desc").html("--");
        $(_pageId + " #BuyRules").html("");
        $(_pageId + " .method_name").html("");

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
