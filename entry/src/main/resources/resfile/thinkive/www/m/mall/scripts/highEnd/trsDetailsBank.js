// 交易记录详情- 预约购买
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            _pageId = "#highEnd_trsDetailsBank ";
    var tools = require("../common/tools");

    function init() {
        var param = appUtils.getPageParam();
        if (param.prod_sub_type2 == "90") {
            $(_pageId + " .normal").removeClass("hidden").addClass("block");
            $(_pageId + " .small").addClass("hidden");
        } else{
            $(_pageId + " .small").removeClass("hidden").addClass("block");
            $(_pageId + " .normal").addClass("hidden");
        }
        //预约购买交易详情查询
        reqFun102102(param);
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function reqFun102102(param) {
        service.reqFun102102(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);

            //交易金额
            var trans_amt = tools.fmoney(results.trans_amt);
            trans_amt = trans_amt + "元";
            //交易状态
            var trans_status = results.trans_status;
            var trans_status_name = tools.fundDataDict(trans_status, "pri_trans_status_name");

            //待确定，暂时展示三个成功状态
            var item0 = $(_pageId + " .rule_box .item")[0];
            var item1 = $(_pageId + " .rule_box .item")[1];
            var item2 = $(_pageId + " .rule_box .item")[2];

            if (true) {
                //显示三个成功状态
                $(item0).find(".name").removeClass("undone");
                $(item1).find(".name").removeClass("undone");
                $(item2).find(".name").removeClass("undone");
            }

            //产品名称
            var prod_name = results.prod_name;
            //产品代码
            var prod_code = "(" + results.prod_code + ")";
            //发起订单日期
            var appointment_date = results.appointment_date;
            if (appointment_date != "--") {
                appointment_date = tools.FormatDateText(appointment_date.substring(4, 8));
            }
            //确认订单日期
            var match_date = results.match_date;
            if (match_date != "--") {
            	match_date = tools.FormatDateText(match_date.substring(4, 8));
            }
            //买入日期
            var trans_date = results.trans_date;
            if (trans_date != "--") {
                trans_date = tools.FormatDateText(trans_date.substring(4, 8));
            }
            //确认日期
            var ack_date = results.ack_date;
            if (ack_date != "--") {
                ack_date = tools.FormatDateText(ack_date.substring(4, 8));
            }
            //交易流水号
            var trans_serno = results.trans_serno;
            //确认份额
            var ack_vol = tools.fmoney(results.ack_vol);
            ack_vol = ack_vol + "份";
            //确认金额
            var ack_amt = tools.fmoney(results.ack_amt);
            ack_amt = ack_amt + "元";
            //交易时间
            var trans_time = results.trans_time;
            trans_time = tools.ftime(trans_time);
            //确认净值
            var ack_nav = tools.fmoney(results.ack_nav, 4);
            ack_nav = ack_nav + "元";
            //手续费
            var feet_amt = tools.fmoney(results.feet_amt);
            feet_amt = feet_amt + "元";
            //撤单备注
            if (results.remark && results.move_date) {
                $(_pageId + " #remark").html(tools.ftime(results.move_time) + " " + results.remark);
                $(_pageId + " .remark_box").show();
            } else {
                $(_pageId + " .remark_box").hide();
            }

            $(_pageId + " .trans_amt").html(trans_amt);
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #prod_code").html(prod_code);
            $(_pageId + " #appointment_date").html(appointment_date);
            $(_pageId + " #match_date").html(match_date);
            $(_pageId + " #buy_date").html(trans_date);
            $(_pageId + " #ack_date").html(ack_date);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " #ack_vol").html(ack_vol);
            $(_pageId + " #ack_nav").html(ack_nav);
            $(_pageId + " #ack_amt").html(ack_amt);
            $(_pageId + " #feet_amt").html(feet_amt);
            $(_pageId + " #trans_time").html(trans_time);
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        $(_pageId + " #trans_amt").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " #appointment_date").html("--");
        $(_pageId + " #buy_date").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " #ack_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " .remark_box").hide();
        $(_pageId + " .small").removeClass("block").addClass("hidden");
        $(_pageId + " .normal").removeClass("block").addClass("hidden");
        $(_pageId + " #ack_nav").html("--");
        $(_pageId + " #feet_amt").html("--");
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
