// 会员福利 - 邀请好友
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageCode = "vipBenefits/friendInvitation",
        _pageId = "#vipBenefits_friendInvitation ";
    var layerUtils = require("layerUtils");
    var common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService");
    var userInfo, cust_no;
    var tools = require("../common/tools");//升级
    var ut = require("../common/userUtil");
    var external = require("external");
    var type,source;
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var userChooseVerson,scene_code,mobileWhole,activityInfo;
    var ewmInfo;//二维码活动信息
    require("../common/jquery.qrcode.min");
    function init() {
        //获取当前用户选中的版本
        userChooseVerson = common.getLocalStorage("userChooseVerson"); //判断用户是否登陆过
        //获取当前用户初始版本
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        mobileWhole = common.getLocalStorage("mobileWhole");
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        scene_code = scene_code ? scene_code : '';
        mobileWhole = mobileWhole ? mobileWhole : '';
        type = appUtils.getPageParam("type");//是否是活动分享
        source = appUtils.getPageParam("source") ? appUtils.getPageParam("source") : appUtils.getSStorageInfo("source");
        appUtils.setSStorageInfo("source",source);///保存来源
        userInfo = ut.getUserInf();
        cust_no = userInfo.cust_no;
        $(_pageId + " #code").html("");
        tools.footerShow(_pageId);
        //获取当前活动信息
        getActivityInfo();
        // qrcode();
    }
    //获取活动信息
    function getActivityInfo(){
        service.reqFun108053({}, (data) => {
            if (data.error_no == 0) {
                let res = data.results[0];
                if(!res || !res.recommendedCount) {
                    $(_pageId + " .newMain").hide();
                    $(_pageId + " .oldMain").show();
                    return
                }
                $(_pageId + " .textHtml").html(res.introduce);
                //拿到日期
                let activityDate = $(_pageId + " .myFriend-card-date").attr('date-id');
                $(_pageId + " .activityDate").text(activityDate);
                activityInfo = res; //存储活动信息
                sessionStorage.activity_id = activityInfo.activityId;
                type == "shareTemplate"
                // console.log(activityInfo,111)
                qrcode(activityInfo)
                $(_pageId + " .recommendedCount").text(activityInfo.recommendedCount?activityInfo.recommendedCount + '人':'0' + '人');
                $(_pageId + " .investedCount").text(activityInfo.investedCount?activityInfo.investedCount + '人':'0' + '人')
                //查询分享模板信息
                // getShareInfo();
                $(_pageId + " .newMain").show();
                $(_pageId + " .oldMain").hide();
                // console.log(res,11)
            } else {
                qrcode()
                //报错，或者活动异常 展示之前的逻辑
                // type == ""
                sessionStorage.activity_id = '';
                $(_pageId + " .newMain").hide();
                $(_pageId + " .oldMain").show();
                // console.log(data)
                // layerUtils.iAlert(data.error_info);
            }
        })
    }
    //查询分享模板信息  
    function getShareInfo(){
        let userInfo = ut.getUserInf();
        let param = {
            tem_type:activityInfo.shareTemplate,
            registered_mobile : userInfo.mobileWhole
        }
        service.reqFun102012(param, (data) => {
            if (data.error_no == 0) {
                let res = data.results[0];
                console.log(res,2)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //我的邀请
        appUtils.bindEvent($(_pageId + " #recommendedEarnings"), function () {
            //新版活动区分
            if(activityInfo && activityInfo.shareTemplate){
                appUtils.setSStorageInfo("saveMoneyFlag",'1');
            }
            appUtils.pageInit(_pageCode, "inviteFriends/recommendedEarnings");
        });
        
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击进入积分规则
        appUtils.bindEvent($(_pageId + " #score_rule"), function () {
            appUtils.pageInit(_pageCode, "vipBenefits/scoreRule");
        });

        // 生成邀请图片
        appUtils.bindEvent($(_pageId + " #invite_img"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            let new_source = source == '3' ? '4' :  source//区分主动和月度/新手
            
            appUtils.pageInit(_pageCode, "vipBenefits/imgInvitation",{source:new_source});
        });
        //邀请好友
        appUtils.bindEvent($(_pageId + " #invite_url"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            $(_pageId + " #pop_layer").show();
        });
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            if (sessionStorage.activity_id) setUser()
            // tools.clickPoint(_pageId, _pageCode, 'share_WeChat', sessionStorage.activity_id);
            if (type == "shareTemplate") {
                common.share("22", sessionStorage.share_template ? sessionStorage.share_template : "0", sessionStorage.activity_id, "", "", "", true,source);
            } else {
                common.share("22", sessionStorage.share_template ? sessionStorage.share_template : "0", sessionStorage.activity_id, "", "", "", false,source);
            }

        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            if (sessionStorage.activity_id) setUser()
            // tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', sessionStorage.activity_id);
            if (type == "shareTemplate") {
                common.share("23", sessionStorage.share_template ? sessionStorage.share_template : "1", sessionStorage.activity_id, "", "", "", true,source);
            } else {
                common.share("23", sessionStorage.share_template ? sessionStorage.share_template : "1", sessionStorage.activity_id, "", "", "", false,source);
            }
        });
        //分享到小程序
        appUtils.bindEvent($(_pageId + " .shareProgram"), function () {
            let userInfo = ut.getUserInf();
            //获取用户性别
            let gender = userInfo.gender == "1" ? "先生" : "女士";
            //获取用户姓
            let inviterName = userInfo.name ?  userInfo.name.split('')[0] : '';
            //获取用户手机号
            let inviterMobile = userInfo.mobileWhole;
            inviterMobile = common.desEncrypt("inviterMobile", inviterMobile);
            //直接跳转小程序
            if(activityInfo && activityInfo.shareTemplate && !ewmInfo.share_url.includes('#')){
                let param = {
                    funcNo:'50231',
                    shareType:'22', 
                    title:'晋金财富',
                    link:'https://www.baidu.com',
                    webpageUrl:'https://www.baidu.com',
                    content:'分享给朋友',
                    userName:global.appletAuthentication.appId,
                    description:"晋金财富",
                    type:global.appletAuthentication.miniprogramType,
                    shareContentType:'10',
                    path:`pages/login/inviteRegister/inviteRegister?inviterMobile=${inviterMobile}&inviterName=${inviterName + gender}`,
                    imgUrl:'https://m.xintongfund.com/m/mall/images/120.png',
                }
                external.callMessage(param);
            }else{
                $(_pageId + " #pop_layer").show();
            }
        });
    }
    //动态生成二位码
    function qrcode(activityInfo) {
        var mobile = ut.getUserInf().mobileWhole;
        let share_type = sessionStorage.share_template ? sessionStorage.share_template : "7";
        if (type == "shareTemplate") {
            share_type = '7'
        }
        // if(activityInfo && activityInfo.shareTemplate){
        //     //新版活动特殊处理
        //     share_type = activityInfo.tem_type
        // }
        // console.log(share_type)
        share(mobile, share_type,activityInfo);
    }
    //记录用户行为
    function setUser() {
        service.reqFun108009({ id: sessionStorage.activity_id, cust_no: cust_no }, (data) => {
            if (data.error_no == 0) {
                //记录成功
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function share(mobile, share_type,activityInfo) {
        console.log(share_type,111)
        // 简化分享链接
        let userInfo = ut.getUserInf();
        //获取用户性别
        let gender = userInfo.gender == "1" ? "先生" : "女士";
        //获取用户姓
        let inviterName = userInfo.name ?  userInfo.name.split('')[0] : '';
        //获取用户手机号
        let inviterMobile = userInfo.mobileWhole;
        inviterMobile = common.desEncrypt("inviterMobile", inviterMobile);
        var query_params = {};
        query_params["registered_mobile"] = mobile;
        query_params["tem_type"] = share_type;
        // if (activityInfo && activityInfo.shareTemplate) {
            //新版活动特殊处理
            // query_params["share_template"] = activityInfo.shareTemplate;
            // sessionStorage.share_template = activityInfo.shareTemplate;
            // query_params["tem_type"] = ''
        // }
        service.reqFun102012(query_params, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results != undefined && data.results.length > 0) {
                    mobile = common.desEncrypt("mobile", mobile);//加密
                    var result = data.results[0];
                    ewmInfo = result;
                    var share_url = result.share_url;
                }
                if (validatorUtil.isEmpty(share_url)) {
                    share_url = global.link;
                }
                let new_source = source == '3' ? '2' :  source//区分主动和月度/新手
                if (share_url.indexOf("?") != -1) { 
                    share_url = share_url + "&mobile=" + mobile + "&source=" + new_source ;
                } else {
                    let lastUrl = share_url.split('!/')[1]; // 获取最后一级url
                    // share_url = share_url + "?mobile=" + mobile + "&source=" + new_source;
                    share_url = global.serverUrl + "/m/mall/index.html?mobile=" + mobile + "&source=" + new_source + '#!/' + lastUrl;
                    // console.log(share_url)
                }
                //新版活动需要的链接参数
                // let chooseUrl = `${result.share_url}?inviterMobile=${inviterMobile}&inviterName=${inviterName + gender}`;
                // if(activityInfo && activityInfo.shareTemplate && !share_url.includes('#')){
                //     return $(_pageId + " #code").qrcode({
                //         render: "canvas", //设置渲染方式，有table和canvas
                //         text: chooseUrl, //扫描二维码后自动跳向该链接
                //         width: 200, //二维码的宽度
                //         height: 200, //二维码的高度
                //         imgWidth: 60,
                //         imgHeight: 60,
                //         src: '../mall/images/icon_app.png'
                //     });
                // }
                //生成短链
                // console.log(share_url)
                service.reqFun101073({ long_url: share_url }, function (res) {
                    if (res.error_no == "0") {
                        if (res.results != undefined && res.results.length > 0) {
                            var short_url = res.results[0].shortUrl;
                            
                            $(_pageId + " #code").qrcode({
                                render: "canvas", //设置渲染方式，有table和canvas
                                text: short_url, //扫描二维码后自动跳向该链接
                                width: 200, //二维码的宽度
                                height: 200, //二维码的高度
                                imgWidth: 60,
                                imgHeight: 60,
                                src: '../mall/images/icon_app.png'
                            });
                        }
                    } else {
                        layerUtils.iAlert(res.error_info);
                    }
                })
                // var params = {};
                // params["url"] = share_url;
                // // service.simplifyURL(params, function (data) {
                // // 	var error_no = data.error_no,
                // // 		error_info = data.error_info;
                // // 	if (error_no == "0") {
                // // 		share_url = data.results[0].shortUrl;
                // // 		var str = share_url + "?mobile=" + mobile + "&type=6";
                // require("../common/jquery.qrcode.min");
                // $(_pageId + " #code").qrcode({
                //     render: "canvas", //设置渲染方式，有table和canvas
                //     text: share_url, //扫描二维码后自动跳向该链接
                //     width: 200, //二维码的宽度
                //     height: 200, //二维码的高度
                //     imgWidth: 60,
                //     imgHeight: 60,
                //     src: '../mall/images/icon_app.png'
                // });
                // 	} else {
                // 		layerUtils.iAlert(error_info);
                // 	}
                // });
            } else {
                layerUtils.iAlert(error_info);
            }
        });

    }


    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        activityInfo = {};
        $(_pageId + " .newMain").hide();
        $(_pageId + " .oldMain").hide();
        $(_pageId + " #score_rule").hide();
        $(_pageId + " #pop_layer").hide();
        sessionStorage.share_template = '';
        sessionStorage.activity_id = '';
        type = "";
    }

    var friendInvitation = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = friendInvitation;
});
