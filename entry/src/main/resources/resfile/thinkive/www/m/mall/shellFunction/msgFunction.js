/**
 * 这里写模块内部的消息处理函数，供原生回调H5
 */
define(function (require, exports, module) {
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var service = require("mobileService");
    var appUtils = require("appUtils");
    var layerUtils = require("layerUtils");
    var oMyMsgFunction = {}; // 暴露给外部的对象
    var msgFunction = require("msgFunction"); // 加载 plugins 目录里面公用的 msgFunction
    var external = require("external");
    var common = require("common");
    var validatorUtil = require("validatorUtil");
    var tools = require("../scripts/common/tools");
    var platform = require("gconfig").platform;
    var ut = require("../scripts/common/userUtil");
    var homePageIndex = require("../scripts/login/userIndexs"); //引入首页js，触发init事件
    /*将本文件里面的函数和 plugins 目录里面公用的 msgFunction 中的函数进行合并操作*/
    msgFunction.conbine(oMyMsgFunction);
    /*将本文件里面的函数和 plugins 目录里面公用的 msgFunction 中的函数进行合并操作，请务必将这句代码放在模块最前面*/

    /**
     * 公用
     * 弹出通讯录，用于选择联系人的信息，选择完毕后回调H5
     * 说明：通讯录插件的调用必须要写在页面UI的事件处理函数中，否则多模块开发原生无法获取到当前激活的模块从而导致无法回调H5
     * @param funcNo 50223
     * @param name String 名称 Y
     * @param phone String 手机号码 Y
     */
    oMyMsgFunction.function50223 = function (paramMap) {
        var selPhoneCallback = window.selPhoneCallback; // 选择电话联系人的回调函数
        if (selPhoneCallback) {
            selPhoneCallback(paramMap);
        }
    }

    /**
     * 公用
     * 日期控件回调H5
     * 说明：日期控件的调用必须要写在页面UI的事件处理函数中，否则多模块开发原生无法获取到当前激活的模块从而导致无法回调H5
     * @param funcNo 50251
     * @param date String 日期 Y
     * @param selector String H5的元素选择器 N
     */
    oMyMsgFunction.function50251 = function (paramMap) {
        paramMap = paramMap || {};
        var selDateCallback = window.selDateCallback; // 选择日期的回调函数
        if (selDateCallback) {
            selDateCallback(paramMap);
        }
    }

    /**
     * 公用
     * 设置系统软件的主题风格
     * @param funcNo 50104
     * @param theme String 主题颜色(见数据字典) Y 0：红色，1：蓝色，2：黑色，3：黄色，9：绿色
     */
    oMyMsgFunction.function50104 = function (paramMap) {
        if (paramMap) {
            var $appCss = $("head>link[href*='" + gconfig.firstLoadCss[0] + "']");
            var skinMap = { 0: "red", 1: "blue", 2: "black", 3: "yellow", 9: "green" };
            var href = gconfig.cssPath + "style_" + skinMap[paramMap.theme] + ".css";
            gconfig.firstLoadCss[0] = href; // 修改当前皮肤对应的 css
            $appCss.attr("href", href);
            var originalOverflow = $("body").css("overflow");
            $("body").css("overflow", "auto"); // 强制浏览器重绘
            $("body").css("overflow", originalOverflow);
        }
    }

    /**
     * 公用
     * 原生的信息提示框回调H5
     * @param funcNo 50111
     * @param flag    String    业务标志    N
     */
    oMyMsgFunction.function50111 = function (paramMap) {
        if (paramMap) {
            layerUtils.iAlert(paramMap.flag);
        }
    }

    /**
     * 公用
     * 通知H5分享后的状态
     * @param funcNo 50232
     * @param shareType    String    分享平台（数据字典）    Y
     * @param flag    String    分享状态（0：失败，1：成功)    Y
     * @param info    String    备注信息    N
     */
    oMyMsgFunction.function50232 = function (paramMap) {
        var _pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = _pageId.replace("_", "/");
        $(".xubox_shade").remove();
        $(".xubox_layer").remove();
        if (!paramMap || paramMap.flag != "1") { //分享失败不做任何处理
            return;
        }
        if (_pageId == "activity_dragonYearAIChat") return;  // 不提示分享成功
        // if (_pageId == "vipBenefits_index") { // 同行好友分享直播间参与活动 只能通过原生监听分享 特殊处理
        //     var vipBenefitsTaskData = appUtils.getSStorageInfo("vipBenefitsTaskData");
        //     if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
        //         service.reqFun108050({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id }, function (data) {
        //             if (data.error_no != '0') {
        //                 layerUtils.iAlert(data.error_info);
        //             }

        //             require.async(gconfig.projPath + "scripts/" + pageCode + ".js", function (page) {
        //                 page.init();
        //             })
        //             appUtils.clearSStorage("vipBenefitsTaskData");
        //         })
        //     }
        // }
        // var activitiesInfo = common.getLocalStorage("activityInfo");
        // var activityInfo_id = common.getLocalStorage("activityInfo_id");
        var activitiesInfo = appUtils.getSStorageInfo("activityInfo");
        if (activitiesInfo) {
            //存在周五活动信息，增加次数
            // service.reqFun108016({ activity_id: activitiesInfo.activity_id }, function (data) {
            //     if (data.error_no != "0") {//失败
            //         layerUtils.iAlert(data.error_info);
            //         return;
            //     }
            //     layerUtils.iMsg(-1, "分享成功");
            //     // $("#" + _pageId + " .share[activitiestype = " + activities_type + "]").removeClass("active share").addClass("rewardActive lottery").html("去抽奖");
            //     if (activitiesInfo.type == "luckDraw") { //抽奖活动分享成功，通知微信端
            //         require.async(gconfig.projPath + "scripts/" + pageCode + ".js", function (page) {
            //             page.init();
            //         })
            //     }
            // })
            return;
        }
        if (sessionStorage.sign == '1') {
            // var pageId = $("body .page[data-display='block']").attr("id");
            // // 当活动是连续签到活动时，调用连续签到活动接口
            // service.reqFun108021({}, (data)=> {
            //     if (data.error_no == 0) {
            //             var results = data.results[0];
            //             // current_state 1:立即签到 2:参与红包雨 3:拆盲盒 4已完成
            //             if(results.current_state == '4' && results.points_amount == '0'){
            //                 $("#" + _pageId + " .sign_succeed .index_info_tishi").html('您已成功签到</br>奖励随后发放');
            //                 $("#" + _pageId + " .sign_succeed .sureBtn span").html('好的')
            //                 $("#" + _pageId + " .sign_succeed").show();
            //             }
            //             if(results.current_state == '4' && results.points_amount != '0'){
            //                 $("#" + _pageId + " .sign_succeed .index_info_tishi").html('恭喜您获得</br><span>' + ( results.points_amount ? results.points_amount : '--' ) + '</span>积分');
            //                 $("#" + _pageId + " .sign_succeed .sureBtn span").html('好的')
            //                 $("#" + _pageId + " .sign_succeed").show();
            //             }
            //             if(results.current_state == '2'){
            //                 $("#" + _pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次参与红包雨机会');
            //                 $("#" + _pageId + " .sign_succeed .sureBtn").attr({'url':results.url,'reward_activity_id':results.reward_activity_id}).find('span').html('去参与');
            //                 $("#" + _pageId + " .sign_succeed").show();
            //             }
            //             if(results.current_state == '3'){
            //                 $("#" + _pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次拆礼盒机会');
            //                 $("#" + _pageId + " .sign_succeed .sureBtn").attr({'url':results.url,'reward_activity_id':results.reward_activity_id}).find('span').html('去参与');
            //                 $("#" + _pageId + " .sign_succeed").show();
            //             }
            //             require("../scripts/vipBenefits/index").reqFunSign(results);
            //
            //     } else {
            //         layerUtils.iLoading(false);
            //         $("#" + _pageId + " .xubox_shade").hide();
            //         layerUtils.iAlert(data.error_info);
            //     }
            // })
            return;
        }
        // if (!activitiesInfo) {
        layerUtils.iMsg(-1, "分享成功");
        return;
        // }
    }

    /**
     * 公用
     * 验证手势密码中忘记密码，修改账号回调H5
     * @param funcNo 50262
     * @param type    String    类型(0：忘记密码，1：修改账号）    N
     */
    oMyMsgFunction.function50262 = function (paramMap) {
        if (paramMap) {
            var type = paramMap.type;
            //补丁
            let pageTopUrlInfo = appUtils.getSStorageInfo("pageTopUrlInfo");
            let financial_prod_type = appUtils.getSStorageInfo("productType");
            if (type == "0" || type == "1") {
                var pageId = $("body .page[data-display='block']").attr("id");
                var pageCode = pageId.replace("_", "/");
                appUtils.clearSStorage(true);// 清除所有缓存
                // var paramlist = {
                //     funcNo: "50042",
                //     key: "account_password",
                //     isEncrypt: "1",
                //     value: ""
                // };
                // external.callMessage(paramlist);
                //补丁
                if (pageTopUrlInfo && pageTopUrlInfo != '') appUtils.getSStorageInfo("pageTopUrlInfo", pageTopUrlInfo);
                if (financial_prod_type && financial_prod_type != '') appUtils.setSStorageInfo("productType", financial_prod_type);
                // 忘记密码
                if (type == "0") {
                    appUtils.pageInit(pageCode, "login/userLogin", {});
                } else {
                    appUtils.pageInit(pageCode, "login/userLogin", {});
                }
            }
        }
    }
    /**
     *
     * 身份证OCR识别回调H5
     * @param funcNo 60303
     * @param shareType    String    分享平台（数据字典）    Y
     * @param name    String    姓名 Y
     * @param birthday    String    生日    Y
     * @param sex    String  性别 Y
     * @param KResulTypeCardName    String    第一或者第二代身份证标识    Y
     * @param address    String    地址    Y
     * @param IDNo    String    身份证号码    Y
     */
    oMyMsgFunction.function60303 = function (paramMap) {
        if (paramMap) {
            if (!paramMap["flag"]) { //识别失败
                layerUtils.iAlert("请上传清晰的身份证");
                return;
            }
            var pageId = $("body .page[data-display='block']").attr("id");
            var pageCode = pageId.replace("_", "/");
            if (paramMap.flag == 1) { //正面
                var IDNo = paramMap.IDNo;
                var name = paramMap.name;
                var sex = paramMap.sex;
                var address = paramMap.address;
                if (sex == "男") {
                    sex = "1"
                } else if (sex == "女") {
                    sex = "0"
                } else {
                    layerUtils.iAlert("身份证识别有误，请重新识别");
                    return;
                }
                if (!address) address = ''
                address = JSON.stringify(address).replace(/\'/g, "").replace(/\"/g, "").replace(/\\/g, "");
                if (address.length < 10) {
                    // layerUtils.iAlert("输入不正确，请输入十个字及以上的地址");
                    sessionStorage.cust_addressISshow = 1
                    $("#" + pageId + " #cust_address").val(address).removeAttr("disabled");
                    $("#" + pageId + " .cust_address").show()
                } else {
                    sessionStorage.cust_addressISshow = null
                    $("#" + pageId + " .cust_address").hide()
                    $("#" + pageId + " #cust_address").attr('disabled', 'disabled')
                    $("#" + pageId + " #cust_address").val(address)
                }
                if (appUtils.getSStorageInfo("ocrType") == "upload") { // 上传流程
                    appUtils.clearSStorage("ocrType");
                    var userInfo = ut.getUserInf();
                    if (IDNo.substr(0, 4) == userInfo.identityNum.substr(0, 4) && IDNo.substr(-4) == userInfo.identityNum.substr(-4)) {
                        $("#" + pageId + " #realName").val(userInfo.name);
                        $("#" + pageId + " #idCard").val(IDNo);
                        $("#" + pageId + " #sex").val(sex);
                        $("#" + pageId + " #idCard").attr('disabled', 'disabled');
                        $("#" + pageId + " #realName").attr('disabled', 'disabled');
                        let noUploadIdCard = appUtils.getSStorageInfo("noUploadIdCard");
                        if(noUploadIdCard == '1') {
                            $("#" + pageId + " .idCardShow").show();
                        }
                        dealImage('data:image/jpeg;base64,' + paramMap.base64, 800, printingZM);
                    } else {
                        layerUtils.iAlert("请上传本人身份证照片");
                    }
                } else { // 绑卡流程
                    if (!address) { // 地址为空，重新识别
                        layerUtils.iAlert("身份证识别有误，请重新识别");
                        return;
                    }
                    $("#" + pageId + " #realName").val(name).removeAttr("disabled");
                    $("#" + pageId + " #idCard").val(IDNo).removeAttr("disabled");
                    $("#" + pageId + " #sex").val(sex);
                    $("#" + pageId + " #tax_certificate").val("仅为中国税收居民");

                    dealImage('data:image/jpeg;base64,' + paramMap.base64, 800, printingZM);
                }

            } else if (paramMap.flag == 0) { // 反面
                var validity = paramMap.validity;
                if (!paramMap || !validity || validity == '' || validity == 'undefind' || (typeof (validity) != 'string')) return layerUtils.iAlert("身份证识别有误，请重新识别");
                var effectEndDate = validity.split("-");
                if (effectEndDate.length != 2 || !validatorUtil.isDate(tools.ftime(effectEndDate[0].replace(/\./g, "")))) {
                    layerUtils.iAlert("身份证识别有误，请重新识别");
                    return;
                }
                if (effectEndDate[1] != "长期" && !validatorUtil.isDate(tools.ftime(effectEndDate[1].replace(/\./g, "")))) {
                    layerUtils.iAlert("身份证识别有误，请重新识别");
                    return;
                }
                if (validatorUtil.isNumeric(effectEndDate[1].replace(/\./g, ""))) {
                    if (new Date(effectEndDate[1].replace(/\./g, "/") + " 23:59:59").getTime() < new Date().getTime()) {
                        layerUtils.iAlert("身份证已过期，请重新上传");
                        return;
                    }
                } else {
                    validity = effectEndDate[0] + "-2359/12/31"
                }
                $("#" + pageId + " #vaild_date").val(validity.split("-")[1].replace(/\./g, "/"));
                dealImage('data:image/jpeg;base64,' + paramMap.base64, 800, printingFM);
            }
        }
    }

    function printingZM(base64) {
        var pageId = $("body .page[data-display='block']").attr("id");
        $("#" + pageId + " #zm_img").attr("src", base64);
        $("#" + pageId + " .zm").attr("isAllow", true);
    }

    function printingFM(base64) {
        var pageId = $("body .page[data-display='block']").attr("id");
        $("#" + pageId + " #fm_img").attr("src", base64);
        $("#" + pageId + " .fm").attr("isAllow", true);
    }
    function dealImage(base64, w, callback) {
        var newImage = new Image();
        var initialQuality = 0.8; // 初始压缩系数
        var maxCompressionAttempts = 20; // 最大压缩尝试次数
        var lastSuccessfulBlobUrl = null; // 用于存储最后一次成功的Blob URL
        layerUtils.iLoading(true);
        newImage.onload = function () {
            var imgWidth = this.width;
            var imgHeight = this.height;
    
            // 计算新的宽高比
            var scale = Math.min(w / Math.max(imgWidth, imgHeight), 1);
            var canvas = document.createElement("canvas");
            canvas.width = imgWidth * scale;
            canvas.height = imgHeight * scale;
    
            var ctx = canvas.getContext("2d");
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(this, 0, 0, canvas.width, canvas.height);
    
            function compressAndCheckSize(q, attempt) {
                if (attempt > maxCompressionAttempts) {
                    if (lastSuccessfulBlobUrl) {
                        layerUtils.iLoading(false);
                        callback(lastSuccessfulBlobUrl); // 返回最后一次成功的Blob URL
                    } else {
                        layerUtils.iLoading(false);
                        callback(null); // 如果没有成功过，则返回null
                    }
                    return;
                }
    
                canvas.toBlob(function(blob) {
                    if (!blob) {
                        cleanup();
                        return;
                    }
    
                    var sizeMb = blob.size / (1024 * 1024); 
                    var sizeKB = blob.size / 1024; 
                    if (sizeMb > 5 && q >= 0.5) {
                        // 如果图片大小超过5MB且当前质量不低于0.5，则减少质量重新压缩
                        compressAndCheckSize(Math.max(q - 0.5, 0.5), attempt + 1);
                    } else if(sizeKB > 200 && q >= 0.5){
                        // 如果图片大小超过200KB且当前质量不低于0.5，则减少质量重新压缩
                        compressAndCheckSize(Math.max(q - 0.1, 0.5), attempt + 1); // 调整步长为0.1以更精细控制
                    }else{
                        // 图片大小合适或者已经降到最低质量，调用回调函数返回结果，转base64返回
                        var reader = new FileReader();
                        reader.onloadend = function() {
                            layerUtils.iLoading(false);
                            callback(reader.result); // 返回Base64编码的字符串
                        };
                        reader.readAsDataURL(blob);
                    }
                }, "image/jpeg", q);
            }
    
            function cleanup() {
                // 清除画布和上下文
                if (canvas) {
                    canvas.width = 0;
                    canvas.height = 0;
                    canvas = null;
                }
                if (ctx) {
                    ctx = null;
                }
                // 清除图像对象
                newImage.src = ''; // 取消对原始Base64字符串的引用
                newImage = null;
                // 释放最后一次成功的Blob URL（如果存在）
                if (lastSuccessfulBlobUrl) {
                    URL.revokeObjectURL(lastSuccessfulBlobUrl);
                    lastSuccessfulBlobUrl = null;
                }
                layerUtils.iLoading(false);
            }
    
            compressAndCheckSize(initialQuality, 1);
    
            // 延迟清理，以确保在回调后不会立即清理资源
            // callback = callback.then ? callback.then(cleanup) : (() => {
            //     callback.apply(this, arguments);
            //     cleanup();
            // });
        };
    
        newImage.onerror = function (error) {
            layerUtils.iLoading(false);
            // cleanup();
        };
    
        newImage.src = base64;
    }
    // function dealImage(base64, w, callback) {
    //     // 清理base64字符串
    //     base64 = JSON.stringify(base64).replace(/\\n/g, "").replace(/\"/g, "");
    
    //     var newImage = new Image();
    //     var quality = 0.8; // 初始压缩系数 0-1 之间
    
    //     newImage.src = base64;
    //     var imgWidth, imgHeight;
    
    //     layerUtils.iLoading(true);
    
    //     newImage.onload = function () {
    //         try {
    //             imgWidth = this.width;
    //             imgHeight = this.height;
    
    //             // 创建 canvas 并获取上下文
    //             var canvas = document.createElement("canvas");
    //             var ctx = canvas.getContext("2d");
    
    //             // 设置 canvas 的尺寸
    //             if (Math.max(imgWidth, imgHeight) > w) {
    //                 // 如果图像较大，则按比例缩小至最大边长不超过 w
    //                 if (imgWidth > imgHeight) {
    //                     canvas.width = w;
    //                     canvas.height = w * imgHeight / imgWidth;
    //                 } else {
    //                     canvas.height = w;
    //                     canvas.width = w * imgWidth / imgHeight;
    //                 }
    //             } else {
    //                 // 否则保持原图大小
    //                 canvas.width = imgWidth;
    //                 canvas.height = imgHeight;
    //             }
    
    //             // 确保 canvas 尺寸适配高分辨率屏幕
    //             const devicePixelRatio = window.devicePixelRatio || 1;
    //             canvas.width *= devicePixelRatio;
    //             canvas.height *= devicePixelRatio;
    //             ctx.scale(devicePixelRatio, devicePixelRatio);
    
    //             // 绘制图像到 canvas 上
    //             // 注意：这里的坐标和尺寸是基于原始图像的尺寸，而不是 canvas 的物理尺寸
    //             ctx.drawImage(this, 0, 0, imgWidth, imgHeight, 0, 0, canvas.width / devicePixelRatio, canvas.height / devicePixelRatio);
    
    //             // 压缩并回调
    //             let finalBase64 = canvas.toDataURL("image/jpeg", quality);
    //             callback(finalBase64);
    
    //         } catch (error) {
    //             console.error('Error processing image:', error);
    //             if (callback) callback('data:image/jpeg;base64,' + base64);
    //         } finally {
    //             layerUtils.iLoading(false);
    //             // 清理不再需要的对象
    //             newImage.src = ''; // 释放图片资源
    //             newImage = null;
    //             canvas = null;
    //             ctx = null;
    //         }
    //     };
    
    //     newImage.onerror = function (error) {
    //         console.error('Error loading image:', error);
    //         if (callback) callback('data:image/jpeg;base64,' + base64);
    //         layerUtils.iLoading(false);
    //     };
    // }
    // function dealImage(base64, w, callback) {
    //     // 清理base64字符串
    //     base64 = JSON.stringify(base64).replace(/\\n/g, "").replace(/\"/g, "");
    
    //     var newImage = new Image();
    //     var initialQuality = 0.8; // 初始压缩系数0-1之间
    //     newImage.src = base64;
    //     var imgWidth, imgHeight;
    //     layerUtils.iLoading(true);
    //     newImage.onload = async function () {
    //         // try {
    //         imgWidth = this.width;
    //         imgHeight = this.height;

    //         var canvas = document.createElement("canvas");
    //         var ctx = canvas.getContext("2d");

    //         if (Math.max(imgWidth, imgHeight) > w) {
    //             if (imgWidth > imgHeight) {
    //                 canvas.width = w;
    //                 canvas.height = w * imgHeight / imgWidth;
    //             } else {
    //                 canvas.height = w;
    //                 canvas.width = w * imgWidth / imgHeight;
    //             }
    //         } else {
    //             canvas.width = imgWidth;
    //             canvas.height = imgHeight;
    //         }

    //         ctx.clearRect(0, 0, canvas.width, canvas.height);
    //         ctx.drawImage(this, 0, 0, canvas.width, canvas.height);

    //         let finalBase64 = await compressToDesiredSize(canvas, initialQuality);
    //         layerUtils.iLoading(false);
    //         callback(finalBase64);
    //         // } catch (error) {
    //         //     layerUtils.iLoading(false);
    //         //     if (callback) callback('data:image/jpeg;base64,' + base64);
    //         // }
    //     };
    
    //     // newImage.onerror = function (error) {
    //     //     layerUtils.iLoading(false);
    //     //     if (callback) callback('data:image/jpeg;base64,' + base64);
    //     // };
    
    //     // 使用 Promise 处理 toBlob 并转换为 Base64
    //     function blobToDataURL(blob) {
    //         return new Promise((resolve, reject) => {
    //             const reader = new FileReader();
    //             reader.onloadend = () => resolve(reader.result);
    //             reader.onerror = reject;
    //             reader.readAsDataURL(blob);
    //         });
    //     }
    
    //     // 异步压缩图像直到满足尺寸要求
    //     async function compressToDesiredSize(canvas, quality) {
    //         let prevSizeKB = Infinity; // 上一次压缩后的大小
    //         let num = 0; // 初始化压缩次数
    
    //         while (num <= 20 && quality >= 0.1) {
    //             // try {
    //             // 尝试使用 toDataURL 进行压缩
    //             let base64Data = canvas.toDataURL("image/jpeg", quality);
    //             let currentSizeKB = base64Data.length / 1024;

    //             // 如果压缩后大小无明显变化或达到目标大小，则停止压缩
    //             if (prevSizeKB - currentSizeKB < 1 || (currentSizeKB <= 200 && currentSizeKB >= 50)) {
    //                 break;
    //             }

    //             prevSizeKB = currentSizeKB;

    //             // 根据文件大小调整质量递减幅度
    //             if (currentSizeKB > 10240) { // 10MB = 10240KB
    //                 quality = Math.max(quality - 0.05, 0.1); // 对于大文件，使用较大的递减幅度
    //             } else {
    //                 quality = Math.max(quality - 0.01, 0.1); // 默认较小的递减幅度
    //             }

    //             num++;
    //             // } catch (error) {
    //             //     break;
    //             // }
    //         }
    
    //         // 如果仍然不满足条件，则使用 toBlob 进行最终压缩
    //         if (prevSizeKB > 200 || prevSizeKB < 50) {
    //             return await handleWithToBlob(canvas, quality);
    //         } else {
    //             return canvas.toDataURL("image/jpeg", quality);
    //         }
    //     }
    
    //     // 使用 toBlob 进行压缩并处理大文件
    //     async function handleWithToBlob(canvas, quality) {
    //         let prevSizeKB = Infinity; // 上一次压缩后的大小
    //         let num = 0; // 初始化压缩次数
    
    //         while (num <= 20 && quality >= 0.1) {
    //             // try {
    //                 // 创建 Blob 并检查大小
    //             let blob = await new Promise((resolve) =>
    //                 canvas.toBlob(resolve, 'image/jpeg', quality)
    //             );

    //             let sizeKB = blob.size / 1024;

    //             // 如果压缩后大小无明显变化或达到目标大小，则停止压缩
    //             if (prevSizeKB - sizeKB < 1 || (sizeKB <= 200 && sizeKB >= 50)) {
    //                 break;
    //             }

    //             prevSizeKB = sizeKB;

    //             // 根据文件大小调整质量递减幅度
    //             if (sizeKB > 10240) { // 10MB = 10240KB
    //                 quality = Math.max(quality - 0.05, 0.1); // 对于大文件，使用较大的递减幅度
    //             } else {
    //                 quality = Math.max(quality - 0.01, 0.1); // 默认较小的递减幅度
    //             }

    //             num++;
    //             // } catch (error) {
    //             //     break;
    //             // }
    //         }
    
    //         // Convert the final Blob back to a Base64 string
    //         const finalBlob = await new Promise((resolve) =>
    //             canvas.toBlob(resolve, 'image/jpeg', quality)
    //         );
    //         layerUtils.iLoading(false);
    //         return await blobToDataURL(finalBlob);
    //     }
    // }
    //校验成功去登录
    function successLogin() {
        var param50043 = {
            funcNo: "50043",
            key: "account_password"
        };
        var firstInstall = common.getLocalStorage("account_password");
        var phoneNum = firstInstall.substring(0, firstInstall.indexOf("_"));
        var trade_pwd = firstInstall.substring(firstInstall.indexOf("_") + 1, firstInstall.length);

        var param50041 = {
            funcNo: "50041",
            key: "deviceTokenKey"
        };
        var data = external.callMessage(param50041);
        var device_token = data.results[0].value;
        //发送登录请求
        var reqParam101018 = {
            "login_acc": phoneNum,
            "login_pwd": trade_pwd,
            "device_token": device_token,
            "type": "0"
        };
        var reqParam9101094 = {
            "registered_mobile": phoneNum,
        }
        let userInfo = ut.getUserInf();
        if(!userInfo.login_pwd_state || userInfo.login_pwd_state == "0" || userInfo.login_pwd_state == ''){
            service.reqFun9101094(reqParam9101094, loginCallback);
        }else{
            service.reqFun101018(reqParam101018, loginCallback);
        }
        function loginCallback(resultVo) {
            var error_no = resultVo.error_no;
            var error_info_login = resultVo.error_info;
            if (error_no == "0") {
                var results = resultVo.results[0];
                //登录成功后吧账号保存到本地
                common.setLocalStorage("mobileWhole", results.mobileWhole);
                appUtils.setSStorageInfo("isAuthenticated", results.mobile + "@|@|@" + new Date().getTime());
                //手势密码，指纹登录成功后 获取是否从营销页进行登录，如果是从营销页进行登录，缓存snowballMarketShow = 1 
                let isMarketLogin = appUtils.getSStorageInfo("isMarketLogin");
                if(isMarketLogin == '1'){
                    common.setLocalStorage("snowballMarketShow",isMarketLogin);
                }else{
                    common.setLocalStorage("snowballMarketShow",'');
                }
                sessionStorage.pop_up_otification = "";
                //记录密码状态
                if(!results.login_pwd_state && results.login_pwd_state != '') results.login_pwd_state = '1';
                ut.saveUserInf(results);
                common.setLocalStorage("userChooseRefresh",'1'); //刷新用户版本
                common.setLocalStorage("userChooseVerson",''); //置空用户选择版本
                //登录后保存用户场景
                common.setLocalStorage("scene_code", results.scene_code);
                //存储渠道代码到本地
                common.setLocalStorage("download_channel_code", results.custLabelCnlCode);
                //同步最新指纹
                let param = {
                    funcNo: "80321",
                };
                external.callMessage(param);
                let flag;
                let custLabelCnlCode = common.getLocalStorage("download_channel_code");
                if (!custLabelCnlCode && custLabelCnlCode != '') {
                    flag = true;
                } else {
                    flag = (custLabelCnlCode == results.custLabelCnlCode) ? true : false;
                    let pageTopUrlInfo = appUtils.getSStorageInfo("pageTopUrlInfo");
                    if (results.custLabelCnlCode == 'yh_jjdx' && (custLabelCnlCode == 'jjdx' || !custLabelCnlCode)) {
                        if (pageTopUrlInfo == 'yuanhui/fundList_1') {
                            flag = false;
                        } else {
                            flag = true;
                        }
                    }
                    if ((results.custLabelCnlCode == 'jjdx' || !results.custLabelCnlCode) && custLabelCnlCode == 'yh_jjdx') {
                        if (pageTopUrlInfo == 'yuanhui/fundList_1') {
                            flag = false;
                        } else {
                            flag = true;
                        }
                    }
                }
                //存储渠道代码到本地
                common.setLocalStorage("isLoginUser", '0');
                common.setLocalStorage("download_channel_code", results.custLabelCnlCode);
                tools.loginQy()
                // 存储是否是异常客户-开户审核中
                appUtils.setSStorageInfo("is_open_acct_excp", results.is_open_acct_excp)
                //银行账户已冻结 （N：正常; C：销户; F：冻结）
                if (results.custStatus == "F") {
                    layerUtils.iMsg(-1, "账户已冻结，请联系" + require("gconfig").global.custServiceTel);
                }
                var pageId = $("body .page[data-display='block']").attr("id");
                $('#' + pageId + " .loginDig").hide();
                var pageCode = pageId.replace("_", "/");
                var skpUrl = appUtils.getSStorageInfo("skipURL");
                appUtils.clearSStorage("skipURL");
                if (pageCode == "yuanhui/userIndexs" || pageCode == "hengjipy/userIndexs") {
                    homePageIndex.init();
                } else {
                    if (skpUrl && skpUrl == pageCode) {
                        return;
                    } else if (skpUrl) {
                        appUtils.pageInit(pageCode, skpUrl);
                    } else {
                        tools.pageToUrl(flag, homePageIndex)
                    }
                    // else if (results.custLabelCnlCode == "yh") {
                    //     appUtils.pageInit(pageCode, "yuanhui/userIndexs");
                    // } else if (results.custLabelCnlCode == "yh_jjdx" || !(results.custLabelCnlCode) || results.custLabelCnlCode == "jjdx") {
                    //     appUtils.pageInit(pageCode, "login/userIndexs");
                    // } else {
                    //     appUtils.pageInit(pageCode, "hengjipy/userIndexs");
                    // }
                }
            } else {
                layerUtils.iAlert(error_info_login);
                // var skipURL = appUtils.getPageParam("skipURL");
                // common.gestureLogin(skipURL);
            }

            //外链打开app的跳转 2018-9-7
            require("../scripts/common/tools").openAPPSkip();
        }
    }

    /**
     * 公用
     * 验证手势密码中忘记密码，修改账号回调H5
     * @param funcNo 50265
     * @param type    String    类型(0：忘记密码，1：修改账号）    N
     * @param flag  状态（0：取消手势，1：设置手势，2：修改手势，3：验证手势）
     * @param result  状态（操作结果（0：成功，-1：失败））
     */
    oMyMsgFunction.function50265 = function (paramMap) {
        if (paramMap) {
            var pageId = $("body .page[data-display='block']").attr("id");
            var pageCode = pageId.replace("_", "/");
            if (pageId.trim().substr(0, 1) != '#') {
                pageId = '#' + pageId
            }
            var flag = paramMap.flag;
            // 设置手势密码
            if (flag == "1") {
                if (paramMap.result == "0") {
                    str = "手势密码<a href='javascript:void(0);' id='gesturePassword' class='btn_modify'>修改</a>" +
                        "<a href='javascript:void(0);' id='gestureoff' class='btn_reset'>关闭</a>";
                    $(pageId + " #gestureManagement").html(str);
                }
            }
            // 取消手势密码
            if (flag == "0") {
                if (paramMap.result == "0") {
                    var str = "手势密码<a href='javascript:void(0);' id='gestureOpen' class='btn_reset'>开启</a>";
                    $(pageId + " #gestureManagement").html(str);
                } else {
                    appUtils.pageInit(pageCode, "login/userLogin", { "login_type": "login_type" });
                }
            }
            // 修改手势密码
            if (flag == "2") {
                if (paramMap.result == "1") {
                    appUtils.pageInit(pageCode, "login/userLogin", { "login_type": "login_type" });
                }
            }
            // 验证手势密码
            if (flag == "3") {
                if (paramMap.result == "0") {
                    successLogin()
                } else {
                    if (paramMap.result == "-1") {
                        //IOS 输错五次取消手势密码
                        let param = {
                            funcNo: "50043",
                            key: "account_password"
                        };
                        $(pageId + " .gesture").hide();
                        $(pageId + " .loginDig_bottom").css('justify-content', 'flex-end');
                        let data = external.callMessage(param);
                        let firstInstall = data.results[0].value;
                        let account = firstInstall.substring(0, firstInstall.indexOf("_"));
                        let setParam = {
                            "funcNo": "50264", //设置手势密码的设置状态
                            "moduleName": "mall",
                            "flag": "3", //flag	String	状态（0：取消手势，1：设置手势，2：修改手势）
                            "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
                            "account": account,
                            "isCanBack": "1",
                            "position": "0",
                            "errorNum": "5",
                            "userImage": ""
                        };
                        external.callMessage(setParam);
                    }
                }
            }
            // 返回
            if (flag == "4") {
                appUtils.setSStorageInfo("pageTopUrlInfo", "");
                appUtils.setSStorageInfo("skipURL", '');
                var pageId = $("body .page[data-display='block']").attr("id");
                var pageCode = pageId.replace("_", "/");
                if (pageCode == "safety/passwordManage") {
                    return;
                } else {
                    var isCanBack = appUtils.getSStorageInfo("isCanBack");
                    appUtils.clearSStorage("isCanBack");
                    if ((pageCode == "login/userIndexs" || pageCode == "yuanhui/userIndexs" || pageCode == "hengjipy/userIndexs") && !isCanBack) {
                        require.async(gconfig.projPath + "scripts/" + pageCode + ".js", function (page) {
                            // appUtils.clearSStorage(true);
                            // page.init();
                            common.setLocalStorage("sceneRefresh",'0');     
                            common.setLocalStorage("userChooseRefresh",'0');
                            setTimeout(async () => {
                                page.destroy();
                                location.reload();
                            }, 0);
                        })
                        return
                    }
                    if ((pageCode == "login/userIndexs" || pageCode == "yuanhui/userIndexs" || pageCode == "hengjipy/userIndexs") && isCanBack == "0") { //首次下载app点击跳过
                        return;
                    } else {
                        appUtils.clearSStorage(true);
                        appUtils.pageInit(pageCode, "login/userIndexs");
                    }
                }
            }
        }
    }
    /**
     * 公用
     * 扫描图片二维码的内容回调给H5页面
     * @param funcNo 50272
     * @param content    String    内容    Y
     */
    oMyMsgFunction.function50272 = function (paramMap) {
        if (paramMap) {
            layerUtils.iAlert("扫描图片二维码的内容回调给H5页面	50272" + JSON.stringify(paramMap));
        }
    }


    /**
     *
     * 上传图片回调
     * @param funcNo 50274
     * @param content    String    内容    Y
     */
    oMyMsgFunction.function50274 = function (paramMap) {
        var pageId = "#" + $("body .page[data-display='block']").attr("id");
        if (paramMap) {
            if (require("gconfig").platform == "2" && paramMap.error_no != "0") {//ios调用相机或相册不成功需要提示
                layerUtils.iAlert(paramMap.error_info);
                return;
            }
            var base64Image = paramMap.base64Image;
            if (!base64Image) return;
            if (base64Image.length * 3 / 4 > 5 * 1000 * 1000) {
                layerUtils.iAlert("您上传的图片大于5M，请重新选择！");
                return;
            }
            if (paramMap.error_no == "0" && paramMap.paramExt.type != null && paramMap.paramExt.type != "undefined") {
                var imgtype = paramMap.paramExt.imgtype;
                var type = paramMap.paramExt.type;
                // var base64Image = paramMap.base64Image;
                if (type == "children_header") { //子女产品上传
                    return service.reqFun199019({
                        base64_list: [base64Image],
                    }, function (data) {
                        if (data.error_no == "0") {
                            // layerUtils.iLoading(false);
                            let img_url = data.results;
                            let header_url = global.oss_url + img_url[0].dir;
                            $(pageId + " .header img").attr("src", header_url)
                            layerUtils.iLoading(false);
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info)
                        }
                    }, { isLastReq: false })
                    return;
                }
                if (type == "bank_face") { //银行开户人脸识别
                    service.reqFun151111({ base64: base64Image }, function (data) {
                        if (data.error_no == "0") {
                            var data = data.results[0];
                            appUtils.setSStorageInfo("bankOpenImg", data);
                            appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "bank/openAccount", { "upFlag": "ok" });
                        } else {
                            appUtils.clearSStorage("bankOpenImg");
                            var error_info = data.error_info;
                            layerUtils.iAlert(error_info);
                        }
                    })
                    return;
                }
                if (type == "friday_activiy") { // 周五活动上传集赞截图
                    service.reqFun199018({ base_data: base64Image, activity_id: paramMap.paramExt.activity_id, cust_no: paramMap.paramExt.cust_no }, function (data) {
                        if (data.error_no == "0") {
                            var data = data.results[0];
                            appUtils.setSStorageInfo("fridayScreenImg", data);
                            if (data.img_url) {
                                layerUtils.iAlert("上传成功");
                            }
                            // appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "bank/openAccount", { "upFlag": "ok" });
                        } else {
                            appUtils.clearSStorage("fridayScreenImg");
                            var error_info = data.error_info;
                            layerUtils.iAlert(error_info);
                        }
                    })
                    return;
                }
                service.reqFun199012({ base_data: base64Image, type: imgtype }, function (data) {
                    if (data.error_no != 0) {
                        if (imgtype == "4" || imgtype == "5" || imgtype == "12") {
                            appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "safety/uploadphoto", {
                                "upFlag": "fail",
                                "imgtype": imgtype
                            });
                        } else if (imgtype == "10" || imgtype == "11") {
                            appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "safety/newcardinfo", {
                                "upFlag": "fail",
                                "imgtype": imgtype
                            });
                        }
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var result = data.results[0];
                    appUtils.setSStorageInfo("attached_url", global.oss_url + result.dir);
                    if (imgtype == "4" || imgtype == "5" || imgtype == "12") {//(uploadphoto)
                        $(type).html("<img src=data:image/gif;base64," + base64Image + " alt='British Blog Directory'/>");
                        appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "safety/uploadphoto", {
                            "upFlag": "ok",
                            "imgtype": imgtype
                        });
                    } else if (imgtype == "10" || imgtype == "11") {//(newcardinfo)
                        $(type).html("<img src=data:image/gif;base64," + base64Image + " alt='British Blog Directory'/>");
                        appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "safety/newcardinfo", {
                            "upFlag": "ok",
                            "imgtype": imgtype
                        });
                    }
                })


            } else {
                if (paramMap.error_no == "0") { //头像上传
                    var base_data = paramMap.base64Image;
                    service.reqFun199010({ base_data: base_data }, function (data) {
                        if (data.error_no != "0") {
                            layerUtils.iLoading(false);
                            return;
                        }
                        appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "account/personMessage", { "head_portrait": "ok" });
                    })
                }
                if (paramMap.error_no == "-1") {
                    appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), "account/personMessage", { "head_portrait": "no" });
                }
            }
        }
    }
    /**
     *
     * 批量上传图片回调
     * @param funcNo 50274
     * @param content    String    内容    Y
     */
    oMyMsgFunction.function50278 = function (paramMap) {
        var pageId = "#" + $("body .page[data-display='block']").attr("id");
        if (!paramMap) return;
        if (paramMap.error_no != "0") {//ios调用相机或相册不成功需要提示
            layerUtils.iAlert(paramMap.error_info);
            return;
        }
        if (paramMap.paramExt != null && paramMap.paramExt != "undefined") {
            var paramExt = paramMap.paramExt;
            var base64Images = paramMap.base64Images;
            if (!paramExt || !base64Images) return; //未选中
            if (!(base64Images instanceof Array)) {
                base64Images = [base64Images];
            }
            if (paramExt.type == "qualifiedInvestor") { //合格投资人资料上传
                var totalImgs = $(pageId + " .uploadImg img").length;
                if (paramExt.name == "firstAdd") { //首次加载按钮
                    $(pageId + " .firstAdd").hide();
                    $(pageId + " .uploadBtn").show();
                }
                var str = "";
                if (base64Images && (parseFloat(totalImgs) + parseFloat(base64Images.length) > 10)) {
                    layerUtils.iAlert("最多允许上传10张图片");
                } else {
                    if (!base64Images || !base64Images.length) return;
                    totalImgs = parseFloat(totalImgs) + parseFloat(base64Images.length);
                    $(pageId + " .uploadBtn").remove();
                    for (var i = 0; i < base64Images.length; i++) {
                        str += '<img src="data:image/gif;base64,' + base64Images[i] + '" alt="">'
                    }
                    str += '<div class="uploadBtn" style="display: block"></div>';
                    $(pageId + " .uploadImg").append(str);
                    if (totalImgs == 10) {
                        $(pageId + " .uploadBtn").hide();
                    }
                }
            }
        }
    }
    /**
     *
     * 消息推送原生主动传值给H5
     * @param funcNo 50117
     *
     */
    oMyMsgFunction.function50117 = function (paramMap) {
        var pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = pageId.replace(/_/g, "/");
        if (paramMap) {
            var pageParam = paramMap.paramExt ? paramMap.paramExt : paramMap;
            var jump_url = pageParam.jump_url;//跳转H5地址
            var jump_type = pageParam.jump_type;//0、APP  1外链  2内链  3 公告 4 咨询
            var parmsjson = pageParam.parmsjson;
            var param = {
                "id": parmsjson
            };
            if (jump_type == "0") { //推送
                appUtils.pageInit(pageCode, "moreDetails/pushSend", { "template_id": parmsjson });//0  template_id
            } else if (jump_type == "1") { //外链
                appUtils.pageInit(pageCode, "guide/advertisement", { "url": jump_url });
            } else if (jump_type == "2") { //内链
                appUtils.pageInit(pageCode, jump_url, param);
            } else if (jump_type == "3" && parmsjson) {//公告
                appUtils.pageInit(pageCode, "moreDetails/noticeDetails", param);
            } else if (jump_type == "4" && parmsjson) {// 咨询
                appUtils.pageInit(pageCode, "moreDetails/consultationDetails", param);
            }
        } else {
            appUtils.pageInit(pageCode, "login/userIndexs", {});
        }
    }

    /**
     * 开户
     * 手机验证码回调H5
     * @param funcNo 60052
     * @param shareType    smsContent    短信验证码    Y
     */
    oMyMsgFunction.function60052 = function (paramMap) {

        if (paramMap) {
            var pageId = $("body .page[data-display='block']").attr("id");
            $("#" + pageId + " #yzmBox input").val(paramMap.smsContent);
        }
    }

    /**
     * 开户
     * 获取手机通信录回调H5
     * @param funcNo 50226
     * @param userName 用户名
     * @param phoneNo  手机号
     * @param userName 户名
     */
    oMyMsgFunction.function50226 = function (paramMap) {

        if (paramMap) {
            var pageId = $("body .page[data-display='block']").attr("id");
            var page = 0;
            if (paramMap.error_no == "0" || paramMap.error_no == "1") {
                var userLists = paramMap.results;
                var str = "";
                for (var i = 0; i < userLists.length; i++) {
                    if ((i + 1) % 10 == 1) {
                        page += 1;
                    }
                    str += "<li class='addressPage_" + page + "  pageAddShow'><strong class='addressName'>" + userLists[i].userName + "</strong><p class='addressPhone'>" + userLists[i].phoneNo + "</p><a href='javascript:void(0)' class='btn_invite'>邀请</a></li>";
                }
                $("#" + pageId + " .contact_friends .friend_list ul").append(str);
                $("#" + pageId + " .contact_friends .friend_list ul li").hide();
                $("#" + pageId + " .contact_friends .friend_list ul li.addressPage_1").show();

                //判断电话号码是否有值
                if (userLists.length == 0) {
                    $("#" + pageId + " .contact_friends #v_container_productList").hide();
                    $("#" + pageId + " .contact_friends .my_date").show();
                } else {
                    if (userLists.length <= 10) {
                        $("#" + pageId + " .visc_pullUp").hide();
                    } else {
                        $("#" + pageId + " .visc_pullUp").show();
                    }
                    //获取电话号码显示状态
                    var pageClass = "addressPage_1";
                    var mobilestr = "";
                    $("#" + pageId + " .contact_friends .friend_list ul li." + pageClass).each(function () {
                        var mobile = $(this).find("p").html();
                        mobile = mobile.replace(/-/g, "");
                        mobile = mobile.replace(/ /g, "");
                        mobile = mobile.replace(/\+86/g, "");
                        mobile = mobile.replace(/\&nbsp;/g, "");
                        mobilestr += mobile + "$";
                    })
                    mobilestr = mobilestr.substring(0, mobilestr.length - 1);
                    var param = {
                        "mobileStr": mobilestr,
                        "user_id": appUtils.getSStorageInfo("userId")
                    }
                    service.getPageMobileSate(param, function (data) {
                        if (data.error_no == "0") {
                            var result = data.results;
                            var i = 0;
                            $("#" + pageId + " .contact_friends .friend_list ul li.addressPage_1").each(function () {
                                var phoneNum = $(this).find("p").html();
                                phoneNum = phoneNum.replace(/-/g, "");
                                phoneNum = phoneNum.replace(/ /g, "");
                                phoneNum = phoneNum.replace(/\+86/g, "");
                                phoneNum = phoneNum.replace(/\&nbsp;/g, "");
                                if (phoneNum == result[i].plat_reg_mobile) {
                                    if (result[i].flag == "1") {
                                        $(this).find("a").html("已注册").addClass("gray");
                                    }
                                }
                                i++;
                            })
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    });
                }
            } else {
                layerUtils.iMsg(-1, paramMap.error_info);
                appUtils.pageInit("inviteFriends/addressBook", "inviteFriends/friendInvitation", { "isShowLayer": "show" });
            }
        }
    }


    /**
     * 发送短信回调H5--我们不做任何处理
     * @param   error_no    String    错误号
     * @param   error_info    String    错误信息
     * @param   results        JSON    通讯录数据
     * @param   paramExt    JSON    参数(json格式数据)
     */
    oMyMsgFunction.function50227 = function (paramMap) {

    }

    oMyMsgFunction.function80003 = function (paramMap) {
        //ios插件冲突,做兼容处理,下次ios强制升级后可删除该处理,只保留80004即可
        if (paramMap && paramMap.activityName) {
            openappFunction(paramMap);
        }
        //下次升级后删除以上内容,保留以下原功能 日期:2018-11-19
        if (paramMap) {
            var data = paramMap;
            var biz_code = data.biz_code;
            if (biz_code == "share") {
                $("#guide_advertisement #pop_layer").show();
                return;
            }
            if (biz_code == "pointsFor") {
                appUtils.pageInit("guide/advertisement", "vipBenefits/index")
                return;
            }
            if (biz_code == "redict") {
                var pageId = $("body .page[data-display='block']").attr("id");
                var pageCode = pageId.replace("_", "/");

                var page_code = data.page_code;

                appUtils.pageInit(pageCode, page_code, data);
            }

            if (biz_code == "login") {
                var skipURL = data.skipURL;
                var url = data.url;
                appUtils.clearSStorage("_loginInPageCode");
                appUtils.clearSStorage("_loginInPageParam");
                appUtils.clearSStorage("_isLoginIn");
                appUtils.clearSStorage();
                appUtils.setSStorageInfo("isLoginTimeOut", "yes");
                appUtils.setSStorageInfo("extra_url", url);
                common.gestureLogin(skipURL, "");
            }
            if (biz_code == "logOut") {
                appUtils.pageInit("wxfund/index", "account/myAccount");
            }
            if (biz_code == "open_prodDetail") {
                var pageId = $("body .page[data-display='block']").attr("id");
                var pageCode = pageId.replace("_", "/").replace(" ", "");
                var param = data.param;
                var detailObj = {
                    "10": "inclusive/moneytaryDetail", //货基
                    "21": "thfund/gatherDetail", //浙商
                    "23": "thfund/gatherDetail",//太平洋滚存
                    "24": "thfund/gatherDetail",//太平洋一次赎回
                    "20": "thfund/gatherDetail",//大集合其他
                    "30": "inclusive/jjThirtyDetail",//定制30天
                    "40": "",//短期理财
                    "51": "inclusive/bondFixDetail",//定开
                    "52": "inclusive/holdsDetail",//持有期产品
                    "50": "thfund/bondDetail",//其他债基
                    "90": "highEnd/productDetail",//普通私募
                    "91": "highEnd/smallgatherDetail",//小集合
                    "92": "highEnd/productDetail",//私募定开
                    "94": "highEnd/productLockDetail",//私募 持有期
                    "95": "highEnd/productPolicyDetail",//私募 政金债
                    "96": "highEnd/productPolicyDetail",//私募 申港小集合
                    "100": "bank/bankDetail",//银行
                }
                if (param.prod_sub_type == "10" && param.fund_code == "000709") {
                    appUtils.setSStorageInfo("jjbFundCode", param.fund_code);
                    appUtils.setSStorageInfo("productInfo_jjs", param);
                } else if (param.prod_sub_type == "100") {
                    appUtils.setSStorageInfo("productInfo", { prod_code: param.fund_code });
                } else {
                    // if (param.prod_sub_type == "90" || param.prod_sub_type == "91" || param.prod_sub_type == "92" || param.prod_sub_type == "94" || param.prod_sub_type == "95") { //私募、小集合、私募定开、持有期
                    //     service.reqFun101037({}, function (datas) {
                    //         if (datas.error_no == 0) {
                    //             var state = datas.results[0].state;
                    //             if (state == 1) { // 合格投资人已确认
                    //                 appUtils.setSStorageInfo("fund_code", param.fund_code);
                    //                 appUtils.setSStorageInfo("productInfo", param);
                    //                 appUtils.pageInit(pageCode, detailObj[param.prod_sub_type]);
                    //             } else { //合格投资人未确认
                    //                 appUtils.setSStorageInfo("fund_code", param.fund_code);
                    //                 appUtils.setSStorageInfo("productInfo", param);
                    //                 appUtils.pageInit(pageCode, "highEnd/fundList");
                    //             }
                    //         } else {
                    //             layerUtils.iAlert(datas.error_info);
                    //         }
                    //     });
                    //     return;
                    // }
                    appUtils.setSStorageInfo("fund_code", param.fund_code);
                    appUtils.setSStorageInfo("productInfo", param);
                }
                appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                appUtils.pageInit(pageCode, detailObj[param.prod_sub_type]);
            }
        }

    }

    /**
     * 外链打开app
     * @param funcNo 80004
    */
    oMyMsgFunction.function80004 = function (paramMap) {
        let str = paramMap.paramExt
        if (str.indexOf('login/userIndexs') != -1) {
            return openappFunction(paramMap)
        }
        str = '?' + str.split("?")[1]
        let data = tools.getQueryVariable(str)
        if (data.goPageIndex == '1') {  //直接跳转首页
            return
        }
        if (data.page_type) {    //用户分享详情页面
            var chancel = null;
            var soft_no = global.soft_no;
            var oVersion = external.callMessage({ funcNo: "50010" });
            oVersion = oVersion.results ? oVersion.results[0] : { versionSn: global.version_code };
            var version = oVersion.versionSn;
            global.version_code = version;
            // if (platform == "1") {
            //     chancel = "1";//手机
            // } else if (platform == "0") {
            //     chancel = "0";//PC
            // } else if (platform == "2") {
            //     chancel = "2";//IOS
            // } else {

            // }
            chancel = platform;
            var queryParam = {
                "versionsn": version,
                "channel": chancel,
                "soft_no": soft_no
            };
            // [{
            //     "1":"自主下载",
            //     "2":"邀请好友-面对面",
            //     "3":"邀请好友-邀请链接",
            //     "4":"邀请好友-图片",
            //     "5":"邀请好友签到",
            //     "6":"同行好友-新手",
            //     "7":"同行好友-月度",
            //     "8":"集团二维码",
            //     "9":"所有活动",
            //     "10":"投教文章",
            //     "11":"banner",
            //     "12":"详情页分享",
            //     "13":"营销页分享",
            // }]
            //调用 102070接口
            service.reqFun102070(queryParam, function (datas) {
                if (datas.error_no == 0) {
                    if (datas.results.length == 0) {
                        let arr = [
                            {
                                "3": "", //banner详情
                                "2": "template/publicMarketing", //公募营销页
                                "1": "template/publicOfferingDetail", //公募产品详情
                                "4": "moreDetails/noticeDetails",  //文章详情
                                "6": "liveBroadcast/newsDetatil",  //消息详情
                                "7": "template/decentralizedPurchasing",  //系列产品
                                "8": "combProduct/combProdDetail",  //投顾产品详情
                                "9": "combProduct/combProdMarketing"  //投顾营销详情
                            }
                        ]
                        let page_type = data.page_type;
                        if (page_type == '3') {
                            if (data.img && data.img != 'undefined' && data.img != 'null') {  //投资
                                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                                    "group_id": data.group_id,
                                    "banner_id": data.busi_id,
                                    "url": global.serverUrl + '/m/mall/index.html#!/activity/prodDetailTemp.html?fundCode=&prodSubType=&img=' + data.img
                                });
                            } else { //大转盘
                                appUtils.pageInit("login/userIndexs", "activity/fundLuckdrawnew", {
                                    "activity_id": data.activity_id,
                                    "group_id": data.group_id,
                                    "banner_id": data.busi_id,
                                    "channel": 'jjcf_app',
                                    "type": "luckDraw"
                                });
                            }
                        } else {
                            let productInfo = appUtils.getSStorageInfo("productInfo");
                            if (productInfo) {
                                productInfo.fund_code = data.busi_id
                            }
                            appUtils.setSStorageInfo("productInfo", productInfo);
                            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                            sessionStorage.vip_buttonShow = true;
                            appUtils.pageInit('login/userIndex', arr[0][page_type], {
                                busi_id: data.busi_id //传递唯一标识
                            });
                        }
                    }
                }
            })
        }
    }

    function openappFunction(paramMap) {
        //参数校验,不符合规定格式,则不做处理
        if (!paramMap) {
            return;
        }
        if (!paramMap.paramExt) {
            return;
        }
        var paramExt = JSON.parse(paramMap.paramExt); //获取外链传递信息

        if (paramExt.openType == "1") { //晋金所引流注册财富
            if (ut.getUserInf()) {
                return;
            }
            var pageId = $("body .page[data-display='block']").attr("id");
            var pageCode = pageId.replace("_", "/");
            appUtils.pageInit(pageCode, "login/userRegistered", { mobile: paramExt.mobile });
            return;
        }
        if (paramExt.page) {
            appUtils.pageInit(pageCode, paramExt.page, paramExt);
        }
    }

    /**
     *模块切换初始化?
     * 静默更新后刷新页面
     */
    oMyMsgFunction.function50113 = function (param) {
        if (!param.toPage) {//是静默更新
            var pageId = $("body .page[data-display='block']").attr("id");
            var pageCode = pageId.split("_").join("/");
            var pageJs = require("../scripts/" + pageCode);
            pageJs.destroy();
            location.reload();
        }

    };
    //监听折叠屏手机发生折叠/展开，原生通知H5
    oMyMsgFunction.function80325 = function (param) {
        let pageId = $("body .page[data-display='block']").attr("id");
        let pageCode = pageId.split("_").join("/");
        if(!pageId || !pageCode) return;
        let pageJs = require("../scripts/" + pageCode);
        //设置页面过滤
        if(pageId == 'combProduct_combAssetDetails' || pageId == 'combProduct_combHoldDistribution' || pageId == 'combProduct_combProdDetail' || pageId == 'combProduct_combProdMarketing' || pageId == 'fundSupermarket_fundsMarketing' || pageId == 'scene_combProdDetail' || pageId == 'scene_gasStation' || pageId == 'template_decentralizedPurchasing' || pageId == 'template_heighEndProduct' || pageId == 'template_marketing' || pageId == 'template_publicMarketing' || pageId == 'template_publicOfferingDetail' || pageId == 'template_seriesChildrenMarketing'){
            pageJs.destroy();
            location.reload();
        } 
    };
    /**
     *
     * 银行卡OCR识别回调H5
     * @param funcNo 60305
     * @param shareType    String    分享平台（数据字典）    Y
     * @param bankName    String    银行名字 Y
     * @param cardNumber    String    银行卡账号    Y
     * @param expireDate    String  过期时间Y
     * @param recognizeTime    String  授权时间    Y
     * @param cardHolder    String    持卡人姓名    Y
     */
    oMyMsgFunction.function60305 = function (paramMap) {
        if (paramMap) {
            if(paramMap.errorNo == '-1') return;
            if(!paramMap.paramExt) paramMap.paramExt = {};
            var pageId = $("body .page[data-display='block']").attr("id");
            var pageCode = pageId.replace("_", "/");
            var cardNumber = paramMap.cardNumber;
            var base64Image = paramMap.base64;
            var bankName = paramMap.bankName;
            cardNumber = cardNumber.replaceAll(" ", "");
            if (cardNumber) {
                if (paramMap.paramExt.isSport == "No") {//不做cardbin查询
                    $("#" + pageId + " #bankCard").val(cardNumber);
                } else {

                    var param = {
                        "bin_id": cardNumber
                    };
                    service.BankByCard(param, function (data) {
                        var error_info = data.error_info,
                            error_no = data.error_no;
                        if (error_no == "0") {
                            if (paramMap.paramExt.type != null && paramMap.paramExt.type != "undefined") {
                                $(type).html("<img src=data:image/gif;base64," + base64Image + " alt='British Blog Directory' height='127'/>");
                                var imgtype = paramMap.paramExt.imgtype;
                                var type = paramMap.paramExt.type;
                            }
                            if (data.results.length > 0) {
                                $("#" + pageId + " #bankCard").val(cardNumber);
                                $("#" + pageId + " .place").show();
                                var result = data.results[0];
                                var bank_name = result.bank_name;
                                var bank_code = result.bank_code;
                                var single_limit = result.single_limit;
                                var day_limit = result.day_limit;
                                if (parseFloat(day_limit) > 0) {
                                    $("#" + pageId + " .place").show();
                                    $("#" + pageId + " #drxe").html(day_limit + "元");
                                } else if (parseFloat(day_limit) == 0) {
                                    $("#" + pageId + " .place").hide();
                                } else {
                                    $("#" + pageId + " .place").show();
                                    $("#" + pageId + " #drxe").html("不限");
                                }
                                if (parseFloat(single_limit) > 0) {
                                    $("#" + pageId + " .place").show();
                                    $("#" + pageId + " #oneMoney").html(single_limit + "元");
                                } else if (parseFloat(single_limit) == 0) {
                                    $("#" + pageId + " .place").hide();
                                } else {
                                    $("#" + pageId + " .place").show();
                                    $("#" + pageId + " #oneMoney").html("不限");
                                }
                                //识别出来填写银行卡编号 和银行名称
                                $("#" + pageId + " #bankname").val(bank_name);
                                $("#" + pageId + " #bankname").html(bank_name);
                                $("#" + pageId + " #bankname").attr("bank_code", bank_code).attr("payorg_id", result.payorg_id).attr("pay_type", result.pay_type);
                            } else {
                                $("#" + pageId + " #bankname").html("");
                                $("#" + pageId + " #bankname").val("");
                                $("#" + pageId + " .place").hide();
                                $("#" + pageId + " #bankname").attr("bank_code", "").removeAttr("payorg_id").removeAttr("pay_type");
                                layerUtils.iMsg(-1, "不支持的银行卡");
                                return;
                            }
                        } else {
                            // 识别失败时候 去掉银行信息
                            $("#" + pageId + " #bankCard").val("");
                            $("#" + pageId + " #oneMoney").html("不限");
                            $("#" + pageId + " #drxe").html("不限");
                            $("#" + pageId + " #bankname").html("");
                            $("#" + pageId + " #bankname").attr("bank_code", "").removeAttr("payorg_id").removeAttr("pay_type");
                            $("#" + pageId + " .place").hide();
                            layerUtils.iAlert(error_info);
                        }
                    });

                }

            }
        }
    }


    /**
     * 工行网银回调
     */
    oMyMsgFunction.function80302 = function (param) {


        //    	layerUtils.iAlert("80302");
        //验证码重置
        var _pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = _pageId.replace("_", "/");

        //		layerUtils.iAlert("param.errorCode"+param.errorCode+"param.error_info"+param.error_info);
        var $yzm = $("#" + _pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.removeAttr("class");
        $yzm.html("获取验证码");
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk'   style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $("#" + _pageId + " #talkCode").html($dd);
        $("#" + _pageId + " #talkCode").hide();
        $("#" + _pageId + " #weihao").hide();
        $("#" + _pageId + " #yzm").val("");

        //按钮重置
        $("#" + _pageId + " #nextStep").html("确定");
        $("#" + _pageId + " #nextBtn").css("pointer-events", "auto");
        $("#" + _pageId + " #rechargeInfo").empty();

        var errorCode = param.errorCode;
        if (errorCode == "0") {
            //支付成功
            var pageParam = {
                "orderNo": param.orderNo,
                "transStatus": 0

            };
            pageParam["to_recharge"] = appUtils.getPageParam("to_recharge");
            appUtils.setSStorageInfo("cz", pageParam);
            appUtils.pageInit(pageCode, "account2/balance_resultCZJG", pageParam);
        } else {
            layerUtils.iMsg(-1, param.error_info);
        }

    };
    /**
     * 视频见证回调
     * @param videoFlag 返回标志(0：成功，1:失败，2:驳回)
     */
    oMyMsgFunction.function60051 = function (paramMap) {
        var _pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = _pageId.replace("_", "/");
        require.async(gconfig.projPath + "scripts/" + pageCode + ".js", function (page) {
            page.videoWitnessCallback(paramMap)
        })
    }
    /**
     * 原生关闭协议回调,解决ios关闭协议回调bug
     */
    oMyMsgFunction.function50241 = function (paramMap) {
    }
    /*
    * 通知h5 手机模式发生改变（晋金财富）
    * 1:传统模式，2:暗黑模式
    * */
    oMyMsgFunction.function50126 = function (paramMap) {
        var _pageId = "#" + $("body .page[data-display='block']").attr("id");
        if (paramMap) {
            var theme = paramMap.theme;
            var param = {
                "moduleName": "mall"
            }
            if (theme == "theme2") { //暗黑模式
                param.color = "#000000"; //背景色
                param.style = "1"; //字体颜色 0黑色 1 白色
                $(_pageId).addClass("blackMode");
            } else { //theme1 浅色模式
                param.color = "#ffffff";
                param.style = "0";
                $(_pageId).removeClass("blackMode");
            }
            param["funcNo"] = "50119";
            require("external").callMessage(param);
        }
    }
    /*
    * 播放完视频回调H5
    * */
    oMyMsgFunction.function50281 = function (paramMap) {
        if (paramMap) {
            var result = paramMap.result;
        }
    }

    /**
     * 调用建行app支付回调H5
     */
    oMyMsgFunction.function80304 = function (paramMap) {
        if (paramMap) {
            if (paramMap.error_no == 0) {
                var pageId = $("body .page[data-display='block']").attr("id");
                var pageCode = pageId.replace("_", "/");
                setTimeout(function () {
                    $("#" + pageId + " .confirm_layer").show();
                }, 3000);
            } else if (paramMap.error_no == -1) {
                if (require("gconfig").platform == "1") {//安卓提示
                    layerUtils.iMsg(-1, paramMap.error_info);
                }

            } else {
                layerUtils.iMsg(-1, paramMap.error_info);
            }
        }
    };
    /**
     * IOS截屏后操作
     */
    oMyMsgFunction.function80306 = function () {
        var pageId = $("body .page[data-display='block']").attr("id");
        if (pageId == 'login_userLogin') {
            layerUtils.iMsg(-1, '截屏内容请妥善保管。');
        }
    };

    /**
     * APP从后台切到前台调用
     */
    oMyMsgFunction.function80305 = function () {
        var pageId = $("body .page[data-display='block']").attr("id");
        tools.recordEventData('3','backStage','后台切前台',{is_switch:'1'});
        //登录时
        if (pageId == 'login_userLogin') {
            $("#" + pageId + " #yzmBox #pwd").val('');
            $("#" + pageId + " #yzmBox .cursor-bink").css("left", '0.5rem');
            $("#" + pageId + " #yzmBox .placeholderPsd").css('color', '#aaa');
            $("#" + pageId + " #yzmBox .placeholderPsd").html('请输入密码');
        }
    };
    /**
     * 设置七鱼用户信息
     */
    oMyMsgFunction.function80308 = function (paramMap) {
        paramMap = {
            real_name: '',
            avatar: '',
            mobile_phone: '',
            email: '',
            authenticationauthentication: '',
            bankcard: '',
            lastorder: ''
        }
        /**
         * [{\
                "key\":\"real_name\", \"value\":\"晓彤\"},"
                "{\"key\":\"avatar\", \"value\":\"http://pic33.nipic.com/********/3420027_192919547000_2.jpg\"},"
                "{\"key\":\"mobile_phone\", \"value\":\"***********\", \"hidden\":false},"
                "{\"key\":\"email\", \"value\":\"<EMAIL>\"},"
                "{\"index\":0, \"key\":\"authentication\", \"label\":\"实名认证\", \"value\":\"已认证\"},"
                "{\"index\":1, \"key\":\"bankcard\", \"label\":\"绑定银行卡\", \"value\":\"622202******0111015\"},"
                "{\"index\":2, \"key\":\"lastorder\", \"label\":\"最近订单\", \"value\":\"七鱼银票(**********)\"
            }]
         */
    };
    /**
     * 设置七鱼用户信息回调
     */
    oMyMsgFunction.function80310 = function (paramMap) {
        // console.log(paramMap)
        //error_no 0 成功 1 失败
        //error_info 原因
    };
    /**
     *  注销七鱼用户信息 80311 
     */
    oMyMsgFunction.function80311 = function (paramMap) {
        // console.log(paramMap)
        //error_no 0 成功 1 失败
        //error_info 原因
    };
    /**
     *  注销七鱼用户信息回调 80312
     */
    oMyMsgFunction.function80312 = function (paramMap) {
        // console.log(paramMap)
        //error_no 0 成功 1 失败
        //error_info 原因
    };
    /**
     * 关闭七鱼客服页面 80314
     */
    oMyMsgFunction.function80314 = function (paramMap) {
        var pageId = $("body .page[data-display='block']").attr("id");
        $("#" + pageId + " #kefu img").removeClass('active')
        appUtils.setSStorageInfo("isMessage", 0);
        if (paramMap.type == 1 && paramMap.param.url) {   //点击了商品卡片
            external.callMessage({
                funcNo: "50504",
                notiName: "notiH5ToVerify"
            });
            let url = paramMap.param.url

            if (platform == 1 && url.indexOf("http://") != -1) {
                url = url.split("http://")[1]
            } else if (platform == 1 && url.indexOf("https://") != -1) {
                url = url.split("https://")[1]
            }
            let pageUrl = url.split('?')[0] //内链跳转页面地址
            let data = tools.getQueryVariable('?' + url.split('?')[1])//获取拼接参数
            appUtils.setSStorageInfo("productInfo", data);
            appUtils.setSStorageInfo("financial_prod_type", data.financial_prod_type);
            let routerList = ["login/userIndexs"]
            if (data.prod_sub_type2 == "100") {   //点击私募
                if (!common.loginInter()) return;
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.pageInit("login/userIndexs", pageUrl);
            } else if (data.prod_sub_type2 == '200') { //点击公募
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.pageInit("login/userIndexs", pageUrl);
            }
        }
    };
    /**
     * 收到未读消息通知h5 80316
     */
    oMyMsgFunction.function80316 = function (paramMap) {
        var pageId = $("body .page[data-display='block']").attr("id");
        appUtils.setSStorageInfo("isMessage", paramMap.allUnreadCount * 1);
        //首页消息闪烁
        if (paramMap.allUnreadCount * 1 > 0) {
            $("#" + pageId + " #kefu img").addClass('active')
        } else {
            $("#" + pageId + " #kefu img").removeClass('active')
        }
    };
    /**
     * 约定的消息号，接受原生弹窗确定按钮 80317
     */
    oMyMsgFunction.function80317 = function (paramMap) {
        //点击弹窗确定后的通知
        let data50502 = {
            funcNo: "50502",
            notiName: "notiAppVerifyResult",
            notiUserInfo: {
                errorNo: "",
                errorInfo: ""
            },
            notiObject: {
                errorNo: "",
                errorInfo: ""
            }
        }
        service.reqFun101038({}, function (datas) {
            if (datas.error_no == 0) {
                data50502.notiUserInfo.errorNo = '0';
                data50502.notiObject.errorNo = '0';
                external.callMessage(data50502);
                appUtils.setSStorageInfo("isAuthentication", 1);
            } else {
                data50502.notiUserInfo.errorNo = '2';
                data50502.notiObject.errorNo = '2';
                data50502.notiUserInfo.errorInfo = datas.error_info;
                external.callMessage(data50502);
                appUtils.setSStorageInfo("isAuthentication", 2);
                layerUtils.iAlert(datas.error_info);
            }
        });
    };
    /**
     * 指纹的回调 80320
     */
    oMyMsgFunction.function80320 = function (paramMap) {
        /**
         * @fingerprintPwd_flag 是否开始指纹验证
         * @setFingerprintInfo 设置指纹的次数对象 time设置时当前时间 num 设置当日次数
         */
        let errorInfo = paramMap.errorInfo;
        let status = paramMap.errorNo;
        let pageId = $("body .page[data-display='block']").attr("id");
        let timeChange = new Date().getTime();
        let date = tools.setTimeData(timeChange, 'YY-MM-DD');   //当前日期 年月日
        if (status == '-3') {
            common.setLocalStorage("fingerprintPwd_flag", '0');
            if (pageId == 'login_userIndexs') {
                return layerUtils.iAlert("手机未设置系统指纹，请先去手机设置中添加指纹。", () => { }, () => {
                    tools.loginOutQy();
                    appUtils.clearSStorage(true);
                    appUtils.pageInit('login/userIndexs', "login/userLogin", {});
                }, '', '确定');
            } else {
                return layerUtils.iAlert('手机未设置系统指纹，请先去手机设置中添加指纹。');
            }
        }
        if (status == '-2') {
            return layerUtils.iAlert('请先去设置中录制屏幕保护。');
        }
        if (status == '-1') {
            return layerUtils.iAlert('设备不支持指纹');
        }
        if (status == '-5') {
            // common.setLocalStorage("fingerprintPwd_flag",'0');
            // return layerUtils.iAlert(errorInfo + '，请重新登录后再次尝试。');
            layerUtils.iAlert("指纹发生变化后，请重新登录后再次尝试。", () => { }, () => {
                tools.loginOutQy();
                appUtils.clearSStorage(true);
                appUtils.pageInit('login/userIndexs', "login/userLogin", {});
            }, '', '确定');
        }
        if (status == '-7') { //用户取消登录回调
            // $('#' + pageId + " .loginDig").hide();
        }
        if (status == '-6') {
            sessionStorage.isOpenFingerprint = false;
        }
        if (status == '-4') {
            if (pageId == 'safety_fingerprintPwd') {  //安卓中心操作是否开启指纹登录
                let setFingerprintInfo = common.getLocalStorage("setFingerprintInfo");
                if (!setFingerprintInfo || setFingerprintInfo == '') {
                    common.setLocalStorage("setFingerprintInfo", date + ',' + '1');
                } else {
                    let newArr = setFingerprintInfo.split(','); //第一个参数未日期 第二个参数为次数
                    let num = newArr[1] * 1
                    if (date != newArr[0]) { //不在当日
                        common.setLocalStorage("setFingerprintInfo", date + ',' + '1');
                    } else {  //是当日
                        if (num < 5) {   //使用验证次数小于5次
                            common.setLocalStorage("setFingerprintInfo", date + ',' + (num + 1));
                        } else {  //验证次数超过五次
                            //关闭指纹识别功能
                        }
                    }
                }
            } else {
                let loginFingerprint = common.getLocalStorage("loginFingerprint") ? common.getLocalStorage("loginFingerprint") : 0;
                if (loginFingerprint >= 5) {
                    //取消指纹密码登录
                } else {
                    loginFingerprint = loginFingerprint * 1 + 1;
                    common.setLocalStorage("loginFingerprint", loginFingerprint);
                }
            }

        }
        if (status == '0') {  //指纹验证成功
            //根据当前页面地址，判断用户所属操作
            if (pageId == 'safety_fingerprintPwd') {  //安卓中心操作是否开启指纹登录
                common.setLocalStorage("fingerprintPwd_flag", '1');
                $('#' + pageId + " .switch").removeClass("switch_uncheck");
                $('#' + pageId + " .switch").addClass("switch_check");

            } else {
                //首页存储账号密码直接登录
                successLogin()
            }
        }
    };
    /**
     * 切换代理模式的回调
     */
    oMyMsgFunction.function80322 = function () {
        layerUtils.iConfirm("当前网络不稳定，建议您重新登录。", function () {
            //重新登录
            service.reqFun1100004({}, function (data) {
                if (data.error_no == "0") {
                    tools.loginOutQy();
                    appUtils.clearSStorage(true);
                    let arr = ["login/userIndexs"]
                    appUtils.setSStorageInfo("routerList", arr)
                    appUtils.pageInit('login/userIndexs', "login/userLogin", {});
                } else {
                    layerUtils.iMsg(-1, data.error_info);
                }
            });
        }, function () {
            return;
        }, "重新登录", "继续操作");
    };
    module.exports = oMyMsgFunction;
});

