// 公告详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#moreDetails_noticeDetails ";
    var tools = require("../common/tools");
    var _pageCode = "moreDetails/noticeDetails";
    var id;

    function init() {
        id = appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : appUtils.getPageParam("id") ? appUtils.getPageParam("id") : sessionStorage.articleId;// 文章id
        // let busi_id = appUtils.getSStorageInfo("busi_id"); //存储 详情唯一标识ID
        //获取页面分享状态
        getPageShareStatus()
        sessionStorage.articleId = id
        var param = {
            "id": id
        };

        service.reqFun102051(param, function (data) {
            if (data.error_no == "0") {
                if (data.results != undefined && data.results.length > 0) {
                    var createDate = data.results[0].createDate; // 公告发布时间
                    var title = data.results[0].title;// 公告标题
                    var htmlContent = data.results[0].htmlContent;// 公告内容
                    $(_pageId + " #title").html(title);
                    $(_pageId + " #fbTime").html(tools.ftime(createDate));
                    $(_pageId + " #content").html(htmlContent);
                    //wailian
                    // appUtils.bindEvent($(_pageId + " #content a"), function () {
                    //     appUtils.pageInit(_pageCode, "guide/advertisement", {
                    //         "url": $(this).attr("value"),
                    //         "article_id": id,
                    //     });
                    // });
                }

            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        });

    }
    function getPageShareStatus() {
        let data = {
            busi_id: id,
            page_type: '4',
            pageId: _pageId,
            pageCode: _pageCode
        }
        tools.isShowShare(data, '1')
    }
    // 绑定事件
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 内外链
        appUtils.preBindEvent($(_pageId + " #content"), ".chain", function () {
            let url
            if ($(this).attr('src') && $(this).attr('src') != '') {
                url = $(this).attr('src')
            } else {
                url = $(this).text()
            }
            if (!url || url == '') return
            let newStr = url.indexOf("http");
            let newStrs = url.indexOf("https");

            if (newStr == 0 || newStrs == 0) {
                appUtils.pageInit("moreDetails/noticeDetails", "guide/advertisement", {
                    "url": url,
                    "name": '',
                });
            } else {
                appUtils.pageInit("moreDetails/noticeDetails", url, {});
            }
        }, 'click');
        // 小程序
        appUtils.preBindEvent($(_pageId + " #content"), ".applet", function () {
            let url
            if ($(this).attr('src') && $(this).attr('src') != '') {
                url = $(this).attr('src')
            } else {
                url = $(this).text()
            }
            if (!url || url == '') return
            return tools.jump_applet(url);
        }, 'click');
    }

    function destroy() {
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #content").html("");
        $(_pageId + " #fbTime").html("");
        $(_pageId + " #title").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var noticeDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = noticeDetails;
});
