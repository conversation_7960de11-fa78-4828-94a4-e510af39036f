// 手机注册
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#drainage_userRegisteredWx";
    var _pageCode = "drainage/userRegisteredWx";
    var external = require("external");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var timer = null;//计时器
    var i = 120;//倒计时长
    var isAgree = true;
    var Millisecond;
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var yqrPhoneNum;//邀请人手机号
    var labelId;//渠道ID
    function init() {
        initYanZma();
        setImgCode();
        $(_pageId + " #phoneShow").hide();
        common.systemKeybord(); // 解禁系统键盘
        yqrPhoneNum = appUtils.getSStorageInfo("invitationMobile");
        labelId = appUtils.getSStorageInfo("labelId");// 获取渠道ID
        window.clearInterval(timer);
        $(_pageId + " #input_radio2").attr("checked", "checked");
        // tools.getPdf("5");
        // reqFun102016('5')
        if (appUtils.getSStorageInfo("mobile")) {
            $(_pageId + " #phoneNum").val(appUtils.getSStorageInfo("mobile"));
        }else {
            appUtils.pageInit(_pageCode, "drainage/userInvitationWx");
            return;
        }
        if(!yqrPhoneNum){
            $(_pageId + " .yqrPhoneNumClass").show();
        }
    }

    //绑定事件
    function bindPageEvent() {
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "drainage_userRegisteredWx";
            tools.saveAlbum(_pageCode,param)
        });

        appUtils.bindEvent($(_pageId + " input"), function () {
            var localHeight = $(_pageId + " section").height();//获取可视宽度
            var keyboardHeight = localHeight - $(_pageId + " section").height();//获取键盘的高度
            var keyboardY = localHeight - keyboardHeight;
            var addBottom = (parseInt($(this).position().top) + parseInt($(this).height()));//文本域的底部
            var offset = addBottom - keyboardY;//计算上滑的距离
            $(_pageId + " section").scrollTop(offset);
        }, "focus");
        /* 切换图形验证码 */
        appUtils.bindEvent($(_pageId + " #getCode"), function () {
            setImgCode();
        });
        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            //验证手机号input
            var mobile = $(_pageId + " #phoneNum").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            //验证登录密码input
            var pwd_one = $(_pageId + " #pwd1").val();
            var pwd_two = $(_pageId + " #pwd2").val();
            if (!(checkInput(pwd_one, pwd_two))) {
                return;
            }
            //验证图形验证码input
            var imgCode = $(_pageId + " #tuxingCode").val();
            if (imgCode == "") {
                layerUtils.iMsg(-1, "请输入图形验证码");
                return;
            }
            //验证获取验证码按钮可用性
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            window.clearInterval(timer);
            sendPhoneCode(mobile, imgCode);
        });

        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });

        //去首页
        appUtils.bindEvent($(_pageId + " #toLogin"), function () {
            pageBack();
        });


        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #registered"), function () {
            //检查手机号
            // var phone = $.trim($(_pageId + " #phoneNum").val());
            // if (validatorUtil.isEmpty(phone)) {
            //     layerUtils.iMsg(-1, "请输入手机号码");
            //     return;
            // }
            // if (!validatorUtil.isMobile(phone)) {
            //     layerUtils.iMsg(-1, "请确定您输入的手机号是否正确");
            //     return;
            // }
            //检查登录密码
            var pwd1 = $(_pageId + " #pwd1").val();
            var pwd2 = $(_pageId + " #pwd2").val();
            if (!checkInput(pwd1, pwd2)) {
                return;
            }
            // 检查邀请人手机号
            // var yqrPhoneNumClass = $.trim($(_pageId + " #yqrPhoneNum").val());
            // if (validatorUtil.isNotEmpty(yqrPhoneNumClass) && !validatorUtil.isMobile(yqrPhoneNumClass)) {
            //     layerUtils.iMsg(-1, "请确定您输入的邀请人手机号是否正确");
            //     return;
            // }else if(!yqrPhoneNum){
            //     yqrPhoneNum = yqrPhoneNumClass;
            // }
            //验证图形验证码input
            // var imgCode = $(_pageId + " #tuxingCode").val();
            // if (imgCode == "") {
            //     layerUtils.iMsg(-1, "请输入图形验证码");
            //     return;
            // }
            //检查协议勾选
            // var isChecked = $(_pageId + " #input_radio2").attr("checked");
            // if (!isChecked) {
            //     layerUtils.iMsg(-1, "请阅读协议并同意签署");
            //     return;
            // }
            //检查验证码获取按钮
            // var isSend = $(_pageId + " #getYzm").attr("data-state");
            // if (isSend == "true") {
            //     layerUtils.iMsg(-1, "您还未获取验证码");
            //     return;
            // }
            // // //检查验证码input
            // var mobile_vf_code = $.trim($(_pageId + " #verificationCode").val());//验证码
            // if (validatorUtil.isEmpty(mobile_vf_code)) {
            //     layerUtils.iMsg(-1, "请输入手机验证码");
            //     return;
            // }
            // if (!validatorUtil.isNumeric(mobile_vf_code)) {
            //     layerUtils.iMsg(-1, "验证码格式为纯数字");
            //     return;
            // }

            let phone = appUtils.getSStorageInfo("mobile");
            let code = appUtils.getSStorageInfo("code");
            //注册
            var param = {
                registered_mobile: phone,
                login_pwd: pwd1,
                sms_mobile: phone,
                sms_code: code,
                labelId:labelId,
            };
            //验证手机验证码
            registerAccount(param);

        });

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            //验证图形验证码input
            var imgCode = $(_pageId + " #tuxingCode").val();
            if (imgCode == "") {
                layerUtils.iMsg(-1, "请输入图形验证码");
                return;
            }
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

    }


    //获取语音验证码
    function getCodeOFTalk() {
        var mobile = $(_pageId + " #phoneNum").val();
        var imgCode = $(_pageId + " #tuxingCode").val();//图形验证码
        if (mobile && imgCode) {
            var param = {
                "mobile_phone": mobile,
                "type": common.sms_type.register,
                "ticket": imgCode,
                "send_type": "1",
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(_pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        } else {
            if (mobile == "") {
                layerUtils.iMsg(-1, "请输入手机号");
            }
            if (imgCode == "") {
                layerUtils.iMsg(-1, "请输入图像验证码");
            }

        }
    }

    //初始化语音验证码
    function initYanZma() {
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
    }


    /**
     * 发送手机验码
     * */
    function sendPhoneCode(phoneNum, imgCode) {

        var param = {
            mobile_phone: phoneNum,
            type: common.sms_type.register,
            ticket: imgCode,
            send_type: "0"
        };
        service.reqFun9199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                timer = setInterval(function () {

                    shows();
                }, 1000);
            } else {
                var $code = $(_pageId + " #getYzm");
                window.clearInterval(timer);
                i = 120;
                $code.css("background-color", " #C1E3B6");
                $code.attr("data-state", "true");
                $code.html("重新获取验证码");
                setImgCode();
                $(_pageId + " #tuxingCode").val("");
                initYanZma();
                $(_pageId + " #talkCode").show();
                layerUtils.iAlert(error_info);
            }
            $(_pageId + " #talkCode").show();
        });
    }

    /**
     * 显示读秒
     * */
    function shows() {
        var $code = $(_pageId + " #getYzm");
        $code.attr("data-state", "false");//点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.css("background-color", "yellow");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.css("background-color", " #C1E3B6");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();
        }
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iAlert("登录密码不能为空");
            return false;
        }
        if (pwd1.length <6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iAlert("确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iAlert("两次密码不相同");
            return false;
        }
        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }

        if (!isAgree) {
            layerUtils.iAlert("请阅读协议并同意签署");
            return false;
        }
        return true;
    }

    /**
     * 验证手机验证码
     * @param {Object} paramObj 入参json对象
     *    mobile_vf_cod 输入的手机验证码
     *    mobile_phone 输入的手机号码
     */
    function checkPhoneCode(paramObj) {
        registerAccount(paramObj);
    }

    //获取协议
    function reqFun102016(param1) {
        var param = {
            agreement_type: param1,
        }
        service.reqFun102016(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    return;
                }
                var html = "";
                for (var i = 0; i < results.length; i++) {
                    html += '<a href="' + global.oss_url + results[i].url + '" target="_blank" style="color:#319ef2">《' + results[i].agreement_title + '》</a>';
                }
                $(_pageId + " .deal_box").html(html);

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     * 商城用户注册
     *   mobile: 手机号,
     *   sys_trans_pwd: 密码,
     *   yqrPhoneNum:邀请人电话
     */
    function registerAccount(paramObj) {
        service.reqFun101001(paramObj, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            //验证码重置
            var $code = $(_pageId + " #getYzm");
            $(_pageId + " #verificationCode").val("");//验证码
            // setImgCode()//刷新图像验证码
            $(_pageId + " #tuxingCode").val("");

            window.clearInterval(timer);
            i = 120;
            $code.css("background-color", " #C1E3B6");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();
            if (error_no == "0") {
                layerUtils.iAlert("恭喜您，注册成功！", "", function () {
                    appUtils.pageInit(_pageCode, "appdown/index");
                },"立即下载");
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(error_info);
            }
        }, {"isLastReq": false});
    }

    function destroy() {
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        window.clearInterval(timer);
        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.css("background-color", "#C1E3B6");
        $yzm.html("获取验证码");
        $(_pageId + " #input_radio2").attr("isSelect", "true");
        i = 120;
        service.destroy();
        $(_pageId + " #checkBox").removeClass("checked");
        isAgree = true;
        $(_pageId + " #pwd1").val('');
        $(_pageId + " #pwd2").val('');
        $(_pageId + " #tuxingCode").val('');
        $(_pageId + " #verificationCode").val('');
        $(_pageId + " #yqrPhoneNum").val("");
        yqrPhoneNum='';
    }

    function pageBack() {
        appUtils.pageInit(_pageCode, "drainage/userInvitationWx");
    }

    /*****************************************************************************************************************************/

    //刷新图形验证码
    function setImgCode() {
        service.reqFun1100005({}, function (data) {
            if (data.error_no == 0) {
                var base64 = data.results[0].base64;
                $(_pageId + " #getCode img").attr("src", base64);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    /*****************************************************************************************************************************/
    var userRegistered = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userRegistered;
});
