// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "highEnd/indexPremise",
        _pageId = "#highEnd_indexPremise ";

    var productInfo;
    var isAllPass = false;  //是否全部通过验证
    var userInfo;
    var ut = require("../common/userUtil");
    var stepArr = [0, 0, 0, 0]; //0代表未完成，1代表已完成
    var cust_risk_level; //风险等级
    var productRisk;

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        productInfo = appUtils.getSStorageInfo("productInfo")
        userInfo = ut.getUserInf();
        initState();

    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //点击各项按钮
        appUtils.bindEvent($(_pageId + " .operation-btn"), function () {
            var toPage = $(this).attr('data-to');
            if (isBtnDis(this)) return;
            var index = $(this).attr('data-index');
            var sumStep = 0;
            for (var i = 0; i < index; i++) {
                sumStep += stepArr[i];
            }
            if (index != sumStep) {
                layerUtils.iAlert("请完成上一步操作");
            } else {
                if(toPage == "safety/riskQuestion") {
                    appUtils.pageInit(_page_code, 'safety/riskQuestion')
                } else {
                    riskAssessment(toPage)
                }
            }

        })
        $(_pageId + '#goNext').on('click', function () {
            let data = appUtils.getSStorageInfo("buyTransferData");
            if(isAllPass && sessionStorage.isShowTranferPro == 1 && data && data.transfer_vol) return appUtils.pageInit(_page_code, 'myTransfer/buyTransferProduct');
            isAllPass && riskAssessment('highEnd/smallgatherBuy');
        })

    }

    // 检测按钮是否可点击
    function isBtnDis(btn) {
        return $(btn).hasClass('completed') || $(btn).hasClass('reviewing')
    }

    // 风险评估
    function riskAssessment(toPage) {
        productRisk = +(productInfo.risk_level.substr(1));//产品风险等级
        if (cust_risk_level.substr(1) >= productRisk) { //符合
            appUtils.pageInit(_page_code, toPage)
        } else { //不符合
            layerUtils.iConfirm('该产品超出您的风险等级', function () {
                appUtils.pageInit(_page_code, 'safety/riskQuestion')
            }, function () {
            }, '重新评测', '取消')
        }
    }

    // 初始化导航状态
    function initState() {
        var param = {fund_code: productInfo.fund_code, type: productInfo.prod_sub_type2}
        service.reqFun101032(param, function (res) {
            if (+res.error_no == 0) {
                var useData = res.results[0]
                cust_risk_level = useData.cust_risk_level;
                handleRisk(useData.risk_state, useData.cust_risk_name); //risk_state 风险测评状态 0:未测评 1:已测评 2:测评过期  cust_risk_name 风险等级名称
                handleIndentify(useData.accredited_investor); //合格投资人状态 0:不合格, 1:合格, 2:豁免, 3:未鉴定, 4:已到期, 5:审核中
                handleFit(useData.appropriateness); //适当性评估 0:未评估 1:已评估
                handleRiskNote(useData.risk_disclosure); //风险揭示书 0 未签署 1 已签署
                setProgress();
            } else {
                layerUtils.iAlert(res.error_info)
            }
        });
    }

    /*
     * 处理风险等级状态
     * @param { Number } state 状态代码
     * @param { String } riskName 等级名称
     */
    function handleRisk(state, riskName) {
        if (state == '1') {
            stepArr[0] = 1;
            productRisk = +(productInfo.risk_level.substr(1));//产品风险等级
            if (cust_risk_level.substr(1) < productRisk) { //不符合
                $(_pageId + " .risk").show();
                $(_pageId + " .risk_standard").text(common.convRiskLevel(productRisk)).show;
                $(_pageId + '#riskStep .operation-btn').addClass('after');
            } else {
                $(_pageId + '#riskStep .operation-btn').addClass('completed');
            }
        } else if (state == '2') {
            stepArr[0] = 0;
            $(_pageId + '#riskStep').addClass('overdue');
        } else {
            stepArr[0] = 0;
        }
        let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
        $(_pageId + '#riskStep .operation-txt').text(invalidFlag == '0' ? riskName:riskName + '(已到期)' || '')
    }


    /*
     * 处理投资人认证状态
     * @param { Number } state 状态代码
     *  0:不合格, 1:合格, 2:豁免, 3:未鉴定, 4:已到期, 5:审核中
     */
    function handleIndentify(state) {
        var stateName = ''
        if (state == '1') {
            $(_pageId + '#indentifyStep .operation-btn').addClass('completed');
            stepArr[1] = 1;
        } else if (state == '2') {
            $(_pageId + '#indentifyStep').addClass('remit')
            $(_pageId + '#indentifyStep .operation-btn').addClass('completed');
            stepArr[1] = 0;

        } else if (state == '4') {
            $(_pageId + '#indentifyStep').addClass('overdue')
            stepArr[1] = 0;

        } else if (state == '5') {
            $(_pageId + '#indentifyStep .operation-btn').addClass('reviewing')
            stepArr[1] = 0;

        } else if (state == '0') {
            stateName = '不合格'
            $(_pageId + '#indentifyStep').addClass('fail')
            stepArr[1] = 0;
        }
        $(_pageId + '#indentifyStep .operation-txt').text(stateName || '')
    }


    /*
     * 处理适风险揭示书
     * @param { Number } state 状态代码
     * 0 未签署 1 已签署
     */
    function handleRiskNote(state) {
        if (state == '1') {
            stepArr[3] = 1;
            $(_pageId + '#riskNoteStep .operation-btn').addClass('completed')
        } else {
            stepArr[3] = 0;

        }
    }

    /*
     * 处理适当性评估
     * @param { Number } state 状态代码
     * 0:未评估 1:已评估
     */
    function handleFit(state) {
        if (state == '1') {
            stepArr[2] = 1;
            $(_pageId + '#fitStep .operation-btn').addClass('completed')
        } else {
            stepArr[2] = 0;
        }
    }

    /*
     * 设置进度条状态
     */
    function setProgress() {
        var steps = $(_pageId + '#stepList .step')
        var activeIndex = -1
        isAllPass = true
        for (var i = steps.length - 1; i >= 0; i--) {
            if (steps.eq(i).find('.operation-btn').hasClass('completed')) {
                activeIndex = i
            } else {
                isAllPass = false
            }
            i <= activeIndex && steps.eq(i).addClass('active')
        }
        isAllPass && $(_pageId + '#goNext').removeClass('disabled')
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        stepArr = [0, 0, 0, 0]; //0代表未完成，1代表已完成
        isAllPass = false;
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
