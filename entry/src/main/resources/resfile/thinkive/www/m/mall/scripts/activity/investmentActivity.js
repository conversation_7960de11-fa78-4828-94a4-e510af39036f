/**
 * 模块名：晋金财富抽奖外链页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"), layerUtils = require("layerUtils"), SHIscroll = require("shIscroll"),
        service = require("mobileService"), gconfig = require("gconfig"), common = require("common"), validatorUtil = require("validatorUtil");
    var external = require("external");
    var tools = require("../common/tools");
    /* 常量 */
    var _pageCode = "activity/investmentActivity", _pageId = "#activity_investmentActivity";
    /* 变量  活动信息*/
    var activityInfo = "",activity_id_url,bind_card;
    var stopIndex;
    // var drawShow;//是否抽过奖
    // var pointType = 0;
    // 初始化转盘
    var luck = {
        index: 0, // 当前转动到哪个位置，起点位置
        count: 0, // 总共有多少个位置
        timer: 0, // setTimeout的ID，用clearTimeout清除
        speed: 20, // 初始转动速度
        times: 0, // 转动次数
        cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
        prize: -1, // 中奖位置
        init: function (id) {
            if ($("#" + id).find(".luck-unit").length > 0) {
                var $luck = $("#" + id);
                var $units = $luck.find(".luck-unit");
                this.obj = $luck;
                this.count = $units.length;
                $luck.find(".luck-unit-" + this.index).addClass("active");
            }
        },
        roll: function () {
            var index = this.index;
            var count = this.count;
            var luck = this.obj;
            $(luck).find(".luck-unit-" + index).removeClass("active");
            index += 1;
            if (index > count - 1) {
                index = 0;
            }
            $(luck).find(".luck-unit-" + index).addClass("active");
            this.index = index;
            return false;
        },
        stop: function (index) {
            this.prize = index;
            return false;
        }
    };
    var moneyObj = {
        // 顺时针reward_type-0-7
        "5000": {
            list_text: "5000积分", //领取记录文字
            reward_text: "5000积分", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [0],
            reward_type: "0", // 类型-1:随机积分 0:固定积分 2:实物
            btn_txt: "我知道了",
            prompt:"",
        },
        "physical3": {
            list_text: "华为手机", //领取记录文字
            reward_text: "华为手机", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [1],
            reward_type: "2" ,// 实物
            btn_txt: "我知道了",
            prompt:"工作人员将在两个工作日内与您联系",
        },
        "auto": {
            list_text: "随机积分", //领取记录文字
            reward_text: "", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [2],
            reward_type: "1" ,// 随机积分
            btn_txt: "我知道了",
            prompt:"",
        },
        "physical4": {
            list_text: "小米加湿器",
            reward_text: "小米加湿器",
            src: "./images/activity/p_ok.png",
            stopIndex: [7],
            reward_type: "2",
            btn_txt: "我知道了",
            prompt:"工作人员将在两个工作日内与您联系",
        },
        "physical2": {
            list_text: "小米台灯",
            reward_text: "小米台灯",
            src: "./images/activity/p_ok.png",
            stopIndex: [3],
            reward_type: "2" ,//实物
            btn_txt: "我知道了",
            prompt:"工作人员将在两个工作日内与您联系",
        },
        "auto": {
            list_text: "随机积分", //领取记录文字
            reward_text: "", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [6],
            reward_type: "1" ,// 随机积分
            btn_txt: "我知道了",
            prompt:"",
        },
        "0": {
            list_text: "未中奖",
            reward_text: "很遗憾，未中奖",
            src: "./images/activity/p_no.png",
            stopIndex: [5],
            reward_type: "0",
            btn_txt: "我知道了",
            prompt:"",
            // list_text: "小米台灯",
            // reward_text: "小米台灯",
            // stopIndex: [5],
            // reward_type: "2" ,//实物
            // btn_txt: "我知道了",
            // prompt:"工作人员将在两个工作日内与您联系",
        },
        "10000": {
            list_text: "10000积分",
            reward_text: "10000积分",
            src: "./images/activity/p_ok.png",
            stopIndex: [4],
            reward_type: "0",
            btn_txt: "我知道了",
            prompt:"",
        }
    }
    var platform = gconfig.platform;
    /**
     * 初始化
     */
    
    function init() {
        if(platform != "0") {
            var data = external.callMessage({
                "funcNo": "50043",
                "moduleName" : "mall",
                "key": "investment_activityInfo",
            })
            activityInfo = data && data.results && data.results.length > 0 ? data.results[0].value : "";
            activity_id_url = activityInfo.activity_id;
        } else {
            activityInfo = appUtils.getSStorageInfo("investment_activityInfo");
            activity_id_url = activityInfo.activity_id;
        }
        // drawShow = appUtils.getSStorageInfo("drawShow");
        // activity_id_url = appUtils.getSStorageInfo("activity_id");
        if(validatorUtil.isEmpty(activity_id_url)){
            activityInfo = ''
            layerUtils.iAlert("活动ID未配置,请联系管理人员");
        }
    //    var activitiesInfo = common.getLocalStorage("activityInfo");
    //    activityInfo = {
    //        activity_id: "59",
    //        cust_no: "0002867",
    //        channel: "jjcf_app",
    //        type: "luckDraw",
    //        mobile: "18435106096",
    //    }
		if(activityInfo) {
            activityInfo.activity_id = activity_id_url;
			$(_pageId + " .btn").show();
            // $(_pageId + " .fundLuckdraw .content").css({'padding-bottom': '0.1rem'});
		} else {
			$(_pageId + " .btn").hide();
            // $(_pageId + " .fundLuckdraw .content").css({'padding-bottom':'2rem'});
		}
        luck.init('luck');
        // $(_pageId + " .activity_pop_layer").hide();
		// if(activityInfo) {
		// 	getActivityInfo();
		// }
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //隐藏下载提示
        appUtils.bindEvent($(_pageId + " #downloadTip .activityRulesBonce"), function () {
            $(_pageId + " #downloadTip").hide();
            if (platform == "0") { //晋金财富微信
                window.open("https://m.xintongfund.com/m/mall/index.html#!/appdown/index.html");
            } else { //晋金所app
                var setParam = {
                    "funcNo": "80002",
                    "moduleName": "mall",
                    "biz_code": "open_thfund",
					"page": "login/userIndexs"
                };
                external.callMessage(setParam);
            }
        });
        //隐藏下载提示
        appUtils.bindEvent($(_pageId + " #downloadTip"), function () {
            $(_pageId + " #downloadTip").hide();
        });
        //积分兑换
        appUtils.bindEvent($(_pageId + " #pointsFor"), function () {
            // bind_card 是否绑卡- 0 未绑卡、1已绑卡
            if(bind_card == '0'){
                layerUtils.iAlert("请先绑卡");
                return;
            }
            if(platform != "0" && activityInfo && activityInfo.channel == "jjcf_app") { // 晋金财富app
                var setParam = {
                    "funcNo": "80002",
                    "moduleName": "mall",
                    "biz_code": "pointsFor",
                };
                external.callMessage(setParam);
            } else { //晋金所app、晋金财富微信
                $(_pageId + " #downloadTip").show();
            }
        });
        //抽奖
        appUtils.bindEvent($(_pageId + " #start"), function () {
            // if(!activityInfo || activityInfo.channel !== "jjcf_app") {
            //     $(_pageId + " #downloadTip").show();
            //     return;
            // }
            // if(drawShow == "1"){
            //     return layerUtils.iAlert('您已经抽过奖了哦！');
            // }
            // if (state == "2") {
            //     reward_num = 0;
            //     layerUtils.iMsg(-1, "请于周五9:00-17:00参与活动！", 2);
            //     return;
            // }
            // if (state == "3") {
            //     reward_num = 0;
            //     layerUtils.iMsg(-1, "请于周五9:00-17:00参与活动！", 2);
            //     return;
            // }
			// if(shareflag === "0" && reward_num <= 0) {
			// 	layerUtils.iConfirm("分享活动再抽一次",function(){
			// 		var setParam = {
			// 		    "funcNo": "80002",
			// 		    "moduleName": "mall",
			// 		    "biz_code": "share",
			// 		};
			// 		external.callMessage(setParam);
			// 	},function(){

			// 	},"去分享","取消");
			// 	return;
			// }
            // if (reward_num <= 0) {
            //     layerUtils.iMsg(-1, "抽奖次数已用完，请下周五继续参与活动", 2);
            //     return;
            // }
            // if ($(_pageId + " #start").hasClass("notclick")) {
            //     return;
            // }

            // 按钮禁用
            $(_pageId + " #start").addClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0_end.png'>");
            luck.speed = 100;
            var param = {
                "activity_id": activityInfo.activity_id,
                "cust_no": activityInfo.cust_no
            };
            service.reqFun108017(param, function (data){
                // -2 未绑卡
                // -3 活动尚未开始或已结束
                // -4 次数已用完
                // -999011 请求过于频繁，请稍候再试
                //奖池为空时默认谢谢参与
                
                if (data.error_no == "-2" || data.error_no == "-3" || data.error_no == "-7" || data.error_no == "-8" || data.error_no == "-4"  || data.error_no == "-999011") {
                    stopIndex = 0;
                    $(_pageId + " #start").removeClass("notclick");
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.error_no != "0") {
                    stopIndex = 5;
                    $(_pageId + " .index_info").html(moneyObj["0"].reward_text);
                    $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[0].src);
                    $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[0].btn_txt);
                    roll();
                    // layerUtils.iAlert(data.error_info);
                    // reward_num--;
                    // if(state == '1' && shareflag == '0'){
                    //     $(".btn h3").html("剩余&nbsp;<span>"+ reward_num +"</span>&nbsp;次,分享活动可额外获得一次机会");
                    //     $(".btn .fundLuckdraw_btn img").attr("src",'/m/mall/images/activity/fundLuckdraw_btn.png');
                    // }else {
                    //     $(".btn h3").html("剩余&nbsp;<span>"+ reward_num +"</span>&nbsp;次,请于周五9:00-17:00参与活动");
                    //     $(".btn .fundLuckdraw_btn img").attr("src",'/m/mall/images/activity/fundLuckdraw_btn2.png');
                    // }
                    return;
                }
                // drawShow = '1';
                // appUtils.setSStorageInfo("drawShow","1");
                var results = data.results[0];
                var money = Number(results.reward_vol) + "";
                var moneyKey;
                // pointType = null
                //奖励类型reward_type 1:随机积分 0:固定积分 2:实物
                if((results.reward_type == "1" || results.reward_type == "0") && (results.reward_vol == "0")){
                    stopIndex = 5;
                    $(_pageId + " .index_info").html(moneyObj["0"].reward_text);
                    $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[0].src);
                    $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[0].btn_txt);
                    roll();
                    return
                }
                if (results.reward_type === "2") { //实物
                    // 实物类型 2-台灯、3-手机、4-小米加湿器
                        moneyKey = "physical" + results.reward_vol;
                } else if (results.reward_type === "0" ) { //固定积分
                    moneyKey = money;
                    // pointType = 1
                    // $(_pageId + " .integral").html(money)
                }else if (results.reward_type === "1" ) { //随机积分
                    moneyKey = "auto";
                    // pointType = 1
                    // $(_pageId + " .integral").html(money)
                }
                if (validatorUtil.isEmpty(moneyObj[moneyKey])){
                    moneyKey = "auto"
                }
                
				$(_pageId + " .index_info").html(moneyObj[moneyKey].reward_text ? moneyObj[moneyKey].reward_text : money + "积分");
                $(_pageId + " .index_info_tishi").html(moneyObj[moneyKey].prompt ? moneyObj[moneyKey].prompt :'');
				stopIndex = moneyObj[moneyKey].stopIndex[Math.floor(  Math.random() * moneyObj[moneyKey].stopIndex.length )]
                $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[moneyKey].src);
				$(_pageId + " #rewardResult .sureBtn span").html(moneyObj[moneyKey].btn_txt);
                roll();
                // reward_num--;
				// $(_pageId + " .btn h3 ").html("剩余&nbsp;<span> "+ reward_num + "</span>&nbsp;次,分享活动可额外获得一次机会");
                //shareflag 0未分享 1:分享过
                // if(state == '1' && shareflag == '0'){
                //     $(".btn h3").html("剩余&nbsp;<span>"+ reward_num +"</span>&nbsp;次,分享活动可额外获得一次机会");
                //     $(".btn .fundLuckdraw_btn img").attr("src",'/m/mall/images/activity/fundLuckdraw_btn.png');
                // }else {
                //     $(".btn h3").html("剩余&nbsp;<span>"+ reward_num +"</span>&nbsp;次,请于周五9:00-17:00参与活动");
                //     $(".btn .fundLuckdraw_btn img").attr("src",'/m/mall/images/activity/fundLuckdraw_btn2.png');
                // }
            }, {"isShowWait": false});
        });
        //隐藏中奖弹框
        appUtils.bindEvent($(_pageId + " .sureBtn"), function (e) {
            $(_pageId + " #rewardResult").hide();
            
        });
        // appUtils.bindEvent($(_pageId + " .activityRulesBonce_btn"), function (e) {
        //     $(_pageId + " #rewardResult1").hide();
        // });
    }

    /**
     * 销毁
     */
    function destroy() {
        // pointType = 0;
		$(_pageId + " .activity_pop_layer").hide();
        service.destroy();
		$(_pageId + " #pointsFor").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #rewardResult1").hide();
		$(_pageId + " #rewardResult .sureBtn span").html("");
        $(_pageId + " .btn h3").html("");
        $(_pageId + " .btn .fundLuckdraw_btn img").attr("src",'');
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    function roll() {
        luck.times += 1;
        luck.roll();
        if (luck.times > luck.cycle + 10 && luck.prize == luck.index) {
            clearTimeout(luck.timer);
            luck.prize = -1;
            luck.times = 0;
            if (stopIndex !== 4) {
                setTimeout(function () {
                    // if(pointType == 1){ 
                    //     $(_pageId + " #rewardResult1").show();
                    // }else{
                    //     $(_pageId + " #rewardResult").show();
                    // }
                    $(_pageId + " #rewardResult").show();
                }, 600);
            } else {
                setTimeout(function () {
                    // if(pointType == 1){ 
                    //     $(_pageId + " #rewardResult1").show();
                    // }else{
                    //     $(_pageId + " #rewardResult").show();
                    // }
                    $(_pageId + " #rewardResult").show();
                }, 600);
            }
            $(_pageId + " #start").removeClass("notclick");
            $(_pageId + " #start").html("<img src='images/activity/p_0.png'>");
        } else {
            if (luck.times < luck.cycle) {
                luck.speed -= 10;
            } else if (luck.times == luck.cycle) {
                luck.prize = stopIndex;
            } else {
                if (luck.times > luck.cycle + 10 && ((luck.prize == 0 && luck.index == 7) || luck.prize == luck.index + 1)) {
                    luck.speed += 110;
                } else {
                    luck.speed += 20;
                }
            }
            if (luck.speed < 40) {
                luck.speed = 40;
            }
            luck.timer = setTimeout(roll, luck.speed);
        }
        return false;
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
