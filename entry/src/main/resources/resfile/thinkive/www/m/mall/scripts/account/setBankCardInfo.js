// 请输入银行卡信息
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#account_setBankCardInfo ";
    var ut = require("../common/userUtil");
    var idCardInfo;
    var bankAccInfo;
    var tools = require('../common/tools');
    var sms_mobile = require("../common/sms_mobile");
    var pay_type; //0：快捷代扣 1：协议支付
    var send_sms_flag;
    var bank_serial_no; //银行渠道流水
    function init() {
        sms_mobile.init(_pageId);
        // $(_pageId + " #bk").css({backgroundColor: "#D1D4D5"});
        $(_pageId + " .place").hide();
        $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        idCardInfo = appUtils.getSStorageInfo("idCardInfo"); //身份证信息
        bankAccInfo = appUtils.getSStorageInfo("bankAccInfo"); // 银行卡信息
        //优先渲染注册手机号
        var userInfo = ut.getUserInf();
        $(_pageId + " #yhmPhone").val(userInfo.mobileWhole);
        // 清空银行卡信息
        if (bankAccInfo) {
            appUtils.clearSStorage("bankAccInfo");
            $(_pageId + " #bankCard").val(bankAccInfo.bank_acct);
            $(_pageId + " #yhmPhone").val(bankAccInfo.bank_reserved_mobile);
            if (bankAccInfo.bank_acct) {
                bankDistinguish();
            }
        }
        if (idCardInfo) {
            $(_pageId + " #cardPerson").val(idCardInfo.realname);
        }
        tools.getPdf("6");
        BankCardInformation()
    }
    // 绑定事件
    function bindPageEvent() {
        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            var external = require("external");
            var Param = {
                "funcNo": "60304",
                "moduleName": "mall"
            };
            external.callMessage(Param);
        });
        //输入银行卡号
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 预留手机号码
        appUtils.bindEvent($(_pageId + " #yhmPhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 验证码 控制全文字
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        // 点击协议书前的checkBox
        // appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi"), function (e) {
        //     e.stopPropagation();
        //     e.preventDefault();
        //     var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class");
        //     if (classname == "active") {
        //         $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        //         $(_pageId + " #bk").css({backgroundColor: "#D1D4D5"});
        //     } else {
        //         $(_pageId + " .rule_check #xuanzeqi i").addClass("active");
        //         $(_pageId + " #bk").css({backgroundColor: "#E5433B"});
        //     }
        // });
        // 查看银行卡限额
        // appUtils.bindEvent($(_pageId + " .bank_tips_set"), function () {
        //     var bankCard = $(_pageId + " #bankCard").val();  // 银行卡号
        //     bankCard = bankCard.replaceAll(" ", "");
        //     var yhmPhone = $(_pageId + " #yhmPhone").val();  // 银行预留手机号码
        //     var param = {
        //         "bank_acct": bankCard,     // 用户卡号
        //         "bank_reserved_mobile": yhmPhone,
        //     };
        //     appUtils.setSStorageInfo("bankAccInfo", param);
        //     appUtils.pageInit("account/setBankCardInfo", "safety/bankInfo");
        // });
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            // 控制全数字输入
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }

            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);


            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");

        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");

        // 银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            bankDistinguish();
        }, "blur");

        // 点击下一步
        appUtils.bindEvent($(_pageId + " #bk"), function () {
            var bankCard = $(_pageId + " #bankCard").val();  // 银行卡号
            bankCard = bankCard.replaceAll(" ", "");
            var cardPerson = $(_pageId + " #cardPerson").val(); // 真实姓名
            var yhmPhone = $(_pageId + " #yhmPhone").val();  // 银行预留手机号码
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议
            // var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            // var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            // if (classname != "active") {
            //     layerUtils.iAlert("请您签署协议");
            //     return;
            // }
            // if (!validatorUtil.isCnAndEn(cardPerson) || $.trim(cardPerson).length < 2 || $.trim(cardPerson).length > 5) {
            //     layerUtils.iMsg(-1, "请输入真实姓名");
            //     return;
            // }
            if (validatorUtil.isEmpty(bankCard)) {
                layerUtils.iMsg(-1, "银行卡号不能为空");
                return;
            }
            if (!validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确银行卡号");
                return;
            }
            if (!validatorUtil.isMobile(yhmPhone)) {
                layerUtils.iMsg(-1, "请输入正确手机号码");
                return;
            }
            // if (isSend == "true") {
            //     layerUtils.iMsg(-1, "您还未获取验证码");
            //     return;
            // }
            // if (verificationCode.length != 6) {
            //     layerUtils.iMsg(-1, "请输入完整的验证码");
            //     return;
            // }
            // var live_address = idCardInfo.living_address.code.split(" ");
            var param = {
                "cust_name": idCardInfo.realname, // 用户姓名
                "bank_acct": bankCard,     // 用户卡号
                "cert_no": idCardInfo.idCard,   // 用户身份证
                "bank_name": $(_pageId + " #bankname").html(),   //银行名称
                "bank_reserved_mobile": yhmPhone,
                "sms_mobile": yhmPhone,
                // "message_code": verificationCode, // 短信验证码
                // "sms_code": verificationCode, // 短信验证码
                "cust_address": idCardInfo.cust_address, //客户地址
                "cert_type": "0", //证件类型
                "vaild_date": idCardInfo.vaild_date, //证件有效期
                "sex": idCardInfo.sex, //性别 0 女 1 男
                "bank_code": $(_pageId + " #bankname").attr("bank_code"),//银行编码
                "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                "bank_serial_no": bank_serial_no,
                "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                // "vocation_code": idCardInfo.vocation_code.id,
                // "income": idCardInfo.year_income.money*10000,//年收入
                // "living_address_province":(live_address[0]),
                // "living_address_city":(live_address[1]),
                // "living_address_county":(live_address[2])
            };
            //实名认证
            service.reqFun101061(param, function(data) {
                if(data.error_no == "0") {
                    appUtils.setSStorageInfo("bankAccInfo", param);
                    appUtils.pageInit("account/setBankCardInfo", "account/setUserInfo", {});
                } else {
                    sms_mobile.clear();
                    layerUtils.iAlert(data.error_info);
                }
            })
        });

        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var yhmPhone = $(_pageId + " #yhmPhone").val();
                var bankCard = $(_pageId + " #bankCard").val();
                bankCard = bankCard.replaceAll(" ", "");
                var cardPerson = $(_pageId + " #cardPerson").val();
                // if (!validatorUtil.isCnAndEn(cardPerson) || $.trim(cardPerson).length < 2 || $.trim(cardPerson).length > 5) {
                //     layerUtils.iMsg(-1, "请输入真实姓名");
                //     return;
                // }
                if (!validatorUtil.isBankCode(bankCard)) {
                    layerUtils.iMsg(-1, "请输入正确银行卡号");
                    return;
                }
                if (validatorUtil.isEmpty($(_pageId + " #bankname").html())) {
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }
                if (validatorUtil.isEmpty($(_pageId + " #bankname").attr("bank_code"))) {
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }
                if (!validatorUtil.isMobile(yhmPhone)) {
                    layerUtils.iMsg(-1, "请输入正确手机号码");
                    return;
                }
                var param = {
                    "bank_code": $(_pageId + " #bankname").attr("bank_code"),//银行编码
                    "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                    "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                    "bank_acct": bankCard,     // 用户卡号
                    "bank_reserved_mobile":yhmPhone,
                    "cert_no": idCardInfo.idCard,   // 用户身份证
                    "bank_name":$(_pageId + " #bankname").html(),
                    "sms_type":common.sms_type.bindCard,
                    "send_type": "0",
                    "cust_name": idCardInfo.realname, // 用户姓名
                    "cert_type": "0", //证件类型
                    "mobile_phone": yhmPhone,
                    "type": common.sms_type.bindCard,//发送短信验证码
                }
                // sms_mobile.sendPhoneCode(param);
                //099 120度
                // let payorg_id = $(_pageId + " #bankname").attr("payorg_id")
                if(send_sms_flag == "1"){
                    sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                        if (data.error_no == "0") {
                            bank_serial_no = data.results[0].bank_serial_no
                        }else{
                            layerUtils.iAlert(data.error_info);
                        }
                    });
                }else{
                    sms_mobile.sendPhoneCode(param);
                }
            }
        });

        // 点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
    }
    //查出支持的银行卡
    function BankCardInformation() {
        service.reqFun102014({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            var str = "";
            if (error_no == "0") {
                for (var i = 0; i < data.results.length; i++) {
                    var bank_name = data.results[i].bank_name;
                    var day_limit = data.results[i].day_limit;
                    var single_limit = data.results[i].single_limit;
                    var remark = data.results[i].remark;
                    if (day_limit < 0) {
                        day_limit = "不限";
                    }
                    if (single_limit < 0) {
                        single_limit = "不限";
                    }
                    if (day_limit >= 10000) {
                        day_limit = day_limit / 10000 + "万";
                    }
                    if (single_limit >= 10000) {
                        single_limit = single_limit / 10000 + "万";
                    }

                    var bank_transfer_flag = data.results[i].transfer_flag;
                    var recommend_flag = data.results[i].recommend_flag;//是否推荐 0不推荐1推荐
                    if (bank_transfer_flag == "1") {
                        if(recommend_flag == '1'){
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add recommend'><img src='images/star.png'>"+remark+"</td></tr>";
                        }else {
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add'>"+remark+"</td></tr>";
                        }
                    } else {
                        if(recommend_flag == '1'){
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add recommend'><img src='images/star.png'>"+remark+"</td></tr>";
                        }else {
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add'>"+remark+"</td></tr>";
                        };
                    }

                }
                $(_pageId + " #mainInfo").html(str);
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }
    // 失去焦点时候验证银行限额 银reqFun199001行名称
    function bankDistinguish() {
        $(_pageId + " .place").hide();
        $(_pageId + " #pop_view").css("visibility", "hidden");
        $(_pageId + " #big_show_bank").html("");
        var bankCard = $(_pageId + " #bankCard").val();
        bankCard = bankCard.replaceAll(" ", "")
        if (validatorUtil.isEmpty(bankCard)) {
            $(_pageId + " #bankname").html("").removeAttr("bank_code");
            layerUtils.iMsg(-1, "银行卡号不能为空");
            return;
        }
        if (!validatorUtil.isBankCode(bankCard)) {
            $(_pageId + " #oneMoney").html("");
            $(_pageId + " #drxe").html("");
            layerUtils.iMsg(-1, "请输入正确银行卡号");
            return;
        }
        var bin_id = $(_pageId + " #bankCard").val();
        bin_id = bin_id.replaceAll(" ", "");
        var param = {
            "bin_id": bin_id
        };

        service.BankByCard(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {
                $(_pageId + " .place").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    var bank_name = result.bank_name;
                    var bank_code = result.bank_code;
                    var single_limit = result.single_limit;
                    var day_limit = result.day_limit;
                    send_sms_flag = result.send_sms_flag;
                    $(_pageId + " #bankname").attr("payorg_id", result.payorg_id);//支付机构ID
                    $(_pageId + " #bankname").attr("pay_type", result.pay_type);//支付方式
                    if (parseFloat(day_limit) > 0) {
                        $(_pageId + " .place").show();
                        $(_pageId + " #drxe").html(day_limit + "元");
                    } else if (parseFloat(day_limit) == 0) {
                        $(_pageId + " .place").hide();
                    } else {
                        $(_pageId + " .place").show();
                        $(_pageId + " #drxe").html("不限");
                    }
                    if (parseFloat(single_limit) > 0) {
                        $(_pageId + " .place").show();
                        $(_pageId + " #oneMoney").html(single_limit + "元");
                    } else if (parseFloat(single_limit) == 0) {
                        $(_pageId + " .place").hide();
                    } else {
                        $(_pageId + " .place").show();
                        $(_pageId + " #oneMoney").html("不限");
                    }
                    // 识别出来填写银行卡编号 和银行名称
                    $(_pageId + " #bankname").html(bank_name);
                    $(_pageId + " #bankname").attr("bank_code", bank_code);
                } else {
                    $(_pageId + " .place").hide();
                    $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }

            } else {
                // 识别失败时候 去掉银行信息
                $(_pageId + " #oneMoney").html("不限");
                $(_pageId + " #drxe").html("不限");
                $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                $(_pageId + " .place").hide();
                layerUtils.iMsg(-1, error_info);
            }
        });
    }

    function destroy() {
        send_sms_flag = null;
        // $(_pageId + " #verificationCode").val("");
        sms_mobile.destroy();
        $(_pageId + " .place").hide();
        $(_pageId + " input").val("");
        $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
    }

    // 系统返回
    function pageBack() {
        var bankCard = $(_pageId + " #bankCard").val();  // 银行卡号
        bankCard = bankCard.replaceAll(" ", "");
        var yhmPhone = $(_pageId + " #yhmPhone").val();  // 银行预留手机号码
        var param = {
            "bank_acct": bankCard,     // 用户卡号
            "bank_reserved_mobile": yhmPhone,
        };
        appUtils.setSStorageInfo("bankAccInfo", param);
        appUtils.pageBack();
    }

    var setBankCardInfo = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setBankCardInfo;
});
