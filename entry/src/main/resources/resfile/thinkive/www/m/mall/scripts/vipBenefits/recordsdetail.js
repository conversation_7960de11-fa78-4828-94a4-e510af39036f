// 晋金宝交易记录-详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        layerUtils = require("layerUtils"),
        _pageId = "#vipBenefits_recordsdetail ";
    var _pageCode = "vipBenefits/recordsdetail";
        require("../common/clipboard.min.js");
    var param;

    function init() {
        param = appUtils.getPageParam();
        if (param.trans_type == '01' || param.trans_type == '03') {//任务获取
            $(_pageId + "[data-name=remark]").html(param.remark || "无");
        } else if(param.trans_type == '02'){//积分兑换
            // goods_type 商品类型-1话费，2京东e卡
            if (validatorUtil.isNotEmpty(param.goods_type) && param.goods_type == '1'){
                var html= '<h3  style="font-size: 0.16rem;">' + param.remark + '</h3><h3 style="font-size: 0.16rem;margin-top: 0.03rem;">' + param.exchange_phone + '</h3>'
                $(_pageId + "[data-name=remark]").html(html || "无");
            }else if (validatorUtil.isNotEmpty(param.goods_type) && param.goods_type == '2' && param.trans_status == '8'){
                var validity = param.validity ? tools.ftime(param.validity):"--"
                if(validatorUtil.isNotEmpty(param.cardpwd)){
                    var cardpwd_html='<h3 style="font-size: 0.16rem;margin-top: 0.03rem;">卡密:' + param.cardpwd + '<a id="cardpwd" data-clipboard-action="copy" data-clipboard-text="' + param.cardpwd + '" style="color: #319ef2;padding-left: 0.06rem;display: contents;">复制</a></h3>'
                }else{
                    var cardpwd_html='<h3 style="font-size: 0.16rem;margin-top: 0.03rem;">卡密:--</h3>'
                }
                var html= '<h3  style="font-size: 0.16rem;">' + param.remark + '</h3>' + cardpwd_html +
                    '<h3 style="font-size: 0.16rem;margin-top: 0.03rem;">有效期:' + validity + '</h3>' +
                    '<h3 style="font-size: 0.16rem;margin-top: 0.03rem;color: #319ef2;" id="jdUsingProcess">《京东E卡兑换使用流程说明》</h3>'
                $(_pageId + "[data-name=remark]").html(html || "无");
                copyContent("cardpwd");
            }else {
                $(_pageId + "[data-name=remark]").html(param.remark || "无");
            }
            // if (validatorUtil.isEmpty(param.exchange_phone)){
            //     $(_pageId + "[data-name=remark]").html(param.remark || "无");
            // }else {
            //     var html= '<h3  style="font-size: 0.16rem;">' + param.remark + '</h3><h3 style="font-size: 0.16rem;margin-top: 0.03rem;">' + param.exchange_phone + '</h3>'
            //     $(_pageId + "[data-name=remark]").html(html || "无");
            // }
        }
        $(_pageId + "[data-name=create_date]").html(tools.ftime(param.crt_date));
        $(_pageId + "[data-name=tot_price]").html(param.vol);
        $(_pageId + "[data-name=digest]").html(param.points_busi_type_name);
        $(_pageId + "[data-name=order_state]").html(param.statusname);

    }
    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            if(!e.text.trim()) {
                layerUtils.iAlert("复制失败，请重试", -1, function () {
                    getFundInfo(false)
                });
                return;
            }
            layerUtils.iAlert("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #comeback"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " #remark"), " #jdUsingProcess",  function(e) {
            appUtils.setSStorageInfo("previouspage", _pageCode);
            appUtils.pageInit(_pageCode, "vipBenefits/jdUsingProcess",param);
        });
    }


    function destroy() {
        $(_pageId + " #remark").css("border-bottom", "1px solid #C8CDCF");
        $(_pageId + "[data-name=create_date]").html("");
        $(_pageId + "[data-name=tot_price]").html("");
        $(_pageId + "[data-name=digest]").html("");
        $(_pageId + "[data-name=order_state]").html("");
        $(_pageId + "[data-name=remark]").html("");
        param =  null;
    }

    function pageBack() {
    	appUtils.pageBack();
        $(_pageId + " #cardpwd").attr("data-clipboard-text", " ");
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
