//高端： 变更产品到期处理方式
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService");
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    var _pageCode = "highEnd/modifyExpireWay";
    var _pageId = "#highEnd_modifyExpireWay ";
    var _endflag_method;
    var _old_endflag_method;
    var _fund_code;
    var jymm;
    var highEndHoldDetail;
    var agreementList;

    function init() {
        highEndHoldDetail = appUtils.getSStorageInfo("productInfo");
        _fund_code = highEndHoldDetail.fund_code;
        var param = {
            agreement_type: 'prod', //'prod'
            fund_code: _fund_code,
            fixed_invest_flag: '',
            agreement_sub_type: "7", //agreement_sub_type
        }
        service.reqFun102016(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return
            }
            agreementList = data.results;
            //获取详情
            getDetail(highEndHoldDetail);
        })

    }

    function bindPageEvent() {
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击 处理方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _endflag_method = $(this).attr("endflag_method");
            if (_endflag_method == '0' && agreementList.length) {
                $(_pageId + " #pfd_show").css({ "display": "block" });
                $(_pageId + " .agreement2").find("i").removeClass("active");
            } else {
                $(_pageId + " #pfd_show").css({ "display": "none" });
            }
            if (_endflag_method == _old_endflag_method) {
                $(_pageId + " #confirm").addClass("no_active");
                $(_pageId + " .agreement2").find("i").addClass("active");
            } else {
                $(_pageId + " #confirm").removeClass("no_active");
            }

        });

        //变更产品到期处理方式
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            if (!$(_pageId + " .agreement2 i").hasClass("active") && _endflag_method == "0" && agreementList.length) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if ($(this).hasClass("no_active")) {
                return;
            }

            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();

            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_modifyExpireWay";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //交易密码确定
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            var param = {
                fund_code: _fund_code,
                end_flag: _endflag_method,
                trans_pwd: jymm1, //交易密码
                due_date: highEndHoldDetail.due_date,
                vir_fundcode: highEndHoldDetail.vir_fundcode
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                reqFun106015(param);
            }, { isLastReq: false });
        });

        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #back"), function () {
            pageBack();
        });

    }


    //变更产品到期处理方式
    function reqFun106015(param) {
        service.reqFun106015(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                appUtils.pageInit(_pageCode, "highEnd/modifyExpireWayResult");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getDetail(results) {
        //产品简称
        var fund_sname = results.fund_sname;
        //产品代码
        var fund_code = results.fund_code;
        //到期时间
        var due_date = results.due_date;
        if (results.prod_sub_type2 == '100') {
            due_date = results.forbid_alter_due_method
            if (results.due_redeem == "0" || !results.due_redeem || results.due_redeem == "") {
                $(_pageId + " .text").hide()
                $(_pageId + " .text_100").show()
            }
        }
        //获取交易时间
        // console.log(due_date,111)
        reqFun102008(due_date.substr(0, 8));
        due_date = tools.ftime(due_date.substr(0, 8));

        //持有份额  
        if (results.prod_sub_type2 == "81" || results.prod_sub_type2 == "97") {
            var fundvolbalance_mode1 = results.fundvolbalance_mode1;
        } else if (results.prod_sub_type2 == "100") {
            var fundvolbalance_mode1 = results.hold_vol;
        } else {
            var fundvolbalance_mode1 = results.fund_vol;
        }

        fundvolbalance_mode1 = tools.fmoney(fundvolbalance_mode1);
        //变更方式
        _old_endflag_method = results.endflag_method;
        _endflag_method = results.endflag_method;

        $(_pageId + " .modify_box .item .icon").removeClass("active");
        var activeIndex = _old_endflag_method;
        if (activeIndex == "0" && agreementList.length) {
            $(_pageId + " .agreement2").find("i").addClass("active");
            $(_pageId + " #pfd_show").css({ "display": "block" });
        } else {
            $(_pageId + " .agreement2").find("i").removeClass("active");
            $(_pageId + " #pfd_show").css({ "display": "none" });
        }
        var item = $(_pageId + " .modify_box .item[endflag_method = " + activeIndex + "]");
        $(item).find(".icon").addClass("active");
        $(_pageId + " #fund_sname").html(fund_sname);
        $(_pageId + " #fund_code").html(fund_code);
        $(_pageId + " #redconfirm_days").html(tools.ftime(results.due_date.substr(0, 8)));
        $(_pageId + " #fundvolbalance_mode1").html(fundvolbalance_mode1);
        tools.getPdf("prod", fund_code, "7", "", "", false, true)
        // tools.getPdf("prod", fund_code, buy_state)
    }


    //获取交易时间
    function reqFun102008(dqDate) {
        var param = {
            type: "9",
            dqDate: dqDate
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);
                var date = results.date;
                date = tools.ftime(date);
                $(_pageId + " .date").html(date);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        jymm = "";
        $(_pageId + " #fund_sname").html("--");
        $(_pageId + " #fund_code").html("--");
        $(_pageId + " #redconfirm_days").html("--");
        $(_pageId + " #fundvolbalance_mode1").html("--");
        $(_pageId + " .date").html("--");
        $(_pageId + " .modify_box .item .icon").removeClass("active");
        $(_pageId + " #confirm").addClass("no_active");
        _endflag_method = "";
        _old_endflag_method = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
