// 交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#fixedInvestment_stopResults ";
        _pageUrl = "fixedInvestment/stopResults";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var chooseData;
    let weekList = ['','周一','周二','周三','周四','周五']; //每周定投日期
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        let userInfo = ut.getUserInf();
        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
        let bankNameRemark = userInfo.bankName + '(尾号' + bankAcct + ')';
        chooseData = appUtils.getPageParam();
        if(chooseData.paymethod == '0'){
            //晋金宝
            $(_pageId + " .paymethod").text('晋金宝');
        }else{
            $(_pageId + " .paymethod").text(bankNameRemark);
        }
        $(_pageId + " .nextdate").text(setTime(chooseData.investcycle,chooseData.investdate));
        // $(_pageId + " .nextdate").text(showInfo.nextdate);
        $(_pageId + " .money").text(tools.fmoney(chooseData.investmoney));
        $(_pageId + " .prod_sname").text(chooseData.fundname);
        $(_pageId + " .enddate").text(chooseData.enddate);
        $(_pageId + " .exedate").text(chooseData.exedate);
        // paymethod

    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .btn_next"), function () {
            pageBack();
        });
    }
 
    function setTime(investcycle,investdate){
        let str
        if(investcycle == '2'){
            str = '每月 ' + investdate + '日'
        }else if(investcycle == '0'){
            str = '每周 ' + weekList[investdate]
        }else{
            str = '每两周 ' + weekList[investdate]
        }
        return str;
    }
    function pageBack() {
        appUtils.setSStorageInfo("querystatus", '1');
        appUtils.pageBack();
    }
    function destroy() {
    	tools.recordEventData('4','destroy','页面销毁');
    }
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
