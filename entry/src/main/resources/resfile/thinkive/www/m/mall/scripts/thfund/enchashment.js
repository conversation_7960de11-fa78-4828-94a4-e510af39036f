// 晋金宝取现
define(function (require, exports, module) {
    var gconfig = require("gconfig");
    var appUtils = require("appUtils"),
        putils = require("putils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        monkeywords = require("mall/scripts/common/moneykeywords"),
        global = gconfig.global,
        tools = require("../common/tools"),
        _pageId = "#thfund_enchashment";
    var ut = require("../common/userUtil");
    var bankCard = "";
    var bankName = "";
    var jymm = "";
    var item_value = "";
    var timer = null; //计时器
    var i = 120; //倒计时长
    var Millisecond;
    var withdraw_vol; //可取现金额
    var sendMobile = '';
    var useInfo;

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #inputspanid span").addClass("unable").css({color: "#999999"});//默认输入框失去焦点
        //现金宝资产查询101901
        reqFun101901()
        initYanZma();
        showTime();
        useInfo = ut.getUserInf();
        sendMobile = useInfo.bankReservedMobile;


        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.removeAttr("class");
        $yzm.html("获取验证码");
        i = 120;
        window.clearInterval(timer);
        // 解禁系统键盘
        common.systemKeybord();
        //获取可用金额
        // service.limitMoney({}, moneyBack);
        //查询个人资产信息
        // getUserInfo();
        $(_pageId + " #bankName").empty();
        //用户编号
        setBankCardInfo();

        tools.getPdf("prod","000709", "4"); //获取协议


    }

    function bindPageEvent() {

        //2017-7-10 jiaxr 添加客服功能
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "thfund/enchashment";
            tools.saveAlbum("thfund/enchashment",param)
        });

        //隐藏后呼出交易密码
        /*appUtils.bindEvent($(_pageId + " .password_input"), function() {
            //键盘事件
            passboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_enchashment";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });*/
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //输入金额显示数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            moneyboardEvent();
            $(_pageId + " #qxje").val('');
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_enchashment";
            param["eleId"] = "qxje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        // 验证码输入控制
        appUtils.bindEvent($(_pageId + " #yzm"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function (event) {
            pageBack();

        });

        //取现方式 普通取现
        appUtils.bindEvent($(_pageId + " #method1"), function () {
            var chencked = $(_pageId + " #radio_1").attr("checked");
            $(_pageId + " .qxWarn").show();
            $(_pageId + " #nextStep").html("下一步");
            $(_pageId + " #nextStep").attr("style", "");
            $(_pageId + " #radio_1").attr("checked", "checked");
            $(_pageId + " #radio_2").removeAttr("checked");
        }, "click");

        //实时取现
        appUtils.bindEvent($(_pageId + " #method2"), function () {
            var chencked = $(_pageId + " #radio_2").attr("checked");
            $(_pageId + " .qxWarn").hide();
            $(_pageId + " #radio_2").attr("checked", "checked");
            $(_pageId + " #radio_1").removeAttr("checked");
        }, "click");
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #rechargeInfo").empty();
        });
        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });
        //点击发送验证吗事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            monkeywords.flag = 0;
            var param = {};
            param["funcNo"] = "50211";
            require("external").callMessage(param);
            var curVal = $(_pageId + " #qxje").val();
            var moneys = curVal.replace(/,/g, "");
            $(_pageId + " #qxje").val(tools.fmoney(moneys));
            $(_pageId + " #inputspanid span").text(tools.fmoney(moneys));
            //可取现金额未查到不能取现
            if (!withdraw_vol || withdraw_vol <= 0) {
                layerUtils.iAlert("可取现金额不足");
                return;
            }
            //取现金额的判断
            if (!validatorUtil.isMoney(moneys)) {
                layerUtils.iMsg(-1, "请输入正确的取现金额！");
                return;
            }
            if (parseFloat(moneys) == 0) {
                layerUtils.iAlert("请输入正确的取现金额！");
                return;
            }
            var type;
            if($(_pageId + " #radio_1").attr("checked") == "checked") { // 普通取现
                type = common.sms_type.fundNormalEchash; // 普通取现
            } else {
                type = common.sms_type.fundRealTimeEchash; //实时取现
            }
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                window.clearInterval(timer);
                //获取验证码
                var param = {
                    mobile_phone: sendMobile,
                    type: type,
                    mobile_type: "2",
                    send_type: "0"
                };
                sendPhoneCode(param);
            }
        });

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });
        //点击下一步
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            monkeywords.flag = 0;
            var param = {};
            param["funcNo"] = "50211";
            require("external").callMessage(param);
            var curVal = $(_pageId + " #qxje").val();
            var moneys = curVal.replace(/,/g, "");
            $(_pageId + " #qxje").val(tools.fmoney(moneys));
            $(_pageId + " #inputspanid span").text(tools.fmoney(moneys));
            var classname = $(_pageId + " #input_radio2").attr("checked"); // 判断是否签署协议
            $(_pageId + " #rechargeInfo").empty();
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            //可取现金额未查到不能取现
            if (!withdraw_vol || withdraw_vol <= 0) {
                layerUtils.iAlert("可取现金额不足");
                return;
            }
            var yzmState = $(_pageId + " #getYzm").attr("data-state");
            var inputYzm = $(_pageId + " #yzm").val();
            if (yzmState == "true") {
                layerUtils.iAlert("您还未获取验证码！");
                return;
            }
            if (inputYzm.length < 6) {
                layerUtils.iAlert("请确定您的验证码输入的格式是否正确！");
                return;
            }
            //取现金额的判断
            if (!validatorUtil.isMoney(moneys)) {
                layerUtils.iMsg(-1, "请输入正确的取现金额！");
                return;
            }
            if (parseFloat(moneys) == 0) {
                layerUtils.iAlert("请输入正确的取现金额！");
                return;
            }
            if ($(_pageId + " #radio_2").attr("checked")) { // 实时取现
                if (parseFloat(item_value) < parseFloat(moneys)) {
                    layerUtils.iAlert("当日实时取现限额" + item_value + "元，请选择普通取现。");
                    return;
                }
            }
            if (parseFloat(moneys) > parseFloat(withdraw_vol)) {
                layerUtils.iAlert("可取现金额为:" + withdraw_vol + "元");
                return;
            }


            $(_pageId + " #rechargeInfo").append("使用<em>尾号" + useInfo.bankAcct.substr(-4) + " " + useInfo.bankName + "卡，</em>取现<em>" + tools.fmoney(moneys) + "</em>元");
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .pop_layer").show();
            //键盘事件
            passboardEvent();
            monkeywords.flag = 0;
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_enchashment";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //点击确定密码
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            queding_sj(jymm);
        });

    }

    //获取语音验证码
    function getCodeOFTalk() {
        var param = {
            mobile_phone: sendMobile,
            type: common.sms_type.fundRealTimeEchash,
            mobile_type: "2",
            send_type: "1"
        }
        service.reqFun199001(param, function (data) {
            if (data.error_no == "0") {
                var result = data.results;
                var talk_mobile = result[0].orgphone;
                var $dd = "晋金财富将致电您的手机语音告知验证码";
                $(_pageId + " #talkCode").html($dd);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }

    //初始化语音验证码
    function initYanZma() {
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
    }

    function queding_sj(jymm1) {

        guanbi();
        $(_pageId + " .pop_layer").hide();
        if (jymm1.length != 6) {
            //禁用标签的事件
            $(_pageId + " #rechargeInfo").empty();
            layerUtils.iMsg(-1, "请确定您的交易密码格式正确！");
            return;
        }
        $(_pageId + " #nextStep").html("取现中...");
        $(_pageId + " #nextBtn").css("pointer-events", "none");//禁用标签的事件
        //进行取现
        var trans_amt = $(_pageId + " #qxje").val(); //交易金额
        trans_amt = trans_amt.replace(/,/g, "");
        var inputYzm = $(_pageId + " #yzm").val();
        var param = {
            trans_amt: trans_amt,
            trans_pwd: jymm1,
            sms_mobile: useInfo.bankReservedMobile,
            sms_code: inputYzm,
            fund_code: "000709",
            acct_no: useInfo.fncTransAcctNo,
            agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
        };

        //交易密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no == "0") {
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                userChujin(param);
            } else {
                layerUtils.iLoading(false);
                $(_pageId + " #nextStep").html("下一步");
                $(_pageId + " #nextBtn").css("pointer-events", "auto"); //禁用标签的事件
            }
        }, {isLastReq: false});

    }

    //进行取现 出金
    function userChujin(param) {
        var chencked = $(_pageId + " #radio_1").attr("checked");
        //验证码重置
        var $code = $(_pageId + " #getYzm");
        window.clearInterval(timer);
        i = 120;
        $code.attr("data-state", "true"); //点击能发送
        $code.addClass("sending");
        $code.removeAttr("class");
        $code.html("重新获取验证码");
        $(_pageId + " #yzm").val("");
        initYanZma();
        $(_pageId + " #talkCode").show();
        //取消禁用禁用标签的事件
        $(_pageId + " #nextBtn").css("pointer-events", "auto");
        $(_pageId + " #qxfs").css("pointer-events", "auto");
        $(_pageId + " #nextStep").html("下一步");
        //设置密码框为不显示
        $(_pageId + " #jymm").val("");
        $(_pageId + " #rechargeInfo").empty();
        $(_pageId + " .pop_layer").hide();
        if (chencked == "checked") {
            //普通取现
            service.reqFun106007(param, function (data) {
                enchashCallback(data, "pt");
            })
        } else {
            //实时取现
            service.reqFun106006(param, function (data) {
                enchashCallback(data, "ss");
            })
        }
    }

    //取现回调函数
    function enchashCallback(data, type) {
        if (data.error_no == "0") {
            var result = (data.results)[0];
            result.type = type;
            appUtils.pageInit("thfund/enchashment", "thfund/echashResult", result);
        } else {
            layerUtils.iLoading(false);
            //禁用标签的事件
            layerUtils.iAlert(data.error_info);
        }
    }

    function setBankCardInfo() {
        var bankCard = useInfo.bankAcct;
        $(_pageId + " #bankName").append(useInfo.bankName + " <span id='bankCardNum'>" + bankCard.substr(0, 4) + "  ****  ****  " + bankCard.substr(-4) + "</span>");
    }

    //关闭键盘
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i;
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);

    }

    /**
     * 发送手机验码
     * */
    function sendPhoneCode(param) {
        service.reqFun199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                timer = setInterval(function () {
                    shows();
                }, 1000);
            } else {
                layerUtils.iAlert(error_info);
            }
            $(_pageId + " #talkCode").show();
        });
    }

    /**
     * 显示读秒
     * */
    function shows() {
        var $code = $(_pageId + " #getYzm");
        $code.attr("data-state", "false"); //点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.addClass("sending");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.attr("data-state", "true"); //点击能发送
            $code.addClass("sending");
            $code.removeAttr("class");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();
        }
    }


    //现金宝资产查询101901
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                withdraw_vol = results.withdraw_vol;
                $(_pageId + ' #withdraw_vol').html(tools.fmoney(results.withdraw_vol));
            } else {
                $(_pageId + ' #withdraw_vol').html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #nextStep").html("下一步");
        //禁用标签的事件
        $(_pageId + " #nextBtn").css("pointer-events", "auto");
        /*$(_pageId+" #getBack").css("pointer-events","auto");*/
        guanbi();
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #qxje").val("");
        $(_pageId + " #yzm").val("");
        $(_pageId + ' #weihao').hide();
        $(_pageId + " #input_radio2").removeAttr("checked", "checked");
        withdraw_vol = "";
    }

    function pageBack() {
        monkeywords.flag = 0;
        appUtils.pageBack();
    }

    //金额输入数字键盘
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #qxje"),
            endcallback: function () {
                var curVal = $(_pageId + " #qxje").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #qxje").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #qxje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #qxje").val(curVal.substring(0, curVal.length - 1));
                    return;
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #qxje").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #qxje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            }
        })
    }

    //密码输入数字键盘
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                //						queding_sj(jymm);
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i;
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //显示收益日期信息
    function showTime() {
        service.reqFun102008({type: "3"}, function (data) {
            var error_no = data.error_no;
            if (error_no == "0") {
                var beforeDate = data.results[0].afterDate;
                var afterDate = data.results[0].beforeDate;
                beforeDate = common.dateStyle2(beforeDate);
                afterDate = common.dateStyle2(afterDate);
                $(_pageId + " #eTime").text(afterDate);
                $(_pageId + " #lTime").text(beforeDate);
            }
        })
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
