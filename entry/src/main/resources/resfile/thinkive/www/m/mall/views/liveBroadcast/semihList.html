<!-- app内直播页面 进入列表详情页 -->
<div class="page" id="liveBroadcast_semihList" data-pageTitle="直播间列表页" data-isSaveDom="false" data-refresh="true"
     style="-webkit-overflow-scrolling : touch;">
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a herf="javascript:void(0)" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center liveBroadcast_semihList_name">上个页面带入</h1>
            </div>
        </header>
        <article style="padding-bottom: 0rem;">
            <div id="v_container_productList">
                <div class="visc_wrapper" id="v_wrapper_productList" data-iscrollPageId="Products_productList">
                    <div class="visc_scroller">
                        <div class="visc_pullDown" style="display:none;">
                            <span class="visc_pullDownIcon"></span>
                            <div class="visc_pullDownDiv">
                                <span class="visc_pullDownLabel">下拉加载上一页</span><br />
                                时间更新于：<span class="visc_pullDownTime"></span>
                            </div>
                        </div>
                        <!-- 页面展示 -->
                        <div class="list m_padding_10_10 m_paddingTop_0">
                
                        </div>
                        <div class="visc_pullUp" style="display:none;">
                            <span class="visc_pullUpIcon" style="display:none;"></span>
                            <div class="visc_pullUpDiv" style="display:none;">
                                <span class="visc_pullUpLabel">上拉加载下一页</span><br />
                                时间更新于：<span class="visc_pullUpTime"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </article>
    </section>
</div>