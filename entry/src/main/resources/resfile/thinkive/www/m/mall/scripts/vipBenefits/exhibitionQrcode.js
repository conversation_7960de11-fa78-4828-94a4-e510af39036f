// 会员福利 我的二维码
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#vipBenefits_exhibitionQrcode ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var ut = require("../common/userUtil");

    function init() {
		$(_pageId + " #code").html("");
		qrcode();
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " #getBack", function () {
        	pageBack();
        });
    }


	//动态生成二位码
	function qrcode() {
		var mobile = ut.getUserInf().mobileWhole;
		share(mobile, "7");
	}

	function share(mobile, share_type) {
		// 简化分享链接
		var query_params = {};
		query_params["registered_mobile"] = mobile;
		query_params["tem_type"] = share_type;
		service.reqFun102012(query_params, function (data) {
			var error_no = data.error_no,
				error_info = data.error_info;
			if (error_no == "0") {
				if (data.results != undefined && data.results.length > 0) {
					mobile = common.desEncrypt("mobile", mobile);//加密
					var result = data.results[0];
					var share_url = result.share_url;
				}
				if (validatorUtil.isEmpty(share_url)) {
					share_url = global.link;
				}
				if (share_url.indexOf("?") != -1) {
					share_url = share_url + "&mobile=" + mobile;
				} else {
					share_url = share_url + "?mobile=" + mobile;
				}
				var params = {};
				params["url"] = share_url;
				// service.simplifyURL(params, function (data) {
				// 	var error_no = data.error_no,
				// 		error_info = data.error_info;
				// 	if (error_no == "0") {
				// 		share_url = data.results[0].shortUrl;
				// 		var str = share_url + "?mobile=" + mobile + "&type=6";
				require("../common/jquery.qrcode.min");
						$(_pageId + " #code").qrcode({
							render : "canvas", //设置渲染方式，有table和canvas
							text : share_url, //扫描二维码后自动跳向该链接
							width : 200, //二维码的宽度
							height : 200, //二维码的高度
							imgWidth: 60,
							imgHeight: 60,
							src: '../mall/images/icon_app.png'
						});
				// 	} else {
				// 		layerUtils.iAlert(error_info);
				// 	}
				// });
			} else {
				layerUtils.iAlert(error_info);
			}
		});

	}

	function destroy() {

    }
	function pageBack() {
    	appUtils.pageBack();
	}
    var exhibitionQrcode = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
		"pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = exhibitionQrcode;
});