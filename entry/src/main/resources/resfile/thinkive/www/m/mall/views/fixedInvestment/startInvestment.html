<!-- 开始定投页面 -->
<div class="page" id="fixedInvestment_startInvestment" data-pageTitle="定投" data-isSaveDom="false"  data-refresh="true" style="-webkit-overflow-scrolling : touch;">
    <section id="product" class="main fixed" data-page="home" style="padding-bottom: 0.5rem">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回" style="z-index: 99"><span>返回</span></a>
                <h1 class="text_gray text-center"></h1>
                <a id="kefu" operationType="1" operationId="kefu" operationName="客服" href="javascript:void(0)" class="coustomer-service-icon">
                    <img src="./images/customerService.png">
                </a>
            </div>
        </header>
        <article style="padding-top: 0;padding-bottom: 0.5rem">
            
            <div class="product_content right_icon" operationType="1" operationId="product_content" operationName="产品详情">
                <div class="product_name"><span class="prod_sname">--</span><span class="fund_code_box" style="display:none">&nbsp;&nbsp;(<span class="fund_code"></span>)</span>
                </div>
                <div class="product_type"><span class="fund_type_name"></span><span class="placeholder" style="display:none"> | </span><span
                        class="risk_level_name"></span></div>
                <i class="icon_right"></i>
            </div>
            <div class="product_buy" id="fundMoneyBox">
                <p>
                    <span>每期定投金额(元)</span>
                    <span class="m_title_right calculator color_0770ff m_font_size14 m_marginTop_02" operationType="1" operationId="calculator" operationName="定投计算器">定投计算器</span>
                    <a href="javascript:void(0)" id="financialCalculator" class="startInvestment_financial-calculator-btn financial-calculator-btn">理财计算器</a>
                </p>
                <div style="height:0.4rem;position: relative"><i class="symbol">￥</i>
                    <span class="buy_money"  id='inputspanid' operationType="1" operationId="inputspanid" operationName="输入金额弹出数字键盘"><span class="unable" text=''></span></span>
                    <input  id="czje" style="display: none;" readonly="readonly">
                </div>
            </div>
            <div class="buy_rule g_fontSize14 rate_text" style="padding:0 0.2rem;"></div>
            <div class="buy_rule g_fontSize14 showLimit" style="padding:0 0.2rem;"></div> 
            <div class="choosePay bgWhite m_marginTop_10" operationType="1" operationId="choosePay" operationName="选择支付方式">
                <div class="vertical_center m_font_size14 m_text_darkgray">支付方式</div> 
                <!-- <div id="fh" class="m_paddingTop_300  m_paddingRight_10 m_text_right m_text_gray empty ">
                        <span class="color_000 payMethodName">晋金宝</span>     <br>
                        <span class="payMethodRemark"></span>
                </div> -->
                <div class="payList">
                    <div class="pay_mode pay_method_title chooseJjb" payMethod="0" id="amount_enough" operationType="1" operationId="amount_enough_jjb" operationName="晋金宝">
                        <div class="main_flxe">
                            <li class="img active">
                            </li>
                            <li >
                                <h4>晋金宝</h4>
                                <span class="pay_bank">可用金额：<em class="money">--</em>元</span>
                            </li>
                        </div>
                        <div class="jjs_yue" style="display: none; margin-bottom:0;line-height: 0.2rem;">
                            <span class="m_font_size12">若余额不足，可从“晋金所晋金宝”转入</span>
                            <a class="jjs_into m_font_size12" operationType="1" operationId="jjs_into" operationName="户转" style="color: #319ef2;float:right">转入</a>
                        </div>
                    </div>
                    <div class="pay_mode main_flxe pay_method_title chooseBank" payMethod="1" style="display:none;" id="amount_enough" operationType="1" operationId="amount_enough_bank" operationName="银行卡">
                        <li class="img">
                        </li>
                        <li>
                            <h4 class="backName">--</h4>
                            <!-- <span class="">限额<em class="single_limit">--</em></span> -->
                            <p class="m_font_size14">单笔限额<span class="single_limit" style="margin-right: 0.2rem">--</span>单日限额<span
                                class="day_limit">--</span></p>
                        </li>
                        
                    </div>
                </div>
            </div>
            <div class="grid_03 grid_02 grid" style="display: none;">
                <div class="ui field text rounded input_box2" id="yzmBox">
                    <label class="short_label2">验证码</label>
                    <input custom_keybord="0" id="verificationCode" type="tel" :adjust-position="false" maxlength="6" class="ui input code_input" placeholder="">
                    <a id="getYzm" operationType="1" operationId="getYzm" operationName="获取验证码" data-state="true" style="background-color: rgb(193, 227, 182);">获取验证码</a>
                </div>
                <div class="finance_det recharge_det">
                    <dl class="bank_limit">
                        <dt></dt>
                        <dd id="weihao" style="display:none"></dd>
                        <dd></dd>
                    </dl>
                </div>
            </div>
            <div class="cycleClick asset_bonus bgWhite m_marginTop_10 m_padding_10" operationType="1" operationId="cycleClick" operationName="选择定投日期">
                <div class="m_font_size16 m_text_darkgray main_flxe">定投周期</div> 
                <div id="fh" class="m_paddingTop_300 right_icon m_paddingRight_10 m_text_right m_text_gray empty ">
                        <span class="investmentText">--</span>   
                </div>
            </div>
            <div class="m_padding_10 m_text_gray">预计下次扣款时间:<span class="nextTimes"></span>，遇节假日顺延。</div>
            <div class="agreement">
                <div class="agreement2"><i></i>
                    <div style="margin-left: 0.2rem;">我已仔细阅读并同意签署<a class="deal_box"><a href="javascript:void(0);" class="xy"></a></a></div>
                </div>
            </div>
        </article>
        <div class="thfundBtn">
            <div class="buy" operationType="1" operationId="buy" operationName="下一步">
                <span class="f18 purchaseBtn">下一步</span>
            </div>
        </div>
    </section>
    <div class="pop_layer"  style="display: none"></div>
    <!-- <div class="action_sheet_wrapper" style="display: none" id="payMethod">
        <div class="pay_login  slideup in">
            <div class="model_header">
                <div class="close_btn" id="close_payMethod"></div>
                <h1 class="model_title">请选择支付方式</h1>
            </div>
            <div class="model_content">
                <div class="pay_mode pay_method_title not_enough" id="insufficient_fund">
                    <div class="img"></div>
                    <h3>晋金宝余额不足，<a class="recharge">立即充值</a></h3>
                    <span class="pay_bank">可用金额：<em class="money">--</em>元</span>
                </div>
                <div class="pay_mode pay_method_title active" payMethod="0" id="amount_enough">
                    <div class="img"></div>
                    <h3>晋金宝</h3>
                    <span class="pay_bank">可用金额：<em class="money">--</em>元</span>
                </div>
            </div>
            
            <div class="model_bottom">
                <div class="button">确定</div>
            </div>
        </div>
    </div> -->
    <div class="action_sheet_wrapper " style="display: none" id="cycleModel">
        <div class="pay_login">
            <div class="model_header">
                <div class="close_btn" id="closeCycle" operationType="1" operationId="closeCycle" operationName="关闭周期付弹框"></div>
                <h1 class="model_title">请选择定投周期</h1>
                <div class="determine" operationType="1" operationId="determine" operationName="确定">确定</div>
            </div>
        </div>
        <div class="chooseCycle">
            <ul class="listLeft">
                <li id="2" class="active" operationType="1" operationId="chooseCycleMonth" operationName="每月">每月</li>
                <li id="0" operationType="1" operationId="chooseCycleWeek" operationName="每周">每周</li>
                <li id="1" operationType="1" operationId="chooseCycleWeeks" operationName="每两周">每两周</li>
            </ul>
            <ul class="listRight">

            </ul>
        </div>
    </div>
    <div class="action_sheet_wrapper" style="display: none" id="agreementList">
        <div class="pay_login slideup in">
            <div class="model_header">
                <div class="close_btn" id="agree_closeBtn"></div>
                <h1 class="model_title">相关协议与说明</h1>
            </div>
            <div class="model_content">
                <div class="pay_agg" class="deal_box">
                    <a class="xy">《电子签名约定书》</a>
                    <a class="xy">《电子签名约定书》</a>
                    <a class="xy">《电子签名约定书》</a>
                    <a></a>
                </div>
            </div>
        </div>
    </div>
    <div class="password_box" style="display: none;position: absolute;top: 0;left: 0;right: 0;margin: auto;">
        <div class="password_inner slidedown in" style="    z-index: 9999;">
            <a href="javascript:void(0);" id="close" operationType="1" operationId="close" operationName="关闭交易密码输入框" class="close_btn"></a>
            <h4>请输入<span>6</span>位交易密码</h4>
            <h6 id="rechargeInfo">
            	使用<em class="payMethodName">晋金宝</em>，定投<span id="recharge_name">--</span><em id="recharge_money">--</em>元，
                <span id="cycle"></span>
            </h6>
            <div class="password_input" operationType="1" operationId="password_input" operationName="呼出交易密码">
                <span id="span00"></span>
                <span id="span01"></span>
                <span id="span02"></span>
                <span id="span03"></span>
                <span id="span04"></span>
                <span id="span05"></span>
                <input type="text" id="jymm" maxlength="6" style="display:none;" readonly="readonly">
            </div>
            <a href="javascript:void(0);" id="queDing" operationType="1" operationId="queDing" operationName="确定" class="sure_btn text-center">确定</a>
        </div>
    </div>
    <div class="van-overlay" style="display:none">
        <div class="tip_rules"  style="padding:0 0.4rem;">
            <div class="rules_box slideup in pdfCenter_dio" style="width: 100%;overflow-y: auto;max-height:70%;">
                <h5> 
                    <span class="pdf_name">基金产品资料概要</span>
                    <a href="javascript:void(0);" id="paf_new_close" class="close_btn"></a>
                </h5>
                <div class="rules_list" style="padding: 0.15rem;height: 3rem;overflow-y: scroll">
                    <div id="iframe_pdf"></div>
                </div>
                <div class="text-center pdf_remark" style="display:none">提示：您可通过截屏方式留存相关信息</div>
                <div class="grid_02 iframe_pdf_bottom">
                    <a href="javascript:void(0)" class="vant_button ui button block rounded btn_01 disable" id="sure">我知道了</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 协议相关弹窗 -->
    <div class="agreement_layer display_none">
        <div class="agreement_popup in">
            <div class="agreement_popup_header">
                <div class="new_close_btn"></div>
                <h3>相关协议</h3>
            </div>
            <ul class="agreement_list flex vertical_line"></ul>
        </div>
    </div>
</div>

