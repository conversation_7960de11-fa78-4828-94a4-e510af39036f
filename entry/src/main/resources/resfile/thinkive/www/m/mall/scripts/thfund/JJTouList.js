// 晋金投列表
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageUrl = "thfund/JJTouList",
        _pageId = "#thfund_JJTouList ";
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    var cur_page = 1;
    var currentPage;
    var totalPages;
    var isEnd = false;
    var num_per_page = "10";
    var openFlag;
    function init() {
        let param = appUtils.getSStorageInfo("jjsShowData"); //获取缓存中的JJS客户信息
        openFlag = param.isBind;//是否是基金用户
        $(_pageId + ".olay").remove();
        chiyouz_z(false);
    }

    //持有查询
    function chiyouz_z(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        service.reqFun177005({
            func_no: "500004",
            page: cur_page,
            numPerPage: num_per_page,
            period_type: "1"
        }, function (datas) {
            if (datas.error_no == 0) {
                var str = "";
                if (datas.results.length > 0) {
                    currentPage = datas.results[0].currentPage; //当前页数
                    totalPages = datas.results[0].totalPages; //总页数
                    var detailParams = datas.results[0].data
                    if (detailParams && detailParams.length > 0) {
                        if (currentPage <= totalPages) {
                            for (var i = 0; i < detailParams.length; i++) {
                                datas.results[0].data[i].product_detail = "";
                                var is_impawn = datas.results[0].data[i].is_impawn; //是否可质押 0否 1是
                                var alreadyImpawned = eval(datas.results[0].data[i].alreadyImpawned);//是否已质押  0否  1是
                                var winding_date = datas.results[0].data[i].winding_date; //产品到期日
                                var incomeunit = datas.results[0].data[i].incomeunit; //收益率
                                var product_abbr = datas.results[0].data[i].product_abbr; //产品简称
                                var total_vol = datas.results[0].data[i].total_vol; //持有份额

                                var investment_horizon = datas.results[0].data[i].investment_horizon; //期限
                                var establish_date = datas.results[0].data[i].establish_date; //产品成立日
                                var product_id = datas.results[0].data[i].product_id; //产品id
                                var is_transfer = datas.results[0].data[i].is_transfer; //是否转让产品  1是
                                var prod_code = datas.results[0].data[i].prod_code; //产品编号
                                var transfer_begin_date = datas.results[0].data[i].transfer_begin_date;
                                var transfer_end_date = datas.results[0].data[i].transfer_end_date;
                                var liqu_date = datas.results[0].data[i].liqu_date;
                                var busi_code = datas.results[0].data[i].busi_code;
                                var total_vol = datas.results[0].data[i].total_vol;
                                var assignee_ack_amt = datas.results[0].data[i].assignee_ack_amt;
                                var trans_serno = datas.results[0].data[i].trans_serno;
                                var buy_date = datas.results[0].data[i].buy_date.substr(0, 4) + "-" + datas.results[0].data[i].buy_date.substr(4, 2) + "-" + datas.results[0].data[i].buy_date.substr(-2);
                                if (assignee_ack_amt == "" || assignee_ack_amt == null) {
                                    assignee_ack_amt = total_vol;
                                }
                                var due_profit = datas.results[0].data[i].due_profit;
                                var crt_date = datas.results[0].data[i].crt_date;
                                var reg_flag = datas.results[0].data[i].reg_flag;
                                var posting_flag = datas.results[0].data[i].posting_flag;// 02是通华结算，01是民生结算
                                var income;
                                if (crt_date && establish_date && crt_date.replace(/-/g, "") > establish_date.replace(/-/g, "")) {
                                    income = due_profit;
                                } else {
                                    income = incomeunit;
                                }
                                var payMethodStr = "";
                                if (openFlag == "1") { // 基金用户
                                    //cashpayType字段(可能为空或null):
                                    //B:余额；F:基金; 无记录按原默认规则,有基金户兑付到基金,没有基金户兑付到余额
                                    var cashpayType = datas.results[0].data[i].cashpayType;
                                    var cashpayTypeStr = "";

                                    if (cashpayType == 'B') {
                                        cashpayTypeStr = "晋金余额";
                                    } else {
                                        cashpayTypeStr = "华安汇财通货币";
                                    }
                                    //通华结算方式的兑付方式为到宝，不可修改
                                    if (posting_flag == "02") {
                                        payMethodStr = `<p class="flex"><span class="detail" style="color:#4D90FE">交易详情</span><span>兑付至：<span>华安汇财通货币</span></span></p>`;
                                    } else {
                                        payMethodStr = `<p class="flex"><span class="detail" style="color:#4D90FE">交易详情</span><span>兑付至：<span>${cashpayTypeStr}</span><span class='changePayMethod' data-type='th'>修改</span></span></p>`;
                                    }

                                } else {
                                    payMethodStr = `<p class="flex"><span class="detail" style="color:#4D90FE">交易详情</span><span>兑付至：<span>晋金余额</span></span></p>`;
                                }

                                //已质押产品
                                if (alreadyImpawned == "1") {
                                    str += `
                                    <div class='jjt_trade_box'>
                                        <span id='productInfo' style='display: none;'>${JSON.stringify(datas.results[0].data[i])}</span>
                                        <div class="flex" style="border-bottom: 1px solid #d2e3e8;">
                                            <div>
                                                ${is_transfer == '1' ? `<span class="icon_zhuan">转</span>` : ``}
                                                <span class="m_font_size16 m_bold">${product_abbr}</span>
                                            </div>
                                            <div>
                                                <span class="m_font_size16 m_bold">可质押</span><span class='m_text_red due_profit'>${income}%</span>
                                            </div>
                                        </div>
                                        <div style="padding: 0.1rem 0;">
                                            <p>产品编号：<span>${prod_code}</span></p>
                                            <p class="flex">
                                                ${is_transfer == '1' ? `<span>购买金额：<span>${assignee_ack_amt}元</span></span>` : `<span>购买金额：<span>${total_vol}元</span></span>`}
                                                <span>期限：<span>${investment_horizon}天</span></span>
                                            </p>
                                            <p class="flex">
                                                <span>持有份额：<span>${total_vol}元</span></span><span>起息日期：<span>${crt_date}</span></span>
                                            </p>
                                            <p class="flex">
                                                <span>产品到期日：<span>${winding_date}</span></span>
                                                ${is_transfer == "1" ? `` : `<span>产品成立日：<span>${establish_date}</span></span>`}
                                            </p>
                                            ${payMethodStr}
                                        </div>
                                    </div>`;
                                } else {
                                    //可质押产品
                                    if (is_impawn == "1") {
                                        str += `
                                    <div class='jjt_trade_box'>
                                        <span id='productInfo' style='display: none;'>${JSON.stringify(datas.results[0].data[i])}</span>
                                        <div class="flex" style="border-bottom: 1px solid #d2e3e8;">
                                            <div>
                                                ${is_transfer == '1' ? `<span class="icon_zhuan">转</span>` : ``}
                                                <span class="m_font_size16 m_bold">${product_abbr}</span>
                                            </div>
                                            <div>
                                                <span class="m_font_size16 m_bold">可质押</span><span class='m_text_red due_profit'>${income}%</span>
                                            </div>
                                        </div>
                                        <div style="padding: 0.1rem 0;">
                                            <p>产品编号：<span>${prod_code}</span></p>
                                            <p class="flex">
                                            ${is_transfer == '1' ? `<span>购买金额：<span>${assignee_ack_amt}元</span></span>` : `<span>购买金额：<span>${total_vol}元</span></span>`}
                                                <span>期限：<span>${investment_horizon}天</span></span>
                                            </p>
                                            <p class="flex">
                                                <span>持有份额：<span>${total_vol}元</span></span><span>起息日期：<span>${crt_date}</span></span>
                                            </p>
                                            <p class="flex">
                                                <span>产品到期日：<span>${winding_date}</span></span>
                                                ${is_transfer == "1" ? `` : `<span>产品成立日：<span>${establish_date}</span></span>`}
                                            </p>
                                            ${payMethodStr}
                                        </div>
                                    </div>`;
                                    } else {
                                        str += `
                                    <div class='jjt_trade_box'>
                                        <span id='productInfo' style='display: none;'>${JSON.stringify(datas.results[0].data[i])}</span>
                                            <div class="flex" style="border-bottom: 1px solid #d2e3e8;">
                                                <div>
                                                    ${is_transfer == '1' ? `<span class="icon_zhuan">转</span>` : ``}
                                                    <span class="m_font_size16 m_bold">${product_abbr}</span>
                                                </div>
                                                <div>
                                                    <span class='m_text_red m_font_size16 m_bold due_profit'>${income}%</span>
                                                </div>
                                        </div>
                                        <div style="padding: 0.1rem 0;">
                                            <p>产品编号：<span>${prod_code}</span></p>
                                            <p class="flex">
                                            ${is_transfer == '1' ? `<span>购买金额：<span>${assignee_ack_amt}元</span></span>` : `<span>购买金额：<span>${total_vol}元</span></span>`}
                                                <span>期限：<span>${investment_horizon}天</span></span>
                                            </p>
                                            <p class="flex">
                                                <span>持有份额：<span>${total_vol}元</span></span><span>起息日期：<span>${crt_date}</span></span>
                                            </p>
                                            <p class="flex">
                                                <span>产品到期日：<span>${winding_date}</span></span>
                                                ${is_transfer == "1" ? `` : `<span>产品成立日：<span>${establish_date}</span></span>`}
                                            </p>
                                            ${payMethodStr}
                                        </div>
                                    </div>`;
                                    }

                                }
                            }
                        }
                    } else if (!detailParams) {
                        isEnd = true;
                        $(_pageId + " .new_none").show();
                    }
                    if (detailParams && detailParams.length < num_per_page) {
                        isEnd = true;
                        $(_pageId + " .new_none").show();
                    }
                } else {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                pageScrollInit();
            } else if (datas.error_no == "-1") {
                layerUtils.iAlert("网络繁忙，请稍后重试");
            } else {
                layerUtils.iAlert(datas.error_info);
            }
            layerUtils.iLoading(false);
        }, {
            "isLastReq": false
        });
    }

    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    chiyouz_z(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        if (currentPage && (currentPage < totalPages)) {
                            cur_page += 1;
                            $(_pageId + " .visc_pullUpIcon").show();
                            $(_pageId + " .visc_pullUpDiv").show();
                            chiyouz_z(true);
                        }

                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        if (isEnd) {//可能有当前页为1总页数为0的情况
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }

        //ios上拉拖动卡死问题解决
        var pageTouchTimer = null;
        //var contentHeight = $(_pageId + " #wrapper_w1").height()-570;
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
        
    }


    function bindPageEvent() {
        //持有交易详情
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".jjt_trade_box .detail", function (e) {
            var productInfo = JSON.parse($(this).parents(".jjt_trade_box").find("#productInfo").text());
            e.stopPropagation();
            var product_code = productInfo.prod_code;
            appUtils.pageInit(_pageUrl, "thfund/holdProductTrs", product_code);

        }, 'click');
        //返回键
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }


    function destroy() {
        cur_page = 1;
        $(_pageId + ".olay").hide();
        $(_pageId + " .new_none").hide();
        isEnd = false;
        $(_pageId + " .finance_pro").html("");
    }

    //返回页面
    function pageBack() {
        appUtils.pageInit(_pageUrl, "account/jjsAssets", { openFlag: openFlag });
    }

    var jjtouListModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjtouListModule;
});
