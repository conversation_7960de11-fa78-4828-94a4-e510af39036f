// 历史规模
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageId = "#highEnd_historyScaleList";
    var _pageCode = "highEnd/historyScaleList";
    var _fund_code;
    var isEnd = false;
    var _cur_page = 1;
    var _num_per_page = "20";
    function init() {
        vIscroll = { "scroll": null, "_init": false };
        _fund_code = appUtils.getSStorageInfo("fund_code");
        //获取历史规模
        getHistory(false);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

    }

    //获取历史规模
    function getHistory(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        var params = {
            fund_code: _fund_code,
            cur_page: _cur_page + "",
            num_per_page: _num_per_page + "",
        }
        var callback = function (resultVo) {
            if (resultVo.error_no == "0") {
                var results = resultVo.results;
                var html = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        //空数据处理
                        results[i] = tools.FormatNull(results[i]);


                        var nav_date = results[i].nav_date;
                        if (nav_date != "--") {
                            nav_date = tools.ftime(nav_date.substring(0, 8));
                        }

                 
                        //资产规模
                        var total_assets = results[i].total_assets;
                        if (total_assets != "--") {
                            total_assets = (+total_assets).toFixed(2) + "万元";
                        }


                        html += '<div class="item">' +
                            '<span>' + nav_date + '</span>' +
                            '<span class="">' + total_assets + '</span>' +
                            '</div>';
                    }
                } else if (!results) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                if (results && results.length < _num_per_page) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                // $(_pageId + " #historyContent .list_content").html(html);
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }

            if (isAppendFlag) {
                $(_pageId + " #concent").append(html);
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
            } else {
                $(_pageId + " #concent").html(html);
            }
            pageScrollInit();
        }
        service.reqFun102202(params, callback)
    }

    //上下滑动事件
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    _cur_page = 1;
                    getHistory(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        _cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getHistory(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        if (isEnd) {//可能有当前页为1总页数为0的情况
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }


    function addMinusClass(str) {
        var numClass = "add";

        if (str < 0) {
            numClass = "text_green";
        } else if (str > 0) {
            numClass = "text_red";
        } else {
            numClass = "text_grey";
        }
        return numClass;
    }

    function destroy() {
        _cur_page = 1;
        $(_pageId + " .new_none").hide();
        isEnd = false;
        $(_pageId + " #concent").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
