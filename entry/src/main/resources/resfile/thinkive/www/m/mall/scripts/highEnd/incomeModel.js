// 高端： 收入证明要求
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        publicUtil = require('./publicUtil'),
        _page_code = "highEnd/incomeModel",
        _pageId = "#highEnd_incomeModel ";


    function init() {
        initTab()
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function initTab () {
        var tabNavs = $(_pageId + '.common-tab-nav .nav-item')
        var tabContens = $(_pageId + '.common-tab-contents .content-item')
        publicUtil.tabSwitch(tabNavs, tabContens);
    }

    function destroy() {
        $(_pageId + '.common-tab-nav .nav-item').removeClass("active").eq(0).addClass("active");
        $(_pageId + '.content-item').removeClass("active").eq(0).addClass("active");
    }


    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
