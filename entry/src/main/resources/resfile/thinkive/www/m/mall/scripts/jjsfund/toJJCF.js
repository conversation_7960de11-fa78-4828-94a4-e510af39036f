// 晋金所晋金宝转入晋金财富
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        monkeywords = require("../common/moneykeywords"),
        _pageCode = "jjsfund/toJJCF",
        _pageId = "#jjsfund_toJJCF ";
    var jymm = "";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var sendMobile = '';
    var userInfo;
    var available_vol;
    var sms_mobile = require("../common/sms_mobile");
    var is_exist; // 是否已签约 0 未签约  1：已签约
    var payorg_id;//支付机构
    var pay_type; //支付方式
    var bank_serial_no;

    function init() {
        sms_mobile.init(_pageId);
        userInfo = ut.getUserInf();
        $(_pageId + " #inputspanid span").addClass("unable").css({color: "#999999"});//默认输入框失去焦点
        sendMobile = userInfo.bankReservedMobile;
        tools.getPdf("prod", "000709", "1", "我已阅读并同意签署", "，承诺购买基金行为出于本人真实意愿，已充分了解产品风险和服务内容"); //获取协议
        common.systemKeybord(); // 解禁系统键盘
        reqFun177002();
        setBankCardInfo();
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击发送验证吗事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var money = $(_pageId + " #money").val();
            money = money.replace(/,/g, "");
            if (!validatorUtil.isMoney(money)) {
                layerUtils.iAlert("请输入正确的转入金额");
                return;
            }
            if (parseFloat(money) == 0) {
                layerUtils.iAlert("请输入正确的转入金额");
                return;
            }
            var rst = 0;
            for (var i = 0; i < money.length; i++) {
                if (money[i] == 0) {
                    rst++;
                }
            }
            if (money.length == rst) {
                layerUtils.iAlert("请输入正确的转入金额");
                return;
            }
            if ($(_pageId + " #getYzm").attr("data-state") == "false") {
                return;
            }

            if (is_exist == "0") { //未签约
                var param = {
                    "payorg_id": payorg_id,
                    "pay_type": pay_type,
                    "bank_code": userInfo.bankCode,
                    "bank_acct": userInfo.bankAcct,
                    "bank_reserved_mobile": userInfo.bankReservedMobile,
                    "cert_no": userInfo.identityNum,
                    "bank_name": userInfo.bankName,
                    "sms_type": common.sms_type.jjsInput,
                    "cert_type": "0",
                    "cust_name": userInfo.name
                }

                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }
                });
            } else { //已签约
                //获取验证码
                var param = {
                    "mobile_phone": sendMobile,
                    "type": common.sms_type.jjsInput,
                    "send_type": "0",
                    "mobile_type": "2"
                }
                sms_mobile.sendPhoneCode(param);
            }

        }, 'click');

        //点击确定密码
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            quedingoumai(jymm);
        });

        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").css("display", "none");
            $(_pageId + " #rechargeInfo").empty();
            $(_pageId + " #jymm").val("");
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #money").val('');
            $(_pageId + " #nextBtn").show();
            $(_pageId + " #nextBtn_one").hide();
            $(_pageId + " #xianErXianShi").hide();
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "jjsfund_toJJCF";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });


        // 手机号码输入事件
        appUtils.bindEvent($(_pageId + " #yzm"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        //点击下一步
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            monkeywords.close();
            var money = $(_pageId + " #money").val();
            money = money.replace(/,/g, "");
            var yzmState = $(_pageId + " #getYzm").attr("data-state");
            var inputYzm = $(_pageId + " #yzm").val();
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请您签署协议");
                return;
            }
            if (yzmState == "true") {
                layerUtils.iAlert("您还未获取验证码");
                return;
            }
            if (inputYzm.length < 6) {
                layerUtils.iAlert("请确定您的验证码输入的格式是否正确");
                return;
            }
            if (!validatorUtil.isMoney(money)) {
                layerUtils.iAlert("请输入正确的转入金额");
                return;
            }
            if (parseFloat(money) == 0) {
                layerUtils.iAlert("请输入正确的转入金额");
                return;
            }
            var rst = 0;
            for (var i = 0; i < money.length; i++) {
                if (money[i] == 0) {
                    rst++;
                }
            }
            if (money.length == rst) {
                layerUtils.iAlert("请输入正确的转入金额");
                return;
            }
            if (money.split(".").length < 2) {
                money = money + ".00";
            }
            passboardEvent();
            $(_pageId + " #rechargeInfo").html("从<em>晋金所</em>转入晋金财富<em>" + money + "</em>元");
            var phoneNum = appUtils.getSStorageInfo("mobile");
            $(_pageId + " .pop_layer").show();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "jjsfund_toJJCF";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });


    }

    function destroy() {
        //设置密码框为不显示
        $(_pageId + " #rechargeInfo").html("")
        $(_pageId + " #money").val("");
        $(_pageId + " #yzm").val("");
        sms_mobile.destroy();
        guanbi();
        $(_pageId + " .pop_layer").hide();
        is_exist = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }

    /**********************************************************************************************/

    //进行转入 入金 已签约，本地验证码校验
    function userChongZhi_ff(param) {
        var buycallback = function (data) {
            
            if (data.error_no == "0") {
                var param = data.results[0];
                param['method'] = "toJJCF";
                appUtils.pageInit(_pageCode, "jjsfund/inputResult", param);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        }
        sms_mobile.clear();
        $(_pageId + " #rechargeInfo").empty();
        $(_pageId + " #jymm").val("");
        service.reqFun106001(param, function (data) {
            buycallback(data);
        });
    }

    //进行转入 入金 未签约 银行验证码校验
    function userChong_proto(param) {
        var buycallback = function (data) {
            
            if (data.error_no == "0") {
                var param = data.results[0];
                param['method'] = "toJJCF";
                appUtils.pageInit(_pageCode, "jjsfund/inputResult", param);
            } else {
                layerUtils.iAlert(data.error_info);
            }
            
        }
        sms_mobile.clear();
        $(_pageId + " #rechargeInfo").empty();
        $(_pageId + " #jymm").val("");
        service.reqFun9106001(param, function (data) {
            buycallback(data);
        });
    }

    //点击确定
    function quedingoumai(jymm1) {
        guanbi();
        $(_pageId + " .pop_layer").hide();
        if (jymm1.length != 6) {
            layerUtils.iAlert("请确定您的交易密码格式正确");
            return;
        }
        $(_pageId + " #nextBtn").css("pointer-events", "none"); //禁用标签的事件
        //进行转入
        var trans_amt = $(_pageId + " #money").val();
        trans_amt = trans_amt.replace(/,/g, "");
        var sms_code = $(_pageId + " #yzm").val();
        var param = {
            trans_amt: trans_amt, //交易金额
            trans_pwd: jymm1, //交易密码
            sms_mobile: userInfo.bankReservedMobile,
            sms_code: sms_code,
            fund_code: "000709",
            is_exist: is_exist,
            payorg_id: payorg_id,
            pay_type: pay_type,
            message_code: sms_code,
            bank_code: userInfo.bankCode,
            bank_serial_no: bank_serial_no,
            agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"), //协议签署流水号
            pay_mode: "2",
            check_trans_pwd:"1"
        }
        //交易密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no != "0") {
                $(_pageId + " #nextBtn").css("pointer-events", "none"); //禁用标签的事件
                layerUtils.iLoading(false);
                return;
            }
            var modulus = data.results[0].modulus;
            var publicExponent = data.results[0].publicExponent;
            var endecryptUtils = require("endecryptUtils");
            param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
            if (is_exist == "0") { //未签约
                userChong_proto(param);
            } else {//已签约
                userChongZhi_ff(param);
            }
        }, {isLastReq: false});
    }

    //关闭键盘
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                $(_pageId + " .pop_text").hide();
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #money").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                    return;
                }
                var money = $(_pageId + " #money").val();
                money = money.replace(/,/g, "");
                if (available_vol && parseFloat(money) > parseFloat(available_vol)) {
                    $(_pageId + " #money").val(available_vol);
                }
            },
            keyBoardHideFunction: function () {
            },
            keyBoardFinishFunction: function () {
            }
        })
    }

    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //显示用户账户金额
    function reqFun177002() {
        service.reqFun177002({jjs_cust_no:appUtils.getSStorageInfo("jjs_cust_no")}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var result = (data.results)[0];
                available_vol = result.available_vol;
                $(_pageId + " #available_vol").text(tools.fmoney(result.available_vol || "0.00"));//可用金额

            } else {
                $(_pageId + " #available_vol").text("--");//可用金额
                layerUtils.iAlert(error_info);
            }
        });
    }

    //设置银行卡信息  新晋金宝用户银行卡限额查询查询通联
    function setBankCardInfo() {
        service.reqFun102077({bank_code: userInfo.bankCode,pay_mode:"2"}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results && data.results.length > 0) {
                    var result = data.results[0];
                    $(_pageId + " #nextBtn_one a").html("下一步");
                    is_exist = result.is_exist; //是否签约 0 未签约  1 已签约
                    payorg_id = result.payorg_id;//支付机构
                    pay_type = result.pay_type; //支付方式
                }
            } else {
                is_exist = "0";
                $(_pageId + " #nextBtn_one a").html("下一步");
                layerUtils.iAlert(error_info);
            }
        });
    }

    var inputRechargePwd = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = inputRechargePwd;
});
