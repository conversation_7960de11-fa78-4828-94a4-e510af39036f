<!-- 定投计算器 -->
<div class="page" id="fixedInvestment_calculator" data-pageTitle="定投计算器" data-pageLevel="0" data-refresh="true"
    style="-webkit-overflow-scrolling: touch">
  <section class="main fixed" data-page="home" id="highEnd">
    <header class="header">
      <div class="header_inner bg_header">
        <a href="javascript:void(0);" operationType="1" operationId="icon_back" operationName="返回" class="icon_back icon_gray"
          ><span>返回</span></a
        >
        <h1 class="text_gray text-center">定投计算器</h1>
      </div>
    </header>
    <article style="padding-bottom: 0">
      <div class="list ">
        <ul class="flex">
          <li class="color_000">定投产品</li>
          <li class="m_text_darkgray prod_sname  right">--</li>
        </ul>
        <ul class="flex cycleClick" operationType="1" operationId="cycleClick" operationName="选择定投日期">
          <li class="color_000">定投周期</li>
          <li class="m_text_darkgray right_icon right investmentText"></li>
        </ul>
        <ul class="flex">
          <li class="color_000">
            定投金额
          </li>
          <li class="m_text_999  right" id="inputspanid" operationType="1" operationId="inputspanid" operationName="弹出数字键盘">
            --
          </li>
          <input  id="czje" style="display: none;" readonly="readonly">
        </ul>
        <ul class="flex">
          <li class="color_000">开始时间</li>
          <!-- <li class="m_text_999 right_icon right">请选择时间</li> -->
          <li class="m_text_darkgray right_icon right">
            <input type="text" class="chooseTime" style="outline:none;" placeholder="选择开始日期" readonly="readonly" id='startTime' value="">
          </li>
        </ul>
        <ul class="flex">
          <li class="color_000">结束时间</li>
          <li class="m_text_darkgray right_icon right">
            <input type="text" class="chooseTime" style="outline:none;" placeholder="选择结束日期" readonly="readonly" id='endTime' value="">
          </li>
        </ul>
      </div>
      <div class="calculation" operationType="1" operationId="calculation" operationName="计算">
        计算
      </div>
      <!-- 计算弹窗 -->
      <div class="calculationResult" style="display: none;">
        <h5 class="m_center">定投计算结果</h5>
        <ul>
          <li class="flex">
            <span>投入本金（元）</span>
            <span class="capital">5000.00</span>
            
          </li>
          <li class="flex">
            <span>期数</span>
            <span class="periods">6期</span>
          </li>
          <li class="flex">
            <span>定投收益（元）</span>
            <span class="m_text_red income">287.00</span>
          </li>
          <li class="flex">
            <span>定投收益率</span>
            <span class="m_text_red income_per">4.35%</span>
          </li>
        </ul>
      </div>
      <div class="fixedInvestment" operationType="1" operationId="fixedInvestment" operationName="立即定投" style="display: none;">
        立即定投
      </div>
      <div class="remark" style="display: none;">
        <ul>收益结果根据该基金的历史数据测算</ul>
        <ul>仅供参考，不构成未来投资收益预测的依据</ul>
      </div>
    </article>
  </section>
  <div class="pop_layer" style="display: none;"></div>
  <div class="action_sheet_wrapper " style="display: none" id="cycleModel">
      <div class="pay_login">
          <div class="model_header">
              <div class="close_btn" id="closeCycle" operationType="1" operationId="closeCycle" operationName="关闭周期弹框"></div>
              <h1 class="model_title">请选择定投周期</h1>
              <div class="determine" operationType="1" operationId="determine" operationName="确定">确定</div>
          </div>
      </div>
      <div class="chooseCycle">
          <ul class="listLeft">
              <li id="3" class="active" operationType="1" operationId="chooseCycleMonth" operationName="每月">每月</li>
              <li id="1" operationType="1" operationId="chooseCycleWeek" operationName="每周">每周</li>
              <li id="2" operationType="1" operationId="chooseCycleWeeks" operationName="每两周">每两周</li>
          </ul>
          <ul class="listRight">

          </ul>
      </div>
  </div>
</div>
