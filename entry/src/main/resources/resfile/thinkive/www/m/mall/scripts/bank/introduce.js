// 银行简介
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageCode = "bank/introduce",
        _pageId = "#bank_introduce ";
    var productInfo;
    var ut = require("../common/userUtil");
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });


    }
    
    
    function destroy() { 

    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var bankInsuranceSystem = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankInsuranceSystem;
});
