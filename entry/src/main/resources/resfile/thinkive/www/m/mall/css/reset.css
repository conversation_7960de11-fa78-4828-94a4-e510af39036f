* {
    -webkit-tap-highlight-color: rgba(255, 0, 0, 0);
    padding: 0px;
    margin: 0px;
    outline: none;
}
html{
    font-size:62.5%;
}
body {
    font-family:"Microsoft YaHei", "微软雅黑", Helvetica, "黑体", <PERSON><PERSON>, <PERSON>homa;
    font-size:14px;
     font-size: 1.4rem;
    line-height: 1.6rem;
}

li {
    list-style: none;
}

img {
    border: 0px;
}

input, button, a, select, textarea {
    outline: 0;
    outline: none;
    border: 0px;
}

a {
    text-decoration: none;
}

i, em {
    font-style: normal;
}

textarea, input {
    font-family: "Microsoft YaHei", "微软雅黑", Helvetica, "黑体", <PERSON><PERSON>, Tahoma;
}

.clearline {
    background: none;
    border: 0;
    clear: both;
    display: block;
    float: none;
    font-size: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
    visibility: hidden;
    width: 0;
    height: 0;
}

.f_l {
    float: left;
}

.f_r {
    float: right;
}
.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}