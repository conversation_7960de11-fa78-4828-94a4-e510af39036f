Zepto(function($){
	$.each([$(".header"), $(".footer")], function(index, item){
		if(item.attr("data-fixed")){
			item.addClass("fixed");
		}
	});
	$(".main").css("height",$(window).height()-$(".header").height())


	document.addEventListener('touchstart', function (e) {
        e.target.className = 'ce_btn active';
        setTimeout(function(){
        	var event = e||window.event;
        	//console.log(event.type);
        	if(event.type == "touchstart") {
        		e.target.className = 'ce_btn';
        	}
        }, 1000);
    }, false);
    document.addEventListener('touchend', function (e) {  
        e.target.className = 'ce_btn';  
    }, false);
});