// 历史反馈
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#highVersion_historicalFeed ",
        _page_code = "highVersion/historicalFeed";
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var highVersion_historicalFeed = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_historicalFeed;
});
