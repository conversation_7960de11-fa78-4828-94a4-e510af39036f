// 货基 - 产品详情页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        ut = require("../common/userUtil"),
        common = require("common"),
        tools = require("../common/tools"),
        _pageId = "#inclusive_moneytaryDetail";
    var _pageCode = "inclusive/moneytaryDetail";
    var _fund_code = "";
    var chartsUtils = require("chartsUtils");
    var spliceDate;//默认展示7天数据
    var tips; //默认提示为七日年化
    var timeOptions;//绘制折线图配置
    var productInfo;
    function init() {
        spliceDate = -1100;
        _fund_code = appUtils.getSStorageInfo("fund_code");
        productInfo = appUtils.getSStorageInfo("productInfo")
        tools.initFundBtn(productInfo, _pageId);
        //获取产品详情
        getProductDetails();
        //获取业绩表现
        getPerformance()
        //默认展示七日年化数据
        $(_pageId + " #chartTitle").find("span").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
        //七日年化 折线图
        yieldChart()
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        // 基金档案
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailRecord"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailRecord")
        });
        // 基金经理
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailManager"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailManager")
        });
        // 基金公司
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailCompany"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailCompany")
        });
        // 资产配置
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailConfiguration"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailConfiguration")
        });
        // 基金公告
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailNotice"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailNotice")
        });
        // 基金文件
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailFile"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailFile")
        });
        // 交易规则
        appUtils.bindEvent($(_pageId + " .trade_rules"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailRule")
        });
        // 点击产品档案
        appUtils.bindEvent($(_pageId + " .product_archives"), function () {
//			var arrow = $(_pageId + " #product #arrow");
//			if (arrow.hasClass("arrow_up")) {
//				$(_pageId + " #product #arrow").addClass("arrow_down");
//				$(_pageId + " #product #arrow").removeClass("arrow_up");
//
//				$(_pageId + " #item_box").hide();
//			}else{
//				$(_pageId + " #product #arrow").addClass("arrow_up");
//				$(_pageId + " #product #arrow").removeClass("arrow_down");
//
//				$(_pageId + " #item_box").show();
//			}
        });
        // 显示风险提示
        appUtils.bindEvent($(_pageId + " .showAlert"), function () {
            showRiskAlert();
        });
        // 点击风险提示，我知道了
        appUtils.bindEvent($(_pageId + " .risk_alert .know"), function () {
            hideRiskAlert()
        });
        // 点击业绩表现
        appUtils.bindEvent($(_pageId + " #performance"), function () {
            $(_pageId + " #performanceContent").show();
            $(_pageId + " #historyContent").hide();

            $(_pageId + " #performance").addClass("active")
            $(_pageId + " #history").removeClass("active")
        });
        // 点击历史七日年化
        appUtils.bindEvent($(_pageId + " #history"), function () {
            $(_pageId + " #performanceContent").hide();
            $(_pageId + " #historyContent").show();

            $(_pageId + " #performance").removeClass("active")
            $(_pageId + " #history").addClass("active")
            var length = $(_pageId + " #historyContent .list_content").children().length;
            if (length == "0") {
                getHistory();
            }
        });
        // 查看更多 业绩表现
        appUtils.bindEvent($(_pageId + " #PerformanceList"), function () {
            appUtils.pageInit(_pageCode, "thfund/HAPerformanceList")
        });
        // 查看更多 历史七日年化
        appUtils.bindEvent($(_pageId + " #HistoryList"), function () {
            appUtils.pageInit(_pageCode, "thfund/HAHistoryList")
        });

        // 点击历史七日年化
        appUtils.bindEvent($(_pageId + " #yield"), function () {
//			$(this).addClass("active");
//			$(_pageId +" #earnings").removeClass("active");
            $(_pageId + " #chartTitle").find("span").removeClass("active").filter(this).addClass("active");

            $(_pageId + " .spliceDate").find("li").removeClass("active").eq(0).addClass("active");
            yieldChart();
        });
        // 点击万份收益
        appUtils.bindEvent($(_pageId + " #earnings"), function () {
//			$(this).addClass("active");
//			$(_pageId +" #yield").removeClass("active");
            $(_pageId + " #chartTitle").find("span").removeClass("active").filter(this).addClass("active");

            $(_pageId + " .spliceDate").find("li").removeClass("active").eq(0).addClass("active");
            earningsChart();
        });
        //展示限定时间的收益
        appUtils.bindEvent(_pageId + " .spliceDate li", function () {
            $(_pageId + " .spliceDate").find("li").removeClass("active").filter(this).addClass("active");
            var index = $(this).index(),
                selected = chat.rangeSelector.selected;
            if (index === selected) {
                return false;
            }
            chat.rangeSelector.clickButton(index);
        })
    }

    //隐藏风险提示弹框
    function hideRiskAlert() {
        $(_pageId + " .risk_alert").hide();
    }

    //显示风险提示弹框
    function showRiskAlert() {
        $(_pageId + " .risk_alert").show();
    }

    //获取业绩表现
    function getPerformance() {
        service.reqFun102007({fund_code: _fund_code}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                var dataArr = ["近一周", "近一月", "近三月", "近半年", "近一年"];
                var numData = [];
                //近一周
                numData.push(results.week);
                //近一月
                numData.push(results.month);
                //近三月
                numData.push(results.season);
                //近半年
                numData.push(results.six_month);
                //近一年
                numData.push(results.year);
                //近两年
                // numData.push(results.two_year);
                //近三年
                // numData.push(results.three_year);
                var html = "";

                for (var i = 0; i < numData.length; i++) {
                    //空数据处理
                    numData[i] = tools.FormatNull(numData[i]);
                    var rateClass = "";
                    if (numData[i].rate < 0) {
                        rateClass = "minus";
                    } else {
                        rateClass = "add";
                    }

                    if (numData[i].rate != "--") {
                        numData[i].rate = numData[i].rate;
                        numData[i].rate = (+numData[i].rate).toFixed(2) + "%";
                    }

                    html += '<div class="item">' +
                        '<span>' + dataArr[i] + '</span>' +
                        '<span class=' + rateClass + '>' + numData[i].rate + '</span>' +
                        '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取历史七日年化
    function getHistory() {
        var params = {
            fund_code: _fund_code
        }
        service.reqFun102006(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    return;
                }
                var html = "";
                for (var i = 0; i < 5; i++) {
                    //空数据处理
                    results[i] = tools.FormatNull(results[i]);

                    var end_date = results[i].end_date;
                    if (end_date != "--") {
                        end_date = tools.ftime(end_date.substring(0, 8));
                    }

                    var return_pertt = results[i].return_pertt;
                    if (return_pertt != "--") {
                        return_pertt = (+return_pertt).toFixed(4);
                    }

                    var annu_yield = results[i].annu_yield;
                    if (annu_yield != "--") {
                        annu_yield = (+annu_yield).toFixed(4) + "%";
                    }

                    html += '<div class="item">' +
                        '<span>' + end_date + '</span>' +
                        '<span class="">' + return_pertt + '</span>' +
                        '<span class="add">' + annu_yield + '</span>' +
                        '</div>';
                }
                $(_pageId + " #historyContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取产品详情
    function getProductDetails() {
        service.reqFun102002({fund_code: _fund_code}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);
                $.extend(productInfo, results);
//                appUtils.setSStorageInfo("productInfo", productInfo);
                if (results.scale_fe != "--") { //份额
                    results.scale_fe = (results.scale_fe / 100000000).toFixed(2) + '亿元';
                }
                if (results.scale_fund !== "--") { //基金规模
                    results.scale_fund = (results.scale_fund / 100000000).toFixed(2) + '亿元';
                }
                var return_pertt_date = results.return_pertt_date;
                if (return_pertt_date != "--") {
                    return_pertt_date = tools.ftime(return_pertt_date).substring(5);
                }

                //七日年化收益
                if (results.annu_yield != "--") {
                    results.annu_yield = (+results.annu_yield).toFixed(4) + "%";
                }
                //万份收益
                if (results.return_pertt != "--") {
                    results.return_pertt = (+results.return_pertt).toFixed(4) + "元";
                }
                var threshold_amount = results.threshold_amount;
                if (threshold_amount != "--") {
                    threshold_amount = threshold_amount + "元起购";
                }

                var product_desc = '<div class="fund_name" id="prod_name">' + results.prod_sname + '</div>' +
                    '<span class="num" id="fund_code">(' + results.fund_code + ')</span>' +
                    '<div class="product_top">' +
                    '<div>' +
                    '<p>七日年化收益(<span id="nav_date">' + return_pertt_date + '</span>)</p>' +
                    '<p class="red compare_benchmark" id="annu_yield">' + results.annu_yield + '</p>' +
                    '</div>' +
                    '<div>' +
                    '<p>万份收益(<span id="w_nav_date">' + return_pertt_date + '</span>)</p>' +
                    '<p class="black fund_lncome " id="return_pertt">' + results.return_pertt + '</p>' +
                    '</div>' +
                    '</div>';

                var product_tip = '<div class="risk_level_name" id="risk_level_name">' + results.risk_level_name + '</div>' +
                    '<div class="fund_type_name" id="fund_type_name">' + results.fund_type_name + '</div>' +
                    '<div class="money">' +
                    '<span>基金规模：</span>' +
                    '<span id="scale_fe">' + results.scale_fund + '</span>' +
                    '<span id="scale_fe_date">(' + tools.ftime(results.scale_fe_date.substr(4, 4)) + ')</span>' +
                    '</div>';

                appUtils.setSStorageInfo("mgrcomp_sname", results.mgrcomp_sname);
                $(_pageId + " .product_desc").html(product_desc);
                $(_pageId + " .product_tip").html(product_tip);
                //基金经理
                $(_pageId + " #fund_managers").html(results.fund_managers);
                //基金公司
                $(_pageId + " #mgrcomp_name").html(results.mgrcomp_name);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //七日年化 折线图
    function yieldChart() {
        tips = "七日年化";
        service.reqFun102006({fund_code: _fund_code}, function (result) {
            if (result.error_no == 0) {
                var results = result.results;
                var data = [];
                for (var i = 0; i < results.length; i++) {
                    var obj = {
                        x: results[i].end_date.substring(0, 8),
                        y: Number(results[i].annu_yield)
                    }
                    data.push(obj);
                }
                data = data.reverse();
                data = data.slice(spliceDate);
                data = datearr(data);

//                 var data=[
//                 {y: 2.783, x: 1554940800000},
//                 {y: 2.75,  x: 1555027200000},
//                 {y: 2.721, x: 1555113600000},
//                 {y: 2.691, x: 1555200000000},
//                 {y: 2.661, x: 1555286400000},
//                 {y: 2.655, x: 1555372800000},
//                 {y: 2.643, x: 1555459200000},
//                 {y: 2.671, x: 1555545600000},
//                 {y: 2.675, x: 1555632000000},
//                 {y: 2.675, x: 1555718400000},
//                 {y: 2.675, x: 1555804800000},
//                ]
                showChart(data, tips, function (c) {
                    chat = c;
                    chat.rangeSelector.clickButton(0);
                })
            } else {
                layerUtils.iAlert(result.error_info);
            }
        });
    }

    //万份收益 折线图
    function earningsChart() {
        tips = "万份收益";
        service.reqFun102006({fund_code: _fund_code}, function (result) {
            if (result.error_no == 0) {
                var results = result.results;
                var data = [];
                for (var i = 0; i < results.length; i++) {
                    var obj = {
                        x: results[i].end_date.substring(0, 8),
                        y: Number(results[i].return_pertt)
                    }
                    data.push(obj);
                }
                data = data.reverse();
                data = data.slice(spliceDate);
                data = datearr(data);
                showChart(data, tips, function (c) {
                    chat = c;
                    chat.rangeSelector.clickButton(0);
                })
            } else {
                layerUtils.iAlert(result.error_info);
            }
        });
    }

    //设置时间为highChart所需时间格式
    function datearr(data) {
        for (var i = 0; i < data.length; i++) {
            var x = data[i].x.toString();
            Date.UTC()
            data[i].x = Date.UTC(x.substring(0, 4), x.substring(4, 6) - 1, x.substring(6, 8));
        }
        return data;
    }

    //显示晋金宝折线图
    function showChart(data, tips, callback) {
        tips = tips || "七日年化";
        timeOptions = {
            lang: {
                rangeSelectorZoom: null // 不显示 'zoom' 文字
            },
            rangeSelector: {
                inputEnabled: false,
                buttonPosition: '11',
                buttons: [{
                    type: 'month',
                    count: 1,
                    text: '1月'
                }, {
                    type: 'month',
                    count: 3,
                    text: '3月'
                }, {
                    type: 'month',
                    count: 6,
                    text: '6月'
                }, {
                    type: 'year',
                    count: 1,
                    text: '近1年'
                }, {
                    type: 'year',
                    count: 3,
                    text: '近3年'
                }],
                buttonTheme: {
                    display: "none"
                }
            },
            scrollbar: {
                enabled: false
            },
            navigator: {
                enabled: false
            },
            chart: {
                type: 'areaspline',
                panning: false, //禁用放大
                pinchType: ''//禁用手势操作
            },
            title: {
                text: null
            },
            xAxis: {
                title: {
                    text: null
                },
                tickPixelInterval: 60,
                gridLineWidth: 1,
                // type: 'datetime',
                // labels: {
                //     format: '{value:%y/%m/%d}'
                // },
                dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%H:%M',
                    day: '%m/%d',
                    week: '%m/%d',
                    month: '%y/%m',
                    year: '%Y'
                }
            },
            yAxis: {
                title: {
                    text: ''
                },
                gridLineWidth: 1,

                options: {
                    startOnTick: false,
                    endOnTick: false,
                },
                labels: {
                    format: '{value}%'
                },
                tickPixelInterval: 20,
            },
            legend: {
                enabled: false
            },
            plotOptions: {
                series: {
                    turboThreshold: 0
                },
                areaspline: {
                    fillColor: {
                        linearGradient: {
                            x1: 0,
                            y1: 0,
                            x2: 0,
                            y2: 1
                        },
                        stops: [
                            [0, "rgba(632,64,63,0.5)"],
                            [1, "rgba(255,255,255,0.5)"]
                        ]
                    },
                    marker: {
                        radius: 2
                    },
                    lineWidth: 2,
                    states: {
                        hover: {
                            lineWidth: 2
                        }
                    },

                    threshold: null
                }
            },
            tooltip: {
                backgroundColor: '#1E4DA9',   // 背景颜色
                borderColor: '',         // 边框颜色
                borderRadius: 0,             // 边框圆角
                borderWidth: 0,               // 边框宽度
                shadow: true,                 // 是否显示阴影
                animation: true,               // 是否启用动画效果
                crosshairs: "Mixed",
                followTouchMove: true,
                style: {                      // 文字内容相关样式
                    color: "#ffffff",
                    fontSize: "12px",
                    fontWeight: "normal",
                    fontFamily: "Courir new"
                },
                type: "datetime",
                formatter: function (item) {
                    var value = this.y;
                    if (tips == "七日年化") {
                        value = (+value).toFixed(4);
                    }
                    var time = new Date(this.x)
                    return "<div>" + time.getFullYear() + '/' + (time.getMonth() + 1) + '/' + time.getDate() + "</div>" +
                        "<div>" + tips + ":" + value + "</div>"
                },
                useHTML: true,
                headerFormat: '<table><small>{point.key}</small><table>',
                pointFormat: '<tr><td >{series.name} : </td>' +
                    '<td style="text-align: right">{point.y}%</td></tr>',
                footerFormat: '</table>',

            },
            series: [{
                name: "<span style='color:#ffffff'>" + tips + "</span>",
                color: "rgba(632,64,63,1)",
                borderWidth: 2,
                data: data
            }]
        };
        if (tips == "七日年化") {
            timeOptions.yAxis["labels"]["format"] = '{value}%';
            timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}%</td></tr>'
        } else {
            timeOptions.yAxis["labels"]["format"] = '{value}';
            timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}</td></tr>'
        }
        $(_pageId + ' #chartContainer').highcharts('StockChart', timeOptions, callback);
        Highcharts.setOptions({
            lang: {
                rangeSelectorZoom: '' // 不显示 'zoom' 文字
            }
        });
    }

    function destroy() {
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #fund_code").html("--");
        $(_pageId + " #nav_date").html("--");
        $(_pageId + " #annu_yield").html("--");
        $(_pageId + " #w_nav_date").html("--");
        $(_pageId + " #return_pertt").html("--");
        $(_pageId + " #risk_level_name").html("--");
        $(_pageId + " #fund_type_name").html("--");
        $(_pageId + " #scale_fe").html("--");
        $(_pageId + " #performanceContent .item .value").html("--");
        $(_pageId + " #historyContent .list_content").html("");
        $(_pageId + " #qrDate").html("--");
        $(_pageId + " #syDate").html("--");
        $(_pageId + " #ksqrDate").html("--");
        $(_pageId + " #ptqrDate").html("--");
        $(_pageId + " #fund_managers").html("--");
        $(_pageId + " #mgrcomp_name").html("--");
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #performanceContent").show();
        $(_pageId + " #historyContent").hide();

        $(_pageId + " #performance").addClass("active");
        $(_pageId + " #history").removeClass("active");
        hideRiskAlert();

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
