define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        HIscroll = require("hIscroll"),
        // service = require("mobileService"),
        gconfig = require("gconfig"),
        layerUtils = require("layerUtils"),
        hIscroll = {"scroll": null, "_init": false},
        _pageId = "#guide_guidePage";
    require("../common/swiper"); 
    require("../common/ceshi");
    var external = require("external");
    var common = require("common");
    var platform = gconfig.platform;
    var global = gconfig.global;
    var tools = require("../common/tools");

    function init() {
        // tools.getPdf("4"); //获取pdf名称及路径
        addGuide();
        if (platform != 0) {
            // var datas = common.getLocalStorage("account_policy");
            // if (datas) {
            //     $(_pageId + " .card1").hide();
            //     $(_pageId + " .card2").hide();
            //     $(_pageId + " .pop_layer").hide();
            //     // layerUtils.iLoading(false);
            //     //保存剪切板的uuid查询到的渠道代码
            //     saveCnlCode();
            // } else {
               
                
            // }
            // $(_pageId + " .card1").hide();
            // $(_pageId + " .card2").hide();
            // $(_pageId + " .pop_layer").hide();
            layerUtils.iLoading(false);
            // saveCnlCode();
        }
    }

    function bindPageEvent() {

        appUtils.bindEvent($(_pageId + " .step-btn"), function () {
            appUtils.setLStorageInfo("isGuided", "true");
            appUtils.pageInit("guide/guidePage", gconfig.defaultPage.pageCode, {});
        });
        appUtils.bindEvent($(_pageId + " .tiaoguo"), function () {
            appUtils.setLStorageInfo("isGuided", "true");
            appUtils.pageInit("guide/guidePage", gconfig.defaultPage.pageCode, {});
        });

        // appUtils.bindEvent($(_pageId + " #disagree"), function () {
        //     // $(_pageId + " .card1").hide();
        //     // $(_pageId + " .card2").show();
        //     appUtils.setLStorageInfo("isGuided", "false");
        // });

        // appUtils.bindEvent($(_pageId + " #agree"), function () {
            
        //     let paramlist = {
        //         funcNo: "60046",
        //         moduelName: "",
        //         permissions:""
        //     };
        //     if(gconfig.platform == 1) external.callMessage(paramlist);  //应用宝上架修改，针对安卓
        //     // $(_pageId + " .card1").hide();
        //     // $(_pageId + " .pop_layer").hide();
        //     common.setLocalStorage("account_policy", "true");
        //     appUtils.setLStorageInfo("isGuided", "true");
        //     //保存剪切板的uuid查询到的渠道代码
        //     saveCnlCode();
        // });
        // appUtils.bindEvent($(_pageId + " #exit"), function () {
        //     let Param = {
        //         "funcNo": "50105"
        //     };
        //     external.callMessage(Param);
        //     appUtils.setLStorageInfo("isGuided", "flase");
        // });

        // appUtils.bindEvent($(_pageId + " #agree1"), function () {
        //     var paramlist = {
        //         funcNo: "60046",
        //         moduelName: "",
        //         permissions:""
        //     };
        //     if(gconfig.platform == 1) external.callMessage(paramlist); //应用宝上架修改，针对安卓
        //     // $(_pageId + " .card1").hide();
        //     // $(_pageId + " .card2").hide();
        //     // $(_pageId + " .pop_layer").hide();
        //     common.setLocalStorage("account_policy", "true");
        //     appUtils.setLStorageInfo("isGuided", "true");
        //     //保存剪切板的uuid查询到的渠道代码
        //     saveCnlCode();
        // });

    }

    function addGuide() {
        new Swiper($(_pageId).find(".guide_container"), {
            pagination: ".swiper-pagination",
            autoplay: 3000,
            paginationElement: "li",
            bulletActiveClass: "check",
            autoplayDisableOnInteraction: false,
            autoplayStopOnLast: true
        });
    }

    function destroy() {
        hIscroll.scroll = null;
        hIscroll._init = false;
        // $(_pageId + " .card1").show();
        // $(_pageId + " .card2").hide();
        // $(_pageId + " .pop_layer").show();
    }
    
    //存储渠道代码
    function saveCnlCode(){
	var download_channel_code = common.getLocalStorage("download_channel_code");
	if(download_channel_code){
	    //渠道代码不为空,不做处理
	    return;
	}
	//首次打开app,获取剪切板内容
	var Param = {
                "funcNo" : "80302",
                "moduleName" : "mall"
            };
	var jqb = external.callMessage(Param);
	// console.log(jqb);
	var jqbsms;
	if(jqb && jqb.error_no == 0){
	    jqbsms = jqb.results[0].copydata;
	}
	if(jqbsms){
	    //通过剪切板标识码获取渠道码
	    var queryParam = {"uuid":jqbsms};
	    service.reqFun1100010(queryParam, function (data) {
		if (data.error_no == 0) {
		    if (data.results.length != 0) {
			var cnl_code = data.results[0].cnl_code;
			common.setLocalStorage("download_channel_code", cnl_code);
		    }
		}
	    });
	}
    }

    var guidePage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    module.exports = guidePage;
});