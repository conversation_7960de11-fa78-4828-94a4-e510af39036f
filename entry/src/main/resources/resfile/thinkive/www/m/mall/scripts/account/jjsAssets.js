// 晋金所资产
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        _pageCode = "account/jjsAssets",
        _pageId = "#account_jjsAssets";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var external = require("external");
    var tools = require("../common/tools");//升级
    var ut = require("../common/userUtil");
    var userInfo;
    var fundsStatus;
    var isEmpower; //是否授权
    var isMatching;//四要素是否一致
    var isRiskTest;//是否做风险测评
    // var jjsCustNo;//晋金所客户号
    function init() {
        let param = appUtils.getSStorageInfo("jjsShowData");
        fundsStatus = param.isBind;//是否是基金用户
        isEmpower = param.isEmpower;//是否授权
        isMatching = param.isMatching;//四要素是否一致
        isRiskTest = param.isRiskTest;//是否做风险测评
        userInfo = ut.getUserInf();
        if (fundsStatus == '1') { // 基金用户
            $(_pageId + " .jinjibao_region").show();
            jjsUserInfo();
            if(isMatching != '1'){
                return layerUtils.iAlert("您的晋金所和晋金财富绑定银行卡信息不一致，无法进行授权，请先更换一致。如有疑问，详询400-167-8888。",-1, function() {
                }, "确定");
            }
            if(isRiskTest != '1'){
                return layerUtils.iConfirm("您还未在晋金所进行风险测评", function () {
                    appUtils.pageInit(_pageCode, "jjsfund/riskQuestion");
                }, function () {
                }, "去测评", "取消");
            }
            if (isEmpower != "1") { //是否授权 0否 1是
                layerUtils.iConfirm("晋金所与晋金财富app即将合并，请务必完成以下操作，以免影响您的正常使用，如有疑问，详询400-167-8888。", function () {
                    appUtils.pageInit(_pageCode, "account/empower");
                }, function () {
                }, "确定", "取消");
            }
        } else { // 新用户
            $(_pageId + " .jinjibao_region").hide();
            newUserInfo();
        }
    }


    function jjsUserInfo() {
        service.reqFun177005({ func_no: "902211" }, function (data) {
            if (data.error_no == "0") {
                var result = data.results[0];
                var total_vol = result.total_vol ? common.fmoney(result.total_vol, 2) : "--";//总资产
                var total_finan = result.total_finan ? common.fmoney(result.total_finan, 2) : "--";
                var total_jjye = result.total_jjye ? common.fmoney(result.total_jjye, 2) : "--";
                var total_jjb = result.total_jjb ? common.fmoney(result.total_jjb, 2) : "--";
                var trans_bank_vol = result.trans_bank_vol ? common.fmoney(result.trans_bank_vol, 2) : "--";//在途资金
                $(_pageId + " #jinjinyue em").html(total_jjye + "元");
                $(_pageId + " .total_assets em").html(total_vol + "元");
                $(_pageId + " #jinjintou em").html(total_finan + "元");
                $(_pageId + " #jinjibao em").html(total_jjb + "元");
            } else {
                $(_pageId + " #jinjinyue em").html("--");
                $(_pageId + " .total_assets em").html("--");
                $(_pageId + " #jinjintou em").html("--");
                $(_pageId + " #jinjibao em").html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function newUserInfo() {
        service.reqFun177005({ func_no: "902229" }, function (data) {
            if (data.error_no == "0") {
                var result = data.results[0];
                var total_all = result.total_all ? common.fmoney(result.total_all, 2) : "--"; //总资产
                var total_finan = result.total_finan ? common.fmoney(result.total_finan, 2) : "--"; //晋金投
                var total_jjye = result.total_jjye ? common.fmoney(result.total_jjye, 2) : "--"; //晋金余额
                // var trans_bank_vol = result.total_onway ? common.fmoney(result.total_onway, 2) : "--"; // 在途资金
                $(_pageId + " #jinjinyue em").html(total_jjye + "元"); // 
                $(_pageId + " .total_assets em").html(total_all + "元");
                $(_pageId + " #jinjintou em").html(total_finan + "元");
                // $(_pageId + " #zaitu em").html(trans_bank_vol + "元");
                // if (validatorUtil.isNotEmpty(result.trust_means)) {
                //     var trust_means = result.trust_means ? common.fmoney(result.trust_means, 2) : "--";//信托资产
                //     $(_pageId + " .xintuo_region").show().find(" #xintuo em").html(trust_means + "元");
                // } else {
                //     $(_pageId + " .xintuo_region").hide().find(" #xintuo em").html("--");
                // }
            } else {
                $(_pageId + " #jinjinyue em").html("--");
                $(_pageId + " .total_assets em").html("--");
                $(_pageId + " #jinjintou em").html("--");
                // $(_pageId + " #zaitu em").html("--");
                // $(_pageId + " .xintuo_region").hide().find(" #xintuo em").html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //绑定事件
    function bindPageEvent() {
        // 晋金投
        appUtils.bindEvent($(_pageId + " #jinjintou"), function () {
            appUtils.pageInit(_pageCode, "thfund/JJTouList");
        });
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        // appUtils.pageBack();
        appUtils.pageInit(_pageCode, "account/myAccount");
    }

    function destroy() {
        $(_pageId + " #jinjinyue em").html("--");
        $(_pageId + " .total_assets em").html("--");
        $(_pageId + " #jinjintou em").html("--");
        $(_pageId + " #jinjibao em").html("--");
        $(_pageId + " .jinjibao_region").hide();
    }

    var jjsAssetsModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjsAssetsModule;
})
