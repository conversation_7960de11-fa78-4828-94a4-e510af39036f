// 投顾整合卖出页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#combProduct_combProdSell";
    var _pageCode = "combProduct/combProdSell";
    let ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var calculator = require("../common/calculator");
    var monkeywords = require("../common/moneykeywords");
    var cost_list = {}; //费用详情
    var _comb_code;
    var _redem_method = "1";
    var i = 4;
    var timer = null;//计时器
    var jymm;
    var redscalbase = "0"; // 赎回基准
    var sell_min, sell_max, all_sell,adjustflag,targetProfit;
    var selling_fee_show; //是否展示费率列表
    var plan_type;
    var touchStart_x = null,
        touchStart_y = null,
        touchMove_x = null,
        touchMove_y = null,
        boxStartX = null,
        boxStartY = null;

    function init() {
        selling_fee_show = '';//初始化是否展示费率列表
        var holdObj = appUtils.getSStorageInfo("holdObj");  // 从持仓详情带过来的数据
        targetProfit = appUtils.getSStorageInfo("targetProfit");
        plan_type = appUtils.getSStorageInfo("plan_type");//类型，存钱罐特殊处理
        adjustflag = holdObj.adjustflag;//是否展示？
        //删除费率列表 初始化
        $(_pageId + " .card_content_list").html('');
        //可赎回基准
        redscalbase = holdObj.redscalbase;
        //转出最低比例
        // sell_min = parseFloat(holdObj.minredscale);
        //转出最高比例
        sell_max = parseFloat(holdObj.maxredscale);
        all_sell = parseFloat(holdObj.allredscale);
        //判断是否为目标盈，做对应处理
        if(targetProfit == '1'){
            //是目标盈
            $(_pageId + " .targetProfit_show").show();
            $(_pageId + " .targetProfit_hide").hide();
            
            sell_min = 100;
        }else{
            //普通产品
            $(_pageId + " .targetProfit_show").hide();
            $(_pageId + " .targetProfit_hide").show();
            sell_min = parseFloat(holdObj.minredscale);
        }
        
        $(_pageId + " #targetProfit_money").text(tools.fmoney(getnum(common.floatMultiply(redscalbase, all_sell))));
        $(_pageId + " #sell_range").html(`${common.floatMultiply(sell_min, 100)}%-${common.floatMultiply(sell_max, 100)}%`);
        $(_pageId + " #all_ratio").html(`${common.floatMultiply(all_sell, 100)}%`)
        $(_pageId + " #number").html(`${common.floatMultiply(sell_min, 100)}%`); // 默认显示最低比例
        $(_pageId + " #sold_ratio").html(`${common.floatMultiply(sell_min, 100)}`); // 默认显示最低比例
        $(_pageId + " #account").html(tools.fmoney(getnum(calculator.multiply(holdObj.minredscale, redscalbase))));
        $(_pageId + " .sum_fund_amt").text(tools.fmoney(holdObj.sum_fund_amt));
        $(_pageId + " .redscalbase").text(tools.fmoney(getnum(common.floatMultiply(redscalbase, all_sell))));
        if (sell_max < sell_min) {
            moveMaxRatio();
            $(_pageId + " #beyond_show").html(`可卖出比例为<span class="color-red"> ${common.floatMultiply(all_sell, 100)}%</span>`)
            $(_pageId + " #beyond_show").show();
            $(_pageId + " #normal_show").hide();
        } else {
            initPageStyle();
        }
        if(adjustflag == '0'){
            //正常
            $(_pageId + " .thirty_tips").hide();
            // $(_pageId + " .product_content").css('margin-bottom','0.1rem');
            // $(_pageId + " .product_content").css('padding-bottom','0.1rem');
        }else{
            $(_pageId + " .thirty_tips").show();
            // $(_pageId + " .product_content").css('margin-bottom','0');
            // $(_pageId + " .product_content").css('padding-bottom','0');
        }
        _comb_code = appUtils.getSStorageInfo("fund_code");
        //页面埋点初始化
        tools.initPagePointData({fundCode:_comb_code});
        //获取费用详情列表
        // getCostList(targetProfit);
        //获取交易时间
        reqFun102008();
        //获取产品详情
        reqFun102165();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
    }
    //获取投顾费用列表
    function getCostList(targetProfit,curSellRatio){
        let sell_min_param =  curSellRatio;
        let param = {
            comb_code: _comb_code,
            redeem_type:(targetProfit == '1' || curSellRatio == 1) ? '1' : '0',
            redeem_rate:targetProfit == '1' ? 1 : sell_min_param,
            cust_no:ut.getUserInf().custNo
        }
        service.reqFun102192(param, function (data) {
            if (data.error_no == 0) {
                let results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);
                cost_list = results;    //获取费率数据列表
                setList(cost_list)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染费率列表
    function setList(cost_list){
        $(_pageId + " .card_content_list").html('');
        let list = cost_list.redeemfeeList;
        $(_pageId + " .redeem_fee").html(tools.fmoney(cost_list.redeem_fee));
        if(!list || !list.length) return;
        let html = '';
        list.map(item=>{
            let redeem_fee = item.redeem_fee*1;
            if(redeem_fee <= 0) return;
            let str = `
            <p class="flex">
                <span class="m_width_45 m_center">${item.fund_name}</span>
                <span class="m_width_30 m_center">${tools.fmoney(item.redeem_amt)}</span>
                <span class="m_width_25 m_center">${tools.fmoney(item.redeem_fee)}</span>
            </p>
            `
            html += str
        })
        $(_pageId + " .card_content_list").html(html);
        let redeem_fee = cost_list.redeem_fee * 1;
        if(!redeem_fee || redeem_fee <= 0){ //费率为0 原流程
            return next();
        }else{
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .costList").show();
            i = 4;
            timer = setInterval(function () {
                shows();
            }, 1000);
        }
    }
    function getnum(num) {
        var s = num.toString();
        if(s.indexOf(".") < 0) return s + '.00';
        var result = s.substring(0,s.indexOf(".")+3);
        return result;
    }
    function initPageStyle() {
        var wrapWidth = $(_pageId + " #wrap").width();
        var m_left = (wrapWidth * sell_min).toFixed(2);
        var m_right = (wrapWidth * sell_max).toFixed(2);
        // 当可卖出最大比例小于最小比例
        var proportion_width = common.floatSub(m_right, m_left);
        $(_pageId + " #proportion").css({
            width: proportion_width,
            "margin-left": m_left + 'px',
            "margin-right": m_right + 'px'
        });
        $(_pageId + " #progress_btn").css({
            "left": (m_left - 15) + 'px'
        })
        $(_pageId + " #retioProportion").css({
            width: m_left,
        });

    }

    function moveMaxRatio() {
        var currRatio = common.floatMultiply(all_sell, 100); // 可卖出最高比例
        var wrapWidth = $(_pageId + " #wrap").width();
        var m_left = parseFloat(wrapWidth * sell_min);
        var m_right = parseFloat(wrapWidth * all_sell);
        var proportion_width = parseFloat(m_right - m_left);
        $(_pageId + " #sold_ratio").html(currRatio)
        $(_pageId + " #number").html(`${currRatio}%`);
        let leftNum = parseFloat(calculator.multiply(wrapWidth, currRatio / 100));
        $(_pageId + " #retioProportion").css({
            "width": m_right,
        });

        $(_pageId + " #account").html(tools.fmoney(getnum(calculator.multiply(currRatio / 100, redscalbase))));
        $("#progress_btn").css({
            left: leftNum - 15,
        });
    }
    /**
     * 显示读秒
     * */
    function shows() {
        var sell_btn = $(_pageId + " .sell_btn");
        if (i > 0) {
            sell_btn.text("继续卖出(" + i + "s)")
            i--;
        } else {
            window.clearInterval(timer);
            sell_btn.text("继续卖出")
            sell_btn.css({color: "#E5443C"});
            sell_btn.css("pointer-events", "auto");//可以卖出
        }
    }
    function updateSellbtnCss() {
        var sell_btn = $(_pageId + " .sell_btn");
        window.clearInterval(timer);
        sell_btn.text("继续卖出(5s)")
        sell_btn.css({color: "rgb(211, 125, 125)"});
        sell_btn.css("pointer-events", "none");//禁止卖出
    }
    function next(){
        var curSellRatio = $(_pageId + " #sold_ratio").html() / 100;
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        //卖出金额
        var trans_amt = $(_pageId + " #account").html();
        if (_redem_method == "1") {
            $(_pageId + " #payMethod").text("晋金宝");
        } else {
            $(_pageId + " #payMethod").text("银行卡");
        }
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html(common.floatMultiply(curSellRatio, 100) + "%");
        //显示 输入交易密码
        $(_pageId + " .pop_layer").show();
        $(_pageId + " .password_box").show();
        passboardEvent();
        monkeywords.flag = 0;
        //键盘事件
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "combProduct_combProdSell";
        param["eleId"] = "jymm";
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .thirty_tips"), function () {
            layerUtils.iAlert('不可赎回资产包括：买入及卖出在途资金、投顾产品调仓部分');
        });
        //坚持持有
        appUtils.bindEvent($(_pageId + " .persist_holding"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .costList").hide();
            updateSellbtnCss();
        });
        //继续卖出
        appUtils.bindEvent($(_pageId + " .sell_btn"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .costList").hide();
            updateSellbtnCss();
            next();
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        // 进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "combProduct/combProdDetail");
        });

        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });

        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });

        //点击到账方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _redem_method = $(this).attr("redem_method");
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //点击下一步
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            
            var czjeVal = $(_pageId + " #account").html();
            czjeVal = czjeVal.replace(/,/g, "");
            if (czjeVal <= 0 || !czjeVal) {
                layerUtils.iAlert("请选择卖出比例");
                return;
            }
            var curSellRatio = $(_pageId + " #sold_ratio").html() / 100;
            if (curSellRatio == all_sell) {
            } else if (curSellRatio >= sell_min && curSellRatio <= sell_max && (common.floatSub(all_sell, curSellRatio) < sell_min)) {
                layerUtils.iAlert("产品剩余比例小于最低卖出比例，请全部卖出", "-1",
                    () => {
                        moveMaxRatio();
                    });
                return;
            }
            if(selling_fee_show != '1') return next();
            getCostList(targetProfit,curSellRatio);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " .password_box #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            // jymm1 = "123123"
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
            var trans_amt = $(_pageId + " #sold_ratio").html() / 100;
            var param = {
                fund_code: _comb_code,	//基金代码
                trans_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                redem_method: _redem_method,  //赎回到宝:1,赎回到卡：2.
                redeem_fee:cost_list.redeem_fee //手续费
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                if(plan_type && plan_type=="4"){
                    purchaseZQ(param);
                }else{
                    purchase(param);
                }
                
            }, { isLastReq: false });
        });

        appUtils.bindEvent($(_pageId + " #progress_btn"), function (e) {
            touchStart_x = e.originalEvent.targetTouches[0].pageX;

            touchStart_y = e.originalEvent.targetTouches[0].pageY;

            boxStartX = $(_pageId + " #progress_btn").position().left;

            boxStartY = $(_pageId + " #progress_btn").position().top;
        }, "touchstart");

        appUtils.bindEvent($(_pageId + " #progress_btn"), function (e) {
            touchMove_x = e.originalEvent.targetTouches[0].pageX - touchStart_x;
            touchMove_y = e.originalEvent.targetTouches[0].pageY - touchStart_y;
            let leftNum = boxStartX + touchMove_x;
            var wrapWidth = $(_pageId + " #wrap").width();
            var m_left = parseFloat(wrapWidth * sell_min) - 15;
            var m_right = parseFloat(wrapWidth * sell_max);
            let proportionWidth = parseFloat(wrapWidth * sell_max) - 15
            if (sell_max > sell_min) {
                if (leftNum < m_left) {
                    leftNum = m_left;
                } else if (leftNum > proportionWidth) {
                    leftNum = proportionWidth;
                }
                let ratio = 0;
                // 计算百分比
                if ((boxStartX + touchMove_x) <= m_left) {
                    let txt = `${sell_min * 100}%`;
                    ratio = (sell_min * 100).toFixed(0);
                    $(_pageId + " #number").html(txt);
                    $(_pageId + " #sold_ratio").html(ratio);
                }
                else {
                    let txt = (((leftNum + 15) / wrapWidth) * 100).toFixed(0) + "%";
                    ratio = (((leftNum + 15) / wrapWidth) * 100).toFixed(0)
                    $(_pageId + " #number").html(txt);
                    $(_pageId + " #sold_ratio").html(ratio);
                }
                $(_pageId + " #account").html(tools.fmoney(getnum(calculator.multiply(ratio / 100, redscalbase))));
                $("#progress_btn").css({
                    left: leftNum,
                });
                $(_pageId + " #retioProportion").css({
                    width: leftNum + 15,
                });
            }
            else {
                leftNum = common.floatMultiply(wrapWidth, all_sell) - 15
            }

        }, "touchmove");

        appUtils.bindEvent($(_pageId + " .progress_add"), function () {
            var currRatio = parseFloat($(_pageId + " #sold_ratio").html());
            var max_ratio = sell_max * 100; // 最低比例
            var wrapWidth = $(_pageId + " #wrap").width();
            if (currRatio >= parseFloat(max_ratio.toFixed(2))) {
                return;
            } else {
                currRatio += 1
                $(_pageId + " #sold_ratio").html(currRatio)
                $(_pageId + " #number").html(`${currRatio}%`);
                let leftNum = parseFloat(calculator.multiply(wrapWidth, currRatio / 100));
                $("#progress_btn").css({
                    left: leftNum - 15,
                });
                $(_pageId + " #retioProportion").css({
                    width: leftNum,
                })
            }
            $(_pageId + " #account").html(tools.fmoney(getnum(calculator.multiply(currRatio / 100, redscalbase))));
        });
        appUtils.bindEvent($(_pageId + " .progress_sub"), function (e) {
            var currRatio = parseFloat($(_pageId + " #sold_ratio").html());
            var min_ratio = sell_min * 100; // 最低比例
            var max_ratio = sell_max * 100; // 最低比例
            var wrapWidth = $(_pageId + " #wrap").width();
            if (currRatio <= min_ratio || currRatio > max_ratio) {
                return;
            } else {
                currRatio--;
                $(_pageId + " #sold_ratio").html(currRatio)
                $(_pageId + " #number").html(`${currRatio}%`);
                let leftNum = parseFloat(calculator.multiply(wrapWidth, currRatio / 100));
                $("#progress_btn").css({
                    left: leftNum - 15,
                });
                $(_pageId + " #retioProportion").css({
                    width: leftNum,
                })
            }
            $(_pageId + " #account").html(tools.fmoney(getnum(calculator.multiply(currRatio / 100, redscalbase))));
        })

        appUtils.bindEvent($(_pageId + " #all_sell"), function () {
            moveMaxRatio();
        });
        appUtils.bindEvent($(_pageId + " #min_sell"), function (e) {
            if (sell_max < sell_min) {
                return;
            }
            initPageStyle();
            var currRatio = common.floatMultiply(sell_min, 100); // 可卖出最低比例
            var wrapWidth = $(_pageId + " #wrap").width();
            $(_pageId + " #sold_ratio").html(currRatio)
            $(_pageId + " #number").html(`${currRatio}%`);
            let leftNum = parseFloat(calculator.multiply(wrapWidth, currRatio / 100));
            $(_pageId + " #account").html(tools.fmoney(getnum(calculator.multiply(currRatio / 100, redscalbase))));
            $("#progress_btn").css({
                left: leftNum - 15,
            });
        })
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function purchase(param) {
        service.reqFun106060(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "template/templateSaleResult", data.results[0]);
        })
    }

    function purchaseZQ(param) {
        service.reqFun102211(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "template/templateSaleResult", data.results[0]);
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //获取最低持有份额,产品详情
    function reqFun102165() {
        var param = {
            comb_code: _comb_code,
        }
        service.reqFun102165(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                selling_fee_show = results.selling_fee_show;
                $(_pageId + " #prod_sname").text(results.nick_comb_name ? results.nick_comb_name : results.comb_sname);
                $(_pageId + " .header_inner h1").text(results.nick_comb_name ? results.nick_comb_name : results.comb_sname);
                $(_pageId + " .risk_level_name").html(results.comb_risk_name);
                $(_pageId + " .fund_code").html(results.comb_code);
                $(_pageId + " .fund_mechanisme").html(results.mechanism);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取交易时间
    function reqFun102008() {
        var param = {
            comb_code: _comb_code,
            type: "12"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);
                var beforeDate = results.beforeDate;
                var afterDate = results.afterDate;
                //确认日期
                $(_pageId + " #beforeDate").html(beforeDate);
                //收益日期
                $(_pageId + " #afterDate").html(afterDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        selling_fee_show = '';
        $(_pageId + " #account").html("--");
        window.clearInterval(timer);
        $(_pageId + " #sold_ratio").html("--");
        $(_pageId + " .redeem_fee").html("--");
        $(_pageId + " .prod_sname").html("--");//产品全称
        jymm = "";
        _redem_method = "1";
        $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
        $(_pageId + " .modify_box .item").eq(0).children(".icon").addClass("active");
        $(_pageId + " #payMethod").text("--");
        $(_pageId + " #recharge_name").html("--");
        $(_pageId + " #recharge_money").html("--");
        $(_pageId + " .card_content_list").html('');
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .costList").hide();
        monkeywords.destroy();
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
