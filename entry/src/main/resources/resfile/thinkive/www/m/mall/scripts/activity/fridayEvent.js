/**
 * 模块名：晋金财富抽奖外链页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        SHIscroll = require("shIscroll"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        common = require("common"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil");
    var external = require("external");
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    require("../../js/draw.js");
    /* 常量 */
    var _pageCode = "activity/fridayEvent", _pageId = "#activity_fridayEvent";
    /* 变量  活动信息*/
    var userInfo, cust_no, mobile;
    var fridayActivityInfo;
    var activity_id;
    /**
     * 初始化
     */
    function init() {
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        activity_id = appUtils.getSStorageInfo("friday_data").activity_id;
        reqFun108033();//查询活动信息    
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
    }


    function reqFun108033() {
        service.reqFun108033({ activity_id: activity_id }, (data) => {
            if (data.error_no == 0) {
                var results = data.results[0];
                fridayActivityInfo = results;
                $(_pageId + " .rules_main").html(fridayActivityInfo.introduce);
                // 上传查看截图功能启用状态 0不启用 1启用
                if (fridayActivityInfo.upload_function_state == "1") {
                    $(_pageId + " .uploadbtn").show();
                } else if (fridayActivityInfo.upload_function_state == "0") {
                    $(_pageId + " .uploadbtn").hide();
                }
                appUtils.setSStorageInfo("friday_data", fridayActivityInfo);
                appUtils.setSStorageInfo("comment_content", fridayActivityInfo.comment_content);
                if (fridayActivityInfo.comment_content && fridayActivityInfo.comment_content.length > 54) {
                    fridayActivityInfo.comment_content = fridayActivityInfo.comment_content.substring(0, 54) + "...";
                }
                fridayActivityInfo.activity_tips && $(_pageId + " #activity_name").html(fridayActivityInfo.activity_tips);
                // 周五活动子类型
                if (fridayActivityInfo.friday_activity_sub_type == "1") {
                    $(_pageId + " #earnimgs_box").css("display", "block");
                    $(_pageId + " #comment_box").css("display", "none");
                    // $(_pageId + " #activity_name").html("晒收益 邀好友 领积分福利");
                    $(_pageId + " #activity_text").html(fridayActivityInfo.activity_text);
                } else if (fridayActivityInfo.friday_activity_sub_type == "2") {
                    $(_pageId + " #earnimgs_box").css("display", "none");
                    $(_pageId + " #comment_box").css("display", "block");
                    // $(_pageId + " #activity_name").html("晒&nbsp;评&nbsp;论&nbsp;享&nbsp;积&nbsp;分&nbsp;福&nbsp;利");
                    $(_pageId + " .comment-box #activity_text").html("“" + fridayActivityInfo.comment_content + "”");
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        // 生成海报分享
        appUtils.bindEvent($(_pageId + " #generate_poster"), () => {
            if (!ut.hasBindCard(_pageCode)) return;
            if (fridayActivityInfo.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if (fridayActivityInfo.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            appUtils.pageInit(_pageCode, "activity/posterShareInvitation");
            return;
        });
        // 上传我的集赞截图
        appUtils.bindEvent($(_pageId + " #upload_screenshot"), () => {
            if (!ut.hasBindCard(_pageCode)) return;
            if (fridayActivityInfo.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if (fridayActivityInfo.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            if (fridayActivityInfo.share_flag == "0") {
                layerUtils.iAlert("请先参与分享活动");
                return;
            }
            var param = {};
            param["funcNo"] = "50273";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#111111";
            }
            param.paramExt = {
                multi: false,
                type: "friday_activiy", // 周五活动类型
                activity_id: activity_id,
                cust_no: cust_no
            }
            param["cutFlag"] = "0";
            param["compress"] = "0.8";
            param["width"] = "1600";
            param["height"] = "900";
            // let external = require("external");
            tools.fileImg(_pageId, param)

        });
        // 查看我的集赞截图
        appUtils.bindEvent($(_pageId + " #view_screenshot"), () => {
            if (!ut.hasBindCard(_pageCode)) return;
            if (fridayActivityInfo.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            // if (fridayActivityInfo.state == '3') {
            //     layerUtils.iMsg(-1, "活动已结束！", 2);
            //     return;
            // }
            service.reqFun108035({ activity_id: activity_id, cust_no: cust_no }, (data) => {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    if (!results) {
                        layerUtils.iAlert("请先上传集赞截图");
                        return;
                    }
                    var privateurl = results && results.img_url;
                    service.reqFun102093({ privateUrl: privateurl }, function (d) {
                        if (d.error_no != 0) {
                            layerUtils.iAlert(d.error_info);
                            return
                        }
                        var publicUrl = d.results[0] && d.results[0].urlPublic;
                        $(_pageId + " #my_img").attr("src", publicUrl)
                        $(_pageId + " #screen_img").show();
                    });
                }
            })
        })
        // 关闭我的集赞截图
        appUtils.bindEvent($(_pageId + " #close_pop"), () => {
            $(_pageId + " #screen_img").hide();
        })
        // 换个评论
        appUtils.bindEvent($(_pageId + " #exchange_comment"), () => {
            service.reqFun108036({ comment_id: fridayActivityInfo.comment_id }, (data) => {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return
                }
                var result = data.results[0];
                fridayActivityInfo.comment_id = result.comment_id;
                appUtils.setSStorageInfo("comment_content", result.comment_content);
                $(_pageId + " .comment-box #activity_text").html("“" + result.comment_content + "”")
            })
        })
    }
    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " #screen_img").hide();
    };
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
