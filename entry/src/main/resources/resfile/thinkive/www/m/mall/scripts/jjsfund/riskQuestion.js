 // 风险测评
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		_pageId = "#jjsfund_riskQuestion ";

	var biaoshi_fx="";
	var product_id="";
 	var backPage = "";
 	var zhaungrang_fx = ""; 
 	var prod_code="";
	var entrust_no;
	var fundTransfer;
	var current = 1; //当前所处题目
	var total = 1; // 总提数
	var select = 0; // 已选题目
	var answerWords = ["A", "B", "C", "D", "E", "F", "G","H", "I", "G", "K", "L", "M", "N","O", "P", "Q", "R", "S", "T", "U","V", "W", "X", "Y", "Z"];
    function init(){
		backPage = appUtils.getSStorageInfo("_prePageCode");
    	biaoshi_fx = appUtils.getPageParam("biaoshi_fx");
    	zhaungrang_fx = appUtils.getPageParam("zhaungrang_fx");
    	product_id = appUtils.getPageParam("product_id");
    	entrust_no = appUtils.getPageParam("entrust_no");
		prod_code = appUtils.getPageParam("prod_code");
		fundTransfer = appUtils.getPageParam("fundTransfer");
		$(_pageId+" input").removeAttr("checked");
		//2017-6-26 解决ios中出现的单选框点选无效问题 修改人：贾晓茹
		initRisk();
	}




	function initRisk() {
		service.reqFun177004({
            jjs_cust_no:appUtils.getSStorageInfo("jjs_cust_no"),
            func_no:"901406"
		}, function (data) {
			if (data.error_no == "0") {
				if (data.results.length > 0) {
					var str = "";
					$(_pageId + " #RiskQuestionAnswer").html("");
					total = data.results.length;
					$(_pageId + " .progress .line").css({width: (current / total) * 100 + "%"});
					$(_pageId + " .progress_text").html(current + "/" + total);
					for (var i = 0; i < data.results.length; i++) {
						var question_code = data.results[i].question_code;//题目编号
						var question_name = data.results[i].question_name;//风险测评题目名称
						var option_type = data.results[i].option_type;//风险测评题目类型， 传值为数字或者字母
						var option_cont_a = data.results[i].option_cont_a;//
						var option_cont_b = data.results[i].option_cont_b;//
						var option_cont_c = data.results[i].option_cont_c;//
						var option_cont_d = data.results[i].option_cont_d;//
						var option_cont_e = data.results[i].option_cont_e;//
						var option_cont_f = data.results[i].option_cont_f;//
						var option_cont_g = data.results[i].option_cont_g;//
						var option_cont_h = data.results[i].option_cont_h;//
						var option_cont_i = data.results[i].option_cont_i;//
						var option_cont_j = data.results[i].option_cont_j;//
						var option_cont_k = data.results[i].option_cont_k;//
						var option_cont_l = data.results[i].option_cont_l;//
						var option_cont_m = data.results[i].option_cont_m;//
						var option_cont_n = data.results[i].option_cont_n;//
						var option_cont_o = data.results[i].option_cont_o;//
						var option_cont_p = data.results[i].option_cont_p;//
						var option_cont_q = data.results[i].option_cont_q;//
						var option_cont_r = data.results[i].option_cont_r;//
						var option_cont_s = data.results[i].option_cont_s;//
						var option_cont_t = data.results[i].option_cont_t;//
						var option_cont_u = data.results[i].option_cont_u;//
						var option_cont_v = data.results[i].option_cont_v;//
						var option_cont_w = data.results[i].option_cont_w;//
						var option_cont_x = data.results[i].option_cont_x;//
						var option_cont_y = data.results[i].option_cont_y;//
						var option_cont_z = data.results[i].option_cont_z;//
						var option_cont_27 = data.results[i].option_cont_27;//
						var option_cont_28 = data.results[i].option_cont_28;//
						var option_cont_29 = data.results[i].option_cont_29;//
						var option_cont_30 = data.results[i].option_cont_30;//
						var option_cont_31 = data.results[i].option_cont_31;//
						var option_cont_32 = data.results[i].option_cont_32;//
						var option_cont_33 = data.results[i].option_cont_33;//
						var option_cont_34 = data.results[i].option_cont_34;//
						var option_cont_35 = data.results[i].option_cont_35;//
						var option_cont_36 = data.results[i].option_cont_36;//
						var option_cont_37 = data.results[i].option_cont_37;//
						str += "<div  id='" + (i + 1) + "' class='risk_question' option_type='" + option_type + "'>" +
							"<strong>" + (i + 1) + "." + question_name + "</strong>";
						if (option_cont_a) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='A' id='radio_" + (i + 1) + "'><label>" + option_cont_a + "</label></div>";
						}
						if (option_cont_b) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='B' id='radio_" + (i + 1) + "'><label>" + option_cont_b + "</label></div>";
						}
						if (option_cont_c) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='C' id='radio_" + (i + 1) + "'><label>" + option_cont_c + "</label></div>";
						}
						if (option_cont_d) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='D' id='radio_" + (i + 1) + "'><label>" + option_cont_d + "</label></div>";
						}
						if (option_cont_e) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='E' id='radio_" + (i + 1) + "'><label>" + option_cont_e + "</label></div>";
						}
						if (option_cont_f) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='F' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
						}
						if (option_cont_g) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='G' id='radio_" + (i + 1) + "'><label>" + option_cont_g + "</label></div>";
						}
						if (option_cont_h) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='H' id='radio_" + (i + 1) + "'><label>" + option_cont_h + "</label></div>";
						}
						if (option_cont_i) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='I' id='radio_" + (i + 1) + "'><label>" + option_cont_i + "</label></div>";
						}
						if (option_cont_j) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='J' id='radio_" + (i + 1) + "'><label>" + option_cont_j + "</label></div>";
						}
						if (option_cont_k) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='K' id='radio_" + (i + 1) + "'><label>" + option_cont_k + "</label></div>";
						}
						if (option_cont_l) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='L' id='radio_" + (i + 1) + "'><label>" + option_cont_l + "</label></div>";
						}
						if (option_cont_m) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='M' id='radio_" + (i + 1) + "'><label>" + option_cont_m + "</label></div>";
						}
						if (option_cont_n) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='N' id='radio_" + (i + 1) + "'><label>" + option_cont_n + "</label></div>";
						}
						if (option_cont_o) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='O' id='radio_" + (i + 1) + "'><label>" + option_cont_o + "</label></div>";
						}
						if (option_cont_p) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='P' id='radio_" + (i + 1) + "'><label>" + option_cont_p + "</label></div>";
						}
						if (option_cont_q) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='Q' id='radio_" + (i + 1) + "'><label>" + option_cont_q + "</label></div>";
						}
						if (option_cont_r) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='R' id='radio_" + (i + 1) + "'><label>" + option_cont_r + "</label></div>";
						}
						if (option_cont_s) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='S' id='radio_" + (i + 1) + "'><label>" + option_cont_s + "</label></div>";
						}
						if (option_cont_t) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='T' id='radio_" + (i + 1) + "'><label>" + option_cont_t + "</label></div>";
						}

						if (option_cont_u) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='U' id='radio_" + (i + 1) + "'><label>" + option_cont_u + "</label></div>";
						}
						if (option_cont_v) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='V' id='radio_" + (i + 1) + "'><label>" + option_cont_v + "</label></div>";
						}
						if (option_cont_w) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='W' id='radio_" + (i + 1) + "'><label>" + option_cont_w + "</label></div>";
						}
						if (option_cont_x) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='X' id='radio_" + (i + 1) + "'><label>" + option_cont_x + "</label></div>";
						}
						if (option_cont_y) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='Y' id='radio_" + (i + 1) + "'><label>" + option_cont_y + "</label></div>";
						}
						if (option_cont_z) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='Z' id='radio_" + (i + 1) + "'><label>" + option_cont_z + "</label></div>";
						}
						if (option_cont_27) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='27' id='radio_" + (i + 1) + "'><label>" + option_cont_27 + "</label></div>";
						}
						if (option_cont_28) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='28' id='radio_" + (i + 1) + "'><label>" + option_cont_28 + "</label></div>";
						}
						if (option_cont_29) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='29' id='radio_" + (i + 1) + "'><label>" + option_cont_29 + "</label></div>";
						}
						if (option_cont_30) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='30' id='radio_" + (i + 1) + "'><label>" + option_cont_30 + "</label></div>";
						}

						if (option_cont_31) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='31' id='radio_" + (i + 1) + "'><label>" + option_cont_31 + "</label></div>";
						}
						if (option_cont_32) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='32' id='radio_" + (i + 1) + "'><label>" + option_cont_32 + "</label></div>";
						}
						if (option_cont_33) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='33' id='radio_" + (i + 1) + "'><label>" + option_cont_33 + "</label></div>";
						}
						if (option_cont_34) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='34' id='radio_" + (i + 1) + "'><label>" + option_cont_34 + "</label></div>";
						}
						if (option_cont_35) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='35' id='radio_" + (i + 1) + "'><label>" + option_cont_35 + "</label></div>";
						}
						if (option_cont_36) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='36' id='radio_" + (i + 1) + "'><label>" + option_cont_36 + "</label></div>";
						}
						if (option_cont_37) {
							str += "<p></p><div class='ui radio  riskQuesAns'>" +
								"<input type='radio' value='37' id='radio_" + (i + 1) + "'><label>" + option_cont_37 + "</label></div>";
						}

						str += "</div>";
					}
					$(_pageId + " #RiskQuestionAnswer").append(str);
					$(_pageId + " #RiskQuestionAnswer div").css('width', '100%');
					$(_pageId + " #RiskQuestionAnswer input").css('display', 'none');
					$(_pageId + " .risk_question strong").css({'margin': '0 0.15rem 0.1rem', 'display': 'block'});
					$(_pageId + " .risk_question div").css({'padding': '0 0.15rem'});
					$(_pageId + " .risk_question").eq(0).show();
					$(_pageId + " #next").addClass("disable");
					$(_pageId + " #previous").addClass("disable");
				}
			} else {
				layerUtils.iAlert(data.error_info);
			}
		});
	}
	function bindPageEvent(){
		appUtils.preBindEvent($(_pageId + " #RiskQuestionAnswer"), ".riskQuesAns", function () {
			var checkedS = $(this).parent(".risk_question").attr("id");
			var inputs = $("#" + checkedS).children("div").children("input");
			var checked = $(this).children("input").prop("checked");
			inputs.removeAttr("checked");
			$(this).children("input").attr("checked", "checked");
			//current-当前所处题目，total-总题数，select-已选题目
			if (select < current) {//已选题目小于当前所处题目
				select++;
				$(_pageId + " #next").addClass("disable");
			} else if (select == current) {
				$(_pageId + " #next").addClass("disable");
			} else {
				$(_pageId + " #next").removeClass("disable");
			}
			if (current < total) {//当前所处题目小于总题数
				current++;
			}
			$(_pageId + " #previous").removeClass("disable");
			$(_pageId + " .progress .line").css({width: (current / total) * 100 + "%"});
			$(_pageId + " .progress_text").html(current + "/" + total);
			$(_pageId + " .risk_question").eq(current-2).hide();
			$(_pageId + " .risk_question").eq(current - 1).show();
			if (select == total) {
				var answer = "";
				for (var i = 1; i <= total; i++) {
					//获得每题中是否被选择
					var $isSelect = $(_pageId + " #" + i).children("div").children("input:radio:checked");
					var option_type = $(_pageId + " #" + i).attr("option_type");
					if(option_type == "1") {
						answer += (answerWords.indexOf($isSelect[0].value) > -1 ? (answerWords.indexOf($isSelect[0].value) + 1 + ",") :$isSelect[0].value    + ",");
					} else {
						answer += ($isSelect[0].value + ",");
					}
				}
				var param901410 = {
					"cust_risk_result": answer.substr(0, answer.length - 1),
					"jjs_cust_no":appUtils.getSStorageInfo("jjs_cust_no"),
                    "func_no":"901121"
				};
				submintAnswer(param901410);
			}

		}, 'click');
		//下一题
		appUtils.bindEvent($(_pageId + " #next"), function () {
			if (current == total) return;
			if ($(this).hasClass("disable")) return;
			$(_pageId + " #previous").removeClass("disable");
			if (select <= current) {
				$(_pageId + " #next").addClass("disable");
			} else {
				$(_pageId + " #next").removeClass("disable");
			}
			current++;
			$(_pageId + " .progress .line").css({width: (current / total) * 100 + "%"});
			$(_pageId + " .progress_text").html(current + "/" + total);
			$(_pageId + " .risk_question").eq(current-2).hide();
			$(_pageId + " .risk_question").eq(current - 1).show();
		});

		//上一题
		appUtils.bindEvent($(_pageId + " #previous"), function () {
			if (current == 1) return;
			$(_pageId + " #next").removeClass("disable");
			current--;
			$(_pageId + " .progress .line").css({width: (current / total) * 100 + "%"});
			$(_pageId + " .progress_text").html(current + "/" + total);
			$(_pageId + " .risk_question").eq(current).hide()
			$(_pageId + " .risk_question").eq(current - 1).show();
			if (current == 1) {
				$(_pageId + " #previous").addClass("disable");
			}
		});

		//点击返回
		appUtils.bindEvent(_pageId+" #getBack",function(){
            pageBack();
		});
	}

	function submintAnswer(param901410){
		layerUtils.iLoading(true);
		service.reqFun177004(param901410,function(data){
			var error_no = data.error_no;
			error_info = data.error_info;
			if (error_no == "0"){

				
				service.reqFun177005({"func_no":"901501"},function(datas){
					if(datas.error_no=="0"){	//获取成功
						let param = appUtils.getSStorageInfo("jjsShowData");
						if(param) param.isRiskTest = '1';
						appUtils.setSStorageInfo("jjsShowData",param);
						appUtils.pageInit("jjsfund/riskQuestion","jjsfund/riskResult",datas.results[0]);
					}else{
						layerUtils.iLoading(false);
						layerUtils.iAlert("登录信息异常,请重新登录");
					}
				});
			}else{
				layerUtils.iLoading(false);
				layerUtils.iMsg(-1,error_info);
			}
		});
	}
	function pageBack(){
        appUtils.pageBack();
    }
	function destroy(){
		$(_pageId + " #RiskQuestionAnswer").html("");
		$(_pageId + " #next").addClass("disable");
		$(_pageId + " .progress .line").css({width: 0});
		$(_pageId + " .progress_text").html("");
		total = 1;
		current = 1;
		select = 0;
	}

	var riskQuestion = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack" : pageBack
	};
	// 暴露对外的接口
	module.exports = riskQuestion;
});