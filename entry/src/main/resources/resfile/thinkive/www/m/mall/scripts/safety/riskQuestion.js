// 风险测评
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_riskQuestion";
    var common = require("common");
    var ut = require("../common/userUtil");
    var custRiskResultList;

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " input").removeAttr("checked");
        //2017-6-26 解决ios中出现的单选框点选无效问题 修改人：贾晓茹
        initRisk();
        //渲染风险测评到期日，适合购买的风险等级产品
        setCustRiskInvalidDate()
    }
    function setCustRiskInvalidDate(){
        let info = ut.getUserInf();
        let custRiskInvalidDate = info.custRiskInvalidDate ? tools.ftime(info.custRiskInvalidDate) : '';
        let riskLevel = info.riskLevel ? info.riskLevel[1] : '';
        let riskName = info.riskName ? info.riskName : '';
        console.log(riskLevel)
        if(riskLevel && riskLevel.length) riskLevel = tools.getRiskLevel(riskLevel);
        $(_pageId + " .custRiskInvalidDate").html(custRiskInvalidDate);
        $(_pageId + " .riskLevel").html(riskLevel);
        $(_pageId + " .riskName").text(riskName);
    }
    function initRisk() {
        service.reqFun101022({}, function (data) {
            if (data.error_no == "0") {
                let res,questList;
                res = data.results[0].questList;
                questList = data.results[0].questList;
                custRiskResultList = data.results[0].custRiskResultList;
                custRiskResultList = (custRiskResultList  && custRiskResultList.length) ? custRiskResultList.split(','):null
                // console.log(custRiskResultList)
                if (res.length > 0) {
                    var str = "";
                    $(_pageId + " #RiskQuestionAnswer").html("");
                    for (var i = 0; i < res.length; i++) {
                        var question_code = res[i].question_code;//题目编号
                        var question_name = res[i].question_name;//风险测评题目名称
                        var option_cont_a = res[i].option_cont_a;//a
                        var option_cont_b = res[i].option_cont_b;//b
                        var option_cont_c = res[i].option_cont_c;//c
                        var option_cont_d = res[i].option_cont_d;//d
                        var option_cont_e = res[i].option_cont_e;//e
                        var option_cont_f = res[i].option_cont_f;//f
                        var option_cont_g = res[i].option_cont_g;//g
                        var option_cont_h = res[i].option_cont_h;//h
                        var option_cont_i = res[i].option_cont_i;//i
                        var option_cont_j = res[i].option_cont_j;//j
                        str += "<div  id='" + (i + 1) + "' class='risk_question'>" +
                            "<strong>" + (i + 1) + "." + question_name + "</strong>";
                        if (option_cont_a) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='A' id='radio_" + (i + 1) + "'><label>" + option_cont_a + "</label></div>";
                        }
                        if (option_cont_b) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='B' id='radio_" + (i + 1) + "'><label>" + option_cont_b + "</label></div>";
                        }
                        if (option_cont_c) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='C' id='radio_" + (i + 1) + "'><label>" + option_cont_c + "</label></div>";
                        }
                        if (option_cont_d) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='D' id='radio_" + (i + 1) + "'><label>" + option_cont_d + "</label></div>";
                        }
                        if (option_cont_e) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='E' id='radio_" + (i + 1) + "'><label>" + option_cont_e + "</label></div>";
                        }
                        if (option_cont_f) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='F' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_g) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='G' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_h) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='H' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_i) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='I' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        if (option_cont_j) {
                            str += "<p></p><div class='ui radio flow in riskQuesAns'>" +
                                "<input type='radio' value='J' id='radio_" + (i + 1) + "'><label>" + option_cont_f + "</label></div>";
                        }
                        str += "</div>";
                    }
                    $(_pageId + " #RiskQuestionAnswer").append(str);
                    $(_pageId + " #RiskQuestionAnswer div").css('width', '100%');
                    $(_pageId + " #RiskQuestionAnswer input").css('display', 'none');
                    $(_pageId + " .risk_question").css({'padding': '0.2rem 0rem 0'});
                    $(_pageId + " .risk_question strong").css({'margin': '0 0.15rem', 'display': 'block'});
                    $(_pageId + " .risk_question div").css({'padding': '0 0.15rem'});
                    console.log(custRiskResultList,2222)
                    if(custRiskResultList){
                        setAnswers(custRiskResultList);
                        //已经测评过，优先展示 当前风险等级
                        setHave();
                    }else { 
                        $(_pageId + " .haveRisk").hide();
                        $(_pageId + " .noRisk").show();
                        // let operationId = 'riskAssessment'
                        // layerUtils.iConfirm("已为您显示上次测评的回答，点击确定后继续使用或重新测评", function () {
                        //     //取消选中的答案
                        //     clearSelections();
                        // }, function () {
                        //     //渲染初始化时的答案

                        // }, "重新测评", "确定",operationId)
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }
    function setHave(){
        $(_pageId + " .noRisk").hide();
        $(_pageId + " .haveRisk").show();

    }
    /**
     * arr 答案数组集合
     */
    function setAnswers(arr){
        // 给定的答案数组
        var answers = arr;
        // 获取所有类名为 'risk_question' 的 div 元素
        var riskQuestions = document.getElementsByClassName('risk_question');
        // 遍历每个问题
        for(var i = 0; i < riskQuestions.length; i++) {
            // 获取当前问题下所有的 'riskQuesAns' 类元素
            var options = riskQuestions[i].getElementsByClassName('riskQuesAns');
            // 遍历这些选项
            for(var j = 0; j < options.length; j++) {
                // 获取选项中的input元素
                var input = options[j].getElementsByTagName('input')[0];
                // 如果这个选项的value与答案数组中对应的值匹配，则选中它
                if(input && input.value === answers[i]) {
                    input.checked = true;
                    break; // 找到匹配项后，跳出内部循环
                }
            }
        }
    }
    //取消选中
    function clearSelections() {
        // 获取所有类名为 'riskQuesAns' 的元素
        var options = document.getElementsByClassName('riskQuesAns');
        // 遍历这些选项
        for(var i = 0; i < options.length; i++) {
            // 获取选项中的input元素
            var input = options[i].getElementsByTagName('input')[0];
            // 如果input元素存在，则取消选中
            if(input) {
                input.checked = false;
            }
        }
    }
    function bindPageEvent() {
        
        appUtils.bindEvent(_pageId + " #againSubmit", function () {
            $(_pageId + " .haveRisk").hide();
            $(_pageId + " .noRisk").show();
            let operationId = 'riskAssessment'

            layerUtils.iConfirm("已为您显示上次测评的回答，点击确定后继续使用或重新测评", function () {
                //取消选中的答案
                clearSelections();
            }, function () {
                //渲染初始化时的答案

            }, "重新测评", "确定",operationId)
        });
        appUtils.preBindEvent($(_pageId + " #RiskQuestionAnswer"), ".riskQuesAns", function () {
            var checkedS = $(this).parent(".risk_question").attr("id");
            tools.recordEventData('1','riskQuesAns_' + checkedS,'回答题目-'+checkedS);
            var inputs = $(_pageId + " #" + checkedS).children("div").children("input");
            var checked = $(this).children("input").prop("checked");
            inputs.removeAttr("checked");
            $(this).children("input").attr("checked", "checked");
        }, 'click');

        //提交按钮
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            var answer = "";
            var answerNum = 0;
            var length = $(_pageId + " .risk_question").length;
            for (var i = 1; i < length + 1; i++) {
                //获得每题中是否被选择
                var $isSelect = $(_pageId + " #" + i).children("div").children("input:radio:checked");
                if ($isSelect.length > 0) {
                    $isSelect[0].value;
                    answer += $isSelect[0].value;
                    answerNum++;
                    if (answerNum != length) {
                        answer += ",";
                    }
                } else {
                    layerUtils.iMsg(-1, "第" + i + "题您未选择,请核对后提交");
                    break;
                }
            }
            if (answerNum == length) {
                var param101008 = {
                    cust_risk_result: answer,
                    cust_type: "1"
                };
                submintAnswer(param101008);
            }
        });

        //点击返回
        appUtils.bindEvent(_pageId + " #getBack", function () {
            pageBack();
        });
    }

    function submintAnswer(param101008) {
        service.reqFun101008(param101008, function (data) {
            if (data.error_no == "0") {
                // let info = 
                let info = ut.getUserInf()
                info.invalidFlag = '0'
                info.riskLevel = data.results[0].cust_risk_level
                ut.saveUserInf(info);
                appUtils.pageInit("safety/riskQuestion", "safety/riskResult", data.results[0]);
            } else {
                layerUtils.iMsg(-1, data.error_info);
            }
        });
    }

    function pageBack() {
        tools.recordEventData('4','destroy','页面销毁');
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " .noRisk").hide();
        $(_pageId + " .haveRisk").hide();
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #RiskQuestionAnswer").html("");
    }

    var riskQuestion = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = riskQuestion;
});
