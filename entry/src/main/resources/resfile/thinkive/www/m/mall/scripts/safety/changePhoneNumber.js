// 修改支付密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        endecryptUtils = require("endecryptUtils"),
        monkeywords = require("mall/scripts/common/passwordKeywords"),
        common = require("common"),
        isUserBankCard, //用户是否绑卡
        _pageId = "#safety_changePhoneNumber ";
    var tools = require("../common/tools");
    var ut = require("../common/userUtil");
    var external = require("external");
    var platform = require("gconfig").platform;
    function init() {
         //页面埋点初始化
        tools.initPagePointData();
        //页面跳转过来的就需要清除参数
        // console.log(appUtils.getSStorageInfo("isUserBankCard"))
        isUserBankCard = appUtils.getSStorageInfo("isUserBankCard");
        $(_pageId + " #oldPwd").val("");
        if(isUserBankCard == false){    //未绑卡进入
            $(_pageId + " #name").hide();
            $(_pageId + " #idCard").hide();
            $(_pageId + " .check_tips").hide();
            $(_pageId + " #password1").show();
            $(_pageId + " #password1 .cursor-bink").css("left",'1.05rem');
            $(_pageId + " #password1 .cursor-bink").hide();
            $(_pageId + " #password1 .placeholderPsd").html('');
        }else{  //已绑卡进入
            $(_pageId + " .check_tips").show();
            $(_pageId + " #name").show();
            $(_pageId + " #idCard").show();
            $(_pageId + " #password1").hide();
        }
        var changePhoneInfo = appUtils.getSStorageInfo("changePhoneInfo");
        if (changePhoneInfo != null) {
            $(_pageId + " #changename").val(changePhoneInfo.changename);
            $(_pageId + " #changeid").val(changePhoneInfo.changeid);
            $(_pageId + " #changephone").val(changePhoneInfo.changephone);
            appUtils.clearSStorage("changePhoneInfo");
        } else {
            $(_pageId + " #changename").val("");
            $(_pageId + " #changeid").val("");
            $(_pageId + " #changephone").val("");
        }
        common.systemKeybord(); // 解禁系统键盘
        var phoneNum = ut.getUserInf().mobile;
        // $(_pageId + " #userInfo").html("您好！您正在为账户 " + phoneNum.substr(0, 3) + "****" + phoneNum.substr(7, 4) + " 更换注册手机。");
    }
    function moneyboardEvent(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #oldPwd"),
            endcallback: function () {
                setPassword(" #password1"," #oldPwd")
            },
            inputcallback: function () {
                setPassword(" #password1"," #oldPwd")
            },
            keyBoardHide: function () {
                setPassword(" #password1"," #oldPwd")
            }
        })
    }
    function setPassword(_fatherPageId,_childPageId){
        let spacing = platform == 1 ? 0.06 : 0.075
        let pwd = $(_pageId + _childPageId).val();
        if(!pwd){
            $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#aaa');
            $(_pageId + _fatherPageId + " .placeholderPsd").html('');
            $(_pageId + _fatherPageId + " .cursor-bink").css("left",'1.05rem');
            return
        }  
        $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#666');
        let length = pwd.length*1
        if(length > 16) return
        let str = ''
        for(let i = 0; i<length; i++){
            str = str + '*'
        }
        $(_pageId + _fatherPageId + " .placeholderPsd").html(str);
        $(_pageId + _childPageId ).val(pwd);
        // let leftValue = 1 + (length*spacing)
        let leftValue = ($(_pageId + _fatherPageId + " .placeholderPsd")[0].clientWidth/100) + 0.9;
        $(_pageId + _fatherPageId + " .cursor-bink").css("left",leftValue+'rem');
    }
    function noBindCardSumit(){ //未绑卡提交
        // var changephone = $(_pageId + " #changephone").val();
        var phoneNum = $(_pageId + " #changephone").val();
        var newPwd = $(_pageId + " #oldPwd").val();
        // newPwd = 'qwe123'
		if (validatorUtil.isEmpty(phoneNum)) {
		    layerUtils.iMsg(-1, "用户原手机号码不能为空");
		    return;
		}
		if (!validatorUtil.isMobile(phoneNum)) {
		    layerUtils.iMsg(-1, "请输入正确的手机号");
		    return;
		}
        // if (!validatorUtil.isMobile(phoneNum)) {
        //     layerUtils.iMsg(-1, "您输入的手机号有误");
        //     return;
        // }
        var result = checkInput(newPwd);
        if(result){
            //密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                newPwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, newPwd);
                var param = {
                    login_pwd: newPwd,
                    registered_mobile: phoneNum
                }
                service.reqFun101085(param, function (data) {
                    // console.log(data)
                    if(data.error_no != "0") { //校验失败
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                    }else{
                        var changePhoneInfo = {
                            "changephone": phoneNum
                        }
						layerUtils.iLoading(false);
                        appUtils.setSStorageInfo("changePhoneInfo", changePhoneInfo);
                        //校验成功
                        appUtils.pageInit("safety/changePhoneNumber", "safety/changePhoneNumberSuccess", {});
                    }
                }, {isLastReq: false});
            }, {isLastReq: false});
        }
    }
    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #password1"), function (event) {
            event.stopPropagation();
            $(_pageId + " #password1 .cursor-bink").show()
            moneyboardEvent()
            // $(_pageId + " #newPwd").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "safety_changePhoneNumber";
            param["eleId"] = "oldPwd";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " #password1 .cursor-bink").hide()
            monkeywords.close();
        });
        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            if(isUserBankCard == false) return noBindCardSumit()
            var changename = $(_pageId + " #changename").val();
            var changeid = $(_pageId + " #changeid").val();
            var changephone = $(_pageId + " #changephone").val();
            if (validatorUtil.isEmpty(changename)) {
                layerUtils.iMsg(-1, "用户姓名不能为空");
                return false;
            }
            if (changename != ut.getUserInf().name) {
                layerUtils.iMsg(-1, "请输入正确的用户名");
                return;
            }
            if (validatorUtil.isEmpty(changeid)) {
                layerUtils.iMsg(-1, "用户身份证号不能为空");
                return false;
            }
            // if (!validatorUtil.isCardID(changeid)) {
            //     layerUtils.iMsg(-1, "请输入正确的身份证号");
            //     return;
            // }
            var param1100001 = {
                "value": changeid,
                "type": "cert_no"
            }
            service.reqFun1100001(param1100001, function (data) {
                if (data.error_no == "0") {
                    if (validatorUtil.isEmpty(changephone)) {
                        layerUtils.iLoading(false);
                        layerUtils.iMsg(-1, "用户原手机号码不能为空");
                        return;
                    }
                    if (!validatorUtil.isMobile(changephone)) {
                        layerUtils.iLoading(false);
                        layerUtils.iMsg(-1, "请输入正确的手机号");
                        return;
                    }
                    service.reqFun1100001({"value": changephone, "type": "mobile"}, function (data) {
                        if (data.error_no == "0") {
                            var changePhoneInfo = {
                                "changename": changename,
                                "changeid": changeid,
                                "changephone": changephone
                            }
                            appUtils.setSStorageInfo("changePhoneInfo", changePhoneInfo);
                            appUtils.pageInit("safety/changePhoneNumber", "safety/changePhoneNumberSuccess", {});
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iMsg(-1, "请输入正确的手机号");
                        }
                    });
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iMsg(-1, "请输入正确的身份证号");
                }
            }, {isLastReq: false});
        });
    }
    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
     function checkInput(pwd1) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iMsg(-1, "登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        // if (pwd1 !== pwd2) {
        //     layerUtils.iMsg(-1, "两次密码不相同");
        //     return false;
        // }

        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iMsg(-1, "登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iMsg(-1, "登录密码请输入6-16位字母和数字组合");
            return false;
        }
        return true;
    }

    function destroy() {
        $(_pageId + " .placeholderPsd").css('color','#aaa');
        $(_pageId + " .placeholderPsd").show();
        $(_pageId + " .cursor-bink").hide();
        $(_pageId + " .placeholderPsd").html('');
        $(_pageId + " .check_tips").hide();
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        $(_pageId + " .cursor-bink").hide()
        appUtils.pageBack();
    }
    var changePhoneNumber = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = changePhoneNumber;
});
