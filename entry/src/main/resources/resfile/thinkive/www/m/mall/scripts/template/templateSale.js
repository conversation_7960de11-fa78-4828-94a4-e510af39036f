// 公募整合卖出页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#template_templateSale ";
    var _pageCode = "template/templateSale";
    var tools = require("../common/tools");
    var calculator = require("../common/calculator");
    var monkeywords = require("../common/moneykeywords");

    var _fund_code;
    var _redem_method = "1";
    var jymm;
    var _available_vol = "0";
    var _holdmin = 0;
    var _redeem_min = 0;
    var nav; //单位净值
    var sell_max_time;
    var prod_sub_type2;
    var _thirty_vol = "0";
    var important_tip_buy;
    var prodType, productInfo;
    var isShowRedeemRateStr;//是否展示费率弹窗
    var redemptionRate; // 各阶段赎回费率

    function init() {
        
        var holdObj = appUtils.getSStorageInfo("holdObj");
        _fund_code = appUtils.getSStorageInfo("fund_code");
        prodType = appUtils.getSStorageInfo("prodType");
        productInfo = appUtils.getSStorageInfo("productInfo");
         //页面埋点初始化
         tools.initPagePointData({fundCode:_fund_code});
        //可赎回份额
        _available_vol = holdObj.available_vol;
        // //满30天份额
        _thirty_vol = holdObj.used_vol_sum_30;

        // _available_vol = 1000;
        // //满30天份额
        // _thirty_vol = 0;

        //获取交易时间
        reqFun102008();
        //获取最低持有份额，产品详情
        reqFun102113();
        //获取免赎回费天数
        reqFun102045();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘

    }

    function getFcitemRate() {
        service.reqFun102172({ fund_code: productInfo.fund_code }, function (data) {
            if (data.error_no == 0) {
                redemptionRate = data.results;
                if (redemptionRate && redemptionRate instanceof Array && redemptionRate.length) {
                    $(_pageId + " .sold_tip").hide();
                    $(_pageId + " .null_rate").show();
                } else {
                    $(_pageId + " .sold_tip").show();
                    $(_pageId + " .null_rate").hide();
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     *  计算赎回费用
     * 预估到账金额 = 卖出份额 * 单位净值 - 赎回费
     * 预估赎回费用 = 赎回金额*赎回费率=卖出份额*单位净值*赎回费率
     */
    function calculateCharges(available_vol) {
        var _available_vol = +available_vol.replace(/,/g, "")
        if (nav) { // 计算到账金额
            // 计算分阶段赎回费用
            $(_pageId + " .thirty_tips").show();
            var redemTotalAccount = 0; // 预估赎回费用
            if (redemptionRate && redemptionRate instanceof Array && redemptionRate.length) {
                for (var i = redemptionRate.length - 1; i >= 0; i--) {
                    var diff_val = common.floatSub(_available_vol, redemptionRate[i].hold_vol);
                    if (parseFloat(diff_val) > 0) {
                        redemptionRate[i]['ava_vol'] = tools.fmoney(redemptionRate[i].hold_vol); // 可赎回份额
                        var redemAccount = common.floatMultiply(common.floatMultiply(nav, +redemptionRate[i].hold_vol.replace(/,/g, "")), common.floatDivide(redemptionRate[i].discount_rate, 100)) // 预计赎回费用
                        redemptionRate[i]['redem_account'] = redemAccount; // 区间赎回费用
                        _available_vol = diff_val;
                    } else {
                        redemptionRate[i]['ava_vol'] = tools.fmoney(_available_vol); // 可赎回份额
                        var redemAccount = common.floatMultiply(common.floatMultiply(nav, _available_vol), common.floatDivide(redemptionRate[i].discount_rate, 100))
                        redemptionRate[i]['redem_account'] = redemAccount; // 区间赎回费用
                        _available_vol = "0";
                    }
                    redemTotalAccount = common.floatAdd(redemTotalAccount, redemptionRate[i]['redem_account'])
                }
                var redeemRateStr = "";
                isShowRedeemRateStr = true
                redeemRateStr += '' +
                    '<h1 class="m_center m_bold">温馨提示</h1><div class="highFinancialRateInfo_top m_bold"><p class="m_text_center">预估赎回费</p>' +
                    '<p><span>合计</span> <span class="m_text_red">' + tools.fmoney(redemTotalAccount) + '</span>元</p></div>';
                for (var i = 0; i < redemptionRate.length; i++) {
                    var datestr = "<span>" + redemptionRate[i].ava_vol + "份</span>";
                    var fcitem_lval = redemptionRate[i].fcitem_lval; //最小
                    var fcitem_lvunit = redemptionRate[i].fcitem_lvunit;//最小单位
                    var fcitem_tval = redemptionRate[i].fcitem_tval;//最大
                    var fcitem_tvunit = redemptionRate[i].fcitem_tvunit;//最大单位
                    if (fcitem_tval == "-1") { //最大
                        datestr += "<span>持有" + fcitem_lval + fcitem_lvunit + "以上</span>";
                    } else if (fcitem_lval == "0") { //最小
                        datestr += "<span>持有不足" + fcitem_tval + fcitem_tvunit + "</span>";
                    } else {
                        datestr += "<span>持有" + fcitem_lval + "-" + fcitem_tval + fcitem_tvunit + "</span>";
                    }
                    redeemRateStr += `<div class="g_fontSize12">
                    <p class="estimate_rate">${datestr}</p>
                    <p class="rate_detail"><span><span class="m_text_red">${tools.fmoney(redemptionRate[i].redem_account)}</span>元</span><span class="rate_item">费率${redemptionRate[i].discount_rate}${redemptionRate[i].chgrate_unit}</span></p>
                    </div>`;
                }
                redeemRateStr += "";
            } else {
                isShowRedeemRateStr = false
                redeemRateStr = '<div class="m_center">暂无数据</div>'
            }
            $(_pageId + " #redemptionFee").html(tools.fmoney(redemTotalAccount));
            $(_pageId + " #redemptionFee").attr("data-money", redemTotalAccount);
            $(_pageId + " #hasRateAccount").html(tools.fmoney(common.floatSub(common.floatMultiply(nav, +available_vol.replace(/,/g, "")), redemTotalAccount)));
            $(_pageId + " .pop_box2 .content_box .highFinancialRateInfo").html(redeemRateStr);
        }
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(_pageCode, "template/publicOfferingDetail");
        });
        //全部卖出
        appUtils.bindEvent($(_pageId + " #all_buy"), function () {
            var available_vol = tools.fmoney(_available_vol);
            $(_pageId + " #inputspanid>span").html(available_vol).css({ "color": "#000000" });
            $(_pageId + " #czje").val(available_vol);
            if (nav) { // 计算到账金额
                if (redemptionRate && redemptionRate.length) { // 有赎回费
                    calculateCharges(available_vol);
                } else {
                    $(_pageId + " #account").html(tools.fmoney(calculator.multiply(nav, +available_vol.replace(/,/g, ""))));
                }

            }
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "template_templateSale";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
            $(_pageId + " #account").html("--");
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击到账方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _redem_method = $(this).attr("redem_method");
        });
        //点击份额
        appUtils.bindEvent($(_pageId + " .shareList li"), function () {
            let num = $(this).attr('num') * 1
            $(_pageId + " .shareList li").removeClass('active')
            $(this).addClass('active')
            var available_vol = tools.fmoney((_available_vol * 1) / num);
            $(_pageId + " #inputspanid>span").html(available_vol).css({ "color": "#000000" });
            $(_pageId + " #czje").val(available_vol);

            if (nav) { // 计算到账金额以及赎回费率
                if (redemptionRate && redemptionRate.length) { // 有赎回费
                    calculateCharges(available_vol);
                } else {
                    $(_pageId + " #account").html(tools.fmoney(calculator.multiply(nav, +available_vol.replace(/,/g, ""))));
                }

            }
            // let flag =  $(this).attr("data-id");  //当前tab标识
            // fund_market_type = flag //全局赋值唯一标识
            //默认隐藏所有tab
            // $(_pageId + " main").hide()
            //展示指定tab
            // $(_pageId + " .main_" + flag).show()
            // $(_pageId + " #tab li").each(function () {
            //     $(this).find("span").removeClass("active")
            // })
            // $(this).find("span").addClass("active");


        });
        //点击暂不卖出
        appUtils.bindEvent($(_pageId + " .btn_box .moment_btn"), function () {
            //隐藏温馨提示弹框
            hidePopBox();
        });
        //点击 继续卖出
        appUtils.bindEvent($(_pageId + " .btn_box .continue_btn"), function () {
            //隐藏温馨提示弹框
            hidePopBox();
            setRechargeInfo();
            if (isShowRedeemRateStr) {
                $(_pageId + " .pop_box2 #noRate").hide();
                $(_pageId + " .pop_box2 #hasRate").show();
                showPopBox2()
            } else {
                //显示 输入交易密码
                $(_pageId + " .pop_layer").show();
                $(_pageId + " .password_box").show();
                passboardEvent();
                monkeywords.flag = 0;
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "template_templateSale";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
            }
        });

        //点击下一步
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            var czjeVal = $(_pageId + " #czje").val();
            czjeVal = czjeVal.replace(/,/g, "");
            if (czjeVal <= 0 || !czjeVal) {
                layerUtils.iAlert("请输入卖出份额");
                return;
            }

            var available_vol = (+_available_vol);
            if (czjeVal > available_vol) {
                layerUtils.iAlert("超过可用份额");
                return;
            }

            if (available_vol > _redeem_min && czjeVal < parseFloat(_redeem_min)) {
                layerUtils.iAlert("最低赎回" + _redeem_min + "份");
                return;
            }

            var surplus = available_vol - czjeVal;
            if (parseFloat(surplus) < parseFloat(_holdmin) && surplus != "0") { //剩余金额 < 最低持有金额 && 剩余金额 != 0
                layerUtils.iConfirm("剩余份额低于产品最低持有份额" + _holdmin, function () {
                    $(_pageId + " #inputspanid>span").html("");
                    $(_pageId + " #czje").val("");
                    return;
                }, function () {
                    var available_vol = tools.fmoney(_available_vol);
                    $(_pageId + " #inputspanid>span").html(available_vol);
                    $(_pageId + " #czje").val(_available_vol);
                }, "取消", "全部卖出");
                return;
            }

            // if (prodType && prodType == "sevenDay") {
            //     if ((+czjeVal) > +(_thirty_vol)) {
            //         var exceed = (czjeVal - _thirty_vol).toFixed(2);
            //         $(_pageId + " #exceed").html(exceed);
            //         //显示温馨提示弹框
            //         showPopBox(exceed);
            //         return;
            //     }
            // }
            setRechargeInfo();
            // if (sell_max_time && prodType != "sevenDay" && sell_max_time.substr(0, 1) > 0) { //免赎回费率天数存在 && 天数 > 0
            //     showPopBox1();
            // } else {

            // }
            if (isShowRedeemRateStr && parseFloat($(_pageId + " #redemptionFee").attr("data-money")) > 0) {
                $(_pageId + " .pop_box2 #noRate").hide();
                $(_pageId + " .pop_box2 #hasRate").show();
                showPopBox2()
            } else {
                //显示 输入交易密码
                $(_pageId + " .pop_layer").show();
                $(_pageId + " .password_box").show();
                passboardEvent();
                monkeywords.flag = 0;
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "template_templateSale";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
            }
        });
        appUtils.bindEvent($(_pageId + " .pop_box2 #continue"), function () {
            $(_pageId + " .pop_box2").hide();
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "template_templateSale";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        appUtils.bindEvent($(_pageId + " .pop_box2 #queDing"), function () {
            $(_pageId + " .pop_box2").hide();
        });

        appUtils.bindEvent($(_pageId + " .pop_box2 #cancel"), function () {
            $(_pageId + " .pop_box2").hide();
        });

        //调用支付接口
        appUtils.bindEvent($(_pageId + " .password_box #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");

            var param = {
                fund_code: _fund_code,	//基金代码
                trans_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                redem_method: _redem_method,  //赎回到宝:1,赎回到卡：2.
                vir_fundcode: productInfo.vir_fundcode ? productInfo.vir_fundcode : '',
                due_date: productInfo.due_date ? productInfo.due_date : '',
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                purchase(param);
            }, { isLastReq: false });
        });

        appUtils.bindEvent($(_pageId + " .thirty_tips"), function () {
            $(_pageId + " .pop_box2 #noRate").show();
            $(_pageId + " .pop_box2 #hasRate").hide();
            showPopBox2()
        })
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //晋金宝7天显示温馨提示弹框
    function showPopBox(exceed) {
        var param = {
            fund_code: _fund_code,
            vol: exceed
        }
        service.reqFun102045(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                $(_pageId + " .rate").html(results.sell_max_rate + results.sell_max_chgrate_unit);
                $(_pageId + " #service_charge").html(results.sell_service_charge);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
        $(_pageId + " .pop_box").show();
    }

    //显示温馨提示弹框
    function showPopBox1() {
        $(_pageId + " .pop_box1").show();
    }
    //显示费率提示
    function showPopBox2() {
        $(_pageId + " .pop_box2").show();
    }
    //隐藏温馨提示弹框
    function hidePopBox() {
        $(_pageId + " .pop_box1").hide();
        $(_pageId + " .pop_box").hide();
    }

    function purchase(param) {
        service.reqFun106010(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "template/templateSaleResult", data.results[0]);
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                if (curVal == ".") {
                    curVal = "";
                }
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    var available_vol = (+_available_vol);
                    if (moneys > available_vol) {
                        available_vol = tools.fmoney(available_vol + "");
                        $(_pageId + " #czje").val(available_vol);
                    } else {
                        moneys = tools.fmoney(moneys);
                        $(_pageId + " #czje").val(moneys);
                    }
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));

                }
                var available_vol = (+_available_vol);
                if (curVal > available_vol) {
                    curVal = available_vol;
                    available_vol = tools.fmoney(available_vol + "");
                    $(_pageId + " #czje").val(available_vol);
                }
                if (nav && curVal) { // 计算到账金额
                    if (redemptionRate && redemptionRate.length) { // 有赎回费
                        calculateCharges($(_pageId + " #czje").val().replace(/,/g, ""));
                    } else {
                        $(_pageId + " #account").html(tools.fmoney(calculator.multiply(nav, $(_pageId + " #czje").val().replace(/,/g, ""))));
                    }
                } else {
                    $(_pageId + " #account").html("--");
                    $(_pageId + " #hasRateAccount").html("--");
                    $(_pageId + " #redemptionFee").html("--");
                    $(_pageId + " .thirty_tips").hide();
                }
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //获取最低持有份额,产品详情
    function reqFun102113() {
        var param = {
            fund_code: _fund_code,
        }
        service.reqFun102113(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                $(_pageId + " #prod_sname").text(results.prod_sname);
                $(_pageId + " .header_inner h1").text(results.prod_sname);
                $(_pageId + " .risk_level_name").html(results.risk_level_name);
                $(_pageId + " .fund_code").html(results.fund_code);
                $(_pageId + " .fund_type_name").html(results.fund_type_name);
                $(_pageId + " .holding_days").html(results.fund_days_customized);
                //最低持有份额
                _holdmin = results.holdmin;
                //最低赎回份额
                _redeem_min = results.redeem_min;
                //是否是晋金宝7天
                important_tip_buy = results.important_tip_buy;
                //单位净值
                nav = results.nav;

                if (important_tip_buy && important_tip_buy == "1" && prodType == "sevenDay") {
                    $(_pageId + " .saleInfo").html("满" + results.fund_days_customized + "天份额，" + _thirty_vol + "份");
                    $(_pageId + " #all_buy").hide();
                } else {
                    $(_pageId + " .saleInfo").html("可赎回份额" + _available_vol + "份");
                    $(_pageId + " #all_buy").show();
                }
                // if (prodType && prodType == "gather") {
                //     $(_pageId + " .sold_tip").hide();
                // } else {
                //     $(_pageId + " .sold_tip").show();
                // }
                if (prodType && prodType == "netValue") {
                    let available_vol = tools.fmoney(_available_vol);
                    $(_pageId + " #inputspanid>span").html(available_vol).css({ "color": "#000000" });
                    $(_pageId + " #czje").val(available_vol);
                    $(_pageId + " #czje").attr("disabled", true)
                    $(_pageId + " #all_buy").hide()
                    $(_pageId + " #account").html(tools.fmoney(calculator.multiply(nav, available_vol.replace(/,/g, ""))));
                }
                // getRateInfo()
                getFcitemRate();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取交易时间
    function reqFun102008() {
        var param = {
            fund_code: _fund_code,
            type: "7"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);

                var beforeDate = results.beforeDate;
                if (beforeDate != "--") {
                    beforeDate = tools.FormatDateText(beforeDate.substring(4));
                }

                var afterDate = results.afterDate;
                if (afterDate != "--") {
                    afterDate = tools.FormatDateText(afterDate.substring(4));
                }
                //确认日期
                $(_pageId + " #beforeDate").html(beforeDate);
                //收益日期
                $(_pageId + " #afterDate").html(afterDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        //赎回份额
        var trans_amt = $(_pageId + " #czje").val();
        if (_redem_method == "1") {
            $(_pageId + " #payMethod").text("晋金宝");
        } else {
            $(_pageId + " #payMethod").text("银行卡");
        }
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html(tools.fmoney(trans_amt));
    }

    //获取免赎回费 天数
    function reqFun102045() {
        service.reqFun102045({ fund_code: _fund_code }, function (data) { //
            if (data.error_no == "0") {
                var results = data.results;
                if (results.length > 0) {
                    sell_max_time = results[0].sell_max_time;
                    $(_pageId + " #exceed").html(sell_max_time);
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " #account").html("--");
        $(_pageId + " .saleInfo").html("");
        nav = "";
        $(_pageId + " .pop_box2").hide();
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");
        $(_pageId + " .fund_type_name").html("--");
        $(_pageId + " #dzDate").html("--");
        jymm = "";
        _redem_method = "1";
        $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
        $(_pageId + " .modify_box .item").eq(0).children(".icon").addClass("active");
        $(_pageId + " #czje").val("");
        $(_pageId + " #inputspanid span").text("请输入卖出份额").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #payMethod").text("--");
        $(_pageId + " #recharge_name").html("--");
        $(_pageId + " #recharge_money").html("--");
        $(_pageId + " #exceed").html("--");
        $(_pageId + " #all_buy").hide();
        $(_pageId + " .sold_tip").hide();
        $(_pageId + " .null_rate").hide();
        $(_pageId + " .thirty_tips").hide();
        $(_pageId + " .pop_box2 #noRate").show();
        $(_pageId + " .pop_box2 #hasRate").hide();
        hidePopBox()
        monkeywords.destroy();
        _holdmin = 0;
        _redeem_min = 0;
        sell_max_time = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
