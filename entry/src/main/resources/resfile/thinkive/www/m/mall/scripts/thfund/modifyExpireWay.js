// 变更产品到期处理方式
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService");
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    var _pageCode = "thfund/modifyExpireWay";
    var _pageId = "#thfund_modifyExpireWay ";
    var _endflag_method;
    var _old_endflag_method;
    var holdObj;
    var jymm;

    function init() {
        holdObj = appUtils.getSStorageInfo("holdObj");
        $(_pageId + " #fund_sname").html(holdObj.fund_sname); //产品名称
        $(_pageId + " #fund_code").html(holdObj.fund_code); // 产品编码
        if(holdObj.prod_sub_type2 == "200"){
            var due_date = holdObj.due_date;
            if(holdObj.due_date == "" || holdObj.due_date == "--"){
                due_date = holdObj.estimated_opening_start;
            }
            $(_pageId + " #redconfirm_days").html(tools.ftime(due_date) + "到期"); //到期日期
            reqFun102008(due_date);
            //变更方式
            _old_endflag_method = holdObj.endflag_method;
            _endflag_method = holdObj.endflag_method;

            $(_pageId + " .modify_box .item .icon").removeClass("active");
            var activeIndex = _old_endflag_method;
            var item = $(_pageId + " .modify_box .item")[activeIndex];
            $(item).find(".icon").addClass("active");

            $(_pageId + " #fundvolbalance_mode1").html(tools.fmoney(holdObj.hold_vol)); //持有份额
        }else{
            //获取详情
            reqFun101903();
        }

    }

    function bindPageEvent() {
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击 处理方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _endflag_method = $(this).attr("endflag_method");
            if (_endflag_method == _old_endflag_method) {
                $(_pageId + " #confirm").addClass("no_active");
            } else {
                $(_pageId + " #confirm").removeClass("no_active");
            }
        });

        //变更产品到期处理方式
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            if ($(this).hasClass("no_active")) {
                return;
            }

            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();

            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_modifyExpireWay";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //交易密码确定
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();

            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            var param = {
                fund_code: holdObj.fund_code,
                end_flag: _endflag_method,
                trans_pwd: jymm1, //交易密码
                vir_fundcode:holdObj.vir_fundcode
            };
            if(holdObj.prod_sub_type2 == "25" || holdObj.prod_sub_type2 == "200") {
                param.due_date = holdObj.due_date
            }
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                reqFun106015(param);
            }, {isLastReq: false});
        });

        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        appUtils.bindEvent($(_pageId + " #back"), function () {
            pageBack();
        });
    }


    //变更产品到期处理方式
    function reqFun106015(param) {
        service.reqFun106015(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                appUtils.pageInit(_pageCode, "thfund/modifyExpireWayResult");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取详情
    function reqFun101903() {
        service.reqFun101903(holdObj, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    layerUtils.iLoading(false);
                    return;
                }

                //空数据处理
                results = tools.FormatNull(results);

                // 大集合产品类型 01:浙商 03:太平洋滚存  04:太平洋X份额  00:其他 25:日鑫
                var prod_sub_type2 = results.prod_sub_type2
                if (prod_sub_type2 == "04") { //強赎 到期日取管理台日期
                    layerUtils.iLoading(false);
                    $(_pageId + " #redconfirm_days").html(tools.ftime(results.due_date) + "到期"); //到期日期
                    //获取交易时间
                    reqFun102008(results.due_date);
                } else if (prod_sub_type2 == "25") { //日鑫
                    layerUtils.iLoading(false)
                    $(_pageId + " #redconfirm_days").html(tools.ftime(holdObj.due_date) + "到期"); //到期日期
                    reqFun102008(holdObj.due_date);
                } else {
                    //查询产品购买状态
                    reqFun102049();
                }
                //变更方式
                _old_endflag_method = results.endflag_method;
                _endflag_method = results.endflag_method;

                $(_pageId + " .modify_box .item .icon").removeClass("active");
                var activeIndex = _old_endflag_method;
                var item = $(_pageId + " .modify_box .item")[activeIndex];
                $(item).find(".icon").addClass("active");

                $(_pageId + " #fundvolbalance_mode1").html(tools.fmoney(results.fundvolbalance_mode1)); //持有份额
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, {isLastReq: false})
    }

    function reqFun102049() {
        service.reqFun102049(holdObj, function (data) {
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                layerUtils.iLoading(false);
                return;
            }
            //获取交易时间
            reqFun102008(results.open_date);
            $(_pageId + " #redconfirm_days").html(tools.ftime(results.open_date) + "到期");
        }, {isLastReq: false})
    }

    //获取交易时间
    function reqFun102008(dqDate) {
        var param = {
            type: "9",
            dqDate: dqDate
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                $(_pageId + " .date").html(tools.ftime(results.date));
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        jymm = "";
        $(_pageId + " #fund_sname").html("--");
        $(_pageId + " #fund_code").html("--");
        $(_pageId + " #redconfirm_days").html("--");
        $(_pageId + " #fundvolbalance_mode1").html("--");
        $(_pageId + " .date").html("--");
        $(_pageId + " .modify_box .item .icon").removeClass("active");
        $(_pageId + " #confirm").addClass("no_active");
        _endflag_method = "";
        _old_endflag_method = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
