// 所转财富授权页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#account_empower ";
        pageCode = "account/empower"
    var ut = require("../common/userUtil");
    var sms_mobile = require("../common/sms_mobile");
    var tools = require("../common/tools");
    var is_exist; // 是否已签约 0 未签约  1：已签约
    var payorg_id;//支付机构
    var pay_type; //支付方式
    var bank_serial_no;
    let userInfo;   //用户信息
    var jjsCustNo;//晋金所客户号
    function init() {
        $(_pageId + " #yzm").val('');
        sms_mobile.init(_pageId);
        userInfo = ut.getUserInf();
        jjsCustNo = appUtils.getSStorageInfo("jjsCustNo");//晋金所客户号
        //渲染用户信息
        setInfo();
        getPdf("prod", "000709", "1", "我已阅读并同意签署", ""); //获取协议
        setBankCardInfo();
    }
    function setInfo(){
        service.reqFun101020({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results && data.results.length > 0) {
                    var result = data.results[0];
                    $(_pageId + " .bank_name").text(result.bankName);
                    $(_pageId + " .mobile_ck").text(result.mobileWhole);
                    $(_pageId + " .card_no").text(result.bankAcct.substr(0, 4) + "****" + result.bankAcct.substr(-4));
                    $(_pageId + " .name").text(result.name);
                    $(_pageId + " .bankReservedMobile").text(result.bankReservedMobile);
                }
            } else {
                layerUtils.iAlert(error_info);
            }
        });
        
        
    }
    function getPdf(agreement_type, fund_code, agreement_sub_type, startStr, endStr) {
        //协议类型  产品Code 子类型 协议开始语 协议结束语
        // 产品协议增加留痕
        if ($(_pageId + ' .agreement').length > 0) {
                var agrStr = '<div class="agreement2">\n' +
                        '             <i></i>\n' +
                        '             <div style="margin: 0.1rem 0;">' + startStr + '<span class="click_agreement" style="color:#319ef2">《相关协议》</span>' + endStr + '</div>' +
                        '</div>'
                $(_pageId + " .agreement").html(agrStr);
            }
        var param = {
            agreement_type: agreement_type,
            fund_code: fund_code,
            agreement_sub_type: agreement_sub_type,
            bank_code: userInfo.bankCode
        };
        //获取协议
        service.reqFun102016(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return
            }
            var html = "";
            var agreement_id = [];
            var agreement_id_pay = [];
            for (var i = 0; i < data.results.length; i++) {
                html += '<li href="javascript:void(0);" class="xy" url="' + require("gconfig").global.oss_url + data.results[i].url + '">';
                html += "<span>《" + data.results[i].agreement_title + "》</span>";
                html += '</li>';
                agreement_id.push(data.results[i].agreement_id);
                if (data.results[i].agreement_type == "pay") { // 是支付协议
                    agreement_id_pay.push(data.results[i].agreement_id);
                }
            }
            $(_pageId + " .agreement_list").html(html);
            //点击相关协议
            appUtils.bindEvent($(_pageId + " .click_agreement"), function (e) {
                 $(_pageId + " .agreement_layer").show();
                    
            });
            //关闭协议
            appUtils.bindEvent($(_pageId + " .new_close_btn"), function (e) {
                 e.preventDefault();
                 e.stopPropagation();
                 $(_pageId + " .agreement_layer").hide();
                    
            });

            //查看PDF文件
            appUtils.preBindEvent($(_pageId + " .agreement_list"), ".xy", function (e) {
                e.preventDefault();
                e.stopPropagation();
                let title = '内容查看'
            	let statusColor = '#2F4D80'
            	let titleColor = '#ffffff'
				var param = {};
				param["funcNo"] = "50240"; 
           		param["url"] = $(this).attr("url");  
            	param["title"] = title;
            	param["statusColor"] = statusColor;
            	param["titleColor"] = titleColor; 
                require("external").callMessage(param);
            }, "click");
            //点击返回按钮
            appUtils.bindEvent($(_pageId + " .icon_back"), function () {
                appUtils.pageBack();
            });
            //勾选协议
            appUtils.preBindEvent($(_pageId + " .agreement"), ".agreement2", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var _self = this;
                if ($(this).find("i").hasClass("active")) {
                    $(this).find("i").removeClass("active")
                } else {
                    if (agreement_id_pay.length == 0) {
                        $(this).find("i").addClass("active");
                        return;
                    }
                    if (agreement_id_pay.length > 0) { //晋金宝 协议支付
                        agreement_id = agreement_id_pay; //只签署支付协议
                    }
                    service.reqFun106020({agreement_id: agreement_id.join(",")}, function (datas) {
                        if (datas.error_no == "0") {
                            var results = datas.results;
                            if (datas.length == 0) {
                                layerUtils.iAlert("网络繁忙，请稍后重试");
                                return;
                            }
                            var agreement_sign_no = results[0].agreement_sign_no;
                            $(_self).find("i").addClass("active");
                            $(_pageId + " .agreement2").attr("agreement_sign_no", agreement_sign_no)
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                }
            }, 'click');
        });
    }
    //设置银行卡信息  新晋金宝用户银行卡限额查询查询通联
    function setBankCardInfo() {
        service.reqFun102077({bank_code: userInfo.bankCode,pay_mode:'2'}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results && data.results.length > 0) {
                    var result = data.results[0];
                    is_exist = result.is_exist;
                    pay_type = result.pay_type;
                    payorg_id = result.payorg_id;
                }
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            var verificationCode = $(_pageId + " #yzm").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            let param = {
                isexist:is_exist,
                bank_serial_no:bank_serial_no ? bank_serial_no : '',
                messagecode : verificationCode,
                paytype:pay_type,
                jjs_cust_no:jjsCustNo,
                payorgid:payorg_id,
                sms_mobile:userInfo.bankReservedMobile
            }
            // if(is_exist == '1'){ //已签约
            // }
            if(is_exist == '1'){
                //需要校验验证码
                service.reqFun9177006(param, function (data) {
                    var error_no = data.error_no,
                        error_info = data.error_info;
                    if (error_no == "0") {
                        if (data.results && data.results.length > 0) {
                            appUtils.pageInit("login/userIndex", "account/empowerSuccess", {});
                        }
                    } else {
                        sms_mobile.clear();
                        $(_pageId + " #yzm").val("");
                        layerUtils.iAlert(error_info);
                    }
                });
            }else{
                service.reqFun177006(param, function (data) {
                    var error_no = data.error_no,
                        error_info = data.error_info;
                    if (error_no == "0") {
                        if (data.results && data.results.length > 0) {
                            // var result = data.results[0];
                            appUtils.pageInit("login/userIndex", "account/empowerSuccess", {});
                        }
                    } else {
                        layerUtils.iAlert(error_info);
                    }
                });
            }
        });
    }
    //点击发送验证吗事件
    appUtils.bindEvent($(_pageId + " #getYzm"), function () {
        if ($(_pageId + " #getYzm").attr("data-state") == "false") {
            return;
        }

        if (is_exist == "0") { //未签约
            var param = {
                "payorg_id": payorg_id,
                "pay_type": pay_type,
                "bank_code": userInfo.bankCode,
                "bank_acct": userInfo.bankAcct,
                "bank_reserved_mobile": userInfo.bankReservedMobile,
                "cert_no": userInfo.identityNum,
                "bank_name": userInfo.bankName,
                "sms_type": "168",
                "cert_type": "0",
                "cust_name": userInfo.name,
            }

            sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                if (data.error_no == "0") {
                    bank_serial_no = data.results[0].bank_serial_no
                }
            });
        } else { //已签约
            //获取验证码
            var param = {
                mobile_phone: userInfo.bankReservedMobile,
                // pre_content: "验证码：",
                // end_content: "，请勿泄露，您正在办理转入晋金财富业务，详询************。",
                type: "168",
                mobile_type: "2",
                send_type: "0",
                // cnl_code:"jjcf"
            };
            sms_mobile.sendPhoneCode(param);
        }

    }, 'click');
    // 销毁方法
    function destroy() {
        sms_mobile.destroy();
        $(_pageId + " #yzm").val('');
    }
    function pageBack() {
        appUtils.pageBack();
    }

    var empower = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = empower;
});
