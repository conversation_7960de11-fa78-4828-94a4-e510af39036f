// 咨询详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        gconfig = require("gconfig"),
        _pageId = "#moreDetails_consultationDetails ";
    var tools = require("../common/tools");
    var title, content, id, yyyymonth;

    function init() {
        id = appUtils.getPageParam("id") ? appUtils.getPageParam("id") : sessionStorage.articleId;// 文章id
        sessionStorage.articleId = id
        var param = {
            "id": id
        };
        service.reqFun102056(param, function (data) {
            if (data.error_no == "0") {
                if (data.results != undefined && data.results.length > 0) {
                    var createDate = data.results[0].createDate; // 公告发布时间
                    var title = data.results[0].title;// 公告标题
                    var htmlContent = data.results[0].htmlContent;// 公告内容
                    $(_pageId + " #title").html(title);
                    $(_pageId + " #fbTime").html(tools.ftime(createDate));
                    $(_pageId + " #content").html(htmlContent);
                    getAllA()
                }
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        });

    }
    function getAllA() {
        $(".announcement_detail a").each(function (index, element) {
            // $(this).attr('href',$(this).attr('href')+"&type=" + index);
            // console.log($(this).attr())
        });
    }
    // 绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 内外链
        appUtils.preBindEvent($(_pageId + " .announcement_detail"), ".chain", function () {
            let url
            if ($(this).attr('src') && $(this).attr('src') != '') {
                url = $(this).attr('src')
            } else {
                url = $(this).text()
            }
            if (!url || url == '') return
            let newStr = url.indexOf("http");
            let newStrs = url.indexOf("https");
            if (newStr == 0 || newStrs == 0) {
                appUtils.pageInit("moreDetails/consultationDetails", "guide/advertisement", {
                    "url": url,
                    "name": '',
                });
            } else {
                appUtils.pageInit("moreDetails/consultationDetails", url, {});
            }
        }, 'click');
        // 小程序
        appUtils.preBindEvent($(_pageId + " .announcement_detail"), ".applet", function () {
            let url
            if ($(this).attr('src') && $(this).attr('src') != '') {
                url = $(this).attr('src')
            } else {
                url = $(this).text()
            }
            if (!url || url == '') return
            return tools.jump_applet(url);
        }, 'click');
    }

    function destroy() {
        $(_pageId + " #title").html("");
        $(_pageId + " #fbTime").html("");
        $(_pageId + " #content").html("");
        $(_pageId + " #pop_layer").hide();
    }

    function pageBack() {

        appUtils.pageBack();
    }

    var noticeDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = noticeDetails;
});
