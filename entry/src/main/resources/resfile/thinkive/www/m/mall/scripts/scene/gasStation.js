//活动 - 攒钱加油站
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools.js"),
        validatorUtil = require("validatorUtil");
    var ut = require("../common/userUtil");
    require("chartsUtils");
    require('../common/echarts.min');
    var _pageId = "#scene_gasStation ";
    var _pageCode = "scene/gasStation"
    var layerUtils = require("layerUtils");
    var global = gconfig.global;
    var introduce;
    var ut = require("../common/userUtil.js");
    var essay_id;
    var catalog_id;
    let article_id,pageTitleName;
    /**
     * 初始化
     */
    async function init() {
        var userStatus = await getUserAuthenticationStatus();
        userAuthenticationStatus = userStatus[0].state //获取用户认证状态
        //渲染柱状图
        reqFun108056()
        //获取当前页面数据
        getCurrentPageData();
        //页面埋点初始化
        tools.initPagePointData();
        //获取投资必看模板
        let templateId = '207';//测试
        // let templateId = '129';//准生产
        let res = await getTemplate({templateId:templateId});
        $(_pageId + " .investmentMustSee").html(res);
        catalog_id = $(_pageId + " .productText").attr("catalog_id");
        getProductInterpretationList();
        
        
    };
    function reqFun108056(){
        service.reqFun108056({}, (data) => { 
            if (data.error_no != "0") return layerUtils.iAlert(data.error_info);
            let res = data.results;
            // //渲染柱状图数据
            setCategoryChart(res);
        })
    }
    //获取当前页面数据
    async function getCurrentPageData(){
        service.reqFun108058({}, (data) => { 
            if (data.error_no != "0") return layerUtils.iAlert(data.error_info);
            let res = data.results[0];
            // //渲染都在攒模块数据
            setAccumulating(res.douzaizan);
            // //渲染赚积分模块
            if(res.jiayouzhan && res.jiayouzhan.activityId) setEarnPoints(res.jiayouzhan);
            // //员工陪你攒
            if(res.peinizan && res.peinizan.investmentMonthAmt) setStaff(res.peinizan)
        })
    }

     //获取用户的认证状态
     async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    function setStaff(data){
        $(_pageId + " .investmentMonthAmt").text(data.investmentMonthAmt);
        $(_pageId + " .investmentStaffNum").text(data.investmentStaffNum);
        $(_pageId + " .investmentTotalAmt").text(data.investmentTotalAmt);
        $(_pageId + " .investmentTotalIncome").text(data.investmentTotalIncome);
        if(data.activityText && data.activityText.length){
            $(_pageId + " .activityText").text(data.activityText);
            $(_pageId + " .staffShow").show();
        }else{
            $(_pageId + " .staffShow").hide();
        }
        $(_pageId + " .catalogId").attr('id',data.catalogId);
        $(_pageId + " .catalogId").attr('operationname',data.catalogName);
        $(_pageId + " .catalogId").show();
        $(_pageId + " .gasStationStaff").show();
    }
    function setAccumulating(data){
        //平台攒钱总金额
        let total_assets = data.total_assets*1;
        $(_pageId + " .total_people").text(data.total_people + '人');
        if(total_assets > 10000){
            total_assets = (total_assets/10000).toFixed(0) + '万'
        }else{
            total_assets = total_assets.toFixed(0) + '元'
        }
        $(_pageId + " .total_assets").text(total_assets);
    }

    function setEarnPoints(data){
        // console.log(data,111)
        // introduce = data.introduce;
        $(_pageId + " .introduce").html(data.introduce);
        $(_pageId + " .holdDays").text(data.holdDays + '天');
        $(_pageId + " .totalAmt").text(data.totalAmt + '元');
        $(_pageId + " .totalPoints").text(data.totalPoints + '分');
        $(_pageId + " .gasStationHeader").show();
    }
    async function getProductInterpretationList(){
        service.reqFun181006({catalog_id:catalog_id}, (datas) => {
            if (datas.error_no != "0")  {
                scene_gasStation.bindPageEvent();
                return layerUtils.iAlert(datas.error_info);
            }
            list = datas.results[0];
            let catalog_idList = catalog_id.split(',');
            catalog_idList.map((item,index) => {
                let keyId = item*1;
                let html = ``;
                if(!list  || !list[keyId] || !list[keyId].length) return $(_pageId + " .productReed .list" +keyId).html(`<p style="padding:0.2rem 0;text-align: center;">暂无数据</p>`);
                list[keyId].forEach((items, index) => {
                    items.essay_id = items.article_id;
                    html += `
                        <li class="flex vertical_center ${index == 0 ? 'active_bg' : ''}" qualified_investor_visible="${items.qualified_investor_visible}" operationType="1" operationId="articleDetails" operationName="文章详情"  contentType="14" essay_id="${items.article_id}" catalog_id="${items.catalog_id}">
                           <em style="display:none">${JSON.stringify(items)}</em>
                            <p class="flex_1 m_font_size16">${items.title}</p>
                            <img class="${items.img_url ? '' : 'display_none'}" style="width:0.94rem;height:0.62rem" src="${global.oss_url + items.img_url}" alt="">
                        </li>
                    `
                })
                $(_pageId + " .productReed .list" +keyId).html(html);
                //手动绑定点击事件
                
            })
            scene_gasStation.bindPageEvent();
        })
    }
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    //渲染柱状图
    function setCategoryChart(chartData) {
        // 初始化图表
        var myChart = echarts.init(document.getElementById('categoryChart'));
        // 数据和颜色映射
        let data = [
            { value: '', name: '现在'},
            { value: '', name: '5年后'},
            { value: '', name: '10年后'},
            { value: '', name: '20年后'},
            { value: '', name: '30年后'}
        ];
        // chartData.map(item=>{
        //     item.value = item.income*1
        // })
        data.map((item,index) => {
            item.value = chartData[index].income*1;
            // item.value = 100;
        })
        let is_invest = chartData[0].is_invest;//是否展示柱状图的remark
        let is_investRemark = is_invest == '1' ? '按照您当前的持仓及定投计划，坚持下去：' : '按照首投1万元，每月定投1000元，坚持下去：'
        $(_pageId + " .investmentRemark").html(is_investRemark);
        const colors = ['rgb(49,97,268)', 'rgb(165,87,250)', 'rgb(251,82,98)', 'rgb(251,141,32)', 'rgb(253,202,40)'];
    
        // 配置项
        var option = {
            xAxis: {
                type: 'category',
                data: data.map(item => item.name),
                axisLabel: { color: '#666', fontSize: 14 },
                axisLine: { lineStyle: { color: '#ccc', width: 1 } }
            },
            yAxis: {
                type: 'value',
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { show: false },
                axisLabel: { show: false },
            },
            series: [{
                type: 'bar',
                barWidth: '30%',
                itemStyle: {
                    borderRadius: [4, 4, 0, 0],
                    color: (params) => colors[params.dataIndex],
                    opacity:0.9
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#fff', // 固定文字颜色
                    padding: [2, 4],
                    borderRadius: 4,
                    // formatter: ({ value,name,index }) => index > 0 ? `{a|${(value / 10000).toFixed(0)}万}` : `{b|${(value / 10000).toFixed(0)}万}`,
                    formatter: (item) => {
                        // console.log(item.dataIndex)
                        if (item.dataIndex == 0) {
                            return `{a|${item.value >= 10000 ? (item.value / 10000).toFixed(2) + '万': item.value.toFixed(2) + '元'}}`
                        } else if(item.dataIndex == 1) {
                            return `{b|${item.value >= 10000 ? (item.value / 10000).toFixed(2) + '万': item.value.toFixed(2) + '元'}}`
                        }else if(item.dataIndex == 2) {
                            return `{c|${item.value >= 10000 ? (item.value / 10000).toFixed(2) + '万': item.value.toFixed(2) + '元'}}`
                        }else if(item.dataIndex == 3) {
                            return `{d|${item.value >= 10000 ? (item.value / 10000).toFixed(2) + '万': item.value.toFixed(2) + '元'}}`
                        }else if(item.dataIndex == 4) {
                            return `{e|${item.value >= 10000 ? (item.value / 10000).toFixed(2) + '万': item.value.toFixed(2) + '元'}}`
                        }
                    },
                    rich:{
                        a: {
                            backgroundColor: 'rgb(49,97,268)', // 动态背景色
                            padding: [2, 4],
                            borderRadius: 4
                        },
                        b:{
                            backgroundColor: 'rgb(165,87,250)', // 动态背景色
                            padding: [2, 4],
                            borderRadius: 4
                        },
                        c:{
                            backgroundColor: 'rgb(251,82,98)', // 动态背景色
                            padding: [2, 4],
                            borderRadius: 4
                        },
                        d:{
                            backgroundColor: 'rgb(251,141,32)', // 动态背景色
                            padding: [2, 4],
                            borderRadius: 4
                        },
                        e:{
                            backgroundColor: 'rgb(253,202,40)', // 动态背景色
                            padding: [2, 4],
                            borderRadius: 4
                        }
                    }
                },
                data: data.map(item => item.value)
            }],
            grid: {
                left: '0%',
                right: '0%',
                top: '10%',
                bottom: '15%'
            }
        };
    
        // 设置配置项
        myChart.setOption(option);
    
        // 响应窗口尺寸变化
        window.addEventListener('resize', () => {
            myChart.resize();
        });
    }
    
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //赚积分规则
        appUtils.bindEvent($(_pageId + " .annual_details"), function () {
            $(_pageId + " #activityRules").show();
        });
        appUtils.bindEvent($(_pageId + " .okBtn"), function () {
            $(_pageId + " #activityRules").hide();
        });
        //跳转更多 产品解读
        appUtils.bindEvent($(_pageId + " .moreProduct"), function () {
            appUtils.setSStorageInfo("catalog_id", $(this).attr("id"));
            // tools.recordEventData('1','moreProduct','更多');
            appUtils.setSStorageInfo("productInterpretationName",$(this).attr("operationname"));
            appUtils.pageInit(_pageCode, "template/productInterpretation");
        });
        //跳转文章详情
        appUtils.preBindEvent($(_pageId + " .productReed .list"), "li", function (e) {
            pageTitleName = $(this).find("p").text();
            catalog_id = $(this).attr("catalog_id");
            essay_id = $(this).attr("essay_id");
            let qualified_investor_visible = $(this).attr("qualified_investor_visible");//是否需要验证合格投资者
            //tools.recordEventData('1','articleDetails','文章详情',{essayId: essay_id});
            if(userAuthenticationStatus == '0' && qualified_investor_visible == '1'){
                if (!ut.hasBindCard(_pageCode)) return;
                return $(_pageId + " .qualifiedInvestor").show();
            }
            appUtils.pageInit(_pageCode, "highVersion/articleDetails",{essay_id: essay_id,catalog_id: catalog_id});
        }, 'click');
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            // tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_pageCode)
        });
        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    $(_pageId + " .qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    appUtils.pageInit(_pageCode, "highVersion/articleDetails",{essay_id:essay_id,catalog_id: catalog_id});
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
         // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            $(_pageId + " .qualifiedInvestor").hide();
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " .gasStationHeader").hide();
        $(_pageId + " .gasStationStaff").hide();
        $(_pageId + " .catalogId").hide();
        $(_pageId + " #activityRules").hide();
        $(_pageId + " .staffShow").hide();
        article_id = '';
        userAuthenticationStatus = '';
        tools.recordEventData('4','destroy','页面销毁');
    };
    var scene_gasStation = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = scene_gasStation;
});
