/**
 * <AUTHOR>
 * @date: 2016-3-22
 * @description 统一账号登录验证 * 
 */
define(function(require, exports, module) {
    var gconfig = require("gconfig"); 
    var global = gconfig.global;
	var digitalSignatureUtils = require("digitalSignatureUtils");
	var aes = require("aes");
	var des = require("des");
	var endecryptUtils = require("endecryptUtils");
	var ajax = require("ajax"); 
	var signKey = '';
	var merchant_id = '';
	var test = '';
	var func = '';
	
	/**
	 * 统一登录账户登录
	 * @curPageID 当前页面ID
	 * @toPageID 需要跳转的SSO登录页面ID
	 * @callback 登录成功后的回调方法(已经登录情况)
	 * @okCallback 本身当前应用已经登录，执行回调
	 * @desc 统一登录处理逻辑如下
	 * 先判断cookie，如果不存在值，那么直接跳转到统一登录界面去登录。
	 * 如果存在值，再判断是否session里面是否存在值，如果session不存在，直接获取cookie里面token,去登录
	 * 如果session里面存在，则判断cookie值和session里面值是否匹配,则不用登录(当前已经登录过)，
	 * 如果不匹配(其他站点可能换了用户登录)，则跳转到统一登录界面做登录操作
	 * 	
	 * */
	function ssoLogin(url,callback){
		ajax.request(url,{},callback);		
	}
	
	+function(){test='thin';}();
	+function(){func='func';}();
	
	/**
	 * 用户登录(当前应用)
	 * @url，远程SSO登录地址
	 * @param 非业务参数
	 * @serviceParam 业务参数(比如用户名和密码，验证码等)
	 * */
	function ssoRemoteLogin(url,param,callback){   
	   var bizcode = param['bizcode'];
	   delete param.bizcode;
       var obj = ssoSignFunc(bizcode,param);    
        if(url)
        {
       	 	ajax.request(url,obj,callback);
        }
	}
		
	
	 /*统一登录方法转换
	  * @bizcode，业务接口编码
	  * @param，sso登录相关参数
	  */
	 
	function ssoSignFunc(bizcode,param){
		var serviceParam = {};
		serviceParam['bizcode'] = bizcode;//业务接口编码	        
	    var json = JSON.stringify(param); 	 
	    if(global.encryMode == 'des'){//des加密
		    var deskey = endecryptUtils.desEncrypt(signKey,json);
		    serviceParam['data'] = deskey;
		    serviceParam['encry_mode'] = 'des'; 
	    }else if(global.encryMode == 'aes'){//aes加密  	
	    	var aeskey = signKey.substring(0,16);
	    	var desc = endecryptUtils.aesEncrypt(aeskey,json);
		    serviceParam['data'] = desc;
		    serviceParam['encry_mode'] = 'aes'; 
	    }else {
	    	var base64 = endecryptUtils.encoderBase64(json);
	    	 serviceParam['data'] = base64;
	    }
	    
	    if(merchant_id == ""){ //对商户不存在问题的保护
	    	merchant_id = "00000001";
	    }
		
        serviceParam['merchant_id'] = merchant_id;//商户ID
	    serviceParam['signKey']  =  signKey;   
	    serviceParam['request_id']  =  guid();    
		
		//将参数按照首字母排序组装字符串age=30&name=test，当然也可以不做，参数也不多，直接手动排序选择下就行
//        var obj = JSON.stringify(serviceParam);
//        var signStr = obj.replace(/{/g,"").replace(/}/g,"").replace(/"/g,"").split(",").sort(function(a,b){
//        	return a[0]>b[0];
//        }).join("&").replace(/:/g,"=");      
	    
	    var signStr = "bizcode=" + serviceParam['bizcode'];
	    signStr += "&data=" + serviceParam['data'];
	    signStr += "&encry_mode=" + serviceParam['encry_mode'];
	    signStr += "&merchant_id=" + serviceParam['merchant_id'];
	    signStr += "&request_id=" + serviceParam['request_id'];
	    signStr += "&signKey=" + serviceParam['signKey'];
        
        //4：组装业务请求参数
        var signKeys = digitalSignatureUtils.md5.md5(signStr).toUpperCase();
        var newStr = signStr.substring(0,signStr.indexOf('signKey')-1);
        newStr = (newStr+"&sign="+signKeys);
        
        
        //为了post提交数据，这里需要转换JSON对象模式
        return newStr;    
		
	}
	
	function Bytes2Str(arr)    
	{    
	    var str = "";    
	    for(var i=0; i<arr.length; i++)    
	    {    
	       var tmp = arr[i].toString(16);    
	       if(tmp.length == 1)    
	       {    
	           tmp = "0" + tmp;    
	       }    
	       str += tmp;    
	    }    
	    return str;    
	 } 
	
	//获取guuid
	function guid() 
	{
	    function S4() {
	       return (((1+Math.random())*0x10000)|0).toString(16).substring(1);
	    }
	    return (S4()+S4()+"-"+S4()+"-"+S4()+"-"+S4()+"-"+S4()+S4()+S4());
    }
	
	/**
	 * 把URL参数解析为一个对象
	 * @url地址
	 * */
	function parseQueryString(url){
        var items=url.split("&");
        var result={};
       for(var i=0,len=items.length;i<len; i++){
       	     var obj = items[i];
       	     var startIndex = obj.indexOf('=');
       	     var key = obj.substring(0,startIndex);
       	     var value = obj.substring(startIndex+1);
             result[key] = value;
       }

      return result;
   }
	
	
	
	var doDecrypt = function(data){
		var keyHex = aes.enc.Utf8.parse(test+func);
		var iv = aes.enc.Utf8.parse(test+func); 
		var valueHex = aes.enc.Base64.parse(data);  
		var decrypted = aes.AES.decrypt({
			ciphertext: valueHex
		}, keyHex, {
			iv: iv,
			mode: aes.mode.CBC
		});  
	    return JSON.parse(decrypted.toString(aes.enc.Utf8));
	};
	
	/**
	 * 解密统一登录签名key
	 * 检查当前站点cookie和session值是否一致* 
	 * @渠道，比如理财就是mall,小贷就是xdt
	 * */
	function checkCookieValidity(merid){
		
		//用于给内存中赋值解密签名key
		var signKeys = global.ssoSignKey;
		if(signKeys)
		{
			var signalls = '';
			try{
				signalls = doDecrypt(signKeys);
			}catch(e){
				//TODO handle the exception
			}
			
			if(signalls)
			{
				signKey = signalls.signKey;
				merchant_id = signalls.merchant_id;
			}
		}

	}
	
	module.exports = {
		'ssoLogin': ssoLogin,
		'ssoRemoteLogin': ssoRemoteLogin,
		'checkCookieValidity': checkCookieValidity,
		'ssoSignFunc': ssoSignFunc,
		'test': test,
		'func': func
	};
});