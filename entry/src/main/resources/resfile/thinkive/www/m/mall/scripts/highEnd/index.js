// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "highEnd/index",
        _pageId = "#highEnd_index ";

    var productInfo,stepsInfo;
    var isAllPass = false;  //是否全部通过验证
    var userInfo;
    var ut = require("../common/userUtil");
    var stepArr = []; //0代表未完成，1代表已完成
    var cust_risk_level; //风险等级
    function init() {
        stepArr =  [0, 0, 0, 0, 0, 0]; //0代表未完成，1代表已完成
        productInfo = appUtils.getSStorageInfo("productInfo")
        // stepsInfo = appUtils.getSStorageInfo("stepsInfo")
        userInfo = ut.getUserInf();
        // handleStatus(stepsInfo)  //处理初始化状态
        //页面埋点初始化
        tools.initPagePointData();
        initState()
        //判断风险
        // show_risk()
    }
    function show_risk(riskName){
        // console.log(riskName,111)
        let riskLevel = userInfo.riskLevel
        let risk_level = productInfo.risk_level
        if((riskLevel == '00' || riskLevel == 'C0') && risk_level != 'R1'){
            
        }
        
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //点击各项按钮
        appUtils.bindEvent($(_pageId + " .operation-btn"), function () {
            var toPage = $(this).attr('data-to');
            if (isBtnDis(this)) return;
            var index = $(this).attr('data-index');
            var sumStep = 0;
            for (var i = 0; i < index; i++) {
                sumStep += stepArr[i];
            }
            if (index != sumStep) {
                layerUtils.iAlert("请完成上一步操作");
            } else {
                if (toPage == "highEnd/suitResult") {
                    riskAssessment(toPage) //适当性评估处理
                } else if (toPage == "highEnd/qualifiedInvestor1") { //合格投资人
                    appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor1");
                } else if (toPage) {
                    appUtils.pageInit(_page_code, toPage)
                }
            }

        })
        $(_pageId + '#goNext').on('click', function () {
            /*if(productInfo.prod_sub_type2 == "93" && isAllPass) {
                appUtils.pageInit(_page_code, 'yuanhui/purchase')
                return
            }*/
            let data = appUtils.getSStorageInfo("buyTransferData");
            if(isAllPass && sessionStorage.isShowTranferPro == 1 && data && data.transfer_vol) return appUtils.pageInit(_page_code, 'myTransfer/buyTransferProduct');
            isAllPass && appUtils.pageInit(_page_code, 'highEnd/purchase');

        })

    } 

    // 检测按钮是否可点击
    function isBtnDis(btn) {
        return $(btn).hasClass('completed') || $(btn).hasClass('reviewing')
    }

    // 风险评估
    function riskAssessment(toPage) {
        var productRisk = +(productInfo.risk_level.substr(1));//产品风险等级
        if (cust_risk_level.substr(1) >= productRisk) { //符合
            appUtils.pageInit(_page_code, toPage)
        }
        // else if (cust_risk_level.substr(1) == 1) { //不符合且最低
        //     layerUtils.iConfirm('该产品超出您的风险等级', function () {
        //         appUtils.pageInit(_page_code, 'safety/riskQuestion')
        //     }, function () {
        //     }, '重新评测', '取消')
        // }
        else { //不符合不是最低
            appUtils.pageInit(_page_code, toPage)
        }
    }
    function handleStatus(useData){
        cust_risk_level = useData.cust_risk_level;
        handleRisk(useData.risk_state, useData.cust_risk_name); //risk_state 风险测评状态 0:未测评 1:已测评 2:测评过期  cust_risk_name 风险等级名称
        handleUserInfo(useData.perfect_info); //完善信息状态  0:未完善 1:已完善 2:证件到期
        handleIndentify(useData.accredited_investor); //合格投资人状态 0:不合格, 1:合格, 2:豁免, 3:未鉴定, 4:已到期, 5:审核中
        handleFit(useData.appropriateness); //适当性评估 0:未评估 1:已评估
        handleRiskNote(useData.risk_disclosure); //风险揭示书 0 未签署 1 已签署
        if(productInfo.is_vedio_record == "1") {
            $(_pageId + " #videoStep").show();
            $(_pageId + " #riskNoteStep .step-line-active2").show();
            handleVideo(useData.video_witness); //视频见证状态 0:未见证 1:已见证 2:审核中
        } else {
            stepArr.length = 5;
            $(_pageId + " #videoStep").hide();
            $(_pageId + " #riskNoteStep .step-line-active2").hide();
        }
        setProgress()
    }
    // 初始化导航状态
    function initState() {
        var param = {fund_code: productInfo.fund_code}
        service.reqFun101032(param, function (res) {
            if (+res.error_no == 0) {
                var useData = res.results[0]
                cust_risk_level = useData.cust_risk_level;
                handleRisk(useData.risk_state, useData.cust_risk_name); //risk_state 风险测评状态 0:未测评 1:已测评 2:测评过期  cust_risk_name 风险等级名称
                handleUserInfo(useData.perfect_info); //完善信息状态  0:未完善 1:已完善 2:证件到期
                handleIndentify(useData.accredited_investor); //合格投资人状态 0:不合格, 1:合格, 2:豁免, 3:未鉴定, 4:已到期, 5:审核中
                handleFit(useData.appropriateness); //适当性评估 0:未评估 1:已评估
                handleRiskNote(useData.risk_disclosure); //风险揭示书 0 未签署 1 已签署
                if(productInfo.is_vedio_record == "1") {
                    $(_pageId + " #videoStep").show();
                    $(_pageId + " #riskNoteStep .step-line-active2").show();
                    handleVideo(useData.video_witness); //视频见证状态 0:未见证 1:已见证 2:审核中
                } else {
                    stepArr.length = 5;
                    $(_pageId + " #videoStep").hide();
                    $(_pageId + " #riskNoteStep .step-line-active2").hide();
                }
                setProgress()
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })
    }

    /*
     * 处理风险等级状态
     * @param { Number } state 状态代码
     * @param { String } riskName 等级名称
     */
    function handleRisk(state, riskName) {
        // console.log(state,riskName)
        let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag ? appUtils.getSStorageInfo("user").invalidFlag : '0'
        if (state == '1') {
            stepArr[0] = 1;
            // productRisk = +(productInfo.risk_level.substr(1));//产品风险等级
            $(_pageId + '#riskStep .operation-btn').addClass('completed');
            // if (cust_risk_level.substr(1) < productRisk) { //不符合
            //     $(_pageId + " .risk").show();
            //     $(_pageId + " .risk_standard").text(common.convRiskLevel(productRisk)).show;
            //     $(_pageId + '#riskStep .operation-btn').addClass('after');
            // } else {
                
            // }
        } else if (state == '2') {
            stepArr[0] = 0;
            $(_pageId + '#riskStep').addClass('overdue');
        } else {
            stepArr[0] = 0;
        }
        $(_pageId + '#riskStep .operation-txt').text(invalidFlag == '0' ? riskName != "" ? riskName : '未测评' : riskName + '(已到期)' || '')
        // let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
        // if (state == '1') {
        //     stepArr[0] = 1;
        //     $(_pageId + '#riskStep .operation-btn').addClass('completed');
        // } else if (state == '2') {
        //     stepArr[0] = 0;
        //     $(_pageId + '#riskStep').addClass('overdue');
        // } else {
        //     stepArr[0] = 0;
        // }
        // $(_pageId + '#riskStep .operation-txt').text(invalidFlag == '0' ? riskName:riskName + '(已到期)' || '')
        // show_risk()
    }

    /*
     * 处理完善信息项
     * @param { Number } state 状态代码
     */
    function handleUserInfo(state) {
        if (state == '1') {
            stepArr[1] = 1;
            $(_pageId + '#infoStep .operation-btn').addClass('completed')
        } else if (state == '2') {
            stepArr[1] = 0;
            $(_pageId + '#infoStep').addClass('overdue')
            $(_pageId + '#infoStep .operation-btn').attr("data-to", "account/uploadIDCard")
        } else {
            $(_pageId + '#infoStep .operation-btn').attr("data-to", "highEnd/perfectInfo");
            stepArr[1] = 0;
        }
    }

    /*
     * 处理投资人认证状态
     * @param { Number } state 状态代码
     *  0:不合格, 1:合格, 2:豁免, 3:未鉴定, 4:已到期, 5:审核中
     */
    function handleIndentify(state) {
        var stateName = ''
        if (state == '1') {
            $(_pageId + '#indentifyStep .operation-btn').addClass('completed');
            stepArr[2] = 1;
        } else if (state == '2') {
            $(_pageId + '#indentifyStep').addClass('remit')
            $(_pageId + '#indentifyStep .operation-btn').addClass('completed');
            stepArr[2] = 0;

        } else if (state == '4') {
            $(_pageId + '#indentifyStep').addClass('overdue')
            stepArr[2] = 0;

        } else if (state == '5') {
            $(_pageId + '#indentifyStep .operation-btn').addClass('reviewing')
            stepArr[2] = 0;

        } else if (state == '0') {
            stateName = '不合格'
            $(_pageId + '#indentifyStep').addClass('fail')
            stepArr[2] = 0;
        }
        $(_pageId + '#indentifyStep .operation-txt').text(stateName || '')
    }

    /*
     * 处理适当性评估
     * @param { Number } state 状态代码
     * 0:未评估 1:已评估
     */
    function handleFit(state) {
        if (state == '1') {
            stepArr[3] = 1;
            $(_pageId + '#fitStep .operation-btn').addClass('completed')
        } else {
            stepArr[3] = 0;
        }
    }

    /*
     * 处理适风险揭示书
     * @param { Number } state 状态代码
     * 0 未签署 1 已签署
     */
    function handleRiskNote(state) {
        if (state == '1') {
            stepArr[4] = 1;
            $(_pageId + '#riskNoteStep .operation-btn').addClass('completed')
        } else {
            stepArr[4] = 0;

        }
    }

    /*
     * 处理视频见证
     * @param { Number } state 状态代码
     * 0:未见证 1:已见证 2:审核中
     */
    function handleVideo(state) {
        if (state == '1') {
            stepArr[5] = 1;
            $(_pageId + '#videoStep .operation-btn').addClass('completed')
        } else if (state == '2') {
            stepArr[5] = 0;
            $(_pageId + '#videoStep .operation-btn').addClass('reviewing')
        }
    }

    /*
     * 设置进度条状态
     */
    function setProgress() {
        var steps = $(_pageId + '#stepList .step');
        var activeIndex = -1
        isAllPass = true
        for (var i = stepArr.length - 1; i >= 0; i--) {
            if (steps.eq(i).find('.operation-btn').hasClass('completed')) {
                activeIndex = i
            } else {
                isAllPass = false
            }
            i <= activeIndex && steps.eq(i).addClass('active')
        }
        isAllPass && $(_pageId + '#goNext').removeClass('disabled')
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        stepArr = []; //0代表未完成，1代表已完成
        isAllPass = false;
        $(_pageId + " #videoStep").hide();
        $(_pageId + " #riskNoteStep .step-line-active2").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
