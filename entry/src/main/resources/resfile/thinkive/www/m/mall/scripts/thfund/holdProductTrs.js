// 持有理财查询详情
////@版本: 1.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _page_code = "thfund/holdProductTrs",
        _pageId = "#thfund_holdProductTrs";
    var page = 1;
    var toZT = "0";

    function init() {
        chengongjiaoyi();
    }

    function chengongjiaoyi() {
        var productCode = appUtils.getPageParam();
        var param = {
            prodCode: productCode,
            func_no: "902224",
        };
        service.reqFun177005(param, function (data) {
            if (data.error_no == 0) {
                var str = "";
                if (data.results.length > 0) {
                    for (var i = 0; i < data.results.length; i++) {
                    	//交易日期
                        var trade_date = data.results[i].trade_date.split(" ")[0];
                        //trade_date=dateFormat(trade_date);
                        //交易金额
                        var tot_price = data.results[i].tot_price;
                        //交易类型
                        var busi_name = data.results[i].busi_name;
                        //备注
                        var type = data.results[i].remark;
                        str += '<div class="region">' +
                            '<span class="inform" style="display:none;">' + JSON.stringify(data.results[i]) + '</span>' +
                            '<div class="list date">' + trade_date + '</div>' +
                            '<div class="list money">' + tot_price + '</div>' +
                            '<div class="list type">' + busi_name + '</div>' +
                            '<div class="list status">' + type + '</div>' +
                            '</div>'
                    }
                } else {
                    str += "<div class='my_finance'>暂无数据...</div>";
                }
                $(_pageId + " .box").html(str);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    
//    function  dateFormat(str){
//    	if(str.indexOf("-") != -1){
//    		return str.slice(0,10);
//    	}
//    	return str.slice(0,4)+"-"+str.slice(5,6)+"-"+str.slice(7,8);
//    }

    function bindPageEvent() {
        $(_pageId + " .icon_back").on("click", function () {
            pageBack();
        })
    }

    function destroy() {
    }

    function pageBack() {
        appUtils.pageInit(_page_code, "thfund/JJTouList");
    }

    var holdProductTrs = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack

    };
    //  暴露对外的接口
    module.exports = holdProductTrs;
});