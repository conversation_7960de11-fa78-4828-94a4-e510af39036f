// 银行卡管理
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#account_bankCardAdministration";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var perPage = "account/bankCardAdministration";
    var ut = require("../common/userUtil");
    var userInfo;

    function init() {
        userInfo = ut.getUserInf();
        $(_pageId + " article").empty();
        changeCardMessageInfo(); //换卡消息队列
        setBankCard(); //设置银行卡信息
        changeCard(); //换卡状态
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .grid_02"), function () {
            $(_pageId + " .pop_layer4").hide();
            $(_pageId + " .card_rules").hide();
        });
        appUtils.preBindEvent($(_pageId), ".bank_tips", function () {
        	appUtils.setSStorageInfo("isShowChangeBank",'0')
            appUtils.pageInit(perPage, "safety/bankInfo");
        }, 'click');
    }
    //换卡状态查询
    function changeCard(callback) {
        service.reqFun101024({}, function (resultVo) {
            // replace_status 0:待审核 1:已审核待确认 2:换卡成功 3:换卡失败 4: 非换卡期间
            // 得到当前页
            if (resultVo.error_no == "0") {
                var dataList = resultVo.results[0];
                if (dataList.replace_status == "0") {
                    $(_pageId + " .beAudited").show();
                    $(_pageId + " .beConfirm").hide();
                    $(_pageId + " .change_card").hide();
                    return
                }
                // if (dataList.replace_status == "1") {
                //     $(_pageId + " .beAudited").hide();
                //     $(_pageId + " .beConfirm").show();
                //     $(_pageId + " .change_card").hide();
                //     layerUtils.iConfirm("您的换卡已通过审核,请确认", function () {
                //         appUtils.pageInit(perPage, "safety/changecardConfirm");
                //     })
                //     return;
                // }
                $(_pageId + " .beAudited").hide();
                $(_pageId + " .beConfirm").hide();
                $(_pageId + " .change_card").show();
                if (callback) callback();
            } else {
                $(_pageId + " .beAudited").hide();
                $(_pageId + " .beConfirm").hide();
                $(_pageId + " .change_card").show();
                layerUtils.iAlert(resultVo.error_info);
            }
        })
    }

    //设置银行卡信息
    function setBankCard() {
        //查询我的银行卡
        var bankCard = userInfo.bankAcct;
        var imgurl = "images/bank_" + userInfo.bankCode + ".png";
        var bankName = userInfo.bankName;
        $(_pageId + " article").append("<div class='bank_box'><div class='bank_inner flow in'><h4><em><img src=" + imgurl + " width=28'></em>" + bankName + "<span class='beAudited pull-right' style='display: none'>审核中</span><span class='beConfirm pull-right' style='display: none'>待确认</span> <span class='change_card pull-right' style='display: none'><a href='javascript:void(0)' id='changCard'>换卡</a></span></h4><p>"
            + bankCard.substr(0, 4) + " **** **** " + bankCard.substr(bankCard.length - 4, 4) + "</p><div class='union_pay'><img src='images/icon_union.png' width='100%'></div></div></div><div class='bank_tips' style='text-align:center' ><a  href='javascript:void(0)'>*支持的银行卡</a></div>");

        appUtils.bindEvent($(_pageId + " .beConfirm"), function () {
            layerUtils.iConfirm("您的换卡已通过审核,请确认", function () {
                appUtils.pageInit(perPage, "safety/changecardConfirm");
            })
        });

        appUtils.bindEvent($(_pageId + " #changCard"), function () {
            changeCardJY();
        });
        appUtils.bindEvent($(_pageId + " #kjchangCard"), function () {
            kjchangeCardJY();
        });
    }

    //换卡消息查询
    function changeCardMessageInfo() {
        service.reqFun101028({type: "0"}, function (data) {
            if (data.error_no == "0") {
                var results = data.results;
                var success = function (id) {
                    /*更新换卡标识*/
                    service.reqFun101029({"id": id}, function () {

                    });
                };
                if (results.length > 0) {
                    layerUtils.iAlert(results[0].message_text, -1, success(results[0].id));
                }
            } else {
                layerUtils.iAlert(data.error_info)
            }
        })
    }

    //换卡校验
    function changeCardJY() {
        var param = {
            "bank_acct": userInfo.bankAcct
        }
        service.reqFun101044({}, function (data) { // 查询是否存在在途资金
            if (data.error_no == "0") {
                service.reqFun101027(param, function (data) { //查询资产是否为0
                    if (data.error_no == "0") {
                        layerUtils.iLoading(false);
                        appUtils.pageInit(perPage, "safety/kjyanzheng");
                        return;
                    }else{
                        appUtils.pageInit(perPage, "safety/yanzheng");
                        return;
                    }
                }, {isLastReq: false});
            }else{
                layerUtils.iConfirm("您有在途资金尚未到账，请到账后再试", function () {
                    return;
                }, function () {
                    $(_pageId + " .pop_layer4").show();
                    $(_pageId + " .card_rules1").show();
                }, "", "查看规则");
            }
        })
    }

    //快捷换卡校验
    function kjchangeCardJY() {
        var param = {
            "bank_acct": userInfo.bankAcct
        }
        service.reqFun101044({}, function (data) { // 查询是否存在在途资金
            if (data.error_no == "0") {
                service.reqFun101027(param, function (data) { // 查询总资产是否为0
                    if (data.error_no == 0) {
                        appUtils.pageInit(perPage, "safety/kjyanzheng");
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iConfirm("您平台总资产为0时，才可以进行快捷换卡", function funOk() {
                            return;
                        }, function funcNo() {
                            $(_pageId + " .pop_layer4").show();
                            $(_pageId + " .card_rules2").show();
                        }, "", "查看规则");
                    }
                });
            }else{
                layerUtils.iConfirm("您有在途资金尚未到账，请到账后再试", function () {
                    return;
                }, function () {
                    $(_pageId + " .pop_layer4").show();
                    $(_pageId + " .card_rules2").show();
                }, "", "查看规则");
            }
        })
    }

    function destroy() {
        $(_pageId + " .pop_layer4").hide();
        $(_pageId + " .card_rules").hide();
    }

    function pageBack() {
        var routerList = appUtils.getSStorageInfo("routerList");
        if (routerList.indexOf("account/personMessage") > -1) {
            appUtils.pageInit(perPage, "account/personMessage");
        } else {
            appUtils.pageInit(perPage, "account/myAccount");
        }
    }

    var bankCardAdministration = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankCardAdministration;
});
