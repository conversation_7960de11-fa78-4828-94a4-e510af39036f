// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "highEnd/suitResult",
        _pageId = "#highEnd_suitResult ";
    var ut = require("../common/userUtil");
    var isFit;
    var submitBtn = false;
    var userInfo;
    var productInfo;

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        getUserInfo()
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //勾选同意
        $(_pageId + '.suit-sign-text').on('click', function () {
            if ($(this).hasClass('checked')) {
                $(this).removeClass('checked')
                    .find('.mini-check-icon').removeClass('checked')
                $('#suitSignBtn').addClass('disabled')
                tools.recordEventData('1','sure','取消选中');
                submitBtn = false

            } else {
                $(this).addClass('checked')
                    .find('.mini-check-icon').addClass('checked')
                $('#suitSignBtn').removeClass('disabled')
                tools.recordEventData('1','sure','选中');
                submitBtn = true
            }
        })

        //同意签署点击
        $(_pageId + '#suitSignBtn').on('click', function () {
            if (submitBtn) {
                var param = {
                    type: isFit,
                    fund_code: productInfo.fund_code
                }
                tools.recordEventData('1','suitSignBtn','同意签署');
                service.reqFun101035(param, function (res) {
                    if (+res.error_no == 0) {
                        pageBack()
                    } else {
                        layerUtils.iAlert(res.error_info)
                    }
                })
            }
        })
    }
    //获取用户详情
    function getUserInfo(){
        service.reqFun101020({}, function (data) {
            var error_no = data.error_no;
            if (error_no == "0") {
                var results = data.results[0];
                common.setLocalStorage("mobileWhole", results.mobileWhole);
                ut.saveUserInf(results);
                layerUtils.iLoading(false);
                productInfo = appUtils.getSStorageInfo("productInfo");
                userInfo = ut.getUserInf();
                $(_pageId + " .name").text(userInfo.name);
                $(_pageId + " .identityNum").text(userInfo.identityNum);
                var riskLevel = userInfo.riskLevel+"";
                if(riskLevel[0] == "0"){
                    riskLevel = "C"+riskLevel.slice(1);
                }
                $(_pageId + " .riskName").text(userInfo.riskName + '(' + riskLevel + ')')
                $(_pageId + " .prod_sname").text(productInfo.prod_sname)
                $(_pageId + " .risk_level_desc").text(productInfo.risk_level_desc )
                initResult()

            } else {
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, "网络繁忙,请重新登录");
                appUtils.clearSStorage("_loginInPageCode");
                appUtils.clearSStorage("_loginInPageParam");
                appUtils.clearSStorage("_isLoginIn");
                appUtils.clearSStorage();
                common.gestureLogin();
            }
        })
    }
    //初始化页面信息
    function initResult() {
        if (userInfo.riskLevel.substr(1) >= productInfo.risk_level.substr(1)) {
            $(_pageId + '#fitAgreement').show()
            $(_pageId + '#unFitAgreement').hide()
            $(_pageId + '#Investor').show()
            $(_pageId + '#unInvestor').hide()
            isFit = "1";
        } else {
            $(_pageId + '#fitAgreement').hide()
            $(_pageId + '#unFitAgreement').show()
            $(_pageId + '#Investor').hide()
            $(_pageId + '#unInvestor').show()
            isFit = "2";
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + '#fitAgreement').hide()
        $(_pageId + '#unFitAgreement').hide()
        $(_pageId + '#Investor').hide()
        $(_pageId + '#unInvestor').hide()
        $(_pageId + " .suit-sign-text .mini-check-icon").removeClass("checked")
        $(_pageId + '#suitSignBtn').addClass("disabled");
        submitBtn = false;
        isFit = "";
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
