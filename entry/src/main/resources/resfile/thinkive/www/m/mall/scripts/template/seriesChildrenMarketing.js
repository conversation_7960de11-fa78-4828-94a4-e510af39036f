// 系列-子女成长计划 营销页

define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        _page_code = "template/seriesChildrenMarketing",
        _pageId = "#template_seriesChildrenMarketing ";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var monkeywords = require("../common/moneykeywords");
    var global = gconfig.global;
    var combProdMarketing_player = '';
    var ut = require("../common/userUtil");
    let combProdMarketDetail //new 一个 vue 实例
    let combProductInfo;
    let series_info;
    let activeClass = '12';
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];
    var threshold_amount; //起购金额
    var step_amt; //递增金额
    var invest_amount = 0; // 定投金额 没有可以是0
    var isFirstPurchase;// 是否首次购买产品
    var activity_id_string = [];//获取ID集合
    var startTime, timer = null, newData;
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '2',
                fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (series_info.series_id ? series_info.series_id : combProductInfo.fund_code)
            }
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    async function init() {
        //页面埋点初始化
        // tools.initPagePointData();
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        activity_id_string = [];
        series_info = appUtils.getSStorageInfo("series_info");
        
        newData = appUtils.getPageParam();
        // 同行好友投教活动
        if (newData && newData.activity_id) {
            startTime = Date.now();
            var readingTime = newData.duration && parseFloat(newData.duration) * 1000;
            if (newData.task_id && newData.task_type == '1') {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        tools.vipTjActivityIn({ activity_id: newData.activity_id, task_id: newData.task_id });
                    }, readingTime)
                }
            }
        }
        if (newData && (newData.jump_page_prodid || newData.busi_id)) {    //弹窗特殊与情况
            appUtils.setSStorageInfo("financial_prod_type", newData.financial_prod_type);
            series_info = {
                series_id: newData.jump_page_prodid,
            }
            combProductInfo = series_info;
            appUtils.setSStorageInfo("series_info", series_info);
            appUtils.setSStorageInfo("combProductInfo", combProductInfo)
        } else {
            combProductInfo = appUtils.getSStorageInfo("series_info"); // 获取从缓存带过来的产品信息
        }
        //页面埋点初始化
        tools.initPagePointData({fundCode:appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (series_info.series_id ? series_info.series_id : combProductInfo.fund_code)});
        let html = await setTemplate() //拿到模板数据
        //缓存当前是否为系列投顾从产品
        appUtils.setSStorageInfo("isSeriesComb", '1');
        $(".main_seriesChildrenMarketing").html(html)   //渲染模板
        $(_pageId + " .isHoldShow").hide();
        $(_pageId + " .isHoldHide").hide();
        $(_pageId + " .targeting").hide();
        $(_pageId + " .speciaSpan").addClass("specialActive")
        common.systemKeybord(); // 解禁系统键盘
        combProdMarketDetail = new Vue({
            el: '#main_seriesChildrenMarketing',
            data() {
                return {
                    pageState: false,//当前页面状态
                    oss_url: global.oss_url,
                    detailsInfo: {
                    },//详情信息
                    subFundList: [],
                    holdList: [],//持有列表
                    moreName: "更多",
                    isHold: false,
                    timeListNew: [  //区间
                        {
                            name: '近1个月',
                            section: "1"
                        },
                        {
                            name: '近3个月',
                            section: "3"
                        },
                        {
                            name: '近6个月',
                            section: "6"
                        },
                        {
                            name: '近一年',
                            section: "12"
                        },
                    ],
                    timeListMore: [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        // {
                        //     name: '近五年',
                        //     section: "60",
                        //     index: "5"
                        // },
                        {
                            name: '上线后',
                            section: "",
                            index: "6"
                        },
                    ],
                    timeOptions: '',//绘制折线图配置
                    activeClass: activeClass,  //高亮时间（7天）
                    activeClassSecond: '1', //高亮业绩表现，历史净值
                    activeClassTg: '1',
                    available: "1"
                }
            },
            //视图 渲染前
            async created() {
                //判断用户是否持有该系列产品（子女）
                let res = await this.getUserIsHold();
                //缓存投顾代码到本地
                combProductInfo.comb_code = res.nextCombCode;
                this.isHold = res.isHold == '1' ? true : false; //是否首次购买 
                this.pageState = this.isHold;
                if (this.isHold) {
                    $(_pageId + " .isHoldShow").show();
                    $(_pageId + " .targeting").show();
                } else {
                    $(_pageId + " .isHoldHide").show();
                    $(_pageId + " .targeting").show();
                }
                this.available = res.available; //当前系列产品是否可以购买
                this.reqFun106058(); // 查询产品是否首次购买 并获取详情
                if (this.isHold) {
                    //调用列表接口
                    this.reqFun102196();
                }
                this.initTrajectoryData();
                // this.getSubFundList();
            },
            //渲染完成后
            mounted() {

                $(_pageId + " .buildPlanBtn").hide();
                $(_pageId + " #inputspanidAmt span").addClass("unable");//默认输入框失去焦点
                $(_pageId + " #inputspanidInvestAmt span").addClass("unable");//默认输入框失去焦点
                // 初始化下拉
                this.planJoinTimeSelect();
                this.planJoinYieldSelect();
                var appletEnterImg = require("gconfig").global.oss_url + $(_pageId + " #applet_enter").html();
                $(_pageId + " #applet_enter_img").attr("src", appletEnterImg);
            },
            //计算属性
            computed: {
                //日期处理
                timeResult: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        if (num1) return tools.ftime(time.substr(num, num1), "-")
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
                threshold_amount_Result: () => {
                    return (threshold_amount) => {
                        if (!threshold_amount) return '--元'
                        threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万元" : tools.fmoney(threshold_amount) + '元';
                        return threshold_amount
                    }
                },
                setNum1: () => {
                    return (str, len) => {
                        if (!str) return '--'
                        return (+str).toFixed(len)
                    }
                }
            },
            //绑定事件
            methods: {
                //去定投
                fixed_investment(item) {
                    if (item.fixed_investment_list != '1') return;
                    tools.recordEventData('1','startInvestment','开始定投');
                    appUtils.setSStorageInfo("isAdvisoryInvestment", '1'); //确保为投顾定投
                    combProductInfo.comb_code = item.comb_code;
                    appUtils.setSStorageInfo("productInfo", combProductInfo);
                    appUtils.pageInit(_page_code, "fixedInvestment/startInvestment");

                },
                //去购买
                buy(item) {
                    if (item.purchase_state != '1') return;
                    tools.recordEventData('1','combProdBuy','购买');
                    combProductInfo.comb_code = item.comb_code;
                    appUtils.setSStorageInfo("productInfo", combProductInfo);
                    appUtils.setSStorageInfo("comb_code", item.comb_code);
                    appUtils.pageInit(_page_code, "combProduct/combProdBuy");
                },
                //跳转详情
                details(item) {
                    combProductInfo.comb_code = item.comb_code;
                    appUtils.setSStorageInfo("productInfo", combProductInfo);
                    if (item.fund_vol * 1 > 0) {
                        //有资产，跳转持仓详情
                        tools.recordEventData('1','combHoldHeightDetail','持仓详情');
                        appUtils.pageInit(_page_code, "combProduct/combHoldHeightDetail");
                    } else {
                        combProductInfo.fund_code = item.comb_code;
                        appUtils.setSStorageInfo("productInfo", combProductInfo);
                        appUtils.setSStorageInfo("fixed_investment_list", item.fixed_investment_list);
                        appUtils.setSStorageInfo("isAdvisoryInvestment", '1');
                        //无资产跳转我的定投
                        tools.recordEventData('1','investmentList','我的定投');
                        appUtils.pageInit(_page_code, "fixedInvestment/investmentList");
                    }
                },
                //调用计划列表
                reqFun102196() {
                    let data = {
                        series_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.series_id ? combProductInfo.series_id : combProductInfo.fund_code)
                    }
                    service.reqFun102196(data, async (data) => {
                        if (data.error_no == '0') {
                            this.holdList = data.results;
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                chooseTrajectoryData(item) {
                    if (item.section == this.activeClass) {
                        return;
                    }
                    // console.log(item,111)
                    tools.recordEventData('1','chooseTrajectoryData-' + item.section,item.name);
                    this.activeClass = item.section
                    this.moreName = "更多"
                    this.initTrajectoryData(); // 获取业绩走势
                },
                chooseMoreList(item) {
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    tools.recordEventData('1','chooseMoreList-' + item.section,item.name);
                    this.moreName = item.name;
                    this.activeClass = item.section;
                    this.initTrajectoryData(); // 获取业绩走势
                },
                // 显示更多区间
                showMoreSpliceDate() {
                    //$(_pageId + " #tooltip").parent().css({ "z-index": "999" })
                    tools.recordEventData('1','showMoreSpliceDate','显示更多区间');
                    $(_pageId + " .thfundBtn").hide();
                    $(_pageId + " #moreSpliceDate").show();
                },
                cancelMoreSpliceDate() {
                    tools.recordEventData('1','hideMoreSpliceDate','隐藏更多区间');
                    $(_pageId + " .thfundBtn").show();
                    $(_pageId + " #moreSpliceDate").hide();
                },
                // 获取业绩走势折线图
                initTrajectoryData(section) {
                    section = this.activeClass;
                    $(_pageId + " #navTrend").hide();
                    $(_pageId + " #trajectory").show();
                    this.initShowChat = 0;
                    service.reqFun102169({ comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code), section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer1").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer1").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: (params) => {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">本产品：</span><span style="color:${t.color}"><b>${(t.value * 100).toFixed(2)}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                hideDelay: 10
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                // axisLabel: {
                                //     formatter: '{value} %'
                                // },
                                splitNumber: 3,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: (value, index) => {
                                        return (value * 100).toFixed(2) + "%"
                                    }
                                },

                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '5%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                                // data: ['实际巡检', '计划巡检', '漏检次数'],
                            },
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                // name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series;
                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = document.getElementById("markChartContainer");
                        // let dom = $(_pageId + " #chartContainer1");
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                pageTo_details() {
                    let data = {    //缓存产品ID
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code)
                    }
                    tools.recordEventData('1','combProdDetail','投顾详情');
                    appUtils.setSStorageInfo("productInfo", data);
                    appUtils.pageInit(_page_code, "combProduct/combProdDetail");
                },
                // 微信跳转
                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                },
                //播放视频
                play() {
                    tools.recordEventData('1','playVideo','点击视频');
                    let html = `<video id="comMarking_new_example_video" class="video-js vjs-default-skin vjs-big-play-centered" style="width:100%;height:100%" width="100%"
                        webkit-playsinline="true" playsinline="true" height="100%" controls preload="auto" poster=""
                        data-setup="{}">
                    </video>`
                    $(_pageId + " #new_example_div").html(html);
                    //初始化视频
                    combProdMarketing_player = videojs('comMarking_new_example_video', {
                    }, function onPlayerReady() {
                        //结束和暂时时清除定时器，并向后台发送数据
                        this.on('ended', function () {
                            // window.clearInterval(time1);
                        });
                        this.on('pause', function () {
                            // window.clearInterval(time1);
                        });
                        this.on('waiting', function () {
                            // window.clearInterval(time1);
                        })
                    });
                    let productInfo = appUtils.getSStorageInfo("productInfo")
                    combProdMarketing_player.reset();
                    combProdMarketing_player.src({ src: global.video_oss_url + productInfo.video_path })
                    combProdMarketing_player.load(global.video_oss_url + productInfo.video_path)
                    $(_pageId + " video").attr("poster", global.oss_url + productInfo.cover_path)
                    $(_pageId + " #showVideo").show()
                },
                //获取分享状态
                async getPageShareStatus(is_share) {
                    // tools.recordEventData('1','share','分享');
                    let data = {
                        busi_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : this.detailsInfo.comb_code,
                        page_type: '9',
                        pageId: _pageId,
                        pageCode: _page_code
                    }
                    tools.isShowShare(data, is_share);
                },
                //获取用户是否首次购买  系列
                async getUserIsHold() {
                    return new Promise(async (resolve) => {
                        let data = {
                            // comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code)
                            series_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.series_id ? combProductInfo.series_id : combProductInfo.fund_code)
                        }
                        service.reqFun102197(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                //判断昵称是否重复
                async getUserIsNickName() {
                    return new Promise(async (resolve) => {
                        let data = {
                            series_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.series_id ? combProductInfo.series_id : combProductInfo.fund_code),
                            nick_name: $(_pageId + " .nick_name").val()
                        }
                        service.reqFun102195(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                // 调取产品详情接口
                async getDetails() {
                    return new Promise(async (resolve) => {
                        let data = {
                            comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code)
                        }
                        service.reqFun102165(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                // 获取产品详情
                async getCombProdDetatils() {
                    let res = await this.getDetails()
                    let info = Object.assign(combProductInfo, res)
                    if (info.cover_path && info.video_path) {
                        $(_pageId + " .bg").attr("src", global.oss_url + info.cover_path);
                        $(_pageId + " .startCasting_banner").show();
                    }
                    $(_pageId + " #title").html(info.prod_name_list ? info.prod_name_list : info.comb_sname);
                    threshold_amount = res.page_first_per_min ? res.page_first_per_min : res.first_per_min; // 起购
                    step_amt = res.step_unit; // 递增
                    if (isFirstPurchase) {
                        invest_amount = res.first_per_min
                    } else {
                        invest_amount = res.fixed_invest_min // 定投金额
                    }
                    appUtils.setSStorageInfo("productInfo", info);
                    appUtils.setSStorageInfo("comb_code", res.comb_code);
                    appUtils.setSStorageInfo("financial_prod_type", info.financial_prod_type);
                    var strAmt = `${threshold_amount}元以上`
                    var strInvestAmt = `0元或${invest_amount}元以上`
                    if (strAmt) {
                        $(_pageId + " #inputspanidAmt span").text(strAmt).attr("text", strAmt);
                    } else {
                        $(_pageId + " #inputspanidAmt span").text("").attr("text", "");
                    }
                    if (strInvestAmt) {
                        $(_pageId + " #inputspanidInvestAmt span").text(strInvestAmt).attr("text", strInvestAmt);
                    } else {
                        $(_pageId + " #inputspanidInvestAmt span").text("").attr("text", "");
                    }
                    this.detailsInfo = res;
                    this.detailsInfo.updateVesion = true;

                    this.reqFun102178(res);
                    let is_share = $(_pageId + " .pro_share").attr("is_share")
                    this.getPageShareStatus(is_share)
                    tools.setPageTop("#inclusive_jjThirtyDetail");
                },
                // async getSubFundList() {
                //     service.reqFun102186({
                //         comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code)
                //     }, async (data) => {
                //         if (data.error_no == '0') {
                //             this.subFundList = data.results;
                //         } else {
                //             layerUtils.iAlert(data.error_info);
                //         }
                //     })
                // },
                //查询产品是否首次购买
                reqFun106058() {
                    var that = this;
                    var param = {
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code),
                    }
                    service.reqFun106058(param, function (data) {
                        if (data.error_no == 0) {
                            var results = data.results[0];
                            if (results.is_can_add == 0) {
                                isFirstPurchase = false;
                            } else {
                                isFirstPurchase = true;
                            }
                        } else {
                            isFirstPurchase = true;
                            layerUtils.iAlert(data.error_info);
                        }
                        //获取产品详情
                        that.getCombProdDetatils();
                    }, { isLastReq: false })
                },

                // 查询是否测评
                reqFun102178(_productInfo) {
                    service.reqFun102178({
                        comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code)
                    }, async (data) => {
                        if (data.error_no == '0') {
                            var result = data.results[0];
                            if (result.risk_state && result.risk_state == '0') {
                                let _html = '';
                                result.question_list && result.question_list.forEach(item => {
                                    _html += `<li style="list-style: disc;">${item.question_name}：${item.answer}</li>`
                                })
                                if ($("#template_seriesChildrenMarketing").attr("data-display") == 'block') {
                                    let operationId = 'investmentIntention';
                                    layerUtils.iConfirm(`<span style='text-align: left;padding:0;display:inline-block'>请您确认以下信息是否符合您的投资意向 <ul style="padding: 0 0.46rem;
                                text-align: left; margin-top: -0.2rem;margin-bottom: -0.2rem;color: #2e2e2e;font-size: 16px">${_html}</ul></span>`, () => {
                                        pageBack();
                                    }, () => {
                                        var param101088 = {
                                            tg_cust_question_result: result.answer,
                                            cust_type: "1",
                                            comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (combProductInfo.comb_code ? combProductInfo.comb_code : combProductInfo.fund_code)
                                        };
                                        service.reqFun101088(param101088, function (data) {
                                            if (data.error_no == "0") {

                                            } else {
                                                layerUtils.iMsg(-1, data.error_info);
                                            }
                                        });
                                    }, "返回", "确认",operationId);
                                }
                            }
                            $(_pageId + " .buildPlanBtn").show();
                            // layerUtils.iConfirm.close_pop();
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },

                //输入首投/每月定投总额弹出数字键盘
                inputSpanEvent(id) {
                    tools.recordEventData('1','id','开始输入');
                    $(_pageId + " .pop_text").show();
                    event.stopPropagation();
                    $(_pageId + ` #czje_${id}`).val('');
                    //键盘事件
                    moneyboardEvent(id);
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "template_seriesChildrenMarketing";
                    param["eleId"] = `czje_${id}`;
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "3";
                    require("external").callMessage(param);
                },

                // 选择加入年限
                planJoinTimeSelect() {
                    dataArr = JSON.parse($(_pageId + " #joinTimeData").attr("data"))
                    // dataArr = [
                    //     { id: "5", value: "5年", year: "5" },
                    //     { id: "10", value: "10年", year: "10" },
                    //     { id: "15", value: "15年", year: "15" },
                    //     { id: "20", value: "20年", year: "20" },
                    //     { id: "25", value: "25年", year: "25" },
                    //     { id: "30", value: "30年", year: "30" }
                    // ]
                    tools.mobileSelect({
                        trigger: _pageId + " #planJoinTime",
                        title: "",
                        position: $(_pageId + " #joinTimeData").attr("data-position"),
                        dataArr: dataArr,
                        callback: function (data) {
                            $(_pageId + " #planJoinTime").html(`<span class="m_font_size18">${data[0].year}</span> <span>▼</span>`);
                            $(_pageId + " #planJoinTime").attr("data-id", data[0].id);
                            $(_pageId + " #planJoinTime").attr("data-value", data[0].year);
                        }
                    });
                },

                // 选择收益率
                planJoinYieldSelect() {
                    dataArrYield = JSON.parse($(_pageId + " #joinYieldData").attr("data"))
                    tools.mobileSelect({
                        trigger: _pageId + " #planYield",
                        title: "",
                        position: $(_pageId + " #joinYieldData").attr("data-position"),
                        dataArr: dataArrYield,
                        callback: function (data) {
                            $(_pageId + " #planYield").html(`<span class="color-red m_font_size18">${data[0].value}</span><span>▼</span>`);
                            $(_pageId + " #planYield").attr("data-id", data[0].id);
                            $(_pageId + " #planYield").attr("data-value", data[0].value);
                        }
                    });
                },
                // 生成预算结果
                clickGenerateResult() {
                    tools.recordEventData('1','clickGenerateResult','生成结果');
                    var amt = $(_pageId + " #czje_Amt").val().replace(/,/g, "");
                    var invest_amt = $(_pageId + " #czje_InvestAmt").val().replace(/,/g, "");
                    var term = $(_pageId + " #planJoinTime").attr("data-value");
                    var yield = $(_pageId + " #planYield").attr("data-value");
                    if (amt <= 0 || !amt) {
                        layerUtils.iAlert("请输入首投金额");
                        return;
                    }
                    if (threshold_amount && parseFloat(amt) < parseFloat(threshold_amount)) { //首次购买
                        layerUtils.iAlert(`首投金额不能低于${threshold_amount}元`);
                        return;
                    }
                    if (tools.isMatchAddAmt(amt, threshold_amount, step_amt)) {
                        return
                    }
                    if (invest_amt > 0) {
                        if (invest_amount && parseFloat(invest_amt) < parseFloat(invest_amount)) { //首次购买
                            layerUtils.iAlert(`定投金额不能低于${invest_amount}元`);
                            return;
                        }
                        if (tools.isMatchAddAmt(invest_amt, invest_amount, step_amt)) {
                            return
                        }
                    }
                    service.reqFun102185({
                        amt: amt,
                        invest_amt: invest_amt && invest_amt > 0 ? invest_amt : "0",
                        term: term,
                        yield: yield
                    }, async (data) => {
                        if (data.error_no == '0') {
                            var results = data.results[0];
                            $(_pageId + " .speciaSpan").removeClass("specialActive")
                            $(_pageId + " #income").text(tools.fmoney(results.income));
                            $(_pageId + " #year").text(term);
                            $(_pageId + " #income_per").text((+results.income_per).toFixed(2))
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                async clickBuildPlanBtn() {
                    // return console.log(this.pageState)
                    tools.recordEventData('1','buildPlanBtn','加入计划');
                    if (this.pageState) {
                        $(_pageId + " .isHoldShow").hide();
                        $(_pageId + " .isHoldHide").show();
                        // $(_pageId + " .content").scrollTop(0)
                        $(_pageId + " .template_seriesChildrenMarketing_article").scrollTop(0);
                        this.pageState = false;
                        return;
                    }
                    if (this.available == '0') return layerUtils.iAlert("超过计划个数的上限，详询客服400-167-8888");
                    let nick_name = $(_pageId + " .nick_name").val() ? $(_pageId + " .nick_name").val().trim() : '';
                    if (nick_name) {
                        let regex = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
                        if (!regex.test(nick_name)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
                        if (!tools.validateInputName(nick_name)) return layerUtils.iAlert("昵称字数不能超过5个字符，请修改");
                        let res = await this.getUserIsNickName() //判断昵称是否重复
                        if (res.existNick == '1') return layerUtils.iAlert("子女昵称已存在，请重新输入");
                    }
                    //缓存当前是否为系列投顾从产品
                    appUtils.setSStorageInfo("isSeriesComb", '1');
                    appUtils.pageInit(_page_code, "combProduct/startCasting", {
                        amt: $(_pageId + " #czje_Amt").val() ? $(_pageId + " #czje_Amt").val().replace(/,/g, "") : '',
                        invest_amt: $(_pageId + " #czje_InvestAmt").val() ? $(_pageId + " #czje_InvestAmt").val().replace(/,/g, "") : '',
                        nick_name: nick_name ? nick_name : ''
                    });
                    return;
                },
                clickToProdDetail(item) {
                    tools.recordEventData('1','publicOfferingDetail','产品详情');
                    var productInfo = {};
                    productInfo.fund_code = item.prod_code;//产品编码
                    appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
                    appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                    // appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
                    // appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
                    sessionStorage.vip_buttonShow = true;
                    appUtils.pageInit(_page_code, "template/publicOfferingDetail");
                },
            },
        })

    }
    //绑定事件
    function bindPageEvent() {
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_page_code)
        });
        appUtils.preBindEvent($(_pageId + " #earnPointsActivityRules"), " .okBtn", function (e) {
            $(_pageId + " #earnPointsActivityRules").hide();
        });
        appUtils.preBindEvent($(_pageId + " #pointsDetails"), " .okBtn", function (e) {
            $(_pageId + " #pointsDetails").hide();
        });
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            tools.recordEventData('1','closeVideo','关闭视频');
            combProdMarketing_player.pause();
            setTimeout(function () {
                combProdMarketing_player.dispose();
                combProdMarketing_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        });
    }


    // 金额键盘事件
    function moneyboardEvent(id) {
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () { // 键盘完成
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var moneys = curVal.replace(/,/g, "");
                if (id == "Amt") {
                    if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                        return
                    }
                } else if (id == "") {
                    if (tools.isMatchAddAmt(moneys, invest_amount, step_amt)) {
                        return
                    }
                }
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + ` #czje_${id}`).val(moneys);
            },
            inputcallback: function () {// 键盘输入
                // 处理单个选择产品金额变化
                var curVal = $(_pageId + ` #czje_${id}`).val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + ` #czje_${id}`).val(curVal.substring(0, curVal.length - 1));
                }

            }, // 键盘隐藏
            keyBoardHide: function () {
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(moneys));
                }
            },
        })
    }

    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        activity_id_string = [];
        $(_pageId + " .header_inner #title").html("");
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        $(_pageId + " #showVideo").hide();
        $(_pageId + " #inputspanidAmt span").text("").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #inputspanidInvestAmt span").text("").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #czje_Amt").val("");
        $(_pageId + " #czje_InvestAmt").val("")
        $(".mobileSelect").remove();
        $(_pageId + " #income").text("****");
        $(_pageId + " #income_per").text("**%")
        $(_pageId + " #earnPointsActivityRules").hide();
        $(_pageId + " #pointsDetails").hide();
        $(_pageId + " .tooltip_front").hide()
        $(_pageId + " .tooltip_behind").hide();
        $(_pageId + " .speciaSpan").addClass("specialActive");
        $(_pageId + " .remarkTop").hide();
        $(_pageId + " .startCasting_banner").hide();
        $(_pageId + " .isHoldHide").hide();
        $(_pageId + " .isHoldShow").hide();
        $(_pageId + " .targeting").hide();
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        newData = null;
        startTime = '';

    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        if (combProdMarketing_player) {
            combProdMarketing_player.pause();
            setTimeout(function () {
                combProdMarketing_player.dispose();
                combProdMarketing_player = '';
            }, 0);
        }
        $(_pageId + " #showVideo").hide();
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        if (newData && newData.task_id && newData.task_type == '1' && !tools.getStayTime(startTime, newData.duration)) {
            var remainTime = tools.getRemainTime(startTime, newData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        tools.vipTjActivityIn({ activity_id: newData.activity_id, task_id: newData.task_id });
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_page_code, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }
    let combProdMarketing = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = combProdMarketing;
});
