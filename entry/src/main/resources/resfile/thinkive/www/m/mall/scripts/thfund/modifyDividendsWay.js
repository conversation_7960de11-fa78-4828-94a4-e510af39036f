// 修改分红方式
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#thfund_modifyDividendsWay ";
    var _pageCode = "thfund/modifyDividendsWay";
    var monkeywords = require("../common/moneykeywords");
    var _dividendsWay = "";
    var _old_dividend_method;
    var _fund_code;
    var tools = require("../common/tools");
    var jymm;
    var holdObj;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        _fund_code = appUtils.getSStorageInfo("fund_code");
        holdObj = appUtils.getSStorageInfo("holdObj");
        //获取详情
        getHoldDetail();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击分红方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _dividendsWay = $(this).attr("dividendsWay");
            if (_dividendsWay == _old_dividend_method) {
                $(_pageId + " #confirm").addClass("no_active");
            } else {
                $(_pageId + " #confirm").removeClass("no_active");
            }
        });

        //分红方式变更
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            if ($(this).hasClass("no_active")) {
                return;
            }

            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();

            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_modifyDividendsWay";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //交易密码确定
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();

            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            var param = {
                fund_code: _fund_code,
                dividend_method: _dividendsWay,
                trans_pwd: jymm1, //交易密码
                ack_date: holdObj.ack_date
            };
            if(holdObj.prod_sub_type2 == "25" || holdObj.prod_sub_type2 == "200") {
                param.due_date = holdObj.due_date
            }
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                reqFun101043(param);
            }, {isLastReq: false});
        });

        //返回
        appUtils.bindEvent($(_pageId + " #back_btn"), function () {
            pageBack();
        });

    }


    //分红方式变更
    function reqFun101043(param) {
        service.reqFun101043(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                appUtils.pageInit(_pageCode, "thfund/modifyDividendsWayResult");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取详情
    function getHoldDetail() {
        //产品简称
        var fund_sname = holdObj.fund_sname;
        //产品代码
        var fund_code = holdObj.fund_code;
        //分红方式
        _old_dividend_method = holdObj.defdividend_method;
        _dividendsWay = holdObj.defdividend_method;
        if (_old_dividend_method == "2") { //如果返回分红到卡，变量设为分红到宝，防止重复修改分红到宝
            _old_dividend_method = "3";
        }
        //分红方式
        // "1": "红利再投",
        // "2": "分红到银行卡",
        // "3": "分红到晋金宝",
        $(_pageId + " .modify_box .item .icon").removeClass("active");
        var item = "";
        if (_old_dividend_method == "1") {
            item = $(_pageId + " .modify_box .item")[0];
        } else if (_old_dividend_method == "3" || _old_dividend_method == "2") { //分红到卡、分红到宝合并为分红到宝
            item = $(_pageId + " .modify_box .item")[1];
        }
        $(item).find(".icon").addClass("active");
        $(_pageId + " #fund_sname").html(fund_sname);
        $(_pageId + " #fund_code").html(fund_code);
    }


    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        jymm = "";
        $(_pageId + " #fund_sname").html("--");
        $(_pageId + " #fund_code").html("--");
        $(_pageId + " .modify_box .item .icon").removeClass("active");
        $(_pageId + " #confirm").addClass("no_active");
        _dividendsWay = "";
        _old_dividend_method = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
