/*创建时间hSea 2015-12-31 15:36:59 PM */
define(function(require,exports,module){function a(a,b,c){$(function(){var d=new iChart.Area2D({render:a,data:b,title:c.title,width:c.width,height:c.height,tip:{enable:!0,shadow:!0,listeners:{parseText:function(a,b,c){return"<span style='color:#005268;font-size:16px;font-weight:600;'>"+c+"</span>"}}},crosshair:{enable:!0,line_width:2,line_color:"#6cc4f4"},coordinate:{height:"90%",background_color:"#edf8fa",scale:[{position:"left",start_scale:c.scale.start,end_scale:c.scale.end,scale_space:c.scale.space,scale_size:2,scale_enable:!1,label:{font:"Helvetica, Arial, sans-serif",fontsize:14},scale_color:"#9f9f9f"},{position:"bottom",scale_enable:!1,label:{font:"Helvetica, Arial, sans-serif",fontsize:14},labels:c.labels}]},sub_option:{smooth:!1,label:!1,point_hollow:!0,intersection:!1,gradient:!0}});d.draw()})}function b(a,b,c){$(function(){var d=new iChart.Donut2D({render:a,data:b,center:{color:"#3e576f",shadow:!0,shadow_blur:2,shadow_color:"#557797",shadow_offsetx:0,shadow_offsety:0,fontsize:20},sub_option:{label:{background_color:null,sign:!1,padding:"0 1",fontsize:15,fontweight:600,color:"#4572a7"},border:{width:2,color:"#ffffff"}},shadow:!1,shadow_blur:6,shadow_color:"#aaaaaa",shadow_offsetx:0,shadow_offsety:0,background_color:"#fefefe",animation:!0,offset_angle:-120,showpercent:!0,decimalsnum:1,donutwidth:30,width:c.width,height:c.height,radius:c.radius});d.draw()})}require("icharts");var c={draw2DAnnular:b,draw2DArea:a};module.exports=c});
/*创建时间 2015-12-31 15:36:59 PM */