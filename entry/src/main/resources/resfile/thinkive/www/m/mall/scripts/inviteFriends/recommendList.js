 // 手机注册
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		serviceConstants = require("constants"),
		service = require("mobileService"),
		des = require("des"),
		_pageId = "#inviteFriends_recommendList";
	var common = require("common");
	var gconfig = require("gconfig");
	var global = gconfig.global;
	 var tools = require("../common/tools");//升级
	var endecryptUtils = require("endecryptUtils");
	var ut = require("../common/userUtil");

	function init(){
		var type=appUtils.getPageParam('type')||0;
		//邀请好友列表
		// reqFun101074(type);
		//区分入口 recommendedEarnings 新版活动
		let saveMoneyFlag = appUtils.getSStorageInfo("saveMoneyFlag") ? appUtils.getSStorageInfo("saveMoneyFlag") : '';
		if(saveMoneyFlag == '1'){
			reqFun108054(type);
		}else{
			//邀请好友列表
			reqFun101074(type);
		}
	}

	//绑定事件
	function bindPageEvent(){
		//点击返回按钮
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});
	}
	/*邀请好友列表*/
	function reqFun101074(type){
		service.reqFun101074({},function(data){
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				if(type==0)var list=data.results[0].listFriBuy;
				if(type==1)var list=data.results[0].listFriNoBuy;
				if(type==2)var list=data.results[0].listFriNoband;
				var recommendhtml=recommendListHtml(list,type);
				var joinStatus;
				if(type==0)joinStatus='11';
				if(type==1)joinStatus='12';
				if(type==2)joinStatus='13';
				$(_pageId+ " .recommendInfAll").html(recommendhtml);
			}else{
				layerUtils.iAlert(error_info);
			}
		})
	}
	/*邀请好友列表*/
	function reqFun108054(type){
		service.reqFun108054({},function(data){
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				console.log(type);
				if(type==0)var list=data.results[0].buyList;
				if(type==1)var list=data.results[0].notBuyList;
				if(type==2)var list=data.results[0].notBindList;
				var recommendhtml=recommendListHtml(list,type);
				var joinStatus;
				if(type==0)joinStatus='11';
				if(type==1)joinStatus='12';
				if(type==2)joinStatus='13';
				$(_pageId+ " .recommendInfAll").html(recommendhtml);
			}else{
				layerUtils.iAlert(error_info);
			}
		})
	}
	/*生成邀请列表*/
	function recommendListHtml(list,type){
		let saveMoneyFlag = appUtils.getSStorageInfo("saveMoneyFlag") ? appUtils.getSStorageInfo("saveMoneyFlag") : '';
		var typename;
		if(type==0)typename='已投资';
		if(type==1)typename='绑卡未投资';
		if(type==2)typename='注册未绑卡';
		const listhtml = `
			<div class="recommendInfListName">${typename}(${list.length})</div>
			<div class="recommendInfListNav ${(saveMoneyFlag == '1' && type == '0') ? 'flex' :''}">
				<span>手机号</span>
				<span>姓名</span>
				${(saveMoneyFlag == '1' && type == '0') ? '<span>首投日期</span>' : ''}
			</div>
			${
				list.length > 0 && list
				? list.map(item => 
					item ? `
					<div class="recommendInfList ${(saveMoneyFlag == '1' && type == '0') ? 'flex' :''}" >
						<span>${item.registered_mobile.substring(0, 3)}****${item.registered_mobile.substring(7,11)}</span>
						<span>${item.cust_name.substring(0,1)}*</span>
						${(saveMoneyFlag == '1' && type == '0') ? `<span>${item.first_invest_date ? tools.formatDateStringNew(item.first_invest_date,1):'--'}</span>` : ''}
					</div>
					` : ''
					).join('')
				: '<div class="nodata">暂无数据</div>'
			}
		`;
		return listhtml;
	}

	function destroy(){
		$(_pageId+ " .recommendInfAll").html('');
	}
	function pageBack(){
		appUtils.pageBack();
	}
	var recommendedEarnings = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = recommendedEarnings;
});
