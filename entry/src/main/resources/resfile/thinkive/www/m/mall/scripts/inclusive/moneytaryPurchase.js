define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#inclusive_moneytaryPurchase ";
    var ut = require("../common/userUtil");
    var _pageCode = "inclusive/moneytaryPurchase";
    var tools = require("../common/tools");
    var get_pdf_file = require("../common/StrongHintPdf")
    var userInfo;
    var calculator = require("../common/calculator");
    var monkeywords = require("../common/moneykeywords");
    var threshold_amount; //起购金额
    var jymm;
    var _available_vol; //可用金额
    var _fund_code;
    var step_amt; //递增金额
    var buyflag; //风险等级是否匹配  1 不匹配
    var _first_max_amt = "";
    var productInfo;
    var buy_state;

    function init() {
        userInfo = ut.getUserInf();
        productInfo = appUtils.getSStorageInfo("productInfo");
        _fund_code = appUtils.getSStorageInfo("fund_code");
        //获取产品详情
        getFundInfo();
        //可用份额
        reqFun101901();
        //获取时间
        reqFun102008();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        //PDF相关，走公共方法
        is_show_paf()
    }
    function is_show_paf() {
        get_pdf_file.get_file(_fund_code, _pageId)
    }
    function bindPageEvent() {
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            tools.intercommunication(_pageCode);
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "inclusive_moneytaryPurchase";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            // console.log(1)
            // return console.log(productInfo,111);
            if(productInfo.exclusive_product_type == '05'){
                sessionStorage.vip_buttonShow = false;
                return appUtils.pageInit(_pageCode, "template/publicOfferingDetail");
            }
            appUtils.pageInit(_pageCode, "inclusive/moneytaryDetail");
        });
        //全部
        appUtils.bindEvent($(_pageId + " .allMoney"), function () {
            $(_pageId + " #inputspanid span").text(tools.fmoney(productInfo.prod_per_max_amt));
            $(_pageId + " #czje").val(productInfo.prod_per_max_amt);
            $(_pageId + " #inputspanid span").css({ color: "#000000" }).addClass("active")
        });

        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //风险承受能力确认
        appUtils.bindEvent($(_pageId + " .agreement1"), function () {
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active")
            } else {
                $(this).find("i").addClass("active")
            }
        });
        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });
        //显示购买弹框
        appUtils.bindEvent($(_pageId + " #next"), function () {
            monkeywords.close();
            var curVal = $(_pageId + " #czje").val();
            var moneys = curVal.replace(/,/g, "");
            if (buy_state != "1" && productInfo.exclusive_product_type) return // 货基  售罄|| 敬请期待不允许购买
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (moneys <= 0 || !moneys) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (threshold_amount && parseFloat(moneys) < parseFloat(threshold_amount)) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                $(_pageId + " .bast_rate").text("--");
                return
            }
            if (_first_max_amt && moneys > parseFloat(_first_max_amt)) {
                layerUtils.iAlert("超过单笔最高限额");
                return;
            }
            if (moneys && productInfo.prod_max_amt && productInfo.used_amt && calculator.minus(productInfo.prod_max_amt, productInfo.used_amt) < parseFloat(moneys)) { // 总额度 - 剩余额度 < 当前金额
                layerUtils.iAlert("产品剩余额度不足，剩余额度为" + tools.fmoney(calculator.minus(productInfo.prod_max_amt, productInfo.used_amt)));
                return;
            }

            if (productInfo.exclusive_type == "2" && (productInfo.exclusive_product_type == "03" || productInfo.exclusive_product_type == "05")) { //新手 && 货基专享
                service.reqFun109003({}, function (data) { //查询是否新手
                    if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var is_sales_tiro = data.results[0].is_sales_tiro; // 是否新手（0-是 1-否）
                    if (is_sales_tiro != "0") {
                        layerUtils.iAlert("您已不是新手");
                        return;
                    }
                    next();
                })
                return;
            }
            if (productInfo.buy_again == "0" && (productInfo.exclusive_product_type == "03" || productInfo.exclusive_product_type == "05")) { //不能追加 && 货基专享
                service.reqFun150004({ fund_code: productInfo.prod_id }, function (data) {
                    if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var is_exist_hold = data.results[0].is_exist_hold; // 1：持有
                    if (is_exist_hold == "1") {
                        layerUtils.iAlert("持有期间无法重复购买");
                        return;
                    }
                    next();
                })
            } else {
                next();
            }
            function next() {
                if (buyflag == "1") {
                    layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>您的风险承受能力为" + userInfo.riskName + "，此产品超过了您的风险承受能力，若仍然选择投资，则表明在上述情况下，您仍自愿投资该产品，并愿意承担可能由此产生的风险</span>", function () {
                        appUtils.pageInit(_pageCode, "safety/riskQuestion");
                    }, function funcNo() {
                        $(_pageId + " .pop_layer").show();
                        $(_pageId + " #payMethod").show();
                        //是否可以购买
                        isCanBuy();
                    }, "重新测评", "继续购买");
                    return;
                }
                $(_pageId + " .pop_layer").show();
                $(_pageId + " #payMethod").show();
                //是否可以购买
                isCanBuy();
            }

        });

        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "inclusive_moneytaryPurchase";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //进行充值
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                app_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                fund_code: _fund_code,
                buyflag: buyflag,
                exclusive_type: productInfo.exclusive_type,
                exclusive_product_type: productInfo.exclusive_product_type,
                buy_again: productInfo.buy_again,
                id: productInfo.id,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                purchase(param);
            }, { isLastReq: false });
        });
    }

    // 产品详情查询
    function getFundInfo() {
        var str = "";
        if (!!productInfo.exclusive_product_type) { // 专享入口进入 展示综合利率， 上限金额  全部按钮
            $(_pageId + " .allMoney").show();
            _first_max_amt = productInfo.prod_per_max_amt;
            threshold_amount = productInfo.prod_per_min_amt;//专享起购
            step_amt = productInfo.incrs_amt; // 专享递增
            if (productInfo.exclusive_type == "8") {
                $(_pageId + " .rate_text").html("七日年化：<span class='red g_fontSize16'>" + tools.fmoney(productInfo.annu_yield) + "</span>%");
            } else {
                $(_pageId + " .rate_text").html("综合收益率：<span class='red g_fontSize16'>" + tools.fmoney(productInfo.rate) + "</span>%");
            }
            $(_pageId + " .prod_rule").html(productInfo.prod_rule);
            // $(_pageId + " .exclusive_type_desc").html(productInfo.exclusive_type_desc);
            $(_pageId + " .buy_rule").html('现在购买，' + tools.FormatDateText(productInfo.confirm_date.substr(4, 8)) + '开始计算收益，预计' + tools.FormatDateText(productInfo.expire_date.substr(4, 8)) + '到期');
            // $(_pageId + " .last_amt").html("剩余可买额度：" + tools.fmoney(calculator.minus(productInfo.prod_max_amt, productInfo.used_amt)) + "元");
            $(_pageId + " .noviciate").show();
            if (productInfo.prod_per_min_amt) {
                str += (productInfo.prod_per_min_amt >= 10000 ? (productInfo.prod_per_min_amt / 10000 + "万") : tools.fmoney(productInfo.prod_per_min_amt)) + "元起购";
            }
            if (productInfo.prod_per_max_amt) {
                str += "，活动限额" + (productInfo.prod_per_max_amt >= 10000 ? (productInfo.prod_per_max_amt / 10000 + "万") : tools.fmoney(productInfo.prod_per_max_amt)) + "元";
            }

            if (productInfo.exclusive_type == "8") {
                $(_pageId + " .buy_rule").hide();
                $(_pageId + " .noviciate").hide();
            }
            if (productInfo.exclusive_type == "8" && threshold_amount == productInfo.prod_per_max_amt) {//起购金额=单笔最高限额
                $(_pageId + " #inputspan").show();
                $(_pageId + " #inputspanid").hide();
                $(_pageId + " #inputspan span").text(tools.fmoney(threshold_amount));
                $(_pageId + " #czje").val(threshold_amount);
                $(_pageId + " .allMoney").hide();
                $(_pageId + " .buy_rule").hide();
            } else {
                $(_pageId + " #inputspanid").show();
                if (productInfo.prod_per_min_amt) {
                    $(_pageId + " #inputspanid span").text(str);
                    $(_pageId + " #inputspanid span").attr("text", str);
                } else {
                    $(_pageId + " #inputspanid span").text("请输入购买金额");
                    $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
                }
            }

            if (productInfo.buy_state == "0") { // 敬请期待
                $(_pageId + " #next").html("敬请期待").css({ background: '#e5443c' })
            }
            if (productInfo.buy_state == "1") { //购买
                buy_state = "1"
                $(_pageId + " #next").html("下一步").css({ background: '#e5443c' })
            }
            if (productInfo.buy_state == "2") { //售罄
                $(_pageId + " #next").html("售罄").css({ background: '#CDCDCD' })
            }
            tools.getPdf("prod", _fund_code, "1"); //获取协议
        } else {
            $(_pageId + " #next").html("下一步").css({ background: '#e5443c' })
        }

        //详情查询接口
        service.reqFun102027({ fund_code: _fund_code }, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                $(_pageId + " .prod_sname").html(productInfo.prod_exclusive_name || result.prod_sname);//产品简称
                $.extend(productInfo, result);
                _first_max_amt = result.first_max_amt;
                //比较风险等级
                compareRiskLevel();
                if (!!productInfo.exclusive_product_type) return;
                tools.getPdf("prod", _fund_code, result.buy_state); //获取协议
                step_amt = result.step_amt;
                threshold_amount = result.threshold_amount;
                $(_pageId + " .allMoney").hide();
                if (threshold_amount) {
                    str += tools.fmoney(threshold_amount) + "元起购";
                }
                if (step_amt) {
                    str += "，" + tools.fmoney(step_amt) + "元递增";
                }
                if (threshold_amount) {
                    $(_pageId + " #inputspanid span").text(str);
                    $(_pageId + " #inputspanid span").attr("text", str);
                } else {
                    $(_pageId + " #inputspanid span").text("请输入购买金额");
                    $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
                }
                $(_pageId + " .noviciate").hide();
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, { isLastReq: false })
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //比较风险等级
    function compareRiskLevel() {
        var userRiskLevel = userInfo.riskLevel;
        userRiskLevel = +(userRiskLevel.substr(-1))
        var risk_level = (+productInfo.risk_level.substr(-1));
        if (risk_level == 1) return;
        if (risk_level > userRiskLevel) {
            buyflag = "1";
        }
    }

    //现金宝认购、申购产品
    function purchase(param) {
        var callback = function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "inclusive/moneytaryPurchaseResult", data.results[0]);
        }
        if (!!productInfo.exclusive_product_type) { //专享购买
            service.reqFun106022(param, function (data) {
                callback(data);
            })
        } else { // 非专享购买
            service.reqFun106022(param, function (data) {
                callback(data);
            })
        }
    }

    //获取时间
    function reqFun102008() {
        var params = {
            type: "1",
        }
        service.reqFun102008(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                result = tools.FormatNull(result);
                var afterDate = result.afterDate;
                if (afterDate != "--") {
                    afterDate = tools.FormatDateText(afterDate.substr(4, 4));
                }
                var beforeDate = result.beforeDate;
                if (beforeDate != "--") {
                    beforeDate = tools.FormatDateText(beforeDate.substr(4, 4));
                }

                $(_pageId + " #afterDate").html(afterDate);
                $(_pageId + " #beforeDate").html(beforeDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }

        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #czje").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                }
                if (productInfo.prod_per_max_amt && parseFloat(curVal) > parseFloat(productInfo.prod_per_max_amt)) { //上限控制
                    $(_pageId + " #czje").val(productInfo.prod_per_max_amt);
                    $(_pageId + " #inputspanid span").text(tools.fmoney(productInfo.prod_per_max_amt));
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
                if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
            }
        })
    }

    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];

            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额：<em class="money">' + tools.fmoney(_available_vol + "") + '</em>元';

            $(_pageId + " .pay_bank").html(html);
        })
    }

    //是否可以购买
    function isCanBuy() {
        tools.whiteList(_pageId);
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }


    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html($(_pageId + " #czje").val());
    }


    function destroy() {
        get_pdf_file.clearTime()
        guanbi();
        $(_pageId + " #inputspanid span").css({ color: "rgb(153, 153, 153)" }).removeClass("active");
        $(_pageId + " .agreement1 i").removeClass("active");
        $(_pageId + " .empty").html("").val("");
        $(_pageId + " .data-line").html("--");
        $(_pageId + " .hide").hide();
        buyflag = "";
        monkeywords.destroy();
        _first_max_amt = "";
        productInfo = null;
        buy_state = '';
        $(_pageId + " #inputspan").hide();
        $(_pageId + " #inputspanid").show();
        $(_pageId + " .buy_rule").show();
        $(_pageId + " .jjs_yue").hide();
        $(_pageId + " .van-overlay").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var moneytaryDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = moneytaryDetails;
});
