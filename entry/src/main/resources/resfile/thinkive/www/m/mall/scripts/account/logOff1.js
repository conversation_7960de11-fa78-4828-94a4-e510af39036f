// 注销账户
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#account_logOff1 ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    function init() {
         //页面埋点初始化
        tools.initPagePointData();
        tools.getPdf("9");
    }

    // 绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum("account/logOff1")
        });
        // 点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class");
            if (classname == "active") {
                $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
                $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
            } else {
                $(_pageId + " .rule_check #xuanzeqi i").addClass("active");
                $(_pageId + " #next").css({backgroundColor: "#E5433B"});

            }
        });

        // 点击下一步
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议
            if (classname != "active") {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            JudgeAsset();
            
        });
    }

    function JudgeAsset() {
        service.reqFun101046({}, function (data) {
            if (data.error_no == "0") {
                var results = data.results;
                appUtils.pageInit("account/logOff1", "account/logOff2", {});
            } else {
                layerUtils.iAlert(data.error_info)
            }
        })
    }
    function destroy() {
        $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        $(_pageId + " #bk").css({backgroundColor: "#D1D4D5"});
        tools.recordEventData('4','destroy','页面销毁');
    }

    // 系统返回
    function pageBack() {
        appUtils.pageBack();
    }

    var accountLogOff1 = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = accountLogOff1;
});
