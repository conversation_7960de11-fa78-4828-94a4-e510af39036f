//公募： 修改到期兑付方式结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageUrl = "thfund/modifyExpireWayResult",
        _pageId = "#thfund_modifyExpireWayResult ";

    function init() {
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " #done_btn"), function () {
            appUtils.pageInit(_pageUrl, "template/positionList");
        })
    }

    function destroy() {
        
    }

    

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
