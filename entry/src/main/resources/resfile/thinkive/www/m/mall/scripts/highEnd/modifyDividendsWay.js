// 高端： 变更产品分红方式
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            _pageId = "#highEnd_modifyDividendsWay ";
    var _pageCode = "highEnd/modifyDividendsWay";
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    var highEndHoldDetail;
    var defdividend_method;
    var jymm;

    function init() {
        highEndHoldDetail = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " #fund_sname").text(highEndHoldDetail.fund_sname);
        $(_pageId + " #fund_code").text(highEndHoldDetail.fund_code);
        defdividend_method = highEndHoldDetail.defdividend_method;
        if (defdividend_method == "2") { //如果返回分红到卡，变量设为分红到宝，防止重复修改分红到宝
            defdividend_method = "3";
        }
        //分红方式
        // "1": "红利再投",
        // "2": "分红到银行卡",
        // "3": "分红到晋金宝",
        $(_pageId + " .modify_box .item .icon").removeClass("active");
        var item = "";
        if (defdividend_method == "1") {
            item = $(_pageId + " .modify_box .item")[0];
        } else if (defdividend_method == "3" || defdividend_method == "2") { //分红到卡、分红到宝合并为分红到宝
            item = $(_pageId + " .modify_box .item")[1];
        }
        $(item).find(".icon").addClass("active");
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击分红方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            var _dividendsWay = $(this).attr("dividendsWay");
            if (_dividendsWay == defdividend_method) {
                $(_pageId + " #confirm").addClass("no_active");
            } else {
                $(_pageId + " #confirm").removeClass("no_active");
            }
            defdividend_method = _dividendsWay;
        });

        //分红方式变更
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            if ($(this).hasClass("no_active")) {
                return;
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_modifyDividendsWay";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //交易密码确定
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();

            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            var param = {
                fund_code: highEndHoldDetail.fund_code,
                dividend_method: defdividend_method,
                trans_pwd: jymm1, //交易密码
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                reqFun101043(param);
            }, {isLastReq: false});
        });

        //返回
        appUtils.bindEvent($(_pageId + " #back_btn"), function () {
            pageBack();
        });

    }


    //分红方式变更
    function reqFun101043(param) {
        service.reqFun101043(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                appUtils.pageInit(_pageCode, "highEnd/modifyDividendsWayResult");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        jymm = "";
        $(_pageId + " #fund_sname").html("--");
        $(_pageId + " #fund_code").html("--");
        $(_pageId + " .modify_box .item .icon").removeClass("active");
        $(_pageId + " #confirm").addClass("no_active");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
