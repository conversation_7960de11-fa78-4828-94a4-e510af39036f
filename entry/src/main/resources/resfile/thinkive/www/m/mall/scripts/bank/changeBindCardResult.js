//更换结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageUrl = "bank/changeBindCardResult",
        _pageId = "#bank_changeBindCardResult ";

    function init() {
        
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + ".done_btn"), function () {
            appUtils.pageInit(_pageUrl, "login/userIndexs");
        })
    }

     


    function destroy() {
         
    }


    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
