// 多基金买入营销模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        gconfig = require("gconfig"),
        _page_code = "fundSupermarket/fundsMarketing",
        _pageId = "#fundSupermarket_fundsMarketing ";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    let heighEndProduct //new 一个 vue 实例
    var bottomImg
    var headImg

    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            service.reqFun102148({}, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "")
            })
        })
    }

    async function init() {
        let html = await setTemplate()
        $(".main_fundsMarketing").html(html)   //渲染模板
        //页面埋点初始化
        tools.initPagePointData();
        heighEndProduct = new Vue({
            el: '#main_fundsMarketing',
            data() {
                return {
                    fundList: {},
                    indexList: {},
                }
            },
            //视图 渲染前
            created() {
               this.dataDetails()
               
            },
            //渲染完成后
            mounted() {  
            	         
                bottomImg = global.oss_url + $(".main_fundsMarketing #bottom_img_f").html();
                headImg = global.oss_url + $(".main_fundsMarketing #head_img_f").html();
                $(_pageId + " .main_fundsMarketing .fundsMarketing_desc").attr("style", "background:url("+headImg+")");
                $(_pageId + ".main_fundsMarketing #bottom_img").attr("src", bottomImg);
            },
            //计算属性
            computed: {
               activation(){
               	return(month_rate)=>{
               	
               	}
               }
            },
            //绑定事件
            methods: {
               numFilter(value) {
      				const realVal = parseFloat(value).toFixed(2);
      				return realVal;
    		   },
               dataDetails(){
               	service.reqFun102149({}, (datas) => {
               		if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                	    let res = datas.results[0]
                        this.fundList = res.fund;
                        this.indexList = res.index;
            		})
            	},
            	
            }
               
             
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        appUtils.bindEvent($(_pageId + " .buy"), function () {
            tools.setPageToUrl('fundSupermarket/fundsBuy','1','fundSupermarket/fundsMarketing');
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            // console.log(ut.getUserInf())
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if (perfect_info == 4) {
                let operationId = 'replaceIdCard';
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    let operationId = 'riskAssessment';
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    let operationId = 'riskAssessment';
                    layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, '', '确定',operationId)
                    return;
                }          
                appUtils.pageInit(_page_code, "fundSupermarket/fundsBuy");
            });

        });
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .header_inner #title").html("");
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        headImg = "";
        bottomImg = "";
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
