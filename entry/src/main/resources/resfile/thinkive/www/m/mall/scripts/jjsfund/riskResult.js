// 风险测评结果
//@版本: 2.0
define(function(require, exports, module) {
	var appUtils = require("appUtils"),
		dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		serviceConstants = require("constants"),
		_pageId = "#jjsfund_riskResult";
	var riskResult="";
    var biaoshi_fx="";
    var product_id="";
    var prod_code = "";
    var entrust_no = "";
    var zhaungrang_fx = "";
    var fundTransfer ="";
    var ut = require("../common/userUtil");
	function init() {
    	biaoshi_fx = appUtils.getPageParam("biaoshi_fx");
    	product_id = appUtils.getPageParam("product_id");
    	prod_code = appUtils.getPageParam("prod_code");
		entrust_no = appUtils.getPageParam("entrust_no");
		zhaungrang_fx = appUtils.getPageParam("zhaungrang_fx");
		fundTransfer = appUtils.getPageParam("fundTransfer");
		$(_pageId+" #rRisk").html("");
		var userId = appUtils.getSStorageInfo("custid");
		var custNo = appUtils.getSStorageInfo("custNo");
		var param = appUtils.getPageParam();
		var user_info = ut.getUserInf();
		// userInfo.riskName = param.cust_risk_desc;
        // userInfo.riskLevel = param.cust_risk_level;
		// ut.saveUserInf(userInfo);
		 if(user_info){
			 $(_pageId+" #rRisk").html(user_info.riskName);
		 }
//		setUserRick(param);
	}

	function bindPageEvent() {

		//确定按钮
		appUtils.bindEvent(_pageId+" #queding",function(){
            pageBack()
		});

		//点击返回
		appUtils.bindEvent(_pageId + " #getBack",function(){
            pageBack()
		});
	}

    function pageBack() {
        var routerList = appUtils.getSStorageInfo("routerList");
        routerList.splice(-2);
        appUtils.pageInit("account/riskResult", routerList[routerList.length - 1]);

    }

	function destroy() {
	}

	var myAccount = {
			"init": init,
			"bindPageEvent": bindPageEvent,
			"destroy": destroy
		};
		// 暴露对外的接口
		module.exports = myAccount;
	});
