// 产品-交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#bank_prodTrans ";
    var ut = require("../common/userUtil");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var isEnd = false;
    var startTime = "";
    var endTime = "";
    var to_index;
    var tools = require("../common/tools");
    var productInfo;
    var curr_page; // 当前页数
    function init() {
        curr_page = 1;
        resetInputDate();
        productInfo = appUtils.getSStorageInfo("productInfo");
        selectDate(_pageId + ' #endTime', 1, _pageId);
        selectDate(_pageId + ' #startTime', 0, _pageId);
        getUsertransaction();
        var date = new Date();
        setInputDate("endTime", date);
    }

    function bindPageEvent() {
        //筛选日期
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " #jymx_filter").show();
            $(_pageId + " #filter_layer").show();
        });
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId + " #query_date li"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).find("a").addClass("active");
            var data_value = $(this).find("a").attr("data-value");
            setInputDate("endTime", new Date());
            var start_date = new Date();//开始日期
            if (data_value == "90") {
                start_date.setMonth(start_date.getMonth() - 3);
            } else if (data_value == "360") {
                start_date.setFullYear(start_date.getFullYear() - 1);
            } else {
                start_date = "";
                endTime = "";
                setInputDate("endTime", "");
            }
            setInputDate("startTime", start_date);
        });
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId + " #query_type li"), function () {
            $(_pageId + " #query_type li a").removeClass("active");
            $(this).find("a").addClass("active");

        });
        //重置
        appUtils.bindEvent($(_pageId + " #reset"), function () {
            resetInputDate();
        });
        //确定
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
            endTime = "";
            getUsertransaction();
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " #filter_layer"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
        });
        //点击取消时间控件
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");
        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function getUsertransaction() {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        endTime = $(_pageId + '#endTime').attr('time').replace(/-/g, "");
        startTime = $(_pageId + '#startTime').attr('time').replace(/-/g, "");
        var bus_type = $(_pageId + " #query_type .active").attr("bus_type");
        var bus_type_name = {
            "240": "购买",
            "241": "提前支取",
            "242": "到期系统自动兑付",
            "244": "到期支取",
            "245": "周期付息",
        }
        var param = {
            bank_channel_code: productInfo.bank_channel_code,
            str_date: startTime,
            end_date: endTime,
            bus_type: bus_type, //240-购买   241-提前支取 242-到期系统自动兑付  244-到期支取 245-周期付息 N
            order_no: productInfo.order_no
        };

        var gettransActionCallBack = function (data) {
            if (data.error_no == "0") {
                var results = data.results[0].transInfiList;
                if (data.tol_num == "0") {
                    $(_pageId + " .trade_history").html('<div class="nodata">暂无数据</div>');
                    return;
                }
                var str = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        //购买日期
                        var bus_type = results[i].bus_type;//交易类型
                        var trans_date = results[i].trans_date; // 交易日期
                        var trans_amt = results[i].trans_amt; //交易金额
                        var txn_amt = results[i].txn_amt; //交易本金
                        var txn_lnt = results[i].txn_lnt; //交易利息
                        var rgs_date = results[i].rgs_date; //登记日期
                        var rgs_time = results[i].rgs_time; //登记时间
                        var fund_code = results[i].fund_code; //产品编码
                        var fund_name = results[i].fund_name; //产品名称
                        var trans_rate = results[i].trans_rate; //交易利率
                        var remark = "";
                        if(bus_type == "241" || bus_type == "242" || bus_type =="244") {
                            remark = "<div style='width: 100%' class='g_fontSize12 text_gray'>备注：本金：" + tools.fmoney(txn_amt) + "，利息：" + tools.fmoney(txn_lnt) + "</div>"
                        }

                        str += '<div class="trade_box">' +
                            // '<div class="icon"><img src="./images/bank_' + bus_type + '_icon.png" alt=""></div>' +
                            '<div class="fundInfo">' +
                            '<p class="g_fontSize16">' + bus_type_name[bus_type] + '</p>' +
                            '<p class="date g_fontSize14 text_gray">' + tools.ftime(rgs_date) + " " +tools.ftime(rgs_time) + '</p>' +
                            '</div>' +
                            '<div class="result" style="padding-right: 0">' +
                            '<p>' + tools.fmoney(trans_amt) + '元</p>' +
                            '<p>交易成功</p>' +
                            '</div>' +
                            remark +
                            '</div>';

                        // str += '<div class="trade_box">' +
                        //     // '<div class="icon"><img src="./images/bank_' + bus_type + '_icon.png" alt=""></div>' +
                        //     '<div class="fundInfo">' +
                        //     '<p>' + bus_type_name[bus_type] + '</p>' +
                        //     '<p class="fund_name">' + remark + '</p>' +
                        //     '<p class="date">' + tools.ftime(rgs_date) + " " +tools.ftime(rgs_time) + '</p>' +
                        //     '</div>' +
                        //     '<div class="result" style="padding-right: 0">' +
                        //     '<p>' + tools.fmoney(trans_amt) + '元</p>' +
                        //     '<p>交易成功</p>' +
                        //     '</div>' +
                        //     '</div>';
                    }
                }
                if (results && results.length == "0" || !results) {
                    str += '<div class="nodata">暂无数据</div>'
                }

                $(_pageId + " .my_finance").hide();
                $(_pageId + " #v_container_productList").show();
                $(_pageId + " .trade_history").html(str);
                $(_pageId + " .trade_history").show();

            } else {
                layerUtils.iAlert(data.error_info);
            }
        };
        service.reqFun151109(param, gettransActionCallBack);
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        page = 1;
        $(_pageId + " #start_date").val("查询日期");
        $(_pageId + " .my_finance").hide();
        $(_pageId + " .trade_history").hide();
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        endTime = "";
        resetInputDate();
    }

    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", date);
            $(_pageId + " #" + id).val(date);
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        //快捷筛选选中
        $(_pageId + " #jymx_filter ul li a").removeClass("active");
        // $(_pageId + " #init_li").addClass("active");


        $(_pageId + " #query_type li").eq(0).find("a").addClass("active");
        $(_pageId + " #query_date li").eq(0).find("a").addClass("active");

        //开始时间框设置
        $(_pageId + '#startTime').attr("time", "");
        $(_pageId + '#startTime').val("");
        $(_pageId + '#endTime').attr("time", "");
        $(_pageId + '#endTime').val("");
        
// //		//结束时间框设置
//         date.setMonth(date.getMonth() - 3);
//         setInputDate("startTime", date);
    }

    var transaction = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = transaction;
});
