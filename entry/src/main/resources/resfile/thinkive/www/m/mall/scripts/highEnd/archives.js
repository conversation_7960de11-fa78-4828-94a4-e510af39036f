// 晋金高端 政金债 产品档案
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
            _pageId = "#highEnd_archives ",
            _page_code = "highEnd/archives";
    var productInfo;
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        $(_pageId + " .prodName").text(productInfo.prod_name);
        $(_pageId + " .fund_code").text(productInfo.fund_code);
        $(_pageId + " .purconfirm_days").text(tools.ftime(productInfo.purconfirm_days));
        $(_pageId + " .riskDesc").text(productInfo.risk_level_desc);
        $(_pageId + " .issuingScale").text(productInfo.issuing_scale? tools.fmoney(productInfo.issuing_scale / 10000 )+'万': "--");
        $(_pageId + " .mgrcompName").text(productInfo.mgrcomp_name);
        $(_pageId + " .mgrcompSname").text(productInfo.mgrcomp_sname);
        $(_pageId + " .trusteeName").text(productInfo.trustee_name);//托管人
        $(_pageId + " .fundManagers").text(productInfo.fund_managers);
        if(productInfo.income_type == 2) {
        	if(productInfo.prod_sub_type == '80'){
        		$(_pageId + " .historyValueList").hide()
        	} else{
        	    $(_pageId + " .historyValueList").show()   
        	}
        }else{
            $(_pageId + " .historyValueList").show()
        }
        if(productInfo.prod_sub_type == '80'){
        	$(_pageId + " .historyScaleList").hide()
        }else{
            $(_pageId + " .historyScaleList").show()
        }
       
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //概况
        appUtils.bindEvent($(_pageId + " .gk"), function () {
            appUtils.pageInit(_page_code, "highEnd/survey");
        });
        //公告
        appUtils.bindEvent($(_pageId + " .notice"), function () {
            appUtils.pageInit(_page_code, "highEnd/notice", {});
        });
        //产品合同
        appUtils.bindEvent($(_pageId + " .contract"), function () {
            appUtils.pageInit(_page_code, "highEnd/contract", {});
        });
        //交易规则
        appUtils.bindEvent($(_pageId + " .tradeDetail"), function () {
            appUtils.pageInit(_page_code, "highEnd/tradeRules");
        });
        //历史净值
        appUtils.bindEvent($(_pageId + " .historyValueList"), function () {
            appUtils.pageInit(_page_code, "highEnd/historyValueList");
        });
        //历史规模
        appUtils.bindEvent($(_pageId + " .historyScaleList"), function () {
            appUtils.pageInit(_page_code, "highEnd/historyScaleList");
        });
        //分红
        appUtils.bindEvent($(_pageId + " .dividends"), function () {
            appUtils.pageInit(_page_code, "highEnd/dividends");
        });
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .prodName").text("");
        $(_pageId + " .fund_code").text("");
        $(_pageId + " .establishDate").text("");
        $(_pageId + " .riskDesc").text("");
        $(_pageId + " .issuingScale").text("");
        $(_pageId + " .mgrcompName").text("");
        $(_pageId + " .mgrcompSname").text("");
        $(_pageId + " .trusteeName").text("");
        $(_pageId + " .fundManagers").text("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thsurvey = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thsurvey;
});
