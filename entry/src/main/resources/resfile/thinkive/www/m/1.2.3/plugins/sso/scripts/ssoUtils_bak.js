/*创建时间hSea 2016-04-29 23:15:08 PM */
define(function(require,exports,module){function a(a,b){k.request(a,{},b)}function b(a,b,d){var e=b.funcNo;delete b.funcNo;var f=c(e,b);a&&k.request(a,f,d)}function c(a,b){var c={};c.bizcode=a;var e=JSON.stringify(b);if("des"==g.encryMode){var f=j.desEncrypt(l,e);c.data=f,c.encry_mode="des"}else if("aes"==g.encryMode){var i=l.substring(0,16),k=j.aesEncrypt(i,e);c.data=k,c.encry_mode="aes"}else{var n=j.encoderBase64(e);c.data=n}c.merchant_id=m,c.signKey=l,c.request_id=d();for(var o=["bizcode","data","encry_mode","merchant_id","request_id","signKey"],p="",q=0,r=o.length;r>q;q++)p+=o[q]+"="+c[o[q]]+"&";p=p.substring(0,p.length-1);var s=h.md5.md5(p).toUpperCase(),t=p.substring(0,p.indexOf("signKey")-1);return t=t+"&sign="+s}function d(){function a(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return a()+a()+"-"+a()+"-"+a()+"-"+a()+"-"+a()+a()+a()}function e(){var a=g.ssoSignKey;if(a){var b="";try{b=p(a)}catch(c){}b&&(l=b.signKey,m=b.merchant_id)}}var f=require("gconfig"),g=f.global,h=require("digitalSignatureUtils"),i=require("aes"),j=(require("des"),require("endecryptUtils")),k=require("ajax"),l="",m="",n="",o="";+function(){n="thin"}(),+function(){o="func"}();var p=function(a){var b=i.enc.Utf8.parse(n+o),c=i.enc.Utf8.parse(n+o),d=i.enc.Base64.parse(a),e=i.AES.decrypt({ciphertext:d},b,{iv:c,mode:i.mode.CBC});return JSON.parse(e.toString(i.enc.Utf8))};module.exports={ssoLogin:a,ssoRemoteLogin:b,checkCookieValidity:e,ssoSignFunc:c,test:n,func:o}});
/*创建时间 2016-04-29 23:15:08 PM */