// 交易记录详情 - 赎回
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#highEnd_trsDetailsAccumulated ";
    var tools = require("../common/tools");
    // 备注
    var remark_bj = {
        "12413": "滚入下一期产品", 
	
    }


    function init() {
        var param = appUtils.getPageParam();
        $(_pageId + " .sub_name_type").html(param.sub_busi_code)
      	service.reqFun102090(param, function (data) {
                render(data);
        });
        
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    //数据渲染
    function render(data) {
        if (data.error_no != "0") {
            layerUtils.iAlert(data.error_info);
            return;
        }
        var results = data.results[0];
        if (!results || results.length == 0) {
            return;
        }
        //数据处理 空 和 --
        results = tools.FormatNull(results);


        //交易状态
        var trans_status = results.trans_status;
       
        var trans_status_name = tools.fundDataDict(trans_status, "pri_trans_status_name");

        //产品名称
        var prod_name = results.prod_name;
        //产品代码
        var prod_code = "(" + results.prod_code + ")";
        //赎回日期（交易日期）
        var trans_date = results.redeem_date;
        if (trans_date != "--" && trans_date) {
            trans_date = tools.FormatDateText(trans_date.substring(4, 8));
        }
        //确认日期
        var ack_date = results.ack_date;
        if (ack_date != "--" && ack_date) {
            ack_date = tools.FormatDateText(ack_date.substring(4, 8));
        }

        var end_text = "";
        var trans_vol = "";

        trans_vol = tools.fmoney(results.ack_vol);
        trans_vol = trans_vol + "份";
      

        //交易流水号
        var trans_serno = results.trans_serno;

        //确认金额
        var ack_amt = tools.fmoney(results.ack_amt);
        ack_amt = ack_amt + "元";
        //确认净值
        var ack_nav = tools.fmoney(results.ack_nav, 4);
        ack_nav = ack_nav + "元";
        //手续费
        var feet_amt = tools.fmoney(results.feet_amt);
        feet_amt = feet_amt + "元";
        //交易时间
        var trans_time = results.trans_time;
        trans_time = tools.ftime(trans_time);
        if (results.redeem_method == "12402" || results.redeem_method == "12404") { //主动赎回不展示备注
            $(_pageId + " .remark_box").hide();
        } else {
            $(_pageId + " .remark_box").show();
        }
        $(_pageId + " #remark").html(remark_bj[results.redeem_method]);
        $(_pageId + " .trans_vol").html(trans_vol);
        $(_pageId + " #trans_status").html(trans_status_name);
        $(_pageId + " #prod_name").html(prod_name);
        $(_pageId + " #prod_code").html(prod_code);
        $(_pageId + " .trans_date").html(trans_date);
        $(_pageId + " #ack_date").html(ack_date);
        $(_pageId + " #trans_serno").html(trans_serno);
        $(_pageId + " .ack_amt").html(ack_amt);
        $(_pageId + " #ack_nav").html(ack_nav);
        $(_pageId + " #feet_amt").html(feet_amt);
        $(_pageId + " #trans_time").html(trans_time);
        $(_pageId + " .end_text").html(end_text)
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        $(_pageId + " .transfer_sell_text").html('资金到账，回款到')
        $(_pageId + " #sellShow").show();
        $(_pageId + " .trans_vol").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " #redeem_date").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #to_account_date").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " .ack_amt").html("--");
        $(_pageId + " #ack_nav").html("--");
        $(_pageId + " #feet_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " .end_text").html("");
        $(_pageId + " #remark").html("");
        $(_pageId + " #remark_box").hide();
        $(_pageId + " #redeem_date").html("--");
        $(_pageId + " .trans_date").html("--");
        $(_pageId + " #cancelorderBtn").hide();
        $(_pageId + " #overtimeBtn").hide();
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
