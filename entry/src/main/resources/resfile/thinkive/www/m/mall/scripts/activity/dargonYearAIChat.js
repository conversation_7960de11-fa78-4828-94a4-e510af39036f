//活动 - 理财账单
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools");
    var _pageId = "#activity_dargonYearAIChat ";
    var _pageCode = "activity/dargonYearAIChat"
    var layerUtils = require("layerUtils");
    var userInfo, activityInfo;

    /**
     * 初始化
     */
    function init() {
        var contentH = $(_pageId + " .dargon_year_main").height() - 60 - 90;
        $(_pageId + " .activity_content").css({height: contentH})
        console.log(contentH);

    };

    //绑定事件
    function bindPageEvent() {
        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #screen_img").hide();
    };

    var dargonYearAIChatModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = dargonYearAIChatModule;
});
