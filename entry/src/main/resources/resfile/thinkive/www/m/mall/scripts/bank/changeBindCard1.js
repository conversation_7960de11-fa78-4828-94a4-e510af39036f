// 银行换卡-第一步
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        _pageId = "#bank_changeBindCard1 ";
    var _pageCode = "bank/changeBindCard1";
    var tools = require("../common/tools");
    var myAccountInfo;

    function init() {
    	myAccountInfo = appUtils.getSStorageInfo("myAccountInfo");
    	getBankInfo();
    }


    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //更换为其他银行卡
        appUtils.bindEvent($(_pageId + " #change"), function () {
            appUtils.pageInit(_pageCode, "bank/changeBindCard2");
        });
    }

    //获取银行信息
    function getBankInfo(){
    	var src = "./images/bank_"+myAccountInfo.bd_bank_code+".png";
    	$(_pageId + " #bank_icon").attr("src",src);
        $(_pageId + " #bank_name").text(myAccountInfo.bd_bank_name);
        $(_pageId + " #bank_num").text("(尾号"+myAccountInfo.bank_acct_no.substr(-4)+")");
    }
    
    function destroy() {
    	$(_pageId + " #bank_icon").attr("src","");
    	$(_pageId + " #bank_name").text("--");
        $(_pageId + " #bank_num").text("(尾号--)");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
