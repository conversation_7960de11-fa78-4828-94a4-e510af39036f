//选择产品
define(function (require, exports, module) {
    require('../common/vue.min');
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        service = require("mobileService"),
        _pageId = "#scene_selectProduct ";
        _pageCode = "scene/selectProduct";
        gconfig = require("gconfig"),
        global = gconfig.global;
    var ut = require("../common/userUtil");
    var get_pdf_file = require("../common/StrongHintPdf");
    var tools = require("../common/tools");
    let selectProduct //new 一个 vue 实例
    let plan_type;
    let sceneInfo;
    // let fundCode;//产品代码
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    async function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //缓存当前是否为系列投顾从产品
        appUtils.setSStorageInfo("isSeriesComb", '1');
        sceneInfo = appUtils.getSStorageInfo("sceneInfo");//获取场景产品信息
        plan_type = sceneInfo.plan_type;//获取场景产品类型
        let html = await getTemplate({templateId:sceneInfo.select_id}) //拿到模板数据
        $(".scene_selectProduct").html(html)   //渲染模板
        $(_pageId + " #selectProduct").attr("style", `background:${sceneInfo.bgColor}`);//设置背景色
        $(_pageId + " .pageTitle").text(sceneInfo.pageTitle);//设置标题
        appUtils.setSStorageInfo("financial_prod_type", '07');
        selectProduct = new Vue({
            el: '#selectProduct',
            data() {
                return{
                    oss_url: global.oss_url,
                    tgjhList:[],
                    plan_type:'',
                    //用户选择的答案
                    answer:'',
                    //当前产品全部信息
                    senceProduct:{},
                    fundCode:"",
                }
            },
            //视图 渲染前
            created(){
                //获取可选定投产品
                this.getList();
                //保存产品类型
                this.plan_type = plan_type;
            },
            //渲染完成后
            mounted() {
                
            },
            //计算属性
            computed:{
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
            },
            //绑定事件
            methods:{
                //获取场景列表
                getList(){
                    service.reqFun102205({}, async (data) => {
                        if (data.error_no == '0') {
                            let list = data.results;
                            this.answer = list[0].answer;
                            this.tgjhList = list[0].tgjhList;
                            //匹配用户选中的答案，判断当前用户缓存中是否有选中的答案 如果有缓存的答案就直接匹配用户选中的答案，没有取用户目前的答案，没有为空
                            if(appUtils.getSStorageInfo("userChooseAnswer")){
                                this.answer = appUtils.getSStorageInfo("userChooseAnswer");
                            }else{
                                this.answer = list[0].answer;
                            }
                            //渲染
                            this.chooseProdect(this.answer,this.tgjhList);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //选择答案
                chooseAnswer(answer,flag,item){
                    tools.recordEventData('1','chooseAnswer_' + answer,'选择答案');
                    if(!this.answer) this.reqFun102209(answer);
                    this.answer = answer;
                    this.series_name = item.series_sname;
                    let userChooseAnswer = this.answer;
                    //缓存用户当前选中的答案
                    appUtils.setSStorageInfo("userChooseAnswer",userChooseAnswer);
                    if(flag) this.chooseProdect(answer,this.tgjhList);
                },
                reqFun102209(answer){
                    service.reqFun102209({answer:answer}, async (data) => {
                        if (data.error_no == '0') {
                            
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //PDF相关，走公共方法
                is_show_paf() {
                    if(this.fundCode) get_pdf_file.get_file(this.fundCode, _pageId, "7")
                },
                //产品详情
                pageDetails(item){
                    let productInfo = {
                        fund_code:item.income_prod
                    }
                    //缓存当前是持仓还是开启计划
                    appUtils.setSStorageInfo("productInfo",productInfo);
                    //标记用户首次进入 场景产品详情
                    appUtils.setSStorageInfo("firstSceneCombProdDetail",'1');
                    appUtils.setSStorageInfo("isSeriesComb", '1');
                    //记录用户选中的答案
                    appUtils.pageInit(_pageCode, "scene/combProdDetail",{});
                },
                //获取当前选中的产品
                chooseProdect(answer,list){
                    const matchedItems = list.filter(item => item.risk_answer === answer);
                    if(!matchedItems || !matchedItems[0]){
                        this.answer = ''
                    };
                    this.senceProduct = matchedItems.length > 0 ? matchedItems[0] : {};
                    this.fundCode = matchedItems.length > 0 ? matchedItems[0].income_prod : '';
                    appUtils.setSStorageInfo("senceProduct",this.senceProduct);
                    //查询PDF
                    this.is_show_paf();
                },
                //下一步，带着答案跳转开启计划页面
                buy(){
                    if(!this.answer) return layerUtils.iAlert("请选择产品");
                    tools.recordEventData('1','buy','下一步');
                    appUtils.pageInit(_pageCode, "scene/snowballPlan",{});
                }
            }
        })
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function pageBack() {
        appUtils.setSStorageInfo("userChooseAnswer",'');
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .pageTitle").text('');
        
    }
    var scene_selectProduct = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    
    // 暴露对外的接口
    module.exports = scene_selectProduct;
});
