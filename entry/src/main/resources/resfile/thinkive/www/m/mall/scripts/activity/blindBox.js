/**
 * 模块名：晋金财富抽奖外链页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),layerUtils = require("layerUtils"), SHIscroll = require("shIscroll"),
        service = require("mobileService"), gconfig = require("gconfig"), common = require("common"), validatorUtil = require("validatorUtil");
    var external = require("external");
    require("../../js/draw.js");
    /* 常量 */
    var _pageCode = "activity/blindBox", _pageId = "#activity_blindBox";
    /* 变量  活动信息*/
    var state;
    var tools = require("../common/tools");
    var reward_activity_id;
    var blindBoxTimer = null;
    var blindBoxTimer2 = null;
    var blindBoxTimer3 = null;
    var moneyObj = {
        '1':{
            list_text:'积分',
            src: "../mall/images/activity/integral.png",
        },
        '2':{
            list_text:'小米台灯',
            src: "../mall/images/activity/deskLamp.png",
        },
        '3':{
            list_text:'华为手机',
            src: "../mall/images/activity/phone.png",
        },
        '4':{
            list_text:'小米加湿器',
            src: "../mall/images/activity/humidifier.png",
        },
        '5':{
            list_text:'谢谢参与',
            src: "../mall/images/activity/thanks.png",
        },
    };
    /**
     * 初始化
     */
    function init() {
        reward_activity_id = appUtils.getPageParam("reward_activity_id");
        // console.log(reward_activity_id)
        reqFun108022();//盲盒活动信息查询
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), ()=> {
            pageBack();
        });
        //去兑换
        appUtils.bindEvent($(_pageId + " .exchange"), function () {
            appUtils.pageInit(_pageCode, "vipBenefits/index",{'flag':'blindBox'});
        });
        //立即领取
        appUtils.preBindEvent($(_pageId + " .activity_blindBox_ok .btn"), " span",  function(e) {
            $(_pageId + " .activity_blindBox_ok").hide();
            appUtils.pageBack();
        }, "click");

        // appUtils.bindEvent($(_pageId + " .activity_blindBox_ok .btn span"), function () {
        //
        // });
        //开始
        appUtils.preBindEvent($(_pageId + " .game"), " .blindBox_btn",  function(e) {
            // state 1:进行中 2:未开始 3:已结束
            if(state == '2'){
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if(state == '3'){
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            tools.clickPoint(_pageId,_pageCode,'blindBox',reward_activity_id);
            if ($(_pageId + " .game .blindBox_btn").hasClass("notclick")) {
                layerUtils.iAlert('今日抽奖次数已用完');
                return;
            }
            if ($(_pageId + " .game ul li").hasClass("activity")) {
                layerUtils.iAlert('请选个礼盒，试试手气！');
                return;
            }
            var blindBoxCardHtml = '<ul class="main">' +
                '            <li class="card1 card">' +
                '                <img class="positive" src="../mall/images/activity/card2.png" alt="">' +
                '                <img class="reverse" src="../mall/images/activity/card5.png" alt="" style="opacity:0">\n' +
                '            </li>\n' +
                '            <li  class="card2 card">\n' +
                '                <img class="positive" src="../mall/images/activity/card1.png" alt="">\n' +
                '                <img class="reverse" src="../mall/images/activity/card5.png" alt="" style="opacity: 0">\n' +
                '            </li>\n' +
                '            <li class="card3 card">\n' +
                '                <img class="positive" src="../mall/images/activity/card2.png" alt="">\n' +
                '                <img class="reverse"  src="../mall/images/activity/card5.png" alt="" style="opacity: 0">\n' +
                '            </li>\n' +
                '            <li class="card4 card">\n' +
                '                <img class="positive" src="../mall/images/activity/card1.png" alt="">\n' +
                '                <img class="reverse" src="../mall/images/activity/card5.png" alt="" style="opacity: 0">\n' +
                '            </li>\n' +
                '            <li class="card5 card">\n' +
                '                <img class="positive" src="../mall/images/activity/card3.png" alt="">\n' +
                '                <img class="reverse" src="../mall/images/activity/card5.png" alt="" style="opacity: 0">\n' +
                '            </li>\n' +
                '            <li class="card6 card">\n' +
                '                <img class="positive" src="../mall/images/activity/card4.png" alt="">\n' +
                '                <img class="reverse" src="../mall/images/activity/card5.png" alt="" style="opacity: 0">\n' +
                '            </li>\n' +
                '        </ul>'
            $(_pageId + " .activity_blindBox_card").html(blindBoxCardHtml);
            draw.init({el :'.activity_blindBox_card .card', times:10, time:0.2,moveX:33,moveY:1.3,});
            $(_pageId + " .activity_blindBox_card").show()
            // 卡片打乱
            blindBoxTimer1 = setTimeout(() => {
                draw.start()
            }, 3000)
            blindBoxTimer2 = setTimeout(() => {
                // 盲盒相对屏幕的位置
                var boxTop1 = $(_pageId + " .game li[num='0']").offset().top;
                var boxTop2 = $(_pageId + " .activity_blindBox_card .card1").offset().top;
                var heightDif = boxTop1 - boxTop2;
              
                if(heightDif > 0){
                    heightDif = heightDif;
                }else if(heightDif < 0 && heightDif > -100){
                    heightDif = heightDif - 20;
                }else if(heightDif < -100 && heightDif > -200){
                    heightDif = heightDif + 30;
                }else {
                    heightDif = heightDif + 80;
                }
                
                $(_pageId + " .activity_blindBox_card .reverse").animate({
                    top:heightDif,
                },1000,function(){
                    $(_pageId + " .activity_blindBox_card").hide()
                    $(_pageId + " .game ul li").addClass('activity')
                    $(_pageId + " .blindBox .content .game .tishi span").html('选个礼盒，试试手气 ×1');
				})
                $(_pageId + " .activity_blindBox_card .reverse").css({
                    "animation":"donghua 1s linear 0s forwards",
                })
            }, 5000)
        })
        //抽盲盒
        appUtils.preBindEvent($(_pageId + " .game"), ".activity",  function(e) {
            var num=$(this).attr('num')
            // state 1:进行中 2:未开始 3:已结束
            if(state == '2'){
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if(state == '3'){
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            service.reqFun9108017({activity_id:reward_activity_id}, function (data){
                // -2 未绑卡
                // -3 活动尚未开始或已结束
                // -4 次数已用完
                // -6 签到状态已完成
                // -999011 请求过于频繁，请稍候再试
                // 奖池为空时默认谢谢参与
                if (data.error_no == "-2" || data.error_no == "-3" || data.error_no == "-4" || data.error_no == "-999011" || data.error_no == "-6") {
                    $(_pageId + " .game").removeClass("notclick");
                    $(_pageId + " .game .blindBox_btn").removeClass("notclick")
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.error_no != "0") {
                    $(_pageId + " #game").removeClass("notclick");
                    // $(_pageId + " .game ul li").eq(num).addClass('active').removeClass('notDraw').find('img').attr('src',moneyObj[5].src).css('padding-top','0.03rem');
                    // other =   ['1','1','2','2','3'];
                    $(_pageId + " .blindBox .content .game .tishi span").html('好遗憾，离大奖就差一点！');
                    $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv span").html('未中奖');
                    $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv img").attr('src',moneyObj[5].src);
                    $(_pageId + " .activity_blindBox_ok .activityRulesBonce .btn span").html('我知道了');
                    // other.sort(randomsort);
                    // for (var i = 0; i < '5'; i++) {
                    //     $(_pageId + " .game ul li.notDraw").eq(i).find('img').attr('src',moneyObj[other[i]].src).css('padding-top','0.03rem');
                    // }
                    $(_pageId + " .activity_blindBox_ok").show()
                    $(_pageId + " .game .blindBox_btn").removeClass("notclick")
                    blindBoxTimer3 = setTimeout(() => {
                        $(".activity_blindBox_ok .time1").hide()
                    }, 2500)
                    return;
                }
                var results = data.results[0];
                // 按钮禁用
                if ($(_pageId + " .game").hasClass("notclick")) {
                    return;
                }
                $(_pageId + " .game").addClass("notclick");
                var money = Number(results.reward_vol) + "";
                var moneyKey;
                $(_pageId + " #rewardResult").show();
                //奖励类型reward_type 1:随机积分 0:固定积分 2:实物
                if (results.reward_type === "2") { //实物
                    // 实物类型 2-台灯、3-手机、4-小米加湿器
                    moneyKey = results.reward_vol;
                    // $(_pageId + " .game ul li").eq(num).addClass('active').removeClass('notDraw').find('img').attr('src',moneyObj[moneyKey].src).css('padding-top','0.03rem');
                    // other = results.reward_vol == '2' ? ['1','1','2','3','5'] : results.reward_vol == '3' ? ['1','1','2','2','5'] : ['1','1','2','3','5'];
                    $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv span").html(moneyObj[moneyKey].list_text);
                    $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv img").attr('src',moneyObj[moneyKey].src);
                    $(_pageId + " .activity_blindBox_ok .activityRulesBonce .btn span").html('立即领取');
                    $(_pageId + " .blindBox .content .game .tishi span").html('恭喜您获得' + moneyObj[moneyKey].list_text + '!');
                }
                if (results.reward_type === "0" || results.reward_type === "1") { //固定积分、随机积分
                    if(results.reward_vol == '0'){
                        // moneyKey = '5'
                        // other = ['1','1','2','2','3'];
                        $(_pageId + " .blindBox .content .game .tishi span").html('好遗憾，离大奖就差一点！');
                        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv span").html('未中奖');
                        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv img").attr('src',moneyObj[5].src);
                        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .btn span").html('我知道了');
                    }else {
                        // moneyKey = '1'
                        // other =['1','2','2','3','5'];
                        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv span").html(results.reward_vol + '积分');
                        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv img").attr('src',moneyObj[1].src);
                        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .btn span").html('立即领取');
                        $(_pageId + " .blindBox .content .game .tishi span").html('好遗憾，离大奖就差一点！');
                    }
                    // $(_pageId + " .game ul li").eq(num).addClass('active').removeClass('notDraw').find('img').attr('src',moneyObj[moneyKey].src).css('padding-top','0.03rem');
                }
                // other.sort(randomsort);
                // for (var i = 0; i < '5'; i++) {
                //     $(_pageId + " .game ul li.notDraw").eq(i).find('img').attr('src',moneyObj[other[i]].src).css('padding-top','0.03rem');
                // }
                blindBoxTimer2 = setTimeout(() => {
                    $(".activity_blindBox_ok .time1").hide()
                }, 2500)
                $(_pageId + " .activity_blindBox_ok").show()
                $(_pageId + " .game .blindBox_btn").removeClass("notclick")
                $(_pageId + " .game").removeClass("notclick");
            }, {"isShowWait": false});
        }, "click");
    }
    function randomsort(a, b) {
        return Math.random()>.5 ? -1 : 1;
        //用Math.random()函数生成0~1之间的随机数与0.5比较，返回-1或1
    };
    //活动信息查询接口(红包雨、盲盒)108022
    function reqFun108022() {
        // reward_activity_id = '72'
        $(_pageId + " .game .blindBox_btn").removeClass("notclick");
        $(_pageId + " .game").removeClass("notclick");
        if(validatorUtil.isEmpty(reward_activity_id)){
            layerUtils.iAlert('活动ID未配置,请联系管理人员', -1,function () {
                pageBack();
            });
            return;
        }
        service.reqFun108022({activity_id:reward_activity_id}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results[0];
                state = results.state;
                if (state == "2") {
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                }
                if (state == "3") {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     * 销毁
     */
    function destroy() {
		$(_pageId + " .activity_pop_layer").hide();
        service.destroy();
		$(_pageId + " #pointsFor").hide();
        $(_pageId + " #share").hide();
		$(_pageId + " #rewardResult .sureBtn span").html("");
        $(_pageId + " .btn h3").html("");
        $(_pageId + " .btn .fundLuckdraw_btn img").attr("src",'');
        $(_pageId + " .game ul li").removeClass('activity');
        $(_pageId + " .activity_blindBox_card").html("").hide();
        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv span").html('--');
        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .resultsdiv img").attr('src',"");
        $(_pageId + " .activity_blindBox_ok .activityRulesBonce .btn span").html('立即领取');
        $(_pageId + " .blindBox .content .game .tishi span").html('点击“开始”参与游戏');
        $(_pageId + " .game .blindBox_btn").removeClass("notclick").find('li img').attr("src",'../mall/images/activity/box.png');
        $(_pageId + " .game").removeClass("notclick");
        clearTimeout(blindBoxTimer);
        clearTimeout(blindBoxTimer2);
        clearTimeout(blindBoxTimer3);
    };
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
