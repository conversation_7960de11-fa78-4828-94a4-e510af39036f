// 我的转让页面
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#myTransfer_index";
    var _pageCode = "myTransfer/index";
    var apt_mix_money;//起投金额
    // var start = 1;  //分页参数-开始下标
    // var count = 10; //分页参数-每页条数
    // var currentPage;    //当前页
    // var totalPages; //总页数
    var _cust_fund_type = 0
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        _cust_fund_type = appUtils.getPageParam("cust_fund_type") ? appUtils.getPageParam("cust_fund_type") : appUtils.getSStorageInfo("_cust_fund_type") ? appUtils.getSStorageInfo("_cust_fund_type") : 0;
        $(_pageId + " .tab_box a").removeClass("current");
        if(_cust_fund_type == 0){
            $(_pageId + " .cust_fund_type_0").addClass('current');
        }else if(_cust_fund_type == 2){
            $(_pageId + " .cust_fund_type_2").addClass('current');
        }else if(_cust_fund_type == 1){
            $(_pageId + " .cust_fund_type_1").addClass('current');
        }
        chanping();
        // $(_pageId + " .visc_pullDown").css("visibility", "hidden");
        var pageTouchTimer = null;
        // appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
        //     pageTouchTimer && clearTimeout(pageTouchTimer);
        //     pageTouchTimer = setTimeout(function () {
        //         vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
        //     }, 500);
        // }, "touchmove");
        // appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
        //     pageTouchTimer && clearTimeout(pageTouchTimer);
        // }, "touchend");
    }
    function bindPageEvent() {
        //弹出转让规则
        appUtils.bindEvent($(_pageId + " #transferRule"),function () {
            $(_pageId + " .rule_dio").show();
            $(_pageId + " .card1").show();
        })
        //关闭转让规则弹窗
        appUtils.bindEvent($(_pageId + " .card_footer"),function () {
            $(_pageId + " .rule_dio").hide();
            $(_pageId + " .card1").hide();
        })
        //点击持有或在途或已完成按钮
        appUtils.bindEvent($(_pageId + " .tab_box a"), function () {
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            _cust_fund_type = $(this).attr("cust_fund_type");
            appUtils.setSStorageInfo("_cust_fund_type", _cust_fund_type);
            $(_pageId + " .finance_pro .my_finance .purchase_list").html("");
            $(_pageId + " .finance_pro .my_finance .sale_list").html("");
            currentPage = 1;
            if(_cust_fund_type == "2"){
            	// chanping(start, false);
                chanping()
            }else{
            	// chanping(start, false);
                chanping()
            }
        }, "click");
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".pro_detail", function (e) {
            if(_cust_fund_type == 1) return
            let data = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("cancelOrderInfo", data);
            appUtils.setSStorageInfo("_cust_fund_type", _cust_fund_type);
            //转让人状态是转让中 无法查看 
            if(data.entrust_status == '3' && data.same_prod_transfer_all == '1') return;
            //审核中到结果页
            if(data.buy_entrust_status == '1' || data.buy_entrust_status == '2') return appUtils.pageInit(_pageCode, "myTransfer/hangingOrderSuccess", {});
            // if(data.buy_entrust_status == '0'){
            //     appUtils.pageInit(_pageCode, "myTransfer/uploadCredentials", {});
            // }else{
                
            // }
            appUtils.pageInit(_pageCode, "myTransfer/cancellationDetails", {});
            
        }, 'click')
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        _cust_fund_type = 0;
        $(_pageId + " .rule_dio").hide();
        $(_pageId + " .card1").hide();
        $(_pageId + " .finance_pro").html("")
        $(_pageId + " .tab_box a").removeClass("current").eq(0).addClass("current");
        $(_pageId + " .finance_pro .my_finance .purchase_list").html("");
        $(_pageId + " .finance_pro .my_finance .sale_list").html("");
    }

    //我的转让 正在委托
    async function reqFun107005Transfer(){
        return new Promise(async (resolve, reject) => {
            service.reqFun107005({},  (datas)=> {
                if (datas.error_no == 0) {
                    let results = datas.results;
                    resolve(results)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }   
    //我的转让 历史委托
    async function reqFun107007Transfer(){
        return new Promise(async (resolve, reject) => {
            service.reqFun107007({},  (datas)=> {
                if (datas.error_no == 0) {
                    let results = datas.results;
                    resolve(results) 
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }
    //我的转让 正在购买
    async function reqFun107020Transfer(){
        return new Promise(async (resolve, reject) => {
            service.reqFun107020({},  (datas)=> {
                if (datas.error_no == 0) {
                    let results = datas.results;
                    resolve(results) 
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }
    //委托状态
    function show_entrust_status(type,type1){
        var prod_type_obj = {
            "2" : "撤单",
            "6" : "已完成",
            "1" : "挂单成功",
            "4" : "审核中",
            "5" : "转让中",
            "3" : "转让中"
        }
        var pro_his_type_obj = {
            // "1" : "系统撤单",
            // "2" : "系统撤单",
            // "3" : "转让人撤单",
            // "6" : "转让成功"

            //新
            "1":"转让成功",
            "2":"已购买转让",
            "3":"转让人撤单",
            "4":"受让人撤单",
            "5":"系统撤单",
            "6":"购买转让失败"
        }
        var pro_buy_type_obj = {
            "0" : "待汇款",
            "1" : "审核中",
            "2" : "已汇款",
            "3" : "已购买转让",
            "4" : "已撤销购买"
        }
        // console.log(_cust_fund_type)
        return _cust_fund_type == "0" ? prod_type_obj[type] : _cust_fund_type == "1" ? pro_his_type_obj[type1]: pro_buy_type_obj[type];
    }
    // function chanping(start, isAppendFlag) {
    async function chanping(){
        // var param = {
        //     start: 1,
        //     count: 10,
        //     custLabelCnlCode:ut.getUserInf().custLabelCnlCode,
        //     recommend_type:'202'
        // };
        let results 
        if(_cust_fund_type == "0"){
            results = await reqFun107005Transfer()
        }else if(_cust_fund_type == '1'){
            results = await reqFun107007Transfer()
        }else{
            results = await reqFun107020Transfer()
        }
        let productList = results;
        let str = ""
        for (var i = 0; i < productList.length; i++) {
            // if(_cust_fund_type == 0){
                let prod_sname = productList[i].prod_sname //名称
                let costmoney = tools.fmoney(productList[i].costmoney) //本金
                let give_profit = tools.fmoney(productList[i].give_profit) //让出收益
                let transfer_amt = tools.fmoney(productList[i].transfer_amt) //转让金额
                let entrust_date = tools.ftime(productList[i].entrust_date) //转让日期
                let total_amt = tools.fmoney(productList[i].total_amt) //让出收益
                productList[i].back_flag = productList[i].back_flag ? productList[i].back_flag : productList[i].entrust_status //判断撤单和最终状态
                let entrust_status = show_entrust_status(productList[i].entrust_status,productList[i].page_show_state) //转让状态
                let buy_entrust_status = show_entrust_status(productList[i].buy_entrust_status) //转让状态
                let same_prod_transfer_all = productList[i].same_prod_transfer_all; //是否是小集合转让
                let rate = tools.fmoney(productList[i].rate) //转让状态
                let trans_type = productList[i].trans_type == 0 ? '卖' : '买'; // 0卖 1买
                // console.log(costmoney,1111)
                if(_cust_fund_type == '0' || _cust_fund_type == '1'){
                    str += '<div operationType="1" operationId="pro_detail" operationName=""'+ prod_sname +'"" class="pro_detail item template_box flex">' +
                                '<div style="display: none" class="productInfo">' + JSON.stringify(productList[i]) + '</div>' +
                                '<ul class="template_left m_list_color vertical_line">' +
                                    '<li style="border-bottom: 2px solid rgb(217, 229, 232)" class="title m_font_size16 m_bold m_text_darkgray666">'+
                                        prod_sname + `<span class="trans_type ${_cust_fund_type == '1' ? '' : 'display_none'}">${_cust_fund_type == '1' ? trans_type : ''}</span>` + 
                                    '</li>'+
                                    '<li style="width: 100% !important" class="m_font_size12 m_width_20rem flex wrap">'+
                                        '<p class="">转让本金：<span>'+ costmoney +'元</span></p>' +
                                        '<p class="">委托状态：<span>'+ entrust_status +'</span></p>' +
                                    '</li>' +
                                    '<li class="m_font_size12">' +
                                        '<p>让出收益：<span>'+ give_profit +'元</span></p>' +
                                    '</li>' +
                                    '<li class="m_font_size12">' +
                                        '<p>转让价格：<span>'+ transfer_amt +'元</span></p>' +
                                    '</li>'+
                                    '<li class="m_font_size12">'+
                                        '<p>委托时间：<span>'+ entrust_date +'</span></p>'+
                                    '</li>'+
                                '</ul>'+ ((_cust_fund_type == 0 && productList[i].entrust_status == "1")  ? '<ul class="template_right flex_end level_center vertical_center">'+
                                '<li class="btn vertical_center flex level_center">撤单</li>'+
                                '</ul>':'')+
                            '</div>'
                }else{
                    str += '<div operationType="1" operationId="pro_detail" operationName="'+ prod_sname +'" class="pro_detail item template_box flex">' +
                                '<div style="display: none" class="productInfo">' + JSON.stringify(productList[i]) + '</div>' +
                                '<ul class="template_left m_list_color vertical_line">' +
                                    '<li style="border-bottom: 2px solid rgb(217, 229, 232)" class="title m_font_size16 m_bold m_text_darkgray666">'+
                                        prod_sname +
                                    '</li>'+
                                    '<li style="width: 100% !important" class="m_font_size12 m_width_20rem flex wrap">'+
                                        '<p class="">业绩计提基准：<span><span class="m_text_red">'+ rate +'</span>%</span></p>' +
                                        '<p class="">购买状态：<span>'+ buy_entrust_status +'</span></p>' +
                                    '</li>' +
                                    '<li class="m_font_size12">' +
                                        '<p>转让本金：<span>'+ (same_prod_transfer_all == 1 ? costmoney : transfer_amt) +'元</span></p>' +
                                    '</li>' +
                                    '<li class="m_font_size12">' +
                                        '<p>卖方让出收益：<span>'+ give_profit +'元</span></p>' +
                                    '</li>'+
                                    '<li class="m_font_size12">'+
                                        '<p>合计金额：<span>'+ total_amt +'元</span></p>'+
                                    '</li>'+
                                '</ul><ul class="template_right flex_end level_center vertical_center">'+
                                '<li class="btn vertical_center flex level_center">查看</li>'+
                                '</ul>'+
                            '</div>' 
                }
                
            // }else{
            //     str+='<div class="pro_detail item template_box flex"fund_code="A80006"buy_state="1"><div style="display: none" class="productInfo"></div><ul class="template_left m_list_color vertical_line"><li style="border-bottom:2px solid rgb(217,229,232)" class="title m_font_size16 m_bold m_text_darkgray666">晋金元辉固收2116号</li><li style="width: 100% !important;" class="m_font_size12 m_width_20rem flex wrap"><p class="">转让本金：<span>1,000,000.00元</span></p><p class="">委托状态：<span>转让中</span></p></li><li class="m_font_size12"><p>让出收益：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>转让价格：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>委托时间：<span>2020-12-21</span></p></li></ul><ul class="template_right flex_end level_center vertical_center"></ul></div>'
            // }
        }
        $(_pageId + " .finance_pro").html(str);
        // service.reqFun107005({}, function (datas) {
        //     if (datas.error_no == 0) {
        //         var results = datas.results[0];
        //         //空数据处理
        //         results = tools.FormatNull(results);
        //         if (!results) {
        //             //判断是否显示 没有更多数据
        //             // show_new_none(_cust_fund_type)
        //             // $(_pageId + " .new_none").show();
        //             // hidePullUp();
        //             return;
        //         }
        //         // totalPages = results.totalPages;
        //         // currentPage = results.currentPage;
        //         let productList = results.data;
        //         if (!productList || productList.length == 0) {
        //             //判断是否显示 没有更多数据
        //             // show_new_none(_cust_fund_type)
        //             // $(_pageId + " .new_none").show();
        //             // hidePullUp();
        //             return;
        //         }
        //         var str = "";
        //         for (var i = 0; i < productList.length; i++) {
        //             if(_cust_fund_type == 0){
        //                 str+='<div class="pro_detail item template_box flex"fund_code="A80006"buy_state="1"><div style="display: none" class="productInfo"></div><ul class="template_left m_list_color vertical_line"><li style="border-bottom:2px solid rgb(217,229,232)" class="title m_font_size16 m_bold m_text_darkgray666">晋金元辉固收2116号</li><li style="width: 100% !important;" class="m_font_size12 m_width_20rem flex wrap"><p class="">转让本金：<span>1,000,000.00元</span></p><p class="">委托状态：<span>转让中</span></p></li><li class="m_font_size12"><p>让出收益：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>转让价格：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>委托时间：<span>2020-12-21</span></p></li></ul><ul class="template_right flex_end level_center vertical_center"><li class="btn vertical_center flex level_center">撤单</li></ul></div>'
        //             }else{
        //                 str+='<div class="pro_detail item template_box flex"fund_code="A80006"buy_state="1"><div style="display: none" class="productInfo"></div><ul class="template_left m_list_color vertical_line"><li style="border-bottom:2px solid rgb(217,229,232)" class="title m_font_size16 m_bold m_text_darkgray666">晋金元辉固收2116号</li><li style="width: 100% !important;" class="m_font_size12 m_width_20rem flex wrap"><p class="">转让本金：<span>1,000,000.00元</span></p><p class="">委托状态：<span>转让中</span></p></li><li class="m_font_size12"><p>让出收益：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>转让价格：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>委托时间：<span>2020-12-21</span></p></li></ul><ul class="template_right flex_end level_center vertical_center"></ul></div>'
        //             }
        //         }
        //         // if (currentPage <= totalPages) {
        //         //     for (var i = 0; i < productList.length; i++) {
        //         //         if(_cust_fund_type == 0){
        //         //             str+='<div class="pro_detail item template_box flex"fund_code="A80006"buy_state="1"><div style="display: none" class="productInfo"></div><ul class="template_left m_list_color vertical_line"><li style="border-bottom:2px solid rgb(217,229,232)" class="title m_font_size16 m_bold m_text_darkgray666">晋金元辉固收2116号</li><li style="width: 100% !important;" class="m_font_size12 m_width_20rem flex wrap"><p class="">转让本金：<span>1,000,000.00元</span></p><p class="">委托状态：<span>转让中</span></p></li><li class="m_font_size12"><p>让出收益：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>转让价格：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>委托时间：<span>2020-12-21</span></p></li></ul><ul class="template_right flex_end level_center vertical_center"><li class="btn vertical_center flex level_center">撤单</li></ul></div>'
        //         //         }else{
        //         //             str+='<div class="pro_detail item template_box flex"fund_code="A80006"buy_state="1"><div style="display: none" class="productInfo"></div><ul class="template_left m_list_color vertical_line"><li style="border-bottom:2px solid rgb(217,229,232)" class="title m_font_size16 m_bold m_text_darkgray666">晋金元辉固收2116号</li><li style="width: 100% !important;" class="m_font_size12 m_width_20rem flex wrap"><p class="">转让本金：<span>1,000,000.00元</span></p><p class="">委托状态：<span>转让中</span></p></li><li class="m_font_size12"><p>让出收益：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>转让价格：<span>1,000,000.00元</span></p></li><li class="m_font_size12"><p>委托时间：<span>2020-12-21</span></p></li></ul><ul class="template_right flex_end level_center vertical_center"></ul></div>'
        //         //         }
        //         //     }
        //         // }
        //         // if (isAppendFlag) {
        //         //     $(_pageId + " .finance_pro").append(str);
        //         //     // $(_pageId + " .visc_pullUpIcon").hide();
        //         //     // $(_pageId + " .visc_pullUpDiv").hide();
        //         // } else {
        //         //     $(_pageId + " .finance_pro").html(str);
        //         // }
        //         $(_pageId + " .finance_pro").html(str);
        //         // pageScrollInit();

        //     } else {
        //         layerUtils.iAlert(datas.error_info);
        //     }
        // });
    }
    function hidePullUp() {
    //$(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }
    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) { 
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    start = 1;
                    // chanping(start, false);
                    chanping()
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        start += count;
                        // chanping(start, true);
                        chanping()
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
            
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});
