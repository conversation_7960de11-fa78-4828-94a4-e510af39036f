//转让买入，卖出交易记录详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#myTransfer_transferPurchaseDetails ";
        _pageUrl = "myTransfer/transferPurchaseDetails";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var info;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //拿到上个页面的数据
        info = appUtils.getPageParam();
        // console.log(info)
        //remark_title
        if(info.sub_busi_code_e == '12226'){
            //展示手续费 买入
            $(_pageId + " .feet_amt").show();
        }else{
            $(_pageId + " .feet_amt").hide();
        }
        $(_pageId + " .statusName").text(info.sub_busi_code);
        detail(info)
    }
    function detail(param) {
        var callback = function(data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }

            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);
            let prod_name = results.prod_name;
            let trans_status = results.trans_status; //交易订单状态
            let its_date = results.its_date;//申购日期
            let trans_serno = results.trans_serno;//流水号
            let feet_amt = tools.fmoney(results.feet_amt);//手续费
            let trans_time = results.trans_time;//交易时间
            let ack_vol = tools.fmoney(results.ack_vol);//转让份额
            let ack_amt = tools.fmoney(results.ack_amt);//转让价格
            let remark1 = results.remark1; //备注
            if (its_date != "--") {
                its_date = tools.FormatDateText(its_date.substring(4, 8));
            }
            let trans_status_name = tools.fundDataDict(trans_status, "pri_trans_status_name");
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #its_date").html(its_date);
            $(_pageId + " #feet_amt").html(feet_amt + '元');
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " #trans_time").html(trans_time);
            $(_pageId + " #ack_vol").html(ack_vol + '份');
            $(_pageId + " #ack_amt").html(ack_amt + '元');
            $(_pageId + " .trans_amt").html(ack_amt + '元');
            $(_pageId + " #remark1").text(remark1);
        }
        //转让购买 if(info.sub_busi_code_e == '12226') 
        service.reqFun102190(param,  callback);
        
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function pageBack() {
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    	$(_pageId + " .feet_amt").hide();
    }
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
