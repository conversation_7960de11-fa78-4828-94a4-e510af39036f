define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        putils = require("putils"),
        common = require("common");
    var ut = require("../common/userUtil");
    var _pageCode = "fundSupermarket/purchaseDetail";
    var _pageId = "#fundSupermarket_purchaseDetail"
    var tools = require("../common/tools");
    let pdfh5 = require('../common/pdfh5.js')
    var monkeywords = require("../common/moneykeywords");
    var userInfo;
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var new_setInterval;
    var pdfInfo, fund_codes, buy_moneys, purchase_list// 产品代码， 购买总额
    var _available_vol; //可用金额
    var jymm;
    let bank_state = "";//银行状态
    let bankInfo  = {};//缓存的银行卡信息
    var prod_list = [];
    var bankcard_fixedinvest_flag;//多基金定投协议
    var sms_mobile = require("../common/sms_mobile");
    var pop_time;
    var tranType;//交易类型，01-一键买入  02-一键定投
    let weekList = ['','周一','周二','周三','周四','周五']; //每周定投日期
    var nextdate,firstText,secoundText;//下次扣款时间
    let investcycle = '2';
    let investdate = '1';
    let agreementsFlag = '0';
    let payMethod;
    let bank_serial_no;
    // let pdfParam = {};//获取PDF信息
    // let resultDeta
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        tools.whiteList(_pageId);
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);//初始化发短信
        buy_moneys = appUtils.getSStorageInfo("buy_moneys");
        var prodList = appUtils.getSStorageInfo("prodList")
        tranType = appUtils.getSStorageInfo("tranType");
        let param = [];
        fund_codes = [], prod_list = [];
        prodList.forEach(its => {
            if (its.checked && parseFloat(its.buy_money) > 0) {
                param.push({
                    fund_code: its.fund_code,
                    money: its.buy_money
                });
                fund_codes.push(its.fund_code);
                prod_list.push(its);
            }
        })
        
        param = JSON.stringify(param);
        initPurchaseDetail(param);
        //可用份额
        reqFun101901()
        // pdfParam.fund_code = fund_codes;
        //渲染银行卡
        setBankInfo()
        is_show_paf(fund_codes);
    }
    //协议页面回显
    function backShow(){
        if (tranType != '02') return
        
    }
    function setBankInfo() {
        if (tranType != '02') return;    //回显
        //获取银行卡限额
        get_single_limit()
        let imgUrl = "images/bank_" + userInfo.bankCode + ".png"
        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
        $(_pageId + " .bankImg").css({ "background": "url('" + imgUrl + "') no-repeat" });
        $(_pageId + " .backName").html(userInfo.bankName + '(尾号' + bankAcct + ')')
    }
    //获取银行卡限额
    function get_single_limit() {
        let params = {
            acct_no:userInfo.bankAcct,
            bank_code:userInfo.bankCode,
            bank_reserved_mobile:userInfo.bankReservedMobile
        }
        service.reqFun101084(params, function (data) {
            
            if (data.results && data.results.length > 0) {
                var result = data.results[0];
                bankInfo = result;
                let is_bank_fixed_investment = result.is_bank_fixed_investment;//是否支持银行卡定投
                let fixed_investment_priority = result.fixed_investment_priority;//优先级判断 0 晋金宝 1 银行卡
                let is_exist = result.is_exist; //是否签约 0未签约 1已签约
                bank_state = result.bank_state; //0银行维护中，1银行正常
                let backData = appUtils.getSStorageInfo("backData");    //缓存中的页面数据
                if(is_bank_fixed_investment == '0'){    //不支持
                    bankcard_fixedinvest_flag = '0'
                    payMethod = '0'
                    // tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank").hide();
                    $(_pageId + " .grid_03").hide();
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .chooseJjb").removeClass('borderActive');
                    return;
                }else{
                    // $(_pageId + " .chooseJjb").addClass('borderActive');
                    // $(_pageId + " .chooseBank").show();
                    bankcard_fixedinvest_flag = '1'
                    // tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank").show();
                    $(_pageId + " .grid_03").show();
                    $(_pageId + " .chooseJjb").addClass('borderActive');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                };
                if(fixed_investment_priority == '0'){   //优先晋金宝
                    // tools.getPdf(pdfParam);
                    bankcard_fixedinvest_flag = '0';
                    payMethod = '0';
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .grid_03").hide();
                }else{
                    if(is_exist == '0'){    //未签约，展示验证码
                        $(_pageId + " .chooseBank").addClass('borderActive');
                        $(_pageId + " .grid_03").show();
                    }else{
                        $(_pageId + " .grid_03").hide();
                    }
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    // let data = pdfParam;
                    // payMethod = '1'; 
                    if(!backData || backData.backShow != "1") payMethod = '1';
                    bankcard_fixedinvest_flag = '1'
                    // tools.getPdf(data);
                }
                if(bank_state == '0'){  //维护中
                    bank_state = '0';
                    bankcard_fixedinvest_flag = '0'
                    payMethod = '0'
                    // tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .chooseBank").addClass('color_ccc')
                }else{
                    // tools.getPdf(pdfParam);
                    bank_state = '1';
                    // payMethod = '1';
                    // if(!backData || backData.backShow != "1") payMethod = '1';
                    $(_pageId + " .chooseBank").removeClass('color_ccc')
                }
                
                single_limit = result.single_limit;
                day_limit = result.day_limit;
                if (single_limit) {
                    if (single_limit < 0) {
                        $(_pageId + " .single_limit").html("不限");
                    } else {
                        if (single_limit.length > 4) {
                            $(_pageId + " .single_limit").html(single_limit / 10000 + "万元");
                        } else {
                            $(_pageId + " .single_limit").html(single_limit + "元");
                        }
                    }
                }
                if (day_limit) {
                    if (day_limit < 0) {
                        $(_pageId + " .day_limit").html("不限");
                    } else {
                        if (day_limit.length > 4) {
                            $(_pageId + " .day_limit").html(day_limit / 10000 + "万元");
                        } else {
                            $(_pageId + " .day_limit").html(day_limit + "元");
                        }
                    }
                }
                
                // console.log($(_pageId + " .grid_03").hasClass("active"))
                if(backData && backData.backShow == '1'){
                    if(backData.payMethod == '0'){
                        $(_pageId + " .chooseBank .img").removeClass('active');
                        $(_pageId + " .chooseJjb .img").addClass('active');
                        $(_pageId + " .grid_03").hide();
                        payMethod = '0'
                    }else if(backData.payMethod == '1'){
                        $(_pageId + " .chooseJjb .img").removeClass('active');
                        $(_pageId + " .chooseBank .img").addClass('active');
                        payMethod = '1'
                        if(backData.is_exist == '0'){
                            //未签约
                            $(_pageId + " .chooseBank").addClass('borderActive');
                            $(_pageId + " .grid_03").show();
                        }else{
                            $(_pageId + " .grid_03").hide();
                            $(_pageId + " .chooseBank").removeClass('borderActive');
                        }
                    }
                }
                if(!backData || backData == 'null') return;
                backData.backShow = '0'
                appUtils.setSStorageInfo("backData",backData);
            } else {
                layerUtils.iAlert(error_info);
            }
        })
    }
    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额：<em class="money m_text_red">' + tools.fmoney(_available_vol + "") + '</em>元';
            $(_pageId + " .pay_bank").html(html);
            $(_pageId + " .payMethodRemark").html(html).find('.money').css('color','#e5443c');
        })
    }

    // 是否展示产品概览
    function is_show_paf(fund_codes) {
        service.reqFun102137({
            cust_no: userInfo.custNo,
            fund_code_list: fund_codes.join(",")
        }, (data) => {
            if (data.error_no == '0') {
                let res = data.results[0]
                if (res.list && !res.list.length) return;
                pdfInfo = res;
                pop_time = res.pop_time ? res.pop_time : 5;
                let info = res.list;
                let html = '';
                info.forEach((item, i) => {
                    item._file_url = global.oss_url + item.file_url
                    html += ` <li  value=${i}>${item.prod_sname}</li>`
                })
                // 默认显示第一个
                $(_pageId + " .pdf_name").html(`${info[0].prod_sname}`)
                $(_pageId + ' #more_select').html(html);
                ExhibitionPdf(info[0], _pageId)
                // 下拉change
                appUtils.preBindEvent($(_pageId + " #more_select"), " li", function () {
                    var i = $(this).attr("value");
                    ExhibitionPdf(info[i], _pageId)
                    $(_pageId + " .pdf_name").html(`${info[i].prod_sname}`)
                    $(_pageId + " #more_select").hide();
                });
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function ExhibitionPdf(data, _pageId) { //进行渲染PDF
        //阅读并同意（4）秒
        let setDom = $(_pageId + " .iframe_pdf_bottom .vant_button") //操作按钮的dom元素
        $(_pageId + ' .iframe_pdf_header span').text(data.file_title)
        $(_pageId + ' .van-overlay').show()
        setDom.addClass("disable");
        setDom.attr('getServer', true);
        let tip = '阅读并同意（' + pop_time + '）秒'
        setDom.text(tip)
        new_setInterval = setInterval(() => {
            if (pop_time > 0) {
                pop_time--
                let text = '阅读并同意（' + pop_time + '）秒'
                setDom.text(text)
            } else {
                clearTime()
                setDom.attr('getServer', false);
                setDom.removeClass("disable");
                setDom.text('我已阅读并同意')
                return
            }
        }, 1000);
        pdfh5 = new Pdfh5(_pageId + ' #iframe_pdf', {
            pdfurl: data._file_url,
            loadingBar: true
        });
        pdfh5.on("complete", (status, msg, time) => {
            // console.log('渲染完成')
        })
        pdfh5.on("error", function (time) {
            clearTime()
            layerUtils.iAlert('获取PDF文件失败，请检查文件');
        })
    }

    function clearTime() {
        if (new_setInterval) clearInterval(new_setInterval)
    }
    // 定投相关初始化
    function fixedDate() {
        if(appUtils.getSStorageInfo("fixedDate")){
            investcycle = appUtils.getSStorageInfo("fixedDate").investcycle;
            investdate =appUtils.getSStorageInfo("fixedDate").investdate;
            firstText = (investcycle == '2') ? "每月" :  (investcycle == '0') ? "每周" : "每两周";
            if(investcycle == '2'){//每月
                //渲染每月
                setChooseTime(0)
                secoundText = investdate + '日';
            }else {
                //渲染每周 每两周 日期数据
                setChooseTime()
                secoundText = weekList[investdate];

            }
            $(_pageId + " .listLeft li").removeClass('active');
            $(_pageId + " .listLeft").find(' #'+ investcycle).addClass('active');
            $(_pageId + " .listRight li").removeClass('active');
            $(_pageId + " .listRight").find(' #'+ investdate).addClass('active');
        }else {
            investcycle = '2';
            investdate = '1';
            firstText = '每月';
            secoundText = '1日';
            $(_pageId + " .listLeft li").removeClass('active');
            $(_pageId + " .listLeft li").first().addClass('active');
            setChooseTime(0);
        }
        $(_pageId + " .investmentText").text(firstText + ' ' +secoundText);

        //获取下一交易日
        getNextTime(investcycle,investdate)
    }

    function initPurchaseDetail(param) {
        var theadData;
        if (tranType == '01'){
            theadData = '<th style="width: 50%;">基金名称</th>\n' +
                '                            <th>买入金额</th>\n' +
                '                            <th>买入费用</th>'
            $(_pageId + " .header_inner h1").html('一键分散买入');
            $(_pageId + " .tranType").hide();
            $(_pageId + " .tranType1").show()

        }else if (tranType == '02'){
            theadData = '<th style="width: 50%;">基金名称</th>\n' +
                '                            <th>定投金额</th>\n' +
                '                            <th>买入费用</th>'
            $(_pageId + " .header_inner h1").html('一键分散定投');
            $(_pageId + " .tranType").hide();
            $(_pageId + " .tranType2").show()
            // 定投相关参数
            fixedDate()
        }
        $(_pageId + ' #prod_detail table thead').html(theadData);
        // 查询一键购买明细
        service.reqFun102139({ fund_list: param }, (data) => {
            if (data.error_no == '0') {
                var res = data.results[0]
                $(_pageId + " #qrDate").html(tools.FormatDateText(res.qrDate.substring(4, 8)));
                if (res.list && res.list.length) {
                    purchase_list = res.list;
                    let html = '';
                    var total_money = 0;
                    var total_cost = 0
                    res.list.forEach(item => {
                        total_cost = common.floatAdd(total_cost, item.cost);
                        total_money = common.floatAdd(total_money, item.money);
                        html += `
                            <tr>
                                <td>${item.fund_sname}</td>
                                <td>${tools.fmoney(item.money)}</td>
                                <td>${tools.fmoney(item.cost)}</td>
                            </tr>`;
                    })
                    html += `
                        <tr>
                            <td class="m_bold">总计</td>
                            <td class="m_bold">${tools.fmoney(total_money)}</td>
                            <td class="m_bold">${tools.fmoney(total_cost)}</td>
                        </tr>
            `
                    $(_pageId + ' #prod_detail table tbody').html(html);
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 获取协议
    function getAgreement() {
        let param = {
            cust_no: userInfo.custNo,
            fund_code_list: fund_codes.join(","),
            agreement_sub_type: 1,
        }
        if (tranType == '02'){
            param["fixed_invest_flag"] = "1";
            param["bank_code"] = userInfo.bankCode;
            param["acct_no"] = userInfo.bankAcct;
            param["bankcard_fixedinvest_flag"] = bankcard_fixedinvest_flag;
            let backData = {
                payMethod:payMethod,
                backShow:'1',
                is_exist:bankInfo.is_exist
            }
            appUtils.setSStorageInfo("backData", backData);
        }
        service.reqFun102140(param, function (data) {
            if (data.error_no == '0') {
                var res = data.results[0];
                appUtils.setSStorageInfo("agreements", res);
                agreementsFlag = '1';
                appUtils.pageInit(_pageCode, "fundSupermarket/moreAgreement");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 协议留痕
    function agreementTrace() {
        let param = {
            cust_no: userInfo.custNo,
            cust_type: userInfo.custType,
            fund_code_list: fund_codes.join(","),
            agreement_sub_type: 1
        }
        if (tranType == '02'){
            param["fixed_invest_flag"] = "1";
            param["bank_code"] = userInfo.bankCode;
            param["acct_no"] = userInfo.bankAcct;
            param["bankcard_fixedinvest_flag"] = bankcard_fixedinvest_flag;
        }
        service.reqFun102141(param, function (data) {
            if (data.error_no == '0') {
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = appUtils.getSStorageInfo("buy_moneys");
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        // 购买金额小于可用金额
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            if (tranType == '01'){
                // 一键购买
                $(_pageId + " .model_bottom").addClass("noactive");
            }else if (tranType == '02'){
                // 一键定投
                $(_pageId + " .model_bottom").removeClass("noactive");
            }
        }
    }
    //渲染每月 每周 每两周 日期数据
    function setChooseTime(num){
        let str = ''
        if(num == 0){
            //月
            secoundText = '1日'
            for(let i = 1;i<=28;i++){
                let childStr = `<li operationType="1" operationId="date_${i}" operationName="${i + '日'}" class="${i == 1 ? 'active' : ''}" id="${i}">${i+'日'}</li>`
                str += childStr
            }
        }else{
            //周
            secoundText = '周一'
            for(let i = 1;i<weekList.length;i++){
                let childStr = `<li operationType="1" operationId="week_${i}" operationName="${weekList[i]}" class="${i == 1 ? 'active' : ''}" id="${i}">${weekList[i]}</li>`
                str += childStr
            }
        }
        // investdate = '1'
        $(_pageId + ' .listRight').html(str)
    }
    // 获取下一交易日
    function getNextTime(investcycle,investdate){
        let data = {
            investcycle:investcycle,
            investdate:investdate
        }
        service.reqFun106045(data, function (datas) {
            if(datas.error_no != '0'){
                layerUtils.iAlert(data.error_info);
                return;
            }
            nextdate = (tools.FormatDateText(datas.results[0].nextdate,1))
            $(_pageId + ' .nextTimes').text(nextdate + ' 11:00')
        })

    }

    function bindPageEvent() {
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var param = {
                    "bank_code": userInfo.bankCode,//银行编码
                    "pay_type": bankInfo.pay_type,
                    "payorg_id": bankInfo.payorg_id,
                    "bank_acct": userInfo.bankAcct,     // 用户卡号
                    "bank_reserved_mobile":userInfo.bankReservedMobile,
                    "cert_no": userInfo.identityNum,   // 用户身份证
                    "bank_name":userInfo.bankName,
                    "sms_type":common.sms_type.bankInvestment,
                    "send_type": "0",
                    "cust_name": userInfo.name, // 用户姓名
                    "cert_type": "0", //证件类型
                    "mobile_phone": userInfo.mobileWhole,
                    "type": common.sms_type.bankInvestment,//发送短信验证码
                }
                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
    	//晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            let backData = {
                payMethod:payMethod,
                backShow:'1',
                is_exist:bankInfo.is_exist
            }
            appUtils.setSStorageInfo("backData", backData);
            tools.intercommunication(_pageCode);
        });
        //选择定投日期
        appUtils.bindEvent($(_pageId + " .cycleClick"), function () {
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #cycleModel").show()
        });
        //确定选择定投日期
        appUtils.bindEvent($(_pageId + " .determine"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #cycleModel").hide()
            $(_pageId + " .investmentText").text(firstText + ' ' + secoundText)
            var fixedDate = {
                'investcycle':investcycle,
                'investdate':investdate,
            }
            appUtils.setSStorageInfo("fixedDate", fixedDate);
            getNextTime(investcycle,investdate)
        });
        //选择日期
        appUtils.preBindEvent($(_pageId + " .listRight"), "li", function () {
            $(_pageId + " .listRight li").removeClass('active');
            $(this).addClass('active');
            //文案初始化
            investdate = $(this).attr('id');
            secoundText = $(this).text();
            //参数初始化
            // investcycle = '2';
            // investdate = '1';
        }, 'click');
        //选择周，两周，月
        appUtils.preBindEvent($(_pageId + " .listLeft"), "li", function () {
            $(_pageId + " .listLeft li").removeClass('active')
            $(this).addClass('active')
            investcycle = $(this).attr('id')
            firstText = $(this).text()
            if(investcycle == 2){
                setChooseTime(0)
            }else{
                setChooseTime()
            }
        }, 'click');
        //关闭定投日期选择弹框
        appUtils.bindEvent($(_pageId + " #closeCycle"), function () {
            $(_pageId + " #cycleModel").hide();
            $(_pageId + " .pop_layer").hide();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

        //返回页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        appUtils.bindEvent($(_pageId + " #more_desc .placeholder"), function () {
            $(_pageId + " #more_desc #more_select").show();
        });
        appUtils.bindEvent($(_pageId + " .iframe_pdf_bottom .vant_button"), (e) => {
            e.stopPropagation();
            let setDom = $(_pageId + " .iframe_pdf_bottom .vant_button") //操作按钮的dom元素
            if (!setDom.attr('getServer')) return
            //点击确定调用接口
            let param = [];
            pdfInfo.list.forEach(item => {
                param.push({
                    fund_code: item.prod_id,
                    version: item.version
                })
            })
            param = JSON.stringify(param);
            service.reqFun102138({ fund_list: param }, (data) => {
                if (data.error_no == '0') {
                    clearTime()
                    $(_pageId + " .van-overlay").hide();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })

        //取消
        appUtils.bindEvent($(_pageId + " #paf_new_close"), (e) => {
            clearTime()
            $(" .van-overlay").hide();
            appUtils.pageBack();
        })

        //勾选协议
        appUtils.preBindEvent($(_pageId + " .agreement"), ".agreement2", function (e) {
            e.stopPropagation();
            e.preventDefault();
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active")
            } else {
                $(this).find("i").addClass("active")
            }
        }, 'click');

        // 点击相关协议
        appUtils.bindEvent($(_pageId + " .deal_box"), function (e) {
            e.stopPropagation();
            getAgreement();
        })

        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //选择支付方式
        appUtils.preBindEvent($(_pageId + " .payList"), "#amount_enough", function () {
            if($(this).attr('payMethod') == '1' && bank_state == '0') return;
            $(_pageId + " #amount_enough .img").removeClass('active')
            $(this).find(".img").addClass('active')
            payMethod = $(this).attr('payMethod');
            if(payMethod == '1'){   //选中银行卡
                // let data = pdfParam;
                bankcard_fixedinvest_flag = '1'
                // tools.getPdf(data);
                if(bankInfo.is_exist == '0'){    //未签约，展示验证码
                    $(_pageId + " .chooseBank").addClass('borderActive');
                    $(_pageId + " .grid_03").show();
                }else{
                    $(_pageId + " .grid_03").hide();
                }
                
            }else{
                bankcard_fixedinvest_flag = '0'
                // tools.getPdf(pdfParam);
                $(_pageId + " .chooseBank").removeClass('borderActive');
                $(_pageId + " .grid_03").hide();
            }
        }, 'click');
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });

        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });

        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });

        //下一步 显示购买弹框
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }

            agreementTrace(); // 协议留痕
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && prod_list.some(item => item.risklevel_code != 'R!')) {
                let noR1Prod = prod_list.filter(prod => {
                    return prod.risklevel_code != 'R1';
                })
                if (noR1Prod.length) {
                    let _html = '';
                    noR1Prod.forEach(item => {
                        _html += `<li style="list-style: disc;">${item.prod_sname}</li>`
                    })
                    layerUtils.iConfirm(`<span style='text-align: left;padding:0;display:inline-block'>您的风险承受能力为${userInfo.riskName}，以下产品超过了您的风险承受能力，若仍选择投资，请重新测评。<ul style="padding: 0 0.46rem;
                text-align: left; margin-top: -0.2rem;margin-bottom: -0.2rem;color: #2e2e2e;font-size: 16px">${_html}</ul></span>`, () => { }, () => {
                        appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                    }, '取消', '重新测评');
                }

                return;
            }
            // 比较风险等级
            var userRiskLevel = userInfo.riskLevel;
            userRiskLevel = +(userRiskLevel.substr(-1))
            var trans_amt = appUtils.getSStorageInfo("buy_moneys");
            trans_amt = trans_amt.replace(/,/g, "");
            let buyFlagProd = prod_list.filter(prod => {
                let product_risk_level = (+prod.risklevel_code.substr(-1));
                return product_risk_level > userRiskLevel
            })
            let bank_first_max_amt = bankInfo.single_limit*1
            
            if(payMethod == '1' && (trans_amt*1 > bank_first_max_amt)){
                return layerUtils.iAlert("定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。");
            }
            // return console.log(payMethod,trans_amt*1 > bank_first_max_amt)
            if(payMethod == '1' && bankInfo.is_exist == '0' && isSend == "true"){
                return layerUtils.iAlert("请获取验证码");
            }
            if (verificationCode.length != 6 && payMethod == '1' && bankInfo.is_exist == '0') {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            // if (payMethod == '0') {
            //     $(_pageId + " .payMethodName").text('晋金宝');
            //     var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
            //     $(_pageId + " .payMethodRemark").html(html);
            //     // $(_pageId + " .payMethodRemark").html('可用金额:'+tools.fmoney(_available_vol) + "元");

            // } else if (payMethod == '1') {
            //     let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
            //     $(_pageId + " .payMethodName").text(userInfo.bankName + '(尾号' + bankAcct + ')');
            //     $(_pageId + " .payMethodRemark").text('限额:');
            // }
            if (buyFlagProd.length) {
                let html = ``;
                buyFlagProd.forEach(item => {
                    html += `<li style="list-style: disc;">${item.prod_sname}</li>`
                })
                layerUtils.iConfirm(`<span style='text-align: left;padding:0;display:inline-block'>您的风险承受能力为${userInfo.riskName}，以下产品超过了您的风险承受能力，若继续购买，则表明您愿意承担由此产生的风险<ul style="padding: 0 0.46rem;
                text-align: left; margin-top: -0.2rem;margin-bottom: -0.2rem;color: #2e2e2e;font-size: 16px">${html}</ul></span>`, function () {
                    appUtils.pageInit(_pageCode, "safety/riskQuestion");
                }, function funcNo() {
                    if (tranType == '02'){
                        //多基金定投逻辑
                        $(_pageId + " .pop_layer").show();
                        // $(_pageId + " #payMethod").show();
                        $(_pageId + " .password_box").show();
                        //输入交易密码时的提示
                        setRechargeInfo();
                        passboardEvent();
                        monkeywords.flag = 0;
                        //键盘事件
                        var param = {};
                        param["moduleName"] = "mall";
                        param["funcNo"] = "50210";
                        param["pageId"] = "fundSupermarket_purchaseDetail";
                        param["eleId"] = "jymm";
                        param["doneLable"] = "确定";
                        param["keyboardType"] = "4";
                        require("external").callMessage(param);
                    }else{
                        //多基金购买逻辑
                        $(_pageId + " .pop_layer").show();
                        $(_pageId + " #payMethod").show();
                        //是否可以购买
                        isCanBuy();
                    }
                    
                }, "重新测评", "继续购买");
                return;
            }
            if (tranType == '02'){
                //多基金定投逻辑
                $(_pageId + " .pop_layer").show();
                // $(_pageId + " #payMethod").show();
                $(_pageId + " .password_box").show();
                //输入交易密码时的提示
                setRechargeInfo();
                passboardEvent();
                monkeywords.flag = 0;
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "fundSupermarket_purchaseDetail";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
            }else{
                //多基金购买逻辑
                $(_pageId + " .pop_layer").show();
                $(_pageId + " #payMethod").show();
                //是否可以购买
                isCanBuy();
            }
        })
        // 验证码 控制全文字
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fundSupermarket_purchaseDetail";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            // jymm1 = '123123';
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            if (tranType == '01'){
                // 一键购买
                oneClickPurchase(jymm1);
            }else if (tranType == '02'){
                // 一键定投
                oneClickFixedInvestment(jymm1);
            }
        });

    }
    // 一键购买
    function oneClickPurchase(jymm1) {
        //进行充值
        sms_mobile.clear();
        var trans_amt = appUtils.getSStorageInfo("buy_moneys");
        trans_amt = trans_amt.replace(/,/g, "");

        var param = {
            totalmoney: trans_amt, //交易金额
            trans_pwd: jymm1, //交易密码
            purchase_list: [],
            custno: userInfo.custNo,
        };
        if (purchase_list && purchase_list.length) {
            let arr = [];
            purchase_list.forEach(item => {
                arr.push({
                    fundcode: item.fund_code,
                    purchasemoney: item.money,
                    purchasecost: item.cost,
                    virfundcode: ""
                });
            })
            param.purchase_list = JSON.stringify(arr);
        }
        //交易密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                return;
            }
            var modulus = data.results[0].modulus;
            var publicExponent = data.results[0].publicExponent;
            var endecryptUtils = require("endecryptUtils");
            param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
            service.reqFun106047(param, function (resultVo) {
                if (resultVo.error_no != 0) {
                    layerUtils.iAlert(resultVo.error_info);
                    return;
                }
                appUtils.pageInit(_pageCode, "template/templateBuyResult", { type: "fundsBuy" });
            });

        }, { isLastReq: false });
    }
    // 一键定投
    function oneClickFixedInvestment(jymm1) {
        //进行充值
        var trans_amt = appUtils.getSStorageInfo("buy_moneys");
        trans_amt = trans_amt.replace(/,/g, "");

        var param = {
            totalmoney: trans_amt, //交易总金额
            transpwd: jymm1, //交易密码
            custno:userInfo.custNo,
            payMethod:payMethod,//支付方式0晋金宝
            investdate:investdate,//定投日期
            investcycle:investcycle,//定投周期，0每周1每两周2每月
            purchase_list:[],//定投数据
            isexist:bankInfo.is_exist,
            messagecode:$(_pageId + " #verificationCode").val(),
        };
        if (purchase_list && purchase_list.length) {
            let arr = [];
            purchase_list.forEach(item => {
                arr.push({
                    fundcode: item.fund_code,
                    investmoney: item.money,//定投金额
                    investcost: item.cost,//买入费率
                    virfundcode: ""
                });
            })
            param.purchase_list = JSON.stringify(arr);
        }
        //交易密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                return;
            }
            var modulus = data.results[0].modulus;
            var publicExponent = data.results[0].publicExponent;
            var endecryptUtils = require("endecryptUtils");
            param.transpwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.transpwd);
            param.bank_serial_no = bank_serial_no;
            service.reqFun106049(param, function (resultVo) {
                if (resultVo.error_no != 0) {
                    layerUtils.iAlert(resultVo.error_info);
                    return;
                }
                let params = {
                    payMethodName:$(_pageId + " .backName").text(),
                    nextdate:nextdate,
                    firstText:firstText,
                    secoundText:secoundText,
                    prod_sname:'',
                    payMethod:payMethod,
                    money:tools.fmoney(trans_amt),
                    prod_sname: $(_pageId + " .prod_sname").html(),
                }
                appUtils.pageInit(_pageCode, "fixedInvestment/addResults", params);
            });
        }, { isLastReq: false });
    }
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        // console.log(payMethod)
        //购买金额
        // $(_pageId + " #recharge_money").html(tools.fmoney(appUtils.getSStorageInfo("buy_moneys").replace(/,/g, "")));
        let passwordPrompt;
        if (tranType == '01'){
            passwordPrompt = '使用<em>晋金宝</em>，一键分散买<em id="recharge_money">' + tools.fmoney(appUtils.getSStorageInfo("buy_moneys").replace(/,/g, "")) + '</em>元'
        }else if (tranType == '02'){
            let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
            let str;
            if(payMethod == "0"){
                str = '晋金宝';
            }else if(payMethod == '1'){
                str = userInfo.bankName + '(尾号' + bankAcct + ')'
            }
            passwordPrompt = '使用<em>'+ str +'</em>，一键分散定投<em id="recharge_money">' + tools.fmoney(appUtils.getSStorageInfo("buy_moneys").replace(/,/g, "")) + '</em>元，' +
                '<em>'+ $(_pageId + " .investmentText").text() +'</em>'  + '扣款。';
        }
        $(_pageId + " #rechargeInfo").html(passwordPrompt)
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    //页面数据初始化
    function initialization(){
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        $(_pageId + " #amount_enough").removeClass('active');
        $(_pageId + " .investmentText").text('每月 1日');
        firstText = '每月';
        secoundText = '1日';
        payMethod = '0';
        if(agreementsFlag == '0'){
            $(_pageId + " .agreement2 i").removeClass("active");
        }else {
            agreementsFlag = '0';
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .grid_03").hide();
        $(_pageId + " .chooseJjb").removeClass('borderActive');
        $(_pageId + " .chooseBank").hide();
        $(_pageId + " .chooseBank").removeClass('borderActive');
        $(_pageId + " .chooseJjb .img").removeClass('active');
        $(_pageId + " .chooseBank .img").removeClass('active');
        $(_pageId + " #verificationCode").val("");
        monkeywords.destroy();
        sms_mobile.destroy();
        prod_list = [];
        $(_pageId + " #amount_enough").show();
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .password_box").hide();
        // $(_pageId + " .agreement2 i").removeClass("active");
        $(_pageId + ' .van-overlay').hide()
        $(_pageId + " #more_select").hide();
        $(_pageId + " #payMethod").hide();
        $(_pageId + " #cycleModel").hide();
        $(_pageId + " .jjs_yue").hide();
        investcycle = '2'
        investdate = '1'
        $(_pageId + ' .nextTimes').text('')
        initialization();
        clearTime();
    }

    function pageBack() {
        $(_pageId + " .grid_03").hide();
        appUtils.pageBack();
    }
    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
