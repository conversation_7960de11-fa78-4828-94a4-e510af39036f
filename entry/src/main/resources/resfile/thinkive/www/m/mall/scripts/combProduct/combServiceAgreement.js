define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#template_templateBuy ";
    var ut = require("../common/userUtil");
    var _pageCode = "combProduct/combServiceAgreement";
    var _pageId = "#combProduct_combServiceAgreement"
    var tools = require("../common/tools");
    var userInfo;
    var global = require("gconfig").global;
    // let resultDeta
    function init() {
        userInfo = ut.getUserInf();
        //页面埋点初始化
        tools.initPagePointData();
        // 获取协议信息
        var agreements = appUtils.getSStorageInfo("agreements")
        let html = '';
        if (agreements && agreements.length) {
            agreements.forEach(item => {
                html += `<li href="javascript:void(0);" class="flex xy" url="${global.oss_url + item.url}"> <span>《${item.agreement_title}》</span><i></i></li>`;
            })
        }
        $(_pageId + " .agreement_list").html(html);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //返回页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //查看PDF文件
        appUtils.preBindEvent($(_pageId + " .prod_agreement_list"), ".xy", function (e) {
            e.preventDefault();
            e.stopPropagation();
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            tools.recordEventData('1','xy',title);
            param["funcNo"] = "50240";
            param["url"] = url;
            param["title"] = title;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        }, "click");
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }
    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
