/*创建时间hSea 2016-05-26 11:04:51 AM */
define(function(require,exports,module){function a(a,b){if("[object String]"===Object.prototype.toString.call(a))if(0!==a.indexOf("http://")&&0!==a.indexOf("https://")&&0!==a.indexOf("file://")&&0!==a.indexOf("/")&&(a=k.projPath+a),a+="?v="+window._sysVersion,0===$("head>link[href*='"+a+"']").length){var c=document.createElement("link");c.charset="utf-8",c.rel="stylesheet",c.href=a,document.querySelector("head").appendChild(c);var d="onload"in c,e=+navigator.userAgent.replace(/.*(?:AppleWebKit|AndroidWebKit)\/?(\d+).*/i,"$1")<536||+navigator.userAgent.replace(/.*(?:AppleWebKit|AndroidWebKit)\/?(\d+).*/i,"$1")+""=="NaN";if(!e&&d)c.onerror=c.onload=b;else var f=setInterval(function(){(e&&c.sheet||c.sheet&&c.sheet.cssRules)&&(clearInterval(f),b&&b())},16.7)}else b&&b();else b&&b()}function b(a,b){var c=document.createElement(a);return c.style.cssText=b,c}function c(a){a.bgColor=a.bgColor||"black",a.color=a.color||"lightgreen",a.css=a.css||"";var c=b("div","font-family:Helvetica,Arial,sans-serif;font-size:10px;font-weight:bold;padding:5px;text-align:left;opacity:0.8;position:fixed;right:0;top:0;min-width:200px;max-height:50vh;overflow:auto;z-index:1000000;background:"+a.bgColor+";color:"+a.color+";"+a.css);return c}function d(){var a=b("div","line-height:18px;background:"+(m.children.length%2?"rgba(255,255,255,0.1)":"")),c=[].slice.call(arguments).reduce(function(a,b){return a+" "+b},"");a.textContent=c,m.appendChild(a),m.scrollTop=m.scrollHeight-m.clientHeight,m.style.display="block"}function e(){m.innerHTML=""}function f(a){n||(n=!0,a=a||{},m=c(a),m.addEventListener("tap",function(){m.style.display="none"}),document.body.appendChild(m),a.freeConsole||(o.log=console.log,o.clear=console.clear,console.log=j(d,"log"),console.clear=j(e,"clear")))}function g(){n=!1,console.log=o.log,console.clear=o.clear,m.remove()}function h(){if(!n)throw"You need to call `screenLog.init()` first."}function i(a){return function(){return h(),a.apply(this,arguments)}}function j(a,b){return function(){a.apply(this,arguments),"function"==typeof o[b]&&o[b].apply(console,arguments)}}var k=require("gconfig"),l=seajs.resolve("screenlog");l=l.substring(0,l.indexOf("scripts/screenlog")),l+="css/zzsc.css",a(l);var m,n=!1,o={};1==k.isDebug&&f(),module.exports={init:f,log:j(i(d),"log"),clear:j(i(e),"clear"),destroy:i(g)}});
/*创建时间 2016-05-26 11:04:51 AM */