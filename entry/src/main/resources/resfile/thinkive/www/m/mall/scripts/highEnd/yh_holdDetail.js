// 源晖持有详情页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "highEnd/yh_holdDetail",
        _pageId = "#highEnd_yh_holdDetail ";
        // let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
        //     if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
    var ut = require("../common/userUtil");
    var highEndHoldDetail;
    var sold_state;
    var buy_stateObj = {
        "1": {
            "txt": "买入",
            "class": "",
        },
        "2": {
            "txt": "预约",
            "class": "",
        },
        "3": {
            "txt": "买入",
            "class": "no_active",
        },
        "4": {
            "txt": "买入",
            "class": "no_active",
        },
    }

    async function init() {
        highEndHoldDetail = appUtils.getSStorageInfo("productInfo");
        //空数据处理
        highEndHoldDetail = tools.FormatNull(highEndHoldDetail);
        let res = await getDetails();//获取详情接口
        $(_pageId + " .fund_name").text(res.fund_sname);
        $(_pageId + " .fund_vol").text(tools.fmoney(res.sum_fund_amt));//资产 展示单笔资产
        $(_pageId + " .total_income").text(tools.fmoney(res.accumulated_income)).addClass(tools.addMinusClass(res.accumulated_income));//累计收益
        $(_pageId + " .yest_income").text(tools.fmoney(res.last_income)).addClass(tools.addMinusClass(res.last_income));//昨日收益
        $(_pageId + " .hold_income").text(tools.fmoney(res.hold_income)).addClass(tools.addMinusClass(res.hold_income));//持仓收益
        $(_pageId + " .sum_hold_vol").text(tools.fmoney(res.hold_vol));//持有总份额
        if (res.fund_in_way_vol > 0) {
            $(_pageId + " #fund_way_vol_box").show();
            $(_pageId + " #fund_way_vol").text(tools.fmoney(res.fund_in_way_vol));//买入在途
        } else {
            $(_pageId + " #fund_way_vol_box").hide();
        }
        //卖出在途
        if (res.fund_out_way_vol > 0) {
            $(_pageId + " #fund_out_way_vol_box").show();
            $(_pageId + " #fund_out_way_vol").text(tools.fmoney(res.fund_out_way_vol));//卖出在途
        } else {
            $(_pageId + " #fund_out_way_vol_box").hide();
        }
        var defdividend_method = res.defdividend_method;
        var dividend_status = res.dividend_status;
        if (dividend_status == "01") { //受理成功
            $(_pageId + " .dividend_method").text(fhWay(defdividend_method) + "(修改中)");
        } else {
            $(_pageId + " .dividend_method").text(fhWay(defdividend_method));
        }
        highEndHoldDetail = $.extend(highEndHoldDetail, res);
        appUtils.setSStorageInfo("productInfo", highEndHoldDetail)
        reqFun102049();//获取购买状态
    }

    /**
     * 分红方式
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function fhWay(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "1":
                dictName = "红利再投";
                break;
            case "2":
                dictName = "分红到晋金宝";
                break;
            case "3":
                dictName = "分红到晋金宝";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //修改分红方式
        appUtils.bindEvent($(_pageId + " #fh"), function () {
            var bonus_if_alter = highEndHoldDetail.bonus_if_alter;
            var dividend_status = highEndHoldDetail.dividend_status;
            //0 不可更改  1可能改
            if (bonus_if_alter == "0") {
                layerUtils.iAlert("该产品不能修改分红方式");
                return;
            }
            if (dividend_status == "01") {
                layerUtils.iAlert("分红方式确认前将不能再次修改");
                return;
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/modifyDividendsWay");
            });
        });

        //产品详情
        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            appUtils.pageInit(_pageCode, "yuanhui/productDetail");
        });
        //持有详情
        appUtils.bindEvent($(_pageId + " .showListFlag"), function () {
            if($(_pageId + " #flag").attr("class") == "bottom_icon") {
                $(_pageId + " #flag").attr({class: "top_icon"});
                $(_pageId + " .holeDetail_list").slideDown(200);
            } else {
                $(_pageId + " #flag").attr({class: "bottom_icon"});
                $(_pageId + " .holeDetail_list").slideUp(200);
            }

        });



        //卖出
        appUtils.bindEvent($(_pageId + " #sell"), function () {
            if ($(this).hasClass("no_active")) return;
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageCode)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            if (highEndHoldDetail.available_vol && highEndHoldDetail.available_vol == 0) {
                layerUtils.iConfirm("已提交赎回申请，请勿重复卖出", function () {
                }, function () {
                    appUtils.setSStorageInfo("series_id",'');
                    appUtils.pageInit(_pageCode, "template/transaction");
                }, "确定", "查看交易记录");
                return;
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "yuanhui/sale");
            });
        });
        //买入
        appUtils.bindEvent($(_pageId + " #buy"), function () {
            if ($(this).hasClass("no_active")) return;
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageCode)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            common.changeCardInter(function () {
                // appUtils.pageInit(_pageCode, "yuanhui/purchase");
                appUtils.pageInit(_pageCode, "highEnd/purchase");

            });
        });

    }
    async function getDetails(){
        return new Promise(async(resolve) => {
            let data = {
                acct_no:ut.getUserInf().fncTransAcctNo,
                fund_code:highEndHoldDetail.fund_code,
                if_period:highEndHoldDetail.if_period,
                vir_fundcode:highEndHoldDetail.vir_fundcode,
                // due_date:highEndHoldDetail.due_date
            }
            if(highEndHoldDetail.due_date && highEndHoldDetail.due_date != '--') data.due_date = highEndHoldDetail.due_date
            service.reqFun101935(data,async(data)=> {
                if(data.error_no == '0'){
                    resolve(data.results[0])
                }else{
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    //查询产品购买状态
    function reqFun102049() {
        service.reqFun102049({fund_code: highEndHoldDetail.fund_code}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //购买状态
            var buy_state = results.buy_state;
            //卖出状态 0:不可卖出 1:可卖出
            sold_state = results.sold_state;

            $(_pageId + " #buy").html(buy_stateObj[buy_state].txt).addClass(buy_stateObj[buy_state].class);

            if (sold_state == "0") {
                $(_pageId + " #sell").addClass("no_active");
            } else if (sold_state == "1") {
                $(_pageId + " #sell").removeClass("no_active");
            }
            if (highEndHoldDetail.cust_fund_type == "1") {
                $(_pageId + " #sell").addClass("no_active");
            }
        });
    }

    function destroy() {
        $(_pageId + " .data-line").html("--");
        $(_pageId + " .empty").html("");
        $(_pageId + " .total_income").text("--").removeClass("text_red").removeClass("text_green").removeClass("text_gray");//收益
        $(_pageId + " .yest_income").text("--").removeClass("text_red").removeClass("text_green").removeClass("text_gray");//收益
        $(_pageId + " .hold_income").text("--").removeClass("text_red").removeClass("text_green").removeClass("text_gray");//收益
        $(_pageId + " #buy").removeClass("no_active");
        sold_state = "";
        $(_pageId + " #flag").attr({class: "bottom_icon"});
        $(_pageId + " .holeDetail_list").hide();
        $(_pageId + " .holeDetail_list").html("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.clearSStorage("highEndHoldDetail");
        appUtils.pageBack();
    }

    var highEndDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highEndDetail;
});
