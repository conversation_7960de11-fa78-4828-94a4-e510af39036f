// 银行开户成功
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#bank_openAccountSuccess",
        _pageUrl = "bank/openAccountSuccess";
    var results;

    function init() {
        appUtils.clearSStorage("bankOpenImg");
        results = appUtils.getPageParam();
        $(_pageId + " .bankElectronInfo").html("您的" + results.bank_name + "卡号为：" + results.acct);
    }

    //绑定事件
    function bindPageEvent() {
        //完成
        appUtils.bindEvent($(_pageId + " #wancheng"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-3);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit(_pageUrl, "bank/purchase", {});
        });
    }

    function destroy() {
    }

    var setBankCardSuccess = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = setBankCardSuccess;
});
