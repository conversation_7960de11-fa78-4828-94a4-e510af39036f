// 晋金超市列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "fundMarket/fundList",
        _pageId = "#fundMarket_fundList ";
    var currentPage = 1;
    var totalPages;
    var numPerPage = 10;
    var validatorUtil = require("validatorUtil");
    var ut = require("../common/userUtil");
    var _prod_dep_day = "";
    var buy_state_obj = {
        "1": {
            text: "购买",
            class: ""
        },
        "2": {
            text: "预约",
            class: ""
        },
        "3": {
            text: "敬请期待",
            class: ""
        },
        "4": {
            text: "封闭中",
            class: "sold_out"
        },
        "5": {
            text: "售罄",
            class: "sold_out"
        },
        "6": {
            text: "购买",
            class: ""
        }
    }

    function init() {
        //首页进来获取产品列表
        getProductList(false);
        $(_pageId + " .tabbar").removeClass("active").eq(0).addClass("active");
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //购买
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".pro_detail", function () {
            var self = $(this);
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            appUtils.setSStorageInfo("productInfo", productInfo);

            if (productInfo.prod_sub_type == "10" && productInfo.fund_code == "000709") { //宝宝类
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    }
                    appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
                })
            } else {
                tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
            }
        }, 'click');

        //导航栏
        appUtils.bindEvent($(_pageId + " .tabbar"), function (e) {
            var contentId = $(this).attr("content");
            $(_pageId + " .new_none").hide();
            $(_pageId + " .tabbar").removeClass("active").filter(this).addClass("active");
            if (contentId == "bondContent") {
                $(_pageId + " .qryDate").show();
            } else {
                $(_pageId + " .qryDate").hide();
            }
            if ($(this).index() == "0") {
                currentPage = 1;
                $(_pageId + " #allContent").html("");
                getProductList(false);
            } else if ($(this).index() == "1") {
                currentPage = 1;
                $(_pageId + " #allContent").html("");
                getCurrencyList(false);
            } else {
                $(_pageId + " #allContent").html("");
            }
            pageScrollInit();
        });

        // 投资期限遮罩层
        appUtils.bindEvent($(_pageId + " .qryDate"), function (e) {
            e.preventDefault();
            e.stopPropagation();
            $(_pageId + " .qryDate").hide();
            $(_pageId + " #allContent").html("");
            currentPage = 1;
            //债券基金产品列表
            getBondList(false);
        });
        // 投资期限选择
        appUtils.bindEvent($(_pageId + " .qryDate div"), function (e) {
            e.preventDefault();
            e.stopPropagation();
            $(_pageId + " .qryDate").hide();
            $(_pageId + " .qryDate div").removeClass("active").filter(this).addClass("active");
            _prod_dep_day = $(this).attr("prod_dep_day");
            $(_pageId + " #allContent").html("");
            currentPage = 1;
            getBondList(false, _prod_dep_day);
        });

    }


    //全部产品列表
    function getProductList(isAppendFlag) {
        var params = {
            prod_type: "01",
            current: currentPage + "",
            count: numPerPage + "",
        }
        service.reqFun102001(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    hidePullUp();
                    return;
                }
                if (!results[0].data || results[0].data.length == 0) {
                    hidePullUp();
                    return;
                }
                totalPages = results[0].totalPages;
                //获取晋金普惠列表
                getInclusiveList(results[0].data, isAppendFlag);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取晋金普惠列表
    function getInclusiveList(data, isAppendFlag) {
        var html = "";
        for (var i = 0; i < data.length; i++) {
            //空数据处理
            data[i] = tools.FormatNull(data[i]);

            if (data[i].prod_sub_type == "10") { //货基
                html += renderCurrency(data[i]);
            } else if (data[i].prod_sub_type == "20") { //大集合
                html += renderGather(data[i]);
            } else if (data[i].prod_sub_type == "30") { //30天
                html += renderThirty(data[i]);
            } else if (data[i].prod_sub_type == "50" || (data.prod_sub_type == "--")) { //债基
                html += renderBond(data[i]);
            }
        }
        if (isAppendFlag) {
            $(_pageId + " .finance_pro #allContent").append(html);
        } else {
            $(_pageId + " .finance_pro #allContent").html(html);
        }
        hidePullUp();
    }

    //货币基金产品列表
    function getCurrencyList(isAppendFlag) {
        var params = {
            prod_type: "01",
            prod_type2: "40",
            current: currentPage + "",
            count: numPerPage + "",
        }
        service.reqFun102001(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    hidePullUp();
                    return;
                }

                if (!results.data || results.data.length == 0) {
                    hidePullUp();
                    return;
                }
                totalPages = results.totalPages;
                //获取货币基金列表
                var html = "";
                for (var i = 0; i < results.data.length; i++) {
                    html += renderCurrency(results.data[i]);
                }
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro #allContent").append(html);
                } else {
                    $(_pageId + " .finance_pro #allContent").html(html);
                }
                hidePullUp();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //债券基金产品列表
    function getBondList(isAppendFlag, prod_dep_day) {
        var params = {
            prod_type: "01",
            prod_type2: "30",
            current: currentPage + "",
            count: numPerPage + "",
        }
        //term_type  0:一个月内    1:1-6个月 	6:6个月以上
        if (prod_dep_day) {
            params.term_type = prod_dep_day;
        }
        service.reqFun102001(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    if(!isAppendFlag) {
                        $(_pageId + " .new_none").text("暂无数据").show();
                    }
                    hidePullUp();
                    return;
                }

                if (!results.data || results.data.length == 0) {
                    if(!isAppendFlag) {
                        $(_pageId + " .new_none").text("暂无数据").show();
                    }
                    hidePullUp();
                    return;
                }
                totalPages = results.totalPages;
                //获取货币基金列表
                var html = "";
                for (var i = 0; i < results.data.length; i++) {
                    if (results.data[i].prod_sub_type == "50") html += renderBond(results.data[i]);
                    if (results.data[i].prod_sub_type == "30") html += renderThirty(results.data[i]);
                    if (results.data[i].prod_sub_type == "20") html += renderGather(results.data[i]);
                }
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro #allContent").append(html);
                } else {
                    $(_pageId + " .finance_pro #allContent").html(html);
                }
                hidePullUp();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //渲染债基
    function renderBond(data) {
        var html = "";
        data = tools.FormatNull(data);
        if (data.prod_sub_type2 == "01") { //定开
            //空数据处理
            var dk_income_rate = data.dk_income_rate;
            var per_yield = data.per_yield;
            html += "<div class='pro_detail'>" +
                "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
                "<div class='box'>" +
                "<h4>" + data.prod_sname + "</h4>" +
                "<p>上一封闭期年化收益率：：<span class= 'redcol " + tools.addMinusClass(per_yield) + "'>" + tools.fmoney(per_yield) + "</span>%</p>" +
                "<p><span style='width: 1.1rem;display: inline-block'>起购：" + tools.fmoney(data.threshold_amount) + "元</span>封闭期：" + data.maturity_days + "</p>" +
                "<a  href='javascript:void(0)' class='buy_btn pop in " + buy_state_obj[data.buy_state].class + "'>" + buy_state_obj[data.buy_state].text + "</a>" +
                "</div>" +
                "</div>";
        } else if(data.prod_sub_type2 == "52") { //定开
            //空数据处理
            var dk_income_rate = data.dk_income_rate;
            html += "<div class='pro_detail'>" +
                "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
                "<div class='box'>" +
                "<h4>" + data.prod_sname + "</h4>" +
                "<p>" + data.income_period_type_desc + "年化：<span class= 'redcol " + tools.addMinusClass(dk_income_rate) + "'>" + tools.fmoney(dk_income_rate) + "</span>%</p>" +
                "<p><span style='width: 1.1rem;display: inline-block'>起购：" + tools.fmoney(data.threshold_amount) + "元</span>锁定期：" + data.maturity_days + "</p>" +
                "<a  href='javascript:void(0)' class='buy_btn pop in " + buy_state_obj[data.buy_state].class + "'>" + buy_state_obj[data.buy_state].text + "</a>" +
                "</div>" +
                "</div>";
        }else {
            //空数据处理
            data = tools.FormatNull(data);
            html += "<div class='pro_detail'>" +
                "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
                "<div class='box'>" +
                "<h4>" + data.prod_sname + "</h4>" +
                '<p><span style="display: inline-block;width: 1.1rem">单位净值：' + tools.fmoney(data.nav) + '</span>' +
                '         年涨幅：<span style="margin-right: 0.4rem">' + tools.fmoney(data.year_rate) + '%</span>' +
                '      </p>' +
                "<a  href='javascript:void(0)' class='buy_btn pop in " + buy_state_obj[data.buy_state].class + "'>" + buy_state_obj[data.buy_state].text + "</a>" +
                "</div>" +
                "</div>"
        }
        return html;
    }

    //渲染货基
    function renderCurrency(data) {
        var html = "";
        //空数据处理
        data = tools.FormatNull(data);
        var annu_yield = data.annu_yield;
        if (annu_yield != "--") {
            annu_yield = (+annu_yield).toFixed(2);
        }
        html += "<div class='pro_detail'>" +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
            "<div class='box'>" +
            "<h4>" + data.prod_sname + "</h4>" +
            "<p>七日年化：<span class= 'redcol'>" + annu_yield + "</span> %</p>" +
            "<a  href='javascript:void(0)' class='buy_btn pop in " + buy_state_obj[data.buy_state].class + "'>" + buy_state_obj[data.buy_state].text + "</a>" +
            "</div>" +
            "</div>";
        return html;
    }

    //渲染30天
    function renderThirty(data) {
        var html = "";
        //空数据处理
        data = tools.FormatNull(data);
        var p_expected_yield = data.p_expected_yield;
        html += "<div class='pro_detail'>" +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
            "<div class='box'>" +
            "<h4>" + "晋金宝" + require("gconfig").global.holding_days + "天" + "</h4>" +
            "<p>近一个月年化：<span class= 'redcol " + tools.addMinusClass(p_expected_yield) + "'>" + tools.fmoney(p_expected_yield) + "</span>%</p>" +
            "<p><span style='width: 1.1rem;display: inline-block'>起购：" + tools.fmoney(data.threshold_amount) + "元</span>建议持有30天以上</p>" +
            "<a  href='javascript:void(0)' class='buy_btn pop in " + buy_state_obj[data.buy_state].class + "'>" + buy_state_obj[data.buy_state].text + "</a>" +
            "</div>" +
            "</div>";
        return html;
    }

    //渲染大集合
    function renderGather(data) {
        var html = "";
        var preincomerate = data.preincomerate;
        if (preincomerate != "--") {
            preincomerate = (+preincomerate).toFixed(2);
        }
        html += "<div class='pro_detail'>" +
            "<span style='display: none' class='productInfo'>" + JSON.stringify(data) + "</span>" +
            "<div class='box'>" +
            "<h4>" + data.prod_sname + "</h4>" +
            "<p>年化业绩基准：<span class= 'redcol " + tools.addMinusClass(preincomerate) + "'>" + tools.fmoney(preincomerate) + "</span>%</p>" +
            "<p><span style='width: 1.1rem;display: inline-block'>起购：" + tools.fmoney(data.threshold_amount) + "元</span>期限：" + data.maturity_days + "</p>" +
            "<a  href='javascript:void(0)' class='buy_btn pop in " + buy_state_obj[data.buy_state].class + "'>" + buy_state_obj[data.buy_state].text + "</a>" +
            "</div>" +
            "</div>";

        return html
    }

    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    currentPage = 1;
                    $(_pageId + " .visc_pullUp").hide();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                    var content = $(_pageId + " .tab .active").attr("content");
                    //债券基金
                    if (content == "bondContent") {
                        loadData(content, false, _prod_dep_day);
                    } else {
                        loadData(content, false);
                    }
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        currentPage++;
                        $(_pageId + " .visc_pullUp").show();
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        var content = $(_pageId + " .tab .active").attr("content");
                        //债券基金
                        if (content == "bondContent") {
                            loadData(content, true, _prod_dep_day);
                        } else {
                            loadData(content, true);
                        }
                    } else {
                        $(_pageId + " .new_none").text("没有更多数据").show();
                    }

                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }

    function hidePullUp() {
        $(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }

    function loadData(str, isAppendFlag, _prod_dep_day) {
        $(_pageId + " .new_none").hide();
        var obj = {
            "allContent": getProductList,
            "currencyContent": getCurrencyList,
            "bondContent": getBondList
        }
        if (_prod_dep_day) {
            obj[str](isAppendFlag, _prod_dep_day);
        } else {
            obj[str](isAppendFlag);
        }

    }

    function destroy() {
        currentPage = 1;
        totalPages = '';
        _prod_dep_day = "";
        $(_pageId + " .tabbar").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .qryDate").hide();
        $(_pageId + " .qryDate div").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .finance_pro #allContent").html("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thfundList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thfundList;
});
