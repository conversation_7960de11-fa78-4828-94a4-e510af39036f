/**
 * 模块名：晋金财富抽奖外链页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        common = require("common"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil");
    var ut = require("../common/userUtil");
    var global = gconfig.global;
    require("../../js/draw.js");
    require("../common/html2canvas.min");
    /* 常量 */
    var _pageCode = "activity/fridayShareImg", _pageId = "#activity_fridayShareImg";
    /* 变量  活动信息*/
    var state;
    var fridayActivityInfo;
    var activityRule; // 活动规则
    var pageTouchTimer = null;
    /**
     * 初始化
     */
    function init() {
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        // activity_id = appUtils.getPageParam("activity_id");
        activity_id = appUtils.getSStorageInfo("friday_data").activity_id;
        // console.log(appUtils.getSStorageInfo("friday_data"));
        reqFun108033();//查询活动信息    

    }

    function reqFun108033() {
        service.reqFun108033({ activity_id: activity_id }, (data) => {
            // console.log(data);
            if (data.error_no == 0) {
                var results = data.results[0];
                fridayActivityInfo = results;
                $(_pageId + " .rule").html(fridayActivityInfo.introduce);
                if (fridayActivityInfo.upload_function_state == "1") {
                    $(_pageId + " #upload_btn").show();
                    $(_pageId + " #view_btn").show();
                } else if (fridayActivityInfo.upload_function_state == "0") {
                    $(_pageId + " #upload_btn").hide();
                    $(_pageId + " #view_btn").hide();
                }
                let requestData = {
                    img_url: global.oss_url + fridayActivityInfo.img_url
                }
                let base64Str;
                service.reqFun102119(requestData, function (d) {
                    if (d.error_no == "0") {
                        base64Str = 'data:image/jpeg;base64,' + d.results[0].base64Str
                        $(_pageId + " #acticity_content_img").attr("src", base64Str)
                    } else {
                        layerUtils.iAlert(d.error_info);
                    }
                })

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        // 分享到朋友圈
        appUtils.bindEvent($(_pageId + " #shared_btn"), () => {
            if (!ut.hasBindCard(_pageCode)) return;
            if (fridayActivityInfo.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if (fridayActivityInfo.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_fridayShareImg");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " .activity-content img")
            html2canvas(dom, {
                scale: 4,
                height: dom.offsetHeight, //注意 下面解决当页面滚动之后生成图片出现白边问题
                width: dom.offsetWidth,  //注意 下面解决当页面滚动之后生成图片出现白边问题
            }).then(canvas => {
                var base64 = canvas.toDataURL("image/png");
                var _base64 = base64.split(",")[1];
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                service.reqFun108034({ cust_no: cust_no, activity_id: activity_id }, (data) => {
                    if (data.error_no == "0") {
                        common.share("23", sessionStorage.share_template ? sessionStorage.share_template : "1", "", true, _base64);
                        reqFun108033();
                        var results = data.results[0];
                        if (results) {
                            pageTouchTimer = setTimeout(() => {
                                // current_state 0 未发放 1 已发放 2 稍后发放
                                if (results.rewardStatus == '1' && results.pointsAmount) {
                                    $(_pageId + " #pointsResult .single_layer_desc").html(results.pointsAmount + "积分已发放至您的账户");
                                    $(_pageId + " #pointsResult").show();
                                }
                                if (results.rewardStatus == '2') {
                                    $(_pageId + " #pointsResult .single_layer_desc").html("奖励随后发放");
                                    $(_pageId + " #pointsResult").show();
                                }
                            }, 4000);
                        }
                    
                       
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                })
            })
        });
        // 上传集赞截图 
        appUtils.bindEvent($(_pageId + " #upload_btn"), () => {
            if (!ut.hasBindCard(_pageCode)) return;
            if (fridayActivityInfo.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if (fridayActivityInfo.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            if (fridayActivityInfo.share_flag == "0") {
                layerUtils.iAlert("请先参与分享活动");
                return;
            }
            var param = {};
            param["funcNo"] = "50273";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#111111";
            }
            param.paramExt = {
                multi: false,
                type: "friday_activiy", // 周五活动类型
                activity_id: activity_id,
                cust_no: cust_no
            }
            param["cutFlag"] = "0";
            param["compress"] = "0.8";
            param["width"] = "1600";
            param["height"] = "900";
            // let external = require("external");
            tools.fileImg(_pageId, param)
        });
        // 查看上传记录 
        appUtils.bindEvent($(_pageId + " #view_btn"), () => {
            if (!ut.hasBindCard(_pageCode)) return;
            if (fridayActivityInfo.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            service.reqFun108035({ activity_id: activity_id, cust_no: cust_no }, (data) => {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    if (!results) {
                        layerUtils.iAlert("请先上传集赞截图");
                        return;
                    }
                    var privateurl = results && results.img_url;
                    service.reqFun102093({ privateUrl: privateurl }, function (d) {
                        if (d.error_no != 0) {
                            layerUtils.iAlert(d.error_info);
                            return
                        }
                        var publicUrl = d.results[0].urlPublic;
                        $(_pageId + " #my_img").attr("src", publicUrl)
                        $(_pageId + " #screen_img").show();
                    });
                }
            })
        });

        // 关闭我的集赞截图
        appUtils.bindEvent($(_pageId + " #close_pop"), () => {
            $(_pageId + " #screen_img").hide();
        })
        // 知道了
        appUtils.bindEvent($(_pageId + " #successBtn"), function () {
            $(_pageId + " #pointsResult").hide();
        });

    }
    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " #screen_img").hide();
        $(_pageId + " #pointsResult .single_layer_desc").html("");
        $(_pageId + " #pointsResult").hide();
        clearTimeout(pageTouchTimer);
    };
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
