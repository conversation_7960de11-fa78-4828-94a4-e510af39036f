define(function(require,exports,module){
	var listenerF="";
	(function($){
		$.fn.pluginName = function(options){
			
			var opts = $.extend({},$.fn.pluginName.defaults,options);
			var ele = $(opts.eleId).get(0);
			listenerF = function(){ excuteF(opts.eleId); }
			if(ele.addEventListener){				
				ele.addEventListener("input",listenerF, false);
			}
			
		};
		$.fn.pluginName.defaults = {
			"eleId":""
		};				
		
		var origLen = 1;
		var newLen = 0;
		var origComma = 0;
		var newComma = 0;
		function excuteF(text){
			
			var str= $(text).val();            //str=123
            str = str.replace(/,/g,"");
  
            str = str.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
            str = str.replace(/^\./g,"");  //验证第一个字符是数字而不是
            str = str.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
            str = str.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
            
            
            origLen = newLen;       
            newLen = str.length;
                        
            var str1 ="";
            var str2 = "";
            if(str.split(".").length>1){
            	str1 = str.split(".")[0];
                str2 = "." + str.split(".")[1];
            }else{
            	str1 = str;
            }
            
            var newStr=new Array(str1.length+parseInt(str1.length/3));   //newStr[4]
    
            var strArray=str1.split("");                              //strArray[1,2,3]
            newStr[newStr.length-1]=strArray[strArray.length-1];       //newStr[3]=3
            var currentIndex=strArray.length-1;             //currentIndex=2
            for(var i=newStr.length-1;i>=0;i--){          //i=3;i>=0;i--
	              if((newStr.length-i)%4==0){
	            	  newStr[i]= i==0 ? "":",";
	              }else{
	            	  newStr[i]=strArray[currentIndex--];  
	              }
            }
            var val1 = newStr.join("") + str2;
            
            origComma = newComma;
            var strResut = val1.match(/\,/g); 
            newComma = strResut==null || strResut==undefined ? 0 : strResut.length;           
                      
            
            var o = $(text).get(0);
            var cusor_start = o.selectionStart;
            if(newComma > origComma ){
            	cusor_start = cusor_start+1;
            }
            if( origLen == newLen ){
            	cusor_start = cusor_start-1;
            }

			var slength = 0,
			 p = val1.indexOf('.');
			if (p >= 0) {
				var s_ = val1.split("."),
				   slength_ = s_[1].length;
				if(slength_>2){
					slength = 2;
				}else{
					slength = slength_;
				}
				val1 = val1.substring(0, p+slength+1);
			}
		
            $(text).val(val1);                   
            
            
            if(o.setSelectionRange){ //W3C
            	o.setSelectionRange(cusor_start,cusor_start);
		        o.focus();
            }
            else if(o.createTextRange){  //IE
            	var textRange=o.createTextRange();
			        textRange.moveStart("character",cusor_start);
			        textRange.moveEnd("character",0);
			        textRange.select();
            }
		}
	})(jQuery);
	

	function usePlug(eleObj)
	{
		$.fn.pluginName(eleObj);
	}
	
	function deslistener(eleObj)
	{
		var obj = $(eleObj.eleId).get(0);
		obj.removeEventListener("input", listenerF, false);
	}
	
	var common = {
			"usePlug":usePlug,
			"deslistener":deslistener
	};
	module.exports = common;
});