//高端：修改分红方式结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageUrl = "highEnd/modifyDividendsWayResult",
        _pageId = "#highEnd_modifyDividendsWayResult ";
    function init() {
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + ".done_btn"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageBack();
        })
       
    }

    function destroy() {
        
    }

    

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
