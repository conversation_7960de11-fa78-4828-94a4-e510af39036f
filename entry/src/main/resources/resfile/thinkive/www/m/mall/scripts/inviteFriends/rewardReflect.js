 // 提现至晋金宝
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		putils = require("putils"),
		service = require("mobileService"),
		commTool= require("mall/scripts/common/commTool"),
		_pageId = "#inviteFriends_rewardReflect ";
	var gconfig = require("gconfig");
	var global = gconfig.global;
	var common = require("common");
	var kyje = "";
	var jymm="";
//	var timer = null;//计时器
//    var i = 120;//倒计时长
 //   var Millisecond;

	function init(){
		//验证码初始化
//
//		var $yzm =  $(_pageId + " #getYzm");
//		$yzm.attr("data-state", "true");
//		$yzm.removeAttr("class");
//		$yzm.html("获取验证码");
		//i = 120;
		//window.clearInterval(timer);
		common.systemKeybord(); // 解禁系统键盘
		var custNo = appUtils.getSStorageInfo("custNo");
		var card_no = appUtils.getSStorageInfo("cardNo");
		var param = {
				cust_no:custNo,
				card_no:card_no
		};
		//查询出我赚的奖励
		getYaoQinInfo(param);
		//获取协议
		getXY();
		//键盘初始化
		window.customKeyboardEvent = {
				keyBoardFinishFunction: function(){
					quedingoumai(jymm);
				}, // 键盘完成按钮的事件
				keyBoardInputFunction: function(){
				jymm=$(_pageId+" #jymm").val();
				    var len = jymm.length;
				    if(len<=6){
				    	for(var i=0;i<6;i++){
				    		var idspan="#span0"+i
				    		if(i<len){
					    		$(_pageId+" "+idspan).attr("class","point");
					    		continue;
				    		}
				    		$(_pageId+" "+idspan).removeAttr("class","point");
				    	}
				    }else {
				    	jymm = jymm.substring(0,6);
					    $(_pageId+" #jymm").val(jymm);
				    }
				} // 键盘的输入事件
				};
	}

	//绑定事件
	function bindPageEvent(){

		//隐藏后呼出交易密码
		appUtils.bindEvent($(_pageId+" .password_input"),function(){
			var param = {};
		    param["moduleName"] = "mall";
	        param["funcNo"] = "50210";
	        param["pageId"] = "inviteFriends_rewardReflect";
	        param["eleId"] = "jymm";
	        param["doneLable"] = "确定";
	        param["keyboardType"] = "4";
	        require("external").callMessage(param);
		});

		// 验证码输入控制
		 appUtils.bindEvent($(_pageId+" #txje"),function(){
			var curVal = this.value;
			if(!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal)))
			{
				$(_pageId + " #txje").val(curVal.substring(0,curVal.length-1));
				return;
			}
        }, "input");

		// 验证码输入控制
//		appUtils.bindEvent($(_pageId+" #yzm"),function(){
//			var curVal = this.value;
//			var curPosi = putils.getInputCursorPosition(this);
//			var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
//			if(!/\d/.test(curInputVal))
//			{
//				this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
//				putils.setInputCursorPosition(this, curPosi - 1);
//			}
//        }, "input");

		//点击返回按钮
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});

		//点击输入密码框的关闭按钮
		appUtils.bindEvent($(_pageId+" #close"),function(){
			$(_pageId + " .pop_layer").css("display","none");
			$(_pageId+" #rechargeInfo").empty();
			$(_pageId + " #jymm").val("");
			guanbi();
		});

		//查看PDF文件
		appUtils.bindEvent($(_pageId+" #xy"), function() {
			var url = $(this).attr("url");
			let title = '内容查看'
            let statusColor = '#2F4D80'
			let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
			param["statusColor"] = statusColor;
			param["titleColor"] = titleColor;
            require("external").callMessage(param);
		});

		//点击协议书前的checkBox
		appUtils.bindEvent($(_pageId + " #isChecked"),function(){
			var chencked=$(_pageId+" #input_radio2").attr("checked");
			if(chencked=="checked"){
				$(_pageId+" #input_radio2").removeAttr("checked","checked");
				/*$(_pageId+" #input_radio2").attr("ischeck","2");*/
			}else{
				$(_pageId+" #input_radio2").attr("checked","checked");
			/*	$(_pageId+" #input_radio2").attr("ischeck","1");*/
			}
        });

		//点击下一步
		appUtils.bindEvent($(_pageId+" #submit"),function(){
			//秘密框弹出
			$(_pageId+" .pop_layer").hide();
			var qxje = $(_pageId+" #txje").val();

//			var yzmZtai = $(_pageId+" #getYzm").attr("data-state");
//			var yzmVal = $(_pageId+" #yzm").val();
//			if(yzmZtai == "true"){
//				layerUtils.iAlert("您还未获取验证码");
//				return;
//			}
//			if(yzmVal.length != 6){
//				layerUtils.iAlert("请确定您的验证码格式是否正确");
//				return;
//			}
			/*var isChecked = $(_pageId+" #input_radio2").attr("checked");
			if(isChecked){
			}else{
				layerUtils.iMsg(-1,"您还未同意协议内容");
				return;
			}*/

			if(!validatorUtil.isMoney(qxje)){
				layerUtils.iAlert("请确定您的提现金额是否正确");
				return;
			}
			qxje = parseFloat(qxje);
			kyje = parseFloat(kyje);
			if(qxje==0){
				layerUtils.iAlert("提现金额要大于0!");
				return;
			}
			if( kyje < qxje ){
				layerUtils.iAlert("提现金额超过当前可余额");
				return;
			}
			$(_pageId+" #rechargeInfo").html("您将提现<em>"+common.fmoney(qxje)+"</em>到晋金宝");
			//调用密码输入框
			$(_pageId+" .pop_layer").show();
	    	var param = {};
		    param["moduleName"] = "mall";
	        param["funcNo"] = "50210";
	        param["pageId"] = "inviteFriends_rewardReflect";
	        param["eleId"] = "jymm";
	        param["doneLable"] = "确定";
	        param["keyboardType"] = "4";
	        require("external").callMessage(param);
		});

		//点击确定
		appUtils.bindEvent($(_pageId+ " #queDing"),function(){

			$(_pageId+" #submit").html("提现中...");
			$(_pageId+" #submit").css("pointer-events","none");
			quedingoumai(jymm);
		});

		//点击发送验证吗事件
//		appUtils.bindEvent($(_pageId+" #getYzm"),function(){
//			var qxje = $(_pageId+" #txje").val();
//			if(!validatorUtil.isMoney(qxje)){
//				layerUtils.iAlert("请确定您的提现金额是否正确");
//				return;
//			}
//			qxje = parseFloat(qxje);
//			kyje = parseFloat(kyje);
//			if(qxje==0){
//				layerUtils.iAlert("提现金额要大于0!");
//				return;
//			}
//			if( kyje < qxje ){
//				layerUtils.iAlert("提现金额超过当前可余额");
//				return;
//			}
//			var sessionPhoneNum = appUtils.getSStorageInfo("mobile");
//			var $code = $(_pageId + " #getYzm");
//			if($code.attr("data-state")=="false"){
//				return ;
//			}else{
//				window.clearInterval(timer);
//				//获取验证码
//				var param = {
//						phoneNum:sessionPhoneNum,
//						pre_content:"您正在进行奖励提取操作，验证码为",
//						end_content:"，请勿泄露，如有疑问 请致电4001678888。",
//						type:"9"
//						};
//				sendPhoneCode(param)
//			}
//		});
	}


	function getYaoQinInfo(param){
		service.getYaoQinInfo(param,function(data) {
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				var results = data.results[0];
				kyje = results.total_reward;
				$(_pageId+ " #txje").attr("placeholder","当前可提现余额"+common.fmoney(results.bonus_usable_vol)+"元");
			}else{
				layerUtils.iAlert(error_info);
			}
		});
	}

	function Withdraw(param){
		service.Withdraw(param,function(data){
			var error_no = data.error_no,
				error_info = data.error_info;
			if ( error_no == "0" ) {
				getYaoQinInfo(param);
				$(_pageId+" #submit").html("提现");
				$(_pageId+" #submit").css("pointer-events","auto");
				layerUtils.iLoading(true);
				/*layerUtils.iConfirm("提现至晋金宝成功，是否进行提现？",function(){
					//验证码初始化
					var $yzm =  $(_pageId + " #getYzm");
					$yzm.attr("data-state", "true");
					$yzm.removeAttr("class");
					$yzm.html("获取验证码");
					i = 120;
					$(_pageId + " #yzm").val("");
					window.clearInterval(timer);
					layerUtils.iLoading(false);
				},function(){
					appUtils.pageInit("inviteFriends/rewardReflect","inviteFriends/recommendedEarnings");
				});*/
				var order_id =data.results[0].order_id;
				var order_state =data.results[0].order_state;
				var product_name =data.results[0].product_name;
				var tot_price =data.results[0].tot_price;
				  var para={
						  "order_id" :order_id,
						  "order_state" :order_state,
						  "product_name" :product_name,
						  "tot_price" :tot_price,
						  "Statuslabel":"rew_money"
						};
				if(order_state)
				{
				appUtils.pageInit("inviteFriends/rewardReflect","account/acceptanceState",para);
				}else{
					layerUtils.iAlert("提取奖励受理失败");
				}

			} else {
				//验证码初始化
//				var $yzm =  $(_pageId + " #getYzm");
//				$yzm.attr("data-state", "true");
//				$yzm.removeAttr("class");
//				$yzm.html("获取验证码");
//				i = 120;
//				$(_pageId + " #yzm").val("");
//				window.clearInterval(timer);
				$(_pageId+" #submit").html("提现");
				$(_pageId+" #submit").css("pointer-events","auto");
				$(_pageId+" #rechargeInfo").empty();
				$(_pageId + " #jymm").val("");
				layerUtils.iAlert(error_info);
			}
		});
	}

	//增加协议
	function getXY(){
		service.getBindCardAgreement(function(data){
			var error_no = data.error_no;
			if(error_no == 0){
				var html="";
				if(data.results.length==1)
				{
					$(_pageId+" #xy").attr("url",global.pdf+data.results[0].url);
					$(_pageId+" #xy").html("《"+data.results[0].agreement_title+"》");
				}else
				{
					for(var i=0 ; i < data.results.length; i++){
						//<a href="javascript:void(0);" id="xy"></a>
					    html+='<a href="javascript:void(0);" id="xy" url="' +global.pdf+data.results[i].url+'">';
					    html+="《"+data.results[i].agreement_title+"》";
					    html+='</a>';
					}
					$(_pageId+" .deal_box").html(html);
				}
			}else{
				layerUtils.iAlert(data.error_info);
			}
		},12);
	}

	//点击确定
	function quedingoumai(jymm){
		guanbi();
		$(_pageId+" .pop_layer").hide();
		if(jymm.length == 6){
			//进行充值
			var custNo = appUtils.getSStorageInfo("custNo");
			var user_id = appUtils.getSStorageInfo("custid");
			var card_no = appUtils.getSStorageInfo("cardNo");
			var sessionPhoneNum = appUtils.getSStorageInfo("mobile");
			var txje = $(_pageId + " #txje").val();
			//var yzmVal = $(_pageId+" #yzm").val();
			var commend_preson =  appUtils.getSStorageInfo("commendPreson");
			var param = {
					cust_no:custNo,
					card_no:card_no,
					sys_trans_pwd: jymm,
					trans_amt:txje,
					commend_preson: commend_preson,
					user_id:user_id,
					phone:sessionPhoneNum,
					//sms_code:yzmVal
					/*custNo:custNo,
					user_id:user_id,
					card_no:card_no,
					trans_amt:trans_amt,
					sys_trans_pwd:jymm*/
			};
			service.getRSAKey({},function(data){
				if(data.error_no=="0")
				{
					var modulus = data.results[0].modulus;
					var publicExponent =data.results[0].publicExponent;
					var endecryptUtils = require("endecryptUtils");
					param.sys_trans_pwd= endecryptUtils.rsaEncrypt(modulus,publicExponent,param.sys_trans_pwd);
					Withdraw(param);
				}
			});
		}else{
			$(_pageId+" #submit").html("提现");
			$(_pageId+" #submit").css("pointer-events","auto");
			layerUtils.iAlert("请确定您的交易密码格式正确");
		}
	}

	/**
	 * 发送手机验码
	 * */
//	function　sendPhoneCode(param){
//		service.sendPhoneCode(param,function(data){
//			var error_no = data.error_no,
//			error_info = data.error_info,
//			results= null;
//			results = data.results;
//
//        if (error_no == "0"){
//
//       	   isSendPhone = true;
//           timer = setInterval(function(){
//	           	shows();
//	           },1000);
//       } else {
//    	   isSendPhone = false;
//           layerUtils.iAlert(error_info);
//        }
//		});
//	}

	/**
	 * 显示读秒
	 * */
//	function shows (){
//
//		var $code = $(_pageId + " #getYzm");
//		$code.attr("data-state","false");//点击不能发送
//		var myDate = new Date();
//		var TimeDifference = myDate.getTime();
//
//		if(i==120){
//			 Millisecond = TimeDifference+120000;
//		}
//		i=(Millisecond-(Millisecond-TimeDifference)%1000-TimeDifference)/1000;
//		if(i>1){
//			$code.html("" + i + "秒后重新获取");
//			$code.addClass("sending");
//			i--;
//		}else{
//			$code.attr("data-state","true");//点击不能发送
//			window.clearInterval(timer);
//			i= 120;
//			$code.addClass("sending");
//			$code.removeAttr("class");
//			$code.html("重新获取验证码");
//		}
//	}

	//关闭键盘
    function guanbi(){
		$(_pageId+" #jymm").val("");
    	jymm="";
    	for(var i=0;i<6;i++){
    		var idspan="#span0"+i
    		$(_pageId+" "+idspan).removeAttr("class","point");
    	}
    	var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
	}

    function destroy(){
    	guanbi();
    	//$(_pageId+ " #yzm").val("");
		$(_pageId+" #submit").html("提现");
		$(_pageId+" #submit").css("pointer-events","auto");
		$(_pageId+ " #txje").val("");
	}
	function pageBack(){
		appUtils.pageBack();
	}
	var rewardReflect = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = rewardReflect;
});
