<div class="page" id="vipBenefits_phoneVouchersConfirm" data-pageTitle="同行好友" data-refresh="true" data-pageLevel="0"
     data-isSaveDom="false"
     data-refresh="true" style="-webkit-overflow-scrolling : touch;">
    <div class="pop_layer" style="display:none;position: absolute;">
        <div class="password_box">
            <div class="password_inner slidedown in">
                <a href="javascript:void(0);" id="close" class="close_btn"></a>
                <h4>请输入<span>6</span>位交易密码</h4>
                <h6 id="rechargeInfo"><!-- 使用<em>尾号7782光大银行卡，</em>充值现金宝<em>2.00</em>元 --></h6>
                <div class="password_input">
                    <span id="span00"></span>
                    <span id="span01"></span>
                    <span id="span02"></span>
                    <span id="span03"></span>
                    <span id="span04"></span>
                    <span id="span05"></span>
                    <input type="text" id="jymm" maxlength="6" style="display:none;">
                </div>
                <a href="javascript:void(0);" id="queDing" class="sure_btn text-center">确定</a>
            </div>
        </div>
    </div>
    <section class="main fixed" data-page="home" id="arrticle">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">积分兑换</h1>
            </div>
        </header>

        <article class="bg">
            <div class="pvConfirm">
                <div class="pvConfirmTitle">
                    <h2>兑换确认</h2>
                </div>
                <div class="pvConfirmGoods">
                    <div class="left">
                        <img src="" alt="" class="confirm_img">
                    </div>
                    <div class="right">
                        <h3 class="top"></h3>
                        <div class="jifen">
                            <span class="jifen_one"><em></em>积分</span>
                            <span>×<em>1</em></span>
                        </div>
                    </div>
                </div>
                <!--<div class="pvConfirmPrice">-->
                    <!--<h4>共<span>1</span>件商品，小计：<span class="red"><em>1000</em>积分</span></h4>-->
                <!--</div>-->
            </div>
            <div class="phone_securities card" style="display: none">
                <div class="pvConfirmMobile">
                    <span>充值手机号</span>
                    <input custom_keybord="0"  id="topUp" type="tel" maxlength="11" class="ui input code_input" placeholder="请输入充值手机号" autocomplete="off">
                </div>
                <div class="phone_btns"><a href="javascript:void(0);" id="phone_btns" class="btns_css">兑换确认</a></div>
            </div>
            <div class="Jingdong_card card" style="display: none">
                <div class="phone_btns"><a href="javascript:void(0);" id="phone_btns_jd" class="btns_css">兑换确认</a></div>
                <div class="convertibleNotes">
                    <h4>兑换须知</h4>
                    <p>1.该商品为电子卡密，一经兑换概不接受任何形式的退换。</p>
                    <p>2.兑换成功后，会以短信形式发送账号及密码至注册手机号，请在有效期内完成绑定，以防卡券过期。</p>
                    <p>3.京东E卡仅可用于购买京东APP自营商品，部分商品不可用，更多使用规则以京东官网公布的《购卡章程》为准。</p>
                    <p>4.点击查看<span style="color: #319ef2;" class="jdUsingProcess">《京东E卡兑换使用流程说明》</span>。</p>
                </div>
            </div>

        </article>
    </section>
</div>