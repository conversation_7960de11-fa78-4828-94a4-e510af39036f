// 快捷换卡
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_kjnewcardinfo ";
    var timer = null;//计时器
    var i = 120;//倒计时长
    var bank_code = "";
    var mobile = "", isBankValid;
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var single_limit;
    var day_limit;
    var send_sms_flag;
    var ut = require("../common/userUtil");
    var userInfo;
    var Millisecond;
    var pay_type; //0：快捷代扣 1：协议支付
    var bank_serial_no; //银行渠道流水
    var payorg_id;//支付机构ID
    var sms_mobile = require("../common/sms_mobile");

    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        //点击查看支持的银行卡限额返回后，数据恢复
        var newCardInfo = appUtils.getSStorageInfo("newCardInfo");
        if (newCardInfo != "" && newCardInfo != null) {
            if (!parseFloat(newCardInfo.oneMoney) || !newCardInfo.bankname) {
                $(_pageId + " .place").hide();
            } else {
                $(_pageId + " .place").show();
            }
            $(_pageId + " #bankCard").val(newCardInfo.bankCard);
            $(_pageId + " #bankname").val(newCardInfo.bankname).attr("bank_code", newCardInfo.bank_code).attr("payorg_id", newCardInfo.payorg_id).attr("pay_type", newCardInfo.pay_type);
            $(_pageId + " #oneMoney").html(newCardInfo.oneMoney);
            $(_pageId + " #drxe").html(newCardInfo.drxe);
            $(_pageId + " #yhmPhone").val(newCardInfo.yhmPhone);
            $(_pageId + " #verificationCode").val(newCardInfo.verificationCode);
        } else {
            clearPage();
        }
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .bank_tips"), function () {
            // 存储路径
            let routerList = appUtils.getSStorageInfo("routerList");
            routerList = routerList.join(',');
            routerList = routerList.replace('safety/bankInfo,','')
            routerList = routerList.split(',')
            appUtils.setSStorageInfo("routerList",routerList);
            //保存当页数据
            var bankCard = $(_pageId + " #bankCard").val();
            var bankname = $(_pageId + " #bankname").val();
            var oneMoney = $(_pageId + " #oneMoney").html();
            var drxe = $(_pageId + " #drxe").html();
            var yhmPhone = $(_pageId + " #yhmPhone").val();
            var verificationCode = $(_pageId + " #verificationCode").html();
            var param = {
                'bankCard': bankCard,
                'bankname': bankname,
                'oneMoney': oneMoney,
                'drxe': drxe,
                'yhmPhone': yhmPhone,
                'bank_code': $(_pageId + " #bankname").attr("bank_code"),
                'payorg_id': payorg_id,
                'pay_type': pay_type,
                'verificationCode': verificationCode
            };
            appUtils.setSStorageInfo("newCardInfo", param);
            appUtils.setSStorageInfo("isShowChangeBank",'0')
            appUtils.pageInit("safety/kjnewcardinfo", "safety/bankInfo");
        });
        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            var external = require("external");
            var Param = {
                "funcNo": "60304",
                "moduleName": "mall"
            };
            external.callMessage(Param);
        });
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");
        //银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            $(_pageId + " .place").hide();
            $(_pageId + " #pop_view").css("visibility", "hidden");
            $(_pageId + " #big_show_bank").html("");
            var bankCard = $(_pageId + "#bankCard").val();
            if (validatorUtil.isEmpty(bankCard)) {
                layerUtils.iMsg(-1, "银行卡号不能为空");
                return;
            }
            if (!validatorUtil.isBankCode(bankCard)) {
                $(_pageId + " #oneMoney").html("");
                $(_pageId + " #drxe").html("");
                $(_pageId + " #bankname").val("");
                layerUtils.iMsg(-1, "无效卡号请重新输入");
                return;
            }
            var bin_id = bankCard;
            bin_id = bin_id.replaceAll(" ", "");
            bank_code = bin_id;
            var param = {
                bin_id: bin_id
            };
            service.BankByCard(param, function (data) {
                var error_info = data.error_info,
                    error_no = data.error_no;
                if (error_no == "0") {
                    $(_pageId + " .place").show();
                    if (data.results.length > 0) {
                        var result = data.results[0];
                        var bank_name = result.bank_name;
                        var bank_code = result.bank_code;
                        single_limit = result.single_limit;
                        day_limit = result.day_limit;
                        pay_type = result.pay_type; //支付方式
                        payorg_id = result.payorg_id; //支付机构ID
                        send_sms_flag = result.send_sms_flag;
                        if (parseFloat(day_limit) > 0) {
                            $(_pageId + " .place").show();
                            $(_pageId + " #drxe").html(day_limit + "元");
                        } else if (parseFloat(day_limit) == 0) {
                            $(_pageId + " .place").hide();
                        } else {
                            $(_pageId + " .place").show();
                            $(_pageId + " #drxe").html("不限");
                        }
                        if (parseFloat(single_limit) > 0) {
                            $(_pageId + " .place").show();
                            $(_pageId + " #oneMoney").html(single_limit + "元");
                        } else if (parseFloat(single_limit) == 0) {
                            $(_pageId + " .place").hide();
                        } else {
                            $(_pageId + " .place").show();
                            $(_pageId + " #oneMoney").html("不限");
                        }
                        //$(_pageId+" #bankname").remove();
                        $(_pageId + " #bankname").val(bank_name);
                        $(_pageId + " #bankname").attr("bank_code", bank_code).attr("payorg_id", payorg_id).attr("pay_type", pay_type);
                        // getPdf(bin_id, bank_no);
                    } else {
                        $(_pageId + " .place").hide();
                        $(_pageId + " #bankname").val("");
                        $(_pageId + " #bankname").attr("bank_code", "").removeAttr("payorg_id").removeAttr("pay_type");
                        layerUtils.iMsg(-1, "不支持的银行卡");
                        return;
                    }

                } else {
                    $(_pageId + " .place").hide();
                    $(_pageId + " #bankname").attr("bank_code", "").removeAttr("payorg_id").removeAttr("pay_type");
                    $(_pageId + " #bankname").val("");
                    layerUtils.iAlert(error_info);
                }
            });
        }, "blur");

        appUtils.bindEvent($(_pageId + " .grid_02"), function () {
            $(_pageId + " .pop_layer4").hide();
            $(_pageId + " .card_rules").hide();

        });
        appUtils.bindEvent($(_pageId + " .right_btn"), function () {
            $(_pageId + " .pop_layer4").show();
            $(_pageId + " .card_rules").show();
        });
        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            mobile = $(_pageId + " #yhmPhone").val();
            var bankCard = $(_pageId + " #bankCard").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            if ($code.attr("data-state") == "false") {
                return;
            }
            if (mobile != null && mobile != "") {
                if (!validatorUtil.isMobile(mobile)) {
                    //判断输入的是否是手机号
                    layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                    return;
                } else {
                    //获取验证码
                    var param = {
                        "bank_code": $(_pageId + " #bankname").attr("bank_code"),//银行编码
                        "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                        "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                        "bank_acct":  $(_pageId + "#bankCard").val(),     // 用户卡号
                        "cert_no": userInfo.identityNum,   // 用户身份证
                        "bank_reserved_mobile":mobile,
                        "cert_type": "0", //证件类型
                        "bank_name":$(_pageId + " #bankname").val(),
                        "sms_type":common.sms_type.kjChangeCard,
                        "send_type": "0",//发送短信验证码
                        "cust_name": userInfo.name, // 用户姓名
                        "mobile_phone": mobile,
                        "type": common.sms_type.kjChangeCard,
                    };
                    let payorg_id = $(_pageId + " #bankname").attr("payorg_id")
                    if(send_sms_flag == "1"){
                        sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                            if (data.error_no == "0") {
                                bank_serial_no = data.results[0].bank_serial_no
                            }else{
                                layerUtils.iAlert(data.error_info);
                            }
                        });
                    }else{
                        sms_mobile.sendPhoneCode(param);
                    }
                }

            }
        });
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var sms_code = $(_pageId + " #verificationCode").val();
            var bankCard = $(_pageId + " #bankCard").val();
            if (!validatorUtil.isBankCode(bankCard)) {
                $(_pageId + " #oneMoney").html("");
                $(_pageId + " #drxe").html("");
                $(_pageId + " #bankname").val("");
                layerUtils.iMsg(-1, "无效卡号请重新输入");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").val())) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            mobile = $(_pageId + " #yhmPhone").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                layerUtils.iMsg(-1, "请先获取验证码");
                return
            }
            if (sms_code == "") {
                if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                    layerUtils.iMsg(-1, "请先获取验证码");
                } else {
                    layerUtils.iMsg(-1, "请输入验证码");
                }
            } else {
                var chencked = $(_pageId + " #input_radio2").attr("checked");
                if (chencked != "checked" && isBankValid) {
                    layerUtils.iMsg(-1, "请阅读协议并同意签署");
                    return;
                }
                //验证短信验证码
                var param = {
                    'org_bank_acct': userInfo.bankAcct,//旧银行卡号
                    'org_bank_code': userInfo.bankCode,//旧银行编号
                    'org_bank_reserved_mobile': userInfo.bankReservedMobile,//旧银行卡预留手机号
                    'bank_reserved_mobile': $(_pageId + " #yhmPhone").val(),//新银行卡预留手机号
                    'cust_name': userInfo.name,
                    'bank_acct': $(_pageId + "#bankCard").val(),//新银行卡号
                    'bank_code': $(_pageId + "#bankname").attr("bank_code"),//新银行编码
                    "sms_mobile": mobile,
                    "cert_type": "0", //证件类型
                    "message_code": sms_code, //短信验证码
                    "sms_code": sms_code, //短信验证码
                    "pay_type": $(_pageId + "#bankname").attr("pay_type"),
                    "payorg_id": $(_pageId + "#bankname").attr("payorg_id"),
                    "bank_serial_no": bank_serial_no
                }
                //实名认证
                service.reqFun101061(param, function (data) {
                    if (data.error_no != "0") {
                        sms_mobile.clear();
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    service.reqFun101012(param, function (resultsVo) {
                        callback(resultsVo);
                    });
                }, {isLastReq: false})

                var callback = function (resultsVo) {
                    if (resultsVo.error_no == 0) {
                        appUtils.clearSStorage("newCardInfo");
                        //将新卡添加到session里
                        var user = ut.getUserInf();
                        user.bankName = $(_pageId + " #bankname").val();
                        user.bankReservedMobile = $(_pageId + " #yhmPhone").val().substr(0, 4) + "****" + $(_pageId + " #yhmPhone").val().substr(-4);
                        user.bankCode = $(_pageId + "#bankname").attr("bank_code");
                        user.bankAcct = $(_pageId + "#bankCard").val().substr(0, 4) + "****" + $(_pageId + "#bankCard").val().substr(-4);
                        ut.saveUserInf(user);
                        appUtils.pageInit("safety/kjnewcardinfo", "safety/kjsetBankCardSuccess");
                    } else {
                        layerUtils.iAlert(resultsVo.error_info);
                    }
                }
            }
        });

        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });
    }


    //页面清理
    function clearPage() {
        $(_pageId + " input").attr("value", "");
        $(_pageId + " #bankname").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");

    }

    function destroy() {
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        sms_mobile.destroy(_pageId);
        $(_pageId + " .place").hide();
        service.destroy();
        $(_pageId + " #getYzm").removeAttr("style");
        $(_pageId + " input").attr("value", "");
        $(_pageId + " .sure_box").hide();
        $(_pageId + " #bankname").removeAttr("bank_code").removeAttr("pay_type").removeAttr("payorg_id");
        $(_pageId + ' #weihao').hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
