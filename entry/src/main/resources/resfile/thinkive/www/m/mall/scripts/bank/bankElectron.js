// 银行电子账户
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_bankElectron ";
    var _pageCode = "bank/bankElectron";
    var tools = require("../common/tools");
    var type;
    var bankListColor = ["#319ef2", "#e5443c", "#FF7A11"];

    function init() {
        type = appUtils.getSStorageInfo("bankEntrytype");
        $(_pageId + " #hava_data").show();
        getBankAccountInfo();
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .go_stroll"), function () {
            appUtils.pageInit(_pageCode, "bank/bankList");
        });

        appUtils.preBindEvent($(_pageId + " .bank_box"), ".bank_inner", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            // if(productInfo.is_open == "0") return;
            appUtils.setSStorageInfo("productInfo", productInfo);
            var name = $(this).attr("data-name");
            if (name == "enchash") { //银行取现
                appUtils.pageInit(_pageCode, "bank/enchashment");
            } else if (name == "recharge") {
                appUtils.pageInit(_pageCode, "bank/recharge");
            } else if(name == "openAccount"){ //银行账户主页
                appUtils.pageInit(_pageCode, "bank/faceRecognition");
            } else { //银行账户主页
                appUtils.pageInit(_pageCode, "bank/myAccount");
            }
        }, 'click');

    }


    function getBankAccountInfo() {
        var params = {};
        var btnObj = {
            "recharge": {
                "0": {
                    text: "开户",
                    class: "openAccount",
                },
                "1": {
                    text: "充值",
                    class: "recharge",
                },
            },
            "enchash":  {
                "0": {
                    text: "",
                    class: "",
                },
                "1": {
                    text: "取现",
                    class: "enchash",
                },
            },
            "": {
                "0": {
                    text: "开户",
                    class: "openAccount",
                },
                "1": {
                    text: "",
                    class: "hidden",
                },
            }
        }
        service.reqFun151122(params, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                var list = results.acctNoList;
                var html = "";
                var length = list.length;
                for (var i = 0; i < length; i++) {
                    var bank_name = list[i].bank_channel_name;
                    var bank_no_class = "",bank_no;
                    if(list[i].acct_no) {
                        bank_no_class = "block";
                        bank_no = "****" + list[i].acct_no.substr(-4);
                    } else {
                        bank_no_class = "hidden";
                    }
                    var avail_amt = tools.fmoney(list[i].avail_amt) + "元";
                    var icon = tools.judgeBankImg(list[i].bank_channel_code).icon;
                    var is_open = list[i].is_open; // 0未开户  1已开户
                    if (is_open == "0" && type == "enchash") { // 取现入口进入，并且未开户的不展示

                    } else {
                        html += '<div class="bank_inner flow in" style="background: ' + bankListColor[i] + '" data-name="' + btnObj[type][is_open].class + '">' +
                            '<span class="icon"></span>' +
                            "<span style='display: none' class='productInfo'>" + JSON.stringify(list[i]) + "</span>" +
                            '<h4>' +
                            '<em><img src=' + icon + ' width="22" alt=""></em>' +
                            '<span>' + bank_name + '</span>' +
                            '<b class="' + bank_no_class + '">' + bank_no + '</b>' +
                            '</h4>' +
                            '<p>账户余额：<span>' + avail_amt + '</span>' +
                            '<span class="btn ' + btnObj[type][is_open].class + '" >' + btnObj[type][is_open].text + '</span>' +
                            '</p>' +
                            '</div>';
                    }
                }
                $(_pageId + " .bank_box").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        $(_pageId + " #hava_data").hide();
        $(_pageId + " #no_data").hide();
        $(_pageId + " .bank_box").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var bankElectron = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankElectron;
});
