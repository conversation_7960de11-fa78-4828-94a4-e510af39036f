<div class="page" id="bank_bankMoreDetail" data-pageTitle="产品详情" data-pageLevel="0"
     data-refresh="true" style="-webkit-overflow-scrolling : touch;">
    <section class="main fixed" data-page="home" id="product">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">产品详情</h1>
            </div>
        </header>
        <article style="padding-top: 0">
            <div class="info-contennt">
                <div class="highFinancialInfo-item">
                    <p class="item-left ">产品名称</p>
                    <p class="item-right fund_name"></p>
                </div>
                <div class="highFinancialInfo-item">
                    <p class="item-left">期存金额</p>
                    <p class="item-right fund_sname"></p>
                </div>
                <div class="highFinancialInfo-item">
                    <p class="item-left">存款银行</p>
                    <p class="item-right fund_code"></p>
                </div>
                <div class="highFinancialInfo-item">
                    <p class="item-left">产品类型</p>
                    <p class="item-right fund_type_name"></p>
                </div>
                <div class="highFinancialInfo-item">
                    <p class="item-left">支取方式</p>
                    <p class="item-right establish_date"></p>
                </div>
            </div>
        </article>
    </section>
</div>
