// 交易记录详情   強赎
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#combProduct_combTrsDetailsFore ";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    // 备注
    var remark_bj = {
        "14201": "强赎到宝",
        "14202": "强赎到卡"
    }
    var accountObj = {
        "14201": "晋金宝",
        "14202": "银行卡"
    }
    var trans_serno, nextPageData, detailsInfo;

    function init() {
        var param = appUtils.getPageParam() ? appUtils.getPageParam() : appUtils.getSStorageInfo("nextPageData");
        nextPageData = param;
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " .small").removeClass("hidden").addClass("block");
        $(_pageId + " .normal").addClass("hidden");
        //交易详情查询
        detail(param)
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function detail(param) {
        var callback = function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);
            detailsInfo = results;

            //交易时间
            var trans_time = results.trans_time;
            trans_time = tools.ftime(trans_time);

            //交易状态
            var trans_status = results.trans_status;
            var trans_status_name = tools.fundDataDict(trans_status, "comb_qs_trans_status_name");

            //产品名称
            var prod_name = results.comb_name;
            //交易流水号
            trans_serno = results.trans_serno;
            //交易流水号
            var trans_serno = results.trans_serno;
            //交易份额
            var trans_amt = tools.fmoney(results.trans_amt);
            trans_amt = trans_amt + "份";
            //确认份额
            var ack_vol = tools.fmoney(results.ack_vol);
            ack_vol = ack_vol + "份";
            //确认金额
            var ack_amt = tools.fmoney(results.ack_amt);
            ack_amt = ack_amt + "元";
            //确认净值
            var ack_nav = tools.fmoney(results.ack_nav, 4);
            ack_nav = ack_nav + "元";
            //手续费
            var feet_amt = tools.fmoney(results.feet_amt);
            feet_amt = feet_amt + "元";

            $(_pageId + " #ack_date").html(results.trans_date);
            $(_pageId + " #pay_date").html(results.pay_date);
            $(_pageId + " .trans_amt").html(trans_amt);
            $(_pageId + " .ack_vol").html(ack_vol);
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #remarkname").html(results.remarkname);
            $(_pageId + " .sell_info").html(accountObj[results.sub_busi_code]);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " #ack_vol").html(ack_vol);
            $(_pageId + " #ack_amt").html(ack_amt);
            $(_pageId + " #ack_nav").html(ack_nav);
            $(_pageId + " #feet_amt").html(feet_amt);
            $(_pageId + " #trans_time").html(trans_time);
        }
        service.reqFun102163({ trans_serno: param.trans_serno }, callback);
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #pay_date").html("--");
        $(_pageId + " #trans_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        // $(_pageId + " .end_text").html("");
        // $(_pageId + " .start_text").html("");
        $(_pageId + " .small").removeClass("block").addClass("hidden");
        $(_pageId + " .normal").removeClass("block").addClass("hidden");
        $(_pageId + " #remarkname").html("--");
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
