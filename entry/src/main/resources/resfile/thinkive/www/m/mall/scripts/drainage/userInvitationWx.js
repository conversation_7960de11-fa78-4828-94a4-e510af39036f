// 分享链接注册
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        putils = require("putils"),
        common = require("common"),
        _pageId = "#drainage_userInvitationWx";
    var _pageCode = "drainage/userInvitationWx";
    var sms_mobile = require("../common/sms_mobile");
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");
    var i = 120;//倒计时长
    var invitationMobile;
    var labelId,timer;
    var userType; //0 普通用户 1 引流晋金所用户
    var endecryptUtils = require("endecryptUtils");
    require("../common/verify")
    
    function init() {
        tools.getPdf("5");
        $(_pageId + " .drainShow").hide()
        window.clearInterval(timer);
        $(_pageId + ' #verificationCode').attr("disabled",true);
        // 解禁系统键盘
        common.systemKeybord();
        //banner轮播
        tools.guanggao({_pageId: _pageId, group_id: "24"});
        //刷新图形验证码
        // setImgCode();
        //获取邀请人手机号
        invitationMobile = appUtils.getPageParam("mobile");
        if(invitationMobile) {
            invitationMobile = endecryptUtils.desDecrypt("mobile", invitationMobile);//解密
        }
        if(invitationMobile) {
            appUtils.setSStorageInfo("invitationMobile", invitationMobile);
        }
        //获取渠道ID
        labelId = appUtils.getPageParam("labelId");
        if(labelId) {
            appUtils.setSStorageInfo("labelId", labelId);
        }
        $(_pageId + " #pwd").val("");
        $(_pageId + " #phontNum").val("");
        sms_mobile.init(_pageId);
        getSlderInit()
        
    }

    //绑定事件
    function bindPageEvent() {
        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });
        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            //验证手机号input
            var mobile = $.trim($(_pageId + " #phontNum").val());
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            // //验证登录密码input
            // var pwd_one = $(_pageId + " #pwd1").val();
            // var pwd_two = $(_pageId + " #pwd2").val();
            // if (!(checkInput(pwd_one, pwd_two))) {
            //     return;
            // }
            // //验证图形验证码input
            // var imgCode = $(_pageId + " #tuxingCode").val();
            // if (imgCode == "") {
            //     layerUtils.iMsg(-1, "请输入图形验证码");
            //     return;
            // }
            //验证获取验证码按钮可用性
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            window.clearInterval(timer);
            sendPhoneCode(mobile);
        });
        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");

//            // 是否是有效内链
//            if (file_type == "0" && file_state == "1" && url) {
//                appUtils.pageInit(_pageCode, url, {});
//                return;
//            }
            // 是否是有效外链接
            if (file_type == "1" && file_state == "1" && url) {
                appUtils.pageInit(_pageCode, "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                });
                return;
            }
        }, 'click');

        // /* 切换图形验证码 */
        // appUtils.bindEvent($(_pageId + " #getCode"), function () {
        //     setImgCode();
        // });
        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #registered"), function () {
            //检查手机号
            var phone = $.trim($(_pageId + " #phontNum").val());
            //检查协议勾选
            var isChecked = $(_pageId + " #input_radio2").attr("checked");
            if (!isChecked) {
                layerUtils.iMsg(-1, "请阅读协议并同意签署");
                return;
            }
            if (validatorUtil.isEmpty(phone)) {
                layerUtils.iMsg(-1, "请输入手机号码");
                return;
            }
            if (!validatorUtil.isMobile(phone)) {
                layerUtils.iMsg(-1, "请确定您输入的手机号是否正确");
                return;
            }
            //检查图形验证码
            var imgCode = $(_pageId + " #code").val();
            if (imgCode == "") {
                layerUtils.iMsg(-1, "请输入验证码");
                return;
            };
            appUtils.setSStorageInfo("code", imgCode);
            if(userType == 1){  //引流用户
                //校验短信验证码
                var param1100003 = {
                    "sms_mobile": phone,
                    "sms_code": imgCode,
                };
                service.reqFun1100003(param1100003, function(data) {
                    if(data.error_no == "0") {
                        appUtils.pageInit(_pageCode, "drainage/loginPwdWx");
                    } else {
                        sms_mobile.clear();
                        $(_pageId + " #pwd").val("");
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }else{
                appUtils.pageInit(_pageCode, "drainage/userRegisteredWx");  //普通用户
            }
            
            // var param = {
            //     mobile: phone,
            //     ticket: imgCode,
            // };
            // registerAccount(param);
        });

    }
    //初始化滑动
    function getSlderInit(){
        $(_pageId + " #mpanel").empty()
        $(_pageId + " #getYzm").hide()
        $(_pageId + ' #mpanel').slideVerify({
            type : 1,		//类型
            vOffset : 5,	//误差量，根据需求自行调整
            barSize : {
                width : '100%',
                height : '44px',
            },
            ready : function() {

            },
            success : function() {
                getCode()
            },
            error : function() {
            }
            
        });
    }
    //获取短信验证码
    function getCode(){
        //获取短信验证码
        var phone = $.trim($(_pageId + " #phontNum").val());
        if (!validatorUtil.isMobile(phone)) {
            layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
            getSlderInit()
            return;
        }
        window.clearInterval(timer);
        registerAccount({mobile:phone})
        // sendPhoneCode(phone);
    }
    function sendPhoneCode(phoneNum){
        var param = {
            mobile_phone: phoneNum,
            type: common.sms_type.register,
            send_type: "0"
        };
        service.reqFun199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                timer = setInterval(function () {
                    // $(_pageId + " #talkCode").show();
                    $(_pageId + ' #code').attr("disabled",false);
                    shows();
                }, 1000);
            } else {
                // var $code = $(_pageId + " #getYzm");
                getSlderInit()
                window.clearInterval(timer);
                i = 120;
                // $code.css("background-color", " #C1E3B6");
                // $code.attr("data-state", "true");
                // $code.html("重新获取验证码");
                // $(_pageId + " #talkCode").show();
                layerUtils.iAlert(error_info);
            }
            // $(_pageId + " #talkCode").show();
        });
    }
    /**
     * 显示读秒
     * */
     function shows() {
        var $code = $(_pageId + " #getYzm");
        $(_pageId + " #getYzm").show()
        $code.attr("data-state", "false");//点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();
        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.css("background-color", "yellow");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.css("background-color", " #C1E3B6");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            // initYanZma();
            // $(_pageId + " #talkCode").show();
        }
    }
    function registerAccount(param) {
        service.reqFun102005(param, function (data) {
            if (data.error_no == "-********") {//******** 账号已注册
                getSlderInit()
                layerUtils.iAlert("该手机号已注册，请下载app登录", "", function () {
                    tools.getPdf("12")
                    appUtils.pageInit(_pageCode, "appdown/index");
                },"下载");
            } else if (data.error_no == "-********") {//黑名单
                getSlderInit()
                tools.getPdf("12")
                $(_pageId + " input").blur();
                layerUtils.iAlert("网络繁忙,请稍后重试!");
            } else if (data.error_no == "-********") {//晋金所已开户
                appUtils.setSStorageInfo("mobile", param.mobile);
                tools.getPdf("12")
                userType = 1
                $(_pageId + " input").blur();
                $(_pageId + " .drainShow").show()
                let data = {
                    mobile_phone: param.mobile,
                    type: common.sms_type.loginDrain,
                    send_type: "0"
                };
                sms_mobile.sendDrainPhoneCode(data,function(res){
                    if(res.error_no == '0'){
                        timer = setInterval(function () {
                            $(_pageId + ' #code').attr("disabled",false);
                            shows();
                        }, 1000);
                    }
                })
                // layerUtils.iAlert("检测到您已在晋金所开户，请授权注册", "", function () {
                //     appUtils.setSStorageInfo("mobile", param.mobile);
                //     appUtils.pageInit(_pageCode, "drainage/userLoginWx");
                // });
            }  else if (data.error_no == "0") {//晋金所未开户
                tools.getPdf("12")
                userType = 0
                sendPhoneCode(param.mobile);
                $(_pageId + " input").blur();
                appUtils.setSStorageInfo("mobile", param.mobile);
                // appUtils.setSStorageInfo("code", param.ticket);
                // appUtils.pageInit(_pageCode, "drainage/userRegisteredWx");
            } else if (data.error_no != "0") {
                getSlderInit()
                tools.getPdf("12")
                layerUtils.iAlert(data.error_info);
            }
            // setImgCode();
            // $(_pageId + " #tuxingCode").val("");
        });
    }
    function destroy() {
        $(_pageId + " input").val("");
        $(_pageId + " .drainShow").hide()
        // $(_pageId + " #talkCode").hide();
        $(_pageId + " #mpanel").empty()
        window.clearInterval(timer);
        i = 120
        service.destroy();
    }

    function pageBack() {
        appUtils.pageInit("drainage/userInvitationWx", "login/userIndexs", {});
    }

    // //刷新图形验证码
    // function setImgCode() {
    //     service.reqFun1100005({}, function (data) {
    //         if (data.error_no == 0) {
    //             var base64 = data.results[0].base64;
    //             $(_pageId + " #getCode img").attr("src", base64);
    //         } else {
    //             layerUtils.iAlert(data.error_info);
    //         }
    //     })
    // }

    var userInfo = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userInfo;
});
