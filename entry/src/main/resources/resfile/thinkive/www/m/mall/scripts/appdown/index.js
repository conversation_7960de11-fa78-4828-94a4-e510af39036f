// 下载页面
define(function (require, exports, module) {
    var gconfig = require("gconfig"),
        global = gconfig.global,
        service = require("mobileService"),
        _pageId = "#appdown_index",
        appUtils = require("appUtils"),
        layerUtils = require("layerUtils");
        progress = require('../common/progress.js');
    var flag; // 是否重复点击
    var is_weixin = false;
    var isIphone = false;
    var isAndroid = false;
    var downloadUrl = "https://jjdx.sxjjd.com/oss/fund_filesystem/app/jjdxtest.apk";
    function init() {
        flag = true;
        var ua = navigator.userAgent.toLowerCase();
        if (ua.indexOf('iphone') > 0 || ua.indexOf('ipad') > 0) {						//需对所有 iOS 系统 UA 信息进行判断
            isIphone = true;
        }
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            is_weixin = true;
        }
        if (ua.indexOf('android') > 0 || ua.indexOf('window') > 0 ) {				//需对所有 Android 系统 UA 信息进行判断
            isAndroid = true;
        }
        if (isIphone && is_weixin)              //IPone 手机打开微信
        {
            $(_pageId + " #iphone_weixin").show();
            return;
        }

        if (isAndroid && is_weixin) {
            $(_pageId + " #android_weixin").show();
            return;
        }

        if (isIphone) {
            downloadUrl =  $(_pageId + " .uploadIosSrc").attr("upload_ios_url"); //下载地址


            window.location =downloadUrl  ;
            return;
        }

        if (isAndroid) {
            initAndroidInfo();
            return;
        }

    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #updateBtn"),  ()=> { 
            // console.log(progress)
            $(_pageId + " #updateBtn").attr('disabled',true); 
            // const rand_num = progress.rand(11)
            // progress.new_download('https://jjdx.sxjjd.com/oss/fund_filesystem/app/jjdxtest.apk',rand_num)
            // $(_pageId + " #updateBtn").text("下载中");
            // progress.
            if(!flag) return;
            flag = false;
            $("iframe").remove();
            $("body").append("<iframe src='" + downloadUrl+ "' style='display: none'></iframe>");
            setTimeout(()=>{
                $(_pageId + " #updateBtn").text("下载中");
                $(_pageId + " #updateBarInfo").show();
                flag = true;
            }, 2000)
        })
    }

    function destroy() {
        $(_pageId + " #updateBarInfo").hide();
        $("iframe").remove();
        downloadUrl = "";
    }

    // 初始化安卓信息
    function initAndroidInfo() {
        $(_pageId + " #android_download").show();
        $(_pageId + " #updateBtn").text("下载");
        var soft_no = global.soft_no;
        var oVersion = global.version_code;
        var queryParam = {
            "channel": "1",
        };
        service.reqFun102071(queryParam, (data)=> {
            if (data.error_no == 0) {
                if (data.results.length != 0) {
                    let description = data.results[0].description;  //更新说明
                    downloadUrl =  $(_pageId + " .uploadSrc").attr("upload_url"); //下载地址
                    $(_pageId + " .description").html(description);
                }
            }
        }, {"isShowWait": false});
    }
    var userIndex = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
// 暴露对外的接口
    module.exports = userIndex;
})
;
