// 代言人-全部好友页面
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        gconfig = require("gconfig"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageCode = "spokesperson/allFriendsPage",
        ut = require("../common/userUtil"),
        _pageId = "#spokesperson_allFriendsPage ";
    require("../common/html2canvas.min.js");
    require("../common/jquery.qrcode.min.js");
    var global = gconfig.global;
    var pageDataInfo;
    var source;//来源
    var currentPage;
    var totalPages;
    var cur_page = 1;
    var platform = gconfig.platform;
    function init() {
        source = '17';
        queryList(false);
    }

    function queryList(isAppendFlag) {
        var state = $(_pageId + "  .list_tab .selected").attr("data-type");
        $(_pageId + " .new_none").hide();
        $(_pageId + " .list_header").hide();
        var param = {
            current: cur_page,
            count: "20",
            cust_type: state
        };
        service.reqFun113007(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            var str = "";
            if (error_no == "0") {
                pageDataInfo = data.results[0];
                let len = pageDataInfo.show_prod_name.length;
                $(_pageId + " #invitedCount").html(`(${pageDataInfo.invited_count})`)
                $(_pageId + " .list_header #prodName").html(`${pageDataInfo.show_prod_name.slice(0, 4)}<br>${pageDataInfo.show_prod_name.slice(4, len)}`)
                var listData = [];
                if (state == '1') { // 未绑卡
                    listData = data.results[0].unbound_list;
                } else if (state == '2') { // 绑卡未投资
                    listData = data.results[0].not_invested_list;
                } else if (state == '3') { // 已投资
                    listData = data.results[0].invested_list;
                }
                if (listData.data != undefined && listData.data.length > 0) {
                    $(_pageId + " .list_header").show();
                    currentPage = listData.currentPage;//当前页数
                    totalPages = listData.totalPages;//总页数
                    if (currentPage <= totalPages) {
                        listData.data.forEach(item => {
                            item.buy_flag += "";
                            item = tools.FormatNull(item); // 空数据展示--
                            str += ` <ul class="list_item">
                                <li style="width: 30%;">${item.registered_mobile}</li>
                                <li style="width: 20%;">${item.cust_name}</li>
                                <li style="width: 30%;">${item.buy_flag == '0' ? '否' : '是'}</li>
                                <li style="width: 20%;">
                                    <div class="send_wx" data-type="${state}">发微信</div>
                                </li>
                            </ul>`;
                        })
                    } else {
                        str += "<div class='nodata'>暂无数据</div>";
                    }

                } else {
                    $(_pageId + " .list_header").hide();
                    str += "<div class='nodata'>暂无数据</div>";
                }
                if (isAppendFlag) {
                    $(_pageId + " .list_content").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .list_content").html(str);
                }
                pageScrollInit();

            } else {
                layerUtils.iAlert(data.error_info);
            }

        });
    }

    function getShareTemplateInfo(share_template, type) {
        let params = {
            registered_mobile: ut.getUserInf().mobileWhole,
            share_template: share_template
        };
        service.reqFun102012(params, function (data) {
            if (data.error_no == '0') {
                var result = data.results[0];
                if (data.results[0] && data.results[0].share_form == 2) {  // 若分享的是卡片，先渲染卡片
                    setShareImg(result, type);
                } else if (data.results[0] && data.results[0].share_form == 1) {  // 链接
                    result['page_type'] = '10';
                    let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                    return tools.pageShare(result, type, _pageId, shareUrlLast, null, pageDataInfo.activity_id);//活动页面分享
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //渲染分享卡片图片
    function setShareImg(chooseData, type) {
        let bgImg = gconfig.global.oss_url + chooseData.img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                layerUtils.iMsg(-1, "启动分享中...请稍后！");
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #qr_img").show();
                    $(_pageId + " #code").hide();
                    $(_pageId + "#qr_img").empty();
                    let qr_code_img = global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #qr_img").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                        shareCard(type);
                    })
                } else {
                    $(_pageId + " #qr_img").hide();
                    $(_pageId + " #code").show();
                    let mobile = ut.getUserInf().mobileWhole;
                    mobile = common.desEncrypt("mobile", mobile);//加密
                    // let long_url = "https://xhxts.sxfae.com/m/mall/index.html#!/drainage/userInvitationWx.html?mobile=" + mobile;
                    let long_url = chooseData.qr_code_img_url + '?mobile=' + mobile + '&source=' + source;;
                    $(_pageId + "#code").empty();
                    service.reqFun101073({ long_url: long_url }, function (res) {
                        if (res.error_no == "0") {
                            if (res.results != undefined && res.results.length > 0) {
                                var short_url = res.results[0].shortUrl;
                                $(_pageId + " #code").qrcode({
                                    render: "canvas", //设置渲染方式，有table和canvas
                                    text: short_url, //扫描二维码后自动跳向该链接
                                    width: 70, //二维码的宽度
                                    height: 70, //二维码的高度
                                    imgWidth: 20,
                                    imgHeight: 20,
                                    src: '../mall/images/icon_app.png'
                                });
                                setTimeout(function () {
                                    shareCard(type);
                                }, 200);
                            }
                        } else {
                            layerUtils.iAlert(res.error_info);
                        }
                    })
                    // qrcode(chooseData, type);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /*分享卡片*/
    function shareCard(type) {
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        $(_pageId + " .pop_layer").hide();
        var father = document.querySelector("#content");
        var _fatherHTML = document.querySelectorAll("#content .page");
        var cur = document.querySelector("#spokesperson_allFriendsPage");
        father.innerHTML = "";
        father.appendChild(cur);
        let dom = document.querySelector(_pageId + " .shareImg");
        html2canvas(dom, {
            scale: 4
        }).then(canvas => {
            var base64 = canvas.toDataURL("image/png");
            var _base64 = base64.split(",")[1];
            father.innerHTML = "";
            for (let i = 0; i < _fatherHTML.length; i++) {
                father.appendChild(_fatherHTML[i]);
            }
            param = {
                "funcNo": "50231",
                "imgUrl": _base64,
                "shareType": type,
                "imageShare": "1",
                "imageType":"base64"
            }
            require("external").callMessage(param);
        })
    }

    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    queryList(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (currentPage && (currentPage < totalPages)) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        queryList(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
        //ios上拉拖动卡死问题解决
        var pageTouchTimer = null;
        //var contentHeight = $(_pageId + " #wrapper_w1").height()-570;
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");

    }


    //绑定事件
    function bindPageEvent() {
        // 手动添加好友
        appUtils.bindEvent($(_pageId + " #addFriendsBtnSD"), function () {
            $(_pageId + " #addFriends").show();
        });
        // 添加好友-提交
        appUtils.bindEvent($(_pageId + " #submitBtn"), function () {
            if (validatorUtil.isEmpty($(_pageId + " #inviteCustName").val())) {
                layerUtils.iMsg(-1, "请填写真实姓名");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #inviteRegisteredMobile").val())) {
                layerUtils.iMsg(-1, "请填写注册手机号");
                return;
            }
            if (!validatorUtil.isMobile($(_pageId + " #inviteRegisteredMobile").val())) {
                layerUtils.iMsg(-1, "请填写正确的手机号码");
                return;
            }
            let param = {
                invite_cust_name: $(_pageId + " #inviteCustName").val(),
                invite_registered_mobile: $(_pageId + " #inviteRegisteredMobile").val()
            }
            $(_pageId + " #addFriends").hide();
            $(_pageId + " #inviteCustName").val("");
            $(_pageId + " #inviteRegisteredMobile").val("");
            service.reqFun113006(param, function (data) {
                if (data.error_no == '0') {
                    $(_pageId + " #addFriendsSuccess").show();
                } else if (data.error_no == '-2' || data.error_no == '-3' || data.error_no == '-4' || data.error_no == '-5' || data.error_no == '-8') {
                    $(_pageId + " #addFriendsFail #errorInfo").html(`不符合条件，详询${global.custServiceTel}`);
                    $(_pageId + " #addFriendsFail").show();
                } else if (data.error_no == '-6') {
                    $(_pageId + " #addFriendsFail #errorInfo").html(`该客户已是您的好友，不需重复提交`);
                    $(_pageId + " #addFriendsFail").show();
                } else if (data.error_no == '-7') {
                    $(_pageId + " #addFriendsFail #errorInfo").html(`审核中，请耐心等待`);
                    $(_pageId + " #addFriendsFail").show();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })

        });
        // 添加成功
        appUtils.bindEvent($(_pageId + " #successBtn"), function () {
            $(_pageId + " #addFriendsSuccess").hide();
        });
        // 添加失败
        appUtils.bindEvent($(_pageId + " #failBtn"), function () {
            $(_pageId + " #addFriendsFail").hide();
        });
        // 添加记录
        appUtils.bindEvent($(_pageId + " #addRecordBtn"), function () {
            appUtils.pageInit(_pageCode, "spokesperson/addFriendRecord");
        })
        // 关闭手动添加好友
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " #inviteCustName").val("");
            $(_pageId + " #inviteRegisteredMobile").val("");
            $(_pageId + " #addFriends").hide();
        });
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.preBindEvent($(_pageId + " .list_tab"), "li", function () {
            vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, -40, 200);
            $(_pageId + " .list_tab li").removeClass("selected");
            $(this).addClass("selected")
            $(_pageId + " .new_none").hide();
            $(_pageId + " .list_content").html("");
            cur_page = 1;
            queryList(false);
        }, 'click')
        // 发微信
        appUtils.preBindEvent($(_pageId + " .list_content"), ".send_wx", function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            var type = $(this).attr("data-type");
            $(_pageId + " #pop_layer").attr("data-type", type)
            $(_pageId + " #pop_layer").show();
        }, "click")
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            var type = $(_pageId + " #pop_layer").attr("data-type");
            var share_template = "";
            if (type == 1) {
                share_template = pageDataInfo.share_template_id_unbound
            } else if (type == 2) {
                share_template = pageDataInfo.share_template_id_not_invested
            } else if (type == 3) {
                share_template = pageDataInfo.share_template_id_invested
            }
            tools.clickPoint(_pageId, _pageCode, 'share_WeChat', pageDataInfo.activity_id);
            getShareTemplateInfo(share_template, '22');
            // common.share("22", share_template ? share_template : "0", "", "", "", "", true, source);

        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            var type = $(_pageId + " #pop_layer").attr("data-type");
            var share_template = "";
            if (type == 1) {
                share_template = pageDataInfo.share_template_id_unbound
            } else if (type == 2) {
                share_template = pageDataInfo.share_template_id_not_invested
            } else if (type == 3) {
                share_template = pageDataInfo.share_template_id_invested
            }
            tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', pageDataInfo.activity_id);
            getShareTemplateInfo(share_template, '23');
            // common.share("23", share_template ? share_template : "0", "", "", "", "", true, source);
        });

    }
    //页面销毁
    function destroy() {
        $(_pageId + " #addFriendsFail").hide();
        $(_pageId + " #addFriendsSuccess").hide();
        $(_pageId + " #addFriends").hide();
        $(_pageId + " #UnboundCardList").show();
        $(_pageId + " #notInvestedList").hide();
        $(_pageId + " #investedList").hide();
        $(_pageId + " #UnboundCardList").html("");
        $(_pageId + " #notInvestedList").html("");
        $(_pageId + " #investedList").html("");
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #invitedCount").html("");
        $(_pageId + " #inviteCustName").val("");
        $(_pageId + " #inviteRegisteredMobile").val("");
        $(_pageId + " .list_tab li").removeClass("selected");
        $(_pageId + " .list_tab li:nth-child(1)").addClass("selected");
        $(_pageId + " #qr_img").hide();
        $(_pageId + " #code").show();
        $(_pageId + " .new_none").hide();
        $(_pageId + " .list_content").html("");
        $(_pageId + " .list_header").hide();
        cur_page = 1;
        pageDataInfo = '';
        totalPages = "";
    }


    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    let allFriendsPageModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = allFriendsPageModule;
});
