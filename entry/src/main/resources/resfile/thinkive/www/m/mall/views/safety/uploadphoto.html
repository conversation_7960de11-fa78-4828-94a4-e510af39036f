<div class="page" id="safety_uploadphoto" data-pageTitle="上传照片" data-refresh="false">
    <div class="pop_layer4 pop_layer" style="display:none"></div>
    <div class="card_rules" style="display:none">
        <div class="rules_box slideup in">
            <h5>换卡规则</h5>
            <div class="rules_list">
                <strong>1.客户需上传电子资料：</strong>
                <p>a.身份证正反面照片或扫描件。</p>
                <p>b.新银行卡正反面照片或扫描件。</p>
                <p>c.一手持新银行卡正面，一手持身份证正面照片（含本人头像，要求身份证信息、银行卡信息清晰）。</p>
                <strong>2.提交换卡申请后，我公司将在两个工作日（节假日顺延）内进行审核。</strong>
                <strong id="str1">3.提交换卡申请后，在收到换卡结果的短信前，不能交易。</strong>
                <strong id="str2">3.提交换卡审核成功后，需在页面上进行短信验证码确认，在确认前不能交易。</strong>
            </div>
            <p class="risk_tips">风险提示：换卡存在一定的风险，换卡成功后，取现资金到账银行卡为您变更后新的银行卡，请知晓。</p>
            <div class="grid_02">
                <a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
            </div>
        </div>
    </div>
    <div class="pop_layer" style="display:none">
        <div class="take_photo slideInUp">
            <div class="photo_list">
                <a href="javascript:void(0)">拍照</a>
                <a href="javascript:void(0)">从手机相册选择</a>
            </div>
            <a href="javascript:void(0)">取消</a>
        </div>
    </div>
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a herf="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">上传照片</h1>
                <a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
                <a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
            </div>
        </header>
        <article>
            <!-- UPLOAD_BOX START -->
            <div class="upload_box">
<!--                <h2 class="text-center">上传如下提示的照片<span>（请确保照片清晰）</span></h2>-->
                <div class="check_tips slidedown in " style="background: none;border-bottom:none">
                    <p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
                </div>
                <div class="upload_inner">
                    <div class="inner">
                        <div class="pic_box" id="frontimg">
                            <img src="images/card/sfz1.png" alt="">
                        </div>
                        <div class="gray_mask">
                            <div class="upload">
                                <a href="javascript:void(0)" id="upfront">上传</a>
                            </div>
                        </div>
                    </div>
                    <p class="picture_tips">身份证正面照</p>
                    <div class="inner">
                        <div class="pic_box" id="backimg">
                            <img src="images/card/sfz2.png" alt="">
                        </div>
                        <div class="gray_mask">
                            <div class="upload">
                                <a href="javascript:void(0)" id="upback">上传</a>
                            </div>
                        </div>
                    </div>
                    <p class="picture_tips">身份证反面照</p>
                    <div class="inner">
                        <div class="pic_box" id="handimg">
                            <img src="images/card/sc.png" alt="">
                        </div>
                        <div class="gray_mask">
                            <div class="upload">
                                <a href="javascript:void(0)" id="uphand">上传</a>
                            </div>
                        </div>
                    </div>
                    <p class="picture_tips">一手持身份证正面，一手持新银行卡正面照</p>
                </div>
                <div class="grid_02">
                    <a href="javascript:void(0)" class="ui button block rounded btn_01" id="next">下一步</a>
                </div>
            </div>
            <!-- UPLOAD_BOX END -->
        </article>
    </section>
</div>
