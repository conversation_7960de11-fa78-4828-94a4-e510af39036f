// 引流注册-设置交易密码
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#drainage_transactionPwd ";
    var ut = require("../common/userUtil");
    var weakpwdArr = ['111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999', "000000"];
    var tools = require("../common/tools");
    function init() {
        // 初始化键盘
        jpInit();
    }

    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //交易密码获得焦点
        appUtils.bindEvent($(_pageId + " #jymm11"), function () {
            if ($(_pageId + " #jymm").val() == "") {
                $(_pageId + " #jymm1").removeClass("unable");
            } else {
                $(_pageId + " #jymm1").removeClass("unable").addClass("active");
            }
            $(_pageId + " #jymm21").removeClass("active").addClass("unable");
            kaiqi("jymm");
        }, "click");
        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #jymm211"), function () {
            if ($(_pageId + " #jymm2").val() == "") {
                $(_pageId + " #jymm21").removeClass("unable");
            } else {
                $(_pageId + " #jymm21").removeClass("unable").addClass("active");
            }
            $(_pageId + " #jymm1").removeClass("active").addClass("unable");
            kaiqi("jymm2");
        }, "click");


        // 点击校验支付密码的合法性
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            var pwd1 = $(_pageId + " #jymm").val();
            var pwd2 = $(_pageId + " #jymm2").val();

            if (!checkTradePwd(pwd1, pwd2)) {
                return;
            } else {
                //密码加密/**/
                service.getRSAKey({}, function (data) {
                    if (data.error_no == "0") {
                        var modulus = data.results[0].modulus;
                        var publicExponent = data.results[0].publicExponent;
                        var endecryptUtils = require("endecryptUtils");
                        var jypwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, pwd1);
                        // 取到之前存取的绑卡信息
                        var bankAccInfo = appUtils.getSStorageInfo("userInfo");
                        var live_address = bankAccInfo.living_address.code.split(" ");
                        bankAccInfo.vocation_code = bankAccInfo.vocation_code.id;
                        bankAccInfo.income = bankAccInfo.year_income.money*10000 + '',//年收入
                        bankAccInfo.living_address_province = (live_address[0]);
                        bankAccInfo.living_address_city = (live_address[1]);
                        bankAccInfo.living_address_county = (live_address[2]);
                        bankAccInfo.trans_pwd = jypwd;
                        bankAccInfo.recommend = bankAccInfo.recommend;
                        bankAccInfo.vaild_date = bankAccInfo.vaild_date.replace(/\//g, "");
                        bankAccInfo.bad_credit = bankAccInfo.bad_credit.id;//不良诚信记录
                        bankAccInfo.actualisself = bankAccInfo.actualisself.id;//控制人
                        bankAccInfo.beneficiary = bankAccInfo.beneficiary.id;//受益人
                        bankAccInfo.cert_no = tools.hasLowercase(bankAccInfo.cert_no);//身份证校验
                        let setBankInfo = appUtils.getSStorageInfo("setBankInfo")
                        if(setBankInfo && setBankInfo.error_reason){
                            let idInfo = appUtils.getSStorageInfo("idInfo")
                            bankAccInfo = {...bankAccInfo,...idInfo};
                            bankAccInfo.error_reason = setBankInfo.error_reason;
                            appUtils.setSStorageInfo("errorMessage","1")
                            service.reqFun101081(bankAccInfo, saveCallBackFun, {"isLastReq": false,});
                        }else{
                            appUtils.setSStorageInfo("errorMessage","0")
                            service.reqFun101002(bankAccInfo, saveCallBackFun, {"isLastReq": false,});
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                    }
                }, {"isLastReq": false,});
            }

        });
    }

    // 销毁方法
    function destroy() {
        guanbi();
        $(_pageId + "input").val(""); // 清空所有的记录
        $(_pageId + " #jymm").val("");
        $(_pageId + " #jymm2").val("");
        $(_pageId + " #jymm1").text("请输入6位数字");
        $(_pageId + " #jymm21").text("请输入6位数字");
    }

    /**
     * 验证密码格式
     * pwd:面码
     * pwd_name:密码名 如支付密码
     * */
    function checkTradePwd(pwd, pwd2) {
        if (validatorUtil.isEmpty(pwd)) {
            layerUtils.iMsg(-1, "密码不能为空");
            return;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iMsg(-1, "请输入确认密码");
            return false;
        }
        if (!/^\d{6}$/.test(pwd)) {
            layerUtils.iMsg(-1, "密码为6位数字");
            return;
        }
        if (!/^\d{6}$/.test(pwd2)) {
            layerUtils.iMsg(-1, "确认密码为6位数字");
            return;
        }
        var temp_pwd = 0;
        for (var i = 0; i < pwd.length - 1; i++) {
            if (parseInt(pwd[i]) + 1 == parseInt(pwd[i + 1])) {
                temp_pwd++;
            }
        }
        if (temp_pwd == 5) {
//			layerUtils.iAlert("密码不能为顺增的6位数字");
            layerUtils.iMsg(-1, "请设置6位强密码");
            return;
        }
        for (var i = 0; i < weakpwdArr.length; i++) {
            var weakpwd = weakpwdArr[i];
            if (weakpwd.indexOf(pwd) > -1) {
//				layerUtils.iAlert("密码不能为连续数字");
                layerUtils.iMsg(-1, "请设置6位强密码");
                return;
            }
        }
        if (pwd2 !== (pwd)) {
            layerUtils.iMsg(-1, "两次输入密码不一致");
            return false;
        }
        return true;
    }

    // 初始化键盘
    function jpInit() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #jymm").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #jymm1").addClass("active");
                    }
                } else {
                    passflag = "请输入6位数字";
                    $(_pageId + " #jymm1").removeClass("active");
                }
                $(_pageId + " #jymm1").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #jymm").val(shuru);
                    $(_pageId + " #jymm1").text(passflag);
                }
                var shuru_2 = $(_pageId + " #jymm2").val();
                var passflag_2 = "";
                if (shuru_2) {
                    for (var i = 0; i < shuru_2.length; i++) {
                        passflag_2 += "*";
                    }
                    if (shuru_2.length == 1) {
                        $(_pageId + " #jymm21").addClass("active");
                    }
                } else {
                    passflag_2 = "请输入6位数字";
                    $(_pageId + " #jymm21").removeClass("active");
                }
                $(_pageId + " #jymm21").text(passflag_2);
                var len = shuru_2.length;
                if (len >= 6) {
                    shuru_2 = shuru_2.substring(0, 6);
                    $(_pageId + " #jymm2").val(shuru_2);
                    $(_pageId + " #jymm21").text(passflag_2);
                }
            } // 键盘的输入事件
        };
    }

    //开启键盘
    function kaiqi(eleId) {
        //调用键盘
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "drainage_transactionPwd";
        param["eleId"] = eleId;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //开户绑卡后的回调函数
    function saveCallBackFun(data) {
        var error_no = data.error_no;
        var error_info = data.error_info;
        if (error_no == "0") {
            var userInfo = ut.getUserInf()
            if(appUtils.getSStorageInfo("errorMessage") == '1'){
                common.setLocalStorage("mobileWhole", userInfo.mobileWhole);
                appUtils.setSStorageInfo("setBankInfo",{})
                layerUtils.iLoading(false);
             // 存储是否是异常客户-开户审核中
                appUtils.setSStorageInfo("is_open_acct_excp",'1')
                appUtils.pageInit("drainage/transactionPwd", "drainage/openAccountSuccess", {});
            }else{
                service.reqFun101020({}, function (data) {
                    var error_no = data.error_no;
                    if (error_no == "0") {
                        appUtils.setSStorageInfo("setBankInfo",{})
                        var results = data.results[0];
                        common.setLocalStorage("mobileWhole", results.mobileWhole);
                        var userInfo = ut.getUserInf()
                        results.message_push_flag = userInfo.message_push_flag
                        ut.saveUserInf(results);
                        layerUtils.iLoading(false);
                        appUtils.pageInit("drainage/transactionPwd", "drainage/openAccountSuccess", {});
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iMsg(-1, "网络繁忙,请重新登录");
                        appUtils.clearSStorage("_loginInPageCode");
                        appUtils.clearSStorage("_loginInPageParam");
                        appUtils.clearSStorage("_isLoginIn");
                        appUtils.clearSStorage();
                        common.gestureLogin();
                    }
                })
            }
            
        } else {
            layerUtils.iLoading(false);
            layerUtils.iAlert(error_info);
        }
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var transactionPwd = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = transactionPwd;
});
