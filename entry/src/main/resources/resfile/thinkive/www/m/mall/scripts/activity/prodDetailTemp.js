//活动 - 产品详情页模板
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        _pageId = "#activity_prodDetailTemp ";
    var external = require("external");
    var param = {};

    function init() {
        param = appUtils.getPageParam();
        if(param.fundCode) {
            $(_pageId + " .openBtn").show();
        } else {
            $(_pageId + " .openBtn").hide();
        }
        $(_pageId + " img").attr("src", require("gconfig").global.oss_url + "fund_filesystem/activity_prod/" + param.img);

    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .openBtn"), function () {
            var setParam = {
                "funcNo": "80002",
                "moduleName": "mall",
                "biz_code": "open_prodDetail",
                "param": {
                    fund_code: param.fundCode,
                    prod_sub_type: param.prodSubType
                }
            };
            external.callMessage(setParam);
        })


    }

    function destroy() {
        $(_pageId + " img").attr("src", "");
        $(_pageId + " .openBtn").hide();
        param = {};
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
