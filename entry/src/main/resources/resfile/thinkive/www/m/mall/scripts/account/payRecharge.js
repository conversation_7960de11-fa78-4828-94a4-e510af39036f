// 线下入金
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageId = "#account_payRecharge ";
    var ut = require("../common/userUtil");
    require("../common/clipboard.min.js");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var user;
    function init() {
        $(_pageId + " .custServiceTel").html(global.custServiceTel);
        getFundInfo(true);//获取汇款充值账户信息
        user = ut.getUserInf();
        var bankName = user.bankName;
        var bankCard = user.bankAcct;
        $(_pageId + " #bankName").html(bankName);
        $(_pageId + " #bankCard").html(bankCard.substring(bankCard.length - 4, bankCard.length));
        copyContent("hmCopy");
        copyContent("zhCopy");
        copyContent("khhCopy");
        copyContent("iphone_bank");
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #sure"), function () {
            $(_pageId + " #tit_hkcz").hide();
            $(_pageId + " #mask_hkcz").hide();
        })
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //点击取消
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            $(_pageId + " .pop_layer").hide();
        });
        //点击继续
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            tools.openBankApp(user.bankCode);
            $(_pageId + " .pop_layer").hide();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum("account/payRecharge")
        });
        //拨打电话
        appUtils.bindEvent($(_pageId + " .custServiceTel"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //打开手机银行
        // appUtils.bindEvent($(_pageId + " .iphone_bank"), function () {
        //     let account = $(_pageId + " #zh").text().trim();
        //     layerUtils.iConfirm(`已复制<span style="color:#e5443c">${account}</span>收款账号,去银行转账即可`, function () {
        //         tools.openBankApp(user.bankCode);
        //     }, function () {
        //     }, "继续", "取消");
        // });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .web_bank"), function () {
            layerUtils.iAlert("请登录" + user.bankName + "网站进行转账汇款");
        });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .counter_bank"), function () {
            layerUtils.iAlert("请您到" + user.bankName + "网点进行转账汇款");
        });
    }
    function getFundInfo(flag) {
        var jjbFundCode = appUtils.getSStorageInfo("jjbFundCode");
        service.reqFun102073({fund_code: jjbFundCode}, function (data) {
            if (data.error_no == "0") {
                var resutls =data.results[0];
                $(_pageId + " #hm").html(resutls.fund_accname);
                $(_pageId + " #hmCopy").attr("data-clipboard-text", resutls.fund_accname);
                $(_pageId + " #zh").html(resutls.fund_acc);
                $(_pageId + " #zhCopy").attr("data-clipboard-text", resutls.fund_acc);
                $(_pageId + " #iphone_bank").attr("data-clipboard-text", resutls.fund_acc);
                $(_pageId + " #khh").html(resutls.acc_bank);
                $(_pageId + " #khhCopy").attr("data-clipboard-text", resutls.acc_bank);
            } else {
                if(flag) {
                    layerUtils.iAlert(data.error_info);
                }
            }
        })
    }

    // 复制
    function copyContent(id) {
        // console.log(id)
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);
        clipboard.on('success', function (e) {
            // console.log(e)
            if(!e.text.trim()) {
                layerUtils.iAlert("复制失败，请重试", -1, function () {
                    getFundInfo(false)
                });
                return;
            }
            // console.log(id,e)
            if(id == "zhCopy" || id == "iphone_bank"){
                // console.log(e.text.trim())
                let account = e.text.trim();
                $(_pageId + " .account").text(account);
                $(_pageId + " .pop_layer").show();
                // layerUtils.iConfirm(`已复制<span style="color:#e5443c">${account}</span>收款账号,去银行转账即可`, function () {
                //     // $("#pop_tip_confirm").prepend('<div style="margin-top: 0.2rem;font-size: 0.18rem;font-weight: 600;">即将打开银行APP</div>')
                //     tools.openBankApp(user.bankCode);
                // }, function () {
                // }, "继续", "取消");
            }else{
                layerUtils.iAlert("复制成功，可粘贴");
            }
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .account").text('');
        $(_pageId + " #bankName").html("");
        $(_pageId + " #bankCard").html("");
        $(_pageId + " #hm").html("");
        $(_pageId + " #hmCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #zh").html("");
        $(_pageId + " #zhCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #iphone_bank").attr("data-clipboard-text", " ");
        $(_pageId + " #khh").html("");
        $(_pageId + " #khhCopy").attr("data-clipboard-text", " ");
    }


    var payRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = payRecharge;
});
