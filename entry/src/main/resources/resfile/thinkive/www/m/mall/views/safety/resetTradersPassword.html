<div class="page" id="safety_resetTradersPassword" data-pageTitle="重置交易密码" >
<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a id="getBack" class="icon_back icon_gray" operationType="1" operationId="getBack" operationName="返回"><span>返回</span></a>
				<h1 class="text_gray text-center">重置交易密码</h1>
			</div>
		</header>
		<article>
			<!-- TRADE_PASSWORD START -->
			<div class="bank_msg2 bank_msg">
			<div class="grid_03 grid_02 grid">
					<!--<div class="check_tips slidedown in">
						<p id="userInfo">您好！您正在为账户 重置登录密码。</p>
					</div>-->
					<div class="check_tips slidedown in " style="border-bottom: none;background: none">
						<p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
					</div>
					<div class="ui field text input_box2" id="pwd11">
						<label>新密码</label><!-- readonly="readonly" -->
						<input id="pwd" readonly="readonly"  type="password" maxlength="6"  class="ui input" placeholder="请输入6位数字" style="display:none"/>
						<div class="simulate_input no_border">
							<span class="unable" id="pwd1" style="padding: 0;">请输入6位数字</span>
						</div>
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text input_box2" id="pwd211">
						<label>确认密码</label><!-- -->
						<input id="pwd2" readonly="readonly"  type="password" maxlength="6" class="ui input" placeholder="请输入6位数字" style="display:none"/>
						<div class="simulate_input no_border">
							<span class="unable" id="pwd21" style="padding: 0;">请输入6位数字</span>
						</div>
					</div>
				</div>
			</div>
			<div class="login_password2">
				<div class="grid_03 grid_02 grid  pop_tips">
					<div class="pop_view" id="pop_view" style="visibility:hidden;">
						<p id="big_show_bank">1234 1234 1111 1111 111</p>
					</div>
					<div class="grid_03 grid_02 grid">
						<div class="ui field text input_box2">
							<label>银行卡号</label>
							<input  custom_keybord="0" id="bankCard" maxlength="19"  type="tel" class="ui input" placeholder="" />
						</div>
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text input_box2">
						<label>真实姓名</label>
						<input  custom_keybord="0" id="realName" type="text" maxlength="20" class="ui input" placeholder="" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text input_box2">
						<label>身份证号</label>
						<input  custom_keybord="0" id="card" type="text" maxlength="18" class="ui input" placeholder="" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text input_box2">
						<label>预留手机号</label>
						<input custom_keybord="0" id="phoneNum" type="tel" maxlength="11"  class="ui input" placeholder="" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text input_box2" id="yzmBox">
						<label>验证码</label>
						<input  custom_keybord="0" type="tel" id="yzm" maxlength="6" class="ui input code_input" placeholder="" />
						<a herf="javascript:void(0);"  id="getYzm" >获取验证码</a>
					</div>
				</div>
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="weihao"   style="display:none" ><dd>
					</dl>
				</div>
				<!-- 语音验证码 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
					</dl>
				</div>
				<!-- 语音验证码 -->

			</div>
		    <div class="grid_02 mt20">
				<a herf="javascript:void(0);" id="submit" class="ui button block rounded btn_register pop in" operationType="1" operationId="submit" operationName="完成">完成</a>
			</div>
			<!-- TRADE_PASSWORD END -->
		</article>
	</section>
</div>
