define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var serviceConstants = require("../constants/serviceConstants");
    var layerUtils = require("layerUtils");
    //用户信息
    var ut = {};
    //获取用户信息
    ut.getUserInf = function () {
        var userInfo = appUtils.getSStorageInfo("user");
        if(userInfo) return userInfo;
        return '';
    };
    //删除用户信息
    ut.removeUserInf = function () {
        appUtils.removeStorage("isAuthenticated");
        appUtils.removeStorage("userInfo");
    };
    //存储用户信息
    ut.saveUserInf = function (userInfo) {
        //新晋金宝获取用户信息
        // appUtils.setSStorageInfo("userInfo",userInfo);
        if (userInfo.bankName && userInfo.bankName == '农村信用合作联社') userInfo.bankName = '山西农信';
        appUtils.setSStorageInfo(serviceConstants.session.USER, userInfo);
    }

    //判断用户开户状态
    ut.getFundStatus = function () {
        if (ut.getUserInf().openFlag == "1") {
            return true;
        }
        return false;
    }
    //判断是否上传身份证照片
    ut.getUploadStatus = function () {
        if (ut.getUserInf().idCardUploadFlag == "1") { // 1 已上传 0 未上传
            return true;
        }
        return false;
    }
    //判断注册未绑卡用户
    ut.isNewCust = function () {
        if (ut.getUserInf().isNewCust == "1") {
            return true
        }
        return false
    }

    // 是否绑卡
    ut.hasBindCard = function (_pageCode, callback) {
        var userInfo = ut.getUserInf();
        if (userInfo && userInfo.bankAcct) {
            return true
        }
        // let cardBindingAudit
        let is_open_acct_excp = appUtils.getSStorageInfo("is_open_acct_excp")
        //异常客户
        if(is_open_acct_excp == "1"){
            // layerUtils.iConfirm("您的资料暂未审核通过，待审核通过后，您可进行充值购买", function () {
            //     return false;
            // }, function () {
            //     if(callback) callback();
            //     return false;
            // },"确定");
            layerUtils.iAlert("您的资料暂未审核通过，待审核通过后，您可进行充值购买");
            return false;
        }
        if(userInfo.isJjsCust == "1") { //isJjsCust 1是晋金所用户 0不是
            appUtils.clearSStorage("idCardInfo");
            appUtils.clearSStorage("bankAccInfo");
            let operationId = 'bindBankCard';
            layerUtils.iConfirm("您还未完成绑卡", function () {
                appUtils.pageInit(_pageCode, "drainage/openAccount", {});
                return false;
            }, function () {
                if(callback) callback();
                return false;
            }, "绑卡", "取消",operationId);
            return false;
        }
        let operationId = 'bindBankCard';
        layerUtils.iConfirm("您还未完成绑卡", function () {
            appUtils.pageInit(_pageCode, "account/setBankCard", {});
            return false;
        }, function () {
            if(callback) callback();
            return false;
        }, "绑卡", "取消",operationId);
        return false;
    }

    /*** end ************************************************/
    // 暴露对外的接口
    module.exports = ut;
});
