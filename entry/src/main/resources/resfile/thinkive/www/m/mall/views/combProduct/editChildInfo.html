<div class="page" id="combProduct_editChildInfo" data-pageTitle="编辑资料" data-refresh="true"
     style="-webkit-overflow-scrolling : touch;">
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center">编辑资料</h1>
                <a href="javascript:void(0);" id="cancelorderBtn" style="display: none" class="btn_filter" operationType="1" operationId="cancelorderBtn" operationName="撤单">撤单</a>
                <a href="javascript:void(0);" id="overtimeBtn" style="display: none;color:#999" class="btn_filter" operationType="1" operationId="overtimeBtn" operationName="撤单">撤单</a>
            </div>
        </header>
        <article style="padding-bottom:0">
            <div class="header">
                <ul>

                    <li class="m_text_center" :src="{{childrenInfo.avatar_url ? childrenInfo.avatar_url : '../mall/images/icon_app.png'}}">
                        <img src="../mall/images/icon_app.png" alt="" style="width: 0.6rem; height: 0.6rem; border-radius: 50%;">
                    </li>
                    <li class="m_text_center">
                        <span class="show_dio" style="color: rgb(49, 158, 242);" operationType="1" operationId="show_dio" operationName="更换头像">更换头像</span>
                    </li>
                </ul>
            </div>
            <div class="info">
                <ul>
                    <li class="ui field main_flxe vertical_center">
                        <span>昵称</span>
                        <input type="text" placeholder="请输入昵称" id='nick_name' class="ui input" value="" maxlength="10">
                    </li>
                    <li class="ui field main_flxe vertical_center baby" style="display: none;"> 
                        <span>生日</span>
                        <input type="text" placeholder="请选择宝贝生日" readonly="readonly" id='birthday' class="ui input" value="">
                    </li>
                    <li class="ui field main_flxe vertical_center baby" style="display: none;">
                        <span>爱好</span>
                        <input type="text" placeholder="请输入宝贝爱好" id='hobby' custom_keybord="0" class="ui input" maxlength="10">
                    </li>
                    <li class="ui field main_flxe vertical_center target" style="display: none;">
                        <span>攒钱目标</span>
                        <input type="text" placeholder="请输入攒钱目标" id='target' custom_keybord="0" class="ui input" maxlength="10">
                    </li>
                </ul>
            </div>
            <footer operationType="1" operationId="overtimeBtn" operationName="确认">
                确 &nbsp;&nbsp; 认
            </footer>
        </article>
    </section>
</div>
