// 银行换卡-第二步
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#bank_changeBindCard2 ";
    var sms_mobile = require("../common/sms_mobile");
    var _pageCode = "bank/changeBindCard2";
    var bank_channel_code = "Y01";
    var _bank_name;
    var common = require("common");
    function init() {
        sms_mobile.init(_pageId);
    }

    // 绑定事件
    function bindPageEvent() {
        //输入银行卡号
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 预留手机号码
        appUtils.bindEvent($(_pageId + " #yhmPhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 验证码 控制全文字
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            // 控制全数字输入
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);


            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");

        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");

        // 银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            bankDistinguish();
        }, "blur");

        // 点击下一步
        appUtils.bindEvent($(_pageId + " #complete_btn"), function () {
            var bankCard = $(_pageId + " #bankCard").val();  // 银行卡号
            bankCard = bankCard.replaceAll(" ", "");
            var yhmPhone = $(_pageId + " #yhmPhone").val();  // 银行预留手机号码
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码

            if (validatorUtil.isEmpty(bankCard)) {
                layerUtils.iMsg(-1, "银行卡号不能为空");
                return;
            }
            if (!validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确银行卡号");
                return;
            }
            if (!validatorUtil.isMobile(yhmPhone)) {
                layerUtils.iMsg(-1, "请输入正确手机号码");
                return;
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }

            var bank_code = $(_pageId + " #bankname").attr("bank_code");
            var param = {
                bank_channel_code: bank_channel_code,
                bank_code:bank_code,
                bank_acct_no: bankCard,
                bank_reserved_mobile: yhmPhone,
                sms_code: verificationCode,
                sms_mobile: yhmPhone,
                bank_name:_bank_name,
            }
            reqFun151002(param);

        });

        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var yhmPhone = $(_pageId + " #yhmPhone").val();
                var bankCard = $(_pageId + " #bankCard").val();
                if (!validatorUtil.isBankCode(bankCard)) {
                    layerUtils.iMsg(-1, "请输入正确银行卡号");
                    return;
                }
                if (validatorUtil.isEmpty($(_pageId + " #bankname").html())) {
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }
                if (validatorUtil.isEmpty($(_pageId + " #bankname").attr("bank_code"))) {
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }
                if (!validatorUtil.isMobile(yhmPhone)) {
                    layerUtils.iMsg(-1, "请输入正确手机号码");
                    return;
                }
                // 获取验证码
                var param = {
                    "mobile_phone": yhmPhone,
                    "type": common.sms_type.bankChangeCard,
                    "send_type": "0",
                };
                sms_mobile.sendPhoneCode(param);
            }
        });

        // 点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
    }

    // 失去焦点时候验证银行限额 银行名称
    function bankDistinguish() {
        $(_pageId + " #pop_view").css("visibility", "hidden");
        $(_pageId + " #big_show_bank").html("");
        var bankCard = $(_pageId + " #bankCard").val();
        bankCard = bankCard.replaceAll(" ", "")
        if (validatorUtil.isEmpty(bankCard)) {
            layerUtils.iMsg(-1, "银行卡号不能为空");
            return;
        }
        if (!validatorUtil.isBankCode(bankCard)) {
            $(_pageId + " #oneMoney").html("");
            $(_pageId + " #drxe").html("");
            layerUtils.iMsg(-1, "请输入正确银行卡号");
            return;
        }
        var param = {
            "bank_channel_code": bank_channel_code,
            "bank_acct": bankCard,
        };

        service.reqFun151114(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {
                $(_pageId + " .place").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    _bank_name = result.bank_name;
                    var bank_code = result.bank_code;
                    var single_limit = result.single_limit;
                    var day_limit = result.day_limit;
                    if (parseFloat(day_limit) > 0) {
                        $(_pageId + " .place").show();
                        $(_pageId + " #drxe").html(day_limit + "元");
                    } else if (parseFloat(day_limit) == 0) {
                        $(_pageId + " .place").hide();
                    } else {
                        $(_pageId + " .place").show();
                        $(_pageId + " #drxe").html("不限");
                    }
                    if (parseFloat(single_limit) > 0) {
                        $(_pageId + " .place").show();
                        $(_pageId + " #oneMoney").html(single_limit + "元");
                    } else if (parseFloat(single_limit) == 0) {
                        $(_pageId + " .place").hide();
                    } else {
                        $(_pageId + " .place").show();
                        $(_pageId + " #oneMoney").html("不限");
                    }
                    // 识别出来填写银行卡编号 和银行名称
                    $(_pageId + " #bankname").html(_bank_name);
                    $(_pageId + " #bankname").attr("bank_code", bank_code);
                } else {
                    $(_pageId + " .place").hide();
                    $(_pageId + " #bankname").html("");
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }

            } else {
                // 识别失败时候 去掉银行信息
                $(_pageId + " #oneMoney").html("不限");
                $(_pageId + " #drxe").html("不限");
                $(_pageId + " #bankname").html("");
                $(_pageId + " .place").hide();
                layerUtils.iMsg(-1, error_info);
            }
        });
    }

    //更换绑定卡
    function reqFun151002(param) {
        service.reqFun151002(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            sms_mobile.init(_pageId);
            $(_pageId + " #verificationCode").val("");
            if (error_no == "0") {
                appUtils.pageInit(_pageCode, "bank/changeBindCardResult");
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    function destroy() {
        sms_mobile.destroy(_pageId);
        $(_pageId + " .place").hide();
        $(_pageId + " input").val("");
        $(_pageId + " #bankname").html("");
        _bank_name = "";
    }

    // 系统返回
    function pageBack() {
        appUtils.pageBack();
    }

    var setBankCardInfo = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setBankCardInfo;
});
