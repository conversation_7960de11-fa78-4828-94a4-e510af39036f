/**
 * 模块名：晋金财富红包雨页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        SHIscroll = require("shIscroll"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        common = require("common"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil");
    var external = require("external");
    var reward_activity_id;
    /* 常量 */
    var _pageCode = "activity/redRain", _pageId = "#activity_redRain";
    var timer; // 等待时间定时器
    var t;
    var timeWait = 15; // 红包持续时间
    var _timeWait = 19; // 倒计时
    var hbTotal = 20; // 红包总个数
    var minHbScore = 5; // 单个红包最小值
    var maxHbScore = 5; // 单个红包最大值
    var hbScore; // 固定积分单个红包积分数量
    var alreadHasHbNum = 0; // 已领红包个数
    var alreadHasScore = 0; // 已领积分
    var hbSpread; // 红包落下间隔    红包总数/时间
    var win;
    var num = 0; // 红包下落个数
    var numz = 4; //准备时间
    var isJoinActivity = false; // 是否参与活动
    var state;
    var errorObject;
    var rewardType = "1"; // 红包积分类型  固定积分0 随机积分 

    // 红包雨
    $.extend({
        tipsBox: function (options) {
            options = $.extend({
                obj: null,  //jq对象，要在那个html标签上显示
                str: "+1",  //字符串，要显示的内容;也可以传一段html，如: "<b style='font-family:Microsoft YaHei;'>+1</b>"
                startSize: "26px",  //动画开始的文字大小
                endSize: "40px",    //动画结束的文字大小
                interval: 1000,  //动画时间间隔
                color: "#fff",    //文字颜色
                callback: function () { }    //回调函数
            }, options);
            $("body").append("<span class='num'>" + options.str + "</span>");
            var box = $(".num");
            var left = options.obj.offset().left + options.obj.width() / 2;
            var top = options.obj.offset().top - options.obj.height();
            box.css({  
                "position": "absolute",
                "left": left + "px",
                "top": top + "px",
                "z-index": 100001,
                "font-size": options.startSize,
                "line-height": options.endSize,
                "color": options.color,
                "font-weight": "300%"
            });
            box.animate({
                "font-size": options.endSize,
                "opacity": "0",
                "top": top - parseInt(options.endSize) + "px"
            }, options.interval, function () {
                box.remove();
                options.callback();
            });
        }
    });
    /**
     * 初始化
     */
    function init() {
        win = (parseInt($(_pageId + " .couten").css("width"))) - 60;
        $(_pageId + " .couten").css("height", $(document).height());
        $(_pageId + " .backward").css("height", $(document).height());
        $(_pageId + " #getScoreResult .score").html(alreadHasScore);
        $(_pageId + " .backward").css("display", "none");
        $(_pageId + " li").css({});
        reward_activity_id = appUtils.getPageParam("reward_activity_id");
        // reward_activity_id = "45";

        let params = {
            activity_id: reward_activity_id,
        };
        service.reqFun108022(params, function (data) {
            errorObject = data;
            if (data.error_no == 0) {
                var results = data.results[0];
                state = results.state;
                if (state == "2") {
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                }
                if (state == "3") {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                }
                initcallback(results);
            } else {
                // isJoinActivity = true;
                // layerUtils.iAlert(data.error_info);
            }

        });
        
    }

    // 15s 20个 5分
    function initcallback(data) {
        timeWait = data.duration;
        hbTotal = data.amount; // 红包总个数
        hbScore = data.points_amount; // 固定积分单个红包值
        minHbScore = data.points_min; // 单个红包最小值
        maxHbScore = data.points_max; // 单个红包最大值
        hbSpread = Math.floor(Math.floor(timeWait) * 1000 / parseInt(hbTotal));
        _timeWait = Math.floor(timeWait) + 4;
        rewardType = data.reward_type;
    }
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //准备
        appUtils.bindEvent($(_pageId + " #startUp"), function () {
            startUp();
        });

        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });

        // 结果返回
        appUtils.bindEvent($(_pageId + " #resultBtn a"), function () {
            $(_pageId + " .result").css("display", "none");
            $(_pageId + " .backward").css("display", "none");
            $(_pageId + " #getScoreResult").css("display", "none");
            isJoinActivity = true;
            pageBack();
        });

    }

    /*
 * 返回
 */
    function pageBack() {
        appUtils.pageBack();
    }


    function add() {
        let isClick = true; // 单个红包只能点击一次，多次点击只算一次结果
        var hb = parseInt(Math.random() * (5 - 1) + 1);
        var Wh = Math.floor(Math.random() * (70 - 60 + 2) + 60);
        var Left = parseInt(Math.random() * (win - 0) + 0);
        var rot = (parseInt(Math.random() * (45 - (-45)) - 45)) + "deg";  // TODO：旋转角度，看是否需要
        num++;
        if (_timeWait) {
            $(_pageId + " .couten").append("<li data-disabled='1' class='li" + num + "' ><a href='javascript:;'><img src='../mall/images/activity/hby/hb_" + hb + ".png'></a></li>");
        }
        $(_pageId + " .li" + num).css({
            "left": Left,
            "z-index": 9999
        });
        $(_pageId + " .li" + num + " a img").css({
            "width": Wh,
        });
        $(_pageId + " .li" + num).animate({ 'top': $(window).height() + 20 }, 5000, function () {
            //删掉已经显示的红包
            this.remove()
        });
        // $(_pageId + " .li" + num).css({
        //     // "transform": "rotate(" + 180 + "deg)",
        //     "animation":"qq 5s .5s ease"

        // })
        //点击红包的时候弹出对应积分
        $(_pageId + ` .li${num}`).click(function () {
            // console.log($(this));
            var hbNum = 0; 
            if (rewardType == "0") { // 固定积分
                hbNum = Math.floor(hbScore);
            } else if (rewardType == "1") { // 随机积分
                var min = Math.floor(minHbScore);
                var max = Math.floor(maxHbScore);
                hbNum = Math.floor(Math.random() * (max - min + 1)) + min;
            }
            $(this).css({"display": "none"})
          
            if (isClick) {
                $.tipsBox({
                    obj: $(".mo"),
                    str: `<b style='font-family:Microsoft YaHei;'>+${hbNum}</b>`,
                    callback: function () {
                    }
                });
                alreadHasScore = alreadHasScore + hbNum; // 统计已领积分
                alreadHasHbNum++;
                $(_pageId + " #getScoreResult .score").html(alreadHasScore);
                isClick = !isClick;
            }
        });
        hbSpread = Math.floor(Math.floor(timeWait) * 1000 / parseInt(hbTotal));
        t = setTimeout(add, hbSpread);
        if (!_timeWait) {
            t && clearTimeout(t);
        }

    }

    // 准备
    function startUp() {
        // console.log(timeWait,hbTotal, numz);
        if (state == "2") {
            layerUtils.iMsg(-1, "活动尚未开始！", 2);
            return;
        }
        if (state == "3") {
            layerUtils.iMsg(-1, "活动已结束！", 2);
            return;
        }
        if (isJoinActivity) {
            layerUtils.iAlert("您已经参与过该活动");
            return;
        }
        if (errorObject && errorObject.error_no != 0) {
            layerUtils.iAlert(errorObject.error_info);
            return;
        }
        tools.clickPoint(_pageId,_pageCode,'startUp',reward_activity_id);
        $(_pageId + " .backward").css("display", "block");
        numz = 4; // 倒计时
        backward();
        setTimeout(add, 3000);
        timer = setInterval(() => {
            _timeWait--;
            if (_timeWait <= timeWait) {
                $(_pageId + " .wait").css("display", "block");
                $(_pageId + " #getScoreResult").css("display", "block");
                $(_pageId + " .wait").html(`${_timeWait}秒`);
            } else {
                $(_pageId + " .wait").html();
            }

            if (!_timeWait) {
                clearInterval(timer);
                $(_pageId + " .wait").css("display", "none");
                $(_pageId + " .result").css("display", "block");
                $(_pageId + " #resultMsg").html(`获得${alreadHasScore}积分`);
                $(_pageId + " .couten li").css("display", "none");
                end();
            }
        }, 1000)
    }

    function backward() {
        numz--;
        // console.log(numz);
        if (numz > 0) {
            $(_pageId + " .backward span").html(numz);
            setTimeout(backward, 1000)
        } else {
            $(_pageId + " .backward span").remove();
        }
       
    }

    /**
     * 销毁
     */
    function destroy() {
        // console.log("销毁");
        // $(_pageId + " .activity_pop_layer").hide();
        service.destroy();
        // $(_pageId + " #pointsFor").hide();
        // $(_pageId + " #share").hide();
        // $(_pageId + " #rewardResult .sureBtn span").html("");
        // $(_pageId + " .btn h3").html("");
        // $(_pageId + " .btn .fundLuckdraw_btn img").attr("src", '');
        isJoinActivity = false;
        errorObject = null;
        timeWait = 15; // 红包持续时间
        _timeWait = 19; // 倒计时
        alreadHasHbNum = 0; // 已领红包个数
        alreadHasScore = 0; // 已领积分
        num = 0; // 红包下落个数
        numz = 4; //准备时间
        $(_pageId + " .result").css("display", "none");
        $(_pageId + " .backward").css("display", "none");
        $(_pageId + " #getScoreResult").css("display", "none");
        t && clearTimeout(t);
        timer && clearInterval(timer);
    };

    function end() {
        // reward_activity_id = "45";
        var params = {
            activity_id: reward_activity_id,
            amount: alreadHasHbNum.toString(),
            points: alreadHasScore.toString()
        }
        service.reqFun108023(params, function (data) {
            if (data.error_no == 0) {
                isJoinActivity = true;
            } else {
                layerUtils.iAlert(data.error_info);
            }
            // initcallback(data);
        });
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
