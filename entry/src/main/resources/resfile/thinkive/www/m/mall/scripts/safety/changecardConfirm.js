// 手机注册
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_changecardConfirm ",
        _pageCode = "safety/changecardConfirm";
    var mobile = "";
    var changeCardMess;//新旧银行卡信息
    var userInfo;
    var ut = require("../common/userUtil");
    var sms_mobile = require("../common/sms_mobile");

    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        $(_pageId + " #verificationCode").val("");
        service.reqFun101026({}, function (data) {
            if (data.error_no == "0") {
                changeCardMess = data.results[0];
                var bank_acct = changeCardMess.bank_acct;
                bank_acct = bank_acct.substr(0, 4) + "****" + bank_acct.substr(-4, 4);
                var bank_reserved_mobile = changeCardMess.bank_reserved_mobile;
                var org_bank_acct = changeCardMess.org_bank_acct;
                org_bank_acct = org_bank_acct.substr(0, 4) + "****" + org_bank_acct.substr(-4, 4);
                $(_pageId + " #bankCard").val(org_bank_acct);
                $(_pageId + " #newbankCard").val(bank_acct);
                $(_pageId + " #yhmPhone").val(bank_reserved_mobile);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //绑定事件
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            mobile = $(_pageId + " #yhmPhone").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            if ($code.attr("data-state") == "false") {
                return;
            }

            var param = {
                mobile_phone: changeCardMess.bank_reserved_mobile,
                type: common.sms_type.changeCard,
                send_type: "0"
            };
            sms_mobile.sendPhoneCode(param);

        });
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var sms_code = $(_pageId + " #verificationCode").val();
            var bankCard = $(_pageId + " #bankCard").val();
            if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                layerUtils.iMsg(-1, "请先获取验证码");
                return;
            }
            if (sms_code == "") {
                layerUtils.iMsg(-1, "请输入验证码");
                return;
            }

            var changCardCallback = function (dataResult) {
                if (dataResult.error_no == "0") {//0成功，其它失败
                    service.reqFun1100007({}, function (data) {
                        if (data.error_no == "0") {
                            var results = data.results[0];
                            ut.saveUserInf(results);
                        }
                    });
                    appUtils.pageInit("safety/kjnewcardinfo", "safety/kjsetBankCardSuccess");
                } else {
                    sms_mobile.clear(_pageId);
                    service.reqFun101028({type: "0"}, function (data) {
                        if (data.error_no == "0") {
                            var results = data.results;
                            if (results.length == 0) {
                                layerUtils.iLoading(false);
                                return;
                            }
                            /*更新换卡标识*/
                            service.reqFun101029({"id": results[0].id}, function () {

                            });
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info)
                        }
                    }, {isLastReq: false})
                    layerUtils.iAlert(dataResult.error_info);//把失败的消息置为已读
                }
            }
            var param = {
                // userInfo
                org_bank_code: changeCardMess.org_bank_code,
                org_bank_acct: changeCardMess.org_bank_acct,
                org_bank_reserved_mobile: changeCardMess.org_bank_reserved_mobile,
                bank_acct: changeCardMess.bank_acct,
                bank_code: changeCardMess.bank_code,
                cert_no: userInfo.identityNum,
                bank_reserved_mobile: changeCardMess.bank_reserved_mobile,
                sms_mobile: changeCardMess.bank_reserved_mobile,
                sms_code: sms_code,
            }
            service.reqFun101017(param, changCardCallback);
        });

    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        sms_mobile.destroy(_pageId);
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        $(_pageId + " .place").hide();
        service.destroy();
        $(_pageId + " #getYzm").removeAttr("style");
        $(_pageId + " input").attr("value", "");
        $(_pageId + " .sure_box").hide();
        $(_pageId + " #bankname").attr("bank_no", "");
        $(_pageId + ' #weihao').hide();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
