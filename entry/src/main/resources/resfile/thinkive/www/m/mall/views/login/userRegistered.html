<div class="page" id="login_userRegistered" data-pageTitle="注册" data-refresh="true">
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a id="toLogin" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
				<h1 class="text_gray text-center">注册</h1>
				<a id="kefu" operationType="1" operationId="kefu" operationName="客服" href="javascript:void(0)" class="coustomer-service-icon" >
					<img src = "./images/customerService.png" >
				</a>
			</div>
		</header>
		<article class="no_padding" style="padding-bottom: 0">
			<!-- <div class="van-overlay" style="z-index: 2001;">
				<div style="display:none" id="iframe_pdf"></div>
			</div> -->
			<div class="register_box">
				<!-- <div class="grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label class="short_label">* 手机号码</label>
						<input custom_keybord="0" id="phoneNum" maxlength="11" type="tel" class="ui input" placeholder="" />
					</div>
				</div> -->
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2">
						<div class="ui label">
							<img src="front/images/label_01.png" width="12" alt="">
						</div>
						<input type="text"  class="ui input" custom_keybord="0" id="phoneNum" maxlength="11" placeholder="手机号"/>
						<!-- <a id="getYzm" class="new_code"  data-state="true">获取验证码</a> -->
						<span id="getYzm" operationType="1" operationId="getYzm" operationName="获取验证码" data-state="true">获取验证码</span>
					</div>
				</div>
				<!-- 手机提示 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="phoneShow"   style="display:none;color:red;margin-left:0.15rem">如收不到短信 <dd>
					</dl>
				</div>
				
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2">
						<div class="ui label">
							<img src="front/images/label_03.png" width="12" alt="">
						</div>
						<input type="text"  class="ui input" custom_keybord="0" id="verificationCode" maxlength="6" placeholder="验证码"/>
						
					</div>
				</div>
				<div id="registerDig" style="display:none" class="pop_layer flex_center main_flxe">
					<div id="mpanel" class="flex">
						
					</div>
				</div>
				<!-- 手机提示 -->
				<!-- <div class="grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label class="short_label">* 登录密码</label>
						<input custom_keybord="0" type="password"  id="pwd1" maxlength="16"  class="ui input" placeholder="6-16位字母、数字、字符组合" />
					</div>
				</div> -->
				<!-- <div class="grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label style="width:0.9rem;">* 确认密码</label>
						<input custom_keybord="0" type="password" id="pwd2" maxlength="16"   class="ui input" placeholder="请确认密码" />
					</div>
				</div> -->
				<!-- <div class="grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label>&nbsp;&nbsp;邀请人手机号</label>
						<input custom_keybord="0"    type="tel"   id="yqrPhoneNum" maxlength="11" class="ui input" placeholder="选填" />
					</div>
				</div> -->
				<!-- 邀请码 -->
				<!-- <div class="grid_02 grid" style="display:none" id = "labelId_div">
                    <div class="ui field text rounded input_box2">
                        <label>&nbsp;&nbsp;* 邀请码</label>
                        <input custom_keybord="0" type="tel"   id="labelId" maxlength="11" class="ui input" />
                    </div>
                </div> -->
				<!-- <div class="grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label class="short_label" style="width: 1.1rem">* 图形验证码</label>
						<input custom_keybord="0" id="tuxingCode" type="text" maxlength="4" class="ui input code_input" placeholder=""/>
						<a class="pic_code" id="getCode" data-state="true"  style="padding:0 0;width:82px;"><img src="" alt=""></a>
					</div>
				</div> -->
				<!-- <div class="grid_02 grid">
					<div class="ui field text rounded input_box2" id="yzmBox">
						<label class="short_label">* 验证码</label>
						<input custom_keybord="0" id="verificationCode"  type="tel"  maxlength="6" class="ui input code_input" placeholder=""/>
						<a id="getYzm" data-state="true">获取验证码</a>
					</div>
				</div> -->

				<!-- 语音验证码 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode" class="m_paddingLeft_30" style="display:none">如收不到短信 &nbsp;&nbsp;<span operationType="1" operationId="getTalk" operationName="获取语音验证码" id="getTalk" style="color:blue">语音获取</span><dd>
					</dl>
				</div>
			    <div class=" grid_02 text-center">
				    <div class="radio_box" style="display:none">
						<div class="ui radio">
							<input type="radio" id="input_radio2">
							<label id="isChecked" operationType="1" operationId="isChecked" operationName="点击协议">我已阅读并同意签署
							</label>
							<span class="agreement_list deal_box"></span>
							<!-- <span class="m_agreement_color click_agreement">
								《相关协议》
							</span> -->
						</div>
					</div>
				</div>
				<!-- 协议相关弹窗 -->
				<!-- <div class="agreement_layer display_none">
					<div class="agreement_popup in">
						<div class="agreement_popup_header">
							<div class="new_close_btn"></div>
							<h3>相关协议</h3>
						</div>
						<ul class="agreement_list flex vertical_line"></ul>
					</div>
				</div> -->
				<div class="grid_06 drainHide">
					<a href="javascript:void(0);" class="ui button block rounded btn_login " operationType="1" operationId="registered" operationName="注册" id="registered">注&nbsp;&nbsp;册</a>
				</div>
			    <!-- <div class="grid_02">
					<a id="registered" class="ui button block rounded btn_register">注册</a>
				</div> -->
			</div>
			<!-- REGISTER_BOX END -->
		</article>
	</section>
</div>
