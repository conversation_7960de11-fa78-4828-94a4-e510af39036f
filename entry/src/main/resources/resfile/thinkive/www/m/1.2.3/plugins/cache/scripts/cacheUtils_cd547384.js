/*创建时间hSea 2015-12-31 15:36:59 PM */
define(function(require,exports,module){function a(a,b,c,d){switch(d=d||"h5_session",b=b||"",c="undefined"==typeof c||null===c?1:c,d){case"h5_session":case"h5_local":f.setItem(a,b,c,d);break;case"app_session":case"app_local":g?g.setItem(a,b,c,d):require.async("cacheUtils4App",function(module){g=module,g.setItem(a,b,c,d)})}}function b(a,b){switch(b=b||"h5_session"){case"h5_session":case"h5_local":f.removeItem(a,b);break;case"app_session":case"app_local":g?g.removeItem(a,b):require.async("cacheUtils4App",function(module){g=module,g.removeItem(a,b)})}}function c(a,b){b=b||"h5_session";var c=null;switch(b){case"h5_session":case"h5_local":f.getItem(a,b);break;case"app_session":case"app_local":g?g.getItem(a,b):require.async("cacheUtils4App",function(module){g=module,g.getItem(a,b)})}return c}function d(){var a=1e3*i.cacheScanInterval,c=function(a){if(null===a||"null"===a||""===a)return null;try{var b=h.enc.Utf8.parse("iloveyou"),c=h.enc.Utf8.parse("iloveyou"),d=h.enc.Base64.parse(a),e=h.AES.decrypt({ciphertext:d},b,{iv:c,mode:h.mode.CBC});return JSON.parse(e.toString(h.enc.Utf8))}catch(f){return{time:0,value:a}}},e=function(a){var b=0;return a&&a.hasOwnProperty("time")&&a.hasOwnProperty("value")&&(b=+a.time,b=isNaN(b)?0:b),b},f=function(a){var d=null;"h5_session"===a?d=window.sessionStorage:"h5_local"===a&&(d=window.localStorage);for(var f in d)if(d.hasOwnProperty(f)){var g=c(d.getItem(f)),h=e(g);if(0!==h){var i=g.createDate;((new Date).getTime()-i)/1e3>h&&b(f,a)}}};j=window.setTimeout(function(){f("h5_session"),f("h5_local"),d()},a)}function e(){window.clearTimeout(j)}var f=require("cacheUtils4H5"),g=(require("layerUtils"),null),h=require("aes"),i=require("gconfig"),j=null;d();var k={setItem:a,removeItem:b,getItem:c,startCacheScanner:d,stopCacheScanner:e};module.exports=k});
/*创建时间 2015-12-31 15:36:59 PM */