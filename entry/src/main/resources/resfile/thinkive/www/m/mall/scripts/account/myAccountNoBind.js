// 我的-未绑卡
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageUrl = "account/myAccountNoBind",
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#account_myAccountNoBind";
    _page_code = "account/myAccountNoBind";
    var ut = require("../common/userUtil");
    var layerUtils = require("layerUtils");
    var tools = require("../common/tools");
    let userChooseVerson,scene_code,mobileWhole
    function init() {
        //获取当前用户选中的版本
        userChooseVerson = common.getLocalStorage("userChooseVerson"); //判断用户是否登陆过
        //获取当前用户初始版本
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        mobileWhole = common.getLocalStorage("mobileWhole"); //页面版本类型 1标准版 X版
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        scene_code = scene_code ? scene_code : '';
        mobileWhole = mobileWhole ? mobileWhole : '';
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #phoneNum").html(ut.getUserInf().mobile);
        // ut.hasBindCard(_pageUrl);  // TODO:未绑卡提醒
        tools.hasNewMsg(_pageId); // 是否有新消息
        tools.footerShow(_pageId,userChooseVerson,scene_code,mobileWhole);
        if (ut.getUserInf().custLabelCnlCode === "yh") {
            $(_pageId + " .bankDeposit_region").hide();
            $(_pageId + " .jjInclusive_region").hide();
            $(_pageId + " .jjHighEnd_region").hide();
            $(_pageId + " .yuanhui_region").show();
        } else if (ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) || ut.getUserInf().custLabelCnlCode == "jjdx") {
            $(_pageId + " .bankDeposit_region").show();
            $(_pageId + " .jjInclusive_region").show();
            $(_pageId + " .jjHighEnd_region").show();
            $(_pageId + " .yuanhui_region").hide();
            $(_pageId + " #vipBenefits").show();
            $(_pageId + " #InviteFriends").show();
        } else {
            $(_pageId + " .bankDeposit_region").hide();
            $(_pageId + " .jjInclusive_region").hide();
            $(_pageId + " .jjHighEnd_region").show();
            $(_pageId + " .yuanhui_region").hide();
        }
        getList()
    }
    //获取我的页面列表
    function getList() {
        service.reqFun101932({}, function (data) {
            if (data.error_no == "0") {
                let res = data.results[0];
                let total_assets = res.total_assets ? common.fmoney(res.total_assets, 2) : "--";//总资产
                let arr = res.data;
                let html = '';
                for (let i = 0; i < arr.length; i++) {
                    let classify_desc = arr[i].financial_prod_type == '01' ? '晋金宝' : arr[i].classify_desc
                    let total_amt = common.fmoney(arr[i].total_amt)
                    if ((arr[i].financial_prod_type != '05') || (arr[i].financial_prod_type == '05' && (arr[i].total_amt * 1) > 0)) {
                        html += '<p><a operationType="1" operationName="'+ classify_desc +'" operationId="item_'+ arr[i].financial_prod_type +'" class="item" href="javascript:void(0)" classify_desc="' + arr[i].classify_desc + '" financial_prod_type="' + arr[i].financial_prod_type + '" clickName="' + arr[i].financial_prod_type + '" id="' + arr[i].financial_prod_type + '" >' + classify_desc + '<span><em>' + total_amt + '</em> </span></a></p>'
                    }
                }
                html = '<p class="total_assets">总资产：<span><em>' + total_assets + '</em></span></p>' + html
                $(_pageId + " .topTitleList").html(html)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //跳转资产列表页面
        appUtils.preBindEvent($(_pageId + " .topTitleList"), ".item", function (e) {
            if (!ut.hasBindCard(_page_code)) return;
            let financial_prod_type = $(this).attr('financial_prod_type')
            tools.clickPoint(_pageId, _pageUrl, financial_prod_type)
            if (financial_prod_type == '01') {    //01跳转晋金宝
                tools.clickPoint(_pageId, _pageUrl, 'thfund')
                appUtils.pageInit(_pageUrl, "thfund/myProfit");
            } else if (financial_prod_type == '05') {
                appUtils.pageInit(_pageUrl, "bank/bankDeposit");
            } else {
                appUtils.setSStorageInfo("financial_prod_type", financial_prod_type)
                appUtils.pageInit(_pageUrl, "template/positionList");
            }

        }, "click");
        //消息
        appUtils.bindEvent($(_pageId + " #message_img"), function () {
            appUtils.pageInit(_pageUrl, "moreDetails/msg");
        });
        appUtils.bindEvent($(_pageId + " .my_account"), function () {
            tools.clickPoint(_pageId, _pageUrl, 'my_account')
            appUtils.pageInit(_pageUrl, "account/personMessage")
        });
        appUtils.bindEvent($(_pageId + " #thfund"), function () {
            ut.hasBindCard(_pageUrl);
        });
        appUtils.bindEvent($(_pageId + " #bankDeposit"), function () {
            ut.hasBindCard(_pageUrl);
        });
        appUtils.bindEvent($(_pageId + " #jjInclusive"), function () {
            ut.hasBindCard(_pageUrl);
        });
        appUtils.bindEvent($(_pageId + " #jjHighEnd"), function () {
            ut.hasBindCard(_pageUrl);
        });
        appUtils.bindEvent($(_pageId + " #yuanhui"), function () {
            ut.hasBindCard(_pageUrl);
        });
        appUtils.bindEvent($(_pageId + " #fundsInTransit_region"), function () {
            ut.hasBindCard(_pageUrl);
        });
        // 安全中心
        appUtils.bindEvent($(_pageId + " #passWordManage"), function () {
            // ut.hasBindCard(_pageUrl);
            tools.clickPoint(_pageId, _pageUrl, 'passWordManage')
            appUtils.pageInit(_pageUrl, "safety/passwordManage");
        });
        // 会员福利
        appUtils.bindEvent($(_pageId + " #vipBenefits"), function () {
            tools.clickPoint(_pageId, _pageUrl, 'vipBenefits')
            appUtils.pageInit(_pageUrl, "vipBenefits/index");
        });
        // 邀请好友
        appUtils.bindEvent($(_pageId + " #InviteFriends"), function () {
            // console.log(ut.hasBindCard())
            if (!ut.getUserInf().bankAcct || ut.getUserInf().bankAcct == '') return layerUtils.iAlert("仅注册客户暂未开通");
            tools.clickPoint(_pageId, _pageUrl, 'InviteFriends');
            appUtils.pageInit(_pageUrl, "vipBenefits/friendInvitation");
        });
        //红包 体验金
        appUtils.bindEvent($(_pageId + " #redpack"), function () {
            appUtils.pageInit(_pageUrl, "redPack/index", {});
        });
        appUtils.bindEvent($(_pageId + " #shouye"), function () {
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageUrl, "login/userIndexs", {});
        });
        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageUrl, "moreDetails/more", {});
        });
        //跳转到学投资页面
        appUtils.bindEvent($(_pageId + " #Learn"), function () {
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageUrl, "liveBroadcast/index", {});
        });


    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #phoneNum").html("");
        $(_pageId + " .bankDeposit_region").hide();
        $(_pageId + " .jjInclusive_region").hide();
        $(_pageId + " .jjHighEnd_region").hide();
        $(_pageId + " .yuanhui_region").hide();
        $(_pageId + " .hengji_region").hide();
        $(_pageId + " #InviteFriends").hide();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
