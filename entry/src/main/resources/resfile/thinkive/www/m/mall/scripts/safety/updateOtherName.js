// 修改支付密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_updateOtherName";
    var tools = require("../common/tools");
    var pageEle = "";
    //弱密码库
    var ut = require("../common/userUtil");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        pageEle = "";
        common.systemKeybord(); // 解禁系统键盘
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function (e) {
                var old_pwd = $(_pageId + " #oldPwd").val();
                var new_pwd = $(_pageId + " #newPwd1").val();
                new_pwd = new_pwd.replace(/[^\a-\z\A-\Z0-9]/g, '');
                $(_pageId + " #newPwd1").val(new_pwd);
                if (new_pwd.length > 20) {
                    new_pwd = new_pwd.substring(0, 20);
                    $(_pageId + " #newPwd1").val(new_pwd)
                }
                $(_pageId + " #oldPwd1").text(old_pwd);
                if (new_pwd == "") {
                    $(_pageId + " #newPwd11").text("请输入新别名");
                    if (pageEle == "newPwd1") {
                        $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
                        $(_pageId + " #newPwd11").removeClass("unable");
                    }
                } else {
                    $(_pageId + " #newPwd11").text(new_pwd);
                }
                if ((new_pwd.length == 1 || new_pwd.length == 3) && pageEle == "newPwd1") {
                    $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
                    $(_pageId + " #newPwd11").removeClass("unable").addClass("active");
                }
            } // 键盘的输入事件
        };
        var _otherName = ut.getUserInf().otherName;
        if (_otherName) {
            $(_pageId + " #oldPwd").val(_otherName);
            $(_pageId + " #oldPwd1").text(_otherName);
        } else {
            $(_pageId + " #oldPwd").val("");
            $(_pageId + " #oldPwd1").text("请输入原别名");
        }
        $(_pageId + " #newPwd1").val("");
        $(_pageId + " #newPwd11").text("请输入新别名");
        $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
        $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
        var phoneNum = ut.getUserInf().mobile;
        $(_pageId + " #userInfo").html("您好！您正在为账户 " + phoneNum.substr(0, 3) + "****" + phoneNum.substr(7, 4) + "修改别名。");

    }

    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack()
        });

        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
            $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
            var oldName = $(_pageId + " #oldPwd").val();
            var newName = $(_pageId + " #newPwd1").val();
            var _otherName = ut.getUserInf().otherName;
            checkInfo(oldName, newName, _otherName);
        });

        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #oldPwd11"), function () {
            guanbi();
        }, "click");

        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #newPwd111"), function () {
            if ($(_pageId + " #newPwd1").val() == "") {
                $(_pageId + " #newPwd11").removeClass("unable");
            } else {
                $(_pageId + " #newPwd11").removeClass("unable").addClass("active");
            }
//			$(_pageId+" #oldPwd1").removeClass("active").addClass("unable");
            kaiqi("newPwd1");
        }, "click");
    }


    //别名校验
    function checkInfo(oldName, newName, _otherName) {

        if (oldName == "" || oldName == null) {
            layerUtils.iMsg(-1, "原别名不能为空");
            return;
        }

        if (oldName != _otherName) {
            layerUtils.iMsg(-1, "原别名输入错误");
            return;
        }

        if (newName == "" || newName == null) {
            layerUtils.iMsg(-1, "新别名不能为空");
            return;
        }


        if (newName.length < 6 || newName.length > 20) {
            layerUtils.iMsg(-1, "新别名长度保持在6位到20位之间");
            return;
        }

        if (validatorUtil.isNumeric(newName.charAt(0))) {
            layerUtils.iMsg(-1, "新别名首位字符不能为数字");
            return;
        }

        var reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
        if (reg.test(newName)) {
            layerUtils.iMsg(-1, "新别名不包含中文字符");
            return;
        }

        //flag:判断别名格式(只包括英文和数字) flag==2：全部通过
        var flag = 0;
        for (var i = 0; i < newName.length; i++) {
            if (newName[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }

        for (var i = 0; i < newName.length; i++) {
            if (newName[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }

        if (flag != 2) {
            layerUtils.iMsg(-1, "新别名格式不正确,至少包含数字,字母(区分大小写)两种组合");
            return;
        }

        if (oldName == newName) {
            layerUtils.iMsg(-1, "新别名不能与原别名相同");
            return;
        }
        updateAlias(newName);//修改别名
    }


    function updateAlias(newName) {
        var param101013 = {
            "by_name": newName
        };
        service.reqFun101013(param101013, function (data) {
            if (data.error_no == "0") {
                var user = ut.getUserInf();
                user.otherName = newName;
                ut.saveUserInf(user);
                layerUtils.iMsg(-1, "别名修改成功");
                appUtils.pageBack()
            } else {
                layerUtils.iMsg(-1, data.error_info);
            }
        });
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
        $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
        $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
    }

    //开启键盘
    function kaiqi(eleId) {
        //调用键盘
        pageEle = eleId;
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_updateOtherName";
        param["eleId"] = eleId;
        param["doneLable"] = "确定";
        param["keyboardType"] = "7";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        pageEle = null;
        $(_pageId + " #oldPwd").val("");
        $(_pageId + " #newPwd1").val("");
//		$(_pageId+" #oldPwd1").text("请输入原别名");
        $(_pageId + " #newPwd11").text("请输入新别名");
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack()
    }

    var updateOtherName = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = updateOtherName;
});
