<div class="page" id="safety_changeBankphone" data-pageTitle="变更预留手机号" data-refresh="true">
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray" operationType="1" operationId="getBack" operationName="返回"><span>返回</span></a>
				<h1 class="text_gray text-center">变更预留手机号</h1>
			</div>
		</header>
		<article class="bg_blue">
			<div class="user_check">
				<div class="check_tips slidedown in " style="border-bottom: none">
					<p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
				</div>
				<div class="input_box">
					<div class="grid_03 grid_02 grid">
						<div class="ui field text rounded input_box2">
							<label class="short_label" style="width: 0.9rem;">真实姓名</label>
							<input type="text" class="ui input" placeholder="" id="realName" type="num" >
						</div>
					</div>
					<div class="grid_03 grid_02 grid" style="border-bottom: none;">
						<div class="ui field text rounded input_box2" style="border-bottom: none;">
							<label class="short_label" style="width: 0.9rem;">身份证号</label>
							<input type="text" class="ui input" placeholder="" id="idCard" maxlength="18" type="num" >
						</div>
					</div>
					<a href="javascript:void(0);" class="icon_photo"></a>
				</div>
				<div class="grid_03 grid_02 grid" >
					<div class="ui field text rounded input_box2">
						<label class="short_label">银行卡号</label>
						<input type="text" class="ui input" placeholder=""  id="cardNo" maxlength="19" >
					</div>
					<div class="ui field text rounded input_box2">
						<label class="short_label">银行名称</label>
						<input type="text" class="ui input" placeholder=""  id="bankname"  readonly="readonly" style="background: transparent;">
					</div>
					<div class="ui field text rounded input_box2">
						<label class="short_label" >原预留手机号</label>
						<input type="text" class="ui input" placeholder=""  id="bankLeavePhone" maxlength="11"  >
					</div>
					<div class="ui field text rounded input_box2">
						<label class="short_label" >新预留手机号</label>
						<input type="text" class="ui input" placeholder=""  id="bankLeavePhoneNew" maxlength="11"  >
					</div>
					<div class="ui field text rounded input_box2" id="yzmBox">
							<label class="short_label">验证码</label>
							<input custom_keybord="0" type="tel" id="yzm" maxlength="6" class="ui input code_input" placeholder="" >
							<a herf="javascript:void(0);" id="getYzm" data-state="true" operationType="1" operationId="getYzm" operationName="获取验证码">获取验证码</a>
					</div>
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="weihao"   style="display:none" ><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue" operationType="1" operationId="getTalk" operationName="语音获取">语音获取</span><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
					<div class="ui field text rounded input_box2">
						<label class="short_label">交易密码</label>
						<span style="position:absolute;height: 0.44rem;left:1rem ;right: 0;" id='inputspanid'><span style="color: #999999;line-height: 0.44rem;font-size: 0.14rem;height: 0.44rem;" text='请输入交易密码'>请输入交易密码</span></span>
						<input type="password" class="ui input" placeholder="请输入交易密码" readonly="readonly" id="tradeNum" maxlength="6" style="margin-left:0.2rem;display:none" >
						<!--<div class="simulate_input no_border" id="tradeNum1">
							<span class="unable" id="jiatradeNum">请输入交易密码</span>
						</div>-->
					</div>
				</div>

				<!-- <div class="input_box ui field text" style="padding-right: 0rem;" id="tradeNum1">
				<em>*</em>
				<input type="password" class="ui input" placeholder="交易密码" readonly="readonly" id="tradeNum" maxlength="6" style="margin-left:0.2rem;display:none" >
					<div class="simulate_input no_border" style="margin-left:0.2rem">
						<span class="unable" id="jiatradeNum">交易密码</span>
					</div>
				</div> -->
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="xyb" operationType="1" operationId="xyb" operationName="下一步">下一步</a>
				</div>
			</div>
		</article>
	</section>
</div>

