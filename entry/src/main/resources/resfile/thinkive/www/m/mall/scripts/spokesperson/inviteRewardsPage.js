// 代言人-邀请奖励
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        gconfig = require("gconfig"),
        ut = require("../common/userUtil"),
        _pageCode = "spokesperson/inviteRewardsPage",
        _pageId = "#spokesperson_inviteRewardsPage ";
    require("../common/html2canvas.min.js");
    require("../common/jquery.qrcode.min.js");
    var global = gconfig.global;
    var activityInfo;
    let source;//来源
    var platform = gconfig.platform;
    function init() {
        source = '17';
        // 查询代言人活动信息
        queryActivityInfo();
    }

    function queryActivityInfo() {
        service.reqFun113003({}, function (data) {
            if (data.error_no == '0') {
                activityInfo = data.results[0];
                $(_pageId + " .withdrawable_amt").html(tools.fmoney(activityInfo.withdrawable_amt));
                $(_pageId + " .total_amt").html(tools.fmoney(activityInfo.total_amt));
                $(_pageId + " .yesterday_amt").html(tools.fmoney(activityInfo.yesterday_amt));
                $(_pageId + " .activity_rules").html(activityInfo.introduce);
                $(_pageId + " #invitedCount").html(`(${activityInfo.invited_count})`)
                appUtils.setSStorageInfo("splitMaxAmt", activityInfo.split_max_amt); //产品信息
                let len = activityInfo.show_prod_name.length;
                $(_pageId + " #prodName").html()
                var html = ` <div class="invite_info_item info_header">
                    <div class="invite_type">
                    </div>
                    <div class="info_list">
                        <ul class="info">
                            <li>手机号</li>
                            <li>姓名</li>
                            <li><span id="prodName">${activityInfo.show_prod_name.slice(0, 4)}<br>${activityInfo.show_prod_name.slice(4, len)}</span></li>
                        </ul>

                    </div>
                </div><div class="info_content">`;
                if ((!activityInfo.invested_list || activityInfo.invested_list.length == '0') && (!activityInfo.not_invested_list || activityInfo.not_invested_list.length == '0') && (!activityInfo.unbound_list || activityInfo.unbound_list.length == '0')) {
                    $(_pageId + " .invite_info").html("<div class='nodata'>您暂未邀请好友</div>");
                    $(_pageId + " #viewAllBtn").hide();
                    return;
                }
                if (activityInfo.invested_list && activityInfo.invested_list.length > activityInfo.show_number_invested ||
                    activityInfo.unbound_list && activityInfo.unbound_list.length > activityInfo.show_number_unbound ||
                    activityInfo.not_invested_list && activityInfo.not_invested_list.length > activityInfo.show_number_not_invested
                ) {
                    $(_pageId + " #viewAllBtn").show();
                } else {
                    $(_pageId + " #viewAllBtn").hide();
                }
                if (activityInfo.unbound_list && activityInfo.unbound_list.length) {
                    html += dealHtml(activityInfo.unbound_list, "1", activityInfo.show_number_unbound);
                }
                if (activityInfo.not_invested_list && activityInfo.not_invested_list.length) {
                    html += dealHtml(activityInfo.not_invested_list, "2", activityInfo.show_number_not_invested);
                }
                if (activityInfo.invested_list && activityInfo.invested_list.length) {
                    html += dealHtml(activityInfo.invested_list, "3", activityInfo.show_number_invested);
                }
                $(_pageId + " .invite_info").html(html);

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function dealHtml(arr, type, number) {
        // type 1 未绑卡 2 已绑卡未投资 3 已投资
        let str = ``;
        arr.forEach((item, i) => {
            if (i < number) {
                item = tools.FormatNull(item); // 空数据展示--
                str += `
                <ul class="info">
                    <li>${item.registered_mobile}</li>
                    <li>${item.cust_name}</li>
                    <li>${item.buy_flag == '0' ? '否' : '是'}</li>
                    <li>
                        <div class="send_wx" data-type="${type}">发微信</div>
                    </li>
                </ul>`
            }

        })
        let html = `
            <div class="invite_info_item" id="${type == 1 ? 'unboundList' : `${type == 2 ? 'notInvestedList' : 'investedList'}`}">
            <div class="invite_type">
                <span class="name">${type == 1 ? '未绑卡' : `${type == 2 ? '绑卡未投资' : '已投资'}`}</span>
            </div>
            <div class="info_list">${str} </div>
        </div></div>`;
        return html;
    }

    function getShareTemplateInfo(share_template, type) {
        let params = {
            registered_mobile: ut.getUserInf().mobileWhole,
            share_template: share_template
        };
        service.reqFun102012(params, function (data) {
            if (data.error_no == '0') {
                var result = data.results[0];
                if (data.results[0] && data.results[0].share_form == 2) {  // 若分享的是卡片，先渲染卡片
                    setShareImg(result, type);
                } else if (data.results[0] && data.results[0].share_form == 1) {  // 链接
                    // results.title = result.title;
                    // results.content = result.content;
                    // results.img_url = result.img_url;
                    result['page_type'] = '10';
                    let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                    return tools.pageShare(result, type, _pageId, shareUrlLast, null, activityInfo.activity_id);//活动页面分享
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //渲染分享卡片图片
    function setShareImg(chooseData, type) {
        let bgImg = gconfig.global.oss_url + chooseData.img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                layerUtils.iMsg(-1, "启动分享中...请稍后！");
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #qr_img").show();
                    $(_pageId + " #code").hide();
                    $(_pageId + "#qr_img").empty();
                    let qr_code_img = global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #qr_img").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                        shareCard(type);
                    })
                } else {
                    $(_pageId + " #qr_img").hide();
                    $(_pageId + " #code").show();
                    let mobile = ut.getUserInf().mobileWhole;
                    mobile = common.desEncrypt("mobile", mobile);//加密
                    // let long_url = "https://xhxts.sxfae.com/m/mall/index.html#!/drainage/userInvitationWx.html?mobile=" + mobile;
                    let long_url = chooseData.qr_code_img_url + '?mobile=' + mobile + '&source=' + source;;
                    $(_pageId + "#code").empty();
                    service.reqFun101073({ long_url: long_url }, function (res) {
                        if (res.error_no == "0") {
                            if (res.results != undefined && res.results.length > 0) {
                                var short_url = res.results[0].shortUrl;
                                $(_pageId + " #code").qrcode({
                                    render: "canvas", //设置渲染方式，有table和canvas
                                    text: short_url, //扫描二维码后自动跳向该链接
                                    width: 70, //二维码的宽度
                                    height: 70, //二维码的高度
                                    imgWidth: 20,
                                    imgHeight: 20,
                                    src: '../mall/images/icon_app.png'
                                });
                                setTimeout(function () {
                                    shareCard(type);
                                }, 200);
                            }
                        } else {
                            layerUtils.iAlert(res.error_info);
                        }
                    })
                    // qrcode(chooseData, type);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /*分享卡片*/
    function shareCard(type) {
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        $(_pageId + " .pop_layer").hide();
        var father = document.querySelector("#content");
        var _fatherHTML = document.querySelectorAll("#content .page");
        var cur = document.querySelector("#spokesperson_inviteRewardsPage");
        father.innerHTML = "";
        father.appendChild(cur);
        let dom = document.querySelector(_pageId + " .shareImg");
        html2canvas(dom, {
            scale: 4
        }).then(canvas => {
            var base64 = canvas.toDataURL("image/png");
            var _base64 = base64.split(",")[1];
            father.innerHTML = "";
            for (let i = 0; i < _fatherHTML.length; i++) {
                father.appendChild(_fatherHTML[i]);
            }
            param = {
                "funcNo": "50231",
                "imgUrl": _base64,
                "shareType": type,
                "imageShare": "1",
                "imageType":"base64"
            }
            require("external").callMessage(param);
        })
    }

    //绑定事件
    function bindPageEvent() {
        // 提取明细
        appUtils.bindEvent($(_pageId + " .btn_extract"), function () {
            appUtils.pageInit(_pageCode, "spokesperson/withdrawalRecord");
        });
        // 提现到微信
        appUtils.bindEvent($(_pageId + " #withdrawWxBtn"), function () {

            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if (activityInfo.state == "2") {
                layerUtils.iMsg(-1, "活动未开始！", 2);
                return;
            }
            if (activityInfo.state == "3") {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            if (activityInfo.withdrawing_flag == '0') {
                layerUtils.iAlert(`当前${activityInfo.withdrawing_prod_name}持仓不满足${activityInfo.withdrawing_prompt_amt}元，不可提现。`);
                return;
            }
            if (parseFloat(activityInfo.withdrawable_amt) < parseFloat(activityInfo.withdrawing_min_amt)) {
                layerUtils.iAlert(`提现金额高于${activityInfo.withdrawing_min_amt}元可提现`);
                return;
            }
            var newDate = Date.parse(new Date());	// 此处获取系统当前时间点
            var nowTime = new Date();
            var year = nowTime.getFullYear();  // 获取当前时间的年
            var month = nowTime.getMonth() + 1;  // 获取当前时间的月（注意要加1）
            var date = nowTime.getDate();  // 获取当前时间的日
            var startTime = year + '/' + month + '/' + date + ' 00:30:00';// 拼接开始的限制时间点
            var endTime = year + '/' + month + '/' + date + ' 23:30:00';	// 拼接结束限制时间点
            startTime = Date.parse(new Date(startTime));
            endTime = Date.parse(new Date(endTime));
            if (newDate > startTime && newDate < endTime) {	// 当前时间大于限制的开始时间并且小于限制的结束时间
                // 通过校验,跳转小程序
                layerUtils.iAlert(`即将为您跳转到微信小程序提现页`, () => { }, function () {
                    let url = "pages/index/userLogin/userLogin";
                    tools.jump_applet(url);
                    return;
                }, '', '确定')
            } else {
                layerUtils.iAlert(`提现时间为0:30-23:30`);
                return;
            }

        });
        // 邀请好友
        appUtils.bindEvent($(_pageId + " #inviteFriendsBtn"), function () {
            // tools.clickPoint(_pageId, _pageCode, 'InviteFriends')
            // appUtils.pageInit(_pageCode, "vipBenefits/friendInvitation", { source: source });    //主动邀请
            $(_pageId + " #pop_layer").attr("data-type", "")
            $(_pageId + " #pop_layer").show();
        });
        // 查看全部好友
        appUtils.bindEvent($(_pageId + " #viewAllBtn"), function () {
            appUtils.pageInit(_pageCode, "spokesperson/allFriendsPage");
        });
        // 查看添加记录
        appUtils.bindEvent($(_pageId + " #viewAddRecord"), function () {
            appUtils.pageInit(_pageCode, "spokesperson/addFriendRecord");
        });
        // 手动添加好友
        appUtils.bindEvent($(_pageId + " #addFriendsBtn"), function () {
            $(_pageId + " #addFriends").show();
        });
        // 添加好友-提交
        appUtils.bindEvent($(_pageId + " #submitBtn"), function () {
            if (validatorUtil.isEmpty($(_pageId + " #inviteCustName").val())) {
                layerUtils.iMsg(-1, "请填写真实姓名");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #inviteRegisteredMobile").val())) {
                layerUtils.iMsg(-1, "请填写注册手机号");
                return;
            }
            if (!validatorUtil.isMobile($(_pageId + " #inviteRegisteredMobile").val())) {
                layerUtils.iMsg(-1, "请填写正确的手机号码");
                return;
            }
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            let param = {
                invite_cust_name: $(_pageId + " #inviteCustName").val(),
                invite_registered_mobile: $(_pageId + " #inviteRegisteredMobile").val()
            }
            $(_pageId + " #addFriends").hide();
            $(_pageId + " #inviteCustName").val("");
            $(_pageId + " #inviteRegisteredMobile").val("");
            service.reqFun113006(param, function (data) {
                if (data.error_no == '0') {
                    $(_pageId + " #addFriendsSuccess").show();
                } else if (data.error_no == '-2' || data.error_no == '-3' || data.error_no == '-4' || data.error_no == '-5' || data.error_no == '-8') {
                    $(_pageId + " #addFriendsFail #errorInfo").html(`不符合条件，详询${global.custServiceTel}`);
                    $(_pageId + " #addFriendsFail").show();
                } else if (data.error_no == '-6') {
                    $(_pageId + " #addFriendsFail #errorInfo").html(`该客户已是您的好友，不需重复提交`);
                    $(_pageId + " #addFriendsFail").show();
                } else if (data.error_no == '-7') {
                    $(_pageId + " #addFriendsFail #errorInfo").html(`审核中，请耐心等待`);
                    $(_pageId + " #addFriendsFail").show();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        });
        // 添加成功
        appUtils.bindEvent($(_pageId + " #successBtn"), function () {
            $(_pageId + " #addFriendsSuccess").hide();
        });
        // 关闭手动添加好友
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " #inviteCustName").val("");
            $(_pageId + " #inviteRegisteredMobile").val("");
            $(_pageId + " #addFriends").hide();
        });
        // 添加失败
        appUtils.bindEvent($(_pageId + " #failBtn"), function () {
            $(_pageId + " #addFriendsFail").hide();
        });
        // 发微信
        appUtils.preBindEvent($(_pageId + " .invite_info"), ".invite_info_item .info .send_wx", function () {
            if (!ut.hasBindCard(_pageCode)) return;
            var type = $(this).attr("data-type");
            $(_pageId + " #pop_layer").attr("data-type", type)
            $(_pageId + " #pop_layer").show();
        })
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").removeAttr("data-type");
            $(_pageId + " #pop_layer").hide();
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            var type = $(_pageId + " #pop_layer").attr("data-type");
            var share_template = "";
            if (type == 1) {
                share_template = activityInfo.share_template_id_unbound
            } else if (type == 2) {
                share_template = activityInfo.share_template_id_not_invested
            } else if (type == 3) {
                share_template = activityInfo.share_template_id_invested
            } else {
                share_template = activityInfo.share_template
            }
            getShareTemplateInfo(share_template, "22");
            tools.clickPoint(_pageId, _pageCode, 'share_WeChat', activityInfo.activity_id);
            // common.share("22", share_template ? share_template : "0", "", "", "", "", true, source);

        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            var type = $(_pageId + " #pop_layer").attr("data-type");
            var share_template = "";
            if (type == 1) {
                share_template = activityInfo.share_template_id_unbound
            } else if (type == 2) {
                share_template = activityInfo.share_template_id_not_invested
            } else if (type == 3) {
                share_template = activityInfo.share_template_id_invested
            } else {
                share_template = activityInfo.share_template
            }
            getShareTemplateInfo(share_template, "23");
            tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', activityInfo.activity_id);
            // common.share("23", share_template ? share_template : "0", "", "", "", "", true, source);
        });
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //页面销毁
    function destroy() {
        $(_pageId + " .withdrawable_amt").html("");
        $(_pageId + " .total_amt").html("");
        $(_pageId + " .yesterday_amt").html("");
        $(_pageId + " .activity_rules").html("");
        $(_pageId + " #invitedCount").html("")
        $(_pageId + " #prodName").html("")
        $(_pageId + " #addFriendsFail").hide();
        $(_pageId + " #addFriendsSuccess").hide();
        $(_pageId + " #addFriends").hide();
        $(_pageId + " #pop_layer").removeAttr("data-type");
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #inviteCustName").val("");
        $(_pageId + " #inviteRegisteredMobile").val("");
        $(_pageId + " .invite_info").html("");
        $(_pageId + " #qr_img").hide();
        $(_pageId + " #code").show();
        $(_pageId + " #viewAllBtn").hide();
        activityInfo = ''
    }

    //安卓物理返回键会调用该方法
    function pageBack() {

        appUtils.pageBack();
    }

    let inviteRewardsPageModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = inviteRewardsPageModule;
});
