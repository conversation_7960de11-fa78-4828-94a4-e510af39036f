// 修改支付密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_setOtherName ";
    var ut = require("../common/userUtil");

    function init() {

        common.systemKeybord(); // 解禁系统键盘
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var oldPwd = $(_pageId + " #oldPwd").val();
                oldPwd = oldPwd.replace(/[^\a-\z\A-\Z0-9]/g, '');
                $(_pageId + " #oldPwd").val(oldPwd);
                if (oldPwd.length > 20) {
                    oldPwd = oldPwd.substring(0, 20);
                    $(_pageId + " #oldPwd").val(oldPwd)
                }
                if (oldPwd == "") {
                    $(_pageId + " #oldPwd1").text("别名为英文和数字组合");
                    $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
                    $(_pageId + " #oldPwd1").removeClass("unable");
                } else {
                    $(_pageId + " #oldPwd1").html(oldPwd);
                }
                if (oldPwd.length == 1 || oldPwd.length == 3) {
                    $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
                    $(_pageId + " #oldPwd1").removeClass("unable").addClass("active");
                }
            } // 键盘的输入事件
        };
        $(_pageId + " #oldPwd1").text("别名为英文和数字组合");
        $(_pageId + " #oldPwd").val("");
        $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
        var phoneNum = ut.getUserInf().mobile;
        $(_pageId + " #userInfo").html("您好！您正在为账户 " + phoneNum.substr(0, 3) + "****" + phoneNum.substr(7, 4) + "设置别名。");

    }

    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
            var name = $(_pageId + " #oldPwd").val();
            checkOtherName(name);
        });

        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #oldPwd11"), function () {
            if ($(_pageId + " #oldPwd").val() == "") {
                $(_pageId + " #oldPwd1").removeClass("unable");
            } else {
                $(_pageId + " #oldPwd1").removeClass("unable").addClass("active");
            }
            kaiqi("oldPwd");
        }, "click");

        //离开焦点
        appUtils.bindEvent($(_pageId + " #oldPwd1"), function () {
            $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
        }, 'blur');

    }

    //验证别名
    function checkOtherName(name) {

        if (name == "" || name == null) {
            layerUtils.iMsg(-1, "别名不能为空");
            return;
        }

        if (name.length < 6) {
            layerUtils.iMsg(-1, "别名长度不少于6位数");
            return;
        }

        if (validatorUtil.isNumeric(name.charAt(0))) {
            layerUtils.iMsg(-1, "首字符不能为数字");
            return;
        }

        var reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
        if (reg.test(name)) {
            layerUtils.iMsg(-1, "别名不包含中文字符");
            return;
        }

        var flag = 0;
        for (var i = 0; i < name.length; i++) {
            if (name[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }

        for (var i = 0; i < name.length; i++) {
            if (name[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }

        if (flag != 2) {
            layerUtils.iMsg(-1, "别名格式不正确,至少包含数字,字母(区分大小写)两种组合");
            return;
        }

        //设置别名
        setAlias(name);
    }


    //设置用户别名
    function setAlias(name) {
        var param101013 = {
            "by_name": name
        };
        service.reqFun101013(param101013, function (data) {
            if (data.error_no == "0") {
                var user = ut.getUserInf();
                user.otherName = name;
                ut.saveUserInf(user);
                layerUtils.iMsg(-1, "别名设置成功");
                appUtils.pageBack();
            } else {
                layerUtils.iMsg(-1, data.error_info);
            }
        });

    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
        $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
    }

    //开启键盘
    function kaiqi(eleId) {
        //调用键盘
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_setOtherName";
        param["eleId"] = eleId;
        param["doneLable"] = "确定";
        param["keyboardType"] = "7";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        $(_pageId + " #oldPwd").val("");
        $(_pageId + " #oldPwd1").text("别名为英文和数字组合");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var setOtherName = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setOtherName;
});
