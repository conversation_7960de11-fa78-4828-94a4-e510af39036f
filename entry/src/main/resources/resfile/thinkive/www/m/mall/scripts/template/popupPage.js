// 广告模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        gconfig = require("gconfig"),
        _page_code = "template/popupPage",
        _pageId = "#template_popupPage ";
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    let heighEndProduct //new 一个 vue 实例
    var bottomImg
    var headImg
    var contentImg
    let fundcode,pageInfo,pageUrl

    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150({ templateId: pageInfo.template_id}, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "")
            })
        })
    }

    async function init() {
        //页面埋点初始化
        tools.initPagePointData();
        pageInfo = appUtils.getPageParam();
        if(!pageInfo || !pageInfo.template_id){
            pageInfo = appUtils.getSStorageInfo("pageInfo")
        };
        if(pageInfo.jump_page_type == '7') {
            let html = `<img style="width:100%" src="${global.oss_url + pageInfo.detail_image_url}"></img>`
            $(_pageId + " .main_popupPage").html(html)
            return;
        };
        if(pageInfo.jump_page_type == '6' && pageInfo.detail_jump_page_type == '5') {
            pageInfo.template_id = '76'
        };
        let html =  await setTemplate()
        $(".main_popupPage").html(html)   //渲染模板
        pageUrl = pageInfo.trueUrl;
        if(pageInfo.detail_bottom_button_show == '1'){  //是否展示底部按钮
            $(_pageId + " .buy").show();
            $(_pageId + " .buy").text(pageInfo.detail_bottom_button_text);
        }else{
            $(_pageId + " .buy").hide();
        }
        heighEndProduct = new Vue({
            el: '#main_popupPage',
            data() {
                return {
                    oss_url:global.oss_url,
                    detailsInfo: {},//详情信息

                }
            },
            //视图 渲染前
            created() {

               
            },
            //渲染完成后
            mounted() {
                bottomImg = global.oss_url + pageInfo.detail_image_url;
                $(_pageId + ".main_popupPage #bottom_img").attr("src", bottomImg);
                fundcode = pageInfo.jump_page_prodid;
                appUtils.setSStorageInfo("busi_id",fundcode); //缓存产品代码方便后续使用
                appUtils.setSStorageInfo("fund_code",fundcode);
            },
            //计算属性
            computed: {
                
            },
            //绑定事件
            methods: {
                //获取用户的认证状态
                async  getUserAuthenticationStatus() {
                    return new Promise(async (resolve, reject) => {
                        service.reqFun101062({}, async (data) => {
                            if (data.error_no == '0') {
                                var res = data.results
                                resolve(res)
                            } else {
                                $(_pageId + " .footerAll").hide();
                                $(_pageId + ' .homePageIndex_classificationList').html('');
                                reject('异常抛出')
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                //获取产品详情
                getDetails() {
                    let data = {
                        fund_code: fundcode
                    }
                    service.reqFun102113(data, (datas) => {
                        if (datas.error_no == '0') {
                            let res = datas.results[0]
                            this.detailsInfo = res
                            res.nav_date = tools.FormatDateText(res.nav_date.substr(4));//收益日
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                },
                async toOtherPage(){
                    let data = {
                        fund_code:fundcode
                    }
                    appUtils.setSStorageInfo("productInfo",data);
                    appUtils.setSStorageInfo("pageInfo",pageInfo);
                    tools.recordEventData('1','toOtherPage','跳转页面');
                    appUtils.pageInit(_page_code, pageUrl,pageInfo); //直接跳转
                    // if(pageInfo.flag == '1'){ //私募
                    //     // let userData = await this.getUserAuthenticationStatus();
                    //     // return console.log(userData)
                    //     // if (userData[0].sm_white_state == "1" || userData[0].state == "1") { //白名单用户 || 合格投资人 展示详细数据
                            
                    //     // } else {
                    //     //     this.isUser = true;
                    //     // }
                        
                    // }else if(pageInfo.flag == '0'){ //公募
                        
                    // }
                    
                },
                //关闭合格投资者弹窗
                closeDig(){
                    // this.isUser = false;
                },
            	//点击确定合格投资者弹窗
                affirm(){
                    service.reqFun101038({}, function (datas) {
                        if (datas.error_no == 0) {
                            // this.isUser = false;
                            userAuthenticationStatus = '1'
                            tools.recordEventData('1','isAuthentication','点击合格投资者');
                            appUtils.setSStorageInfo("isAuthentication", 1)
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    });
                }
            }
               
             
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .newBuy"), function () {
            let data = {
                fund_code:fundcode
            }
            appUtils.setSStorageInfo("productInfo",data);
            appUtils.setSStorageInfo("pageInfo",pageInfo);
            tools.recordEventData('1','newBuy','跳转');
            appUtils.pageInit(_page_code, pageUrl,pageInfo); //直接跳转
        });
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        // $(_pageId + " .header_inner #title").html("");
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        headImg = "";
        bottomImg = "";
        contentImg= "";
        $(".main_marketing").html()
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
