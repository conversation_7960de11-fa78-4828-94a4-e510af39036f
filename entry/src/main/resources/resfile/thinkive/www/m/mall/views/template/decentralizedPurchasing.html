<!-- 一键分散购买 -->
<div class="page" id="template_decentralizedPurchasing" data-pageTitle="系列营销页" data-pageLevel="0" data-refresh="true"
    style="-webkit-overflow-scrolling: touch">
    <section id="m_product " class="main fixed" data-page="home" style="padding-bottom: 0">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" class="icon_back icon_gray" style="z-index: 99"><span>返回</span></a>
                <h1 class="list_title text_gray text-center"></h1>
                <a id="kefu" href="javascript:void(0)" style="display: none;" class="coustomer-service-icon">
                    <img src="./images/customerService.png" />
                </a>
                <a id="share" href="javascript:void(0)" style="display: none;"
                    class="coustomer-service-icon coustomer-service-icon-share">
                    <img src="./images/share.png" />
                </a>
            </div>
        </header>
        <article id="inclusive_jjThirtyDetail" style="padding-bottom: 0.84rem;">
            <div class="risk_alert">
                <div class="content_box">
                    <p class="title">风险提示</p>
                    <p class="content">购买货币市场基金并不等于将资金作为存款存在银行或者存款类金融机构，基金管理人不保证基金一定盈利，也不保证最低收益</p>
                    <p class="know">我知道了</p>
                </div>
            </div>
            <!-- <div class="list">
                <ul>
                    <li class="remark">分散买入，波动小，收益更确定；也快克单只或自由组合购买。</li>
                    <li class="list_title m_bold m_font_size18 m_center">--</li>
                    <li class="flex list_price m_center">
                        <p class="main_flxe vertical_line">
                            <span class="m_text_red m_bold m_font_size22 day_rate">--</span>
                            <span class="m_bold m_font_size12">日涨跌幅</span>
                        </p>
                        <p class="main_flxe vertical_line">
                            <span class="m_font_size22 m_bold revenue">--</span>
                            <span class="m_bold m_font_size12 revenueName">--</span>
                        </p>
                    </li>
                    <li class="main_flxe vertical_line list_card">
                        
                    </li>
                    <li class="color_ccc m_font_size12">数据日期：<span class="dataDate">--</span></li>
                </ul>
            </div> -->
            <div id="main_decentralizedPurchasing" class="main_decentralizedPurchasing" v-cloak>
                
            </div>
            <div class="thfundBtn" style="height: 0.86rem;display:none">
                <div class="purchase">
                    <span class="purchaseAmount"></span>
                </div>

                <div class="fixedInvestment f18">一键分散定投</div>
                <div style="left:50%;width:50%" class="buy  ">
                    <span class="f18" id="buy_state">一键分散买</span>
                </div>
            </div>
        </article>
        <div class="pop_layer" style="display:none;" id="pop_layer_pageShare">
            <div class="share_box slidedown in">
                <a href="javascript:void(0);" class="btn_shareto text-center">分享赢好礼</a>
                <ul>
                    <li>
                        <a href="javascript:void(0);" id="share_WeChat">
                            <em></em>
                            <span>微信好友</span>
                        </a>
                    </li>
                    <li>
                        <a href="javascript:void(0);" id="share_WeChatFriend">
                            <em></em>
                            <span>微信朋友圈</span>
                        </a>
                    </li>
                </ul>
                <a href="javascript:void(0);" class="btn_cancel text-center" id="cancelShare">取消</a>
            </div>
        </div>
        <!-- <div id="showVideo" class="activity_pop_layer main_flxe flex_center" style="display:none">
            <div class="header_inner ">
                <a href="javascript:void(0)" id="getBack" @click="getBack"
                    class="icon_back icon_gray close"><span>返回</span></a>
            </div>
            <video id="new_example_video" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
                webkit-playsinline="true" playsinline="true" height="100%" controls preload="auto" poster=""
                data-setup="{}">
            </video>
        </div> -->
    </section>
</div>