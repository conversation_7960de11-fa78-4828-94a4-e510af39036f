<div class="page" id="safety_kjnewcardinfo" data-pageTitle="快捷换卡" data-refresh="true">
	<div class="pop_layer4 pop_layer" style="display:none" ></div>
	<div class="card_rules" style="display:none"  >
		<div class="rules_box slideup in">
			<h5>快捷换卡规则</h5>
			<div class="rules_list">
				<strong>1. 客户总资产为零，且无未确认的交易（在途资金、单边账等）的情况下才可进行快捷换卡。</strong>
				<strong>2. 客户需输入平台交易密码和手机验证码，填写新卡卡号和新卡银行预留手机号，系统验签通过后，即完成快捷换卡。</strong>
			</div>
			<p class="risk_tips">温馨提示：换卡成功后，您的交易将通过变更后的新签约银行卡办理，请知晓。</p>
			<div class="grid_02">
				<a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
			</div>
		</div>
	</div>
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">快捷换卡</h1>
				<a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
			</div>
		</header>
		<article class="bg_blue">
			<div class="bank_form">
				<h3>请填写新卡信息</h3>
				<div class="input_box">
					<div class="ui field text">
					<div class="pop_view" id="pop_view"  style="visibility:hidden;">
						<p id="big_show_bank"></p>
					</div>
						<label class="ui label">新银行卡号</label><input id="bankCard" maxlength="19" type="tel" class="ui input" />
						<a href="javascript:void(0);" class="icon_photo"></a>
					</div>
					<div class="ui field text">
						<label class="ui label">银行名称</label>
						<!-- <div class="ui dropdown" id="bankname" style="margin-left:0.1rem;">
							<strong id="chooseBank"></strong>
						</div> -->
						<input style="background:white;" id="bankname"  readonly="readonly"  unselectable="on"  type="text" class="ui input" />
					</div>
				</div>
				<div class="place" style="display:none">
					<p>银行卡单笔限额：<span id="oneMoney" style="color: #000;font-weight: bold;"></span>  单日限额：<span id="drxe" style="color: #000;font-weight: bold;"></span></p>
				</div>
				<div class="input_box">
					<div class="ui field text">
						<label class="ui label">新预留手机</label><input id="yhmPhone" maxlength="11" type="tel"   placeholder="请输入新银行卡预留手机号" class="ui input" />
					</div>
					<div class="grid_03 grid_02 grid">
						<div class="ui field text rounded input_box2" id="yzmBox">
							<label class="short_label2" style="text-align: right;width: 0.9rem;padding: 0 0.1rem">验证码</label>
							<input custom_keybord="0" id="verificationCode" type="tel" maxlength="6" class="ui input code_input"
								   placeholder=""/>
							<a id="getYzm" data-state="true">获取验证码</a>
						</div>
					</div>
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="weihao" style="display:none"></dd>
							<dd>
							</dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->

				</div>

				<div class="sure_box" style="display:none">
					<div class="radio_box mt20">
						<div class="ui radio">
							<input type="radio" id="input_radio2" checked="checked">
							<label id="isChecked">我已阅读并同意签署</label>
						</div>
					</div>
					<div id="bank_xy"></div>

				</div>
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="next">提 交</a>
				</div>
				<div class='bank_tips' style="text-align: center;"><a href='javascript:void(0)'>*支持的银行卡</a></div>
			</div>
		</article>
	</section>
</div>
