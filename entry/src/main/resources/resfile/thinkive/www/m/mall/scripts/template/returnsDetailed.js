// 公募--收益明细
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageUrl = "template/returnsDetailed",
        _pageId = "#template_returnsDetailed ";
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var cur_page = 1;
    var startTime = "";
    var endTime = "";
    var isEnd = false;
    var num_per_page = "20";
    var tools = require("../common/tools");

    function init() {
        // tools.initFundBtn(productInfo, _pageId);
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + ".olay").remove();
        //初始化时间筛选列表及时间框显示(默认七天)
        selectDate(_pageId + '#startTime', 0, _pageId);
        selectDate(_pageId + '#endTime', 1, _pageId);
        resetInputDate();
        if (appUtils.getPageParam("start_time")) {
            $('#startTime').attr('time', appUtils.getPageParam("start_time"));
        }
        if (appUtils.getPageParam("end_time")) {
            $('#endTime').attr('time', appUtils.getPageParam("end_time"));
        }
        getUsertransaction(false);
    }


    function bindPageEvent() {
        //返回键
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");

        //打开筛选层
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " .record_filter").show();
            $(_pageId + " #filter_layer").show();
            var recordHeight = $(_pageId + " .record_filter").innerHeight();
            var topHeight = recordHeight + 44 + "px";
            $(_pageId + " .pop_layer").css("top", topHeight);
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " .pop_layer"), function () {
            $(_pageId + " .record_filter").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //快捷键选择时间
        appUtils.bindEvent($(_pageId + " #query_date li a"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).addClass("active");
            var data_value = $(this).attr("data-value");
            var endTime = new Date();
            var startTime = new Date();
            if (data_value == "30") {
                startTime.setMonth(endTime.getMonth() - 1);
            } else if (data_value == "90") {
                startTime.setMonth(endTime.getMonth() - 3);
            } else if (data_value == "180") {
                startTime.setMonth(endTime.getMonth() - 6);
            } else {
                startTime = "";
                endTime = "";
            }
            setInputDate("endTime", endTime);
            setInputDate("startTime", startTime);
        });

        //确定/重置按钮绑定事件
        appUtils.bindEvent($(_pageId + " .record_filter #query-button a"), function () {
            var dataType = $(this).attr("data-value");
            if (dataType === "reset") {
                //重置按钮
                resetInputDate();
            } else if (dataType === "confirm") {
                cur_page = 1;
                startTime = $(_pageId + " #startTime").attr("time");
                endTime = $(_pageId + " #endTime").attr("time");
                getUsertransaction(false);
                $(_pageId + " .record_filter").hide();
                $(_pageId + " .pop_layer").hide();
            }
        });

    }

    //查询收益明细
    function getUsertransaction(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        endTime = $(_pageId + '#endTime').attr('time');
        startTime = $(_pageId + '#startTime').attr('time');
        var param = {
            "cur_page": cur_page,
            "num_per_page": num_per_page,
            "start_date": startTime,
            "end_date": endTime,
        };
        // console.log(param);
        var trsFundCode = appUtils.getSStorageInfo("trsFundCode");
        if (trsFundCode) {
            param.fund_code = trsFundCode;
        }
        var callback = function (resultVo) {
            if (resultVo.error_no == "0") {
                var detailParams = resultVo.results[0].data.data;
                var htmls = "";
                if (detailParams && detailParams.length > 0) {
                    for (var i = 0; i < detailParams.length; i++) {
                        var income_date = tools.ftime(detailParams[i].income_date)
                        var total_vol = tools.fmoney(detailParams[i].total_vol);
                        var nav = tools.changeTwoDecimal_f(detailParams[i].nav, 4);  //净值
                        var income = tools.fmoney(detailParams[i].income);
                        var className = "m_text_red";
                        if (parseFloat(detailParams[i].income) >= 0) {
                            className = "m_text_red"
                            income = "+" + tools.fmoney(detailParams[i].income);
                        } else {
                            className = "m_text_green"
                        }

                        htmls += `<tr class="m_border_bottom_gray">
                            <td class="m_width_33">${income_date}</td>
                            <td class="m_width_33">
                                <div>
                                    <div>${nav}</div>
                                    <span class="text_gray">${total_vol}份</span>
                                </div>
                            </td>
                            <td class="m_width_33 ${className}">${income}</td>
                        </tr>`
                    }
                } else if (!detailParams) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                if (detailParams && detailParams.length < num_per_page) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }

            if (isAppendFlag) {
                $(_pageId + " #concent").append(htmls);
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
            } else {
                $(_pageId + " #concent").html(htmls);
            }
            pageScrollInit();
        }
        service.reqFun102144(param, callback);
    }

    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {

        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    getUsertransaction(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getUsertransaction(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        if (isEnd) {//可能有当前页为1总页数为0的情况
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }

        //ios上拉拖动卡死问题解决
        var pageTouchTimer = null;
        //var contentHeight = $(_pageId + " #wrapper_w1").height()-570;
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        startTime = "";
        endTime = "";
        cur_page = 1;
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .record_filter").hide();
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        $(_pageId + " .new_none").hide();
        isEnd = false;
        $(_pageId + " #concent").html("");
    }

    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", "").val("");
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        //快捷时间筛选
        $(_pageId + " #query_date li a").removeClass("active").eq(0).addClass("active");
        $(_pageId + '#startTime').attr("time", "").val("");
        $(_pageId + '#endTime').attr("time", "").val("");
    }

    //返回页面
    function pageBack() {
    	appUtils.setSStorageInfo("trsFundCode",'');
        appUtils.pageBack();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
