// 产品整合已完成交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageId = "#template_cpdTransaction ",
        _pageCode = "template/cpdTransaction",
        tools = require("../common/tools");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var isEnd = false;
    var busiList;
    var startTime = "";
    var financial_prod_type = '';
    var endTime = "";
    var _cur_page = 1;
    var _num_per_page = 10;
    var expire_obj = {
        "13901": "自动滚入下一期",
        "13902": "自动赎回到银行卡",
        "13903": "自动赎回到晋金宝",
    }
    // 分红方式
    var dividend_obj = {
        "12901": "红利再投",
        "12902": "分红到晋金宝",
        "12903": "分红到晋金宝"
    }

    function init() {
        _cur_page = 1;
        financial_prod_type = appUtils.getSStorageInfo("financial_prod_type");
        // console.log(financial_prod_type)
        $(_pageId + ".olay").remove();
        selectDate(_pageId + '#startTime', 0, _pageId);
        selectDate(_pageId + '#endTime', 1, _pageId);
        //页面埋点初始化
        tools.initPagePointData();
        //用于接收详情页带回的参数，找回跳转前的筛选结果
        // if (appUtils.getSStorageInfo("qry_condition")) {
        //     var qry_condition = appUtils.getSStorageInfo("qry_condition");
        //     appUtils.clearSStorage("qry_condition");
        //     $(_pageId + " #query_type li a").removeClass("active");
        //     if (qry_condition.busi_type) {
        //         $(_pageId + "#query_type [data-value=" + qry_condition.busi_type + "]").addClass("active");
        //     } else {
        //         $(_pageId + " #query_type li a").eq(0).addClass("active");
        //     }
        //     $(_pageId + ' #startTime').attr('time', qry_condition.startTime).val(qry_condition.startTime);
        //     $(_pageId + ' #endTime').attr('time', qry_condition.endTime).val(qry_condition.endTime);
        //     $(_pageId + " #query_date li a").removeClass("active");
        //     if (qry_condition.date_value) {
        //         $(_pageId + "#query_date [data-value=" + qry_condition.date_value + "]").addClass("active");
        //     } else {
        //         $(_pageId + " #query_date li a").eq(0).addClass("active");
        //     }
        // } else {
        resetInputDate();
        initBusiType();
        // }
     

    }

    function bindPageEvent() {
        //筛选日期
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " #jymx_filter").show();
            $(_pageId + " #filter_layer").show();
        });
        //快捷键选择时间
        appUtils.bindEvent($(_pageId + " #query_date li a"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).addClass("active");
            var data_value = $(this).attr("data-value");
            var endTime = new Date();
            var startTime = new Date();
            if (data_value == "30") {
                startTime.setMonth(endTime.getMonth() - 1);
            } else if (data_value == "90") {
                startTime.setMonth(endTime.getMonth() - 3);
            } else if (data_value == "180") {
                startTime.setMonth(endTime.getMonth() - 6);
            } else {
                startTime = "";
                endTime = "";
            }
            setInputDate("endTime", endTime);
            setInputDate("startTime", startTime);
        });

        //确定/重置按钮绑定事件
        appUtils.bindEvent($(_pageId + " .record_filter #query-button a"), function () {
            _cur_page = 1;
            var dataType = $(this).attr("data-value");
            if (dataType == "reset") {
                //重置按钮
                resetInputDate();
            } else if (dataType == "confirm") {
                startTime = $(_pageId + " #startTime").attr("time");
                endTime = $(_pageId + " #endTime").attr("time");
                getUsertransaction(false);
                $(_pageId + " .record_filter").hide();
                $(_pageId + " .pop_layer").hide();
            }
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " #filter_layer"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
        });
        //点击取消时间控件
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");

        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转详情
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".trade_box", function (e) {
            var info = JSON.parse($(this).find(".info").text());
            //跳转详情
            judge_busi_code_e(info);
        }, "click");

    }

    function getUsertransaction(isAppendFlag) {
        isEnd = false;
        endTime = $(_pageId + '#endTime').attr('time');
        if (!validatorUtil.isEmpty(endTime)) {
            endTime = endTime.replace(/-/g, "");
        }
        startTime = $(_pageId + '#startTime').attr('time');
        if (!validatorUtil.isEmpty(startTime)) {
            startTime = startTime.replace(/-/g, "");
        }
        var param = {
            "start_date": startTime,
            "end_date": endTime,
            "cur_page": _cur_page,
            "num_per_page": _num_per_page,
            // "busi_type": $(_pageId + " #query_type li .active").attr("data-value"),
            // "financial_prod_type":financial_prod_type
        };
        var productInfo = appUtils.getSStorageInfo("productInfo");
        if (productInfo) {
            param.fund_code = productInfo.fund_code;
            param.vir_fundcode = productInfo.vir_fundcode;
            param.start_date = productInfo.start_date;
            param.end_date = productInfo.end_date;
        }
        var gettransActionCallBack = function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var totalPages = data.results[0].data.totalPages; //总页数
                var results = data.results[0].data.data;
                var str = "";
                for (var i = 0; i < results.length; i++) {
                    busiList.forEach(item => {
                        if (item.subBusiType.indexOf(results[i].sub_busi_code_e) > -1) {
                            results[i].sub_busi_code = item.busiName
                        }
                    })
                    if (results[i].sub_busi_code_e == '13901' || results[i].sub_busi_code_e == '13902' || results[i].sub_busi_code_e == '13903') { //修改到期方式
                        str += expireList(results[i]);
                    } else if (results[i].sub_busi_code_e == '12901' || results[i].sub_busi_code_e == '12902' || results[i].sub_busi_code_e == '12903') { // 修改付息方式
                        str += dividendList(results[i]);
                    } else {
                        str += tradeList(results[i]);
                    }
                }
                if (totalPages == _cur_page) {
                    isEnd = true;
                    str += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && results.length == 0) {
                    isEnd = true;
                    str = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(str);
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
            } else {
                layerUtils.iAlert(error_info);
            }
        };
        service.reqFun102126(param, gettransActionCallBack);
    }

    function initBusiType() {
        //交易类型（1.现金宝；2.公募；3.私募）产品整合后4：整合后交易类型
        var param = {
            "trans_type": "4",
        }
        service.reqFun102059(param, function (resultVo) {
            var str = "<li><a class='active' data-value=''>所有</a></li>";
            if (resultVo.error_no == "0") {
                var dataList = busiList = resultVo.results;
                getUsertransaction(false);
                for (var i = 0; i < dataList.length; i++) {
                    str += "<li><a data-value='" + dataList[i].busi_type + "'>" + dataList[i].busiName + "</a></li>"
                }
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }
            $(_pageId + " #query_type").html(str);
            // 快捷键选择类型
            appUtils.bindEvent($(_pageId + " #query_type li a"), function () {
                $(_pageId + " #query_type li a").removeClass("active");
                $(this).addClass("active");
            });
        })
    }

    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    _cur_page = 1;
                    endTime = "";
                    getUsertransaction(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        _cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getUsertransaction(true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }

    //修改付息方式列表
    function dividendList(data) {
        var html = '<div class="trade_box" operationType="1" operationId="trade_box" operationName="详情">' +
            '<div class="fundInfo">' +
            '<p class="info" style="display: none">' + JSON.stringify(data) + '</p>' +
            '<p>' + data.sub_busi_code + '</p>' +
            '<p style="width:1.6rem">' + data.fund_name + '</p>' +
            '<p>' + tools.ftime(data.crt_date + data.crt_time) + '</p>' +
            '</div>' +
            '<div class="result" style="padding-right: 0">' +
            '<p>' + dividend_obj[data.sub_busi_code_e] + '</p>' +
            '<p>' + tools.fundDataDict(data.trans_status, "pub_updateDivide_status_name") + '</p>' +
            '</div>' +
            '</div>';
        return html;
    }

    //修改到期方式列表
    function expireList(data) {
        var html = '<div class="trade_box" operationType="1" operationId="trade_box" operationName="详情">' +
            '<div class="icon">' +
            '</div>' +
            '<div class="fundInfo">' +
            '<p class="info" style="display: none">' + JSON.stringify(data) + '</p>' +
            '<p>' + data.sub_busi_code + '</p>' +
            '<p style="width:1.6rem">' + data.fund_name + '</p>' +
            '<p>' + tools.ftime(data.crt_date + data.crt_time) + '</p>' +
            '</div>' +
            '<div class="result" style="padding-right: 0">' +
            '<p>' + expire_obj[data.sub_busi_code_e] + '</p>' +
            '<p>' + tools.fundDataDict(data.trans_status, "pub_updateExpire_status_name") + '</p>' +
            '</div>' +
            '</div>';
        return html;
    }

    //交易记录列表
    function tradeList(data) {
        var amt = "";
        if (data.sub_busi_code_e == '12202' || data.sub_busi_code_e == '12002'//购买
            || data.sub_busi_code_e == '12303' || data.sub_busi_code_e == '12102' || data.sub_busi_code_e == '12302' //预约购买
            || data.sub_busi_code_e == '12205' || data.sub_busi_code_e == '12206' || data.sub_busi_code_e == '12226' || data.sub_busi_code_e == '12301' || data.sub_busi_code_e == '12001' || data.sub_busi_code_e == '12101' || data.sub_busi_code_e == '12207') {  //银行卡购买
            amt = tools.fmoney(data.app_amt) + "元";//申请金额
        } else if (data.sub_busi_code_e == '13101' || data.sub_busi_code_e == '13201' || data.sub_busi_code_e == '14301' || data.sub_busi_code_e == '14401' || data.sub_busi_code_e == "14501") { // 冻结  解冻 红利再投
            amt = tools.fmoney(data.ack_vol) + "份";//申请金额
        } else if (data.sub_busi_code_e == '12402' || data.sub_busi_code_e == '12411' || data.sub_busi_code_e == '12431' || data.sub_busi_code_e == '12404' || data.sub_busi_code_e == '12406' || data.sub_busi_code_e == '12408' || data.sub_busi_code_e == "14202") { // 赎回
            amt = tools.fmoney(data.app_vol) + "份";//赎回份额
        } else if (data.sub_busi_code_e == '15003' || data.sub_busi_code_e == '14201' || data.sub_busi_code_e == '14202' || data.sub_busi_code_e == '15002') { // 強赎
            if (data.prod_sub_type2 == "91" || data.prod_sub_type2 == "92" || data.prod_sub_type2 == "93") { //小集合 定开  源晖
                amt = tools.fmoney(data.app_vol) + "份";
            } else { //私募
                amt = tools.fmoney(data.ack_amt) + "元";//确认金额
            }
        } else {
            amt = tools.fmoney(data.ack_amt) + "元";//确认金额
        }
        var remark1 = data.remark1,
            remarkStr = "";
        if (remark1) {
            remarkStr = '<p class="remark1" style="display: inline-block;width: 100%;">备注：' + remark1 + '</p>'
        }
        var status = "";
        if (data.sub_busi_code_e == '14301' || data.sub_busi_code_e == '14302' || data.sub_busi_code_e == '14303' //分红
            || data.sub_busi_code_e == '15002' || data.sub_busi_code_e == '15003' || data.sub_busi_code_e == '14201' || data.sub_busi_code_e == '14202') { //強赎
            status = tools.fundDataDict(data.trans_status, "pri_bonus_trans_status_name")
        } else if (data.sub_busi_code_e == '12402' || data.sub_busi_code_e == '12404' || data.sub_busi_code_e == '12406' || data.sub_busi_code_e == '12408') { //赎回
            status = tools.fundDataDict(data.trans_status, "pri_redeem_status_name")
        } else {
            status = tools.fundDataDict(data.trans_status, "pri_trans_status_name")
        }
        var html = '<div class="trade_box">' +
            '<div class="icon">' +
            '</div>' +
            '<div class="fundInfo">' +
            '<p class="info" style="display: none">' + JSON.stringify(data) + '</p>' +
            '<p>' + data.sub_busi_code + '</p>' +
            '<p style="width:1.6rem">' + data.fund_name + '</p>' +
            '<p>' + tools.ftime(data.crt_date + data.crt_time) + '</p>' +
            '</div>' +
            '<div class="result right_icon" style="height: 100%">' +
            '<p>' + amt + '</p>' +
            '<p>' + status + '</p>' +
            '</div>' +
            remarkStr +
            '</div>'
        return html;
    }

    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", date);
            $(_pageId + " #" + id).val(date);
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        $(_pageId + " #query_type li a").removeClass("active").eq(0).addClass("active");
        //快捷时间筛选
        $(_pageId + " #query_date li a").removeClass("active").eq(0).addClass("active");
        $(_pageId + '#startTime').attr("time", "").val("");
        $(_pageId + '#endTime').attr("time", "").val("");
    }

    //跳转详情
    function judge_busi_code_e(info) {
        var sub_busi_code_e = info.sub_busi_code_e;
        var obj = {
            "12202": "highEnd/trsDetailsBuy", //申购
            "12002": "highEnd/trsDetailsBuy", //申购
            "12209": "highEnd/trsDetailsBuy", //基金超市一键购买
            "12303": "highEnd/trsDetailsAppoint", //预约申购
            "12102": "highEnd/trsDetailsAppoint", //预约申购
            "12302": "highEnd/trsDetailsAppoint", //预约申购
            "12208": "highEnd/trsDetailsBuy", //上一期滚入

            "12205": "highEnd/trsDetailsBank", //银行卡申购
            "12301": "highEnd/trsDetailsBank", //银行卡预约申购
            "12001": "highEnd/trsDetailsBank", //银行卡认购
            "12101": "highEnd/trsDetailsBank", //银行卡预约认购


            "15002": "highEnd/trsDetailsFore", //强制赎回 清盘到卡
            "15003": "highEnd/trsDetailsFore", //强制赎回 清盘到宝
            "14201": "highEnd/trsDetailsFore", //强制赎回 -管理人赎回（宝）
            "14202": "highEnd/trsDetailsFore", //强制赎回 -管理人赎回卡

            "12402": "highEnd/trsDetailsSell", //赎回 主动赎回（卡）
            "12404": "highEnd/trsDetailsSell", //赎回 主动赎回 （宝）
            "12406": "highEnd/trsDetailsSell", //赎回 到期自动赎回到宝（宝）
            "12408": "highEnd/trsDetailsSell", //赎回 到期自动赎回到宝（卡）
            "14401":"inclusive/additionSubtraction", //强增
            "14501":"inclusive/additionSubtraction", //强减
            
            "12413": "highEnd/trsDetailsAccumulated", //滚入下一期

            "14301": "highEnd/trsDetailsProfit", //分红 红利再投
            "14302": "highEnd/trsDetailsProfit", //分红 分红到宝
            "14303": "highEnd/trsDetailsProfit", //分红 分红到宝

            "12206" : "highEnd/trsDetailsBuy",//转让
            "12226" : "highEnd/trsDetailsBuy",
            "12411" : "highEnd/trsDetailsSell",
            "12431" : "highEnd/trsDetailsSell",
            "12207" : "highEnd/trsDetailsBuy",

        }
        var param = {
            date_value: $(_pageId + " #query_date li a.active").attr("data-value"),
            startTime: $(_pageId + " #startTime").val(),
            endTime: $(_pageId + " #endTime").val(),
            busi_type: $(_pageId + " #query_type li a.active").attr("data-value")
        }
        appUtils.setSStorageInfo("qry_condition", param); //保留此次筛选条件
        if (obj[sub_busi_code_e]) {
            appUtils.pageInit(_pageCode, obj[sub_busi_code_e], info);
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        _cur_page = 1;
        $(_pageId + " #start_date").val("查询日期");
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        isEnd = false;
        startTime = "";
        endTime = "";
        $(_pageId + " .finance_pro").html("");
    }

    function pageBack() {
        appUtils.setSStorageInfo("trsFundCode", '');
        appUtils.pageBack();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
