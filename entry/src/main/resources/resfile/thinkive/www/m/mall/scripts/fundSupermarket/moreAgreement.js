define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#template_templateBuy ";
    var ut = require("../common/userUtil");
    var _pageCode = "fundSupermarket/moreAgreement";
    var _pageId = "#fundSupermarket_moreAgreement"
    var tools = require("../common/tools");
    var userInfo;
    var global = require("gconfig").global;
    // let resultDeta
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        // 获取协议信息
        var agreements = appUtils.getSStorageInfo("agreements")
        if (agreements) {
            let html = '';
            if (agreements && agreements.payAgreementList && agreements.payAgreementList.length){
                let fixed_invest_html = "";//定投相关协议
                agreements.payAgreementList.forEach(item => {
                    fixed_invest_html += `<li href="javascript:void(0);" class="flex xy" operationType="1" operationId="xy" operationName="查看协议_${item.agreement_title}" url="${global.oss_url + item.url}"> <span>《${item.agreement_title}》</span><i></i></li>`;
                })
                html +=  `<ul class="agreement_list flex vertical_line">${fixed_invest_html}</ul>`
            }
            if (agreements && agreements.bankCardfixedInvestAgreement && agreements.bankCardfixedInvestAgreement.length){
                let fixed_invest_html = "";//定投相关协议
                agreements.bankCardfixedInvestAgreement.forEach(item => {
                    fixed_invest_html += `<li href="javascript:void(0);" class="flex xy" operationType="1" operationId="xy" operationName="查看协议_${item.agreement_title}" url="${global.oss_url + item.url}"> <span>《${item.agreement_title}》</span><i></i></li>`;
                })
                html +=  `<ul class="agreement_list flex vertical_line">${fixed_invest_html}</ul>`
            }
            if (agreements && agreements.fixedInvestAgreement && agreements.fixedInvestAgreement.length){
                let fixed_invest_html = "";//定投相关协议
                agreements.fixedInvestAgreement.forEach(item => {
                    fixed_invest_html += `<li href="javascript:void(0);" class="flex xy" operationType="1" operationId="xy" operationName="查看协议_${item.agreement_title}" url="${global.oss_url + item.url}"> <span>《${item.agreement_title}》</span><i></i></li>`;
                })
                html +=  `<ul class="agreement_list flex vertical_line">${fixed_invest_html}</ul>`
            }
            if (agreements && agreements.list && agreements.list.length) {
                agreements.list.forEach(item => {
                    item.agg_list = JSON.parse(item.agg_list);
                    let sub_html = "";
                    if (item.agg_list && item.agg_list.length) {
                        item.agg_list.forEach(its => {
                            sub_html += `<li href="javascript:void(0);" class="flex xy" operationType="1" operationId="xy" operationName="查看协议_${its.agreement_title}" url="${global.oss_url + its.url}"> <span>《${its.agreement_title}》</span><i></i></li>`;
                        })
                    }
                    html += ` <div style="padding: 0.1rem;font-size: 16px" class="m_bold m_text_darkgray">${item.prod_name}</div>
                    <ul style="border-top:1px solid #f0f0f0;" class="agreement_list flex vertical_line">${sub_html}</ul>`
                })
            }
            $(_pageId + " .prod_agreement_list").html(html);
        }
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //返回页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //查看PDF文件
        appUtils.preBindEvent($(_pageId + " .prod_agreement_list"), ".xy", function (e) {
            e.preventDefault();
            e.stopPropagation();
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["url"] = url;
            param["title"] = title;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        }, "click");
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }
    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
