//撤单并支取
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_earlyDraw ";
    var ut = require("../common/userUtil");
    var _pageCode = "bank/earlyDraw";
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var sms_mobile = require("../common/sms_mobile");
    var hang_list_amt; //持有金额
    var bankElectornReservedMobile;
    var productInfo;// 产品信息
    var bankElectronName;
    var bankElectronNo;
    var acct_keep_min; //保留金额
    var drw_low_amt; //支取最小金额
    var drw_long_amt; //支取递增金额

    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        getAccountInfo();
        initProductInfo();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "bank/bankDetail");
        });
        //全部
        appUtils.bindEvent($(_pageId + " .allMoney"), function () {
            $(_pageId + " #money").val(hang_list_amt);
            $(_pageId + " #inputspanid span").html(tools.fmoney(hang_list_amt)).css({color: "#000000"}).addClass("active");
        });


        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "bank_earlyDraw";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                if (money <= 0 || !money) {
                    layerUtils.iAlert("请输入支取金额");
                    return;
                }
                if (parseFloat(money.replace(/,/g, "")) < parseFloat(drw_low_amt)) {
                    layerUtils.iAlert("最小支取金额" + tools.fmoney(drw_low_amt) + "元");
                    return;
                }
                if (money && drw_low_amt && drw_long_amt && Math.round((parseFloat(money.replace(/,/g, "")) - drw_low_amt) % drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("支取递增金额为" + tools.fmoney(drw_long_amt) + "元");
                    return
                }
                if (money && acct_keep_min && (parseFloat(hang_list_amt * 100 - money * 100) < parseFloat(acct_keep_min * 100) && parseFloat(hang_list_amt * 100 - money * 100) != 0)) { //持有金额 - 输入金额 == 0 || 持有金额 - 输入金额 > 保留金额
                    layerUtils.iAlert("剩余金额不能低于" + tools.fmoney(acct_keep_min) + "元");
                    return
                }
                var param = {
                    "send_type": "0",
                    "mobile_phone": bankElectornReservedMobile,
                    "type": common.sms_type.bankEarlyDraw,
                    "bank_abbr": productInfo.prod_name
                };
                sms_mobile.sendPhoneCode(param)
            }
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var money = $(_pageId + " #money").val().replace(/,/g, "");

            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (money <= 0 || !money) {
                layerUtils.iAlert("请输入支取金额");
                return;
            }
            if (parseFloat(money.replace(/,/g, "")) < parseFloat(drw_low_amt)) {
                layerUtils.iAlert("最小支取金额" + tools.fmoney(drw_low_amt) + "元");
                return;
            }
            if (money && drw_low_amt && drw_long_amt && Math.round((parseFloat(money.replace(/,/g, "")) - drw_low_amt) % drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                layerUtils.iAlert("支取递增金额为" + tools.fmoney(drw_long_amt) + "元");
                return
            }
            if (money && acct_keep_min && (parseFloat(hang_list_amt * 100 - money * 100) < parseFloat(acct_keep_min * 100) && parseFloat(hang_list_amt * 100 - money * 100) != 0)) { //持有金额 - 输入金额 == 0 || 持有金额 - 输入金额 > 保留金额
                layerUtils.iAlert("剩余金额不能低于" + tools.fmoney(acct_keep_min) + "元");
                return
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            //进行存入
            var trans_amt = $(_pageId + " #money").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                trans_amt: trans_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                prod_code: productInfo.prod_code,
                sms_code: verificationCode,
                sms_mobile: bankElectornReservedMobile,
                order_no: productInfo.order_no,
                brnd_sris: productInfo.brnd_sris,
                hang_list_grp_srlno: productInfo.hang_list_grp_srlno,
            };
            trade(param);
        });
    }

    function trade(param) {
        service.reqFun151015(param, function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/drawResult", {
                trans_serno: trans_serno,
                bankElectronName: bankElectronName,
                bankElectronNo: bankElectronNo,
            });
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    curVal = tools.fmoney(moneys);
                }
                $(_pageId + " #money").val(curVal);
                if (parseFloat(moneys) < parseFloat(drw_low_amt)) {
                    layerUtils.iAlert("最小支取金额" + tools.fmoney(drw_low_amt) + "元");
                    return;
                }
                if (moneys && drw_low_amt && drw_long_amt && Math.round((parseFloat(moneys) - drw_low_amt) % drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("支取递增金额为" + tools.fmoney(drw_long_amt) + "元");
                    return;
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #money").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #money").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
                if (parseFloat(moneys) < parseFloat(drw_low_amt)) {
                    layerUtils.iAlert("最小支取金额" + tools.fmoney(drw_low_amt) + "元")
                    return;
                }
                if (moneys && drw_low_amt && drw_long_amt && Math.round((parseFloat(moneys.replace(/,/g, "")) - drw_low_amt) % drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("支取递增金额为" + tools.fmoney(drw_long_amt) + "元");
                    return;
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    curVal = curVal.substring(0, curVal.length - 1)
                    // $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }

                if (parseFloat(curVal) > parseFloat(hang_list_amt)) {
                    $(_pageId + " #money").val(hang_list_amt);
                } else {
                    $(_pageId + " #money").val(curVal);
                }
            }
        })
    }

    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                $(_pageId + " .bankIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "(尾号" + results.acct_no.substr(-4) + ")");
                bankElectornReservedMobile = results.mobile_phone;
                bankElectronName = results.bank_channel_name;
                bankElectronNo = results.acct_no;
                $(_pageId + " .bank_act_int").text(results.bank_act_int);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, {isGlobal: true, isShowWait: true})
    }

    function initProductInfo() {
        $(_pageId + " .prod_sname").text(productInfo.prod_name);
        hang_list_amt = productInfo.hang_list_amt;
        $(_pageId + " #inputspanid span").text("可支取金额" + tools.fmoney(hang_list_amt) + "元");
        $(_pageId + " #inputspanid span").attr("text", "可支取金额" + tools.fmoney(hang_list_amt) + "元");
        $(_pageId + " .rate").text(tools.fmoney(productInfo.bas_int_rate) + "%");
        $(_pageId + " .save_dt").text(productInfo.save_dt);
        $(_pageId + " #money").val("");
        var params = {
            bank_channel_code: productInfo.bank_channel_code,
            prod_code: productInfo.prod_code
        };
        service.reqFun151117(params, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.prod_info) {
                    var prod_info = results.prod_info;
                    acct_keep_min = prod_info.acct_keep_min;
                    drw_low_amt = prod_info.drw_low_amt;
                    drw_long_amt = prod_info.drw_long_amt;
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, {isGlobal: true, isShowWait: true})
    }

    function destroy() {
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .rate").html("--");//产品编码
        $(_pageId + " #inputspanid span").text("请输入支取金额").css({color: "#999999"}).removeClass("active");
        $(_pageId + " #inputspanid span").attr("text", "请输入支取金额");
        $(_pageId + " #money").val("");
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " .bankIcon img").attr("src", "");
        $(_pageId + " .bank_electron_info").html("--");
        $(_pageId + " .bank_act_int").text("--");
        $(_pageId + " .save_dt").text("--");

        guanbi();
        sms_mobile.destroy();
        monkeywords.destroy();
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
