// 源晖产品列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "yuanhui/fundList",
        _pageId = "#yuanhui_fundList ";
    var btnObj = {
        "1": {
            btnClass: "",
            btnText: "购买"
        },
        "2": {
            btnClass: "",
            btnText: "预约"
        },
        "3": {
            btnClass: "",
            btnText: "敬请期待"
        },
        "4": {
            btnClass: "sold_out",
            btnText: "封闭中"
        },
    }

    function init() {
        //获取源晖产品列表
        getYuanhuiList();
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //购买
        appUtils.preBindEvent($(_pageId + " .highEndDiv"), ".pro_detail", function (e) {
            var prod_sub_type = $(this).attr("prod_sub_type");
            var fund_code = $(this).attr("fund_code");
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("fund_code", fund_code);
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.setSStorageInfo("prod_sub_type", prod_sub_type);
            appUtils.pageInit(_page_code, "yuanhui/productDetail", {});
        });
    }
    //获取排序参数根据渠道吗
    async function get_recommend_type(channelCode){
        return new Promise(async (resolve, reject) => {
            service.reqFun102105({channel_code:channelCode},  (datas)=> {
                if (datas.error_no == 0) {
                    let results = datas.results[0];
                    resolve(results)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }
    async function getYuanhuiList() {
        let channelCode = common.getLocalStorage("download_channel_code");
        let res = await get_recommend_type(channelCode)
        service.reqFun102095({recommend_type:res.list_recommend_type}, function (datas) {
            if (datas.error_no == 0) {
                var productList = datas.results;
                if(productList.length == 0) {
                    $(_pageId + " .highEndDiv").html("<div class='nodata '>暂无数据</div>");
                    return;
                }
                var str = "";
                for (var i = 0; i < productList.length; i++) {
                    var fund_code = productList[i].fund_code; //产品代码
                    var strategy = productList[i].yh_prod_strategy_desc;//产品策略
                    var strategy_code = productList[i].yh_prod_strategy;//产品策略 代码
                    var prod_sname = productList[i].prod_sname; //产品简称
                    var buy_state = productList[i].buy_state; //按钮状态
                    var nav = productList[i].nav; //净值
                    var nav_date = productList[i].nav_date; //净值日期
                    var this_year_rate = productList[i].this_year_rate; //今年以来收益
                    var found_rate = productList[i].found_rate; //成立以来收益

                    if (i == 0 || strategy_code !== productList[i - 1].yh_prod_strategy) {
                        str += "<h3 class='highEnd_area' style='padding:0.1rem'><span style='font-size:0.16rem'>" + strategy + "</span></h3>"
                    }
                    str += "<div class='pro_detail' fund_code='" + fund_code + "' buy_state='" + buy_state + "'>" +
                        "<h4>" + prod_sname + "</h4>" +
                        "<p>最新净值: <span class='redcol'>" + tools.fmoney(nav,4) + "</span> </p>" +
                        "<p>今年来收益: <span>" + (this_year_rate ? tools.fmoney(this_year_rate) + "%" : "--") + "</span> <span style='margin-left: 0.2rem'>成立以来收益：" + (found_rate ? tools.fmoney(found_rate) : "--") + "</span>%</p>" +
                        "<p class='productInfo' style='display: none'>" + JSON.stringify(productList[i]) + "</p>" +
                        "<a product_id='href='javascript:void(0)'' class='buy_btn pop in " + btnObj[buy_state].btnClass + "'>" + btnObj[buy_state].btnText + "</a></div>"

                }

                $(_pageId + " .highEndDiv").html(str);

            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }


    function destroy() {
        $(_pageId + ".qualifiedInvestor").hide();
        $(_pageId + " .finance_pro").html("")
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var yuanhuifundList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = yuanhuifundList;
});
