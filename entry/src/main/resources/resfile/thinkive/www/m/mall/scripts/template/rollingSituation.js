//滚入本期前投资情况
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        _pageId = "#template_rollingSituation ";
    var tools = require("../common/tools");
    var _pageCode = "template/rollingSituation";
    var ut = require("../common/userUtil");
    let getData;
    let userInfo;
    var gconfig = require("gconfig");
    var global = gconfig.global;
    function init() {
        getData = appUtils.getSStorageInfo("productInfo");
        document.querySelector('.fixed-table-container').scrollLeft = 0;
        userInfo = ut.getUserInf();
        setData();
    }


  
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
     
        appUtils.bindEvent($(_pageId + " .consultant"), function () {
            appUtils.pageInit(_pageCode, "account/wealthAdvisor");
        });
        //打电话
        appUtils.bindEvent($(_pageId + " .callPhone"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
    }

  

    function setData() {
        let queuest = {
            fund_code: getData.fund_code,
            cust_no: userInfo.custNo,
            vir_fundcode: getData.vir_fundcode,
        }
        service.reqFun101950(queuest, function (data) {
            if (data.error_no == '0') {

                if (data.results[0].data.length > 0) {
                    let arr = data.results[0].data
                    let html = ''
                    for (let i = 0; i < arr.length; i++) {
                        let status_name;
                        let status_html;
                        if (arr[i].fund_status == '1') {
                            status_name ="卖出"
                            status_html = "投资收益"+tools.changeTwoDecimal_f(arr[i].hold_income)+"元，分红到宝"+tools.changeTwoDecimal_f(arr[i].dividend_amt)+"元"

                        } else if (arr[i].fund_status == '0'){
                            status_name ="滚入"
                            let rolling_amt = tools.changeTwoDecimal_f(arr[i].hold_income-arr[i].dividend_amt);
                            status_html = "投资收益"+tools.changeTwoDecimal_f(arr[i].hold_income)+"元，滚入下一期"+tools.changeTwoDecimal_f(rolling_amt)+"元，分红到宝"+tools.changeTwoDecimal_f(arr[i].dividend_amt)+"元"
                        }
                        
                        html += '<tr style="color:#000;"><td>' + status_name + '</td><td>' + tools.changeTwoDecimal_f(arr[i].costmoney) + '</td><td>' + tools.changeTwoDecimal_f(arr[i].hold_income) + '</td><td>' + tools.changeTwoDecimal_f(arr[i].dividend_amt) + '</td><td>'+ tools.changeTwoDecimal_f(arr[i].rate_amt) +'%</td><td>'+tools.changeTwoDecimal_f(arr[i].annualize_income)+'%</td><td>'+arr[i].income_date+'</td> </tr>'
                        html +='<tr style="font-size:0.1rem;"><td colspan="7" style="text-align: left;padding-left: 0.4rem;border-bottom: 0.1rem solid #f2f6f8;">'+status_html+'</td></tr>'
                    }
                    $(_pageId + " .getDate").html(html)
                } else {
                    $(_pageId + " .getDate").html("暂无数据")
                }
            } else {
                $(_pageId + " .getDate").html("暂无数据")
            }
        })
    }
    function destroy() {
        $(_pageId + " .getDate").html("")
    }
    function pageBack() {
        appUtils.pageBack();
    }

    var bondFixMochikura = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bondFixMochikura;
});
