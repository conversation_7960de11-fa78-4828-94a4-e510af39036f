// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            common = require("common"),
            tools = require("../common/tools"),
            service = require("mobileService"),
            _page_code = "highEnd/riskNote",
            _pageId = "#highEnd_riskNote ";
    var submitBtn = false
    var html = '';
    var productInfo;
    var tipSt;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        initTip();
        queryFxjs()
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .close_btn"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #sure"), function () {
            if ($(_pageId + " #sure").hasClass("disable")) return;
            $(_pageId + " #tit_riseNote").hide();
        });
        //单个项选择
        appUtils.preBindEvent($(_pageId + " .risk-note-check"), "dd", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var isChecked = $(this).hasClass('checked')
            toggleItem($(this), !isChecked)

            var isAll = !isChecked ? isAllChecked() : false  //取消选择时肯定为false
            toggleItem($(_pageId + '.risk-check-all'), isAll)
            btnState(isAll)

        }, 'click');

        //全选按钮点击
        $(_pageId + '.risk-check-all').on('click', function () {
            var isSelected = $(this).hasClass('checked')
            toggleItem($(this), !isSelected)
            toggleItem($(_pageId + '.risk-note-check dd'), !isSelected)
            btnState(!isSelected)
        })

        //同意签署按钮点击
        $(_pageId + '#riskAgreeBtn').on('click', function () {
            if (submitBtn) {
                var param = {fund_code: productInfo.fund_code}
                if(productInfo.entrust_no) param.transfer_flag = '1'
                service.reqFun101036(param, function (res) {
                    if (res.error_no != '0') {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(res.error_info);
                        return;
                    }
                    service.reqFun106014({fund_code: productInfo.fund_code}, function (data) {
                        if (data.error_no != '0') {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }

                        if ((productInfo.prod_sub_type2 == "100" &&  productInfo.prod_sub_type == "90") || productInfo.prod_sub_type2 == "90" || productInfo.prod_sub_type2 == "93" || productInfo.prod_sub_type2 == "92" || productInfo.prod_sub_type2 == "94" || productInfo.prod_sub_type2 == "97") {
                            appUtils.pageInit(_page_code, "highEnd/index");
                        } else if ((productInfo.prod_sub_type2 == "100" &&  productInfo.prod_sub_type == "80") || productInfo.prod_sub_type2 == "91" || productInfo.prod_sub_type2 == "95" || productInfo.prod_sub_type2 == "96" || productInfo.prod_sub_type2 == "81") {
                            appUtils.pageInit(_page_code, "highEnd/indexPremise");
                        }
                    })
                }, {isLastReq: false})
            } else {
                layerUtils.iAlert('请逐一勾选以上声明内容')
            }

        })
    }

    //查询私募产品风险揭示书（102046）
    function queryFxjs() {
        var param = {fund_code: productInfo.fund_code}
        service.reqFun102046(param, function (res) {
            if (res.error_no == '0') {
                var list = res.results
                $(_pageId + " .total").text(list.length);

                for (var i in list) {
                    if (list.hasOwnProperty(i) == true) {
                        var serial = Number(i) + 1
                        html += "<dd operationType='1' operationId='risk-check-item_" + i + "' operationName='" + list[i].text + "'><i  class='mini-check-icon risk-bigger'></i><div class='risk-check-item'>" + serial + "、" + list[i].text + "</div></dd>"
                    }
                }
                $(_pageId + " .content").html(html);
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })
    }

    //初始化提示框
    function initTip() {
        var param = {fund_code: productInfo.fund_code}
        service.reqFun102082(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results;
            if (results.length == 0) {
                return;
            }
            var risk_disclosure_text = results[0].risk_disclosure_text;
            var time = results[0].risk_disclosure_time;
            var risk_disclosure_selectall = results[0].risk_disclosure_selectall;
            if (risk_disclosure_selectall == "1") {
                $(_pageId + " .all_check").removeClass("hidden");
                $(_pageId + " .no_check").addClass("hidden");
            } else {
                $(_pageId + " .all_check").addClass("hidden");
                $(_pageId + " .no_check").removeClass("hidden");
            }
            if (!time && !risk_disclosure_text) return;
            $(_pageId + " .tit_riseNote_text").html(risk_disclosure_text);
            $(_pageId + " #tit_riseNote").show();
            $(_pageId + " #sure").html("阅读并同意（" + time + "秒）");
            $(_pageId + " #sure").addClass("disable");
            tipSt = setInterval(function () {
                if (time > 1) {
                    time--;
                    $(_pageId + " #sure").html("阅读并同意（" + time + "秒）");
                } else {
                    $(_pageId + " #sure").html("阅读并同意");
                    $(_pageId + " #sure").removeClass("disable");
                    clearInterval(tipSt);
                }
            }, 1000)
        })

    }

    //判断是否全选
    function isAllChecked() {
        return $(_pageId + '.risk-note-check').find('dd:not(.checked)').length == 0
    }

    //切换选项
    function toggleItem(el, isCheck) {
        var dealFun = isCheck ? 'addClass' : 'removeClass'
        el[dealFun]('checked')
                .children('.mini-check-icon')[dealFun]('checked')
    }

    //设置按钮是否可点
    function btnState(canClick) {
        var agreeBtn = $(_pageId + '#riskAgreeBtn')
        if (canClick) {
            agreeBtn.removeClass('disabled')
            submitBtn = true
        } else {
            agreeBtn.addClass('disabled')
            submitBtn = false
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .content").html("")
        $(_pageId + '.risk-check-all').removeClass("checked")
        $(_pageId + '.risk-check-all i').removeClass("checked")
        submitBtn = false;
        $(_pageId + " #tit_riseNote").hide();
        $(_pageId + " #sure").addClass("disable");
        $(_pageId + " #tit_riseNote").hide();
        $(_pageId + " .tit_riseNote_text").html("");
        html = '';
        clearInterval(tipSt);
        $(_pageId + " .all_check").addClass("hidden");
        $(_pageId + " .no_check").addClass("hidden");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
