<div class="page" id="thfund_enchashment" data-pageTitle="取现" data-isSaveDom="false"  data-refresh="true" style="-webkit-overflow-scrolling : touch;">
	<div class="pop_layer" style="display:none;position: absolute;">
		<div class="password_box">
			<div class="password_inner slidedown in">
				<a href="javascript:void(0);" id="close" operationType="1" operationId="close" operationName="关闭密码输入框" class="close_btn"></a>
				<h4>请输入<span>6</span>位交易密码</h4>
				 <h6 id="rechargeInfo"><!-- 使用<em>尾号7782光大银行卡，</em>充值现金宝<em>2.00</em>元 --></h6>
				<div class="password_input">
				<span id="span00"></span>
                <span id="span01"></span>
                <span id="span02"></span>
                <span id="span03"></span>
                <span id="span04"></span>
                <span id="span05"></span>
                	<input type="text" id="jymm" maxlength="6"  style="display:none;">
				</div>
				<a href="javascript:void(0);" operationType="1" operationId="queDing" operationName="确定密码" id="queDing" class="sure_btn text-center">确定</a>
			</div>
		</div>
	</div>
	<section class="main fixed" data-page="home" id="product" style="padding-bottom: 0">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="#" operationType="1" operationId="getBack" operationName="返回" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">取现</h1>
				<!-- <a id="kefu" href="javascript:void(0)" class="coustomer-service-icon">
					<img src = "./images/customerService.png" >
				</a> -->
			</div>
		</header>
		<article class="no_padding">
			<!-- CASH_BOX START -->
			<div class="cash_box">
				<div class="available_cash">
					<h2 class="text-center">可取现金额(元)</h2>
					<strong class="block text-center"  id="withdraw_vol"></strong>
					<p class="mt10"  id="bankName" ><span id="bankCardNum"></span></p>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text input_box2 has_border">
						<label class="short_label">取现金额</label>
						<span style="position:absolute;height: 0.44rem;left:0.8rem ;right: 0;"
							  id='inputspanid' operationType="1" operationId="inputspanid" operationName="输入金额弹出数字键盘"><span style="position: absolute; left: 0px; bottom: 0.14rem; height: 0.16rem; width: auto; line-height: 1.3;" text='请输入取现金额'>请输入取现金额</span></span>
						<input custom_keybord="0" type="text" onkeyup="value=value.replace(/[^\d\.\,]/g,'')"
							   onblur="value=value.replace(/[^\d\.\,]/g,'')" id="qxje" maxlength="12"
							   class="ui input" placeholder="请输入取现金额"
							   style="outline: none;border: none;margin-left:5px;display: none;"
							   readonly="readonly">
<!--						<span style="position:absolute;height: 0.44rem;left:0.8rem ;right: 0;" id='inputspanid'><span style="color: #999999;line-height: 0.44rem;" text='请输入取现金额'>请输入取现金额</span></span>-->
<!--						<input type="text"  onkeyup="value=value.replace(/[^\d\.\,]/g,'')" onblur="value=value.replace(/[^\d\.\,]/g,'')"  class="ui input"  maxlength="15" placeholder="请输入取现金额" id="qxje" readonly="readonly" style="display: none;">-->
					</div>
				</div>
				<div class="radio_box2" id="qxfs">
					到账方式
					<div class="ui radio" id="method1" operationType="1" operationId="method1" operationName="普通取现">
						<input type="radio" id="radio_1" value="1">
						<label>普通</label>
					</div>
					<div class="ui radio" id="method2" operationType="1" operationId="method2" operationName="实时取现">
						<input type="radio" id="radio_2" value="0" checked="checked">
						<label>实时</label>
					</div>
				</div>
				<div class="radio_box2 qxWarn" style="padding: 0.1rem;background: #F0F0F0;height: auto;line-height: 1.5em;" id="ptts">
						<p>15:00前取现<span id="eTime">--</span>12:00左右到账,15:00后取现<span id="lTime">--</span>12:00左右到账</p>
				</div>
				<div class="grid_03 grid_02 grid mt10">
					<div class="ui field text input_box2 has_border" id="yzmBox">
						<label class="short_label2">验证码</label>
						<input type="tel" class="ui input code_input" id="yzm" type="tel"  maxlength="6"  placeholder="">
						<a id="getYzm" operationType="1" operationId="getYzm" operationName="获取验证码" data-state="true">获取验证码</a>
					</div>
				</div>
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="weihao"   style="display:none" ><dd>
					</dl>
				</div>
				<!-- 语音验证码 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode"   style="display:none"  >如收不到短信 &nbsp;&nbsp;<span id="getTalk" operationType="1" operationId="getTalk" operationName="获取语音验证码" style="color:blue">语音获取</span><dd>
					</dl>
				</div>
				<!-- 语音验证码 -->

				<div class="agreement">
                    <div class="radio_box mt20">
                        <div class="ui radio">
                            <input type="radio" id="input_radio2" checked="checked">
                            <label id="isChecked" operationType="1" operationId="isChecked" operationName="点击协议"></label>
                        </div>我已阅读并同意签署<span class="deal_box"></span>
                    </div>
                </div>
			    <div class="grid_02">
					<a href="javascript:void(0);" id="nextStep" operationType="1" operationId="nextStep" operationName="下一步" class="ui button block rounded btn_next pop in">下一步</a>
				</div>
				<div class="cash_rules">
					<strong>*取现规则</strong>
					<table width="100%" class="ui table rounded" cellspacing="0" cellpadding="0">
						<tbody><tr>
							<th colspan="2" >取现限额</th>
						</tr>
						<tr>
							<td width="50%">实时</td>
							<td>普通</td>
						</tr>
						<tr>
							<td height="60">单日累计10000元</td>
							<td class="dayLimit">不限</td>
						</tr>
						</tbody></table>
					<strong>*普通取现到账时间具体如下</strong>
					<table width="100%" class="ui table rounded" cellspacing="0" cellpadding="0">
						<tbody><tr style="background: #ffffff;">
							<th width="56%">申请日</th>
							<th width="22%">到账日</th>
							<th>收益截止日</th>
						</tr>
						<tr>
							<td>周一0：00-周一15：00</td>
							<td>周二</td>
							<td>周一</td>
						</tr>
						<tr>
							<td>周一15：00(含15：00)-周二15：00</td>
							<td>周三</td>
							<td>周二</td>
						</tr>
						<tr>
							<td>周二15：00(含15：00)-周三15：00</td>
							<td>周四</td>
							<td>周三</td>
						</tr>
						<tr>
							<td>周三15：00(含15：00)-周四15：00</td>
							<td>周五</td>
							<td>周四</td>
						</tr>
						<tr>
							<td>周四15：00(含15：00)-周五15：00</td>
							<td>下周一</td>
							<td>周日</td>
						</tr>
						<tr>
							<td>周五15：00(含15：00)-周日24：00</td>
							<td>下周二</td>
							<td>下周一</td>
						</tr>
						</tbody></table>
					<div style="margin-top: 0.15rem;">
						<div style="float: left;width: 10%;line-height: 1.3;">注：</div>
						<div style="float: left;width: 90%;">
							<p style="margin-bottom: 0.01rem;margin-top: 0;">1.交易日为沪深交易所的交易日；申请日遇非交易日，自动顺延至下一交易日。</p>
							<p style="margin-top: 0;">2.实时取现服务非法定义务，提现有条件，依约可暂停。</p>
						</div>
					</div>
					<!-- 协议相关弹窗 -->
					<div class="agreement_layer display_none">
						<div class="agreement_popup in">
							<div class="agreement_popup_header">
								<div class="new_close_btn" operationType="1" operationId="new_close_btn" operationName="关闭协议弹窗"></div>
								<h3>相关协议</h3>
							</div>
							<ul class="agreement_list flex vertical_line"></ul>
						</div>
					</div>
				</div>
			</div>
			<!-- CASH_BOX END -->
		</article>
	</section>
</div>
