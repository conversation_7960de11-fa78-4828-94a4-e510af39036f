// 上传收入证明
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        publicUtil = require('./publicUtil'),
        _page_code = "highEnd/qualifiedInvestor3",
        _pageId = "#highEnd_qualifiedInvestor3 ";
    var surplusAmount;
    var productInfo;

    function init() {
        $(_pageId + " #vip_pop_layer").hide();
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        surplusAmount = appUtils.getSStorageInfo("surplusAmount");
        $(_pageId + " .surplusAmount").html(tools.fmoney(surplusAmount));
        if (productInfo.prod_sub_type2 == "100") {
            $(_pageId + " .averageIncome").html(publicUtil.assetInfo[productInfo.prod_sub_type2][productInfo.prod_sub_type].averageIncome);
        } else {
            $(_pageId + " .averageIncome").html(publicUtil.assetInfo[productInfo.prod_sub_type2].averageIncome);
        }
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .agreement .agreement2"), function () {
            $(this).find('i').toggleClass("active");
        });
        // 查看模板
        appUtils.bindEvent($(_pageId + " #assetsModel"), function () {
            appUtils.setSStorageInfo("isSave", true);
            appUtils.pageInit(_page_code, "highEnd/incomeModel");
        });
        // 批量上传图片
        appUtils.bindEvent($(_pageId + " .firstAdd"), function () {
            // let str = '<div class="pop_layer camera_pop_layer">\n' +
            //         '            <div  class="slideup in">\n' +
            //         '                <div class="camera">拍照</div>\n' +
            //         '                <div class="album">从相册中选取</div>\n' +
            //         '                <div class="cancel_camera">取消</div>\n' +
            //         '            </div>\n' +
            //         '        </div>'
            // if ($(_pageId + " .camera_pop_layer").length > 0) {
            //     $(_pageId + " .camera_pop_layer").show();
            // } else {
            //     $(_pageId).append(str);
            // }
            var param = {};
            let platform = require("gconfig").platform;
            param["funcNo"] = "50277";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (platform == "2" || platform == "5") {
                param["titleColor"] = "#ffffff";
            }
            param['paramExt'] = {
                type: "qualifiedInvestor",
                name: "firstAdd",
                uploadType: "2",
                multi: true
            };
            // param["compress"] = "0.5";
            param["compressSize"] = '200';
            param["width"] = "1600";
            param["height"] = "900";
            param["cutFlag"] = "0";
            tools.fileImg(_pageId, param)
            // //调用相册
            // appUtils.bindEvent($(_pageId + " .album"), function () {
            //     param["mode"] = 1
            //     require("external").callMessage(param);
            // });
            // //调用相机
            // appUtils.bindEvent($(_pageId + " .camera"), function () {
            //     param["mode"] = 2
            //     require("external").callMessage(param);
            // });
            // //关闭弹框
            // appUtils.bindEvent($(_pageId + " .camera_pop_layer"), function (e) {
            //     e.stopPropagation();
            //     $(_pageId + ' .camera_pop_layer').hide();
            // });
            // appUtils.bindEvent($(_pageId + " .cancel_camera"), function (e) {
            //     $(_pageId + ' .camera_pop_layer').hide();
            // });
            // require("external").callMessage(param);
        });
        // 上传图片
        appUtils.preBindEvent($(_pageId + " .uploadImg"), ".uploadBtn", function (e) {
            var param = {};
            let platform = require("gconfig").platform;
            param["funcNo"] = "50277";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (platform == "2" || platform == "5") {
                param["titleColor"] = "#ffffff";
            }
            param['paramExt'] = {
                type: "qualifiedInvestor",
                name: "uploadBtn",
                uploadType: "2",
                multi: true
            };
            // param["compress"] = "0.5";
            param["compressSize"] = '200';
            param["width"] = "1600";
            param["height"] = "900";
            param["cutFlag"] = "0";
            tools.fileImg(_pageId, param)
            // require("external").callMessage(param);
        });
        // 确认上传
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var base64_list = [];
            for (var i = 0; i < $(_pageId + " .uploadImg img").length; i++) {
                base64_list.push($(_pageId + " .uploadImg img").eq(i).attr("src").replace("data:image/gif;base64,", ""));
            }
            if (base64_list.length == 0) {
                layerUtils.iAlert("请上传图片");
                return;
            }
            var agreementflag = $(_pageId + " .agreement .agreement2 i").hasClass('active');
            if (!agreementflag) {
                layerUtils.iAlert("请承诺材料真实有效");
                return;
            }
            //100类型判断
            let objective = (productInfo.prod_sub_type2 == "100" && productInfo.prod_sub_type == "90") ? "90" : (productInfo.prod_sub_type2 == "100" && productInfo.prod_sub_type == "80") ? '91' : null
            if (productInfo.prod_sub_type2 == "90" || productInfo.prod_sub_type2 == "92" || productInfo.prod_sub_type2 == "93" || productInfo.prod_sub_type2 == "94" || productInfo.prod_sub_type2 == "97") {
                productInfo.prod_sub_type2 = "90";
            } else {
                productInfo.prod_sub_type2 = "91";
            }
            $(_pageId + " #vip_pop_layer").show();
            service.reqFun101034({
                type: "3",
                objective: objective ? objective : productInfo.prod_sub_type2,
                base64_list: base64_list,
                certia_mount: surplusAmount.replace(/,/g, "")
            }, function (data) {
                if (data.error_no == "0") {
                    var user = appUtils.getSStorageInfo("user");
                    user.hgSoonInvalidState = "5";
                    appUtils.setSStorageInfo("user", user);
                    $(_pageId + " #vip_pop_layer").hide();
                    appUtils.pageInit(_page_code, "highEnd/qualifiedInvestorVerify");
                } else {
                    $(_pageId + " #vip_pop_layer").hide();
                    layerUtils.iAlert(data.error_info)
                }
            },{ "isShowWait": false })
        });
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #vip_pop_layer").hide();
        if (appUtils.getSStorageInfo("isSave")) {
            appUtils.clearSStorage("isSave");
            return;
        }
        $(_pageId + " .uploadImg img").remove();
        $(_pageId + " .uploadBtn").hide();
        $(_pageId + " .firstAdd").show();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
