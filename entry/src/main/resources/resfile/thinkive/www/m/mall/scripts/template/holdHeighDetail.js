//私募 持仓详情页面
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        ut = require("../common/userUtil"),
        common = require("common"),
        monkeywords = require("mall/scripts/common/moneykeywords"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageUrl = "template/holdHeighDetail",
        _pageId = "#template_holdHeighDetail";
    let highEndHoldDetail, template_holdHeighDetail
    var gconfig = require("gconfig");
    var global = gconfig.global;
    require("chartsUtils");
    //初始化
    async function init() {
        let newData = appUtils.getPageParam();
        if(newData && newData.jump_page_virfundcode){    //弹窗特殊与情况
            let res = await detailData(newData);
            res.vir_fundcode = newData.jump_page_virfundcode;
            appUtils.setSStorageInfo("productInfo", res); //产品信息
            appUtils.setSStorageInfo("fund_code", res.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", res.prod_sub_type);
            appUtils.setSStorageInfo("financial_prod_type", res.financial_prod_type);
        }
        highEndHoldDetail = appUtils.getSStorageInfo("productInfo");  //缓存的列表详情
        sessionStorage.vir_fundcode = highEndHoldDetail.vir_fundcode
        //获取模板
        let data = {
            fund_code: highEndHoldDetail.fund_code,
            type: '1'
        }
        //页面埋点初始化
        tools.initPagePointData({fundCode:highEndHoldDetail.fund_code});
        let html = await getHtmlData(data)
        $(_pageId + ' .main_template').html(html)
        //new vue 示例对象
        template_holdHeighDetail = new Vue({
            el: '#main_template',
            //绑定的数据
            data() {
                return {
                    oss_url:global.oss_url,
					button_flag:false,
                    transferData:{},//转让产品参数
                    flag_data: {},//按钮详情
                    //持仓详情
                    sold_state: '',//卖出状态
                    detail: {},
                    hanging_flag: '',//挂单按钮
                    buy_flag: "",    //购买按钮
                    sell_flag: "",   //卖出按钮
                    prodMsg: [],
                    subtitleShow: true,//是否展示副标题var defdividend_method_obj = {
                    //分红方式
                    defdividend_method_obj: {
                        "1": "红利再投",
                        "2": "分红到晋金宝",
                        "3": "分红到晋金宝",
                    },
                    allowTransfer: '',//是否可转让
                    //产品到期方式
                    endflag_method_text_obj: {
                        "0": "自动滚入下一期",
                        "1": "自动赎回到银行卡",
                        "2": "自动赎回到晋金宝",
                    },
                    //购买状态
                    buy_stateObj: {
                        "1": {
                            "txt": "买入",
                            "class": "",
                        },
                        "2": {
                            "txt": "预约",
                            "class": "",
                        },
                        "3": {
                            "txt": "买入",
                            "class": "no_active",
                        },
                        "4": {
                            "txt": "买入",
                            "class": "no_active",
                        },
                    }
                }
            },
            //视图更新前
            async created() {
                this.detail = await this.getDetails()
                //获取持仓详情
                appUtils.setSStorageInfo("productInfo", { ...highEndHoldDetail, ...this.detail }); //产品信息
                this.detail.prod_sub_type2 = '100'
                // this.detail.due_date = highEndHoldDetail.due_date ? highEndHoldDetail.due_date : null
                // if(!this.detail.due_date ||  highEndHoldDetail.due_date == '--'){
                //     if(highEndHoldDetail.due_date && highEndHoldDetail.due_date != '--'){
                //         this.detail.due_date = highEndHoldDetail.due_date
                //     }
                // }
                //买入在途
                if (this.detail.fund_in_way_vol > 0) {
                    $(_pageId + " #fund_way_vol_box").show();
                    $(_pageId + " #fund_way_vol").text(tools.fmoney(this.detail.fund_in_way_vol));//买入在途
                } else {
                    $(_pageId + " #fund_way_vol_box").hide();
                }
                // 产品资讯
                if (this.detail.prodMsg && this.detail.prodMsg.length) {
                    this.prodMsg = this.detail.prodMsg;

                }
                //卖出在途
                if (this.detail.fund_out_way_vol > 0) {
                    $(_pageId + " #fund_out_way_vol_box").show();
                    $(_pageId + " #fund_out_way_vol").text(tools.fmoney(this.detail.fund_out_way_vol));//卖出在途
                } else {
                    $(_pageId + " #fund_out_way_vol_box").hide();
                }
               
                this.accumulated_income = tools.changeTwoDecimal_f(this.detail.accumulated_income)
                this.detail.fund_vol = tools.changeTwoDecimal_f(this.detail.sum_fund_amt);    //资产
                this.detail.day_name = this.detail.lock_period_unit == '0' ? '年' : this.detail.lock_period_unit == '1' ? '月' : '天' //单位
                this.detail.hold_vol = tools.changeTwoDecimal_f(this.detail.hold_vol);    //持有份额
                this.detail.lock_vol = tools.changeTwoDecimal_f(this.detail.lock_vol);    //锁定份额
                this.detail.cost_money = tools.changeTwoDecimal_f(this.detail.cost_money);    //本金
                this.detail.last_income = tools.changeTwoDecimal_f(this.detail.last_income);    //昨天收益
                this.detail.hold_income = tools.changeTwoDecimal_f(this.detail.hold_income);  //收益
                this.detail.cost_unit_price = tools.changeTwoDecimal_f(this.detail.cost_unit_price, 4);  //成本单价
                this.detail.nav = await tools.changeTwoDecimal_f(this.detail.nav, 4);  //成本单价
                this.detail.dividend_amt = tools.changeTwoDecimal_f(this.detail.dividend_amt);    //分红到宝
                this.achievement_pay = tools.changeTwoDecimal_f(this.detail.achievement_pay); //超额计提
                this.detail.nav_date = this.detail.nav_date ? (' ' + tools.ftime(this.detail.nav_date.substring(4, 8))) : "--"
                // this.detail.fund_code = '(' + this.detail.fund_code + ')'
                this.getBuyState()  //productInfo
                appUtils.setSStorageInfo("trsFundCode", this.detail.fund_code);
                appUtils.setSStorageInfo("custHoldTerm", this.detail.custHoldTerm);
                //判断当前产品是否可转让
                this.isTransfer(this.detail)
                if (this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length > 1) {
                    this.dealSwiper(this.prodMsg);
                }
                 //套利产品展示计提
                 if (this.detail.is_arbitrage_prod && this.detail.is_arbitrage_prod == "1") {
                    $(_pageId + " .is_arbitrage_prod").show();
                    if(this.detail.is_exists_rolling && this.detail.is_exists_rolling == "1"){
                        $(_pageId + " .rolling_situation").show();
                    }else{
                        $(_pageId + " .rolling_situation").hide();
                    }
                }else{
                    $(_pageId + " .is_arbitrage_prod").hide();
                }

            },
            //视图更新后
            mounted() {
                var appletEnterImg = require("gconfig").global.oss_url + $(_pageId + " #applet_enter").html();
                $(_pageId + " #applet_enter_img").attr("src", appletEnterImg);
                // this.getDetails().then((res) => {
                //     // console.log(res);
                //     this.prodMsg = res.prodMsg;
                //     // setTimeout(async () => {
                //     // console.log(this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length);
                //     if (res.prodMsg && res.prodMsg instanceof Array && res.prodMsg.length > 1) {
                //         this.dealSwiper(res.prodMsg);
                //     }
                // })
            },
            //页面销毁
            destroyed() {
                // console.log(222)
            },
            //计算属性
            computed: {
                timeResult: () => {
                    return (time, num) => {
                        if (!time) return '--'
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
                changeTwoDecimal_f: () => {
                    return (money) => {
                        if (!money) return '--'
                        let str = ''
                        str = tools.changeTwoDecimal_f(money)
                        return str
                    }
                },
                money_set: () => {
                    return (money) => {
                        if (!money) return '--'
                        return tools.fmoney(money) + '元';
                    }
                },
                saveDecimal_val: () => {
                    return (val) => {
                        if (!val || val == '--') return '--'
                        return tools.saveDecimal(val)
                    }
                },
                //时间处理
                setTime: () => {
                    return (time) => {
                        if (!time) return '--'
                        return tools.ftime(time);
                    }
                }
            },
            //监听
            watch: {

            },
            //组件内绑定的事件
            methods: {
                //陪伴服务 文章列表
                escortService(){
                    tools.recordEventData('1','escortService','陪伴服务');
                    this.detail.fund_code
                    let escortService_data = {
                        prod_id:this.detail.fund_code,
                        location:'2'
                    }
                    appUtils.setSStorageInfo("escortService_data", escortService_data);
                    appUtils.pageInit(_pageUrl, "template/escortService");
                },
                //点击业绩计提基准说明
                annual_details() {
                    tools.recordEventData('1','annual_details','点击业绩计提基准说明');
                    if (this.detail.interestRatelist && this.detail.interestRatelist.length) {
                        let html = '<div class="header"><span>期限</span><span>业绩计提基准</span></div>';
                        this.detail.interestRatelist.forEach(item => {
                            html += `<div class="rate-list"><span>持有满${item.hold_years}</span><span style="color: #e5443c">${item.interest_rate}%</span></div>`
                        })
                        $(_pageId + " #interest_rate_list .model_content").html(html)
                        $(_pageId + " #interest_rate_list").css({ "visibility": "visible" })
                        return;
                    }
                },
                //判断产品是否可转让
                isTransfer(params) {
                    service.reqFun107003({ fund_code: params.fund_code, vir_fundcode: sessionStorage.vir_fundcode }, (datas) => {
                        if (datas.error_no == 0) {
                            this.allowTransfer = datas.results[0].allowTransfer
                            this.transferData = datas.results[0]
                            if (!this.allowTransfer || this.allowTransfer == 0) {
                                $(_pageId + " #hanging").hide();
                                this.hanging_flag = "no_active"
                                return;
                            } else {
                                $(_pageId + " #hanging").show();
                                this.hanging_flag = ""

                            }
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                },
                //弹窗
                toestRemark() {
                    layerUtils.iAlert("<span style='text-align:left;display:block'>本产品业绩基准" + tools.fmoney(highEndHoldDetail.accrual_basis, 2) + "%（不构成最低收益保证），" + this.detail.administrator_accrual +  "</span>")
                },
                toestRemarkNew() {
                    layerUtils.iAlert("<span style='text-align:left;display:block'>本产品业绩基准" + tools.fmoney(highEndHoldDetail.accrual_basis, 2) + "%（不构成最低收益保证），" + this.detail.administrator_accrual +  "，收益=资产-本金-管理人超额提成</span>")
                },
                //获取产品购买状态
                getBuyState() {
                    let new_data = {
                        fund_code: this.detail.fund_code,
                        due_date: this.detail.due_date ? this.detail.due_date : highEndHoldDetail.due_date,
                        vir_fundcode:this.detail.vir_fundcode ? this.detail.vir_fundcode : ''
                    }
                    service.reqFun102049(new_data, (data) => {
                        this.button_flag = true;
                        if (data.error_no != "0") {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        let results = data.results[0];
                        this.flag_data = results
                        if (!results || results.length == 0) {
                            this.buy_flag = this.sell_flag = "no_active"
                            return;
                        }
                        //购买状态
                        let buy_state = results.buy_state;
                        //卖出状态 0:不可卖出 1:可卖出
                        this.sold_state = results.sold_state;
                        //due_redeem 定开
                        //redeemable_vol 日开
                        if (buy_state == 3 || buy_state == 4) this.buy_flag = "no_active"
                        //是否支持手动赎回 prod_open_type
                        if (this.sold_state == 0 || (this.detail.due_redeem == "0" && this.detail.prod_open_type == "1") || (this.detail.available_vol <= 0 && this.detail.prod_open_type == "2")) this.sell_flag = "no_active"
                    });
                },
                //单位净值提示
                thirtyTip() {
                    tools.recordEventData('1','thirtyTip','单位净值提示');
                    layerUtils.iAlert("单位净值未剔除管理人业绩报酬部分");
                },
                //买入
                buy() {
                    if (this.buy_flag) return;
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageUrl)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageUrl)
                    common.changeCardInter(() => {
                        appUtils.setSStorageInfo("productInfo", this.detail);
                        tools.recordEventData('1','buy','买入');
                        let pageToUrl = this.detail.prod_sub_type == "90" ? "highEnd/purchase" : "highEnd/smallgatherBuy"
                        appUtils.pageInit(_pageUrl, pageToUrl);
                    });
                },
                //挂单
                hanging() {
                    if (this.hanging_flag == "no_active") return
                    appUtils.setSStorageInfo("transferData", this.transferData);
                    // console.log(this.transferData.same_prod_transfer_all)
                    if(this.transferData.same_prod_transfer_all == '1'){
                        common.changeCardInter(() => {
                            //小集合产品
                            if(this.transferData.hold_multiple == '1'){ //小集合多期
                                layerUtils.iAlert("您同一产品持有多笔，转让时必须全部转让", () => { }, () => {
                                    tools.recordEventData('1','hanging_hold_multiple','挂单-全部转让');
                                    appUtils.pageInit(_pageUrl, "myTransfer/hangingOrder");
                                }, '', '确定');
                            }else{
                                tools.recordEventData('1','hanging','挂单');
                                appUtils.pageInit(_pageUrl, "myTransfer/hangingOrder");
                            }
                        });
                        return;
                    }else{
                        tools.recordEventData('1','hanging','挂单');
                        //普通私募
                        appUtils.pageInit(_pageUrl, "myTransfer/hangingOrder");
                    }
                    
                },
                //卖出
                sell() {
                    //  是否可手动赎回
                    //  可赎回份额 > 0 日开
                    if (this.sell_flag) return;
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageUrl)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageUrl)
                    if (this.detail.available_vol && this.detail.available_vol == 0) {
                        layerUtils.iConfirm("已提交赎回申请，请勿重复卖出", function () {
                        }, function () {
                            appUtils.setSStorageInfo("series_id",'');
                            tools.recordEventData('1','transaction','查看交易记录');
                            appUtils.pageInit(_pageUrl, "template/transaction");
                        }, "确定", "查看交易记录");
                        return;
                    }
                    common.changeCardInter(() => {
                        this.detail.vir_fundcode = sessionStorage.vir_fundcode
                        appUtils.setSStorageInfo("productInfo", this.detail);
                        tools.recordEventData('1','sell','卖出');
                        appUtils.pageInit(_pageUrl, "highEnd/smallSale");
                    });
                },
                //获取持仓详情
                async getDetails() {
                    return new Promise(async (resolve) => {
                        let data = {
                            acct_no: ut.getUserInf().fncTransAcctNo,
                            fund_code: highEndHoldDetail.fund_code,
                            if_period: highEndHoldDetail.if_period,
                            vir_fundcode: highEndHoldDetail.vir_fundcode,
                            due_date: highEndHoldDetail.due_date
                        }
                        if (highEndHoldDetail.due_date) data.due_date = highEndHoldDetail.due_date
                        service.reqFun101935(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                dealSwiper(data) {
                    swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                        pagination: '.swiper-pagination',
                        autoplay: false,
                        paginationElement: "li",
                        bulletActiveClass: "check",
                        autoplayDisableOnInteraction: false,
                        // observer:true, // 启动动态检查器(OB/观众/观看者)
                        // observeParents:true, // 修改swiper的父元素时，自动初始化swiper
                        // loop: true,
                        // onClick: function (swiper, event) {
                        //     // console.log(this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length);
                        //     if (data && data instanceof Array && data.length) {
                        //         var param = data[swiper.realIndex];
                        //         if(param.msg_type == '3'){
                        //             //去转让
                        //             appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);//缓存页面
                        //             appUtils.setSStorageInfo("productType", '06') //存储类型
                        //             if (!common.loginInter()) return;
                        //             appUtils.pageInit(_pageUrl, "login/listMorePage", {});
                        //         }else{
                        //             tools.recordEventData('1','prodMsgDetail','产品资讯');
                        //             appUtils.pageInit(_pageUrl, "template/prodMsgDetail", param);
                        //         }
                        //     }
                        // },
                        beforeDestroy: function () {
                            swipeInstance = null
                        }

                    });
                    if (data.length > 1) {
                        $(".swiper_scrollbar .swiper-pagination li").css({ "width": (1 / data.length) * 100 + "%" });
                    }
                },
                //是否超过三个
                goBeyondShow() {
                    let show = 1
                    return show ? 'm_width_50' : 'flex_1'
                },
                // //跳转查看份额页面
                seePortion() {
                    appUtils.setSStorageInfo("holdObj", this.detail);
                    tools.recordEventData('1','seePortion','查看份额');
                    appUtils.pageInit(_pageUrl, "template/lockInShare");
                },
                //跳转产品详情
                PageToProductDetails() {
                    tools.recordEventData('1','PageToProductDetails','产品详情');
                    appUtils.pageInit(_pageUrl, "template/heighEndProduct");
                },
                //查看滚入本期前投资情况
                seeRolling(){
                    appUtils.setSStorageInfo("productInfo", this.detail);
                    tools.recordEventData('1','seeRolling','滚入本期前投资情况');
                    appUtils.pageInit(_pageUrl, "template/rollingSituation");                  
                },
                //查看交易记录
                seeTransactionRecords() {
                    appUtils.setSStorageInfo("series_id",'');
                    tools.recordEventData('1','seeTransactionRecords','交易记录');
                    appUtils.pageInit(_pageUrl, "template/transaction");
                },
                // 修改持有期
                seeInterest() {
                    let modify_holdterm_flag = this.detail.modify_holdterm_flag;
                    if (modify_holdterm_flag == 'false') {
                        layerUtils.iAlert("当前时间不能修改持有期");
                        return;
                    }
                    tools.recordEventData('1','asset_bonus','修改持有期');
                    let html = "";
                    let interestRatelist = this.detail.interestRatelist;

                    if (!this.detail.custHoldTerm) {
                        this.detail.interestRatelist[this.detail.interestRatelist.length - 1]['checked'] = true
                    } else {
                        this.detail.interestRatelist && (this.detail.interestRatelist.forEach((item, i) => {
                            item['checked'] = false;
                            if (item.hold_years == this.detail.custHoldTerm) {
                                item['checked'] = true;
                                $(_pageId + " #confrim").css("background-color", "#999")
                                $(_pageId + " #confrim").attr("data-id", "0");
                            }
                        }))
                    }
                    interestRatelist.forEach((item, i) => {
                        html += `<li>
						<div class="ui radio">
							<input type="radio" id="radio_0" value="${item.hold_years}" ${item.checked ? "checked=checked" : ""}" data-item=${JSON.stringify(item)}>
							<label>${item.hold_years}</label>
						</div>
					</li>`;
                    })
                    $(_pageId + " #model_content").html(html);
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " #interestRate").show();
                    tools.getPdf({
                        agreement_type: "prod",
                        fund_code: this.detail.fund_code,
                        bookTitleMark: "1",
                        agreement_sub_type: '1,6'
                    }); //获取协议
                },
                //修改分红方式
                seeBonus() {
                    let bonus_if_alter = this.detail.bonus_if_alter;
                    let dividend_status = this.detail.dividend_status;
                    //0 不可更改  1可能改
                    if (bonus_if_alter == "0") {
                        layerUtils.iAlert("该产品不能修改分红方式");
                        return;
                    }
                    if (dividend_status == "01") {
                        layerUtils.iAlert("分红方式确认前将不能再次修改");
                        return;
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == 1) return tools.pageTo_evaluation(_pageUrl)
                    appUtils.setSStorageInfo("productInfo", this.detail);
                    common.changeCardInter(function () {
                        tools.recordEventData('1','asset_bonus','修改分红方式');
                        appUtils.pageInit(_pageUrl, "highEnd/modifyDividendsWay");
                    });
                },
                //持有期收益率
                holdYield() {
                    appUtils.setSStorageInfo("productInfo", this.detail);
                    tools.recordEventData('1','holdingPeriod','持有期收益率');
                    appUtils.pageInit(_pageUrl, "inclusive/holdingPeriod");
                },
                //修改到期方式
                seeMaturity() {
                    tools.recordEventData('1','modifyExpireWay','修改到期方式');
                    let back_way_alter = this.detail.back_way_alter;
                    //0 不可更改  1可能改
                    if (back_way_alter == "0") {
                        layerUtils.iAlert("该产品不能修改到期方式");
                        return;
                    }
                    //当前日期是否可以修改到期方式  0不可更改 1可更改
                    if (this.detail.special_back_way_alter == "0") {
                        layerUtils.iAlert("当前时间不可修改到期方式");
                        return;
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == 1) return tools.pageTo_evaluation(_pageUrl)
                    this.detail.is_stages_flag = highEndHoldDetail.is_stages_flag
                    this.detail.vir_fundcode = sessionStorage.vir_fundcode
                    appUtils.setSStorageInfo("productInfo", this.detail);
                    common.changeCardInter(function () {
                        appUtils.pageInit(_pageUrl, "highEnd/modifyExpireWay");
                    });
                },
                // 查看资讯
                readProdMsg() {
                    if (this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length && this.prodMsg.length <= 1) {
                        var param = this.prodMsg[0];
                        //查看消息
                        if(param.msg_type == '3'){
                            //去转让
                            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);//缓存页面
                            appUtils.setSStorageInfo("productType", '06') //存储类型
                            if (!common.loginInter()) return;
                            tools.recordEventData('1','listMorePage','列表页');
                            appUtils.pageInit(_pageUrl, "login/listMorePage", {});
                        }else{
                            tools.recordEventData('1','prodMsgDetail','产品资讯');
                            appUtils.pageInit(_pageUrl, "template/prodMsgDetail", param);
                        }
                        // appUtils.pageInit(_pageUrl, "template/prodMsgDetail", param);
                    }
                },
                // 改版查看资讯
                seeProdMsg(item){
                    tools.recordEventData('1','readProdMsg','查看资讯',{fundMsgId:item.id});
                    if(item.msg_type == '3'){
                        //去转让
                        appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);//缓存页面
                        appUtils.setSStorageInfo("productType", '06') //存储类型
                        if (!common.loginInter()) return;
                        tools.recordEventData('1','listMorePage','列表页');
                        appUtils.pageInit(_pageUrl, "login/listMorePage", {});
                    }else{
                        tools.recordEventData('1','prodMsgDetail','产品资讯');
                        if(item && item.html_content) item.html_content = encodeURIComponent(item.html_content);
                        appUtils.pageInit(_pageUrl, "template/prodMsgDetail", item);
                    }
                },
                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                }
            }
        })
    }
    //组件外的绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            sessionStorage.vir_fundcode = null
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageUrl)
        });
        //关闭修改持有期弹框
        appUtils.bindEvent($(_pageId + " #cancel"), function () {
            $(_pageId + " #interestRate").hide();
            $(_pageId + " .pop_layer").hide();
        });
        appUtils.preBindEvent($(_pageId + " #interestRate ul"), "li", function (e) {
            if ($(this).find("input").val() == appUtils.getSStorageInfo("custHoldTerm")) {
                $(_pageId + " #confrim").css("background-color", "#999")
                $(_pageId + " #confrim").attr("data-id", "0");
            } else {
                $(_pageId + " #confrim").css("background-color", "#e5443c")
                $(_pageId + " #confrim").attr("data-id", "1");
            }
            $(_pageId + " #interestRate ul li").each((i, item) => {
                $(_pageId + ` #radio_${i}`).removeAttr("checked");
            })
            $(this).find("input").attr("checked", "checked");

        })
        //确认修改持有期弹框
        appUtils.bindEvent($(_pageId + " .confrim"), function () {
            if ($(this).attr("data-id") == '0') return;
            //检查协议勾选
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            let selectedRate = JSON.parse($(_pageId + " #interestRate").find("input[checked]").attr("data-item"));
            $(_pageId + " #interestRate").hide();
            $(_pageId + " .exit_date").text(tools.ftime(selectedRate.exit_date.substr(0), "-"));
            $(_pageId + " .rate").text(tools.fmoney(selectedRate.interest_rate) + '%');
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "template_holdHeighDetail";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);

        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").hide()
            $(_pageId + " .password_box").hide()
            $(_pageId + " #jymm").val("");
            guanbi();
        });
        //调用交易
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            let selectedRate = JSON.parse($(_pageId + " #interestRate").find("input[checked]").attr("data-item"));
            $(_pageId + " .pop_layer").hide()
            $(_pageId + " .password_box").hide()
            var trans_pwd = $(_pageId + " #jymm").val()
            // trans_pwd = "123123"
            guanbi();
            if (trans_pwd.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            let data = {
                fund_code: highEndHoldDetail.fund_code,
                vir_fundcode: highEndHoldDetail.vir_fundcode,
                hold_term: selectedRate.hold_years,
                trans_pwd: trans_pwd,
                due_date: selectedRate.exit_date,
            }
            service.reqFun106051(data, async (data) => {
                if (data.error_no == '0') {
                    common.setLocalStorage("sceneRefresh",'0');     
                    common.setLocalStorage("userChooseRefresh",'0');
                    location.reload();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })

        });
        // 阶段业绩计提确定
        appUtils.bindEvent($(_pageId + " .tips-confrim"), function () {
            $(_pageId + " #interest_rate_list").css({ "visibility": "hidden" })
        })
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //密码输入数字键盘
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                //						queding_sj(jymm);
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i;
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //获取组件模板和数据Data 确保获得数据后更新模板
    async function getHtmlData(datas) {
        return new Promise(async (resolve) => {
            service.reqFun102109(datas, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0].template_content)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }
    async function detailData(requestData) {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '0',
                fund_code: requestData.jump_page_prodid
            }
            service.reqFun102108(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res)
            })
        })
    }
    //页面销毁时候
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " #hanging").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " #interest_rate_list").css({ "visibility": "hidden" })
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #interestRate").hide();
        monkeywords.flag = 0;
        monkeywords.destroy();
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    var thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});

