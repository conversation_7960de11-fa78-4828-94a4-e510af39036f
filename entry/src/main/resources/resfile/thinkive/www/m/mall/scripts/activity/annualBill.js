//活动 - 年度账单
define(function (require, exports, module) {
    let appUtils = require("appUtils");
    let service = require("mobileService")
    let tools = require("../common/tools");
    layerUtils = require("layerUtils");
    let _pageId = "#activity_annualBill ";
    require('../common/echarts.min');
    require('../common/echartsData.js');
    async function init() {
        //获取年度账单数据 使用 async/await 确保拿到数据
        let data = await getBillData();
        //分配数据
        setData(data);
        chooseYear()
    }
    //点返回
    appUtils.bindEvent($(_pageId + " .icon_back"), function () {
        pageBack();
    });
    //处理数据
    function setData(data){
        //渲染echarts 1买入 2卖出
        buyEchartsData(data.buyMaxMonth,data.buySumInfo,data.buyList,1)
        buyEchartsData(data.sellMaxMonth,data.sellSumInfo,data.sellList,2)
        //总金额展示
        let totalAsstes = '￥'+tools.fmoney(data.totalAsstes)
        $(_pageId + ' .money').html(totalAsstes)
        //渲染持仓列表
        positionList(data.holdList)
    }
    //渲染列表
    function positionList(data){
        var listhtml = "";
        for(let i=0 ; i<data.length;i++){
            let stylename;
            if(i==0)stylename='background:#ee0a24';
            if(i==1)stylename='background:rgb(253, 100, 16)';
            if(i==2)stylename='background:rgb(16, 142, 227)';
            if(i==3)stylename='background:rgb(250, 231, 47)';

            listhtml +='<ul class="center_title"><i style="'+stylename+'"></i><span>'+data[i].classify_desc+'</span></ul><table border="1" id="list1" valign="middle" width="100%" cellspacing="0" cellpadding="0" id="zhanshiList">';
            let topStr = '<tr><td class="title">名称</td><td class="title">份额</td><td class="title">资产</td></tr>';
            let prodList =JSON.parse(data[i].yearList);
            if(prodList.length == '0'){
                listhtml += '<div class="trim1" style="display: block">暂无资产</div>';
            }else{
                for(let j=0;j<prodList.length;j++){
                    let prod_name = prodList[j].prod_sname ;
                    if(prod_name.indexOf("FOF（") > 0 ){
                        prod_name = prod_name.split("（")[0] + '<br>（' + prod_name.split("（")[1];
                    }
                    topStr += '<tr><td>' + prod_name  + '<br>' +  prodList[j].fund_code + '</td><td>'+ tools.fmoney(prodList[j].total_vol)+'</td><td>'+ tools.fmoney(prodList[j].total_amt)+'</td></tr>';
                }
                listhtml += topStr+'</table>';
            }
        }
        $(_pageId+ " .annualBill_center_card").html(listhtml);


        // let list = data;
        // let [list1,list2,list3] = [[],[],[]]    //处理服务器数据
        // let [list1Str,list2Str,list3Str] = ['','',''] //dom操作需要的字符串
        // list.map(res=>{
        //     if(res.prod_type == '00') list1.push(res)
        //     if(res.prod_type == '01') list2.push(res)
        //     if(res.prod_type == '02') list3.push(res)
        // })
        // let topStr = '<tr><td class="title">名称</td><td class="title">份额</td><td class="title">资产</td></tr>'
        // $.each(list1,(k,v)=>{ list1Str += '<tr><td>' + list1[k].prod_sname  + '<br>' + list1[k].prod_id + '</td><td>'+list1[k].total_vol+'</td><td>'+list1[k].total_amt+'</td></tr>'})
        // $.each(list2,(k,v)=>{ list2Str += '<tr><td>' + list2[k].prod_sname  + '<br>' + list2[k].prod_id + '</td><td>'+list2[k].total_vol+'</td><td>'+list2[k].total_amt+'</td></tr>'})
        // $.each(list3,(k,v)=>{ list3Str += '<tr><td>' + list3[k].prod_sname  + '<br>' + list3[k].prod_id + '</td><td>'+list3[k].total_vol+'</td><td>'+list3[k].total_amt+'</td></tr>'})
        // list1Str == ''?$(_pageId +' .trim1').show():$(_pageId + ' #list1').append(topStr+list1Str)
        // list2Str == ''?$(_pageId +' .trim2').show():$(_pageId + ' #list2').append(topStr+list2Str)
        // list3Str == ''?$(_pageId +' .trim3').show():$(_pageId + ' #list3').append(topStr+list3Str)
    }
    //渲染买入echarts
    function buyEchartsData(MaxMonth,SumInfo,List,num){
        //最大月份，购买信息，echartsdata,买入卖出
        if(!MaxMonth) MaxMonth = '01'
        let echart //创建echart 对象
        let echartData = JSON.parse(JSON.stringify(lineData))
        echartData.title.subtext = SumInfo.sum_amt?SumInfo.sum_amt + '元':'0.00元'
        echartData.title.text = num == 1 ? '买入' + + (SumInfo.sum_nm?SumInfo.sum_nm:0) + '笔，合计' :'卖出' + (SumInfo.sum_nm?SumInfo.sum_nm:0) + '笔，合计'
        let arr = []
        List.map(item=>{
            arr.push(item.trans_amt*1)
        })
        echartData.series[0].data = arr
        let str = MaxMonth.substring(MaxMonth.length-2)
        if(str.substr(0,1) != '0'){
            str = str*1 - 1
        }else{ str = str.substr(1,2)*1 - 1}
        echartData.tooltip.formatter = function(params){
            let result = ''
            params.forEach( (item)=> {
                // console.log(item)
                item.marker = ''
                let showAxisRemark = num == 1?'买入最多':'卖出最多'
                let AxisRemark = num == 1?'买入':'卖出'
                let showAxisValue = item.dataIndex == str?item.axisValue + showAxisRemark:item.axisValue+AxisRemark
                result += showAxisValue +  "</br>" + item.marker  + '￥' + item.data
            })
            return result
        }
        //买入渲染
        if(num == 1) echart = echarts.init(document.getElementById('buyEcharts'));
        //卖出渲染
        if(num == 2) echart = echarts.init(document.getElementById('sellEcharts'));
        echart.setOption(echartData);
        //防止无法渲染 最大数量默认展示
        setTimeout(()=>{ echart.dispatchAction({    
            type: 'showTip', 
            seriesIndex:0,  // 显示第几个series
            dataIndex: str // 显示第几个数据
        })})
    }
    //获取初始化数据
    async function getBillData(){
        return new Promise(async(resolve, reject) => {
            service.reqFun101927({}, async(data)=> {
                if (data.error_no == 0) {
                    resolve(data.results[0])
                } else {
                    reject(-1)
                    layerUtils.iAlert(data.error_info);
                }
            });
        })
    }
    //选择年度
    function chooseYear(){
        let dataArr = [
            {id: "0", value: '2021'},
        ]
        let position = 0;

        tools.mobileSelect({
            trigger: _pageId + " #chooseYear",
            title: "请选择年度",
            dataArr: dataArr,
            position: position + "",
            callback: function (data, index) {
                // console.log(data)
            }
        })
    }
    //绑定事件
    function bindPageEvent() {

    }
    //选择年度
    function destroy() {
        $(_pageId + ' #list1').html('')
        $(_pageId + ' #list2').html('')
        $(_pageId + ' #list3').html('') 
        $(_pageId + ' #list3').html('')
        $(".mobileSelect").remove();
        $(".picker").remove();
        $(_pageId + " #live_address").val(""); 
        $(".trim1").hide();
        $(".trim2").hide();
        $(".trim3").hide();
        $(_pageId + " .right").html('请选择年度')
    }
    function pageBack() {
        appUtils.pageBack();
    }
    var caitongimage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = caitongimage;
});
