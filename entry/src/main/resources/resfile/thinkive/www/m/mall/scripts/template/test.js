// 晋金高端  持仓详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            common = require("common"),
            tools = require("../common/tools"),
            service = require("mobileService"),
            _pageCode = "template/holdHeighDetail",
            _pageId = "#template_holdHeighDetail ";
    var highEndHoldDetail;
    //分红方式
    var defdividend_method_obj = {
        "1": "红利再投",
        "2": "分红到晋金宝",
        "3": "分红到晋金宝",
    }
    var endflag_method_text_obj = {
        "0": "自动滚入下一期",
        "1": "自动赎回到银行卡",
        "2": "自动赎回到晋金宝",
    }
    var buy_stateObj = {
        "1": {
            "txt": "买入",
            "class": "",
        },
        "2": {
            "txt": "预约",
            "class": "",
        },
        "3": {
            "txt": "买入",
            "class": "no_active",
        },
        "4": {
            "txt": "买入",
            "class": "no_active",
        },
    }
    function init() {
        highEndHoldDetail = appUtils.getSStorageInfo("productInfo");
        if (highEndHoldDetail.cust_fund_type == "0") {  //持有
            $(_pageId + " .asset_bonus").show();
        } else if (highEndHoldDetail.cust_fund_type == "1") {  //在途
            $(_pageId + " .asset_bonus").hide();
        }

        $(_pageId + " .fund_name").text(highEndHoldDetail.fund_sname); //产品名称
        //产品代码
        var fundcode = highEndHoldDetail.fund_code;
        if (!fundcode) {
            fundcode = "--";
        }
        //基金类型  股票型 债券型 货币型.....
        var prod_type2 = highEndHoldDetail.prod_type2;
        if (!prod_type2) {
            prod_type2 = "--";
        }
        fundcode = "(" + fundcode + ")" + tools.transformFundType(prod_type2);
        $(_pageId + " .fund_code").text(fundcode); //产品名称
        $(_pageId + " .betweenDays").text(highEndHoldDetail.betweenDays > 0 ? highEndHoldDetail.betweenDays : "--"); //持有日期
        $(_pageId + " .cost_money").text(tools.fmoney(highEndHoldDetail.cost_money)); //本金

        service.reqFun101924(highEndHoldDetail, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var result = data.results[0];
            $.extend(highEndHoldDetail, result);
            $(_pageId + " .fund_vol").text(tools.fmoney(highEndHoldDetail.fund_vol)); // 资产
            $(_pageId + " .nav_date").text(result.nav_date ? "(" + tools.ftime(result.nav_date.substr(4, 4)) + ")" : "");
            $(_pageId + " .hold_income").text(tools.fmoney(result.hold_income)).addClass(tools.addMinusClass(result.hold_income)); //持仓收益
            $(_pageId + " .dividend_amt").text(tools.fmoney(result.dividend_amt)); //分红金额
            $(_pageId + " .having_vol").text(tools.fmoney(result.having_vol)); //持有份额
            $(_pageId + " .nav").text(tools.fmoney(result.nav, 4)); //净值
            $(_pageId + " .due_date").text(tools.ftime(result.due_date)); // 下一开放日
            //分红方式状态 01-受理成功，02-受理失败，03-确认成功，04-确认失败
            var dividend_status_txt = result.dividend_status == "01" ? "（修改中）" : "";
            $(_pageId + " #fh").html(defdividend_method_obj[result.defdividend_method] + dividend_status_txt); // 分红方式
            $(_pageId + " #dq").html(endflag_method_text_obj[result.endflag_method]); // 到期兑付方式

        });
        reqFun102049();//获取购买状态
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //提示
        appUtils.bindEvent($(_pageId + " .thirty_tips"), function () {
            layerUtils.iAlert("资产=本金+收益+分红到宝")
        });
        //修改分红方式
        appUtils.bindEvent($(_pageId + " #fh"), function () {
            //0 不可更改  1可能改
            if (highEndHoldDetail.bonus_if_alter == "0") {
                layerUtils.iAlert("该产品不能修改分红方式");
                return;
            }
            // 01 修改中
            if (highEndHoldDetail.dividend_status == "01") {
                layerUtils.iAlert("分红方式确认前将不能再次修改");
                return;
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/modifyDividendsWay");
            });
        });
        //修改到期方式
        appUtils.bindEvent($(_pageId + " #dq"), function () {
            //0 不可更改  1可能改
            if (highEndHoldDetail.back_way_alter == "0") {
                layerUtils.iAlert("该产品不能修改到期方式");
                return;
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/modifyExpireWay");
            });
        });


        //产品详情
        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            appUtils.pageInit(_pageCode, "highEnd/productPolicyDetail");
        });
        //卖出
        appUtils.bindEvent($(_pageId + " #sell"), function () {
            if ($(this).hasClass("no_active")) return;
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageCode)) return;
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return pageTo_evaluation()
            if (highEndHoldDetail.available_vol && highEndHoldDetail.available_vol == 0) {
                layerUtils.iConfirm("已提交赎回申请，请勿重复卖出", function () {
                }, function () {
                    appUtils.pageInit(_pageCode, "highEnd/transaction");
                }, "确定", "查看交易记录");
                return;
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/sale");
            });
        });
        //买入
        appUtils.bindEvent($(_pageId + " #buy"), function () {
            if ($(this).hasClass("no_active")) return;
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageCode)) return;
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return pageTo_evaluation()
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/purchase");
            });
        });


    }

    //查询产品购买状态
    function reqFun102049() {
        service.reqFun102049(highEndHoldDetail, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //购买状态
            var buy_state = results.buy_state;
            //卖出状态 0:不可卖出 1:可卖出
            sold_state = results.sold_state;
            $(_pageId + " #sell").show();
            $(_pageId + " #buy").show();

            $(_pageId + " #buy").html(buy_stateObj[buy_state].txt).addClass(buy_stateObj[buy_state].class)

            if (sold_state == "0" || highEndHoldDetail.due_redeem == "0") {//due_redeem到期日是否支持手动赎回 0:不支持 1:支持
                $(_pageId + " #sell").addClass("no_active");
            } else if (sold_state == "1" && highEndHoldDetail.due_redeem == "1") {
                $(_pageId + " #sell").removeClass("no_active");
            }
            // if(highEndHoldDetail.cust_fund_type == "1") {
            // $(_pageId + " #sell").addClass("no_active");
            // }
        });
    }

    function destroy() {
        $(_pageId + " .empty").html("");
        $(_pageId + " .data-line").html("--");
        $(_pageId + " .hold_income").removeClass("text_red").removeClass("text_green").removeClass("text_gray");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.clearSStorage("highEndHoldDetail");
        appUtils.pageBack();
    }

    var highEndDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highEndDetail;
});
