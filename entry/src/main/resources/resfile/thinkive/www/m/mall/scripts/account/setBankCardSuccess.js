// 绑卡成功
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        _pageId = "#account_setBankCardSuccess";
        _page_code = "account/setBankCardSuccess"
        common = require("common");
    var ut = require("../common/userUtil");
    var gconfig = require("gconfig"); 
    var global = gconfig.global;
    let getSucessData //是否展示福利图片
    var tools = require("../common/tools");
    function init() {
        appUtils.clearSStorage("idCardInfo");
        appUtils.clearSStorage("bankAccInfo");
        //获取是否可以进行推广参数
        getExtensionData();
        let errorMessage = appUtils.getSStorageInfo("errorMessage");
        if(errorMessage == "1"){
            $(_pageId + " .success_message").text("您已经提交资料，待审核通过后，您可进行充值购买");
        }else{
            $(_pageId + " .success_message").text("绑卡受理成功！");
        }
    }
    //去测评
    function pageTo_evaluation(){
        layerUtils.iAlert("您的风险测评已到期，请重新测评",  ()=> {},()=>{
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        },'','确定')
    }
    //绑定事件
    function bindPageEvent() {
        //完成
        appUtils.bindEvent($(_pageId + " #wancheng"), function () {
            if(ut.getUserInf().custLabelCnlCode === "yh") {
                appUtils.pageInit("account/setBankCardSuccess", "yuanhui/userIndexs", {bindCard: true});
            } else if(ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) ||ut.getUserInf().custLabelCnlCode == "jjdx"){
                appUtils.pageInit("account/setBankCardSuccess", "login/userIndexs", {bindCard: true});
            }else{
                appUtils.pageInit("account/setBankCardSuccess", "hengjipy/userIndexs", {bindCard: true});
            }
        });
        //点击图片
        appUtils.bindEvent($(_pageId + " .firstImg"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
            if(getSucessData.is_exclusive_prod == "1" && getSucessData.activity_form == '0'){ //是专享产品
                //专享产品特殊处理
                // appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                exclusiveProducts()
            }else if(getSucessData.is_exclusive_prod != "1" && getSucessData.activity_form == '0'){ 
                //公募其他
                // appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                elseProducts()
            }else{
                //链接直接跳转 外链
                if(getSucessData.activity_form == 2){
                    appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                        "url": getSucessData.link_url,
                        "name": null,
                        "description": null,
                    });
                //链接直接跳转 内链
                }else{
                    appUtils.pageInit('login/userIndexs', getSucessData.link_url, {});
                }
            }
        });
    }
    //公募产品处理
    function elseProducts(){
        if(getSucessData.prod_sub_type == "10"){    //针对晋金宝进行特殊处理，直接去做风险测评
            appUtils.pageInit('login/userIndexs', "safety/riskQuestion", {});
        }
        appUtils.setSStorageInfo("fund_code", getSucessData.recommend_prod_id);
        appUtils.setSStorageInfo("prod_sub_type", getSucessData.prod_sub_type);
        
        tools.jumpDetailPage('login/userIndexs', getSucessData.prod_sub_type, getSucessData.prod_sub_type2)
    }
    //针对专享进行特殊处理
    async function exclusiveProducts(){
        let fund_code = getSucessData.recommend_prod_id
        let invalidFlag = appUtils.getSStorageInfo("user")?appUtils.getSStorageInfo("user").invalidFlag:null
        let list = await getExclusiveList()
        let productInfo = list.filter((value,index,a)=>{return fund_code == value.prod_id})[0]
        productInfo.fund_code = productInfo.prod_id;//产品编码
        if (productInfo.exclusive_product_type == "02") { //公募 大集合
            appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.pageInit('login/userIndexs', "thfund/gatherDetail");
            return;
        }
        if (productInfo.exclusive_product_type == "04") { //公募 持有期
            appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.pageInit('login/userIndexs', "inclusive/holdsDetail");
            return;
        }
        //银行 新手专享   10点抢购
        if (!common.loginInter()) return;
        if (!ut.hasBindCard(_page_code)) return;
        //校验用户是否上传过身份证照片
        if(!ut.getUploadStatus()){
            let operationId = 'noUploadIdCard'
            appUtils.setSStorageInfo("noUploadIdCard", '1');
            return layerUtils.iConfirm("您还未上传身份证照片", function () {
                appUtils.pageInit(_page_code, "account/uploadIDCard", {});
            }, function () {
            }, "去上传", "取消",operationId);
        }
        if((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')){
            pageTo_evaluation()
            return
        }
        if (productInfo.buy_state == "0" && productInfo.exclusive_product_type != "03") { // 不包括货基
            layerUtils.iAlert("敬请期待");
            return;
        }
        if (productInfo.buy_state == "2" && productInfo.exclusive_product_type != "03") { // 不包括货币基金
            layerUtils.iAlert("产品已售罄");
            return;
        }
        if (productInfo.exclusive_product_type == "03" || productInfo.exclusive_product_type == "05") { //货币基金
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit('login/userIndexs', "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消");
                    return;
                }
                appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
                productInfo["prod_sub_type"] = "10";
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.pageInit('login/userIndexs', "inclusive/moneytaryPurchase");
            });
            return;
        }
        productInfo.prod_code = productInfo.prod_id;//产品编码-
        productInfo.surv_amt = productInfo.prod_per_min_amt; //起购金额
        productInfo.bas_int_rate = productInfo.rate; //存款利率
        appUtils.setSStorageInfo("productInfo", productInfo);
        service.reqFun151110({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            if (data.results[0].bank_flag == 0) { //未开户
                layerUtils.iLoading(false);
                appUtils.pageInit('login/userIndexs', "bank/faceRecognition");
                return;
            }
            if (productInfo.exclusive_type == "2") { //新手专享
                service.reqFun109003({}, function (data) { //查询是否新手
                    if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var is_sales_tiro = data.results[0].is_sales_tiro; // 是否新手（0-是 1-否）
                    if (is_sales_tiro != "0") {
                        layerUtils.iAlert("您已不是新手");
                        return;
                    }
                    appUtils.pageInit('login/userIndexs', "bank/purchase");
                })
                return;
            }
            appUtils.pageInit('login/userIndexs', "bank/purchase");
        }, {isLastReq: false})
    }
    //获取专享产品列表
    async function getExclusiveList(){
        return new Promise(async (resolve, reject) => {
            service.reqFun109002({}, (datas)=> {  
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results
                resolve(res)
            })
        })
    }
    //获取图片
    function getExtensionData(){
        let userType = ut.getUserInf().custLabelCnlCode
        if(!userType || userType == '' || userType == 'yh_jjdx'){
            service.reqFun108012({}, function(data) {
                if(data.error_no == "0") {
                    getSucessData = data.results[0]
                    getSucessData.oss_url = gconfig.global.oss_url + '/' + getSucessData.oss_url
                    //是否存在绑卡活动
                    //是否是专享产品
                    // if(getSucessData.hasBindCardAct == '0' || sessionStorage.firstBankCard == 1) return
                    $(_pageId + " .theFirstTime").show()
                    $(_pageId + " .firstImg").attr('src',getSucessData.oss_url)
                    sessionStorage.firstBankCard = 1
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }
    }
    function destroy() {
        $(_pageId + " .theFirstTime").hide()
    }
    var setBankCardSuccess = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = setBankCardSuccess;
});
