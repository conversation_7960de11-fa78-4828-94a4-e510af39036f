//晋金高端购买页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#highEnd_purchase ";
    var ut = require("../common/userUtil");
    var _pageCode = "highEnd/purchase";
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var productInfo;
    var jymm;
    var buyflag; //风险等级是否匹配   1 不匹配
    var available_vol; //可用余额
    var step_amt_str;
    var isFirstPurchase;// 是否首次购买产品
    var _redem_method = "1";

    function init() {
        userInfo = ut.getUserInf();
        appUtils.clearSStorage("productInfo_jjb"); //删除华安汇财通本地信息，防止本地信息混淆
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        // console.log(productInfo);
        reqFun101901();
        isFirstPurchase = true;
//        if (productInfo.prod_sub_type2 == '94') {
//            //查询该产品是否首次购买
//            reqFun102072();
//        }
        reqFun102103();

    }

    function bindPageEvent() {
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            tools.intercommunication(_pageCode);
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });

        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            // console.log(productInfo)
            tools.jumpPriDetailPage(_pageCode, productInfo.prod_sub_type2);
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_purchase";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });

        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_purchase";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //关闭数字键盘
        // appUtils.bindEvent($(_pageId), function () {
           
        // });
        //查看PDF文件
        appUtils.bindEvent($(_pageId + " #xy"), function () {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").hide()
            $(_pageId + " .password_box").hide()
            $(_pageId + " #jymm").val("");
            guanbi();
        });

        // 跳转充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });

        //购买
        appUtils.bindEvent($(_pageId + " #next"), function () {
            monkeywords.close();
            if((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (money <= 0 || !money) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (isFirstPurchase && productInfo.threshold_amount && parseFloat(money) < parseFloat(productInfo.threshold_amount)) { //首次购买 满足起购金额
                layerUtils.iAlert("起购金额为" + productInfo.threshold_amount / 10000 + "万元");
                return;
            }
            if (!isFirstPurchase && productInfo.addition_amt && parseFloat(money) < parseFloat(productInfo.addition_amt)) { // 持有状态 满足追加金额
                layerUtils.iAlert("起购金额为" + productInfo.addition_amt / 10000 + "万元");
                return;
            }
            if (isFirstPurchase && tools.isMatchAddAmt(money, productInfo.threshold_amount, productInfo.step_amt)) return
            if (!isFirstPurchase && tools.isMatchAddAmt(money, productInfo.addition_amt, productInfo.step_amt)) return
            if (money && productInfo.first_max_amt && money > parseFloat(productInfo.first_max_amt)) { // (当前金额 - 起投金额) % 递增金额 == 0
                layerUtils.iAlert("超过单笔最高限额");
                return;
            }
            if (productInfo.interest_rate_list && productInfo.interest_rate_list.length && productInfo.prod_sub_type2 == '100') {
                // layerUtils.iAlert(`<span>期限</span><span>业绩计提基准</span></br>
                // <span>持有满23天</span><span>5.40%</span></br>
                // <span>持有满23天</span><span>5.40%</span></br>`)
                let html = '<div class="header"><span>期限</span><span>业绩计提基准</span></div>';
                productInfo.interest_rate_list.forEach(item => {
                    html += `<div class="rate-list"><span>持有满${item.hold_years}</span><span style="color: #e5443c">${tools.fmoney(item.interest_rate)}%</span></div>`
                })
                $(_pageId + " #interest_rate_list .model_content").html(html)
                $(_pageId + " #interest_rate_list").css({ "visibility": "visible" })
                return;
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #payMethod").show();
            isCanBuy();
        });

        // 阶段业绩计提确定
        appUtils.bindEvent($(_pageId + " .tips-confrim"), function () {
            $(_pageId + " #interest_rate_list").css({ "visibility": "hidden" })
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #payMethod").show();
            isCanBuy();
        })
        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .gmje").text(tools.fmoney(money) + '元');
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_purchase";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //调用交易
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide()
            $(_pageId + " .password_box").hide()
            var app_amt = $(_pageId + " #money").val();
            app_amt = app_amt.replace(/,/g, "");
            var trans_pwd = $(_pageId + " #jymm").val()
            guanbi();
            if (trans_pwd.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            var param = {
                app_amt: app_amt, //交易金额
                fund_code: productInfo.fund_code,
                trans_pwd: trans_pwd,
                buyflag: buyflag,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
                period: productInfo.period
            };
            //2 预约  1 购买
            if (productInfo.buy_state == '2') {
                appoint(param);
            }
            if (productInfo.buy_state == '1') {
                purchase(param)
            }

        });
    }

    //预约
    function appoint(param) {
        service.reqFun106012(param, function (data) {
            if (data.error_no == 0) {
                appUtils.pageInit(_pageCode, "highEnd/purchaseResult", {
                    app_amt: param.app_amt,
                    trans_serno: data.results[0].trans_serno
                });
            } else {
                layerUtils.iAlert(data.error_info);
                return;
            }

        })
    }

    //购买
    function purchase(param) {
        service.reqFun106013(param, function (data) {
            if (data.error_no == 0) {
                appUtils.pageInit(_pageCode, "highEnd/purchaseResult", {
                    app_amt: param.app_amt,
                    trans_serno: data.results[0].trans_serno
                });
            } else {
                layerUtils.iAlert(data.error_info);
                return;
            }

        })
    }


    function initBuyFlag() {
        if ((parseFloat(userInfo.riskLevel.substr(1)) >= parseFloat(productInfo.risk_level.substr(1))) || parseFloat(productInfo.risk_level.substr(1)) == 1) {
            buyflag = "";
        } else {
            buyflag = "1";
        }
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //可用金额查询
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            available_vol = results.available_vol;
            //可用份额
            $(_pageId + " .kyje").text(tools.fmoney(results.available_vol))
        })
    }

    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        //进行充值
        var app_amt = $(_pageId + " #money").val();
        app_amt = app_amt.replace(/,/g, "");
        app_amt = (+app_amt);
        available_vol = (+available_vol);
        if (app_amt <= available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }
    // //私募产品详情（102108） 新
    // function new_smDetails(){
    //     let fund_code = productInfo.fund_code
    //     service.reqFun102108({fund_code:fund_code}, function (datas) {
    //         if (datas.error_no == 0) {
    //             productInfo = datas.results[0];
    //             productInfo.prod_sub_type2 = "100"
    //             appUtils.setSStorageInfo("productInfo", productInfo);
    //             $(_pageId + " .prod_sname").text(productInfo.prod_sname);
    //             var str = "";
    //             if (productInfo.threshold_amount && isFirstPurchase) {
    //                 str += (productInfo.threshold_amount >= 10000 ? (productInfo.threshold_amount / 10000 + "万") : tools.fmoney(productInfo.threshold_amount)) + '元起购';
    //             } else if (productInfo.addition_amt && !isFirstPurchase) {
    //                 str += (productInfo.addition_amt >= 10000 ? (productInfo.addition_amt / 10000 + "万") : tools.fmoney(productInfo.addition_amt)) + '元起购';
    //             }

    //             if (productInfo.step_amt && productInfo.step_amt > 0) {
    //                 str += "，" + (productInfo.step_amt >= 10000 ? (productInfo.step_amt / 10000 + '万') :
    //                         tools.fmoney(productInfo.step_amt + ''))
    //                         + "元递增"
    //             }
    //             if (str) {
    //                 $(_pageId + " #inputspanid span").text(str).attr("text", str);
    //             }
    //             if (productInfo.prod_sub_type2 == "94") { //持有期
    //                 $(_pageId + " .tips").html("预计将在" + tools.FormatDateText(productInfo.lock_start.substr(4, 4)) + "起息并给您拨打回访电话请您注意接听")
    //             } else {
    //                 var hfrq = productInfo.return_visit_date;
    //                 if (hfrq) {
    //                     $(_pageId + " .tips").html("温馨提示：将于" + tools.ftime(hfrq) + "进行电话回访，请注意接听")
    //                 }
    //             }

    //             tools.getPdf("prod", productInfo.fund_code, productInfo.buy_state); //获取协议
    //             initBuyFlag();
    //         } else {
    //             layerUtils.iAlert(datas.error_info);
    //         }
    //     });
    // }
    //私募产品详情查询（102043）
    function smDetails() {
        var fund_code = productInfo.fund_code;
        var param = {
            fund_code: fund_code
        };
        if (productInfo.prod_sub_type2 == "100") {
            //新接口查询
            service.reqFun102108(param, function (datas) {
                if (datas.error_no == 0) {
                    productInfo = datas.results[0];
                    productInfo.prod_sub_type2 = "100"
                    handleData(productInfo)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        } else {
            //老接口查询
            service.reqFun102043(param, function (datas) {
                if (datas.error_no == 0) {
                    productInfo = datas.results[0];
                    handleData(productInfo)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        }
    }
    //处理详情数据
    function handleData(productInfo) {
        // appUtils.setSStorageInfo("productInfo", productInfo);
        $(_pageId + " .prod_sname").text(productInfo.prod_sname);
        var str = "";
        if (productInfo.threshold_amount && isFirstPurchase) {
            str += (productInfo.threshold_amount >= 10000 ? (productInfo.threshold_amount / 10000 + "万") : tools.fmoney(productInfo.threshold_amount)) + '元起购';
        } else if (productInfo.addition_amt && !isFirstPurchase) {
            str += (productInfo.addition_amt >= 10000 ? (productInfo.addition_amt / 10000 + "万") : tools.fmoney(productInfo.addition_amt)) + '元起购';
        }

        if (productInfo.step_amt && productInfo.step_amt > 0) {
            str += "，" + (productInfo.step_amt >= 10000 ? (productInfo.step_amt / 10000 + '万') :
                tools.fmoney(productInfo.step_amt + ''))
                + "元递增"
        }
        if (str) {
            $(_pageId + " #inputspanid span").text(str).attr("text", str);
        }
        if (productInfo.prod_sub_type2 == "94") { //持有期
            $(_pageId + " .tips").html("预计将在" + tools.FormatDateText(productInfo.lock_start.substr(4, 4)) + "起息并给您拨打回访电话请您注意接听")
        } else {
            var hfrq = productInfo.return_visit_date;
            if (hfrq) {
                $(_pageId + " .tips").html("温馨提示：将于" + tools.ftime(hfrq) + "后进行电话回访，请注意接听")
            }
        }

        tools.getPdf("prod", productInfo.fund_code, productInfo.buy_state); //获取协议
        initBuyFlag();
    }
    //金额键盘事件  
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            isShowUpMoney: true,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (parseFloat(moneys) < parseFloat(productInfo.threshold_amount) && isFirstPurchase) {
                    layerUtils.iAlert("起购金额为" + productInfo.threshold_amount / 10000 + "万元");
                    return;
                }
                if (parseFloat(moneys) < parseFloat(productInfo.addition_amt) && !isFirstPurchase) {
                    layerUtils.iAlert("起购金额为" + productInfo.addition_amt / 10000 + "万元");
                    return;
                }
                $(_pageId + " #money").val(tools.fmoney(moneys));
                if (isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.threshold_amount, productInfo.step_amt)) return
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.addition_amt, productInfo.step_amt)) return
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }
                curVal = $(_pageId + " #money").val().replace(/,/g, "");
                // if (parseFloat(curVal) > parseFloat(available_vol)) {
                //     guanbi();
                //     layerUtils.iConfirm("晋金宝余额不足，请先充值", function () {
                //         monkeywords.empty();
                //         return;
                //     }, function () {
                //         appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
                //     }, "取消", "去充值");
                // }
                if (parseFloat(curVal) > productInfo.surplus_amount * 10000) {
                    $(_pageId + " #money").val(productInfo.surplus_amount * 10000);
                    layerUtils.iAlert("剩余额度为" + productInfo.surplus_amount + "万");
                    return;
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #money").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #money").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            }
        })
    }

    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            // 键盘完成按钮的事件
            keyBoardFinishFunction: function () {
            },
            // 键盘的输入事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            }
        };
    }

    //查询产品是否首次购买
    function reqFun102103() {
        service.reqFun102103(productInfo, function (data) {
            if (data.error_no != 0) {
                isFirstPurchase = true;
            } else {
                var results = data.results[0];
                if (results.is_can_add == 0) {//不是首次购买，能追加
                    isFirstPurchase = false;
                } else {
                    isFirstPurchase = true;
                }
            }
            // if(productInfo.prod_sub_type2 == "100"){
            //     new_smDetails()
            // }else{

            // }
            smDetails()

        }, { isLastReq: false })
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " .hfDate").text("");
        $(_pageId + " #money").val('');
        $(_pageId + " .prod_sname").text("");
        $(_pageId + " #inputspanid span").text("请输入购买金额").attr("text", "请输入购买金额").css({ color: "#999999" });
        $(_pageId + " .password_box").hide();
        $(_pageId + " .pop_layer").hide();
        monkeywords.flag = 0;
        monkeywords.destroy();
        step_amt_str = "";
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #payMethod").hide();
        $(_pageId + " .jjs_yue").hide();

    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
