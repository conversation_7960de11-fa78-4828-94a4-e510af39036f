// 源晖持有列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            common = require("common"),
            tools = require("../common/tools"),
            service = require("mobileService"),
            _page_code = "yuanhui/hold",
            _pageId = "#yuanhui_hold ";
    var VIscroll = require("vIscroll");
    var vIscroll = {"scroll": null, "_init": false};

    //cust_fund_type 客户资产类型 0-持有  1-在途 2-待支付
    var _cust_fund_type = "0";
    var currentPage = 1; //当前页数
    var numPerPage = 5; //每页条数
    var totalPages = 1;

    function init() {
        $(_pageId + " .visc_pullDown").css("visibility", "hidden");
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
        appUtils.clearSStorage("cancelOrderParam");
        appUtils.clearSStorage("highEndHoldDetail");


        getUnconfirmedProd(false)

//        getHoldProd(false)
        //总资产
        getFundAssetInfo()
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //点击持有或在途或已完成按钮
        appUtils.bindEvent($(_pageId + " .tab_box a"), function () {
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            _cust_fund_type = $(this).attr("cust_fund_type");
            $(_pageId + " .finance_pro .my_finance .purchase").html("");
            $(_pageId + " .finance_pro .my_finance .sell").html("");
            currentPage = 1;
            if (_cust_fund_type == "2") {
                getUnconfirmedProd(false);
            } else {
                getHoldProd(false);
            }

        }, "click");

        //交易记录
        appUtils.bindEvent($(_pageId + " .tradeRecord"), function () {
            appUtils.pageInit(_page_code, "highEnd/transaction");
        }, "click");

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_page_code)
        });
        //私募产品合同
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".contract", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            appUtils.setSStorageInfo("productInfo", detail);
            appUtils.pageInit(_page_code, "highEnd/contract", {isSign: true});
        }, 'click');

        //产品详情
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".pro_box", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).find(".fundInfo").text());
            var page = {
                "93": "yuanhui/holdDetail",
            }
            if ((detail.prod_sub_type2 == "93" && _cust_fund_type == "1") || _cust_fund_type == "2") {
                return;
            }

            appUtils.setSStorageInfo("fund_code", detail.fund_code);
            appUtils.setSStorageInfo("productInfo", detail);
            appUtils.pageInit(_page_code, page[detail.prod_sub_type2]);
        }, 'click');

        //汇款账号
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".remittance", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            appUtils.setSStorageInfo("productInfo", detail);
            appUtils.pageInit(_page_code, "yuanhui/payRecharge");
        }, 'click');

        //取消订单
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".cancel", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            appUtils.setSStorageInfo("cancelOrderParam", detail)
            appUtils.pageInit(_page_code, "highEnd/cancelOrder");
        }, 'click');

    }


    function getFundAssetInfo() {
        service.reqFun101999({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                $(_pageId + ' .prifundAssets').html(tools.fmoney(results.prifundAssets));
            } else {
                $(_pageId + ' .prifundAssets').html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function getHoldProd(isAppendFlag) {
        $(_pageId + " .new_none").hide();
        var param = {
            cur_page: currentPage,
            num_per_page: numPerPage,
            cust_fund_type: _cust_fund_type,
        };

        service.reqFun101904(param, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                var detailParams = results.data;
                if (!detailParams) {
                    //判断是否显示 没有更多数据
                    // show_new_none(_cust_fund_type)
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }
                totalPages = detailParams.totalPages;
                var fundData = detailParams.data;
                if (!fundData || fundData.length == 0) {
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }

                //客户资产类型 0-持有  1-在途
                if (_cust_fund_type == "0") {
                    $(_pageId + " .sum_total_income").html(tools.fmoney(results.sum_total_income));
                    //持有产品列表展示
                    showHoldFunds(fundData, isAppendFlag);
                } else if (_cust_fund_type == "1") {
                    //在途产品列表展示
                    showTransitFunds(fundData, isAppendFlag);
                }

            } else {
                layerUtils.iAlert(datas.error_info);
            }

            hidePullUp();
        });
    }

    //待支付订单
    function getUnconfirmedProd(isAppendFlag) {
        service.reqFun102100({}, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                var fundData = results.detailParams;
                if (!fundData || fundData.length == 0) {
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    getHoldProd(false);
                    return;
                }
                totalPages = 1;
                $(_pageId + " .tab_box a").removeClass("current");
                $(_pageId + " .tab_box a").eq(2).addClass("current");
                _cust_fund_type = "2";
                showUnconfirmedFunds(fundData, isAppendFlag);
            } else {
                layerUtils.iAlert(datas.error_info);
            }

            hidePullUp();
        })
    }

    //待支付列表展示
    function showUnconfirmedFunds(fundData, isAppendFlag) {
        $(_pageId + " .new_none").hide();
        //基金信息
        var html = "";

        for (var i = 0; i < fundData.length; i++) {
            //空数据处理
            fundData[i].cust_fund_type = "2";
            var detail = JSON.stringify(fundData[i]);
            //产品名称
            var fundname = fundData[i].fund_sname;
            //金额
            var app_amt = tools.fmoney(fundData[i].app_amt);
            //时间
            var crt_date = tools.ftime(fundData[i].crt_date);
            var crt_time = tools.ftime(fundData[i].crt_time);

            html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                    "<h4>" + fundname + "<em style='float: right;font-size: 0.12rem;color: #e5443c;'>待支付</em></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>订单创建时间：<span>" + crt_date + "&nbsp" + crt_time +
                    "</span></dd>" +
                    "</dl><dl><dt></dt><dd>订单金额： <span>" + app_amt +
                    "元</span></dd>" +
                    "</dd></dl></div></div><dl><dt><dd class='text-right'><span class='remittance'>汇款账号</span><span class='cancel'>取消订单</span>" +
                    "</dd></dt></dl></div>";
        }

        if (isAppendFlag) {
            $(_pageId + " .finance_pro .my_finance .purchase").append(html);
        } else {
            $(_pageId + " .finance_pro .my_finance .purchase").html(html);
        }
    }

    //持有产品列表展示
    function showHoldFunds(fundData, isAppendFlag) {
        //基金信息
        var html = "";
        for (var i = 0; i < fundData.length; i++) {
            fundData[i].cust_fund_type = "0";
            var detail = JSON.stringify(fundData[i]);
            //产品名称
            var fundname = fundData[i].fund_sname;
            //产品子类型
            var prod_sub_type2 = fundData[i].prod_sub_type2;
            if (prod_sub_type2 == "93") {
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + tools.fmoney(fundData[i].fund_vol) +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>昨日收益：<span>" + tools.fmoney(fundData[i].yest_income) +
                        "元</span></dd></dl><dl><dt></dt><dd>持仓收益： <span>" + tools.fmoney(fundData[i].hold_income) +
                        "元</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";
            }
        }

        if (isAppendFlag) {
            $(_pageId + " .finance_pro .my_finance .purchase").append(html);
        } else {
            $(_pageId + " .finance_pro .my_finance .purchase").html(html);
        }
    }

    //在途产品列表展示
    function showTransitFunds(fundData, isAppendFlag) {
        //基金信息
        var saleStr = "";
        var purchaseStr = "";
        for (var i = 0; i < fundData.length; i++) {
            //空数据处理
            fundData[i].cust_fund_type = "1";
            var detail = JSON.stringify(fundData[i])

            var fundname = fundData[i].fund_sname;//产品名称
            var fundVol = tools.fmoney(fundData[i].fund_vol);//资产
            var transitFlag = fundData[i].transit_flag;//在途标识
            var prod_sub_type2 = fundData[i].prod_sub_type2;//产品类型
            var to_account_amt = fundData[i].to_account_amt;//到账金额
            var cost_money = fundData[i].cost_money;//本金
            var total_income = fundData[i].total_income;//收益
            var confirm_date = fundData[i].confirm_date;//确认日期
            var out_way_vol = fundData[i].out_way_vol;//卖出份额
            var cancel_flag = "";
            if (fundData[i].cancel_flag == "0") {
            	cancel_flag = "<dl><dd></dd><div class='cancel' style='font-size: 0.12rem;color:#fff;width:0.4rem;text-align:center;background:#e5443c;'>撤单</div></dl>"
            }
            if (transitFlag == "1") { //买入在途
                if (prod_sub_type2 == "93") { //源晖
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + fundVol +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>确认日期：<span>" + tools.ftime(confirm_date) +
                            "</span></dd></dl>" + cancel_flag + "</div></div></div>";
                }
            } else { // 卖出在途
                if (prod_sub_type2 == "93") { // 源晖
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + "</h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>卖出份额：<span>" + tools.fmoney(fundVol) +
                            "</span>份</dd><dd class='text-right' style='white-space: nowrap;'" +
                            ">到账金额：<span>" + tools.fmoney(to_account_amt) +
                            "</span>元</dd></dl>" +
                            "</div></div></div>";
                }

            }
        }
        if (purchaseStr && (!isAppendFlag || $(_pageId + " .finance_pro .purchaseStar_title").length == 0)) { //购买串为空 && （下拉刷新 || 未加载买入在途标题）
            purchaseStr = "<div class='purchaseStar_title' style='margin:0.02rem;'>买入在途</div>" + purchaseStr;
        }
        if (saleStr && (!isAppendFlag || $(_pageId + " .finance_pro .sale_title").length == 0)) { //卖出串为空 && （下拉刷新 || 未加载卖出在途标题）
            saleStr = "<div class='sale_title' style='margin:0.02rem;'>卖出在途</div>" + saleStr;
        }

        if (isAppendFlag) {
            $(_pageId + " .finance_pro .my_finance .purchase").append(purchaseStr);
            $(_pageId + " .finance_pro .my_finance .sell").append(saleStr);
        } else {
            $(_pageId + " .finance_pro .my_finance .purchase").html(purchaseStr);
            $(_pageId + " .finance_pro .my_finance .sell").html(saleStr);
        }
    }

    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    currentPage = 1;
                    if (_cust_fund_type === "2") {
                        getUnconfirmedProd(false);
                    } else {
                        getHoldProd(false)
                    }
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        currentPage += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        if (_cust_fund_type == "2") {
                            getUnconfirmedProd(true);
                        } else {
                            getHoldProd(true)
                        }
                    } else {
                        $(_pageId + " .new_none").show();
                    }

                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        //$(_pageId + " .visc_pullUp").show();
    }

    function hidePullUp() {
//    	$(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }


    function destroy() {
        _cust_fund_type = "0";
        totalPages = 1;
        currentPage = 1;
        $(_pageId + ' .prifundAssets').html("--");
        $(_pageId + " .my_finance .purchase").html("");
        $(_pageId + " .my_finance .sell").html("");
        $(_pageId + " .tab_box a").removeClass("current").eq(0).addClass("current");
        $(_pageId + " .holdIncome").html("");
        $(_pageId + " .sum_total_income").html("--");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thhold = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thhold;
});
