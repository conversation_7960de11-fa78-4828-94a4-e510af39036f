// 通知管理
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_setInformManager";
    var ut = require("../common/userUtil");
    var message_push_flag;
    function init() {
        message_push_flag = ut.getUserInf().message_push_flag;
        // console.log(message_push_flag,validatorUtil.isNotEmpty(message_push_flag))
        //消息推送标识，0-拒收消息，1-接收消息
        if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '0'){
            // $(_pageId + " .buttonBox").removeClass("active");
            $(_pageId + " .switch").removeClass("switch_check");
            $(_pageId + " .switch").addClass("switch_uncheck");
        }else if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '1'){
            // $(_pageId + " .buttonBox").addClass("active");
            $(_pageId + " .switch").removeClass("switch_uncheck");
            $(_pageId + " .switch").addClass("switch_check");
        }
        let box = document.querySelector('#safety_setInformManager .switch'); // 监听对象
        let startTime = '' // 触摸开始时间
        let startDistanceX = '' // 触摸开始X轴位置
        let startDistanceY = '' // 触摸开始Y轴位置
        let endTime = '' // 触摸结束时间
        let endDistanceX = '' // 触摸结束X轴位置
        let endDistanceY = '' // 触摸结束Y轴位置
        let moveTime = '' // 触摸时间
        let moveDistanceX = '' // 触摸移动X轴距离
        let moveDistanceY = '' // 触摸移动Y轴距离
        box.addEventListener("touchstart", (e) => {
            startTime = new Date().getTime()
            startDistanceX = e.touches[0].screenX
            startDistanceY = e.touches[0].screenY
        })
        box.addEventListener("touchend", (e) => {
            var message_push_flag_new; 
            endTime = new Date().getTime()
            endDistanceX = e.changedTouches[0].screenX
            endDistanceY = e.changedTouches[0].screenY
            moveTime = endTime - startTime
            moveDistanceX = startDistanceX - endDistanceX
            moveDistanceY = startDistanceY - endDistanceY
            // console.log(moveDistanceX, moveDistanceY)    
            //需判断滑动的长度大于10 左右滑动幅度大于上下滑动幅度
            // 判断滑动距离超过40 且 时间小于500毫秒
            if (Math.abs(moveDistanceX) > 10) {
                // 判断X轴移动的距离是否大于Y轴移动的距离
                if (Math.abs(moveDistanceX) > Math.abs(moveDistanceY)) {
                    if(moveDistanceX > 0){  //关闭
                        if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '1'){
                            message_push_flag_new = '0';
                            setRequest(message_push_flag_new)
                        }
                    }else if(moveDistanceX < 0){    //开启
                        if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '0'){
                            message_push_flag_new = '1';
                            setRequest(message_push_flag_new)
                        }
                    }else if(moveDistanceX == 0){   //点击
                        if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '0'){
                            message_push_flag_new = '1';
                        }else if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '1'){
                            message_push_flag_new = '0';
                        }
                        setRequest(message_push_flag_new)
                    }
                } 
            }
        })
    }
    
    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击完成
        appUtils.bindEvent($(_pageId + " .switch"), function () {
            var message_push_flag_new;
            if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '0'){
                message_push_flag_new = '1';
            }else if(validatorUtil.isNotEmpty(message_push_flag) && message_push_flag == '1'){
                message_push_flag_new = '0';
            }
            setRequest(message_push_flag_new)
        });
    }
    function setRequest(message_push_flag_new){
        service.reqFun101058({message_push_flag: message_push_flag_new}, function (data) {
            if (data.error_no == "0") {
                var user = ut.getUserInf();
                user.message_push_flag = message_push_flag_new;
                ut.saveUserInf(user);
                message_push_flag = message_push_flag_new;
                if(message_push_flag == '0'){
                    $(_pageId + " .switch").removeClass("switch_check");
                    $(_pageId + " .switch").addClass("switch_uncheck");
                }else{
                    $(_pageId + " .switch").removeClass("switch_uncheck");
                    $(_pageId + " .switch").addClass("switch_check");
                }
                // $(_pageId + " .buttonBox").toggleClass("active");
            } else {
                layerUtils.iAlert(data.error_info)
            }
        })
    }
    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack()
    }

    var setInformManager = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setInformManager;
});
