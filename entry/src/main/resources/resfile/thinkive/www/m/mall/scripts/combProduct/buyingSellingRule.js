// 买入卖出规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        cfdUtils = require("cfdUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        common = require("common"),
        _pageId = "#combProduct_buyingSellingRule";
    var _pageCode = "combProduct/buyingSellingRule";
    var ut = require("../common/userUtil");
    var productInfo;
    var qr;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #payRuleBox").show();
        let targetprofit = appUtils.getPageParam() ? appUtils.getPageParam().targetprofit : '';
        if(targetprofit == '1'){
            $(_pageId + " .targetprofit_name").text('运作期');
        }else{
            $(_pageId + " .targetprofit_name").text('开放期');
        }
        productInfo = appUtils.getSStorageInfo("productInfo");
        //显示调仓规则，调仓费用
        if(productInfo && productInfo.adjustment_rule && productInfo.adjustment_rule.length) $(_pageId + " .adjustment_rule").html(productInfo.adjustment_rule);
        if(productInfo && productInfo.adjustment_cost && productInfo.adjustment_cost.length) $(_pageId + " .adjustment_cost").html(productInfo.adjustment_cost);
        if(!productInfo.adjustment_cost && !productInfo.adjustment_rule) {
            $(_pageId + " #transferRule").hide();
        }else{
            if(productInfo.adjustment_rule) $(_pageId + " #adjustment_rule_box").show();
            if(productInfo.adjustment_cost) $(_pageId + " #adjustment_cost_box").show();
            $(_pageId + " #transferRule").show();
        }
        //是否为投顾系列产品
        let isSeriesComb = appUtils.getSStorageInfo("isSeriesComb");
        if(isSeriesComb != '1') tools.initCombFundBtn(productInfo, _pageId);
        // tools.initCombFundBtn(productInfo, _pageId);
        if(productInfo.qrParameter && productInfo.qrParameter != ""){
            var qr = productInfo.qrParameter;
        }else{
            qr = 1;
        }
        //查询产品赎回提示 
        // $(_pageId + " #buy_tip").html(productInfo.buy_tip); // 买入规则
        $(_pageId + " #purchase_cost").html(productInfo.purchase_cost); // 转入费率
        $(_pageId + " #sold_cost").html(productInfo.sold_cost); // 转出费率
        $(_pageId + " #redeem_desc").html(productInfo.sell_revenue);
        if (productInfo.serviceFee && productInfo.serviceFee.length) {
            let html = "";
            productInfo.serviceFee.forEach(item => {
                html += `
                <div class="clause mt10">
                    <span class="textColor1">${item.name}</span>
                </div>
                <div class="bg-white m_text_gray m_padding_10">${item.content}。</div>
                `
            })
            $(_pageId + " #serviceFee").html(html);
        }
    }


    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

        //点击买入规则
        appUtils.bindEvent($(_pageId + " #payRule"), function () {
            $(_pageId + " .box").hide();
            $(_pageId + " .tabbar").removeClass('active');
            $(_pageId + " #payRule").addClass("active");
            $(_pageId + " #enchashmentRule").removeClass("active");
            $(_pageId + " #serviceCharge").removeClass("active");
            $(_pageId + " #payRuleBox").show();
            $(_pageId + " #serviceChargeBox").hide();
            $(_pageId + " #enchashmentRuleBox").hide();
        });
        //点击卖出规则
        appUtils.bindEvent($(_pageId + " #enchashmentRule"), function () {
            $(_pageId + " .box").hide();
            $(_pageId + " .tabbar").removeClass('active');
            $(_pageId + " #payRule").removeClass("active");
            $(_pageId + " #serviceCharge").removeClass("active");
            $(_pageId + " #enchashmentRule").addClass("active");
            $(_pageId + " #enchashmentRuleBox").show();
            $(_pageId + " #serviceChargeBox").hide();
            $(_pageId + " #payRuleBox").hide();
        });
        // 点击服务费用
        appUtils.bindEvent($(_pageId + " #serviceCharge"), function () {
            $(_pageId + " .box").hide();
            $(_pageId + " .tabbar").removeClass('active');
            $(_pageId + " #payRule").removeClass("active");
            $(_pageId + " #enchashmentRule").removeClass("active");
            $(_pageId + " #serviceCharge").addClass("active");
            $(_pageId + " #enchashmentRuleBox").hide();
            $(_pageId + " #payRuleBox").hide();
            $(_pageId + " #serviceChargeBox").show();
        });
        // 点击调仓规则
        appUtils.bindEvent($(_pageId + " #transferRule"), function () {
            $(_pageId + " .box").hide();
            $(_pageId + " .tabbar").removeClass('active');
            $(this).addClass('active');
            $(_pageId + " .box").hide();
            $(_pageId + " #transferRuleBox").show();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)

        });

    }

    function destroy() {
        $(_pageId + " #adjustment_rule_box").hide();
        $(_pageId + " #adjustment_cost_box").hide();
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #payRule").addClass("active");
        $(_pageId + " #enchashmentRule").removeClass("active");
        $(_pageId + " #redeem_desc").html("--");
        $(_pageId + " #payRuleBox").show();
        $(_pageId + " #enchashmentRuleBox").hide();
        $(_pageId + " #thfundBtn").hide();
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
        $(_pageId + " .targetprofit_name").text('');
        $(_pageId + " .box").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
