// 普通换卡--新卡号信息
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_newcardinfo ";
    var ut = require("../common/userUtil");
    var flag1 = false;
    var flag2 = false;
    var imgtype = "";
    var type = "";
    var platform = require("gconfig").platform;
    var activeInputDom = null; // 当前激活的 input
    var backPage = "", isBankValid, bankInfo = null;
    var userInfo;
    var send_sms_flag;
    var pay_type; //0：快捷代扣 1：协议支付
    var bank_serial_no; //银行渠道流水
    var payorg_id;//支付机构ID
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");
    var tips = "申请成功，请等待结果";

    function init() {
        userInfo = ut.getUserInf();
        $(_pageId).attr("data-pageLevel", "0");
        sms_mobile.init(_pageId);
        backPage = appUtils.getSStorageInfo("_prePageCode");
        if (platform == "1") {
            // 处理键盘不想上顶
            keyboardAdapt();
        }

        $(_pageId + " #getYzm").attr("data-state", "true");
        var backflag = appUtils.getSStorageInfo("_prePageCode");
        var upFlag = appUtils.getPageParam("upFlag");//上传是否成功
        var imgtype = appUtils.getPageParam("imgtype");//上传哪张图片
        if (upFlag == "" || upFlag == undefined) {
            flag1 = false;
            $(_pageId + " #frontimg img").attr("src", "./images/card/yhk1.png");
            flag2 = false;
            $(_pageId + " #backimg img").attr("src", "./images/card/yhk2.png");
            clearPage();
        } else if (upFlag == "ok") {//上传成功
            if (imgtype == "10") {
                flag1 = true;
            } else if (imgtype == "11") {
                flag2 = true;
            }
            layerUtils.iMsg(-1, "上传成功");
        } else if (upFlag == "fail") {//上传失败
            if (imgtype == "10") {
                flag1 = false;
                $(_pageId + " #frontimg img").attr("src", "./images/card/yhk1.png");
            } else if (imgtype == "11") {
                flag2 = false;
                $(_pageId + " #frontimg img").attr("src", "./images/card/yhk2.png");
            }
            layerUtils.iMsg(-1, "上传失败");
        }
        //查询所支持的银行
        BankCardInformation();
        $(_pageId + " section").eq(1).hide();
        $(_pageId + " section").eq(0).show();
        tools.getPdf("1");
        getTip();
    }

    function queryBankCardNo(param, truth) {
        service.BankByCard(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (!truth) {
                layerUtils.iLoading(false);
            }
            if (error_no == "0") {
                $(_pageId + " .place").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    bankInfo = result;
                    var bank_name = result.bank_name;
                    var bank_code = result.bank_code;
                    var single_limit = result.single_limit;
                    var day_limit = result.day_limit;
                    send_sms_flag = result.send_sms_flag;
                    payorg_id = result.payorg_id;
                    pay_type = result.pay_type;
                    if (parseFloat(day_limit) > 0) {
                        $(_pageId + " .place").show();
                        $(_pageId + " #drxe").html(day_limit + "元");
                    } else if (parseFloat(day_limit) == 0) {
                        $(_pageId + " .place").hide();
                    } else {
                        $(_pageId + " .place").show();
                        $(_pageId + " #drxe").html("不限");
                    }
                    if (parseFloat(single_limit) > 0) {
                        $(_pageId + " .place").show();
                        $(_pageId + " #oneMoney").html(single_limit + "元");
                    } else if (parseFloat(single_limit) == 0) {
                        $(_pageId + " .place").hide();
                    } else {
                        $(_pageId + " .place").show();
                        $(_pageId + " #oneMoney").html("不限");
                    }
                    $(_pageId + " #bankname").val(bank_name);
                    $(_pageId + " #bankname").attr("bank_code", bank_code).attr("pay_type",pay_type).attr("payorg_id",payorg_id);
                    // getPdf(param.bin_id, bank_no);
                    if (truth) {
                        sendchangecard();
                    }
                } else {
                    layerUtils.iLoading(false);
                    $(_pageId + " #bankname").val("");
                    $(_pageId + " .place").hide();
                    $(_pageId + " #bankname").attr("bank_code", "").removeAttr("pay_type").removeAttr("payorg_id");
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }

            } else {
                layerUtils.iLoading(false);
                $(_pageId + " .place").hide();
                $(_pageId + " #bankname").val("");
                $(_pageId + " #bankname").attr("bank_code", "").removeAttr("pay_type").removeAttr("payorg_id");
                layerUtils.iMsg(-1, error_info);
            }
        }, {isLastReq: false});
    }

    //绑定事件
    function bindPageEvent() {

        appUtils.bindEvent($(_pageId + " input"), function () {
            if (platform == "1") {
                activeInputDom = this;
            }
        }, "touchstart");

        //上传银行卡正面
        appUtils.bindEvent($(_pageId + " #upfront"), function () {
            imgtype = "10";
            type = _pageId + "#frontimg";//要显示图片的元素
            compress();
        });
        //上传银行卡反面
        appUtils.bindEvent($(_pageId + " #upback"), function () {
            imgtype = "11";
            type = _pageId + "#backimg";
            compress();
        });
        //银行卡号ocr识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            var paramExt = {
                "type": "#bank_card_new",
                "imgtype": "8"
            };
            var external = require("external");
            var Param = {
                "funcNo": "60304",
                "moduleName": "mall",
                "paramExt": paramExt
            };
            external.callMessage(Param);

        });
        //返回
        appUtils.bindEvent($(_pageId + " #icon_back"), function () {
            pageBack();
        });
        //关闭换卡必读
        appUtils.bindEvent($(_pageId + " .grid_02 a"), function () {
            $(_pageId + " .card_rules").hide();
            $(_pageId + " .pop_layer4").hide();
        });
        //换卡必读
        appUtils.bindEvent($(_pageId + " .right_btn"), function () {
            $(_pageId + " .card_rules").show();
            $(_pageId + " .pop_layer4").show();
        });
        //银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            $(_pageId + " .place").hide();
            $(_pageId + " #pop_view").css("visibility", "hidden");
            $(_pageId + " #big_show_bank").html("");
            var bin_id = $(this).val();
            bin_id = bin_id.replaceAll(" ", "");
            var param = {
                "bin_id": bin_id
            };
            if (bin_id == "" || bin_id == "undefined") {

            } else {
                if (validatorUtil.isBankCode(bin_id)) {
                    //查银行卡编号
                    queryBankCardNo(param, false);
                } else {
                    layerUtils.iMsg(-1, "无效卡号请重新输入");
                }

            }

        }, "blur");

        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");

        appUtils.bindEvent($(_pageId + " .bank_tips"), function () {
            $(_pageId + " section").eq(0).hide();
            $(_pageId + " section").eq(1).show();
        });
        appUtils.bindEvent($(_pageId + " #jiaback"), function () {
            pageBack();
            // $(_pageId + " section").eq(1).hide();
            // $(_pageId + " section").eq(0).show();
        });

        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            // 控制全数字输入
            /*var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if(!/\d/.test(curInputVal))
            {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }*/

            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);


            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");
        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //获取验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            if (!flag1) {
                layerUtils.iMsg(-1, "请上传新银行卡正面照片");
                return;
            }
            if (!flag2) {
                layerUtils.iMsg(-1, "请上传新银行卡反面照片");
                return;
            }
            var $code = $(_pageId + " #getYzm");
            var mobile = $(_pageId + " #yhmPhone").val();
            if ($code.attr("data-state") == "false") {
                return;
            }
            if (mobile != null && mobile != "") {
                if (!validatorUtil.isMobile(mobile)) {
                    //判断输入的是否是手机号
                    layerUtils.iMsg(-1, "请确定你输入的手机号码是否正确");
                    return;
                } else {
                    // 获取验证码
                    var param = {
                        "bank_code": $(_pageId + " #bankname").attr("bank_code"),//银行编码
                        "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                        "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                        "bank_acct": $(_pageId + " #bankCard").val(),     // 用户卡号
                        "cert_no": userInfo.identityNum,   //用户身份证
                        "bank_reserved_mobile":mobile,
                        "bank_name":$(_pageId + " #bankname").val(),
                        "sms_type":common.sms_type.changeCard,
                        "send_type": "0",//发送短信验证码
                        "cert_type": "0", //证件类型
                        "cust_name": userInfo.name, // 用户姓名
                        "mobile_phone": mobile,
                        "type": common.sms_type.changeCard,
                    };
                    if(send_sms_flag == "1"){
                        sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                            if (data.error_no == "0") {
                                bank_serial_no = data.results[0].bank_serial_no
                            }else{
                                layerUtils.iAlert(data.error_info);
                            }
                        });
                    }else{
                        sms_mobile.sendPhoneCode(param);
                    }
                    // sms_mobile.sendPhoneCode(param);
                }
            } else {
                layerUtils.iMsg(-1, "请确定你输入的手机号码是否正确");
                return;
            }
        });
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var mobile = $(_pageId + " #yhmPhone").val();
            if (!flag1) {
                layerUtils.iMsg(-1, "请上传新银行卡正面照片");
                return;
            }
            if (!flag2) {
                layerUtils.iMsg(-1, "请上传新银行卡反面照片");
                return;
            }
            var bankCard = $(_pageId + " #bankCard").val();
            if (!validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确的新银行卡号");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").val())) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的手机号码是否正确");
                return;
            }
            if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                layerUtils.iMsg(-1, "请先获取验证码");
                return;
            }
            if ($(_pageId + " #verificationCode").val() == "") {
                if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                    layerUtils.iMsg(-1, "请先获取验证码");
                } else {
                    layerUtils.iMsg(-1, "请输入验证码");
                }
            } else {
                var chencked = $(_pageId + " #input_radio2").attr("checked");
                if (chencked != "checked" && isBankValid) {
                    layerUtils.iMsg(-1, "请阅读协议并同意签署");
                    return;
                }
                var bankParam = {
                    "bin_id": $(_pageId + " #bankCard").val()
                };
                queryBankCardNo(bankParam, true);
            }

        });
        appUtils.bindEvent($(_pageId + " #yhmPhone"), function () {
            if (!(bankInfo)) {
                $(_pageId + " .place").hide();
                $(_pageId + " #pop_view").css("visibility", "hidden");
                $(_pageId + " #big_show_bank").html("");
                var bankCard = $(_pageId + " #bankCard").val();
                var bin_id = $(_pageId + " #bankCard").val();
                bin_id = bin_id.replaceAll(" ", "");
                var param = {
                    bin_id: bin_id
                };
                if (validatorUtil.isBankCode(bin_id)) {
                    //查银行卡编号
                    queryBankCardNo(param, false);
                }
            }

        }, "focus");
        //绑卡协议
        appUtils.preBindEvent($(_pageId + " #bank_xy"), "p a", function () {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        }, 'click');

        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });

    }

    //调用原生压缩图片
    function compress() {
//		layerUtils.iLoading(true);
        var paramExt = {
            "type": type,
            "imgtype": imgtype,
            multi:false
        };
        var param = {};
        param["funcNo"] = "50273";
        param["moduleName"] = "mall";
//        param["serverAddr"] = "http://www.sxfae.com:8081/servlet/FileUpload?function=uploadImg";//服务器地址不传，默认为空，压缩后直接返回baseimg64
        param["compress"] = "0.5";
        param["cutFlag"] = "0";
        param["width"] = "1600";
        param["height"] = "900";
        param["fileName"] = "changeCard";
        param["cutFlag"] = "0";
        param["paramExt"] = paramExt;
        tools.fileImg(_pageId,param)
        // require("external").callMessage(param);
    }


    function onSuccesCardUploadFile() {
        var funcCallBack = function (resultsVo) {
            if (resultsVo.error_no == "0") {
                appUtils.pageInit("safety/newcardinfo", "account/bankCardAdministration", {});
            } else {
                layerUtils.iAlert(resultsVo.error_info);
            }
        }
        service.reqFun1100007({}, funcCallBack);
    }

    // 客户是否存在未确认的普通取现交易
    function getTip() {
        service.reqFun102076({}, function(data) {
            if(data.error_no != "0") {
                tips = "申请成功，请等待结果";
                layerUtils.iAlert(data.error_info);
                return;
            }
            var exist_flag = data.results[0].exist_flag; // 0存在 1 不存在
            if(exist_flag == "1") {
                tips = "申请成功，请等待结果";
                return;
            }
            tips = "申请成功，您有在途资金正在处理，到账后方可审核通过";
        })
    }


    function sendchangecard() {
        var attached_url = appUtils.getSStorageInfo("attached_url");
        var hkCardInfo = appUtils.getSStorageInfo("hkCardInfo");
        var hkUserInfo = appUtils.getSStorageInfo("hkUserInfo");
        var param = {
            "cust_name": hkUserInfo.name,//客户姓名
            "bank_acct": $(_pageId + " #bankCard").val(),//新银行卡号
            "org_bank_acct": hkCardInfo.bank_card,//旧银行卡号
            "org_bank_code": userInfo.bankCode,//旧银行编码
            "bank_code": $(_pageId + " #bankname").attr("bank_code"),//新银行编码
            "org_bank_reserved_mobile": hkCardInfo.mobile_phone,//hkCardInfo.mobile_phone,//appUtils.getSStorageInfo("bank_leave_phone"),//旧预留银行手机号码
            "bank_reserved_mobile": $(_pageId + " #yhmPhone").val(),//新银行预留手机号码
            "attached_url": attached_url, //文件打包路径
            "message_code": $(_pageId + " #verificationCode").val(),//银行预留手机号验证码
            "pay_type": $(_pageId + " #bankname").attr("pay_type"),
            "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
            "bank_serial_no": bank_serial_no,
            "cert_type": "0",
            "sms_mobile": $(_pageId + " #yhmPhone").val(),
            "sms_code": $(_pageId + " #verificationCode").val()
        };
        var callback = function (resultsVo) {
            sms_mobile.clear(_pageId);
            //验证码重置
            $(_pageId + " .tips_box").hide();
            //验证码重置
            if (resultsVo.error_no == 0) {
                clearPage();
                appUtils.clearSStorage("hkCardInfo");
                appUtils.clearSStorage("hkUserInfo");
                layerUtils.iAlert(tips, "0", function () {
                    onSuccesCardUploadFile();
                });
            } else {
                layerUtils.iLoading(false);
                $(_pageId).attr("data-pageLevel", "0");
                layerUtils.iAlert(resultsVo.error_info);
            }
        };
        $(_pageId).attr("data-pageLevel", "-1");
        service.reqFun101061(param, function(data) {
            if(data.error_no != "0") {
                sms_mobile.clear();
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            service.reqFun101016(param, callback);
        }, {isLastReq: false})
    }

    //页面清理
    function clearPage() {
        $(_pageId + " input").attr("value", "");
        $(_pageId + " #bankname").removeAttr("pay_type").removeAttr("payorg_id");
    }

    function destroy() {
        send_sms_flag = null;
        bankInfo = null;
        sms_mobile.destroy(_pageId);
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        service.destroy();
        $(_pageId + " #getYzm").removeAttr("style");
        $(_pageId + " .sure_box").hide();
        $(_pageId + ' #weihao').hide();
        $(_pageId + " .place").hide();
        tips = "申请成功，请等待结果";
    }

    /**
     * 处理 键盘遮住
     */
    function keyboardAdapt() {
        var winHeight = $(window).height();
        window.onresize = function () {
            var $curInput = $(activeInputDom);
            var diffHeight = winHeight - $(window).height();
            if (diffHeight > 200 && $curInput.offset().top + diffHeight + $curInput.height() > winHeight) {
                if ($curInput.offset().top + diffHeight + $curInput.height() - winHeight < 50) {
                    $(_pageId + " article")[0].scrollTop += 50;
                } else if ($curInput.offset().top + diffHeight + $curInput.height() - winHeight < 100) {
                    $(_pageId + " article")[0].scrollTop += 100;
                } else if ($curInput.offset().top + diffHeight + $curInput.height() - winHeight < 150) {
                    $(_pageId + " article")[0].scrollTop += 150;
                } else {
                    $(_pageId + " article")[0].scrollTop += diffHeight;
                }
                $(activeInputDom).focus();
            }
        }

    }

    //查出支持的银行卡
    function BankCardInformation() {
        service.reqFun102014({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            var str = "";
            if (error_no == "0") {
                for (var i = 0; i < data.results.length; i++) {
                    var bank_name = data.results[i].bank_name;
                    var day_limit = data.results[i].day_limit;
                    var single_limit = data.results[i].single_limit;
                    var remark = data.results[i].remark;
                    if (day_limit < 0) {
                        day_limit = "不限";
                    }
                    if (single_limit < 0) {
                        single_limit = "不限";
                    }
                    if (day_limit >= 10000) {
                        day_limit = day_limit / 10000 + "万";
                    }
                    if (single_limit >= 10000) {
                        single_limit = single_limit / 10000 + "万";
                    }
                    var recommend_flag = data.results[i].recommend_flag;//是否推荐 0不推荐1推荐
                    if(recommend_flag == '1') {
                        str += "<tr><td>" + bank_name + "</td><td>" + single_limit + "</td><td>" + day_limit + "</td><td class='add recommend'><img src='images/star.png'>" + remark + "</td></tr>";
                    }else{
                        str += "<tr><td>" + bank_name + "</td><td>" + single_limit + "</td><td>" + day_limit + "</td><td class='add'>" + remark + "</td></tr>";
                    }
                }
                $(_pageId + " #mainInfo").html(str);
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }


    // 返回键处理事件
    function pageBack() {
        if($(_pageId + " section").eq(1).css("display")== "none") {
            appUtils.pageBack();
        } else {
            $(_pageId + " section").eq(0).show();
            $(_pageId + " section").eq(1).hide();
        }

    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
