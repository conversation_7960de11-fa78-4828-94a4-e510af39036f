//强增 强减 交易详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#inclusive_additionSubtraction ";
	var ut = require("../common/userUtil");
	var tools = require("../common/tools");

    function init() {
    	var param = appUtils.getPageParam();
    	//预约申购交易详情查询
    	reqFun102110(param);
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }
 
    function reqFun102110(param){
	   	 service.reqFun102110(param, function(data){
	   		 if (data.error_no != "0") {
	   			 layerUtils.iAlert(data.error_info);
	   			 return;
	   		 }
	   		 var results = data.results[0];
	            if (!results || results.length == 0) {
	                return;
	            }
	   		 	//数据处理 空 和 --
	            results = tools.FormatNull(results);
	            // console.log(results,111)
                //标题
                let title = results.sub_busi_code == '14401' ? '强行调增' : '强行调减'
                //状态
                let trans_status_name = tools.fundDataDict(results.trans_status,"pub_trans_status_name");
                //份额
                let ack_vol = tools.fmoney(results.ack_vol) + '份';
                //描述
                let remark = results.prod_name + '(' + results.prod_code + ')'
                //流水号
                let trans_serno = results.trans_serno
                //交易时间
                let trans_time = results.trans_time
	            $(_pageId + " .title").html(title);
                $(_pageId + " #trans_status").html(trans_status_name);
                $(_pageId + " #trans_amt").html(ack_vol);
                $(_pageId + " .text").html(remark);
                $(_pageId + " #trans_serno").html(trans_serno);
                $(_pageId + " #trans_time").html(trans_time);
                $(_pageId + " #ack_vol").html(ack_vol);
	   	 });
    }    

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
    	
    }

     
    
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
