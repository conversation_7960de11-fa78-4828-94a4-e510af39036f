// 高端版本认证首页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#highVersion_authentication ",
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "highVersion/authentication";
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        ///获取用户合格投资者的认证信息
        getAuthenticationInfo();
    }
    //获取用户合格投资者的认证信息
    function getAuthenticationInfo(){
        service.reqFun181010({}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let res = datas.results
            if(!res || !res.length){ // 无数据 未认证
                $(_pageId + ".completed").hide();
                $(_pageId + ".incomplete").show();
                $(_pageId + ".go_certification").attr('status','0');
                $(_pageId + ".go_certification").text('立即认证');
                return;
            }
            //私募合格认证/小集合合格认证 信息
            let privateInfo = {};
            let smallInfo = {};
            res.map((item,index)=>{
                if(item.type == '11') privateInfo = item ? item : {};
                if(item.type == '12') smallInfo = item ? item : {};
            })
            if((!privateInfo.vaild_flag || privateInfo.vaild_flag == '1') && (!smallInfo.vaild_flag || smallInfo.vaild_flag == '1')){
                $(_pageId + ".completed").hide();
                $(_pageId + ".incomplete").show();
            }else if((privateInfo && privateInfo.state == '0') || (smallInfo && smallInfo.state == '0')){
                $(_pageId + ".completed").hide();
                $(_pageId + ".incomplete").show();
            }else if((privateInfo && privateInfo.state == '1' && privateInfo.vaild_flag != '1') || (smallInfo && smallInfo.state == '1' && smallInfo.vaild_flag != '1')){
                $(_pageId + ".incomplete").hide();
                $(_pageId + ".completed").show();
            }
            let html = ``;
            //同时存在 privateInfo/smallInfo 且 .vaild_date 存在且不同 展示两条认证信息
            // if(privateInfo && smallInfo && privateInfo.vaild_date && smallInfo.vaild_date && privateInfo.vaild_date != smallInfo.vaild_date){
                
            // }
            //渲染审核中
            if((privateInfo || smallInfo) && ((privateInfo && privateInfo.state == '0') || (smallInfo && smallInfo.state == '0'))){
                html == ``
            }else if(privateInfo && smallInfo && privateInfo && privateInfo.vaild_date && smallInfo && smallInfo.vaild_date && (privateInfo.vaild_date == smallInfo.vaild_date)){
                html = `
                    <span class="flex ${smallInfo.vaild_date ? '' : 'display_none'}">您的合格投资者有效期至${smallInfo.vaild_date  ? tools.formatDateString(smallInfo.vaild_date):''}</span>
                `
            }else{
                html = `
                    <span class="flex ${(smallInfo && smallInfo.vaild_date && smallInfo.vaild_flag == '0') ? '' : 'display_none'}">您的小集合合格投资者有效期至${(smallInfo && smallInfo.vaild_date)  ? tools.formatDateString(smallInfo.vaild_date):''}</span>
                    <span class="flex ${(privateInfo && privateInfo.vaild_date && privateInfo.vaild_flag == '0') ? '' : 'display_none'}">您的私募合格投资者有效期至${(privateInfo && privateInfo.vaild_date) ? tools.formatDateString(privateInfo.vaild_date):''}</span>
                `
            }
            $(_pageId + ".authenticationInfo").html(html);
            
            //渲染审核中
            if((privateInfo || smallInfo) && ((privateInfo && privateInfo.state == '0') || (smallInfo && smallInfo.state == '0'))){
                //私募合格认证 有效期不存在 展示 审核中
                $(_pageId + ".go_certification").attr('status','1');
                return $(_pageId + ".go_certification").text('审核中');
            }
            //渲染全部过期情况
            if((!smallInfo.vaild_flag || (smallInfo && smallInfo.vaild_flag == '1')) && (!privateInfo.vaild_flag ||(privateInfo && privateInfo.vaild_flag == '1') )){
                $(_pageId + ".go_certification").attr('status','0');
                return $(_pageId + ".go_certification").text('立即认证');
            }
            //渲染重新认证按钮
            if((privateInfo || smallInfo)  && ((privateInfo && privateInfo.vaild_date) || (smallInfo && smallInfo.vaild_date))){
                //存在一个有效期 展示 重新认证
                $(_pageId + ".go_certification").attr('status','2');
                return $(_pageId + ".go_certification").text('重新认证');
            }
            //渲染立即认证
            if(!privateInfo && !smallInfo){
                //不存在有效期 展示 去认证
                $(_pageId + ".go_certification").attr('status','0');
                return $(_pageId + ".go_certification").text('立即认证');
            }
            
        })
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "login_userRegistered";
            tools.saveAlbum(_page_code,param)
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //年收入证明
        appUtils.bindEvent($(_pageId + " .income_proof"), function () {
            appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
            appUtils.pageInit(_page_code,"highEnd/incomeModel");
        });
        //认证攻略
        appUtils.bindEvent($(_pageId + " .authenRemark"), function () {
            appUtils.pageInit(_page_code, "highVersion/articleDetails",{essay_id:'115'});
        });
        //资产证明
        appUtils.bindEvent($(_pageId + " .asset_proof"), function () {
            appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
            appUtils.setSStorageInfo("isSave", true);
            appUtils.pageInit(_page_code,"highEnd/assetsModel");
            // appUtils.pageInit(_page_code,"highEnd/assetsModel");
        });
        //去认证
        appUtils.bindEvent($(_pageId + " .go_certification"), function () {
            let status = $(this).attr('status');
            if(status == '1'){
                tools.recordEventData('1','go_certification_1','审核中');
                return layerUtils.iAlert('您的资格认证审核中，请耐心等待');
            }
            if(status == '2'){
                //去重新认证
                tools.recordEventData('1','go_certification_2','重新认证');
                appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
                return appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor1");
            }
            if(status == '0' || !status){
                tools.recordEventData('1','go_certification_0','立即认证');
                appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
                appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor1");
            }
            
        });
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var highVersion_authentication = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_authentication;
});
