 //   変更银行预留手机 详细信息
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		_pageId = "#safety_bankpromptText ";

	function init(){
		var bankNo = appUtils.getPageParam("bank_no");//根据银行编号查询变更说明
		queryPrompt(bankNo)
	}

	//绑定事件
	function bindPageEvent(){
		//点击返回按钮
		appUtils.bindEvent($(_pageId+" #getBack"),function(){
			appUtils.pageBack();
		});

	}

	//查出支持的银行卡
	function queryPrompt(bankNo){
		service.reqFun102014({},function(data) {
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				for(var i=0;i<data.results.length;i++){
					var bank_no = data.results[i].bank_no;
					if(bankNo==bank_no){
						var bank_name = data.results[i].bank_name;
						var remark = data.results[i].remark;
						$(_pageId + " #bankName").html(bank_name);
						$(_pageId + " #inner").html(remark);
					}
				}
			}else{
				layerUtils.iAlert(error_info);
			}
		});
	}
	function destroy(){

	}
	var bankpromptText = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy
	};
	// 暴露对外的接口
	module.exports = bankpromptText;
});
