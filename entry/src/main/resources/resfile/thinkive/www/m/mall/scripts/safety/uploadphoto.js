 // 上传照片
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		common = require("common"),
		tools = require("../common/tools"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		msgFunction = require("msgFunction"),
		_pageId = "#safety_uploadphoto ";

	var imgtype = "";
	var type = "";
	var msgFunction = require("msgFunction");
	var id = "";
	var flag1 = false,
		flag2 = false,
		flag3 = false;
	var backPage = "";
	function init(){
		if(appUtils.getSStorageInfo("fundsStatus") == "3"){
			$(_pageId + " #str1").hide();
			$(_pageId + " #str2").show();
		}else{
			$(_pageId + " #str2").hide();
			$(_pageId + " #str1").show();
		}
		backPage = appUtils.getSStorageInfo("_prePageCode");
		//初始化页面图片
		var upFlag = appUtils.getPageParam("upFlag");//上传结果
        var imgtype = appUtils.getPageParam("imgtype");//上传哪张图片
		if(upFlag == "" || upFlag == "undefined"){
				flag1 = false;
				$(_pageId + " #frontimg img").attr("src","images/card/sfz1.png");
				flag2 = false;
				$(_pageId + " #backimg img").attr("src","images/card/sfz2.png");
				flag3 = false;
				$(_pageId + " #handimg img").attr("src","images/card/sc.png");
		}else if(upFlag == "fail"){//上传失败
			if(imgtype == "4"){
                flag1 = false;
                $(_pageId + " #frontimg img").attr("src","images/card/sfz1.png");
			}else if(imgtype == "5"){
                flag2 = false;
                $(_pageId + " #backimg img").attr("src","images/card/sfz2.png");
			}else if(imgtype == "12"){
                flag3 = false;
                $(_pageId + " #handimg img").attr("src","images/card/sc.png");
			}
			layerUtils.iMsg(-1,"上传失败");
		}else if(upFlag == "ok"){
            if(imgtype == "4" ){
                flag1 = true;
			}else if(imgtype == "5"){
                flag2 = true;
			}else if(imgtype == "12"){
				flag3 = true;
			}
			layerUtils.iMsg(-1,"上传成功");
		}
		 custno = appUtils.getSStorageInfo("custNo");
	}

	//调用原生压缩图片
	function compress(){
//		layerUtils.iLoading(true);
		var paramExt = {
			"type":type,
			"imgtype":imgtype,
			multi:false
		};
		var param = {};
		param["funcNo"] = "50273";
        param["moduleName"] = "mall";
//        param["serverAddr"] = "http://www.sxfae.com:8081/servlet/FileUpload?function=uploadImg";//服务器地址不传，默认为空，压缩后直接返回baseimg64
        param["compress"] = "0.5";
        param["width"] = "1600";
        param["height"] = "900";
        param["fileName"] = "changeCard";
        param["cutFlag"] = "0";
        param["paramExt"] = paramExt;
		tools.fileImg(_pageId,param)
        // require("external").callMessage(param);
	}




	//绑定事件
	function bindPageEvent(){
		//关闭换卡必读
		appUtils.bindEvent($(_pageId+" .grid_02 a"),function(){
			$(_pageId + " .card_rules").hide();
			$(_pageId + " .pop_layer4").hide();
		});
		//换卡必读
		appUtils.bindEvent($(_pageId+" .right_btn"),function(){
			$(_pageId + " .card_rules").show();
			$(_pageId + " .pop_layer4").show();
		});
		//后退
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});
		//上传身份证正面
		appUtils.bindEvent($(_pageId+" #upfront"),function(){
			imgtype = "4";
			type = _pageId + " #frontimg";
			compress();
		});
		//上传身份证反面
		appUtils.bindEvent($(_pageId+" #upback"),function(){
			imgtype = "5";
			type = _pageId + " #backimg";
			compress();
		});
        //上传手持身份证
        appUtils.bindEvent($(_pageId+" #uphand"),function(){
            imgtype = "12";
            type = _pageId + " #handimg";
            compress();
        });
		//下一步
		appUtils.bindEvent($(_pageId+" #next"),function(){
			if(!flag1){
				layerUtils.iMsg(-1,"请上传身份证正面图片");
				return;
			}

			if(!flag2){
				layerUtils.iMsg(-1,"请上传身份证背面图片");
				return;
			}

			if(!flag3){
				layerUtils.iMsg(-1,"请上传一手持身份证正面，一手持新银行卡正面图片");
				return;
			}

			appUtils.pageInit("safety/uploadphoto","safety/newcardinfo");

		});

	}



	function destroy(){
		var imgtype = "";
		var id = "";
	}
	function pageBack(){
		appUtils.pageBack();
	}
	var advertisement = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = advertisement;
});
