let monthList,yearList
let new_month = new Date().getMonth() + 1;
let new_year = new Date().getFullYear();
let new_day = new Date().getDate();
// let new_month = 5;
// let new_year = 2023;
let monthList_childs = [
    {id:'01',value:'1月'},
    {id:'02',value:'2月'},
    {id:'03',value:'3月'},
    {id:'04',value:'4月'},
    {id:'05',value:'5月'},
    {id:'06',value:'6月'},
    {id:'07',value:'7月'},
    {id:'08',value:'8月'},
    {id:'09',value:'9月'},
    {id:'10',value:'10月'},
    {id:'11',value:'11月'},
    {id:'12',value:'12月'}
];
monthList = [{
    id:'2020',
    value:'2020年',
    childs:[
        {id:'06',value:'6月'},
        {id:'07',value:'7月'},
        {id:'08',value:'8月'},
        {id:'09',value:'9月'},
        {id:'10',value:'10月'},
        {id:'11',value:'11月'},
        {id:'12',value:'12月'}
    ]
},
{
    id:'2021',
    value:'2021年',
    childs:[
        {id:'01',value:'1月'},
        {id:'02',value:'2月'},
        {id:'03',value:'3月'},
        {id:'04',value:'4月'},
        {id:'05',value:'5月'},
        {id:'06',value:'6月'},
        {id:'07',value:'7月'},
        {id:'08',value:'8月'},
        {id:'09',value:'9月'},
        {id:'10',value:'10月'},
        {id:'11',value:'11月'},
        {id:'12',value:'12月'}
    ]
},];
yearList = [
    // {
    //     id:2020,
    //     value:'2020年',
    // },
]

let Last_year;
let Last_month;
let year_flag = 0;
/**********  日期测试*/
// new_year = 2025; 
// new_month = 1;
// new_day = 15;
/********** */
// console.log(new_year,'年',new_month,'月',new_day,'日');
if(new_month == 1){ //一月的特殊处理
    if(new_day > 14){
        Last_month = 12;
        Last_year = new_year - 1;
        year_flag = 0;  //可以看上一年年度
    }else{
        Last_year = new_year - 1;
        Last_month = 11;
        year_flag = 1;//不可以看;
    }
}else if(new_month == 2){   //二月的特殊处理
    if(new_day > 14){
        Last_year = new_year;
        Last_month = 1;
        year_flag = 1;//不可以看;
    }else{
        Last_year = new_year - 1;
        Last_month = 12;
    }
    
}else{
    Last_year = new_year;
    if(new_day > 14){
        Last_month = new_month - 1
    }else{
        Last_month = new_month - 2;
    }
    year_flag = 1;//不可以看;
}
let list;
for(let i=2021; i < Last_year*1;i++){
    list = {
        id:i + 1,
        value:i + 1 + '年',
        childs:[],
    };
    if(i == (Last_year*1 - 1)){
        for(let j=1; j < (Last_month + 1)*1;j++){
            let data = {
                id:(j*1 < 10 ? '0' +  j : j+''),
                value:j+'月'
            }
            list.childs.push(data)
        }
        monthList.push(list);
    }else {
        list.childs = monthList_childs;
        monthList.push(list);
    }
}
let yearNew = Last_year;
if(year_flag == 0){
    yearNew = yearNew + 1
}else{
    yearNew = yearNew;
}
for(let i = 2020; i < yearNew  ; i++){
    yearList.push({
        id:i,
        value:i + '年',
    }) 
}
