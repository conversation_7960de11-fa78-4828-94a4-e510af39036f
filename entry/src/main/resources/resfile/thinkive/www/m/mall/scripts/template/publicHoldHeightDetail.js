// 公募产品持仓模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "template/publicHoldHeightDetail",
        _pageId = "#template_publicHoldHeightDetail";
    require("chartsUtils");
    var ut = require("../common/userUtil");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var validatorUtil = require("validatorUtil");
    // require("../common/html2canvas.min");
    let publicHoldHeightDetail; //new 一个 vue 实例
    let productInfo;
    let prodType;
    let redemptionRate;
    let isProduct540 = false;//是否是540天产品
    var swipeInstance;
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '1',
                fund_code: productInfo.fund_code
            }
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res.template_content)
            })
        })
    }
    //获取产品详情
    async function detailData(newData) {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '1',
                fund_code: newData.jump_page_prodid
            }
            service.reqFun102113(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res)
            })
        })
    }
    //获取私募产品详情
    async function init() {
        let newData = appUtils.getPageParam();
        if (newData && newData.jump_page_virfundcode) {    //弹窗特殊与情况
            let res = await detailData(newData);
            res.vir_fundcode = newData.jump_page_virfundcode;
            appUtils.setSStorageInfo("productInfo", res); //产品信息
            appUtils.setSStorageInfo("fund_code", res.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", res.prod_sub_type);
            appUtils.setSStorageInfo("financial_prod_type", res.financial_prod_type);
        }
        productInfo = appUtils.getSStorageInfo("productInfo");  //缓存的列表详情
        tools.initPagePointData({fundCode:productInfo.fund_code});
        let html = await setTemplate() //拿到模板数据
        $(".main_publicHoldHeightDetail").html(html)   //渲染模板
        publicHoldHeightDetail = new Vue({
            el: '#main_publicHoldHeightDetail',
            data() {
                return {
                    oss_url:global.oss_url,
                    button_flag:false,//底部按钮
                    detail: {},
                    buy_flag: "",    //购买按钮
                    sell_flag: "",   //卖出按钮
                    flag_data: "",
                    new_htmls: "",
                    prodMsg: [],
                    investmentNum: '',//定投个数
                    holding_days: require("gconfig").global.holding_days,
                    //分红方式
                    defdividend_method_obj: {
                        "1": "红利再投",
                        "2": "分红到晋金宝",
                        "3": "分红到晋金宝",
                    },
                    //产品到期方式
                    endflag_method_text_obj: {
                        "0": "自动滚入下一期",
                        "1": "自动赎回到银行卡",
                        "2": "自动赎回到晋金宝",
                    },
                    transformFundType: {
                        "00": "综合性",
                        "10": "股票型",
                        "20": "混合型",
                        "30": "债券型",
                        "40": "货币型",
                        "50": "商品型",
                        "60": "FOF 型",
                        "70": "QDII 型",
                        "80": "其它"
                    },
                    //购买状态
                    buy_stateObj: {
                        "1": {
                            "txt": "买入",
                            "class": "",
                        },
                        "2": {
                            "txt": "预约",
                            "class": "",
                        },
                        "3": {
                            "txt": "买入",
                            "class": "no_active",
                        },
                        "4": {
                            "txt": "买入",
                            "class": "no_active",
                        },
                        "6": {
                            "txt": "买入",
                            "class": "no_active",
                        },
                    }
                }
            },
            //视图 渲染前
            async created() {
                prodType = $(_pageId + " .public_top").attr("prodType");
                isProduct540 = $(_pageId + " .public_top").attr("isproduct540") == '1' ? true : false;
                appUtils.setSStorageInfo("prodType", prodType);
                //获取持仓详情
                appUtils.setSStorageInfo("productInfo", { ...productInfo, ...this.detail }); //产品信息
                this.detail = await this.getDetails();
                this.detail.prod_sub_type2 = '200';
                //买入在途
                if (this.detail.fund_in_way_vol > 0) {
                    $(_pageId + " #fund_way_vol_box").show();
                    $(_pageId + " #fund_way_vol").html(tools.changeTwoDecimal_f(this.detail.fund_in_way_vol));
                } else {
                    $(_pageId + " #fund_way_vol_box").hide();
                }
                //卖出在途
                if (this.detail.fund_out_way_vol > 0) {
                    $(_pageId + " #fund_out_way_vol_box").show();
                    $(_pageId + " #fund_out_way_vol").html(tools.changeTwoDecimal_f(this.detail.fund_out_way_vol));
                } else {
                    $(_pageId + " #fund_out_way_vol_box").hide();
                }
                // 产品资讯
                if (this.detail.prodMsg && this.detail.prodMsg.length) {
                    this.prodMsg = this.detail.prodMsg;
                }

                this.detail.sum_fund_amt = tools.changeTwoDecimal_f(this.detail.sum_fund_amt);    //资产
                this.detail.accrual_basis = tools.changeTwoDecimal_f(this.detail.accrual_basis); //年化业绩
                this.detail.respect_income = tools.changeTwoDecimal_f(this.detail.respect_income); //当期预计收益
                // this.detail.due_date = tools.ftime(this.detail.due_date);//到期日
                this.detail.hold_vol = tools.changeTwoDecimal_f(this.detail.hold_vol);    //持有份额
                this.detail.lock_vol = tools.changeTwoDecimal_f(this.detail.lock_vol);    //锁定份额
                this.detail.available_vol = tools.changeTwoDecimal_f(this.detail.available_vol);    //可用份额
                this.detail.cost_money = tools.changeTwoDecimal_f(this.detail.cost_money);    //本金
                this.detail.hold_income = tools.changeTwoDecimal_f(this.detail.hold_income);  //收益
                this.detail.last_income = tools.changeTwoDecimal_f(this.detail.last_income);    //昨天收益
                this.detail.accumulated_income = tools.changeTwoDecimal_f(this.detail.accumulated_income);  //累计收益
                this.detail.used_vol_sum_30 = tools.changeTwoDecimal_f(this.detail.used_vol_sum_30);  //满7天份额
                this.detail.cost_unit_price = tools.changeTwoDecimal_f(this.detail.cost_unit_price, 4);  //成本单价
                this.detail.unit_price = tools.changeTwoDecimal_f(this.detail.unit_price, 4);  //成本单价-新
                this.detail.nav = await tools.changeTwoDecimal_f(this.detail.nav, 4);  //净值
                this.detail.fixed_investment_list = this.detail.fixed_investment_list * 1;
                let new_nav_date = this.detail.nav_date;
                this.detail.new_nav_date = tools.ftime(new_nav_date.substr(0, 8));
                this.detail.nav_date = '(' + tools.ftime(this.detail.nav_date.substr(4, 8)) + ')'; //净值日期
                this.detail.dividend_amt = tools.changeTwoDecimal_f(this.detail.dividend_amt);    //分红到宝
                this.detail.estimated_opening_start = this.detail.estimated_opening_start.substr(0, 8);//预计下一开放日
                this.detail.hold_rate = tools.changeTwoDecimal_f(this.detail.hold_rate * 100);  //持仓收益率
                this.detail.defdividend_method_remark = this.detail.defdividend_method ? this.defdividend_method_obj[this.detail.defdividend_method] : '请选择分红方式'//分红到宝描述
                this.detail.selling_tips = this.detail.selling_tips;//卖出提示
                // console.log(this.detail.defdividend_method_remark,111)
                if (this.detail.dividend_status && this.detail.dividend_status == '01') this.detail.defdividend_method_remark = this.detail.defdividend_method_remark + '(修改中)'
                this.getBuyState()  //productInfo
                this.getInvestmentNum();
                //获取费率
                this.getFcitemRate(this.detail.available_vol);
                if (this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length > 1) {
                    this.dealSwiper(this.prodMsg);
                }
            },
            //渲染完成后
            mounted() {
                var appletEnterImg = require("gconfig").global.oss_url + $(_pageId + " #applet_enter").html();
                $(_pageId + " #applet_enter_img").attr("src", appletEnterImg);
                
            },
            //计算属性
            computed: {
                timeResult: () => {
                    return (time, num) => {
                        if (!time) return '--'
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
            },
            //绑定事件
            methods: {
                //陪伴服务 文章列表
                escortService(){
                    tools.recordEventData('1','escortService','陪伴服务');
                    this.detail.fund_code
                    let escortService_data = {
                        prod_id:this.detail.fund_code,
                        location:'2'
                    }
                    appUtils.setSStorageInfo("escortService_data", escortService_data);
                    appUtils.pageInit(_pageCode, "template/escortService");
                },
                //费率查询
                getFcitemRate(available_vol) {
                    service.reqFun102172({ fund_code: this.detail.fund_code },  (data)=> {
                        if (data.error_no == 0) {
                            redemptionRate = data.results;
                            this.calculateCharges(available_vol);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                /**
                     *  计算赎回费用
                     * 预估到账金额 = 卖出份额 * 单位净值 - 赎回费
                     * 预估赎回费用 = 赎回金额*赎回费率=卖出份额*单位净值*赎回费率
                */
                calculateCharges(available_vol) {
                    var _available_vol = +available_vol.replace(/,/g, "")
                    if (this.detail.nav) { // 计算到账金额
                        // 计算分阶段赎回费用
                        $(_pageId + " .thirty_tips").show();
                        var redemTotalAccount = 0; // 预估赎回费用
                    if (redemptionRate && redemptionRate instanceof Array && redemptionRate.length) {
                        // $(_pageId + " .upwards540 span").text(redemptionRate[redemptionRate.length - 1].hold_vol + "份")
                        let showData = redemptionRate[redemptionRate.length - 1];
                        //渲染金额，天数
                        $(_pageId + " .upwards540 .hold_vol").text(showData.hold_vol + '份');
                        $(_pageId + " .upwards540 .fcitem_lval").text(showData.fcitem_lval);
                        $(_pageId + " .under540 .fcitem_lval").text(showData.fcitem_lval);
                        //判断是否有540天的持仓份额 展示remark
                        if((showData.fcitem_lval*1) >= '540' && (showData.hold_vol*1) >0){
                            $(_pageId + " .under540").hide();
                            $(_pageId + " .upwards540").show();
                        }else{
                            $(_pageId + " .upwards540").hide();
                            $(_pageId + " .under540").show();
                        }
                        for (var i = redemptionRate.length - 1; i >= 0; i--) {
                            var diff_val = common.floatSub(_available_vol, redemptionRate[i].hold_vol);
                            if (parseFloat(diff_val) > 0) {
                                redemptionRate[i]['ava_vol'] = tools.fmoney(redemptionRate[i].hold_vol); // 可赎回份额
                                var redemAccount = common.floatMultiply(common.floatMultiply(this.detail.nav, +redemptionRate[i].hold_vol.replace(/,/g, "")), common.floatDivide(redemptionRate[i].discount_rate, 100)) // 预计赎回费用
                                redemptionRate[i]['redem_account'] = redemAccount; // 区间赎回费用
                                _available_vol = diff_val;
                            } else {
                                redemptionRate[i]['ava_vol'] = tools.fmoney(_available_vol); // 可赎回份额
                                var redemAccount = common.floatMultiply(common.floatMultiply(this.detail.nav, _available_vol), common.floatDivide(redemptionRate[i].discount_rate, 100))
                                redemptionRate[i]['redem_account'] = redemAccount; // 区间赎回费用
                                _available_vol = "0";
                            }
                        redemTotalAccount = common.floatAdd(redemTotalAccount, redemptionRate[i]['redem_account'])
                    }
                    var redeemRateStr = "";
                    isShowRedeemRateStr = true
                    redeemRateStr += '' +
                    '<h1 class="m_center m_bold">温馨提示</h1><div class="highFinancialRateInfo_top m_bold"><p class="m_text_center" style="width:100%;text-align:center">现在卖出，预估赎回费<span class="m_text_red">'+ tools.fmoney(redemTotalAccount) +'</span>元</p></div>';
                            for (var i = 0; i < redemptionRate.length; i++) {
                                var datestr = "<span>" + redemptionRate[i].ava_vol + "份</span>";
                                var fcitem_lval = redemptionRate[i].fcitem_lval; //最小
                                var fcitem_lvunit = redemptionRate[i].fcitem_lvunit;//最小单位
                                var fcitem_tval = redemptionRate[i].fcitem_tval;//最大
                                var fcitem_tvunit = redemptionRate[i].fcitem_tvunit;//最大单位
                                if (fcitem_tval == "-1") { //最大
                                    datestr += "<span>持有" + fcitem_lval + fcitem_lvunit + "以上</span>";
                                } else if (fcitem_lval == "0") { //最小
                                    datestr += "<span>持有不足" + fcitem_tval + fcitem_tvunit + "</span>";
                                } else {
                                    datestr += "<span>持有" + fcitem_lval + "-" + fcitem_tval + fcitem_tvunit + "</span>";
                                }
                                redeemRateStr += `<div class="g_fontSize14">
                                <p class="estimate_rate">${datestr}</p>
                                <p class="rate_detail"><span><span class="m_text_red">${tools.fmoney(redemptionRate[i].redem_account)}</span>元</span><span class="rate_item">费率${redemptionRate[i].discount_rate}${redemptionRate[i].chgrate_unit}</span></p>
                                </div>`;
                            }
                            redeemRateStr += "";
                        } else {
                            isShowRedeemRateStr = false
                            redeemRateStr = '<div class="m_center">暂无数据</div>'
                        }
                        $(_pageId + " #redemptionFee").html(tools.fmoney(redemTotalAccount));
                        $(_pageId + " #redemptionFee").attr("data-money", redemTotalAccount);
                        $(_pageId + " #hasRateAccount").html(tools.fmoney(common.floatSub(common.floatMultiply(this.detail.nav, +available_vol.replace(/,/g, "")), redemTotalAccount)));
                        $(_pageId + " .pop_box2 .content_box .highFinancialRateInfo").html(redeemRateStr);
                    }
                },
                //获取定投数量
                getInvestmentNum() {
                    let userInfo = ut.getUserInf()
                    let data = {
                        custno: userInfo.custNo,
                        fundcode: productInfo.fund_code,
                        virfundcode: productInfo.vir_fundcode,
                    }
                    service.reqFun106041(data, (res) => {
                        if (res.error_no == '0') {
                            let results = res.results[0]
                            this.investmentNum = results.amount + '个'
                            // console.log(results)
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                // //点击定投
                fixedInvestment() {
                    tools.recordEventData('1','fixedInvestment','定投');
                    //跳转新增定投页面
                    appUtils.setSStorageInfo("fund_code", this.detail.fund_code);
                    if (!common.loginInter(_pageCode)) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode)
                    common.changeCardInter(() => {
                        appUtils.setSStorageInfo("isAdvisoryInvestment", '');
                        appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
                    });
                    // common.changeCardInter(function () {
                    //     if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    //         layerUtils.iConfirm("您还未进行风险测评", function () {
                    //             appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                    //         }, function () {
                    //         }, "去测评", "取消");
                    //         return;
                    //     }
                    //     appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
                    // });
                    // appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment");
                },
                //获取产品详情
                async getDetails() {
                    return new Promise(async (resolve) => {
                        let data = {
                            fund_code: productInfo.fund_code,
                            if_period: productInfo.if_period || "",
                            vir_fundcode: productInfo.vir_fundcode || "",
                            due_date: productInfo.due_date || "",
                            respect_income: productInfo.respect_income || "",
                        }
                        // if(publicHoldHeightDetail.due_date) data.due_date = publicHoldHeightDetail.due_date
                        service.reqFun101934(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                dealSwiper(data) {
                    swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                        pagination: '.swiper-pagination',
                        autoplay: false,
                        paginationElement: "li",
                        bulletActiveClass: "check",
                        autoplayDisableOnInteraction: false,
                        // observer:true, // 启动动态检查器(OB/观众/观看者)
                        observeParents:true, // 修改swiper的父元素时，自动初始化swiper
                        // loop: true,
                        // onClick: function (swiper, event) {
                        //     // console.log(this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length);
                        //     if (data && data instanceof Array && data.length) {
                        //         var param = data[swiper.realIndex];
                        //         appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);
                        //     }
                        // },
                        beforeDestroy: function () {
                            swipeInstance = null
                        }
                    });
                    if (data.length > 1) {
                        $(".swiper_scrollbar .swiper-pagination li").css({ "width": (1 / data.length) * 100 + "%" });
                    }
                },
                //获取产品购买状态
                getBuyState() {
                    let new_data = {
                        fund_code: this.detail.fund_code,
                        due_date: this.detail.due_date
                    }
                    service.reqFun102049(new_data, (data) => {
                        this.button_flag = true;
                        if (data.error_no != "0") {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        let results = data.results[0];
                        this.flag_data = results
                        if (!results || results.length == 0) {
                            this.buy_flag = this.sell_flag = "no_active"
                            return;
                        }
                        //海外产品节假日判断
                        let not_workday = ``;
                        if(results.not_workday_start && results.not_workday_start.length){
                            not_workday = `<div class="not-workday">根据基金公司公告，${tools.FormatDateText(results.not_workday_start)}${(results.not_workday_end&&results.not_workday_end.length)?`~${results.not_workday_end.substr(6,2)*1}日`:''}为境外证券交易所非交易日，涉及境外投资的基金产品暂停交易，请知悉</div>`;
                        }
                        console.log(not_workday)
                        $(_pageId + " #main_publicHoldHeightDetail").append(not_workday)
                        //购买状态
                        let buy_state = results.buy_state;
                        //卖出状态 0:不可卖出 1:可卖出
                        this.sold_state = results.sold_state;

                        if (buy_state == 3 || buy_state == 4 || buy_state == 6) this.buy_flag = "no_active"
                        //due_redeem 是否支持手动赎回
                        //redeemable_vol 可赎回份额
                        //prod_open_type 1定开 2日开
                        if (this.sold_state == 0 || (this.detail.due_redeem == "0" && this.detail.prod_open_type == "1") || (this.detail.available_vol <= 0 && this.detail.prod_open_type == "2")) this.sell_flag = "no_active"
                    });
                },
                //买入
                buy() {
                    tools.recordEventData('1','buy','买入');
                    if (this.buy_flag) return;
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode)
                    common.changeCardInter(() => {
                        appUtils.setSStorageInfo("fund_code", this.detail.fund_code);
                        appUtils.setSStorageInfo("productInfo", this.detail);
                        appUtils.pageInit(_pageCode, "template/templateBuy");
                    });
                },
                //卖出
                sell() {
                    tools.recordEventData('1','sell','卖出');
                    //  是否可手动赎回
                    //  可赎回份额 > 0 日开
                    if (this.sell_flag && !isProduct540) return;
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                    if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode);
                    //540天特殊处理
                    if(this.sell_flag && isProduct540 && this.detail.available_vol && (this.detail.available_vol*1 > 0)){
                        return $(_pageId + " .pop_box2").show()
                    }
                    if(this.sell_flag) return;
                    if (this.detail.available_vol && this.detail.available_vol == 0) {
                        let operationId = 'submitRedeem'
                        layerUtils.iConfirm("已提交赎回申请，请勿重复卖出", function () {
                        }, function () {
                            appUtils.setSStorageInfo("series_id",'');
                            appUtils.pageInit(_pageCode, "template/transaction");
                        }, "确定", "查看交易记录",operationId);
                        return;
                    }
                    if (this.detail.selling_tips) {
                        let selling_tip = this.detail.selling_tips;
                        common.changeCardInter(() => {
                            let operationId = 'continueSell'
                            layerUtils.iConfirm(selling_tip, () => {
                            }, () => {
                                appUtils.setSStorageInfo("fund_code", this.detail.fund_code);
                                appUtils.setSStorageInfo("holdObj", { ...productInfo, ...this.detail });
                                appUtils.pageInit(_pageCode, "template/templateSale");
                            }, "取消", "继续卖出",operationId);

                        });
                    } else {
                        common.changeCardInter(() => {
                            appUtils.setSStorageInfo("fund_code", this.detail.fund_code);
                            appUtils.setSStorageInfo("holdObj", { ...productInfo, ...this.detail });
                            appUtils.pageInit(_pageCode, "template/templateSale");
                        });
                    }
                },
                //跳转定投计划页面
                investmentPlan() {
                    tools.recordEventData('1','investmentPlan','定投计划');
                    appUtils.setSStorageInfo("singlePlan", '1');
                    appUtils.setSStorageInfo("fixed_investment_list", this.detail.fixed_investment_list);
                    // console.log(this.detail.fixed_investment_list)
                    let param = {
                        fixed_investment_list: this.detail.fixed_investment_list
                    }
                    appUtils.setSStorageInfo("isAdvisoryInvestment", '');
                    appUtils.pageInit(_pageCode, "fixedInvestment/investmentList", param);
                },
                //持有期收益率
                holdYield() {
                    tools.recordEventData('1','holdYield','持有期收益率');
                    appUtils.setSStorageInfo("productInfo", this.detail);
                    appUtils.pageInit(_pageCode, "inclusive/holdingPeriod");
                },
                //90天锁定份额
                lockList() {
                    tools.recordEventData('1','lockList','锁定份额');
                    appUtils.setSStorageInfo("holdObj", this.detail);
                    appUtils.pageInit(_pageCode, "inclusive/lockingList");
                },
                //最新收益弹窗
                latestRevenue() {
                    tools.recordEventData('1','latestRevenue','最新收益');
                    let str = `
                        <ul class="newPopup p10">
                           <li>最新收益（元）</li>
                           <li>${this.detail.new_nav_date}</li>
                           <li class="m_text_red m_font_size30">${this.detail.last_income}</li>
                           <li class="m_text_999">每个交易日20点开始更新</li>
                        </ul>
                    `
                    layerUtils.iAlert(str);
                    $('.newPopup').prev('p').css("padding", "0")
                },
                //持仓收益弹窗
                positionReturn() {
                    tools.recordEventData('1','positionReturn','持仓收益');
                    let str = `
                        <ul class="newPopup p10">
                           <li>持仓收益（元）</li>
                           <li>${this.detail.new_nav_date}</li>
                           <li class="m_text_red m_font_size30">${this.detail.hold_income}</li>
                           <li class="m_text_999">持仓收益=（最新净值-成本单价）*持有份额</li>
                        </ul>
                    `
                    layerUtils.iAlert(str);
                    $('.newPopup').prev('p').css("padding", "0")
                },
                //持仓收益率弹窗
                returnonHoldings() {
                    tools.recordEventData('1','returnonHoldings','持仓收益率');
                    let str = `
                        <ul class="newPopup p10">
                           <li>持仓收益率</li>
                           <li>${this.detail.new_nav_date}</li>
                           <li class="m_text_red m_font_size30">${this.detail.hold_rate + "%"}</li>
                           <li class="m_text_999">持仓收益率=持仓收益/持仓成本*100%</li>
                           <li class="m_text_999">持仓成本=成本单价*持有份额</li>
                        </ul>
                    `
                    layerUtils.iAlert(str);
                    $('.newPopup').prev('p').css("padding", "0")
                },
                //跳转产品详情
                PageToProductDetails() {
                    tools.recordEventData('1','PageToProductDetails','产品详情');
                    // appUtils.setSStorageInfo("productInfo", this.detail);
                    sessionStorage.vip_buttonShow = true;
                    appUtils.pageInit(_pageCode, "template/publicOfferingDetail");
                },
                //查看交易记录
                seeTransactionRecords() {
                    tools.recordEventData('1','seeTransactionRecords','交易记录');
                    appUtils.setSStorageInfo("series_id",'');
                    appUtils.setSStorageInfo("trsFundCode", this.detail.fund_code);
                    appUtils.pageInit(_pageCode, "template/transaction");
                },
                // 查看资讯
                readProdMsg(event) {
                    // console.log(event,111)
                    tools.recordEventData('1','readProdMsg','查看资讯');
                    if (this.prodMsg && this.prodMsg instanceof Array && this.prodMsg.length && this.prodMsg.length <= 1) {
                        var param = this.prodMsg[0];
                        appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);
                    }
                },
                // 改版查看资讯
                seeProdMsg(item){
                    tools.recordEventData('1','readProdMsg','查看资讯',{fundMsgId:item.id});
                    if(item && item.html_content) item.html_content = encodeURIComponent(item.html_content);
                    appUtils.pageInit(_pageCode, "template/prodMsgDetail", item);
                },
                //满7天提示
                thirtyTip() {
                    tools.recordEventData('1','thirtyTip','满7天提示');
                    layerUtils.iAlert("持有满" + this.detail.fund_days_customized + "天，赎回免手续费");
                },
                //满540天提示
                thirtyTip_540() {
                    tools.recordEventData('1','thirtyTip_540','满540天提示');
                    layerUtils.iAlert("建议持有满" + this.detail.fund_days_customized + "天以上");
                },
                //修改分红方式
                seeBonus() {
                    tools.recordEventData('1','seeBonus','修改分红方式');
                    let bonus_if_alter = this.detail.bonus_if_alter;
                    let dividend_status = this.detail.dividend_status;
                    //0 不可更改  1可能改
                    if (bonus_if_alter == "0") {
                        layerUtils.iAlert("该产品不能修改分红方式");
                        return;
                    }
                    if (dividend_status == "01") {
                        layerUtils.iAlert("分红方式确认前将不能再次修改");
                        return;
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
                    appUtils.setSStorageInfo("holdObj", this.detail);
                    common.changeCardInter(function () {
                        appUtils.pageInit(_pageCode, "thfund/modifyDividendsWay");
                    });
                },
                //修改到期方式
                seeMaturity() {
                    tools.recordEventData('1','seeMaturity','修改到期方式');
                    let back_way_alter = this.detail.back_way_alter;
                    //0 不可更改  1可能改
                    if (back_way_alter == "0") {
                        layerUtils.iAlert("该产品不能修改到期方式");
                        return;
                    }
                    //当前日期是否可以修改到期方式  0不可更改 1可更改
                    if (this.detail.special_back_way_alter == "0") {
                        layerUtils.iAlert("当前时间不可修改到期方式");
                        return;
                    }
                    let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
                    if (invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
                    appUtils.setSStorageInfo("holdObj", this.detail);
                    common.changeCardInter(function () {
                        appUtils.pageInit(_pageCode, "thfund/modifyExpireWay");
                    });
                },
                // 查看收益明细
                seeReturnsDetailed() {
                    tools.recordEventData('1','seeReturnsDetailed','查看收益明细');
                    appUtils.setSStorageInfo("trsFundCode", this.detail.fund_code);
                    appUtils.pageInit(_pageCode, "template/returnsDetailed");
                },
                appletEnter(url) {
                    tools.recordEventData('1','appletEnter','跳转小程序');
                    tools.jump_applet(url);
                    return;
                }
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //bannear 点击链接
        // appUtils.preBindEvent($(_pageId + " #scroller_index3"), ".msg_list", function (e) {
        //     // e.stopPropagation();
        //     // e.preventDefault();
        //     var msgInfo = JSON.parse($(this).find(".msgInfo").text());
        //     var param = msgInfo;
        //     tools.recordEventData('1','msg_list','产品资讯');
        //     appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);

        // }, 'click');
        appUtils.bindEvent($(_pageId + " .pop_box2 #queDing"), function () {
            $(_pageId + " .pop_box2").hide();
        });
    }
    //页面销毁
    function destroy() {
        swipeInstance = null;
        tools.recordEventData('4','destroy','页面销毁');
        isProduct540 = false;
        $(_pageId + " #scroller_index3").html("");
        $(".main_publicHoldHeightDetail").html("")
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    let publicHoldHeightDetaillModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = publicHoldHeightDetaillModule;
});