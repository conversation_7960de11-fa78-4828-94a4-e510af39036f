// 晋金高端概况
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        layerUtils = require("layerUtils"),
        _pageId = "#yuanhui_tradeRule ";
    var productInfo;
    var productRate;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        productRate = appUtils.getSStorageInfo("productRate");
        $(_pageId + " .prodName").html(productInfo.prod_name);
        $(_pageId + " .fund_code").html(productInfo.fund_code);
        $(_pageId + " .inrest_term").html(tools.ftime(productInfo.inrest_term));//封闭期
        if (productInfo.threshold_amount && Number(productInfo.threshold_amount) > 10000) {
            $(_pageId + " .thresholdAmount").html(productInfo.threshold_amount ? Number(productInfo.threshold_amount / 10000) + "万元" : "--");//起购金额
        } else {
            $(_pageId + " .thresholdAmount").html(productInfo.threshold_amount ? productInfo.threshold_amount + "元" : "--");//起购金额
        }
        if (productInfo.addition_amt && Number(productInfo.addition_amt) > 10000) {
            $(_pageId + " .addition_amt").html(productInfo.addition_amt ? Number(productInfo.addition_amt / 10000) + "万元" : "--");//追加金额
        } else {
            $(_pageId + " .addition_amt").html(productInfo.addition_amt ? productInfo.addition_amt + "元" : "--");//起购金额
        }
        $(_pageId + " .yh_open_date_desc").html(productInfo.yh_open_date_desc);//开放日期说明
        $(_pageId + " .yh_p_reward_accrual_rate").html(productInfo.yh_p_reward_accrual_rate);//业绩报酬计提率
        $(_pageId + " .yh_p_reward_accrual_date").html(tools.ftime(productInfo.yh_p_reward_accrual_date));//业绩报酬计提日
        $(_pageId + " .yh_p_reward_accrual_form").html(productInfo.yh_p_reward_accrual_form);//业绩报酬计提形式
        $(_pageId + " .yh_p_reward_accrual_condition").html(productInfo.yh_p_reward_accrual_condition);//业绩报酬计条件
        $(_pageId + " .yh_p_reward_add_condition").html(productInfo.yh_p_reward_add_condition);//业绩报酬附加条件
        getRateInfo();
        reqFun102047();
    }

    //费率
    function getRateInfo() {
        var businesscode = productInfo.businesscode; // 22申购 20认购
        var purchaseRateStr = "";
        var operateRateStr = "";
        var redeemRateStr = "";
        if (productRate.purchaseRate && productRate.purchaseRate.length > 0 && businesscode == "22") {
            if (productRate.purchaseRate.length == 1 && (productRate.purchaseRate[0].chgrate_tval == 0 || productRate.purchaseRate[0].fcitem_lval == 0)) {
                var rateStr = "";
                var discount_ratevalue = productRate.purchaseRate[0].discount_ratevalue;
                if (discount_ratevalue) {
                    rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + productRate.purchaseRate[0].chgrate_tval + productRate.purchaseRate[0].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + productRate.purchaseRate[0].chgrate_unit;
                } else {
                    rateStr = productRate.purchaseRate[0].chgrate_tval + productRate.purchaseRate[0].chgrate_unit;
                }
                purchaseRateStr +=
                        '<div class="highFinancialInfo-item"><p class="item-left ">' + productRate.purchaseRate[0].chgrate_item_desc + '</p><p class="item-left g_fontSize14 text_darkgray">' + rateStr + '</p></div>';
            } else {
                purchaseRateStr += '<div class="highFinancialRateInfo">' +
                    '<h1 style="margin-top: 0.1rem;color: #282828;">' + productRate.purchaseRate[0].chgrate_item_desc + '</h1><div><p>适用金额</p>' +
                    '<p>' + productRate.purchaseRate[0].chgrate_item_desc + '</p></div>';
                for (var i = 0; i < productRate.purchaseRate.length; i++) {
                    var fcitem_tval = productRate.purchaseRate[i].fcitem_tval; //最大
                    var fcitem_lval = productRate.purchaseRate[i].fcitem_lval; //最小
                    var discount_ratevalue = productRate.purchaseRate[i].discount_ratevalue;
                    var purchaseStr = "";
                    if (fcitem_lval == 0) { //最小为0
                        purchaseStr += fcitem_tval / 10000 + '万元以下';
                    } else if (fcitem_tval == "-1") { //最大
                        purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                    } else {
                        purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                    }
                    var rateStr = "";
                    if (discount_ratevalue) {
                        rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + productRate.purchaseRate[i].chgrate_tval + productRate.purchaseRate[i].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + productRate.purchaseRate[i].chgrate_unit;
                    } else {
                        rateStr = productRate.purchaseRate[i].chgrate_tval + productRate.purchaseRate[i].chgrate_unit;
                    }
                    purchaseRateStr += '<div>' +
                        '<p>' + purchaseStr + '</p>' +
                        '<p>' + rateStr + '</p>' +
                        '</div>';
                }
            }
            purchaseRateStr += "</div>"
        } else if (productRate.subscription && productRate.subscription.length > 0 && businesscode == "20") {
            if (productRate.subscription.length == 1 && (productRate.subscription[0].chgrate_tval == 0 || productRate.subscription[0].fcitem_lval == 0)) {
                var rateStr = "";
                var discount_ratevalue = productRate.subscription[0].discount_ratevalue;
                if (discount_ratevalue) {
                    rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + productRate.subscription[0].chgrate_tval + productRate.subscription[0].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + productRate.purchaseRate[0].chgrate_unit;
                } else {
                    rateStr = productRate.subscription[0].chgrate_tval + productRate.subscription[0].chgrate_unit;
                }
                purchaseRateStr +=
                        '<div class="highFinancialInfo-item"><p class="item-left ">' + productRate.subscription[0].chgrate_item_desc + '</p><p  class="item-left g_fontSize14 text_darkgray">' + rateStr + '</p></div>';
            } else {
                purchaseRateStr += '<div class="highFinancialRateInfo">' +
                    '<h1 style="margin-top: 0.1rem;color: #282828;">' + productRate.subscription[0].chgrate_item_desc + '</h1><div><p>适用金额</p>' +
                    '<p>' + productRate.subscription[0].chgrate_item_desc + '</p></div>';
                for (var i = 0; i < productRate.subscription.length; i++) {
                    var fcitem_tval = productRate.subscription[i].fcitem_tval; //最大
                    var fcitem_lval = productRate.subscription[i].fcitem_lval; //最小
                    var discount_ratevalue = productRate.subscription[i].discount_ratevalue;
                    var purchaseStr = "";
                    if (fcitem_lval == 0) { //最小为0
                        purchaseStr += fcitem_tval / 10000 + '万元以下';
                    } else if (fcitem_tval == "-1") { //最大
                        purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                    } else {
                        purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                    }
                    var rateStr = "";
                    if (discount_ratevalue) {
                        rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + productRate.subscription[i].chgrate_tval + productRate.subscription[i].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + productRate.subscription[i].chgrate_unit;
                    } else {
                        rateStr = productRate.subscription[i].chgrate_tval + productRate.subscription[i].chgrate_unit;
                    }
                    purchaseRateStr += '<div>' +
                        '<p>' + purchaseStr + '</p>' +
                        '<p>' + rateStr + '</p>' +
                        '</div>';
                }
            }
            purchaseRateStr += "</div>"
        } else {
             purchaseRateStr += "<div class='highFinancialInfo-item'><p class='item-left '>买入费率</p>" +
                 "<p class='item-left g_fontSize14 text_darkgray'>无</p>"
        }
        if (productRate.redeemRate && productRate.redeemRate.length > 0) {
            redeemRateStr += '<div class="highFinancialRateInfo">' +
                '<h1 style="margin-top: 0.1rem;color: #282828;">' + productRate.redeemRate[0].chgrate_item_desc + '</h1><div><p>适用期限</p>' +
                '<p>'  + productRate.redeemRate[0].chgrate_item_desc +  '</p></div>';

            for (var i = 0; i < productRate.redeemRate.length; i++) {
                var datestr = "";
                var fcitem_lval = productRate.redeemRate[i].fcitem_lval; //最小
                var fcitem_lvunit = productRate.redeemRate[i].fcitem_lvunit;//最小单位
                var fcitem_tval = productRate.redeemRate[i].fcitem_tval;//最大
                var fcitem_tvunit = productRate.redeemRate[i].fcitem_tvunit;//最大单位
                if (fcitem_tval == "-1") { //最大
                    datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                } else if (fcitem_lval == "0") { //最小
                    datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                } else {
                    datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                }
                redeemRateStr += '<div>' +
                    '<p>' + datestr + '</p>' +
                    '<p>' + productRate.redeemRate[i].chgrate_tval + productRate.redeemRate[i].chgrate_unit + '</p>' +
                    '</div>';
            }
            redeemRateStr += "</div>";
        } else {
            redeemRateStr += "<div class='highFinancialInfo-item'><p class='item-left '>赎回费率</p>" +
                "<p class='item-left g_fontSize14 shfl'>--</p>"
        }
        $(_pageId + " .buy_item_box").html(purchaseRateStr + operateRateStr);
        $(_pageId + " .sell_item_box").html(redeemRateStr);

        for (var i = 0; i < productRate.operateRate.length; i++) {
            if (productRate.operateRate[i].chgrate_type == '11') {
                $(_pageId + " .glf").text(tools.fmoney(productRate.operateRate[i].chgrate_tval,4) + productRate.operateRate[i].chgrate_unit)
            }
            if (productRate.operateRate[i].chgrate_type == '10') {
                $(_pageId + " .fwf").text(tools.fmoney(productRate.operateRate[i].chgrate_tval,4) + productRate.operateRate[i].chgrate_unit)
            }
            if (productRate.operateRate[i].chgrate_type == '12') {
                $(_pageId + " .tgf").text(tools.fmoney(productRate.operateRate[i].chgrate_tval,4) + productRate.operateRate[i].chgrate_unit)
            }
        }
    }

    //查询产品赎回提示
    function reqFun102047() {
        var productInfo = appUtils.getSStorageInfo("productInfo");
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102047(param, function (data) {
            if (data.error_no == 0) {
                if (!data.results || data.results.length == 0) {
                    return;
                }
                var results = data.results[0];
                var redeem_confirm = results.redeem_confirm;
                var redeem_desc = results.redeem_desc;
                if (redeem_desc) {
                    $(_pageId + " .redeem_desc").html("T+" + redeem_desc);
                    $(_pageId + " .redeem_confirm").html("T+" + redeem_confirm);
                } else {
                    $(_pageId + " .redeem_desc").html("--");
                    $(_pageId + " .redeem_confirm").html("--");
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }


    function destroy() {
        $(_pageId + " .prodName").text("");
        $(_pageId + " .fund_code").text("");
        $(_pageId + " .establishDate").text("");
        $(_pageId + " .riskDesc").text("");
        $(_pageId + " .issuingScale").text("");
        $(_pageId + " .mgrcompName").text("");
        $(_pageId + " .mgrcompSname").text("");
        $(_pageId + " .trusteeName").text("");
        $(_pageId + " .fundManagers").text("");
        $(_pageId + " .empt").text("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thsurvey = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thsurvey;
});
