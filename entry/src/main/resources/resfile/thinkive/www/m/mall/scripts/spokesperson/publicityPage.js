// 代言人宣传页
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        ut = require("../common/userUtil"),
        _pageCode = "spokesperson/publicityPage",
        _pageId = "#spokesperson_publicityPage ";
    var global = gconfig.global;
    var effectiveInfo; // 代言人信息
    function init() {
        // 获取代言人申请状态
        getApplyStatus();
    }

    function getApplyStatus() {
        service.reqFun113001({}, (data) => {
            if (data.error_no == '0') {
                effectiveInfo = data.results[0];
                $(_pageId + " .bg_img").attr("src", global.oss_url + effectiveInfo.oss_url)
                $(_pageId + " #apply_state").attr("data-status", effectiveInfo.audit_status)
                if (effectiveInfo.audit_status == '0' && effectiveInfo.effective_status != 1) { // 未审核
                    $(_pageId + " #apply_state span").html("审核中");
                } else if (effectiveInfo.audit_status == '1') { // 审核通过
                    $(_pageId + " #apply_state span").html("已通过审核");
                } else if (effectiveInfo.audit_status == '2') { // 审核驳回
                    $(_pageId + " #apply_state span").html("申请成为代言人");
                } else {
                    $(_pageId + " #apply_state span").html("申请成为代言人");
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 申请成为代言人
        appUtils.bindEvent($(_pageId + " #apply_state"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return; // 未绑卡
            if (effectiveInfo.effective_status == "1") {  // 已经是代言人
                layerUtils.iAlert(`您已成为代言人`);
                return;
            } else if (effectiveInfo.effective_status == "0") { // 失效代言人
                layerUtils.iAlert(`您已退出代言人团队，如需重新申请，请联系${require("gconfig").global.custServiceTel}`);
                return;
            } else if (effectiveInfo.effective_status == "") { // 还不是代言人
                if (effectiveInfo.audit_status == "" || effectiveInfo.audit_status == "2") { // 申请驳回或暂未申请的情况下，点击进去申请页   
                    appUtils.pageInit(_pageCode, "spokesperson/applicationPage");
                    return;
                }
            }
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //页面销毁
    function destroy() {
        $(_pageId + " #apply_state span").html("申请成为代言人");
        effectiveInfo = "";
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    let publicityPageModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = publicityPageModule;
});
