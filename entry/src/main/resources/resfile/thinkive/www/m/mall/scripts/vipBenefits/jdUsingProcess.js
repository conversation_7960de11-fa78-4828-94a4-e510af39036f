//《京东E卡兑换使用流程说明》
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        _pageId = "#vipBenefits_jdUsingProcess";
    var _pageCode = "vipBenefits/jdUsingProcess";
    var info;
    var previouspage;
    function init() {
        info = appUtils.getPageParam();
        previouspage = appUtils.getSStorageInfo("previouspage");
    }
    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
                appUtils.pageInit(_pageCode, previouspage, info);
        });
    }
    function destroy() {
        info =  null;
        previouspage = null;
        appUtils.clearSStorage("previouspage");
    }
    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
