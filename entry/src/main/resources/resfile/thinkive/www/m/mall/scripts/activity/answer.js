/**
 * 模块名：新理念抢先猜
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"), layerUtils = require("layerUtils"), SHIscroll = require("shIscroll"),
        service = require("mobileService"), gconfig = require("gconfig"), common = require("common"), validatorUtil = require("validatorUtil");
    var external = require("external");
    var ut = require("../common/userUtil");
    var swiper = require("../common/swiper");
    /* 常量 */
    var _pageCode = "activity/answer", _pageId = "#activity_answer";
    /* 变量  活动信息*/
    var state,answer="",shareTemplate="",amount="",flag='0';//状态1:进行中 2:未开始 3:已结束;分享模板
    /**
     * 初始化
     */
    function init() {
        $(_pageId + " .pageone").show().addClass("activity");
        $(_pageId + " .pagetwo").hide()
        reqFun108025();//新理念竞猜答题活动信息接口查询
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), ()=> {
            pageBack();
        });
        // 答题
        appUtils.preBindEvent($(_pageId + " .content .answer_main"), "li", function () {
            // 判断绑卡
            if (!ut.hasBindCard(_pageCode)) return;
            // 1:进行中 2:未开始 3:已结束
            if(state == '2'){
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if(state == '3'){
                layerUtils.iMsg(-1, "该活动已结束，不支持修改答案！", 2);
                return;
            }
            var quizAnswer = $(this).attr('data');
            if( quizAnswer != answer) {
                //新理念竞猜答题接口
                reqFun108026(quizAnswer)
            }
        }, 'click');
        // 规则
        appUtils.preBindEvent($(_pageId + " .content"), ".introduce", function () {
            $(_pageId + " #activityRules").show();
        }, 'click');
        // 规则隐藏
        appUtils.preBindEvent($(_pageId + " #rulesDiv"), ".ruleSure", function () {
            $(_pageId + " #activityRules").hide();
        }, 'click');


    }
    //邀请好友参与
    appUtils.bindEvent($(_pageId + " #share"), function () {
        // 判断绑卡
        if (!ut.hasBindCard(_pageCode)) return;
        $(_pageId + " #pop_layer").show();

    });
    //微信朋友圈
    appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
        if (!shareTemplate) {
            common.share("23", "1");
            return;
        }
        common.share("23", shareTemplate);
    });
    //取消邀请好友
    appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
        $(_pageId + " #pop_layer").hide();
    });
    //新理念竞猜答题活动信息接口查询
    function reqFun108025() {
        service.reqFun108025({}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results[0];
                state = results.state;
                shareTemplate = results.shareTemplate;
                answer = results.answer;
                amount = results.amount;
                $(_pageId + " .content .answer_main .xuanxiang").find('i').removeClass('active');
                $(_pageId + " .content .answer_main .xuanxiang li[data=" + (results.answer ? results.answer : "-") + "]").find('i').addClass('active')
                $(_pageId + " .content .publicity h5 span").html(results.amount ? results.amount : '--');
                $(_pageId + " #rulesDiv .b_con").html(results.introduce ? results.introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>' )
                $(_pageId + " .pageone").removeClass("activity");
            } else {
                layerUtils.iAlert(data.error_info);
                $(_pageId + " #rulesDiv .b_con").html('<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>' )
            }
        })
    }
    function reqFun108026(quizAnswer) {
        service.reqFun108026({"answer":quizAnswer}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results[0];
                $(_pageId + " .content .answer_main .xuanxiang").find('i').removeClass('active');
                $(_pageId + " .content .answer_main .xuanxiang li[data=" + quizAnswer+ "]").find('i').addClass('active')
                if(validatorUtil.isEmpty(answer) && validatorUtil.isNotEmpty(amount)){
                    $(_pageId + " .content .publicity h5 span").html((parseInt(amount) + 1)? (parseInt(amount) + 1):amount);
                }
                answer = quizAnswer;
                layerUtils.iMsg(-1, "提交成功，活动期间可修改！", 2);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " .content .answer_main .xuanxiang").find('i').removeClass('active');
        $(_pageId + " .content .publicity h5 span").html('--');
        $(_pageId + " #rulesDiv .b_con").html("")
        $(_pageId + " .pageone").show().addClass("activity");
        $(_pageId + " .pagetwo").hide();

    };
    /*
     * 返回
     */
    function pageBack() {
        if(flag == '1'){
            $(_pageId + " .pageone").show();
            $(_pageId + " .pagetwo").hide();
            flag = '0';
            return;
        }
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
