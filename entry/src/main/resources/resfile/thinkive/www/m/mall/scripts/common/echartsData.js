/**
 * 折线图初始化数据值
 */
let lineData = {
    title: {
        text: '买入5笔，合计',
        subtext: '340000.00元',
        // x:'left',
        textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
            color: 'rgb(38,38,38)',
            fontSize: 16,
            fontWeight: "normal"
        },
        subtextStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
            color: 'rgb(38,38,38)',
            fontSize: 18,
            fontWeight: "bolder"
        },
    },
    legend: {

    },
    grid: {
        top: '20%',

    },
    // 提示框
    tooltip: {
        trigger: 'axis',
        position: 'top',
        backgroundColor: "rgb(21,94,255)",
        textStyle: {
            color: "#fff" //设置文字颜色
        },
        // formatter: (params)=> {
        //     var result = ''
        //     params.forEach( (item)=> {
        //         item.marker = ''
        //         result += item.axisValue + '卖出最多' + "</br>" + item.marker  + '￥' + item.data
        //     })
        //     return result
        // }

    },
    //工具框，可以选择
    toolbox: {
        feature: {
            // saveAsImage: {} //下载工具
        }
    },
    xAxis: {
        name: '',
        type: 'category',
        axisLine: {
            lineStyle: {
                // 设置x轴颜色
            }
        },
        // 设置X轴数据旋转倾斜
        axisLabel: {
            interval: 1  //设置X轴数据间隔几个显示一个，为0表示都显示
        },
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },

    yAxis: {
        show: true,
        type: 'value',
        scale: false,
        axisLine: {
            show: false,
        },
        axisTick: {
            show: false,
        },
        minorTick: {
            show: false,
        },
        splitLine: {
            show: true,
        },
        axisLabel: {
            formatter: function () {
                return '';
            },
            show: false
        },
        // minInterval: 20,//最小值
        // interval: 20,
        // max: 100,//纵坐标最大值
    },
    series: [
        {
            smooth: true,
            areaStyle: {
                normal: {
                    color: {
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: "rgb(240,248,254)" // 0% 处的颜色
                        }, {
                            offset: 0.7,
                            color: "rgb(240,248,254)" // 100% 处的颜色
                        }],
                        globalCoord: false // 缺省为 false
                    }
                }
            },
            name: '',
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12000],
            type: 'line',
            symbolSize: 8,
            smooth: 0.1,
        },
    ],
}
let superMarketOption = {
    grid: {
        top: "20",
        bottom: "20",
        left: "9%",
        right: "2%"
    },
    tooltip: {
        trigger: 'axis',
        // position: function (pt) {
        //     // console.log(pt);
        //     return [pt[0] - 40, pt[1] - 50]
        // },
        // formatter: '{c}',
        formatter: function (params) {
            return parseFloat(params[0].value) + "";
        },
        valueFormatter: (value) => '$' + parseFloat(value).toFixed(2),
        backgroundColor: "#1170D8",
        padding: 1,
        borderColor: "#1170D8",
        textStyle: {
            color: '#fff'
        }
    },
    toolbox: {
    },
    xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
            show: false,
            // interval: 1000,
            // formatter: (value, index) => { // 第一个参数没有用 所以用_代替
            //     if (index === 0) {
            //         return value
            //     } else if (index === 20) // 或者是 等于 data.lenght-1(前提是数据大于1条)
            //         return `0`;
            // }
            // showMinLabel: true,
            // showMaxLabel: true,
            // align:'right',
            nameTextStyle: {
                // width:200
            },
            overflow: 'none'
        }
    },
    yAxis: {
        type: 'value',
        boundaryGap: [0, '100%'],
        splitLine: {
            show: false
        },
        // splitArea: {
        //     show: true,
        //     interval: 5,
        //     areaStyle: {
        //         color: [
        //             '#FEEEEF', '#FFF8EE', '#E7FCF5'
        //         ]
        //     }
        // }
    },
    series: [
        {
            name: 'Fake Data',
            type: 'line',
            symbol: 'none',
            data: [],
            markArea: {
                silent: true,
                data: [
                    [
                        {
                            // yAxis: 0,
                            itemStyle: {
                                color: "#E7FCF5",
                            }
                        },
                        {
                            yAxis: 0.8
                        }
                    ],
                    [
                        {
                            yAxis: 0.8,
                            itemStyle: {
                                color: "#FDF9ED"
                            }
                        },
                        {
                            yAxis: 0.9
                        }
                    ],
                    [
                        {
                            yAxis: 0.9,
                            itemStyle: {
                                color: "#FEEEEF"
                            }
                        },
                        {
                            // yAxis: 1.4
                        }
                    ]
                ]
            },
            // markPoint: {
            //     data: [
            //         {
            //             symbol: "circle",
            //             symbolSize: 15,
            //             yAxis: 0.4,
            //             x: '95%'
            //         }
            //     ]
            // },
        }
    ]
};
let prodDetailAssetAllocationOption = {
    tooltip: {
        // trigger: 'item',
        // formatter: "{b} : ({d}%)"
        show: false
    },
    title: {
        text: '4.02亿元', // 主标题
        subtext: '净资产',
        itemGap: 8,
        left: "29%",
        y: 'center',
        top: '55',
        textStyle: {
            fontSize: '16',
            color: '#333'
        },
        subtextStyle: {
            fill: "#333",
            fontSize: 14,
            fontWeight: 500
        },
        textAlign: 'center'
    },
    // graphic: {
    //     type: "text",
    //     left: "24%",
    //     top: "85",
    //     style: {
    //         text: "净资产",
    //         textAlign: "center",
    //         fill: "#333",
    //         fontSize: 14,
    //         fontWeight: 500
    //     }
    // },
    color: ["#f47d71", "#86aef9", "#fbac12", "#f88439", "#857bfd"],
    legend: {
        icon: "circle",
        itemHeight: 9,
        orient: "vertical",
        textStyle: { color: "#666", fontSize: 12 },
        right: 20,
        top: 40,
        bottom: 40,
        // formatter: function (params) {
        //     return parseFloat(params[0].value) + "";
        // },
        formatter: '{a|{name}}\n{name}',
    },
    series: [
        {
            name: 'Access From',
            type: 'pie',
            radius: ['65%', '85%'],
            center: ["30%", "50%"],
            avoidLabelOverlap: false,
            // legendHoverLink:false,
            silent: true,
            emphasis: {
                disable: false,
                scale: false,
                scaleSize: 0
            },
            label: {
                show: false,
                position: 'center'
            },
            // emphasis: {
            //     label: {
            //         show: false,
            //         fontSize: '30',
            //         fontWeight: 'bold'
            //     }
            // },
            labelLine: {
                show: false
            },
            data: [
                { value: 0.82, name: '股票' },
                { value: 0.08, name: '固收' },
                { value: 7.83, name: '现金' },
                { value: 0.97, name: '其他' },
                { value: 90.29, name: '基金' }
            ]
        }
    ]
};
let prodDetailNavTrend = {

};
let prodDetailTrajectory = {

}

let combHoldDistOption = {
    tooltip: {
        show: false
    },
    legend: {
        icon: "circle",
        itemHeight: 9,
        orient: "vertical",
        textStyle: { color: "#666", fontSize: 14 },
        top: "middle",
        right: "right",
        right: 28,
        formatter: '{a|{name}}\n{name}',
        selectedMode: false
    },
    color: ['#5470c6', '#ea7ccc', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#91cc75'],
    series: [
        {
            name: 'Access From',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ["28%", "48%"],
            avoidLabelOverlap: false,
            // legendHoverLink:false,
            silent: true,
            emphasis: {
                disable: false,
                scale: false,
                scaleSize: 0
            },
            label: {
                show: false,
                position: 'center'
            },
            labelLine: {
                show: false
            },
            data: []
        }
    ]

}

let combTcAdjuest = {
    tooltip: {
        show: false
    },
    legend: {
        icon: "circle",
        itemHeight: 9,
        orient: "vertical",
        textStyle: { color: "#666", fontSize: 14 },
        right: "center",
        top: "50%",
        bottom: "15",
        formatter: '{a|{name}}\n{name}',
        selectedMode: false
    },
    color: ['#5470c6', '#ea7ccc', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#91cc75'],
    series: [
        {
            name: 'Access From',
            type: 'pie',
            radius: ['30%', '50%'],
            center: ["50%", "25%"],
            avoidLabelOverlap: false,
            // legendHoverLink:false,
            silent: true,
            emphasis: {
                disable: false,
                scale: false,
                scaleSize: 0
            },
            label: {
                show: false,
                position: 'center'
            },
            labelLine: {
                show: false
            },
            data: []
        }
    ]

}
let label = {
    position: 'left', // 设置标签位置为点的左侧
    formatter: '分红', // 设置标签内容为“分红”
    color: '#ffffff', // 设置标签文字颜色为黑色
    padding: [2, 5, 2, 5], // 设置标签内边距
    backgroundColor: '#e5443c', // 设置标签背景颜色为白色
    borderWidth: 2, // 设置标签边框宽度
    borderColor: '', // 设置标签边框颜色为黑色
    borderRadius: 0, // 设置标签边框圆角半径
    fontSize: 10 // 设置标签文字大小
}