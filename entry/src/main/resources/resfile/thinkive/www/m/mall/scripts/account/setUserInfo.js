// 绑定银行卡--输入个人信息
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        pageCode = "account/setUserInfo",
        _pageId = "#account_setUserInfo";

    var saveImgNum = 0;
    var tools = require("../common/tools");
    var ut = require("../common/userUtil");
    var monkeywords = require("../common/moneykeywords");
    require("../../js/prov_city.js");
    require("../../js/city.js");
    require("../../js/picker.min.js");
    let bankAccInfo,userInfo,idCardInfo;
    var first = []; /* 省，直辖市 */
    var second = []; /* 市 */
    var third = []; /* 镇 */
    var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */
    var checked = [0, 0, 0]; /* 已选选项 */
    var picker = ""; //地址选择器
    

    async function init() {
        let registerType = sessionStorage.registerType
        common.systemKeybord(); // 解禁系统键盘
        //获取缓存的上个页面信息
        bankAccInfo = registerType == '1' ? appUtils.getSStorageInfo("idCardInfo") : appUtils.getSStorageInfo("bankAccInfo")
        //本页面的缓存
        userInfo = appUtils.getSStorageInfo("userInfo")
        // console.log(userInfo)
        //清空页面的信息
        if (!appUtils.getSStorageInfo("userInfo")) {
            queryJob({name: "其他", id: "06"});
            queryIncome({money: "10", id: "01", value: "0~10（含）"});
            selectorArea();
            queryBadCredit();
            queryControlPeople();
            querybeneficiary();
        } else {
            $(_pageId + " #cust_address").val(userInfo.cust_address);
            queryJob(userInfo.vocation_code);
            queryIncome(userInfo.year_income);
            selectorArea(userInfo.living_address);
            queryBadCredit(userInfo.bad_credit);
            queryControlPeople(userInfo.actualisself);
            querybeneficiary(userInfo.beneficiary);
        }
        // console.log(ut.getUserInf().custLabelCnlCode)
        let channelCode = ut.getUserInf().custLabelCnlCode //获取当前账号渠道号
        let res = await getShowInvitee()    //获取当前账号是否有邀请人
        if( res.inviterExit == '1') {
            $(_pageId + " #inviterBox").hide()
        }
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        })
        //点击下一步
        appUtils.bindEvent($(_pageId + " #xyb"), function () {
            // var cust_address = $(_pageId + " #cust_address").val();
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议         
            var income_id = $(_pageId + " #income").attr("data-id");
            if (income_id == "04") {
                var income_money = $(_pageId + " #income").val();
            } else {
                var income_money = $(_pageId + " #income").attr("data-money");
            }
            // if ($.trim(cust_address) == "" || $.trim(cust_address) == null) {
            //     layerUtils.iMsg(-1, "地址不能为空");
            //     return;
            // }
            if (classname != "active") {
                layerUtils.iAlert("请确认个人税收居民类型");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #occp").val())) {
                layerUtils.iMsg(-1, "请选择职业");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #income").val())) {
                layerUtils.iMsg(-1, "请选择年收入");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #live_address").val())) {
                layerUtils.iMsg(-1, "请选择地址");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bad_credit").val())) {
                layerUtils.iMsg(-1, "请选择不良诚信记录");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #control_people").val())) {
                layerUtils.iMsg(-1, "请选择账户控制人");
                return;
            }
            if($(_pageId + " #control_people").attr('data-value') == '0'){
                var actualctrlname = $(_pageId + " #actualctrlname").val()
                var actualctrlidcode = $(_pageId + " #actualctrlidcode").val()
                if(!checkNameCard('账户控制人',actualctrlname,actualctrlidcode)){return;};
            }
            if (validatorUtil.isEmpty($(_pageId + " #beneficiary").val())) {
                layerUtils.iMsg(-1, "请选择账户受益人");
                return;
            }
            if($(_pageId + " #beneficiary").attr('data-value') == '0'){
                var beneficiaryname = $(_pageId + " #beneficiaryname").val()
                var beneficiaryno = $(_pageId + " #beneficiaryno").val()
                if(!checkNameCard('账户受益人',beneficiaryname,beneficiaryno)){return;};
            }
            let recommend = $(_pageId + " #inviter").val();
            // layerUtils.iLoading(true);
            // let live_address = {name: $(_pageId + " #live_address").val(),code:$(_pageId + " #live_address").attr("data-code")}
            //缓存之前两个页面参数
            var param = {
                // recommend:bankAccInfo.recommend,
                cust_name: bankAccInfo.cust_name,
                vaild_date:bankAccInfo.vaild_date,
                recommend:recommend,
                cert_no:bankAccInfo.cert_no,
                idCard: bankAccInfo.idCard,
                bank_acct: bankAccInfo.bank_acct,    //用户卡号
                bank_name:bankAccInfo.bank_name,    //银行名称
                bank_reserved_mobile:bankAccInfo.bank_reserved_mobile, //银行预留手机号
                sms_mobile:bankAccInfo.sms_mobile, //手机号
                message_code:bankAccInfo.message_code,//短信验证码(银行)
                sms_code:bankAccInfo.sms_code,//验证码
                pay_type:bankAccInfo.pay_type,
                bank_serial_no:bankAccInfo.bank_serial_no,
                payorg_id:bankAccInfo.payorg_id,
                cert_type:"0",
                sex:bankAccInfo.sex,
                bank_code:bankAccInfo.bank_code,
                cust_address:bankAccInfo.cust_address,
                vocation_code: {name: $(_pageId + " #occp").val(), id: $(_pageId + " #occp").attr("data-value")},
                year_income: {
                    money: income_money,
                    id: $(_pageId + " #income").attr("data-id"),
                    value: $(_pageId + " #income").attr("data-value")
                },
                
                living_address:{name: $(_pageId + " #live_address").val(),code:$(_pageId + " #live_address").attr("data-code")},
                bad_credit: {value: $(_pageId + " #bad_credit").val(), id: $(_pageId + " #bad_credit").attr("data-value")},
                actualisself: {value: $(_pageId + " #control_people").val(), id: $(_pageId + " #control_people").attr("data-value")},
                beneficiary: {value: $(_pageId + " #beneficiary").val(), id: $(_pageId + " #beneficiary").attr("data-value")},
                actualctrlname:actualctrlname ? actualctrlname : '',
                actualctrlidcode:actualctrlidcode ? actualctrlidcode : '',
                beneficiaryname:beneficiaryname ? beneficiaryname : '',
                beneficiaryno:beneficiaryno ? beneficiaryno : '',
                // "living_address_province":(live_address[0]),
                // "living_address_city":(live_address[1]),
                // "living_address_county":(live_address[2])
            }
            // 缓存完成 将用户的部分信息保存到session中
            appUtils.setSStorageInfo("userInfo", param);
            let url = sessionStorage.registerType == 1 ? 'drainage/transactionPwd' : 'account/transactionPwd'
            appUtils.pageInit(pageCode, url);
        });

        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active");
                $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
            } else {
                $(this).find("i").addClass("active");
                $(_pageId + " #next").css({backgroundColor: "#E5433B"});

            }
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId + " #moneyBox"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "account_setUserInfo";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);

        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " .input_box2"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "account_setUserInfo";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
    }
    //校验姓名
    var checkName = function (name) {
        var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/;
        var reg1 = /^[\u4E00-\u9FA5\uf900-\ufa2d.s]{2,20}$/;
        if (reg.test(name) || reg1.test(name)) return true;
        return false;
    }
    function checkNameCard(hint,name,Card) {
        if ($.trim(name) == "" || $.trim(name) == null) {
            layerUtils.iMsg(-1, hint + "姓名不能为空");
            return false;
        }
        if (!checkName($.trim(name))) {
            layerUtils.iMsg(-1, "请输入真实" + hint +"姓名");
            return false;
        }
        if ($.trim(Card).length <= 0) {
            layerUtils.iMsg(-1, hint + "证件号码不能为空");
            return false;
        }
        if (!validatorUtil.isCardID(Card)) {
            layerUtils.iMsg(-1, hint + "证件号码格式错误");
            return  false;
        }
        return true;
    }
    //判断用户是否填写过邀请人
    async function getShowInvitee(){
        return new Promise(async (resolve, reject) => {
            service.reqFun101059({mobile:ut.getUserInf().mobileWhole},  (datas)=> {
                if (datas.error_no == 0) {
                    let results = datas.results[0];
                    resolve(results)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }
    /**
     * 查询职业信息
     * */
    function queryJob(info) {
        var param = {code: 'occupation_jz'}
        if ($(".mobileSelect").length > 0) {
            return;
        }
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                if (info) {
                    $(_pageId + " #occp").val(info.name);
                    $(_pageId + " #occp").attr("data-value", info.id);
                } else {
                    $(_pageId + " #occp").val();
                    $(_pageId + " #occp").attr("data-value", "");
                }
                tools.mobileSelect({
                    trigger: _pageId + " #occp",
                    title: "请选择职业",
                    dataArr: dataArr,
                    callback: function (data) {
                        $(_pageId + " #occp").val(data[0].value);
                        $(_pageId + " #occp").attr("data-value", data[0].id);
                    }
                })
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })
    }
 
    /**
     * 查询年收入信息
     * */
    function queryIncome(info) {
    	 dataArr_income = [
    		 {id: "01", value: "0~10（含）", money: "10"},
             {id: "02", value: "10~50（含）", money: "50"},
             {id: "03", value: "50~100（含）", money: "100"},
             {id: "04", value: "其他", money: ""}
    	 ]
        var position = 0;
        if (info) {
        	if(info.id == "04") {
        		$(_pageId + " #income").val(info.money);
        	} else {
        		$(_pageId + " #income").val(info.value);
        	}
        	$(_pageId + " #income").attr("data-id", info.id).attr("data-value", info.value).attr("data-money", info.money);
        } else {
            $(_pageId + " #income").val();
            $(_pageId + " #income").attr("data-id", "");
        }
        tools.mobileSelect({
            trigger: _pageId + " #income",
            title: "请选择年收入（万元）",
            dataArr: dataArr_income,
            callback: function (data) {
                $(_pageId + " #income").val(data[0].value);
                $(_pageId + " #income").attr("data-id", data[0].id);
                $(_pageId + " #income").attr("data-value", data[0].value);
                $(_pageId + " #income").attr("data-money", data[0].money);
                if (data[0].id == "04") {
                    $(_pageId + " .pop_layer1").show();
                    $(_pageId + " .password_box").show();
                    event.stopPropagation();
                    $(_pageId + " #srje").val('');
            		$(_pageId + " #inputspanid span").html('');
                    //键盘事件
                    moneyboardEvent();
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "account_setUserInfo";
                    param["eleId"] = "srje";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "3";
                    require("external").callMessage(param);
                    var srje = $(_pageId + " #srje").val();
                    $(_pageId + " #income").val(srje);
                    $(_pageId + " #income").attr("data-id", "04").attr("data-money", srje).attr("data-value", dataArr_income[3].value);
                    $(_pageId + " #inputspanid span").html(srje);
                }
            }
        });

    }

    //金额输入数字键盘
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #srje"),
            endcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                    monkeywords.empty();
                } else if(moneys<=0){
                	layerUtils.iAlert("请输入大于零的金额");
                    monkeywords.empty();
                }else {
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .password_box").hide();
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}$/.test(curVal))) {
                    moneys = moneys.substring(0, curVal.length - 1)
                }
                $(_pageId + " #income").val(Number(moneys));
                $(_pageId + " #income").attr("data-id", "04").attr("data-value", dataArr_income[3].value).attr("data-money", moneys);
                $(_pageId + " #inputspanid span").html(Number(moneys));
                $(_pageId + " #srje").val(Number(moneys));
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                    monkeywords.empty();
                } else if(moneys<=0){
                	layerUtils.iAlert("请输入大于零的金额");
                    monkeywords.empty();
                }else {
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .password_box").hide();
                }
            }
        })
    }
    /**
     * 居住地址选择
     */
    function selectorArea(info) {
//        var nameEl = document.getElementById(id);

    	 if (info) {
             $(_pageId + " #live_address").val(info.name);
             $(_pageId + " #live_address").attr("data-code", info.code);
         } 
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            value = text1 + ' ' + text2 + ' ' + text3;
            // $(_pageId + " #live_address").val(value);
            var code1 = first[selectedIndex[0]].code;
            var code2 = second[selectedIndex[1]].code;
            var code3 = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            var code = code1 + ' ' + code2 + ' ' + code3;
            $(_pageId + " #live_address").attr("data-code", code);
            let flag = await tools.is_region(code1,code2,code3,city)
            if(flag){
                $(_pageId + " #live_address").val(value);
            }else{
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }
            
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
    }
    /**
     * 查询不良诚信记录
     * */
    function queryBadCredit(info) {
        dataArr_bad_credit = [
            {id: "0", value: "无"},
            {id: "1", value: "有"},
        ]
        if (info) {
            $(_pageId + " #bad_credit").val(info.value);
            $(_pageId + " #bad_credit").attr("data-value", info.id);
        } else {
            $(_pageId + " #bad_credit").val(dataArr_bad_credit[0].value);
            $(_pageId + " #bad_credit").attr("data-value", dataArr_bad_credit[0].id);
        }
        tools.mobileSelect({
            trigger: _pageId + " #bad_credit",
            title: "请选择不良诚信记录",
            dataArr: dataArr_bad_credit,
            callback: function (data) {
                $(_pageId + " #bad_credit").val(data[0].value);
                $(_pageId + " #bad_credit").attr("data-value", data[0].id);
            }
        })
    }
    /**
     * 选择账户控制人
     * */
    function queryControlPeople(info) {
        dataArr_actualisself = [
            {id: "1", value: "本人"},
            {id: "0", value: "非本人"},
        ]
        if (info) {
            $(_pageId + " #control_people").val(info.value);
            $(_pageId + " #control_people").attr("data-value", info.id);
            if(info.id == '1'){
                $(_pageId + " .actualctrl").hide().find('input').val('');
            }else {
                $(_pageId + " .actualctrl").show();
                $(_pageId + " #actualctrlname").val(userInfo.actualctrlname ? userInfo.actualctrlname : '');
                $(_pageId + " #actualctrlidcode").val(userInfo.actualctrlidcode ? userInfo.actualctrlidcode : '');
            }
        } else {
            $(_pageId + " .actualctrl").hide();
            $(_pageId + " #control_people").val(dataArr_actualisself[0].value);
            $(_pageId + " #control_people").attr("data-value", dataArr_actualisself[0].id);
        }
        // tools.mobileSelect({
        //     trigger: _pageId + " #control_people",
        //     title: "请选择账户控制人",
        //     dataArr: dataArr_actualisself,
        //     callback: function (data) {
        //         $(_pageId + " #control_people").val(data[0].value);
        //         $(_pageId + " #control_people").attr("data-value", data[0].id);
        //         if(data[0].id == '1'){
        //             $(_pageId + " .actualctrl").hide().find('input').val('');
        //         }else {
        //             $(_pageId + " .actualctrl").show()
        //         }
        //     }
        // })
    }
    /**
     * 选择账户受益人
     * */
    function querybeneficiary(info) {
        dataArr_beneficiary = [
            {id: "1", value: "本人"},
            {id: "0", value: "非本人"},
        ]
        if (info) {
            $(_pageId + " #beneficiary").val(info.value);
            $(_pageId + " #beneficiary").attr("data-value", info.id);
            if(info.id == '1'){
                $(_pageId + " .beneficiary").hide().find('input').val('');
            }else {
                $(_pageId + " .beneficiary").show()
                $(_pageId + " #beneficiaryname").val(userInfo.beneficiaryname ? userInfo.beneficiaryname : '');
                $(_pageId + " #beneficiaryno").val(userInfo.beneficiaryno ? userInfo.beneficiaryno : '');
            }
        } else {
            $(_pageId + " .beneficiary").hide();
            $(_pageId + " #beneficiary").val(dataArr_beneficiary[0].value);
            $(_pageId + " #beneficiary").attr("data-value", dataArr_beneficiary[0].id);
        }
        // tools.mobileSelect({
        //     trigger: _pageId + " #beneficiary",
        //     title: "请选择账户受益人",
        //     dataArr: dataArr_beneficiary,
        //     callback: function (data) {
        //         $(_pageId + " #beneficiary").val(data[0].value);
        //         $(_pageId + " #beneficiary").attr("data-value", data[0].id);
        //         if(data[0].id == '1'){
        //             $(_pageId + " .beneficiary").hide().find('input').val('');
        //         }else {
        //             $(_pageId + " .beneficiary").show()
        //         }
        //     }
        // })
    }
    function destroy() {
        saveImgNum = 0;
        $(_pageId + " #inviterBox").show()
        $(_pageId + " .camera_pop_layer").remove();
        $(_pageId + " input").val("");
        $(".mobileSelect").remove();
        $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        $(_pageId + " #srje").val("");
        $(".picker").remove();
        $(_pageId + " #live_address").val("");
        first = [];
        second = [];
        third = [];

    }

    function pageBack() {
        // appUtils.clearSStorage("idCardInfo");
        // appUtils.clearSStorage("bankAccInfo");
        // var routerList = appUtils.getSStorageInfo("routerList");
        // if (routerList.indexOf("account/myAccountNoBind") > -1) {
        //     appUtils.pageBack();
        // } else { //直接注册登陆返回首页
        //     appUtils.pageInit(pageCode, "login/userIndexs", {});
        // }
        appUtils.pageBack();
    }

    var setBankCard = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setBankCard;
});
