// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "highEnd/qualifiedInvestorVerify",
        _pageId = "#highEnd_qualifiedInvestorVerify ";

    function init() {
        $(_pageId + " .custServiceTel").text(require("gconfig").global.custServiceTel)
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #next"), function () {
            // let info = ut.getUserInf()
            // info.invalidFlag = '1'
            // ut.saveUserInf(info);
            pageBack();
        });

    }

    function destroy() {

    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        var routerList = appUtils.getSStorageInfo("routerList");
        routerList.splice(-2);
        appUtils.setSStorageInfo("routerList", routerList)
        appUtils.pageBack();
    }

    var thsurvey = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thsurvey;
});
