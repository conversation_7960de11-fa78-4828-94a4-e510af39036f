// 银行存款列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        common = require("common"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "bank/bankList",
        _pageId = "#bank_bankList ";
    var currentPage;
    var totalPages;
    var page = 1;
    var pay_int_hz;
    var bank_channel_code;
    var ut = require("../common/userUtil");
    var isEnd;
    var sort_field;

    function init() {
        bank_channel_code = appUtils.getPageParam("bank_channel_code");
        pay_int_hz = "0";//默认查全部
        page = 1;
        getProdList(page, false, pay_int_hz);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //产品详情
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".pro_detail", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (!common.loginInter(_page_code)) return;
            if (!ut.hasBindCard(_page_code)) return;
            var userInfo = ut.getUserInf();
            if (productInfo.prod_status && productInfo.prod_status != "2") { //兼容挂单列表
                layerUtils.iAlert("产品已售罄");
                return;
            }

            service.reqFun151110({bank_channel_code: productInfo.bank_channel_code}, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.results[0].bank_flag == 0) { //未开户
                    appUtils.pageInit(_page_code, "bank/faceRecognition");
                    return;
                }
                if ($(_pageId + " #offering").hasClass("current")) {
                    appUtils.pageInit(_page_code, "bank/purchase");
                } else {
                    if (productInfo.cust_no == userInfo.custNo) {
                        layerUtils.iAlert("不能购买自己转让的产品");
                        return;
                    }
                    productInfo["isBuy"] = "0";
                    appUtils.setSStorageInfo("productInfo", productInfo);
                    appUtils.pageInit(_page_code, "bank/transferBuy");
                }
            })
        }, 'click');

        //导航栏
        appUtils.bindEvent($(_pageId + " .tabbar"), function (e) {
            var contentId = $(this).attr("content");
            $(_pageId + ".tabbar").removeClass("active").filter(this).addClass("active");
            if (contentId == "all") {
                pay_int_hz = "0";
                $(_pageId + " .qryDate div").removeClass("active").eq(0).addClass("active");
                page = 1;
                getProdList(page, false, pay_int_hz);
            }
        });
        // 投资期限选择
        appUtils.bindEvent($(_pageId + " .qryDate div"), function (e) {
            $(_pageId + " .qryDate div").removeClass("active").filter(this).addClass("active");
            pay_int_hz = $(this).attr("pay_int_hz");
            $(_pageId + " .finance_pro").html("");
            page = 1;
            getProdList(page, false, pay_int_hz);
        });
        // 发行中
        appUtils.bindEvent($(_pageId + " #offering"), function (e) {
            $(_pageId + " .qryTransfer").hide();
            $(_pageId + " .qryDate").show();
            $(_pageId + " .tab_box a").removeClass("current").filter(this).addClass("current");
            $(_pageId + " .qryDate div").removeClass("active").eq(0).addClass("active");
            pay_int_hz = $(_pageId + " .qryDate div.active").attr("pay_int_hz");
            $(_pageId + " .finance_pro").html("");
            page = 1;
            getProdList(page, false, pay_int_hz);
        });
        // 转让中
        appUtils.bindEvent($(_pageId + " #transfering"), function (e) {
            $(_pageId + " .qryTransfer").show();
            $(_pageId + " .qryDate").hide();
            $(_pageId + " .tab_box a").removeClass("current").filter(this).addClass("current");
            $(_pageId + " .sort_box a").addClass("desc");
            page = 1;
            sort_field = "00"
            getTransList(page, false, sort_field);
        });

        //排序
        appUtils.bindEvent($(_pageId + " .sort_box a"), function () {
            var qryAmt = "";
            var qryDay = "";
            if ($(_pageId + " .sort_box a").eq(0).find("em").hasClass("desc")) {
                qryAmt = "1"
            } else {
                qryAmt = "0"
            }
            if ($(_pageId + " .sort_box a").eq(1).find("em").hasClass("desc")) {
                qryDay = "1"
            } else {
                qryDay = "0"
            }
            if ($(this).find("em").hasClass("desc")) {
                $(this).find("em").removeClass("desc").addClass("asc");
            } else {
                $(this).find("em").removeClass("asc").addClass("desc");
            }
            page = 1;
            sort_field = qryAmt + qryDay;
            getTransList(page, false, sort_field);
        });
    }


    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    page = 1;
                    if ($(_pageId + " #offering").hasClass("current")) {
                        getProdList(page, false, pay_int_hz);
                    } else {
                        getTransList(page, false, sort_field);
                    }
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        page += 1;
                        if ($(_pageId + " #offering").hasClass("current")) {
                            getProdList(page, true, pay_int_hz);
                        } else {
                            getTransList(page, true, sort_field);
                        }
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }

    //发行中列表
    function getProdList(list_page, isAppendFlag, pay_int_hz) {
        var param = {
            page: list_page,
            num_per_page: "10",
            pay_int_hz: pay_int_hz, //天数 0 不限   1 一个月内   2 1-6个月  3  6个月以上
        };
        if (bank_channel_code) {
            param.bank_channel_code = bank_channel_code;
        }
        service.reqFun151101(param, function (datas) {
            if (datas.error_no == 0) {
                if (datas.results.length == 0) return;
                currentPage = datas.results[0].current_page;//当前页数
                totalPages = datas.results[0].total_pages;//总页数
                var productList = datas.results[0].prod_list;
                var str = "", btnClass = "", btnText = "", depDayTypeName;
                var dateObj = {
                    "D": "天",
                    "M": "月",
                    "Y": "年",
                }
                if (parseFloat(currentPage) <= parseFloat(totalPages)) {
                    for (var i = 0; i < productList.length; i++) {
                        var prod_code = productList[i].prod_code; //产品代码
                        var prod_name = productList[i].prod_name; //产品名称
                        var surv_amt = productList[i].surv_amt; //起存金额（元）
                        var pay_int_type = productList[i].pay_int_type;  //付息单位 D-天 M-月 Y-年
                        var pay_int_hz = productList[i].pay_int_hz; //付息周期（天数）
                        var dep_day_type = productList[i].dep_day_type;  //周期单位 D-天 M-月 Y-年
                        var prod_dep_day = productList[i].prod_dep_day; //周期（天数）
                        var bas_int_rate = productList[i].bas_int_rate; //基础利率
                        var is_transfer = productList[i].is_transfer;//可转让
                        var prod_status = productList[i].prod_status; //产品状态 2-发售 4-售罄 6-停售
                        var brnd_sris = productList[i].brnd_sris; //产品系列   SD001 众力存  SD002 中惠存
                        if (brnd_sris == "SD002") {
                            depDayTypeName = dateObj[pay_int_type];
                            var dateStr = "<span>每" + pay_int_hz + depDayTypeName + "付息</span>";
                        } else if (brnd_sris == "SD001") {
                            depDayTypeName = dateObj[dep_day_type];
                            pay_int_hz = prod_dep_day;
                            var dateStr = "<span>期限：" + pay_int_hz + depDayTypeName + "</span>";
                        }
                        if (prod_status == "2") {
                            btnClass = "";
                            btnText = "购买";
                        } else if (prod_status == "4") {
                            btnClass = "sold_out";
                            btnText = "售罄";
                        } else if (prod_status == "6") {
                            btnClass = "sold_out";
                            btnText = "停售";
                        }
                        var transflagStr = "";
                        if (is_transfer == "1") {
                            transflagStr = '<span class="transflag">可转让</span>';
                        }
                        str += "<div class='pro_detail' prod_code='" + prod_code + "'><div class='box'>" +
                            "<span style='display: none' class='productInfo'>" + JSON.stringify(productList[i]) + "</span>" +
                            "<h4>" + prod_name + transflagStr + "</h4>" +
                            "<p>存款利率：<span class='redcol'>" + tools.fmoney(bas_int_rate) + "</span>%</p>" +
                            "<p><span style='width: 1.2rem;display: inline-block'>起购：" + tools.fmoney(surv_amt) + "元</span>" + dateStr + "</p>" +
                            "<em><i class='turn in'></i></em></p><a  href='javascript:void(0)' class='buy_btn pop " + btnClass + " in'>" + btnText + "</a></div></div>";
                    }
                }
                if (currentPage == 1 && productList.length == 0) {
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                    str = "<div class='nodata'>暂无数据</div>";
                }
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                pageScrollInit();
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }

    //转让中列表
    function getTransList(list_page, isAppendFlag, sort_field) {
        // 00:按挂单金额降序；01：按挂单金额升序；10：按剩余期限降序；11：按剩余天数升序
        var param = {
            page: list_page,
            num_per_page: "10",
            sort_field: sort_field,
        };
        if (bank_channel_code) {
            param.bank_channel_code = bank_channel_code;
        }
        if (!isAppendFlag) {
            $(_pageId + " .finance_pro").html("");
        }
        service.reqFun151118(param, function (datas) {
            if (datas.error_no == 0) {
                if (datas.results.length == 0) return;
                currentPage = datas.results[0].current_page;//当前页数
                totalPages = datas.results[0].total_pages;//总页数
                var hang_list = datas.results[0].hang_list;
                var str = "";

                if (parseFloat(currentPage) <= parseFloat(totalPages)) {
                    for (var i = 0; i < hang_list.length; i++) {
                        var prod_code = hang_list[i].prod_code; //产品代码
                        var prod_name = hang_list[i].prod_name; //产品名称
                        var hang_list_amt = hang_list[i].hang_list_amt; //挂单金额
                        var remind_term = hang_list[i].remind_term; //剩余期限
                        var bas_int_rate = hang_list[i].bas_int_rate; //基础利率
                        var dateStr = "<span>剩余期限：" + remind_term + "天</span>";
                        str += "<div class='pro_detail' prod_code='" + prod_code + "'><div class='box'>" +
                            "<span style='display: none' class='productInfo'>" + JSON.stringify(hang_list[i]) + "</span>" +
                            "<h4>" + prod_name + "</h4>" +
                            "<p>存款利率：<span class='redcol'>" + tools.fmoney(bas_int_rate) + "</span>%</p>" +
                            "<p><span style='margin-right: 0.2rem'>转让金额：" + tools.fmoney(hang_list_amt) + "元</span>" + dateStr + "</p>" +
                            "<em><i class='turn in'></i></em></p><a  href='javascript:void(0)' class='buy_btn pop  in'>购买</a></div></div>";
                    }
                }
                if (currentPage == 1 && hang_list.length == 0) {
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                    if ($(_pageId + " .nodata").length == 0) {
                        str = "<div class='nodata'>暂无数据</div>";
                    }
                }
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                pageScrollInit();
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }

    function destroy() {
        page = 1;
        // $(_pageId + " .qryDate").hide();
        // $(_pageId + ".tabbar").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .qryDate div").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .finance_pro").html("");
        $(_pageId + " .tab_box a").removeClass("current").eq(0).addClass("current");
        $(_pageId + " .qryDate div").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .qryDate").show();
        $(_pageId + " .qryTransfer").hide();
        $(_pageId + " .sort_box a em").removeClass("asc").addClass("desc");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var bankList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankList;
});
