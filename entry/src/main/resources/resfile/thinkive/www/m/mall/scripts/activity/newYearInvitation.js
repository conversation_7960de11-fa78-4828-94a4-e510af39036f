// 会员福利 - 邀请好友
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageCode = "activity/newYearInvitation",
        _pageId = "#activity_newYearInvitation ",
        layerUtils = require("layerUtils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService");
    var userInfo, cust_no;
    var tools = require("../common/tools");//升级
    var ut = require("../common/userUtil");
    require("../common/html2canvas.min");
    var share_url; //分享链接
    var isAllowSave;
    var _base64; //保存图片时，图片的base64编码
    // var pageTouchTimer = null;
    function init() {
        // console.log(appUtils);
        userInfo = ut.getUserInf();
        cust_no = userInfo.cust_no
        $(_pageId + " #code").html("");
        qrcode();
        //getSharedBanner();
    }
    // 获取分享图片
    function getSharedBanner() {
        var imgList = [
            {
                id: 0,
                img: "./images/activity/img_invitaion1.png"
            },
            {
                id: 1,
                img: "./images/activity/img_invitaion2.png"
            }
        ]
        var htmls = "";
        for (var i = 0; i < imgList.length; i++) {
            htmls += `<li class="li swiper-slide" data="${imgList[i].id}">
                <div class="imvitaion-img" style="margin: 0.2rem 0.25rem;position: relative;">
                    <img style="width: 100%;" src="${imgList[i].img}" alt="">
                    <div class="text-center" id="code" style="
                        width: 76px;
                        height: 76px;
                        position: absolute;
                        top: 81%;
                        right: 10%;
                    "></div>
                </div>
            </li>`
        }
        $(_pageId + ' #scroller_index2').html(htmls);
        swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
            pagination: '.swiper-pagination',
            autoplay: false,
            paginationElement: "li",
            bulletActiveClass: "check",
            autoplayDisableOnInteraction: false,
            loop: true,
            onImagesReady: function () {
                // if (callback) callback();
            },
            beforeDestroy: function () {
                swipeInstance = null
            }
        });
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //保存图片
        appUtils.bindEvent($(_pageId + " #save_picture"), function () {
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_newYearInvitation");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " #wrapper_index2 .swiper-slide-active .imvitaion-img")
            html2canvas(dom, {
                scale: 4,
            }).then(function (canvas) {
                var base64 = canvas.toDataURL("image/png");
                _base64 = base64;
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "60393";
                param["base64Image"] = base64;
                param["isJsCallBack"] = "1";
                param["flowNo"] = new Date().getTime() + "";
                require("external").callMessage(param, function () {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert("保存成功");
                });
            });

        });

        //邀请好友
        appUtils.bindEvent($(_pageId + " #invite_url"), function () {
            $(_pageId + " #pop_layer").show();
        });
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_newYearInvitation");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " #wrapper_index2 .swiper-slide-active .imvitaion-img")
            html2canvas(dom, {
                scale: 4,
            }).then(canvas => {
                var base64 = canvas.toDataURL("image/png");
                var _base64 = base64.split(",")[1];
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                common.share("22", sessionStorage.share_template ? sessionStorage.share_template : "0", "", true, _base64);
            })
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_newYearInvitation");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " #wrapper_index2 .swiper-slide-active .imvitaion-img")
            html2canvas(dom, {
                scale: 4
            }).then(canvas => {
                var base64 = canvas.toDataURL("image/png");
                var _base64 = base64.split(",")[1];
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                // console.log(base64);
                // 连续分享活动（春节）
                var id = appUtils.getSStorageInfo("toSharedId");
                common.share("23", sessionStorage.share_template ? sessionStorage.share_template : "1", "", true, _base64);
                service.reqFun108031({ cust_no: cust_no, activity_id: id }, (data) => {
                    if (data.error_no == 0) {
                        // pageTouchTimer = setTimeout(() => {
                        //     appUtils.pageInit(_pageCode, "activity/newYearEvent");
                        //     layerUtils.iMsg(-1, "分享成功！");
                        // }, 6000);
                    }
                })

            })
        });
    }

    //动态生成二位码
    function qrcode() {
        var mobile = ut.getUserInf().mobileWhole;
        let share_type = sessionStorage.share_template ? sessionStorage.share_template : "7"
        share(mobile, share_type);
    }
    //记录用户行为
    function setUser() {
        service.reqFun108009({ id: sessionStorage.activity_id, cust_no: cust_no }, (data) => {
            if (data.error_no == 0) {
                //记录成功
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function share(mobile, share_type) {
        // console.log(share_type,111)
        // 简化分享链接

        var query_params = {};
        query_params["registered_mobile"] = mobile;
        query_params["tem_type"] = share_type;
        service.reqFun102012(query_params, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results != undefined && data.results.length > 0) {
                    mobile = common.desEncrypt("mobile", mobile);//加密
                    var result = data.results[0];
                    var share_url = result.share_url;
                }
                if (validatorUtil.isEmpty(share_url)) {
                    share_url = global.link;
                }
                if (share_url.indexOf("?") != -1) {
                    share_url = share_url + "&mobile=" + mobile;
                } else {
                    share_url = share_url + "?mobile=" + mobile;
                }
                service.reqFun101073({ long_url: share_url }, function (res) {
                    if (res.error_no == "0") {
                        if (res.results != undefined && res.results.length > 0) {
                            var short_url = res.results[0].shortUrl;
                            // var params = {};
                            // params["url"] = "https://dwz.cn/XqQow9xF";
                            // service.simplifyURL(params, function (data) {
                            // 	var error_no = data.error_no,
                            // 		error_info = data.error_info;
                            // 	if (error_no == "0") {
                            // 		share_url = data.results[0].shortUrl;
                            // 		var str = share_url + "?mobile=" + mobile + "&type=6";
                            require("../common/jquery.qrcode.min");
                            $(_pageId + " #code").qrcode({
                                render: "canvas", //设置渲染方式，有table和canvas
                                text: short_url, //扫描二维码后自动跳向该链接
                                width: 70, //二维码的宽度
                                height: 70, //二维码的高度
                                imgWidth: 20,
                                imgHeight: 20,
                                src: '../mall/images/icon_app.png'
                            });
                            // 	} else {
                            // 		layerUtils.iAlert(error_info);
                            // 	}
                            // });
                        }
                    } else {
                        layerUtils.iAlert(res.error_info);
                    }
                })

            } else {
                layerUtils.iAlert(error_info);
            }
        });

    }


    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #pop_layer").hide();
        sessionStorage.share_template = ''
        sessionStorage.activity_id = ''
        // clearTimeout(pageTouchTimer);
    }

    var friendInvitation = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = friendInvitation;
})
    ;
