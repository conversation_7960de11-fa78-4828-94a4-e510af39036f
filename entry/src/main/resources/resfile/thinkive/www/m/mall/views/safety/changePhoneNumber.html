<div class="page" id="safety_changePhoneNumber" data-pageTitle="更换注册手机">
<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a href="javascript:void(0);" id="getBack" class="icon_back icon_gray" operationType="1" operationId="getBack" operationName="返回"><span>返回</span></a>
				<h1 class="text_gray text-center">更换注册手机</h1>
			</div>
		</header>
		<article>
			<!-- FORGET_PSD START -->
			<div class="forget_psd">
				<div class="check_tips slidedown in " style="border-bottom: none;display:none">
					<p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
				</div>
				<div class="grid_03 grid_02 grid" id="name">
					<div class="ui field text rounded input_box2">
						<label class="short_label">真实姓名</label>
						<input custom_keybord="0" maxlength="16" id="changename" type="text" class="ui input" placeholder="" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid" id="idCard">
					<div class="ui field text rounded input_box2">
						<label class="short_label">身份证号</label>
						<input custom_keybord="0" maxlength="18" id="changeid"  type="text" class="ui input" placeholder="" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid" id="olePhone">
					<div class="ui field text rounded input_box2">
						<label style="width:auto;">原注册手机号</label>
						<input custom_keybord="0" maxlength="11" id="changephone" type="tel" class="ui input" placeholder="" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2" id="password1">
						<label class="short_label">登录密码</label>
						<input custom_keybord="0" style="display:none" maxlength="16" id="oldPwd" disabled="" type="password" class="ui input" placeholder="">
						<!-- <div class=""></div> -->
						<div class="placeholderPsd"></div>
						<span style="display:none" class="cursor-bink">&nbsp;</span>
					</div>
				</div>
			</div>
		    <div class="grid_02 mt20">
				<a href="javascript:void(0);" id="submit" class="ui button block rounded btn_register pop in" perationType="1" operationId="submit" operationName="下一步">下一步</a>
			</div>
			<!-- FORGET_PSD END -->
		</article>
	</section>
</div>
