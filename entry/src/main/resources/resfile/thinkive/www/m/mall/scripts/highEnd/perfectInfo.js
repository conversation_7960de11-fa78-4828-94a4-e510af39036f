// 完善信息
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#highEnd_perfectInfo ",
        _pageCode = "highEnd/perfectInfo";
    var tools = require("../common/tools");
    var common = require("common");
    var userInfo;
    var ut = require("../common/userUtil");
    var monkeywords = require("../common/moneykeywords");
    require("../../js/prov_city.js");
    require("../../js/city.js");
    require("../../js/picker.min.js");
    var first = [];
    /* 省，直辖市 */
    var second = [];
    /* 市 */
    var third = [];
    /* 镇 */
    var selectedIndex = [0, 0, 0];
    /* 默认选中的地区 */
    var checked = [0, 0, 0];
    /* 已选选项 */
    var picker = ""; //地址选择器
    var live_province;
    var live_city;
    var live_county;

    function init() {
        userInfo = ut.getUserInf();
        setBankInfo();
        queryJob();
//        isshowdAdress();
        selectorArea();
        emailChange();
        queryIncome()
        
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //点击上传身份证上传
        appUtils.bindEvent($(_pageId + " #reUpload"), function () {
            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
        });
        appUtils.bindEvent(_pageId + " .select", function () {
            if($(_pageId + " .email_two").is(":hidden")){
                $(_pageId + " .email_two").show();
            }else{
                $(_pageId + " .email_two").hide();
            }
        });
        appUtils.bindEvent(_pageId + " .drop_down", function () {
            if($(_pageId + " .email_two").is(":hidden")){
                $(_pageId + " .email_two").show();
            }else{
                $(_pageId + " .email_two").hide();
            }
        });
        appUtils.preBindEvent($(_pageId+" .email_two")," li",function(){
            $(_pageId + " .email_two").hide();
            $(_pageId + ' #concent').attr('value', $(this).attr('value')).text($(this).text());
            if ($(_pageId + " #concent").attr("value") == '6') {
                $(_pageId + " #email").attr("placeholder", "格式：<EMAIL>");
                return
            }
            $(_pageId + " #email").attr("placeholder", "格式：xxxx");
            var val = $(_pageId + " #email").val();
            if (val && val.indexOf("@") > -1) {
                $(_pageId + " #email").val(val.substring(0, val.indexOf("@")));
            }
        });
        //邮箱修改
        appUtils.bindEvent(_pageId + " .changeEmail", function () {
            $(_pageId + " #email").removeAttr("readonly").val("");
            $(_pageId + " .select").show();
            $(_pageId + " .drop_down").show();
            $(_pageId + " .changeEmail").hide();
        });
        //地址修改
        appUtils.bindEvent(_pageId + " .changeCustAddress", function () {
            $(_pageId + " #cust_address").attr("contenteditable", "true");
            $(_pageId + " .changeCustAddress").hide();
        });
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        });
        appUtils.bindEvent($(_pageId + " #moneyBox"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_perfectInfo";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);

        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " .input_box2"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_perfectInfo";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        // 点击下一步
        appUtils.bindEvent($(_pageId + " #next"), function () {
            if($(_pageId + " #concent").attr("value")  == '6'  ||  $(_pageId + " .select").css("display") == "none"){
                var email = $(_pageId + " #email").val()
            }else{
                var email = $(_pageId + " #email").val() + $(_pageId + " #concent").text();
            }

            var occupation = $(_pageId + " #ocp").attr("data-value");
            var address = $(_pageId + " #cust_address").html();
            var year_income = $(_pageId + " #income").attr("data-money");
            if (validatorUtil.isEmpty(year_income)) {
                layerUtils.iMsg(-1, "请选择个人年收入");
                return;
            }
            if (validatorUtil.isEmpty(occupation)) {
                layerUtils.iAlert("请选择职业");
                return;
            }
            if (validatorUtil.isEmpty(email)) {
                layerUtils.iAlert("请输入邮箱");
                return;
            }
            if (!validatorUtil.isEmail(email)) {
                layerUtils.iAlert("邮箱格式不正确");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #live_address").val())) {
                layerUtils.iMsg(-1, "请选择居住地址");
                return;
            }

            var param = {
                email: email,
                occupation: occupation,
                year_icome: year_income*10000 + "",
                bank_reserved_mobile: userInfo.bankReservedMobile,
                living_address_province: live_province,
                living_address_city: live_city,
                living_address_county: live_county
            }

            service.reqFun101033(param, function (res) {
                if (res.error_no == "0") {
                    saveCallBackFun();
                } else {
                    layerUtils.iAlert(res.error_info);
                }
            },{"isLastReq": false})
        });
    }

    /**
     * 查询职业信息
     * */
    function queryJob() {
        var param = {code: 'occupation_jz'}
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                var duty = userInfo.duty;
                var position;
                if (duty) {
                    position = dataArr.findIndex(function (item) {
                        return duty == item.id;
                    });
                    $(_pageId + " #ocp").val(dataArr[position].value);
                    $(_pageId + " #ocp").attr("data-value", dataArr[position].id);
                } else {
                    position = 0;
                    $(_pageId + " #ocp").val();
                    $(_pageId + " #ocp").attr("data-value", "");
                }

                tools.mobileSelect({
                    trigger: _pageId + " #ocp",
                    title: "请选择职业",
                    dataArr: dataArr,
                    position: position + "",
                    callback: function (data, index) {
                        $(_pageId + " #ocp").val(data[0].value);
                        $(_pageId + " #ocp").attr("data-value", data[0].id);
                    }
                })
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })

    }

    /**
     * 设置用户银行信息
     * */
    function setBankInfo() {
        setQualifiedInvestor();
        $(_pageId + " #cardPerson").val(userInfo.name);
        $(_pageId + " #idCard").val(userInfo.identityNum);
        $(_pageId + " #cert_type").val("身份证");
        $(_pageId + " #cust_address").val(userInfo.cust_address);
        if (userInfo.email) {
            $(_pageId + " #email").val(userInfo.email).attr("readonly", "readonly");
            $(_pageId + " .select").hide();
            $(_pageId + " .drop_down").hide();
            $(_pageId + " .changeEmail").show();
        } else {
            $(_pageId + " #email").removeAttr("readonly");
            $(_pageId + " .select").show();
            $(_pageId + " .drop_down").show();
            $(_pageId + " .changeEmail").hide();
        }
        if (userInfo.custAddress) {
            $(_pageId + " #cust_address").html(userInfo.custAddress);
            $(_pageId + " .changeCustAddress").show();
        } else {
            $(_pageId + " #cust_address").attr("contenteditable", "true");
            $(_pageId + " .changeCustAddress").hide();
        }
        service.destroy();

    }

    /**
     * 查询年收入信息
     * */
    function queryIncome() {
        dataArr_income = [
            {id: "01", value: "0~10（含）", money: "10"},
            {id: "02", value: "10~50（含）", money: "50"},
            {id: "03", value: "50~100（含）", money: "100"},
            {id: "04", value: "其他", money: ""}
        ]
        var year_icome = userInfo.year_icome / 10000;
        var position;
        if(year_icome){
            var position = dataArr_income.findIndex(function (item) {
                return year_icome == item.money;
            });
            if(position == -1){
                position = 3;
                $(_pageId + " #income").val(year_icome);
                $(_pageId + " #income").attr("data-id", "04").attr("data-money", year_icome).attr("data-value", dataArr_income[3].value);
            }else {
                $(_pageId + " #income").val(dataArr_income[position].value);
                $(_pageId + " #income").attr("data-money", dataArr_income[position].money);
            }
        } else {
            position = 0;
            $(_pageId + " #income").val();
            $(_pageId + " #income").attr("data-money", "");
        }


        tools.mobileSelect({
            trigger: _pageId + " #income",
            title: "请选择年收入（万元）",
            dataArr: dataArr_income,
            position: position + "",
            callback: function (data) {
                $(_pageId + " #income").val(data[0].value);
                $(_pageId + " #income").attr("data-id", data[0].id);
                $(_pageId + " #income").attr("data-value", data[0].value);
                $(_pageId + " #income").attr("data-money", data[0].money);
                if (data[0].id == "04") {
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " .password_box").show();
                    event.stopPropagation();
                    $(_pageId + " #srje").val('');
                    $(_pageId + " #inputspanid span").html('');
                    //键盘事件
                    moneyboardEvent();
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "highEnd_perfectInfo";
                    param["eleId"] = "srje";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "3";
                    require("external").callMessage(param);
                    var srje = $(_pageId + " #srje").val();
                    $(_pageId + " #income").val(srje);
                    $(_pageId + " #income").attr("data-id", "04").attr("data-money", srje).attr("data-value", dataArr_income[3].value);
                    $(_pageId + " #inputspanid span").html(srje);
                }
            }
        });

    }
    //金额输入数字键盘
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #srje"),
            endcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                } else if(moneys<=0){
                    layerUtils.iAlert("请输入大于零的金额");
                }else {
                    $(_pageId + " .pop_layer").hide();
                    $(_pageId + " .password_box").hide();
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}$/.test(curVal))) {
                    moneys = moneys.substring(0, curVal.length - 1)
                }
                $(_pageId + " #income").val(Number(moneys));
                $(_pageId + " #income").attr("data-id", "04").attr("data-value", dataArr_income[3].value).attr("data-money", moneys);
                $(_pageId + " #inputspanid span").html(Number(moneys));
                $(_pageId + " #srje").val(Number(moneys));


            }
        })
    }

    /**
     * 居住地址选择
     */
    function selectorArea() {
      	live_province = userInfo.living_address_province;
    	live_city= userInfo.living_address_city;
        live_county =userInfo.living_address_county;
    	var position;
        var position1;
        var position2;
        
        if (live_province) {
            try {
                position = city.findIndex(function (item) {
                    return live_province == item.code;
                });    
                position1 = city[position].sub.findIndex(function (item) {
                    return live_city == item.code;
                });
                position2 = city[position].sub[position1].sub.findIndex(function (item) {
                    return live_county == item.code;
                });           
                var value = city[position].name + ' ' + city[position].sub[position1].name + ' ' + city[position].sub[position1].sub[position2].name;
                $(_pageId + " #live_address").val(value);
            } catch (error) {
                position = 0;
                position1 = 0;
                position2 = 0;
            }
            
        } else {
            position = 0;
            position1 = 0;
            position2 = 0;
        }
        selectedIndex = [position, position1, position2];
  
        
    	
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            value = text1 + ' ' + text2 + ' ' + text3;
            // $(_pageId + " #live_address").val(value);
            live_province = first[selectedIndex[0]].code;
            live_city = second[selectedIndex[1]].code;
            live_county = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            let flag = await tools.is_region(live_province,live_city,live_county,city)
            if(flag){
                $(_pageId + " #live_address").val(value);
            }else{
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }
//           var code = code1 + ' ' + code2 + ' ' + code3;
//           $(_pageId + " #live_address").attr("data-code", code);
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
  
    }

    function emailChange() {
        var dataArr = common.emailType;
        $(_pageId + " .email_two").html(dataArr.map(function (item) {
           /* return "<option value='" + item.id + "'>" + item.value + "</option>"*/
           return "<li value='" + item.id + "'>" + item.value + "</li>"
        }))
    }

    function setQualifiedInvestor() {
        service.reqFun101040({}, function (data) {
            if (data.error_no == "0") {
                //完善信息状态  0:未完善 1:已完善 2:证件到期
                var perfect_info = data.results[0].perfect_info;

                if (perfect_info == "2") {
                    $(_pageId + " #ocrIsUpload").val("已过期");
                    $(_pageId + " #reUpload").html("重新上传").show();
                } else {
                    $(_pageId + " #ocrIsUpload").val("已上传");
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function saveCallBackFun() {
        service.reqFun101020({}, function (data) {
            var error_no = data.error_no;
            if (error_no == "0") {
                var results = data.results[0];
                common.setLocalStorage("mobileWhole", results.mobileWhole);
                ut.saveUserInf(results);
                layerUtils.iLoading(false);
                pageBack();
            } else {
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, "网络繁忙,请重新登录");
                appUtils.clearSStorage("_loginInPageCode");
                appUtils.clearSStorage("_loginInPageParam");
                appUtils.clearSStorage("_isLoginIn");
                appUtils.clearSStorage();
                common.gestureLogin();
            }
        })
    }


    function destroy() {
        $(_pageId + " input").val("");
        $(_pageId + " .changeEmail").hide();
        $(_pageId + " .changeCustAddress").hide();
        $(_pageId + " #cust_address").removeAttr("contenteditable");
        $(".mobileSelect").remove();
        $(_pageId + " .whaddress").hide();
        $(".picker").remove();
        $(_pageId + " .email_two").hide();
        $(_pageId + " .drop_down").hide();
        $(_pageId + ' #concent').attr('value', '0').text("@qq.com");
        $(_pageId + " #live_address").val("");
        $(_pageId + " #ocp").val("");
        $(_pageId + " #income").val("");
        $(_pageId + " #ocrIsUpload").val("");
        $(_pageId + " #reUpload").html("");
        first = [];
        second = [];
        third = [];
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var highEndPerfect = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highEndPerfect;
});
