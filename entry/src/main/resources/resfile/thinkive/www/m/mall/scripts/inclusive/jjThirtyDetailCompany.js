// 晋金公司
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageId = "#inclusive_jjThirtyDetailCompany";
    var _pageCode = "inclusive/jjThirtyDetailCompany";
    var _mgrcomp_sname = "";
    var productInfo;
    var ut = require("../common/userUtil");
    var common = require("common");

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        _mgrcomp_sname = appUtils.getSStorageInfo("mgrcomp_sname");
        //查询基金公司信息
        reqFun102022();
        tools.initFundBtn(productInfo, _pageId);

    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //查询基金公司信息
    function reqFun102022() {
        var param = {
            mgrcomp_sname: _mgrcomp_sname,
        }
        service.reqFun102022(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                var mgrcomp_name = results.mgrcomp_name;
                var found_date = tools.ftime(results.found_date);
                var reg_capital = tools.fmoney(results.reg_capital) + "万元";
                var website = results.website;
                var business_scope = results.business_scope;

                $(_pageId + " #mgrcomp_name").html(mgrcomp_name);
                $(_pageId + " #found_date").html(found_date);
                $(_pageId + " #reg_capital").html(reg_capital);
                $(_pageId + " #website").html(website);
                $(_pageId + " #business_scope").html(business_scope);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .item-right ").html("");
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailCompany = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailCompany;
});
