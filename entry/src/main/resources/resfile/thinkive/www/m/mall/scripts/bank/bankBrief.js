// 银行简介
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        global = gconfig.global,
        _pageId = "#bank_bankBrief ";

    function init() {
        var porductInfo = appUtils.getSStorageInfo("productInfo");
        service.reqFun151121({bank_channel_code: porductInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var result = data.results[0];
                $(_pageId + " img").attr("src", global.oss_url + result.bank_picture);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function destroy() {
        $(_pageId + " .img").attr("src", "");

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
