// 转让产品购买页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        serviceConstants = require("constants"),
        monkeywords = require("mall/scripts/common/moneykeywords"),
        ut = require("../common/userUtil");
    var _pageId = "#myTransfer_buyTransferProduct";
    var _pageCode = "myTransfer/buyTransferProduct";
    var proNumberFlag; //是否多期 true 多期 false 单
    let info,_available_vol,jymm
    function init() {
        let agreement_sub_type;
        proNumberFlag = false;//默认单期
        //页面埋点初始化
        tools.initPagePointData();
        info = appUtils.getSStorageInfo("buyTransferData"); //拿到产品基本信息
        if(info.same_prod_transfer_all == '1'){
            //小集合多期转让
            $(_pageId + " .service_charge").show();
            $(_pageId + " .total_amt").show();
            $(_pageId + " .step").show();
            $(_pageId + " .myTransferFooter").text("提交购买申请");
            $(_pageId + " .new_pay").show();
            $(_pageId + " .old_pay").hide();
            // agreement_sub_type = '6';
            // getTransferMoney(info.costmoney);
            let listData = info.details;
            if(listData && listData.length > 1){
                //存在多期
                $(_pageId + " .see_details").show();
                $(_pageId + " .rate").hide();
                $(_pageId + " .last_day").hide();
                $(_pageId + " .entrust_date").hide();
                proNumberFlag = true;
                setListData(listData)
            }else{
                $(_pageId + " .rate").show();
                $(_pageId + " .last_day").show();
                $(_pageId + " .entrust_date").show();
                $(_pageId + " #entrust_date").text(tools.ftime(listData[0].due_date));
                //只有一期
                $(_pageId + " .see_details").hide();
            }
            
        }else{
            $(_pageId + " .new_pay").hide();
            $(_pageId + " .old_pay").show();
            $(_pageId + " .rate").show();
            $(_pageId + " .last_day").show();
            $(_pageId + " .entrust_date").show();
            $(_pageId + " .service_charge").hide();
            $(_pageId + " .total_amt").hide();
            $(_pageId + " .step").hide();
            $(_pageId + " .myTransferFooter").text("购买");
            // agreement_sub_type = '1,6'
        }
        // $(_pageId + " .prod_sname").html(info.prod_sname)
        tools.getPdf({
            agreement_type: "prod",
            fund_code: info.fund_code,
            bookTitleMark: "0",
            agreement_sub_type: '1,6',
        }); //获取协议
        renderData(info)
        reqFun101901(); //可用
    }
    //渲染卡片列表信息
    function setListData(list){
        let html = '<ul class="flex"><li>本金</li><li>到期日</li><li>剩余期限</li><li>业绩计提基准</li></ul>';
        list.map(item=>{
            html += `
            <ul class="flex">
                    <li>${tools.fmoney(item.costmoney)}元</li>
                    <li>${tools.ftime(item.due_date)}</li>
                    <li>${item.surpterm}天</li>
                    <li class="m_text_red">${tools.fmoney(item.rate)}%</li>
            </ul>
            `
        })
        $(_pageId + " .card").html(html)
        $(_pageId + " .card").show();
    }
    //获取实际交易金额
    function getTransferMoney(money){
        let param = {
            give_profit:money + '',
            fund_code:info.fund_code,
            vir_fundcode:info.vir_fundcode
        }
        service.reqFun107008(param, function (data) {
            if (data.error_no == 0) {
                // let val = data.results[0].transfer_amt;
                // if(transfer_type == '1'){
                //     $(_pageId + ' #transfer_amt').html(tools.fmoney(data.results[0].transfer_amt) + '元');
                //     $(_pageId + " #fund_vol").html(tools.fmoney(data.results[0].costmoney_sum) + '元');
                // }else{
                //     $(_pageId + ' #transfer_amt').html(data.results[0].transfer_amt);
                // }
                
                // setProList
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function bindPageEvent() {
        //弹出明细列表
        appUtils.bindEvent($(_pageId + " .see_details"),function () {
            //判断卡片是否展示
            let showCard = $(_pageId + " .card").is((':visible'))
            if(!showCard){
                $(_pageId + " .see_details .bottom").hide();
                $(_pageId + " .see_details .top").show();
                $(_pageId + " .card").show();
            }else{
                $(_pageId + " .see_details .top").hide();
                $(_pageId + " .see_details .bottom").show();
                $(_pageId + " .card").hide();
            }
        })
        //弹出转让规则
        appUtils.bindEvent($(_pageId + " #transferRule"),function () {
            $(_pageId + " .rule_dio").show();
            $(_pageId + " .card1").show();
        })
        //关闭转让规则弹窗
        appUtils.bindEvent($(_pageId + " .card_footer"),function () {
            $(_pageId + " .rule_dio").hide();
            $(_pageId + " .card1").hide();
        })
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "myTransfer_buyTransferProduct";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转产品详情
        appUtils.bindEvent($(_pageId + " .myTransferFund_entry"), function () {
            // appUtils.pageInit(_pageUrl, "highEnd/transaction");
            appUtils.pageInit(_pageCode, "template/heighEndProduct");
        });
        
        //小集合多期确认购买
        appUtils.bindEvent($(_pageId + " .btn_bottom"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .buyBefore").hide();
            $(_pageId + " .buyDio").show();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "myTransfer_buyTransferProduct";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        appUtils.bindEvent($(_pageId + " #buy"),function(){
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            //小集合转让特殊处理
            if(info.same_prod_transfer_all == '1'){
                //小集合多期特殊处理
                if(proNumberFlag == true){
                    $(_pageId + " .buyBefore .card").show();
                    $(_pageId + " .buyDio").show();
                    $(_pageId + " .buyBefore").show();
                }else{
                    $(_pageId + " #payMethod").hide();
                    $(_pageId + " .buyDio").show();
                    $(_pageId + " .password_box").show();
                    //输入交易密码时的提示
                    setRechargeInfo();
                    passboardEvent();
                    monkeywords.flag = 0;
                    //键盘事件
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "myTransfer_buyTransferProduct";
                    param["eleId"] = "jymm";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "4";
                    require("external").callMessage(param);
                }
            }else{
                $(_pageId + " .buyDio").show();
                $(_pageId + " #payMethod").show();
                isCanBuy(); //是否可以购买
            }
        })
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .buyDio").hide();
        });
        //点击确定
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "myTransfer_buyTransferProduct";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        })
        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        $(_pageId + " #recharge_name").html(info.prod_sname);
        $(_pageId + " #recharge_money").html(tools.fmoney(info.costmoney));
        $(_pageId + " #transfer_money").html(tools.fmoney(info.transfer_amt));
        
    }
    //关闭密码输入框
    appUtils.bindEvent($(_pageId + " #close"), function () {
        guanbi();
        $(_pageId + " .password_box").hide();
        $(_pageId + " .buyDio").hide();
    });
    //还原
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //调用支付接口
    appUtils.bindEvent($(_pageId + " #queDing"), function () {
        $(_pageId + " .buyDio").hide();
        $(_pageId + " .password_box").hide();
        let trans_pwd = $(_pageId + " #jymm").val();
        guanbi();
        // trans_pwd = '123123'
        if (trans_pwd.length != 6) {
            layerUtils.iAlert("请确定您的交易密码格式正确");
            return;
        }
        //进行充值
        // trans_amt = trans_amt.replace(/,/g, "");
        // console.log(ut.getUserInf().fncTransAcctNo,222)
        var param = {
            trans_pwd: trans_pwd, //交易密码
            fund_code: info.fund_code, //产品编码
            app_vol:info.transfer_vol,//份额
            // entrust_no:info.entrust_no,
            entrust_no:info.entrust_no,
            deal_amt:info.transfer_amt,
            period:info.period ? info.period : ''
        };
        if(info.same_prod_transfer_all == '1'){ //小集合多期提交订单
            service.reqFun107016(param, function (data) {
                if (data.error_no == 0) {
                    info.trans_serno = data.results[0].trans_serno; //记录订单编号
                    appUtils.setSStorageInfo("buyTransferData",info); //拿到产品基本信息
                    appUtils.pageInit(_pageCode, "myTransfer/uploadCredentials",{pageTo:'myTransfer'});    //首次购买进入下级页面 点击返回 回到我的转让页面
                    // pageBack();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }else{
            service.reqFun106026(param, function (data) {
                if (data.error_no == 0) {
                    appUtils.pageInit(_pageCode, "highEnd/purchaseResult", {
                        app_amt: param.deal_amt,
                        trans_serno: data.results[0].trans_serno
                    });
                    // pageBack();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }
        
    });
    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = info.transfer_amt;
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }
    
    //查询可用金额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];

            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额：<em class="money">' + tools.fmoney(_available_vol + "") + '</em>元';

            $(_pageId + " .pay_bank").html(html);
        })
    }
    //渲染交易详情数据
    function renderData(info){
        let fund_code = '(' + info.fund_code + ')'
        let rate = tools.fmoney(info.rate) + '%' //年化标准
        let due_date = tools.ftime(info.due_date)
        $(_pageId + " #fund_sname").text(info.prod_sname)
        $(_pageId + " #fund_code").text(fund_code)
        $(_pageId + " #rate").text(rate)
        $(_pageId + " #last_day").text(info.last_day + '天')
        $(_pageId + " #transfer_amt").text(tools.fmoney(info.transfer_amt) + '元')
        $(_pageId + " #costmoney").text(tools.fmoney(info.costmoney) + '元')
        $(_pageId + " #give_profit").text(tools.fmoney(info.give_profit) + '元')
        $(_pageId + " #service_charge").text(tools.fmoney(info.service_charge) + '元')
        $(_pageId + " #total_amt").text(tools.fmoney(info.total_amt) + '元')
        if(info.same_prod_transfer_all != '1' ) $(_pageId + " #entrust_date").text(due_date);
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi()
        $(_pageId + " .card").html('');
        proNumberFlag = false;//默认单期
        monkeywords.destroy();
        $(_pageId + " .rule_dio").hide();
        $(_pageId + " .card1").hide();
        $(_pageId + " .card").hide();
        $(_pageId + " .see_details").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " .agreement1").hide();
        $(_pageId + " .agreement").attr("isVerify", "false");
        $(_pageId + " #jymm").val("");
        $(_pageId + " .buyDio").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " .service_charge").hide();
        $(_pageId + " .agreement1 i").removeClass("active");
        $(_pageId + " .hint_table").html("");
        $(_pageId + " .rate_text").html("");
        $(_pageId + " #fund_sname").text('--')
        $(_pageId + " #fund_code").text('--')
        $(_pageId + " #rate").text('--')
        $(_pageId + " #last_day").text('--')
        $(_pageId + " #transfer_amt").text('--')
        $(_pageId + " .buyBefore").hide();
        $(_pageId + " #costmoney").text('--')
        $(_pageId + " #give_profit").text('--')
        $(_pageId + " #entrust_date").text('--')
        $(_pageId + " .rate").hide();
        $(_pageId + " .last_day").hide();
        $(_pageId + " .entrust_date").hide();
        $(_pageId + " .total_amt").hide();
        $(_pageId + " .new_pay").hide();
        $(_pageId + " .old_pay").hide();
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});
