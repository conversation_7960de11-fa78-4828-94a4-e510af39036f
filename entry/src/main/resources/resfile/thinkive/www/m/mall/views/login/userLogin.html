<div class="page" id="login_userLogin" data-pageTitle="登录" data-refresh="true">
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<!-- 埋点相关备注参数 -->
				<a href="javascript:void(0);" class="icon_back icon_gray" operationType="1" operationId="icon_back"
					operationName="返回"><span>返回</span></a>
				<h1 class="text_gray text-center">登录</h1>
			</div>
		</header>
		<article class="bg_blue">
			<!-- 切换登录方式 -->
			<div class="grid_06 grid_02">
				<ul class="flex choose-login-type m_bold m_font_size14">
					<li class="active main_flxe flex_center login_psd">密码登录</li>
					<li class="main_flxe flex_center login_yzm">验证码登录</li>
				</ul>
			</div>
			<!-- 账号密码登录 -->
			<div class="login_page" style="display:none;padding:0">
				<!-- <div class="m_font_size16" style="margin:0.2rem 0.1rem 0.1rem 0.4rem;font-weight: 600;">账号密码登录</div> -->
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2">
						<div class="ui label">
							<img src="front/images/label_01.png" width="12" alt="">
						</div>
						<input type="text" class="ui input" custom_keybord="0" id="phontNum" maxlength="20"
							placeholder="手机号/别名" />
					</div>
				</div>
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2" id="yzmBox" operationType="1"
						operationId="yzmBox" operationName="弹出数字键盘">
						<div class="ui label">
							<img src="front/images/label_02.png" width="12" alt="">
						</div>
						<input class="ui input" style="display:none" custom_keybord="0" type="password" maxlength="16"
							disabled type="tel" id="pwd" placeholder="请输入登录密码">
						<input class="ui input YzmShow" style="display:none" custom_keybord="0" type="number"
							oninput="if(value.length>6)value=value.slice(0,6)" id="yzm" placeholder="请输入验证码">
						<div class="placeholderPsd">请输入密码</div>
						<span style="display:none" class="cursor-bink">&nbsp;</span>
					</div>
				</div>
				<div class="grid_06 drainHide">
					<a href="javascript:void(0);" class="ui button block rounded btn_login " id="login"
						operationType="1" operationId="login" operationName="登录">登&nbsp;&nbsp;录</a>
				</div>
				<div class="grid_06">
					<p class="link_box m_font_size16">
						<a href="javascript:void(0);" class="link_01 drainHide" id="register" operationType="1"
							operationId="register" operationName="立即注册">立即注册</a>
						<a href="javascript:void(0);" class="link_02 drainHide" id="wjmm" operationType="1"
							operationId="wjmm" operationName="忘记密码">忘记密码？</a>
					</p>
				</div>
				<!-- <div class="login_yzm m_text_999">
					<div class="flex" style="padding:0 0.5rem">
						<p style="color:#ccc;font-size: 0.12rem;">————</p>
						<p>其他登录方式</p>
						<p style="color:#ccc;font-size: 0.12rem;">————</p>
					</div>
					<div class="login_img"><img src="../images/loginMobile.png"></div>
					<div>手机验证码登录</div>
				</div> -->

			</div>
			<!-- LOGIN_PAGE END -->


			<!-- 验证码登录-->
			<div class="register_box" style="display:none;margin: 0;padding:0">
				<!-- <div class="m_font_size16" style="margin:0.2rem 0.1rem 0.1rem 0.4rem;font-weight: 600;">手机验证码登录</div> -->
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2">
						<div class="ui label">
							<img src="front/images/label_01.png" width="12" alt="">
						</div>
						<input type="text" class="ui input" custom_keybord="0" id="phoneNum" maxlength="11"
							placeholder="手机号" />
						
					</div>
				</div>
				<!-- 手机提示 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="phoneShow" style="display:none;color:red;margin-left:0.15rem">如收不到短信
						<dd>
					</dl>
				</div>

				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2">
						<div class="ui label">
							<img src="front/images/label_03.png" width="12" alt="">
						</div>
						<input type="text" class="ui input" custom_keybord="0" id="verificationCode" maxlength="6"
							placeholder="短信验证码" />
						<span id="getYzmNew" class="red" operationType="1" operationId="getYzmNew" operationName="获取验证码"
							data-state="true">获取验证码</span>

					</div>
				</div>


				<!-- 语音验证码 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode" class="m_paddingLeft_30" style="display:none">如收不到短信 &nbsp;&nbsp;<span
								operationType="1" operationId="getTalk" operationName="获取语音验证码" id="getTalk"
								style="color:blue">语音获取</span>
						<dd>
					</dl>
				</div>
				<div class=" grid_02 text-center">
					<div class="radio_box" style="display:none">
						<div class="ui radio">
							<input type="radio" id="input_radio2">
							<label id="isChecked" operationType="1" operationId="isChecked"
								operationName="点击协议">我已阅读并同意签署
							</label>
							<span class="agreement_list deal_box"></span>
						</div>
					</div>
				</div>
				<div class="grid_06">
					<a href="javascript:void(0);" class="ui button block rounded btn_login " operationType="1"
						operationId="registered" operationName="登录"
						id="registered">注&nbsp;&nbsp;册&nbsp;&nbsp;/&nbsp;&nbsp;登&nbsp;&nbsp;录</a>
				</div>

				<!-- <div class="login_psd m_text_999">
					<div class="flex" style="padding:0 0.5rem">
						<p style="color:#ccc;font-size: 0.12rem;">————</p>
						<p>其他登录方式</p>
						<p style="color:#ccc;font-size: 0.12rem;">————</p>
					</div>
					<div class="login_img"><img src="../images/login1.png"></div>
					<div>账号密码登录</div>
				</div> -->
			</div>



			<!-- REGISTER_BOX END -->
		</article>
	</section>
</div>