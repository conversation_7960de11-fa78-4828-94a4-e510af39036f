//债基 定开： 封闭期历史记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageId = "#inclusive_bondFixCloseList";
    var _pageCode = "inclusive/bondFixCloseList";
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " .header_inner h1").html(productInfo.prod_sname);
        //获取业绩表现
        getPerformance();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //获取业绩表现
    function getPerformance() {
        var prams = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102034(prams, function (data) {
            if (data.error_no == 0) {
                if (data.results.length == 0) {
                    $(_pageId + " #performanceContent .list_content").html("<div class='nodata'>暂无数据</div>");
                    $(_pageId + " .warm-tip").hide();
                    return;
                }
                var results = data.results;

                var html = "";
                for (var i = 0; i < results.length; i++) {
                    var start_date = results[i].start_date;
                    var end_date = results[i].end_date;
                    var yield = results[i].yield;
                    var dateStr = "";
                    if (start_date && end_date) {
                        dateStr = tools.ftime(start_date, ".") + " - " +tools.ftime(end_date, ".")
                    } else {
                        dateStr = "--";
                    }
                    if (yield) {
                        yield = tools.fmoney(yield) + "%";
                    } else {
                        yield = "--";
                    }
                    html += '<div class="item">' +
                        '<span style="width: 70%">' + dateStr + '</span>' +
                        '<span class=" ' + tools.addMinusClass(results[i].yield) + '">' + yield + '</span>' +
                        '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
                $(_pageId + " .warm-tip").show();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        $(_pageId + " .header_inner h1").html("--");
        $(_pageId + " #performanceContent .list_content").html("");
        $(_pageId + " .warm-tip").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
