//银行产品-转让
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_transferSell ";
    var ut = require("../common/userUtil");
    var _pageCode = "bank/transferSell";
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var sms_mobile = require("../common/sms_mobile");
    var productInfo;// 产品信息
    var bankElectornReservedMobile; //电子银行预留手机号
    var isInput;//是否输入过金额
    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        tools.getBankPdf("2", productInfo.bank_channel_code, productInfo.prod_code);
        getAccountInfo();
        initProductInfo();
        isInput = false;
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            productInfo["isBuy"] = "0";
            appUtils.pageInit(_pageCode, "bank/bankDetail");
        });
        //产品详情
        // appUtils.bindEvent($(_pageId + " .product_content"), function () {
        //     appUtils.pageInit(_pageCode, "bank/bankDetail");
        // });

        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            isInput = true;
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "bank_transferSell";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();

            if (isInput) {
                isInput = false;
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                getRate(moneys);
            }
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            var money = $(_pageId + " #money").val().replace(/,/g, "");

            if ($code.attr("data-state") == "false") {
                return;
            } else {
                if (money <= 0 || !money) {
                    layerUtils.iAlert("请输入买入金额");
                    return;
                }
                if (parseFloat(money) < parseFloat(productInfo.tran_low_amt)) {
                    layerUtils.iAlert("转让最低金额" + tools.fmoney(productInfo.tran_low_amt) + "元");
                    return;
                }
                if (money && productInfo.tran_low_amt && productInfo.drw_step_amt && Math.round((parseFloat(money) - productInfo.tran_low_amt) % productInfo.drw_step_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("递增金额为" + tools.fmoney(productInfo.drw_step_amt) + "元");
                    return;
                }
                // 获取验证码
                var param = {
                    "mobile_phone": bankElectornReservedMobile,
                    "type": common.sms_type.bankTransferSell,
                    "send_type": "0",
                    "mobile_type": "2",
                    "bank_abbr": productInfo.prod_name
                };
                sms_mobile.sendPhoneCode(param)
            }
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (money <= 0 || !money) {
                layerUtils.iAlert("请输入转让金额");
                return;
            }

            if (parseFloat(money) < parseFloat(productInfo.tran_low_amt)) {
                layerUtils.iAlert("转让最低金额金额为" + tools.fmoney(productInfo.tran_low_amt) + "元");
                return;
            }
            if (money && productInfo.tran_low_amt && productInfo.drw_step_amt && Math.round((parseFloat(money) - productInfo.tran_low_amt) % productInfo.drw_step_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                layerUtils.iAlert("递增金额为" + tools.fmoney(productInfo.drw_step_amt) + "元");
                return;
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            //转让
            var trans_amt = $(_pageId + " #money").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                hang_list_amt: trans_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                prod_code: productInfo.prod_code,
                brnd_sris: productInfo.brnd_sris,
                trans_type: "1",
                sms_code: verificationCode,
                sms_mobile: bankElectornReservedMobile,
                order_no: productInfo.order_no,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            trade(param);
        });
    }

    function trade(param) {
        service.reqFun151011(param, function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/transferResult", {
                trans_serno: trans_serno
            });
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                isInput = false;
                $(_pageId + " #inputspanid span").removeClass("active").addClass("unable");
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) return;
                curVal = tools.fmoney(moneys);
                $(_pageId + " #money").val(curVal);
                if (parseFloat(moneys) < parseFloat(productInfo.tran_low_amt)) {
                    layerUtils.iAlert("转让最低金额为" + tools.fmoney(productInfo.tran_low_amt) + "元");
                    return;
                }
                if (moneys && productInfo.tran_low_amt && productInfo.drw_step_amt && Math.round((parseFloat(moneys) - productInfo.tran_low_amt) % productInfo.drw_step_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("递增金额为" + tools.fmoney(productInfo.drw_step_amt) + "元");
                    $(_pageId + ' [data-name="hold_rate"]').text("--");
                    return;
                }
                getRate(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }
                if (parseFloat(curVal) > parseFloat(productInfo.trans_amt)) {
                    $(_pageId + " #money").val(productInfo.trans_amt);
                    return;
                }
            },
            keyBoardHide: function () {
                isInput = false;
                var curVal = $(_pageId + " #money").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                getRate(moneys);
            }
        })
    }

    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    return;
                }
                $(_pageId + " .bankIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "(尾号" + results.acct_no.substr(-4) + ")");
                bankElectornReservedMobile = results.mobile_phone;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getRate(moneys) {
        if (!moneys) {
            $(_pageId + ' [data-name="hold_rate"]').text("自动计算");
            return;
        }
        var param = {
            bank_channel_code: productInfo.bank_channel_code,//银行渠道代码
            prod_code: productInfo.prod_code,//产品代码
            brnd_sris: productInfo.brnd_sris,//
            hang_list_amt: moneys,//交易金额
            order_no: productInfo.order_no,//订单号
        }
        service.reqFun151013(param, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                $(_pageId + ' [data-name="hold_rate"]').text(tools.fmoney(results.hang_list_dayearn));
            } else {
                layerUtils.iAlert(data.error_info)
                $(_pageId + ' [data-name="hold_rate"]').text("--");
            }
        })

    }

    function initProductInfo() {
        $(_pageId + " .prod_sname").text(productInfo.prod_name);
        $(_pageId + " .rate").text(tools.fmoney(productInfo.base_lnt_rate) + "%");
        $(_pageId + " #money").val("");
        $(_pageId + " #inputspanid span").text(tools.fmoney(productInfo.tran_low_amt) + "元起，" + tools.fmoney(productInfo.drw_step_amt) + "元递增");
        $(_pageId + " #inputspanid span").attr("text", tools.fmoney(productInfo.tran_low_amt) + "元起，" + tools.fmoney(productInfo.drw_step_amt) + "元递增");
        $(_pageId + " [data-name=trans_amt]").text(tools.fmoney(productInfo.trans_amt)); //可转让金额
        $(_pageId + " [data-name=order_save_date]").text(productInfo.order_save_date); //持有时间
        $(_pageId + " [data-name=exprd_date]").text(tools.ftime(productInfo.exprd_date));//到期日
        $(_pageId + " #money").val("");
    }

    function destroy() {
        $(_pageId + " .prod_sname").text("--");
        $(_pageId + " #inputspanid span").text("请输入转让金额").css({color: "#999999"}).removeClass("active");
        $(_pageId + " #inputspanid span").attr("text", "请输入转让金额");
        $(_pageId + " #money").text("");
        $(_pageId + " .bank_electron_info").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " .bankIcon img").attr("src", "");
        $(_pageId + " [data-name=hold_rate]").text("自动计算");
        $(_pageId + " [data-name=trans_amt]").text("--"); //可转让金额
        $(_pageId + " [data-name=order_save_date]").text("--"); //持有时间
        $(_pageId + " [data-name=exprd_date]").text("--");//到期日
        monkeywords.flag = 0;
        sms_mobile.destroy();
        guanbi();
        monkeywords.destroy();
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
