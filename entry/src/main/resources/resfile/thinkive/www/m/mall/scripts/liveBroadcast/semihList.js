//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        des = require("des"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#liveBroadcast_semihList ";
    var _pageCode = "liveBroadcast/semihList";
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var timer = null;
    var cur_page;
    var isEnd = false;
    var prod_id;
    var id;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        prod_id = appUtils.getPageParam("prod_id");// 产品id
        id = appUtils.getPageParam("id");// 文章id
        //进页面首页清除定时器
        if (timer) {
            clearInterval(timer);
            timer = null;
        };
        vIscroll = { "scroll": null, "_init": false };
        cur_page = 1;
        getData(false);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
        if (prod_id && id) {
            productNews();
        }
       

    }

    //产品消息已读
    function productNews() {
        let param = {
            msg_id: id,//文章id
            prod_id: prod_id// 产品id
        }
        service.reqFun101083(param, (data) => {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            //            let results = data.results[0];
        });
    }



    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击直接进入直播间
        appUtils.preBindEvent($(_pageId + " .list"), ".card", function (e) {
            e.stopPropagation();
            e.preventDefault();
            // let uid = ut.getUserInf().custNo;
            // let name = ut.getUserInf().name;
            // let trueName = tools.format(name);
            // let h5Url = $(this).attr("h5Url");
            // let room_id = $(this).attr("room_id")
            // appUtils.pageInit(_pageCode, "liveBroadcast/broadcastPage", {
            //     h5Url:h5Url + '&uid=' + uid + "&nick=" + trueName,
            //     room_id:room_id
            // });
            let white_label_id = $(this).attr("white_label_id");
            let uid = ut.getUserInf().custNo;
            let name = ut.getUserInf() ? ut.getUserInf().name : '';
            /** 姓名格式化处理 */
            let trueName = tools.format(name);
            let h5Url = $(this).attr("h5Url");
            let room_id = $(this).attr("room_id")
            if (!white_label_id || white_label_id == '') {
                if (!timer || timer == null) {
                    timer = setInterval(() => {
                        //保持心跳
                        service.reqFun112010({}, function (data) { }, { "isShowWait": false });
                    }, 10000);
                }
                return tools.livePageTo(uid, trueName, h5Url, room_id, _pageCode);
            }
            service.reqFun112005({ white_label_id: white_label_id }, function (data) {
                if (!data || !data.results || !data.results[0] || data.results[0].is_white_label_user == '0') return layerUtils.iAlert('抱歉，您不能观看此次直播');
                if (!timer || timer == null) {
                    timer = setInterval(() => {
                        //保持心跳
                        service.reqFun112010({}, function (data) { }, { "isShowWait": false });
                    }, 10000);
                }
                return tools.livePageTo(uid, trueName, h5Url, room_id, _pageCode);
            })

        }, 'click');
    }
    //获取需要渲染的数据
    function getData(flag) {
        let room_type = sessionStorage.room_type;
        let room_name = sessionStorage.msg_title;
        $(_pageId + " .liveBroadcast_semihList_name").text(room_name);
        let requuestData = {
            room_type: room_type,
            current: cur_page + ''
        };
        service.reqFun112004(requuestData, function (data) {
            isEnd = false;
            if (data.error_no == "0") {
                let html = '';
                // if(!data.results[0]) return $(_pageId + " .list").html('<p class="m_padding_10_10 m_center">暂无数据</p>')
                var results = data.results[0] ? data.results[0] : {};
                var totalPages = data.results[0] ? data.results[0].totalPages : 0; //总页数
                let list = results.data ? results.data : [];
                if (!list) list = [];
                list.map(item => {
                    let statusRemark = item.status == 0 ? '未开始' : item.status == 1 ? '直播中' : '回放'
                    let statusClassName = item.status == "0" ? "noStart" : item.status == "1" ? "doing" : "backIng"
                    //是否展示时间
                    let isShowTime = item.status == "0" ? "" : "display_none";
                    //是否展示img
                    let imgShow = item.status == "1" ? "" : "display_none";
                    html += `
                        <ul h5Url="${item.h5Url}" room_id="${item.room_id}" white_label_id="${item.white_label_id}" operationType="1" operationId="card_${item.room_id}" operationName="进入直播间" class="card flex bg-white m_padding_10_10 m_radius m_marginTop_10">
                            <li  class="m_width_40 m_relative listCard">
                                <img class="m_width_100" src="${item.cover}" alt="">
                                <p class="labelIcon m_font_size12 m_center ${statusClassName}">
                                    <img style="width:0.12rem;height:0.12rem" class="${imgShow}" src="images/liveing.png" alt=""/>
                                    <em>${statusRemark}</em>
                                </p>
                                <p class="liveTime ${isShowTime}">${item.startTime.slice(5)}</p>
                            </li>
                            <li class="m_paddingLeft_10 m_relative" style="flex:1;display: flex;flex-flow: column;">
                                <p class="m_bold m_font_size16 m_text_darkgray" style="line-height:0.2rem;">${item.title}</p>
                                <p class="m_font_size12" style="flex: 1;">${item.desc ? item.desc : "暂无简介"}</p>
                                <p class="m_font_size12" style="text-align:right;">直播日期：${item.startTime ? item.startTime : ""}</p>
                            </li>
                        </ul>
                    `
                })
                // $(_pageId + " .list").html(html)
                if (totalPages == cur_page) {
                    isEnd = true;
                    html += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && list.length == 0) {
                    isEnd = true;
                    html = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                // console.log(flag)
                if (flag) {
                    $(_pageId + " .list").append(html);
                } else {
                    $(_pageId + " .list").html(html);
                }
                // $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
                // console.log(results)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    endTime = "";
                    getData(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getData(true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .list").html("");
        isEnd = true;
        cur_page = 1;
        prod_id = "";
        id = "";
        //进页面首页清除定时器
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
    }
    /*
     * 返回
     */
    function pageBack() {
        //进页面首页清除定时器
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
        appUtils.pageBack();
    }
    var liveBroadcast_semihList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_semihList;
});
