//活动 - 新手指引
define(function (require, exports, module) {
    var appUtils = require("appUtils");

    function init() {

    }

    //绑定事件
    function bindPageEvent() {

    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var yinhangimage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = yinhangimage;
});
