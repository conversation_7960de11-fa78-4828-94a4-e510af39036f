<div class="page" id="safety_oldcardinfo" data-pageTitle="原卡号信息" data-refresh="true">
	<div class="pop_layer4 pop_layer" style="display:none"></div>
	<div class="card_rules" style="display:none">
		<div class="rules_box slideup in">
			<h5>换卡规则</h5>
			<div class="rules_list">
				<strong>1.客户需上传电子资料：</strong>
				<p>a.身份证正反面照片或扫描件。</p>
				<p>b.新银行卡正反面照片或扫描件。</p>
				<p>c.一手持新银行卡正面，一手持身份证正面照片（含本人头像，要求身份证信息、银行卡信息清晰）。</p>
				<strong>2.提交换卡申请后，我公司将在两个工作日（节假日顺延）内进行审核。</strong>
				<!--<strong id="str1">3.提交换卡申请后，在收到换卡结果的短信前，不能交易。</strong>-->
				<strong id="str2">3.提交换卡审核成功后，需在页面上进行短信验证码确认，在确认前不能交易。</strong>
			</div>
			<p class="risk_tips">风险提示：换卡存在一定的风险，换卡成功后，取现资金到账银行卡为您变更后新的银行卡，请知晓。</p>
			<div class="grid_02">
				<a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
			</div>
		</div>
	</div>
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">原卡号信息</h1>
				<a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
			</div>
		</header>
		<article>
			<!-- IDENTITY_BOX START -->
			<div class="bank_form">
				<!-- <div class="input_box">

				</div> -->
				<div class="input_box">
					<div class="ui field text">
						<div class="pop_view" id="pop_view"  style="visibility:hidden;z-index: 9999">
							<p id="big_show_bank"></p>
						</div>
						<em style="color:#666666;font-size:14px;">原银行卡号</em>
						<input id="bankCard" disabled  placeholder="" maxlength="19" type="tel" class="ui input" style="margin-left:1.1rem"/>
						<!-- <a href="javascript:void(0);" class="icon_photo"></a> -->
					</div>

					<div class="ui field text">
						<em  style="color:#666666;font-size:14px;">原预留手机号</em>
						<input id="yhmPhone" disabled maxlength="11" type="tel"   placeholder="" class="ui input" style="margin-left:1.1rem"/>
						<!-- <a href="javascript:void(0);">变更</a> -->
						<span class="changeIphone">变更</span>
					</div>

					<div class="ui field text" id="yzmBox">
						<em  style="color:#666666;font-size:14px;">验证码</em>
						<input type="tel"  maxlength="6" id="verificationCode" placeholder=""  class="ui input" style="margin-left:0.7rem"/>
						<a href="javascript:void(0);" class="get_code" id="getYzm">获取验证码</a>
					</div>
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="weihao"   style="display:none" ><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
				</div>
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="bk">下一步</a>
				</div>
			</div>
			<!-- IDENTITY_BOX END -->
		</article>
	</section>
</div>
