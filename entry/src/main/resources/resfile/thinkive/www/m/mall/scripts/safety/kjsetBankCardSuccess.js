// 快捷换卡结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_kjsetBankCardSuccess ";
    var ut = require("../common/userUtil");

    function init() {
        //更新REDIS

    }

    //绑定事件
    function bindPageEvent() {
        //完成
        appUtils.bindEvent($(_pageId + " #wancheng"), function () {
            service.reqFun1100007({}, function (data) {
                if (data.error_no == 0) {
                    ut.saveUserInf(data.results[0]);
                }
                appUtils.pageInit("safety/setBankCardSuccess", "login/userIndexs", {});
            });
        });
    }

    function destroy() {
    }

    var setBankCardSuccess = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = setBankCardSuccess;
});
