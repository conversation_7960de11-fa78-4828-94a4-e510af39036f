<div class="page" id="safety_newcardinfo" data-pageTitle="新卡号信息" data-refresh="true"  style="-webkit-overflow-scrolling : touch;">
	<div class="pop_layer4 pop_layer" style="display:none"></div>
	<div class="card_rules" style="display:none">
		<div class="rules_box slideup in">
			<h5>换卡规则</h5>
			<div class="rules_list">
				<strong>1.客户需上传电子资料：</strong>
				<p>a.身份证正反面照片或扫描件。</p>
				<p>b.新银行卡正反面照片或扫描件。</p>
				<p>c.一手持新银行卡正面，一手持身份证正面照片（含本人头像，要求身份证信息、银行卡信息清晰）。</p>
				<strong>2.提交换卡申请后，我公司将在两个工作日（节假日顺延）内进行审核。</strong>
				<strong id="str1">3.提交换卡申请后，在收到换卡结果的短信前，不能交易。</strong>
<!--				<strong id="str2">3.提交换卡审核成功后，需在页面上进行短信验证码确认，在确认前不能交易。</strong>-->
			</div>
			<p class="risk_tips">风险提示：换卡存在一定的风险，换卡成功后，取现资金到账银行卡为您变更后新的银行卡，请知晓。</p>
			<div class="grid_02">
				<a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
			</div>
		</div>
	</div>
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray" id="icon_back"><span>返回</span></a>
				<h1 class="text_gray text-center">新卡号信息</h1>
				<a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
			</div>
		</header>
		<article>
			<div class="upload_box">
				<h2 class="text-center">上传新银行卡照片<span>（请确保照片清晰）</span></h2>
				<div class="upload_inner">
					<div class="inner">
						<div class="pic_box" id="frontimg" >
							<img src="../../images/card/yhk1.png" alt="">
						</div>
						<div class="gray_mask">
							<div class="upload">
								<a href="javascript:void(0)" id="upfront">上传</a>
							</div>
						</div>
					</div>

					<p class="picture_tips">新银行卡正面照</p>
					<div class="inner">
						<div class="pic_box" id="backimg" >
							<img src="../../images/card/yhk2.png" alt="">
						</div>
						<div class="gray_mask">
							<div class="upload">
								<a href="javascript:void(0)" id="upback">上传</a>
							</div>
						</div>
					</div>
					<p class="picture_tips">新银行卡反面照</p>
				</div>

			</div>
			<!-- IDENTITY_BOX START -->
			<div class="bank_form">
				<div class="input_box">
					<div class="ui field text">
						<div class="pop_view" id="pop_view"  style="visibility:hidden;">
							<p id="big_show_bank"></p>
						</div>
						<em style="color:#666666;font-size:14px;">新银行卡号</em>
						<input id="bankCard" placeholder="" maxlength="19" type="tel" class="ui input" style="margin-left:1.0rem"/>
						<a href="javascript:void(0);" class="icon_photo"></a>
					</div>
					<div class="ui field text">
						<em style="color:#666666;font-size:14px;">新银行名称</em>
						<input type="tel" class="ui input" maxlength="19" placeholder="" id="bankname" style="margin-left:1.0rem;background: transparent" readonly="readonly"/>
						<!-- <strong id="chooseBank"></strong> -->
					</div>
				</div>
				<div class="place" style="display:none">
					<p>银行卡单笔限额：<span id="oneMoney" style="color: #000;font-weight: bold;"></span>  单日限额：<span id="drxe" style="color: #000;font-weight: bold;"></span></p>
				</div>
				<div class="input_box">
					<div class="ui field text">
						<em style="color:#666666;font-size:14px;">新预留手机号</em>
						<input id="yhmPhone" maxlength="11" type="tel" placeholder="" class="ui input" style="margin-left:1rem"/>
					</div>
					<div class="grid_03 grid_02 grid">
						<div class="ui field text rounded input_box2" id="yzmBox">
							<label class="short_label2">验证码</label>
							<input custom_keybord="0" id="verificationCode" type="tel" maxlength="6" class="ui input code_input"
								   placeholder=""/>
							<a id="getYzm" data-state="true">获取验证码</a>
						</div>
					</div>
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="weihao" style="display:none"></dd>
							<dd>
							</dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->

				</div>
				<div class="sure_box" style="display:none">
					<div class="radio_box mt20">
						<div class="ui radio">
							<input type="radio" id="input_radio2" checked="checked">
							<label id="isChecked">我已阅读并同意签署</label>
						</div>
					</div>
					<div id="bank_xy"></div>

				</div>
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="next">下一步</a>
				</div>
				<div class="bank_tips" style="text-align: center;">
					<a href="javascript:void(0)">*支持的银行卡</a>
				</div>
			</div>
			<!-- IDENTITY_BOX END -->

		</article>
	</section>

	<section class="main fixed" data-page="home" style="display: none;">
		<header class="header">
			<div class="header_inner bg_header">
				<a href="javascript:void(0);" class="icon_back icon_gray" id="jiaback"><span>返回</span></a>
				<h1 class="text_gray text-center">支持的银行卡</h1>
			</div>
		</header>
		<article>
			<div class="transaction_record">
				<div class="record_inner">
					<table width="100%" cellpadding="0" cellspacing="0">
						<tr>
							<th>银行</th>
 							<th>单笔限额 </th>
 							<th>当日限额 </th>
							<th>备注</th>
						</tr>

					</table>
					<table width="100%" cellpadding="0" cellspacing="0" id="mainInfo">

					</table>
				</div>

			</div>

		</article>
	</section>
</div>
