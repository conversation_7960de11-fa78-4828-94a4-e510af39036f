// 宝宝记账
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#combProduct_combAccounting ",
        _pageCode = "combProduct/combAccounting",
        tools = require("../common/tools.js");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    require('../../css/iosSelect.css');

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
      
        toggleDetails();
        
    }

    function bindPageEvent() {
        // 收缩
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".tc_box .up", function () {
            tools.recordEventData('4','up','收缩');
            $(this).removeClass("up").addClass("down");
            var broDiv = $(this).parents(".tc_box").children(".tc_box_list");
            broDiv.css({ "display": "none" })
        }, 'click');

        // 展开
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".tc_box .down", function () {
            tools.recordEventData('4','down','展开');
            $(this).removeClass("down").addClass("up");
            var broDiv = $(this).parents(".tc_box").children(".tc_box_list");
            broDiv.css({ "display": "block" })
        }, 'click');
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }



       // 绘制简单条形图函数
       function drawBar(containerId, data) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const total =  Math.abs(data.income) + Math.abs(data.outgo);
            const incomeWidth = (Math.abs(data.income) / total) * 100;
            const expenseWidth = (Math.abs(data.outgo) / total) * 100;

            // 清空容器内容
            container.innerHTML = '';

            // 添加数值标签
            const labelsDiv = document.createElement('div');
            labelsDiv.className = 'bar-labels';

            const incomeLabel = document.createElement('span');
            incomeLabel.className = 'bar-label m_text_red';
            incomeLabel.textContent = `收入：+${Math.abs(data.income).toFixed(2)}`;

            const expenseLabel = document.createElement('span');
            expenseLabel.className = 'bar-label m_text_green';
            expenseLabel.textContent = `支出：-${Math.abs(data.outgo).toFixed(2)}`;

            labelsDiv.appendChild(incomeLabel);
            labelsDiv.appendChild(expenseLabel);

            container.appendChild(labelsDiv);

            // 创建收入条
            const incomeBar = document.createElement('div');
            incomeBar.className = 'bar-income';
            incomeBar.style.width = `${incomeWidth}%`;


            container.appendChild(incomeBar);

            // 创建支出条
            const expenseBar = document.createElement('div');
            expenseBar.className = 'bar-expense';
            expenseBar.style.width = `${expenseWidth}%`;

            // 添加支出数值

            container.appendChild(expenseBar);
        }


        function toggleDetails(isAppendFlag) {               
            
            service.reqFun102207({comb_code:productInfo.fund_code}, async (data) => {
                if (data.error_no == '0') {
                    var results = data.results[0].month_list;
                    
                    $(_pageId + " #income").html(tools.fmoney(data.results[0].summary.income));
                    $(_pageId + " #outgo").html(tools.fmoney(data.results[0].summary.outgo));
                    var html = "";
                    var isFirstEntry = true; // 标记是否是第一条数据
                    for (var key in results) {
                        if (results.hasOwnProperty(key)) {                      
                            var subHtml = "";
                            var list = JSON.parse(results[key].detail);
                            var year = Number(results[key].month_ym.substring(0, 4));
                            var month = Number(results[key].month_ym.substring(4, 6));
                            var year_month = year + "年" + month + "月";
                            if (list && list.length) {
                                list.forEach(function(list) {
                                    //const iconClass = list.amount >= 0 ? 'icon-arrow-up' : 'icon-arrow-down';
                                    let iconClass = list.account_type == "1" ? 'icon_buy' : 'icon_out';
                                    let colorClass = list.account_type == "1"  ? 'm_text_red' : 'm_text_green';
                                    subHtml += `                                   
                                       <div class="hold_detail_tr">
                                            <i class="${iconClass}"></i>
                                            <div style="margin-left: 0.2rem;">                                
                                                <div>${list.remark}</div>
                                                <span>${list.crt_date_format}</span>                                    
                                            </div>
                                            <div class="list_right ${colorClass}">${list.trans_amt}</div>
                                        </div>
                                    `;
                                });
                            }
                            if(results[key].income == "0.00" && results[key].outgo == "0.00"){
                                html +=``;
                            }else{
                                html += `
                                <div class='tc_box'>
                                    <div class="tc_box_item">
                                        <div>
                                            <p class="year_month">${year_month}</p>
                                        </div>
                                        <span class="tc_icon ${isFirstEntry ? 'up' : 'down'}"></span>
                                    </div>
                                    <div class="tc_box_list" style="display:${isFirstEntry ? 'block' : 'none'}">
                                        <div class="bar-container" id="bar${results[key].month_ym}"></div>
                                        <div class="hold_detail" id="transactions${results[key].month_ym}">
                                            ${subHtml}
                                        </div>
                                    </div>
                                </div>
                            `;
                            isFirstEntry = false; // 设置标志位为false，后续不再为第一条  
                            }                                 
                        }
                            
                    }
                    
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(html);
                } else {
                    $(_pageId + " .finance_pro").html(html);
                }
            

                for (var key in results) {
                    if (results.hasOwnProperty(key)) {
                        drawBar(`bar${results[key].month_ym}`, results[key]);
                    }
                }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
            


       

    }



    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .finance_pro").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var combProduct_combAccounting = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = combProduct_combAccounting;
});
