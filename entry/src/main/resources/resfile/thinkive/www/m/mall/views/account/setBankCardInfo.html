<div class="page" id="account_setBankCardInfo" data-pageTitle="绑定银行卡" data-refresh="true">
    <section class="main fixed add_padding" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray" id="getBack"><span>返回</span></a>
                <h1 class="text_gray text-center">绑定银行卡</h1>
            </div>
        </header>
        <article class="bg_blue no_padding">
            <div class="progressBarUser main_flxe vertical_line">
                <ul class="progressBarTop flex vertical_center">
                    <li class="main_flxe vertical_line flex_center">
                        <img class="imgIcon" src="../../images/stepFirst.png" alt="" id="stepFirst"/>
                        <span class="stepName">实名认证</span>
                    </li>
                    
                    <img class="" src="../../images/redLine.png" style="height: 2px;margin-top: -0.35rem;" alt="" id="redLine"/>
                    <li class="main_flxe vertical_line flex_center">
                        <img class="imgIcon" src="../../images/stepSecond.png" alt="" id="noStepSecond"/>
                        <span class="stepName">绑定银行卡</span>
                    </li>
                    <img src="../../images/greyLine.png" alt="" style="height: 2px;margin-top: -0.35rem;" id="greyLine"/>
                    <li class="main_flxe vertical_line flex_center">
                        <img class="imgIcon" src="../../images/noStepThird.png" alt="" id="noStepThird"/>
                        <span class="stepName">设置交易密码</span>
                    </li>
                </ul>
            </div>
            <div class="bank_form">
                <!-- <h3>请输入银行卡信息</h3> -->
                <div class="check_tips slidedown " style="background: none;border:none">
                    <p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息，向银行发送姓名、银行卡信息进行验证。</p>
                </div>
                <div class="input_box">
                    <div class="ui field text">
                        <label class="ui label">持卡人</label><input id="cardPerson" type="text" class="ui input" disabled/>
                    </div>

                    <div class="ui field text">
                        <div class="pop_view" id="pop_view" style="visibility:hidden;">
                            <p id="big_show_bank"></p>
                        </div>
                        <label class="ui label">银行卡号</label><input id="bankCard" maxlength="19" type="tel"
                                                                   class="ui input"/>
                        <a href="javascript:void(0);" class="icon_photo"></a>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">银行名称</label>
                        <div class="ui dropdown" id="bankname" style="margin-left:0.1rem;">
                        </div>
                    </div>
                </div>
                <div class="place">
                    <p>银行卡单笔限额：<span id="oneMoney" style="color: #000;font-weight: bold;"></span> 单日限额：<span id="drxe"
                                                                                                             style="color: #000;font-weight: bold;"></span>
                    </p>
                </div>
                <div class="input_box">
                    <div class="ui field text">
                        <label class="ui label">手机号码</label><input id="yhmPhone" maxlength="11" type="tel"
                                                                   placeholder="请输入银行预留手机号" class="ui input"/>
                    </div>
                    <!-- <div class="grid_03 grid_02 grid">
                        <div class="ui field text rounded input_box2" id="yzmBox">
                            <label class="short_label2 text-right" style="width:0.9rem;padding-right:0.1rem;">验证码</label>
                            <input custom_keybord="0" id="verificationCode" type="tel" maxlength="6" class="ui input code_input"
                                   placeholder=""/>
                            <a id="getYzm" data-state="true">获取验证码</a>
                        </div>
                    </div> -->
                    <div class="finance_det recharge_det">
                        <dl class="bank_limit">
                            <dt></dt>
                            <dd id="weihao" style="display:none"></dd>
                            <dd>
                            </dd>
                        </dl>
                    </div>
                    <!-- 语音验证码 -->
                    <div class="finance_det recharge_det">
                        <dl class="bank_limit">
                            <dt></dt>
                            <dd id="talkCode" style="display: block;">晋金财富将致电您的手机语音告知验证码
                            </dd>
                            <dd>
                            </dd>
                        </dl>
                    </div>
                </div>
                <!-- 语音验证码 -->
<!--                <div class="rule_check">-->
<!--                    <span id="xuanzeqi"><i></i>我已阅读并同意签署</span> <span class="deal_box"></span>-->
<!--                    &lt;!&ndash; <a href="javascript:void(0);">《业务说明书》</a><a href="javascript:void(0);">《定向委托管理协议》</a><a href="javascript:void(0);">《资金结算协议》</a> &ndash;&gt;-->
<!--                </div>-->
                <div class="btn">
                    <a href="javascript:void(0);" class="ui button block rounded" id="bk">下一步</a>
                </div>
                <div class='bank_tips_set m_bold'>支持的银行卡</div>
                <div class="transaction_record">
                    <div class="record_inner">
                        <table width="100%" cellpadding="0" cellspacing="0">
                            <tr>
                                <th>银行</th>
                                <th>单笔限额 </th>
                                <th>当日限额 </th>
                                <th>备注 </th>
                            </tr>
                        </table>
                        <table width="100%" cellpadding="0" cellspacing="0" id="mainInfo">

                        </table>
                    </div>
                </div>
            </div>
            <!-- 协议相关弹窗 -->
            <div class="agreement_layer display_none">
                <div class="agreement_popup in">
                    <div class="agreement_popup_header">
                        <div class="new_close_btn"></div>
                        <h3>相关协议</h3>
                    </div>
                    <ul class="agreement_list flex vertical_line"></ul>
                </div>
            </div>
        </article>
    </section>
</div>
