<!-- 消息列表页面 -->
<div class="page" id="liveBroadcast_newsDetatil" data-pageTitle="详情" data-isSaveDom="false" data-refresh="true"
     style="-webkit-overflow-scrolling : touch;">
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a herf="javascript:void(0)" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center newTitle">详情</h1>
                <a id="share"  href="javascript:void(0)" style="display: none;" class="coustomer-service-icon coustomer-service-icon-share">
					<img src="./images/share.png" />
				</a>
            </div>
        </header>
        <article style="padding-bottom: 0rem;" class="bg_fff m_padding_10_10">
            <div class="header_title">
                <h1 class="color_000 liveBroadcast_newsDetatil_title"></h1>
                <p style="padding:0.06rem 0">
                    <span class="liveBroadcast_newsDetatil_name" style="padding-right: 0.1rem;color:rgb(79, 140, 194)" class="m_paddingRight_10"></span>
                    <span class="liveBroadcast_newsDetatil_time" style="color:#aaa"></span>
                </p>
            </div>
            <div class="list" >

            </div>
        </article>
        <div class="pop_layer" style="display:none;" id="pop_layer_pageShare">
			<div class="share_box slidedown in">
				<a href="javascript:void(0);" class="btn_shareto text-center">分享赢好礼</a>
				<ul>
					<li>
						<a href="javascript:void(0);" id="share_WeChat">
							<em></em>
							<span>微信好友</span>
						</a>
					</li>
					<li>
						<a href="javascript:void(0);" id="share_WeChatFriend">
							<em></em>
							<span>微信朋友圈</span>
						</a>
					</li>
				</ul>
				<a href="javascript:void(0);" class="btn_cancel text-center" id="cancelShare">取消</a>
			</div>
		</div>
    </section>
</div>