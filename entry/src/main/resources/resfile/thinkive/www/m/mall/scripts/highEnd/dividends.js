// 分红详情页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "highEnd/dividends",
        _pageId = "#highEnd_dividends ";
    var productInfo;
    var platform = require("gconfig").platform;
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        getDividendsInfo()
    }
    function getDividendsInfo(){
        service.reqFun102023({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if(!results || !results.length) return $(_pageId + " .card").html('暂无数据')
                let html = ``
                results.map(item=>{
                    // console.log(item)
                    html += `
                        <li class="flex">
                            <span>${tools.ftime(item.create_time)}</span>
                            <span>${tools.ftime(item.divi_pay_date)}</span>
                            <span>${item.cash_atax_rmb}</span>
                        </li>
                    `
                })
                $(_pageId + " .card").html(html)
                // var html = "";
                // if (!results || results.length == 0) {
                //     $(_pageId + " #historyContent .no_data").show();
                // } else {
                //     $(_pageId + " #historyContent .no_data").hide();
                // }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        sessionStorage.transferable = null
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thcontract = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thcontract;
});
