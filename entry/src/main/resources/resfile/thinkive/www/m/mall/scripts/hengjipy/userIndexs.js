// 源晖--首页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            common = require("common"),
            service = require("mobileService"),
            hIscroll = null,
            validatorUtil = require("validatorUtil"),
            _page_code = "hengjipy/userIndexs",
            fingerprint = require('../common/fingerprint.js'),
            _pageId = "#hengjipy_userIndexs ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var platform = gconfig.platform;
    var tools = require("../common/tools"); //是否有新消息
    var ut = require("../common/userUtil");
    var external = require("external");
    var qualifiedInvestorStartAmount = require("gconfig").global.qualifiedInvestorStartAmount;
    var timer,newPopupInfo;
    var channelCode;
    var banner_id;
    var digPageInfo,digUserAuthenticationStatus;
    var userAuthenticationStatus; //用户认证状态
    var btnObj = {
        "1": {
            btnClass: "",
            btnText: "购买"
        },
        "2": {
            btnClass: "",
            btnText: "预约"
        },
        "3": {
            btnClass: "",
            btnText: "敬请期待"
        },
        "4": {
            btnClass: "sold_out",
            btnText: "封闭中"
        },
    }

    async function init() {
        //初始化路径
        clearPath();
        $(_pageId + " .qualifiedInvestorStartAmount").html(qualifiedInvestorStartAmount / 10000 + "万");
        appUtils.setSStorageInfo("routerList", ["hengjipy/userIndexs"]); //首页设置默认路径
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
        // let firstInstall = common.getLocalStorage("account_password");
        // let phoneNum = firstInstall.substring(0, firstInstall.indexOf("_"));
        // phoneNum = phoneNum.substr(0, 3) + "****" + phoneNum.substr(-4)
        // $(_pageId + " .loginDig_phone").text(phoneNum);
        //首次进入页面，获取更新状态
        common.updateVers(_pageId);
        //自动更新，轮询查询
        if (!timer) {
            timer = setInterval(function () {
                common.updateVers(_pageId);
            }, global.updateTime);
        }
        
        // 苹果暗黑模式设置
        // tools.getSystemMode(_pageId);
        channelCode = common.getLocalStorage("download_channel_code");
        // channelCode = 'hjpy'
        banner(channelCode);
        
        getProductList(); //获取晋金宝产品列表
        // getHighProduct();
        if (!ut.getUserInf()) {
            $(_pageId + " #loginOut").text('登录');
            userLoginStatus = '0'   //未登录
            userAuthenticationStatus = '0' //未认证
            return;
        }
        userLoginStatus = '1'   //已登录
        tools.isShowGesture(_pageId);
        $(_pageId + " #loginOut").text('退出');
        if (ut.getUserInf().bankAcct) { //查询绑卡状态
            // investorStatus();
            //查询换卡状态，首页提示
            common.changeCardInter(function () {
            }, false);
        }
        var res = await getUserAuthenticationStatus();
        userAuthenticationStatus = res[0].state //获取用户认证状态
        appUtils.setSStorageInfo("isAuthentication", userAuthenticationStatus);
        // if (!validatorUtil.isEmpty(ut.getUserInf().bankAcct) && (validatorUtil.isEmpty(ut.getUserInf().duty) || validatorUtil.isEmpty(ut.getUserInf().year_icome) || validatorUtil.isEmpty(ut.getUserInf().living_address_province)) && !appUtils.getSStorageInfo("hasShowUploadTip")) {
        //     appUtils.setSStorageInfo("hasShowUploadTip", true);
        //     layerUtils.iConfirm("根据监管要求，需要完善您的信息", function () {
        //     }, function () {
        //         appUtils.pageInit(_page_code, "account/perfectInfo", {});
        //     }, "取消", "确定");
        // }
        // if (!validatorUtil.isEmpty(ut.getUserInf().soonInvalidFlag) && ut.getUserInf().soonInvalidFlag =='1' && !appUtils.getSStorageInfo("hasShowUploadTip")) {
        //     appUtils.setSStorageInfo("hasShowUploadTip", true);
        //     layerUtils.iConfirm("您的风险测评结果即将到期，到期将会影响交易，请尽快完成风险测评", function () {
        //     }, function () {
        //         appUtils.pageInit(_page_code, "safety/riskQuestion", {});;
        //     }, "取消", "去测评");
        // }
        //获取最新弹窗
        getNewPopUp();
        //活动弹框+定向弹窗
        // AppProMsg();
        //首次登陆设置手势密码,首先判断指纹相关
        // setFingerprintPwd()
        //身份证
        id_card_info()
        //获取是否异常开户用户
        // getAbnormalInfo()


    }
    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    //获取最新弹窗 是否紧急弹窗
    function getNewPopUp(){
        service.reqFun101091({}, function (data) {
            let popupInfo = data.results[0];
            if(!popupInfo || popupInfo.is_urgent != "1"){
                if(sessionStorage.pop_up_otification == '1') return;
                if (!validatorUtil.isEmpty(ut.getUserInf().bankAcct) && (validatorUtil.isEmpty(ut.getUserInf().duty) || validatorUtil.isEmpty(ut.getUserInf().year_icome) || validatorUtil.isEmpty(ut.getUserInf().living_address_province)) && !appUtils.getSStorageInfo("hasShowUploadTip")) {
                    appUtils.setSStorageInfo("hasShowUploadTip", true);
                    sessionStorage.pop_up_otification = 1
                    layerUtils.iConfirm("根据监管要求，需要完善您的信息", function () {
                    }, function () {
                        appUtils.pageInit(_page_code, "account/perfectInfo", {});
                    }, "取消", "确定");
                    return;
                }
                if (!validatorUtil.isEmpty(ut.getUserInf().soonInvalidFlag) && ut.getUserInf().soonInvalidFlag == '1' && !appUtils.getSStorageInfo("hasShowUploadTip")) {
                    appUtils.setSStorageInfo("hasShowUploadTip", true);
                    sessionStorage.pop_up_otification = 1
                    layerUtils.iConfirm("您的风险测评结果即将到期，到期将会影响交易，请尽快完成风险测评", function () {
                    }, function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});;
                    }, "取消", "去测评");
                    return;
                }
                // newPopupInfo = null;
                if(popupInfo && popupInfo.pop_id) newPopupInfo = popupInfo
                if(!popupInfo || !popupInfo.pop_id) newPopupInfo = null;
                getAbnormalInfo();
                // AppProMsg();
                // setFingerprintPwd();
                return;
            }  
            newPopupInfo = popupInfo;
            if(sessionStorage.pop_up_otification == 1) return;
            activityShow(_pageId, popupInfo.content, popupInfo.type, popupInfo.url_type, popupInfo.url, popupInfo.activityId?popupInfo.activityId:popupInfo.pop_id, true);
            // sessionStorage.pop_up_otification = 1;
            dropNewPopUp();
        });
    };
    //标记弹过紧急弹窗
    function dropNewPopUp(){
        service.reqFun101090({pop_id:newPopupInfo.pop_id}, function (data) {

        });
    };
    function setFingerprintPwd(){
        // let user = ut.userInfo()
        //新增参数，弹出提示框 去设置指纹次数 最多弹出两次 setFingerprintNum
        let setFingerprintNum = common.getLocalStorage("setFingerprintNum");
        let isLoginUser = common.getLocalStorage("isLoginUser");
        isLoginUser = isLoginUser ? isLoginUser : '0'
        setFingerprintNum = setFingerprintNum?setFingerprintNum*1:0;
        let fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
        if(setFingerprintNum >= 2 || fingerprintPwd_flag == '1' || platform == '2' || platform == '5'){
            //弹窗次数大于两次走原逻辑
            AppProMsg();
            return mobilePhoneControl();
        }
        if(isLoginUser == '1') return AppProMsg();
        if (platform == "0" || platform == '2' || platform == '5') { //浏览器端
            return AppProMsg();
        }
        if (!ut.getUserInf()) { //未登录
            return;
        }
        if (appUtils.getPageParam("bindCard")) { //绑卡流程进入首页
            return AppProMsg();
        }
        setFingerprintNum = setFingerprintNum + 1;
        common.setLocalStorage("setFingerprintNum",setFingerprintNum);
        common.setLocalStorage("isLoginUser",'1');
        sessionStorage.pop_up_otification = "1";
        layerUtils.iConfirm('指纹登录已上线，操作更便捷',  ()=> {
            //点击取消关闭弹窗
        },  ()=> {
            //跳转至指纹弹窗页面
            appUtils.pageInit(_page_code, "safety/fingerprintPwd"); 
        }, "取消", "去开启");
    }
    //获取是否异常开户用户
    function getAbnormalInfo(){
        let is_open_acct_excp = appUtils.getSStorageInfo("is_open_acct_excp")
        if(is_open_acct_excp == "1"){return setFingerprintPwd();}
        service.reqFun101082({}, (data) =>{
            if(data.error_no == '0'){
                var res = data.results
                if(res[0] && res[0].id){
                    let text = res[0].message_text?res[0].message_text:'';
                    let id = res[0].id;
                    sessionStorage.pop_up_otification = '1';
                    layerUtils.iConfirm(text, function () {
                        service.reqFun101029({"id": id}, function () {
                            var userInfo = ut.getUserInf();
                            if (userInfo && userInfo.bankAcct) {
                                return true;
                            }
                            if(userInfo.isJjsCust == "1") { //isJjsCust 1是晋金所用户 0不是
                                appUtils.clearSStorage("idCardInfo");
                                appUtils.clearSStorage("bankAccInfo");
                                appUtils.pageInit(_page_code, "drainage/openAccount", {});
                                return false;
                            }else {
                                appUtils.pageInit(_page_code, "account/setBankCard", {});
                                return false;
                            }
                        });
                    }, function () {
                        service.reqFun101029({"id": id}, function () {
                            getAbnormalInfo()
                        });
                    }, "去绑卡", "取消");
                }else{
                    setFingerprintPwd()
                }
            }else{
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //身份证认证提示
    function id_card_info(){
        //身份证认证状态 0:未完善 1:已完善 2:证件到期3:到期前3个月 4:到期后3个月
        if(!appUtils.getSStorageInfo("user")) return
        let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        let parentHtml = $(_pageId + ' .tip') //主节点
        let tip_mainText = $(_pageId + ' .tip' + ' .tip_mainText') //文案
        switch (perfect_info) {
            case '3':
                parentHtml.css("display",'block')
                break;
            case '2':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display",'block')
                break;
            case '4':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display",'block')
                break;
            default:
                break;
        }
    }
    //去测评
    function pageTo_evaluation(){
        layerUtils.iAlert("您的风险测评已到期，请重新测评",  ()=> {},()=>{
            // console.log('跳转到测评页面')
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        },'','确定')
    }
    //活动弹窗
    function AppProMsg() {
        service.reqFun102018({}, function (data) {
            if (data.error_no == 0) {
                if(!data.results || !data.results[0] || !data.results[0].url) {
                    if(newPopupInfo && newPopupInfo.pop_id){
                        if(sessionStorage.pop_up_otification == '1') return;
                        activityShow(_pageId, newPopupInfo.content, newPopupInfo.type, newPopupInfo.url_type, newPopupInfo.url, newPopupInfo.pop_id, true);
                        dropNewPopUp();
                        return;
                    }  
                }
                for (var i = 0; i < data.results.length; i++) {
                    var result = data.results[i];
                    var content = result.content; //展示内容
                    var type = result.type; //0 只弹一次 1.每天提示 2.定向弹窗  3.在某段时间内，登陆一次弹一次
                    var url_type = result.url_type; //1:內连  0：外链
                    var url = result.url; //url
                    var activityId = result.id; //ID
                    var activity = appUtils.getLStorageInfo("app_activity");
                    if (type == 2 && content) { //定向弹窗
                        var isShowLay = $(_pageId + " .special_pop_layer").css("display");
                        if (isShowLay != "block") {
                            $(_pageId + " #speciaDialog #src_Image img").attr("src", global.oss_url + content);
                            $(_pageId + " #speciaDialog").attr("urls", url);
                            $(_pageId + " #speciaDialog").attr("urlType", url_type);
                            $(_pageId + " .special_pop_layer").show();
                            $(_pageId + " #speciaDialog").show();
                            $(_pageId + " #speciaDialog").attr("activityId", activityId);
                            $(_pageId + " #speciaDialog").attr("pop_id", result.pop_id);
                        }
                    } else if (type == 3) { //活动弹窗（每次进入页面都弹框）
                        if (!appUtils.getSStorageInfo("isShowAppProMsg")) {
                            appUtils.setSStorageInfo("isShowAppProMsg", true);
                            activityShow(_pageId, content, type, url_type, url, activityId, true);
                        }
                    } else {
                        if (activity) {
                            var aid = activity.activeId; //活动ID
                            //活动相同
                            if (aid == activityId) {
                                if (type == 1) {
                                    activityShow(_pageId, content, type, url_type, url, activityId, false);
                                }
                            } else {
                                activityShow(_pageId, content, type, url_type, url, activityId, true);
                            }
                        } else {
                            activityShow(_pageId, content, type, url_type, url, activityId, false);
                        }
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }

    //活动展示-
    function activityShow(_pageId, content, type, url_type, url, activityId, flag) {
        if(sessionStorage.pop_up_otification == "1") return;
        var date = new Date();
        var isNews = isNewDay(date.getFullYear(), date.getMonth() + 1, date.getDate());
        if (flag || isNews) {
            var html = '<div class="activityDialog pop_layer" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;" urls="' + url + '" urltype="' + url_type + '" activityid="' + activityId + '" activetype="' + type + '"><div class="index-popup" style="width: 75%;z-index: 2100;position: static;"><img id="src_Image" class="popup-description" src= ' + global.oss_url + content + ' style="width:100%;height:100%;"><hr><a class="index-popup__btn-close"></a></div></div>'
            $(_pageId + " #activityDialog").append(html);
            sessionStorage.pop_up_otification = '1';

        }
    }

    //判断是否是新的一天
    /* @param oyear  当前年
     * @param omonth 当前月
     * @param oday   当前天
     * return true 新的一天
     * 不对手机时间自行修改到账时间混乱判断
     */
    function isNewDay(oyear, omonth, oday) {
        var activedate = appUtils.getLStorageInfo("app_activedate");
        if (activedate) {
            var ayear = activedate.year;
            var amonth = activedate.month;
            var aday = activedate.day;
            if (oyear == ayear && omonth == amonth && oday == aday) {
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    function bindPageEvent() {
        // //使用手势密码登录
        // appUtils.bindEvent($(_pageId + " .gesture"), function () {
        //     // 获取用户账户信息
        //     let param50043 = {
        //         funcNo: "50043",
        //         key: "account_password"
        //     };
        //     let firstInstall = external.callMessage(param50043);
        //     firstInstall = firstInstall.results[0].value;
        //     let account = firstInstall.substring(0, firstInstall.indexOf("_"));
        //     let setParam = {
        //         "funcNo": "50261",
        //         "moduleName": "mall",
        //         "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
        //         "account": account,
        //         "errorNum": "5",
        //         "isCanBack": "1",
        //         "lockSenconds": "60",
        //         "userImage": ""
        //     };
        //     external.callMessage(setParam);
        // });
        // //跳转登录页面
        // appUtils.bindEvent($(_pageId + " .pageToLogin"), function () {
        //     // common.gestureLogin();
        //     appUtils.pageInit(_page_code, "login/userLogin", {});
        // });
        // //进行指纹登录
        // appUtils.bindEvent($(_pageId + " .setFingerprint"), function () {
        //     tools.fingerprintPwd();
        // });
        // //去掉指纹密码弹窗
        // appUtils.bindEvent($(_pageId + " .loginDig_back"), function () {
        //     // appUtils.pageInit(_page_code, "moreDetails/message", {});
        //     // console.log(111)
        //     $(_pageId + " .loginDig").hide()
        // });
        //关闭更新
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + ' .update_prompt').hide();
        });
        //认证身份证
        appUtils.bindEvent($(_pageId + " .tip .uploadIDCard"),()=>{
            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        })
        //退出登录
        appUtils.bindEvent($(_pageId + " #loginOut"), function () {
            if (ut.getUserInf()) {
                layerUtils.iConfirm("确定退出吗？", function () {
                    service.reqFun1100004({}, function (data) {
                        if (data.error_no == "0") {
                            appUtils.clearSStorage(true);
                            /*$(_pageId + " .highEnd_area").hide();
                            $(_pageId + " .highEnd_list").hide();*/
                            $(_pageId + " #loginOut").text("登录");
                        } else {
                            layerUtils.iMsg(-1, data.error_info);
                        }
                    });
                    getHighProduct("2");
                }, function () {
                });
            } else {
                clearPath();
                let fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
                if(fingerprintPwd_flag == '1'){
                    // tools.fingerprintPwd();
                    // $(_pageId + " .loginDig").show();
                    //初始化指纹弹窗
                    fingerprint.showLoginDig(_pageId,_page_code)
                    //tools.showLoginDig()
                }else{
                    common.gestureLogin();
                }
                // appUtils.pageInit(_page_code, "login/userLogin", {});
            }
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_page_code)
        });
        // 晋金高端列表页 更多入口
        appUtils.bindEvent($(_pageId + " .highEnd_area .more"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "highEnd/fundList");
        }, 'click');

        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            // if (!common.loginInter()) return;
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");

            // 是否是有效内链
            if (file_type == "0" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", url, {});
                return;

            }
            // 是否是有效内外链接
            if (file_type == "1" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                });
                return;
            }
            // 登录授权
            if (file_type == "2" && file_state == "1" && url) {
                if (!common.loginInter()) return
                if (url.indexOf("activity/fundLuckdraw") > -1) {
                    common.setLocalStorage("activityInfo", {
                        activity_id: "2505",
                        cust_no: ut.getUserInf().custNo,
                        channel: "jjcf_app",
                        type: "luckDraw",
                        mobile: ut.getUserInf().mobileWhole,
                    })
                }
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                });
            }
        }, 'click');


        //关闭特定活动弹窗
        appUtils.bindEvent($(_pageId + " #speciaDialog .index-popup__btn-close"), function () {
            //调用接口，使弹框值出现一次，messageType:
            var id = $(_pageId + " #speciaDialog").attr("activityId");
            service.reqFun101029({"id": id}, function () {
                $(_pageId + " #updateLevel").hide();
                $(_pageId + " .updateLevel_layer").hide();
                var user = ut.getUserInf();
                user['messageInfo'] = "";
                ut.saveUserInf(user);
                $(_pageId + " #speciaDialog").hide();
                $(_pageId + " .pop_layer").hide();
            });
        });

        //定向弹窗跳转
        appUtils.bindEvent($(_pageId + " #speciaDialog .popup-description"), function () {
            var url = $(this).parents("#speciaDialog").attr("urls");
            var file_type = $(this).parents("#speciaDialog").attr("urltype");
            //调用接口，使弹框值出现一次，messageType:1 晋金宝升级 2 定向弹窗
            var id = $(_pageId + " #speciaDialog").attr("activityId");
            var pop_id = $(_pageId + " #speciaDialog").attr("pop_id");
            service.reqFun101029({"id": id}, function () {
                $(_pageId + " #updateLevel").hide();
                $(_pageId + " .updateLevel_layer").hide();
                $(_pageId + " #speciaDialog").hide();
                $(_pageId + " .pop_layer").hide();
                service.reqFun101083({msg_id: pop_id}, (data) => {
                    if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }                       
                });   
                //仅图片特殊处理
                if (file_type == "0" && !url) return tools.jump_page(_page_code,newPopupInfo,userAuthenticationStatus,_pageId);            
                // 是否是有效内连接
                if (file_type == "0" && url != "" && url != undefined && url != null) {
                    if (url.indexOf("activity/marketing") > -1){
                        if (url.indexOf("?") > -1) {
                            var skip_url = url.split("?")[0];
                            var parameter = url.split("?")[1];
                            var parameter_arr = parameter.split("&"); //各个参数放到数组里
                            var urlInfo = {};//url的参数信息
                            for (var i = 0; i < parameter_arr.length; i++) {
                                num = parameter_arr[i].indexOf("=");
                                if (num > 0) {
                                    name = parameter_arr[i].substring(0, num);
                                    value = parameter_arr[i].substr(num + 1);
                                    urlInfo[name] = value;
                                }
                            }
                            appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                        }
                        return;
                    }
                    tools.jump_page(_page_code,newPopupInfo,userAuthenticationStatus,_pageId);
                    //是否是新版
                    // if (url.indexOf("template/popupPage") > -1 || url.indexOf("template/holdHeighDetail") > -1 || url.indexOf("template/publicHoldHeightDetail") > -1) {
                    //     if (url.indexOf("?") > -1) {
                    //         var skip_url = url.split("?")[0];
                    //         var parameter = url.split("?")[1];
                    //         var parameter_arr = parameter.split("&"); //各个参数放到数组里
                    //         var urlInfo = {};//url的参数信息
                    //         for (var i = 0; i < parameter_arr.length; i++) {
                    //             num = parameter_arr[i].indexOf("=");
                    //             if (num > 0) {
                    //                 name = parameter_arr[i].substring(0, num);
                    //                 value = parameter_arr[i].substr(num + 1);
                    //                 urlInfo[name] = value;
                    //             }
                    //         }
                    //         if (userAuthenticationStatus == '0' && urlInfo.flag == "1") {
                    //             if (!ut.hasBindCard(_page_code)) return;
                    //             digUserAuthenticationStatus = "1";
                    //             digPageInfo = urlInfo;
                    //             return $(_pageId + ".qualifiedInvestor").show();
                    //         }
                    //         appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                    //     }
                    //     return;
                    // }
                    // appUtils.pageInit(_page_code, url, {});
                }
                // 是否是有效外链
                if (file_type == "1" && url != "" && url != undefined && url != null) {
                    // appUtils.pageInit(_page_code, "guide/advertisement", {
                    //     "url": url,
                    //     "prePage_code": _page_code
                    // });
                    tools.livePageTo(null,null,url,null,_page_code,'晋金财富');
                }

            });
        });

        //关闭活动弹窗
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog .index-popup__btn-close", function () {
            var date = new Date();
            if ($(this).parents(".activityDialog").attr("activetype") !== "3") {
                var activityId = $(this).parents(".activityDialog").attr("activityId");
                var activetype = $(this).parents(".activityDialog").attr("activetype");

                appUtils.setLStorageInfo("app_activity", {
                    "activeId": activityId,
                    "activetype": activetype
                });
                appUtils.setLStorageInfo("app_activedate", {
                    "year": date.getFullYear(),
                    "month": date.getMonth() + 1,
                    "day": date.getDate()
                });
            }
            $(this).parents(".activityDialog").remove();
        }, 'click');

        //活动弹窗跳转
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog .popup-description", function () {
            var date = new Date();
            if ($(this).parents(".activityDialog").attr("activetype") !== "3") {
                var activityId = $(this).parents(".activityDialog").attr("activityId");
                var activetype = $(this).parents(".activityDialog").attr("activetype");
                appUtils.setLStorageInfo("app_activity", {
                    "activeId": activityId,
                    "activetype": activetype
                });
                appUtils.setLStorageInfo("app_activedate", {
                    "year": date.getFullYear(),
                    "month": date.getMonth() + 1,
                    "day": date.getDate()
                });
            }
            var url = $(this).parents(".activityDialog").attr("urls");
            var file_type = $(this).parents(".activityDialog").attr("urltype");
            var activityId = $(this).parents(".activityDialog").attr("activityId");
            $(this).parents(".activityDialog").remove();
            service.reqFun101083({msg_id: activityId}, (data) => {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }                       
            });
            // 是否是有效内连接
            if (file_type == "0" && url) {              
                if (url.indexOf("activity/marketing") > -1){
                    if (url.indexOf("?") > -1) {
                        var skip_url = url.split("?")[0];
                        var parameter = url.split("?")[1];
                        var parameter_arr = parameter.split("&"); //各个参数放到数组里
                        var urlInfo = {};//url的参数信息
                        for (var i = 0; i < parameter_arr.length; i++) {
                            num = parameter_arr[i].indexOf("=");
                            if (num > 0) {
                                name = parameter_arr[i].substring(0, num);
                                value = parameter_arr[i].substr(num + 1);
                                urlInfo[name] = value;
                            }
                        }
                        appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                    }
                    return;
                }
                 appUtils.pageInit(_page_code, url, {});
            }
            // 是否是有效外链
            if (file_type == "1" && url) {
                // appUtils.pageInit(_page_code, "guide/advertisement", {
                //     "url": url,
                //     "prePage_code": _page_code
                // });
                tools.livePageTo(null,null,url,null,_page_code,'晋金财富');
            }

        }, 'click');

        //充值
        appUtils.bindEvent($(_pageId + " #recharge"), function () {
            tools.clickPoint(_pageId,_page_code,'recharge')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if(perfect_info == 4) {
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", ()=> {}, ()=> {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换");
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消");
                    return;
                }else if(invalidFlag == '1'){
                    pageTo_evaluation()
                    return
                }
                appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
            });
        });

        //取现
        appUtils.bindEvent($(_pageId + " #enchashment"), function () {
            tools.clickPoint(_pageId,_page_code,'enchashment')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            if(perfect_info == 4) {
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", ()=> {}, ()=> {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换");
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "thfund/enchashment", {});
            })

        });

        //晋金宝
        appUtils.bindEvent($(_pageId + " #thfund"), function () {
            tools.clickPoint(_pageId,_page_code,'thfund')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "thfund/myProfit", {});
            })
        });

        //我的资产
        appUtils.bindEvent($(_pageId + " #myAsset"), function () {
            tools.clickPoint(_pageId,_page_code,'myAsset')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            common.changeCardInter(function () {
//                appUtils.pageInit(_page_code, "account/myAssets", {});
                appUtils.pageInit(_page_code, "yuanhui/myAccount", {});
            })
        });

        //点击我的
        appUtils.bindEvent($(_pageId + " #wode"), function () {
            if (!common.loginInter()) return;
            if (ut.getUserInf().bankAcct) {
                appUtils.pageInit(_page_code, "yuanhui/myAccount", {});
            } else {
                appUtils.pageInit(_page_code, "account/myAccountNoBind", {});
            }
        });

        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            appUtils.pageInit(_page_code, "moreDetails/more", {});
        });

        //晋金宝
        appUtils.preBindEvent($(_pageId + " .thfund_list"), ".item", function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            let invalidFlag = appUtils.getSStorageInfo("user")?appUtils.getSStorageInfo("user").invalidFlag:null
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消");
                    return;
                }else if((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')){
                    pageTo_evaluation()
                    return
                }
                if(perfect_info == 4) {
                    return layerUtils.iConfirm("您的身份证照片已到期，请先更换", ()=> {}, ()=> {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, "取消", "更换");
                }
                appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
            });
        }, 'click');

        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    // if (digUserAuthenticationStatus == '1') return appUtils.pageInit("login/userIndexs", "template/popupPage");
                    if (sessionStorage.digUserAuthenticationStatus == '1')  {
                        appUtils.pageInit("login/userIndexs", sessionStorage.digUserAuthenticationUrl);
                        sessionStorage.digUserAuthenticationStatus = '';
                        sessionStorage.digUserAuthenticationUrl = '';
                        return;
                    }
                    $(_pageId + ".qualifiedInvestor").hide();
                    //获取产品列表
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    getHighProduct("1");
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            appUtils.clearSStorage("fund_code");
            appUtils.clearSStorage("productInfo");
            $(_pageId + ".qualifiedInvestor").hide();
        });

        //购买高端产品
        appUtils.preBindEvent($(_pageId + " .highEnd_list"), ".item", function (e) {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            if($(this).hasClass("unactive")) {
                appUtils.pageInit(_page_code, "highEnd/fundList");
                return;
            }
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            var userState = JSON.parse($(this).find(".btn").attr("userState"));
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (userState.state != "1") { //未做过合格投资人认证
                $(_pageId + ".qualifiedInvestor").show();
                return;
            }

            tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
        });
    }

    //获取产品列表
    function getProductList() {
        //获取产品列表
        service.reqFun102020({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                //获取晋金宝列表
                getJJBList(results.hqList);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取晋金宝列表
    function getJJBList(data) {
        var html = "";
        for (var i = 0; i < data.length; i++) {
            if (data[i].fund_code == "000709") {
                html = '<div class="item" style="padding:0.15rem 0.1rem">' +
                        "   <span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        '   <p class="title">晋金宝</p>' +
                        '   <div class="balance">' +
                        '       <span>七日年化：</span>' +
                        '       <span class="num ' + tools.addMinusClass(data[i].annu_yield) + '">' + tools.fmoney(data[i].annu_yield) + '</span>' +
                        '       <span>%</span>' +
                        '   </div>' +
                        '   <div class="btn">购买</div>' +
                        '</div>'
            }
        }
        if (data.length == 0) {
            $(_pageId + " .thfund_area").hide();
        } else {
            $(_pageId + " .thfund_area").show();
        }
        $(_pageId + " .thfund_list .item_box").html(html);
    }

    //手势密码 控件
    function mobilePhoneControl() {
        if (platform == "0") { //浏览器端
            return;
        }
        if (!ut.getUserInf()) { //未登录
            return;
        }
        if (appUtils.getPageParam("bindCard")) { //绑卡流程进入首页
            return;
        }
        /**
         * gesture_code_data
         */
         let flag;
         // 获取用户账户信息
         let param50043 = {
             funcNo: "50043",
             key: "account_password"
         };
         let firstInstall = external.callMessage(param50043);
         if (!firstInstall || !firstInstall.results || !firstInstall.results[0] || !firstInstall.results[0].value){
             flag = '0';
         }else{
            firstInstall = firstInstall.results[0].value;
            let account = firstInstall.substring(0, firstInstall.indexOf("_"));
            let param = {
                "funcNo": "50263",
                "account": account
            };
            let data = external.callMessage(param);
            flag = data.results[0].flag;
         }
         //判断是否弹过手势弹窗
         var gesture_code_data = common.getLocalStorage("gesture_code");
         if (gesture_code_data == "1" || flag == '1') { //首页已弹过 && 已经设置过
             return;
         }
         setGesture()
        // //存储首页是否弹过手势密码  1 已弹过
        // common.setLocalStorage("gesture_code", "1");
        // var account = common.getLocalStorage("mobileWhole");
        // appUtils.setSStorageInfo("isCanBack", "0");
        // var setParam = {
        //     "funcNo": "50264", //设置手势密码的设置状态
        //     "moduleName": "mall",
        //     "flag": 1, //flag	String	状态（0：取消手势，1：设置手势，2：修改手势）
        //     "style": "1", //style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
        //     "account": account,
        //     "isCanBack": "1",
        //     "position": "1",
        //     "errorNum": "5"
        // };
        // external.callMessage(setParam);
    }
    function clearPath(){
        appUtils.setSStorageInfo("pageTopUrlInfo","");
        appUtils.setSStorageInfo("skipURL", '');
    }
    function setGesture(){
        //存储首页是否弹过手势密码  1 已弹过
        common.setLocalStorage("gesture_code", "1");
        var account = common.getLocalStorage("mobileWhole");
        appUtils.setSStorageInfo("isCanBack", "0");
        var setParam = {
            "funcNo": "50264", //设置手势密码的设置状态
            "moduleName": "mall",
            "flag": 1, //flag	String	状态（0:取消手势，1:设置手势，2:修改手势）
            "style": "1", //style	String	手势密码的样式类型(0:不显示中心小圆，1:显示）
            "account": account,
            "isCanBack": "1",
            "position": "1",
            "errorNum": "5"
        };
        external.callMessage(setParam);
    }
    // 用户合格投资人弹窗状态(101037)
    function investorStatus() {
        service.reqFun101024({}, function (datas) {
            if (resultVo.error_no == "0") {
                var dataList = resultVo.results[0];
                appUtils.setSStorageInfo("is_open_acct_excp",dataList.is_open_acct_excp)
            } else {
                layerUtils.iAlert(resultVo.error_info);
                layerUtils.iLoading(false);
            }
        });
    }

    function getHighProduct(type,recommend_type) {
        $(_pageId +".highEndDiv").show();
        var param = {
            start: 1,
            count: 20,
            custLabelCnlCode:channelCode,
            recommend_type:recommend_type
        };
        service.reqFun102042(param, function (datas) {
            if (datas.error_no == 0) {
                currentPage = datas.results[0].currentPage;//当前页数
                totalPages = datas.results[0].totalPages;//总页数
                var productList = datas.results[0].data;
                if (currentPage <= totalPages) {
                    getHighEndList(productList, type);
                }
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });


    }

    //获取高端推荐列表
    // type 1 确认做过合格投资着认证 2 未登录状态
    function getHighEndList(data, type) {
        var html = "";

        var fullData = function (userState) {
            for (var i = 0; i < data.length; i++) {
                var transferable = data[i].transferable;//是否可转让
                var str = "";
                if (transferable == "1") {
                    str = "<img src='" + global.oss_url + data[i].url + "' style='width: 14%;margin-left: 0.12rem;margin-top: -0.04rem;'>"
                }
                var recommend_info = data[i].recommend_info;
                var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
                
                if(data[i].prod_sub_type2 == '100'){
                    /**
                     * 产品整合相关
                     */
                    let prod_name_list = data[i].prod_name_list //列表名称
                    let found_rate = data[i].found_rate //成立以来收益
                    let prod_sname = prod_name_list ? prod_name_list : data[i].prod_sname ? data[i].prod_sname : '--' //产品名称
                    let preincomerate = tools.fmoney(data[i].preincomerate) //年化标准
                    let threshold_amount = data[i].threshold_amount / 10000 //起购金额
                    let inrest_term = data[i].inrest_term   //封闭期/锁定期
                    let nav = tools.fmoney(data[i].nav,4)
                    let this_year_rate = data[i].this_year_rate ? data[i].this_year_rate:'--'
                    // let recommend_info = data[i].recommend_info //提示
    
                    //产品整合 是否展示
                    let compare_benchmark_list = data[i].compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                    let fund_rate_list = data[i].fund_rate_list == '1' ? '' : 'display_none' //是否展示成立以来收益
                    let closed_period_list = data[i].closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                    let lock_period_list = data[i].lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                    let this_year_rate_list = data[i].this_year_rate_list == '1' ? '' : 'display_none' //是否展示今年以来收益
                    let nav_list = data[i].nav_list == '1' ? '' : 'display_none' //是否展示净值
                    let recommend_info_list = data[i].recommend_info_list == '1' ? '' : 'display_none'
                    let threshold_amount_list = data[i].threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
            html+= "<div class='template_item'>" +
                        "<div class='item template_box flex'>" +
                        "<div style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</div>" +
                            "<ul class='template_left m_list_color vertical_line'>"+
                                "<li class='title m_font_size16 m_bold'>"+ prod_sname + str +"</li>"+
                                "<li class='flex'>"+
                                    "<p class='m_font_size12 "+ compare_benchmark_list + "'>业绩计提基准(年化)：<span class='m_text_red m_font_size18'>"+ preincomerate +"</span>%</p>" +
                                    "<p class='m_font_size12 "+ fund_rate_list + "'>成立以来收益：<span class='m_text_red m_font_size18'>"+ (found_rate ? tools.fmoney(found_rate) : "--")  +"</span>%</p>" +
                                "</li>" + 
                                "<li class='m_font_size12 m_width_20rem flex wrap'>" +
                                    "<p class='"+ threshold_amount_list +"'>起购：<span>"+ threshold_amount +"万元</span></p>"+
                                    "<p class='"+ closed_period_list +"'>期限：<span>"+ inrest_term +"</span></p>"+
                                    "<p class='"+ lock_period_list +"'>锁定期：<span>"+ inrest_term +"</span></p>"+
                                    "<p class='"+ this_year_rate_list +"'>今年来收益：<span>"+(this_year_rate ? tools.fmoney(this_year_rate) : "--")+"</span>%</p>"+
                                    "<p class='"+ nav_list +"'>最新净值：<span>"+ nav +"</span></p>"+
                                "</li>"+
                                "<li class='m_font_size12 "+ recommend_info_list +"'>"+ recommend_info +"</li>"+
                            "</ul>"+
                            "<ul class='template_right level_center vertical_center'>"+
                                "<li  class='btn vertical_center flex level_center "+tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnClass + " in' userState='" + JSON.stringify(userState)+"'>"+tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnText+"</li>"+
                            "</ul>"+
                        "</div>"
                    "</div>";
                }else if (data[i].prod_sub_type2 == "95") { //政金债
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + str +"</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num " + tools.addMinusClass(data[i].preincomerate) + "'>" + tools.fmoney(data[i].preincomerate) + "</span>%</span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>期限：" + data[i].inrest_term + "</span></p>" +
                        recommend_info_str;
                } else if (data[i].prod_sub_type2 == "94") { // 持有期
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num text_red'>" + tools.fmoney(data[i].interest_rate_min) + '-' + tools.fmoney(data[i].interest_rate_max) + "</span>%</span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>锁定期：" + data[i].inrest_term + "</span></p>" +
                        recommend_info_str;
                } else if(data[i].prod_sub_type2 == "93"){//源晖分销产品
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + "</span></p>" +
                        "<div class='balance'><span>最新净值：<span class='num " + tools.addMinusClass(data[i].nav) + "'>" + tools.fmoney(data[i].nav,4) + "</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>今年来收益：" + (data[i].this_year_rate ? tools.fmoney(data[i].this_year_rate) + "%" : "--")+ "</span><span>成立以来收益：" + (data[i].found_rate ? tools.fmoney(data[i].found_rate) : "--") + "</span>%</p>" +
                        recommend_info_str;
                } else{
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + data[i].prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num " + tools.addMinusClass(data[i].preincomerate) + "'>" + tools.fmoney(data[i].preincomerate) + "</span>%</span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：" + data[i].threshold_amount / 10000 + "万元</span><span>封闭期：" + data[i].inrest_term + "</span></p>" +
                        recommend_info_str;
                }
                if(data[i].prod_sub_type2 != '100') html += "</p><div  class='btn " + tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnClass + " in' userState='" + JSON.stringify(userState) + "'>" + tools.priBtnObj(data[i].buy_state, data[i].prod_sub_type2).btnText + "</div></div></div>";
            }
        }


        var unknowData = function (btnText, userState) {
            for (var i = 0; i < data.length; i++) {
              	let prod_name_list = data[i].prod_name_list //列表名称
                let prod_sname = prod_name_list ? prod_name_list : data[i].prod_sname ? data[i].prod_sname : '--' //产品名称
                var recommend_info = data[i].recommend_info;
                var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
                if (data[i].prod_sub_type2 == "95") { //政金债
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num'>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：--</span><span>期限：--</span></p>" +
                        recommend_info_str;
                } else if (data[i].prod_sub_type2 == "94") { //持有期
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num'>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：--</span><span>锁定期：--</span></p>" +
                        recommend_info_str;
                } else if(data[i].prod_sub_type2 == "93"){//源晖分销产品
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>最新净值：<span class='num '>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>今年来收益：--</span><span>成立以来收益：-- </span></p>" +
                        recommend_info_str;
                } else {
                    html += "<div class='item'><div class='box'>" +
                        "<span style='display: none' class='productInfo'>" + JSON.stringify(data[i]) + "</span>" +
                        "<p class='title'><span>" + prod_sname + "</span></p>" +
                        "<div class='balance'><span>业绩计提基准(年化)：<span class='num'>--</span></span></div>" +
                        "<p class='balance'><span style='width: 1.2rem;display: inline-block'>起购：--</span><span>封闭期：--</span></p>" +
                        recommend_info_str;
                }
                html += "</p><div  class='btn  in' userState='" + JSON.stringify(userState) + "'>" + btnText + "</div></div></div>";
            }
        }

        if (type == "1") {
            fullData({state: "1"});
            $(_pageId + " .highEnd_list").html(html);
            return;
        }

        if (!ut.getUserInf() || type == "2") {
            unknowData("登录可见");
            $(_pageId + " .highEnd_list").html(html);
            return;
        }
        service.reqFun101057({}, function (resultVo) {
            if (resultVo.error_no != "0") {
                layerUtils.iAlert(resultVo.error_info);
                return;
            }
            var sm_white_state = resultVo.results[0].sm_white_state; //101057 sm_white_state 1是白名单 2不是白名单
            service.reqFun101037({}, function (datas) {
                if (datas.error_no != "0") {
                    layerUtils.iAlert(datas.error_info);
                    return;
                }
                var state = datas.results[0].state; //1 已确认
                var userState = {
                    "state": state,
                    "sm_white_state": sm_white_state,
                }
                if (sm_white_state == "1" || state == "1") { //白名单用户 || 合格投资人 展示详细数据
                    fullData(userState);
                } else {
                    unknowData("认证可见", userState);
                }
                $(_pageId + " .highEnd_list").html(html);

            });


        })
    }

    function banner(channelCode) {
        service.reqFun102105({channel_code:channelCode}, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                let recommend_type = results.index_recommend_type
                banner_id = results.banner_id;
                //banner轮播
                tools.guanggao({_pageId: _pageId, group_id: banner_id});
                getHighProduct(null,recommend_type);
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    function destroy() {
        $(_pageId + " .loginDig_phone").text('');
        $(_pageId + " .loginDig").hide();
        $(_pageId + " #loginOut").text('');
        if (hIscroll) {
            hIscroll.destroy();
            hIscroll = null;
        }
        $(_pageId).hide();
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .pop_gold").hide();
        $(_pageId + " .gold_inner #tyj_money").html("");
        $(_pageId + " .tip").hide();
        $(_pageId + " .inclusive_area").hide();
        $(_pageId + " .thfund_area").hide();
        $(_pageId + " .bank_area").hide();
        $(_pageId + " .exclusive").hide();
        $(_pageId + " .highEndDiv").hide();

    }

    var userIndexs = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = userIndexs;
});
