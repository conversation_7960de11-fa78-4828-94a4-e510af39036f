// 产品详情模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        _page_code = "template/publicMarketing",
        _pageId = "#template_publicMarketing ";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var global = gconfig.global;
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];
    // let productInfo;
    var ut = require("../common/userUtil");
    let heighEndProduct //new 一个 vue 实例
    let createdData
    var bottomImg
    var headImg
    let activeClass
    let busi_id
    let fixed_investment_list
    var activeClass1
    var vipBenefitsTaskData, startTime, timer = null;
    //获取产品详情
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '2',
                fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : createdData.fund_code
            }
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "")
            })
        })
    }
    async function init() {
        //页面埋点初始化
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        createdData = appUtils.getSStorageInfo("productInfo"); //产品信息
        tools.initPagePointData({fundCode:createdData.fund_code});
        busi_id = appUtils.getSStorageInfo("busi_id");
        vipBenefitsTaskData = appUtils.getPageParam();
        if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
            startTime = Date.now();
            var readingTime = vipBenefitsTaskData.duration && parseFloat(vipBenefitsTaskData.duration) * 1000;
            if (vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3')) {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, readingTime)
                }
            }
        }
        let html = await setTemplate() //拿到模板数据
        $(".main_marketProduct_public").html(html)   //渲染模板
        activeClass = $(_pageId + " .chartContent").attr("activeClass") ? $(_pageId + " .chartContent").attr("activeClass") : 0;
        activeClass1 = $(_pageId + " .perChart").attr("activeClass1") ? $(_pageId + " .perChart").attr("activeClass1") : 0;
        heighEndProduct = new Vue({
            el: '#main_marketProduct_public',
            data() {
                return {
                    oss_url: global.oss_url,
                    detailsInfo: {},//详情信息
                    bottomImg: null,
                    perList: [],
                    headImg: null,
                    spliceDate: -1100,//默认展示7天数据
                    tips: '', //默认提示为累计收益走势
                    timeOptions: '',//绘制折线图配置
                    activeClass: activeClass,  //高亮时间（7天）
                    activeClass1: activeClass1, // 默认3个月
                    activeClassSecond: '1', //高亮业绩表现，历史净值
                    moreName: "更多",
                    timeList: [  //区间
                        {
                            name: '近1个月'
                        },
                        {
                            name: '近3个月'
                        },
                        {
                            name: '近6个月'
                        },
                        {
                            name: '近一年'
                        },
                        {
                            name: '近三年'
                        },
                    ],
                    timeListNew: [  //区间
                        {
                            name: '近1个月',
                            section: "1"
                        },
                        {
                            name: '近3个月',
                            section: "3"
                        },
                        {
                            name: '近6个月',
                            section: "6"
                        },
                        {
                            name: '近一年',
                            section: "12"
                        },
                    ],
                    timeListMore: [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        {
                            name: '近五年',
                            section: "60",
                            index: "5"
                        },
                        {
                            name: '成立来',
                            section: "",
                            index: "6"
                        },
                    ],
                }
            },
            //视图 渲染前
            created() {
                this.perList = [];
                this.smDetails()
            },
            //渲染完成后
            mounted() {
                var appletEnterImg = global.oss_url + $("#main_marketProduct_public #applet_enter").html();
                $(_pageId + " #main_marketProduct_public #applet_enter_img").attr("src", appletEnterImg);
                let prodType = $(_pageId + " .marketing_desc").attr("prodType");
                appUtils.setSStorageInfo("prodType", prodType);
                this.detailsInfo = createdData
                bottomImg = global.oss_url + $(".main_marketProduct_public #bottom_img_f").html();
                headImg = global.oss_url + $(".main_marketProduct_public #head_img_f").html();

                if (prodType && (prodType == "ninetyDay")) {
                    this.earningsChart1();
                } else if (prodType && (prodType == "ninetyDayYield")) {
                    this.earningsChart1();
                    this.getYield(true);
                }
                $(_pageId + " .main_marketProduct_public #head_img").attr("src", headImg);
                $(_pageId + ".main_marketProduct_public #bottom_img").attr("src", bottomImg);
            },
            //计算属性
            computed: {
                //日期处理
                timeResult: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        if (num1) return tools.ftime(time.substr(num, num1), "-")
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
                threshold_amount_Result: () => {
                    return (threshold_amount) => {
                        if (!threshold_amount) return '--元'
                        threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万元" : tools.fmoney(threshold_amount) + '元';
                        return threshold_amount
                    }
                },
                savePoint: () => {
                    return (value, num) => {
                        if (!value || value == '--') return '--'
                        return (+value).toFixed(num)
                    }
                },
                setNum1: () => {
                    return (str, len) => {
                        if (!str) return '--'
                        return (+str).toFixed(len)
                    }
                }
            },
            //绑定事件
            methods: {
                //获取分享状态
                async getPageShareStatus(is_share) {
                    // tools.recordEventData('1','share','点击分享');
                    let data = {
                        busi_id: createdData.fund_code,
                        page_type: '2',
                        pageId: _pageId,
                        pageCode: _page_code
                    }
                    if(vipBenefitsTaskData && vipBenefitsTaskData.activity_id) data.activity_id = vipBenefitsTaskData.activity_id;
                    tools.isShowShare(data, is_share, vipBenefitsTaskData, startTime);
                },
                //颜色
                text_color(risk_level) {
                    if (!risk_level) return ''
                    if (risk_level.substr(1) >= 4) {
                        return 'm_text_red'
                    } else {
                        return 'm_text_green'
                    }
                },
                //跳转产品详情
                page_details() {
                    // TODO:跳转公募详情
                    // this.detailsInfo.prod_sub_type2 = "200"
                    tools.recordEventData('1','page_details','产品详情');
                    tools.jumpDetailPage(_page_code, this.detailsInfo.prod_sub_type, this.detailsInfo.prod_sub_type2)
                },
                //公募产品详情查询（102043）
                smDetails() {
                    let data = {
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : createdData.fund_code
                    }
                    service.reqFun102113(data, (datas) => {
                        if (datas.error_no == '0') {
                            let res = datas.results[0]
                            appUtils.setSStorageInfo("productInfo", res);
                            appUtils.setSStorageInfo("fund_code", res.fund_code);
                            appUtils.setSStorageInfo("financial_prod_type", res.financial_prod_type);
                            appUtils.setSStorageInfo("prod_sub_type", res.prod_sub_type);
                            res.p_expected_yield = (+res.p_expected_yield).toFixed(2) + '%';
                            if (res.nav_date && res.nav_date != '--') res.nav_date = tools.ftime(res.nav_date).substring(5);
                            if (res.nav != '--') res.nav = (+res.nav).toFixed(4) + "元";//单位净值
                            if (res.scale_fe != '--') res.scale_fe = (res.scale_fe / 100000000).toFixed(2) + '亿元';
                            res.scale_fe_date = tools.ftime(res.scale_fe_date.substr(4, 4));
                            tools.initFundBtn(res, _pageId);
                            this.detailsInfo = res
                            let prod_sname = res.prod_name_list ? res.prod_name_list : res.prod_sname ? res.prod_sname : res.prod_exclusive_name
                            $(_pageId + " .header_inner #title").html(prod_sname);
                            let newprod_sname = res.prod_sname.split("（")[0] + "</br>（" + res.prod_sname.split("（")[1];
                            $(_pageId + " .prod_sname").html(newprod_sname)
                            let is_share = $(_pageId + " .pro_share").attr("is_share")
                            this.getPageShareStatus(is_share)
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                },
                //选择时间高亮 7天
                chooseTimeList7(index) {
                    if (index == this.activeClass) {
                        return;
                    }
                    // console.log(index)
                    tools.recordEventData('1','chooseTimeList' + index,this.timeList(index).name);
                    this.activeClass = index
                    chat.rangeSelector.clickButton(index);
                },
                choosePerList(index) {
                    if (index == this.activeClass1) {
                        return;
                    }
                    tools.recordEventData('1','choosePerList_'+index,'历史盈利概率-' + index+'个月');
                    this.activeClass1 = index
                    this.getYield(false);
                },
                //设置时间为highChart所需时间格式
                datearr(data) {
                    for (var i = 0; i < data.length; i++) {
                        var x = data[i].x.toString();
                        Date.UTC()
                        data[i].x = Date.UTC(x.substring(0, 4), x.substring(4, 6) - 1, x.substring(6, 8));
                    }
                    return data;
                },
                // 显示更多区间
                showMoreSpliceDate() {
                    tools.recordEventData('1','moreSpliceDate' , '显示更多区间');
                    //$(_pageId + " #tooltip").parent().css({ "z-index": "999" })
                    $(_pageId + " .thfundBtn").hide();
                    $(_pageId + " #moreSpliceDate").show();
                },
                cancelMoreSpliceDate() {
                    tools.recordEventData('1','thfundBtn', '隐藏更多区间');
                    $(_pageId + " .thfundBtn").show();
                    $(_pageId + " #moreSpliceDate").hide();
                },
                chooseTrajectoryData(item) {
                    if (item.section == this.activeClass) {
                        return;
                    }
                    tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                    this.activeClass = item.section
                    this.moreName = "更多"
                    this.earningsChart1(); // 获取净值走势
                },
                chooseMoreList(item) {
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    tools.recordEventData('1','chooseMoreList' + item.section,'更多_' + item.name);
                    this.moreName = item.name;
                    this.activeClass = item.section;
                    this.earningsChart1();
                },
                earningsChart1() {
                    section = this.activeClass;
                    service.reqFun102147({ fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : createdData.fund_code, section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainerMarket").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        // results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainerMarket").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: function (params) {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    return `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;">${year}-${month}-${day}</div><div class="chart_tooltip_item" style="margin-top:5px;height:20px;">单位净值：${parseFloat(params[0].value).toFixed(4)}</div>`
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc"
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                splitNumber: 5,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: function (value, index) {
                                        return value.toFixed(4)
                                    }
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '5%',
                                bottom: '8%',
                                top: '18%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                            },
                            series: [],
                        };
                        let series = [];
                        //处理分红标点
                        let markPoint = {data:[]}
                        if(results[0].dividsplit && results[0].dividsplit.length > 0){
                            let dividsplit = JSON.parse(results[0].dividsplit.substring(1, results[0].dividsplit.length - 1));
                            dividsplit.forEach((item,i) =>{
                                // console.log(i,1111)
                                markPoint.data.push(
                                    {
                                        // 在第三个点添加高亮
                                        name: 'redPoint',
                                        coord: [item.xIndex,item.yValue],
                                        value: '',
                                        symbol: 'circle', // 将点的形状设置为圆
                                        symbolSize: 5,   // 设置点的大小
                                        itemStyle: {
                                            color: '#e5443c' // 设置点的颜色为红色
                                        },
                                        // 添加标签
                                        label: i == dividsplit.length - 1 ?label : {}
                                    }
                                )
                            })
                        }
                        results.forEach((item, i) => {
                            item.nav = JSON.parse(item.nav.substring(1, item.nav.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                // name: item.indexName,
                                data: item.nav,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                markPoint:markPoint,
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series

                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = document.getElementById(`chartContainerMarket`);
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                            myChart.off('click');
                            myChart.on('click', {seriesType: 'line'},  function(params) {
                                params.event.event.stopPropagation();
                                setTimeout(function() {
                                    // 你的 click 事件处理逻辑
                                    if(params.name == 'redPoint'){
                                        // 获取 markPoint 的数据
                                        let xIndex = params.data.coord[0];
                                        //获取点击红点的日期
                                        let chooseDate = tools.ftime(results[0].date[xIndex]).split('-');
                                        // 在这里可以处理点击 markPoint 的逻辑，例如显示提示信息等
                                        layerUtils.iAlert(`${chooseDate[1]}月${chooseDate[2]}日分红，分红金额已红利再投或现金分红`);
                                    }
                                }, 100);
                            });
                        }
                        // 监听图表的点击事件
                        // myChart.on('click',  (params)=> {
                        //     // 如果点击的是 markPoint
                        //     if (params.componentType === 'markPoint') {
                        //         // 获取 markPoint 的数据
                        //         // 获取点击分红的index
                        //         let xIndex = params.data.coord[0];
                        //         //获取点击红点的日期
                        //         let chooseDate = tools.ftime(results[0].date[xIndex]).split('-');
                        //         // 在这里可以处理点击 markPoint 的逻辑，例如显示提示信息等
                        //         layerUtils.iAlert(`${chooseDate[1]}月${chooseDate[2]}日分红，分红金额已红利再投或现金分红`);
                        //     }
                        // });
                    })
                },
                addMinusClass(str) {
                    var numClass = "text_red";

                    if (str < 0) {
                        numClass = "text_green";
                    } else if (str > 0) {
                        numClass = "text_red";
                    } else {
                        numClass = "text_gray"
                    }
                    return numClass;
                },
                getYield(isInit) {
                    section = this.activeClass1;
                    var params = {
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : createdData.fund_code,
                        section: section
                    }
                    service.reqFun102128(params, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results;
                            if (!results || results.length == 0) {
                                return;
                            }
                            this.showPer(results, isInit)
                        }
                    });
                },
                showPer(results, isInit) {
                    var yAxis = [];
                    var data = [];
                    var maxValue = parseFloat(results[0].income_per);
                    var max = results[0].income_between;
                    var probabilityNum = 0;
                    var orderNm = $(_pageId + " .perMar").attr("data-order");
                    var sectionProbability = 0;
                    for (let i = 0; i < results.length; i++) {
                        yAxis.push(results[i].income_between);
                        if (results[i].order_nm.substr(0, 1) == "1") {
                            probabilityNum = common.floatAdd(probabilityNum, results[i].income_per);
                        }
                        data.push(parseFloat(results[i].income_per));
                        if (maxValue < results[i].income_per) {
                            max = results[i].income_between;
                            maxValue = parseFloat(results[i].income_per)
                        }
                        if (orderNm > 0 && results[i].order_nm <= orderNm) {
                            sectionProbability = common.floatAdd(sectionProbability, results[i].income_per);
                        }

                    }
                    if (isInit) {
                        $(_pageId + " .perProbability").html(probabilityNum + "%");
                        $(_pageId + " .perNum").html(max);
                        $(_pageId + " .sectionProbability").html(sectionProbability + "%");
                    }
                    $(_pageId + " .perDate").html(tools.ftime(results[0].trans_date));

                    var myChart = echarts.init(document.getElementById('main'));
                    // 指定图表的配置项和数据
                    var option = {

                        tooltip: {
                            trigger: false
                        },
                        legend: {

                        },
                        grid: {
                            left: '2%',
                            right: '32%',
                            top: '0.2%',
                            bottom: '0',
                            containLabel: true //设置自适应画布大小状态为开，也可通过设置left左移实现相同效果。
                        },
                        xAxis: {
                            type: 'value',
                            show: false
                        },
                        yAxis: {
                            type: 'category',
                            data: yAxis,
                            axisLabel: {
                                interval: 0,
                                textStyle: {
                                    color: function (param) {
                                        if (param.substr(0, 1) == "-") {
                                            return '#00c35e'
                                        } else {
                                            return '#F7575E'
                                        }
                                    },
                                    fontWeight: 600
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#F7575E',
                                    fontWeight: 600,
                                    width: 2
                                }
                            },
                            axisTick: {
                                show: false,
                                length: 1,

                            },
                        },
                        series: [{
                            type: 'bar',
                            data: data,
                            barMinHeight: 2,
                            barMaxHeight: 70,
                            barWidth: 20,
                            itemStyle: {
                                color: function (param) {
                                    if (param.name.substr(0, 1) == "-") {
                                        return '#C8CACF'
                                    } else {
                                        return '#E17A74'
                                    }
                                }
                            },
                            label: {
                                show: true,
                                position: 'right',//控制数据显示位置，‘’right‘’为显示在柱状图右侧
                                rich: {
                                    a: {
                                        backgroundColor: "#E17A74",
                                        color: "#fff",
                                        borderRadius: 8,
                                        padding: [2, 3],
                                        fontSize: 10,
                                        align: 'center',

                                    }
                                },
                                formatter: function (params) {
                                    if (params.data == maxValue) {
                                        return params.data + '%' + ' {a| 大概率}'
                                    } else {
                                        return params.data + '%'
                                    }
                                }
                            }
                        }]
                    };
                    myChart.setOption(option);
                    window.onresize = function (ec) { // 监听窗口大小变化
                        myChart.resize()       // 自适应大小变化
                    }
                },

                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                }
            },
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        //查看更多
        appUtils.bindEvent($(_pageId + " .more"), function () {
            if ($(_pageId + " .detailImgBox").height() == 200) {
                tools.recordEventData('1','more','查看更多');
                $(_pageId + " .detailImgBox").css({ height: 'auto' });
                $(_pageId + " .more").text("收起");
            } else {
                tools.recordEventData('1','close_more','收起');
                $(_pageId + " .detailImgBox").css({ height: 200 });
                $(_pageId + " .more").text("查看更多");
            }
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_page_code)
        });
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .header_inner #title").html("");
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        $(_pageId + " .thfundBtn").hide();
        headImg = "";
        bottomImg = "";
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        vipBenefitsTaskData = ""; startTime = "";
        $(_pageId + " #share").removeAttr("has-share");
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3') && !tools.getStayTime(startTime, vipBenefitsTaskData.duration)) {
            var remainTime = tools.getRemainTime(startTime, vipBenefitsTaskData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                startTime = Date.now();
                vipBenefitsTaskData.duration = remainTime / 1000;
                let is_share = $(_pageId + " .pro_share").attr("is_share")
                let data = {
                    busi_id: createdData.fund_code,
                    page_type: '2',
                    pageId: _pageId,
                    pageCode: _page_code
                }
                if(vipBenefitsTaskData && vipBenefitsTaskData.activity_id) data.activity_id = vipBenefitsTaskData.activity_id;
                tools.isShowShare(data, is_share, vipBenefitsTaskData, startTime);
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_page_code, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
