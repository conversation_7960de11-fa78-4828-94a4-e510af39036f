// 晋金高端持有列表
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            common = require("common"),
            tools = require("../common/tools"),
            service = require("mobileService"),
            _page_code = "highEnd/hold",
            _pageId = "#highEnd_hold ";
    var VIscroll = require("vIscroll");
    var vIscroll = {"scroll": null, "_init": false};
    var ut = require("../common/userUtil");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    //cust_fund_type 客户资产类型 0-持有  1-在途 2-待支付
    var _cust_fund_type = "0";
    var currentPage = 1; //当前页数
    var numPerPage = 5; //每页条数
    var totalPages = 1;


    function init() {
        if (ut.getUserInf().custLabelCnlCode == "yh_jjdx") {//源晖及财富标签的用户，展示“待支付”
            $(_pageId + " .tobepaid").show();
            getUnconfirmedProd(false);
        }else{
        	$(_pageId + " .tobepaid").hide();
        	getHoldProd(false)
        }
        $(_pageId + " .visc_pullDown").css("visibility", "hidden");
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
        appUtils.clearSStorage("cancelOrderParam");
        appUtils.clearSStorage("highEndHoldDetail");


        //总资产
        getFundAssetInfo()
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //点击持有或在途或已完成按钮
        appUtils.bindEvent($(_pageId + " .tab_box a"), function () {
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            _cust_fund_type = $(this).attr("cust_fund_type");
            $(_pageId + " .finance_pro .my_finance .purchase_list").html("");
            $(_pageId + " .finance_pro .my_finance .sale_list").html("");
            currentPage = 1;
//            getHoldProd(false);
            if(_cust_fund_type == "2"){
            	getUnconfirmedProd(false);
            }else{
            	getHoldProd(false);
            }
        }, "click");

        //交易记录
        appUtils.bindEvent($(_pageId + " .tradeRecord"), function () {
            appUtils.clearSStorage("trsFundCode");
            appUtils.pageInit(_page_code, "highEnd/transaction");
        }, "click");

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_page_code)
        });
        //私募产品合同
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".contract", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            //查看可转让合同
            if(detail.transferable == '1') sessionStorage.transferable = 1
            appUtils.setSStorageInfo("productInfo", detail);
            appUtils.pageInit(_page_code, "highEnd/contract", {isSign: true});
        }, 'click');
        //撤单
        appUtils.preBindEvent($(_pageId + " .my_finance"), "#cancelOrder", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            if (detail.return_visit_status == "1") return; //1- 回访成功 不允许撤单
            appUtils.setSStorageInfo("cancelOrderParam", detail)
            appUtils.pageInit(_page_code, "highEnd/cancelOrder");
        }, 'click');

        //产品详情
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".pro_box", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).find(".fundInfo").text());
            sessionStorage.due_date = detail.due_date ? detail.due_date : null
            var page = {
                "81": "highEnd/holdForgeDetail", //一创小集合
                "90": "highEnd/holdDetail",
                "91": "highEnd/smallholdDetail",
                "92": "highEnd/fixHoldDetail",
                "93": "yuanhui/holdDetail",
                "94": "highEnd/holdLockDetail",
                "95": "highEnd/holdPolicyDetail",
                "96": "highEnd/holdSolidDetail",
                "97": "highEnd/holdFixDetail",
                "100":"template/holdHeighDetail"
            }
            if ((detail.prod_sub_type2 != "90" && detail.prod_sub_type2 != "91" ) && _cust_fund_type == "1") {
                return;
            } 
            if( _cust_fund_type == "2") return;
            appUtils.setSStorageInfo("fund_code", detail.fund_code);
            appUtils.setSStorageInfo("productInfo", detail);
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(_page_code, page[detail.prod_sub_type2]);
        }, 'click');

        //汇款账号
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".remittance", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            appUtils.setSStorageInfo("productInfo", detail);
            appUtils.pageInit(_page_code, "yuanhui/payRecharge");
        }, 'click');

        //取消订单
        appUtils.preBindEvent($(_pageId + " .my_finance"), ".cancel", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            appUtils.setSStorageInfo("cancelOrderParam", detail)
            appUtils.pageInit(_page_code, "highEnd/cancelOrder");
        }, 'click');


    }


    function getFundAssetInfo() {
        service.reqFun101999({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                $(_pageId + ' .prifundAssets').html(tools.fmoney(results.prifundAssets));
            } else {
                $(_pageId + ' .prifundAssets').html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function getHoldProd(isAppendFlag) {
        $(_pageId + " .new_none").hide();
        var param = {
            cur_page: currentPage,
            num_per_page: numPerPage,
            cust_fund_type: _cust_fund_type,
        };

        service.reqFun101904(param, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                //空数据处理
                results = tools.FormatNull(results);
                var detailParams = results.data;
                if (!detailParams) {
                    //判断是否显示 没有更多数据
                    // show_new_none(_cust_fund_type)
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }
                totalPages = detailParams.totalPages;
                var fundData = detailParams.data;
                if (!fundData || fundData.length == 0) {
                    //判断是否显示 没有更多数据
                    // show_new_none(_cust_fund_type)
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }

                //客户资产类型 0-持有  1-在途
                if (_cust_fund_type == "0") {
                    //持有产品列表展示
                    showHoldFunds(fundData, isAppendFlag);
                } else if (_cust_fund_type == "1") {
                    //在途产品列表展示
                    showTransitFunds(fundData, isAppendFlag);
                }

            } else {
                layerUtils.iAlert(datas.error_info);
            }

            hidePullUp();
        });
    }

    //持有产品列表展示
    function showHoldFunds(fundData, isAppendFlag) {
        //基金信息
        var html = "";
        for (var i = 0; i < fundData.length; i++) {
            //空数据处理
            fundData[i].cust_fund_type = "0";
            var detail = JSON.stringify(fundData[i]);
            //产品名称
            var fundname = fundData[i].fund_sname;
            //资产
            var fundVol = tools.fmoney(fundData[i].fund_vol);
            //本金
            var cost_money = tools.fmoney(fundData[i].cost_money);
            //业绩计提基准（年化）
            var accrualBasis = fundData[i].accrual_basis ? tools.fmoney(fundData[i].accrual_basis) : "--";
            //期限
            var closedLength = fundData[i].closed_length;
            //成立日
            var interestStartDate = fundData[i].interest_start_date;
            //到期日
            var dueDate = fundData[i].due_date;
            //预计下一开放日
            var respect_next_open_date = fundData[i].respect_next_open_date;
            //产品子类型
            var prod_sub_type2 = fundData[i].prod_sub_type2;
            //持有天数
            var betweenDays = fundData[i].betweenDays > 0 ? fundData[i].betweenDays : "--";
            //收益
            // var total_income = fundData[i].total_income;
            //持有期94业绩年化
            var min_interest_rate = tools.fmoney(fundData[i].min_interest_rate);
            var max_interest_rate = tools.fmoney(fundData[i].max_interest_rate);
            var transferable = fundData[i].transferable;//是否可转让
            var str = "";
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + fundData[i].url + "' style='width: 14%;margin-left: 0.12rem;margin-top: -0.04rem;'>"
            }

            //一创小集合金额
            var principal = tools.fmoney(fundData[i].principal);
            //定开私募温馨提示
            var hold_tips = fundData[i].hold_tips;
            var tip_str = hold_tips ? "<dl><dd>温馨提示："+hold_tips+"</dd></dl>" :""

            //100   模板相关参数
            // let fund_vol = tools.fmoney(fundData[i].fund_vol)
            if(prod_sub_type2 == '100'){
                //参数
                // let respect_ack_date = fundData[i].respect_ack_date ? fundData[i].respect_ack_date : '--'
                let due_date = dueDate ? tools.ftime(dueDate.substr(0, 8)) : '--'
                let hold_income = fundData[i].hold_income ? fundData[i].hold_income : '--'
                let day_name = fundData[i].lock_period_unit == '0' ? '年' : fundData[i].lock_period_unit == '1' ? '月' : '天'
                //是否展示
                let assets_is_show = fundData[i].assets_is_show == '1' ? '' : 'display_none' //是否展示资产
                let term_is_show = fundData[i].term_is_show == '1' ? '' : 'display_none'    //是否展示封闭期
                let compare_benchmark_is_show = fundData[i].compare_benchmark_is_show == '1' ? '' : 'display_none' //是否展示年化基准
                let redemption_date_is_show = fundData[i].redemption_date_is_show == '1' ? '' : 'display_none' //是否展示预计可赎回日
                let confirmation_date_is_show = fundData[i].confirmation_date_is_show == '1' ? '' : 'display_none' //是否展示确认日
                let tips_is_show = fundData[i].tips_is_show == '1' ? '' : 'display_none'
                let profit_is_show = fundData[i].profit_is_show == '1' ? '' : 'display_none'
                //持有列表，是否展示
                html +=  "<div class='pro_box item template_box flex'>" +
                    "<div style='display: none' class='fundInfo'>" + detail + "</div>" +
                        "<ul class='template_left vertical_line'>"+
                            "<li class='m_border_bottom_D2E3E8 m_right_icon m_font_size16 m_bold'>"+ fundname + str +"</li>"+
                            "<li class='m_text_color_778590 flex m_paddingTop_10'>"+
                                "<span class='m_font_size12 "+ assets_is_show + "'>资产：<span>"+ fundVol +"元</span></span>" +
                                "<span class='m_font_size12 "+ profit_is_show + "'>收益：<span>"+ hold_income +"元</span></span>" +
                                "<span class='m_font_size12 "+ compare_benchmark_is_show + "'>业绩计提基准(年化)：<span>"+ accrualBasis +"</span>%</span>" +
                            "</li>" + 
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                                "<span class='m_font_size12 "+ redemption_date_is_show +"'>预计可赎回日：<span>"+ due_date +"</span></span>"+
                                "<span class='m_font_size12 "+ term_is_show +"'>期限：<span>"+ closedLength + day_name +"</span></span>"+
                                // "<span class='m_font_size12 "+ confirmation_date_is_show +"'>确认日：<span>"+ respect_ack_date +"</span></span>"+
                            "</li>"+
                            "<li class='m_text_color_778590 m_font_size12 m_paddingTop_10 "+ tips_is_show +"'>"+ hold_tips +"</li>"+
                            "<li style='white-space: nowrap;color:#319ef2' class='m_text_right contract m_font_size12 m_paddingTop_10  "+ '' +"'>产品合同</li>"+
                        "</ul>"+
                    "</div>"
                // html += "<div class='pro_box shadow_box'>" +
                //     "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                //     "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                //     "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + fund_vol + //
                //     "</span></dd><dd class='text-right' style='white-space: nowrap;'>预计确认日：<span>" + tools.ftime(fundData[i].due_date.substr(0, 8)) +
                //     "</span></dd></dl><dl>" +
                //     "<dd>锁定期： <span>" + (closedLength?closedLength:'--') +
                //     "</span></dd><dl><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                //     "</dd></dl></div></div></div>";
            }else if (prod_sub_type2 == "90") { //普通私募
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + cost_money +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                        "%</span></dd></dl><dl><dt></dt><dd>期限： <span>" + closedLength +
                        "</span></dd><dd class='text-right' style='white-space: nowrap;'>确认日：<span>" + tools.ftime(interestStartDate) +
                        "</span></dd></dl><dl><dt></dt><dd>到期日： <span>" + tools.ftime(dueDate) +
                        "</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";

            } else if (prod_sub_type2 == "91") {//小集合
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + fundVol +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                        "%</span></dd></dl><dl><dt></dt><dd>封闭期： <span>" + closedLength +
                        "</span></dd><dd class='text-right' style='white-space: nowrap;'>预计下一开放日：<span>" + tools.ftime(respect_next_open_date.substr(0, 8)) +
                        "</span></dd>" +
                        "</dl><dl><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>"
            } else if (prod_sub_type2 == "92") {//私募定开
             /*   if (fundData[i].cust_hold_vol_list) {
                    //ack_vol为单笔资产  fund_vol为多笔总资产
                    for (var j = 0; j < fundData[i].cust_hold_vol_list.length; j++) {
                        html += "<div class='pro_box shadow_box'>" +
                                "<div class='fundInfo' style='display: none'>" + JSON.stringify($.extend(fundData[i], fundData[i].cust_hold_vol_list[j])) + "</div>" +
                                "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                                "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + tools.fmoney(fundData[i].cust_hold_vol_list[j].fund_vol) +
                                "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                                "%</span></dd></dl><dl><dt></dt><dd>期限： <span>" + closedLength +
                                "</span></dd><dd class='text-right' style='white-space: nowrap;'>确认日：<span>" + tools.ftime(fundData[i].cust_hold_vol_list[j].ack_date) +
                                "</span></dd></dl><dl><dt></dt><dd>预计可赎回日： <span>" + tools.ftime(fundData[i].cust_hold_vol_list[j].due_date) +
                                "</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                                "</dd></dl></div></div></div>";
                    }
                } else {
                    html += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + fundVol +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dt></dt><dd>期限： <span>" + closedLength +
                            "</span></dd><dd class='text-right' style='white-space: nowrap;'>确认日：<span>--" +
                            "</span></dd></dl><dl><dt></dt><dd>预计可赎回日： <span>--" +
                            "</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                            "</dd></dl></div></div></div>";
                }*/
                html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                    "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + principal +
                    "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                    "%</span></dd></dl><dl>" +
                    "<dd style='flex: 2;'>预计可赎回日： <span>" + tools.ftime(fundData[i].due_date.substr(0, 8)) +
                    
                    "</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                    "</dd></dl>"+tip_str+"</div></div></div>";

            } else if (prod_sub_type2 == "93") {
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + tools.fmoney(fundData[i].fund_vol) +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>昨日收益：<span>" + tools.fmoney(fundData[i].yest_income) +
                        "元</span></dd></dl><dl><dt></dt><dd>持仓收益： <span>" + tools.fmoney(fundData[i].hold_income) +
                        "元</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";
            } else if (prod_sub_type2 == "94") {
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>本金：<span>" + tools.fmoney(fundData[i].cost_money) +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准：<span>" + tools.fmoney(min_interest_rate) + '%-' + tools.fmoney(max_interest_rate) +
                        "%</span></dd></dl><dl><dt></dt><dd>锁定期： <span>" + fundData[i].lock_day +
                        "</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";
            } else if (prod_sub_type2 == "95") {
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + fundVol +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                        "%</span></dd></dl><dl><dt></dt><dd>确认日： <span>" + tools.ftime(fundData[i].registerdate) +
                        "</span></dd><dd class='text-right' style='white-space: nowrap;'>持有时间：<span>" + betweenDays +
                        "天</span></dd></dl><dl><dt></dt><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";
            } else if (prod_sub_type2 == "96") { // 申港小集合
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + cost_money +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                        "%</span></dd></dl><dl><dt></dt><dd>成立日： <span>" + tools.ftime(fundData[i].interest_start_date) +
                        "</span></dd><dd class='text-right' style='white-space: nowrap;'>封闭期：<span>" + fundData[i].closed_length +
                        "</span></dd></dl><dl><dt></dt>" +
                        "<dd>到期日： <span>" + tools.ftime(fundData[i].due_date) +
                        "</span></dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";
            } else if (prod_sub_type2 == "97") { //浦信
                html += "<div class='pro_box shadow_box'>" +
                        "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                        "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                        "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + principal +
                        "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                        "%</span></dd></dl><dl>" +
                        "<dd>下一开放日： <span>" + tools.ftime(fundData[i].due_date.substr(0, 8)) +
                        "</span></dd><dl><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                        "</dd></dl></div></div></div>";
            } else if (prod_sub_type2 == "81") { // 一创小集合
                html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                    "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + principal +
                    "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                    "%</span></dd></dl><dl>" +
                    "<dd>预计可赎回日： <span>" + tools.ftime(fundData[i].due_date.substr(0, 8)) +
                    "</span></dd><dl><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" +
                    "</dd></dl></div></div></div>";
            }
        }
        if (isAppendFlag) {
            $(_pageId + " .finance_pro .my_finance .purchase_list").append(html);
        } else {
            $(_pageId + " .finance_pro .my_finance .purchase_list").html(html);
        }
    }

    //在途产品列表展示
    function showTransitFunds(fundData, isAppendFlag) {
        //基金信息
        var saleStr = "";
        var purchaseStr = "";
        for (var i = 0; i < fundData.length; i++) {
            //空数据处理
            fundData[i] = tools.FormatNull(fundData[i]);
            fundData[i].cust_fund_type = "1";
            var detail = JSON.stringify(fundData[i])
            //产品名称
            var fundname = fundData[i].fund_sname;
            //资产
            var fundVol = tools.fmoney(fundData[i].fund_vol);
            //本金
            var cost_money = tools.fmoney(fundData[i].cost_money);
            //业绩计提基准（年化）
            var accrualBasis = fundData[i].accrual_basis ? tools.fmoney(fundData[i].accrual_basis) : "--";
            //期限
            var closedLength = fundData[i].closed_length;
            //成立日
            var interestStartDate = fundData[i].interest_start_date;
            //到期日
            var dueDate = fundData[i].due_date;
            //回访状态
            var return_visit_status = fundData[i].return_visit_status; //0-未回访 1- 回访成功 2-回访失败
            //是否可以撤单
            var is_cancel_order = fundData[i].is_cancel_order; //0：不可撤单；1可撤单

            var cancelOrderBtn = "";
            if (is_cancel_order == "1") {
                cancelOrderBtn = "<div id='cancelOrder' style='font-size: 0.12rem;color:#fff;width:0.4rem;text-align:center;background:#e5443c;'>撤单</div>"
            }
            if (fundData[i].cancel_flag == "0") {
                cancelOrderBtn = "<dl><dd></dd><div id='cancelOrder' style='font-size: 0.12rem;color:#fff;width:0.4rem;text-align:center;background:#e5443c;'>撤单</div></dl>"
            }
            //在途标识
            var transitFlag = fundData[i].transit_flag;
            //产品类型
            var prod_sub_type2 = fundData[i].prod_sub_type2;
            //到账资金
            var toAccountAmt = tools.fmoney(fundData[i].to_account_amt);
            //预计确认日
            var respect_income_date = fundData[i].respect_income_date;
            //预计下一开放日
            var respect_next_open_date = fundData[i].respect_next_open_date;
            // 预计到账时间
            var to_account_date = fundData[i].to_account_date ? fundData[i].to_account_date : "";
            // 到账金额
            var to_account_amt = fundData[i].to_account_amt;
            //赎回份额
            var out_way_vol = fundData[i].out_way_vol;
            //收益
            var total_income = fundData[i].total_income;
            //确认日
            var confirm_date = fundData[i].confirm_date;

            //持有期94业绩年化
            var min_interest_rate = tools.fmoney(fundData[i].min_interest_rate);
            var max_interest_rate = tools.fmoney(fundData[i].max_interest_rate);
            var confirmed_out_way_vol = fundData[i].confirmed_out_way_vol;//卖出在途
            var return_visit_date = fundData[i].return_visit_date;//回访日
            var transferable = fundData[i].transferable;//是否可转让
            var str = "";
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + fundData[i].url + "' style='width: 14%;margin-left: 0.12rem;margin-top: -0.04rem;'>"
            }

            //一创小集合金额
            var principal = tools.fmoney(fundData[i].principal);

            if (transitFlag == "1") { //买入在途
                if(prod_sub_type2 == '100'){
                let assets_is_show = fundData[i].assets_is_show == '1' ? '' : 'display_none' //是否展示资产
                let compare_benchmark_is_show = fundData[i].compare_benchmark_is_show == '1' ? '' : 'display_none' //是否展示年化基准
                let confirmation_date_is_show = fundData[i].confirmation_date_is_show == '1' ? '' : 'display_none' //是否展示确认日
                let lock_period_is_show = fundData[i].lock_period_is_show == '1' ? '' : 'display_none' //是否展示锁定期
                let term_is_show = fundData[i].term_is_show == '1' ? '' : 'display_none'    //是否展示期限
                let day_name = fundData[i].lock_period_unit == '0' ? '年' : fundData[i].lock_period_unit == '1' ? '月' : '天'
                purchaseStr +=     "<div class='pro_box item template_box flex'>" +
                        "<div style='display: none' class='fundInfo'>" + detail + "</div>" +
                        "<ul class='template_left vertical_line'>"+
                            "<li class='m_border_bottom_D2E3E8  m_font_size16 m_bold'>"+ fundname + str +"</li>"+
                            "<li class='m_text_color_778590 flex m_paddingTop_10'>"+
                                "<span class='m_font_size12 "+ assets_is_show + "'>资产：<span>"+ fundVol +"元</span></span>" +
                                "<span class='m_font_size12 "+ compare_benchmark_is_show + "'>业绩计提基准(年化)：<span>"+ accrualBasis +"</span>%</span>" +
                            "</li>" + 
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                                "<span class='m_font_size12 "+ term_is_show +"'>期限：<span>"+ closedLength + day_name +"</span></span>"+
                                "<span class='m_font_size12 "+ lock_period_is_show +"'>锁定期：<span>"+ closedLength + day_name +"</span></span>"+
                                "<span class='m_font_size12 "+ confirmation_date_is_show +"'>确认日：<span>"+ tools.ftime(confirm_date) +"</span></span>"+
                            "</li>"+
                        "</ul>"+
                    "</div>"
                }else if (prod_sub_type2 == "90") { //普通私募
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + cost_money +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dt></dt><dd>期限：<span>" + closedLength +
                            "</span></dd><dd class='text-right' style='white-space: nowrap;'>确认日：<span>" + tools.ftime(interestStartDate) +
                            "</span></dd></dl><dl><dt></dt><dd>到期日：<span>" + tools.ftime(dueDate) +
                            "</span></dd>" + cancelOrderBtn + "</dl></div></div></div>";
                } else if (prod_sub_type2 == "91") { //小集合
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + fundVol +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dt></dt><dd>封闭期：<span>" + closedLength +
                            "</span></dd><dd class='text-right' style='white-space: nowrap;'>预计下一开放日：<span>" + tools.ftime(respect_next_open_date.substr(0, 8)) +
                            "</span></dd></dl><dl><dt></dt><dd>预计" + tools.FormatDateText(respect_income_date.substr(4, 4)) + "确认，开始计算收益" +
                            "</dd></dl></div></div></div>";
                } else if (prod_sub_type2 == "92") { //定开私募
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + principal +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dt></dt><dd>预计确认日：<span>" +  tools.ftime(confirm_date) +
                            "</span></dd></dl></div></div></div>";
                } else if (prod_sub_type2 == "93") { //源晖
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + fundVol +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>确认日期：<span>" + tools.ftime(confirm_date) +
                            "</span></dd></dl>" + cancelOrderBtn + "</div></div></div>";
                } else if (prod_sub_type2 == "94") { //持有期
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + cost_money +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + min_interest_rate + '%-' + max_interest_rate +
                            "%</span></dd></dl><dl><dt></dt><dd>锁定期：<span>" + closedLength +
                            "</span></dd></dl><dl><dd style='white-space: nowrap;'>预计<span>" + tools.ftime(confirm_date) +
                            "确认，" + tools.ftime(return_visit_date) + "开始计算收益</span></dd></dl></div></div></div>";
                } else if (prod_sub_type2 == "95") { //政金债
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 class=''>" + fundname + str + "</h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + cost_money +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dd style='white-space: nowrap;'>预计确认日：<span>" + tools.ftime(interestStartDate) +
                            "</span></dd></dl></div></div></div>";
                } else if (prod_sub_type2 == "96") { // 申港小集合
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "</h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + cost_money +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dd style='white-space: nowrap;'>预计确认日：<span>" + tools.ftime(interestStartDate) +
                            "</span></dd><dd class='text-right' >封闭期：<span>" + closedLength +
                            "</span></dd></dl></div></div></div>";
                } else if (prod_sub_type2 == "81" || prod_sub_type2 == "97") { // 一创小集合
                    purchaseStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "</h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>金额：<span>" + principal +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dd style='white-space: nowrap;'>预计确认日：<span>" + tools.ftime(confirm_date) +
                            "</span></dd></dl></div></div></div>";
                }
            } else { // 卖出在途
                if(prod_sub_type2 == "100"){
                    let vol_is_show = fundData[i].vol_is_show == '1' ? '' : 'display_none'
                    let return_money_is_show = fundData[i].return_money_is_show == '1' ? '' : 'display_none'
                    let return_time_is_show = fundData[i].return_time_is_show == '1' ? '' : 'display_none'
                    saleStr +=     "<div class='pro_box item template_box flex'>" +
                        "<div style='display: none' class='fundInfo'>" + detail + "</div>" +
                        "<ul class='template_left vertical_line'>"+
                            "<li class='m_border_bottom_D2E3E8  m_font_size16 m_bold'>"+ fundname + str +"</li>"+
                            "<li class='m_text_color_778590 flex m_paddingTop_10'>"+
                                "<span class='m_font_size12 "+ vol_is_show + "'>卖出份额：<span>"+ tools.fmoney(fundVol) +"份</span></span>" +
                                "<span class='m_font_size12 "+ return_money_is_show +"'>到账金额：<span>"+ tools.fmoney(to_account_amt) +"元</span></span>"+
                            "</li>" + 
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                                "<span class='m_font_size12 "+ return_time_is_show +"'>预计到账时间：<span>"+ tools.FormatDateText(to_account_date.substr(4, 4)) +"</span></span>"+
                            "</li>"+
                        "</ul>"+
                    "</div>"
                }else if (prod_sub_type2 == "90") { //普通私募
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + cost_money +
                            "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                            "%</span></dd></dl><dl><dt></dt><dd>期限：<span>" + closedLength +
                            "</span></dd><dd class='text-right' style='white-space: nowrap;'>确认日：<span>" + tools.ftime(interestStartDate) +
                            "</span></dd></dl><dl><dt></dt><dd>到期日：<span>" + tools.ftime(dueDate) +
                            "</span></dd><dd class='text-right' style='white-space: nowrap;'>到账资金：<span>" + toAccountAmt + "</span></dd></dl>" +
                            "<dl><dt></dt><dd>资金将在10个工作日内到账</dd></dl></div></div></div>";
                } else if (prod_sub_type2 == "91") { // 小集合
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 class='right_icon'>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>卖出份额：<span>" + tools.fmoney(fundVol) +
                            "</span>份</dd><dd class='text-right' style='white-space: nowrap;'>到账金额：<span>" + tools.fmoney(to_account_amt) +
                            "</span>元</dd></dl><dl><dt></dt><dd>预计" + tools.FormatDateText(to_account_date.substr(4, 4)) + "到账<span>" +
                            "</dd></dl>" +
                            "</div></div></div>";
                } else if (prod_sub_type2 == "93") { // 源晖
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "</h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>卖出份额：<span>" + tools.fmoney(fundVol) +
                            "</span>份</dd><dd class='text-right' style='white-space: nowrap;'>到账金额：<span>" + tools.fmoney(to_account_amt) +
                            "</span>元</dd></dl>" +
                            "</div></div></div>";
                } else if (prod_sub_type2 == "94") { // 锁定期
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "</h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>卖出份额：<span>" + tools.fmoney(confirmed_out_way_vol) +
                            "</span>份</dd><dd class='text-right' style='white-space: nowrap;'>到账金额：<span>" + tools.fmoney(to_account_amt) +
                            "</span>元</dd></dl><dl><dt></dt><dd>预计" + tools.FormatDateText(to_account_date.substr(4, 4)) + "到账<span>" +
                            "</dd></dl>" +
                            "</div></div></div>";
                } else if (prod_sub_type2 == "95") { // 政金债
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4>" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>卖出份额：<span>" + tools.fmoney(confirmed_out_way_vol) +
                            "</span>份</dd><dd class='text-right' style='white-space: nowrap;'>到账金额：<span>" + tools.fmoney(to_account_amt) +
                            "</span>元</dd></dl><dl><dt></dt><dd>预计" + tools.FormatDateText(to_account_date.substr(4, 4)) + "到账<span>" +
                            "</dd></dl>" +
                            "</div></div></div>";
                } else if (prod_sub_type2 == "96") { // 申港小集合
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 >" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'>" +
                            "<div class='pro-info-body'><dl><dd>金额：<span>" + tools.fmoney(to_account_amt) +
                            "元</span></dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + tools.fmoney(accrualBasis) +
                            "%</span></dd></dl><dl><dt></dt><dd>预计" + tools.FormatDateText(to_account_date.substr(4, 4)) + "到账<span>" +
                            "</dd><dd class='text-right'>封闭期：" + closedLength + "<span>" +
                            "</dd></dl>" +
                            "</div></div></div>";
                }else if (prod_sub_type2 == "81" || prod_sub_type2 == "97" || prod_sub_type2 == "92") { // 一创小集合 普信定开 明毅持有期定开
                    saleStr += "<div class='pro_box shadow_box'>" +
                            "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                            "<h4 >" + fundname + str + "<span class='icon'></span></h4>" +
                            "<div class='pro-info-content'>" +
                            "<div class='pro-info-body'><dl><dd>金额：<span>" + tools.fmoney(principal) +
                            "元</span></dd><dd class='text-right' style='white-space: nowrap;'><span> 预计" + tools.FormatDateText(to_account_date.substr(4, 4))+
                            "到账</span></dd></dl>" +
                            "</dd></dl>" +
                            "</div></div></div>";
                }

            }
        }

        if (purchaseStr && (!isAppendFlag || $(_pageId + " .finance_pro .purchaseStar_title").length == 0)) { //购买串为空 && （下拉刷新 || 未加载买入在途标题）
            purchaseStr = "<div class='purchaseStar_title' style='margin:0.02rem;'>买入在途</div>" + purchaseStr;
        }
        if (saleStr && (!isAppendFlag || $(_pageId + " .finance_pro .sale_title").length == 0)) { //卖出串为空 && （下拉刷新 || 未加载卖出在途标题）
            saleStr = "<div class='sale_title' style='margin:0.02rem;'>卖出在途</div>" + saleStr;
        }
        if (isAppendFlag) {
            $(_pageId + " .finance_pro .my_finance .purchase_list").append(purchaseStr);
            $(_pageId + " .finance_pro .my_finance .sale_list").append(saleStr);
        } else {
            $(_pageId + " .finance_pro .my_finance .purchase_list").html(purchaseStr);
            $(_pageId + " .finance_pro .my_finance .sale_list").html(saleStr);
        }
    }


    //待支付订单
    function getUnconfirmedProd(isAppendFlag){
    	service.reqFun102100({}, function (datas) {
    		if (datas.error_no == 0) {
    			 var results = datas.results[0];
                 //空数据处理
                 results = tools.FormatNull(results);

                 var fundData = results.detailParams;
                 if (!fundData || fundData.length == 0) {
                     $(_pageId + " .new_none").show();
                     hidePullUp();
                     getHoldProd(false);
                     return;
                 }
                totalPages = 1;
                 $(_pageId + " .tab_box a").removeClass("current");
                 $(_pageId + " .tab_box a").eq(2).addClass("current");
                 _cust_fund_type = "2";
                 showUnconfirmedFunds(fundData, isAppendFlag);
    		}else {
                layerUtils.iAlert(datas.error_info);
            }

            hidePullUp();
    	})
    }

    //待支付列表展示
    function showUnconfirmedFunds(fundData, isAppendFlag) {
    	$(_pageId + " .new_none").hide();
        //基金信息
        var html = "";

        for (var i = 0; i < fundData.length; i++) {
            //空数据处理
            fundData[i] = tools.FormatNull(fundData[i]);
            fundData[i].cust_fund_type = "2";
            var detail = JSON.stringify(fundData[i]);
            //产品名称
            var fundname = fundData[i].fund_sname;
            //金额
            var app_amt = tools.fmoney(fundData[i].app_amt);
            //时间
            var crt_date = tools.ftime(fundData[i].crt_date);
            var crt_time = tools.ftime(fundData[i].crt_time);

            html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + detail + "</div>" +
                    "<h4>" + fundname + "<em style='float: right;font-size: 0.12rem;color: #e5443c;'>待支付</em></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>订单创建时间：<span>" + crt_date +"&nbsp"+ crt_time +
                    "</span></dd>"+
                    "</dl><dl><dt></dt><dd>订单金额： <span>" + app_amt +
                    "元</span></dd>" +
                    "</dd></dl></div></div><dl><dt><dd class='text-right'><span class='remittance'>汇款账号</span><span class='cancel'>取消订单</span>" +
                    "</dd></dt></dl></div>";
        }

        if (isAppendFlag) {
            $(_pageId + " .finance_pro .my_finance .purchase_list").append(html);
        } else {
            $(_pageId + " .finance_pro .my_finance .purchase_list").html(html);
        }
    }

    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    currentPage = 1;
                    if(_cust_fund_type == "2") {
                        getUnconfirmedProd(false)
                    } else {
                        getHoldProd(false);
                    }
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        currentPage += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        if(_cust_fund_type == "2") {
                            getUnconfirmedProd(true)
                        } else {
                            getHoldProd(true);
                        }
                    } else {
                        $(_pageId + " .new_none").show();
                    }

                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        //$(_pageId + " .visc_pullUp").show();
    }

    function hidePullUp() {
//    	$(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }

    //判断是否显示 没有更多数据
    function show_new_none(cust_fund_type) {
        var acitve = $(_pageId + " .tab_box a.current").attr("cust_fund_type");
        if (acitve == cust_fund_type) {
            $(_pageId + " .new_none").show();
        }
    }


    function destroy() {
        _cust_fund_type = "0";
        // sessionStorage.transferable = null
        totalPages = 1;
        currentPage = 1;
        $(_pageId + ' .prifundAssets').html("--");
        $(_pageId + " .purchase_list").html("");
        $(_pageId + " .sale_list").html("");
        $(_pageId + " .tab_box a").removeClass("current").eq(0).addClass("current");
        $(_pageId + " .tobepaid").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thhold = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thhold;
});
