//开始定投页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        global = gconfig.global,
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#combProduct_startCasting ";
        _pageCode = "combProduct/startCasting";
    var ut = require("../common/userUtil");
    var get_pdf_file = require("../common/StrongHintPdf");
    var monkeywords = require("../common/moneykeywords");   
    var sms_mobile = require("../common/sms_mobile");
    var tools = require("../common/tools");
    var isFirstPurchase = true;
    let weekList = ['', '周一', '周二', '周三', '周四', '周五'] //每周定投日期
    var start_player = '';
    var investcycle = '2';  //每月
    var investdate = '1'; //哪天
    var firstText = ""; //每月/每周/每两周
    var info,productInfo,secoundText,userInfo;
    let pdfParam = {};//获取PDF信息
    let payMethod;
    let bankNameRemark; //默认自己的卡卡
    let bankInfo;//银行卡信息
    let threshold_amount;//起购金额
    let addition_amt;//追加
    let step_amt;//递增
    let _available_vol;//可用余额
    let fixed_invest_min;//起投
    var buyflag; //风险等级是否匹配  1 不匹配
    var bank_serial_no;//银行签约流水
    var newstr,str; //
    var purchase_state; //购买按钮状态
	var startBuyMoney;//起购金额判断值
    var isSeriesComb;//是否为投顾系列产品
    var pay_mode,pay_modelimit
    function init() {
        userInfo = ut.getUserInf();
        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
        bankNameRemark = userInfo.bankName + '(尾号' + bankAcct + ')';
        $(_pageId + " #inputspanidAmt span").addClass("unable").css({ color: "#aaaaaa" });//默认输入框失去焦点
        $(_pageId + " #inputspanidInvestAmt span").addClass("unable").css({ color: "#aaaaaa" });//默认输入框失去焦点
        //初始化参数配置
        info = appUtils.getPageParam() ? appUtils.getPageParam() : {}; //获取缓存参数
        productInfo = appUtils.getSStorageInfo("productInfo");
        productInfo.fund_code = productInfo.comb_code;
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        //是否为投顾系列产品
        isSeriesComb = appUtils.getSStorageInfo("isSeriesComb");
        appUtils.setSStorageInfo("productInfo",productInfo);
        pdfParam = {
            agreement_type: 'prod',
            agreement_sub_type: '1',
            fund_code: productInfo.comb_code,
            fixed_invest_flag: '1'
        }
        common.systemKeybord(); // 解禁系统键盘
        //初始化页面数据
        setData(productInfo,info)

    }
    //初始化
    async function setData(productInfo,info){
        //判断是否首投
        let res = await reqFun106058(); //同步接口去是否首次购买
        // console.log(res);
        isFirstPurchase = res;
        purchase_state = productInfo.purchase_state; //产品状态
        let purchase_state_class = purchase_state == "1" ? "" : "no_active";
        $(_pageId + " .thfundBtn .buy").addClass(purchase_state_class);
        $(_pageId + " .startCasting_title").text(productInfo.comb_sname);
        $(_pageId + " .startCasting_remark").html('本策略由' + productInfo.mechanism + '提供');//remark
        if(productInfo.cover_path && productInfo.video_path){
            $(_pageId + " .bg").attr("src",global.oss_url + productInfo.cover_path);
            $(_pageId + " .startCasting_banner").show();
        }else{
            $(_pageId + " .startCasting_banner").hide();
        }
        var result = productInfo;
        threshold_amount = result.page_first_per_min ? result.page_first_per_min : result.first_per_min; // 起购
		startBuyMoney = threshold_amount;
        addition_amt = result.con_per_min; // 追加
        step_amt = result.step_unit; // 递增
        // let money1 = result.first_per_min*1;
        // let money2 = result.fixed_invest_min*1;
        fixed_invest_min = result.fixed_invest_min; // 起投
        product_risk_level = result.comb_risk_level;
        /**买入渲染 */
        str = "";
        str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
        if (step_amt && step_amt > 0) {
            str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                tools.fmoney(step_amt + ''))
                + "元递增"
        }
        /**定投渲染 */
        // newstr += (fixed_invest_min >= 10000 ? (fixed_invest_min / 10000 + "万") : tools.fmoney(fixed_invest_min)) + '元起投';
        // if (step_amt && step_amt > 0) {
        //     newstr += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
        //         tools.fmoney(step_amt + ''))
        //         + "元递增"
        // }
        newstr = "";
        if (threshold_amount && isFirstPurchase) {
            threshold_amount = result.first_per_min;
            newstr += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
        } else if (fixed_invest_min && !isFirstPurchase) {
            newstr += (fixed_invest_min >= 10000 ? (fixed_invest_min / 10000 + "万") : tools.fmoney(fixed_invest_min)) + '元起购';
        }
        if (step_amt && step_amt > 0) {
            newstr += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                tools.fmoney(step_amt + ''))
                + "元递增"
        }
        // //渲染试算金额
        if(info.amt){
            $(_pageId + " #inputspanidAmt span").text(info.amt).attr("text", info.amt);
            $(_pageId + " #inputspanidAmt span").css({ color: "#000" });//默认输入框失去焦点
            $(_pageId + " #czje_Amt").val(info.amt);//默认输入框失去焦点
        }else{
            $(_pageId + " #inputspanidAmt span").css({ color: "#aaa" });//默认输入框失去焦点
            $(_pageId + " #inputspanidAmt span").text(str ? str : '请输入首投金额');
            $(_pageId + " #inputspanidAmt span").attr("text", str ? str : '请输入首投金额');
        }
        if(info.invest_amt){
            $(_pageId + " #czje_InvestAmt").val(info.invest_amt);//默认输入框失去焦点
            $(_pageId + " #inputspanidInvestAmt span").css({ color: "#000" });//默认输入框失去焦点
            $(_pageId + " #inputspanidInvestAmt span").text(info.invest_amt).attr("text", info.invest_amt);
        }else{
            $(_pageId + " #inputspanidInvestAmt span").css({ color: "#aaa" });//默认输入框失去焦点
            $(_pageId + " #inputspanidInvestAmt span").text(newstr ? newstr : '请输入定投金额');
            $(_pageId + " #inputspanidInvestAmt span").attr("text", newstr ? newstr : '请输入定投金额');
        }
        if(isSeriesComb == '1'){
            //特殊子女产品
            $(_pageId + " .nick_name").val(info.nick_name);
            $(_pageId + " #nick_name").show();
        }else{
            $(_pageId + " #nick_name").hide();
        }
        //默认使用晋金宝
        payMethod = '0';
        let pageInputCasting = appUtils.getSStorageInfo("pageInputCasting") ? appUtils.getSStorageInfo("pageInputCasting") : {};
        investdate = pageInputCasting.investdate ? pageInputCasting.investdate : '1'
        //渲染默认选中日期
        $(_pageId + " .newData").text('每月'+ investdate +'日')
        //渲染日期
        firstText = '每月';
        investcycle = '2';
        investdate = '1';
        secoundText = '1日';
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        setChooseTime(0);
        //获取下一扣款日
        getNextTime(investcycle, investdate);
        //渲染银行卡
        setBankInfo();
        //可用份额
        reqFun101901();
        tools.whiteList(_pageId);//白名单
        sms_mobile.init(_pageId);//初始化发短信
        //PDF相关，走公共方法
        is_show_paf()
        //比较风险等级
        compareRiskLevel();
        //回填缓存的输入内容
        pageInfo();
    }
    //页面数据初始化
    function initialization() {
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        $(_pageId + " #amount_enough").removeClass('active');
        firstText = '每月';
        secoundText = '1日';
        payMethod = '0';
    }
    function pageInfo(){
        let pageInputCasting = appUtils.getSStorageInfo("pageInputCasting") ? appUtils.getSStorageInfo("pageInputCasting") : {};
        // console.log(secoundText,111)
        if(!pageInputCasting || pageInputCasting == {}) return; //无数据
        if(pageInputCasting.nick_name) $(_pageId + " .nick_name").val(pageInputCasting.nick_name);
        if(pageInputCasting.trans_amt){
            $(_pageId + ` #czje_Amt`).val(tools.fmoney(pageInputCasting.trans_amt).replace(/,/g, ""));
            $(_pageId + ` #inputspanidAmt span`).html(tools.fmoney(pageInputCasting.trans_amt));
            $(_pageId + " #inputspanidAmt span").css({ color: "#000" });//默认输入框失去焦点
        } 
        if(pageInputCasting.invest_money){
            $(_pageId + ` #czje_InvestAmt`).val(tools.fmoney(pageInputCasting.invest_money).replace(/,/g, ""));
            $(_pageId + ` #inputspanidInvestAmt span`).html(tools.fmoney(pageInputCasting.invest_money));
            $(_pageId + " #inputspanidInvestAmt span").css({ color: "#000" });//默认输入框失去焦点
        }
    }
    //比较风险等级
    function compareRiskLevel() {
        var userRiskLevel = userInfo.riskLevel;
        userRiskLevel = +(userRiskLevel.substr(-1))
        // product_risk_level = (+product_risk_level.substr(-1));
        // if (product_risk_level == 1) return;
        // console.log(userRiskLevel,product_risk_level)
        if (product_risk_level > userRiskLevel) {
            buyflag = "1";
        }else{
            buyflag = '0'
        }
    }
    //输入首投/每月定投总额弹出数字键盘
    function inputSpanEvent(id,event) {
        event.stopPropagation();
        $(_pageId + ` #czje_${id}`).val('');
        //键盘事件
        moneyboardEvent(id);
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "combProduct_startCasting";
        param["eleId"] = `czje_${id}`;
        param["doneLable"] = "确定";
        param["keyboardType"] = "3";
        //新增留白 移动滚动条
        $(_pageId + " article").addClass("combProduct_startCasting_article");
        $(_pageId + " .combProduct_startCasting_article").scrollTop(300);
        require("external").callMessage(param);
    }
    // 金额键盘事件
    function moneyboardEvent(id) {
        _available_vol = _available_vol*1;
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () { // 键盘完成
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var moneys = curVal.replace(/,/g, "");
                
                if (id == "Amt") {
                    if(_available_vol < curVal){
                        $(_pageId + " .rechargeNow").show();
                    }else{
                        $(_pageId + " .rechargeNow").hide();
                    }
                    if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                        return
                    }
                } else if (id == "") {
                    if (tools.isMatchAddAmt(moneys, invest_amount, step_amt)) {
                        return
                    }
                }
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text(str ? str : '请输入首投金额');
                        $(_pageId + " #inputspanidAmt span").attr("text", str ? str : '请输入首投金额');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text(newstr ? newstr : '请输入定投金额');
                        $(_pageId + " #inputspanidInvestAmt span").attr("text", newstr ? newstr : '请输入定投金额');
                    }
                }
                $(_pageId + ` #czje_${id}`).val(moneys);
                $(_pageId + " article").removeClass("combProduct_startCasting_article");
            },
            inputcallback: function () {// 键盘输入
                // 处理单个选择产品金额变化
                var curVal = $(_pageId + ` #czje_${id}`).val();
                curVal = curVal.replace(/,/g, "");
                if (id == "Amt"){
                    if(_available_vol < curVal){
                        $(_pageId + " .rechargeNow").show();
                    }else{
                        $(_pageId + " .rechargeNow").hide();
                    }
                }else{
                    if(pay_mode == '2' && payMethod == '1' && curVal && (parseFloat(curVal) > parseFloat(pay_modelimit))){
                        $(_pageId + ` #czje_${id}`).val('');
                        layerUtils.iAlert(pay_modelimitInfo);
                    }
                }
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + ` #czje_${id}`).val(curVal.substring(0, curVal.length - 1));
                }

            }, // 键盘隐藏
            keyBoardHide: function () {
                $(_pageId + " article").removeClass("combProduct_startCasting_article");
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                curVal = curVal.replace(/,/g, "");
                curVal = curVal*1;
                if (id == "Amt"){
                    if(_available_vol < curVal){
                        $(_pageId + " .rechargeNow").show();
                    }else{
                        $(_pageId + " .rechargeNow").hide();
                    }
                }
                var moneys = curVal;
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text(str ? str : '请输入首投金额');
                        $(_pageId + " #inputspanidAmt span").attr("text", str ? str : '请输入首投金额');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text(newstr ? newstr : '请输入定投金额');
                        $(_pageId + " #inputspanidInvestAmt span").attr("text", newstr ? newstr : '请输入定投金额');
                    }
                }
                if (moneys) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(moneys));
                }
            },
        })
    }
    //查询产品是否首次购买
    async function reqFun106058() {
        var param = {
            fund_code: productInfo.comb_code,
        }
        return new Promise((resolve, reject) => {
            service.reqFun106058(param, function (data) {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    if (results.is_can_add == 0) {
                        isFirstPurchase = false;    //非首次
                        resolve(isFirstPurchase);
                    } else {
                        isFirstPurchase = true;     //首次
                        resolve(isFirstPurchase);
                    }
                } else {
                    isFirstPurchase = true;
                    resolve(isFirstPurchase);
                    layerUtils.iAlert(data.error_info);
                }
                //获取产品详情
                // getFundInfo();
            }, { isLastReq: false })
        })
        
    }
    //PDF相关，走公共方法
    function is_show_paf() {
        get_pdf_file.get_file(productInfo.comb_code, _pageId, "7")
    }
    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            //可用份额
            _available_vol = results.available_vol*1;
            let buymoney = $(_pageId + " #czje_Amt").val();
            buymoney = buymoney ? (buymoney.replace(/,/g, "")) : '';
            if(buymoney && _available_vol < buymoney){
                $(_pageId + " .rechargeNow").show();
            }else{
                $(_pageId + " .rechargeNow").hide();
            }
            $(_pageId + " .save_available").text(tools.fmoney(_available_vol + ""));
            var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
            $(_pageId + " .payMethodRemark").html(html);
            $(_pageId + " .pay_bank").html(html);
        })
    }
    //获取银行卡限额
    function setBankInfo() {
        get_single_limit()
        let imgUrl = "images/bank_" + userInfo.bankCode + ".png";
        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
        $(_pageId + " .bankImg").css({ "background": "url('" + imgUrl + "') no-repeat" });
        $(_pageId + " .backName").html(userInfo.bankName + '(尾号' + bankAcct + ')');
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () { }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    //获取银行卡限额
    function get_single_limit() {
        let params = {
            acct_no:userInfo.bankAcct,
            bank_code:userInfo.bankCode,
            bank_reserved_mobile:userInfo.bankReservedMobile
        }
        service.reqFun101084(params, function (data) {
            if (data.results && data.results.length > 0) {
                var result = data.results[0];
                bankInfo = result;
                let is_bank_fixed_investment = result.is_bank_fixed_investment;//是否支持银行卡定投
                let fixed_investment_priority = result.fixed_investment_priority;//优先级判断 0 晋金宝 1 银行卡
                let is_exist = result.is_exist; //是否签约 0未签约 1已签约
                bank_state = result.bank_state; //0银行维护中，1银行正常
                pay_mode= result.pay_mode;  //支付模式， 0 签约+支付 1 单独签约
                pay_modelimit= result.pay_sendsms_amt;  // 单独签约模式下发短信限额
                pay_modelimitInfo = `定投金额超过${pay_modelimit*1}元，请您使用晋金宝定投。`
                let backData = appUtils.getSStorageInfo("backData");    //缓存中的页面数据
                if(is_bank_fixed_investment == '0'){    //不支持
                    pdfParam.bankcard_fixedinvest_flag = '0'
                    payMethod = '0'
                    tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .payRemark").text("晋金宝")
                    $(_pageId + " .chooseBank").hide();
                    $(_pageId + " .grid_03").hide();
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .chooseJjb").removeClass('borderActive');
                    return;
                }else{
                    // $(_pageId + " .chooseJjb").addClass('borderActive');
                    // $(_pageId + " .chooseBank").show();
                    // pdfParam.bankcard_fixedinvest_flag = '1'
                    // tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank").show();
                    $(_pageId + " .grid_03").show();
                    $(_pageId + " .chooseJjb").addClass('borderActive');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                };
                if(fixed_investment_priority == '0'){   //优先晋金宝
                    pdfParam.bankcard_fixedinvest_flag = '0';
                    payMethod = '0';
                    tools.getPdf(pdfParam);
                    $(_pageId + " .payRemark").text("晋金宝")
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .grid_03").hide();
                }else{
                    if(is_exist == '0'){    //未签约，展示验证码
                        $(_pageId + " .chooseBank").addClass('borderActive');
                        $(_pageId + " .grid_03").show();
                    }else{
                        $(_pageId + " .grid_03").hide();
                    }
                    $(_pageId + " .payRemark").text(bankNameRemark)
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    let data = pdfParam;
                    
                    // payMethod = '1'; 
                    if(!backData || backData.backShow != "1") payMethod = '1';
                    data.bankcard_fixedinvest_flag = '1'
                    tools.getPdf(data);
                }
                if(bank_state == '0'){  //维护中
                    bank_state = '0';
                    pdfParam.bankcard_fixedinvest_flag = '0'
                    payMethod = '0'
                    tools.getPdf(pdfParam); //获取协议
                    
                    $(_pageId + " .payRemark").text('晋金宝');
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .chooseBank").addClass('color_ccc')
                    $(_pageId + " .single_limit").addClass('color_ccc')
                    $(_pageId + " .day_limit").addClass('color_ccc')
                    
                    
                }else{
                    tools.getPdf(pdfParam);
                    bank_state = '1';
                    // payMethod = '1';
                    // if(!backData || backData.backShow != "1") payMethod = '1';
                    $(_pageId + " .chooseBank").removeClass('color_ccc')
                    $(_pageId + " .single_limit").removeClass('color_ccc')
                    $(_pageId + " .day_limit").removeClass('color_ccc')
                }
                
                single_limit = result.single_limit;
                day_limit = result.day_limit;
                if (single_limit) {
                    if (single_limit < 0) {
                        $(_pageId + " .single_limit").html("不限");
                    } else {
                        if (single_limit.length > 4) {
                            $(_pageId + " .single_limit").html(single_limit / 10000 + "万元");
                        } else {
                            $(_pageId + " .single_limit").html(single_limit + "元");
                        }
                    }
                }
                if (day_limit) {
                    if (day_limit < 0) {
                        $(_pageId + " .day_limit").html("不限");
                    } else {
                        if (day_limit.length > 4) {
                            $(_pageId + " .day_limit").html(day_limit / 10000 + "万元");
                        } else {
                            $(_pageId + " .day_limit").html(day_limit + "元");
                        }
                    }
                }
                if(backData && backData.backShow == '1'){
                    if(backData.payMethod == '0'){
                        $(_pageId + " .chooseBank .img").removeClass('active');
                        $(_pageId + " .chooseJjb .img").addClass('active');
                        $(_pageId + " .grid_03").hide();
                        payMethod = '0'
                    }else if(backData.payMethod == '1'){
                        $(_pageId + " .chooseJjb .img").removeClass('active');
                        $(_pageId + " .chooseBank .img").addClass('active');
                        payMethod = '1'
                        if(backData.is_exist == '0'){
                            //未签约
                            $(_pageId + " .chooseBank").addClass('borderActive');
                            $(_pageId + " .grid_03").show();
                        }else{
                            $(_pageId + " .grid_03").hide();
                            $(_pageId + " .chooseBank").removeClass('borderActive');
                        }
                    }
                }
                let pageInputCasting = appUtils.getSStorageInfo("pageInputCasting") ? appUtils.getSStorageInfo("pageInputCasting") : {};
                if(pageInputCasting.payMethod == '0'){
                    payMethod = '0'
                    $(_pageId + " .payRemark").text("晋金宝")
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .grid_03").hide();
                }
                if(pageInputCasting.payMethod == '1'){
                    payMethod = '1'
                    $(_pageId + " .payRemark").text(bankNameRemark)
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    if(bankInfo.is_exist == '0'){
                        //未签约
                        $(_pageId + " .chooseBank").addClass('borderActive');
                        $(_pageId + " .grid_03").show();
                    }else{
                        $(_pageId + " .grid_03").hide();
                        $(_pageId + " .chooseBank").removeClass('borderActive');
                    }
                }
                if(!backData || backData == 'null') return;
                backData.backShow = '0'
                appUtils.setSStorageInfo("backData",backData);
            } else {
                layerUtils.iAlert(error_info);
            }
        })
    }
    function getNextTime(investcycle, investdate) {
        let data = {
            investcycle: investcycle,
            investdate: investdate
        }
        service.reqFun106045(data, function (datas) {
            if (datas.error_no != '0') {
                layerUtils.iAlert(data.error_info);
                return;
            }
            nextdate = (tools.FormatDateText(datas.results[0].nextdate, 1))
            // console.log(nextdate)
            $(_pageId + ' .nextdate').text('下一扣款日' + nextdate)
        })
    }
    //渲染日
    function setChooseTime(num) {
        let str = ''
        if (num == 0) {
            //月
            secoundText = '1日'
            for (let i = 1; i <= 28; i++) {
                let childStr = `<li class="${i == 1 ? 'active' : ''}" id="${i}">${i + '日'}</li>`
                str += childStr
            }
        } else {
            //周
            secoundText = '周一'
            for (let i = 1; i < weekList.length; i++) {
                let childStr = `<li class="${i == 1 ? 'active' : ''}" id="${i}">${weekList[i]}</li>`
                str += childStr
            }
        }
        investdate = '1'
        $(_pageId + ' .listRight').html(str)
    }
    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //flag true 存 false 清
    function inputInfo(flag){
        let pageInputCasting
        //缓存当前页面输入内容
        if(flag){
            pageInputCasting = {
                invest_money : $(_pageId + " #czje_InvestAmt").val(),
                trans_amt : $(_pageId + " #czje_Amt").val(),
                nick_name: $(_pageId + " .nick_name").val(),
                investdate:investdate,
                payMethod:payMethod
            } 
        }else{
            pageInputCasting = {}
        }
        appUtils.setSStorageInfo("pageInputCasting", pageInputCasting);
    }
    function buy_success(){
        let params = {
            payMethodName:$(_pageId + " .payRemark").text(),
            nextdate: $(_pageId + " .newData").text(),
            payMethod:payMethod,
            prod_sname: isSeriesComb == '1' ? $(_pageId + " .nick_name").val().trim() + '的财富成长计划' : $(_pageId + " .startCasting_title").html(),
            czje_Amt: tools.fmoney($(_pageId + " #czje_Amt").val().replace(/,/g, "")), //购买
            czje_InvestAmt: tools.fmoney($(_pageId + " #czje_InvestAmt").val().replace(/,/g, "")) //定投
        };
        $(_pageId + " #inputspanidAmt span").addClass("unable");//默认输入框失去焦点
        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #inputspanidInvestAmt span").addClass("unable");//默认输入框失去焦点
        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " .grid_03").hide();
        appUtils.setSStorageInfo("tranType",'');
        inputInfo(false);
        appUtils.pageInit("login/userIndexs", "combProduct/castingResult", params);
    }
    //判断昵称是否重复
    async function getUserIsNickName(){
        return new Promise(async (resolve) => {
            let data = {
                series_id:appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.series_id ? productInfo.series_id : productInfo.fund_code),
                nick_name:$(_pageId + " .nick_name").val()
            }
            service.reqFun102195(data, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0])
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    function bindPageEvent() {
        //选择周，两周，月
        appUtils.preBindEvent($(_pageId + " .listLeft"), "li", function () {
            $(_pageId + " .listLeft li").removeClass('active')
            $(this).addClass('active')
            investcycle = $(this).attr('id')
            firstText = $(this).text()
            if (investcycle == 2) {
                setChooseTime(0)
            } else {
                setChooseTime()
            }
        }, 'click');
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            let backData = {
                payMethod:payMethod,
                backShow:'1',
                is_exist:bankInfo.is_exist
            }
            appUtils.setSStorageInfo("backData", backData);
            tools.intercommunication(_pageCode);
        });
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //播放视频
        appUtils.bindEvent($(_pageId + " .play"), function () {
            let html = `<video id="startCasting_new_example_video" style="width:100%;height:100%" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
                webkit-playsinline="true" playsinline="true" height="100%" controls preload="auto" poster=""
                data-setup="{}">
            </video>`
            $(_pageId + " #new_example_div").html(html);
            //初始化视频
            start_player = videojs('startCasting_new_example_video', {
            }, function onPlayerReady() {
                //结束和暂时时清除定时器，并向后台发送数据
                this.on('ended', function () {
                    // window.clearInterval(time1);
                });
                this.on('pause', function () {
                    // window.clearInterval(time1);
                });
                this.on('waiting', function () {
                    // window.clearInterval(time1);
                })
            });
            start_player.reset();
            start_player.src({ src: global.video_oss_url + productInfo.video_path })
            start_player.load(global.video_oss_url + productInfo.video_path)
            $(_pageId + " video").attr("poster", global.oss_url + productInfo.cover_path)
            $(_pageId + " #showVideo").show()
        });
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            start_player.pause();
            setTimeout(function() {
                start_player.dispose();
                start_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        });
        appUtils.bindEvent($(_pageId + " .proTitle ul"), function () {
            inputInfo(true);
            appUtils.pageInit(_pageCode, "combProduct/combProdDetail");
        });
        appUtils.bindEvent($(_pageId + " .rechargeNow"), function () {
            inputInfo(true);
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });
        //选择日期
        appUtils.preBindEvent($(_pageId + " .listRight"), "li", function () {
            $(_pageId + " .listRight li").removeClass('active');
            $(this).addClass('active');
            //文案初始化
            investdate = $(this).attr('id');
            //标记当前选中日期
            secoundText = $(this).text();
            tools.recordEventData('1','chooseData' + investdate,'选择日期_' + secoundText);
        }, 'click');
        
        
        //确定选择定投日期
        appUtils.bindEvent($(_pageId + " .determine"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #cycleModel").hide()
            $(_pageId + " .newData").text(firstText + ' ' + secoundText)
            getNextTime(investcycle, investdate)
        });
        //关闭周期选择弹框
        appUtils.bindEvent($(_pageId + " #closeCycle"), function () {
            $(_pageId + " #cycleModel").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //选择定投日期
        appUtils.bindEvent($(_pageId + " .cycleClick"), function () {
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #cycleModel").show()
        });
        //选择支付方式
        appUtils.preBindEvent($(_pageId + " .payList"), "#amount_enough", function () {
            if($(this).attr('payMethod') == '1' && bank_state == '0') return;
            $(_pageId + " #amount_enough .img").removeClass('active')
            $(this).find(".img").addClass('active')
            payMethod = $(this).attr('payMethod');
            tools.recordEventData('1','amount_enough_' + payMethod,payMethod == 1 ? '银行卡' : '晋金宝');
            if(payMethod == '1'){   //选中银行卡
                $(_pageId + ".payRemark").text(bankNameRemark)
                let data = pdfParam;
                data.bankcard_fixedinvest_flag = '1'
                tools.getPdf(data);
                if(bankInfo.is_exist == '0'){    //未签约，展示验证码
                    $(_pageId + " .chooseBank").addClass('borderActive');
                    $(_pageId + " .grid_03").show();
                }else{
                    $(_pageId + " .grid_03").hide();
                }
                
            }else{
                $(_pageId + ".payRemark").text('晋金宝')
                pdfParam.bankcard_fixedinvest_flag = '0'
                tools.getPdf(pdfParam);
                $(_pageId + " .chooseBank").removeClass('borderActive');
                $(_pageId + " .grid_03").hide();
            }
        }, 'click');
        //关闭选择支付方式
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //开始选择支付方式
        appUtils.bindEvent($(_pageId + " .choosePayType"), function () {
            $(_pageId + " #payMethod").show();
            $(_pageId + " .pop_layer").show();
        });
        //选中支付方式
        appUtils.bindEvent($(_pageId + " .model_bottom"), function (){
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " article").removeClass("combProduct_startCasting_article");
            monkeywords.close();
        });
        //开启计划
        appUtils.bindEvent($(_pageId + " .buy"), async function (){
            if ($(this).hasClass("no_active")) {
                if (purchase_state == "6") {
                    layerUtils.iAlert("当前时间不支持购买");
                }
                return;
            }
            //获取验证码
            var verificationCode = $(_pageId + " #verificationCode").val();
            //获取首次投入金额
            var czje_Amt = $(_pageId + " #czje_Amt").val(); //购买金额
            var czje_InvestAmt = $(_pageId + " #czje_InvestAmt").val(); //定投金额
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if(czje_Amt) czje_Amt = czje_Amt.replace(/,/g, "");
            if(czje_InvestAmt) czje_InvestAmt = czje_InvestAmt.replace(/,/g, "");
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0')) return tools.is_show_c0(_pageCode)
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            //购买相关
            let name = $(_pageId + " .nick_name").val().trim();
            if(isSeriesComb == '1' && !name){
                return layerUtils.iAlert("请输入宝贝姓名");
            }
            if(isSeriesComb == '1' && name && name.length > 0){
                let regex = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
                if(!regex.test(name)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
                if(!tools.validateInputName(name)) return layerUtils.iAlert("昵称字数不能超过5个字符，请修改");
                let res = await getUserIsNickName() //判断昵称是否重复
                if(res.existNick == '1') return layerUtils.iAlert("子女昵称已存在，请重新输入");
            }
            if (czje_Amt <= 0 || !czje_Amt) {
                layerUtils.iAlert("请输入首次投入金额");
                return;
            }
            if (startBuyMoney && parseFloat(czje_Amt) < parseFloat(startBuyMoney)) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            //判断是否提示去充值
            _available_vol = _available_vol*1; //字符串转数字
            if(_available_vol < czje_Amt){
                let operationId = "recharge";
                return layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>晋金宝可用余额不足，请充值</span>", function () {
                }, function funcNo() {
                    inputInfo(true);
                    appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
                }, "取消", "去充值",operationId);
            }
            if (tools.isMatchAddAmt(czje_Amt, startBuyMoney, step_amt)) {
                return
            }
            
            //定投相关
            if(czje_InvestAmt && czje_InvestAmt >0){
                if (fixed_invest_min && parseFloat(czje_InvestAmt) < parseFloat(fixed_invest_min)) {
                    layerUtils.iAlert("定投金额不能低于起投金额");
                    return;
                }
                if (tools.isMatchAddAmt(czje_InvestAmt, fixed_invest_min, step_amt)) {
                    return;
                }
                if(pay_mode == '2' && payMethod == '1' && parseFloat(czje_InvestAmt) > parseFloat(pay_modelimit)){
                    return layerUtils.iAlert(pay_modelimitInfo);
                }
            }
            if (buyflag == "1") {
                let operationId = "riskAssessment";
                layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>本产品为" + productInfo.comb_risk_name + "等级，超出您的风险承受能力（" + userInfo.riskName + "），若仍选择投资，请重新测评。</span>", function () {
                }, function funcNo() {
                    // $(_pageId + " .pop_layer").show();
                    // $(_pageId + " #payMethod").show();
                    // //是否可以购买
                    // isCanBuy();
                    inputInfo(true);
                    appUtils.pageInit(_pageCode, "safety/riskQuestion");
                }, "取消", "重新测评",operationId);
                return;
            }
            let bank_first_max_amt = bankInfo.single_limit*1
            if(payMethod == '1' && czje_InvestAmt > bank_first_max_amt){
                return layerUtils.iAlert("定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。");
            }
            if(payMethod == '1' && bankInfo.is_exist == '0' && isSend == "true"){
                return layerUtils.iAlert("请获取验证码");
            }
            if (payMethod == '1' && verificationCode.length != 6 && bankInfo.is_exist == '0') {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            if (payMethod == '0') {
                $(_pageId + " .payMethodName").text('晋金宝');
                var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
                $(_pageId + " .payMethodRemark").html(html);
                // $(_pageId + " .payMethodRemark").html('可用金额:'+tools.fmoney(_available_vol) + "元");

            } else if (payMethod == '1') {
                let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
                // $(_pageId + " .payMethodName").text(userInfo.bankName + '(尾号' + bankAcct + ')');
                $(_pageId + " .payMethodName").text('晋金宝');
                $(_pageId + " .payMethodRemark").text('限额:');
            }
            //渲染密码弹窗
            let prod_sname;
            if(isSeriesComb == '1'){
                //特殊子女产品
                prod_sname = $(_pageId + " .nick_name").val().trim() + '的财富成长计划'
            }else{
                prod_sname = $(_pageId + " .startCasting_title").html();
            }
            $(_pageId + " #recharge_name").text(prod_sname);
            $(_pageId + " #recharge_money").text(tools.fmoney($(_pageId + " #czje_Amt").val().replace(/,/g, "")));
            if(czje_InvestAmt && czje_InvestAmt >0){
                $(_pageId + " .investmentRemark").show();
                $(_pageId + " .investmentMoney").text(tools.fmoney($(_pageId + " #czje_InvestAmt").val().replace(/,/g, "")));
                $(_pageId + " .investmentDate").text($(_pageId + " .newData").text());
                $(_pageId + " .cycle_remark").text(firstText);
                
            }else{
                $(_pageId + " .investmentRemark").hide();
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "combProduct_startCasting";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
            // $(_pageId + " #cycle").html('<em>' + $(_pageId + " .investmentText").text() + '</em>' + '扣款。')
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();
            // let param = {
            //     acct_no:ut.getUserInf().fncTransAcctNo,
            //     fund_code:"jjcf20230129003",
            //     m_fund_code:"000709",
            //     trans_amt:'1000',
            //     buy_flag:'0',
            //     invest_money:'500',
            //     invest_cycle:'2',
            //     pay_method:'0',
            //     invest_date:'15',
            //     trans_pwd:'123123',
            //     agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            // }
            // // return console.log(param)
            // service.reqFun106063(param, function (datas) {
            //     if (datas.error_no != '0') {
            //         layerUtils.iAlert(data.error_info);
            //         return;
            //     }
            // })
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        appUtils.bindEvent($(_pageId + " #inputspanidAmt"), function (event) {
            let invest_money = $(_pageId + " #czje_InvestAmt").val();
            let trans_amt = $(_pageId + " #czje_Amt").val();
            if(!invest_money || invest_money == ''){
                $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(153, 153, 153)" });
                $(_pageId + " #inputspanidInvestAmt span").addClass("unable");
                $(_pageId + " #inputspanidInvestAmt span").text($(_pageId + " #inputspanidInvestAmt span").attr("text"));
            }
            if(!trans_amt || trans_amt == ''){
                $(_pageId + " #inputspanidAmt span").attr("text",str)
            }
            inputSpanEvent("Amt",event)
        });
        appUtils.bindEvent($(_pageId + " #inputspanidInvestAmt"), function (event) {
            let invest_money = $(_pageId + " #czje_InvestAmt").val();
            let trans_amt = $(_pageId + " #czje_Amt").val();
            if(!trans_amt || trans_amt == ''){
                $(_pageId + " #inputspanidAmt span").css({ color: "rgb(153, 153, 153)" });
                $(_pageId + " #inputspanidAmt span").addClass("unable");
                $(_pageId + " #inputspanidAmt span").text($(_pageId + " #inputspanidAmt span").attr("text"));
            }
            if(!invest_money || invest_money == ''){
                $(_pageId + " #inputspanidInvestAmt span").attr("text",newstr)
            }
            inputSpanEvent("InvestAmt",event)
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var czje_InvestAmt = $(_pageId + " #czje_InvestAmt").val(); //定投金额
            if(czje_InvestAmt) czje_InvestAmt = czje_InvestAmt.replace(/,/g, "");
            let bank_first_max_amt = bankInfo.single_limit*1
            if(payMethod == '1' && czje_InvestAmt > bank_first_max_amt){
                return layerUtils.iAlert("定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。");
            }
            if(pay_mode == '2' && payMethod == '1' && czje_InvestAmt && czje_InvestAmt > 0 &&  (parseFloat(czje_InvestAmt) > parseFloat(pay_modelimit))){
                return layerUtils.iAlert(pay_modelimitInfo);
            }
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var param = {
                    "bank_code": userInfo.bankCode,//银行编码
                    "pay_type": bankInfo.pay_type,
                    "payorg_id": bankInfo.payorg_id,
                    "bank_acct": userInfo.bankAcct,     // 用户卡号
                    "bank_reserved_mobile":userInfo.bankReservedMobile,
                    "cert_no": userInfo.identityNum,   // 用户身份证
                    "bank_name":userInfo.bankName,
                    "sms_type":common.sms_type.bankInvestment,
                    "send_type": "0",
                    "cust_name": userInfo.name, // 用户姓名
                    "cert_type": "0", //证件类型
                    "mobile_phone": userInfo.mobileWhole,
                    "type": common.sms_type.bankInvestment,//发送短信验证码
                }
                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "combProduct_startCasting";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            // jymm1 = '123123'
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //购买
            let invest_money = $(_pageId + " #czje_InvestAmt").val();
            var trans_amt = $(_pageId + " #czje_Amt").val();
            invest_money = invest_money.replace(/,/g, "");
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                fund_code:productInfo.comb_code,
                m_fund_code:"000709",
                nick_name:$(_pageId + " .nick_name").val().trim(),
                buy_flag: '1',
                series_id:productInfo.series_id ? productInfo.series_id : '',
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
                invest_money:invest_money ? invest_money : '0',
                bankserialno : bank_serial_no,
                trans_amt:trans_amt,
                invest_cycle:investcycle,
                pay_method:payMethod,
                invest_date:investdate,
                trans_pwd:jymm1,
                is_exist:bankInfo.is_exist,
                messagecode:$(_pageId + " #verificationCode").val(),
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                if(isSeriesComb == '1'){
                    //特殊子女产品
                    service.reqFun102198(param, function (datas) {
                        if (datas.error_no != '0') {
                            layerUtils.iAlert(datas.error_info);
                            return;
                            //定投+购买成功 跳转结果页
                        }
                        buy_success()
                    })
                }else{
                    service.reqFun106063(param, function (datas) {
                        if (datas.error_no != '0') {
                            layerUtils.iAlert(datas.error_info);
                            return;
                            //定投+购买成功 跳转结果页
                        }
                        buy_success()
                        
                    })
                }
            }, { isLastReq: false });
        });
        //输入首投金额
        // 
    }
    function pageBack() {
        if(start_player){
            start_player.pause();
            setTimeout(function() {
                start_player.dispose();
                start_player = '';
            }, 0);
        }
        investcycle = '2';
        investdate = '1';
        monkeywords.close();
        $(_pageId + " #showVideo").hide();
        inputInfo(false);
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .cycle_remark").text('');
        initialization();
        // if(start_player) start_player.dispose();
        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
        investcycle = '2';
        investdate = '1';
        monkeywords.close();
    	$(_pageId + " #showVideo").hide();
        $(_pageId + " .newData").text('每月1日');
        $(_pageId + " #inputspanidAmt span").text("").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #inputspanidInvestAmt span").text("").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #czje_Amt").val("");
        $(_pageId + " #czje_InvestAmt").val("");
        $(_pageId + " .investmentRemark").hide();
        $(_pageId + " #jymm").val("");
        buyflag = "";
        buy_state = "";
        sms_mobile.destroy();
        $(_pageId + " .startCasting_banner").hide();
        monkeywords.destroy();
        _first_max_amt = "";
        productInfo = null;
        $(_pageId + " .jjs_yue").hide();
        $(_pageId + " .agreement_layer").hide();
        $(_pageId + " .rechargeNow").hide();
        $(_pageId + " #nick_name").hide();
        $(_pageId + " .nick_name").val('');
        $(_pageId + " article").removeClass("combProduct_startCasting_article");
    }
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    
    // 暴露对外的接口
    module.exports = myAccount;
});
