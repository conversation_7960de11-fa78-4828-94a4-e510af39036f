define(function(require, exports, module) {
	var appUtils = require('appUtils');
	var layerUtils = require("layerUtils");
	var gconfig = require("gconfig");
	var global = gconfig.global;
	var 
	common = require("common"),
	validatorUtil = require("validatorUtil");
	

	function filterLoginOut(data){
		if(data.error_no == "-999"){
			layerUtils.iMsg(-1,"由于长时间未操作，系统自动跳转到登入前首页!");
			appUtils.clearSStorage("_loginInPageCode");
			appUtils.clearSStorage("_loginInPageParam");
			appUtils.clearSStorage("_isLoginIn");
			appUtils.clearSStorage();
			appUtils.setSStorageInfo("isLoginTimeOut","yes");
			common.gestureLogin();
		//	appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/userLogin", {});
		}
		
		if(data.error_no == "-99903"){
			layerUtils.iAlert("系统检测到您的账户在其他设备登录,如非本人操作,请立即修改密码,客服电话：" + require("gconfig").global.custServiceTel,-1,function(){
				appUtils.clearSStorage("_loginInPageCode");
				appUtils.clearSStorage("_loginInPageParam");
				appUtils.clearSStorage("_isLoginIn");
				appUtils.clearSStorage();
				appUtils.setSStorageInfo("isLoginTimeOut","yes");
				common.gestureLogin();
			});
		}
		
	}
	
	function filterCardOut(data){
		if(data.error_no == "-99900"){
			layerUtils.iConfirm("系统检测到您还未完成绑卡？",function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"account/setBankCard",{});
			},function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/userIndexs",{});
			},"去绑卡","取消");
		}
	}
	
	function filterActiveOut(data){
		if(data.error_no == "-99901"){
			layerUtils.iConfirm("系统检测到您还未完成激活？",function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/openingAccoun",{});
			},function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/userIndexs",{});
			},"去激活","取消");
		}
	}
	


	//判断输入的值是否合法
	function numberLimit(id){
		// 先把非数字的都替换掉，除了数字和.
		$(id).val($(id).val().replace(/[^\d.]/g, ""));
		// 必须保证第一个为数字而不是.
		$(id).val($(id).val().replace(/^\./g, ""));
		// 保证只有出现一个.而没有多个.
		$(id).val($(id).val().replace(/\.{2,}/g, "."));
		// 保证.只出现一次，而不能出现两次以上
		$(id).val($(id).val().replace(".", "$#$").replace(/\./g, "").replace("$#$","."));
		//只能输入两个小数  
		$(id).val($(id).val().replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'));
	}	
	function moneyFormat(mStr){//
		// 1先把非数字的都替换掉，除了数字和.2保证只有出现一个.而没有多个.3保证.只出现一次，而不能出现两次以上 4只能输入两个小数  
		mStr = (mStr+".00").replace(/[^\d.]/g, "").replace(/^\./g, "").replace(/\.{2,}/g, ".").replace(".", "$#$").replace(/\./g, "").replace("$#$",".").replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3') ;
		if(mStr=="0.00"){
			mStr = "0" ;
		}
		return mStr ;
	}


	//判断风险等级
	function risk_level(pa)
	{
		var level=null;
		if(pa=="1")
		{
			return level="低";
		}
		else if(pa=="2")
		{
			return level="中低";
		}
		else if(pa=="3")
		{
			return level="中";
		}
		else if(pa=="4")
		{
			return level="中高";
		}
		else if(pa=="5")
		{
			return level="高";
		}
		else
		{
			return level="默认"	;
		}
	}


	// 判断开放状态
	function prstor(pa)
	{
		var c=null;

		if(pa=="0")
		{

			return  c="正常开放";
		}
		else if (pa=="1")
		{

			return  c="认购时期";
		}

		else  if(pa=="2")
		{

			return  c="停止赎回";
		}
		else  if(pa=="3")
		{

			return c="停止申购";
		}
		else  if(pa=="4")
		{

			return c="封闭期";
		}
		else  if(pa=="5")
		{

			return c="停止交易";
		}

		else  if(pa=="6")
		{

			return c="基金终止";
		}
		else(pa=="7")
		{

			return c="权益登记";
		}

	}

	// 判断理财目标
	function  geuid (rt)
	{
		var t =null;
		if(rt=="1")
		{
			return  t="投资理财";

		}
		if(rt=="2")
		{
			return t="资产增值";
		}
		if(rt=="3")
		{
			return  t="投资理财&nbsp;资产增值";
		}

	}




	/**
	 * car 是否开放期
	 * @param product_status
	 */
	function checkProdStatus(product_status)
	{
		var flag = true;
		if(product_status=="3")
		{
			flag = false;
		}
		return flag;
	}


	/**
	 * car投资类型
	 * @param fund_type
	 */
	function tz_fund_type(rt)
	{
		var t =null;
		if(rt=="0")
		{
			return  t="股票型";

		}
		if(rt=="1")
		{
			return  t="混合型";

		}
		if(rt=="2")
		{
			return t="债券型";
		}
		if(rt=="3")
		{
			return  t="货币型";
		}
		if(rt=="4")
		{
			return t="保本型";
		}
		if(rt=="5")
		{
			return   t="QDII型";
		}
		else
		{
			return t="其他类型";
		}

	}
//	/**
//	 * 自定义确认框
//	 * @param layerMsg 确认框的内容
//	 * @param oneText 第一个按钮按钮的文本内容
//	 * @param twoText 第二个按钮的文本内容
//	 * @param funcOne 第一个按钮的回调函数
//	 * @param funcTwo 第二个按钮的回调函数
//	 */
//	function layerTwoButton(layerMsg,oneText,twoText,funcOne,funcTwo){
//		var viewContent = '<div class="pop_tip notice" id="utils_confirm"><span class="icon"></span><p style="text-align:left">'+layerMsg+'</p><div class="btn"><a href="javascript:void(0);" id="utils_confirm_one">'+oneText+'</a><a href="javascript:void(0);" id="utils_confirm_two">'+twoText+'</a></div></div>';
//		var iConfirm = layerUtils.layerCustom(viewContent);
//		appUtils.preBindEvent($("#utils_confirm")," #utils_confirm_one",function(){
//			if(funcOne)
//			{
//				funcOne();
//			}
//			layerUtils.iCustomClose();
//		});
//		appUtils.preBindEvent($("#utils_confirm")," #utils_confirm_two",function(){
//			if(funcTwo)
//			{
//				funcTwo();
//			}
//			layerUtils.iCustomClose();
//		});
//	}
 //金额 用逗号 隔开,可以控制小数位数，自动四舍五入
	//排除数字、.-之外字符，保留给定小数位，默认两位
	//s -金额   n -保留小数位数
	function fmoney(s, p){ 
	   var n =(typeof (p)=='number') && p > 0 && p <= 20 ? p : 2;   
	   s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";   
	   var l = s.split(".")[0].split("").reverse(),   
	   r = s.split(".")[1],   
	   t = "";   
	   for(var i = 0; i < l.length; i ++ ){   
	      t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");   
	   }   
	   return t.split("").reverse().join("") + "." + r;   
  } 
	//金额，还原函数
	function rmoney(s){   
	   return parseFloat(s.replace(/[^\d\.-]/g, ""));   
	} 
//
//     /**
//      * 自定义确认提示框
//      * @param {Object} alertMsg 提示信息
//      * @return {TypeName} 
//      */
//    function selfAlertLayer(alertMsg){
//    	require("project/css/self_confirm.css");
//    	var layerStr = ' <div class="window_wrap"   id="layer_alert"> <div class="window"> <div class="text_p p20_28_12" id="alert_tips_info">';
//            layerStr += alertMsg+ ' </div> <div class="btn_box"> <a href="javascript:void(0);" class="a3" id="alert_btn">确定</a> </div> </div> </div>';
//    	 $("body").append(layerStr);
//		 appUtils.bindEvent($("#alert_btn"),function(){
//    		 $("#layer_alert").remove();
//    		 return false;
//    	 });
//     }
	
	/**
	 * 获取输入input域的光标索引位置
	 * @param inputDOM input元素
	 */
	function getInputCursorPosition(inputDOM) 
	{
		var cursurPosition = 0;
		// 非IE浏览器
		
		if('selectionStart' in inputDOM) 
		{
			cursurPosition= inputDOM.valueAsNumber;
		}
		// IE
		else 
		{
			if(document.selection) 
			{
				var range = document.selection.createRange();
				range.moveStart("character", -inputDOM.value.length);
				cursurPosition = range.text.length;
			}
		}
		return cursurPosition;
	}

	/*
	 * 设置输入域(input/textarea)光标的位置
	 * @param inputDOM input元素
	 * @param index 要设置的索引位置
	 */
	function setInputCursorPosition(inputDOM, index)
	{
		var val = inputDOM.value;
		var len = val.length;
	 
		// 要设置的光标位置不超过文本长度时
		if(index <= len) 
		{
			inputDOM.focus();
			// 标准浏览器
			if(inputDOM.setSelectionRange)
			{
				inputDOM.setSelectionRange(index, index);
			}
			// IE9-
			else 
			{
				var range = inputDOM.createTextRange();
				range.moveStart("character", -len);
				range.moveEnd("character", -len);
				range.moveStart("character", index);
				range.moveEnd("character", 0);
				range.select();
			}
		}
	}
	
	var putils = {
		"fmoney":fmoney,
		"rmoney":rmoney,
		//"layerTwoButton" : layerTwoButton,
		"checkProdStatus": checkProdStatus,
		"prstor":prstor,
		"geuid":geuid,
		"risk_level":risk_level,
		"numberLimit":numberLimit,
		"tz_fund_type":tz_fund_type,
		"moneyFormat":moneyFormat,
		"filterLoginOut": filterLoginOut,
		"filterCardOut":filterCardOut,
		"filterActiveOut":filterActiveOut,
		"getInputCursorPosition": getInputCursorPosition,
		"setInputCursorPosition": setInputCursorPosition
		//"selfAlertLayer":selfAlertLayer
	};


	//暴露对外的接口
	module.exports = putils;
});