// 私募自定义列表
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false };
    let gconfig = require("gconfig");
    let global = gconfig.global;
    let currentPage;
    let totalPages;
    let qualifiedInvestorStartAmount = require("gconfig").global.qualifiedInvestorStartAmount;
    let start = 1;//分页参数-开始下标
    let count = 10;//分页参数-每页条数
    let prod_sub_type2;
    let ut = require("../common/userUtil");
    _pageCode = "template/privatePlacementList",
        _pageId = "#template_privatePlacementList ";
    let privatePlacementList
    //初始化
    function init() {
        privatePlacementList = new Vue({
            el: '#main_privatePlacementList',
            data() {
                return {
                    startingInvestmentMoney: '',//合格投资人起投金额
                    showQualifiedInvestor: false,//是否展示弹窗
                    show_visc_pullUpIcon: false,//分页相关
                    show_visc_pullUpDiv: false,
                    show_visc_pullUp: false,
                    show_visc_pullDown: false,
                    list: [],//私募列表
                }
            },
            //视图更新前，主拿数据
            created() {

            },
            //视图更新完成
            mounted() {
                //埋点初始化
                tools.initPagePointData();
                this.$nextTick(() => { // 页面渲染完成后的回调 渲染下拉组件
                    this.startingInvestmentMoney = qualifiedInvestorStartAmount / 10000 + "万"
                    this.investorStatus()
                    let pageTouchTimer = null;
                    appUtils.bindEvent($(_pageId + " #v_container_productList"), () => {
                        pageTouchTimer && clearTimeout(pageTouchTimer);
                        pageTouchTimer = setTimeout(function () {
                            vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
                        }, 500);
                    }, "touchmove");

                    appUtils.bindEvent($(_pageId + " #v_container_productList"), () => {
                        pageTouchTimer && clearTimeout(pageTouchTimer);
                    }, "touchend");
                })
            },
            //计算属性
            computed: {

            },
            //字段监听
            watch: {

            },
            //事件绑定
            methods: {
                //是否展示合格投资弹窗
                investorStatus() {
                    service.reqFun101037({}, (datas) => {
                        if (datas.error_no == 0) {
                            var state = datas.results[0].state;
                            //未确认弹窗
                            if (state == 1) { // 已确认
                                this.showQualifiedInvestor = false
                                this.chanping(start, false);
                            } else {
                                this.showQualifiedInvestor = true
                            }
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    });
                },
                //开始渲染
                chanping(start, isAppendFlag) {

                    var param = {
                        start: start,
                        count: count,
                        custLabelCnlCode: ut.getUserInf().custLabelCnlCode
                    };
                    //获取列表数据
                    service.reqFun102042(param, (datas) => {
                        this.$nextTick(() => {
                            if (datas.error_no == 0) {
                                currentPage = datas.results[0].currentPage;//当前页数
                                totalPages = datas.results[0].totalPages;//总页数
                                let productList = datas.results[0].data;
                                if (currentPage <= totalPages) {
                                    // console.log(productList,111)
                                }

                                if (isAppendFlag) { //追加数据
                                    this.list = [...this.list, ...productList]
                                    this.show_visc_pullUpIcon = false
                                    this.show_visc_pullUpDiv = false
                                } else {    //渲染
                                    this.list = productList
                                }
                                this.pageScrollInit();

                            } else {
                                layerUtils.iAlert(datas.error_info);
                            }
                        })
                    });
                },
                /**上下滑动刷新事件**/
                pageScrollInit() {
                    this.$nextTick(() => {
                        let height = $(_pageId + " #v_container_productList").offset().top;
                        let height2 = $(window).height() - height - 50 + 44;
                        if (!vIscroll._init) {
                            let config = {
                                "isPagingType": false,
                                "visibleHeight": height2, //这个是中间数据的高度
                                "container": $(_pageId + " #v_container_productList"),
                                "wrapper": $(_pageId + " #v_wrapper_productList"),
                                "downHandle": () => {
                                    start = 1;
                                    this.chanping(start, false);
                                    // $(_pageId + " .visc_pullUp").show();
                                    this.show_visc_pullDown = true
                                    this.show_visc_pullUpIcon = false
                                    this.show_visc_pullUpDiv = false
                                    // $(_pageId + " .visc_pullUpIcon").hide();
                                    // $(_pageId + " .visc_pullUpDiv").hide();
                                },
                                "upHandle": () => {
                                    if (currentPage < totalPages) {
                                        this.show_visc_pullUpIcon = true
                                        this.show_visc_pullUpDiv = true
                                        // $(_pageId + " .visc_pullUpIcon").show();
                                        // $(_pageId + " .visc_pullUpDiv").show();
                                        start += count;
                                        this.chanping(start, true);
                                    }
                                },
                                "wrapperObj": null
                            };
                            vIscroll.scroll = new VIscroll(config); //初始化
                            vIscroll._init = true;
                        } else {
                            vIscroll.scroll.refresh();
                        }

                        if (currentPage == totalPages) {
                            // $(_pageId + " .visc_pullUp").hide);
                            this.show_visc_pullUp = false
                        } else {
                            // $(_pageId + " .visc_pullUp").show();
                            this.show_visc_pullUp = true
                        }

                    })
                }
            },
        })
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        //销毁vue 实例
        privatePlacementList = null;
        start = 1;//分页参数-开始下标
        count = 10;//分页参数-每页条数
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    var highEndDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highEndDetail;
});
