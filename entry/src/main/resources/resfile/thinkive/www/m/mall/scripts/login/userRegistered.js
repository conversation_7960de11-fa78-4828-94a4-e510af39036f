// 手机注册
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#login_userRegistered";
    var _pageCode = "login/userRegistered";
    var external = require("external");
    var timer = null;//计时器
    var i = 120;//倒计时长
    var isAgree = true;
    var Millisecond;
    var userInfoType = '0';
    var mobile_save;//用于本地存储手机号
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var label_show;
    require("../common/verify")
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        getSlderInit()
        // $(_pageId + ' #mpanel').hide()
        $(_pageId + ' #verificationCode').attr("disabled",true);
        // $(_pageId + " .agreement_layer").hide();
        initYanZma();
        // setImgCode();
        mobile_save = "";
        $(_pageId + " #phoneShow").hide();
        common.systemKeybord(); // 解禁系统键盘
        window.clearInterval(timer);
    //    $(_pageId + " #input_radio2").attr("checked", "checked");
        tools.getPdf("5");
        //查询协议记录信息
        if (!appUtils.getSStorageInfo("registedXy")) {
            $(_pageId + " #yqrPhoneNum").val("");
            $(_pageId + " #tuxingCode").val("");
            $(_pageId + " #phoneNum").val("");
            $(_pageId + " #pwd1").val("");
            $(_pageId + " #pwd2").val("");
        } else {
            appUtils.clearSStorage("registedXy");
        }
        if (appUtils.getPageParam("mobile")) {
            $(_pageId + " #phoneNum").val(appUtils.getPageParam("mobile"));
            checkMobileIsExists({"mobile": appUtils.getPageParam("mobile")}, function () {
                layerUtils.iLoading(false);
            });
        }
        //是否有本地存储的渠道代码,有渠道,先查询对应的标签id
        // var channelCode = common.getLocalStorage("download_channel_code");
        // if(channelCode){
        //     service.reqFun102105({"channel_code":channelCode}, function (data) {
        // 	if (data.error_no == "0") {
        // 	    if(data.results && data.results.length>0){
        // 		var result = data.results[0];
        // 		//是否要展示邀请码
        // 		label_show = result.label_show;
        // 		if(result.label_show == "1"){
        // 		    $(_pageId + " #labelId_div").show();
        // 		}
        // 		//邀请码是否有默认值
        // 		if(result.label){
        // 		    $(_pageId + " #labelId").val(result.label);
        // 		}
        // 	    }
        //         } else {
        //             layerUtils.iAlert(data.error_info);
        //         }
        //     });
        // }
    }
    //初始化滑动
    function getSlderInit(){
        $(_pageId + " #mpanel").empty()
        $(_pageId + ' #mpanel').slideVerify({
            type : 1,		//类型
            vOffset : 5,	//误差量，根据需求自行调整
            barSize : {
                width : '100%',
                height : '34px',
            },
            ready : function() {

            },
            success : function() {
                let mobile = $(_pageId + " #phoneNum").val();
                sendPhoneCode(mobile)
                $(_pageId + ' #registerDig').hide()
                // checkMobileIsExists({"mobile": mobile}, function () {
                //     sendPhoneCode(mobile);
                // });
            },
            error : function(e) {
            }
        });
    }
    //绑定事件
    function bindPageEvent() {
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "login_userRegistered";
            tools.saveAlbum(_pageCode,param)
        });

        appUtils.bindEvent($(_pageId + " input"), function () {
            var localHeight = $(_pageId + " section").height();//获取可视宽度
            var keyboardHeight = localHeight - $(_pageId + " section").height();//获取键盘的高度
            var keyboardY = localHeight - keyboardHeight;
            var addBottom = (parseInt($(this).position().top) + parseInt($(this).height()));//文本域的底部
            var offset = addBottom - keyboardY;//计算上滑的距离
            $(_pageId + " section").scrollTop(offset);
        }, "focus");
        /* 切换图形验证码 */
        appUtils.bindEvent($(_pageId + " #getCode"), function () {
            // setImgCode();
        });
        // 手机号码输入事件
        appUtils.bindEvent($(_pageId + " #phoneNum"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            $(_pageId + " #phoneShow").hide();
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
            var param = {
                mobile: curVal,
            };
            if (!validatorUtil.isMobile(curVal)) {
                return;
            }
            service.reqFun102005(param, function (data) {
                if (data.error_no == "-10200502") {//10200501 账号已注册
                    getSlderInit()
                    userInfoType = '1' //用户已注册
                    let operationId = 'registered'
                    layerUtils.iConfirm("手机号码已注册，是否去登录？", function () {
                        appUtils.pageInit(_pageCode, "login/userLogin", {mobile: curVal});
                    }, function () {
                        return;
                    }, "好的", "取消",operationId);
                } else if (data.error_no == "-10200501") {//黑名单
                    userInfoType = '2' //该用户为黑名单用户
                    // $(_pageId + ' #mpanel').hide()
                    $(_pageId + " input").blur();
                    return layerUtils.iAlert("网络繁忙,请稍后重试!");
                } else if (data.error_no == "-********") {//晋金所已开户
                    $(_pageId + " input").blur();
                    // $(_pageId + ' #mpanel').hide()
                    layerUtils.iAlert("检测到您已在晋金所开户，请登录", "", function () {
                        appUtils.pageInit(_pageCode, "login/userLogin", {mobile: param.mobile});
                    });
                    return 
                } else if (data.error_no != "0") {
                    userInfoType = '3' //发生其他未知错误
                    $(_pageId + ' #mpanel').hide()
                    layerUtils.iAlert(data.error_info);
                    return
                }
                $(_pageId + ' #mpanel').show()
            });
        }, "input");

        // 邀请人手机号码输入事件
        appUtils.bindEvent($(_pageId + " #yqrPhoneNum"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var isChecked = $(_pageId + " #input_radio2").attr("checked");
            if (!isChecked) {
                layerUtils.iMsg(-1, "请阅读协议并同意签署");
                return;
            }
            //验证手机号input
            var mobile = $(_pageId + " #phoneNum").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            //验证获取验证码按钮可用性
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            getSlderInit()
            $(_pageId + " #registerDig").show()
            //验证登录密码input
            // var pwd_one = $(_pageId + " #pwd1").val();
            // var pwd_two = $(_pageId + " #pwd2").val();
            // if (!(checkInput(pwd_one, pwd_two))) {
            //     return;
            // }
            //确保手机号未注册后，获取验证码
            // window.clearInterval(timer);
            // checkMobileIsExists({"mobile": mobile}, function () {
            //     sendPhoneCode(mobile);
            // });
        });

        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });
        //去首页
        appUtils.bindEvent($(_pageId + " #toLogin"), function () {
            pageBack();
        });
        //点击相关协议
        // appUtils.bindEvent($(_pageId + " .click_agreement"), function (e) {
        //     e.preventDefault();
        //     e.stopPropagation();
        //     $(_pageId + " .agreement_layer").show();
            
        // });
        //关闭协议
        // appUtils.bindEvent($(_pageId + " .close_btn"), function (e) {
        //     e.preventDefault();
        //     e.stopPropagation();
        //     $(_pageId + " .agreement_layer").hide();
            
        // });
        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #registered"), function () {
            //检查协议勾选
            var isChecked = $(_pageId + " #input_radio2").attr("checked");
            if (!isChecked) {
                layerUtils.iMsg(-1, "请阅读协议并同意签署");
                return;
            }
            //检查手机号
            var phone = $.trim($(_pageId + " #phoneNum").val());
            if (validatorUtil.isEmpty(phone)) {
                layerUtils.iMsg(-1, "请输入手机号码");
                return;
            }
            if (!validatorUtil.isMobile(phone)) {
                layerUtils.iMsg(-1, "请确定您输入的手机号是否正确");
                return;
            }
            //检查验证码获取按钮
            var isSend = $(_pageId + " #getYzm").attr("data-state");
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            // // //检查验证码input
            var mobile_vf_code = $.trim($(_pageId + " #verificationCode").val());//验证码
            if (validatorUtil.isEmpty(mobile_vf_code)) {
                layerUtils.iMsg(-1, "请输入手机验证码");
                return;
            }
            if (!validatorUtil.isNumeric(mobile_vf_code)) {
                layerUtils.iMsg(-1, "验证码格式为纯数字");
                return;
            }
            //如果邀请人电话存在就不操作负责就将邀请人电话赋值为空
            // var yqrPhoneNum = $.trim($(_pageId + " #yqrPhoneNum").val());//邀请人电话
            // if (validatorUtil.isNotEmpty(yqrPhoneNum) && !validatorUtil.isMobile(yqrPhoneNum)) {
            //     layerUtils.iMsg(-1, "请确定你输入的邀请人号码是否正确");
            //     return;
            // }
            //检查登录密码
            // var pwd1 = $(_pageId + " #pwd1").val();
            // var pwd2 = $(_pageId + " #pwd2").val();
            // if (!checkInput(pwd1, pwd2)) {
            //     return;
            // }
            //注册
            let param = {
                registered_mobile: phone,
                sms_mobile: phone,
                sms_code: mobile_vf_code,
            };
            appUtils.pageInit(_pageCode, "login/setPassword", param);
            // mobile_save = phone;
        	// registerAccount(param);
        });

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getSlderInit()
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            // $(_pageId + ' #mpanel').hide()
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

    }


    //获取语音验证码
    function getCodeOFTalk() {
        var mobile = $(_pageId + " #phoneNum").val();
        // var imgCode = $(_pageId + " #tuxingCode").val();//图形验证码
        if (mobile) {
            var param = {
                "mobile_phone": mobile,
                "type": common.sms_type.register,
                // "ticket": imgCode,
                "send_type": "1",
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(_pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        } else {
            if (mobile == "") {
                layerUtils.iMsg(-1, "请输入手机号");
            }
            if (imgCode == "") {
                layerUtils.iMsg(-1, "请输入图像验证码");
            }

        }
    }

    //初始化语音验证码
    function initYanZma() {
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
    }

    //判断手机号是否注册过
    function checkMobileIsExists(param, callBack) {
        //解决安卓图形验证码session不一致问题 2018-1-12
        // if (require("gconfig").platform == "1") {
        //     var Param = {
        //         "funcNo": "50502",
        //         "moduleName": "mall",
        //         "url": global.serverUrl
        //     };
        //     external.callMessage(Param);
        // }
        service.reqFun102005(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "-10200502") {
                getSlderInit()
                layerUtils.iLoading(false);
                //埋点ID
                let operationId = 'registered'
                layerUtils.iConfirm("手机号码已注册，是否去登录？", function () {
                    appUtils.pageInit(_pageCode, "login/userLogin", {mobile: param.mobile});
                }, function () {
                    return;
                }, "好的", "取消",operationId);
            } else if (error_no == "0") {
                callBack();
            } else if (data.error_no == "-10200501") {
                layerUtils.iLoading(false);
                getSlderInit()
                layerUtils.iAlert("网络繁忙,请稍后重试!");
            } else if (data.error_no == "-********") {//晋金所已开户
                layerUtils.iLoading(false);
                getSlderInit()
                $(_pageId + " input[id!='phoneNum']").blur();
                layerUtils.iAlert("检测到您已在晋金所开户，请登录", "", function () {
                    appUtils.pageInit(_pageCode, "login/userLogin", {mobile: param.mobile});
                });
            } else {
                getSlderInit()
                layerUtils.iLoading(false);
                $(_pageId + " .bank_limit").hide();
                layerUtils.iAlert(error_info);
            }
        }, {isLastReq: false});
    }

    /**
     * 发送手机验码
     * */
    function sendPhoneCode(mobile) {
        if (!validatorUtil.isMobile(mobile)) {
            layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
            getSlderInit()
            return;
        }
        var $code = $(_pageId + " #getYzm");
        if ($code.attr("data-state") == "false") {
            getSlderInit()
            return;
        }
        checkMobileIsExists({"mobile": mobile}, function () {
            window.clearInterval(timer);
            var param = {
                mobile_phone: mobile,
                type: common.sms_type.register,
                send_type: "0"
            };
            service.reqFun199001(param, function (data) {
                var error_no = data.error_no,
                    error_info = data.error_info;
                if (error_no == "0") {
                    $(_pageId + " #talkCode").show();
                    timer = setInterval(function () {
                        shows();
                    }, 1000);
                } else {
                    // var $code = $(_pageId + " #getYzm");
                    // window.clearInterval(timer);
                    // i = 120;
                    // $code.css("background-color", " #C1E3B6");
                    // $code.attr("data-state", "true");
                    // $code.html("重新获取验证码");
                    // $(_pageId + " #tuxingCode").val("");
                    // initYanZma();
                    // $(_pageId + " #talkCode").show();
                    // layerUtils.iAlert(error_info);
                    getSlderInit()
                    $(_pageId + ' #verificationCode').attr("disabled",true);
                    window.clearInterval(timer);
                    i = 120;
                    initYanZma();
                    $(_pageId + " #talkCode").show();
                    layerUtils.iAlert(error_info);
                }
            });
        });
        
    }

    /**
     * 显示读秒
     * */
    function shows() {
        $(_pageId + ' #verificationCode').attr("disabled",false);
        var $code = $(_pageId + " #getYzm");
        $(_pageId + " #getYzm").show()
        $code.attr("data-state", "false");//点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            // $code.css("background-color", "yellow");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            // $code.css("background-color", " #C1E3B6");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();
        }
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iAlert("登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iAlert("确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iAlert("两次密码不相同");
            return false;
        }
        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }

        if (!isAgree) {
            layerUtils.iAlert("请阅读协议并同意签署");
            return false;
        }
        return true;
    }

    /**
     * 验证手机验证码
     * @param {Object} paramObj 入参json对象
     *    mobile_vf_cod 输入的手机验证码
     *    mobile_phone 输入的手机号码
     */
    function checkPhoneCode(paramObj) {
        registerAccount(paramObj);
    }

    /**
     * 商城用户注册
     *   mobile: 手机号,
     *   sys_trans_pwd: 密码,
     *   yqrPhoneNum:邀请人电话
     */
    function registerAccount(paramObj) {
	paramObj.labelId = $(_pageId + " #labelId").val();
	paramObj.custCnl = common.getLocalStorage("download_channel_code");
    paramObj.source = '1';//app用户自主注册
        service.reqFun101001(paramObj, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            //验证码重置
            var $code = $(_pageId + " #getYzm");
            $(_pageId + " #verificationCode").val("");//验证码
            window.clearInterval(timer);
            i = 120;
            // $code.css("background-color", " #C1E3B6");
            // $code.attr("data-state", "true");
            // $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();
            if (error_no == "0") {
                //对登录密码进行加密*/
                var pwdCallBack = function (pwdRSAStr) {
                    paramObj.login_pwd = pwdRSAStr;
                    paramObj.login_acc = paramObj.registered_mobile;
                    paramObj.type = "0";
                    // 存登录号 和 登录加密密码 方便手势密码
                    var platform = require("gconfig").platform;
                    if (platform != "0") {
                        var paramlist = {
                            funcNo: "50042",
                            key: "account_password",
                            isEncrypt: "1",
                            value: paramObj.registered_mobile + "_" + pwdRSAStr
                        };
                        external.callMessage(paramlist);
                    }
                    //获取token
                    var param = {
                        funcNo: "50041",
                        key: "deviceTokenKey"
                    };
                    if (platform != "0") {
                        var data = external.callMessage(param);
                        var device_token = data.results[0].value;
                        paramObj["device_token"] = device_token;
                    }
                    mobile_save = paramObj.registered_mobile;
                    service.reqFun101018(paramObj, loginCallBackFun);
                };
                common.rsaEncrypt(paramObj.login_pwd, pwdCallBack);
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(error_info);
            }
        }, {"isLastReq": false});
    }

    //登录回调函数
    function loginCallBackFun(resultVo) {
        var error_no = resultVo.error_no;
        var error_info_login = resultVo.error_info;
        if (error_no == "0") {
            var results = resultVo.results[0];
            common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
            common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
            common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
            sessionStorage.pop_up_otification = "";
            common.setLocalStorage("snowballMarketShow",'');
            common.setLocalStorage("mobileWhole", results.mobileWhole);//获取完整手机号保存到本地
            appUtils.setSStorageInfo("isAuthenticated", results.mobile + "@|@|@" + new Date().getTime());
            ut.saveUserInf(resultVo.results[0]);
            common.setLocalStorage("userChooseRefresh",'1'); //刷新用户版本
            common.setLocalStorage("userChooseVerson",''); //置空用户选择版本
            //登录后保存用户场景
            common.setLocalStorage("scene_code", results.scene_code);
            //存储渠道代码到本地
            common.setLocalStorage("download_channel_code", results.custLabelCnlCode);
            // 存储是否是异常客户-开户审核中
            appUtils.setSStorageInfo("is_open_acct_excp",results.is_open_acct_excp)
            //存储渠道代码到本地
            common.setLocalStorage("download_channel_code", results.custLabelCnlCode);
            tools.loginQy()
            //银行账户已冻结 （N：正常; C：销户; F：冻结）
            if (results.custStatus == "F") {
                layerUtils.iMsg(-1, "账户已冻结，请联系" + require("gconfig").global.custServiceTel);
            }
            appUtils.pageInit("login/userRegistered", "login/userRegisteredSuccess", {});
        } else {
            layerUtils.iAlert(error_info_login);
        }
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        getSlderInit()
        $(_pageId + ' #registerDig').hide()
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        // $(_pageId + ' #mpanel').hide()
        window.clearInterval(timer);
        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        // $yzm.css("background-color", "#C1E3B6");
        $yzm.html("获取验证码");
        $(_pageId + " #input_radio2").removeAttr("checked", "checked");
        $(_pageId + " #input_radio2").attr("isSelect", "true");
        i = 120;
        service.destroy();
        $(_pageId + " #checkBox").removeClass("checked");
        isAgree = true;
        mobile_save = null;
        $(_pageId + " #labelId_div").hide();
        label_show="";
    }

    function pageBack() {
        appUtils.pageBack();
    }

    /*****************************************************************************************************************************/

    //刷新图形验证码
    function setImgCode() {
        service.reqFun1100005({}, function (data) {
            if (data.error_no == 0) {
                var base64 = data.results[0].base64;
                $(_pageId + " #getCode img").attr("src", base64);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    /*****************************************************************************************************************************/
    var userRegistered = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userRegistered;
});
