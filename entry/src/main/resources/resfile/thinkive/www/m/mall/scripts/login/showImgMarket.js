// 产品详情模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        _page_code = "login/showImgMarket",
        _pageId = "#login_showImgMarket ";
    var global = gconfig.global;
    // let productInfo;
    var ut = require("../common/userUtil");
    var params; 
   
    function init() {
        params = appUtils.getPageParam();
        $(_pageId + " #img").attr("src", params.imgUrl);
        $(_pageId + " .header_inner h1").html(params.zoneName);
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
             	//跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_page_code)
        });
    
    }
    //页面销毁
    function destroy() {

    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
