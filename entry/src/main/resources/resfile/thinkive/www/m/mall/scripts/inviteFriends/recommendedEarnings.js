 // 手机注册
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		serviceConstants = require("constants"),
		service = require("mobileService"),
		des = require("des"),
		_page_code = "inviteFriends/recommendedEarnings",
		_pageId = "#inviteFriends_recommendedEarnings";
		let source;//来源
	function init(){
		source = '3';//主动邀请
		//区分入口 recommendedEarnings 新版活动
		let saveMoneyFlag = appUtils.getSStorageInfo("saveMoneyFlag") ? appUtils.getSStorageInfo("saveMoneyFlag") : '';
		if(saveMoneyFlag == '1'){
			reqFun108054();
		}else{
			//邀请好友列表
			reqFun101074();
		}
		
	}

	//绑定事件
	function bindPageEvent(){
		//邀请好友详细列表
		appUtils.preBindEvent($(_pageId+" .recommendFriends"), ".recommendModel .modelname", function() {
			appUtils.pageInit( "inviteFriends/recommendedEarnings","inviteFriends/recommendList",{type:$(this).attr('type')});
		},'click');

		//邀请好友
		appUtils.bindEvent($(_pageId+" #yq_xhb"),function(){
			appUtils.pageInit(_page_code, "vipBenefits/friendInvitation",{source:source});
		});

		//点击返回按钮
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});
	}

	/*邀请好友列表*/
	function reqFun101074(){
		service.reqFun101074({},function(data){
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				$(_pageId+ " .invitaion_num").html(data.results[0].recommendedCount);
				var recommendhtml=recommendListHtml(data.results[0].listFriBuy,0)+recommendListHtml(data.results[0].listFriNoBuy,1)+recommendListHtml(data.results[0].listFriNoband,2);
				$(_pageId+ " .recommendFriends").html(recommendhtml);
			}else{
				layerUtils.iAlert(error_info);
			}
		})
	}
	/*新活动 邀请好友列表*/ 
	function reqFun108054(){
		service.reqFun108054({},function(data){
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				$(_pageId+ " .invitaion_num").html(data.results[0].recommendedCount);
				var recommendhtml=recommendListHtml(data.results[0].buyList,0)+recommendListHtml(data.results[0].notBuyList,1)+recommendListHtml(data.results[0].notBindList,2);
				$(_pageId+ " .recommendFriends").html(recommendhtml);
			}else{
				layerUtils.iAlert(error_info);
			}
		})
	}
	/*生成邀请列表*/
	function recommendListHtml(list,type){
		var typename;
		if(type==0)typename='已投资';
		if(type==1)typename='绑卡未投资';
		if(type==2)typename='注册未绑卡';
		var listhtml='<div class="recommendModel"><div class="modelname" type="'+type+'">'+typename+'（'+list.length+'）</div>';
		if(list.length>0&&list){
			for(var i=0;i<3;i++){
				if(list[i]){
					listhtml+='<div class="modelList"><span>'+list[i].registered_mobile.substring(0, 3)+ "****" +list[i].registered_mobile.substring(7,11)+'</span><span>'+list[i].cust_name.substring(0,1)+'*</span></div>';
				}
			}
		}else{
			listhtml+='<div class="nodata">暂无数据</div>';
		}
		listhtml+='</div>';
		return listhtml;
	}

	function pageBack(){
		appUtils.pageBack();
	}

	function destroy(){
		$(_pageId+ " #zrjl").html();
		$(_pageId+ " #ljjl").html();
		$(_pageId+" #wtjl").html();
		$(_pageId+" #pop_layer").hide();
	}

	var recommendedEarnings = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = recommendedEarnings;
});
