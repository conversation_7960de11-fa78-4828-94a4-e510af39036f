// 高端： 金融资产证明要求
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
    tools = require("../common/tools"),
            publicUtil = require('./publicUtil'),
            _pageId = "#highEnd_assetsModel ";
    var assetModes = {
        "80": {
            title: [
                {text: "金融资产证明", id: "fundIncome"},
                {text: "家庭关系证明", id: "familyRelation"}
            ],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {
                "00": "家庭关系金融资产",
                "01": "户口本",
                "02": "结婚证",
            }
        },
        "81": {
            title: [
                {text: "金融资产证明", id: "fundIncome"},
                {text: "家庭关系证明", id: "familyRelation"}
            ],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {
                "00": "家庭关系金融资产",
                "01": "户口本",
                "02": "结婚证",
            }
        },
        "90": {
            title: [],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {}
        },
        "91": {

            title: [
                {text: "金融资产证明", id: "fundIncome"},
                {text: "家庭关系证明", id: "familyRelation"}
            ],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {
                "00": "家庭关系金融资产",
                "01": "户口本",
                "02": "结婚证",
            }
        },
        "92": {
            title: [],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {}
        },
        "93": {
            title: [],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {}
        },
        "94": {
            title: [],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {}
        },
        "95": {

            title: [
                {text: "金融资产证明", id: "fundIncome"},
                {text: "家庭关系证明", id: "familyRelation"}
            ],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {
                "00": "家庭关系金融资产",
                "01": "户口本",
                "02": "结婚证",
            }
        },
        "96": {
            title: [
                {text: "金融资产证明", id: "fundIncome"},
                {text: "家庭关系证明", id: "familyRelation"}
            ],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {
                "00": "家庭关系金融资产",
                "01": "户口本",
                "02": "结婚证",
            }
        },
        "97": {
            title: [],
            fundIncome: {
                "00": "银行存款",
                "01": "股票债券等",
                "02": "理财产品",
                "03": "保险",
            },
            familyRelation: {}
        },
    }
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        //顶部导航渲染
        var titleStr = "";

        var title = assetModes[productInfo.prod_sub_type2]?assetModes[productInfo.prod_sub_type2].title:assetModes[productInfo.prod_sub_type].title;
        for (var i = 0; i < title.length; i++) {
            titleStr += '<li operationType="1" operationId="nav-item_' + title[i].id + '" operationName="' + title[i].text + '" class="nav-item " name="' + title[i].id + '">' + title[i].text + '</li>'
        }
        $(_pageId + " .title").html(titleStr);
        if (title.length > 0) {
            $(_pageId + " .title").show();
            $(_pageId + " .common-tab-contents").css({top: 107})
            $(_pageId + " .title li").eq(0).addClass("active");
        } else {
            $(_pageId + " .common-tab-contents").css({top: 51})
            $(_pageId + " .title").hide();
        }

        // 子栏目导航渲染
        var sub_title = assetModes[productInfo.prod_sub_type2]?assetModes[productInfo.prod_sub_type2].fundIncome:assetModes[productInfo.prod_sub_type].fundIncome;
        var sub_titleStr = "";
        for (var i in sub_title) {
            sub_titleStr += '<li operationType="1" operationId="nav-item_' + i + '" operationName="' + sub_title[i] + '" class="nav-item" key="' + i + '">' + sub_title[i] + '</li>'
        }
        $(_pageId + " .sub_title").html(sub_titleStr);
        $(_pageId + " .sub_title li").eq(0).addClass("active");
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //顶部导航
        appUtils.preBindEvent($(_pageId + " .title"), ".nav-item", function () {
            var name = $(this).attr("name");
            $(this).addClass('active').siblings().removeClass('active');

            // 子栏目导航渲染
            var sub_title = assetModes[productInfo.prod_sub_type2] ? assetModes[productInfo.prod_sub_type2][name] : assetModes[productInfo.prod_sub_type][name];
            var sub_titleStr = "";
            for (var i in sub_title) {
                sub_titleStr += '<li class="nav-item" key="' + i + '">' + sub_title[i] + '</li>'
            }
            $(_pageId + " .sub_title").html(sub_titleStr);

            $(_pageId + " .sub_title li").eq(0).addClass("active");
            $(_pageId + " .content-item").removeClass("active").filter("." + name + "00").addClass("active");

        }, 'click');
        //子栏目导航
        appUtils.preBindEvent($(_pageId + " .sub_title"), ".nav-item", function () {
            var key = $(this).attr("key");
            var name = $(_pageId + " .title li.active").attr("name") ? $(_pageId + " .title li.active").attr("name") : "fundIncome";
            $(_pageId + " .content-item").removeClass("active").filter("." + name + key).addClass("active");
            $(this).addClass('active').siblings().removeClass('active');
        }, 'click');

    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + '.common-tab-nav .nav-item').removeClass("active").eq(0).addClass("active");
        $(_pageId + '.content-item').removeClass("active").eq(0).addClass("active");
        titleStr = null
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
