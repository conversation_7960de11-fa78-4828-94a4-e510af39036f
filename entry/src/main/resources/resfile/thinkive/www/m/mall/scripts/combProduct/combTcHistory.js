// 产品整合交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageId = "#combProduct_combTcHistory ",
        _pageCode = "combProduct/combTcHistory",
        tools = require("../common/tools");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    require('../../css/iosSelect.css');
    var isEnd = false;
    var _cur_page = '1';
    var _num_per_page = '10';
    var productInfo;
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        _cur_page = '1';
        $(_pageId + ".olay").remove();
        // getUsertransaction(false);
        gettcHistoryList(false);
    }

    function bindPageEvent() {
        // 收缩
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".tc_box .up", function () {
            tools.recordEventData('4','up','收缩');
            $(this).removeClass("up").addClass("down");
            var broDiv = $(this).parents(".tc_box").children(".hold_detail");
            broDiv.css({ "display": "none" })
            pageScrollInit();
        }, 'click');

        // 展开
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".tc_box .down", function () {
            tools.recordEventData('4','down','展开');
            $(this).removeClass("down").addClass("up");
            var broDiv = $(this).parents(".tc_box").children(".hold_detail");
            broDiv.css({ "display": "block" })
            pageScrollInit();
        }, 'click');
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function gettcHistoryList(isAppendFlag) {
        isEnd = false;
        var param = {
            comb_code: productInfo.comb_code,
            count: _num_per_page,
            current: String(_cur_page),
        };
        var gettcHistoryListCallBack = function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var totalPages = data.results[0].totalPages; //总页数
                var results = data.results[0].data;
                var html = "";
                var subHtml = "";
                
                for (var i = 0; i < results.length; i++) {
                    results[i].adjust_date = tools.ftime(results[i].adjust_date.substr(0, 8))
                    if (results[i].list && results[i].list.length) {
                        subHtml = ` <div class="hold_detail bg-white" style="display: ${i == 0 ? "block" : "none"}">
                        <div style="padding: 0.06rem 0.15rem 0.06rem 0.15rem;">调仓理由：${results[i].adjust_desc}</div>
                        <div class="hold_detail_th m_font_size16 m_bold">
                            <span>明细</span>
                            <span class="m_title_right">调仓操作</span>
                        </div>`;
                        var str = "";
                        results[i].list.forEach(item => {
                            str += ` <div><div class="hold_detail_item">${item.investment_name}</div><div>`
                            item.list = JSON.parse(item.list);
                            if (item.list && item.list.length && item.list instanceof Array) {
                                item.list.forEach(fund => {
                                    // adjust_type:调仓类型(1建仓 2加仓 3不变 4减仓 5清仓)]
                                    if (fund.adjust_type == 1) {
                                        fund['adjust_desc'] = "新增", fund['adjust_class'] = "color_0770ff"
                                    } else if (fund.adjust_type == 2) {
                                        fund['adjust_desc'] = "加仓", fund['adjust_class'] = "m_text_red";
                                    } else if (fund.adjust_type == 3) {
                                        fund['adjust_desc'] = "不变"
                                    } else if (fund.adjust_type == 4) {
                                        fund['adjust_desc'] = "减仓", fund['adjust_class'] = "m_text_green";
                                    } else if (fund.adjust_type == 5) {
                                        fund['adjust_desc'] = "清仓", fund['adjust_class'] = "m_text_green";
                                    }
                                    str += `
                                        <div class="hold_detail_tr">
                                            <div>
                                                <div>${fund.prod_sname}</div>    
                                                <span>${fund.prod_id}</span>
                                            </div>
                                            <div class=${fund.adjust_class}>${fund.adjust_desc}</div>
                                        </div>
                                    `;
                                })
                            }
                            str += '</div></div>';
                        })
                        subHtml += str + `</div>`
                    }
                    html += `
                        <div class='tc_box'>
                            <div class="tc_box_item">
                                <div>
                                    <p>${results[i].adjust_date}</p>
                                </div>
                                <span class="tc_icon ${i == 0 ? 'up' : 'down'}"></span>
                            </div>
                            ${subHtml}
                        </div>
                    `
                }
                if (totalPages == _cur_page) {
                    isEnd = true;
                    html += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && results.length == 0) {
                    isEnd = true;
                    html = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(html);
                } else {
                    $(_pageId + " .finance_pro").html(html);
                }
                $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
            } else {
                layerUtils.iAlert(error_info);
            }
        };
        service.reqFun102182(param, gettcHistoryListCallBack);
    }

    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    _cur_page = 1;
                    endTime = "";
                    gettcHistoryList(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        _cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        gettcHistoryList(true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        _cur_page = 1;
        $(_pageId + ".olay").hide();
        isEnd = false;
        $(_pageId + " .finance_pro").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
