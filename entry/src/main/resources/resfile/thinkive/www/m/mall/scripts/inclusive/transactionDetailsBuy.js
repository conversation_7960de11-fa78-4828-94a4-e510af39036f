// 交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#inclusive_transactionDetailsBuy ";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");

    function init() {
        var param = appUtils.getPageParam();
    	//申购交易详情查询
    	reqFun102065(param);
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }
 
    function reqFun102065(param){
    	 service.reqFun102065(param, function(data){
    		 if (data.error_no != "0") {
    			 layerUtils.iAlert(data.error_info);
    			 return;
    		 }
    		 
    		 var results = data.results[0];
             if (!results || results.length == 0) {
                 return;
             }
    		 //数据处理 空 和 --
             results = tools.FormatNull(results);
             
             //交易金额
             var trans_amt = tools.fmoney(results.trans_amt);
             trans_amt = trans_amt + "元";
             //交易状态
             var trans_status = results.trans_status;
             var trans_status_name = tools.fundDataDict(trans_status,"pub_purchase_status_name");
             var item = $(_pageId + " .rule_box .item")[1];
             if(trans_status != "8"){
                $(item).find(".name").addClass("undone");
             }else{
                $(item).find(".name").removeClass("undone");
             }
             //产品名称
             var prod_name = results.prod_name;
             //产品代码
             var prod_code = "("+results.prod_code+")";
             //申购日期
             var trans_date = results.trans_date;
             if(trans_date != "--"){
            	 trans_date = tools.FormatDateText(trans_date.substring(4,8));
             }
             //确认日期
             var ack_date = results.ack_date;
             if(ack_date != "--"){
            	 ack_date = tools.FormatDateText(ack_date.substring(4,8));
             }
             //交易流水号
             var trans_serno = results.trans_serno;
             //确认份额  
             var ack_vol = tools.fmoney(results.ack_vol);
             ack_vol = ack_vol + "份";
             //确认金额  
             var ack_amt = tools.fmoney(results.ack_amt);
             ack_amt = ack_amt + "元";
             //确认净值 截取4位小数
             var ack_nav = results.ack_nav;
             if(ack_nav == 0){
                ack_nav =ack_nav+"";
             }
             if(ack_nav && ack_nav != "--"){
                ack_nav = ack_nav+"";
                var dotIndex = ack_nav.indexOf(".");
                if(ack_nav.substring(0,dotIndex + 1)>4){
                   ack_nav = ack_nav.substring(0,dotIndex + 5);
                }else{
                   ack_nav = (+ack_nav).toFixed(4);
                }
             }
             
            //  var ack_nav = tools.fmoney(results.ack_nav);
             ack_nav = ack_nav + "元";
             //手续费
             var feet_amt = tools.fmoney(results.feet_amt);
             feet_amt = feet_amt + "元";
             //交易时间  
             var trans_time = results.trans_time;
             trans_time = tools.ftime(trans_time);
             	
             $(_pageId + " .trans_amt").html(trans_amt);
             $(_pageId + " #trans_status").html(trans_status_name);
             $(_pageId + " #prod_name").html(prod_name);
             $(_pageId + " #prod_code").html(prod_code);
             $(_pageId + " #trans_date").html(trans_date);
             $(_pageId + " #ack_date").html(ack_date);
             $(_pageId + " #trans_serno").html(trans_serno);
             $(_pageId + " #ack_vol").html(ack_vol);
             $(_pageId + " #ack_amt").html(ack_amt);
             $(_pageId + " #ack_nav").html(ack_nav);
             $(_pageId + " #feet_amt").html(feet_amt);
             $(_pageId + " #trans_time").html(trans_time);
    	 });
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
    	$(_pageId + " .trans_amt").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " #trans_date").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " #ack_amt").html("--");
        $(_pageId + " #ack_nav").html("--");
        $(_pageId + " #feet_amt").html("--");
        $(_pageId + " #trans_time").html("--");
    }

     
    
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
