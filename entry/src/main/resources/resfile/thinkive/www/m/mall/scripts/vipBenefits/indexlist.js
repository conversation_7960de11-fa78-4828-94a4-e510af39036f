// 会员福利 - 列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService");
    var _pageId = "#vipBenefits_index";
    let common = require("common");
    let ut = require("../common/userUtil");
    var _pageCode = "vipBenefits/index";
    var tools = require("../common/tools");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    let userInfo,cust_no,mobile
    var usable_vol;//总积分
    let chooseData  //调用原生分享传参
    var cardVoucherType;//卡券类型 1为话费券，2为京东券
    function init() {
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        mobile = userInfo.mobileWhole
        reqFun108001();//会员总积分查询
        reqFun102080();//会员活动图标类型
        // reqFun102081();//会员赚积分活动查询
        reqFun108008();//活动列表查询
        var cardtType = appUtils.getSStorageInfo("cardtType");
        if(validatorUtil.isNotEmpty(cardtType)){
            cardtTypeShow(cardtType)
            appUtils.clearSStorage("cardtType");
        }
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
    }
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), ()=> {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), ()=> {
            tools.saveAlbum(_pageCode)
        });
        //积分明细
        appUtils.bindEvent($(_pageId + " #integral_records"),  ()=> {
            appUtils.pageInit(_pageCode, "vipBenefits/records")
        });
        //图标控制
        // type 1:积分抽奖 2:赚积分 3:积分兑换  4:我的卡券;state 1:敬请期待 2:正常使用
        appUtils.preBindEvent($(_pageId + " .vipBenefits_list"), "li",  function (e) {
            e.stopPropagation();
            e.preventDefault();
            var str = $(this).attr("id");
            var str_type = str.slice(0, str.indexOf('_'));
            var str_state = str.slice(str.indexOf('_') + 1);
            var title = $(this).find("p").html();
            if (str_state == '1') {
                layerUtils.iAlert("敬请期待!");
            } else if (str_state == '2' && str_type == '2') {//2:赚积分
                $(_pageId + " #benefitsBox h3").html(title);
                $(_pageId + " .type_content").hide();
                $(_pageId + " #benefitsBox").show();
            } else if (str_state == '2' && str_type == '3') {//3:积分兑换
                $(_pageId + " #benefitsShopType h3").html(title);
                $(_pageId + " .type_content").hide();
                $(_pageId + " #benefitsShopType").show();
            }
        }, "click");

        //赚积分
        // activity_type 1.邀好友基数奖  state  1:正在进行 2:未开始 3:已结束 4:领积分 5:已完成
        appUtils.preBindEvent($(_pageId + " .earnPoints"), "li .btn",  function (e){
            e.stopPropagation();
            e.preventDefault();
            var actStr = $(this).attr("id");
            var actStr_type = actStr.slice(0, actStr.indexOf('_'));
            var actStr_state = actStr.slice(actStr.indexOf('_') + 1);
            sessionStorage.activity_id = $(this).attr("activity_id")
            chooseData = {
                id:$(this).attr("activity_id"),
                activity_type:actStr_type,
                mobile:mobile,
                cust_no:cust_no,
                actStr_state:actStr_state,
                share_template:$(this).attr("share_template"),
                jump_to_qrcode_page:$(this).attr("jump_to_qrcode_page"),
            }
            if ((chooseData.activity_type == '1' || chooseData.activity_type == '3') && actStr_state == '1' && chooseData.jump_to_qrcode_page == '1') {
                sessionStorage.share_template = chooseData.share_template
                return appUtils.pageInit(_pageCode, "vipBenefits/friendInvitation"); 
            }
            if (actStr_state == '2') {//2:未开始
                layerUtils.iAlert("活动尚未开始!");
            } else if (actStr_state == '1') {//2:正在进行，1.邀好友基数奖
                $(_pageId + " #pop_layer").show()
            } else if (actStr_state == '3') {//3:已结束
                layerUtils.iAlert("活动已结束!");
            } else if (actStr_state == '4') {   //领积分
                getPoints()
            }
        }, "click");
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            common.share("22", chooseData.share_template);
            setUser() //记录用户行为
            // return console.log()
            if(chooseData.activity_type == 2 && chooseData.actStr_state == 1) editActivityState() //修改活动状态
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            common.share("23", chooseData.share_template,);
            setUser() //记录用户行为
            if(chooseData.activity_type == 2 && chooseData.actStr_state == 1)  editActivityState() //修改活动状态
        });
        //点击话费券
        appUtils.preBindEvent($(_pageId + " #benefitsShop"), "li",  function(e) {
            e.stopPropagation();
            e.preventDefault();
            var info = JSON.parse($(this).find(".info").html());//兑换所需积分
            var shopStr_type = info.type;//1为话费券，2为京东券
            var shopStr_points = info.points;//所需积分
            var shopStr_state = info.state;//state-0正常1敬请期待
            if (shopStr_type == '1' || shopStr_type == '2') {//1为话费券，2为京东券
                if(shopStr_state == '1'){
                    layerUtils.iAlert("敬请期待!");
                    return;
                }
                if (parseFloat(usable_vol) < parseFloat(shopStr_points)) {//总积分小于兑换所需积分
                    layerUtils.iConfirm("您的积分不足!",  ()=> {
                    },  ()=> {
                        $(_pageId + " .type_content").hide();
                        $(_pageId + " #benefitsBox").show();
                    }, "确定", "赚积分");
                    return;
                }
                //缓存积分兑换信息
                let exchangeInfo = info;
                appUtils.setSStorageInfo("exchangeInfo", exchangeInfo);
                appUtils.pageInit(_pageCode, "vipBenefits/phoneVouchersConfirm", info);
            } else {
                layerUtils.iAlert("敬请期待!");
            }
        }, "click");
        //积分换话费
        appUtils.preBindEvent($(_pageId + " #benefitsShopType"), " #phone_securities",  function(e) {
            $(_pageId + " #benefitsShop .placeholder").hide()
            $(_pageId + " #benefitsShop .benefitsCon").html("")
            $(_pageId + " #benefitsShop h3").html('积分兑换——换话费<span class="getback" cardVoucherType="1">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").show();
            reqFun108004();//商品查询
        });
        //积分换京东E卡
        appUtils.preBindEvent($(_pageId + " #benefitsShopType"), " #Jingdong_card",  function(e) {
            $(_pageId + " #benefitsShop .placeholder").hide()
            $(_pageId + " #benefitsShop .benefitsCon").html("")
            $(_pageId + " #benefitsShop h3").html('积分兑换——换京东E卡<span  class="getback" cardVoucherType="2">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").hide();
            reqFun108004();//商品查询
        });
        //话费和京东E卡返回
        appUtils.preBindEvent($(_pageId + " #benefitsShop"), " .getback",  function(e) {
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShopType").show();
        });

    }
    //从积分兑换页面返回，展示信息控制
    function cardtTypeShow(cardtType){
        if(validatorUtil.isNotEmpty(cardtType) && cardtType == '1'){
            //类型,1为话费券，2为京东券
            $(_pageId + " #benefitsShop h3").html('积分兑换——换话费<span class="getback" cardVoucherType="1">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").show();
            reqFun108004();//商品查询
        }else if(validatorUtil.isNotEmpty(cardtType) && cardtType == '2'){
            $(_pageId + " #benefitsShop h3").html('积分兑换——换京东E卡<span  class="getback" cardVoucherType="2">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").hide();
            reqFun108004();//商品查询
        }
    }
    //领积分
    function getPoints(){
        service.reqFun108011(chooseData, (data)=> {
            // $(_pageId + " .vipBenefits_list").html('');
            reqFun108008()
            reqFun108001()
            if( data.error_info ) layerUtils.iAlert(data.error_info);
            
        })
    }
    //修改活动状态
    function editActivityState(){
        service.reqFun108010(chooseData, (data)=> {
            if (data.error_no == 0) {
                setTimeout(() => {
                    layerUtils.iAlert('分享成功');
                    reqFun108008()
                }, 3000);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //记录用户行为
    function setUser(){
        service.reqFun108009({id:chooseData.id,cust_no:cust_no}, (data)=> {
            // console.log(data,'记录')
            if (data.error_no == 0) {
                // console.log(results,'----积分')
                // console.log('记录成功')
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //会员总积分查询(108001)
    function reqFun108001() {
        service.reqFun108001({cust_no:cust_no,mobile:mobile}, (data)=> {
            if (data.error_no == 0) {
                let results = data.results[0];
                // console.log(results,'----积分')
                usable_vol = results.usable_vol;
                $(_pageId + ' #integral').html(usable_vol);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //会员活动图标类型(102080)
    function reqFun102080() {
        service.reqFun102080({}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results;
                // console.log(results,'----会员活动')
                var htmls = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        htmls += "<li id='" + results[i].type + "_" + results[i].state + "'>" +
                            "<img src='" + global.oss_url + results[i].img + "' alt=' '>" +
                            "<p>" + results[i].name + "</p>" +
                            " </li>";
                        if (results[i].type == '2') {//2.赚积分
                            $(_pageId + " #benefitsBox h3").html(results[i].name);
                        } else if (results[i].type == '3') {//3:积分兑换
                            $(_pageId + " #benefitsShopType h3").html(results[i].name);
                        }
                    }
                }
                $(_pageId + " .vipBenefits_list").html(htmls);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //会员赚积分活动查询-102081
    function reqFun102081() {
        service.reqFun102081({},  (data)=> {
            if (data.error_no == 0) {
                let results = data.results;
                let htmls = "";
                // console.log(results,'----积分活动')
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        htmls += "<li class='benefitsList'>" +
                            "<div class='benefitsTitle'>" +
                            "<h4>" + results[i].name + "</h4>" +
                            "<div class='benefits'>" + results[i].activity_tips + "</div>" +
                            "</div>" +
                            "<div class='benefitsBtn'><a class='btn active' id='" + results[i].activity_type + "_" + results[i].state + "'>" + results[i].btn_name + "jump_to_qrcode_page='"+ results[i].share_template + "share_template='"+ results[i].share_template + " </a></div>" +
                        "</li>";
                    }
                } else {
                    $(_pageId + " #benefitsBox .placeholder").show();
                }
                $(_pageId + " #benefitsBox .benefitsCon").html(htmls);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //商品查询-108004
    function reqFun108004() {
        cardVoucherType = $(_pageId + " #benefitsShop h3 .getback").attr('cardvouchertype')
        service.reqFun108004({exchange_type:cardVoucherType}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results;
                var htmls = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        htmls += '<li class="benefitsList">' +
                            '<div class="info" style="display: none">' + JSON.stringify(results[i]) + '</div>' +
                            '<div class="phoneSecurities">' +
                            '<img src="' + global.oss_url + results[i].img + '" alt="">' +
                            '</div>' +
                            '<p>' + results[i].name + '</p>' +
                            '<h4><span class="jifen">' + results[i].points + '</span>积分</h4>' +
                            '</li>';
                    }
                } else {
                    $(_pageId + " #benefitsShop .placeholder").show();
                    $(_pageId + " #benefitsShop .warm_prompt").hide();
                }
                $(_pageId + " #benefitsShop .benefitsCon").html(htmls);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //活动列表查询
    function reqFun108008(){
        let mobile = userInfo.mobileWhole
        service.reqFun108008({mobile:mobile,cust_no:cust_no}, (data)=> {
            if (data.error_no == 0) {
                // console.log(data,'活动列表')
                let arr = data.results
                let html = ""
                //rewardActive
                if(arr && arr.length){
                    for (var i = 0; i < arr.length; i++) {
                        
                        let addClass = arr[i].activity_state == 4 ? 'rewardActive' : arr[i].activity_state == 5 ? 'unactive' : 'active'
                        // let smallTip = arr[i].count >= 0 ?"<span class='m_font_size10'>(已邀请" + arr[i].count + "位)</span>" :''
                        if(arr[i].count >= 0 && arr[i].show_schedule == 1){
                            smallTip = "<span class='m_font_size10'>(已邀请" + arr[i].count + "位)</span>"
                        }else{
                            smallTip = ''
                        }
                        html += "<li class='benefitsList'>" +
                            "<div class='benefitsTitle'>" +
                            "<h4>" + arr[i].name + smallTip + "</h4>" +
                           
                            "<div class='benefits'>" + arr[i].activity_tips + "</div>" +
                            "</div>" +
                            "<div class='benefitsBtn'><a share_template='" + arr[i].share_template +"' jump_to_qrcode_page='" + arr[i].jump_to_qrcode_page + "'class='"+ 'btn ' + addClass + " ' activity_id='" + arr[i].id +"' id='" + arr[i].activity_type + "_" + arr[i].activity_state + "'>" + arr[i].btn_name + "</a></div>" +
                        "</li>";
                    }
                }else{
                    $(_pageId + " #benefitsBox .placeholder").show();
                }
                $(_pageId + " #benefitsBox .benefitsCon").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //销毁事件
    function destroy() {
        $(_pageId + " .benefitsCon").html("");
        $(_pageId + ' #integral').html("--");
        $(_pageId + " .vipBenefits_list").html("");
        $(_pageId + " #benefitsShop .placeholder").hide();
        usable_vol = "";
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});