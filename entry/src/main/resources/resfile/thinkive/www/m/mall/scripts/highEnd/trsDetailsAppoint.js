// 交易记录详情- 预约购买
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            _pageId = "#highEnd_trsDetailsAppoint ";
            _pageCode = "highEnd/trsDetailsAppoint";
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    var jymm,trans_serno,prod_sub_type2,nextPageData,detailsInfo;
    function init() {
        // var param = appUtils.getPageParam();
        var param = appUtils.getPageParam() ? appUtils.getPageParam() : appUtils.getSStorageInfo("nextPageData");
        nextPageData = param;
        prod_sub_type2 = param.prod_sub_type2;
        //页面埋点初始化
        tools.initPagePointData();
        if (param.prod_sub_type2 == "90") {
            $(_pageId + " .normal").removeClass("hidden").addClass("block");
            $(_pageId + " .small").addClass("hidden");
        }  else {
            $(_pageId + " .small").removeClass("hidden").addClass("block");
            $(_pageId + " .normal").addClass("hidden");
        }
        //预约购买交易详情查询
        reqFun102085(param);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #cancelorderBtn"), function () {
            let operationId = 'cancelorder'
            layerUtils.iConfirm("<span style='color:#508cee'>" + tools.FormatDateText(detailsInfo.its_date.substring(4, 8)) + "14:00</span>前可以撤单", function () {
                if(prod_sub_type2 == '200'){
                    let money = $(_pageId + " .money").text();
                    let trans_pay = $(_pageId + " #trans_pay").text();
                    let str;
                    // if(trs_state == '0'){
                    //     str = `赎回申请将撤销`
                    // }else{
                        
                    // }
                    str = `申请撤单，资金<em>${money}</em>将退回至<em>${trans_pay}</em>`
                    $(_pageId + " #rechargeInfo").html(str);
                    $(_pageId + " .pop_layer").show();
                    //呼出交易密码
                    passboardEvent();
                    monkeywords.flag = 0;
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "highEnd_trsDetailsAppoint";
                    param["eleId"] = "jymm";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "4";
                    require("external").callMessage(param);
                    
                }else{
                    let param = {};
                    param["funcNo"] = "50220";
                    param["telNo"] = require("gconfig").global.custServiceTel;
                    param["callType"] = "0";
                    require("external").callMessage(param);
                }
            }, function () {
            }, "继续撤单", "取消",operationId);
        });
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            passboardEvent();
            monkeywords.flag = 0;
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_trsDetailsAppoint";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").hide()
            // $(_pageId + " .password_box").hide()
            $(_pageId + " #jymm").val("");
            guanbi();
        });
        //调用撤单
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide()
            // $(_pageId + " .password_box").hide()
            var trans_pwd = $(_pageId + " #jymm").val()
            guanbi();
            if (trans_pwd.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //验证交易密码
            service.getRSAKey({}, function (data) {
            if (data.error_no == "0") {
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, trans_pwd);
                var param = {
                    "trans_pwd": trans_pwd
                }
                var callBack = function (resultsVo) {
                    if (resultsVo.error_no == 0) {
                        //交易密码验证成功
                        service.reqFun106055({trans_serno:trans_serno}, (datas) => {
                            if (datas.error_no == 0) {
                                let results = datas.results;
                                nextPageData.appoint = true;
                                // console.log(results)
                                appUtils.pageInit(_pageCode, "highEnd/cancelOrderResultNew", nextPageData);
                            } else {
                                layerUtils.iAlert(datas.error_info);
                            }
                        })
                    } else {
                        layerUtils.iAlert(resultsVo.error_info);
                    }
                };
                appUtils.setSStorageInfo("nextPageData", nextPageData);
                service.reqFun101025(param, callBack)
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, {isLastReq: false});
        });
    }
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            // 键盘完成按钮的事件
            keyBoardFinishFunction: function () {
            },
            // 键盘的输入事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            }
        };
    }

    function reqFun102085(param) {
        service.reqFun102085(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);
            detailsInfo = results;
            //交易金额
            var trans_amt = tools.fmoney(results.trans_amt);
            trans_amt = trans_amt + "元";
            //交易状态
            var trans_status = results.trans_status;
            if(param.sub_busi_code_e != "12208" && param.sub_busi_code_e != "12210" && param.sub_busi_code_e != "12005" && param.sub_busi_code_e != "12006" && param.sub_busi_code_e != "12007" && param.sub_busi_code_e != "12211" && param.sub_busi_code_e != "12212" && param.sub_busi_code_e != "12209" && param.sub_busi_code_e != "12204"){
            	tools.isShowCancelorderBtn(_pageId,trans_status, "pri_trans_status_name",results.its_date,results.its_flag,prod_sub_type2,'1');
            }
            // tools.isShowCancelorderBtn(_pageId,trans_status, "pri_trans_status_name",results.its_date,results.its_flag,prod_sub_type2,'1');
            var trans_status_name = tools.fundDataDict(trans_status, "pri_trans_status_name");

            //待确定，暂时展示三个成功状态
            var item0 = $(_pageId + " .rule_box .item")[0];
            var item1 = $(_pageId + " .rule_box .item")[1];
            var item2 = $(_pageId + " .rule_box .item")[2];

            if (true) {
                //显示三个成功状态
                $(item0).find(".name").removeClass("undone");
                $(item1).find(".name").removeClass("undone");
                $(item2).find(".name").removeClass("undone");
            }

            //产品名称
            var prod_name = results.prod_name;
            //产品代码
            var prod_code = "(" + results.prod_code + ")";
            //预约日期
            var appointment_date = results.appointment_date;
            if (appointment_date != "--") {
                appointment_date = tools.FormatDateText(appointment_date.substring(4, 8));
            }
            //买入日期
            var trans_date = results.trans_date;
            if (trans_date != "--") {
                trans_date = tools.FormatDateText(trans_date.substring(4, 8));
            }
            //确认日期
            var ack_date = results.ack_date;
            if (ack_date != "--") {
                ack_date = tools.FormatDateText(ack_date.substring(4, 8));
            }
            //交易流水号
            trans_serno = results.trans_serno;
            //确认份额
            var ack_vol = tools.fmoney(results.ack_vol);
            ack_vol = ack_vol + "份";
            //确认金额
            var ack_amt = tools.fmoney(results.ack_amt);
            ack_amt = ack_amt + "元";
            //交易时间
            var trans_time = results.trans_time;
            trans_time = tools.ftime(trans_time);
            //确认净值
            var ack_nav = tools.fmoney(results.ack_nav, 4);
            ack_nav = ack_nav + "元";
            //手续费
            var feet_amt = tools.fmoney(results.feet_amt);
            feet_amt = feet_amt + "元";
            //撤单备注
            if (results.remark && results.move_date) {
                $(_pageId + " #remark").html(tools.ftime(results.move_time) + " " + results.remark);
                $(_pageId + " .remark_box").show();
            } else {
                $(_pageId + " .remark_box").hide();
            }

            $(_pageId + " .trans_amt").html(trans_amt);
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #prod_code").html(prod_code);
            $(_pageId + " #appointment_date").html(appointment_date);
            $(_pageId + " #buy_date").html(trans_date);
            $(_pageId + " #ack_date").html(ack_date);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " #ack_vol").html(ack_vol);
            $(_pageId + " #ack_nav").html(ack_nav);
            $(_pageId + " #ack_amt").html(ack_amt);
            $(_pageId + " #feet_amt").html(feet_amt);
            $(_pageId + " #trans_time").html(trans_time);
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #trans_amt").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " #appointment_date").html("--");
        $(_pageId + " #buy_date").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " #ack_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " .remark_box").hide();
        $(_pageId + " .small").removeClass("block").addClass("hidden");
        $(_pageId + " .normal").removeClass("block").addClass("hidden");
        $(_pageId + " #ack_nav").html("--");
        $(_pageId + " #feet_amt").html("--");
        $(_pageId + " #cancelorderBtn").hide();
        $(_pageId + " #overtimeBtn").hide();
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
