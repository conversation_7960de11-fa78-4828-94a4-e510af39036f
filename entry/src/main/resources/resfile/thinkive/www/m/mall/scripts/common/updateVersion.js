/**
 * 创建于2017.12.29
 *用于对app系统升级，包括静默升级与自动跟新
 *
 */

define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var gconfig = require("gconfig");
    var service = require("mobileService"); //业务层接口，请求数据
    var external = require("external");
    var platform = require("gconfig").platform;
    var global = gconfig.global;

    //非静默升级
    function updateVers_show(_pageId) {
        // appUtils.setSStorageInfo("To_update", "1");
        var chancel = null;
        var soft_no = global.soft_no;
        var oVersion = external.callMessage({funcNo: "50010"});
        oVersion = oVersion.results ? oVersion.results[0] : {versionSn: global.version_code};
        var version = oVersion.versionSn;
        global.version_code = version;
        // if (platform == "1") {
        //     chancel = "1";//手机
        // } else if (platform == "0") {
        //     chancel = "0";//PC
        // } else if (platform == "2") {
        //     chancel = "2";//IOS
        // } else {

        // }
        chancel = platform;
        var queryParam = {
            "versionsn": version,
            "channel": chancel,
            "soft_no": soft_no
        };
        service.reqFun102070(queryParam, function (data) {
            if (data.error_no == 0) {
                if (data.results.length != 0) {
                    var version_name = data.results[0].versionName;  //版本应用名称
                    var description = data.results[0].description;  //更新说明
                    var version_size = data.results[0].versionSize; //版本更新大小
                    var app_version = data.results[0].versionCode; //
                    var typeFlag = data.results[0].typeFlag;  //下载类型(0：原生，1：H5）
                    var isSilenceUpdate = data.results[0].isSilenceUpdate;  //是否是静默升级
                    var url = data.results[0].downloadUrl;
                    var versionStoreUpdate = data.results[0].versionStoreUpdate;
                    let invokeParam;
                    $(_pageId + ' #edition').html(version_name);
                    $(_pageId + ' #description').html(description);
                    $(_pageId + ' #Size').html(version_size);
                    if (isSilenceUpdate == "0" && typeFlag == "1") {//静默升级  此处不做静默升级(静默升级任何情况不弹框)
                        return;
                    }
                    if (data.results[0].updateFlag == "0") {// 0 普通更新
                        if (parseFloat(version) < parseFloat(app_version)) {
                            $(_pageId + " #activityDialog").hide();
                            $(_pageId + ' .pop_layer').show();
                            $(_pageId + ' .update_prompt').show();
                            $(_pageId + ' #close').show();
                            $(_pageId + ' #immediateUpdates').css('width', '50%');
                            // 点击更新事件
                            appUtils.bindEvent($(_pageId + " #immediateUpdates"), function () {
                                //缓存用户选中版本
                                // setLocalStorage("sceneRefresh",'0');
                                // setLocalStorage("userChooseRefresh",'0');
                                $(_pageId + ' .pop_layer').hide();
                                $(_pageId + ' .update_prompt').hide();
                                invokeParam = {
                                    funcNo: "50201", // String	功能号	Y
                                    url: url, // 更新地址
                                    type: typeFlag, //下载类型(0：原生，1：H5）
                                    version: version_name,  //app版本
                                    isShowUpdateTip: isSilenceUpdate,
                                    versionSn: app_version //app版本序列号
                                };
                                external.callMessage(invokeParam);

                            });

                        }
                    } else if (data.results[0].updateFlag == "1") { //1 强制更新
                        if (parseFloat(version) < parseFloat(app_version)) {
                            $(_pageId + " #activityDialog").hide();
                            $(_pageId + ' .pop_layer').show();
                            $(_pageId + ' .update_prompt').show();
                            $(_pageId + ' #close').hide();
                            $(_pageId + ' #immediateUpdates').css('width', '100%');
                            // 点击更新事件
                            appUtils.bindEvent($(_pageId + " #immediateUpdates"), function () {
                                //缓存用户选中版本
                                // setLocalStorage("sceneRefresh",'0');
                                // setLocalStorage("userChooseRefresh",'0');
                                $(_pageId + ' .pop_layer').hide();
                                $(_pageId + ' .update_prompt').hide();
                                if (typeFlag == "0" && platform == "1") { //安卓原生升级
                                    if(versionStoreUpdate == 1){
                                        invokeParam = {
                                            funcNo: "80318", // String	功能号	Y
                                            url: url, //下载地址）
                                        };
                                    }else{
                                        invokeParam = {
                                            funcNo: "80050", // String	功能号	Y
                                            downloadLink: url, //下载地址）
                                        };
                                    }
                                } else {
                                    invokeParam = {
                                        funcNo: "50201", // String	功能号	Y
                                        url: url, // 更新地址
                                        type: typeFlag, //下载类型(0：原生，1：H5）
                                        version: version_name,  //app版本
                                        isShowUpdateTip: isSilenceUpdate,
                                        versionSn: app_version //app版本序列号
                                    };
                                }
                                external.callMessage(invokeParam);
                            });
                        }
                    }
                }
            }
        }, {"isShowWait": false});
    }

    //静默升级，自动更新
    function updateVers(_pageId) {
        appUtils.setSStorageInfo("To_update", "1");
        var chancel = null;
        var soft_no = global.soft_no;
        var oVersion = external.callMessage({funcNo: "50010"});
        oVersion = oVersion.results ? oVersion.results[0] : {versionSn: global.version_code};
        var version = oVersion.versionSn;

        global.version_code = version;
        // if (platform == "1") {
        //     chancel = "1";//手机
        // } else if (platform == "0") {
        //     chancel = "0";//PC
        // } else if (platform == "2") {
        //     chancel = "2";//IOS
        // } else {

        // }
        chancel = platform;
        var queryParam = {
            "versionsn": version,
            "channel": chancel,
            "soft_no": soft_no
        };
        service.reqFun102070(queryParam, function (data) {
            if (data.error_no == 0) {
                if (data.results.length != 0) {
                    var version_name = data.results[0].versionName;  //版本应用名称
                    var description = data.results[0].description;  //更新说明
                    var version_size = data.results[0].versionSize; //版本更新大小
                    var app_version = data.results[0].versionCode; //
                    var typeFlag = data.results[0].typeFlag;  //下载类型(0：原生，1：H5）
                    var isSilenceUpdate = data.results[0].isSilenceUpdate;  //是否是静默升级
                    var url = data.results[0].downloadUrl;
                    $(_pageId + ' #edition').html(version_name);
                    $(_pageId + ' #description').html(description);
                    $(_pageId + ' #Size').html(version_size);
                    if (isSilenceUpdate == "0" && typeFlag == "1") {//静默升级
                        if (parseFloat(version) < parseFloat(app_version)) {
                            //缓存用户选中版本
                            // setLocalStorage("sceneRefresh",'0');
                            // setLocalStorage("userChooseRefresh",'0');
                            $(_pageId + " #activityDialog").hide();
                            var invokeParam = {
                                funcNo: "50201", // String	功能号	Y
                                url: url, // 更新地址
                                type: typeFlag, //下载类型(0：原生，1：H5）
                                version: version_name,  //app版本
                                isShowUpdateTip: isSilenceUpdate,
                                versionSn: app_version //app版本序列号
                            };
                            external.callMessage(invokeParam);
                        }
                    }
                }
            }
        }, {"isShowWait": false});
    }

    var updateVersion = {
        "updateVers_show": updateVers_show,
        "updateVers": updateVers
    }
    //暴露对外的接口
    module.exports = updateVersion;
});
