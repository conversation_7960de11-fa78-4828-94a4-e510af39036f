<div class="page" id="account_perfectInfo" data-pageTitle="完善信息" data-refresh="true">

    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray" id="getBack"><span>返回</span></a>
                <h1 class="text_gray text-center">完善信息</h1>
                <a id="kefu" href="javascript:void(0)" class="coustomer-service-icon">
                    <img src="./images/customerService.png">
                </a>
            </div>
        </header>
        <article class="bg_blue" style="padding-bottom: 0">
            <div class="bank_form">
                <div class="input_box">

                    <div class="check_tips slidedown in " >
                        <p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">姓名</label>
                        <input id="cardPerson" type="text" class="ui input" readonly="readonly"
                               style="color: #666666;"/>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">证件类型</label>
                        <input id="cert_type" type="text" class="ui input" readonly="readonly" style="color: #666666;"/>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">证件号码</label>
                        <input id="idCard" maxlength="19" type="tel"
                               class="ui input" readonly="readonly"
                               style="color: #666666;"/>
                    </div>
                    <div class="ui field text mb10">
                        <label class="ui label">证件照片</label>
                        <input maxlength="19" class="ui input" id="ocrIsUpload" readonly="readonly" style="color: #666666;"/>
                        <a  href="javascript:void(0);" id="reUpload" class="link_btn float_right" style="display: none;line-height: 0.44rem;margin-right: 0.1rem;color: #319ef2;"></a>
                    </div>
                    <div class="input_box" style="border-top:0">
                        <div class="ui field text right_icon" style="border-top:1px solid  #e6e6e6">
                            <label class="ui label">个人年收入（万）</label><input id="income" placeholder="请选择年收入" class="ui input"
                                                                           readonly="readonly"
                                                                           style="color: #666666;padding-right: 0.3rem"/>
                        </div>
                    </div>
                    <div class="ui field text right_icon" >
                        <label class="ui label">职业</label>
                        <input type="text" class="ui input" readonly="readonly" placeholder="请选择职业"
                               style="color: #666666;" id="ocp"/>
                    </div>

                    <div class="input_box">
                        <div class="ui field text right_icon" style="border: none">
                            <label class="ui label">居住地址</label>
                            <input id="live_address" placeholder="请选择居住地址" class="ui input" readonly  style="color: #666666;padding-right: 0.3rem"/>
                        </div>
                    </div>

                </div>
                <div class="btn mt20" >
                    <a href="javascript:void(0);" class="ui button block rounded" id="next"
                    >完成</a>
                </div>
            </div>
        </article>
    </section>
    <div class="pop_layer" id="moneyBox" style="display: none"></div>
    <div class="password_box" style="display: none;position: absolute;top: 40%;left: 0;right: 0;margin: auto;">
        <div class="password_inner slidedown in" style="padding: 0.1rem;z-index: 9999;">
            <div class="ui field text input_box2 has_border">
                <label class="short_label">个人年收入（万元）</label>
                <span style="position:absolute;height: 0.24rem;line-height:0.24rem;left:1.4rem ;right: 0;" id="inputspanid"><span style="position: absolute; left: 0px; width: auto; color: rgb(153, 153, 153);" text="请输入金额" class="unable">请输入金额</span></span>
                <input custom_keybord="0" type="text" onkeyup="value=value.replace(/[^\d\.\,]/g,'')" onblur="value=value.replace(/[^\d\.\,]/g,'')" id="srje" maxlength="12" class="ui input" placeholder="请输入金额" style="outline: none;border: none;margin-left:5px;display: none;" readonly="readonly">
            </div>
        </div>
    </div>
</div>
