//更换手机号结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        serviceConstants = require("constants"),
        service = require("mobileService"),
        common = require("common"),
        _pageUrl = "bank/changePhoneNumberResult",
        _pageId = "#bank_changePhoneNumberResult ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var tools = require("../common/tools");//升级
    var outputInfo;
    var num;
    var _fund_code = "";

    function init() {
        
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + ".done_btn"), function () {
            appUtils.pageInit(_pageUrl, "login/userIndexs");
        })
    }

     


    function destroy() {
         
    }


    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
