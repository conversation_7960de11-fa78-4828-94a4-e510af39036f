// 修改支付密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        serviceConstants = require("constants"),
        common = require("common"),
        _page_code = "safety/passwordManage",
        _pageId = "#safety_passwordManage ";
    var platform = require("gconfig").platform;
    var external = require("external");
    var riskResult = "";
    var custRiskDesc = "";
    var ut = require("../common/userUtil");
    var userInfo;
    var tools = require("../common/tools");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        if(!userInfo.bankAcct || userInfo.bankAcct == ''){
            // $(_pageId + " #change").hide();
            // $(_pageId + " #phoneNum").css('margin-right','0')
        }else{
            // $(_pageId + " #change").show();
            // $(_pageId + " #phoneNum").css('margin-right','0.9rem')
        }
        //是否展示设置/修改密码 按钮
        passwordBtmShow();
        initRisk();
        setGesturePass(); //设置手势密码
        changeCard();  // 换卡状态查询
        changeCardMessageInfo();// 换卡信息队列查询
        celarBankInfo();
        if(platform == '1'){    //安卓展示指纹登录设置
            $(_pageId + " #fingerprint_parent").show()
        }
    }
    //判断展示修改密码或设置密码 login_pwd_state 1 修改
    function passwordBtmShow(){
        let login_pwd_state = userInfo.login_pwd_state;
        if(login_pwd_state == '1'){
            $(_pageId + " #setLoginPassword").hide();
            $(_pageId + " #modifyLogin").show();
        }else{
            $(_pageId + " #modifyLogin").hide();
            $(_pageId + " #setLoginPassword").show();
        }
    }
    function celarBankInfo() {
        // 清空银行卡信息
        $("#account_setBankCardInfo #oneMoney").html("");
        $("#account_setBankCardInfo #drxe").html("");
        $("#account_setBankCardInfo #bankname").html("");  // 银行名称
        $("#account_setBankCardInfo input").val(""); // 清空所有的记录
    }

    function initRisk() {
        setUserRick();
        showinf();
        getUser_investment_auth()
    }
    //获取用户投资认证
    function getUser_investment_auth() {
        // console.log(userInfo);
        if (userInfo && userInfo.bankAcct) {
            $(_pageId + ' #investment_auth_no_bank').hide();
            $(_pageId + ' #investment_auth').show();
            service.reqFun101040({}, (data) => {
                //获取合格投资人的认证
                if (data.error_no == '0') {
                    //accredited_investor  0:不合格, 1:合格, 2:豁免, 3:未认证, 4:已到期, 5:审核中
                    let res = data.results[0].accredited_investor
                    let parentHtml = $(_pageId + ' #investment_auth')
                    if (res == '3') parentHtml.html('认证').show()
                    if (res == '0' || res == '1') {
                        parentHtml.html('重新认证').show()
                        if (res == 0) parentHtml.parent().find("span").html('认证失败');//风险测评结果显示
                        if (res == 1) parentHtml.parent().find("span").html('已认证');//风险测评结果显示
                    }
                    if (res == '4') parentHtml.html('重新认证').show()
                    if (res == '5') {
                        parentHtml.parent().find("span").html('审核中');//风险测评结果显示
                        parentHtml.hide()
                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        } else {
            // $(_pageId + ' #investment_auth').show();
            $(_pageId + ' #investment_auth').html('认证').hide()
            $(_pageId + ' #investment_auth_no_bank').show();

            return;
        }
    }
    //获得用户风险等级
    function setUserRick() {
        if (userInfo && userInfo.bankAcct) {
            $(_pageId + " #pRrisk_no_bank").hide();
            $(_pageId + " #pRrisk").show();//风险测评结果显示
            //风险测评设置
            let riskName = "";
            riskName = userInfo.riskName
            if (userInfo.invalidFlag == '1') riskName = riskName + '(已到期)'
            if (riskName == "" || riskName == null) {
                $(_pageId + " #pRrisk").html("评估");
                $(_pageId + " #pRrisk").parent().find("span").hide();
            } else {
                $(_pageId + " #pRrisk").parent().find("span").show();
                $(_pageId + " #pRrisk").parent().find("span").html(riskName);//风险测评结果显示
                $(_pageId + " #pRrisk").html("重新测评");
            }
        } else {
            $(_pageId + " #pRrisk_no_bank").show();
            $(_pageId + " #pRrisk").hide();//风险测评结果显示
            $(_pageId + " #pRrisk").parent().find("span").html("");//风险测评结果显示
        }
    }

    //判断邮箱、别名设置状态
    function showinf() {
        // 邮箱
       

        // 别名
        var _otherName = userInfo.otherName;
        if (!_otherName) {
            $(_pageId + " #setOtherName").show();
            $(_pageId + " #updateOtherName").hide();
        } else {
            $(_pageId + " #setOtherName").hide();
            $(_pageId + " #updateOtherName").show();
            $(_pageId + " #otherName_span").html(_otherName);
        }
       
        if (userInfo && userInfo.bankAcct) {
             // 银行预留手机号
            if (userInfo.bankReservedMobile) {
                // $(_pageId + " #bankphonechange_p").show();
                $(_pageId + " #bankphonechange_no_bank").hide();
                $(_pageId + " #bankphonechange").show();
                $(_pageId + " #bankphoneNum").text(userInfo.bankReservedMobile);
            }
            $(_pageId + " #jiaoYi_no_bank").hide();
            $(_pageId + " #modifyJiaoYi").show();
            $(_pageId + " #resetJiaoYi").show();
            // 邮箱
            var email = userInfo.email;
            $(_pageId + " #email_no_bank").hide();
            if (!email) {
                $(_pageId + " #setEmial").show();
                $(_pageId + " #updateEmial").hide();
            } else {
                $(_pageId + " #setEmial").hide();
                $(_pageId + " #updateEmial").show();
                var n = email.indexOf("@");
                var email2 = email.substring(0, 1) + "****" + email.substring(n - 1);
                $(_pageId + " #emial_span").html(email2);
            }
        } else {
             // 银行预留手机号
            $(_pageId + " #bankphonechange_no_bank").show();
            $(_pageId + " #bankphonechange").hide();
            $(_pageId + " #bankphoneNum").text("");

            // 交易密码
            $(_pageId + " #jiaoYi_no_bank").show();
            $(_pageId + " #modifyJiaoYi").hide();
            $(_pageId + " #resetJiaoYi").hide();

            // 邮箱
            $(_pageId + " #setEmial").hide();
            $(_pageId + " #updateEmial").hide();
            $(_pageId + " #email_no_bank").show();
            $(_pageId + " #emial_span").html("");
        }
      
        
        $(_pageId + " #phoneNum").text(userInfo.mobile);
    }

    //绑定事件
    function bindPageEvent() {
        //点击去合格投资者认证
        appUtils.bindEvent($(_pageId + " #investment_auth"), () => {
            appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
            appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor1");
        });
        appUtils.bindEvent($(_pageId + " #cardauditsta_makesure"), function () {
            layerUtils.iConfirm("您的换卡已通过审核,请确认", function () {
                appUtils.pageInit(_page_code, "safety/changecardConfirm");
            })
        })
        //点击换卡
        appUtils.bindEvent($(_pageId + " #changeCard"), function () {
            changeCardJY();
        });

        //点击风险测评
        appUtils.bindEvent(_pageId + " #pRrisk", function () {
            //2017-7-20 jiaxr修改 直接跳转到测评页面（原来重新测评时跳转到评估结果页面）
            if (custRiskDesc == "" || custRiskDesc == null) {
                appUtils.pageInit("safety/passwordManage", "safety/riskQuestion", {});
                // appUtils.pageInit("safety/passwordManage", "account/qualifiedInvestor", {});
            } else {
                appUtils.pageInit("safety/passwordManage", "safety/riskQuestion", {});
            }
        });

        //点击快捷换卡
        appUtils.bindEvent($(_pageId + " #kjchangeCard"), function () {
            kjchangeCardJY();
        });

        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击修改登录密码
        appUtils.bindEvent($(_pageId + " #modifyLogin"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/updateUserPassword", {});
        });
        //点击设置登录密码
        appUtils.bindEvent($(_pageId + " #setLoginPassword"), function () {
            appUtils.pageInit("safety/passwordManage", "login/setPassword", {});
        });
        //点击修改交易密码
        appUtils.bindEvent($(_pageId + " #modifyJiaoYi"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/updateTradersPassword", {});
        });

        appUtils.bindEvent($(_pageId + " .grid_02"), function () {
            $(_pageId + " .pop_layer4").hide();
            $(_pageId + " .card_rules").hide();
        });

        //点击重置密码
        appUtils.bindEvent($(_pageId + " #resetJiaoYi"), function () {
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "safety/resetTradersPassword", {});
            }, true, "您正处于换卡阶段，暂不支持该项操作");
        });

        //点击设置手势密码
        appUtils.preBindEvent($(_pageId + " #gestureManagement"), "#gestureOpen", function () {
            MobilePhoneControl("1");
        }, 'click');
        //点击修改手势密码
        appUtils.preBindEvent($(_pageId + " #gestureManagement"), "#gesturePassword", function () {
            MobilePhoneControl("2");
        }, 'click');
        //点击关闭手势密码
        appUtils.preBindEvent($(_pageId + " #gestureManagement"), "#gestureoff", function () {
            MobilePhoneControl("0");
        }, 'click');
        /*--------------------新增----------------------*/
        //点击平台手机号
        appUtils.bindEvent($(_pageId + " #change"), function () {
            if(userInfo.login_pwd_state != '1' && !userInfo.bankAcct){
                //没有设置登录密码
                return layerUtils.iConfirm("请先设置登录密码", function () {
                }, function () {
                    appUtils.pageInit(_page_code, "login/setPassword", {});
                }, "取消", "去设置");
            }
            appUtils.setSStorageInfo("isUserBankCard", userInfo.bankAcct ? true : false);
            appUtils.pageInit("safety/passwordManage", "safety/changePhoneNumber");
        });
        //变更预留手机号
        appUtils.bindEvent($(_pageId + " #bankphonechange"), function () {
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "safety/changeBankphone", {});
            }, true, "您正处于换卡阶段，暂不支持该项操作");
        });
        //点击设置电子邮箱
        appUtils.bindEvent($(_pageId + " #setEmial"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/setEmial", {});
        });
        //点击修改电子邮箱
        appUtils.bindEvent($(_pageId + " #updateEmial"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/updateEmial", {});
        });
        //点击设置别名
        appUtils.bindEvent($(_pageId + " #setOtherName"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/setOtherName", {});
        });
        //点击修改别名
        appUtils.bindEvent($(_pageId + " #updateOtherName"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/updateOtherName", {});
        });
        //点击通知管理去设置
        appUtils.bindEvent($(_pageId + " #setInformManager"), function () {
            appUtils.pageInit("safety/passwordManage", "safety/setInformManager", {});
        });
        //设置指纹
        appUtils.bindEvent($(_pageId + " #fingerprint"), function () {
            //跳转指纹设置页面
            appUtils.pageInit("safety/passwordManage", "safety/fingerprintPwd", {});
            // var param80319 = {
            //     funcNo: "80319",
            // };
            // external.callMessage(param80319);
        });
        
    }

    //设置手势密码
    function setGesturePass() {
        // 获取用户信息
        if (platform != "0") {
            var param50043 = {
                funcNo: "50043",
                key: "account_password"
            };
            var firstInstall = external.callMessage(param50043);
            // 得到用户手机号码进行查询是否设置过手势密码
            if (firstInstall.results[0].value) {
                firstInstall = firstInstall.results[0].value;
                if (firstInstall) {
                    var account = firstInstall.substring(0, firstInstall.indexOf("_"));
                    var param50263 = {
                        "funcNo": "50263",
                        "account": account
                    };
                    var data = external.callMessage(param50263);
                    var flag = data.results[0].flag;
                    var str = "";
                    if (flag == "1") { //flag	String	状态（0：未设置，1：已设置）
                        str = "手势密码<a href='javascript:void(0);' id='gesturePassword' class='btn_modify'>修改</a>" +
                            "<a href='javascript:void(0);' id='gestureoff' class='btn_reset'>关闭</a>";
                    } else {
                        str = "手势密码<a href='javascript:void(0);' id='gestureOpen' class='btn_reset'>开启</a>";
                    }
                    $(_pageId + " #gestureManagement").html(str);
                }
            } else {
                layerUtils.iMsg(-1, "获取设置信息有误");
            }
        }
    }

    //  手势密码 控件
    function MobilePhoneControl(type) {
        // 获取用户头像信息
        var userImage = "";
        var params = {
            funcNo: "50043",
            key: "photo_url"
        };
        var datas = external.callMessage(params);
        if (datas.results[0].value) {
            userImage = datas.results[0].value;
        }

        var param = {
            funcNo: "50043",
            key: "account_password"
        };
        var data = external.callMessage(param);
        var firstInstall = data.results[0].value;
        var account = firstInstall.substring(0, firstInstall.indexOf("_"));

        var setParam = {
            "funcNo": "50264", //设置手势密码的设置状态
            "moduleName": "mall",
            "flag": type, //flag	String	状态（0：取消手势，1：设置手势，2：修改手势）
            "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
            "account": account,
            "isCanBack": "1",
            "position": "0",
            "errorNum": "5",
            "userImage": userImage
        };
        external.callMessage(setParam);
    }

    //换卡状态查询
    function changeCard(callback) {
        if (userInfo && userInfo.bankAcct) {
            $(_pageId + " #cardauditsta_no_bankAcct").hide();
            service.reqFun101024({}, function (resultVo) {
                // replace_status 0:待审核 1:已审核待确认 2:换卡成功 3:换卡失败 4: 非换卡期间
                // 得到当前页
                if (resultVo.error_no == "0") {
                    var dataList = resultVo.results[0];
                    if (dataList.replace_status == "0") {
                        $(_pageId + " #cardauditsta_z").show();
                        $(_pageId + " #cardauditsta").hide();
                        $(_pageId + " #cardauditsta_makesure").hide();
                    }
                    // else if (dataList.replace_status == "1") {
                    //     $(_pageId + " #cardauditsta_makesure").show();
                    //     $(_pageId + " #cardauditsta").hide();
                    //     $(_pageId + " #cardauditsta_z").hide();
                    //     layerUtils.iConfirm("您的换卡已通过审核,请确认", function () {
                    //         appUtils.pageInit(_page_code, "safety/changecardConfirm");
                    //     })
                    // }
                    else {
                        $(_pageId + " #cardauditsta_makesure").hide();
                        $(_pageId + " #cardauditsta").show();
                        $(_pageId + " #cardauditsta_z").hide();
                        if (callback) callback();
                    }
                } else {
                    layerUtils.iAlert(resultVo.error_info);
                }
            })
        } else {
            $(_pageId + " #cardauditsta_no_bankAcct").show();
        }


    }

    //换卡消息查询
    function changeCardMessageInfo() {
        service.reqFun101028({ type: "0" }, function (data) {
            if (data.error_no == "0") {
                var results = data.results;
                var success = function (id) {
                    /*更新换卡标识*/
                    service.reqFun101029({ "id": id }, function () {

                    });
                };
                if (results.length > 0) {
                    layerUtils.iAlert(results[0].message_text, -1, success(results[0].id));
                }
            } else {
                layerUtils.iAlert(data.error_info)
            }
        })
    }

    //换卡校验
    function changeCardJY() {
        var param = {
            "bank_acct": userInfo.bankAcct
        }
        service.reqFun101044({}, function (data) { // 查询是否存在在途资金
            if (data.error_no == "0") {
                service.reqFun101027(param, function (data) { //查询资产是否为0
                    if (data.error_no == "0") {
                        layerUtils.iLoading(false);
                        appUtils.pageInit(_page_code, "safety/kjyanzheng");
                        return;
                    }else{
                        appUtils.pageInit(_page_code, "safety/yanzheng");
                        return;
                    }
                }, { isLastReq: false });
            }else{
                layerUtils.iConfirm("您有在途资金尚未到账，请到账后再试", function () {
                    return;
                }, function () {
                    $(_pageId + " .pop_layer4").show();
                    $(_pageId + " .card_rules1").show();
                }, "", "查看规则");
            }
        })
        
    }

    //快捷换卡校验
    function kjchangeCardJY() {
        var param = {
            "bank_acct": userInfo.bankAcct
        }
        service.reqFun101044({}, function (data) { // 查询是否存在在途资金
            if (data.error_no == "0") {
                service.reqFun101027(param, function (data) { // 查询总资产是否为0
                    if (data.error_no == 0) {
                        appUtils.pageInit(_page_code, "safety/kjyanzheng");
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iConfirm("您平台总资产为0时，才可以进行快捷换卡", function funOk() {
                            return;
                        }, function funcNo() {
                            $(_pageId + " .pop_layer4").show();
                            $(_pageId + " .card_rules2").show();
                        }, "", "查看规则");
                    }
                }, { isLastReq: false });
            }else{
                layerUtils.iConfirm("您有在途资金尚未到账，请到账后再试", function () {
                    return;
                }, function () {
                    $(_pageId + " .pop_layer4").show();
                    $(_pageId + " .card_rules2").show();
                }, "", "查看规则");
            }
        })
        
    }

    function destroy() {
        custRiskDesc = "";
        $(_pageId + " #modifyLogin").hide();
        $(_pageId + " #setLoginPassword").hide();
        $(_pageId + " #fingerprint_parent").hide()
        $(_pageId + " .pop_layer4").hide();
        $(_pageId + " .card_rules").hide();
        $(_pageId + " #gestureManagement").html("");
        $(_pageId + " #cardauditsta_makesure").hide();
        $(_pageId + " #cardauditsta").hide();
        $(_pageId + " #cardauditsta_z").hide();
        $(_pageId + " #otherName_span").text("");
        $(_pageId + " #emial_span").text("");
        $(_pageId + ' #investment_auth').parent().find("span").html('');
        tools.recordEventData('4','destroy','页面销毁');

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var changePassword = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = changePassword;
});
