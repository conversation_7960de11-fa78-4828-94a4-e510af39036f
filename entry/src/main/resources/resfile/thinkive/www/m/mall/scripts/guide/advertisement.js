// 外链文章
define(function (require, exports, module) {
    var tools = require("../common/tools");
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#guide_advertisement ";
    var share_url = ""; //分享地址
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var platform = gconfig.platform;
    var ut = require("../common/userUtil")

    function init() {

        $(_pageId + " #guangaolianjie").css({height: "100%"});
        var url = appUtils.getPageParam("url");
        var group_id = appUtils.getPageParam("group_id");
        var banner_id = appUtils.getPageParam("banner_id");
        var type = appUtils.getPageParam("type");
        if(group_id && banner_id){
            getPageShareStatus(url,group_id,banner_id)
        }
        if (url == "" || url == null) {
            url = appUtils.getSStorageInfo("extra_url");
        }
        if (url == "" || url == null) {
            appUtils.pageInit("guide/advertisement", "login/userIndexs");
            return;
        }
        if (url.indexOf("ks.wjx.top") > -1 && platform == "2") { //周五答题解决ios展示问题
            $(_pageId + " #guangaolianjie").css({height: 1700})
        }

        // if (!ut.getUserInf()) {
            if(type!= 'kefu') url = setUrlParam("r", Math.random(), url);
            share_url = url;
            $(_pageId + " #guangaolianjie").attr("src", url); //生成随机地址，防止页面不刷新
            return;
        // }
        // var first_split = url.split("//");
        // var without_resource = first_split[1];
        // var second_split = without_resource.split("/");
        // var host_url = second_split[0];
        // var param = {"host_url": host_url};
        // service.getUserAuthorization(param, function (data) {
        //     if (data.error_no == 0) {
        //         var app_secret = data.results[0].app_secret;
        //         url = setUrlParam("token", app_secret, url);
        //     }
        //     url = setUrlParam("r", Math.random(), url);
        //     share_url = url;
        //     $(_pageId + " #guangaolianjie").attr("src", url);
        // });
    }
    function getPageShareStatus(url,group_id,banner_id){
        let data = {
            group_id:group_id,
            busi_id:banner_id,
            page_type:'3',
            pageId:_pageId,
            pageCode:url,
            banner_url:url
        }
        tools.isShowShare(data,'1')
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        }, "click");
        //邀请好友
        appUtils.bindEvent($(_pageId + " #share_img"), function () {
            // 判断是否登录
            if (ut.getUserInf()) {
                // 登录跳转到对应的页面
                $(_pageId + " #pop_layer").show();
            } else {
                common.gestureLogin();
            }
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            common.share("22", "0");
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
//            common.share("23", "1");
        	var activitiesInfo = common.getLocalStorage("activityInfo");
        	var activityInfo_id = common.getLocalStorage("activityInfo_id");
            let activity_id = activityInfo_id.activity_id_url
            if (!activitiesInfo || !activityInfo_id.share_template) {
            	common.share("23", "1",activity_id);
                return;
            }
            common.share("23", activityInfo_id.share_template,activity_id);
        });
        //腾讯QQ
        appUtils.bindEvent($(_pageId + " #share_qq"), function () {
            common.share("24", "2");
        });
        //新浪微博
        appUtils.bindEvent($(_pageId + " #share_sinaWeibo"), function () {
            common.share("1", "4");
        });
        //腾讯微博
        appUtils.bindEvent($(_pageId + " #share_tenxun"), function () {
            common.share("2", "5");
        });

        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
    }

    function setUrlParam(paramName, value, url) {

        if (url.indexOf("?") != -1) {
            url = url + "&" + paramName + "=" + value;
        } else {
            url = url + "?" + paramName + "=" + value;
        }
        return url;
    }

    //返回键的执行函数
    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #guangaolianjie").attr("src", "");
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #share").hide()
        share_url = "";
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
