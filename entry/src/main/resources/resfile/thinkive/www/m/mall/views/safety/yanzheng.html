<div class="page" id="safety_yanzheng" data-pageTitle="身份证信息验证" data-refresh="true">
	<div class="pop_layer4 pop_layer"></div>
	<div class="card_rules">
		<div class="rules_box slideup in">
			<h5>换卡规则</h5>
			<div class="rules_list">
<!--				<strong>1.无未确认的交易（在途资金、单边账等）的情况下可进行换卡</strong>-->
				<strong>1.客户需上传电子资料：</strong>
				<p>a.身份证正反面照片或扫描件。</p>
				<p>b.新银行卡正反面照片或扫描件。</p>
				<p>c.一手持新银行卡正面，一手持身份证正面照片（含本人头像，要求身份证信息、银行卡信息清晰）。</p>
				<strong>2.提交换卡申请后，我公司将在两个工作日（节假日顺延）内进行审核。</strong>
<!--				<strong id="str2">3.提交换卡审核成功后，需在页面上进行短信验证码确认，在确认前不能交易。</strong>-->
			</div>
			<p class="risk_tips">风险提示：换卡存在一定的风险，换卡成功后，取现资金到账银行卡为您变更后新的银行卡，请知晓。</p>
			<div class="grid_02">
				<a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
			</div>
		</div>
	</div>
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">身份信息验证</h1>
				<a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
			</div>
		</header>
		<!-- <article>
			IDENTITY_BOX START
			<div class="identity_box">
				<div class="grid_03">
					<div class="ui field identity_input">
						<em>*</em>
						<input id="realName" type="text" class="ui input" placeholder="真实姓名">
					</div>
				</div>
				<div class="grid_03">
					<div class="ui field identity_input">
						<em>*</em>
						<input id="cardNum" type="text" class="ui input" placeholder="持卡人证件号码">
					</div>
				</div>
				<div class="grid_03">
					<div class="ui field identity_input">
						<em>*</em>
						<input id="changePwd" type="password" class="ui input" placeholder="交易密码">
					</div>
				</div>
				<div class="grid_02 mt10">
					<a href="javascript:void(0)" class="ui button block rounded btn_01" id="next">下一步</a>
				</div>
			</div>
			IDENTITY_BOX END
		</article> -->
		<article class="bg_blue">
			<div class="user_check">
				<!--<h3>请输入身份证信息</h3>-->
				<div class="check_tips slidedown in " style="border-bottom: none;background: none">
					<p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
				</div>
				<div class="input_box">
					<div class="grid_03 grid_02 grid">
						<div class="ui field text rounded input_box2">
							<label class="short_label">真实姓名</label>
							<input type="text" class="ui input" placeholder="" id="realName" type="num" >
						</div>
					</div>
					<div class="grid_03 grid_02 grid">
						<div class="ui field text rounded input_box2">
							<label class="short_label">身份证号</label>
							<input type="text" class="ui input" placeholder="" id="idCard" maxlength="18" type="num" >
						</div>
					</div>
					<a href="javascript:void(0);" class="icon_photo"></a>
				</div>
				<div class="grid_03 grid_02 grid" id="tradeNum1">
					<div class="ui field text rounded input_box2">
						<label class="short_label">交易密码</label>
						<input type="password" class="ui input" placeholder="请输入交易密码" readonly="readonly" id="tradeNum" maxlength="6" style="margin-left:0.2rem;display:none" >
						<div class="simulate_input no_border" >
							<span class="unable" id="jiatradeNum">请输入交易密码</span>
						</div>
					</div>
				</div>
				<!-- <div class="input_box ui field text" style="padding-right: 0rem;" id="tradeNum1">
				<em>*</em>
				<input type="password" class="ui input" placeholder="交易密码" readonly="readonly" id="tradeNum" maxlength="6" style="margin-left:0.2rem;display:none" >
					<div class="simulate_input no_border" style="margin-left:0.2rem">
						<span class="unable" id="jiatradeNum">交易密码</span>
					</div>
				</div> -->
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="xyb">下一步</a>
				</div>
				<div class="m_paddingTop_10 supportedBankCards main_flxe flex_center m_agreement_color">
					*支持的银行卡
				</div>
			</div>
		</article>
	</section>
</div>

