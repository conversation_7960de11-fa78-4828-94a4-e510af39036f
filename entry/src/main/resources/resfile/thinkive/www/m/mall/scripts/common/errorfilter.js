/**
 * <AUTHOR>
 * @原生调用50118错误号过滤
 */
define(function(require, exports, module) {
	var appUtils = require('appUtils');
	var layerUtils = require("layerUtils");
	var gconfig = require("gconfig");
	var global = gconfig.global;
	var	validatorUtil = require("validatorUtil");
	var external = require("external");
	var endecryptUtils = require("endecryptUtils");
	var platform = require("gconfig").platform;
	var fingerprint = require('./fingerprint.js');
	var serviceConstants = require("constants");
	function filterLoginOut(data){
		if(data.error_no == "-999"){
			layerUtils.iMsg(-1,"由于长时间未操作，系统自动跳转到登入前首页!");
			appUtils.clearSStorage("_loginInPageCode");
			appUtils.clearSStorage("_loginInPageParam");
			appUtils.clearSStorage("_isLoginIn");
			appUtils.clearSStorage();
			appUtils.setSStorageInfo("isLoginTimeOut","yes");
			gestureLogin();
		}

		if(data.error_no == "-99903"){
			layerUtils.iAlert("系统检测到您的账户在其他设备登录,如非本人操作,请立即修改密码,客服电话：" + require("gconfig").global.custServiceTel,-1,function(){
				appUtils.clearSStorage("_loginInPageCode");
				appUtils.clearSStorage("_loginInPageParam");
				appUtils.clearSStorage("_isLoginIn");
				appUtils.clearSStorage();
				appUtils.setSStorageInfo("isLoginTimeOut","yes");
				gestureLogin();
			});
		}
		if(data.error_no == "-88801"){
			layerUtils.iAlert("系统升级中，本次升级预计于今日24：00前完成，详询" + require("gconfig").global.custServiceTel,-1,function(){
				appUtils.clearSStorage("_loginInPageCode");
				appUtils.clearSStorage("_loginInPageParam");
				appUtils.clearSStorage("_isLoginIn");
				// appUtils.clearSStorage();
				appUtils.setSStorageInfo("isLoginTimeOut","yes");
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/userIndexs",{});
//                gestureLogin();
			});
		}
	}
	//从apk获取信息
    function getLocalStorage(key) {
        var data = external.callMessage({
            funcNo: "50043",
            key: key,
        })
        if(data && data.results &&data.results.length > 0) {
            return  data.results[0].value
        }
        return "";
    }
	function gestureLogin(skipURL,product_id){
		if(!(skipURL)){
			skipURL="";
		}else{
			appUtils.setSStorageInfo("skipURL",skipURL);
		}
		if(!(product_id)){
			product_id="";
		}else{
			appUtils.setSStorageInfo("product_id",product_id);
		}
		// 得到当前页
		var pageId = $("body .page[data-display='block']").attr("id");
		var pageCode = pageId.replace("_", "/");

		// 获取用户信息
		if(platform != "0"){
			// 存储之前的路径 和必须要的参数
			var paramurl = {
				funcNo: "50042",
				key: "skipURL",
				isEncrypt:1,//加密
				value: skipURL
			};
			external.callMessage(paramurl);

			var parampid= {
				funcNo: "50042",
				key: "product_id",
				isEncrypt:1,//加密
				value: product_id
			};
			external.callMessage(parampid);

			// 获取用户头像信息
			var userImage="";
			var params = {
				funcNo: "50043",
				key: "photo_url"
			};
			var datas = external.callMessage(params);
			if(datas.results[0].value){
				userImage = datas.results[0].value;
			}
			// 获取用户账户信息
			var param = {
				funcNo: "50043",
				key: "account_password"
			};
			var data = external.callMessage(param);
			var firstInstall = data;
            let fingerprintPwd_flag = getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
            if(fingerprintPwd_flag == '1') return fingerprint.showLoginDig(pageId,pageCode)
			// 得到用户手机号码进行查询是否设置过手势密码
			if(firstInstall.results[0].value){
				firstInstall = firstInstall.results[0].value;
				if(firstInstall){
					var account = firstInstall.substring(0,firstInstall.indexOf("_"));
					var param = {
						"funcNo" : "50263",
						"account" : account
					};
					var data = external.callMessage(param);
					var flag = data.results[0].flag;
					var	str="";
					if(flag == "1"){ //flag	String	状态（0：未设置，1：已设置）
						var setParam = {
							"funcNo" : "50261",
							"moduleName" : "mall",
							"style" : "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
							"account" :account,
							"errorNum" : "5",
							"isCanBack":"1",
							"lockSenconds" : "60",
							"userImage" : userImage
						};
						external.callMessage(setParam);
					}else{
						appUtils.pageInit(pageCode,"login/userLogin",{"skipURL":skipURL,"product_id":product_id});
					}
				}
			}else{
				appUtils.pageInit(pageCode,"login/userLogin",{"skipURL":skipURL,"product_id":product_id});
			}
		}else{
			appUtils.pageInit(pageCode,"login/userLogin",{"skipURL":skipURL,"product_id":product_id});
		}
	}

	function filterCardOut(data){
		if(data.error_no == "-99900"){
			layerUtils.iConfirm("系统检测到您还未完成绑卡？",function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"account/setBankCard",{});
			},function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/userIndexs",{});
			},"去绑卡","取消");
		}
	}

	function filterActiveOut(data){
		if(data.error_no == "-99901"){
			layerUtils.iConfirm("系统检测到您还未完成激活？",function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/openingAccoun",{});
			},function(){
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"login/userIndexs",{});
			},"去激活","取消");
		}
	}

	function filterUpdate(data){
		if(data.error_no == "-566"){
			var error_info=data.error_info;
			var index=error_info.indexOf("|");
			var update=error_info.substring(index+1,error_info.length);
			var tipsError=error_info.substring(0,index);
			var daJson=JSON.parse(update); //获取返回JSOn
			var url=daJson.version_url;//下载地址
			var versionStoreUpdate = daJson.versionStoreUpdate;
			var versionCode=daJson.version_code;//版本号
			var soft_no = global.soft_no;
			var invokeParam;
			layerUtils.iAlert(tipsError,"-1",function(){
				if(platform == "1") {
					if(versionStoreUpdate == "1"){
						invokeParam = {
							funcNo: "80318", // String	功能号	Y
							url: url, //下载地址
						};
					}else{
						invokeParam = {
							funcNo: "80050", // String	功能号	Y
							downloadLink: url, //下载地址）
						};
					}
				} else {
					invokeParam = {
						funcNo: "50201", // String	功能号	Y
						url: url, // 更新地址
						type: 0, //下载类型(0：原生，1：H5）
						version: versionCode,  //app版本  版本名称
						isShowUpdateTip:"0",
						versionSn: versionCode //app版本序列号 版本号
					};
				}
				external.callMessage(invokeParam);
			},"立即更新");
		}
	}
	function transPassError(data){
		if(data.error_no == "7001"){
			let pageId = $("body .page[data-display='block']").attr("id");
			let pageCode = pageId.split("_").join("/");
			$('#' + pageId + " #nextStep").html('确定');
			layerUtils.iLoading(false);
			//开启计划页面特殊处理
			if(pageId == 'scene_snowballPlan'){
				require.async("../../../mall/scripts/" + pageCode + ".js",function(pageJs){
					if(pageJs)pageJs.inputInfo(true);
				});
			}
			layerUtils.iConfirm(data.error_info,function(){
				//跳转到重置交易密码页面
				appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"safety/resetTradersPassword",{});
			},function(){
				//自动关闭弹窗
			},"重置密码","确定");
		}
	}
	var errorfilter = {
		"filterLoginOut": filterLoginOut,
		"filterCardOut":filterCardOut,
		"filterActiveOut":filterActiveOut,
		"filterUpdate":filterUpdate,
		"transPassError":transPassError,
	};


	//暴露对外的接口
	module.exports = errorfilter;
});
