// 货基 - 卖出页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#inclusive_moneytaryHousingSale ";
    var _pageCode = "inclusive/moneytaryHousingSale";
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    var _redem_method = "1";
    var jymm;
    var holdObj;
    var _available_vol;
    var _holdmin = 0;
    var _redeem_min = 0;
    var _fund_code;
    function init() {
        // _fund_code = appUtils.getSStorageInfo("fund_code");
        holdObj = appUtils.getSStorageInfo("holdObj");
        _fund_code = holdObj.fund_code
        appUtils.setSStorageInfo("fund_code",_fund_code);
        _available_vol = holdObj.available_vol;
        $(_pageId + " .prod_sname").html(holdObj.fund_sname);
        //获取交易时间
        reqFun102008();
        //获取最低持有份额，产品详情
        reqFun102026();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "inclusive/moneytaryDetail");
        });
        //全部卖出
        appUtils.bindEvent($(_pageId + " #all_buy"), function () {
            var available_vol = tools.fmoney(_available_vol);
            $(_pageId + " #inputspanid>span").html(available_vol).css({"color": "#000000"});
            $(_pageId + " #czje").val(available_vol);
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "inclusive_moneytaryHousingSale";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
            $(_pageId + " #account").html("--");
        });

        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击到账方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _redem_method = $(this).attr("redem_method");
        });

        //点击下一步
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            var czjeVal = $(_pageId + " #czje").val();
            czjeVal = czjeVal.replace(/,/g, "");
            if (czjeVal <= 0 || !czjeVal) {
                layerUtils.iAlert("请输入卖出份额");
                return;
            }

            var available_vol = (+_available_vol);
            if (czjeVal > available_vol) {
                layerUtils.iAlert("超过可用份额");
                return;
            }

            if (available_vol > _redeem_min && czjeVal < parseFloat(_redeem_min)) {
                layerUtils.iAlert("最低赎回" + _redeem_min + "份");
                return;
            }

            var surplus = available_vol - czjeVal;
            if (parseFloat(surplus) < parseFloat(_holdmin) && surplus != "0") { //剩余金额 < 最低持有金额 && 剩余金额 != 0
                layerUtils.iConfirm("剩余份额低于产品最低持有份额" + _holdmin, function () {
                    $(_pageId + " #inputspanid>span").html("");
                    $(_pageId + " #czje").val("");
                    return;
                }, function () {
                    var available_vol = tools.fmoney(_available_vol);
                    $(_pageId + " #inputspanid>span").html(available_vol);
                    $(_pageId + " #czje").val(_available_vol);
                }, "取消", "全部卖出");
                return;
            }
            setRechargeInfo();
            //显示 输入交易密码
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();

            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "inclusive_moneytaryHousingSale";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                fund_code: holdObj.fund_code,	//基金代码
                vir_fundcode:holdObj.vir_fundcode,
                trans_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                redem_method: _redem_method,  //赎回到宝:1,赎回到卡：2.
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                trade(param);
            }, {isLastReq: false});
        });
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function trade(param) {
        service.reqFun106023(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "inclusive/moneytarySaleResult", data.results[0]);
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                if (curVal == ".") {
                    curVal = "";
                }
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    var available_vol = (+_available_vol);
                    if (moneys > available_vol) {
                        available_vol = tools.fmoney(available_vol + "");
                        $(_pageId + " #czje").val(available_vol);
                    } else {
                        moneys = tools.fmoney(moneys);
                        $(_pageId + " #czje").val(moneys);
                    }
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));

                }

                var available_vol = (+_available_vol);
                if (curVal > available_vol) {
                    curVal = available_vol;
                    available_vol = tools.fmoney(available_vol + "");
                    $(_pageId + " #czje").val(available_vol);
                }
            }
        })
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //获取最低持有份额,产品详情
    function reqFun102026() {
        var param = {
            fund_code: _fund_code
        }
        service.reqFun102026(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                $(_pageId + " #prod_sname").text(results.prod_sname);
                $(_pageId + " .header_inner h1").text(results.prod_sname);
                $(_pageId + " .risk_level_name").html(results.risk_level_name);
                $(_pageId + " .fund_code").html(results.fund_code);
                $(_pageId + " .fund_type_name").html(results.fund_type_name);

                //最低持有份额
                _holdmin = results.holdmin;
                //最低赎回份额
                _redeem_min = results.redeem_min;
                nav = results.nav;//单位净值
                prod_sub_type2 = results.prod_sub_type2;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //获取交易时间
    function reqFun102008() {
        var param = {
            fund_code: holdObj.fund_code,
            type: "7"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);

                var beforeDate = results.beforeDate;
                if (beforeDate != "--") {
                    beforeDate = tools.FormatDateText(beforeDate.substring(4));
                }

                var afterDate = results.afterDate;
                if (afterDate != "--") {
                    afterDate = tools.FormatDateText(afterDate.substring(4));
                }
                //确认日期
                $(_pageId + " #beforeDate").html(beforeDate);
                //收益日期
                $(_pageId + " #afterDate").html(afterDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //赎回份额
        var trans_amt = $(_pageId + " #czje").val();
        if (_redem_method == "1") {
            $(_pageId + " #payMethod").text("晋金宝");
        } else {
            $(_pageId + " #payMethod").text("银行卡");
        }
        $(_pageId + " #recharge_money").html(tools.fmoney(trans_amt));
    }

    function destroy() {
        guanbi();
        $(_pageId + " #account").html("--");
        $(_pageId + " .saleInfo").html("");
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");
        $(_pageId + " .fund_type_name").html("--");
        $(_pageId + " #dzDate").html("--");
        jymm = "";
        _redem_method = "1";
        $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
        $(_pageId + " .modify_box .item").eq(0).children(".icon").addClass("active");
        $(_pageId + " #czje").val("");
        $(_pageId + " #inputspanid").text("");
        $(_pageId + " #payMethod").text("--");
        $(_pageId + " #recharge_name").html("--");
        $(_pageId + " #recharge_money").html("--");
        monkeywords.destroy();
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
