//高端：修改到期方式结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageUrl = "highEnd/modifyExpireWayResult",
        _pageId = "#highEnd_modifyExpireWayResult ";
    var ut = require("../common/userUtil");
    function init() {
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " #done_btn"), function () {
            if(ut.getUserInf().custLabelCnlCode == "yh"){
                appUtils.setSStorageInfo("routerList", ["yuanhui/userIndexs", "yuanhui/myAccount"]);
                appUtils.pageInit(_pageUrl, "yuanhui/hold");
                return;
            }else if(ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) ||ut.getUserInf().custLabelCnlCode == "jjdx"){
                appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount"]);
                appUtils.pageInit(_pageUrl, "template/positionList");
                return;
            }else{
                appUtils.setSStorageInfo("routerList", ["hengjipy/userIndexs", "yuanhui/myAccount"]);
                appUtils.pageInit(_pageUrl, "template/positionList");
                return;
            }

        })
    }

    function destroy() {
        
    }

    

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
