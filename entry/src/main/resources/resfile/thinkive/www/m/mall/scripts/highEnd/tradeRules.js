// 晋金高端 交易规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            common = require("common"),
            tools = require("../common/tools"),
            service = require("mobileService"),
            VIscroll = require("vIscroll"),
            vIscroll = {"scroll": null, "_init": false},
            _page_code = "highEnd/tradeRules",
            _pageId = "#highEnd_tradeRules ";
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        $(_pageId + " .threshold_amount").html(productInfo.threshold_amount / 10000 + '万元');
        var addition_amt;
        if (productInfo.addition_amt >= 10000) {
            addition_amt = productInfo.addition_amt / 10000 + '万元';
        } else {
            addition_amt = tools.fmoney(productInfo.addition_amt) + '元';
        }
        $(_pageId + " .addition_amt").html(addition_amt);
        $(_pageId + " .close_day").html(productInfo.inrest_term || "固定封闭期");
        if (productInfo.prod_sub_type2 == "94") {
            $(_pageId + " .close_msg").html("锁定期");
        } else if(productInfo.prod_sub_type2 == "100"){
            //产品整合判断
            if(productInfo.closed_period_list == "0" && productInfo.lock_period_list == "0") $(_pageId + " .tem_show").hide()
            if(productInfo.closed_period_list == "1") $(_pageId + " .close_msg").html("封闭期")
            if(productInfo.lock_period_list == "1") $(_pageId + " .close_msg").html("锁定期")
        }else{
            $(_pageId + " .close_msg").html("封闭期");
        }
        $(_pageId + " .redeem_rules").html(productInfo.redeem_rules || "无");
        $(_pageId + " .redemption_received_days").html(productInfo.redemption_received_days || "5");
        $(_pageId + " .open_redeem_rule").html(productInfo.open_redeem_rule || "无");
        if (productInfo.prod_sub_type2 == "91") {
            $(_pageId + " .smallgatherInfo-must").show();
            $(_pageId + " .productLockDetail-must").hide();
            $(_pageId + " .highFinancialInfo-must").hide();
            $(_pageId + " .next_day").html(tools.ftime(productInfo.estimated_opening_day.substr(0, 8), "-"));
            reqFun102047();
        } else if (productInfo.prod_sub_type2 == "90"){
            $(_pageId + " .highFinancialInfo-must").show();
            $(_pageId + " .smallgatherInfo-must").hide();
            $(_pageId + " .productLockDetail-must").hide();
        }else  {
            $(_pageId + " .highFinancialInfo-must").hide();
            $(_pageId + " .smallgatherInfo-must").hide();
            $(_pageId + " .productLockDetail-must").show();
            reqFun102047();
        } 
        if (productInfo.interest_method) {
            $(_pageId + " .interestMethod").html(productInfo.interest_method)
            $(_pageId + " .interestMethodBox").show();
        } else {
            $(_pageId + " .interestMethodBox").hide();

        }
        getRateInfo();
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " .highFinancialInfo-item"),".rule_tip", function () {
            tools.recordEventData('1','rule_tip','提示');
            layerUtils.iAlert("客户维护费指基金管理人与基金销售机构通过基金销售协议约定，依据销售机构销售基金的保有量，从基金管理费中列支一定比例，用以向基金销售机构支付客户服务及销售活动中产生的相关费用。");
        });
        //买入
        appUtils.bindEvent($(_pageId + " .purchase"), function () {
            $(_pageId + " .purchase").addClass('active')
            $(_pageId + " .redeem").removeClass('active')
            $(_pageId + " .redeem_content").hide()
            $(_pageId + " .purchase_content").show()
        });
        //赎回
        appUtils.bindEvent($(_pageId + " .redeem"), function () {
            $(_pageId + " .redeem").addClass('active')
            $(_pageId + " .purchase").removeClass('active')
            $(_pageId + " .purchase_content").hide()
            $(_pageId + " .redeem_content").show()
        });
    }

    //查询产品赎回提示
    function reqFun102047() {
        var productInfo = appUtils.getSStorageInfo("productInfo");
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102047(param, function (data) {
            if (data.error_no == 0) {
                if (!data.results || data.results.length == 0) {
                    return;
                }
                var results = data.results[0];
                var redeem_confirm = results.redeem_confirm;
                var redeem_desc = results.redeem_desc;
                if (redeem_desc) {
                    $(_pageId + " .redeem_desc").html("T+" + redeem_desc);
                    $(_pageId + " .redeem_confirm").html("T+" + redeem_confirm);
                } else {
                    $(_pageId + " .redeem_desc").html("--");
                    $(_pageId + " .redeem_confirm").html("--");
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getRateInfo() {
        // 产品详情查询--费率查询
        service.reqFun102003({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                var purchaseRateStr = "";
                var operateRateStr = "";
                var redeemRateStr = "";
                if (result.purchaseRate && result.purchaseRate.length > 0 && productInfo.businesscode == "22") {
                    if (result.purchaseRate.length == 1 && (result.purchaseRate[0].chgrate_tval == 0 || result.purchaseRate[0].fcitem_lval == 0)) {
                        var rateStr = "";
                        var discount_ratevalue = result.purchaseRate[0].discount_ratevalue;
                        if (discount_ratevalue) {
                            rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.purchaseRate[0].chgrate_tval + result.purchaseRate[0].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + result.purchaseRate[0].chgrate_unit;
                        } else {
                            rateStr = result.purchaseRate[0].chgrate_tval + result.purchaseRate[0].chgrate_unit;
                        }
                        purchaseRateStr +=
                                '<div class="highFinancialInfo-item"><p class="item-left ">' + result.purchaseRate[0].chgrate_item_desc + '</p><p class="item-right g_fontSize14 text_darkgray">' + rateStr + '</p></div>';
                    } else {
                        purchaseRateStr += '<div class="highFinancialRateInfo">' +
                                '<h1 style="margin-top: 0.1rem;color: #282828;">' + result.purchaseRate[0].chgrate_item_desc + '</h1><div><p>适用金额</p>' +
                                '<p>' + result.purchaseRate[0].chgrate_item_desc + '</p></div>';
                        for (var i = 0; i < result.purchaseRate.length; i++) {
                            var fcitem_tval = result.purchaseRate[i].fcitem_tval; //最大
                            var fcitem_lval = result.purchaseRate[i].fcitem_lval; //最小
                            var discount_ratevalue = result.purchaseRate[i].discount_ratevalue;
                            var purchaseStr = "";
                            if (fcitem_lval == 0) { //最小为0
                                purchaseStr += fcitem_tval / 10000 + '万元以下';
                            } else if (fcitem_tval == "-1") { //最大
                                purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                            } else {
                                purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                            }
                            var rateStr = "";
                            if (discount_ratevalue) {
                                rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + result.purchaseRate[i].chgrate_unit;
                            } else {
                                rateStr = result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit;
                            }
                            purchaseRateStr += '<div>' +
                                    '<p>' + purchaseStr + '</p>' +
                                    '<p>' + rateStr + '</p>' +
                                    '</div>';
                        }
                    }
                    purchaseRateStr += "</div>"
                } else if (result.subscription && result.subscription.length > 0 && productInfo.businesscode == "20") {
                    if (result.subscription.length == 1 && (result.subscription[0].chgrate_tval == 0 || result.subscription[0].fcitem_lval == 0)) {
                        var rateStr = "";
                        var discount_ratevalue = result.subscription[0].discount_ratevalue;
                        if (discount_ratevalue) {
                            rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.subscription[0].chgrate_tval + result.subscription[0].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + result.purchaseRate[0].chgrate_unit;
                        } else {
                            rateStr = result.subscription[0].chgrate_tval + result.subscription[0].chgrate_unit;
                        }
                        purchaseRateStr +=
                                '<div class="highFinancialInfo-item"><p class="item-left ">' + result.subscription[0].chgrate_item_desc + '</p><p  class="item-right g_fontSize14 text_darkgray">' + rateStr + '</p></div>';
                    } else {
                        purchaseRateStr += '<div class="highFinancialRateInfo">' +
                                '<h1 style="margin-top: 0.1rem;color: #282828;">' + result.subscription[0].chgrate_item_desc + '</h1><div><p>适用金额</p>' +
                                '<p>' + result.subscription[0].chgrate_item_desc + '</p></div>';
                        for (var i = 0; i < result.subscription.length; i++) {
                            var fcitem_tval = result.subscription[i].fcitem_tval; //最大
                            var fcitem_lval = result.subscription[i].fcitem_lval; //最小
                            var discount_ratevalue = result.subscription[i].discount_ratevalue;
                            var purchaseStr = "";
                            if (fcitem_lval == 0) { //最小为0
                                purchaseStr += fcitem_tval / 10000 + '万元以下';
                            } else if (fcitem_tval == "-1") { //最大
                                purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                            } else {
                                purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                            }
                            var rateStr = "";
                            if (discount_ratevalue) {
                                rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.subscription[i].chgrate_tval + result.subscription[i].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + result.subscription[i].chgrate_unit;
                            } else {
                                rateStr = result.subscription[i].chgrate_tval + result.subscription[i].chgrate_unit;
                            }
                            purchaseRateStr += '<div>' +
                                    '<p>' + purchaseStr + '</p>' +
                                    '<p>' + rateStr + '</p>' +
                                    '</div>';
                        }
                    }
                    purchaseRateStr += "</div>"
                } else {
                    purchaseRateStr += "<div class='highFinancialInfo-item'><p class='item-left '>买入费率</p>" +
                            "<p class='item-right g_fontSize14 text_darkgray'>无</p>"
                }
                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr += '<div class="highFinancialRateInfo">' +
                            '<h1 style="margin-top: 0.1rem;color: #282828;">卖出费率</h1><div><p>适用期限</p>' +
                            '<p>卖出费率</p></div>';

                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var datestr = "";
                        var fcitem_lval = result.redeemRate[i].fcitem_lval; //最小
                        var fcitem_lvunit = result.redeemRate[i].fcitem_lvunit;//最小单位
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;//最大
                        var fcitem_tvunit = result.redeemRate[i].fcitem_tvunit;//最大单位
                        if (fcitem_tval == "-1") { //最大
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                        } else if (fcitem_lval == "0") { //最小
                            datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                        } else {
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                        }
                        redeemRateStr += '<div>' +
                                '<p>' + datestr + '</p>' +
                                '<p>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</p>' +
                                '</div>';
                    }
                    redeemRateStr += "</div>";
                } else {
                    redeemRateStr += "<div class='highFinancialInfo-item'><p class='item-left '>卖出费率</p>" +
                            "<p class='item-right g_fontSize14 text_darkgray shfl'>无</p>"
                }
                $(_pageId + " .purchase_content .highFinancialRate").html(purchaseRateStr + operateRateStr);
                $(_pageId + " .redeem_content .highFinancialRate").html(redeemRateStr);

                for (var i = 0; i < result.operateRate.length; i++) {
                    if (result.operateRate[i].chgrate_type == '11') {
                        if(result.operateRate[i].commission_per_p && result.operateRate[i].commission_per_p > 0){
                            var str = '<em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                '<em class="proportion" >·客户维护费占' + (+result.operateRate[i].commission_per_p).toFixed(2)/1 + '%<img src="../mall/images/rule_tip.png" class="rule_tip"></em>';
                            $(_pageId + " .glf").html(str)
                        }else{
                            $(_pageId + " .glf").text(tools.fmoney(result.operateRate[i].chgrate_tval,4) + result.operateRate[i].chgrate_unit)
                        }

                    }
                    if (result.operateRate[i].chgrate_type == '10') {
                        $(_pageId + " .fwf").text(tools.fmoney(result.operateRate[i].chgrate_tval,4) + result.operateRate[i].chgrate_unit)
                    }
                    if (result.operateRate[i].chgrate_type == '12') {
                        $(_pageId + " .tgf").text(tools.fmoney(result.operateRate[i].chgrate_tval,4) + result.operateRate[i].chgrate_unit)
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .threshold_amount").text("");
        $(_pageId + " .addition_amt").text("");
        $(_pageId + " .mrfl").text("");
        $(_pageId + " .shfl").text("");
        $(_pageId + " .fwf").text("");
        $(_pageId + " .tgf").text("");
        $(_pageId + " .glf").text("");
        $(_pageId + " .purchase").addClass('active')
        $(_pageId + " .redeem").removeClass('active')
        $(_pageId + " .redeem_content").hide()
        $(_pageId + " .purchase_content").show()
        $(_pageId + " .interestMethod").text("")
        $(_pageId + " .interestMethodBox").hide();
        $(_pageId + " .redeem_desc").html("--");
        $(_pageId + " .redeem_confirm").html("--");
        $(_pageId + " .open_redeem_rule").html("");
        $(_pageId + " .smallgatherInfo-must").hide();
        $(_pageId + " .redemption_received_days").html("");
        $(_pageId + " .productLockDetail-must").hide();


    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thtradeRules = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thtradeRules;
});
