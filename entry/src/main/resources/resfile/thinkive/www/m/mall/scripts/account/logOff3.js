//注销账户-结果页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#account_logOff3",
        common = require("common"),
        _pageCode = "account/logOff3";
    var external = require("external");

    function init() {
// 清除手势密码账号和参数
        common.clearLocalStorage("account_password");
        common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
        common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
        common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
        common.clearLocalStorage("mobileWhole");
        appUtils.clearSStorage(true);
    }

    //绑定事件
    function bindPageEvent() {
        //点返回
        appUtils.bindEvent($(_pageId + " #goHome"), function () {
            appUtils.pageInit(_pageCode, "login/userLogin");
        });
    }

    function destroy() {

    }

    var accountLogOff4 = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
    };
    // 暴露对外的接口
    module.exports = accountLogOff4;
});
