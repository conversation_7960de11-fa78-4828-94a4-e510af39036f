// 开户
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        putils = require("putils"),
        common = require("common"),
        _pageId = "#bank_openAccount ",
        _pageCode = "bank/openAccount ";
    var tools = require("../common/tools");
    var userInfo;
    var ut = require("../common/userUtil");
    var sms_mobile = require("../common/sms_mobile");
    var bank_channel_code;
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        bank_channel_code = productInfo.bank_channel_code;
        tools.getBankPdf("1",bank_channel_code);
        userInfo = ut.getUserInf();
        var upFlag = appUtils.getPageParam("upFlag");
        sms_mobile.init(_pageId);

        if (appUtils.getSStorageInfo("bankOpenInfo")) {
            appUtils.clearSStorage("bankOpenInfo");
            return;
        } else {
            if (upFlag == "ok") {
                $(_pageId + " .faceRecognitionResult").text("已识别").show();
                layerUtils.iAlert("识别成功");
                $(_pageId + " .goFaceRecognition").text("重新识别");
            } else if (upFlag == "fail") {
                $(_pageId + " .faceRecognitionResult").text("识别失败").show();
                $(_pageId + " .goFaceRecognition").text("重新识别");
                if (appUtils.getPageParam("error_info")) {
                    layerUtils.iAlert(appUtils.getPageParam("error_info"));
                } else {
                    layerUtils.iAlert("识别失败");
                }
            } else {
                $(_pageId + " .faceRecognitionResult").text("").hide();
                $(_pageId + " .goRecognition").text("去识别");
            }
        }
        setBankInfo();
        queryJob();
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
        // 查看银行卡限额
        appUtils.bindEvent($(_pageId + " .bankInfo"), function () {
            appUtils.setSStorageInfo("bankOpenInfo", true);
            appUtils.pageInit(_pageCode, "bank/bankInfo");
        });

        //展示人脸识别页面
        appUtils.bindEvent($(_pageId + " .goFaceRecognition"), function () {
            // if ($(_pageId + " .goFaceRecognition").text() == "已上传") return;
            // appUtils.setSStorageInfo("bankOpenInfo", true);
            appUtils.pageInit(_pageCode, "bank/faceRecognition");
        });

        // 预留手机号码
        appUtils.bindEvent($(_pageId + " #yhmPhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 验证码 控制全文字
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //银行卡号输入
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            // 控制全数字输入
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }

            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);


            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");
        //银行卡号获得焦点
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");
        // 银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            bankDistinguish(true);
        }, "blur");
        // 更换银行卡
        appUtils.bindEvent($(_pageId + " .changeBankCard"), function () {
            $(_pageId + " #bankCard").val("").removeAttr("disabled");
            $(_pageId + " #bankname").text("").removeAttr("bank_code");
            $(_pageId + ".limit").hide();
        });
        // 更换银行预留手机号
        appUtils.bindEvent($(_pageId + " .changeBankMobile"), function () {
            $(_pageId + " #yhmPhone").val("").removeAttr("disabled");
        });
        // 重新上传身份证照片
        appUtils.bindEvent($(_pageId + " .retryUpload"), function () {
            appUtils.setSStorageInfo("bankOpenInfo", true);
            appUtils.pageInit(_pageCode, "account/uploadIDCard");
        });
        // 点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi i"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class");
            if ($(this).hasClass("active")) {
                $(this).removeClass("active");
                $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
            } else {
                $(this).addClass("active");
                $(_pageId + " #next").css({backgroundColor: "#E5433B"});

            }
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var yhmPhone = $(_pageId + " #yhmPhone").val();
                var bankCard = $(_pageId + " #bankCard").val();
                var cardPerson = $(_pageId + " #cardPerson").val();
                if (!checkName($.trim(cardPerson))) {
                    layerUtils.iMsg(-1, "请输入真实姓名");
                    return;
                }
                if (bankCard != userInfo.bankAcct && !validatorUtil.isBankCode(bankCard)) {
                    layerUtils.iMsg(-1, "请输入正确银行卡号");
                    return;
                }
                if (validatorUtil.isEmpty($(_pageId + " #bankname").html())) {
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }
                if (validatorUtil.isEmpty($(_pageId + " #bankname").attr("bank_code"))) {
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }
                if (!appUtils.getSStorageInfo("bankOpenImg")) {
                    layerUtils.iMsg(-1, "请上传人脸照片");
                    return;
                }
                if (yhmPhone != userInfo.bankReservedMobile && !validatorUtil.isMobile(yhmPhone)) {
                    layerUtils.iMsg(-1, "请输入正确手机号码");
                    return;
                }
                // 获取验证码
                var param = {
                    "mobile_phone": yhmPhone,
                    "type": common.sms_type.bankOpen,
                    "send_type": "0",
                    "mobile_type": "2",
                    "bank_electron_name": productInfo.bank_channel_name
                }
                if (yhmPhone.indexOf("*") > -1) {
                    param.mobile_type = "2";
                }
                sms_mobile.sendPhoneCode(param);
            }
        });
        // 点击下一步
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var bankCard = $(_pageId + " #bankCard").val();  // 银行卡号
            bankCard = bankCard.replaceAll(" ", "");
            var yhmPhone = $(_pageId + " #yhmPhone").val();  // 银行预留手机号码
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            var bank_code = $(_pageId + " #bankname").attr("bank_code");
            if (classname != "active") {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (bankCard != userInfo.bankAcct && validatorUtil.isEmpty(bankCard)) {
                layerUtils.iMsg(-1, "银行卡号不能为空");
                return;
            }
            if (bankCard != userInfo.bankAcct && !validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确银行卡号");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").html())) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").attr("bank_code"))) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (!appUtils.getSStorageInfo("bankOpenImg")) {
                layerUtils.iMsg(-1, "请上传人脸照片");
                return;
            }
            if (yhmPhone != userInfo.bankReservedMobile && !validatorUtil.isMobile(yhmPhone)) {
                layerUtils.iMsg(-1, "请输入正确手机号码");
                return;
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #occp").val())) {
                layerUtils.iMsg(-1, "请选择职业");
                return;
            }
            var bankOpenImg = appUtils.getSStorageInfo("bankOpenImg");
            // 用户登录手机
            var param = {
                "bank_channel_code": bank_channel_code,//银行渠道代码
                "bank_acct_no": bankCard, //绑定银行卡号
                "bank_reserved_mobile": yhmPhone,//预留手机号
                "sms_code": verificationCode,         // 验证码
                "sms_mobile": yhmPhone,         // 手机号
                "bank_code": bank_code,         // 银行编码
                "ocp": $(_pageId + " #occp").attr("data-value"),//职业
                "front_file_id": bankOpenImg.frontFileId,//身份证正面
                "back_file_id": bankOpenImg.backFileId,//身份证背面
                "face_file_id": bankOpenImg.faceFileId,//人脸
                "bank_name": $(_pageId + " #bankname").text()
            };
            service.reqFun151001(param, function (data) {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    appUtils.pageInit(_pageCode, "bank/openAccountSuccess", results);
                } else {
                    sms_mobile.clear();
                    $(_pageId + " #verificationCode").val("");
                    layerUtils.iAlert(data.error_info);
                }
            })
        });
    }

    /**
     * 设置用户银行信息
     * */
    function setBankInfo() {
        $(_pageId + " #bankCard").val(userInfo.bankAcct);
        $(_pageId + " #bankname").text(userInfo.bankName);
        $(_pageId + " #bankname").attr("bank_code", userInfo.bankCode);
        $(_pageId + " #idCard").val(userInfo.identityNum);
        $(_pageId + " #cardPerson").val(userInfo.name);
        $(_pageId + " #yhmPhone").val(userInfo.bankReservedMobile);
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
        $(_pageId + " .bank_electron_name").html(productInfo.bank_channel_name);
        $(_pageId + " .bank_electron_icon").attr("src", tools.judgeBankImg(bank_channel_code).logo);
        bankDistinguish(false); //首次进入，不提示原因
    }

    //校验姓名
    var checkName = function (name) {
        var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/;
        var reg1 = /^[\u4E00-\u9FA5\uf900-\ufa2d.s]{2,20}$/;
        if (reg.test(name) || reg1.test(name)) return true;
        return false;
    }

    /**
     * 查询职业信息
     * */
    function queryJob() {
        var param = {code: 'occupation'}
        if($(".mobileSelect").length > 0) {
            return;
        }
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                $(_pageId + " #occp").val(dataArr[0].value);
                $(_pageId + " #occp").attr("data-value", dataArr[0].id);
                tools.mobileSelect({
                    trigger: _pageId + " #occp",
                    title: "请选择职业",
                    dataArr: dataArr,
                    callback: function (data) {
                        $(_pageId + " #occp").val(data[0].value);
                        $(_pageId + " #occp").attr("data-value", data[0].id);
                    }
                })
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })

    }

    // 失去焦点时候验证银行限额 银行名称
    function bankDistinguish(flag) {
        $(_pageId + " .limit").hide();
        $(_pageId + " #pop_view").css("visibility", "hidden");
        $(_pageId + " #big_show_bank").html("");
        var bankCard = $(_pageId + " #bankCard").val();
        bankCard = bankCard.replaceAll(" ", "");
        if (validatorUtil.isEmpty(bankCard)) {
            layerUtils.iMsg(-1, "银行卡号不能为空");
            return;
        }
        if (bankCard.indexOf("*") == -1 && !validatorUtil.isBankCode(bankCard)) {
            $(_pageId + " #oneMoney").html("");
            $(_pageId + " #drxe").html("");
            layerUtils.iMsg(-1, "请输入正确银行卡号");
            return;
        }
        var param = {
            "bank_channel_code": bank_channel_code,
            "bank_acct": bankCard
        };
        service.reqFun151114(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {
                $(_pageId + " .limit").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    var bank_name = result.bank_name;
                    var bank_code = result.bank_code;
                    var single_limit = result.single_limit;
                    var day_limit = result.day_limit;
                    if (parseFloat(day_limit) > 0) {
                        $(_pageId + " .limit").show();
                        $(_pageId + " #drxe").html(day_limit + "元");
                    } else if (parseFloat(day_limit) == 0) {
                        $(_pageId + " .limit").hide();
                    } else {
                        $(_pageId + " .limit").show();
                        $(_pageId + " #drxe").html("不限");
                    }
                    if (parseFloat(single_limit) > 0) {
                        $(_pageId + " .limit").show();
                        $(_pageId + " #oneMoney").html(single_limit + "元");
                    } else if (parseFloat(single_limit) == 0) {
                        $(_pageId + " .limit").hide();
                    } else {
                        $(_pageId + " .limit").show();
                        $(_pageId + " #oneMoney").html("不限");
                    }
                    // 识别出来填写银行卡编号 和银行名称
                    $(_pageId + " #bankname").html(bank_name);
                    $(_pageId + " #bankname").attr("bank_code", bank_code);
                } else {
                    $(_pageId + " .limit").hide();
                    $(_pageId + " #bankname").html("").removeAttr("bank_code");
                    if(flag) {
                        layerUtils.iMsg(-1, "不支持的银行卡");
                    }
                    return;
                }
            } else {
                if (param.bank_acct.indexOf("*") > -1) {
                    $(_pageId + " #bankCard").val("").removeAttr("disabled");
                }
                // 识别失败时候 去掉银行信息
                $(_pageId + " #oneMoney").html("不限");
                $(_pageId + " #drxe").html("不限");
                $(_pageId + " #bankname").html("").removeAttr("bank_code");
                $(_pageId + " .limit").hide();
                if(flag) {
                    layerUtils.iMsg(-1, error_info);
                }
            }
        });
    }

    function destroy() {
        if (appUtils.getSStorageInfo("bankOpenInfo")) {
            appUtils.clearSStorage("bankOpenInfo");
            return;
        }
        $(_pageId + " input").val("").removeAttr("data-value");
        $(_pageId + " #bankCard").attr("disabled", "disabled");
        $(_pageId + " #yhmPhone").attr("disabled", "disabled");
        $(_pageId + " #bankname").removeAttr("bank_code");
        $(".mobileSelect").remove();
        $(_pageId + " #bankApen").show();
        $(_pageId + " #faceRecognition").hide();
        $(_pageId + " .goFaceRecognition").text("去识别");
        $(_pageId + " .bank_electron_name").html("");
        $(_pageId + " .bank_electron_icon").attr("src", "");
        sms_mobile.destroy(_pageId);
        $(_pageId + " .faceRecognitionResult").text("").hide();
        $(_pageId + " .goRecognition").text("");

    }

    function pageBack() {
        appUtils.clearSStorage("bankOpenImg");
        var routerList = appUtils.getSStorageInfo("routerList");
        routerList.splice(-1);
        appUtils.setSStorageInfo("routerList", routerList);
        appUtils.pageBack();
    }

    var bankOpen = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankOpen;
});
