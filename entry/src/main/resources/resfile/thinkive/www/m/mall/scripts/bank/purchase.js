define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_purchase ";
    var gconfig = require("gconfig");
    var ut = require("../common/userUtil");
    var _pageCode = "bank/purchase";
    var validatorUtil = require("validatorUtil");
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var sms_mobile = require("../common/sms_mobile");
    var avail_amt; // 电子账户可用余额
    var productInfo;// 产品信息
    var bankElectornReservedMobile; //电子银行预留手机号
    var total_amt; //用户持有该银行总资产
    var bank_max_recharge;
    var calculator = require("../common/calculator");
    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId, {
            "backgroundColor": "#ffffff",
            "btnText": "120s",
            "activeBtnText": "s",
            "activeEndBtnText": "重新获取",
            "disBackgroundColor": "#ffffff",
            "talkCodeText": "如收不到短信 &nbsp;&nbsp;<span id='getTalk'   style='color:blue;font-size:0.14rem;'>语音获取</span>"
        });
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        getAccountInfo();
        initProductInfo();
        reqFun199015();//获取银行最大限额
        $(_pageId + " #verificationCode").focus();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "bank/bankDetail");
        });

        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "bank_purchase";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭验证码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        // 跳转充值页面
        appUtils.bindEvent($(_pageId + " #recharge "), function () {
            appUtils.pageInit(_pageCode, "bank/recharge");
        });
        // 全部
        appUtils.bindEvent($(_pageId + " .allMoney "), function () {
            if (bank_max_recharge && bank_max_recharge - total_amt < avail_amt) {
                var lose = bank_max_recharge - total_amt < 0 ? 0 : bank_max_recharge - total_amt;
                layerUtils.iAlert("当前银行限购" + (bank_max_recharge / 10000).toFixed(0) + "万元，您还可购买" + tools.fmoney(lose + "") + "元");
                $(_pageId + " #money").val(lose);
                $(_pageId + " #inputspanid span").text(tools.fmoney(lose + ""));

            } else {
                $(_pageId + " #inputspanid span").text(tools.fmoney(avail_amt));
                $(_pageId + " #money").val(avail_amt);
            }
            $(_pageId + " #inputspanid span").css({color: "#000000"}).addClass("active")

        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");

            if ($code.attr("data-state") == "false") {
                return;
            }
            var param = {
                "mobile_phone": bankElectornReservedMobile,
                "type": common.sms_type.bankPurchase,
                "send_type": "0",
                "mobile_type": "2",
                "bank_abbr": productInfo.prod_name
            };
            sms_mobile.sendPhoneCode(param);
        });
        //显示验证码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }

            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            var money = $(_pageId + " #money").val()
            $(_pageId + " #input_money").text(money + "元");
            // 获取验证码
            var param = {
                "mobile_phone": bankElectornReservedMobile,
                "type": common.sms_type.bankPurchase,
                "send_type": "0",
                "mobile_type": "2",
                "bank_abbr": productInfo.prod_name
            };
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            sms_mobile.sendPhoneCode(param, function () {
                $(_pageId + " #verificationCode").focus();
            });


        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {

            document.activeElement.blur();
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            //进行充值
            var trans_amt = $(_pageId + " #money").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                trans_amt: trans_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                prod_code: productInfo.prod_code,
                trans_type: "1",
                sms_code: verificationCode,
                sms_mobile: bankElectornReservedMobile,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
                prod_type: productInfo.exclusive_type,
                prod_name: productInfo.prod_name,
                id: productInfo.id,
            };
            trade(param);

        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (money <= 0 || !money) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (parseFloat(money) < parseFloat(productInfo.surv_amt)) {
                layerUtils.iAlert("起购金额为" + tools.fmoney(productInfo.surv_amt) + "元");
                return;
            }
            if (money && productInfo.surv_amt && productInfo.incrs_amt && Math.round((parseFloat(money) - productInfo.surv_amt) % productInfo.incrs_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                layerUtils.iAlert("递增金额为" + tools.fmoney(productInfo.incrs_amt) + "元");
                return;
            }
            if (money && productInfo.prod_per_max_amt && parseFloat(money) > productInfo.prod_per_max_amt) { // 当前金额 > 最大额度
                layerUtils.iAlert("最高可购买额度为" + tools.fmoney(productInfo.prod_per_max_amt));
                return;
            }
            if (money && productInfo.prod_max_amt && productInfo.used_amt && calculator.minus(productInfo.prod_max_amt, productInfo.used_amt) <  parseFloat(money) ) { // 总额度 - 剩余额度 < 当前金额
                layerUtils.iAlert("产品剩余额度不足，剩余额度为" + tools.fmoney(calculator.minus(productInfo.prod_max_amt, productInfo.used_amt)));
                return;
            }

            $(_pageId + " .pop_layer").show();
            $(_pageId + " .action_sheet_wrapper").show();
            //是否可以购买
            isCanBuy();
        });

    }

    function trade(param) {
        var callback = function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                $(_pageId + " #verificationCode").val("");
                sms_mobile.clear();
                layerUtils.iAlert(data.error_info);
                return;
            }
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/purchaseResult", {
                trans_serno: trans_serno,
                is_transfer: productInfo.is_transfer
            });
        }
        if (productInfo.exclusive_type && productInfo.exclusive_type != "2") { //非新手专享
            service.reqFun109001(param, callback);
            return;
        }
        if (productInfo.exclusive_type == "2") { //新手专享
            service.reqFun109003({}, function (data) { //查询是否新手
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var is_sales_tiro = data.results[0].is_sales_tiro; // 是否新手（0-是 1-否）
                if (is_sales_tiro != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert("该产品为新手专享");
                    return;
                }
                service.reqFun109001(param, callback);
            }, {isLastReq: false})
            return;
        }
        service.reqFun151006(param, callback);

    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #money").val(moneys);
                if (parseFloat(curVal.replace(/,/g, "")) < parseFloat(productInfo.surv_amt)) {
                    layerUtils.iAlert("起购金额为" + tools.fmoney(productInfo.surv_amt) + "元");
                    return;
                }
                if (curVal && productInfo.surv_amt && productInfo.incrs_amt && Math.round((parseFloat(curVal.replace(/,/g, "")) - productInfo.surv_amt) % productInfo.incrs_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("递增金额为" + tools.fmoney(productInfo.incrs_amt) + "元");
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }
                if (parseFloat(curVal) > parseFloat(productInfo.prod_per_max_amt)) {
                    $(_pageId + " #money").val(productInfo.prod_per_max_amt);
                }
                if (bank_max_recharge && parseFloat(bank_max_recharge) - total_amt < curVal) {
                    var lose = bank_max_recharge - total_amt < 0 ? 0 : bank_max_recharge - total_amt;
                    layerUtils.iAlert("当前银行限购" + (bank_max_recharge / 10000).toFixed(0) + "万元，您还可购买" + tools.fmoney(lose + "") + "元");
                    $(_pageId + " #money").val(tools.fmoney(lose + ""));
                    guanbi();
                    return;
                }
            }
        })
    }

    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                $(_pageId + " .bankIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "(尾号" + results.acct_no.substr(-4) + ")");
                $(_pageId + " .bank_electron_name").text(results.bank_channel_name);
                avail_amt = results.avail_amt;
                bankElectornReservedMobile = results.mobile_phone;
                $(_pageId + " .avail_amt").text(tools.fmoney(results.avail_amt) + "元");
                getMyAsset();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //查询用户持有该银行总资产
    function getMyAsset() {
        service.reqFun151106({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                total_amt = results.total_amt;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function initProductInfo() {
        $(_pageId + " .prod_sname").text(productInfo.prod_name);
        $(_pageId + " .rate").text(tools.fmoney(productInfo.bas_int_rate) + "%");
        $(_pageId + " #money").val("");
        $(_pageId + " #inputspanid span").text(tools.fmoney(productInfo.surv_amt) + "元起购，" + tools.fmoney(productInfo.incrs_amt) + "元递增");
        $(_pageId + " #inputspanid span").attr("text", tools.fmoney(productInfo.surv_amt) + "元起购，" + tools.fmoney(productInfo.incrs_amt) + "元递增");
        tools.getBankPdf("1", productInfo.bank_channel_code, productInfo.prod_code); //获取协议
        $(_pageId + " .prod_per_max_amt").html(tools.fmoney(productInfo.prod_per_max_amt));
        $(_pageId + " .increase_term").html(productInfo.increase_term);
        $(_pageId + " .exclusive_type_desc").html(productInfo.exclusive_type_desc);
        $(_pageId + " .rate_base").html(productInfo.rate_base);
        $(_pageId + " .rate_reward").html(productInfo.rate_reward);
        $(_pageId + " .rate_tatal").html(productInfo.rate);
        // $(_pageId + " .buy_start_time").html(Number(productInfo.buy_start_time.substr(8, 2)));
        if(!!productInfo.exclusive_type) {
            $(_pageId + " .rate_text").html("综合利率：");
            $(_pageId + " .last_amt").html("剩余可买额度：" + tools.fmoney(calculator.minus(productInfo.prod_max_amt, productInfo.used_amt)) + "元");
            $(_pageId + " .prod_rule").html(productInfo.prod_rule);
            $(_pageId + " .noviciate").show();

        } else {
            $(_pageId + " .rate_text").html("储蓄存款利率：")
            $(_pageId + " .last_amt").html("");
            $(_pageId + " .noviciate").hide();

        }
    }

    //是否可以购买
    function isCanBuy() {
        var trans_amt = $(_pageId + " #money").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        avail_amt = (+avail_amt);
        if (trans_amt <= avail_amt) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }

    function reqFun199015() {
        service.reqFun199015({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                bank_max_recharge = results.val;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        $(_pageId + " .prod_sname").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " #inputspanid span").text("请输入买入金额").css({color: "#999999"}).removeClass("active");
        $(_pageId + " #inputspanid span").attr("text", "请输入买入金额");
        $(_pageId + " #money").text("");
        $(_pageId + " .bank_electron_info").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " .avail_amt").text("--");
        $(_pageId + " .bankIcon img").attr("src", "");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " .last_amt").html("");
        monkeywords.flag = 0;
        sms_mobile.destroy();
        guanbi();
        monkeywords.destroy();
        bank_max_recharge = "";
        $(_pageId + " .undata").html("--");
        $(_pageId + " .noviciate").hide();
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
