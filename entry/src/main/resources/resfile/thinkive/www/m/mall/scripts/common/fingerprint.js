/**
 * 进度条事件
 */
 define((require, exports, module)=> {
    var appUtils = require("appUtils");
    var external = require("external");
	const fingerprint ={
        showLoginDig:(page_id,page_code,snowballMarketShow) =>{
            //缓存当前从场景营销页去登录
            if(snowballMarketShow) appUtils.setSStorageInfo("isMarketLogin", snowballMarketShow);
        	if(page_id.trim().substr(0, 1) != '#'){
                 page_id = '#' + page_id
            }
            let domList = $(page_id + ' .loginDig');
            if(domList.length)  {
                let firstInstall = fingerprint.getLocalStorage("account_password");
                let phoneNum = firstInstall.substring(0, firstInstall.indexOf("_"));
                phoneNum = phoneNum.substr(0, 3) + "****" + phoneNum.substr(-4);
                $(page_id + " .loginDig_phone").text(phoneNum);
                fingerprint.isShowGesture(page_id);
                $(page_id + ' .loginDig').show();
                fingerprint.fingerprintPwd();
                // return;
            }else{
                let html = `
                    <div class="loginDig" style="display: none;width:100%;height:100%;position: absolute;">
                    <ul class="loginDig_back" style="padding:0.1rem;display: inline-flex;">
                        <img class="" style="margin: 0 auto;display: block;width:0.2rem;height: 0.16rem;" src="./images/back_left.png">
                        <span style="display: block;margin-top: -0.03rem;font-size: 0.16rem;">返回</span>
                    </ul>
                    <ul class="loginDig_phone" style="text-align: center;margin-top: 20%;font-size: 0.2rem;color:#35a0d8">
                    </ul>
                    <ul class="loginDig_click" style="margin-top:20%">
                        <img class="setFingerprint" style="margin: 0 auto;display: block;width:0.6rem;height: 0.6rem;" src="./images/fingerprint.png">
                    </ul>
                    <div style="text-align: center;margin-top: 0.1rem;">点击验证指纹登录</div>
                    <ul class="loginDig_bottom flex" style="position:absolute;bottom:0;padding:0.1rem;width:100%;color:#d01a06">
                        <li class="gesture" style="display: none;color: #f5806c;font-size: 0.16rem;padding: 0 0.18rem 0.2rem 0.18rem;">
                            手势密码登录
                        </li>
                        <li class="pageToLogin" style="color: #f5806c;font-size: 0.16rem;padding: 0 0.18rem 0.2rem 0.18rem;">
                            账号密码登录
                        </li>
                    </ul>
                </div>
            `
                $(page_id).append(html);
                $(page_id + " .loginDig").show();
                let firstInstall = fingerprint.getLocalStorage("account_password");
                let phoneNum = firstInstall.substring(0, firstInstall.indexOf("_"));
                phoneNum = phoneNum.substr(0, 3) + "****" + phoneNum.substr(-4)
                $(page_id + " .loginDig_phone").text(phoneNum);
                fingerprint.isShowGesture(page_id);
                fingerprint.fingerprintPwd();
            }
            appUtils.bindEvent($(page_id + " .gesture"), function () {
                // 获取用户账户信息
                let param50043 = {
                    funcNo: "50043",
                    key: "account_password"
                };
                let firstInstall = external.callMessage(param50043);
                firstInstall = firstInstall.results[0].value;
                let account = firstInstall.substring(0, firstInstall.indexOf("_"));
                let setParam = {
                    "funcNo": "50261",
                    "moduleName": "mall",
                    "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
                    "account": account,
                    "errorNum": "5",
                    "isCanBack": "1",
                    "lockSenconds": "60",
                    "userImage": ""
                };
                external.callMessage(setParam);
            });
            appUtils.bindEvent($(page_id + " .pageToLogin"), function () {
                $(page_id + ' .loginDig').hide()
                appUtils.pageInit(page_code, "login/userLogin", {});
            });
            appUtils.bindEvent($(page_id + " .loginDig_back"), function () {
                $(page_id + " .loginDig").hide()
            });
            appUtils.bindEvent($(page_id + " .loginDig_click"), function () {
                fingerprint.fingerprintPwd();
            });
        },
        getLocalStorage:(key)=> {
            var data = external.callMessage({
                funcNo: "50043",
                key: key,
            })
            if(data && data.results &&data.results.length > 0) {
                return  data.results[0].value
            }
            return "";
        },
        //获取是否设置过手势密码
        isShowGesture:(_pageId)=>{
            // 获取用户账户信息
            var param50043 = {
                funcNo: "50043",
                key: "account_password"
            };
            let flag;
            var firstInstall = external.callMessage(param50043);
            if (firstInstall && firstInstall.results && firstInstall.results[0].value) {
                firstInstall = firstInstall.results[0].value;
                if (firstInstall) {
                    var account = firstInstall.substring(0, firstInstall.indexOf("_"));
                    var param = {
                        "funcNo": "50263",
                        "account": account
                    };
                    var data = external.callMessage(param);
                    flag = data.results[0].flag;
                    if (flag != '1') { //未开启
                        $(_pageId + " .gesture").hide();
                        $(_pageId + " .loginDig_bottom").css('justify-content','flex-end')
                    }else{  //已开启
                        $(_pageId + " .gesture").show();
                        $(_pageId + " .loginDig_bottom").css('justify-content','space-between')
                    }
                }
            }
            // let gesture_code_data = common.getLocalStorage("gesture_code");
        },
        fingerprintPwd:()=>{
            var param80319 = {
                funcNo: "80319",
            };
            sessionStorage.isOpenFingerprint = true;
            external.callMessage(param80319);
        },
    };
    // 暴露对外的接口
    module.exports = fingerprint;
})