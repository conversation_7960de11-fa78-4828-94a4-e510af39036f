 // 奖励明细
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		 VIscroll = require("vIscroll"),
		 common = require("common"),
		 vIscroll = {"scroll":null,"_init":false},
		_pageId = "#inviteFriends_incomeDetails ";
	 var currentPage;
     var totalPages;
     var page=1;
     var busitype="'R01','R02','R03'";
    var selectDate=require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var transstartdate;
    var transenddate;
	function init(){
		//初始化时间筛选框
        selectDate(_pageId+'#endTime',1,_pageId);
        selectDate(_pageId+'#startTime',0,_pageId);
        resetInputDate();

		// var config={
		// 		"mode": "mixed",
		// 		"theme":"default"
		// }
		// dateUtils.initDateUI("inviteFriends_incomeDetails",config);

		var c_lass=$(_pageId+" #jiangliminxi").attr("class","current");
		$(_pageId+" #tixianmingxi").removeAttr("class","current");
	    page=1;
	    busitype="'R01','R02','R03'";
		$(_pageId+" #start_date").val("查询日期");
		mingxichauxun(1,false);
	}

	function mingxichauxun(list_page,isAppendFlag){
        transstartdate = $(_pageId).find("#startTime").attr("time");
        transenddate = $(_pageId).find("#endTime").attr("time");
		var param = {
				page:list_page,
		        numPerPage:"10",
		        busi_type:busitype,
		        trans_start_date:transstartdate,
		        trans_end_date:transenddate,
		        cust_no:appUtils.getSStorageInfo("custNo")

			};
			service.recommendedDetail(param,function(datas){
				if(datas.error_no == 0){
					currentPage = datas.results[0].currentPage;//当前页数
					totalPages = datas.results[0].totalPages;//总页数
					var str="";
					if(page==1&&busitype=="'R01','R02','R03'"){
						str="<tr><th>日期</th><th>奖励金额(元)</th><th>奖励来源</th></tr>";
					}
					if(page==1&&busitype=="RTC"){
						str="<tr><th>日期</th><th></th><th>提现金额(元)</th></tr>";
					}
					if(currentPage<=totalPages){
						for(var i=0;i< datas.results[0].data.length;i++)
						{
							var	trans_amt = datas.results[0].data[i].trans_amt;
							var	trans_date = datas.results[0].data[i].trans_date;
							trans_amt=common.fmoney(trans_amt,2);
							var	year = trans_date.substring(0,4);
							var	month = trans_date.substring(4,6);
							var	data = trans_date.substring(6,8);
							trans_date = year+"-"+month+"-"+data;
							if(busitype=="'R01','R02','R03'"){
								var	busi_type = datas.results[0].data[i].busi_type;//奖励来源
								if(busi_type=="R01"){
									busi_type="邀请奖";
								}else if(busi_type=="R03"){
									busi_type="活动奖"
								}else{
									busi_type="邀请奖";
								}
								str+="<tr><td>"+trans_date+"</td><td><em>"+trans_amt+"</em></td><td>"+busi_type+"</td></tr>";
							}
							if(busitype=="RTC"){
								//busi_type="活动奖"
								str+="<tr><td>"+trans_date+"</td><td></td><td><em>"+trans_amt+"</em></td></tr>";
							}
						}
					}

					if(isAppendFlag){
						$(_pageId+" #zhanshiList").append(str);
					    $(_pageId+" .visc_pullUpIcon").hide();
						$(_pageId+" .visc_pullUpDiv").hide();
					}else{
						$(_pageId+" #zhanshiList").html(str);
					}
					pageScrollInit();

				}else{
					layerUtils.iAlert(datas.error_info);
				}
			});
	}

	//绑定事件
	function bindPageEvent(){
		//点击奖励明细
		appUtils.bindEvent($(_pageId+" #jiangliminxi"),function(){
			var c_lass=$(_pageId+" #jiangliminxi").attr("class","current");
			$(_pageId+" #tixianmingxi").removeAttr("class","current");
			busitype="'R01','R02','R03'";
			page = 1;
            resetInputDate();
			mingxichauxun(1,false);
			$(_pageId+" .visc_pullUp").show();
			$(_pageId+" .visc_pullUpIcon").hide();
			$(_pageId+" .visc_pullUpDiv").hide();
		});
		//点击体现明细
		appUtils.bindEvent($(_pageId+" #tixianmingxi"),function(){
			var c_lass=$(_pageId+" #tixianmingxi").attr("class","current");
			$(_pageId+" #jiangliminxi").removeAttr("class","current");
			busitype="RTC";
			page = 1;
            resetInputDate();
			mingxichauxun(1,false);
			$(_pageId+" .visc_pullUp").show();
			$(_pageId+" .visc_pullUpIcon").hide();
		    $(_pageId+" .visc_pullUpDiv").hide();
		});

		//筛选日期
		appUtils.bindEvent($(_pageId+" .btn_filter"),function(){
		    //$(_pageId+" #start_date").focus();
            $(_pageId+" #jymx_filter").show();
            $(_pageId+" #filter_layer").show();
		});
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId+" #jymx_filter ul li"),function(){
            $(_pageId+" #jymx_filter ul li a").removeClass("active");
            $(this).find("a").addClass("active");
            var data_value = $(this).find("a").attr("data-value");
            setInputDate("endTime",new Date());
            var start_date = new Date();//开始日期

            if(data_value=="7"){
                start_date.setDate(start_date.getDate() - 6);
			}else if(data_value=="30"){
            	start_date.setMonth(start_date.getMonth() -1);
			}else{
				start_date.setMonth(start_date.getMonth()-3);
			}

            setInputDate("startTime",start_date);
        });
        //重置
        appUtils.bindEvent($(_pageId+" #reset"),function(){
            resetInputDate();
        });
        //确定
        appUtils.bindEvent($(_pageId+" #confirm"),function(){
            $(_pageId+" #jymx_filter").hide();
            $(_pageId+" #filter_layer").hide();
            page = 1;
            mingxichauxun(1,false);
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId+" #filter_layer"),function(){
            $(_pageId+" #jymx_filter").hide();
            $(_pageId+" #filter_layer").hide();
        });
        //点击取消时间控件
        appUtils.preBindEvent($(_pageId),$(_pageId+" .olay"),function(e){
    		//取消冒泡
			if (e || e.stopPropagation) {
				e.stopPropagation();
			}else {
				window.event.CancelBubble = true;
			}
			//获取事件源
			var node = $(e.srcElement||e.target);
			if(node.hasClass("olay")){
				$(_pageId+" .olay").remove();
			}
         },"click");
		appUtils.bindEvent($(_pageId + ' input[data-dateplugin="mobiScroll"]'), function(){
			var sDate = $(this).val();
			$(this).val(sDate);
			sDate=sDate.replace(/-/g, "");
			if(sDate=="查询日期"){
			}else{
				page = 1;
				transstartdate=sDate;
				mingxichauxun(1,false,sDate,"");
			}

        }, "change input");

		//点击返回按钮
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});


	}


	/**上下滑动刷新事件**/
	function pageScrollInit(){
		var height = $(_pageId+" #v_container_productList").offset().top;
		var height2 = $(window).height() - height - 50 + 44;
		if(!vIscroll._init) {
			var config = {
				"isPagingType": false,
				"visibleHeight": height2, //这个是中间数据的高度
				"container": $(_pageId+" #v_container_productList"),
				"wrapper": $(_pageId+" #v_wrapper_productList"),
				"downHandle": function() {
					page = 1;
					mingxichauxun(1,false,transstartdate,transenddate);
				    $(_pageId+" .visc_pullUp").show();
				    $(_pageId+" .visc_pullUpIcon").hide();
					$(_pageId+" .visc_pullUpDiv").hide();
				},
				"upHandle": function() {
					if(currentPage<totalPages){
						$(_pageId+" .visc_pullUpIcon").show();
						$(_pageId+" .visc_pullUpDiv").show();
						page+=1;
						mingxichauxun(page,true,transstartdate,transenddate);
					}
				},
				"wrapperObj": null
			};
			vIscroll.scroll = new VIscroll(config); //初始化
			vIscroll._init = true;
		}else{
			vIscroll.scroll.refresh();
		}

		if(currentPage==totalPages){
			$(_pageId+" .visc_pullUp").hide();
		}else{
			$(_pageId+" .visc_pullUp").show();
		}

	}

	function destroy(){
		 page=1;
	     busitype="'R01','R02','R03'";
		$(_pageId+" #start_date").val("查询日期");
		 transstartdate="";
		 transenddate="";
		 $(_pageId+" #jymx_filter").hide();
         $(_pageId+" #filter_layer").hide();
         $(_pageId + ".olay").hide();
	}
    //设置时间控件
    function setInputDate(id,date){
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId+" #"+id).attr("data-year",year);
        $(_pageId+" #"+id).attr("data-month",month);
        $(_pageId+" #"+id).attr("data-date",date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId+" #"+id).attr("time",datastr);
        $(_pageId+" #"+id).val(datastr);
    }
    //重置时间框
    function resetInputDate(){
    	//快捷筛选选中
        $(_pageId+" #jymx_filter ul li a").removeClass("active");
        $(_pageId+" #init_li").addClass("active");
		//开始时间框设置
        var date = new Date();
        setInputDate("endTime",date);
        //结束时间框设置
        date.setMonth(date.getMonth()-1);
        setInputDate("startTime",date);
    }
	function pageBack(){
		appUtils.pageBack();
	}
	var incomeDetails = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = incomeDetails;
});
