// 更换预留手机号
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_changeBankphone ";
    var ut = require("../common/userUtil");
    var keyflag = 0;
    var userInfo,bank_serial_no;
    var send_sms_flag;
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        $(_pageId + ' #inputspanid span').addClass('hide-after');
    }

    //绑定事件
    function bindPageEvent() {
        //点击开启键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + ' #inputspanid span').text('').addClass('inputspan');
            $(_pageId + ' #tradeNum').val('');
            $(_pageId + ' #inputspanid span').removeClass('hide-after');
            kaiqi("tradeNum");
        });
        //关闭密码键盘
        appUtils.bindEvent($(_pageId), function () {
            if (keyflag == 1) {
                $(_pageId + ' .bg_blue').css('margin-top', 0);
                guanbi();
            }
        });
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            tools.openCamera("zm");
            // var external = require("external");
            // var Param = {
            //     "funcNo": "60302",
            //     "moduleName": "mall"
            // };
            // external.callMessage(Param);
        });
        // 银行预留手机号码输入控制
        appUtils.bindEvent($(_pageId + " #bankLeavePhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 银行卡号失去焦点
        appUtils.bindEvent($(_pageId + " #cardNo"), function () {
            var cardNo = $(_pageId + " #cardNo").val();
            if (cardNo == '') {
                layerUtils.iMsg(-1, "银行卡号不能为空!");
                return;
            }
            if (!validatorUtil.isBankCode(cardNo)) {
                layerUtils.iMsg(-1, "无效卡号请重新输入");
                return;
            }
            reqFun1100001({
                "type": "card_no",
                "value": cardNo
            }, function () {
                layerUtils.iLoading(false);
                $(_pageId + ' #bankname').val(userInfo.bankName);
                //获取kb 参数 卡号
                bankDistinguish(cardNo)
            });
        }, "blur");
        appUtils.bindEvent($(_pageId + " #bankLeavePhoneNew"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 验证码输入控制
        appUtils.bindEvent($(_pageId + " #yzm"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var bankLeavePhoneNew = $(_pageId + " #bankLeavePhoneNew").val(); //新预留手机号
            var $code = $(_pageId + " #getYzm");
            var bankCard = $(_pageId + " #cardNo").val();   //卡号
            bankCard = bankCard.replaceAll(" ", "")
            let idCard = $(_pageId + " #idCard").val();   //身份证
            let realName = $(_pageId + " #realName").val(); //真实姓名
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                nameYZ(function () {
                    //验证码 新
                    var param = {
                        "bank_code": $(_pageId + " #bankname").attr("bank_code"),//银行编码
                        "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                        "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                        "bank_acct": bankCard,     // 用户卡号
                        "bank_reserved_mobile":bankLeavePhoneNew,
                        "cert_no": idCard,   // 用户身份证
                        "bank_name":$(_pageId + " #bankname").val(),
                        "sms_type":common.sms_type.changeBankMobile,
                        "send_type": "0",
                        "cust_name": realName, // 用户姓名
                        "cert_type": "0", //证件类型
                        "mobile_phone": bankLeavePhoneNew,
                        "type": common.sms_type.changeBankMobile,
                    }
                    // let payorg_id = $(_pageId + " #bankname").attr("payorg_id")
                    if(send_sms_flag == "1"){
                        sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                            if (data.error_no == "0") {
                                bank_serial_no = data.results[0].bank_serial_no
                            }else{
                                layerUtils.iAlert(data.error_info);
                            }
                        });
                    }else{
                        sms_mobile.sendPhoneCode(param);
                    }
                });
            }
        });
        //提交
        appUtils.bindEvent($(_pageId + " #xyb"), function () {
            nameYZ(function () {
                var sms_code = $(_pageId + " #yzm").val(); //验证码
                if (sms_code == '') {
                    layerUtils.iMsg(-1, "验证码不能为空");
                    return;
                }
                if (sms_code.length != 6) {
                    layerUtils.iMsg(-1, "验证码错误");
                    return;
                }
                //校验密码
                changePwYZ();
            });
        });

    }
    //失去焦点时候验证银行限额 银reqFun199001行名称
    function bankDistinguish(bankNo){
        var param = {
            "bin_id": bankNo
        };
        service.BankByCard(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {  //调用成功
                if (data.results.length > 0){
                    let result = data.results[0];
                    send_sms_flag = result.send_sms_flag;
                    $(_pageId + " #bankname").attr("bank_code", result.bank_code);
                    $(_pageId + " #bankname").attr("payorg_id", result.payorg_id);//支付机构ID
                    $(_pageId + " #bankname").attr("pay_type", result.pay_type);//支付方式
                }else{
                    $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                }
            }else{
                $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                layerUtils.iMsg(-1, error_info);
            }
        })
    }
    //获取语音验证码
    function getCodeOFTalk() {
        var mobile = $(_pageId + " #bankLeavePhoneNew").val();
        if (mobile) {
            var param = {
                "mobile_phone": $(_pageId + " #bankLeavePhoneNew").val(),
                "type": common.sms_type.changeBankMobile,
                "send_type": "1"
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(_pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        } else {
            layerUtils.iAlert("请输入新预留手机号");
        }
    }


    function passwordkey() {
        keyflag = 1;
        $(_pageId + ' #inputspanid span').css('color', '#000000');
        $(_pageId + ' .bg_blue').css('margin-top', '-100px');
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                $(_pageId + ' .bg_blue').css('margin-top', 0);
                keyflag = 0;
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #tradeNum").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                }
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #tradeNum").val(shuru);
                }
                $(_pageId + ' #inputspanid span').text(passflag);
            } // 键盘的输入事件
        };
    }

    //验证身份证号，姓名
    function nameYZ(callback) {
        var name = $(_pageId + " #realName").val(); //真实姓名
        var idCard = $(_pageId + " #idCard").val().toUpperCase(); //身份证
        var cardNo = $(_pageId + " #cardNo").val(); //银行卡号
        var bankLeavePhone = $(_pageId + " #bankLeavePhone").val(); //原预留手机号
        var bankLeavePhoneNew = $(_pageId + " #bankLeavePhoneNew").val(); //新预留手机号
        var sms_code = $(_pageId + " #yzm").val(); //验证码
        if (name == '') {
            layerUtils.iMsg(-1, "姓名不能为空!");
            return;
        }
        if (name != userInfo.name) {
            layerUtils.iMsg(-1, "姓名输入有误!");
            return;
        }
        if (idCard == '') {
            layerUtils.iMsg(-1, "身份证不能为空!");
            return;
        }
        if (!validatorUtil.isCardID(idCard)) {
            layerUtils.iMsg(-1, "证件号码格式错误");
            return;
        }
        //校验身份证号
        reqFun1100001({
            "type": "cert_no",
            "value": idCard
        }, function () {
            //校验银行卡号
            if (cardNo == '') {
                layerUtils.iMsg(-1, "银行卡号不能为空!");
                return;
            }
            if (!validatorUtil.isBankCode(cardNo)) {
                layerUtils.iMsg(-1, "无效卡号请重新输入");
                return;
            }
            reqFun1100001({
                "type": "card_no",
                "value": cardNo
            }, function () {
                $(_pageId + ' #bankname').val(userInfo.bankName);
                if (bankLeavePhone == '') {
                    layerUtils.iMsg(-1, "原预留手机号不能为空");
                    return;
                }
                if (!validatorUtil.isMobile(bankLeavePhone)) {
                    layerUtils.iMsg(-1, "请输入正确的原预留手机号码");
                    return;
                }
                //校验预留手机号
                reqFun1100001({
                    "type": "bank_leave_phone",
                    "value": bankLeavePhone
                }, function () {
                    if (bankLeavePhoneNew == '') {
                        layerUtils.iMsg(-1, "新预留手机号不能为空");
                        return;
                    }
                    if (!validatorUtil.isMobile(bankLeavePhoneNew)) {
                        layerUtils.iMsg(-1, "请输入正确的新预留手机号码");
                        return;
                    }
                    if (bankLeavePhoneNew == bankLeavePhone) {
                        layerUtils.iMsg(-1, "新预留手机号不能与原预留手机号相同");
                        return;
                    }
                    if (callback) callback();
                });
            })
        })

    }

    //验证银行卡号
    function reqFun1100001(parm, callback) {
        service.reqFun1100001(parm, function (data) { //校验身份证
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                if (parm.type == 'bank_leave_phone') {
                    layerUtils.iMsg(-1, '原预留手机号错误');
                    return;
                }
                layerUtils.iMsg(-1, data.error_info);
                return;
            }
            if (callback) callback();
        }, {isLastReq: false});
    }

    //验证交易密码
    function changePwYZ() {
        var trans_pwd = $(_pageId + " #tradeNum").val();
        //密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no == "0") {
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, trans_pwd);
                var name = $(_pageId + " #realName").val(); //真实姓名
                var cert_no = $(_pageId + " #idCard").val(); //身份证号
                var bankLeavePhone = $(_pageId + " #bankLeavePhone").val(); //原预留手机号
                var bankLeavePhoneNew = $(_pageId + " #bankLeavePhoneNew").val(); //新预留手机号
                var cardNo = $(_pageId + " #cardNo").val(); //银行卡号
                var sms_code = $(_pageId + " #yzm").val(); //验证码
                var parm = {
                    bank_reserved_mobile: bankLeavePhoneNew, //新预留手机号
                    org_bank_reserved_mobile: bankLeavePhone, //原预留手机号
                    bank_code: userInfo.bankCode, //银行编号
                    bank_acct: cardNo, //银行卡号
                    cust_name: name, //客户姓名
                    cert_no: cert_no,
                    cert_type: "0",
                    sms_code: sms_code,
                    message_code:sms_code,
                    bank_reserved_mobile:bankLeavePhoneNew,
                    sms_mobile: bankLeavePhoneNew,
                    trans_pwd: trans_pwd,
                    bank_name:$(_pageId + " #bankname").val(),
                    pay_type: $(_pageId + " #bankname").attr("pay_type"),
                    bank_serial_no: bank_serial_no,
                    payorg_id: $(_pageId + " #bankname").attr("payorg_id"),
                };
                //实名认证
                service.reqFun101061(parm, function (data) {
                    if (data.error_no != "0") {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    //更换预留手机号申请
                    service.reqFun101011(parm, function (data) {
                        sms_mobile.clear();
                        if (data.error_no != 0) {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        var user = ut.getUserInf();
                        // user.mobileWhole = bankLeavePhoneNew;
                        user.bankReservedMobile = bankLeavePhoneNew.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                        ut.saveUserInf(user);
                        layerUtils.iMsg(-1, "更换受理成功！");
                        appUtils.pageBack();
                    });
                }, {isLastReq: false})

            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, {isLastReq: false})
    }

    //关闭键盘
    function guanbi() {
        $(_pageId + ' #inputspanid span').removeClass('inputspan');
        $(_pageId + ' #inputspanid span').addClass('hide-after');
        if ($(_pageId + ' #inputspanid span').text() == '') {
            $(_pageId + ' #inputspanid span').text($(_pageId + ' #inputspanid span').attr('text')).css('color', '#999999');
        }
        keyflag = 0;
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);

    }

    function kaiqi(jjb_pwd) {
        passwordkey();
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_changeBankphone";
        param["eleId"] = jjb_pwd;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param)
    }

    function destroy() {
        guanbi();
        send_sms_flag = null;
        $(_pageId + " input").val('');
        $(_pageId + " #jiatradeNum").text("请输入交易密码");
        $(_pageId + " #tradeNum").val("");
        $(_pageId + ' #inputspanid span').text($(_pageId + ' #inputspanid span').attr('text')).css('color', '#999999');
        $(_pageId + " article").css({"margin-top": 0});
        sms_mobile.destroy();
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
// 暴露对外的接口
    module.exports = advertisement;
});
