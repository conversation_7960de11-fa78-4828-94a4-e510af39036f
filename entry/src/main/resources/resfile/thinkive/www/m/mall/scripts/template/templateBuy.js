define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#template_templateBuy ";
    var ut = require("../common/userUtil");
    var _pageCode = "template/templateBuy";
    var tools = require("../common/tools");
    var userInfo;
    var get_pdf_file = require("../common/StrongHintPdf")
    var monkeywords = require("../common/moneykeywords");
    var threshold_amount; //起购金额
    var jymm;
    var _available_vol; //可用金额
    var _fund_code;
    var addition_amt;//追加金额
    var step_amt; //递增金额
    var _addition_max_amt = "";//追加最高额
    var product_risk_level; //产品风险等级
    var buyflag; //风险等级是否匹配  1 不匹配
    var buy_state; //1 购买、2 预约
    var isFirstPurchase;// 是否首次购买产品
    var _first_max_amt = "";
    var productInfo;
    var rateList = [];
    var riskLevelName;
    //day_purchase_limit_show 是否展示单日购买限额 0否1是
    var day_purchase_limit_show;
    //单日购买限额
    var td_sum_max_amt;
    // let resultDeta
    function init() {
    	$(_pageId + " .van-overlay").hide();
        userInfo = ut.getUserInf();
        _fund_code = appUtils.getSStorageInfo("fund_code");
        //页面埋点初始化
        tools.initPagePointData({fundCode:_fund_code});
        reqFun102103();
        //获取产品详情
        // getFundInfo();
        //可用份额
        reqFun101901();
        //查询该产品是否首次购买

        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        //PDF相关，走公共方法
        is_show_paf()

    }
    //获取费率
    function getRateList() {
        var params = {
            fund_code: _fund_code
        }
        service.reqFun102003(params, function (data) {
            if (data.error_no == 0) {
                rateList = data.results[0].purchaseRate;
                reqFun102045('init');
            }
        })
    }
    function is_show_paf() {
        get_pdf_file.get_file(_fund_code, _pageId)
    }
    function bindPageEvent() {
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            tools.intercommunication(_pageCode);
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "template_templateBuy";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
            $(_pageId + " .showLimit").removeClass("m_text_red");
            $(_pageId + " #inputspanid span").removeClass('m_text_red');
            $(_pageId + " .thfundBtn .buy").removeClass('no_active');
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(_pageCode, "template/publicOfferingDetail");
        });


        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //风险承受能力确认
        appUtils.bindEvent($(_pageId + " .agreement1"), function () {
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active")
            } else {
                $(this).find("i").addClass("active")
            }
        });
        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //显示购买弹框
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            if ($(this).hasClass("no_active")) return;
            var curVal = $(_pageId + " #czje").val();
            var moneys = curVal.replace(/,/g, "");
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)

            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (moneys <= 0 || !moneys) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (threshold_amount && parseFloat(moneys) < parseFloat(threshold_amount) && isFirstPurchase) { //首次购买
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            } else if (addition_amt && parseFloat(moneys) < parseFloat(addition_amt) && !isFirstPurchase) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                $(_pageId + " .bast_rate").text("--");
                return
            }
            if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) {
                $(_pageId + " .bast_rate").text("--");
                return
            }
            if (_first_max_amt && moneys > parseFloat(_first_max_amt) && isFirstPurchase) {
                layerUtils.iAlert("超过单笔最高限额"+ 
                        tools.fmoney(_first_max_amt + '')+ "元");
                return;
            }
            if (_addition_max_amt && moneys > parseFloat(_addition_max_amt) &&  !isFirstPurchase) {
                layerUtils.iAlert("超过单笔最高限额"+ 
                        tools.fmoney(_addition_max_amt + '')+ "元");
                return;
            }
            //获取单日限购金额
            let sum_max_amt = td_sum_max_amt ? td_sum_max_amt*1 : '';
            if(day_purchase_limit_show == '1' && sum_max_amt && (moneys*1 > sum_max_amt)){
                return layerUtils.iAlert("买入金额不得超过基金单日累计限额"); 
            }
            if (buyflag == "1") {
                let operationId = 'riskAssessment'
                layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>您的风险承受能力为" + userInfo.riskName + "，此产品超过了您的风险承受能力，若仍然选择投资，则表明在上述情况下，您仍自愿投资该产品，并愿意承担可能由此产生的风险</span>", function () {
                    appUtils.pageInit(_pageCode, "safety/riskQuestion");
                }, function funcNo() {
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " #payMethod").show();
                    //是否可以购买
                    isCanBuy();
                }, "重新测评", "继续购买",operationId);
                return;
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #payMethod").show();
            //是否可以购买
            isCanBuy();
        });


        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "template_templateBuy";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //进行充值
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                app_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                fund_code: $(_pageId + " .fund_code").html(),
                buyflag: buyflag,
                period: productInfo.period,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                let prodType = appUtils.getSStorageInfo("prodType");
                if (prodType && prodType == "gather") {
                    service.reqFun106014(param, function (resultVo) {
                        if (resultVo.error_no == "0") {
                            if (buy_state == "1") { //购买
                                purchase(param);
                            } else if (buy_state == "2") { // 预约
                                apponit(param);
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(resultVo.error_info);
                        }
                    }, { isLastReq: false });
                } else {
                    if (buy_state == "1") { //购买
                        purchase(param);
                    } else if (buy_state == "2") { // 预约
                        apponit(param);
                    }
                }

            }, { isLastReq: false });
        });
    }

    // 产品详情查询
    function getFundInfo() {
        var params = {
            fund_code: _fund_code
        }

        service.reqFun102113(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                appUtils.setSStorageInfo("financial_prod_type", result.financial_prod_type);
                threshold_amount = result.threshold_amount;
                addition_amt = result.addition_amt;
                day_purchase_limit_show = result.day_purchase_limit_show ? result.day_purchase_limit_show : '0';
                td_sum_max_amt = result.td_sum_max_amt ? result.td_sum_max_amt : '';
                step_amt = result.step_amt;
                $(_pageId + " .prod_sname").html(result.prod_sname);//产品简称
                $(_pageId + " .header_inner h1").text(result.prod_sname);
                $(_pageId + " .fund_code").html(result.fund_code);//产品编码
                product_risk_level = result.risk_level;
                $(_pageId + " .risk_level_name").html(result.risk_level_name);//风险
                $(_pageId + " .fund_type_name").html(result.fund_type_name);//产品类型
                //持有不足天数
                var tip_max_day = result.tip_max_day;
                $(_pageId + " #tip_max_day").html(tip_max_day);
                $(_pageId + " .buy_tip").html(result.buy_tip);
                if (result.important_tip_buy && result.important_tip_buy == "1") {
                    $(_pageId + " .important_hint").show();
                    //赎回费率
                    initRateInfo();
                }

                var str = "";
                if (threshold_amount && isFirstPurchase) {
                    str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
                } else if (addition_amt && !isFirstPurchase) {
                    str += (addition_amt >= 10000 ? (addition_amt / 10000 + "万") : tools.fmoney(addition_amt)) + '元起购';
                }
                if (step_amt && step_amt > 0) {
                    str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                        tools.fmoney(step_amt + ''))
                        + "元递增"
                }

                if (str) {
                    $(_pageId + " #inputspanid span").text(str);
                    $(_pageId + " #inputspanid span").attr("text", str);
                } else {
                    $(_pageId + " #inputspanid span").text("请输入购买金额");
                    $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
                }
                _first_max_amt = result.first_max_amt;
                _addition_max_amt = result.addition_max_amt;
                buy_state = result.buy_state;
                tools.getPdf("prod", _fund_code, buy_state); //获取协议
                // let chooseClass= text_color(result.risk_level)
                $(_pageId + " .riskLevel").text(result.risk_level_name);
                // $(_pageId + " .riskLevel").addClass(chooseClass)
                //比较风险等级
                compareRiskLevel();
                productInfo = result;
                //获取费率区间
                getRateList()
                //是否展示单日限额
                showDayLimit();
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, { isLastReq: false })
    }
    function showDayLimit(){
        if(day_purchase_limit_show == '1'){
            let str = `单日累计限额${tools.fmoney(td_sum_max_amt*1)}元`
            $(_pageId + " .showLimit").html(str);
            $(_pageId + " .showLimit").show();
        }else{
            $(_pageId + " .showLimit").hide();
        }
    }
    function text_color(risk_level) {
        if (!risk_level) return ''
        if (risk_level.substr(1) >= 4) {
            return 'm_text_red'
        } else {
            return 'm_text_green'
        }
    }
    // 赎回费率查询
    function initRateInfo() {
        var params = {
            fund_code: _fund_code
        }
        service.reqFun102003(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                var redeemRateStr = "";
                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr = '<div class="hint_title"><span>持有期限</span><span>赎回费率</span></div>';
                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var datestr = "";
                        var fcitem_lval = result.redeemRate[i].fcitem_lval; //最小
                        var fcitem_lvunit = result.redeemRate[i].fcitem_lvunit;//最小单位
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;//最大
                        var fcitem_tvunit = result.redeemRate[i].fcitem_tvunit;//最大单位
                        if (fcitem_tval == "-1") { //最大
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                        } else if (fcitem_lval == "0") { //最小
                            datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                        } else {
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                        }
                        redeemRateStr += '<div class="hint_item">' +
                            '<span>' + datestr + '</span>' +
                            '<span>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</span>' +
                            '</div>';
                    }
                }
                $(_pageId + " .hint_table").html(redeemRateStr);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //比较风险等级
    function compareRiskLevel() {
        var userRiskLevel = userInfo.riskLevel;
        userRiskLevel = +(userRiskLevel.substr(-1))
        product_risk_level = (+product_risk_level.substr(-1));
        if (product_risk_level == 1) return;
        if (product_risk_level > userRiskLevel) {
            buyflag = "1";
        }
    }


    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //现金宝认购、申购产品
    function purchase(param) {
        service.reqFun106003(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "template/templateBuyResult", data.results[0]);
        })
    }

    // 现金宝预约产品
    function apponit(param) {
        service.reqFun106008(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "template/templateBuyResult", data.results[0]);
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                if (moneys) {
                    // moneys = tools.fmoney(moneys);
                    //获取单日限购金额
                    let sum_max_amt = td_sum_max_amt ? td_sum_max_amt*1 : '';
                    if(day_purchase_limit_show == '1' && sum_max_amt && (moneys*1 > sum_max_amt)){
                        //字体标红
                        $(_pageId + " .showLimit").addClass("m_text_red");
                        $(_pageId + " #inputspanid span").addClass('m_text_red')
                        $(_pageId + " .thfundBtn .buy").addClass('no_active');
                    }else{
                        $(_pageId + " .showLimit").removeClass("m_text_red");
                        $(_pageId + " #inputspanid span").removeClass('m_text_red');
                        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
                    }
                }
                $(_pageId + " #czje").val(tools.fmoney(moneys));
                reqFun102045();
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return reqFun102045()
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                }
                reqFun102045();
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                    //获取单日限购金额
                    let sum_max_amt = td_sum_max_amt ? td_sum_max_amt*1 : '';
                    if(day_purchase_limit_show == '1' && sum_max_amt && (moneys*1 > sum_max_amt)){
                        //字体标红
                        $(_pageId + " .showLimit").addClass("m_text_red");
                        $(_pageId + " #inputspanid span").addClass('m_text_red');
                        $(_pageId + " .thfundBtn .buy").addClass('no_active');
                    }else{
                        $(_pageId + " .showLimit").removeClass("m_text_red");
                        $(_pageId + " #inputspanid span").removeClass('m_text_red')
                        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
                    }
                }
                if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                reqFun102045();
            }
        })
    }

    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];

            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额：<em class="money">' + tools.fmoney(_available_vol + "") + '</em>元';

            $(_pageId + " .pay_bank").html(html);
        })
    }

    //查询产品是否首次购买
    function reqFun102103() {
        var param = {
            fund_code: _fund_code,
        }
        service.reqFun102103(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (results.is_can_add == 0) {
                    isFirstPurchase = false;
                } else {
                    isFirstPurchase = true;
                }
            } else {
                isFirstPurchase = true;
                layerUtils.iAlert(data.error_info);
            }
            //获取产品详情
            getFundInfo();
        }, { isLastReq: false })
    }

    //获取产品费率
    async function reqFun102045(str) {
        var money = $(_pageId + " #czje").val().replace(/,/g, "");
        if (!money || money == '0') money = 0
        let results = await tools.setRate(money, rateList);
        let rateTxt = ''
        if (!results.chgrate_tval) return
        if (str == 'init' || !money || money <= 0) {  //初始化
            if (!results.discount_ratevalue || results.discount_ratevalue == '') {
                rateTxt = `申购费率：<span class=''>${tools.fmoney(results.chgrate_tval) + results.chgrate_unit}</span>`
            } else {
                rateTxt = `申购费率：<span style='text-decoration: line-through' class=''>${tools.fmoney(results.chgrate_tval) + results.chgrate_unit}</span> <span>${tools.fmoney(results.discount_ratevalue) + results.chgrate_unit}</span>`
            }
            return $(_pageId + " .rate_text").html(rateTxt);
        }
        //判断费率是固定费率还是 %费率
        if (results.chgrate_unit == '元/笔') {
            if (results.chgrate_tval && !results.discount_ratevalue) {
                rateTxt = `估算费用：<span class='text_red'>${tools.fmoney(results.chgrate_tval)}元</span><span>(申购费率:${results.chgrate_tval + results.chgrate_unit})</span>`
                return $(_pageId + " .rate_text").html(rateTxt);
            } else if (results.chgrate_tval && results.discount_ratevalue) {
                rateTxt = `估算费用：<span class='text_red'>${tools.fmoney(results.discount_ratevalue) + '元'}</span > (申购费率:<span style='text-decoration: line-through'>${results.chgrate_tval + results.chgrate_unit}</span> <span>${tools.fmoney(results.discount_ratevalue) + results.chgrate_unit}<span>)<span class=''> 省${tools.fmoney(results.chgrate_tval - results.discount_ratevalue)}元</span>`
                return $(_pageId + " .rate_text").html(rateTxt);
            }
        } else if (results.chgrate_unit == '%') {
            let unit = results.chgrate_unit;
            let estimateMoneyMax = common.floatSub(money, common.floatDivide(money, (1 + results.chgrate_tval / 100)))   //估算费用最大值
            if (results && results.chgrate_tval && !results.discount_ratevalue) {   //没有优惠费率
                rateTxt = `估算费用：<span class='text_red'>${tools.fmoney(estimateMoneyMax)}元</span><span>(申购费率:${tools.fmoney(results.chgrate_tval) + unit})</span>`
            }
            if (results && results.discount_ratevalue) {    //拥有优惠费率
                let estimateMoneyMin = common.floatSub(money, common.floatDivide(money, (1 + ((results.discount_ratevalue / 100)))))  //估算费用最小值
                let newMoney = common.floatSub(estimateMoneyMax, estimateMoneyMin)
                rateTxt = `估算费用：<span class="text_red"> ${tools.fmoney(estimateMoneyMin)}元</span>(申购费率:<span style='text-decoration: line-through'>${tools.fmoney(results.chgrate_tval) + unit}</span>  ${tools.fmoney(results.discount_ratevalue) + unit})<span class=''> 省${tools.fmoney(newMoney)}元</span>`;
            }
            $(_pageId + " .rate_text").html(rateTxt);
        }
    }

    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }


    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html(tools.fmoney($(_pageId + " #czje").val().replace(/,/g, "")));
    }

    function destroy() {
        $(_pageId + " .showLimit").hide();
        tools.recordEventData('4','destroy','页面销毁');
        get_pdf_file.clearTime()
        guanbi();
        $(_pageId + " .showLimit").removeClass("m_text_red");
        $(_pageId + " .agreement1").hide();
        $(_pageId + " .agreement").attr("isVerify", "false");
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");//产品编码
        $(_pageId + " .fund_type_name").html("--");//产品编码
        $(_pageId + " #czje").val("");
        $(_pageId + " #inputspanid span").text("").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " .agreement1 i").removeClass("active");
        $(_pageId + " .hint_table").html("");
        $(_pageId + " .rate_text").html("");
        $(_pageId + " .important_hint").hide();
        $(_pageId + " .van-overlay").hide();
        $(_pageId + " .showLimit").removeClass("m_text_red");
        $(_pageId + " #inputspanid span").removeClass('m_text_red');
        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
        buyflag = "";
        buy_state = "";
        monkeywords.destroy();
        _first_max_amt = "";
        productInfo = null;
        $(_pageId + " .jjs_yue").hide();
        $(_pageId + " .agreement_layer").hide();

    }

    function pageBack() {
        appUtils.pageBack();
    }
    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
