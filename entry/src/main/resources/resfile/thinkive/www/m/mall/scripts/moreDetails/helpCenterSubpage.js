/**
 *
 * <AUTHOR>
 * @Description 帮助中心二级菜单
 * @version V1.0
 * @Date 2017-7-26 下午4:56:37
 */

define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        gconfig = require("gconfig"),
        _pageUrl = "moreDetails/helpCenterSubpage",
        service = require("mobileService"),
        _pageId = "#moreDetails_helpCenterSubpage ";
    var windowHeight;
    var tools = require("../common/tools");


    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        var info = appUtils.getSStorageInfo("helpCenterInfo");
        service.reqFun102063({parentId: info.id}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var array = data.results;
            $(_pageId + "#menu_level1").html(info.name);//显示一级菜单名称
            var html = "";
            for (var i = 0; i < array.length; i++) {
                html += "<p id='" + array[i].id + "'><a href='javascript:void(0)'>" + array[i].title + "</a></p>";
            }
            $(_pageId + "#menu_level2").html(html);
        })
        //清除上次页面
        windowHeight = $(window).height();
    }


    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //客服电话
        appUtils.bindEvent($(_pageId + " #phone"), function () {
            $(_pageId + "#phone_div").show();
            $(_pageId + ".pop_layer").show();
        });
        //取消拨打电话
        appUtils.bindEvent($(_pageId + " #call_off"), function () {
            $(_pageId + "#phone_div").hide();
            $(_pageId + ".pop_layer").hide();
        });
        //拨打电话
        appUtils.bindEvent($(_pageId + " #call_up"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = gconfig.global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //目录点击事件
        appUtils.preBindEvent($(_pageId + " #menu_level2"), $(_pageId + " #menu_level2 p"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target).closest("p")[0];
            //跳转到内容页
            appUtils.setSStorageInfo("helpCenterContentInfo", {id: node.id, name: $(node).text()});
            appUtils.pageInit(_pageUrl, "moreDetails/helpCenterContentpage");
        });
        //在线客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = _pageUrl;
            param["param"] = appUtils.getPageParam();
            tools.saveAlbum(_pageUrl,param)
        });
        //搜索按钮
        appUtils.bindEvent($(_pageId + " #searchButton"), function () {
            $(_pageId + "#searchText").blur();
            var key = $.trim($(_pageId + " #searchText").val());
            if (!key) {
                layerUtils.iAlert("请输入问题关键字");
                return;
            }
            var param = {
                "searchText": key,
                "backPageUrl": _pageUrl,
                "param": appUtils.getPageParam()
            };
            appUtils.pageInit(_pageUrl, "moreDetails/helpCenterSearch", param);
        });

    }

    function destroy() {
        $(_pageId + "#menu_level1").html("");
        $(_pageId + "#menu_level2").html("");
        $(_pageId + "#phone_div").hide();
        $(_pageId + ".pop_layer").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var helpCenter = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = helpCenter;
});
