/**
 * 程序入口配置读取
 * 项目开发时需要的自定义配置
 */
define(function (require, exports, module) {
    require('./js/video.min.js');
    require("./scripts/common/pdf.js");
    /*判断是否是iphonex*/
    /*var IphoneXcss="";
    if((/iphone/gi.test(navigator.userAgent) && (screen.height == 812 && screen.width == 375))){
        IphoneXcss='/css/iphonex.css';
    }*/
    // 获取当前页面的完整URL 获取原生传递的参数 isWelcomePage  0 非欢迎页  1 欢迎页
    const currentUrl = window.location.href;
    // 创建URL对象
    const url = new URL(currentUrl);
    // 获取查询参数部分
    const searchParams = url.searchParams;

    // 获取isWelcomePage参数的值
    const isWelcomePage = searchParams.get('isWelcomePage');
    var configuration = {
        /*******************************************必配项***************************************************/
        /**
         * 项目的默认页面，当在地址栏输入的url不带pageCode（“#!/”至“.html”中间的部分）时进入该配置对应的页面，
         * 这个参数是不带pageCode时进入默认页面的入参
         * 默认为：{}
         */
        "defaultPage": { "pageCode": "login/userIndexs", "jsonParam": {} },
        /******************************************选择可配项************************************************/
        /**
         * 项目模块名，默认为project
         */
        "projName": "mall",
        /**
         * 项目中的需要先加载的css样式文件，如果多个，添加在数组里面中，从css目录下写文件路径
         * 不配默认为：["/css/app_style.css"]
         */
        "firstLoadCss": ["/css/verify.css", "/css/pdfh5.css", "/css/animate.css", "/css/common.css", "/css/style.css", "/css/ui-font.css", "/css/activity.css", "/css/template.css", "/css/video-js.min.css"],
        /**
         * 后台返回结果集出参结构，类似error_no、error_info的出参命名定义，
         * 防止不同项目的后台的出参命名不一致，以便框架可取配置的值，由项目自己定义，但后台必须统一
         * 不配默认为：{"error_no": "error_no", "error_info": "error_info"}
         */
        "resultsParser": { "error_no": "error_no", "error_info": "error_info" },
        /**
         * 该配置是请求接口被后台拦截器拦截之后返回的错误号对应的处理
         * 前端根据后台的error_no做的过滤器配置，需要后台配合定义error_no，
         * 有的需要跳转页面，有的只做提示，提示信息如果后台给出，就取后台提示信息，否则取配置中的error_info字段
         * 不配默认为：{}
         */
        "filters": {
            /*"-999": {"pageCode": "login/userLogin", "jsonParam": {}, "error_info":"请先登录"} //用户没有登陆*/
            "-999": { "moduleAlias": "putils", "moduleFuncName": "filterLoginOut" }, //用户没有登陆或者登陆超时
            "-99900": { "moduleAlias": "putils", "moduleFuncName": "filterNotBindCard" },
            "-99903": { "moduleAlias": "putils", "moduleFuncName": "filterLoginOut" },
            "-1": { "moduleAlias": "putils", "moduleFuncName": "riskTransLimit" },
        },
        /**
         * 整个项目的登录页面
         * 不配默认为：{}
         */
        "loginPage": { "pageCode": "login/userLogin", "jsonParam": {} },
        /**
         * 整个应用的引导页配置
         * 不配默认为：{}
         */
        "guidePage": isWelcomePage == '0' ? {} :{ "pageCode": "guide/guidePage", "jsonParam": {} },
        /**
         * 项目中公用模块的别名配置
         * 不配默认为：{}
         */
        "pAlias": {
            "common": "mall/scripts/common/common",
            "cfdUtils": "mall/scripts/common/cfdUtils",
            "mobileService": "mall/service/mobileService",
            "putils": "mall/scripts/common/putils",
            "constants": "mall/scripts/constants/serviceConstants",
            "keyPanel": "plugins/keypanel/scripts/keyPanel",
            "errorfilter": "mall/scripts/common/errorfilter",
            "errorFilters": "mall/scripts/common/putils"
        },
        /**
         * 跳转页面时做的权限校验，提供在外面的方法
         * moduleAlias为项目通用模块配置的别名，moduleFuncName方法里面写校验规则，返回true或者false，避免写异步的代码
         * 不配默认为：{}
         */
        //		"checkPermission": {"moduleAlias":"common", "moduleFuncName":"checkPermission"},
        /**
         * 第一次加载第一个业务模块前所需要的处理，即启动之后提供给外界初始化的接口，
         * 这个方法中避免写异步操作，或者保证异步影响其他代码逻辑
         * moduleAlias为项目通用模块配置的别名，moduleFuncName为执行的方法
         * 这个配置可以做很多事情，当你从业务模块逻辑上不好实现时，可以考虑这里！！
         * 不配默认为：{}
         */
        "firstLoadIntf": { "moduleAlias": "common", "moduleFuncName": "firstLoadFunc" },
        /**
         * 项目中需要调用到的常量、变量这里配置，调用方式，通过require("gconfig").global.*来调用
         * 不配默认为：{}
         */
        "global": {
            "noHmFuncMsg":"暂未开放此功能",//鸿蒙功能不全提示
            "jjbCode":'000709',
            "buriedPointsNum":5,//埋点上报条数阈值
            "buriedPointsNumTrue":15,//埋点上报MAX条数
            "soft_no": "and",
            "version_code": "1",//当前版本号
            "version_name": "1.0",//版本名称,
            "isSign": 1,//接口是否签名0签名，1签名
            "isFordHttpReq": 1,//1:調用接口有原生來執行
            "encryMode": "des",//默认des,和后台保持一致
            "ssoSignKey": "whiktigWnpLUeONpwaF4xaAqFj3AhMcxrQeCAFqbsNFA5gGPNuFANIhtw7cLGn59PjafFndNBZG+wflPxJP/pivaZNAyip3iZpxVaIrgykI=",
            "desKey": "thinkive_mall",  //商城 DES key 与configuration.xml一致
            "filterNo": "1000245",
            "updateTime": 600000,//静默升级查询间隔时间10分钟
            "qualifiedInvestorStartAmount": "3000000",
            "custServiceTel": "************",
            "holding_days": "7",
            "videoAuthentication": {
                //视频配置 优先从url链接上获取视频参数，若没有再取此配置（在app.js中处理)
                "needOneWayVideo": 0, // 打开单向视频 1开启 0关闭(app有效)
                "oneWayVideoCode": "owappvideo", // 单向视频认证标志
                "needTwoWayVideo": 1, // 打开双向视频 1开启 0关闭(app有效)
                "twoWayVideoCode": "twvideo", // 双向视频认证标志
                "useTysp": 1, //是否是统一视频  默认0
                "videoType": "0", //1anychat 0tchat 默认0 注意需要引号
                "anychatAppId": "E507FECF-7B94-AD49-B9A3-5B84BD30311A" //anycaht集群id
            },
            "appletAuthentication": {
                "miniprogramType": "0", // 2 体验版  0 正式
                // "appId": "gh_212a2f9d51d8",  // 信通测试   
                "appId": "gh_396429a6778b"  // 晋金财富
            },
            //本地
            // "serverPath":"http://127.0.0.1:8081/servlet/json",
            // "serverUrl":"http://127.0.0.1:8081",
            // "videoPath": "http://**************:8080/servlet/json",
            // "oss_url": "http://sxjjs.oss-cn-shanghai.aliyuncs.com/",

            //公有云地址
            // "serverPath": "https://jjdxcs.xintongfund.com/servlet/json",
            // "videoPath": "http://**************:8080/servlet/json",
            // "serverUrl": "https://jjdxcs.xintongfund.com",
            // "oss_url": "https://jjdxcs.xintongfund.com/oss/",
            // "wxfund_url": "https://testthfund.sxjjd.com/m/mall/index.html#!/outside/index.html",
            // "video_oss_url": "https://jjdxcs.xintongfund.com/oss/",

            //准生产地址
            // "serverPath": "https://xhxts.xintongfund.com/servlet/json",
            // "videoPath": "http://**************:8080/servlet/json",
            // "serverUrl": "https://xhxts.xintongfund.com/",
            // "oss_url": "https://xhxts.xintongfund.com/",
            // "wxfund_url": "https://thfund.sxfae.com/m/mall/index.html#!/outside/index.html",
            // "video_oss_url": "https://jjdx-video.oss-cn-shanghai.aliyuncs.com/"

            //本地记账测试
            //          "serverPath": "http://120.76.2.197:8081/servlet/json",
            //          "videoPath": "http://**************:8080/servlet/json",
            //          "serverUrl": "http://120.76.2.197:8081",
            //          "oss_url": "https://market.sxfae.com/",
            //          "wxfund_url": "http://120.76.2.197/m/mall/index.html#!/outside/index.html",            


            //生产地址
            "serverPath": "https://m.xintongfund.com/servlet/json",
            "videoPath": "http://112.74.253.129:8080/servlet/json",
            "serverUrl": "https://m.xintongfund.com",
            "oss_url": "https://market.xintongfund.com/",
             "wxfund_url": "https://thfund.sxfae.com/m/mall/index.html#!/outside/index.html",
            "video_oss_url":"https://jjdx-video.oss-cn-shanghai.aliyuncs.com/"

        },
        /**
         * Android手机返回键处理，退出应用还是返回上级页面，true-退出应用，false-返回页面，默认为true
         * 如果需要返回上一级页面，并最终提示退出应用，需要改为false，并且在一级页面的html上设置“data-pageLevel="1"”
         * 不配默认为：true
         */
        "isDirectExit": false,
        /**
         * 弹出层各种弹出层主题样式，默认为系统自带
         * 不配默认为："default"
         */
        "layerTheme": "d",
        /**
         * ajax请求超时时间设置，默认为20秒之后超时
         * 不配默认为：20秒
         */
        "ajaxTimeout": 30,
        /**
         * 当弹出等待层时（iLoading），点击遮罩层是否关闭遮罩，关闭后用户可操作页面
         * 不配默认为：false
         */
        "isClickShadeHide": false
    };
    //暴露对外的接口
    module.exports = window.configuration = configuration;
});
