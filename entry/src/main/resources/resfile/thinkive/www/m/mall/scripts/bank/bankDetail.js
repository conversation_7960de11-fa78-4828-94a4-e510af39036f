// 银行存款详情页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageCode = "bank/bankDetail",
        _pageId = "#bank_bankDetail ";
    var productInfo;
    var prod_status;//产品状态
    var ut = require("../common/userUtil");

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        if(productInfo.isBuy == "0") { //持有页面 点击提前支取不允许再次购买
            $(_pageId + " .thfundBtn").hide();
            $(_pageId + " #product").css({paddingBottom: 0})
        } else {
            $(_pageId + " #product").css({paddingBottom: "0.55rem"})
            $(_pageId + " .thfundBtn").show();
        }
        //获取详情
        reqFun151117();
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.bindEvent($(_pageId + " #question"), function () {
            appUtils.pageInit(_pageCode, "moreDetails/helpCenter");
        });
        appUtils.bindEvent($(_pageId + " #bankInsuranceSystem"), function () {
            appUtils.pageInit(_pageCode, "bank/bankInsuranceSystem");
        });
        //银行简介
        appUtils.bindEvent($(_pageId + " #bankBrief"), function () {
            appUtils.pageInit(_pageCode, "bank/bankBrief");
        });
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageCode)) return;
            if (prod_status != "2") return;
            service.reqFun151110({bank_channel_code: productInfo.bank_channel_code}, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.results[0].bank_flag == 0) { //未开户
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                appUtils.pageInit(_pageCode, "bank/purchase");
            })
        })
    }


    function setProductInfo() {
        prod_status = productInfo.prod_status;
        var btnObj = {
            "2": {
                class: "",
                text: "购买"
            },
            "4": {
                class: "sold_out",
                text: "售罄"
            },
            "6": {
                class: "sold_out",
                text: "停售"
            },
        }
        $(_pageId + " .buy span").html(btnObj[prod_status].text).addClass(btnObj[prod_status].class);


    }


    function reqFun151117() {
        var depDayTypeName = {
            "D": "天",
            "M": "月",
            "Y": "年",
        }
        var params = {
            bank_channel_code: productInfo.bank_channel_code,
            prod_code: productInfo.prod_code
        };
        service.reqFun151117(params, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.prod_info) {
                    var prod_info = results.prod_info;
                    productInfo = results.prod_info;
                    $(_pageId + " .compare_benchmark").html(tools.fmoney(prod_info.bas_int_rate) + "%"); //收益
                    $(_pageId + " .survAmt").html(tools.fmoney(prod_info.surv_amt + "") + "元");//起投
                    if (prod_info.is_transfer == "1") { //可转让产品展示期限，不可转让产品展示付息期限
                        $(_pageId + " .earDrwTypeText").html("期限");//
                        $(_pageId + " .earDrwType ").html(prod_info.prod_dep_day + depDayTypeName[prod_info.dep_day_type]);//产品存期 + 存期类型持有期限
                    } else {
                        $(_pageId + " .earDrwTypeText").html("付息期限");//
                        $(_pageId + " .earDrwType ").html(prod_info.pay_int_hz + depDayTypeName[prod_info.pay_int_type]);//产品存期 + 存期类型持有期限
                    }
                    $(_pageId + " .bank_channel_name").html(prod_info.bank_channel_name);

                    $(_pageId + " .logo").attr("src", tools.judgeBankImg(prod_info.bank_channel_code).logo2);
                    //计息规则
                    if (prod_info.interest_rule && !validatorUtil.isEmpty(prod_info.interest_rule)) {
                        var str = ""
                        var ruleArr = prod_info.interest_rule.split("|");
                        for (var i = 0; i < ruleArr.length; i++) {
                            str += "<p>" + ruleArr[i] + "</p>";
                        }
                        $(_pageId + " .rules_content").html(str)
                    } else {
                        $(_pageId + " .rules_content").html("无")
                    }
                    var productInfo1 = appUtils.getSStorageInfo("productInfo");
                    appUtils.setSStorageInfo("productInfo", Object.assign(productInfo1, results.prod_info));
                    setProductInfo();
                    // $(_pageId + " .remark1").html(prod_info.remark1);
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        $(_pageId + " .buy span").html("").removeClass("sold_out");
        prod_status = "";
        $(_pageId + " .buy span").html("");
        $(_pageId + " .compare_benchmark").html("--"); //收益
        $(_pageId + " .survAmt").html("--");//起投
        $(_pageId + " .earDrwType ").html("--");//持有期限
        $(_pageId + " .bank_channel_name").html("--");
        $(_pageId + " .rules_content").html("");
        // $(_pageId + " .remark1").html("--");
        $(_pageId + " .logo").attr("src", "");
        $(_pageId + " .earDrwTypeText").html("");//
        $(_pageId + " .thfundBtn").hide();



    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var bankDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankDetail;
});
