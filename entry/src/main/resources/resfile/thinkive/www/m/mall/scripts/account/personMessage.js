// 个人信息


//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        tools = require("../common/tools"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#account_personMessage";
    var _pageCode = "account/personMessage";    
    var riskResult = "";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    var common = require("../common/common");
    var userInfo;

    function init() {
         //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        // if(!userInfo.bankAcct || userInfo.bankAcct == ''){
        //     $(_pageId + " #change").hide()
        // }else{
        //     $(_pageId + " #change").show()
        // }
        var head_portrait = appUtils.getPageParam("head_portrait");
        if (head_portrait == "ok") {
            layerUtils.iMsg(-1, "头像上传成功！");
        } else if (head_portrait == "no") {
            layerUtils.iMsg(-1, "头像上传失败！");
        }
        if (ut.getUploadStatus()) {
            $(_pageId + " #uploadPhoto").html("已上传").css({ color: "#3f4e59" });
        } else {
            $(_pageId + " #uploadPhoto").html("上传").css({ color: "#1199EE" });
        }
        $(_pageId + " #phoneNum").html("");
        $(_pageId + " #cardNo").html("--");
        $(_pageId + " #pRrisk").html("");
        $(_pageId + " #bankphoneNum").html("");
        setUserRick();
        setUserInfo();
        showOtherName();//显示别名
        showEmial();//显示邮箱
        getBankCardTailNum();//显示银行卡尾号
        setQualifiedInvestor(); //显示合格投资人认证状态
    }


    //获取银行卡尾号 将从后台获取调整为本地获取
    function getBankCardTailNum() {
        if (userInfo && userInfo.bankAcct) {
            $(_pageId + " #mybank_no_bank").hide();
            $(_pageId + " #mybank").show();
            var card_no = ut.getUserInf().bankAcct;
            var tailNum = card_no.substr(card_no.length - 4, 4);
            $(_pageId + " #bankCardTailNum").html("尾号:" + tailNum);
        } else {
            $(_pageId + " #mybank_no_bank").show();
            $(_pageId + " #mybank").hide();
        }

    }

    //绑定事件
    function bindPageEvent() {
        //点击平台手机号
        appUtils.bindEvent($(_pageId + " #change"), function () {
            if(userInfo.login_pwd_state != '1' && !userInfo.bankAcct){
                //没有设置登录密码
                return layerUtils.iConfirm("请先设置登录密码", function () {
                }, function () {
                    appUtils.pageInit(_page_code, "login/setPassword", {});
                }, "取消", "去设置");
            }
            appUtils.setSStorageInfo("isUserBankCard", userInfo.bankAcct ? true : false);
            appUtils.pageInit(_pageCode, "safety/changePhoneNumber", {});
        });
        //变更预留手机号
        appUtils.bindEvent($(_pageId + " #bankphonechange"), function () {
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "safety/changeBankphone", {});
            }, true, "您正处于换卡阶段，暂不支持该项操作")
        });
        //点击设置电子邮箱
        appUtils.bindEvent($(_pageId + " #setEmial"), function () {
            appUtils.pageInit(_pageCode, "safety/setEmial", {});
        });
        //点击修改电子邮箱
        appUtils.bindEvent($(_pageId + " #updateEmial"), function () {
            appUtils.pageInit(_pageCode, "safety/updateEmial", {});
        });
        //点击设置别名
        appUtils.bindEvent($(_pageId + " #setOtherName"), function () {
            appUtils.pageInit(_pageCode, "safety/setOtherName", {});
        });
        //点击修改别名
        appUtils.bindEvent($(_pageId + " #updateOtherName"), function () {
            appUtils.pageInit(_pageCode, "safety/updateOtherName", {});
        });
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击更换照片
        appUtils.bindEvent($(_pageId + " .avatar_box"), function () {
            // let str = '<div class="pop_layer camera_pop_layer">\n' +
            //         '            <div  class="slideup in">\n' +
            //         '                <div class="camera">拍照</div>\n' +
            //         '                <div class="album">从相册中选取</div>\n' +
            //         '                <div class="cancel_camera">取消</div>\n' +
            //         '            </div>\n' +
            //         '        </div>'
            // if ($(_pageId + " .camera_pop_layer").length > 0) {
            //     $(_pageId + " .camera_pop_layer").show();
            // } else {
            //     $(_pageId).append(str);
            // }

            var param = {};
            param["funcNo"] = "50273";
            param["moduleName"] = "mall";
            // param["serverAddr"] = global.head_address + "/servlet/FileUpload?function=uploadImg";
            param["fileName"] = "headerImg";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#111111";
            }
            param.paramExt = {
                multi: false
            }
            param["cutFlag"] = "0";
            param["compress"] = "0.8";
            param["width"] = "500";
            param["height"] = "500";
            // let external = require("external");
            tools.fileImg(_pageId, param)
            // //调用相册
            // appUtils.bindEvent($(_pageId + " .album"), function () {
            //     param["mode"] = 1
            //     external.callMessage(param);
            // });
            // //调用相机
            // appUtils.bindEvent($(_pageId + " .camera"), function () {
            //     param["mode"] = 2
            //     external.callMessage(param);
            // });
            // //关闭弹框
            // appUtils.bindEvent($(_pageId + " .camera_pop_layer"), function (e) {
            //     e.stopPropagation();
            //     $(_pageId + ' .camera_pop_layer').hide();
            // });
            // appUtils.bindEvent($(_pageId + " .cancel_camera"), function (e) {
            //     $(_pageId + ' .camera_pop_layer').hide();
            // });
        });

        //我的银行卡
        appUtils.bindEvent($(_pageId + " #mybank"), function () {
            appUtils.pageInit(_pageCode, "account/bankCardAdministration", {});
        });

        //点击风险测评
        appUtils.bindEvent($(_pageId + " #pRrisk").parent("a"), function () {
            if (riskResult == "0") {
                appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
            } else {
                appUtils.pageInit(_pageCode, "safety/riskResult", {});
            }
        });
        //点击上传身份证上传
        appUtils.bindEvent($(_pageId + " #reUpload"), function () {
            let noUploadIdCard = userInfo.idCardUploadFlag != "1" ? '1' : '';
            appUtils.setSStorageInfo("noUploadIdCard", noUploadIdCard);
            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
        });
        //销户
        appUtils.bindEvent($(_pageId + " #logOff"), function () {
            if (userInfo && userInfo.bankAcct) { 
                appUtils.pageInit(_pageCode, "account/logOff1");
            } else {
                layerUtils.iAlert("您还未完成绑卡,请拨打客服电话注销账户,客服电话：" + require("gconfig").global.custServiceTel,-1,function(){
                   
                });
            }
        });
        //合格投资人认证
        appUtils.bindEvent($(_pageId + " #qualifiedInvestor"), function () {
            var text = $(_pageId + " #qualifiedInvestor span").html();
            if (text == "已认证" || text == "审核中") return;
            appUtils.pageInit(_pageCode, "highEnd/qualifiedInvestor1");
        });

    }
    function setUserInfo() {
        // console.log(userInfo);
        /*性别判断
        if (parseInt(identity_num.substr(16, 1)) % 2 == 1) {
            var sex = "男";
        } else {
            var sex = "女";
        }*/
        //$("#sex").html(sex);
        /*$(_pageId+" #name").html(custname);*/
        if (userInfo && userInfo.bankAcct) {
            $(_pageId + " #bankphonechange_p").show();
            $(_pageId + " #bankphonechange_p_no_bank").hide();
            $(_pageId + " #cardNo").html(userInfo.identityNum);
            $(_pageId + " #bankphoneNum").html(userInfo.bankReservedMobile);
        } else {
            $(_pageId + " #cardNo").html("--");
            $(_pageId + " #bankphonechange_p_no_bank").show();
            $(_pageId + " #bankphonechange_p").hide();

        }
        $(_pageId + " #phoneNum").children("span").html(userInfo.mobile);

    }

    /**
     * 邮箱设置
     */
    function showEmial() {
        if (userInfo && userInfo.bankAcct) {
            $(_pageId + " #email_no_bank").hide();
            var email = userInfo.email;
            if (!email) {
                $(_pageId + " #setEmial").show();
                $(_pageId + " #updateEmial").hide();
            } else {
                $(_pageId + " #setEmial").hide();
                $(_pageId + " #updateEmial").show();
                var n = email.indexOf("@");
                var email2 = email.substring(0, 1) + "****" + email.substring(n - 1);
                $(_pageId + " #emial_span").html(email2);
            }
        } else {
            $(_pageId + " #setEmial").hide();
            $(_pageId + " #updateEmial").hide();
            $(_pageId + " #email_no_bank").show();
            $(_pageId + " #emial_span").html("");
        }
      
    }

    /**
     * 新增别名设置
     * **/
    function showOtherName() {

        var _otherName = userInfo.otherName;
        if (!_otherName) {
            $(_pageId + " #setOtherName").show();
            $(_pageId + " #updateOtherName").hide();
        } else {
            $(_pageId + " #setOtherName").hide();
            $(_pageId + " #updateOtherName").show();
            $(_pageId + " #otherName_span").html(_otherName);
        }
    }

    //获得用户风险等级
    function setUserRick() {
        //设置用户头像
        service.reqFun1100007({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var result = (data.results)[0];
                riskResult = result.riskLevel;
                var mobile = result.mobile;
                var identityNum = result.identityNum;
                var photo_url = result.photoUrl;//用户头像
                if (photo_url) {
                    $(_pageId + " #headPortrait img").attr("src", global.oss_url + photo_url + "?key=" + new Date().getTime());
                    var external = require("external");
                    var params = {
                        funcNo: "50042",
                        key: "photo_url",
                        isEncrypt: "1",
                        value: global.oss_url + photo_url + "?key=" + new Date().getTime()
                    };
                    external.callMessage(params);
                } else {
                    $(_pageId + " #headPortrait img").attr("src", "./images/highEnd/high_headerImg.png");
                    var external = require("external");
                    var params = {
                        funcNo: "50042",
                        key: "photo_url",
                        value: "",
                        isEncrypt: "1"
                    };
                    external.callMessage(params);
                }
                $(_pageId + " #pRrisk").html(result.riskName);
                $(_pageId + " #phoneNum").html(mobile);
                if (userInfo && userInfo.bankAcct) {
                    $(_pageId + " #cardNo").html(identityNum);
                } else {
                    $(_pageId + " #cardNo").html("--");
                }

            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    function setQualifiedInvestor() {
        if (userInfo && userInfo.bankAcct) {
            service.reqFun101040({}, function (data) {
                if (data.error_no == "0") { //合格投资人状态 0:不合格, 1:合格, 2:豁免, 3:未鉴定, 4:已到期, 5:审核中
                    var accredited_investor = data.results[0].accredited_investor;
                    //完善信息状态  0:未完善 1:已完善 2:证件到期
                    var perfect_info = data.results[0].perfect_info;
                    if (accredited_investor == "1" || accredited_investor == "2") {
                        $(_pageId + " #qualifiedInvestor span").html("已认证");
                    } else if (accredited_investor == "5") {
                        $(_pageId + " #qualifiedInvestor span").html("审核中");
                    } else {
                        $(_pageId + " #qualifiedInvestor span").html("去认证");
                    }
                    //身份证到期
                    if(userInfo.idCardUploadFlag != "1"){
                        //未上传过
                        $(_pageId + " #hasUpload").text("未上传");
                        $(_pageId + " #reUpload").html("去上传").show();
                    }else{
                        if (perfect_info == "2") {
                            $(_pageId + " #hasUpload").text("已过期");
    
                        } else {
                            $(_pageId + " #hasUpload").text("已上传");
                        }
                        
                        $(_pageId + " #reUpload").html("重新上传").show();
                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        } else {
            $(_pageId + " #hasUpload").text("--");
        }

    }

    function destroy() {
        $(_pageId).find("#otherName_span").html("");
        $(_pageId).find("#emial_span").html("");
        $(_pageId + " #qualifiedInvestor span").html("");
        $(_pageId + " #hasUpload").html("");
        $(_pageId + " #reUpload").html("").hide();
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var myProfit = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myProfit;
});
