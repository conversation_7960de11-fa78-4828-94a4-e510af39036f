// 晋金普惠列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        ut = require("../common/userUtil"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        gconfig = require("gconfig"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _page_code = "login/listMorePage",
        _pageId = "#login_listMorePage ";
    var global = gconfig.global;
    let channelCode, _cust_fund_type;
    var numPerPage = 10;
    let order_by, sort //查询时 查询条件
    var start = 1;//分页参数-开始下标
    var count = 10;//分页参数-每页条数
    let userLoginStatus; //用户登录状态 0未登录 1已登录
    let userAuthenticationStatus; //用户认证状态
    let chooase_financial_prod_type,chooseVerson,userChooseVerson;
    let scene_code;//用户版本
    async function init() {
        var financial_prod_type = appUtils.getSStorageInfo("productType");
        //获取当前用户选中的版本
        userChooseVerson = common.getLocalStorage("userChooseVerson") ; //判断用户是否登陆过
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        //获取当前用户版本号
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        scene_code = scene_code ? scene_code : '';
        //获取用户当前切换的版本
        chooseVerson = appUtils.getSStorageInfo("chooseVerson") ? appUtils.getSStorageInfo("chooseVerson") : '';
        // scene_code = '3'
        //存在用户切换版本
        if(userChooseVerson && userChooseVerson !=''){
            if(userChooseVerson == '3' && financial_prod_type != '06'){
                $(_pageId + " article").addClass('high');
            }else{
                $(_pageId + " article").removeClass('high');
            }
        }else{
            if(scene_code == '3' && financial_prod_type != '06'){
                $(_pageId + " article").addClass('high');
            }else{
                $(_pageId + " article").removeClass('high');
            }
        }
        // if((scene_code == '3' && chooseVerson == '3' && financial_prod_type != '06') || (scene_code == '3' && !chooseVerson && !userChooseVerson && financial_prod_type != '06') || (!chooseVerson)){
        //     //高端用户
        //     // $(_pageId + " article").addClass('high');
        //     $(_pageId + " .high_warn").show();
        // }else{
        //     // 
        //     $(_pageId + " .high_warn").hide();
        // }
        // if((financial_prod_type != '06' && scene_code == '3' && chooseVerson == '3') || (scene_code == '3' && !chooseVerson && !userChooseVerson && financial_prod_type != '06')){
        //     $(_pageId + " article").addClass('high');
        // }else{
        //     $(_pageId + " article").removeClass('high');
        // }
        channelCode = common.getLocalStorage("download_channel_code");
        order_by = "costmoney";
        sort = '1';
        //非晋金财富无法转让
        if (channelCode != 'jjdx' && channelCode != '') {
            $(_pageId + " .pro_status").hide();
            $(_pageId + " #transferRule").hide();
        }

        if (!ut.getUserInf()) {
            $(_pageId + " #loginOut").text('登录');
            userLoginStatus = 0;
            userAuthenticationStatus = 0;

        } else {
            userLoginStatus = 1;
            userAuthenticationStatus = 0;
            let res = await getUserAuthenticationStatus()
            userAuthenticationStatus = res[0].state //获取用户认证状态
            if (res[0].sm_white_state == "1" || res[0].state == "1") { //白名单用户 || 合格投资人 展示详细数据
                // setData();
            } else {
                userLoginStatus = 1;
                userAuthenticationStatus = 0;
            }
        }

        var params = {
            financial_prod_type: "" // 分类类型
        }
        
        chooase_financial_prod_type = financial_prod_type;
        params.financial_prod_type = financial_prod_type;
        // params.financial_prod_type = "02";\
        
        let positionTypeTitle = {
            '02': '类固收',
            '03': '固收增强',
            '04': '权益投资',
            '06': '转让专区',
            '07': '攒钱策略',
        }
        //渲染页面名称
        let title = positionTypeTitle[financial_prod_type] ? positionTypeTitle[financial_prod_type] : ""
        $(_pageId + " #title").text(title);
        //页面埋点初始化
        tools.initPagePointData({pageName:title});
        var curLi = $(_pageId + ` #tab li[data-id=${params.financial_prod_type}]`);
        $(_pageId + " #tab li").each(function () {
            $(this).find("span").removeClass("active")
        })
        curLi.find("span").addClass("active");
        // 转让
        if (params.financial_prod_type == "06") {
            investorStatus();
            $(_pageId + " #v_container_productList").css("display", "block");
            $(_pageId + " #tab_content").css("display", "none")
            $(_pageId + " #transferRule").show();
            // getZrInfo(start, false);

        } else {
            // getProductDetail(params);
            setData(params)
            $(_pageId + " #tab_content").css("display", "block")
            $(_pageId + " #v_container_productList").css("display", "none");
            $(_pageId + " #transferRule").hide();
        }
        // if(scene_code != '3') renderCurLi(params.financial_prod_type);
        highRenderCurLi(params.financial_prod_type);
    }

    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    let res = data.results
                    resolve(res)
                } else {
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    function setData(params) {
        params.scene_code = chooseVerson ? chooseVerson : userChooseVerson ? userChooseVerson : scene_code;
        service.reqFun102118(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0].list; //一级列表
                var resultsTwo = data.results[1].data; //二级列表
                if (results && results instanceof Array && results.length) {
                    getProductList(results, resultsTwo);
                } else {
                    $(_pageId + " #content").html(`<p class="tips">即将上线，敬请期待</p>`);
                    $(_pageId + " #content").show();
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //获取recommend_type字段
    async function get_recommend_type() {
        return new Promise(async (resolve, reject) => {
            service.reqFun102105({ channel_code: channelCode }, (datas) => {
                if (datas.error_no == 0) {
                    let results = datas.results[0];
                    resolve(results)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }

    // 用户合格投资人弹窗状态(101037)
    function investorStatus() {
        service.reqFun101037({}, function (datas) {
            if (datas.error_no == 0) {
                var state = datas.results[0].state;
                //未确认弹窗
                if (state == 1) { // 已确认
                    $(_pageId + ".qualifiedInvestor").hide();
                    getZrInfo(start, false);
                } else {
                    $(_pageId + ".qualifiedInvestor").show()
                }
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }

    // 转让列表渲染
    async function getZrInfo(start, isAppendFlag) {
        let chooseData = {}
        if (channelCode && channelCode != 'yh_jjdx') chooseData = await get_recommend_type()
        if (!channelCode || channelCode == 'yh_jjdx') chooseData.list_recommend_type = '202'
        var param = {
            start: start,
            count: count,
            custLabelCnlCode: ut.getUserInf().custLabelCnlCode,
            recommend_type: chooseData.list_recommend_type ? chooseData.list_recommend_type : null
        };
        _cust_fund_type = '1';


        param.order_by = order_by
        param.sort = sort
        service.reqFun107006(param, function (datas) {
            let str = ''
            // datas.results.length = ''
            if (datas.error_no != 0 || !datas.results.length) {
                str = `<div class="tem_null_data flex_center">暂无转让产品</div>`
                $(_pageId + ' .finance_pro').show()
            }else {
                $(_pageId + ' .finance_pro').show()
                $(_pageId + ' .transfer_header').show()
                let productList = datas.results[0].data;
                for (var i = 0; i < productList.length; i++) {
                    let prod_sname = productList[i].prod_sname //名称
                    let rate = tools.fmoney(productList[i].rate) //业绩
                    let give_profit = tools.fmoney(productList[i].give_profit) //让出收益
                    let last_day = productList[i].last_day //转让金额
                    // let entrust_date = tools.ftime(productList[i].entrust_date) //转让日期
                    let costmoney = tools.fmoney(productList[i].costmoney) //本金
                    str +=
                        '<div contentType="1" operationType="1" operationId="pro_detail" operationName="购买" class="pro_detail item template_box flex">' +
                        '<em style="display: none">' + JSON.stringify(productList[i]) + '</em>' +
                        '<div style="display: none" class="productInfo">' + JSON.stringify(productList[i]) + '</div>' +
                        '<ul class="template_left m_list_color vertical_line">' +
                        '<li style="" class="title m_font_size16 m_bold m_text_darkgray666">' +
                        prod_sname +
                        '</li>' +
                        '<li style="width: 100% !important" class="m_font_size12 m_width_20rem flex wrap">' +
                        '<p class="">业绩计提基准(年化):<span class="m_text_red m_font_size18 text_gray">' + rate + '</span>%</p>' +
                        '</li>' +
                        '<li class="m_font_size12">' +
                        '<p>转让本金:<span>' + costmoney + '元</span></p>' +
                        '</li>' +
                        '<li class="m_font_size12">' +
                        '<p>剩余期限:<span>' + last_day + '天</span></p>' +
                        '</li>' +
                        '<li class="m_font_size12">' +
                        '<p>卖方让出收益:<span>' + give_profit + '</span>元</p>' +
                        '</li>' +
                        '</ul>' + (_cust_fund_type == 1 ? '<ul class="template_right  level_center vertical_center">' +
                            '<li class="btn vertical_center flex level_center">购买</li>' +
                            '</ul>' : '') +
                        '</div>'
                }
            }
            $(_pageId + " .finance_pro").html(str);
        })
    }

    //获取分类列表
    function getProductList(results, resultsTwo) {
        if (resultsTwo) {
            results.forEach((item, index) => {
                if (resultsTwo[item.zone_prod_type]) {
                    let list = resultsTwo[item.zone_prod_type];
                    // item.prod_list = resultsTwo[item.zone_prod_type];
                    item.prod_list = [];
                    item.prod_list_child = [];  //新版
                    list.map(items=>{
                        if(items.spread_buy && items.spread_buy == '1' && items.buy_state == '1'){
                            item.prod_list_child.push(items);
                        }else{
                            item.prod_list.push(items);
                        }
                    })
                    
                }
            })
        }
        var prodList = JSON.parse(JSON.stringify(results));
        renderPage(prodList);
    }

    function renderPage(data) {
        var html = "";
        let topLevel //按钮状态
        if (userLoginStatus == 0){
            if(userChooseVerson && userChooseVerson !=''){
                topLevel =  userChooseVerson == '3' ? `登录<br>可见` : '登录可见'
            }else{
                topLevel =  scene_code == '3' ? `登录<br>可见` : '登录可见'
            }
        } 
        if (userLoginStatus == 1 && userAuthenticationStatus == 0){
            if(userChooseVerson && userChooseVerson !=''){
                topLevel =  userChooseVerson == '3' ? `认证<br>可见` : '认证可见'
            }else{
                topLevel =  scene_code == '3' ? `认证<br>可见` : '认证可见'
            }
        } 
        if (data && data.length) {
            data.forEach((item) => {
                var subHtml = "";   //老版
                var subNewHtml = ""; //新版
                //新版处理逻辑
                if((!item.prod_list || !item.prod_list.length) && (!item.prod_list_child || !item.prod_list_child.length)){
                    subHtml += '';
                    subNewHtml += '';
                }else{
                    subHtml = setNewData(item.prod_list,topLevel);
                    subNewHtml = setNewData(item.prod_list_child,topLevel);
                    if(subNewHtml && subNewHtml.length && item.prod_list_child.length && item.prod_list_child.length > 1){
                        // subNewHtml = subNewHtml + `<div class="userIndexBtn" financial_prod_type="${item.financial_prod_type}" zone_prod_type="${item.zone_prod_type}" spread_buy="1">一键分散买 ></div>`
                    }
                }
                if(!subHtml && !subNewHtml){
                    html += ``
                }else{
                    html += `   
                <div class="classify ${(item.is_show_title && item.is_show_title != '0') ? '':'high_noPaddingTop'}">
                    ${(item.is_show_title && item.is_show_title != '0') ? `<div class="list-zoom">
                    <p>${item.zone_name} ${(item.zone_desc ? `<span>|</span> <span>${item.zone_desc}</span>` : '')}  </p>
                    <span operationType="1" operationId="showImg" operationName="${item.img_url}" class="showImg indexPageRightIcon main_flxe" data-img='${item.img_url}' data-name='${item.zone_name}'>
                       ${item.img_url ? '<i></i>' : ''} 
                    </span>
                </div>` : ``}
                        <div class="sub-classify">
                            <ul class="prod-list ${subNewHtml && subHtml && item.prod_list_child.length && item.prod_list_child.length == 1 ? 'moreList_child_one' : ''}">
                                ${subNewHtml}
                            </ul>
                            <ul class="prod-list">
                                ${subHtml}
                            </ul>
                        </div>
                    </div>`
                }
            })
            if (!html) {
                $(_pageId + " #content").html(`<p class="tips">敬请期待</p>`);
            }
            $(_pageId + " #content").html(html);
            $(_pageId + " #content").show();
        }
    }
    function setNewData(list,topLevel){
        let subHtml = '';
        if(!list || !list.length) return '';
        list.forEach(prod => {
            var _prod_propagate_temp = prod.prod_propagate_temp;
            prod = tools.FormatNull(prod);
            prod._prod_propagate_temp = _prod_propagate_temp;
            let prod_sname = prod.prod_name_list ? prod.prod_name_list : prod.prod_sname ? prod.prod_sname : prod.prod_exclusive_name;
            let this_year_rate = tools.fmoney(prod.this_year_rate ? prod.this_year_rate : prod.annu_yield)
            let str = "";
            let transferable = prod.transferable;//是否可转让
            let recommend_info = prod.recommend_info;
            let recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + prod.url + "' style='width:0.32rem;margin-left: 0.04rem;margin-top: -0.04rem;height: 0.16rem;'>"
            }
            if (prod.prod_sub_type2 == '100') {
                //私募列表展示
                let found_rate = prod.found_rate ? prod.found_rate : '--' //成立以来收益
                let preincomerate = tools.fmoney(prod.preincomerate) //年化标准
                let threshold_amount = prod.threshold_amount / 10000 //起购金额
                let inrest_term = prod.inrest_term   //封闭期/锁定期
                let nav = tools.fmoney(prod.nav, 4)
                let interest_rate = tools.fmoney(prod.interest_rate_min) + "%" + "-" + tools.fmoney(prod.interest_rate_max) + "%";
                let dk_income_rate = prod.dk_income_rate ? tools.fmoney(prod.dk_income_rate) : '--'
                var dk_income_rate_chg = prod.dk_income_rate_chg ? tools.fmoney(prod.dk_income_rate_chg) : '--'
                var risklevel_name = (prod.risklevel_name && prod.risklevel_name.split('(')) ? prod.risklevel_name.split("(")[0] : '--'
                //产品整合 是否展示
                let compare_benchmark_list = prod.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                let fund_rate_list = prod.fund_rate_list == '1' ? '' : 'display_none' //是否展示成立以来收益
                let closed_period_list = prod.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                let lock_period_list = prod.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                let this_year_rate_list = prod.this_year_rate_list == '1' ? '' : 'display_none' //是否展示今年以来收益
                let nav_list = prod.nav_list == '1' ? '' : 'display_none' //是否展示净值
                let recommend_info_list = prod.recommend_info_list == '1' ? '' : 'display_none'
                let threshold_amount_list = prod.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                let stage_yield_list = prod.stage_yield_list == '1' ? '' : 'display_none' // 是否展示阶段收益率
                let performance_benchmarks_list = prod.performance_benchmarks_list == '1' ? '' : 'display_none' // 是否展示阶段收益率
                let income_period_list = (prod.income_period_list == '1' && prod.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                let income_period_list_chg = (prod.income_period_list == '1' && prod.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                let income_period_type_desc = prod.income_period_type_desc ? prod.income_period_type_desc : '--' //近多少年化
                subHtml += `
                <li class="item" buy_state="${prod.buy_state}">
                    <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                    <div class="${scene_code == '3' ? 'display_none' : ''}">
                        <p class="m_font_size16 color_000">${prod_sname}${str}</p>
                        <div class="annual m_font_size12 ${compare_benchmark_list}"  style="font-size: 12px;">业绩计提基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : preincomerate}</span>%</div>
                        <div class="annual m_font_size12 ${performance_benchmarks_list}"  style="font-size: 12px;">业绩比较基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : preincomerate}</span>%</div>
                        <div class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${topLevel ? '--' : dk_income_rate}</span>%</div>
                        <div class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${topLevel ? '--' : dk_income_rate_chg}</span>%</div>
                        <p class="m_font_size12 ${stage_yield_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : interest_rate}</span></p>
                        <div class="annual m_font_size12 ${fund_rate_list}"  style="font-size: 12px;">成立以来收益:<span class="m_text_red m_font_size18">${topLevel ? '--' : tools.fmoney(found_rate)}</span>%</div>
                        <div class="purchase m_text_999" style="font-size: 12px;">                 
                            <span class="${threshold_amount_list}">起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span class="${closed_period_list}">期限:${topLevel ? '--' : inrest_term}</span>
                            <span class="${lock_period_list}">锁定期:${topLevel ? '--' : inrest_term}</span>
                            <span class="${this_year_rate_list}">今年来收益:${topLevel ? '--' : this_year_rate ? tools.fmoney(this_year_rate) : "--"}</span>
                            <span class="${nav_list}">最新净值:${topLevel ? '--' : nav}</span>
                        </div>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </div>
                    <div class="main_flxe vertical_line flex_1 ${scene_code == '3' ? '' : 'display_none'}">
                        <p class="m_font_size16 color_000">${prod_sname}${str}</p>
                        <p class="m_font_size12  ${scene_code == '3' ? 'flex' : 'display_none'}">
                            <span class="high_fixed_width main_flxe vertical_line ${compare_benchmark_list}"> 
                                <span class="high_color m_font_size18">${topLevel ? '--' : preincomerate}%</span>
                                <span>业绩计提基准(年化)</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${stage_yield_list}"> 
                                <span class="high_color m_font_size18">${topLevel ? '--' : interest_rate}</span>
                                <span>业绩计提基准(年化)</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${performance_benchmarks_list}"> 
                                <span class="high_color m_font_size18">${topLevel ? '--' : preincomerate}%</span>
                                <span>业绩比较基准(年化)</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${fund_rate_list}"> 
                                <span class="high_color m_font_size18">${topLevel ? '--' : tools.fmoney(found_rate)}%</span>
                                <span>成立以来收益</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                <span class="high_color m_font_size18">${topLevel ? '--' : dk_income_rate}%</span>
                                <span>${income_period_type_desc}年化</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                <span class="high_color m_font_size18">${topLevel ? '--' : dk_income_rate_chg}%</span>
                                <span>${income_period_type_desc}</span>
                            </span>
                            <span class="main_flxe vertical_line flex_1">
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${topLevel ? '--' : inrest_term}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${topLevel ? '--' : inrest_term}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${this_year_rate_list}" style="height:32px;line-height:32px">今年来收益:${topLevel ? '--' : this_year_rate ? tools.fmoney(this_year_rate) : "--"}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${nav_list}" style="height:32px;line-height:32px">最新净值:${topLevel ? '--' : nav}</span>
                                <span><span>${risklevel_name}</span> | <span class="${threshold_amount_list}">起购:${topLevel ? '--' : threshold_amount}万元</span></span>
                            </span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </div>
                    <div class="action-btn ${topLevel ? 'm_golden' : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                        ${topLevel ? topLevel : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                    </div>
                </li>`
            } else if (prod.prod_sub_type2 == '200') {
                let compare_benchmark_list = prod.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                let nav_list = prod.nav_list == '1' ? '' : 'display_none' //是否展示净值
                let income_period_list = (prod.income_period_list == '1' && prod.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                let income_period_list_chg = (prod.income_period_list == '1' && prod.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                let per_yield_list = prod.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                let threshold_amount_list = prod.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                let closed_period_list = prod.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                let recommended_holding_list = prod.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                let lock_period_list = prod.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                let recommend_info_list = prod.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                let performance_benchmarks_list = prod.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                var risklevel_name = (prod.risklevel_name && prod.risklevel_name.split('(')) ? prod.risklevel_name.split("(")[0] : '--'
                //数据展示
                let preincomerate = prod.preincomerate ? tools.fmoney(prod.preincomerate) : '--' //年化标准
                let threshold_amount = prod.threshold_amount ? prod.threshold_amount : '--' //起购金额
                let inrest_term = prod.inrest_term ? prod.inrest_term : '--'   //封闭期/锁定期
                let income_period_type_desc = prod.income_period_type_desc ? prod.income_period_type_desc : '--' //近多少年化
                let dk_income_rate = prod.dk_income_rate ? tools.fmoney(prod.dk_income_rate) : '--'
                var dk_income_rate_chg = prod.dk_income_rate_chg ? tools.fmoney(prod.dk_income_rate_chg) : '--'
                let nav = prod.nav ? tools.fmoney(prod.nav, 4) : '--'
                let holding_days = prod.holding_days ? prod.holding_days : '--'
                let per_yield = prod.per_yield ? tools.fmoney(prod.per_yield) : '--'
                let buy_state = prod.buy_state
                let buy_state_name, btnClass
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                    if(userChooseVerson && userChooseVerson !=''){
                        buy_state_name =  userChooseVerson == '3' ? `敬请<br>期待` : '敬请期待'
                    }else{
                        buy_state_name =  scene_code == '3' ? `敬请<br>期待` : '敬请期待'
                    }
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                subHtml += `
                <li class="item" buy_state="${prod.buy_state}">
                    <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                    <div class="${scene_code == '3' ? 'display_none' : ''}">
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        <div class="annual m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</div>
                        <div class="annual m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</div>
                        <div class="annual m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></div>
                        <div class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</div>
                        <div class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</div>
                        <div class="annual m_font_size12 ${per_yield_list}" >上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</div>
                        <div class="purchase m_text_999" style="font-size: 12px;">
                            <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                            <span class="${closed_period_list}">期限:${inrest_term}</span>
                            <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                            <span class="${recommended_holding_list}">建议持有${holding_days}天以上</span>
                        </div>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </div>
                    <div class="main_flxe vertical_line flex_1 ${scene_code == '3' ? '' : 'display_none'}">
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                            <p class="m_font_size12  ${scene_code == '3' ? 'flex' : 'display_none'}">
                                <span class="high_fixed_width main_flxe vertical_line ${compare_benchmark_list}"> 
                                    <span class="high_color m_font_size18">${preincomerate}%</span>
                                    <span>业绩计提基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${performance_benchmarks_list}"> 
                                    <span class="high_color m_font_size18">${preincomerate}%</span>
                                    <span>业绩比较基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${nav_list}"> 
                                    <span class="high_color m_font_size18">${nav}</span>
                                    <span>单位净值</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                    <span class="high_color m_font_size18">${dk_income_rate}%</span>
                                    <span>${income_period_type_desc}年化</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                    <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                                    <span>${income_period_type_desc}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${per_yield_list}"> 
                                    <span class="high_color m_font_size18">${per_yield}%</span>
                                    <span>上一封闭期年化收益率</span>
                                </span>
                                <span class="main_flxe vertical_line flex_1">
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">建议持有${holding_days}天以上</span>
                                    <span><span>${risklevel_name}</span> | <span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                                </span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </div>
                    <div class="action-btn ${btnClass}">
                        ${buy_state_name}
                    </div>
                </li>`
            } else if (prod.prod_sub_type2 == "95") { //政金债
                let threshold_amount = prod.threshold_amount ? prod.threshold_amount / 10000 : '--' //起购金额
                let inrest_term = prod.inrest_term ? prod.inrest_term : '--'   //封闭期/锁定期
                let recommend_info_list = prod.recommend_info_list == '1' ? '' : 'display_none';
                subHtml += `
                <li class="item" buy_state="${prod.buy_state}">
                    <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                    <div>
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        <div class="annual m_font_size12" >业绩计提基准(年化):<span class="m_text_red m_font_size18 ${tools.addMinusClass(prod.preincomerate)}">${topLevel ? '--' : tools.fmoney(prod.preincomerate)}</span>%</div>
                        <div class="purchase m_text_999 m_font_size12">
                            <span>起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span>期限:${topLevel ? '--' : inrest_term}</span>
                        </div>
                        <p class="m_font_size12 m_golden ${recommend_info_list}">${recommend_info}</p>
                    </div>
                    <div class="action-btn ${topLevel ? 'm_golden' : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                        ${topLevel ? topLevel : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                    </div>
                </li>`
            } else if (prod.prod_sub_type2 == "94") { // 持有期
                let recommend_info_list = prod.recommend_info_list == '1' ? '' : 'display_none'
                let threshold_amount = prod.threshold_amount ? prod.threshold_amount / 10000 : '--' //起购金额
                let inrest_term = prod.inrest_term ? prod.inrest_term : '--'   //封闭期/锁定期
                subHtml += `
                <li class="item" buy_state="${prod.buy_state}">
                    <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                    <div>
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        <div class="annual m_font_size12" >业绩计提基准(年化):<span class="m_text_red m_font_size18 ${tools.addMinusClass(prod.preincomerate)}">${topLevel ? '--' : tools.fmoney(prod.preincomerate)}</span>%</div>
                        <div class="purchase m_text_999 m_font_size12">
                            <span>起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span>锁定期:${topLevel ? '--' : inrest_term}</span>
                        </div>
                        <p class="m_font_size12 m_golden ${recommend_info_list}">${recommend_info}</p>
                    </div>
                    <div class="action-btn ${topLevel ? 'm_golden' : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                        ${topLevel ? topLevel : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                    </div>
                </li>`
            } else if (prod.prod_source == '2'){    //投顾产品特殊处理
                var productInfoSon = JSON.stringify(prod) //二级列表接口传递数据
                var prod_per_min_amt = prod.prod_per_min_amt //投资金额
                var comb_sname = prod.comb_sname;
                var annual_income_list = prod.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                var recommended_holding_list = prod.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var threshold_amount_list = prod.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var characteristic_list = prod.characteristic_list == '1' ? '' : 'display_none'  //是否展示特点
                var corner_marker_list = prod.corner_marker_list == '1' ? '' : 'display_none'  //是否展示角标
                var income_period_list = (prod.income_period_list == '1' && prod.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (prod.income_period_list == '1' && prod.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var strategic_characteristic_list = prod.strategic_characteristic_list == '1' ? '' : 'display_none'  //是否展示策略特点
                var page_threshold_amount_list = prod.page_threshold_amount_list == '1' ? '' : 'display_none'; //是否展示营销页首投金额
                var buy_deadline_list = prod.buy_deadline_list == '1' ? '' : 'display_none';
                var comb_risk_name = (prod.comb_risk_name && prod.comb_risk_name.split('(')) ? prod.comb_risk_name.split("(")[0] : '--'
                //数据展示
                var page_first_per_min = prod.page_first_per_min ? prod.page_first_per_min : '';
                var income_period_type_desc = prod.income_period_type ? prod.income_period_type : '--' //近多少年化
                var annual_income = prod.annual_income; // 目标年化收益
                var holding_time = prod.holding_time ? prod.holding_time : '--' // 建议持有时长
                var characteristic = prod.characteristic; // 特点
                var strategic_characteristic = prod.strategic_characteristic; // 策略特点   
                var threshold_amount = prod.first_per_min ? prod.first_per_min : '--' //起购金额
                var corner_marker = prod.corner_marker; // 角标描述
                var mechanism = prod.mechanism; // 投顾机构
                var income_name = prod.income_name ? prod.income_name : ''; //目标收益率文案
                var establish_rate = prod.establish_rate ? tools.fmoney(prod.establish_rate) : '--'
                var buy_state = prod.purchase_state;
                var buy_state_name, btnClass;
                var income_rate_chg = prod.income_rate_chg ? tools.fmoney(prod.income_rate_chg) : '--' // 涨跌幅
                var holding_time_name = prod.holding_time_name ? prod.holding_time_name : '';
                var buy_deadline = prod.buy_deadline ? prod.buy_deadline : '';
                // threshold_amount = page_threshold_amount_list == '1' ? page_first_per_min : threshold_amount;
                if(buy_deadline){
                    buy_deadline = tools.ftime(buy_deadline.substr(4, 4), "月") + "日 " + tools.ftime(buy_deadline.substr(8, 8), ".").substr(0, 5)
                }
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                    if(userChooseVerson && userChooseVerson !=''){
                        buy_state_name =  userChooseVerson == '3' ? `敬请<br>期待` : '敬请期待'
                    }else{
                        buy_state_name =  scene_code == '3' ? `敬请<br>期待` : '敬请期待'
                    }
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "运作中";
                    btnClass = "sold_out";
                }
                //投顾列表展示
                subHtml += `
                    <li class="item" buy_state="${prod.buy_state}">
                        <div class="main_flxe vertical_line ${scene_code == '3' ? 'display_none' : ''}" >
                            <em style='display: none' class='productInfo'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000">${comb_sname}</p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化：<span class="m_text_red m_font_size18">${establish_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}：<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${annual_income_list}">${income_name}：<span class="m_text_red m_font_size18">${annual_income}</span></p>
                            <p class="m_font_size12 ${buy_deadline_list}">截止时间：<span class="m_text_red m_font_size14">${buy_deadline}</span></p>
                            <p class="m_font_size12 ${strategic_characteristic_list}">策略特点：<span class="m_text_red">${strategic_characteristic}</span></p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${page_threshold_amount_list}">起购：${page_first_per_min}元</span>
                                <span class="${threshold_amount_list}">起购：${threshold_amount}元</span>
                                <span class="${recommended_holding_list}">${holding_time_name}：${holding_time}</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                                <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </div>
                        <div class="main_flxe vertical_line flex_1 ${scene_code == '3' ? '' : 'display_none'}">
                            <p class="m_font_size16 color_000 prod_sname">${comb_sname}</p>
                            <p class="m_font_size12  ${scene_code == '3' ? 'flex' : 'display_none'}">
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                    <span class="high_color m_font_size18">${establish_rate}%</span>
                                    <span>${income_period_type_desc}年化</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                    <span class="high_color m_font_size18">${income_rate_chg}%</span>
                                    <span>${income_period_type_desc}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${annual_income_list}"> 
                                    <span class="high_color m_font_size18">${annual_income}</span>
                                    <span>${income_name}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${buy_deadline_list}"> 
                                    <span class="high_color m_font_size18">${buy_deadline}</span>
                                    <span>截止时间</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${strategic_characteristic_list}"> 
                                    <span class="high_color m_font_size18">${strategic_characteristic}</span>
                                    <span>策略特点</span>
                                </span>
                                <span class="main_flxe vertical_line flex_1">
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">${holding_time_name}:${holding_time}</span>
                                    <span>${comb_risk_name} | <span class="${page_threshold_amount_list}">起购:${page_first_per_min}元</span><span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                                </span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                                <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </div>
                        <div class="action-btn ${btnClass}">
                            ${buy_state_name}
                        </div>
                    </li>
                `
            } else if (prod.prod_source == '3'){ //系列产品
                var income_rate_chg = prod.income_rate_chg ? tools.fmoney(prod.income_rate_chg) : '--' // 涨跌幅
                var establish_rate = prod.establish_rate ? tools.fmoney(prod.establish_rate) : '--'
                var compare_benchmark_list = prod.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = prod.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (prod.income_period_list == '1' && prod.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (prod.income_period_list == '1' && prod.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var per_yield_list = prod.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                // var income_period_list_chg = prod.income_period_list_chg == '1' ? '' : 'display_none' //是否展示近X月涨跌幅
                var threshold_amount_list = prod.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var closed_period_list = prod.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var recommended_holding_list = prod.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var lock_period_list = prod.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var recommend_info_list = prod.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var performance_benchmarks_list = prod.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                //系列投顾产品
                var strategic_characteristic_list = prod.strategic_characteristic_list == '1' ? '' : 'display_none' //是否展示策略特点值
                var characteristic_list = prod.characteristic_list == '1' ? '' : 'display_none';//是否展示特点
                var annual_income_list = prod.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                //数据展示
                var preincomerate = prod.preincomerate ? tools.fmoney(prod.preincomerate) : '--' //年化标准
                var threshold_amount = prod.threshold_amt ? prod.threshold_amt : '--' //起购金额
                var inrest_term = prod.inrest_term ? prod.inrest_term : '--'   //封闭期/锁定期
                var income_period_type_desc = prod.income_period_type_desc ? prod.income_period_type_desc : '--' //近多少年化
                var dk_income_rate = prod.dk_income_rate ? tools.fmoney(prod.dk_income_rate) : '--'
                var dk_income_rate_chg = prod.dk_income_rate_chg ? tools.fmoney(prod.dk_income_rate_chg) : '--'
                var nav = prod.nav ? tools.fmoney(prod.nav, 4) : '--'
                var holding_days = prod.holding_days ? prod.holding_days : '--'
                var per_yield = prod.per_yield ? tools.fmoney(prod.per_yield) : '--'
                var buy_state = prod.buy_state
                var buy_state_name, btnClass
                //系列投顾产品
                var holding_time = prod.holding_time;
                var characteristic = prod.characteristic
                var strategic_characteristic = prod.strategic_characteristic //策略特点值
                var holding_time_name = prod.holding_time_name //持有时长指标名称
                var income_name = prod.income_name ? prod.income_name : ''; //目标收益率文案
                var annual_income = prod.annual_income; // 目标年化收益
                let risklevel_name = prod.risklevel_name ? prod.risklevel_name : '';
                //去掉列表前两个 其中去掉 系列投顾
                let income_name_str,strategic_characteristic_str,new_pro_str;
                let rate_desc = prod.rate_desc ? prod.rate_desc : '';
                if(scene_code == '3'){
                    income_name_str = (prod.series_type == '2' || prod.series_type == '3') ? `
                        <span class="high_fixed_width main_flxe vertical_line ${annual_income_list}"> 
                            <span class="high_color m_font_size18">${annual_income}</span>
                            <span>${income_name}</span>
                        </span>
                    `:``;
                    strategic_characteristic_str = (prod.series_type == '2' || prod.series_type == '3') ? `
                        <span class="high_fixed_width main_flxe vertical_line ${strategic_characteristic_list}"> 
                            <span class="high_color m_font_size18">${strategic_characteristic}</span>
                            <span>策略特点</span>
                        </span>
                    `:``;
                    new_pro_str = (prod.series_type == '2' || prod.series_type == '3') ? `
                        <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                            <span class="high_color m_font_size18">${prod.series_type == '2' ? (dk_income_rate + '%'):rate_desc}</span>
                            <span>${income_period_type_desc}年化</span>
                        </span>
                        <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                            <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                            <span>${income_period_type_desc}</span>
                        </span>
                    ` : `
                        <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                            <span class="high_color m_font_size18">${dk_income_rate}%</span>
                            <span>其中${prod.income_prod_name}${income_period_type_desc}年化</span>
                        </span>
                        <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                            <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                            <span>其中${prod.income_prod_name}${income_period_type_desc}</span>
                        </span>
                    `
                }else{
                    income_name_str = (prod.series_type == '2' || prod.series_type == '3') ?`<p class="m_font_size12 ${annual_income_list}">${income_name}:<span class="m_text_red m_font_size18">${annual_income}</span></p>` : ``;
                    strategic_characteristic_str = (prod.series_type == '2' || prod.series_type == '3') ?`<p class="m_font_size12 ${strategic_characteristic_list}">策略特点:<span class="m_text_red">${strategic_characteristic}</span></p>`: ``;
                    new_pro_str = (prod.series_type == '2' || prod.series_type == '3') ? `<p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${prod.series_type == '2' ? (dk_income_rate + '%'):rate_desc}</span></p>
                        <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>` : `<p class="m_font_size12 ${income_period_list}">其中${prod.income_prod_name}${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                        <p class="m_font_size12 ${income_period_list_chg}">其中${prod.income_prod_name}${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>`
                }
                let characteristic_str = (prod.series_type == '2' || prod.series_type == '3') ? `<p class="m_font_size12 m_text_darkgray666 ${characteristic_list}"><span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span></p>`: '';
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if(prod.series_type == '3'){
                    buy_state_name = '去看看';
                    risklevel_name = prod.risklevel_name_customize;
                } 
                if(prod.series_type == '2'){
                    risklevel_name = prod.comb_risk_name;
                } 
                // subHtml += `
                // <ul class="classificationList_card_main flex">
                //     <li class="main_flxe vertical_line">
                //         <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                //         <p class="m_font_size16 color_000">${prod_sname}</p>
                //         <p class="m_text_999 m_font_size12">
                //             <p class="m_font_size12 ${income_period_list}">其中${prod.income_prod_name}${income_period_type_desc}年化：<span class="m_text_red m_font_size18">${establish_rate}</span>%</p>
                //             <p class="m_font_size12 ${income_period_list_chg}">其中${prod.income_prod_name}${income_period_type_desc}：<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</p>
                //             <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                //             <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>
                //             <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                //         </p>
                //         <p class="m_text_999 m_font_size12">
                //             <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                //             <span class="${closed_period_list}">期限:${inrest_term}</span>
                //             <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                //             <span class="${recommended_holding_list}">建议持有${holding_days}天以上</span>
                //         </p>
                //         <p class="m_font_size12 m_golden ${recommend_info_list}">${recommend_info}</p>
                //     </li>
                //     <div class="action-btn ${btnClass}">
                //         ${buy_state_name}
                //     </div>
                // </ul>
                // `
                subHtml += `
                <li class="item" buy_state="${prod.buy_state}">
                    <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                    <div class="${scene_code == '3' ? 'display_none' : ''}">
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        ${new_pro_str}
                        ${income_name_str}
                        <div class="annual m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</div>
                        <div class="annual m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</div>
                        <div class="annual m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></div>
                        <div class="annual m_font_size12 ${per_yield_list}" >上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</div>
                        ${strategic_characteristic_str}
                        <div class="purchase m_text_999" style="font-size: 12px;">
                            <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                            <span class="${closed_period_list}">期限:${inrest_term}</span>
                            <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                            <span class="${recommended_holding_list}">${prod.series_type == '2' ? holding_time_name : '建议持有'}${holding_time}</span>
                        </div>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                        ${characteristic_str}
                    </div>
                    <div class="main_flxe vertical_line flex_1 ${scene_code == '3' ? '' : 'display_none'}">
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_font_size12  ${scene_code == '3' ? 'flex' : 'display_none'}">
                            ${new_pro_str}
                            ${income_name_str}
                            ${strategic_characteristic_str}
                            <span class="high_fixed_width main_flxe vertical_line ${strategic_characteristic_list}"> 
                                <span class="high_color m_font_size18">${strategic_characteristic}</span>
                                <span>策略特点</span>
                            </span>
                            <span class="main_flxe vertical_line flex_1">
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${inrest_term}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${inrest_term}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">${(prod.series_type == '2' || prod.series_type == '3') ? holding_time_name : '建议持有'}${holding_time}</span>
                                <span>${risklevel_name} | <span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                            </span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                        ${characteristic_str}
                    </div>
                    <div class="action-btn ${btnClass}">
                        ${buy_state_name}
                    </div>
                </li>
                `
            } else if (!prod.exclusive_product_type) {
                let threshold_amount = prod.threshold_amount ? prod.threshold_amount / 10000 : '--' //起购金额
                let inrest_term = prod.inrest_term ? prod.inrest_term : '--'   //封闭期/锁定期
                let recommend_info_list = prod.recommend_info_list == '1' ? '' : 'display_none'
                subHtml += `
                <li class="item" buy_state="${prod.buy_state}">
                    <span style='display: none' class='productInfo'>${JSON.stringify(prod)}</span>
                    <div>
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        <div class="annual m_font_size12" >业绩计提基准(年化):<span class="m_text_red m_font_size18 ${tools.addMinusClass(prod.preincomerate)}">${topLevel ? '--' : tools.fmoney(prod.preincomerate)}</span>%</div>
                        <div class="purchase m_text_999 m_font_size12">
                            <span>起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span>封闭期:${topLevel ? '--' : inrest_term}</span>
                        </div>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </div>
                    <div class="action-btn ${topLevel ? 'm_golden' : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                        ${topLevel ? topLevel : tools.priBtnObj(prod.buy_state, prod.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                    </div>
                </li>`
            }
        })
        return subHtml;
    }
    //投顾产品购买
    function pageTo_advisor(info){
        // var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
        // var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        appUtils.setSStorageInfo("combProductInfo", info);   //存储分类一级内容
        if (info.prod_propagate_temp && info.prod_propagate_temp != '--') {
            tools.setPageToUrl('combProduct/combProdMarketing', '1','login/listMorePage')
        } else {
            tools.setPageToUrl('combProduct/combProdDetail', '1','login/listMorePage')
        }
        if (!common.loginInter()) return;
        if (!ut.hasBindCard(_page_code)) return;
        //校验用户是否上传过身份证照片
        if(!ut.getUploadStatus()){
            let operationId = 'noUploadIdCard'
            appUtils.setSStorageInfo("noUploadIdCard", '1');
            return layerUtils.iConfirm("您还未上传身份证照片", function () {
                appUtils.pageInit(_page_code, "account/uploadIDCard", {});
            }, function () {
            }, "去上传", "取消",operationId);
        }
        var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
        var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        let operationId = 'riskAssessment';
        common.changeCardInter(function () {
            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                layerUtils.iConfirm("您还未进行风险测评", function () {
                    appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                }, function () {
                }, "去测评", "取消",operationId);
                return;
            } else if (invalidFlag == '1') {
                pageTo_evaluation()
                return
            }
            //到期3个月后提示
            if (perfect_info == 4) {
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换");
            }
            appUtils.setSStorageInfo("fund_code", info.comb_code);

            if (info.prod_propagate_temp && info.prod_propagate_temp != '--') {
                appUtils.pageInit(_page_code, "combProduct/combProdMarketing");
            } else {
                appUtils.pageInit(_page_code, "combProduct/combProdDetail");
            }
        });
    }
    /*
    多基金购买页
    * */
    function pageTo_fundsBuy(info) {
        localStorage.financial_prod_type_pro = info.financial_prod_type;
        localStorage.zone_prod_type_pro = '';
        localStorage.series_id = info.fund_code;
        localStorage.templateId_allBuy = info.prod_propagate_temp;
        // appUtils.setSStorageInfo("financial_prod_type_pro", financial_prod_type);
        // appUtils.setSStorageInfo("zone_prod_type_pro", ''); //取消二级页面缓存参数
        
        //判断是否有营销页
        // let data = {
        //     code:financial_prod_type
        // }
        // service.reqFun199016(data, (datas) => {
        //     if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
        //     let id = datas.results[0].val;
            
        // })
        if(info.prod_propagate_temp && info.prod_propagate_temp != 'undefined' && info.prod_propagate_temp != '--'){ //已配营销页面
            appUtils.setSStorageInfo("series_name", info.series_name);
            appUtils.setSStorageInfo("series_info", info);
            // appUtils.pageInit(_page_code, "template/decentralizedPurchasing");
            if(info.series_type == '2'){
                //特殊系列产品 子女
                tools.setPageToUrl('template/seriesChildrenMarketing', '1');
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    } else if (invalidFlag == '1') {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                        }, "取消", "更换");
                    }
                    appUtils.pageInit(_page_code, "template/seriesChildrenMarketing");
                });
                
            }else{
                appUtils.pageInit(_page_code, "template/decentralizedPurchasing");
            }
        }else{  //未配置营销页面
            tools.setPageToUrl('fundSupermarket/fundsBuy', '1');
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if (perfect_info == 4) {
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换");
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消");
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                appUtils.pageInit(_page_code, "fundSupermarket/fundsBuy");
            });
        }
    }
    //绑定事件
    function bindPageEvent() {
        // 一键分散购买
        appUtils.preBindEvent($(_pageId + " .content"), ".userIndexBtn", function (e) {
            let financial_prod_type = $(this).attr("financial_prod_type");
            let zone_prod_type = $(this).attr("zone_prod_type");
            // appUtils.setSStorageInfo("fund_code", "GSZQZH");
            localStorage.zone_prod_type_pro = zone_prod_type;
            localStorage.financial_prod_type_pro = financial_prod_type
            // appUtils.setSStorageInfo("financial_prod_type_pro", c);
            // appUtils.setSStorageInfo("zone_prod_type_pro", zone_prod_type);
            //判断是否有营销页
            let data = {
                code:financial_prod_type + (zone_prod_type ? zone_prod_type : '')
            }
            // service.reqFun199016(data, (datas) => {
                
            // })
            // if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            // let id = datas.results[0].val;
            if(id && id != 'undefined'){ //已配营销页面
                appUtils.pageInit(_page_code, "template/decentralizedPurchasing");
            }else{  //未配置营销页面
                tools.setPageToUrl('fundSupermarket/fundsBuy', '1');
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                //到期3个月后提示
                if (perfect_info == 4) {
                    return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, "取消", "更换");
                }
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    } else if (invalidFlag == '1') {
                        pageTo_evaluation()
                        return
                    }
                    appUtils.pageInit(_page_code, "fundSupermarket/fundsBuy");
                });
            }
            // appUtils.pageInit(_page_code, "template/decentralizedPurchasing");
            // tools.setPageToUrl('template/decentralizedPurchasing', '1');
            // if (!common.loginInter()) return;
            // if (!ut.hasBindCard(_page_code)) return;
            // var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            // var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            // //到期3个月后提示
            // if (perfect_info == 4) {
            //     return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
            //         appUtils.pageInit(_page_code, "account/uploadIDCard", {});
            //     }, "取消", "更换");
            // }
            // common.changeCardInter(function () {
            //     if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
            //         layerUtils.iConfirm("您还未进行风险测评", function () {
            //             appUtils.pageInit(_page_code, "safety/riskQuestion", {});
            //         }, function () {
            //         }, "去测评", "取消");
            //         return;
            //     } else if (invalidFlag == '1') {
            //         pageTo_evaluation()
            //         return
            //     }
                
            // });
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 点击tab
        appUtils.bindEvent($(_pageId + " #tab li"), function () {
            var curLi = $(this);
            var curId = curLi.attr("data-id");
            if (curId == '06') {  //转让登录鉴权特殊处理
                tools.setPageToUrl('login/listMorePage', '2');
                appUtils.setSStorageInfo("productType", '06') //存储类型
                if (!common.loginInter()) return;
            }
            chooase_financial_prod_type = curId;
            if(scene_code != '3') renderCurLi(curId);
            $(_pageId + " #tab li").each(function () {
                $(this).find("span").removeClass("active")
            })
            curLi.find("span").addClass("active");
            appUtils.setSStorageInfo("productType", curId) //存储类型
            if (curId == "06") {
                $(_pageId + " #v_container_productList").css("display", "block");
                $(_pageId + " #tab_content").css("display", "none")
                $(_pageId + " #transferRule").show();
                if (!common.loginInter()) return;
                getZrInfo(start, false);
                investorStatus();
            } else {
                var params = {
                    financial_prod_type: curLi.attr("data-id") // 分类类型
                }
                $(_pageId + " #v_container_productList").css("display", "none");
                $(_pageId + " #tab_content").css("display", "block")
                $(_pageId + " #transferRule").hide();
                // getProductDetail(params);
                setData(params)
            }

        });

        //列表进详情页
        appUtils.preBindEvent($(_pageId + " #content"), ".item", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
            tools.recordEventData('1','details','购买',{fundCode:productInfo.fund_code});
            if(productInfo.series_type == '3'){
                //缓存当前系列info
                appUtils.setSStorageInfo("series_info_high", productInfo);
                return appUtils.pageInit(_page_code, "highVersion/custProducts", {});
            }
            if(productInfo.prod_source == '3'){ //一键分散买
                return pageTo_fundsBuy(productInfo);
            }
            if(productInfo.prod_source == '2'){ //投顾产品跳转
                return pageTo_advisor(productInfo);
            }
            if (productInfo.exclusive_product_type == '03') {
                let invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
                tools.setPageToUrl('login/listMorePage', '2');
                appUtils.setSStorageInfo("productType", chooase_financial_prod_type) //存储类型
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) {
                    pageTo_evaluation()
                    return
                }
                if (productInfo.buy_state == "0" && productInfo.exclusive_product_type != "03") { // 不包括货基
                    layerUtils.iAlert("敬请期待");
                    return;
                }
                if (productInfo.buy_state == "2" && productInfo.exclusive_product_type != "03") { // 不包括货币基金
                    layerUtils.iAlert("产品已售罄");
                    return;
                }
                productInfo.fund_code = productInfo.prod_id;//产品编码
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
                    productInfo["prod_sub_type"] = "10";
                    appUtils.setSStorageInfo("productInfo", productInfo);
                    appUtils.pageInit(_page_code, "inclusive/moneytaryPurchase");
                });
                return;
            } else if (productInfo.prod_sub_type2 == '200') {   //公募
                // tools.setPageToUrl('login/listMorePage','2');
                if (productInfo._prod_propagate_temp) {
                    tools.jumpMarketingPage(_page_code, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
                }
            } else if (productInfo.prod_sub_type2 == '100') {   //私募
                
                tools.setPageToUrl('login/listMorePage', '2');
                appUtils.setSStorageInfo("productType", chooase_financial_prod_type) //存储类型
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                if (userAuthenticationStatus == '0') {
                    return $(_pageId + ".qualifiedInvestor").show();
                }
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                if (productInfo._prod_propagate_temp) {
                    tools.jumpMarketingPage(_page_code, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
                }
            } else {  //私募其他
                
                tools.setPageToUrl('login/listMorePage', '2');
                appUtils.setSStorageInfo("productType", chooase_financial_prod_type) //存储类型
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                if (userAuthenticationStatus == '0') {
                    return $(_pageId + ".qualifiedInvestor").show();
                }
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
            }
        }, 'click');

        //显示营销图片
        appUtils.preBindEvent($(_pageId + " #content"), ".showImg", function () {
            var url = global.oss_url + $(this).attr("data-img");
            var zone_name = $(this).attr("data-name");
            appUtils.pageInit(_page_code, "login/showImgMarket", {
                imgUrl: url, zoneName: zone_name
            });
        }, 'click');

        //点击转让本金相关
        appUtils.bindEvent($(_pageId + " #principal"), function () {
            order_by = "costmoney"
            if ($(_pageId + " #term i")[0].className) sort = '0'
            $(_pageId + " #term i").removeClass()
            if (order_by == "costmoney") {    //当目前高亮本金时候
                if (sort == "1") {  //当前为降序
                    sort = "0"
                    $(_pageId + " #principal i").removeClass('invertedTriangle')
                    $(_pageId + " #principal i").addClass('regularTriangle')
                    getZrInfo(start, false);
                } else {
                    sort = "1"
                    $(_pageId + " #principal i").removeClass('regularTriangle')
                    $(_pageId + " #principal i").addClass('invertedTriangle')
                    getZrInfo(start, false);
                }
            }
        }, "click");

        //点击剩余期限
        appUtils.bindEvent($(_pageId + " #term"), function () {
            order_by = "due_date"
            if ($(_pageId + " #principal i")[0].className) sort = '0'
            $(_pageId + " #principal i").removeClass()
            if (order_by == "due_date") {    //当目前高亮本金时候
                if (sort == "1") {  //当前为降序
                    sort = "0"
                    $(_pageId + " #term i").removeClass('invertedTriangle')
                    $(_pageId + " #term i").addClass('regularTriangle')
                    getZrInfo(start, false);
                } else {
                    sort = "1"
                    $(_pageId + " #term i").removeClass('regularTriangle')
                    $(_pageId + " #term i").addClass('invertedTriangle')
                    getZrInfo(start, false);
                }
            }
        }, "click");

        //购买
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".pro_detail", function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (_cust_fund_type == 1) {
                sessionStorage.isShowTranferPro = 1 //转让特殊处理
                appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
                appUtils.setSStorageInfo("buyTransferData", productInfo);   //购买转让产品信息展示
                common.changeCardInter( ()=> {
                    //校验用户是否上传过身份证照片
                    // if(!ut.getUploadStatus()){
                    //     let operationId = 'noUploadIdCard'
                    //     appUtils.setSStorageInfo("noUploadIdCard", '1');
                    //     return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    //         appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    //     }, function () {
                    //     }, "去上传", "取消",operationId);
                    // }
                    if(productInfo.same_prod_transfer_all == '1'){
                        return appUtils.pageInit(_page_code, "highEnd/indexPremise");
                        //小集合私募转让
                    }else{
                        //普通私募转让
                        return appUtils.pageInit(_page_code, "highEnd/index");
                    }
                });
                return;
            }
            sessionStorage.isShowTranferPro = null
            // let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            // if((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) return pageTo_evaluation()
            tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
        }, 'click');

        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    $(_pageId + ".qualifiedInvestor").hide();
                    userAuthenticationStatus = 1;
                    if (appUtils.getSStorageInfo("productType") == "06") {
                        getZrInfo(start, false);
                    } else {
                        var params = {
                            financial_prod_type: appUtils.getSStorageInfo("productType") // 分类类型
                        }
                        // getProductDetail(params);
                        setData(params)
                    }

                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });

        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            $(_pageId + ".qualifiedInvestor").hide();
            if (appUtils.getSStorageInfo("productType") == "06") {
                if (ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) || ut.getUserInf().custLabelCnlCode == "jjdx") {
                    appUtils.pageInit(_page_code, "login/userIndexs");
                } else if (ut.getUserInf().custLabelCnlCode == "yh") {
                    appUtils.pageInit(_page_code, "yuanhui/userIndexs");
                } else {
                    appUtils.pageInit(_page_code, "hengjipy/userIndexs");
                }
            } else {
                appUtils.clearSStorage("fund_code");
                appUtils.clearSStorage("productInfo");
            }
        });

        //弹出转让规则
        appUtils.bindEvent($(_pageId + " #transferRule"), function () {
            $(_pageId + " .rule_dio").show();
            $(_pageId + " .card1").show();
        });
        //关闭转让规则弹窗
        appUtils.bindEvent($(_pageId + " .card_footer"), function () {
            $(_pageId + " .rule_dio").hide();
            $(_pageId + " .card1").hide();
        });
    }

    //去测评
    function pageTo_evaluation() {
        let operationId = 'riskAssessment';
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }
    function highRenderCurLi(curId){
        let remark;
        switch (curId) {
            case "02":
                remark = '适 合 风 险 偏 好 较 低 的 投 资 者 ，该 产 品 投 向 债 券 等 资 产 ，追 求 年 化 3 - 5% 的 收 益。'
                $(_pageId + " .high_warn").text(remark);
                break;
            case "03":
                remark = '适 合 风 险 偏 好 适 中 的 投 资 者 ，该 产 品 投 资 的 大 部 分 是 利 率 债 、高 等 级 信 用 债 等 固 收 资 产 ，配 有 少 量 权 益 资 产 ，通 过 长 期 持 有 ，追 求 年 化 5 - 7% 的 收 益。'
                $(_pageId + " .high_warn").text(remark);
                break;
            case "04":
                remark = '适 合 风 险 偏 好 较 高 的 投 资 者 ，该 产 品 运 用 估 值 、量 化 模 型 ，择 时 投 资 沪 深 300 、中 证 500 等 宽 基 指 数 和 低 估 的 行 业 指 数 等 权 益 资 产 ，通 过 长 期 持 有 ，追 求 年 化 7% 以 上 的 收 益。'
                $(_pageId + " .high_warn").text(remark);
                break;
            case "07":
                $(_pageId + " .warn img").attr("src", "./images/cqlc.png");
                break;
        }
        $(_pageId + " .high_warn").show();
    }
    function renderCurLi(curId) {
        switch (curId) {
            case "02":
                $(_pageId + " .warn img").attr("src", "./images/lgs.png");
                break;
            case "03":
                $(_pageId + " .warn img").attr("src", "./images/wjlc.png");
                break;
            case "04":
                $(_pageId + " .warn img").attr("src", "./images/cqlc.png");
                break;
            case "07":
                $(_pageId + " .warn img").attr("src", "./images/cqlc.png");
                break;
        }
        $(_pageId + " .warn").show();
    }

    function destroy() {
        $(_pageId + " #content").hide();
        $(_pageId + " .high_warn").hide();
        $(_pageId + " .high_warn").text('');
        $(_pageId + " .main_listMorePage").removeClass('high');
        $(_pageId + " .warn").hide();
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #tab li span").removeClass('active');
        $(_pageId + " #content").html("");
        $(_pageId + " #show_img").html("")
        $(_pageId + ".qualifiedInvestor").hide();
        $(_pageId + " .finance_pro").html("");
        $(_pageId + " .finance_pro").hide();
        _cust_fund_type = 1;
        $(_pageId + ' .transfer_header').hide();
        order_by = "costmoney";
        sort = '1';
        start = 1;
        $(_pageId + " #transferRule").hide();
        
        $(_pageId + " #title").text("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        sessionStorage.isShowTranferPro = null
        appUtils.pageBack();
    }

    var listMorePage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = listMorePage;
});
