// 会员福利 - 列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService");
    var _pageId = "#vipBenefits_exchange";
    let common = require("common");
    let ut = require("../common/userUtil");
    var _pageCode = "vipBenefits/exchange";
    var tools = require("../common/tools");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var cardVoucherType;//卡券类型 1为话费券，2为京东券
    var usable_vol;
    let total_assets;//总资产
    let threshold;//积分兑换限额
    let userInfo,cust_no,mobile

    function init() {
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        mobile = userInfo.mobileWhole
        getTotal_assets();//获取总资产
        reqFun108001();//会员总积分查询
        reqFun108004("1");
        reqFun108004("2");
    }
    //获取总资产
    function getTotal_assets() {
        service.reqFun101932({}, function (data) {
            if (data.error_no == "0") {
                let res = data.results[0];
                total_assets = res.total_assets?res.total_assets*1:0;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), ()=> {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), ()=> {
            tools.saveAlbum(_pageCode)
            // appUtils.pageInit(_pageCode, "customerService/onlineCustomerService");
        });
        //点击话费券
        appUtils.preBindEvent($(_pageId + " .type_content"), "li",  function(e) {
            if (!ut.hasBindCard(_pageCode)) return;
            e.stopPropagation();
            e.preventDefault();
            if(total_assets < threshold){   //总资产小于限额
                return layerUtils.iAlert("您的资产低于" + threshold + '元，暂不支持兑换');;
            }
            var info = JSON.parse($(this).find(".info").html());//兑换所需积分
            var shopStr_type = info.type;//1为话费券，2为京东券
            var shopStr_points = info.points;//所需积分
            var shopStr_state = info.state;//state-0正常1敬请期待
            if (shopStr_type == '1' || shopStr_type == '2') {//1为话费券，2为京东券
                if(shopStr_state == '1'){
                    layerUtils.iAlert("敬请期待!");
                    return;
                }
                if (parseFloat(usable_vol) < parseFloat(shopStr_points)) {//总积分小于兑换所需积分
                    layerUtils.iConfirm("您的积分不足!",  ()=> {
                    },  ()=> {
                        $(_pageId + " .type_content").hide();
                        appUtils.pageInit(_pageCode, "vipBenefits/index");
                    }, "确定", "赚积分");
                    return;
                }
                //缓存积分兑换信息
                let exchangeInfo = info;
                appUtils.setSStorageInfo("exchangeInfo", exchangeInfo);
                appUtils.pageInit(_pageCode, "vipBenefits/phoneVouchersConfirm", info);
            } else {
                layerUtils.iAlert("敬请期待!");
            }
        }, "click");

    }
    //会员总积分查询(108001)
    function reqFun108001() {
        if(appUtils.getPageParam("luckflag")){
            appUtils.setSStorageInfo("luckflag", appUtils.getPageParam("luckflag"));
        }
        luckflag = appUtils.getSStorageInfo("luckflag");
        if(userInfo && !userInfo.bankAcct){
            $(_pageId + ' #integral').html('0');
            return;
        }
        service.reqFun108001({cust_no:cust_no,mobile:mobile}, (data)=> {
            if (data.error_no == 0) {
                let results = data.results[0];
                usable_vol = results.usable_vol;
                threshold = results.threshold*1;
                $(_pageId + ' .bg_white_yue_new .jifen span em').html(usable_vol);
                $(_pageId + ' .bg_white_yue_new .num span em').html(data.results[0].recommended_count ? data.results[0].recommended_count : '--');
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //商品查询-108004
    function reqFun108004(cardVoucherType) {
        service.reqFun108004({exchange_type:cardVoucherType}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results;
                var htmls = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        htmls += '<li class="benefitsList">' +
                            '<div class="info" style="display: none">' + JSON.stringify(results[i]) + '</div>' +
                            '<div class="phoneSecurities">' +
                            '<img src="' + global.oss_url + results[i].img + '" alt="">' +
                            '</div>' +
                            '<p>' + results[i].name + '</p>' +
                            '<h4><span class="jifen">' + results[i].points + '</span>积分</h4>' +
                            '</li>';
                    }
                } else {
                    $(_pageId + " #benefitsShop .placeholder").show();
                    $(_pageId + " #benefitsShop .warm_prompt").hide();
                }
                if(cardVoucherType == "1"){
                    $(_pageId + " #benefitsShop .benefitsCon").html(htmls);
                }else{
                    $(_pageId + " #jingdShop .benefitsCon").html(htmls);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //销毁事件
    function destroy() {
        $(_pageId + " .benefitsCon").html("");
        $(_pageId + " #benefitsShop .placeholder").hide();
        usable_vol = "";
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});