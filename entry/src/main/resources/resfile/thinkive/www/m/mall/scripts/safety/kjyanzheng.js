// 快捷换卡--信息验证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageUrl = "safety/kjyanzheng",
        _pageId = "#safety_kjyanzheng ";
    var user_id = "",//客户id
        custno = "";//客户编号
    var timer = null;//计时器
    var i = 120;//倒计时长
    var yzm = "";
    var mobile = '';
    var sendMobile = '';
    var ut = require("../common/userUtil");
    var backPage = "", product_id = "", entrust_no = "";

    function init() {
        sendMobile = ut.getUserInf().bankReservedMobile;
        initYanZma();
        $(_pageId + " #tradeNum").val("");
        backPage = appUtils.getSStorageInfo("_prePageCode");
        product_id = appUtils.getPageParam("product_id");
        entrust_no = appUtils.getPageParam("entrust_no");
        if (backPage == "financialProducts/productDetails") {
            clearPage();
            appUtils.setSStorageInfo("backPage", backPage);
            appUtils.setSStorageInfo("back_product_id", product_id);
        } else {
            clearPage();
            appUtils.setSStorageInfo("backPage", backPage);
        }
        if (backPage != 'safety/kjnewcardinfo') {
            appUtils.setSStorageInfo("backprepage_huanka", backPage);
        }
        i = 120;
        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.css("background-color", "#E5433B");
        $yzm.html("获取验证码");
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #tradeNum").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #tradeNum1").addClass("active");
                    }
                } else {
                    passflag = "请输入交易密码";
                    $(_pageId + " #tradeNum1").removeClass("active");
                }
                $(_pageId + " #tradeNum1").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #tradeNum").val(shuru);
                    $(_pageId + " #tradeNum1").text(passflag);
                }
            } // 键盘的输入事件
        };
        common.systemKeybord(); // 解禁系统键盘
        window.clearInterval(timer);
        $(_pageId + " #getYzm").attr("data-state", "true");
        clearPage();
        user_id = appUtils.getSStorageInfo("custid");
        custno = appUtils.getSStorageInfo("custNo");
        $(_pageId + " #tradeNum1").text("请输入交易密码");
    }

    //验证交易密码
    function realname() {
        var SysTransPwd = $(_pageId + " #tradeNum").val();

        //密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no == "0") {
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                SysTransPwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, SysTransPwd);
                var param = {
                    "trans_pwd": SysTransPwd,
                }
                var callBack = function (resultsVo) {
                    //验证码重置
                    window.clearInterval(timer);
                    var $code = $(_pageId + " #getYzm");
                    $(_pageId + " .tips_box").hide();
                    i = 120;
                    $code.css("background-color", " #E5433B");
                    $code.attr("data-state", "true");
                    $code.html("重新获取验证码");
                    $(_pageId + " #verificationCode").val("");
                    initYanZma();
                    $(_pageId + " #talkCode").hide();
                    $(_pageId + " #weihao").hide();
                    //验证码重置

                    if (resultsVo.error_no == 0) {
                        appUtils.pageInit("safety/yanzheng", "safety/kjnewcardinfo");
                    } else {
                        layerUtils.iAlert(resultsVo.error_info);
                    }
                };
                service.reqFun101025(param, callBack);
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }

        },{isLastReq: false});
    }

    /**
     * 显示读秒
     * */
    function shows() {
        var $code = $(_pageId + " #getYzm");
        $code.attr("data-state", "false");//点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.css("background-color", "#aaaaaa");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.css("background-color", " #E5433B");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").hide();
            $(_pageId + " #weihao").hide();
        }
    }

    /**
     * 发送手机验码
     * */
    function sendPhoneCode() {
        var param = {
            mobile_phone: sendMobile,
            send_type: "0",
            type: common.sms_type.kjChangeCard,
            mobile_type: "2"
        };
        service.reqFun199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + " #talkCode").show();
                $(_pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                timer = setInterval(function () {
                    shows();
                }, 1000);
            } else {
                var $code = $(_pageId + " #getYzm");
                window.clearInterval(timer);
                i = 120;
                $code.css("background-color", " #E5433B");
                $code.attr("data-state", "true");
                $code.html("重新获取验证码");
                initYanZma();
                $(_pageId + " #talkCode").hide();
                $(_pageId + " #weihao").hide();
                layerUtils.iAlert(error_info);
            }
            $(_pageId + " #talkCode").show();
        });
    }

    //绑定事件
    function bindPageEvent() {
        //点击开启键盘
        appUtils.bindEvent($(_pageId + " #tradeNum11"), function () {
            if ($(_pageId + " #tradeNum").val() == "") {
                $(_pageId + " #tradeNum1").removeClass("unable");
            } else {
                $(_pageId + " #tradeNum1").removeClass("unable").addClass("active");
            }
            kaiqi("tradeNum");
        });
        appUtils.bindEvent($(_pageId + " .grid_02"), function () {
            $(_pageId + " .pop_layer4").hide();
            $(_pageId + " .card_rules").hide();

        });
        appUtils.bindEvent($(_pageId + " .right_btn"), function () {
            $(_pageId + " .pop_layer4").show();
            $(_pageId + " .card_rules").show();
        });
        //失去焦点然后就关闭键盘
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            $(_pageId + " #tradeNum1").removeClass("active").addClass("unable");
            guanbi();
        }, "focus");
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 交易密码全数字验证输入事件
        appUtils.bindEvent($(_pageId + " #tradeNum"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //获取验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                window.clearInterval(timer);
                //获取验证码
                sendPhoneCode(); //发送短信,获取验证码
            }
        });
        //下一步
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var trans_pass = $(_pageId + " #tradeNum").val();
            var sms_code = $(_pageId + " #verificationCode").val();
            if (validatorUtil.isEmpty(trans_pass)) {
                layerUtils.iMsg(-1, "交易密码不能为空");
                return;
            }
            if (trans_pass.length != 6) {
                layerUtils.iMsg(-1, "交易密码不足6位");
                return;
            }
            if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                layerUtils.iMsg(-1, "请先获取验证码");
                return;
            }

            if (sms_code == "") {
                if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                    layerUtils.iMsg(-1, "请先获取验证码");
                    return;
                } else {
                    layerUtils.iMsg(-1, "请输入验证码");
                    return;
                }
            } else {
                //验证短信验证码
                var param = {
                    "sms_code": sms_code,
                    "smm_mobile": sendMobile,
                    "mobile_type": "2",
                }
                var callBack = function (resultsVo) {
                    if (resultsVo.error_no == 0) {
                        realname();
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(resultsVo.error_info);
                    }
                };
                service.reqFun1100003(param, callBack, {isLastReq: false});
            }
        });

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

    }


    //获取语音验证码
    function getCodeOFTalk() {
        if (sendMobile) {
            var param = {
                "mobile_phone": sendMobile,
                "type": common.sms_type.kjChangeCard,
                "send_type": "1",
                "mobile_type": "2"
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(_pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        } else {
            layerUtils.iAlert("请输入手机号");
        }
    }

    //初始化语音验证码
    function initYanZma() {
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
    }


    //页面清理
    function clearPage() {
        $(_pageId + " input").attr("value", "");
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);

    }

    function kaiqi(jjb_pwd) {
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_kjyanzheng";
        param["eleId"] = jjb_pwd;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param)
    }

    function destroy() {
        guanbi();
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        window.clearInterval(timer);
        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.css("background-color", "#E5433B");
        $yzm.html("获取验证码");
        i = 120;
        $code.html("获取验证码");
        $(_pageId + " #tradeNum1").text("请输入交易密码");
        service.destroy();
        $(_pageId + " #getYzm").removeAttr("style");
        $(_pageId + " input").attr("value", "");
        $(_pageId + " #tradeNum").val("");
        $(_pageId + ' #weihao').hide();
        $(_pageId + ' #talkCode').hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
