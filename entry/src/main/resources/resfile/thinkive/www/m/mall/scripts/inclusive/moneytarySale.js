// 货基 - 卖出页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#inclusive_moneytarySale ";
    var _pageCode = "inclusive/moneytarySale";
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    var _redem_method = "1";
    var jymm;
    var holdObj;
    function init() {
        holdObj = appUtils.getSStorageInfo("holdObj");
        $(_pageId + " #inputspanid").html(tools.fmoney(holdObj.available_vol));
        $(_pageId + " .prod_sname").html(holdObj.fund_sname);
        //获取交易时间
        reqFun102008();
        common.systemKeybord(); // 解禁系统键盘
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "inclusive/moneytaryDetail");
        });

        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
            guanbi();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击到账方式
        appUtils.bindEvent($(_pageId + " .modify_box .item"), function () {
            $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
            $(this).children(".icon").addClass("active");
            _redem_method = $(this).attr("redem_method");
        });

        //点击下一步
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            setRechargeInfo();
            //显示 输入交易密码
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .password_box").show();

            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "inclusive_moneytarySale";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();

            var param = {
                fund_code: holdObj.fund_code,	//基金代码
                trans_amt: holdObj.available_vol, //交易金额
                vir_fundcode:holdObj.vir_fundcode,
                trans_pwd: jymm1, //交易密码
                redem_method: _redem_method,  //赎回到宝:1,赎回到卡：2.
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                trade(param);
            }, {isLastReq: false});
        });
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function trade(param) {
        service.reqFun106023(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "inclusive/moneytarySaleResult", data.results[0]);
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //获取交易时间
    function reqFun102008() {
        var param = {
            fund_code: holdObj.fund_code,
            type: "7"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);

                var beforeDate = results.beforeDate;
                if (beforeDate != "--") {
                    beforeDate = tools.FormatDateText(beforeDate.substring(4));
                }

                var afterDate = results.afterDate;
                if (afterDate != "--") {
                    afterDate = tools.FormatDateText(afterDate.substring(4));
                }
                //确认日期
                $(_pageId + " #beforeDate").html(beforeDate);
                //收益日期
                $(_pageId + " #afterDate").html(afterDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        if (_redem_method == "1") {
            $(_pageId + " #payMethod").text("晋金宝");
        } else {
            $(_pageId + " #payMethod").text("银行卡");
        }
        $(_pageId + " #recharge_money").html(tools.fmoney(holdObj.available_vol));
    }

    function destroy() {
        guanbi();
        $(_pageId + " #account").html("--");
        $(_pageId + " .saleInfo").html("");
        nav = "";
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");
        $(_pageId + " .fund_type_name").html("--");
        $(_pageId + " #dzDate").html("--");
        jymm = "";
        _redem_method = "1";
        $(_pageId + " .modify_box .item").children(".icon").removeClass("active");
        $(_pageId + " .modify_box .item").eq(0).children(".icon").addClass("active");
        $(_pageId + " #czje").val("");
        $(_pageId + " #inputspanid").text("");
        $(_pageId + " #payMethod").text("--");
        $(_pageId + " #recharge_name").html("--");
        $(_pageId + " #recharge_money").html("--");
        monkeywords.destroy();
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
