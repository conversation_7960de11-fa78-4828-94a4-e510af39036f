/*创建时间hSea 2015-12-31 15:36:59 PM */
define(function (require, exports, module) {
    function a() {
        var pageId = $("body .page[data-display='block']").attr("id");
        let showType = $('#' + pageId + " .loginDig").is(':hidden');
        if(showType === false) return $('#' + pageId + " .loginDig").hide();
        if (g.isDirectExit) e(); else {
            var a = $(".page[data-display=block]").attr("data-pageLevel");
            if ("1" == a) e(); else if ("-1" == a) return; else {
                var pageCode = pageId.split("_").join("/");
                require.async("../../../../mall/scripts/" + pageCode + ".js",function(pageJs){
                    if(pageJs)pageJs.pageBack();
                });
            }
        }
    }

    function b(a) {
        var b = window.customKeyboardEvent || {}, e = a.pageId, f = a.eleId, g = a.keyCode, h = $("#" + e + " #" + f),
            i = h.val();
        if (g == k.DONE) b.keyBoardFinishFunction && b.keyBoardFinishFunction();
        else if (g == k.HIDE) b.keyBoardHideFunction && b.keyBoardHideFunction();
        else {
            var j = c(h), l = i.substring(0, j), m = i.substring(j, i.length);
            if (g == k.DEL) l && (l = l.substring(0, l.length - 1)), h.val(l + m), d(h, j - 1); else if (g == k.CLEAR) h.val(""); else if (0 > +g) ; else {
                var n = h.attr("maxlength");
                "undefined" != typeof g && null !== g && g.length > 0 && (n && /^\d+$/.test(n) ? h.val((l + g + m + "").substring(0, +n)) : h.val(l + g + m + ""), d(h, j + g.length))
            }
            b.keyBoardInputFunction && b.keyBoardInputFunction(g)
        }
    }

    function c(a) {
        var b = $(a)[0], c = 0;
        if (b.selectionStart) c = b.selectionStart; else if (document.selection) {
            var d = document.selection.createRange();
            d.moveStart("character", -b.value.length), c = d.text.length
        }
        return c
    }

    function d(a, b) {
        var c = $(a)[0], d = a.val(), e = d.length;
        if (e >= b) if (c.focus(), c.setSelectionRange) c.setSelectionRange(b, b); else {
            var f = c.createTextRange();
            f.moveStart("character", -e), f.moveEnd("character", -e), f.moveStart("character", b), f.moveEnd("character", 0), f.select()
        }
    }

    function e() {
        var pageId = $("body .page[data-display='block']").attr("id");
        $('#' + pageId + " .loginDig").hide();
        (new Date).getTime() - j < 2e3 ? (j = 0, i.callMessage({funcNo: "50105"})) : (i.callMessage({
            funcNo: "50106",
            content: "再按一次退出应用程序"
        }), j = (new Date).getTime())
    }

    function f(c) {
        c = c || {}, c.function50212 = b, c.function50107 = a
    }

    var g = require("gconfig"), h = (g.global, require("appUtils")), i = (require("layerUtils"), require("external")),
        j = 0, k = {CLEAR: -2, DONE: -3, DEL: -5, HIDE: -4};
    module.exports = {conbine: f}
});
/*创建时间 2015-12-31 15:36:59 PM */