<div class="page" id="bank_prodTrans" data-pageTitle="交易记录" data-refresh="true"
     style="-webkit-overflow-scrolling : touch;">
    <section class="main fixed" data-page="home">
        <header class="header">
            <input id="start_date" data-dateplugin="mobiScroll" value="查询日期"
                   style="margin-left:101px;position:absolute;z-index:-999" readonly/>
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">交易记录</h1>
                <a href="javascript:void(0);" class="btn_filter">更多查询</a>
            </div>

        </header>
        <article style="padding-bottom:0">
            <!--弹出筛选-->
            <div id="jymx_filter" class="record_filter" style="display:none;position:fixed;top:44px;z-index: 100">
                <div class="block">
                    <div style="margin-left: 0.05rem;">按日期</div>

                    <ul id="query_date" style="overflow: hidden;">
                        <li><a href='javascript:void(0)' data-value=''>不限</a></li>
                        <li><a href='javascript:void(0)' data-value='90'>近3月</a></li>
                        <li><a href='javascript:void(0)' data-value='360'>近1年</a></li>
                    </ul>
                    <div class="date_select" id="query_input" style="border-top: 0;margin-top: 0">
                        <div class="date_text">
                            <input type="text" placeholder="选择开始日期" readonly="readonly" id='startTime' value="">
                        </div>
                        <span class="fg_line"></span>
                        <div class="date_text">
                            <input type="text" placeholder="选择结束日期" readonly="readonly" id='endTime' value="">
                        </div>
                    </div>
                </div>
                <div class="block">
                    <div style="margin-left: 0.05rem;">按类型</div>
                    <ul id="query_type" style="overflow: hidden;">
                        <li><a href="javascript:void(0)" bus_type="">所有</a></li>
                        <li><a href="javascript:void(0)" bus_type="240">购买</a></li>
                        <li><a href="javascript:void(0)" bus_type="241">提前支取</a></li>
                        <li><a href="javascript:void(0)" bus_type="242">到期自动兑付</a></li>
                        <li><a href="javascript:void(0)" bus_type="244">到期支取</a></li>
                        <li><a href="javascript:void(0)" bus_type="245">周期付息</a></li>
                    </ul>
                </div>
                <div class="btn" id='query-button'>
                    <a href="javascript:void(0)" class="active reset" data-value="reset" id="reset">重置</a>
                    <a href="javascript:void(0)" class="active" data-value="confirm" id="confirm">确认</a>
                </div>
            </div>
            <div id="filter_layer" class="pop_layer" style="display:none;z-index: 90"></div>
            <!-- TRADE_HISTORY START -->
            <div class="trade_history">
            </div>

        </article>
    </section>
</div>
