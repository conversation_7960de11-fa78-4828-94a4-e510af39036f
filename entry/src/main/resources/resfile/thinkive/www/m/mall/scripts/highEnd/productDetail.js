// 晋金高端产品详情页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "highEnd/productDetail",
        _pageId = "#highEnd_productDetail ";
    var productInfo;
    var ut = require("../common/userUtil");

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        smDetails();
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //查看更多
        appUtils.bindEvent($(_pageId + " .more"), function () {
            if ($(_pageId + " .detailImgBox").height() == 200) {
                $(_pageId + " .detailImgBox").css({height: 'auto'});
                $(_pageId + " .more").text("收起");
            } else {
                $(_pageId + " .detailImgBox").css({height: 200});
                $(_pageId + " .more").text("查看更多");
            }

        });
        //概况
        appUtils.bindEvent($(_pageId + " .gk"), function () {
            appUtils.pageInit(_page_code, "highEnd/survey");
        });
        //公告
        appUtils.bindEvent($(_pageId + " .notice"), function () {
            appUtils.pageInit(_page_code, "highEnd/notice", {});
        });
        //产品合同
        appUtils.bindEvent($(_pageId + " .contract"), function () {
            appUtils.pageInit(_page_code, "highEnd/contract", {});
        });
        //交易规则
        appUtils.bindEvent($(_pageId + " .tradeDetail"), function () {
            appUtils.pageInit(_page_code, "highEnd/tradeRules");
        });

    }

    //私募产品详情查询（102043）
    function smDetails() {
        var fund_code = productInfo.fund_code;
        var param = {
            fund_code: fund_code
        };
        service.reqFun102043(param, function (datas) {
            if (datas.error_no == 0) {
                var preincomerate = datas.results[0].preincomerate;//年化业绩基准
                preincomerate = (+preincomerate).toFixed(2);
                var inrest_term = datas.results[0].inrest_term;//期限
                var risk_level_desc = datas.results[0].risk_level_desc;//风险等级描述
                var threshold_amount = datas.results[0].threshold_amount;//起投金额
                var interest_method = datas.results[0].interest_method;//付息方式
                var detailed_pictures = datas.results[0].detailed_pictures;//查看更多图片
                var used_per = datas.results[0].used_per;//募集进度
                var surplus_amount = datas.results[0].surplus_amount;//剩余额度
                var issue_start_time = datas.results[0].issue_start_time;//募集开始时间
                var issue_end_time = datas.results[0].issue_end_time;//募集结束时间
                var return_visit_start_date = datas.results[0].return_visit_start_date;//回访开始
                var return_visit_end_date = datas.results[0].return_visit_end_date;//回访结束
                var purconfirm_days = datas.results[0].purconfirm_days;//购买确认
                var redconfirm_days = datas.results[0].redconfirm_days;//赎回确认
                var surplus_num = datas.results[0].surplus_num;//剩余认购人数
                var prod_sname = datas.results[0].prod_sname;//产品简称
                var risk_level = datas.results[0].risk_level;//风险等级
                var return_visit_date = datas.results[0].return_visit_date;//回访日期


                //适当性评估需要字段
                productInfo = datas.results[0];
                appUtils.setSStorageInfo("productInfo", datas.results[0]);
                tools.initPriFundBtn(productInfo, _pageId);
                $(_pageId + " header h1").html(productInfo.prod_sname || "产品详情");
                $(_pageId + " .prod_name").text(productInfo.prod_sname);
                $(_pageId + " .prod_code").text("("+ productInfo.fund_code +")");
                $(_pageId + " .annual").text(preincomerate + '%');
                $(_pageId + " .expires").text(inrest_term)
                if (risk_level_desc) {
                   /* if (risk_level.substr(1) >= 4) {
                        $(_pageId + " .riskDesc").css({color: "#e5443c", borderColor: "#e5443c"});
                    } else {
                        $(_pageId + " .riskDesc").css({color: "#00c35e", borderColor: "#00c35e"});
                    }*/
                    $(_pageId + " .riskDesc").text(risk_level_desc).show();
                } else {
                    $(_pageId + " .riskDesc").hide()
                }
                if (threshold_amount) {
                    $(_pageId + " .thresholdAmount").text(threshold_amount / 10000 + '万起投').show();
                } else {
                    $(_pageId + " .thresholdAmount").hide();

                }
                if (interest_method) {
                    $(_pageId + " .interestMethod").text(interest_method).show();

                } else {
                    $(_pageId + " .interestMethod").hide();

                }
                $(_pageId + " .bgUrl").attr("src", require("gconfig").global.oss_url + detailed_pictures);
                $(_pageId + " .usedPer").text(tools.fmoney(used_per * 100) + '%');
                $(_pageId + " .bar").css({width: used_per * 100});
                $(_pageId + " .surplusAmount").text(tools.fmoney(surplus_amount) + '万元');
                $(_pageId + " .issueEndtime").text(tools.ftime(issue_end_time));

                if (issue_start_time && issue_end_time) {
                    $(_pageId + " .issue").text(tools.ftime(issue_start_time.substr(4, 4), ".") + "-" + tools.ftime(issue_end_time.substr(4, 4), "."));
                }
                if (return_visit_start_date && return_visit_end_date) {
                    $(_pageId + " .visit").text(tools.ftime(return_visit_start_date.substr(4, 4), ".") + "-" + tools.ftime(return_visit_end_date.substr(4, 4), "."));
                }
                if (purconfirm_days && redconfirm_days) {
                    // $(_pageId + " .sealing").text(tools.ftime(purconfirm_days.substr(0,8), ".") + "-" + tools.ftime(redconfirm_days.substr(0,8), "."))
                    $(_pageId + " .sealing").text(purconfirm_days.substr(2, 2) + "." + purconfirm_days.substr(4, 2) + "." + purconfirm_days.substr(6, 2) +
                        "-" + redconfirm_days.substr(2, 2) + "." + redconfirm_days.substr(4, 2) + "." + redconfirm_days.substr(6, 2));
                }
                if (purconfirm_days) {
                    $(_pageId + " .qxr").text(tools.FormatDateText(purconfirm_days.substr(4, 4)) + '确认')
                } else {
                    $(_pageId + " .qxr").text('-月-日确认')

                }
                $(_pageId + " .nav").html(tools.fmoney(productInfo.nav, 4) + "元");
                $(_pageId + " .nav_date").html(productInfo.nav_date ? "(" + tools.ftime(productInfo.nav_date.substr(4, 4)) + ")" : "");
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });

    }
    function destroy() {
        $(_pageId + " .fund_type_name").hide();
        $(_pageId + " .detailImgBox").css({height: 200});
        $(_pageId + " .more").text("查看更多");
        $(_pageId + " .bgUrl").attr("src", "");
        $(_pageId + " header h1").html("");
        $(_pageId + " .riskDesc").css({color: "#999999", borderColor: "#999999"});
        $(_pageId + " .surplusAmount").text("");
        $(_pageId + " .issueEndtime").text("");
        $(_pageId + " .usedPer").text("");
        $(_pageId + " .nav").html("");
        $(_pageId + " .nav_date").html("");
        $(_pageId + " .thfundBtn").html("");
        $(_pageId + " .prod_name").text("");
        $(_pageId + " .prod_code").text("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
