// 晋金经理
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        cfdUtils = require("cfdUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageId = "#inclusive_jjThirtyDetailManager";
    var _pageCode = "inclusive/jjThirtyDetailManager";
    var productInfo;
    var ut = require("../common/userUtil");
    var common = require("common");
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        //获取基金经理信息
        reqFun102021();
        tools.initFundBtn(productInfo, _pageId);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //获取基金经理信息
    function reqFun102021() {
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102021(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results) {
                    return;
                }
                var codeArr = [];
                var html = "";
                var str = ""
                for (var i = 0; i < results.length; i++) {
                	//空数据处理
                	results[i] = tools.FormatNull(results[i]);
                    str += '<div code=' + results[i].manager_code + ' id=' + results[i].manager_code + 'name>' + results[i].manager_name + '</div>';

                    appUtils.preBindEvent($(_pageId + " "), "#" + results[i].manager_code + 'name', function (e) {
                        $(_pageId + " #managerList").find(".warp_box").hide();

                        $(_pageId + " .name_box").find("div").removeClass("active").filter(this).addClass("active");
                        var code = $(this).attr("code");
                        $(_pageId + " #" + code + 'content').show();
                    });

                    html +=
                        '<div class="warp_box" id=' + results[i].manager_code + 'content>' +
                        '<div class="manager_name" id="manager_name">' + results[i].manager_name + '</div>' +
                        '<div class="content" id="content">' + results[i].resume + '</div>' +
                        '<div class="officeholding" id="officeholding">' +
                        '<ul class="text">' +
                        '<li>上任时间</li>' +
                        '<li>任职回报</li>' +
                        '<li>同期沪深300</li>' +
                        '</ul>' +
                        '<ul class="time">' +
                        '<li class="">' + results[i].serving_date + '</li>' +
                        '<li class="' + tools.addMinusClass(results[i].serving_return) + '">' + tools.fmoney(results[i].serving_return) + '%</li>' +
                        '<li class="' + tools.addMinusClass(results[i].return_300) + '">' + tools.fmoney(results[i].return_300) + '%</li>' +
                        '</ul>' +
                        '</div>' +

                        '<div class="history">' +
                        '<div class="title">历任基金</div>' +
                        '<div class="list" id=' + results[i].manager_code + '>' +


                        '</div>' +
                        '</div>' +
                        '</div>';
                    codeArr.push(results[i].manager_code)
                }
                $(_pageId + " .name_box").html(str);
                $(_pageId + " .name_box").find("div").eq(0).addClass("active");
                $(_pageId + " #managerList").html(html);

                $(_pageId + " #managerList").find(".warp_box").hide();
                $(_pageId + " #managerList").find(".warp_box").eq(0).show();

                if (codeArr.length > 1) {
                    $(_pageId + " .name_box").show();
                    $(_pageId + " #product").find(".manager_name").hide();
                } else {
                    $(_pageId + " .name_box").hide();
                }

                for (var i = 0; i < codeArr.length; i++) {
                    //查询基金经理历任基金信息
                    reqFun102025(codeArr[i]);
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //查询基金经理历任基金信息
    function reqFun102025(code) {
        var param = {
            code: code,
        }
        service.reqFun102025(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                var html = "";
                for (var i = 0; i < results.length; i++) {
                	//空数据处理
                	results[i] = tools.FormatNull(results[i]);
                    let resign_date = results[i].resign_date ? results[i].resign_date : '--'
                    html += '<div class="item">' +
                        '<ul class="text">' +
                        '<li>' + results[i].prod_sname + '</li>' +
                        '<li>任职回报</li>' +
                        '<li>同期沪深300</li>' +
                        '</ul>' +
                        '<ul class="time">' +
                        '<li>' + results[i].serving_date + '~' + resign_date + '</li>' +
                        '<li class="' + tools.addMinusClass(results[i].serving_return) + '">' + tools.fmoney(results[i].serving_return) + '%</li>' +
                        '<li class="' + tools.addMinusClass(results[i].return_300) + '">' + tools.fmoney(results[i].return_300) + '%</li>' +
                        '</ul>' +
                        '</div>';
                }
                $(_pageId + " #" + code).html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
		$(_pageId + " .name_box").html("");
		$(_pageId + "#threshold_amount").html("--");
		$(_pageId + "#buy_state").html("");
		$(_pageId + " #managerList").html("");
		$(_pageId + " #buy_rate_box").html("免申购费");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailManager = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailManager;
});
