//投资故事
define(function (require, exports, module) {
    require('../common/vue.min');
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#scene_snowStory ",
        _pageCode = "scene/snowStory",
        gconfig = require("gconfig"),
        global = gconfig.global,
        tools = require("../common/tools");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    require('../../css/iosSelect.css');
    var monkeywords = require("../common/moneykeywords");
    let storyTemplate;

    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }

    async function init() {

        //页面埋点初始化
        tools.initPagePointData();

        //生产
        // let html = await getTemplate({templateId:"110"}) //拿到模板数据
        //测试
        let html = await getTemplate({templateId:"194"}) //拿到模板数据
        $("#story_template").html(html)   //渲染模板
        storyTemplate = new Vue({
            el: '#story_template',
            data() {
                return{
                    selectedYear: '30', // 默认选择10年
                    oss_url: global.oss_url,
                    dataList:{
                        "5": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["1.4万",  "7.6万",  "31万"],
                            double: ["1.5万", "8.1万", "33万"],
                            stock: ["1.6万", "8.6万",  "35万"]
                        },
                        "10": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["3万",  "15万",  "55万"],
                            double: ["3.2万", "17万", "63万"],
                            stock: ["3.6万", "19万",  "71万"]
                        },
                        "15": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["4.7万",  "24万",  "83万"],
                            double: ["5.6万", "29万", "101万"],
                            stock: ["6.6万", "34万",  "123万"]
                        },
                        "20": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["6万",  "34万",  "116万"],
                            double: ["8万", "44万",  "151万"],
                            stock: ["10万",  "56万",  "195万"]
                        },
                        "25": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["9万",  "47万",  "155万"],
                            double: ["12万", "63万", "214万"],
                            stock: ["16万", "86万",  "297万"]
                        },
                        "30": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["12万", "61万", "200万"],
                            double: ["17万", "89万",  "296万"],
                            stock: ["25万",  "129万",  "441万"]
                        }
                    },
                }          
            },
            //视图 渲染前
            created(){
                //获取持仓数据
                //this.getHoldProdect();
            },
            //渲染完成后
            mounted() {
                this.planJoinTimeSelect();
                
            },
            //计算属性
            computed:{
                filteredDataList() {
                    const selectedYearData = this.dataList[this.selectedYear];
                    if (!selectedYearData) return {};
        
                    // 将数据转换为适合表格显示的格式
                    let items = [];
                    for (let i = 0; i < selectedYearData.first_investment.length; i++) {
                        items.push({
                            first_investment: selectedYearData.first_investment[i],
                            fixed_investment: selectedYearData.fixed_investment[i],
                            bond: selectedYearData.bond[i],
                            double: selectedYearData.double[i],
                            stock: selectedYearData.stock[i]
                        });
                    }
                    return {[this.selectedYear]: {items}};
                }

            },
            //绑定事件
            methods:{
                //打开弹框
                calculatorBtn(){
                    tools.recordEventData('1','calculatorBtn','打开弹框');
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " .calculator").show();
                },
                close_btn(){
                    tools.recordEventData('1','close_btn','关闭弹框');
                    cleartData();
                    $(_pageId + " .pop_layer").hide();
                    $(_pageId + " .calculator").hide();
                },
                planJoinTimeSelect() {                   
				    dataArr = [
                        { id: "5", value: "5年", year: "5" },
			            { id: "10", value: "10年", year: "10" },
                        { id: "15", value: "15年", year: "15" },
			            { id: "20", value: "20年", year: "20" },
                        { id: "25", value: "25年", year: "25" },
			            { id: "30", value: "30年", year: "30" }
			        ]
			        
			        tools.mobileSelect({
			            trigger: _pageId + " #planJoinTime",
			            title: "",
			            position:$(_pageId + " #joinTimeData").attr("data-position"),
			            dataArr: dataArr,
			            callback: data => {
                            tools.recordEventData('1','select','选中年限');
			                $(_pageId + " #planJoinTime").html(`<span class="m_font_size16">收益（${data[0].year}年▼）</span>`);
			                $(_pageId + " #planJoinTime").attr("data-id", data[0].id);
			                $(_pageId + " #planJoinTime").attr("data-value", data[0].year);
                            cleartData();
			                this.selectedYear = data[0].id;
			            }
			        });
      
            	},
                //计算财富
                figure_it_out(){
                    tools.recordEventData('1','figure_it_out','计算财富');
                    clickGenerateResult();
                },
                
                inputspanidAmt(){
                    tools.recordEventData('1','inputspanidAmt','首投');
                    let invest_money = $(_pageId + " #czje_InvestAmt").val();
                    let trans_amt = $(_pageId + " #czje_Amt").val();
                    if(!invest_money || invest_money == ''){
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(170, 170, 170)" });
                        $(_pageId + " #inputspanidInvestAmt span").addClass("unable");
                        $(_pageId + " #inputspanidInvestAmt span").text($(_pageId + " #inputspanidInvestAmt span").attr("text"));
                    }
                    if(!trans_amt || trans_amt == ''){
                        $(_pageId + " #inputspanidAmt span").attr("text","请输入")
                    }
                    inputSpanEvent("Amt",event)
                },
                inputspanidInvestAmt(){
                    tools.recordEventData('1','inputspanidInvestAmt','定投');
                    let invest_money = $(_pageId + " #czje_InvestAmt").val();
                    let trans_amt = $(_pageId + " #czje_Amt").val();
                    if(!trans_amt || trans_amt == ''){
                        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(170, 170, 170)" });
                        $(_pageId + " #inputspanidAmt span").addClass("unable");
                        $(_pageId + " #inputspanidAmt span").text($(_pageId + " #inputspanidAmt span").attr("text"));
                    }
                    if(!invest_money || invest_money == ''){
                        $(_pageId + " #inputspanidInvestAmt span").attr("text","请输入")
                    }
                    inputSpanEvent("InvestAmt",event)
                }
            }
        })
    }
        

    function bindPageEvent() {

        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });


         //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " article").removeClass("combProduct_startCasting_article");
            monkeywords.close();
        });

    //     appUtils.bindEvent($(_pageId + " #planJoinTime"), function () {
    //        // dataArr = JSON.parse($(_pageId + " #joinTimeData").attr("data"))
    //         dataArr = [
    //             { id: "10", value: "10年", year: "10" },
    //             { id: "20", value: "20年", year: "20" },
    //             { id: "30", value: "30年", year: "30" }
    //         ]
            
    //         tools.mobileSelect({
    //             trigger: _pageId + " #planJoinTime",
    //             title: "",
    //             position:$(_pageId + " #planJoinTime").attr("data-position"),
    //             dataArr: dataArr,
    //             callback: data => {
    //                 $(_pageId + " #planJoinTime").html(`<span class="m_font_size16">${data[0].year}年</span> <span>▼</span>后收益`);
    //                 $(_pageId + " #planJoinTime").attr("data-id", data[0].id);
    //                 $(_pageId + " #planJoinTime").attr("data-value", data[0].year);
    //                // this.selectedYear = data[0].id;
    //             }
    //         });      
    //    });
    }
    function cleartData(){
        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(170, 170, 170)" });
        $(_pageId + " #inputspanidAmt span").addClass("unable");
        $(_pageId + " #inputspanidAmt span").text($(_pageId + " #inputspanidAmt span").attr("text"));
        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(170, 170, 170)" });
        $(_pageId + " #inputspanidInvestAmt span").addClass("unable");
        $(_pageId + " #inputspanidInvestAmt span").text($(_pageId + " #inputspanidInvestAmt span").attr("text"));
        $(_pageId + " .income3").text("--");
        $(_pageId + " .income5").text("--");
        $(_pageId + " .income7").text("--");
        $(_pageId + " #czje_Amt").val('');
        $(_pageId + " #czje_InvestAmt").val('');

    }
      // 生成预算结果
    function clickGenerateResult() {
        tools.recordEventData('1','clickGenerateResult','算一算');
        var threshold_amount = "200";
        var invest_amount = "200";
        var amt = $(_pageId + " #czje_Amt").val().replace(/,/g, "");
        var invest_amt = $(_pageId + " #czje_InvestAmt").val().replace(/,/g, "");
        var term = $(_pageId + " #planJoinTime").attr("data-value");
        if (amt <= 0 || !amt) {
            layerUtils.iAlert("请输入首投金额");
            return;
        }
        if (threshold_amount && parseFloat(amt) < parseFloat(threshold_amount)) { //首次购买
            layerUtils.iAlert(`首投金额不能低于${threshold_amount}元`);
            return;
        }
        if (tools.isMatchAddAmt(amt, threshold_amount, "1")) {
            return
        }
        if (invest_amt > 0) {
            if (invest_amount && parseFloat(invest_amt) < parseFloat(invest_amount)) { //首次购买
                layerUtils.iAlert(`定投金额不能低于${invest_amount}元`);
                return;
            }
            if (tools.isMatchAddAmt(invest_amt, invest_amount, "1")) {
                return
            }
        }
        service.reqFun102213({
            amt: amt,
            invest_amt: invest_amt && invest_amt > 0 ? invest_amt : "0",
            term: term,
            yield: "3%,5%,7%"
        }, async (data) => {
            if (data.error_no == '0') {
                var results = data.results;
                $(_pageId + " .income3").text(tools.fmoney(results[0].income/10000)+"万");
                $(_pageId + " .income5").text(tools.fmoney(results[1].income/10000)+"万");
                $(_pageId + " .income7").text(tools.fmoney(results[2].income/10000)+"万");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }




      //输入首投/每月定投总额弹出数字键盘
    function inputSpanEvent(id,event) {
        event.stopPropagation();
        $(_pageId + ` #czje_${id}`).val('');
        //键盘事件
        moneyboardEvent(id);
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "scene_snowStory";
        param["eleId"] = `czje_${id}`;
        param["doneLable"] = "确定";
        param["keyboardType"] = "3";
        $(_pageId + " article").addClass("scene_snowStory_article");
        $(_pageId + " .scene_snowStory_article").scrollTop(300);
        require("external").callMessage(param);
    }
    // 金额键盘事件
    function moneyboardEvent(id) {
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () { // 键盘完成
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var moneys = curVal.replace(/,/g, "");
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text('请输入');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text('请输入');
                    }
                }   
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }              
                $(_pageId + ` #czje_${id}`).val(moneys);
                $(_pageId + " article").removeClass("scene_snowStory_article");
            },
            inputcallback: function () {// 键盘输入
                var curVal = $(_pageId + ` #czje_${id}`).val();
                curVal = curVal.replace(/,/g, "");
               
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + ` #czje_${id}`).val(curVal.substring(0, curVal.length - 1));
                }

            }, // 键盘隐藏
            keyBoardHide: function () {
                $(_pageId + " article").removeClass("scene_snowStory_article");
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                curVal = curVal.replace(/,/g, "");
                curVal = curVal*1;   
                            
                var moneys = curVal;   
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text('请输入');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "#aaa" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text('请输入');
                    }
                }             
                if (moneys) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(moneys));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(moneys));
                }
            },
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(".mobileSelect").remove();
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .calculator").hide();
        cleartData();
        monkeywords.close();
    }

    function pageBack() {
        appUtils.pageBack();
        monkeywords.close();
    }

    var scene_snowStory = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = scene_snowStory;
});
