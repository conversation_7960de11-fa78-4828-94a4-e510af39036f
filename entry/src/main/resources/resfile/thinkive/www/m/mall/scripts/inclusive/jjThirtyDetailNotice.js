// 公告
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageId = "#inclusive_jjThirtyDetailNotice",
        _pageCode = "inclusive/jjThirtyDetailNotice";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var _fund_code = "";
    var productInfo;
    var ut = require("../common/userUtil");
    var common = require("common");
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #filter").hide();
        $(_pageId + " .bottom .no").hide();
        tools.initFundBtn(productInfo, _pageId);
        //查询产品公告
        reqFun102033();

    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //点击全部
        appUtils.bindEvent($(_pageId + " #all"), function () {
            $(_pageId + " #all").addClass("active");
            $(_pageId + " #more").removeClass("active");

            $(_pageId + " #more").html("更多分类");

            $(_pageId + " #filter").hide();
            $(_pageId + " #list_box").show();
            $(_pageId + " #bottom").show();
        });
        //点击更多分类
        appUtils.bindEvent($(_pageId + " #more"), function () {
            $(_pageId + " #all").removeClass("active");
            $(_pageId + " #more").addClass("active");

            $(_pageId + " #filter").show();
            $(_pageId + " #list_box").hide();
            $(_pageId + " #bottom").hide();
        });
        //点击更多分类的选项
        appUtils.bindEvent($(_pageId + " #filter li"), function (e) {
            var text = e.target.innerText;
            if (text) {
                $(_pageId + " #more").html(text);

                $(_pageId + " #all").removeClass("active");
                $(_pageId + " #more").addClass("active");

                $(_pageId + " #filter").hide();
                $(_pageId + " #list_box").show();
                $(_pageId + " #bottom").show();
            }
        });

//        //点击公告
//        appUtils.preBindEvent($(_pageId + " #list_box"), ".list .item", function (e) {
//             var id = $(this).attr("data-id");
//             var date = $(this).parent().attr("data-date");
//             var text = $(this).children(".text").html();
//             var params = {
//            	 id:id,
//            	 date:date,
//            	 text:text
//             }
//             appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailNoticeDetail",params);
//        }, 'click');

      //点击公告
        appUtils.preBindEvent($(_pageId + " #list_box"), " .item", function (e) {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            // param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            if(url.indexOf("http") > -1) {
                param["url"] = url;
            } else {
                param["url"] = global.oss_url + url;
            }
            require("external").callMessage(param);
        }, 'click');

    }


    //查询产品公告
    function reqFun102033() {
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102033(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    $(_pageId + " .thfundCopyright").css({position: "fixed", bottom: "1rem"}).show();
                    return;
                }
                $(_pageId + " .thfundCopyright").css({position: "static"}).show();
                var title;
                var html = '';
                for (var i = 0; i < results.length; i++) {
                    var title_new = results[i].info_pubdate.substring(0,4);
                    //年份与之前不同,新增一个年份框,并记录最新年份
                    if(title != title_new){
                	title = title_new
                	html += '<div class="list" data-date='+title_new+'>' +
                        '<div class="title">' + title_new + '年</div>';
                    }
                    //增加本条数据
                    var time = tools.ftime(results[i].info_pubdate).substring(5);
                    html += '<div class="item" operationType="1" operationId="item_'+ results[i].id +'" operationName="公告" url="' + results[i].an_url + '"data-id='+results[i].id+  '>' +
                    '<span class="time">' + time + '</span>' +
                    '<span class="text">' + results[i].an_title + '</span>' +
                    '</div>';

                }
                $(_pageId + " #list_box").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

//    function reqFun102033(){
//        var param = {
//            fund_code: productInfo.fund_code
//        };
//        service.reqFun102033(param, function (data) {
//            if (data.error_no == 0) {
//                var results = data.results;
//                if (!results || results.length == 0) {
//                    return;
//                }
//                var html = "";
//                for (var i = 0; i < results.length; i++) {
//
//
//                    html += '<div class="item right_icon" url="' + results[i].an_url + '">' +
//                        '<span>' + results[i].an_title + '</span>' +
//                        '</div>';
//                }
//
//                $(_pageId + " #list_box").html(html);
//            } else {
//                layerUtils.iAlert(data.error_info);
//            }
//        })
//    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    	$(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
        $(_pageId + " #list_box").html("");
        $(_pageId + " .thfundCopyright").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
