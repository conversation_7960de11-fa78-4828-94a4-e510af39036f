// 尊享服务
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#highVersion_exclusiveEnjoyment ",
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        _page_code = "highVersion/exclusiveEnjoyment";
        service = require("mobileService")
        require("../../js/prov_city.js");
        require("../../js/city.js");
        require("../../js/picker.min.js");
        var userInfo;
        var ut = require("../common/userUtil");
        var common = require("common");
        var first = [];
        var gconfig = require("gconfig");
        var global = gconfig.global;
        /* 省，直辖市 */
        var second = [];
        /* 市 */
        var third = [];
        /* 镇 */
        var selectedIndex = [0, 0, 0];
        /* 默认选中的地区 */
        var checked = [0, 0, 0];
        /* 已选选项 */
        var picker = ""; //地址选择器
        var live_province;
        var live_city;
        var live_county;
        //默认展示礼品回馈卡片
        var pageCardIndex = '1';
        let serviceInformation = "";//尊享服务信息
        let btn_flag = true;//按钮状态
        let appointment_date = "";//预约日期
        let appointment_time = "";//预约时间
        let workdayList = "";
        let gender = "";
        let firstName = "";
        let show_date,show_time
        let service_type;
    function init() {
        service_type = appUtils.getSStorageInfo("service_type") ? appUtils.getSStorageInfo("service_type") : '2';
        // service_type = '2';
        //清除mobileSelect
        $(".mobileSelect").remove();
        pageCardIndex = service_type*1 - 1 + '';
        //初始化顶部
        $(_pageId + " .chooseList ul").removeClass('active');
        $($(_pageId + " .chooseList ul")[service_type*1 - 2]).addClass('active');
        userInfo = ut.getUserInf();
        //获取用户性别
        gender = userInfo.gender == "1" ? "先生" : "女士";
        //获取用户姓
        firstName = userInfo.name ?  userInfo.name.split('')[0] : '';

        //页面埋点初始化
        tools.initPagePointData();
        //渲染轮播图
        // setBanner();
        //渲染地址
        // selectorArea();   
        //渲染日期
        workday();
        //初始化风险偏好
        queryRiskPreference();
        highVersion_exclusiveEnjoyment.bindPageEvent();
        
    }
    /**
     * 选择可预约日期
     * */
    function queryBadCredit() {   
      
        // 格式化日期选项
        const timeOptions = workdayList.map((dateStr, index) => {
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            
            return {
                id: index.toString(),
                value: `${month}-${day}`,
                originalDate: dateStr,
                childs: [
                    {id: `${index}-0`, value: '上午'},
                    {id: `${index}-1`, value: '下午'}
                ]
            };
        });
      
        // 初始化选择器
        tools.mobileSelect({
            trigger: _pageId + " .face_date",
            title: "请选择预约时间",
            dataArr: timeOptions,
            callback: function (data) {
                if (data && data.length >= 2) {
                    const selectedIndex = parseInt(data[0].id);
                    appointment_date = timeOptions[selectedIndex].originalDate;
                    appointment_time = data[1].value;
 
                }
            }
        });
        // 初始化选择器
        tools.mobileSelect({
            trigger: _pageId + " .product_date",
            title: "请选择预约时间",
            dataArr: timeOptions,
            callback: function (data) {
                if (data && data.length >= 2) {
                    const selectedIndex = parseInt(data[0].id);
                    appointment_date = timeOptions[selectedIndex].originalDate;
                    appointment_time = data[1].value;
 
                }
            }
        });
    }
      
    
    /**
     * 居住地址选择
     */
    function selectorArea() {
        var position;
        var position1;
        var position2;
        // if (live_province) {
        //     try {
        //         position = city.findIndex(function (item) {
        //             return live_province == item.code;
        //         });
        //         position1 = city[position].sub.findIndex(function (item) {
        //             return live_city == item.code;
        //         });
        //         position2 = city[position].sub[position1].sub.findIndex(function (item) {
        //             return live_county == item.code;
        //         });
        //         var value = city[position].name + ' ' + city[position].sub[position1].name + ' ' + city[position].sub[position1].sub[position2].name;
        //         $(_pageId + " #live_address").val(value);
        //     } catch (error) {
        //         position = 0;
        //         position1 = 0;
        //         position2 = 0;
        //     }
        // } else {
        //     position = 0;
        //     position1 = 0;
        //     position2 = 0;
        // }
        position = 0;
        position1 = 0;
        position2 = 0;
        selectedIndex = [position, position1, position2];
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });
        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            var value = text1 + ' ' + text2 + ' ' + text3;
            live_province = first[selectedIndex[0]].code;
            live_city = second[selectedIndex[1]].code;
            live_county = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            // var code = code1 + ' ' + code2 + ' ' + code3;
            // $(_pageId + " #live_address").attr("data-code", code);
            let flag = await tools.is_region(live_province,live_city,live_county,city)
            if(flag){
                $(_pageId + " #live_address").val(value);
            }else{
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
    }
    //banner
    function setBanner(){

        // let htmlstr = "";
        // //根据 类型展示对应数据banner
        // if(pageCardIndex == '0'){   // 礼品回馈
        //     tools.guanggao({ _pageId: _pageId, group_id: "44" });

        //     htmlstr = `<ul>
        //             <li class="top-title">服务说明</li>
        //             <li>
        //                 <p class="m_bold">1、服务内容</p>
        //                 <p> 感谢您一直以来对晋金财富的支持与信任，为您准备了礼品，请在活动时间内领取。</p>
        //             </li>
        //             <li>
        //                 <p class="m_bold">2、参与方式</p>
        //                 <p>活动时间内填写联系电话及邮寄地址，我们将尽快完成邮寄。 </p>
        //             </li>
        //             <li>
        //                 <p class="m_bold">3、注意事项</p>
        //                 <p>服务内容如有调整或变更以公告为准，详询财富顾问或<span class="blue call">400-167-8888</span>。</p>
        //             </li>
        //         </ul>`

        // }else if(pageCardIndex == '1'){//财顾面对面
        //     tools.guanggao({ _pageId: _pageId, group_id: "45" });
        //     htmlstr = `<ul>
        //             <li class="top-title">服务说明</li>
        //             <li>
        //                 <p class="m_bold">1、服务内容</p>
        //                 <p> 财富顾问与您1V1答疑，解读产品，分析近期市场，提供专属资产配置建议。</p>
        //             </li>
        //             <li>
        //                 <p class="m_bold">2、参与方式</p>
        //                 <p>选择预约时间，我们将尽快与您联系。 </p>
        //             </li>
        //             <li>
        //                 <p class="m_bold">3、注意事项</p>
        //                 <p>服务内容如有调整或变更以公告为准，详询财富顾问或<span class="blue call">400-167-8888</span>。</p>
        //             </li>
        //         </ul>`
        // }else if(pageCardIndex == '2'){//定制产品
        //     tools.guanggao({ _pageId: _pageId, group_id: "46" });
        //     htmlstr = `<ul>
        //     <li class="top-title">服务说明</li>
        //     <li>
        //         <p class="m_bold">1、服务内容</p>
        //         <p> 根据您的投资需求，为您定制产品及资产配置方案。</p>
        //     </li>
        //     <li>
        //         <p class="m_bold">2、参与方式</p>
        //         <p>选择预约时间，我们将尽快与您联系。</p>
        //     </li>
        //     <li>
        //         <p class="m_bold">3、注意事项</p>
        //         <p>服务内容如有调整或变更以公告为准，详询财富顾问或<span class="blue call">400-167-8888</span>。 </p>
        //     </li>
        // </ul>`
        // }else if(pageCardIndex == '3'){//线下沙龙
        //     tools.guanggao({ _pageId: _pageId, group_id: "47" });
        //     htmlstr = `<ul>
        //     <li class="top-title">服务说明</li>
        //     <li>
        //         <p class="m_bold">1、服务内容</p>
        //         <p> 晋金财富不定期举办线下贵宾活动，名额有限，诚邀您参加。</p>
        //     </li>
        //     <li>
        //         <p class="m_bold">2、参与方式</p>
        //         <p>活动时间内填写联系电话，我们将尽快与您联系。 </p>
        //     </li>
        //     <li>
        //         <p class="m_bold">3、注意事项</p>
        //         <p>服务内容如有调整或变更以公告为准，详询财富顾问或<span class="blue call">400-167-8888</span>。</p>
        //     </li>
        // </ul>`
        // }

        // $(_pageId + " .serverInfo").html(htmlstr);
        
        
    }
    //线下沙龙跳转
    function exclusiveJumpUrl(data){
        let mail_url = data.link_url;
        
        if(data.link_type){
            appUtils.setSStorageInfo("service_type",service_type);
        } else{
            appUtils.setSStorageInfo("service_type",'2');
        }
        if(data.link_type == '1'){
            //内链
            if (mail_url.indexOf("?") > -1){
                var skip_url = mail_url.split("?")[0];
                var parameter = mail_url.split("?")[1];
                var parameter_arr = parameter.split("&"); //各个参数放到数组里
                var mailInfo = {};//url的参数信息
                for (var i = 0; i < parameter_arr.length; i++) {
                    num = parameter_arr[i].indexOf("=");
                    if (num > 0) {
                        let name = parameter_arr[i].substring(0, num);
                        value = parameter_arr[i].substr(num + 1);
                        mailInfo[name] = value;
                    }
                }
                appUtils.pageInit(_page_code, skip_url, mailInfo);
            }else{
                appUtils.pageInit(_page_code, mail_url,{});
            }
        }else if(data.link_type == '2'){
            //外链
            appUtils.pageInit(_page_code, "guide/advertisement", {
                "url": mail_url
            });
        }else if(data.link_type == '3'){
            //小程序
            tools.jump_applet(data.applet_url);
        }
    }
    //绑定事件
    function bindPageEvent() {
        //线下沙龙顶图跳转
        appUtils.bindEvent($(_pageId + " .cardImgTop"), function () {
            let data = {
                link_type:$(this).attr("link_type"),
                applet_url:$(this).attr("applet_url"),
                link_url:$(this).attr("link_url")
            }
            //根据参数进行对应跳转
            exclusiveJumpUrl(data);
        });
        //往期回顾跳转
        appUtils.preBindEvent($(_pageId + " .imgList"), "ul", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let data = {
                link_type:$(this).attr("link_type"),
                applet_url:$(this).attr("applet_url"),
                link_url:$(this).attr("link_url")
            }
            tools.recordEventData('1','previousReview','往期回顾');
            //根据参数进行对应跳转
            exclusiveJumpUrl(data);

        }, 'click');
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //选择地址
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        });
        //顶部切换
        appUtils.preBindEvent($(_pageId + " .chooseList"), "ul", function (e) {
            tools.recordEventData('1','chooseList'+$(this).attr('pageCardIndex'),$(this).attr('clickName'));
            e.stopPropagation();
            e.preventDefault();
            $(_pageId + " .serverInfo").html("");
            $(_pageId + " .chooseList ul").removeClass("active");
            $(this).addClass("active");
            pageCardIndex = $(this).attr('pageCardIndex');

            // setBanner();
            btn_flag = true;
            $(_pageId + " .isShowCard").hide();
            $(_pageId + " .layer_center .layer_btn").removeClass("no_active");
            service_type = pageCardIndex*1 + 1 + ''
            exclusiveActivities(service_type);
            highVersion_exclusiveEnjoyment.bindPageEvent();
        }, 'click');
        // //选择日期
        // appUtils.preBindEvent($(_pageId + " .day_detailsList"), "span", function (e) {
        //     e.stopPropagation();
        //     e.preventDefault();
        //     $(_pageId + " .day_detailsList span").removeClass("active");
        //     $(this).addClass("active");
        //     let chooseId = '';
        //     chooseId = pageCardIndex == '1' ? '.face' : '.product'
        //     $(_pageId + chooseId + " .day").text($(this).text());
        // }, 'click');
        // //选择时段
        // appUtils.preBindEvent($(_pageId + " .time_detailsList"), "span", function (e) {
        //     e.stopPropagation();
        //     e.preventDefault();
        //     $(_pageId + " .time_detailsList span").removeClass("active");
        //     $(this).addClass("active");
        //     let chooseId = '';
        //     chooseId = pageCardIndex == '1' ? '.face' : '.product'
        //     $(_pageId + chooseId + " .time").text($(this).text());
        // }, 'click');

        // appUtils.bindEvent($(_pageId + " .face .choose_time, " + _pageId + " .product .choose_time"), function (e) {
        //     e.preventDefault();
        //     e.stopPropagation();
            
        //     // 判断是哪个选择器被点击
        //     const isFaceTime = $(this).closest('.face').length > 0;
        //     const containerClass = isFaceTime ? '.face' : '.product';
            
        //     // 如果数据已加载，直接初始化选择器
        //     if (workdayList && workdayList.length > 0) {
        //         queryBadCredit(containerClass);
        //     } else {
        //         // 否则先获取数据
        //         workday(containerClass);
        //     }
        // });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "login_userRegistered";
            tools.saveAlbum(_page_code,param)
        });
        //点击banner
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            $(_pageId + " #live_address").val('');
            var service_id = $(this).attr("service_id");
            tools.recordEventData('1','banner_list' + service_id,'活动轮播图');
            $(_pageId + " .choose_dig").hide();
            $(_pageId + " .choose_time img").attr("src","./images/highEnd/right_black.png");
            $(_pageId + " .layer_center .layer_btn").removeClass("no_active");
            btn_flag = true;
            //判断尊享服务活动
            exclusiveActivities(service_id);

        }, 'click');
        //关闭
        appUtils.bindEvent($(_pageId + " .layer_close"), function () {
            if(pageCardIndex == '0'){   // 礼品回馈
                tools.recordEventData('1','layer_close'+pageCardIndex,'关闭礼品回馈');
                $(_pageId + " .gift").hide();
            }else if(pageCardIndex == '1'){//财顾面对面
                tools.recordEventData('1','layer_close'+pageCardIndex,'关闭财顾面对面');
                $(_pageId + " .face").hide();
            }else if(pageCardIndex == '2'){//定制产品
                tools.recordEventData('1','layer_close'+pageCardIndex,'关闭定制产品');
                $(_pageId + " .product").hide();
            }else if(pageCardIndex == '3'){//线下沙龙
                tools.recordEventData('1','layer_close'+pageCardIndex,'关闭线下沙龙');
                $(_pageId + " .salon").hide();
            }
            
        });
        //联系客服
        appUtils.bindEvent($(_pageId + " .call"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = '4001678888';
            param["callType"] = "0";
            // console.log(param)
            require("external").callMessage(param);
        });
        //提交信息
        appUtils.bindEvent($(_pageId + " .layer_btn"), function () {

            if(!btn_flag) return;
            var realName = $(_pageId + " #realName").val();  // 姓名
            var phone = $(_pageId + " #phone").val();  // 手机号
            var phoneNum = $(_pageId + " #phoneNum").val();  // 手机号
            var live_address = $(_pageId + " #live_address").val()
            var address =  $(_pageId + " #address").val()

            if(pageCardIndex == '0'){   // 礼品回馈
                if (validatorUtil.isEmpty(realName)) {
                    layerUtils.iMsg(-1, "请输入姓名");
                    return;
                }
                if (validatorUtil.isEmpty(phone)) {
                    layerUtils.iMsg(-1, "请输入手机号");
                    return;
                }
                if (!validatorUtil.isMobile(phone)) {
                    layerUtils.iMsg(-1, "请输入正确手机号码");
                    return;
                }
                if (validatorUtil.isEmpty(live_address)) {
                    layerUtils.iMsg(-1, "请选择地址");
                    return;
                }
                if (validatorUtil.isEmpty(address)) {
                    layerUtils.iMsg(-1, "请输入详情地址");
                    return;
                }
                if(realName.length > 10){
                    layerUtils.iMsg(-1, "姓名不能超过10个字");
                    return;
                }
                if(address.length > 32){
                    layerUtils.iMsg(-1, "详情地址不能超过32个字");
                    return;
                }
                let addressTest = /^[\u4e00-\u9fa5a-zA-Z0-9\s.,!?，。！？、；：“”‘’（）【】￥\$%\^&*+=_~`'"\-\\|]+$/i;
                if(!addressTest.test(address)){
                    layerUtils.iMsg(-1, "您可能输入表情等特殊字符，请修改");
                    return;
                }
            }else if(pageCardIndex == '3'){//线下沙龙
                // if (validatorUtil.isEmpty(phoneNum)) {
                //     layerUtils.iMsg(-1, "请输入手机号");
                //     return;
                // }
                // if (!validatorUtil.isMobile(phoneNum)) {
                //     layerUtils.iMsg(-1, "请输入正确手机号码");
                //     return;
                // }
            }


            var param = {
                service_id : serviceInformation.service_id,
                service_type : serviceInformation.service_type,
                // phone : phone ? phone : phoneNum,
                // receiver_name : realName,
                // post_address :  live_address.replace(/\s+/g, '') + address,
                // appointment_date : appointment_date ? appointment_date : show_date,
                // appointment_time : appointment_time ? appointment_time : show_time
            }
            if(pageCardIndex == '0'){
                param.phone = phone ? phone : phoneNum;
                param.receiver_name = realName;
                param.post_address =  live_address.replace(/\s+/g, '') + address;
            }
            if(pageCardIndex == '1' || pageCardIndex == '2'){
                param.appointment_date = appointment_date ? appointment_date : show_date;
                param.appointment_time = appointment_time ? appointment_time : show_time;
            }
            if(pageCardIndex == '3'){
                param.phone = phoneNum;
            }
            if(pageCardIndex == '2'){
                param.appointment_date = '';
                param.appointment_time = '';
                param.invest_amt = $(_pageId + " .invest_amt").val();
                param.invest_duration = $(_pageId + " .invest_duration").val() ;
                param.rate = $(_pageId + " .rate").val() ;
                param.risk = $(_pageId + " .choose_risk").text();
                if(!param.invest_amt) return layerUtils.iAlert('请输入投资金额！');
                //检验投资金额是否为数字
                if(!tools.isNumber(param.invest_amt)) return layerUtils.iAlert('请输入正确的投资金额！');
                if((param.invest_amt*1) <= 0) return layerUtils.iAlert('投资金额不能小于0元');
                if(param.invest_duration && !tools.isNumber(param.invest_duration)) return layerUtils.iAlert('请输入正确的投资时长！');
                if(param.rate && !tools.isNumber(param.rate)) return layerUtils.iAlert('请输入正确的年化收益率！');
                if(param.risk == '请选择▼') param.risk = '';
                // if(!param.invest_duration) return layerUtils.iAlert('请填写投资时长');
                // if(!param.rate) return layerUtils.iAlert('请填写年化收益率');
                // if(param.risk == '请选择▼') return layerUtils.iAlert('请选择风险偏好');
            }
            // return console.log(param)
            //提交信息
            service.reqFun181003(param, function (datas){
                if (datas.error_no == 0) {
                    layerUtils.iAlert('提交成功，财富顾问会尽快联系您')
                    btn_flag = false;//按钮不能点击
                    $(_pageId + " .layer_center .layer_btn").addClass("no_active");
                    $(_pageId + " .cardRemark").show();
                    
                    if(pageCardIndex == '0'){   // 礼品回馈
                        tools.recordEventData('1','layer_btn'+pageCardIndex,'提交礼品回馈信息');
                        $(_pageId + " .gift").hide();
                    }else if(pageCardIndex == '1'){//财顾面对面
                        $(_pageId + " .fristCard_box").css("padding-bottom", "0.1rem");
                        $(_pageId + " .layer_center .face_date").css("pointer-events", "none");
                        tools.recordEventData('1','layer_btn'+pageCardIndex,'提交财顾面对面信息');
                        $(_pageId + " .layer_center .layer_btn").text("已预约");
                        // $(_pageId + " .face").hide();
                    }else if(pageCardIndex == '2'){//定制产品
                        // $(_pageId + " .invest_amt").val(param.invest_amt);
                        $(_pageId + " .rate").val(param.rate ? param.rate : '');
                        $(_pageId + " .invest_duration").val(param.invest_duration ? param.invest_duration : '');
                        $(_pageId + " .choose_risk").text(param.risk == '' ? '--' : param.risk);
                        $(_pageId + " .choose_risk").css("color", "#000");
                        $(_pageId + " .layer_center .choose_risk").css("pointer-events", "none");
                        $(_pageId + " .secondCard_box").css("padding-bottom", "0.1rem");
                        tools.recordEventData('1','layer_btn'+pageCardIndex,'提交定制产品信息');
                        $(_pageId + " .secondCard input").prop("disabled", true);
                        $(_pageId + " .layer_center .layer_btn").text("已提交");
                        // $(_pageId + " .product").hide();
                    }else if(pageCardIndex == '3'){//线下沙龙
                        tools.recordEventData('1','layer_btn'+pageCardIndex,'提交线下沙龙信息');
                        $(_pageId + " .layer_center .layer_btn").text("已预约");
                        // $(_pageId + " .salon").hide();
                    }
                }

            })

        }, 'click');
    }
    
    //初始化风险偏好
    function queryRiskPreference(){
        let dataArr = [
            { id: "5", value: "低风险", year: "低风险" },
            { id: "10", value: "中低风险", year: "中低风险" },
            { id: "15", value: "中风险", year: "中风险" },
            { id: "20", value: "中高风险", year: "中高风险" },
            { id: "25", value: "高风险", year: "高风险" },
        ]
        tools.mobileSelect({
            trigger: _pageId + " .choose_risk",
            title: "请选择风险偏好",
            // position: $(_pageId + " #joinTimeData").attr("data-position"),
            dataArr: dataArr,
            callback: function (data) {
                $(_pageId + " .choose_risk").css('color', '#000')
                // $(_pageId + " .choose_risk").html(`<span class="m_font_size18">${data[0].year}</span> <span>▼</span>`);
                // $(_pageId + " #planJoinTime").attr("data-id", data[0].id);
                // $(_pageId + " #planJoinTime").attr("data-value", data[0].year);
            }
        });
    }
    //查询未来n个工作日日期列表
    function workday() {
        service.reqFun181002({}, function (datas) {
            if (datas.error_no == 0) {
                try {
                    let workdayListStr = datas.results[0].workdayList;
                    workdayList = JSON.parse(workdayListStr.replace(/^"+|"+$/g, ''));
                    // console.log('获取的工作日列表:', workdayList);
                    //初始化选中日期
                    appointment_date = workdayList[0];
                    appointment_time = '下午'
                    // 数据获取成功后初始化选择器
                    queryBadCredit();
                    //渲染活动信息
                    exclusiveActivities(service_type);
                } catch (e) {
                    console.error('解析工作日列表失败:', e);
                    layerUtils.iMsg(-1, "获取工作日数据失败");
                }
            } else {
                console.error('获取工作日列表失败:', datas.error_info);
                layerUtils.iMsg(-1, datas.error_info || "获取工作日数据失败");
            }
        });
    }
   
           


    //获取尊享服务内容
    function exclusiveActivities(service_type) {
        $(_pageId + " .layer_center .full_name").text(`${firstName}${gender}，诚邀您参加`);
        service.reqFun181001({service_type:service_type}, function (datas) {
            if (datas.error_no == 0) {
                var service_state = datas.results[0].service_state;// 活动状态:1:进行中 2:未开始 3:已结束
                serviceInformation = datas.results[0];
                let active_service_type = serviceInformation.service_type;
                if (service_state == '2' && active_service_type != '4') {
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                } else if (service_state == '3' && active_service_type != '4') {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                }
                // 设置已选择的日期和时间
                if(serviceInformation.commit_info){
                    show_date = serviceInformation.commit_info.appointment_date;
                    show_time = serviceInformation.commit_info.appointment_time;
                }
                //是否展示卡片顶图
                if(serviceInformation.is_show_img == '1'){
                    $(_pageId + " .cardImgTop").attr("src",global.oss_url + serviceInformation.img_url);
                    $(_pageId + " .cardImgTop").attr("link_type",serviceInformation.link_type);
                    $(_pageId + " .cardImgTop").attr("link_url",serviceInformation.link_url)
                    $(_pageId + " .cardImgTop").attr("applet_url",serviceInformation.applet_url)
                    $(_pageId + " .cardImgTop").show();
                }else{
                    $(_pageId + " .cardImgTop").hide();
                }
                if(serviceInformation.state == '1'){//已提交                 
                    $(_pageId + " .layer_center .layer_btn").addClass("no_active");
                    $(_pageId + " .cardRemark").show();
                    btn_flag = false;//按钮不能点击
                     //根据 类型展示对应数据跟弹窗
                    // if(pageCardIndex == '0'){   // 礼品回馈
                        // $(_pageId + " .gift").show();
                        // // 设置表单字段为只读
                        // $(_pageId + " .layer_center #realName").prop("readonly", true);
                        // $(_pageId + " .layer_center #phone").prop("readonly", true);
                        // $(_pageId + " .layer_center #address").prop("readonly", true);                     
                        // $(_pageId + " .layer_center .layer_btn").text("已确认");
                        // $(_pageId + " .layer_center #realName").val(serviceInformation.commit_info.receiver_name);
                        // $(_pageId + " .layer_center #phone").val(serviceInformation.commit_info.phone);
                        // $(_pageId + " .layer_center #live_address_frame").hide();
                        // $(_pageId + " .layer_center #address").val(serviceInformation.commit_info.post_address);
                    // }else 
                    if(pageCardIndex == '1'){//财顾面对面
                        if(!service_state)  {
                            $(_pageId + " .fristCard").hide();
                            return layerUtils.iMsg(-1, "活动尚未开始！", 2);
                        }
                        $(_pageId + " .fristCard").show();
                        $(_pageId + " .fristCard_box").css("padding-bottom", "0.1rem");
                        $(_pageId + " .layer_center .layer_btn").text("已预约");  
                        $(_pageId + " .layer_center .face_date").css("pointer-events", "none");
                        // $(_pageId + " .layer_center .face_time img").css("opacity", "0.5");   
                        // $(_pageId + " .layer_center .layer_btn").css("background-color", "#ccc !important;");              
                        $(_pageId + " .face_date").text(
                            `${show_date.substring(4, 6)}-${show_date.substring(6, 8)} ${show_time}`
                        );
                    }else if(pageCardIndex == '2'){//定制产品
                        if(!service_state){
                            $(_pageId + " .secondCard").hide();
                            return layerUtils.iMsg(-1, "活动尚未开始！", 2);
                        } 
                        let commit_info = serviceInformation.commit_info;
                        $(_pageId + " .choose_risk").css("color", "#000");
                        $(_pageId + " .secondCard").show();
                        $(_pageId + " .layer_center .layer_btn").text("已提交");
                        $(_pageId + " .layer_center .choose_risk").css("pointer-events", "none");
                        $(_pageId + " .invest_amt").val(commit_info.invest_amt);
                        $(_pageId + " .invest_duration").val(commit_info.invest_duration ? commit_info.invest_duration : '');
                        $(_pageId + " .rate").val(commit_info.rate ? commit_info.rate : '');
                        $(_pageId + " .choose_risk").text(commit_info.risk == '' ? '--' : commit_info.risk);
                        $(_pageId + " .secondCard_box").css("padding-bottom", "0.1rem")
                        $(_pageId + " .secondCard input").prop("disabled", true);
                    }else if(pageCardIndex == '3'){//线下沙龙
                        $(_pageId + " .thirdCard").show();
                        if(service_state == '2'){
                            $(_pageId + " .layer_center .layer_btn").addClass("no_active");
                            $(_pageId + " .cardRemark").hide();
                            btn_flag = false;//按钮不能点击
                            $(_pageId + " .layer_center .layer_btn").text("未开始");
                        }else if(service_state == '3'){
                            $(_pageId + " .layer_center .layer_btn").addClass("no_active");
                            $(_pageId + " .cardRemark").hide();
                            btn_flag = false;//按钮不能点击
                            $(_pageId + " .layer_center .layer_btn").text("已结束");
                        }else{
                            // $(_pageId + " .layer_center .layer_btn").removeClass("no_active");
                            // $(_pageId + " .cardRemark").hide();
                            // btn_flag = true;//按钮能点击
                            // $(_pageId + " .layer_center .layer_btn").text("立即预约");
                            $(_pageId + " .layer_center .layer_btn").text("已预约");
                            // $(_pageId + " .layer_center #phoneNum").prop("readonly", true);
                        }
                        //是否展示往期记录
                        if(serviceInformation.pastActPictureList && serviceInformation.pastActPictureList.length){
                            $(_pageId + " .thirdCard_banner").show();
                            //渲染往期回顾图片
                            setImageList(serviceInformation.pastActPictureList);
                        }else{
                            $(_pageId + " .thirdCard_banner").hide();
                        }
                        
                        $(_pageId + " .service_theme").text(serviceInformation.service_theme);
                        $(_pageId + " .prompt_time").text(serviceInformation.prompt_time);
                        $(_pageId + " .address").text(serviceInformation.address);
                    }
                }else{//未提交
                    $(_pageId + " .cardRemark").hide();
                    $(_pageId + " .layer_center .layer_btn").removeClass("no_active");
                    //根据 类型展示对应数据跟弹窗
                    // if(pageCardIndex == '0'){   // 礼品回馈
                    //     $(_pageId + " .gift").show();
                    //     show_date = null;
                    //     show_time = null;
                    //     let str = `${firstName}${gender}`
                    //     $(_pageId + " .layer_center #realName").val(str);
                    //     $(_pageId + " .layer_center #realName").prop("readonly", false);
                    //     $(_pageId + " .layer_center #phone").prop("readonly", false);
                    //     $(_pageId + " .layer_center #address").prop("readonly", false);                     
                    //     $(_pageId + " .layer_center #phone").val("");
                    //     $(_pageId + " .layer_center #live_address_frame").show();
                    //     $(_pageId + " .layer_center #address").val("");
                    //     $(_pageId + " .layer_center .layer_btn").text("确认");
                    // }else 
                    if(pageCardIndex == '1'){//财顾面对面
                        if(!service_state){
                            $(_pageId + " .fristCard").hide();
                            return layerUtils.iMsg(-1, "活动尚未开始！", 2);
                        } 
                        $(_pageId + " .fristCard_box").css("padding-bottom", "0.2rem");
                        $(_pageId + " .fristCard").show();
                        
                        $(_pageId + " .layer_center .face_date").css("pointer-events", "auto");
                        // $(_pageId + " .face").show();
                        $(_pageId + " .layer_center .layer_btn").text("立即预约");

                        //默认展示下一个工作日的上午下午
                        const now = new Date();
                        const currentHour = now.getHours();
                        const isMorning = currentHour < 12;
                        
                        const defaultDateIndex = 0;
                        const defaultTimeIndex = isMorning ? 0 : 1;
                        
                        let show_date_no = workdayList[defaultDateIndex];
                        show_time = isMorning ? '上午' : '下午';
                        $(_pageId + " .face_date").text(
                            `${show_date_no.substring(4, 6)}-${show_date_no.substring(6, 8)} ${show_time}`
                        );
                        

                    }else if(pageCardIndex == '2'){//定制产品
                        if(!service_state) {
                            $(_pageId + " .secondCard").hide();
                            return layerUtils.iMsg(-1, "活动尚未开始！", 2);
                        }
                        $(_pageId + " .choose_risk").css("color", "#888");
                        $(_pageId + " .secondCard_box").css("padding-bottom", "0.2rem");
                        $(_pageId + " .secondCard").show();
                        $(_pageId + " .layer_center .layer_btn").text("提 交");
                        $(_pageId + " .layer_center .choose_risk").css("pointer-events", "auto");
                        $(_pageId + " .secondCard input").prop("disabled", false);
                        $(_pageId + " .invest_amt").val('');
                        $(_pageId + " .invest_duration").val('');
                        $(_pageId + " .rate").val('');
                        $(_pageId + " .choose_risk").text('请选择▼');
                    }else if(pageCardIndex == '3'){//线下沙龙
                        if(serviceInformation.service_state){
                            $(_pageId + " .thirdCard").show();
                        }else{
                            $(_pageId + " .thirdCard").hide();
                        }
                        $(_pageId + " .service_theme").text(serviceInformation.service_theme);
                        $(_pageId + " .prompt_time").text(serviceInformation.prompt_time);
                        $(_pageId + " .address").text(serviceInformation.address);
                        if(service_state == '2'){
                            $(_pageId + " .layer_center .layer_btn").addClass("no_active");
                            $(_pageId + " .cardRemark").hide();
                            btn_flag = false;//按钮不能点击
                            $(_pageId + " .layer_center .layer_btn").text("未开始");
                        }else if(service_state == '3'){
                            $(_pageId + " .layer_center .layer_btn").addClass("no_active");
                            $(_pageId + " .cardRemark").hide();
                            btn_flag = false;//按钮不能点击
                            $(_pageId + " .layer_center .layer_btn").text("已结束");
                        }else{
                            $(_pageId + " .layer_center .layer_btn").removeClass("no_active");
                            $(_pageId + " .cardRemark").hide();
                            btn_flag = true;//按钮能点击
                            $(_pageId + " .layer_center .layer_btn").text("立即预约");
                        }
                        //是否展示往期记录
                        if(serviceInformation.pastActPictureList && serviceInformation.pastActPictureList.length){
                            $(_pageId + " .thirdCard_banner").show();
                            //渲染往期回顾图片
                            setImageList(serviceInformation.pastActPictureList);
                        }else{
                            $(_pageId + " .thirdCard_banner").hide();
                        }
                        
                    }
                }
                

            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }
    function setImageList(list){
        console.log(list)
        let html = '';
        for(let i = 0; i < list.length; i++){
            let url = global.oss_url + list[i].img_url;
            if(list[i].is_show_img == '1'){
                html += `<ul style="" link_url="${list[i].link_url}" link_type="${list[i].link_type}" applet_url=${list[i].applet_url}  class="swiper-slide"><img style="border-radius:0.1rem" src="${url}" alt=""></ul>`
            }
        }
        $(_pageId + ' .imgList').html(html);
    }
    function destroy() {
        $(".mobileSelect").remove();
        $(_pageId + " .isShowCard").hide();
        $(_pageId + " .cardImgTop").hide();
        if(pageCardIndex == '0'){   // 礼品回馈
            tools.recordEventData('1','layer_close'+pageCardIndex,'关闭礼品回馈');
            $(_pageId + " .gift").hide();
        }else if(pageCardIndex == '1'){//财顾面对面
            tools.recordEventData('1','layer_close'+pageCardIndex,'关闭财顾面对面');
            $(_pageId + " .face").hide();
        }else if(pageCardIndex == '2'){//定制产品
            tools.recordEventData('1','layer_close'+pageCardIndex,'关闭定制产品');
            $(_pageId + " .product").hide();
        }else if(pageCardIndex == '3'){//线下沙龙
            tools.recordEventData('1','layer_close'+pageCardIndex,'关闭线下沙龙');
            $(_pageId + " .salon").hide();
        }
        tools.recordEventData('4','destroy','页面销毁');
        pageCardIndex = '1';
        $(_pageId + ' #scroller_index2').html('');
        $(_pageId + " .serverInfo").html("");
        $(_pageId + " .layer_center #realName").val("");
        $(_pageId + " .layer_center #phone").val("");
        $(_pageId + " .layer_center #address").val("");
        $(_pageId + " .layer_center #phoneNum").val("");
        btn_flag = true;
        $(_pageId + " input").prop("readonly", false);
        $(_pageId + " .face_time").css({
            "pointer-events": "auto",
            "opacity": "1"
        });
        $(_pageId + " .product_time").css({
            "pointer-events": "auto",
            "opacity": "1"
        });
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var highVersion_exclusiveEnjoyment = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_exclusiveEnjoyment;
});
