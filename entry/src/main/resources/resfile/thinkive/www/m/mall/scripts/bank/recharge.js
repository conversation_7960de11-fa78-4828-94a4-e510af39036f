define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_recharge ";
    var gconfig = require("gconfig");
    var ut = require("../common/userUtil");
    var _pageCode = "bank/recharge";
    var validatorUtil = require("validatorUtil");
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var productInfo;// 产品信息
    var single_limit; //单笔限额
    var is_signed;
    var sms_mobile = require("../common/sms_mobile");
    var sign_mes_code;
    var total_amt; // 银行持有总资产
    var bankElectornReservedMobile; // 电子银行预留手偶几号
    var bank_channel_name; //电子银行名称
    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " #inputspanid span").text("请输入充值金额");
        $(_pageId + " #money").val("");
        getAccountInfo();
        getSignInfo();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //汇款充值
        appUtils.bindEvent($(_pageId + " #hkcz"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            appUtils.pageInit(_pageCode, "bank/payRecharge");
        });

        //产品详情
        // appUtils.bindEvent($(_pageId + " .product_content"), function () {
        //     appUtils.pageInit(_pageCode, "bank/bankDetail");
        // });

        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "bank_recharge";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                if ($(_pageId + " #money").val().replace(/,/g, "") <= 0 || !$(_pageId + " #money").val().replace(/,/g, "")) {
                    layerUtils.iAlert("请输入充值金额");
                    return;
                }
                if (is_signed == "1") { // 签约发短信
                    var param = {
                        "bank_channel_code": productInfo.bank_channel_code,
                        "mobile_phone": bankElectornReservedMobile, //用于公共方法展示，非接口必传参数
                        "bank_electron_name": bank_channel_name
                    };
                    sms_mobile.sendPhoneCodeBankProto(param, function (data) {
                        if (data.results && data.results.length > 0) {
                            sign_mes_code = data.results[0].shortMessgRlvncCd
                        }
                    });
                } else { //不需要签约
                    var param = {
                        "mobile_phone": bankElectornReservedMobile,
                        "type": common.sms_type.bankRecharge,
                        "send_type": "0",
                        "bank_electron_name": bank_channel_name
                    }
                    sms_mobile.sendPhoneCode(param);
                }
            }
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if ($(_pageId + " #money").val().replace(/,/g, "") <= 0 || !$(_pageId + " #money").val().replace(/,/g, "")) {
                layerUtils.iAlert("请输入充值金额");
                return;
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            trade();
        });


    }

    function trade() {
        //交易调用
        var trans_amt = $(_pageId + " #money").val();
        trans_amt = trans_amt.replace(/,/g, "");
        if (is_signed == "0") { // 不需要签约
            var param = {
                trans_amt: trans_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                sms_code: $(_pageId + " #verificationCode").val(),
                sms_mobile: bankElectornReservedMobile,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            service.reqFun9151004(param, function (data) {
                callback(data);
            })
        } else {
            var param = {
                trans_amt: trans_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                message_code: $(_pageId + " #verificationCode").val(),
                sign_mes_code: sign_mes_code,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            service.reqFun151004(param, function (data) {
                callback(data);
            })
        }

        var callback = function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/rechargeResult", {
                trans_serno: trans_serno
            });
        }
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #money").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }
                if (single_limit && single_limit > 0 && parseFloat(single_limit) < parseFloat(curVal)) {
                    guanbi();
                    //点击汇款充值
                    layerUtils.iConfirm("充值超过限额,请汇款充值", function () {
                        $(_pageId + " #money").val("");
                        $(_pageId + " #inputspanid span").text("");
                    }, function () {
                        appUtils.pageInit(_pageCode, "bank/payRecharge");
                    }, "取消", "确定");
                }
            }
        })
    }

    //查询电子银行信息
    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-2);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                $(_pageId + " .bankElectronIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                $(_pageId + " .bankIcon img").attr("src", "./images/bank_" + results.bd_bank_code + ".png");
                // acct_no 电子银行    bank_acct_no 绑定银行
                bank_channel_name = results.bank_channel_name;
                $(_pageId + " .bankInfo").find("p").eq(0).html(results.bd_bank_name + "(尾号" + results.bank_acct_no.substr(-4) + ")");
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "(尾号" + results.acct_no.substr(-4) + ")");
                $(_pageId + " .avail_amt").text(tools.fmoney(results.avail_amt) + "元");
                bankElectornReservedMobile = results.mobile_phone;
                tools.getBankPdf({
                    agreement_type: "2",
                    bank_channel_code: productInfo.bank_channel_code,
                    acct_no: results.bank_acct_no,
                    bank_code: results.bd_bank_code,
                }); //获取协议
                getBankLimitInfo(results.bd_bank_code);
                getMyAsset();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //查询用户持有该银行总资产
    function getMyAsset() {
        service.reqFun151106({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                total_amt = results.total_amt;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 查询签约信息
    function getSignInfo() {
        service.reqFun151115({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                is_signed = results.is_signed; // 0-不需要；1需要签约
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getBankLimitInfo(bank_code) {
        $(_pageId + " .limit").hide();
        $(_pageId + " #pop_view").css("visibility", "hidden");
        $(_pageId + " #big_show_bank").html("");
        var param = {
            "bank_channel_code": productInfo.bank_channel_code,
            "bank_code": bank_code,
        };
        service.reqFun151112(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {
                $(_pageId + " .limit").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    single_limit = result.single_limit;
                    var day_limit = result.day_limit;
                    var month_limit = result.month_limit;
                    if (parseFloat(single_limit) < 0) {
                        $(_pageId + " .single_limit").html("不限");
                    } else {
                        $(_pageId + " .single_limit").html(single_limit + "元");
                    }
                    if (parseFloat(day_limit) < 0) {
                        $(_pageId + " .day_limit").html("不限");
                    } else {
                        $(_pageId + " .day_limit").html(day_limit + "元");
                    }
                    if (month_limit && parseFloat(month_limit) < 0) {
                        $(_pageId + " .month_limit_str").html("，月累计不限").show();
                    } else if (month_limit) {
                        $(_pageId + " .month_limit_str").html("，月累计 " + month_limit + "元").show();
                    } else {
                        $(_pageId + " .month_limit_str").hide();
                    }
                } else {
                    $(_pageId + " .single_limit").html("--");
                    $(_pageId + " .day_limit").html("--");
                    $(_pageId + " .month_limit").html("--");
                }
            } else {
                $(_pageId + " .single_limit").html("--");
                $(_pageId + " .day_limit").html("--");
                $(_pageId + " .month_limit").html("--");
                layerUtils.iMsg(-1, error_info);
            }
        });
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        $(_pageId + " #inputspanid span").text("请输入充值金额").css({color: "#999999"}).removeClass("active");
        $(_pageId + " #money").text("");
        $(_pageId + " .bank_electron_info").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " .single_limit").html("--");
        $(_pageId + " .day_limit").html("--");
        $(_pageId + " .month_limit").html("--");
        $(_pageId + " .avail_amt").text("--");
        $(_pageId + " .bankElectronIcon img").attr("src", "");
        monkeywords.destroy();
        monkeywords.flag = 0;
        sms_mobile.destroy();
        $(_pageId + " .month_limit_str").hide();
        guanbi();
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
