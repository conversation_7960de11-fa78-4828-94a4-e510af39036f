// 我的资产
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#account_myAssets ";
    var chartsUtils = require("chartsUtils");
    var bankfundAssetsPer, mfundAssetsPer, prifundAssetsPer, pubfundAssetsPer, fromFundAssetsPer;
    var bankDepositColor = "";
    var jjInclusiveColor = "";
    var jjHighEndColor = "";
    var thfundColor = "";
    var tools = require("../common/tools");
    var _pageUrl = "account/myAssets";
    var calculator = require("../common/calculator");

    function init() {
        //查询个人资产信息,初始化颜色值
        thfundColor = "#E5443C";
        bankDepositColor = "#F3D962";
        jjInclusiveColor = "#EF4CDE";
        jjHighEndColor = "#FF7A11";
        //获取客户资金数据
        getUserInfo();
    }

    function bindPageEvent() {
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageUrl)
        });
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //晋金宝
        appUtils.bindEvent($(_pageId + " #thfund"), function () {
            appUtils.pageInit(_pageUrl, "thfund/myProfit", {});
        });
        //晋金普惠
        appUtils.bindEvent($(_pageId + " #jjInclusive"), function () {
            appUtils.pageInit(_pageUrl, "inclusive/hold");
        });
        //银行存款
        appUtils.bindEvent($(_pageId + " #bankDeposit"), function () {
            appUtils.pageInit(_pageUrl, "bank/bankDeposit");
        });
        //晋金高端
        appUtils.bindEvent($(_pageId + " #jjHighEnd"), function () {
            appUtils.pageInit(_pageUrl, "highEnd/hold");
        });
        //在途资金
        appUtils.bindEvent($(_pageId + " #fundsInTransit"), function () {
            appUtils.pageInit(_pageUrl, "account/theWayRecord");
        });

    }

    function destroy() {
        $(_pageId + ' #container3').html('');
        $(_pageId + " .total_assets").children("span").children("em").text("");
        $(_pageId + " #thfund").children("span").children("em").text("");
        $(_pageId + " #jjInclusive").children("span").children("em").text("");
        $(_pageId + " #bankDeposit").children("span").children("em").text("");
        $(_pageId + " #jjHighEnd").children("span").children("em").text("");
        $(_pageId + " #bankDeposit").parents("p").hide();
        $(_pageId + " #bankDeposit_per").parents("p").hide();

        // $(_pageId + " #fundsInTransit").children("span").children("em").text("");
    }

    /****************************自定义方法写在最后面***********************************/
    //自定义返回方法
    function pageBack() {
        appUtils.pageBack();
    }

    //根据资金数据绘制饼状图
    function circularChart(object) {
        var colors;
        var data;
        if (object.flag == "Y") {
            colors = [thfundColor, bankDepositColor, jjInclusiveColor, jjHighEndColor];
            data = [['晋金宝', mfundAssetsPer], ['银行存款', bankfundAssetsPer], ['活期+定期理财', pubfundAssetsPer], ['高端理财', prifundAssetsPer]];
        } else {
            colors = [thfundColor, jjInclusiveColor, jjHighEndColor];
            data = [['晋金宝', mfundAssetsPer], ['活期+定期理财', pubfundAssetsPer], ['高端理财', prifundAssetsPer]];
        }
        $(_pageId + ' #container').highcharts({
            credits: {
                text: '', //去掉水印
            },
            tooltip: {
                pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
            },
            chart: {
                backgroundColor: '#F2F6F8',
                type: 'pie',
                options3d: {
                    enabled: true,
                    alpha: 0

                }
            },
            colors: colors,
            tooltip: {
                enabled: false
            },
            plotOptions: {
                series: {
                    dataLabels: {
                        enabled: false //true：表示显示；false：表示隐藏
                    }
                }
            },
            title: {
                text: ''
            },
            subtitle: {
                text: ''
            },
            plotOptions: {
                pie: {
                    innerSize: 100,
                    depth: 0
                },
                series: {
                    dataLabels: {
                        enabled: false //true：表示显示；false：表示隐藏
                    }
                }
            },
            series: [{
                name: '占有比例',
                data: data
            }]
        });
    }

    //获取用户资金数据
    function getUserInfo() {
        //填充默认数据，防止加载速度较慢不显示数据
        $(_pageId + " .total_assets").children("span").children("em").text("");
        $(_pageId + " #thfund").children("span").children("em").text("");
        $(_pageId + " #jjInclusive").children("span").children("em").text("");
        $(_pageId + " #bankDeposit").children("span").children("em").text("");
        $(_pageId + " #jjHighEnd").children("span").children("em").text("");
        // $(_pageId + " #fundsInTransit").children("span").children("em").text("");


        //查询晋金宝资金数据
        service.reqFun101999({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var result = data.results[0];
                var totalAssets = result.totalAssets || "--";//总资产
                var bankfundAssets = result.bankfundAssets || "--";//银行总资产
                var mfundAssets = result.mfundAssets || "--";//晋金宝资产
                var prifundAssets = result.prifundAssets || "--";//私募资产
                var pubfundAssets = result.pubfundAssets || "--";//公募资金
                var flag = result.flag; //是否已开银行户  Y开  N未开
                if (flag == "Y") {
                    $(_pageId + " #bankDeposit").parents("p").show();
                    $(_pageId + " #bankDeposit_per").parents("p").show();
                } else {
                    $(_pageId + " #bankDeposit").parents("p").hide();
                    $(_pageId + " #bankDeposit_per").parents("p").hide();
                }

                if (totalAssets == "--" || bankfundAssets == "--" || mfundAssets == "--" || prifundAssets == "--" || pubfundAssets == "--") {
                    $(_pageId + " .ratio_detail").hide();
                } else {
                    $(_pageId + " .ratio_detail").show();
                    //计算比例
                    if (totalAssets != 0) {
                        bankfundAssetsPer = (bankfundAssets / totalAssets) * 100;//银行
                        mfundAssetsPer = (mfundAssets / totalAssets) * 100; // 晋金宝
                        prifundAssetsPer = (prifundAssets / totalAssets) * 100;//私募
                        pubfundAssetsPer = (pubfundAssets / totalAssets) * 100; //公募
                    } else {
                        bankfundAssetsPer = 0;
                        mfundAssetsPer = 0;
                        prifundAssetsPer = 0;
                        pubfundAssetsPer = 0;
                        jjInclusiveColor = "#CCCCCC";
                        bankDepositColor = "#CCCCCC";
                        jjHighEndColor = "#CCCCCC";
                        thfundColor = "#CCCCCC";
                    }
                    var perArrr = [bankfundAssetsPer, mfundAssetsPer, prifundAssetsPer, pubfundAssetsPer];
                    var fc = calculator.minus(100, parseFloat(bankfundAssetsPer) + parseFloat(mfundAssetsPer) + parseFloat(prifundAssetsPer) + parseFloat(pubfundAssetsPer));
                    if (fc != 0) {
                        for (var i = 0; i < perArrr.length; i++) {
                            if (perArrr[i] != 0) {
                                perArrr[i] = parseFloat(fc) + parseFloat(perArrr[i]);
                                break;
                            }
                        }
                    }
                    $(_pageId + " #bankDeposit_per").text(common.fmoney(perArrr[0]) + "%");
                    $(_pageId + " #thfund_per").text(common.fmoney(perArrr[1]) + "%");
                    $(_pageId + " #jjHighEnd_per").text(common.fmoney(perArrr[2]) + "%");
                    $(_pageId + " #jjInclusive_per").text(common.fmoney(perArrr[3]) + "%");

                    //3个比例都为0时饼状图不不显示,更改比例统一用同一种颜色
                    if (bankfundAssetsPer == 0 && mfundAssetsPer == 0 && prifundAssetsPer == 0 && pubfundAssetsPer == 0) {
                        mfundAssetsPer = 100;
                        bankfundAssetsPer = 0;
                        prifundAssetsPer = 0;
                        pubfundAssetsPer = 0;
                        fromFundAssetsPer = 0;
                    }
                    //画出饼状图
                    circularChart({flag: flag});
                }

                //格式化金额
                totalAssets = common.fmoney("" + totalAssets, 2);
                bankfundAssets = common.fmoney("" + bankfundAssets, 2);
                prifundAssets = common.fmoney("" + prifundAssets, 2);
                pubfundAssets = common.fmoney("" + pubfundAssets, 2);
                mfundAssets = common.fmoney("" + mfundAssets, 2);

                appUtils.setSStorageInfo("pubfundAssets", pubfundAssets);
                $(_pageId + " .total_assets").children("span").children("em").text(totalAssets);
                $(_pageId + " #thfund").children("span").children("em").text(mfundAssets);//晋金宝
                $(_pageId + " #bankDeposit").children("span").children("em").text(bankfundAssets);
                $(_pageId + " #jjInclusive").children("span").children("em").text(pubfundAssets);
                $(_pageId + " #jjHighEnd").children("span").children("em").text(prifundAssets);


            } else {
                $(_pageId + " #bankDeposit").parents("p").hide();
                $(_pageId + " #bankDeposit_per").parents("p").hide();
                layerUtils.iAlert(error_info);
            }
        })

    }

    var myAssets = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAssets;
});
