@charset "utf-8";
/* reset.css */
body {
    overflow-y: visible !important;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: moz-none;
    user-select: none;
    -webkit-text-size-adjust: 100% !important;
}

body * {
    box-sizing: border-box;
    -moz-box-sizing: border-box; /* Firefox */
    -webkit-box-sizing: border-box; /* Safari */
}

/** html4 reset **/
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}

fieldset, img {
    border: 0 none;
}

address, caption, cite, code, dfn, em, th, var, b, h1, h2, h3 {
    font-style: normal;
    font-weight: normal;
}

p {
    margin: 0 0 0.1rem;
}

ol, ul, li {
    list-style-type: none
}

q:before, q:after {
    content: '';
}

abbr, acronym {
    border: 0;
    font-variant: normal;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

th, td, caption {
    vertical-align: top;
    text-align: left;
}

input[type="text"],
input[type="email"],
input[type="search"],
input[type="password"],
input[type="date"],
input[type="month"],
input[type="tel"],
input[type="radio"],
input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
}

input[type="search"] {
    -webkit-appearance: textfield;
    box-sizing: content-box;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

img {
    -ms-interpolation-mode: bicubic;
    vertical-align: middle;
    font-size: 0;
}

h1 {
    font-size: 0.24rem;
}

h2 {
    font-size: 0.2rem;
}

h3 {
    font-size: 0.18rem;
}

h4 {
    font-size: 0.16rem;
}

h5 {
    font-size: 0.14rem;
}

/** html5 reset **/
header, footer, section, nav, menu, details, hgroup, figure, figcaption, article, aside {
    margin: 0;
    padding: 0;
    display: block;
}

::-moz-placeholder,
::-webkit-input-placeholder {
    color: #546374;
}

a {
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

a:hover {
    opacity: 1
}

.clear {
    clear: both;
    font-size: 0;
    height: 0;
    line-height: 0;
    overflow: hidden;
}

.clearfix:after {
    clear: both;
    content: "";
    display: block;
    font-size: 0;
    height: 0;
    visibility: hidden;
}

.clearfix {
    zoom: 1;
}

/** Body, links, basics **/
body, html {
    height: 100%;
    width: 100%;
    overflow: hidden;
    font-size: 100px;
}

body {
    font-size: 14px;
    line-height: 1.8;
    /*font-family: 'PFDin', STHeiti STXihei, Microsoft JhengHei, Microsoft YaHei, Arial;*/
    font-family: Arial;
    text-rendering: optimizeLegibility;
    color: #666666;
}

/* latin-ext */
@font-face {
    font-family: 'PFDin';
    font-weight: 400;
    unicode-range: U+30-39;
}

/* 通用样式  */
.hidden {
    display: none!important;
}

.block {
    display: block!important;
}

.inline {
    display: inline;
}

.inline-block {
    display: inline-block;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.rounded:not(.ui) {
    -moz-border-radius: 0.05rem;
    -webkit-border-radius: 0.05rem;
    border-radius: 0.05rem;
}

.circle:not(.ui) {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

/* 布局grid */
section.main.fixed {
    /* position: fixed; */
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: -moz-box;
    display: -webkit-box;
    display: box;
    -moz-box-orient: vertical;
    -webkit-box-orient: vertical;
    box-orient: vertical;
}

section.main.fixed > article {
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    box-flex: 1;
    overflow-x: hidden;
    overflow-y: scroll;
}

.ui.layout {
    display: -webkit-box;
    display: -moz-box;
    display: box;
    height: inherit !important;
}

.ui.layout.vertical {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    box-orient: vertical;
}

.ui.layout > .row-1 {
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    box-flex: 1;
}

.ui.layout > .row-2 {
    -moz-box-flex: 2;
    -webkit-box-flex: 2;
    box-flex: 2;
}

.ui.layout > .row-3 {
    -moz-box-flex: 3;
    -webkit-box-flex: 3;
    box-flex: 3;
}

.scroll {
    overflow-x: hidden;
    overflow-y: scroll;
}

/* 按钮btn */
.ui.button {
    display: inline-block;
    line-height: 0.5rem;
    font-size: 0.16rem;
    padding: 0 0.1rem;
    background: #F2F2F2;
    border: 0;
    color: rgba(0, 0, 0, 0.8);
    text-decoration: none;
}

button.ui.button:focus {
    outline: none;
}

.ui.button.rounded {
    -moz-border-radius: 0.05rem;
    -webkit-border-radius: 0.05rem;
    border-radius: 0.05rem;
}

.ui.button.border {
    border: 1px solid #E5E5E5;
}

.ui.button.block {
    display: block;
    text-align: center;
}

.ui.button.tiny {
    font-size: 0.09rem;
    line-height: 0.18rem;
}

.ui.button.small {
    font-size: 0.12rem;
    line-height: 0.3rem;
}

.ui.button.large {
    font-size: 0.18rem;
    line-height: 0.6rem;
}

.ui.button.active {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.15) inset, 0 0 6px rgba(0, 0, 0, 0.2) inset;
}

.ui.button.disabled {
    cursor: not-allowed;
    -moz-opacity: 0.4;
    opacity: 0.4;
}

.ui.button.primary {
    background: #0078E7;
    color: #FFFFFF;
}

.ui.button.success,
.ui.button.error,
.ui.button.warning,
.ui.button.secondary {
    color: #FFFFFF;
}

.ui.button.success {
    background: rgb(28, 184, 65);
}

.ui.button.error {
    background: rgb(202, 60, 60);
}

.ui.button.warning {
    background: rgb(223, 117, 20);
}

.ui.button.secondary {
    background: rgb(66, 184, 221);
}

.ui.buttons {
    margin: 0 -0.05rem;
    display: flex;
}

.ui.buttons > .ui.button {
    width: 100%;
    margin: 0 0.05rem;
}

.ui.button-group {
    display: flex;
}

.ui.button-group > .ui.button {
    width: 100%;
}

.ui.button-group.rounded > .ui.button:nth-child(1) {
    -moz-border-radius-topleft: 0.05rem;
    -webkit-border-top-left-radius: 0.05rem;
    border-top-left-radius: 0.05rem;
    -moz-border-radius-bottomleft: 0.05rem;
    -webkit-border-bottom-left-radius: 0.05rem;
    border-bottom-left-radius: 0.05rem;
}

.ui.button-group.rounded > .ui.button:nth-last-child(1) {
    -moz-border-radius-topright: 0.05rem;
    -webkit-border-top-right-radius: 0.05rem;
    border-top-right-radius: 0.05rem;
    -moz-border-radius-bottomright: 0.05rem;
    -webkit-border-bottom-right-radius: 0.05rem;
    border-bottom-right-radius: 0.05rem;
}

.ui.button-group.border > .ui.button {
    border: 1px solid #E5E5E5;
}

.ui.button-group.border .ui.button:not(:last-child) {
    border-right: 0;
}

.ui.button-list {
}

.ui.button-list.rounded > .ui.button:nth-child(1) {
    -moz-border-radius-topleft: 0.05rem;
    -webkit-border-top-left-radius: 0.05rem;
    border-top-left-radius: 0.05rem;
    -moz-border-radius-topright: 0.05rem;
    -webkit-border-top-right-radius: 0.05rem;
    border-top-right-radius: 0.05rem;
}

.ui.button-list.rounded > .ui.button:nth-last-child(1) {
    -moz-border-radius-bottomleft: 0.05rem;
    -webkit-border-bottom-left-radius: 0.05rem;
    border-bottom-left-radius: 0.05rem;
    -moz-border-radius-bottomright: 0.05rem;
    -webkit-border-bottom-right-radius: 0.05rem;
    border-bottom-right-radius: 0.05rem;
}

.ui.button-list.border > .ui.button {
    border: 1px solid #E5E5E5;
}

.ui.button-list.border .ui.button:not(:last-child) {
    border-bottom: 0;
}

/* 输入框Form */
.ui.field {
    position: relative;
}

.ui.field > .ui.input {
    background: #FFFFFF;
    height: 0.44rem;
    padding: 0.1rem;
    border: 0;
    border-bottom: 1px solid #DDDDDD;
    color: rgba(0, 0, 0, 0.8);
    font-size: 0.14rem;
    width: 100%;
    white-space: nowrap;
    -moz-transition: all 0.4s ease-in-out;
    -webkit-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}

.ui.field > .ui.dropdown {
    display: block;
    height: 0.44rem;
    line-height: 0.44rem;
    border-bottom: 1px solid #DDDDDD;
    position: relative;
    -moz-transition: all 0.4s ease-in-out;
    -webkit-transition: all 0.4s ease-in-out;
    transition: all 0.4s ease-in-out;
}

.ui.field > .ui.dropdown.active {
    border-color: #0078E7;
}

.ui.field > .ui.dropdown > strong {
    display: block;
    height: 0.44rem;
    padding-right: 0.44rem;
    position: relative;
    padding-left: 0.1rem;
    font-weight: normal;
    font-size: 0.14rem;
}

.ui.field > .ui.dropdown > strong:after {
    content: "";
    width: 0;
    height: 0;
    border: 0.06rem solid rgba(0, 0, 0, 0);
    border-top: 0.06rem solid #CCCCCC;
    position: absolute;
    right: 0.12rem;
    top: 0.2rem;
}

.ui.field > .ui.dropdown > ul {
    z-index: 100;
    position: absolute;
    width: 100%;
    top: 100%;
    background: #FFFFFF;
    box-shadow: 0 0 0.03rem #CCCCCC;
    display: none;
}

.ui.field > .ui.dropdown > ul > li {
    padding: 0 0.1rem;
    font-size: 0.14rem;
}

.ui.field > .ui.dropdown > ul > li:nth-child(odd) {
    background: #FDFDFD;
}

.ui.field > .ui.input[disabled=disabled] {
    background: #F2F2F2;
    cursor: not-allowed;
}

.ui.field > .ui.input[readonly=readonly] {
    background: #F6F6F6;
}

.ui.field > .ui.input:focus {
    outline: 0;
    border-color: #0078e7 !important;
}

.ui.field.border > .ui.input,
.ui.field.border > .ui.dropdown {
    border: 1px solid #DDDDDD;
}

.ui.field.rounded > .ui.input,
.ui.field.rounded > .ui.dropdown {
    -moz-border-radius: 0.05rem;
    -webkit-border-radius: 0.05rem;
    border-radius: 0.05rem;
}

.ui.field.transparent > .ui.input,
.ui.field.transparent > .ui.dropdown {
    background: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.ui.field.error > .ui.input,
.ui.field.error > .ui.input:focus {
    background: #fff0f0;
    border-color: #dbb1b1 !important;
    box-shadow: none;
    color: #d95c5c;
}

.ui.field.text {
    display: -webkit-box; /* OLD - iOS 6-, Safari 3.1-6 */
    display: -moz-box; /* OLD - Firefox 19- (buggy but mostly works) */
    display: -webkit-flex; /* NEW - Chrome */
    display: flex;
}

.ui.field.text > .ui.input,
.ui.field.text > .ui.dropdown {
    display: block;
    -webkit-box-flex: 1; /* OLD - iOS 6-, Safari 3.1-6 */
    -moz-box-flex: 1; /* OLD - Firefox 19- */
    -webkit-flex: 1; /* Chrome */
    flex: 1; /* NEW, Spec - Opera 12.1, Firefox 20+ */
    box-flex: 1;
}

.ui.field.text > .ui.label {
    min-width: 0.9rem;
    padding: 0 0.1rem;
    text-align: right;
    line-height: 0.44rem;
}

.ui.field.text.rounded > .ui.label {
    background: #DDDDDD;
    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.ui.field.text.rounded > .ui.input,
.ui.field.text.rounded > .ui.dropdown {
    -moz-border-radius-topleft: 0;
    -webkit-border-top-left-radius: 0;
    border-top-left-radius: 0;
    -moz-border-radius-bottomleft: 0;
    -webkit-border-bottom-left-radius: 0;
    border-bottom-left-radius: 0;
}

.ui.field > .ui.input[date-placeholder] {
    position: relative;
}

.ui.field > .ui.input[date-placeholder]:before {
    content: attr(date-placeholder);
    position: absolute;
    line-height: 0.44rem;
    color: #999999;
    font-size: 0.14rem;
    left: 0.1rem;
    top: 0;
}

.ui.field > .ui.input[date-placeholder].active {
    border: 1px solid #0078e7;
}

.ui.field > .ui.input[date-placeholder].active:before {
    content: none;
}

.ui.field > .ui.input[date-placeholder].active:after {
    content: "";
    position: absolute;
    height: 0.24rem;
    width: 0.02rem;
    background: #999999;
    left: 0.05rem;
    top: 50%;
    margin-top: -0.12rem;
    -moz-animation: light 1s 0s infinite;
    -webkit-animation: light 1s 0s infinite;
}

@-moz-keyframes light {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@-webkit-keyframes light {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

.ui.radio, .ui.checkbox {
    display: inline-block;
}

.ui.radio > input[type=radio],
.ui.checkbox > input[type=checkbox] {
    left: 0;
    margin: 0;
    opacity: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

.ui.radio label,
.ui.checkbox label {
    padding-left: 0.24rem;
    position: relative;
    display: inline-block;
    min-height: 0.16rem;
    min-width: 0.16rem;
    vertical-align: middle;
}

.ui.radio label:before,
.ui.checkbox label:after {
    box-sizing: border-box;
    -moz-box-sizing: border-box; /* Firefox */
    -webkit-box-sizing: border-box; /* Safari */
}

.ui.radio label:before {
    content: "";
    width: 0.18rem;
    height: 0.18rem;
    border: 1px solid #CBCBCB;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -0.11rem;
}

.ui.radio input[type=radio]:checked + label:after {
    content: "";
    width: 0.1rem;
    height: 0.1rem;
    background: #939393;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    position: absolute;
    left: 0.04rem;
    top: 50%;
    margin-top: -0.07rem;
}

.ui.checkbox label:before {
    content: "";
    width: 0.16rem;
    height: 0.16rem;
    border: 1px solid #CBCBCB;
    position: absolute;
    left: 0;
    top: 0.05rem;
}

.ui.checkbox input[type=checkbox]:checked + label:before {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABjSURBVEhLY2AYBcMuBHxupvynmadAhtPMApjhNLEA2XCqWzCCDCcl7MgKFmKTGFmGgzIGMcmMbMPRLcAWXBQZDsvauAyhiuG4LKGq4YQsoWohRhOXo7uQ2KRLVZ+NGkb1EAAAnfXAYVexWbYAAAAASUVORK5CYII=") no-repeat center;
    background-size: 0.12rem;
}

.ui.switch {
    height: 0.3rem;
    width: 0.6rem;
    position: relative;
    overflow: hidden;
}

.ui.switch > input[type=checkbox] {
    width: 0.6rem;
    height: 0.3rem;
    position: absolute;
    left: 0;
    top: 0;
    -moz-opacity: 0;
    -webkit-opacity: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 100;
}

.ui.switch > .ui.switch-inner {
    height: 0.3rem;
    position: relative;
    background: #CCCCCC;
    -moz-border-radius: 0.3rem;
    -webkit-border-radius: 0.3rem;
    border-radius: 0.3rem;
}

.ui.switch > .ui.switch-inner > .ui.switch-btn {
    height: 0.3rem;
    width: 0.6rem;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 5;
}

.ui.switch > .ui.switch-inner > .ui.switch-btn span {
    width: 0.6rem;
    height: 0.3rem;
    box-sizing: border-box;
    -moz-box-sizing: border-box; /* Firefox */
    -webkit-box-sizing: border-box; /* Safari */
    font-size: 0.12rem;
    text-indent: -30rem;
    -moz-border-radius: 0.3rem;
    -webkit-border-radius: 0.3rem;
    border-radius: 0.3rem;
    overflow: hidden;
    transition: all 0.2s ease-in 0s;
    -moz-transition: all 0.2s ease-in 0s;
    -webkit-transition: all 0.2s ease-in 0s;
    opacity: 0;
    transform: scale(0.5);
    -moz-transform: scale(0.5);
    -webkit-transform: scale(0.5);
    position: absolute;
    left: 0;
    top: 0;
}

.ui.switch > .ui.switch-inner > .ui.switch-btn .active {
    background: #1098ED;
}

.ui.switch > .ui.switch-inner > .ui.switch-arrow {
    height: 0.26rem;
    width: 0.26rem;
    background: #FFFFFF;
    position: absolute;
    top: 0.02rem;
    right: 0.32rem;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
    transition: all 0.1s ease-in 0s;
    -moz-transition: all 0.1s ease-in 0s;
    -webkit-transition: all 0.1s ease-in 0s;
    z-index: 10;
}

.ui.switch > input[type=checkbox]:checked + .ui.switch-inner > .ui.switch-btn .active {
    opacity: 1;
    transform: scale(1);
    -moz-transform: scale(1);
    -webkit-transform: scale(1);
}

.ui.switch > input[type=checkbox]:checked + .ui.switch-inner .ui.switch-arrow {
    right: 0.02rem;
}


.ui.switch.text > .ui.switch-inner > .ui.switch-btn {
    height: 0.3rem;
    line-height: 0.3rem;
    margin-left: -100%;
    transition: margin 0.1s ease-in 0s;
    -moz-transition: margin 0.1s ease-in 0s;
    -webkit-transition: margin 0.1s ease-in 0s;
    width: 200%;
    position: relative;
}

.ui.switch.text > .ui.switch-inner > .ui.switch-btn span {
    float: left;
    width: 50%;
    height: 0.3rem;
    box-sizing: border-box;
    -moz-box-sizing: border-box; /* Firefox */
    -webkit-box-sizing: border-box; /* Safari */
    position: relative;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    font-size: 0.12rem;
    text-indent: 0;
    opacity: 1;
    transform: scale(1);
    -moz-transform: scale(1);
    -webkit-transform: scale(1);
}

.ui.switch.text > .ui.switch-inner > .ui.switch-btn .active {
    color: #FFFFFF;
    padding-right: 0.26rem;
    text-align: center;
    background: none;
}

.ui.switch.text > .ui.switch-inner > .ui.switch-btn .inactive {
    color: #FFFFFF;
    padding-left: 0.26rem;
    text-align: center;
    background: none;
}

.ui.switch.text > input[type=checkbox]:checked + .ui.switch-inner {
    background: #1098ED;
}

.ui.switch.text > input[type=checkbox]:checked + .ui.switch-inner .ui.switch-btn {
    margin-left: 0;
}

.ui.switch.text > input[type=checkbox]:checked + .ui.switch-inner .ui.switch-arrow {
    right: 0.02rem;
}


/* 文本text */
.ui.notice {
    display: block;
    padding: 0.05rem;
    -moz-border-radius: 0.03rem;
    -webkit-border-radius: 0.03rem;
    border-radius: 0.03rem;
    background: rgb(217, 237, 247);
    color: rgb(58, 135, 173);
}

.ui.notice.success {
    background: rgb(219, 255, 219);
    color: rgb(31, 153, 31);
}

.ui.notice.warning {
    background: rgb(255, 237, 219);
    color: rgb(229, 115, 0);
}

.ui.notice.danger {
    background: rgb(255, 219, 219);
    color: rgb(255, 51, 51);
}

.ui.tag {
    display: inline-block;
    padding: 0.03rem 0.05rem;
    line-height: 1;
    white-space: nowrap;
    -moz-border-radius: 0.03rem;
    -webkit-border-radius: 0.03rem;
    border-radius: 0.03rem;
    background: #E3E3E3;
    font-size: 0.11rem;
    color: #FFFFFF;
}

.ui.tag.primary {
    background: rgb(255, 102, 0);
}

.ui.tag.rounded {
    -moz-border-radius: 0.1rem;
    -webkit-border-radius: 0.1rem;
    border-radius: 0.1rem;
}

/* 表格Table */
.ui.table {
    width: 100%;
}

.ui.table tr th,
.ui.table tr td {
    border-bottom: 1px solid #DDDDDD;
    line-height: 2.5;
    vertical-align: top;
    padding: 5px;
}

.ui.table tr:nth-last-child(1) th,
.ui.table tr:nth-last-child(1) td {
    border-bottom: 0;
}

.ui.table tr.active,
.ui.table tr th.active,
.ui.table tr td.active {
    background: #f5f5f5;
}

.ui.table tr.success,
.ui.table tr th.success,
.ui.table tr td.success {
    background: #dff0d8;
}

.ui.table tr.warning,
.ui.table tr th.warning,
.ui.table tr td.warning {
    background: #fcf8e3;
}

.ui.table tr.danger,
.ui.table tr th.danger,
.ui.table tr td.danger {
    background: #f2dede;
}

.ui.table tr.info,
.ui.table tr th.info,
.ui.table tr td.info {
    background: #d9edf7;
}

.ui.table.striped tr:nth-child(2n) th,
.ui.table.striped tr:nth-child(2n) td {
    background: #F6F6F6;
}

.ui.table.border {
    border: 1px solid #DDDDDD;
    border-right: 0;
}

.ui.table.border tr th,
.ui.table.border tr td {
    border-right: 1px solid #DDDDDD;
}

.ui.table.rounded {
    border: 1px solid #DDDDDD;
    border-right: 0;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    border-collapse: separate;
}

.ui.table.rounded tr th,
.ui.table.rounded tr td {
    border-right: 1px solid #DDDDDD;
}

.ui.table.rounded tr:nth-child(1) th:nth-child(1),
.ui.table.rounded tr:nth-child(1) td:nth-child(1) {
    -moz-border-radius-topleft: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
}

.ui.table.rounded tr:nth-child(1) th:nth-last-child(1),
.ui.table.rounded tr:nth-child(1) td:nth-last-child(1) {
    -moz-border-radius-topright: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
}

.ui.table.rounded tr:nth-last-child(1) th:nth-child(1),
.ui.table.rounded tr:nth-last-child(1) td:nth-child(1) {
    -moz-border-radius-bottomleft: 5px;
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.ui.table.rounded tr:nth-last-child(1) th:nth-last-child(1),
.ui.table.rounded tr:nth-last-child(1) td:nth-last-child(1) {
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.ui.table-responsive {
    border: 1px solid #DDDDDD;
    overflow-y: hidden;
    width: 100%;
}

.ui.table-responsive .ui.table tr th,
.ui.table-responsive .ui.table tr td {
    white-space: nowrap;
}


/* 列表List */


/*swiper*/
.swiper-container {
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    z-index: 1
}

.swiper-container-no-flexbox .swiper-slide {
    float: left
}

.swiper-container-vertical > .swiper-wrapper {
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    -ms-flex-direction: column;
    -webkit-flex-direction: column;
    flex-direction: column
}

.swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-transition-property: -webkit-transform;
    -moz-transition-property: -moz-transform;
    -o-transition-property: -o-transform;
    -ms-transition-property: -ms-transform;
    transition-property: transform;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

.swiper-container-android .swiper-slide, .swiper-wrapper {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -o-transform: translate(0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.swiper-container-multirow > .swiper-wrapper {
    -webkit-box-lines: multiple;
    -moz-box-lines: multiple;
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap
}

.swiper-container-free-mode > .swiper-wrapper {
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    -ms-transition-timing-function: ease-out;
    -o-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    margin: 0 auto
}

.swiper-slide {
    -webkit-flex-shrink: 0;
    -ms-flex: 0 0 auto;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative
}

.swiper-container-autoheight, .swiper-container-autoheight .swiper-slide {
    height: auto
}

.swiper-container-autoheight .swiper-wrapper {
    -webkit-box-align: start;
    -ms-flex-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
    -webkit-transition-property: -webkit-transform, height;
    -moz-transition-property: -moz-transform;
    -o-transition-property: -o-transform;
    -ms-transition-property: -ms-transform;
    transition-property: transform, height
}

.swiper-container .swiper-notification {
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
    opacity: 0;
    z-index: -1000
}

.swiper-wp8-horizontal {
    -ms-touch-action: pan-y;
    touch-action: pan-y
}

.swiper-wp8-vertical {
    -ms-touch-action: pan-x;
    touch-action: pan-x
}

.swiper-button-next, .swiper-button-prev {
    position: absolute;
    top: 50%;
    width: 27px;
    height: 44px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    -moz-background-size: 27px 44px;
    -webkit-background-size: 27px 44px;
    background-size: 27px 44px;
    background-position: center;
    background-repeat: no-repeat
}

.swiper-button-next.swiper-button-disabled, .swiper-button-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-prev, .swiper-container-rtl .swiper-button-next {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
    left: 10px;
    right: auto
}

.swiper-button-prev.swiper-button-black, .swiper-container-rtl .swiper-button-next.swiper-button-black {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23000000'%2F%3E%3C%2Fsvg%3E")
}

.swiper-button-prev.swiper-button-white, .swiper-container-rtl .swiper-button-next.swiper-button-white {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E")
}

.swiper-button-next, .swiper-container-rtl .swiper-button-prev {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
    right: 10px;
    left: auto
}

.swiper-button-next.swiper-button-black, .swiper-container-rtl .swiper-button-prev.swiper-button-black {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23000000'%2F%3E%3C%2Fsvg%3E")
}

.swiper-button-next.swiper-button-white, .swiper-container-rtl .swiper-button-prev.swiper-button-white {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E")
}

.swiper-pagination {
    position: absolute;
    text-align: center;
    -webkit-transition: .3s;
    -moz-transition: .3s;
    -o-transition: .3s;
    transition: .3s;
    -webkit-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0
}

.swiper-container-horizontal > .swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction {
    bottom: 10px;
    left: 0;
    width: 100%;
    background: transparent!important;
}

.swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 100%;
    background: #000;
    opacity: .2
}

button.swiper-pagination-bullet {
    border: none;
    margin: 0;
    padding: 0;
    box-shadow: none;
    -moz-appearance: none;
    -ms-appearance: none;
    -webkit-appearance: none;
    appearance: none
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer
}

.swiper-pagination-white .swiper-pagination-bullet {
    background: #fff
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: #007aff
}

.swiper-pagination-white .swiper-pagination-bullet-active {
    background: #fff
}

.swiper-pagination-black .swiper-pagination-bullet-active {
    background: #000
}

.swiper-container-vertical > .swiper-pagination-bullets {
    right: 10px;
    top: 50%;
    -webkit-transform: translate3d(0, -50%, 0);
    -moz-transform: translate3d(0, -50%, 0);
    -o-transform: translate(0, -50%);
    -ms-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0)
}

.swiper-container-vertical > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 5px 0;
    display: block
}

.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 5px
}



.swiper-pagination-progress {
    background: rgba(0, 0, 0, .25);
    position: absolute
}

.swiper-pagination-progress .swiper-pagination-progressbar {
    background: #007aff;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: left top;
    -moz-transform-origin: left top;
    -ms-transform-origin: left top;
    -o-transform-origin: left top;
    transform-origin: left top
}

.swiper-container-rtl .swiper-pagination-progress .swiper-pagination-progressbar {
    -webkit-transform-origin: right top;
    -moz-transform-origin: right top;
    -ms-transform-origin: right top;
    -o-transform-origin: right top;
    transform-origin: right top
}

.swiper-container-horizontal > .swiper-pagination-progress {
    width: 100%;
    height: 4px;
    left: 0;
    top: 0
}

.swiper-container-vertical > .swiper-pagination-progress {
    width: 4px;
    height: 100%;
    left: 0;
    top: 0
}

.swiper-pagination-progress.swiper-pagination-white {
    background: rgba(255, 255, 255, .5)
}

.swiper-pagination-progress.swiper-pagination-white .swiper-pagination-progressbar {
    background: #fff
}

.swiper-pagination-progress.swiper-pagination-black .swiper-pagination-progressbar {
    background: #000
}

.swiper-container-3d {
    -webkit-perspective: 1200px;
    -moz-perspective: 1200px;
    -o-perspective: 1200px;
    perspective: 1200px
}

.swiper-container-3d .swiper-cube-shadow, .swiper-container-3d .swiper-slide, .swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left, .swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top, .swiper-container-3d .swiper-wrapper {
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d
}

.swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left, .swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10
}

.swiper-container-3d .swiper-slide-shadow-left {
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0)));
    background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -moz-linear-gradient(right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -o-linear-gradient(right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: linear-gradient(to left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-right {
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0)));
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -moz-linear-gradient(left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: linear-gradient(to right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-top {
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0)));
    background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -moz-linear-gradient(bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -o-linear-gradient(bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: linear-gradient(to top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-3d .swiper-slide-shadow-bottom {
    background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0)));
    background-image: -webkit-linear-gradient(top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -moz-linear-gradient(top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: -o-linear-gradient(top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0))
}

.swiper-container-coverflow .swiper-wrapper, .swiper-container-flip .swiper-wrapper {
    -ms-perspective: 1200px
}

.swiper-container-cube, .swiper-container-flip {
    overflow: visible
}

.swiper-container-cube .swiper-slide, .swiper-container-flip .swiper-slide {
    pointer-events: none;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 1
}

.swiper-container-cube .swiper-slide .swiper-slide, .swiper-container-flip .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-active .swiper-slide-active, .swiper-container-flip .swiper-slide-active, .swiper-container-flip .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-cube .swiper-slide-shadow-bottom, .swiper-container-cube .swiper-slide-shadow-left, .swiper-container-cube .swiper-slide-shadow-right, .swiper-container-cube .swiper-slide-shadow-top, .swiper-container-flip .swiper-slide-shadow-bottom, .swiper-container-flip .swiper-slide-shadow-left, .swiper-container-flip .swiper-slide-shadow-right, .swiper-container-flip .swiper-slide-shadow-top {
    z-index: 0;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden
}

.swiper-container-cube .swiper-slide {
    visibility: hidden;
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    width: 100%;
    height: 100%
}

.swiper-container-cube.swiper-container-rtl .swiper-slide {
    -webkit-transform-origin: 100% 0;
    -moz-transform-origin: 100% 0;
    -ms-transform-origin: 100% 0;
    transform-origin: 100% 0
}

.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-next, .swiper-container-cube .swiper-slide-next + .swiper-slide, .swiper-container-cube .swiper-slide-prev {
    pointer-events: auto;
    visibility: visible
}

.swiper-container-cube .swiper-cube-shadow {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: .6;
    -webkit-filter: blur(50px);
    filter: blur(50px);
    z-index: 0
}

.swiper-container-fade.swiper-container-free-mode .swiper-slide {
    -webkit-transition-timing-function: ease-out;
    -moz-transition-timing-function: ease-out;
    -ms-transition-timing-function: ease-out;
    -o-transition-timing-function: ease-out;
    transition-timing-function: ease-out
}

.swiper-container-fade .swiper-slide {
    pointer-events: none;
    -webkit-transition-property: opacity;
    -moz-transition-property: opacity;
    -o-transition-property: opacity;
    transition-property: opacity
}

.swiper-container-fade .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-fade .swiper-slide-active, .swiper-container-fade .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-scrollbar {
    border-radius: 10px;
    position: relative;
    -ms-touch-action: none;
    background: rgba(0, 0, 0, .1)
}

.swiper-container-horizontal > .swiper-scrollbar {
    position: absolute;
    left: 1%;
    bottom: 3px;
    z-index: 50;
    height: 5px;
    width: 98%
}

.swiper-container-vertical > .swiper-scrollbar {
    position: absolute;
    right: 3px;
    top: 1%;
    z-index: 50;
    width: 5px;
    height: 98%
}

.swiper-scrollbar-drag {
    height: 100%;
    width: 100%;
    position: relative;
    background: rgba(0, 0, 0, .5);
    border-radius: 10px;
    left: 0;
    top: 0
}

.swiper-scrollbar-cursor-drag {
    cursor: move
}

.swiper-lazy-preloader {
    width: 42px;
    height: 42px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -21px;
    margin-top: -21px;
    z-index: 10;
    -webkit-transform-origin: 50%;
    -moz-transform-origin: 50%;
    transform-origin: 50%;
    -webkit-animation: swiper-preloader-spin 1s steps(12, end) infinite;
    -moz-animation: swiper-preloader-spin 1s steps(12, end) infinite;
    animation: swiper-preloader-spin 1s steps(12, end) infinite
}

.swiper-lazy-preloader:after {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
    background-position: 50%;
    -webkit-background-size: 100%;
    background-size: 100%;
    background-repeat: no-repeat
}

.swiper-lazy-preloader-white:after {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%23fff'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E")
}


.guide_container .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 8px;
}

.guide_container .swiper-pagination-bullet.check {
    background: #e33e3b;
}
.guide_container .swiper-pagination-bullet:nth-of-type(1).check  {
    background: rgb(0,133,245) !important;
}
.guide_container .swiper-pagination-bullet:nth-of-type(2).check {
    background: rgb(0,133,245) !important;
}
.guide_container .swiper-pagination-bullet:nth-of-type(3).check {
    background: #3495FF!important;
}
.guide_container .swiper-pagination-bullet {
    width: 40px;
    height: 6px;
    display: inline-block;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
}
.guide_container .swiper-pagination{
    bottom:8%;
}

@-webkit-keyframes swiper-preloader-spin {
    100% {
        -webkit-transform: rotate(360deg)
    }
}

@keyframes swiper-preloader-spin {
    100% {
        transform: rotate(360deg)
    }
}

.swiper-pagination-bullet {
    background: rgba(0, 0, 0, 0.2);
    opacity: 1;
}
#login_userIndexs .swiper-pagination-bullet, #bank_bankList .swiper-pagination-bullet {
    background: #ffffff;
}
.swiper-pagination-bullet.check {
    background: #e33e3b!important;
}

/* 右箭头样式 */
.right_icon:after {
    content: "";
    width: 0.08rem;
    height: 0.08rem;
    border-left: 2px solid #3f4e59;
    border-bottom: 2px solid #3f4e59;
    transform: rotate(225deg);
    -webkit-transform: rotate(225deg);
    -moz-transform: rotate(225deg);
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -0.04rem;
}

.bottom_icon:after {
    content: "";
    width: 0.08rem;
    height: 0.08rem;
    border-left: 2px solid #3f4e59;
    border-bottom: 2px solid #3f4e59;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -0.04rem;
}

.top_icon:after {
    content: "";
    width: 0.08rem;
    height: 0.08rem;
    border-left: 2px solid #3f4e59;
    border-bottom: 2px solid #3f4e59;
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
    -moz-transform: rotate(135deg);
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -0.07rem;
}

.left_icon:after {
    content: "";
    width: 0.08rem;
    height: 0.08rem;
    border-left: 2px solid #3f4e59;
    border-bottom: 2px solid #3f4e59;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -0.04rem;
}

.left_icon, .top_icon, .bottom_icon, .right_icon {
    position: relative;
}

.camera_pop_layer {
    background: rgba(0, 0, 0, .3);
}

.camera_pop_layer > div {
    width: 90%;
    height: 1.58rem;
    position: absolute;
    left: 0;
    bottom: 0.08rem;
    right: 0;
    margin: auto;
    text-align: center;
    color: #4d90fe;
    font-size: 18px;
    -webkit-animation-duration: 250ms !important;
    animation-duration: 250ms !important;
}

.camera_pop_layer > div > div {
    height: 0.5rem;
    line-height: 0.5rem;
    background: #fff;
    border-radius: 0.1rem;
}

.camera_pop_layer > div .camera {
    border-radius: 0.1rem 0.1rem 0 0;
    border-bottom: 1px solid #e6e6e6;
}

.camera_pop_layer > div .album {
    border-radius: 0 0 0.1rem 0.1rem;
}

.camera_pop_layer > div .cancel_camera {
    margin-top: 0.08rem;
}

.deal_box .xy {
    color: #319ef2 !important;
    font-family: STHeiti STXihei, Microsoft JhengHei, Microsoft YaHei, Arial;
}

/* 移动端选择插件*/
.mobileSelect {
    position: relative;
    z-index: 0;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: opacity 0.4s, z-index 0.4s;
    transition: opacity 0.4s, z-index 0.4s;
}

.mobileSelect * {
    margin: 0;
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.mobileSelect .grayLayer {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: #eee;
    background: rgba(0, 0, 0, 0.7);
    z-index: 888;
    display: block;
}

.mobileSelect .content {
    width: 100%;
    display: block;
    position: fixed;
    z-index: 889;
    color: black;
    -webkit-transition: all 0.4s;
    transition: all 0.4s;
    bottom: -350px;
    left: 0;
    background: white;
}

.mobileSelect .content .fixWidth {
    width: 90%;
    margin: 0 auto;
    position: relative;
}

.mobileSelect .content .fixWidth:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.mobileSelect .content .btnBar {
    border-bottom: 1px solid #DCDCDC;
    font-size: 15px;
    height: 45px;
    position: relative;
    text-align: center;
    line-height: 45px;
}

.mobileSelect .content .btnBar .cancel,
.mobileSelect .content .btnBar .ensure {
    height: 45px;
    width: 55px;
    cursor: pointer;
    position: absolute;
    top: 0;
}

.mobileSelect .content .btnBar .cancel {
    left: 0;
    color: #666;
}

.mobileSelect .content .btnBar .ensure {
    right: 0;
    color: #319ef2;
}

.mobileSelect .content .btnBar .title {
    font-size: 15px;
    padding: 0 15%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.mobileSelect .content .btnBar .choose{
    color: #319ef2;
    margin-top: 0.1rem;
}
.mobileSelect .content .btnBar .choose ul{
    width: 1rem;
    height: 0.3rem;
    line-height: 0.3rem;
    border: 1px solid #319ef2;
}
.mobileSelect .content .btnBar .choose .active{
    color: #fff;
    background: #319ef2;
}
.mobileSelect .content .panel:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.mobileSelect .content .panel .wheels {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.mobileSelect .content .panel .wheel {
    position: relative;
    z-index: 0;
    float: left;
    width: 50%;
    height: 200px;
    overflow: hidden;
    -webkit-transition: width 0.3s ease;
    transition: width 0.3s ease;
}

.mobileSelect .content .panel .wheel .selectContainer {
    display: block;
    text-align: center;
    -webkit-transition: -webkit-transform 0.18s ease-out;
    transition: -webkit-transform 0.18s ease-out;
    transition: transform 0.18s ease-out;
    transition: transform 0.18s ease-out, -webkit-transform 0.18s ease-out;
}

.mobileSelect .content .panel .wheel .selectContainer li {
    font-size: 15px;
    display: block;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mobileSelect .content .panel .selectLine {
    height: 40px;
    width: 100%;
    position: absolute;
    top: 80px;
    pointer-events: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #E5433B;
    border-bottom: 1px solid #E5433B;
}

.mobileSelect .content .panel .shadowMask {
    position: absolute;
    top: 0;
    width: 100%;
    height: 200px;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), color-stop(rgba(255, 255, 255, 0)), to(#ffffff));
    background: -webkit-linear-gradient(top, #ffffff, rgba(255, 255, 255, 0), #ffffff);
    background: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0), #ffffff);
    opacity: 0.9;
    pointer-events: none;
}

.mobileSelect-show {
    opacity: 1;
    z-index: 10000;
    visibility: visible;
}

.mobileSelect-show .content {
    bottom: 0;
}

.blackMode#login_userIndexs .item .btn{
    color: #e5443c !important;
}
.blackMode .menu_box a span{
    color: #DFDFDF !important;
}
.blackMode .index_box h3 span{
    color: #cfcece!important;
}
.blackMode .item .title{
    color: #FBFEFF!important;
}
.blackMode .item .balance{
    color: #D0D2E1!important;
}
.blackMode .item .text .purchase,.blackMode .item .text .deposit,.blackMode .item .text .purchase{
    color: #D0D2E1!important;
}
.blackMode .item{
    border-color:  #D0D2E1!important;
}
.blackMode#login_userIndexs .footer_inner ul li span,.blackMode#login_userLogin .footer_inner ul li span{
    color: #D0D2E1;
}
.blackMode .footer_inner ul li:nth-child(2) em {
    background: url(../images/foot_031.png) no-repeat center bottom;
    background-size: 22px 22px;
}
.blackMode .footer_inner ul li:nth-child(3) em {
    background: url(../images/foot_041.png) no-repeat center bottom;
    background-size: 22px 22px;
}

/* 暗黑模式主题、底部设置 start*/
.blackMode#login_userIndexs  *, .blackMode#login_userLogin  *{
    background-color: #151515!important;
    box-shadow: none!important;
}
/* 暗黑模式主题、底部设置 end*/


/* 暗黑模式头部设置 start*/
.blackMode#login_userIndexs .header_inner2, .blackMode#login_userLogin .header_inner2{
    background-color: #151515!important;
    box-shadow: none!important;
}
.blackMode .header_inner2 {
    background: url(../images/icon_logo.png.jpg) no-repeat center top!important;
    background-size: 100% auto;
}
.blackMode .header_inner2 .login-in_login-out{
    border: 1px solid #e5443c!important;
}
.blackMode header * {
    border-color:  #e5443c!important;
}
.blackMode .header_inner .icon_back.icon_gray span:before{
    border-color: #DBDBDB!important;
}
.blackMode#login_userIndexs .header_inner h1.text_gray,  .blackMode#login_userLogin .header_inner h1.text_gray{
    color: #DBDBDB!important;
}
/* 暗黑模式头部设置 end*/

/* 暗黑模式弹框样式修改 start*/
.blackMode#login_userIndexs .update_prompt{
    background: rgba(0,0,0,.5)!important;
    color: #DFDFDF !important;
}
.blackMode#login_userIndexs .update_detail .update_inner dl dd:nth-child(1) a{
    background-color: #e5443c!important;
}
.blackMode#login_userIndexs .update_detail .update_inner dl dd:nth-child(2) a{
    background-color: #e5443c!important;
}
.blackMode#login_userIndexs .update_detail .update_inner dl dd:nth-child(3) a{
    background-color: #777777!important;
}
.blackMode .update_prompt *{
    color: #DFDFDF !important;
}
.blackMode#login_userIndexs .pop_layer,.blackMode#login_userIndexs #activityDialog  .pop_layer{
    background: rgba(0, 0, 0, 0.5)!important;
}
.blackMode#login_userIndexs #activityDialog *{
    background-color: transparent!important;
}
/* 暗黑模式弹框样式修改 end*/
.blackMode#login_userIndexs  .swiper-pagination{
    background: transparent!important;
}
.blackMode#login_userIndexs .swiper-pagination-bullet.check{
    background: #e33e3b!important;
}
.blackMode img{
    background: transparent!important;
}

.blackMode .deal_box a.xy,.blackMode #moreDetails_more .deal_box a {
    color: #DBDBDB!important;
}
blackMode#login_userIndexs .my_box a:after,blackMode#login_userLogin .my_box a:after {
    border-color: #DBDBDB!important;
}
.blackMode  .my_box_personMsg #qualifiedInvestor:after,.blackMode .my_box_personMsg #mybank:after{
    border-color: #DBDBDB!important;

}
.blackMode#login_userIndexs .header_logo{
    background: url("../images/black_icon_logo.png") no-repeat center center!important;
    height: 100%;
    width: 2rem;
    background-size: 100% 80%!important;
}
.blackMode#login_userLogin * {
    color: #dbdbdb!important;
}
.blackMode#login_userLogin .login_input{
    overflow: hidden;
}
.blackMode#login_userLogin #login {
    background: #e5443c!important;
}
.blackMode#login_userLogin #loginDrain {
    background: #e5443c!important;
}


/* 新版暗黑模式 */
.blackMode#login_userIndexs{
    background:#151515;
}
.blackMode#login_userIndexs .header{
    background:#151515;
}
.blackMode#login_userIndexs .header_logo{
    background: url("../images/black_icon_logo.png") no-repeat center center!important;
    height: 100%;
    width: 2rem;
    background-size: 100% 80%!important;
}
.blackMode#login_userIndexs .homePageIndex_list .menu_item li{
    box-shadow: none !important;
    background:#151515;
    color:#fff;
}
.blackMode#login_userIndexs .homePageIndex_list .menu_item{
    border:1px solid #CBCBCB;
}
.blackMode#login_userIndexs .homePageIndex_classificationList div{
    background:#151515 !important;
    color:#cfcece !important;
    border:1px solid #cfcece;
}
.blackMode#login_userIndexs .color_000{
    color:#fff !important;
}
.blackMode#login_userIndexs .classificationList_card_main{
    background:#151515;
}
.blackMode#login_userIndexs .homePageIndex_mechanism{
    color:#cfcece;
}
.blackMode#login_userIndexs .homePageIndex_remark{
    color:#cfcece;
}
.blackMode#login_userIndexs .footer_inner{
    background:#151515;
}
.blackMode#login_userIndexs .footer_inner ul li span{
    color:#cfcece !important;
}
.blackMode#login_userIndexs .main_list{
    background:#151515;
}
/* .blackMode#login_homePageIndex .homePageIndex_classificationList div */
.swiper_scrollbar .swiper-pagination{
    width: 30%;
    left: 35%;
    display: flex;
    justify-content: center;
}
.swiper_scrollbar .swiper-pagination li{
    /*width: 100%;*/
    margin: 0 !important;
    border-radius: 0;
    height: 0.04rem;
    background: #e6e6e6;
}