// 持有详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#bank_holdDetail ",
        _pageCode = "bank/holdDetail";
    var productInfo;

    function init() {
        var term_type_name = {
            "Y": "年",
            "M": "月",
            "D": "日"
        }
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " .fund_name").html(productInfo.fund_name);
        $(_pageId + " .hold_amt").html(tools.fmoney(productInfo.hold_amt));//持有金额
        $(_pageId + " .base_lnt_rate").html(productInfo.base_lnt_rate + "%"); //活期利率
        $(_pageId + " .order_deal_date").html(tools.ftime(productInfo.order_deal_date)); //计息日期
        $(_pageId + " .exprd_date").html(tools.ftime(productInfo.exprd_date)); //到期日期
        $(_pageId + " .bank_pay_lnt").html(productInfo.bank_pay_lnt);
        $(_pageId + " .prd_trem").html(productInfo.prd_trem + term_type_name[productInfo.term_type]);
        $(_pageId + " .ordr_totexd_amt").html(productInfo.ordr_totexd_amt); //已派发利息
        $(_pageId + " .fund_name").html(productInfo.fund_name);
    }


    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //再次存入
        appUtils.bindEvent($(_pageId + " #recharge"), function () {
            appUtils.pageInit(_pageCode, "bank/recharge");
        });
        //提前取出
        appUtils.bindEvent($(_pageId + " #enchashment"), function () {
            appUtils.pageInit(_pageCode, "bank/enchashment");
        });
    }


    function destroy() {
        $(_pageId + " .fund_name").html("--");
        $(_pageId + " .hold_amt").html("--");
        $(_pageId + " .base_lnt_rate").html("--");
        $(_pageId + " .order_deal_date").html("--");
        $(_pageId + " .exprd_date").html("--");
        $(_pageId + " .bank_pay_lnt").html("--");
        $(_pageId + " .prd_trem").html("--");
        $(_pageId + " .ordr_totexd_amt").html("--");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var bankHoldDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankHoldDetail;
});
