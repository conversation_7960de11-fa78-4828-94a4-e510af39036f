//年终活动
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil");
    var _pageId = "#activity_yearEndActivity ";
    var _pageCode = "activity/yearEndActivity"
    var layerUtils = require("layerUtils");
    var userInfo, activityInfo;
    var external = require("external");
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    require("../../js/draw.js");
    require("../common/html2canvas.min");
    var pageTouchTimer = null;
    /* 变量  活动信息*/
    var activityInfo = "", reward_num = 1, state, shareflag, share_template, turntable_list, activity_id_url, group_id, banner_id;
    var stopIndex;
    var guessActivityInfo, guess_state, guess_words, activity_tips, activity_id,userAuthenticationStatus,recommend_prod_type;
    // 初始化转盘
    var luck = {
        index: 0, // 当前转动到哪个位置，起点位置
        count: 0, // 总共有多少个位置
        timer: 0, // setTimeout的ID，用clearTimeout清除
        speed: 20, // 初始转动速度
        times: 0, // 转动次数
        cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
        prize: -1, // 中奖位置
        init: function (id) {
            if ($("#" + id).find(".luck-unit").length > 0) {
                var $luck = $("#" + id);
                var $units = $luck.find(".luck-unit");
                this.obj = $luck;
                this.count = $units.length;
                this.index = 0;
                $luck.find(".luck-unit").removeClass("active");
                $luck.find(".luck-unit-" + this.index).addClass("active");
            }
        },
        roll: function () {
            var index = this.index;
            var count = this.count;
            var luck = this.obj;
            $(luck).find(".luck-unit-" + index).removeClass("active");
            index += 1;
            if (index > count - 1) {
                index = 0;
            }
            $(luck).find(".luck-unit-" + index).addClass("active");
            this.index = index;
            return false;
        },
        stop: function (index) {
            this.prize = index;
            return false;
        }
    };
    var moneyObj = {};
    var platform = gconfig.platform;
    platform = '1'
    /**
     * 初始化
     */
    async function init() {
        activityInfo = appUtils.getPageParam() ? appUtils.getPageParam() : appUtils.getSStorageInfo("activityInfo");

        if (activityInfo && validatorUtil.isEmpty(activityInfo.activity_id)) {
            activityInfo = ''
            layerUtils.iAlert('活动ID未配置,请联系管理人员', -1, function () {
                pageBack();
            });
        }
        $(_pageId + " .activity_pop_layer").hide();
        // getGuessInfo();
        if (activityInfo) {
            appUtils.setSStorageInfo("activityInfo", activityInfo);
            getActivityInfo();
            
        }
        luck.init('luck');
    };
    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    async function setData(){
        // var res = await getUserAuthenticationStatus();
        // userAuthenticationStatus = res[0].state //获取用户认证状态
        // appUtils.setSStorageInfo("isAuthentication", userAuthenticationStatus);
        // if (res[0].sm_white_state == "1" || res[0].state == "1") { //白名单用户 || 合格投资人 展示详细数据
        //     getProductList();
        // } else {
        //     getProductList(0);
        // }
    }
    /**
     * 获取活动信息
     * */
    function getActivityInfo() {
        service.reqFun108015({
            cust_no: activityInfo.cust_no,
            activity_id: activityInfo.activity_id
        },  function (data) {
            var error_no = data.error_no;
            if (error_no == 0) {
                var result = data.results[0];
                
                recommend_prod_type = result.recommend_prod_type;
                //获取年终活动产品列表
                // setData();
                state = result.activity_status;//1:进行中 2:未开始 3:已结束
                shareflag = result.shareflag; //0未分享 1:分享过
                reward_num = result.times;//抽奖次数
                share_template = result.share_template;//分享模板ID
                turntable_list = result.turntable_list;
                let start_time = result.start_time ? `${result.start_time.substr(0,4)}.${result.start_time.substr(4,2)}.${result.start_time.substr(6,2)}` : ''
                let end_time = result.end_time ? `${result.end_time.substr(0,4)}.${result.end_time.substr(4,2)}.${result.end_time.substr(6,2)}` : ''
                $(_pageId + " #activityRules .b_con").html(result.introduce)
                $(_pageId + " .activity_tips").html(result.activity_tips)
                //渲染开始结束时间
                $(_pageId + " .startTime").html(start_time);
                $(_pageId + " .endTime").html(end_time);
                if(start_time && end_time) $(_pageId + " .time_card").show();
                if (state == "2") {
                    $(_pageId + " #start .start_btn_name").html("即将开始");
                }
                if (state == "3") {
                    $(_pageId + " #start .start_btn_name").html("已结束");
                    $(_pageId + " #start .start_btn_name").css({ "width": '0.7rem' })
                    $(_pageId + " #start").css({ "background": '#808080' })
                }
                // if (state == "1" && shareflag === "0") {
                //     $(_pageId + " #start .start_btn_name").html("立即抽奖");
                //     $(_pageId + " #start").removeClass("notclick");
                // }
                if (state == "1" && reward_num <= 0) {
                    $(_pageId + " #start .start_btn_name").html("再抽一次");
                    $(_pageId + " #start").removeClass("notclick");
                }
                if (result.turntable_list && result.turntable_list.length) {
                    moneyObj = {};
                    var html = '';
                    result.turntable_list.forEach((item, i) => {
                        var index;
                        if (i < 3) {
                            index = i;
                        } else if (i >= 3 && i <= 4) {
                            index = i == 3 ? "7" : "3"
                        } else if (i > 4 && i <= 7) {
                            index = i == 5 ? "6" : (i == 6 ? "5" : "4")
                        }
                        $(_pageId + ` .game .luck-unit-${index} img`).attr("src", global.oss_url + item.img_url)
                        if (item.reward_type == '0') { // 固定积分
                            var obj = {
                                list_text: `${item.points_amount}积分`, //领取记录文字
                                reward_text: `${item.points_amount}积分`, //中奖提示
                                src: "./images/activity/p_ok.png",
                                stopIndex: [parseFloat(index)],
                                reward_type: item.reward_type, // 类型-1:随机积分 0:固定积分 2:实物
                                btn_txt: "我知道了",
                                prompt: "",
                            }
                            moneyObj[`${item.points_amount}-${i}`] = obj;
                        } else if (item.reward_type == "1") { // 随机积分
                            var obj = {
                                list_text: "随机积分", //领取记录文字
                                reward_text: "", //中奖提示
                                src: "./images/activity/p_ok.png",
                                stopIndex: [parseFloat(index)],
                                reward_type: item.reward_type, // 类型-1:随机积分 0:固定积分 2:实物
                                btn_txt: "我知道了",
                                prompt: "",
                            }
                            moneyObj[`auto-${i}`] = obj;
                        } else if (item.reward_type == "2") { // 实物
                            var obj = {
                                list_text: item.name, //领取记录文字
                                reward_text: item.name, //中奖提示
                                src: "./images/activity/p_ok.png",
                                stopIndex: [parseFloat(index)],
                                reward_type: item.reward_type, // 类型-1:随机积分 0:固定积分 2:实物
                                btn_txt: "我知道了",
                                prompt: "请到“抽奖记录”中填写领取方式",
                            }
                            moneyObj[`physical${item.real_reward_code}-${i}`] = obj;
                        }
                    })
                }
                // 获取分享模板
                let query_params = {
                    registered_mobile: ut.getUserInf().mobileWhole,
                    share_template: share_template
                }
                service.reqFun102012(query_params, async function (data) {
                    if (data.error_no == '0') {
                        var result = data.results[0];
                        if (data.results[0] && data.results[0].share_form == 2) {  // 若分享的是卡片，先渲染卡片
                            
                            setShareImg(result);
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
    }
    //渲染分享卡片图片
    function setShareImg(chooseData) {
        let bgImg = gconfig.global.oss_url + chooseData.img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #qr_img").show();
                    $(_pageId + " #code").hide();
                    $(_pageId + "#qr_img").empty();
                    let qr_code_img = global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #qr_img").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                    }, { "isShowWait": false })
                } else {
                    $(_pageId + " #qr_img").hide();
                    $(_pageId + " #code").show();
                    qrcode(chooseData);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    //渲染短链二维码
    function qrcode(chooseData) {
        let mobile = ut.getUserInf().mobileWhole;
        mobile = common.desEncrypt("mobile", mobile);//加密
        // let long_url = "https://xhxts.sxfae.com/m/mall/index.html#!/drainage/userInvitationWx.html?mobile=" + mobile;
        let long_url = chooseData.qr_code_img_url + '?mobile=' + mobile;
        $(_pageId + "#code").empty();
        service.reqFun101073({ long_url: long_url }, function (res) {
            if (res.error_no == "0") {
                if (res.results != undefined && res.results.length > 0) {
                    var short_url = res.results[0].shortUrl;
                    require("../common/jquery.qrcode.min");
                    $(_pageId + " #code").qrcode({
                        render: "canvas", //设置渲染方式，有table和canvas
                        text: short_url, //扫描二维码后自动跳向该链接
                        width: 70, //二维码的宽度
                        height: 70, //二维码的高度
                        imgWidth: 20,
                        imgHeight: 20,
                        src: '../mall/images/icon_app.png'
                    });
                }
            } else {
                layerUtils.iAlert(res.error_info);
            }
        })
    }

    /*分享卡片*/
    function shareCard() {
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        $(_pageId + " .pop_layer").hide();
        var father = document.querySelector("#content");
        var _fatherHTML = document.querySelectorAll("#content .page");
        var cur = document.querySelector("#activity_dragonYearActivity");
        father.innerHTML = "";
        father.appendChild(cur);
        let dom = document.querySelector(_pageId + " .shareImg");
        html2canvas(dom, {
            scale: 4
        }).then(canvas => {
            var base64 = canvas.toDataURL("image/png");
            var _base64 = base64.split(",")[1];
            father.innerHTML = "";
            for (let i = 0; i < _fatherHTML.length; i++) {
                father.appendChild(_fatherHTML[i]);
            }
            param = {
                "funcNo": "50231",
                "imgUrl": _base64,
                "shareType": "23",
                "imageShare": "1",
                "imageType":"base64"
            }
            require("external").callMessage(param);
            // common.share("23", share_template ? share_template : "1", "", true, _base64);
        })
    }

    //点击开始竞猜
    function competition(guess_answer) {
        let data = {
            guess_answer: guess_answer,
            activity_id: activity_id
        }
        service.reqFun112007(data, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (results.status == '2') {
                    guessActivityInfo.guess_answer = results.guess_answer;
                    let guess_fall_count = results.guess_fall_count * 1;
                    let guess_rise_count = results.guess_rise_count * 1;
                    let total = guess_fall_count + guess_rise_count;
                    if (guess_answer == "2") {    //看跌 涨灰色
                        $(_pageId + " .bg_red").css("background", "#ccc");
                    } else if (guess_answer == "1") {  //看涨 跌灰色
                        $(_pageId + " .bg_green").css("background", "#ccc");
                    }
                    //看涨的概率
                    let guess_rise = Math.round(((guess_rise_count / total).toFixed(2)) * 100);
                    //看跌的概率
                    let guess_fall = 100 - guess_rise;
                    $(_pageId + " .marvellous .pro .add").css("width", guess_rise + '%');
                    $(_pageId + " .marvellous .pro .add").html('&nbsp;&nbsp;&nbsp;' + guess_rise + '%');
                    $(_pageId + " .marvellous .pro .lightning").css("left", ('calc(' + guess_rise + "% - 16px)"));
                    $(_pageId + " .marvellous .pro .lightning").show();
                    $(_pageId + " .marvellous .pro .reduce").css("width", guess_fall + "%");
                    if (guess_fall >= 14) $(_pageId + " .marvellous .pro .reduce").html(guess_fall + "%" + '&nbsp;&nbsp;&nbsp;');

                    layerUtils.iAlert('竞猜成功');
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //获取年终活动推荐产品
    function getProductList(userAuthenticationStatus){
        service.reqFun102204({ recommendType:recommend_prod_type }, function (result) {
            if (result.error_no == "0") {
                let list = result.results ? result.results : [];
                if(list.length) {
                    //配置了推荐产品
                    let html = setChildData(list,userAuthenticationStatus)
                    $(_pageId + " .card .card_main").html(html);
                }else{
                    //没有配置推荐产品
                }
                
            } else {
                layerUtils.iAlert(d.error_info);
            }
        })
    }
    //合并处理数据 新增一键分散购买
    function setChildData(list, userAuthenticationStatus) {
        let childrenListHtml = '';
        if (!list || !list.length) return '';
        list.map(item => {
            //埋点ID
            var topLevel; //按钮状态
            let fundCode = item.fund_code ? item.fund_code : '';
            var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
            var prod_per_min_amt = item.prod_per_min_amt //投资金额
            var prod_sname = item.prod_name_list ? item.prod_name_list : item.prod_sname ? item.prod_sname : item.prod_exclusive_name
            var this_year_rate = tools.fmoney(item.this_year_rate ? item.this_year_rate : item.annu_yield);
            var rate = tools.fmoney(item.rate);
            var transferable = item.transferable;//是否可转让
            var recommend_info = item.recommend_info;
            var prod_per_min_amt = item.prod_per_min_amt;
            var increase_term = item.increase_term;
            var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
            var str = "";
            if (userAuthenticationStatus == 0) topLevel = '认证可见'
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + item.url + "' style='width: 14%;margin-left: 0.12rem;margin-top: -0.04rem;'>"
            }
            // TODO;
            if (item.fund_code == '000709') {
                //晋金宝
                var annu_yield = tools.fmoney(item.annu_yield);
                item['new_fund_code'] = item.fund_code;
                item['fund_code'] = "000709";
                productInfoSon = JSON.stringify(item);
                childrenListHtml += `
                <ul class="classificationList_card_main flex">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        <p class="m_font_size12">近七日年化:<span class="m_text_red m_font_size18">${annu_yield}</span>%</p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center">
                            购买
                        </span>
                    </li>
                </ul>
                `
            } else if (item.prod_sub_type2 == '100') {
                //私募列表展示
                var found_rate = item.found_rate ? item.found_rate : '--' //成立以来收益
                var preincomerate = tools.fmoney(item.preincomerate) //年化标准
                var threshold_amount = item.threshold_amount / 10000 //起购金额
                var inrest_term = item.inrest_term   //封闭期/锁定期
                var nav = tools.fmoney(item.nav, 4)
                var interest_rate = tools.fmoney(item.interest_rate_min) + "%" + "-" + tools.fmoney(item.interest_rate_max) + "%";
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                //产品整合 是否展示
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var fund_rate_list = item.fund_rate_list == '1' ? '' : 'display_none' //是否展示成立以来收益
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var this_year_rate_list = item.this_year_rate_list == '1' ? '' : 'display_none' //是否展示今年以来收益
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none'
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var stage_yield_list = item.stage_yield_list == '1' ? '' : 'display_none' // 是否展示阶段收益率
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' // 是否展示业绩比较基准
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                childrenListHtml += `
                    <ul class="classificationList_card_main flex" fundCode="${fundCode}">
                        <li class="main_flxe vertical_line">
                            <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000">${prod_sname}${str}</p>
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : preincomerate}</span>%</p>
                            <p class="m_font_size12 ${stage_yield_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : interest_rate}</span></p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : preincomerate}</span>%</p>
                            <p class="m_font_size12 ${fund_rate_list}">成立以来收益:<span class="m_text_red m_font_size18">${topLevel ? '--' : tools.fmoney(found_rate)}</span>%</p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${topLevel ? '--' : dk_income_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${topLevel ? '--' : dk_income_rate_chg}</span>%</p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${threshold_amount_list}">起购:${topLevel ? '--' : threshold_amount}万元</span>
                                <span class="${closed_period_list}">期限:${topLevel ? '--' : inrest_term}</span>
                                <span class="${lock_period_list}">锁定期:${topLevel ? '--' : inrest_term}</span>
                                <span class="${this_year_rate_list}">今年来收益:${topLevel ? '--' : this_year_rate ? tools.fmoney(this_year_rate) : "--"}</span>
                                <span class="${nav_list}">最新净值:${topLevel ? '--' : nav}</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${topLevel ? 'm_golden' : tools.priBtnObj(item.buy_state, item.prod_sub_type2).btnClass}">
                                ${topLevel ? topLevel : tools.priBtnObj(item.buy_state, item.prod_sub_type2).btnText}
                            </span>
                        </li>
                    </ul>
                    `
            } else if (item.prod_sub_type2 == '200') {
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                // var income_period_list_chg = item.income_period_list_chg == '1' ? '' : 'display_none' //是否展示近X月涨跌幅
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                //数据展示
                var preincomerate = item.preincomerate ? tools.fmoney(item.preincomerate) : '--' //年化标准
                var threshold_amount = item.threshold_amount ? item.threshold_amount : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                var nav = item.nav ? tools.fmoney(item.nav, 4) : '--'
                var holding_days = item.holding_days ? item.holding_days : '--'
                var per_yield = item.per_yield ? tools.fmoney(item.per_yield) : '--'
                var buy_state = item.buy_state
                var buy_state_name, btnClass
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                //公募列表展示
                childrenListHtml += `
                    <ul class="classificationList_card_main flex"  fundCode="${fundCode}">
                        <li class="main_flxe vertical_line">
                            <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000">${prod_sname}</p>
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                <span class="${closed_period_list}">期限:${inrest_term}</span>
                                <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                                <span class="${recommended_holding_list}">建议持有${holding_days}天以上</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${btnClass}">
                                ${buy_state_name}
                            </span>
                        </li>
                    </ul>
                `
            } else if (item.prod_source == '2'){ //投顾特殊处理
                var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
                var prod_per_min_amt = item.prod_per_min_amt //投资金额
                var comb_sname = item.comb_sname;
                var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none'  //是否展示特点
                var corner_marker_list = item.corner_marker_list == '1' ? '' : 'display_none'  //是否展示角标
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none'  //是否展示策略特点
                var buy_deadline_list = item.buy_deadline_list == '1' ? '' : 'display_none';
                var page_threshold_amount_list = item.page_threshold_amount_list == '1' ? '' : 'display_none'; //是否展示营销页首投金额
                //数据展示
                var page_first_per_min = item.page_first_per_min ? item.page_first_per_min : '';
                var income_period_type_desc = item.income_period_type ? item.income_period_type : '--' //近多少年化

                var annual_income = item.annual_income; // 目标年化收益
                var holding_time = item.holding_time ? item.holding_time : '--' // 建议持有时长
                var characteristic = item.characteristic; // 特点
                var strategic_characteristic = item.strategic_characteristic; // 策略特点   
                var threshold_amount = item.first_per_min ? item.first_per_min : '--' //起购金额
                var corner_marker = item.corner_marker; // 角标描述
                var mechanism = item.mechanism; // 投顾机构
                var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                var buy_state = item.purchase_state;
                var buy_state_name, btnClass;
                var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅
                var income_name = item.income_name ? item.income_name : ''; //目标收益率文案
                var holding_time_name = item.holding_time_name ? item.holding_time_name : '';
                var buy_deadline = item.buy_deadline ? item.buy_deadline : '';
                // threshold_amount = page_threshold_amount_list == '1' ? page_first_per_min : threshold_amount;
                if(buy_deadline){
                    buy_deadline = tools.ftime(buy_deadline.substr(4, 4), "月") + "日 " + tools.ftime(buy_deadline.substr(8, 8), ".").substr(0, 5)
                }
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "运作中";
                    btnClass = "sold_out";
                }
                //投顾列表展示
                childrenListHtml += `
                    <ul class="classificationList_card_main flex" fundCode="${fundCode}">
                        <li class="main_flxe vertical_line">
                            <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000">${comb_sname}</p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${establish_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${annual_income_list}">${income_name}:<span class="m_text_red m_font_size18">${annual_income}</span></p>
                            <p class="m_font_size12 ${buy_deadline_list}">截止时间:<span class="m_text_red m_font_size14">${buy_deadline}</span></p>
                            <p class="m_font_size12 ${strategic_characteristic_list}">策略特点:<span class="m_text_red">${strategic_characteristic}</span></p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${page_threshold_amount_list}">起购:${page_first_per_min}元</span>
                                <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                <span class="${recommended_holding_list}">${holding_time_name}:${holding_time}</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                                <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${btnClass}">
                                ${buy_state_name}
                            </span>
                        </li>
                    </ul>
                `
            } else if (item.prod_source == '3') { //系列产品 一键分散买合集
                var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅
                var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                // var income_period_list_chg = item.income_period_list_chg == '1' ? '' : 'display_none' //是否展示近X月涨跌幅
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none' //是否展示策略特点值
                var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none';//是否展示特点
                var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                
                //数据展示
                var preincomerate = item.preincomerate ? tools.fmoney(item.preincomerate) : '--' //年化标准
                var threshold_amount = item.threshold_amt ? item.threshold_amt : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                var nav = item.nav ? tools.fmoney(item.nav, 4) : '--'
                var holding_days = item.holding_days ? item.holding_days : '--'
                var per_yield = item.per_yield ? tools.fmoney(item.per_yield) : '--'
                var buy_state = item.buy_state
                var buy_state_name, btnClass
                var holding_time = item.holding_time;
                var characteristic = item.characteristic
                var strategic_characteristic = item.strategic_characteristic //策略特点值
                var holding_time_name = item.holding_time_name
                var income_name = item.income_name ? item.income_name : ''; //目标收益率文案
                var annual_income = item.annual_income; // 目标年化收益

                let income_name_str = item.series_type == '2' ?`<p class="m_font_size12 ${annual_income_list}">${income_name}:<span class="m_text_red m_font_size18">${annual_income}</span></p>` : ``;
                let strategic_characteristic_str = item.series_type == '2' ?`<p class="m_font_size12 ${strategic_characteristic_list}">策略特点:<span class="m_text_red">${strategic_characteristic}</span></p>`: ``;
                let characteristic_str = item.series_type == '2' ? `<p class="m_font_size12 m_text_darkgray666 ${characteristic_list}"><span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span></p>`: '';
                //去掉列表前两个 其中去掉 系列投顾
                let new_pro_str = item.series_type == '2' ? `<p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>` : `<p class="m_font_size12 ${income_period_list}">其中${item.income_prod_name}${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                <p class="m_font_size12 ${income_period_list_chg}">其中${item.income_prod_name}${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>`
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                childrenListHtml += `
                <ul class="classificationList_card_main flex" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000">${prod_sname}</p>
                        <p class="m_text_999 m_font_size12">
                            ${new_pro_str}
                            ${income_name_str}
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                            <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                            ${strategic_characteristic_str}
                        </p>
                        <p class="m_text_999 m_font_size12">
                            <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                            <span class="${closed_period_list}">期限:${inrest_term}</span>
                            <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                            <span class="${recommended_holding_list}">${item.series_type == '2' ? holding_time_name : '建议持有'}${holding_time}</span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                            ${characteristic_str}
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${btnClass}">
                            ${buy_state_name}
                        </span>
                    </li>
                </ul>
                `
            }
        })
        return childrenListHtml;
    }
    //补充代码
    function clearPath() {
        appUtils.setSStorageInfo("pageTopUrlInfo","");
        appUtils.setSStorageInfo("skipURL", '');
    }
    //去测评
    function pageTo_evaluation() {
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
        }, '', '确定')
    }
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //点击产品跳转首页
        appUtils.bindEvent($(_pageId + " .card_list"), function () {
            appUtils.pageInit(_pageCode, "login/userIndexs")
        });
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //活动规则
        appUtils.bindEvent($(_pageId + " #clickActivityRules"), function () {
            $(_pageId + " #activityRules").show();
        });
        //隐藏活动规则
        appUtils.bindEvent($(_pageId + " #activityRules .ruleSure"), function (e) {
            $(_pageId + " #activityRules").hide();
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #pop_layer #share_WeChatFriend"), function () {
            tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', activityInfo.activity_id);
            appUtils.setSStorageInfo("activityInfo", activityInfo);
            if (!activityInfo || !share_template) {
                common.share("23", "1");
                return;
            }
            let results = {
                banner_id: activityInfo.banner_id,
                group_id: activityInfo.group_id,
                activity_id: activityInfo.activity_id,
            }
            let query_params = {
                registered_mobile: ut.getUserInf().mobileWhole,
                share_template: share_template
            }
            service.reqFun102012(query_params, async function (data) {
                if (data.error_no == '0') {
                    var result = data.results[0];
                    if (data.results[0] && data.results[0].share_form == 1) {  // 链接
                        results.title = result.title;
                        results.content = result.content;
                        results.img_url = result.img_url;
                        let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                        return tools.pageShare(results, '23', _pageId, shareUrlLast, null, activityInfo.activity_id);//活动页面分享
                    } else if (data.results[0] && data.results[0].share_form == 2) { // 图片 3--页面
                        shareCard();
                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
            // common.share("23", share_template);
            pageTouchTimer = setTimeout(() => {
                // 分享加次数
                service.reqFun108016({ activity_id: activityInfo.activity_id }, function (data) {
                    if (data.error_no != "0") {//失败
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    layerUtils.iMsg(-1, "分享成功");
                    getActivityInfo();
                })
            }, 6000);
        });
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #pop_layer #cancelShare"), function () {
            // $(_pageId + " #start").removeClass("notclick");
            $(_pageId + " #pop_layer").hide();
        });
        //抽奖记录
        appUtils.bindEvent($(_pageId + " #clickMyRewards"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            $(_pageId + " .content_list").html("");
            var QueryParam = {
                "activity_id": activityInfo.activity_id,
                "cust_no": activityInfo.cust_no,
            }
            service.reqFun108018(QueryParam, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var results = data.results[0].data;
                if (validatorUtil.isNotEmpty(data.results[0].data) && results.length > 0) {
                    var html = "";
                    for (var i = 0; i < results.length; i++) {
                        var reward_name = results[i].reward_name;//奖励内容
                        var crt_time = results[i].crt_time;//奖励时间
                        html += `<div class="item">
                            <em style="display: none" class="recordInfo">${JSON.stringify(results[i])}</em>  
                            <p style="padding-right:0.05rem;width: 8%;text-align: right">${i + 1}.</p>
                            <p style="padding-right:0.05rem;width: 43%">${crt_time.substr(0, 4)}年${crt_time.substr(4, 2)}月${crt_time.substr(6, 2)}日</p>\n
                            <p style="width: 29%">${reward_name}</p>
                            ${results[i].reward_type == "2" ? `<p class="get_reward">${results[i].is_entered == '1' ? '查看' : '领取'}</p>` : ''}
                          </div > `
                    }
                    $(_pageId + " .content_list").html(html);
                } else {
                    $(_pageId + " .content_list").html("<div style='text-align: center;line-height: 4;font-size: 0.14rem'>暂无领取记录</div>");
                }
                $(_pageId + " #rewardRecord").show();
            }, { "isShowWait": false });
        });
        //隐藏抽奖记录
        appUtils.bindEvent($(_pageId + " .recordSure"), function (e) {
            $(_pageId + " #rewardRecord").hide();
        });
        //抽奖
        appUtils.bindEvent($(_pageId + " #start"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if (state == "2") {
                reward_num = 0;
                layerUtils.iMsg(-1, "活动未开始！", 2);
                return;
            }
            if (state == "3") {
                reward_num = 0;
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            // 没有配置转盘内容的情况
            if (!turntable_list || turntable_list.length == '0') {
                reward_num = 0;
                layerUtils.iMsg(-1, "参与人数过多", 2);
                return;
            }
            if (shareflag === "0" && reward_num <= 0) {
                layerUtils.iConfirm("分享活动给好友可获得一次抽奖机会", function () {
                    $(_pageId + " #pop_layer").show();
                }, function () {

                }, "去分享", "取消");
                return;
            }
            if (reward_num <= 0) {
                layerUtils.iMsg(-1, "今日抽奖次数已用完，请下次再来", 2);
                return;
            }
            if ($(_pageId + " #start").hasClass("notclick")) {
                return;
            }

            // 按钮禁用
            $(_pageId + " #start").addClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0_end.png'>");
            luck.speed = 100;
            var param = {
                "activity_id": activityInfo.activity_id,
                "cust_no": activityInfo.cust_no
            };
            service.reqFun108052(param, function (data) {
                // -2 未绑卡
                // -3 活动尚未开始或已结束
                // -4 次数已用完
                // -6 当前不符合参与条件
                // -999011 请求过于频繁，请稍候再试
                //奖池为空时默认谢谢参与
                if (data.error_no == "-2" || data.error_no == "-3" || data.error_no == "-4" || data.error_no == "-5" || data.error_no == "-6" || data.error_no == "-7" || data.error_no == "-999011") {
                    stopIndex = 0;
                    $(_pageId + " #start").removeClass("notclick");
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.error_no != "0") {
                    stopIndex = 5;
                    $(_pageId + " .index_info").html(moneyObj["0"].reward_text);
                    $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[0].src);
                    $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[0].btn_txt);
                    $(_pageId + " .index_info_tishi").html('');
                    roll();
                    reward_num--;
                    if (state == "1" && reward_num <= 0) {
                        $(_pageId + " #start .start_btn_name").html("再抽一次");
                        $(_pageId + " #start").removeClass("notclick");
                    }
                    return;
                }
                var results = data.results[0];
                var money = Number(results.reward_vol) + "";
                var keyword;
                var moneyKey;
                //奖励类型reward_type 1:随机积分 0:固定积分 2:实物
                if (results.reward_type === "2") { //实物
                    // 实物类型 7-绒布对联、8-小米随手杯、9-小爱音箱
                    keyword = "physical" + results.reward_vol;
                } else if (results.reward_type === "0") { //固定积分
                    keyword = money;
                } else if (results.reward_type === "1") { //随机积分
                    keyword = 'auto';
                } else if (results.reward_type === "6") { // 候补奖励 发的也是随机积分
                    keyword = 'auto';
                }
                var autoKey = []; // 存放以keyword开头的对象，随机取一个
                for (let key in moneyObj) {
                    var keyFront = key.split("-")[0];
                    if (keyword == keyFront) { // 是否以keyword开头
                        autoKey.push(key);
                    }
                }
                moneyKey = autoKey[Math.floor((Math.random() * autoKey.length))] // auto开头的对象，随机取一个
                if (validatorUtil.isEmpty(moneyObj[moneyKey])) {
                    // 随机选一个随机积分
                    var autoKey = [];
                    for (let key in moneyObj) {
                        var keyFront = key.split("-")[0];
                        if (keyFront == "auto") {
                            autoKey.push(key);
                        }
                    }
                    moneyKey = autoKey[Math.floor((Math.random() * autoKey.length))] // auto开头的对象，随机取一个
                }
                $(_pageId + " .index_info").html(moneyObj[moneyKey].reward_text ? moneyObj[moneyKey].reward_text : money + "积分");
                $(_pageId + " .index_info_tishi").html(moneyObj[moneyKey].prompt ? moneyObj[moneyKey].prompt : '');
                stopIndex = moneyObj[moneyKey].stopIndex[Math.floor(Math.random() * moneyObj[moneyKey].stopIndex.length)]
                $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[moneyKey].src);
                $(_pageId + " #rewardResult .sureBtn").attr("data-type", results.reward_type)
                $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[moneyKey].btn_txt);
                roll();
                reward_num--;
                if (state == "1" && reward_num <= 0) {
                    $(_pageId + " #start .start_btn_name").html("再抽一次");
                    $(_pageId + " #start").removeClass("notclick");
                }
            }, { "isShowWait": false });
        });
        //隐藏中奖弹框
        appUtils.bindEvent($(_pageId + " .sureBtn"), function (e) {
            $(_pageId + " #rewardResult").hide();
        });
        //积分兑换
        appUtils.bindEvent($(_pageId + " #pointsFor"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            appUtils.setSStorageInfo("front_pageUrl", `${_pageCode}?activity_id=${activityInfo.activity_id}`);
            appUtils.pageInit("guide/advertisement", "vipBenefits/index", { 'luckflag': 'dargonYearDraw' })
        });
        // 邀好友抽奖
        appUtils.bindEvent($(_pageId + " #inviteBtn"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            $(_pageId + " #pop_layer").show();
        });
        //猜涨
        appUtils.bindEvent($(_pageId + " .bg_red"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            if (guessActivityInfo.state == "1") {
                if (guess_state == '2' || guess_state == '3') {
                    return layerUtils.iAlert(guess_words);
                }
                if (guessActivityInfo.guess_answer == '1') return layerUtils.iAlert("您已选择看涨，下一交易日21时开奖");
                if (guessActivityInfo.guess_answer == '2') return layerUtils.iAlert("您已选择看跌，下一交易日21时开奖");
                layerUtils.iConfirm(`是否选择看涨`, function () {
                    competition('1')
                }, function () {
                }, "确定", "取消");
            } else {
                if (guessActivityInfo.state == "2") return layerUtils.iAlert("活动未开始");
                if (guessActivityInfo.state == "3") return layerUtils.iAlert("活动已结束");
            }

        });
        //猜跌
        appUtils.bindEvent($(_pageId + " .bg_green"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            if (guessActivityInfo.state == "1") {
                if (guess_state == '2' || guess_state == '3') {
                    return layerUtils.iAlert(guess_words);
                }
                if (guessActivityInfo.guess_answer == '1') return layerUtils.iAlert("您已选择看涨，下一交易日21时开奖");
                if (guessActivityInfo.guess_answer == '2') return layerUtils.iAlert("您已选择看跌，下一交易日21时开奖");
                layerUtils.iConfirm(`是否选择看跌`, function () {
                    competition('2')
                }, function () {
                }, "确定", "取消");
            } else {
                if (guessActivityInfo.state == "2") return layerUtils.iAlert("活动未开始");
                if (guessActivityInfo.state == "3") return layerUtils.iAlert("活动已结束");
            }
        });
        //查看竞猜规则
        appUtils.bindEvent($(_pageId + " .guessRules"), function () {
            $(_pageId + " #guessActivityRules").show();
        });
        //关闭竞猜规则
        appUtils.bindEvent($(_pageId + " #guessActivityRules .ruleSure"), function () {
            $(_pageId + " #guessActivityRules").hide();
        });
        //竞猜记录
        appUtils.bindEvent($(_pageId + " .guess_record"), function () {
            let live_activity_id = guessActivityInfo.id;
            sessionStorage.live_activity_id = live_activity_id; //缓存竞猜活动ID
            appUtils.setSStorageInfo("front_pageUrl", `${_pageCode}?activity_id=${activityInfo.activity_id}`);
            appUtils.pageInit("guide/advertisement", "liveBroadcast/guessingRecord", { 'luckflag': 'dargonYearDraw' });
        });
        //TODO
        appUtils.preBindEvent($(_pageId + " #rewardRecord .content_list"), ".get_reward", function () {
            var recordInfo = JSON.parse($(this).parent().find(".recordInfo").html())
            appUtils.setSStorageInfo("front_pageUrl", `${_pageCode}?activity_id=${activityInfo.activity_id}`);
            appUtils.pageInit("guide/advertisement", "activity/receiveAward", { recordInfo: recordInfo, 'luckflag': 'dargonYearDraw' })
        })
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            appUtils.clearSStorage("fund_code");
            appUtils.clearSStorage("productInfo");
            $(_pageId + ".qualifiedInvestor").hide();
        });
        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    if (sessionStorage.digUserAuthenticationStatus == '1') {
                        appUtils.pageInit("login/userIndexs", sessionStorage.digUserAuthenticationUrl);
                        sessionStorage.digUserAuthenticationStatus = '';
                        sessionStorage.digUserAuthenticationUrl = '';
                        return;
                    }
                    $(_pageId + ".qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    // setData();
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
        //跳转产品详情页面
        appUtils.preBindEvent($(_pageId + " .card"), ".card_main .classificationList_card_main", function () {
            var productInfo = JSON.parse($(this).find("em").text()); //存储数据格式
            let parentsHtml = $(this).parent().parent().children('.classificationList_card_top')[0]
            if (productInfo.prod_source == '3') { //系列产品
                appUtils.setSStorageInfo("series_info", productInfo);   //存储分类一级内容
                return pageTo_fundsBuy(productInfo);
            }
            var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
            appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
            if (productInfo.prod_source == '2') { // 投顾 
                appUtils.setSStorageInfo("combProductInfo", productInfo);   //存储分类一级内容
                if (productInfo.prod_propagate_temp) {
                    tools.setPageToUrl('combProduct/combProdMarketing', '1')
                } else {
                    tools.setPageToUrl('combProduct/combProdDetail', '1')
                }
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_pageCode)) return;
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                // var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    } else if (invalidFlag == '1') {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, "取消", "更换");
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.comb_code);

                    if (productInfo.prod_propagate_temp) {
                        appUtils.pageInit(_pageCode, "combProduct/combProdMarketing");
                    } else {
                        //缓存当前是否为系列投顾从产品
                        appUtils.setSStorageInfo("isSeriesComb", '0');
                        appUtils.pageInit(_pageCode, "combProduct/combProdDetail");
                    }
                });
                return;
            } else if (productInfo.fund_code == '000709') {
                //跳转晋金宝
                tools.setPageToUrl('thfund/inputRechargePwd', '1')
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_pageCode)) return;
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消");
                        return;
                    } else if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, "取消", "更换");
                    }
                    appUtils.pageInit(_pageCode, "thfund/inputRechargePwd", {});
                });
                return;
            } else if (productInfo.prod_sub_type2 == '200') {   //公募
                if (productInfo.prod_propagate_temp) {
                    tools.jumpMarketingPage(_pageCode, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpDetailPage(_pageCode, productInfo.prod_sub_type, productInfo.prod_sub_type2)
                }
            } else if (productInfo.prod_sub_type2 == '100') {   //私募
                clearPath();
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_pageCode)) return;
                if (userAuthenticationStatus == '0') {
                    return $(_pageId + ".qualifiedInvestor").show();
                }
                if (productInfo.prod_propagate_temp) {
                    tools.jumpMarketingPage(_pageCode, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpPriDetailPage(_pageCode, productInfo.prod_sub_type2);
                }
            }
            // appUtils.pageInit(_pageCode, "login/listMorePage", {});
        }, 'click');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " .time_card").hide();
        $(_pageId + " .activity_pop_layer").hide();
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #pointsFor").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #rewardResult .sureBtn span").html("");
        $(_pageId + " #qr_img").hide();
        $(_pageId + " #code").show();
        $(_pageId + " #guessActivityRules").hide();
        clearTimeout(pageTouchTimer);
        appUtils.clearSStorage("front_pageUrl");
        service.destroy();
    };

    function roll() {
        luck.times += 1;
        luck.roll();
        if (luck.times > luck.cycle + 10 && luck.prize == luck.index) {
            clearTimeout(luck.timer);
            luck.prize = -1;
            luck.times = 0;
            if (stopIndex !== 4) {
                setTimeout(function () {
                    $(_pageId + " #rewardResult").show();
                }, 600);
            } else {
                setTimeout(function () {
                    $(_pageId + " #rewardResult").show();
                }, 600);
            }
            $(_pageId + " #start").removeClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0.png'>");
        } else {
            if (luck.times < luck.cycle) {
                luck.speed -= 10;
            } else if (luck.times == luck.cycle) {
                luck.prize = stopIndex;
            } else {
                if (luck.times > luck.cycle + 10 && ((luck.prize == 0 && luck.index == 7) || luck.prize == luck.index + 1)) {
                    luck.speed += 110;
                } else {
                    luck.speed += 20;
                }
            }
            if (luck.speed < 40) {
                luck.speed = 40;
            }
            luck.timer = setTimeout(roll, luck.speed);
        }
        return false;
    }


    var dargonYearActivityModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = dargonYearActivityModule;
});
