<div class="page" id="account_empower" data-pageTitle="授权" data-refresh="true">
    <section class="main fixed add_padding" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">授权</h1>
            </div>
        </header>
        <article class="bg_blue no_padding">
            <div class="empower">
                <h3>请确认以下授权信息</h3>
                <div class="top_card">
                    <ul class="top">
                        晋金财富账户信息
                    </ul>
                    <ul class="bottom">
                        <li>
                            <span>姓名：</span>
                            <span class="name"></span>
                        </li>
                        <li>
                            <span>注册手机号：</span>
                            <span class="mobile_ck"></span>
                        </li>
                        <li>
                            <span>银行卡号：</span>
                            <span class="card_no"></span>
                        </li>
                        <li>
                            <span>银行名称：</span>
                            <span class="bank_name"></span>
                        </li>
                        <li>
                            <span>银行预留手机号：</span>
                            <span class="bankReservedMobile"></span>
                        </li>
                    </ul>
                </div>
                <div class="bottom_card">
                    <ul class="top">
                        晋金所授权信息
                    </ul>
                    <ul class="bottom">
                        <li>1.授权晋金宝金额自动转入到晋金财富晋金宝</li>
                        <li>2.授权晋金投付息、到期兑付到晋金财富晋金宝</li>
                        <li class="ui field text" id="yzmBox">
                            <div class="grid_03 grid_02 grid mt10">
                                <div class="ui field text input_box2 has_border" id="yzmBox">
                                    <label class="short_label2">验证码</label>
                                    <input type="tel" class="ui input code_input" id="yzm" type="tel" maxlength="6" placeholder="">
                                    <a id="getYzm" data-state="true">获取验证码</a>
                                </div>
                            </div>
                        </li>
                        <!-- 语音验证码 -->
                        <li class="finance_det recharge_det">
                            <dl class="bank_limit">
                                <dt></dt>
                                <dd id="talkCode" style="display: block;">晋金所将致电您的手机语音告知验证码
                                </dd>
                                <dd>
                                </dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <!-- <div class="agreement">

                </div> -->
                <div class="btn" style="margin-top: 0.1rem;">
                    <a href="javascript:void(0);" class="ui button block rounded" id="nextStep">下一步</a>
                </div>
            </div>
            <!-- 协议相关弹窗 -->
            <div class="agreement_layer" style="display:none">
                <div class="agreement_popup in">
                    <div class="agreement_popup_header">
                        <div class="new_close_btn"></div>
                        <h3>相关协议</h3>
                    </div>
                    <ul class="agreement_list flex vertical_line"></ul>
                </div>
            </div>
        </article>
    </section>
</div>