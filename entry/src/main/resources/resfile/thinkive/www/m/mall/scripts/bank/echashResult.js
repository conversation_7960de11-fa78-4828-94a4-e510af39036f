//取现结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageUrl = "bank/echashResult",
        _pageId = "#bank_echashResult ";
    var num;
    var outputInfo;
    var tools = require("../common/tools");//升级
    var t;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        tools.getActivityInfo(_pageId,_pageUrl)
        num = 5;
        outputInfo = appUtils.getPageParam();
        countDown();
    } 

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + "#goHome"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageBack();
        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-2);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit(_pageUrl, "bank/transaction");
        })
    }

    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun151104(outputInfo, function (data) {
                    $(_pageId + ".load").hide();
                    if (data.error_no == "0") {
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败
                        if (data.results[0].trans_status == "3") {
                            $(_pageId + " .success").show();//取现成功
                        } else if (data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            if (data.results[0].host_msg) {
                                $(_pageId + ' #failinf').text('失败原因：' + data.results[0].host_msg);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，取现失败');
                            }
                            $(_pageId + " .fail").show();//取现失败
                        } else {
                            $(_pageId + " .wait").show();//取现无结果
                        }
                    } else {
                        $(_pageId + " .wait").show();//存入无结果
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info)
                    }
                })
            }

        }, 1000)
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .pay_done").hide();
        $(_pageId + ".load").show();
        window.clearInterval(t);

    }

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
