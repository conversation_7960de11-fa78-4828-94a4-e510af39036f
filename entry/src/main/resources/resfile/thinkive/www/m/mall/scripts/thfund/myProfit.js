// 晋金宝升级
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        serviceConstants = require("constants"),
        _pageUrl = "thfund/myProfit",
        _pageId = "#thfund_myProfit ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");//升级
    var chartsUtils = require("chartsUtils");
    var data;
    var spliceDate;//默认展示7天数据
    var tips; //默认提示为七日年化
    var timeOptions;//绘制折线图配置
    var show_income; //万份收益
    var incomeunit;  //七日年化
    var begin_time;
    var resultParam;
    var _fund_code = "";
    var _chartFlag = "1";
    var _acct_no = ""

    function init() {
        _fund_code = "000709";
        //默认展示七日年化收益率 折线图
        reqFun102006()

        _acct_no = ut.getUserInf().fncTransAcctNo;

        //现金宝资产查询101901
        reqFun101901();
        //获取近7日年化、万分收益
        getProfit();
        $(_pageId + " #mask").hide();
        spliceDate = -200;
        tips = "七日年化";
        //清除缓存数据
        appUtils.setSStorageInfo("toInputRechargePwd", "");
        appUtils.setSStorageInfo("toEnchashment", "");
        //默认展示七日年化数据
        $(_pageId + " .linechart_new li").removeClass("activity").eq(0).addClass("activity");
        $(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
        resultParam = appUtils.getSStorageInfo("toProfit");
        //页面埋点初始化
        tools.initPagePointData();
    }
    //去测评
    function pageTo_evaluation(){
        layerUtils.iAlert("您的风险测评已到期，请重新测评",  ()=> {},()=>{
            appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
        },'','确定')
    }
    //绑定事件
    function bindPageEvent() {
        //退出
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //显示冻结金额提示
        appUtils.bindEvent($(_pageId + " #jjdj"), function () {
            $(_pageId + " .pop_box").show();
        });
        //隐藏冻结金额提示
        appUtils.bindEvent($(_pageId + " #close_msg"), function () {
            $(_pageId + " .pop_box").hide();
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageUrl)
        });
        //充值
        appUtils.bindEvent($(_pageId + " #chongzhi"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageUrl)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if(perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", ()=> {}, ()=> {
                    appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                let operationId = 'riskAssessment'
                layerUtils.iConfirm("您还未进行风险测评", function () {
                    appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
                }, function () {
                }, "去测评", "取消",operationId);
                return;
            }else if(invalidFlag == '1'){
                pageTo_evaluation()
                return
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_pageUrl, "thfund/inputRechargePwd", {});
            });
        });

        //取现
        appUtils.bindEvent($(_pageId + " #quxian"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageUrl)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            if(perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", ()=> {}, ()=> {
                    appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_pageUrl, "thfund/enchashment");
            });
        });
        //交易记录
        appUtils.bindEvent(_pageId + " #jyjl", function () {
            appUtils.pageInit(_pageUrl, "thfund/JJBtransaction", {});
        });
        //展示限定时间的收益
        appUtils.bindEvent(_pageId + " .spliceDate li", function () {
            $(_pageId + " .spliceDate").find("li").removeClass("active").filter(this).addClass("active");
            var index = $(this).index(),
                selected = chat.rangeSelector.selected;
            if (index === selected) {
                return false;
            }
            chat.rangeSelector.clickButton(index);
        })
        //7日年化收益
        appUtils.bindEvent(_pageId + " .linechart_new li", function () {
            $(_pageId + " .linechart_new li").removeClass("activity").filter(this).addClass("activity");
            $(_pageId + " .spliceDate").find("li").removeClass("active").eq(0).addClass("active");
            if ($(this).index() == 0) {
                tips = "七日年化";
                //七日年化折线图 需要的数据
                _chartFlag = "1";
            } else {
                tips = "万份收益";
                _chartFlag = "2";
            }
            //展示折线图
            reqFun102006();
        });
        //去定投
        appUtils.bindEvent(_pageId + " .fixedInvestment",function(){
            //跳转新增定投页面
            appUtils.setSStorageInfo("fund_code", _fund_code);
            if (!common.loginInter(_pageUrl)) return;
            if (!ut.hasBindCard(_pageUrl)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if (invalidFlag == '1') return tools.pageTo_evaluation(_pageUrl)
            common.changeCardInter(() => {
                appUtils.setSStorageInfo("isAdvisoryInvestment", '2');
                appUtils.pageInit(_pageUrl, "fixedInvestment/startInvestment", {});
            });
        })
        // //关闭弹框
        // appUtils.bindEvent(_pageId + " .new_btn",function(){
        //     $(_pageId + " #mask").hide();
        // })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + ' #container1').html('');
        $(_pageId).find("#totalMoney").html("");//显示总金额
        $(_pageId).find("#earnZR").html("");//显示昨日收益
        $(_pageId).find("#earnLJ").html("");//显示累计收益
        $(_pageId + ' #now_date').html("");
        $(_pageId + " .pop_box").hide();
        $(_pageId + " #wfsy").text("");
        $(_pageId + " #qrnh").text("");
        data = null;
    }


    var details = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };

    /********************** 自定义方法 ********************************/

    //设置时间为highChart所需时间格式
    function datearr(data) {
        for (var i = 0; i < data.length; i++) {
            var x = data[i].x.toString();
            Date.UTC()
            data[i].x = Date.UTC(x.substring(0, 4), x.substring(4, 6) - 1, x.substring(6, 8));
        }
        return data;
    }

    function pageBack() {
        appUtils.pageBack();
    }
    //展示七日年化收益率 折线图
    function reqFun102006() {
//		tips = "七日年化";
        var params = {
            fund_code: _fund_code
        };
        service.reqFun102006(params, function (result) {
            if (result.error_no == 0) {
                var results = result.results;
                var data = [];

                //七日年化折线图 需要的数据
                if (_chartFlag == "1") {
                    for (var i = 0; i < results.length; i++) {
                        var annu_yield = Number(results[i].annu_yield);
                        var obj = {
                            x: results[i].end_date.substring(0, 8),
                            y: annu_yield,
                        }
                        data.push(obj);
                    }
                } else {
                    for (var i = 0; i < results.length; i++) {
                        var obj = {
                            x: results[i].end_date.substring(0, 8),
                            y: Number(results[i].return_pertt)
                        }
                        data.push(obj);
                    }
                }

                data = data.reverse();
                data = data.slice(spliceDate);
                data = datearr(data);
                showChart(data, tips, function (c) {
                    chat = c;
                    chat.rangeSelector.clickButton(0);
                })
            } else {
                layerUtils.iAlert(result.error_info);
            }
        });
    }

    //显示晋金宝折线图
    function showChart(data, tips, callback) {
        tips = tips || "七日年化";
        timeOptions = {
            lang: {
                rangeSelectorZoom: null // 不显示 'zoom' 文字
            },
            rangeSelector: {
                inputEnabled: false,
                buttonPosition: '11',
                buttons: [{
                    type: 'day',
                    count: 7,
                    text: '7日'
                }, {
                    type: 'month',
                    count: 1,
                    text: '1月'
                }, {
                    type: 'month',
                    count: 3,
                    text: '3月'
                }, {
                    type: 'month',
                    count: 6,
                    text: '6月'
                }],
                buttonTheme: {
                    display: "none"
                }
            },
            scrollbar: {
                enabled: false
            },
            navigator: {
                enabled: false
            },
            chart: {
                type: 'areaspline',
                panning: false, //禁用放大
                pinchType: ''//禁用手势操作
            },
            title: {
                text: null
            },
            xAxis: {
                title: {
                    text: null
                },
                // tickPixelInterval: 70,
                gridLineWidth: 1,
                type: 'datetime',
                // labels: {
                //     format: '{value:%y/%m/%d}'
                // },
                dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%H:%M',
                    day: '%m/%d',
                    week: '%m/%d',
                    month: '%y/%m',
                    year: '%Y'
                }
            },
            yAxis: {
                title: {
                    text: ''
                },
                gridLineWidth: 1,

                options: {
                    startOnTick: false,
                    endOnTick: false,
                },
                labels: {
                    format: '{value}%'
                },
                tickPixelInterval: 20,
            },
            legend: {
                enabled: false
            },
            plotOptions: {
                series: {
                    turboThreshold: 0
                },
                areaspline: {
                    fillColor: {
                        linearGradient: {
                            x1: 0,
                            y1: 0,
                            x2: 0,
                            y2: 1
                        },
                        stops: [
                            [0, "rgba(632,64,63,0.5)"],
                            [1, "rgba(255,255,255,0.5)"]
                        ]
                    },
                    marker: {
                        radius: 2
                    },
                    lineWidth: 2,
                    states: {
                        hover: {
                            lineWidth: 2
                        }
                    },

                    threshold: null
                }
            },
            tooltip: {
                backgroundColor: '#1E4DA9',   // 背景颜色
                borderColor: '',         // 边框颜色
                borderRadius: 0,             // 边框圆角
                borderWidth: 0,               // 边框宽度
                shadow: true,                 // 是否显示阴影
                animation: true,               // 是否启用动画效果
                crosshairs: "Mixed",
                followTouchMove: true,
                style: {                      // 文字内容相关样式
                    color: "#ffffff",
                    fontSize: "12px",
                    fontWeight: "normal",
                    fontFamily: "Courir new"
                },
                type: "datetime",
                formatter: function (item) {
                    var time = new Date(this.x)
                    return "<div>" + time.getFullYear() + '/' + (time.getMonth() + 1) + '/' + time.getDate() + "</div>" +
                        "<div>" + tips + ":" + (+this.y).toFixed(4) + "</div>"
                },
                useHTML: true,
                headerFormat: '<table><small>{point.key}</small><table>',
                pointFormat: '<tr><td >{series.name} : </td>' +
                    '<td style="text-align: right">{point.y}%</td></tr>',
                footerFormat: '</table>',

            },
            series: [{
                name: "<span style='color:#ffffff'>" + tips + "</span>",
                color: "rgba(632,64,63,1)",
                borderWidth: 2,
                data: data
            }]
        };

        if (tips == "七日年化") {
            timeOptions.yAxis["labels"]["format"] = '{value}%';
            timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}%</td></tr>'
        } else {
            timeOptions.yAxis["labels"]["format"] = '{value}';
            timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}</td></tr>'
        }
        $(_pageId + ' #container1').highcharts('StockChart', timeOptions, callback);
        Highcharts.setOptions({
            lang: {
                rangeSelectorZoom: '' // 不显示 'zoom' 文字
            }
        });

    }


    //现金宝资产查询101901
    function reqFun101901() {
        var params = {
            acct_no: _acct_no,
        }
        service.reqFun101901(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                var total_vol = tools.fmoney(results.total_vol);
                var trd_frozen_vol = tools.fmoney(results.trd_frozen_vol);
                var yest_income = tools.fmoney(results.yest_income);
                var totaladd_income = tools.fmoney(results.totaladd_income);
                var modi_date = results.modi_date;
                modi_date = tools.FormatDateText(modi_date.substring(4));
                $(_pageId + ' #totalMoney').html(total_vol);
                $(_pageId + ' #forzenMoney').html(trd_frozen_vol);
                $(_pageId + ' #earnZR').html(yest_income);
                $(_pageId + ' #earnLJ').html(totaladd_income);
                $(_pageId + ' #now_date').html(modi_date);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获得七日年化、万份收益值
    function getProfit() {
        service.reqFun102002({fund_code: "000709"}, function (data) {
            if (data.error_no == 0) {
                var return_pertt = tools.fmoney(data.results[0].return_pertt, 4) + "元";//万份收益
                var annu_yield = tools.fmoney(data.results[0].annu_yield, 4) + "%";// 七日年化
                $(_pageId + " #wfsy").text(return_pertt);
                $(_pageId + " #qrnh").text(annu_yield);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //查询是否在换卡
    function changecard(callback) {
        var funCallBack = function (resultVo) {
            var data = resultVo;
            if (resultVo.error_no == 0) {
                //改状态不允许交易
                if (data.results[0]) {
                    var mark = data.results[0].trans_status;
                }
                if (mark == 0) {
                    layerUtils.iMsg(-1, "换卡期间不允许交易");
                } else {
                    if (callback) callback();
                }
            }
        };
        service.getchangecardstatus({}, funCallBack);
    }


    // 暴露对外的接口
    module.exports = details;
});
