//源晖购买页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            common = require("common"),
            _pageId = "#yuanhui_purchase ";
    var ut = require("../common/userUtil");
    var _pageCode = "yuanhui/purchase";
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var productInfo;
    var jymm;
    var buyflag; //风险等级是否匹配   1 不匹配
    var available_vol; //可用余额
    var addition_amt_str;
    var isFirstPurchase;// 是否首次购买产品
    var _redem_method = "1";
    function init() {
        // $(_pageId + " .agreement_layer").hide();
        userInfo = ut.getUserInf();
        appUtils.clearSStorage("productInfo_jjb"); //删除华安汇财通本地信息，防止本地信息混淆
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        smDetails();
        reqFun101901();
        isFirstPurchase = true;
        reqFun102103();
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });

        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            tools.jumpPriDetailPage(_pageCode, productInfo.prod_sub_type2);
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #yuanhui_payMethod").hide();
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #yuanhui_payMethod .pay_mode").removeClass("active").eq(0).addClass("active");
        });
        //点击购买方式
        appUtils.bindEvent($(_pageId + " .pay_mode"), function () {
            $(_pageId + " .model_content").children(".pay_mode").removeClass("active");
            $(this).addClass("active");
            _redem_method = $(this).attr("redem_method");
            if (_redem_method == "2") {
                $(_pageId + " .model_bottom").removeClass("noactive");
            } else {
                isCanBuy();
            }
        });

        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "yuanhui_purchase";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });

        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "yuanhui_purchase";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });

        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //查看PDF文件
        appUtils.bindEvent($(_pageId + " #xy"), function () {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        });
        // //关闭数字键盘
        // appUtils.bindEvent($(_pageId), function () {
        //     monkeywords.close();
        // });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").hide()
            $(_pageId + " .password_box").hide()
            $(_pageId + " #jymm").val("");
            guanbi();
        });

        // 跳转充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });

        //购买
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (money <= 0 || !money) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (parseFloat(money) < parseFloat(productInfo.threshold_amount) && isFirstPurchase) {
                layerUtils.iAlert("起购金额为" + productInfo.threshold_amount / 10000 + "万元");
                return;
            }
            if (tools.isMatchAddAmt(money, productInfo.threshold_amount, productInfo.addition_amt)) return
            if (money && productInfo.first_max_amt && money > parseFloat(productInfo.first_max_amt)) { // (当前金额 - 起投金额) % 递增金额 == 0
                layerUtils.iAlert("超过单笔最高限额");
                return;
            }

            $(_pageId + " .pop_layer").show();
            $(_pageId + " #yuanhui_payMethod").show();
            $(_pageId + " .bank_img img").attr("src", "./images/bank_" + userInfo.bankCode + ".png");
            $(_pageId + " .bank_info").html(userInfo.bankName + "卡(尾号：" + userInfo.bankAcct.substr(-4) + ")");
            isCanBuy();
        });
        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {

            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " #yuanhui_payMethod").hide();
            _redem_method = $(_pageId + " #yuanhui_payMethod .pay_mode.active").attr("redem_method")
            if (_redem_method == "2") {//银行卡支付
                $(_pageId + " #bankRecharge").show();
                $(_pageId + " #rechargeInfo").hide();
            } else {
                var money = $(_pageId + " #money").val().replace(/,/g, "");
                $(_pageId + " .gmje").text(tools.fmoney(money) + '元');
                $(_pageId + " #rechargeInfo").show();
                $(_pageId + " #bankRecharge").hide();
            }
            $(_pageId + " .password_box").show();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "yuanhui_purchase";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
            $(_pageId + " #yuanhui_payMethod .pay_mode").removeClass("active").eq(0).addClass("active");
        });

        //调用交易
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide()
            $(_pageId + " .password_box").hide()
            var app_amt = $(_pageId + " #money").val();
            app_amt = app_amt.replace(/,/g, "");
            var trans_pwd = $(_pageId + " #jymm").val()
            guanbi();
            if (trans_pwd.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            var param = {
                app_amt: app_amt, //交易金额
                fund_code: productInfo.fund_code,
                trans_pwd: trans_pwd,
                buyflag: buyflag,
                bank_acct: userInfo.bankAcct,//银行支付-银行卡号
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
                period: productInfo.period,
                buy_state: productInfo.buy_state//购买状态
            };
            if (_redem_method == "2") {//选择银行卡支付
                bankpurchase(param)
            } else {//选择晋金宝支付
                //2 预约  1 购买
                if (productInfo.buy_state == '2') {
                    appoint(param);
                }
                if (productInfo.buy_state == '1') {
                    purchase(param)
                }
            }
        });
    }

    //预约
    function appoint(param) {
        service.reqFun106012(param, function (data) {
            if (data.error_no == 0) {
                appUtils.pageInit(_pageCode, "highEnd/purchaseResult", {
                    app_amt: param.app_amt,
                    trans_serno: data.results[0].trans_serno
                });
            } else {
                layerUtils.iAlert(data.error_info);
                return;
            }

        })
    }

    //购买
    function purchase(param) {
        service.reqFun106013(param, function (data) {
            if (data.error_no == 0) {
                appUtils.pageInit(_pageCode, "highEnd/purchaseResult", {
                    app_amt: param.app_amt,
                    trans_serno: data.results[0].trans_serno
                });
            } else {
                layerUtils.iAlert(data.error_info);
                return;
            }

        })
    }

    //银行卡支付
    function bankpurchase(param) {
        service.reqFun106002(param, function (data) {
            if (data.error_no == 0) {
                appUtils.pageInit(_pageCode, "yuanhui/purchaseResult");
            } else {
                layerUtils.iAlert(data.error_info);
                return;
            }

        })
    }

    function initBuyFlag() {
        if (parseFloat(userInfo.riskLevel.substr(1)) >= parseFloat(productInfo.risk_level.substr(1))) {
            buyflag = "1";
        } else {
            buyflag = "";
        }
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //可用金额查询
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            available_vol = results.available_vol;
            //可用份额
            $(_pageId + " .kyje").text(tools.fmoney(results.available_vol))
        })
    }

    //是否可以购买
    function isCanBuy() {
        //进行充值
        var app_amt = $(_pageId + " #money").val();
        app_amt = app_amt.replace(/,/g, "");
        app_amt = (+app_amt);
        available_vol = (+available_vol);
        $(_pageId + " .pay_mode").removeClass("active");
        if (app_amt <= available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show().addClass("active");
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show().addClass("active");
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }

    //私募产品详情查询（102043）
    function smDetails() {
        service.reqFun102043(productInfo, function (datas) {
            if (datas.error_no == 0) {
                productInfo = datas.results[0];
                appUtils.setSStorageInfo("productInfo", productInfo);
                $(_pageId + " .prod_sname").text(productInfo.prod_sname);
                var str = "";
                if (productInfo.threshold_amount) {
                    str += productInfo.threshold_amount / 10000 + '万元起购';
                }
                if (productInfo.addition_amt) {
                    if (productInfo.addition_amt >= 10000) {
                        addition_amt_str = productInfo.addition_amt / 10000 + '万';
                    } else {
                        addition_amt_str = tools.fmoney(productInfo.addition_amt + '');
                    }
                    str += "，" + addition_amt_str + "元递增";
                }
                if (str) {
                    $(_pageId + " #inputspanid span").text(str).attr("text", str);
                }

                var hfrq = productInfo.return_visit_date;
                if (hfrq) {
                    $(_pageId + " .tips").html("温馨提示：将于24小时后进行电话回访，请注意接听")
                }
                tools.getPdf("prod", productInfo.fund_code, productInfo.buy_state); //获取协议
                initBuyFlag();
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });

    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            isShowUpMoney: true,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (parseFloat(moneys) < parseFloat(productInfo.threshold_amount) && isFirstPurchase) {
                    layerUtils.iAlert("起购金额为" + productInfo.threshold_amount / 10000 + "万元");
                    return;
                }
                $(_pageId + " #money").val(tools.fmoney(moneys));
                if (tools.isMatchAddAmt(moneys, productInfo.threshold_amount, productInfo.addition_amt)) return
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }
                curVal = $(_pageId + " #money").val().replace(/,/g, "");
                if (parseFloat(curVal) > productInfo.surplus_amount * 10000) {
                    $(_pageId + " #money").val(productInfo.surplus_amount * 10000);
                    layerUtils.iAlert("剩余额度为" + productInfo.surplus_amount + "万");
                    return;
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #money").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #money").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            }
        })
    }

    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            // 键盘完成按钮的事件
            keyBoardFinishFunction: function () {
            },
            // 键盘的输入事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            }
        };
    }

    //查询产品是否首次购买
    function reqFun102103() {
        var param = {
            fund_code: productInfo.fund_code
        }
        service.reqFun102103(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (results.is_can_add == 0) {//不是首次购买，能追加
                    isFirstPurchase = false;
                } else {
                    isFirstPurchase = true;
                }
            } else {
                isFirstPurchase = true;
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        guanbi();
        $(_pageId + " .hfDate").text("");
        $(_pageId + " #money").val('');
        $(_pageId + " .prod_sname").text("");
        $(_pageId + " #inputspanid span").text("请输入购买金额").attr("text", "请输入购买金额").css({color: "#999999"});
        $(_pageId + " .password_box").hide();
        $(_pageId + " .pop_layer").hide();
        monkeywords.flag = 0;
        monkeywords.destroy();
        addition_amt_str = "";
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #payMethod").hide();
        $(_pageId + " #yuanhui_payMethod").hide();
        $(_pageId + " #yuanhui_payMethod .pay_mode").removeClass("active").eq(0).addClass("active");
        $(_pageId + " #bankRecharge").hide();
        $(_pageId + " #rechargeInfo").hide();
        _redem_method = "1";
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
