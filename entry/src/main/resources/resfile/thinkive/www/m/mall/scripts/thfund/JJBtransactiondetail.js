// 晋金宝交易记录-详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#thfund_JJBtransactiondetail ";

    function init() {
        var param = appUtils.getPageParam();
        
        // 购买 12403 12302  产品赎回 122124   分红  122143  付息 122111 购买撤销  122152 产品兑付 122150  收益 14301   强制赎回 122142  退款到宝 122140 预约申购 131123

        // 充值 12201  汇款充值 12203  实时取现 12401  普通取现 12402  展示原因
        //页面埋点初始化
        tools.initPagePointData();
        if (param.sub_busi_code_e === "12201" || param.sub_busi_code_e == "12203" ||
            param.sub_busi_code_e == "12401" || param.sub_busi_code_e == "12402") {
            $(_pageId + "[data-name=remark]").html(param.rtn_desc || "无"); //原因
        } else {
            $(_pageId + "[data-name=remark]").html(param.remark || "无"); //产品名称
        }
        $(_pageId + "[data-name=create_date]").html(tools.ftime(param.crt_date));
        $(_pageId + "[data-name=create_time]").html(tools.ftime(param.crt_time));
        $(_pageId + "[data-name=tot_price]").html(tools.fmoney(param.app_amt));
        $(_pageId + "[data-name=digest]").html(param.sub_busi_code);
        $(_pageId + "[data-name=order_state]").html(tools.fundDataDict(param.trans_status, "trans_status_name"));
        // $(_pageId + "[data-name=remark]").html(param.remark || "无");

    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #comeback"), function () {
            pageBack();
        });
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #errorLog").hide();
        $(_pageId + " #errorLogs").hide();
        $(_pageId + " #remark").css("border-bottom", "1px solid #C8CDCF");

        $(_pageId + "[data-name=create_date]").html("");
        $(_pageId + "[data-name=create_time]").html("");
        $(_pageId + "[data-name=tot_price]").html("");
        $(_pageId + "[data-name=digest]").html("");
        $(_pageId + "[data-name=order_state]").html("");
        $(_pageId + "[data-name=remark]").html("");
    }

    function pageBack() {
        appUtils.pageInit("thfund/JJBtransactiondetail", "thfund/JJBtransaction", appUtils.getPageParam().backPageParam);
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
