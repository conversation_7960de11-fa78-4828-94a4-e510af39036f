// 手机注册成功
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#login_userRegisteredSuccess";

    function init() {
        /*分享获取奖励*/
        var shareActive = require("../common/shareActive");
        // shareActive.shareCreated(1, _pageId);
    }
    //绑定事件
    function bindPageEvent() {
        //bdyhk 绑定银行卡
        appUtils.bindEvent($(_pageId + " #bdyhk"), function () {
            appUtils.pageInit("login/userRegisteredSuccess", "account/setBankCard");
        });
        //去逛逛
        appUtils.bindEvent($(_pageId + " #qgg"), function () {
            appUtils.pageInit("login/userRegisteredSuccess", "login/userIndexs");

        });
    }
    function destroy() {

    }
    function pageBack() {
        appUtils.pageInit("login/userRegisteredSuccess", "login/userIndexs");
    }
    var userRegisteredSuccess = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack":pageBack
    };
    // 暴露对外的接口
    module.exports = userRegisteredSuccess;
});
