// 信息验证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#bank_changePhoneNumber ";
    var backParam = {};
    var _pageCode = "bank/changePhoneNumber";
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");

    function init() {
        sms_mobile.init(_pageId);
    }

    

    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //输入新预留手机号
        appUtils.bindEvent($(_pageId + " #bank_reserved_mobile"), function () {
        	//限制输入数字
            restrictInputnNum($(this));
        }, "input");
        //输入验证码
        appUtils.bindEvent($(_pageId + " #message_code"), function () {
        	//限制输入数字
            restrictInputnNum($(this));
        }, "input");
        //获取验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
        	sendPhoneCode();
        });
        //更换手机号
        appUtils.bindEvent($(_pageId + " #complete_btn"), function () {
        	//更换绑定卡预留手机号
            reqFun151009();
        });
    }


    //限制输入数字
    function restrictInputnNum(that){
    	var reg = /[^\d]/g;
    	var str = that.val();
    	if(reg.test(str)){
    		var temp = str.replace(reg,'');
    		that.val(temp);
    	}
    }
    //获取验证
    function sendPhoneCode(){
        var bank_reserved_mobile = $(_pageId + " #bank_reserved_mobile").val();
    	if (!validatorUtil.isMobile(bank_reserved_mobile)) {
            layerUtils.iMsg(-1, "请输入正确新预留手机号");
            return;
        }

        // 获取验证码
        var param = {
            "mobile_phone": bank_reserved_mobile,
            "type": "1",
            "send_type": "0",
        };
        sms_mobile.sendPhoneCode(param);
    }
    
    //更换绑定卡预留手机号
    function reqFun151009(){
    	var bank_reserved_mobile = $(_pageId + " #bank_reserved_mobile").val();
    	var message_code = $(_pageId + " #message_code").val();
        var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
        if (!validatorUtil.isMobile(bank_reserved_mobile)) {
            layerUtils.iMsg(-1, "请输入正确新预留手机号");
            return;
        }
        if (message_code.length != 6) {
            layerUtils.iMsg(-1, "请输入完整的验证码");
            return;
        }
        if (isSend == "true") {
            layerUtils.iMsg(-1, "您还未获取验证码");
            return;
        }
        if (message_code.length != 6) {
            layerUtils.iMsg(-1, "请输入完整的验证码");
            return;
        }
    	
    	var param = {
    			bank_channel_code:"Y01",
    			bank_reserved_mobile:bank_reserved_mobile,
    			message_code:message_code,
        }
    	service.reqFun151009(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
            	appUtils.pageInit(_pageCode, "bank/changePhoneNumberResult");
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }
    function destroy() {
        sms_mobile.destroy(_pageId);
    	$(_pageId + " #bank_reserved_mobile").val("");
    	$(_pageId + " #message_code").val("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
