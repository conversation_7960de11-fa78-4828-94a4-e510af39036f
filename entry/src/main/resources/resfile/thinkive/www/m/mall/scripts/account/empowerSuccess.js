// 所转财富授权成功页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#account_empowerSuccess";
        _page_code = "account/empowerSuccess"
    var tools = require("../common/tools");
    function init() {

    }
    //绑定事件
    function bindPageEvent() {
        //返回我的页面
        appUtils.bindEvent($(_pageId + " #wancheng"), function () {
            // tools.open_jjcf();
            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
            appUtils.pageInit("login/userIndex", "account/myAccount", {});
        });
        //返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
            appUtils.pageInit("login/userIndex", "account/myAccount", {});
        });
        
    }
    function destroy() {

    }
    function pageBack() {
        appUtils.pageInit(_page_code, "login/userIndexs");
    }
    var empower222 = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = empower222;
});
