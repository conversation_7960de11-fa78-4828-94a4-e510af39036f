// 高端版本认证首页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#highVersion_adviser ",
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        common = require("common"),
        service = require("mobileService"),
        _page_code = "highVersion/adviser";
        var ut = require("../common/userUtil");
        var gconfig = require("gconfig");
        var global = gconfig.global;
        var adviser;//当前财顾信息
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //获取财顾信息
        getAdviserInfo();
    }
    //获取财顾信息
    function getAdviserInfo(){
        service.reqFun181004({}, (data) => {
            let beforUrl = global.oss_url
            if (data.error_no != "0") return layerUtils.iAlert(data.error_info);
            if(!data.results || !data.results[0]){
                $(_pageId + " article").hide();
                layerUtils.iConfirm("您的财富顾问正在分配中...", function () {
                    pageBack();
                }, function () {
                    pageBack();
                }, "取消", "确定");
                return;
            };
            let res = data.results[0];
            adviser = res;
            $(_pageId + " .custmanager_alias").text(res.custmanager_alias);
            $(_pageId + " .user_info").text(res.custmanager_customize_introduction);
            $(_pageId + " .photo_url").attr('src',beforUrl + res.photo_url);
            $(_pageId + " article").show();
        })
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "login_userRegistered";
            tools.saveAlbum(_page_code,param)
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .historical"), function () {
            appUtils.pageInit(_page_code, "highVersion/historicalFeed");
        });
        //打电话
        appUtils.bindEvent($(_pageId + " .callPhone"), function () {
            if(!adviser || !adviser.phone) return;
            let mobile = adviser.phone;
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = mobile;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        appUtils.bindEvent($(_pageId + " .wxPage"), function () {
            let user = ut.getUserInf();
            let mobile = user.mobileWhole;
            mobile = common.desEncrypt("mobile", mobile);
            tools.jump_applet('pages/account/wealthAdvisor/wealthAdvisor?mobile=' + mobile);
        });
        //提交需求
        appUtils.bindEvent($(_pageId + " .adviser_btn"), function () {
            let leaveword = $(_pageId + " .leaveword").val().trim();
            let leavewordTest = /^[\u4e00-\u9fa5a-zA-Z0-9\s.,!?，。！？、；：“”‘’（）【】￥\$%\^&*+=_~`'"\-\\|]+$/i
            if(!leaveword || !leaveword.length || leaveword.length == '0') return layerUtils.iAlert('您的需求不能为空');
            if(leaveword.length >= 200) return layerUtils.iAlert('请勿超过200字');
            if(!leavewordTest.test(leaveword)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
            service.reqFun181005({leaveword:leaveword}, (data) => {
                if (data.error_no != "0") return layerUtils.iAlert(data.error_info);
                $(_pageId + " .leaveword").val('');
                layerUtils.iAlert('需求已提交成功，财富顾问会尽快联系您');
            })
        });
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .leaveword").val('');
        $(_pageId + " article").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var highVersion_adviser = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_adviser;
});
