//年终活动
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools.js"),
        validatorUtil = require("validatorUtil");
    var _pageId = "#activity_labourActivity ";
    var _pageCode = "activity/labourActivity"
    var layerUtils = require("layerUtils");
    var userInfo, activityInfo;
    var external = require("external");
    var global = gconfig.global;
    var ut = require("../common/userUtil.js");
    require("../../js/draw.js");
    require("../common/html2canvas.min.js");
    var pageTouchTimer = null;
    var high_player;//首页视频
    /* 变量  活动信息*/
    var is_help,help_img_url;//是否需要助力
    var activityInfo = "", reward_num = 1, state, shareflag, share_template, turntable_list, activity_id_url, group_id, banner_id;
    var stopIndex;
    var guessActivityInfo, guess_state, guess_words, activity_tips, activity_id,userAuthenticationStatus,recommend_prod_type;
    // 初始化转盘
    var luck = {
        index: 0, // 当前转动到哪个位置，起点位置
        count: 0, // 总共有多少个位置
        timer: 0, // setTimeout的ID，用clearTimeout清除
        speed: 20, // 初始转动速度
        times: 0, // 转动次数
        cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
        prize: -1, // 中奖位置
        init: function (id) {
            if ($("#" + id).find(".luck-unit").length > 0) {
                var $luck = $("#" + id);
                var $units = $luck.find(".luck-unit");
                this.obj = $luck;
                this.count = $units.length;
                this.index = 0;
                $luck.find(".luck-unit").removeClass("active");
                $luck.find(".luck-unit-" + this.index).addClass("active");
            }
        },
        roll: function () {
            var index = this.index;
            var count = this.count;
            var luck = this.obj;
            $(luck).find(".luck-unit-" + index).removeClass("active");
            index += 1;
            if (index > count - 1) {
                index = 0;
            }
            $(luck).find(".luck-unit-" + index).addClass("active");
            this.index = index;
            return false;
        },
        stop: function (index) {
            this.prize = index;
            return false;
        }
    };
    var moneyObj = {};
    var platform = gconfig.platform;
    /**
     * 初始化
     */
    async function init() {
        activityInfo = appUtils.getPageParam() ? appUtils.getPageParam() : appUtils.getSStorageInfo("activityInfo");
        if (activityInfo && validatorUtil.isEmpty(activityInfo.activity_id)) {
            activityInfo = ''
            layerUtils.iAlert('活动ID未配置,请联系管理人员', -1, function () {
                pageBack();
            });
        }
        $(_pageId + " .activity_pop_layer").hide();
        // getGuessInfo();
        if (activityInfo) {
            appUtils.setSStorageInfo("activityInfo", activityInfo);
            getActivityInfo();
            
        }
        luck.init('luck');
        getVideoInfo("1081")
    };
    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
  //获取视频信息
    async function getVideoInfo(video_id) {
        service.reqFun102222({video_id:video_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let res = datas.results[0]
            videoInfo = res;
            // console.log(res,111)background: url(./images/index_poster.png) center center no-repeat;
            //渲染视频封面
            // $(_pageId + " .videoShow").attr("video_id");
            cover_path = global.oss_url + res.cover_path;
            $(_pageId + " .cover_path").css({
                "background": `url(${global.oss_url + res.cover_path}) center center no-repeat`,
                "background-size": "cover"
            })
        })
    }
    /**
     * 获取活动信息
     * */
    function getActivityInfo() {
        service.reqFun108015({
            cust_no: activityInfo.cust_no,
            activity_id: activityInfo.activity_id
        },  function (data) {
            var error_no = data.error_no;
            if (error_no == 0) {
                var result = data.results[0];
                
                recommend_prod_type = result.recommend_prod_type;
                //获取年终活动产品列表
                // setData();
                state = result.activity_status;//1:进行中 2:未开始 3:已结束
                shareflag = result.shareflag; //0未分享 1:分享过
                reward_num = result.times;//抽奖次数
                share_template = result.share_template;//分享模板ID
                turntable_list = result.turntable_list;
                is_help = result.is_help;//是否需要助力
                help_img_url = result.help_img_url;//助力图片
                let start_time = result.start_time ? `${result.start_time.substr(0,4)}.${result.start_time.substr(4,2)}.${result.start_time.substr(6,2)}` : ''
                let end_time = result.end_time ? `${result.end_time.substr(0,4)}.${result.end_time.substr(4,2)}.${result.end_time.substr(6,2)}` : ''
                $(_pageId + " #activityRules .b_con").html(result.introduce)
                $(_pageId + " .activity_tips").html(result.activity_tips)
                //渲染开始结束时间
                $(_pageId + " .startTime").html(start_time);
                $(_pageId + " .endTime").html(end_time);
                if(start_time && end_time) $(_pageId + " .time_card").show();
                if (state == "2") {
                    $(_pageId + " #start .start_btn_name").html("即将开始");
                }
                if (state == "3") {
                    $(_pageId + " #start .start_btn_name").html("已结束");
                    $(_pageId + " #start .start_btn_name").css({ "width": '0.7rem' })
                    $(_pageId + " #start").css({ "background": '#808080' })
                }
                // if (state == "1" && shareflag === "0") {
                //     $(_pageId + " #start .start_btn_name").html("立即抽奖");
                //     $(_pageId + " #start").removeClass("notclick");
                // }
                if (state == "1" && reward_num <= 0) {
                    $(_pageId + " #start .start_btn_name").html("再抽一次");
                    $(_pageId + " #start").removeClass("notclick");
                }
                if (result.turntable_list && result.turntable_list.length) {
                    moneyObj = {};
                    var html = '';
                    result.turntable_list.forEach((item, i) => {
                        var index;
                        if (i < 3) {
                            index = i;
                        } else if (i >= 3 && i <= 4) {
                            index = i == 3 ? "7" : "3"
                        } else if (i > 4 && i <= 7) {
                            index = i == 5 ? "6" : (i == 6 ? "5" : "4")
                        }
                        $(_pageId + ` .game .luck-unit-${index} img`).attr("src", global.oss_url + item.img_url)
                        if (item.reward_type == '0') { // 固定积分
                            var obj = {
                                list_text: `${item.points_amount}积分`, //领取记录文字
                                reward_text: `${item.points_amount}积分`, //中奖提示
                                src: "./images/activity/p_ok.png",
                                stopIndex: [parseFloat(index)],
                                reward_type: item.reward_type, // 类型-1:随机积分 0:固定积分 2:实物
                                btn_txt: "我知道了",
                                prompt: "",
                            }
                            moneyObj[`${item.points_amount}-${i}`] = obj;
                        } else if (item.reward_type == "1") { // 随机积分
                            var obj = {
                                list_text: "随机积分", //领取记录文字
                                reward_text: "", //中奖提示
                                src: "./images/activity/p_ok.png",
                                stopIndex: [parseFloat(index)],
                                reward_type: item.reward_type, // 类型-1:随机积分 0:固定积分 2:实物
                                btn_txt: "我知道了",
                                prompt: "",
                            }
                            moneyObj[`auto-${i}`] = obj;
                        } else if (item.reward_type == "2") { // 实物
                            var obj = {
                                list_text: item.name, //领取记录文字
                                reward_text: item.name, //中奖提示
                                src: "./images/activity/p_ok.png",
                                stopIndex: [parseFloat(index)],
                                reward_type: item.reward_type, // 类型-1:随机积分 0:固定积分 2:实物
                                btn_txt: "我知道了",
                                prompt: "请到“抽奖记录”中填写领取方式",
                            }
                            moneyObj[`physical${item.real_reward_code}-${i}`] = obj;
                        }
                    })
                }
                // 获取分享模板
                let query_params = {
                    registered_mobile: ut.getUserInf().mobileWhole,
                    share_template: share_template
                }
                service.reqFun102012(query_params, async function (data) {
                    if (data.error_no == '0') {
                        var result = data.results[0];
                        if (data.results[0] && data.results[0].share_form == 2) {  // 若分享的是卡片，先渲染卡片
                            
                            setShareImg(result);
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
    }
    //渲染分享卡片图片
    function setShareImg(chooseData) {
        let bgImg = gconfig.global.oss_url + chooseData.img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #qr_img").show();
                    $(_pageId + " #code").hide();
                    $(_pageId + "#qr_img").empty();
                    let qr_code_img = global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #qr_img").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                    }, { "isShowWait": false })
                } else {
                    $(_pageId + " #qr_img").hide();
                    $(_pageId + " #code").show();
                    qrcode(chooseData);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    //渲染短链二维码
    function qrcode(chooseData) {
        let mobile = ut.getUserInf().mobileWhole;
        mobile = common.desEncrypt("mobile", mobile);//加密
        // let long_url = "https://xhxts.sxfae.com/m/mall/index.html#!/drainage/userInvitationWx.html?mobile=" + mobile;
        let long_url = chooseData.qr_code_img_url + '?mobile=' + mobile;
        $(_pageId + "#code").empty();
        service.reqFun101073({ long_url: long_url }, function (res) {
            if (res.error_no == "0") {
                if (res.results != undefined && res.results.length > 0) {
                    var short_url = res.results[0].shortUrl;
                    require("../common/jquery.qrcode.min.js");
                    $(_pageId + " #code").qrcode({
                        render: "canvas", //设置渲染方式，有table和canvas
                        text: short_url, //扫描二维码后自动跳向该链接
                        width: 70, //二维码的宽度
                        height: 70, //二维码的高度
                        imgWidth: 20,
                        imgHeight: 20,
                        src: '../mall/images/icon_app.png'
                    });
                }
            } else {
                layerUtils.iAlert(res.error_info);
            }
        })
    }

    /*分享卡片*/
    function shareCard() {
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #pop_layer_pageShare").hide();
        var father = document.querySelector("#content");
        var _fatherHTML = document.querySelectorAll("#content .page");
        var cur = document.querySelector("#activity_dragonYearActivity");
        father.innerHTML = "";
        father.appendChild(cur);
        let dom = document.querySelector(_pageId + " .shareImg");
        html2canvas(dom, {
            scale: 4
        }).then(canvas => {
            var base64 = canvas.toDataURL("image/png");
            var _base64 = base64.split(",")[1];
            father.innerHTML = "";
            for (let i = 0; i < _fatherHTML.length; i++) {
                father.appendChild(_fatherHTML[i]);
            }
            param = {
                "funcNo": "50231",
                "imgUrl": _base64,
                "shareType": "23",
                "imageShare": "1",
                "imageType":"base64"
            }
            require("external").callMessage(param);
            // common.share("23", share_template ? share_template : "1", "", true, _base64);
        })
    }

   

    //补充代码
    function clearPath() {
        appUtils.setSStorageInfo("pageTopUrlInfo","");
        appUtils.setSStorageInfo("skipURL", '');
    }
    //去测评
    function pageTo_evaluation() {
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
        }, '', '确定')
    }
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //点击产品跳转首页
        appUtils.bindEvent($(_pageId + " .card_list"), function () {
            appUtils.pageInit(_pageCode, "login/userIndexs")
        });
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //活动规则
        appUtils.bindEvent($(_pageId + " #clickActivityRules"), function () {
            $(_pageId + " #activityRules").show();
        });
        //隐藏活动规则
        appUtils.bindEvent($(_pageId + " #activityRules .ruleSure"), function (e) {
            $(_pageId + " #activityRules").hide();
        });
         //微信好友
        appUtils.bindEvent($(_pageId + " #pop_layer_pageShare  #share_WeChat"), function () {
         	tools.clickPoint(_pageId, _pageCode, 'share_WeChat', activityInfo.activity_id);
            appUtils.setSStorageInfo("activityInfo", activityInfo);
            if (!activityInfo || !share_template) {
                common.share("22", "1");
                return;
            }
            let results = {
                banner_id: activityInfo.banner_id,
                group_id: activityInfo.group_id,
                activity_id: activityInfo.activity_id,
            }
            let query_params = {
                registered_mobile: ut.getUserInf().mobileWhole,
                share_template: share_template
            }
            service.reqFun102012(query_params, async function (data) {
                if (data.error_no == '0') {
                    var result = data.results[0];
                    if (data.results[0] && data.results[0].share_form == 1) {  // 链接
                        results.title = result.title;
                        results.content = result.content;
                        results.img_url = result.img_url;
                        let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                        return tools.pageShare(results, '22', _pageId, shareUrlLast, null, activityInfo.activity_id);//活动页面分享
                    } else if (data.results[0] && data.results[0].share_form == 2) { // 图片 3--页面
                        shareCard();
                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
            // common.share("23", share_template);
            pageTouchTimer = setTimeout(() => {
                // 分享加次数
                service.reqFun108016({ activity_id: activityInfo.activity_id }, function (data) {
                    if (data.error_no != "0") {//失败
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    layerUtils.iMsg(-1, "分享成功");
                    getActivityInfo();
                })
            }, 6000);
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #pop_layer #share_WeChatFriend"), function () {
            // tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', activityInfo.activity_id);
            appUtils.setSStorageInfo("activityInfo", activityInfo);
            if (!activityInfo || !share_template) {
                common.share("23", "1");
                // shareTypeList, share_type , activity_id,isShowImg, img_base64,is_open, is_activity , source,is_help,help_img_url
                return;
            }
            let results = {
                banner_id: activityInfo.banner_id,
                group_id: activityInfo.group_id,
                activity_id: activityInfo.activity_id,
            }
            let query_params = {
                registered_mobile: ut.getUserInf().mobileWhole,
                share_template: share_template
            }
            service.reqFun102012(query_params, async function (data) {
                if (data.error_no == '0') {
                    var result = data.results[0];
                    if (data.results[0] && data.results[0].share_form == 1) {  // 链接
                        results.title = result.title;
                        results.content = result.content;
                        results.img_url = result.img_url;
                        let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                        return tools.pageShare(results, '23', _pageId, shareUrlLast, null, activityInfo.activity_id,'',help_img_url,is_help);//活动页面分享
                    } else if (data.results[0] && data.results[0].share_form == 2) { // 图片 3--页面
                        shareCard();
                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
            if(is_help != '1'){
                pageTouchTimer = setTimeout(() => {
                    // 分享加次数
                    service.reqFun108016({ activity_id: activityInfo.activity_id }, function (data) {
                        if (data.error_no != "0") {//失败
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        layerUtils.iMsg(-1, "分享成功");
                        getActivityInfo();
                    })
                }, 6000);
            }
            // common.share("23", share_template);
            
        });
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            // $(_pageId + " #start").removeClass("notclick");
            $(_pageId + " #pop_layer").hide();
            $(_pageId + " #pop_layer_pageShare").hide();
        });
        //抽奖记录
        appUtils.bindEvent($(_pageId + " #clickMyRewards"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            $(_pageId + " .content_list").html("");
            var QueryParam = {
                "activity_id": activityInfo.activity_id,
                "cust_no": activityInfo.cust_no,
            }
            service.reqFun108018(QueryParam, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var results = data.results[0].data;
                if (validatorUtil.isNotEmpty(data.results[0].data) && results.length > 0) {
                    var html = "";
                    for (var i = 0; i < results.length; i++) {
                        var reward_name = results[i].reward_name;//奖励内容
                        var crt_time = results[i].crt_time;//奖励时间
                        html += `<div class="item">
                            <em style="display: none" class="recordInfo">${JSON.stringify(results[i])}</em>  
                            <p style="padding-right:0.05rem;width: 8%;text-align: right">${i + 1}.</p>
                            <p style="padding-right:0.05rem;width: 43%">${crt_time.substr(0, 4)}年${crt_time.substr(4, 2)}月${crt_time.substr(6, 2)}日</p>\n
                            <p style="width: 29%">${reward_name}</p>
                            ${results[i].reward_type == "2" ? `<p class="get_reward">${results[i].is_entered == '1' ? '查看' : '领取'}</p>` : ''}
                          </div > `
                    }
                    $(_pageId + " .content_list").html(html);
                } else {
                    $(_pageId + " .content_list").html("<div style='text-align: center;line-height: 4;font-size: 0.14rem'>暂无领取记录</div>");
                }
                $(_pageId + " #rewardRecord").show();
            }, { "isShowWait": false });
        });
        //隐藏抽奖记录
        appUtils.bindEvent($(_pageId + " .recordSure"), function (e) {
            $(_pageId + " #rewardRecord").hide();
        });
        //抽奖
        appUtils.bindEvent($(_pageId + " #start"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if (state == "2") {
                reward_num = 0;
                layerUtils.iMsg(-1, "活动未开始！", 2);
                return;
            }
            if (state == "3") {
                reward_num = 0;
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            // 没有配置转盘内容的情况
            if (!turntable_list || turntable_list.length == '0') {
                reward_num = 0;
                layerUtils.iMsg(-1, "参与人数过多", 2);
                return;
            }
            if (shareflag === "0" && reward_num <= 0) {
                layerUtils.iConfirm("分享活动给好友可获得一次抽奖机会", function () {
                    if(platform == "5"){
                        
                        $(_pageId + " #pop_layer_pageShare").show();
                    }else{
                        $(_pageId + " #pop_layer").show();
                    }
                    
                }, function () {

                }, "去分享", "取消");
                return;
            }
            if (reward_num <= 0) {
                layerUtils.iMsg(-1, "今日抽奖次数已用完，请下次再来", 2);
                return;
            }
            if ($(_pageId + " #start").hasClass("notclick")) {
                return;
            }

            // 按钮禁用
            $(_pageId + " #start").addClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0_end.png'>");
            luck.speed = 100;
            var param = {
                "activity_id": activityInfo.activity_id,
                "cust_no": activityInfo.cust_no
            };
            service.reqFun108052(param, function (data) {
                // -2 未绑卡
                // -3 活动尚未开始或已结束
                // -4 次数已用完
                // -6 当前不符合参与条件
                // -999011 请求过于频繁，请稍候再试
                //奖池为空时默认谢谢参与
                if (data.error_no == "-2" || data.error_no == "-3" || data.error_no == "-4" || data.error_no == "-5" || data.error_no == "-6" || data.error_no == "-7" || data.error_no == "-999011") {
                    stopIndex = 0;
                    $(_pageId + " #start").removeClass("notclick");
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.error_no != "0") {
                    stopIndex = 5;
                    $(_pageId + " .index_info").html(moneyObj["0"].reward_text);
                    $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[0].src);
                    $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[0].btn_txt);
                    $(_pageId + " .index_info_tishi").html('');
                    roll();
                    reward_num--;
                    if (state == "1" && reward_num <= 0) {
                        $(_pageId + " #start .start_btn_name").html("再抽一次");
                        $(_pageId + " #start").removeClass("notclick");
                    }
                    return;
                }
                var results = data.results[0];
                var money = Number(results.reward_vol) + "";
                var keyword;
                var moneyKey;
                //奖励类型reward_type 1:随机积分 0:固定积分 2:实物
                if (results.reward_type === "2") { //实物
                    // 实物类型 7-绒布对联、8-小米随手杯、9-小爱音箱
                    keyword = "physical" + results.reward_vol;
                } else if (results.reward_type === "0") { //固定积分
                    keyword = money;
                } else if (results.reward_type === "1") { //随机积分
                    keyword = 'auto';
                } else if (results.reward_type === "6") { // 候补奖励 发的也是随机积分
                    keyword = 'auto';
                }
                var autoKey = []; // 存放以keyword开头的对象，随机取一个
                for (let key in moneyObj) {
                    var keyFront = key.split("-")[0];
                    if (keyword == keyFront) { // 是否以keyword开头
                        autoKey.push(key);
                    }
                }
                moneyKey = autoKey[Math.floor((Math.random() * autoKey.length))] // auto开头的对象，随机取一个
                if (validatorUtil.isEmpty(moneyObj[moneyKey])) {
                    // 随机选一个随机积分
                    var autoKey = [];
                    for (let key in moneyObj) {
                        var keyFront = key.split("-")[0];
                        if (keyFront == "auto") {
                            autoKey.push(key);
                        }
                    }
                    moneyKey = autoKey[Math.floor((Math.random() * autoKey.length))] // auto开头的对象，随机取一个
                }
                $(_pageId + " .index_info").html(moneyObj[moneyKey].reward_text ? moneyObj[moneyKey].reward_text : money + "积分");
                $(_pageId + " .index_info_tishi").html(moneyObj[moneyKey].prompt ? moneyObj[moneyKey].prompt : '');
                stopIndex = moneyObj[moneyKey].stopIndex[Math.floor(Math.random() * moneyObj[moneyKey].stopIndex.length)]
                $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[moneyKey].src);
                $(_pageId + " #rewardResult .sureBtn").attr("data-type", results.reward_type)
                $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[moneyKey].btn_txt);
                roll();
                reward_num--;
                if (state == "1" && reward_num <= 0) {
                    $(_pageId + " #start .start_btn_name").html("再抽一次");
                    $(_pageId + " #start").removeClass("notclick");
                }
            }, { "isShowWait": false });
        });
        //隐藏中奖弹框
        appUtils.bindEvent($(_pageId + " .sureBtn"), function (e) {
            $(_pageId + " #rewardResult").hide();
        });
        //积分兑换
        appUtils.bindEvent($(_pageId + " #pointsFor"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            appUtils.setSStorageInfo("front_pageUrl", `${_pageCode}?activity_id=${activityInfo.activity_id}`);
            appUtils.pageInit("guide/advertisement", "vipBenefits/index", { 'luckflag': 'dargonYearDraw' })
        });
        // 邀好友抽奖
        appUtils.bindEvent($(_pageId + " #inviteBtn"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            if(platform == '5'){
                $(_pageId + " #pop_layer_pageShare").show();              
            }else{
                $(_pageId + " #pop_layer").show();
            }
            
        });

        //TODO
        appUtils.preBindEvent($(_pageId + " #rewardRecord .content_list"), ".get_reward", function () {
            var recordInfo = JSON.parse($(this).parent().find(".recordInfo").html())
            appUtils.setSStorageInfo("front_pageUrl", `${_pageCode}?activity_id=${activityInfo.activity_id}`);
            appUtils.pageInit("guide/advertisement", "activity/receiveAward", { recordInfo: recordInfo, 'luckflag': 'dargonYearDraw' })
        })
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            appUtils.clearSStorage("fund_code");
            appUtils.clearSStorage("productInfo");
            $(_pageId + ".qualifiedInvestor").hide();
        });
        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    if (sessionStorage.digUserAuthenticationStatus == '1') {
                        appUtils.pageInit("login/userIndexs", sessionStorage.digUserAuthenticationUrl);
                        sessionStorage.digUserAuthenticationStatus = '';
                        sessionStorage.digUserAuthenticationUrl = '';
                        return;
                    }
                    $(_pageId + ".qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    // setData();
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });

         //点击播放
         appUtils.bindEvent($(_pageId + " .videoShow p"), function () {
            tools.recordEventData('1','videoShow','点击播放按钮');
            let html = `<video id="userIndex_new_example_video" style="width:100%;height:100%" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
            webkit-playsinline="true" playsinline="true" autoplay height="100%" controls preload="auto" poster=""
            data-setup="{}">
            </video>`
            $(_pageId + " #new_example_div").html(html);
            //初始化视频
            high_player = videojs('userIndex_new_example_video', {
            }, function onPlayerReady() {
                //结束和暂时时清除定时器，并向后台发送数据
                this.on('ended', function () {
                    // window.clearInterval(time1);
                });
                this.on('pause', function () {
                    // window.clearInterval(time1);
                });
                this.on('waiting', function () {
                    // window.clearInterval(time1);
                })
            });
            high_player.reset();
            high_player.src({ src: global.video_oss_url + videoInfo.video_path, type: 'video/mp4' })
            high_player.load(global.video_oss_url + videoInfo.video_path)
            $(_pageId + " video").attr("poster", cover_path)
            $(_pageId + " #showVideo").show()
        });
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            tools.recordEventData('1','close_video','关闭视频弹窗');
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        });
   
    }

      //安卓物理返回键会调用该方法
      function pageBack() {
        //原生返回判断是否有banner跳转
         // if(high_player){
         //     high_player.pause();
         //     setTimeout(function() {
         //         high_player.dispose();
         //         high_player = '';
         //     }, 0);
         // }
         //原生返回判断视频弹窗是否打开,如果打开关闭弹窗
         if($(_pageId + " #showVideo").is(":visible")){
             high_player.pause();
             setTimeout(function() {
                 high_player.dispose();
                 high_player = '';
             }, 0);
             return $(_pageId + " #showVideo").hide();
         }
         $(_pageId + " #showVideo").hide();
         $(_pageId + " .loginDig").hide();
         appUtils.pageBack();
     }

     function destroyVideo(){
        if(high_player){
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        }
    }
    function destroy() {
        $(_pageId + " .time_card").hide();
        $(_pageId + " .activity_pop_layer").hide();
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #pointsFor").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #rewardResult .sureBtn span").html("");
        $(_pageId + " #qr_img").hide();
        $(_pageId + " #code").show();
        $(_pageId + " #guessActivityRules").hide();
        clearTimeout(pageTouchTimer);
        appUtils.clearSStorage("front_pageUrl");
        service.destroy();
    };

    function roll() {
        luck.times += 1;
        luck.roll();
        if (luck.times > luck.cycle + 10 && luck.prize == luck.index) {
            clearTimeout(luck.timer);
            luck.prize = -1;
            luck.times = 0;
            if (stopIndex !== 4) {
                setTimeout(function () {
                    $(_pageId + " #rewardResult").show();
                }, 600);
            } else {
                setTimeout(function () {
                    $(_pageId + " #rewardResult").show();
                }, 600);
            }
            $(_pageId + " #start").removeClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0.png'>");
        } else {
            if (luck.times < luck.cycle) {
                luck.speed -= 10;
            } else if (luck.times == luck.cycle) {
                luck.prize = stopIndex;
            } else {
                if (luck.times > luck.cycle + 10 && ((luck.prize == 0 && luck.index == 7) || luck.prize == luck.index + 1)) {
                    luck.speed += 110;
                } else {
                    luck.speed += 20;
                }
            }
            if (luck.speed < 40) {
                luck.speed = 40;
            }
            luck.timer = setTimeout(roll, luck.speed);
        }
        return false;
    }


    var dargonYearActivityModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "destroyVideo":destroyVideo
    };
    // 暴露对外的接口
    module.exports = dargonYearActivityModule;
});
