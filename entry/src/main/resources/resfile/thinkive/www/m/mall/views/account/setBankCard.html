<div class="page" id="account_setBankCard" data-pageTitle="实名认证" data-isSaveDom="true" data-refresh="true"
     style="-webkit-overflow-scrolling : touch;">
    <section class="main fixed add_padding" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">实名认证</h1>
                <a id="kefu" href="javascript:void(0)" class="coustomer-service-icon">
                    <img src="./images/customerService.png">
                </a>
            </div>
        </header>
        <article class="bg_blue no_padding" style="padding-bottom: 0.1rem">
            <div class="user_check">
                <!-- <h3>请上传二代身份证<span style="color: #E5433B">（请确保图片清晰)</span></h3>-->
                <div class="progressBarUser main_flxe vertical_line">
                    <ul class="progressBarTop flex vertical_center">
                        <li class="main_flxe vertical_line flex_center">
                            <img class="imgIcon" src="../../images/stepFirst.png" alt="" id="stepFirst"/>
                            <span class="stepName">实名认证</span>
                        </li>
                        
                        <img class="" src="../../images/redLine.png" style="height: 2px;margin-top: -0.35rem;" alt="" id="redLine"/>
                        <li class="main_flxe vertical_line flex_center">
                            <img class="imgIcon" src="../../images/noStepSecond.png" alt="" id="noStepSecond"/>
                            <span class="stepName">绑定银行卡</span>
                        </li>
                        <img src="../../images/greyLine.png" alt="" style="height: 2px;margin-top: -0.35rem;" id="greyLine"/>
                        <li class="main_flxe vertical_line flex_center">
                            <img class="imgIcon" src="../../images/noStepThird.png" alt="" id="noStepThird"/>
                            <span class="stepName">设置交易密码</span>
                        </li>
                        
                        
                    </ul>
                </div>
                <div class="haveIdCard">
                    <div class="check_tips slidedown " style="background: none;border:none">
                        <p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息，向银行发送姓名、身份证信息进行验证。</p>
                    </div>
                    <div class="facemodule_idcard">
                        <ul>
                            <li class="ocr_sfz zm">
                                <a class="active">
                                    <img src="../../images/sfz_01.png" alt="" id="zm_img"/>
                                </a>
                                <p>
                                    <a class="a01 snap"></a>
                                    <span>点击拍摄</span>
                                    <span>（人像面）</span>
    
                                </p>
    
                            </li>
                            <li class="ocr_sfz fm">
                                <a class="active">
                                    <img src="../../images/sfz_02.png" alt="" id="fm_img"/>
                                </a>
                                <p>
                                    <a class="a01 snap"></a>
                                    <span>点击拍摄</span>
                                    <span>（国徽面）</span>
                                </p>
                            </li>
                        </ul>
                    </div>
                    <p style="padding-left: 0.15rem"><span class="text_red">*</span>上传不成功怎么办？<span class="operate_guide blue">操作指南</span></p>
                    <div class="persinformt">
                        <p class="row_01" style="font-size: 0.14rem;">请仔细核对以下信息是否与证件上的一致，若发现识别有误，请重新拍摄</p>
                    </div>
                </div>
                <div class="noIdCard check_tips slidedown " style="background: none;border:none;display:none;">
                    <p id="userInfo" style="font-size: 0.14rem;">基金交易为实名业务，请提供姓名和身份证号完成实名认证</p>
                </div>
                <div id="step1" class="stepsContent">
                    <div class="input_box">
                        <div class="ui field text">
                            <label class="ui label">真实姓名</label>
                            <input id="realName" type="text" class="ui input" disabled/>
                        </div>
                        <div class="ui field text">
                            <label class="ui label">身份证号码</label>
                            <input id="idCard" type="text" class="ui input" maxlength="18" disabled/>
                        </div>
                        <!-- <div class="input_box" style="border-top:0">
                            <div class="ui field text right_icon" style="border: none">
                                <label class="ui label">个人年收入（万）</label><input id="income" placeholder="请选择年收入" class="ui input"
                                                                         readonly="readonly"
                                                                         style="color: #666666;padding-right: 0.3rem"/>
                            </div>
                        </div>
                        <div class="input_box" style="border: none">
                            <div class="ui field text right_icon" style="border: none">
                                <label class="ui label">职业</label><input id="occp" placeholder="请选择职业" class="ui input"
                                                                         readonly="readonly"
                                                                         style="color: #666666;padding-right: 0.3rem"/>
                            </div>
                        </div>
                        <div class="input_box" >
                         	<div class="ui field text right_icon" style="border: none">
                            	<label class="ui label">居住地址</label>
                            	<input id="live_address" placeholder="请选择居住地址" class="ui input" readonly  style="color: #666666;padding-right: 0.3rem"/>
                        	</div>
                   		</div> -->
                        


                        <!--                        <div class="ui field text">-->
                        <!--                            <label class="ui label">个人税收居民身份类别</label>-->
                        <!--                            <input id="tax_certificate" type="text" class="ui input" maxlength="18" disabled/>-->
                        <!--                        </div>-->
                        <div class="ui field text" style="display: none">
                            <label class="ui label">性别</label>
                            <input id="sex" type="text" class="ui input" maxlength="18" disabled/>
                        </div>
                        <div class="ui field text cust_address" >
                            <label class="ui label">身份证地址</label>
                            <input id="cust_address" placeholder="证件地址识别有误，请手动输入" type="text" class="ui input" maxlength="32" disabled/>
                        </div>
                        <div class="ui field text" style="display: none">
                            <label class="ui label">证件有效期</label>
                            <input id="vaild_date" type="text" class="ui input" disabled/>
                        </div>
                    </div>
                    <!-- <div class="bank_form">
                        <div class="rule_check" style="padding: 0 0.1rem">
                            <span id="xuanzeqi" style="display: inline-block"><i></i>个人税收居民身份类别<span>仅为中国税收居民</span></span>
                        </div>
                    </div> -->
                    <div class="btn flex">
                        <a href="javascript:void(0);" class="ui button m_text_center rounded bg_fff haveIdCard" id="waitUpload">稍后上传</a>
                        <a href="javascript:void(0);" class="ui button m_text_center rounded bg_fff noIdCard" id="uploadId" style="display:none;">上传身份证</a>
                        <a href="javascript:void(0);" class="ui button m_text_center rounded" id="xyb">下一步</a>
                    </div>
                </div>
            </div>
        </article>
    </section>
     <div class="pop_layer1" id="moneyBox" style="display: none"></div>
     <div class="password_box" style="display: none;position: absolute;top: 40%;left: 0;right: 0;margin: auto;">
        <div class="password_inner slidedown in" style="padding: 0.1rem;z-index: 9999;">
            <div class="ui field text input_box2 has_border">
				<label class="short_label">个人年收入（万元）</label>
				<span style="position:absolute;height: 0.24rem;line-height:0.24rem;left:1.4rem ;right: 0;" id="inputspanid"><span style="position: absolute; left: 0px; width: auto; color: rgb(153, 153, 153);" text="请输入金额" class="unable">请输入金额</span></span>
				<input custom_keybord="0" type="text" onkeyup="value=value.replace(/[^\d\.\,]/g,'')" onblur="value=value.replace(/[^\d\.\,]/g,'')" id="srje" maxlength="12" class="ui input" placeholder="请输入金额" style="outline: none;border: none;margin-left:5px;display: none;" readonly="readonly">
			</div>
        </div>
    </div> 
  
</div>


