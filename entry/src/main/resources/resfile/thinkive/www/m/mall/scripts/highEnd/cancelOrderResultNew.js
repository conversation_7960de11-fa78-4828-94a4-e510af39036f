// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "highEnd/cancelOrderResultNew",
        _pageId = "#highEnd_cancelOrderResultNew ";
        let getDetails,t,num;
    var ut = require("../common/userUtil");

    function init() {
        getDetails = appUtils.getPageParam();
        //倒计时
        num = 5;
        countDown();
    }
    function countDown(){
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);

                if(getDetails.sell == true){
                    service.reqFun102090(getDetails, function (data) {
                        $(_pageId + ".load").hide();
                        if (data.error_no == "0") {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            let trans_status = results.trans_status;
                            if(trans_status == '6'){
                                //交易成功
                                $(_pageId + " #padding_status").hide();
                                $(_pageId + " #success_status").show();
                            }else{
                                $(_pageId + " #padding_status").hide();
                                $(_pageId + " #success_status").hide();
                                $(_pageId + " #wait_status").show();
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info)
                        }
                    })
                }else if(getDetails.appoint == true){
                    service.reqFun102085(getDetails, function (data) {
                        $(_pageId + ".load").hide();
                        if (data.error_no == "0") {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            let trans_status = results.trans_status;
                            if(trans_status == '6'){
                                //交易成功
                                $(_pageId + " #padding_status").hide();
                                $(_pageId + " #success_status").show();
                            }else{
                                $(_pageId + " #padding_status").hide();
                                $(_pageId + " #success_status").hide();
                                $(_pageId + " #wait_status").show();
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info)
                        }
                    })
                }else{
                    service.reqFun102084(getDetails, function (data) {
                        $(_pageId + ".load").hide();
                        if (data.error_no == "0") {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            let trans_status = results.trans_status;
                            if(trans_status == '6'){
                                //交易成功
                                $(_pageId + " #padding_status").hide();
                                $(_pageId + " #success_status").show();
                            }else{
                                $(_pageId + " #padding_status").hide();
                                $(_pageId + " #success_status").hide();
                                $(_pageId + " #wait_status").show();
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info)
                        }
                    })
                }
            }
        }, 1000)
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            if(getDetails.isInvestment == true){
                //来自定投页面 进入定投记录
                routerList.splice(-1);
                appUtils.setSStorageInfo("routerList",routerList)
                appUtils.pageBack();
            }else{
                //其他进入交易记录
                routerList.splice(-2);
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.setSStorageInfo("series_id",'');
                appUtils.pageInit(_page_code, "template/transaction");
            }
        })
        //申请撤单
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var routerList = appUtils.getSStorageInfo("routerList")
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList",routerList)
            appUtils.pageBack();
        });
    }

    function destroy() {
        $(_pageId + " #wait_status").hide();
        $(_pageId + " #padding_status").show();
        $(_pageId + " #success_status").hide();
        $(_pageId + ".load").show();
        num = 5;
        window.clearInterval(t);
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thcancelOrderResult = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thcancelOrderResult;
});
