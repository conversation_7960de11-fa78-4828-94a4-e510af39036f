// 代言人-添加好友记录
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        gconfig = require("gconfig"),
        _pageCode = "spokesperson/addFriendRecord",
        _pageId = "#spokesperson_addFriendRecord ";
    var global = gconfig.global;
    var cur_page = 1, isEnd = false, num_per_page = "15";

    function init() {
        getAddFriendRecord(false);
    }

    //获取交易记录
    function getAddFriendRecord(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        var param = {
            "current": cur_page,
            "count": num_per_page,
        };
        $(_pageId + "[data-name=digest]").html(tools.fundDataDict(param.busi_type, "sub_busi_name"));
        $(_pageId + "[data-name=order_state]").html(tools.fundDataDict(param.busi_type, "trans_status_name"));
        var callback = function (resultVo) {
            if (resultVo.error_no == "0") {
                var detailParams = resultVo.results[0].data;
                var totalPages = resultVo.results[0].totalPages; //总页数
                var htmls = "";
                if (detailParams && detailParams.length > 0) {
                    for (var i = 0; i < detailParams.length; i++) {
                        // 0未审核 1审核通过 2审核驳回
                        if (detailParams[i].audit_status == "0") {
                            detailParams[i]["audit_status_name"] = "未审核"
                        } else if (detailParams[i].audit_status == "1") {
                            detailParams[i]["audit_status_name"] = "通过"
                        } else if (detailParams[i].audit_status == "2") {
                            detailParams[i]["audit_status_name"] = "驳回"
                        }
                        htmls += "<tr class='addFriendRecord'><td style='width:25%;'>" + tools.ftime(detailParams[i].crt_date) + "</td>" +
                            "<td style='width:25%;'>" + detailParams[i].cust_name + "</td>" +
                            "<td style='width:25%;padding-right: 0.15rem'>" + detailParams[i].registered_mobile + "</td>" +
                            "<td style='width:25%;padding-right: 0.15rem'>" + detailParams[i].audit_status_name + "</td>" +
                            "<td style='display: none' class='addFriendRecordHide'>" + JSON.stringify(detailParams[i]) + "</td>" +
                            "</tr>"
                    }
                } else if (!detailParams) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                if (detailParams && detailParams.length < num_per_page) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }

            if (isAppendFlag) {
                $(_pageId + " #concent").append(htmls);
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
            } else {
                $(_pageId + " #concent").html(htmls);
            }
            pageScrollInit();
        }
        service.reqFun113008(param, callback);
    }

    /**
* 上下滑动刷新事件
* */

    function pageScrollInit() {

        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    getAddFriendRecord(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getAddFriendRecord(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        if (isEnd) {//可能有当前页为1总页数为0的情况
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
        //ios上拉拖动卡死问题解决
        var pageTouchTimer = null;
        //		var contentHeight = $(_pageId + " #wrapper_w1").height()-570;
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");
        appUtils.bindEvent($(_pageId + " #v_wrapper_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //页面销毁
    function destroy() {
        cur_page = 1;
        isEnd = false;
        $(_pageId + " #concent").html("");
        $(_pageId + " .new_none").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    let addFriendRecordModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = addFriendRecordModule;
});
