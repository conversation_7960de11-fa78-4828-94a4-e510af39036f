//开始定投页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#fixedInvestment_startInvestment ";
    var ut = require("../common/userUtil");
    var _pageCode = "fixedInvestment/startInvestment";
    var tools = require("../common/tools");
    var userInfo;
    var get_pdf_file = require("../common/StrongHintPdf");
    var monkeywords = require("../common/moneykeywords");
    var threshold_amount; //起购金额
    var jymm;
    var _available_vol; //可用金额
    var _fund_code; //所购买的产品代码
    var sms_mobile = require("../common/sms_mobile");
    var addition_amt;//追加金额
    var step_amt; //递增金额
    var product_risk_level; //产品风险等级
    var buyflag; //风险等级是否匹配  1 不匹配
    var buy_state; //1 购买、2 预约
    var isFirstPurchase;// 是否首次购买产品
    var _first_max_amt = "";
    var productInfo;
    let weekList = ['', '周一', '周二', '周三', '周四', '周五'] //每周定投日期
    let investcycle = '2';
    let investdate = '';
    let firstText = '';
    let secoundText = '';
    let payMethod = '0';//支付方式
    let nextdate = "";//预计下次扣款时间
    let rateList = [];
    let bank_state = "";//银行状态
    let bankInfo  = {};//缓存的银行卡信息
    let pdfParam = {};//获取PDF信息
    let day_limit;
    let single_limit;
    let bank_serial_no;
    let isAdvisoryInvestment;//区分页面来源 1投顾 
    let sms_time;//倒计时
    //新增签约定投处理
    let pay_mode,pay_modelimit,pay_modelimitInfo
    var day_purchase_limit_show;
    //单日购买限额
    var td_sum_max_amt;
    // let resultDeta
    function init() {
        tools.whiteList(_pageId);
        sms_mobile.init(_pageId);//初始化发短信
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " #inputspanid span").addClass("unable").css({ color: "#999999" });//默认输入框失去焦点
        firstText = '每月';
        investcycle = '2';
        //计算初始化日
        investdate = new Date().getDate() < 28 ?  new Date().getDate() + 1 + '' : '1';
        secoundText = investdate + '日';
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        $(_pageId + " .investmentText").text(`每月 ${investdate}日`);
        //标记客户选中的
        $(_pageId + " .investmentText").attr("investcycle",investcycle)
        $(_pageId + " .investmentText").attr("investdate",investdate)
        userInfo = ut.getUserInf();
        _fund_code = appUtils.getSStorageInfo("fund_code") ? appUtils.getSStorageInfo("fund_code") : appUtils.getSStorageInfo("comb_code");
        //页面埋点初始化
        tools.initPagePointData({fundCode:_fund_code});
        pdfParam.fund_code = _fund_code;

        //区分投顾定投/其他定投
        isAdvisoryInvestment = appUtils.getSStorageInfo("isAdvisoryInvestment");
        if(isAdvisoryInvestment && isAdvisoryInvestment == '1'){
            //投顾定投，显示理财计算器入口
            $(_pageId + " .startInvestment_financial-calculator-btn").show();
            //投顾定投，获取产品详情
            advisorFundInfo();
            $(_pageId + " .buy_rule").hide();
            $(_pageId + " .calculator").hide();
            $(_pageId + " .fund_code_box").hide();
            $(_pageId + " .placeholder").hide();
            $(_pageId + " .iframe_pdf").show();
            $(_pageId + " .pdf_name").text("风险揭示书");
            let comb_code = productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code
            get_pdf_file.get_file(comb_code, _pageId, "7")
        }else{
            //其他定投，隐藏理财计算器入口
            $(_pageId + " .startInvestment_financial-calculator-btn").hide();
            //其他定投，获取产品详情
            getFundInfo();
            $(_pageId + " .buy_rule").show();
            $(_pageId + " .calculator").show();
            $(_pageId + " .fund_code_box").show();
            $(_pageId + " .placeholder").show();
            $(_pageId + " .iframe_pdf").hide();
            $(_pageId + " .pdf_name").text("基金产品资料概要");
            is_show_paf()
            if(isAdvisoryInvestment && isAdvisoryInvestment == '2'){
                //晋金宝定投特殊处理
                $(_pageId + " .chooseJjb").hide();
            }else{
                $(_pageId + " .chooseJjb").show();
            }
        }

        
        //可用份额
        reqFun101901();
        common.systemKeybord(); // 解禁系统键盘
        //PDF相关，走公共方法
        // is_show_paf()
        
        //渲染每月 每周 每两周 日期数据
        setChooseTime(0)
        //初始化
        //获取下一交易日
        getNextTime(investcycle, investdate);
        
        // initialization()
    }
    //获取费率
    function getRateList() {
        var params = {
            fund_code: _fund_code
        }
        service.reqFun102003(params, function (data) {
            if (data.error_no == 0) {
                rateList = data.results[0].purchaseRate;
                reqFun102045('init');
            }
        })
    }
    function getNextTime(investcycle, investdate) {
        let data = {
            investcycle: investcycle,
            investdate: investdate
        }
        service.reqFun106045(data, function (datas) {
            if (datas.error_no != '0') {
                layerUtils.iAlert(data.error_info);
                return;
            }
            nextdate = (tools.FormatDateText(datas.results[0].nextdate, 1))
            $(_pageId + ' .nextTimes').text(nextdate + ' 11:00')
        })
    }
    //页面数据初始化
    function initialization() {
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        $(_pageId + " #amount_enough").removeClass('active');
        $(_pageId + " .investmentText").text(`每月 ${investdate}日`);
        firstText = '每月';
        secoundText = investdate + '日';
        payMethod = '0';
    }
    function setChooseTime(num) {
        let str = ''
        if (num == 0) {
            //月
            if($(_pageId + ' .investmentText').attr("investcycle") == '2'){
                investdate = $(_pageId + ' .investmentText').attr("investdate")
                
            }else{
                investdate = '1'
            }
            secoundText = `${investdate}日`
            for (let i = 1; i <= 28; i++) {
                let childStr = `<li class="${i == investdate ? 'active' : ''}" id="${i}">${i + '日'}</li>`
                str += childStr
            }
        } else {
            //周
            if($(_pageId + ' .investmentText').attr("investcycle") != '2'){
                investdate = $(_pageId + ' .investmentText').attr("investdate")
            }else{
                investdate = '1'
            }
            secoundText = `${weekList[investdate*1]}`
            for (let i = 1; i < weekList.length; i++) {
                let childStr = `<li class="${i == investdate ? 'active' : ''}" id="${i}">${weekList[i]}</li>`
                str += childStr
            }
        }
        // investdate = '1'
        $(_pageId + ' .listRight').html(str);
        if(num == 0){
            let positionNum = investdate*1 - 1;
            $(_pageId + " .listRight").eq(0).scrollTop(45*positionNum);
        }
    }
    function setBankInfo() {
        //获取银行卡限额
        get_single_limit()
        let imgUrl = "images/bank_" + userInfo.bankCode + ".png"
        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
        $(_pageId + " .bankImg").css({ "background": "url('" + imgUrl + "') no-repeat" });
        $(_pageId + " .backName").html(userInfo.bankName + '(尾号' + bankAcct + ')')
    }
    //获取银行卡限额
    function get_single_limit() {
        let params = {
            acct_no:userInfo.bankAcct,
            bank_code:userInfo.bankCode,
            bank_reserved_mobile:userInfo.bankReservedMobile
        }
        service.reqFun101084(params, function (data) {
            if (data.results && data.results.length > 0) {
                var result = data.results[0];
                bankInfo = result;
                let is_bank_fixed_investment = result.is_bank_fixed_investment;//是否支持银行卡定投
                let fixed_investment_priority = result.fixed_investment_priority;//优先级判断 0 晋金宝 1 银行卡
                let is_exist = result.is_exist; //是否签约 0未签约 1已签约
                bank_state = result.bank_state; //0银行维护中，1银行正常
                pay_mode= result.pay_mode;  //支付模式， 0 签约+支付 1 单独签约
                pay_modelimit = result.pay_sendsms_amt;  // 单独签约模式下发短信限额
                pay_modelimitInfo = `定投金额超过${pay_modelimit*1}元，请您使用晋金宝定投。`
                sms_time = result.sms_time;
                let backData = appUtils.getSStorageInfo("backData");    //缓存中的页面数据
                if(is_bank_fixed_investment == '0'){    //不支持
                    pdfParam.bankcard_fixedinvest_flag = '0'
                    payMethod = '0'
                    tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank").hide();
                    $(_pageId + " .grid_03").hide();
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .chooseJjb").removeClass('borderActive');
                    return;
                }else{
                    // $(_pageId + " .chooseJjb").addClass('borderActive');
                    // $(_pageId + " .chooseBank").show();
                    // pdfParam.bankcard_fixedinvest_flag = '1'
                    // tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank").show();
                    $(_pageId + " .grid_03").show();
                    $(_pageId + " .chooseJjb").addClass('borderActive');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                };
                if(fixed_investment_priority == '0'){   //优先晋金宝
                    pdfParam.bankcard_fixedinvest_flag = '0';
                    payMethod = '0';
                    tools.getPdf(pdfParam);
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .grid_03").hide();
                }else{
                    if(is_exist == '0'){    //未签约，展示验证码
                        $(_pageId + " .chooseBank").addClass('borderActive');
                        $(_pageId + " .grid_03").show();
                    }else{
                        $(_pageId + " .grid_03").hide();
                    }
                    $(_pageId + " .chooseJjb .img").removeClass('active');
                    $(_pageId + " .chooseBank .img").addClass('active');
                    let data = pdfParam;
                    
                    // payMethod = '1'; 
                    if(!backData || backData.backShow != "1") payMethod = '1';
                    data.bankcard_fixedinvest_flag = '1'
                    tools.getPdf(data);
                }
                if(bank_state == '0'){  //维护中
                    bank_state = '0';
                    pdfParam.bankcard_fixedinvest_flag = '0'
                    payMethod = '0'
                    tools.getPdf(pdfParam); //获取协议
                    $(_pageId + " .chooseBank .img").removeClass('active');
                    $(_pageId + " .chooseJjb .img").addClass('active');
                    $(_pageId + " .chooseBank").addClass('color_ccc')
                }else{
                    tools.getPdf(pdfParam);
                    bank_state = '1';
                    // payMethod = '1';
                    // if(!backData || backData.backShow != "1") payMethod = '1';
                    $(_pageId + " .chooseBank").removeClass('color_ccc')
                }
                
                single_limit = result.single_limit;
                day_limit = result.day_limit;
                if (single_limit) {
                    if (single_limit < 0) {
                        $(_pageId + " .single_limit").html("不限");
                    } else {
                        if (single_limit.length > 4) {
                            $(_pageId + " .single_limit").html(single_limit / 10000 + "万元");
                        } else {
                            $(_pageId + " .single_limit").html(single_limit + "元");
                        }
                    }
                }
                if (day_limit) {
                    if (day_limit < 0) {
                        $(_pageId + " .day_limit").html("不限");
                    } else {
                        if (day_limit.length > 4) {
                            $(_pageId + " .day_limit").html(day_limit / 10000 + "万元");
                        } else {
                            $(_pageId + " .day_limit").html(day_limit + "元");
                        }
                    }
                }
                if(backData && backData.backShow == '1'){
                    if(backData.payMethod == '0'){
                        $(_pageId + " .chooseBank .img").removeClass('active');
                        $(_pageId + " .chooseJjb .img").addClass('active');
                        $(_pageId + " .grid_03").hide();
                        payMethod = '0'
                    }else if(backData.payMethod == '1'){
                        $(_pageId + " .chooseJjb .img").removeClass('active');
                        $(_pageId + " .chooseBank .img").addClass('active');
                        payMethod = '1'
                        if(backData.is_exist == '0'){
                            //未签约
                            $(_pageId + " .chooseBank").addClass('borderActive');
                            $(_pageId + " .grid_03").show();
                        }else{
                            $(_pageId + " .grid_03").hide();
                            $(_pageId + " .chooseBank").removeClass('borderActive');
                        }
                    }
                }
                if(!backData || backData == 'null') return;
                backData.backShow = '0'
                appUtils.setSStorageInfo("backData",backData);
            } else {
                layerUtils.iAlert(error_info);
            }
        })
    }
    function is_show_paf() {
        get_pdf_file.get_file(_fund_code, _pageId)
    }
    function bindPageEvent() {
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            let czje_InvestAmt = $(_pageId + " #czje").val();
            if(czje_InvestAmt) czje_InvestAmt = czje_InvestAmt.replace(/,/g, "");
            if(pay_mode == '2' && payMethod == '1' && czje_InvestAmt && parseFloat(czje_InvestAmt) > 0 &&  (parseFloat(czje_InvestAmt) > parseFloat(pay_modelimit))){
                return layerUtils.iAlert(pay_modelimitInfo);
            }
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var param = {
                    "bank_code": userInfo.bankCode,//银行编码
                    "pay_type": bankInfo.pay_type,
                    "payorg_id": bankInfo.payorg_id,
                    "bank_acct": userInfo.bankAcct,     // 用户卡号
                    "bank_reserved_mobile":userInfo.bankReservedMobile,
                    "cert_no": userInfo.identityNum,   // 用户身份证
                    "bank_name":userInfo.bankName,
                    "sms_type":common.sms_type.bankInvestment,
                    "send_type": "0",
                    "cust_name": userInfo.name, // 用户姓名
                    "cert_type": "0", //证件类型
                    "mobile_phone": userInfo.mobileWhole,
                    "type": common.sms_type.bankInvestment,//发送短信验证码
                    "sms_time":sms_time,//短信验证码倒计时
                }
                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
        appUtils.bindEvent($(_pageId + " .calculator"), function () {
            if (!common.loginInter(_pageCode)) return;
            appUtils.pageInit(_pageCode, "fixedInvestment/calculator");
        });
        
        // 理财计算器按钮点击事件
        appUtils.bindEvent($(_pageId + " .startInvestment_financial-calculator-btn"), function () {
             tools.recordEventData('1','startInvestment_financial-calculator-btn','跳转到理财计算器页面');
             var trialIncomeRate = productInfo && productInfo.trial_income_rate ? parseFloat(productInfo.trial_income_rate.trim()) : 5;
             appUtils.setSStorageInfo("calculator_productType", trialIncomeRate);
             // 跳转到理财计算器页面
             appUtils.pageInit(_pageCode, "scene/financialCalculator");
        });

        //选择日期
        appUtils.preBindEvent($(_pageId + " .listRight"), "li", function () {
            $(_pageId + " .listRight li").removeClass('active');
            $(this).addClass('active');
            //文案初始化
            investdate = $(this).attr('id');
            secoundText = $(this).text();
            //参数初始化
            // investcycle = '2';
            // investdate = '1';
        }, 'click');
        //选择周，两周，月
        appUtils.preBindEvent($(_pageId + " .listLeft"), "li", function () {
            $(_pageId + " .listLeft li").removeClass('active')
            $(this).addClass('active')
            investcycle = $(this).attr('id')
            firstText = $(this).text()
            if (investcycle == 2) {
                setChooseTime(0)
            } else {
                setChooseTime()
            }
        }, 'click');
        //选择支付方式
        appUtils.preBindEvent($(_pageId + " .payList"), "#amount_enough", function () {
            if($(this).attr('payMethod') == '1' && bank_state == '0') return;
            $(_pageId + " #amount_enough .img").removeClass('active')
            $(this).find(".img").addClass('active')
            payMethod = $(this).attr('payMethod');
            if(payMethod == '1'){   //选中银行卡
                let data = pdfParam;
                data.bankcard_fixedinvest_flag = '1'
                tools.getPdf(data);
                if(bankInfo.is_exist == '0'){    //未签约，展示验证码
                    $(_pageId + " .chooseBank").addClass('borderActive');
                    $(_pageId + " .grid_03").show();
                }else{
                    $(_pageId + " .grid_03").hide();
                }
                
            }else{
                pdfParam.bankcard_fixedinvest_flag = '0'
                tools.getPdf(pdfParam);
                $(_pageId + " .chooseBank").removeClass('borderActive');
                $(_pageId + " .grid_03").hide();
            }
        }, 'click');
        //选择定投日期
        appUtils.bindEvent($(_pageId + " .cycleClick"), function () {
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #cycleModel").show()
            let positionNum = investdate*1 - 1;
            $(_pageId + " .listRight").eq(0).scrollTop(45*positionNum);
        });
        //确定选择定投日期
        appUtils.bindEvent($(_pageId + " .determine"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #cycleModel").hide()
            $(_pageId + " .investmentText").text(firstText + ' ' + secoundText)
            $(_pageId + " .investmentText").attr("investcycle",investcycle);
            $(_pageId + " .investmentText").attr("investdate",investdate);
            getNextTime(investcycle, investdate)
        });
        // appUtils.bindEvent($(_pageId + " .short_label2"), function () {
        //     sms_mobile.clear();
        // });
        //选择支付方式弹窗
        appUtils.bindEvent($(_pageId + " .choosePay"), function () {
            // $(_pageId + " .pop_layer").show();
            // $(_pageId + " #payMethod").show()
        });
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            let backData = {
                payMethod:payMethod,
                backShow:'1',
                is_exist:bankInfo.is_exist
            }
            appUtils.setSStorageInfo("backData", backData);
            tools.intercommunication(_pageCode);
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            // console.log(payMethod)
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fixedInvestment_startInvestment";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
            $(_pageId + " .showLimit").removeClass("m_text_red");
            $(_pageId + " #inputspanid span").removeClass('m_text_red');
            $(_pageId + " .thfundBtn .buy").removeClass('no_active');
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            sessionStorage.vip_buttonShow = true;
            if(isAdvisoryInvestment == '1'){
                //跳转投顾
                appUtils.pageInit(_pageCode, "combProduct/combProdDetail");
            }else{
                appUtils.pageInit(_pageCode, "template/publicOfferingDetail");
            }
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭周期选择弹框
        appUtils.bindEvent($(_pageId + " #closeCycle"), function () {
            $(_pageId + " #cycleModel").hide();
            $(_pageId + " .pop_layer").hide();
            //重置客户选中的
            investcycle = $(_pageId + " .investmentText").attr("investcycle");
            investdate = $(_pageId + " .investmentText").attr("investdate");
            // firstText = ($(_pageId + " .investmentText").text()).split(' ')[0];
            // secoundText = ($(_pageId + " .investmentText").text()).split(' ')[1];
            //周期对应文案列表
            let investcycleData = {
                "0":"每周",
                "1":"每两周",
                "2":"每月"
            }
            firstText = investcycleData[investcycle];
            secoundText = investcycle == '2' ? investdate + '日' : '周' + investdate;
            //还原用户之前选择的
            $(_pageId + " .listLeft li").removeClass("active");
            $(_pageId + " .listRight li").removeClass("active");
            // $('#myDiv ul li:nth-child(2)').addClass('new-class');
            if(investcycle == '0'){  //每周
                $(_pageId + " .listLeft li").eq(1).addClass("active");
            }else if(investcycle == '1'){//每两周
                $(_pageId + " .listLeft li").eq(2).addClass("active");
            }else if(investcycle == '2'){//
                $(_pageId + " .listLeft li").eq(0).addClass("active");
            }
            let num = investcycle == '2' ? 0 : 1;
            setChooseTime(num);
        });
        //风险承受能力确认
        appUtils.bindEvent($(_pageId + " .agreement1"), function () {
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active")
            } else {
                $(this).find("i").addClass("active")
            }
        });
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fixedInvestment_startInvestment";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //显示购买弹框
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            if ($(this).hasClass("no_active")) return;
            var curVal = $(_pageId + " #czje").val();
            var moneys = curVal.replace(/,/g, "");
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)

            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }

            if (moneys <= 0 || !moneys) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (!investdate || !investcycle) return layerUtils.iAlert("请选择定投周期");
            if (threshold_amount && parseFloat(moneys) < parseFloat(threshold_amount) && isFirstPurchase) { //首次购买
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            } else if (addition_amt && parseFloat(moneys) < parseFloat(addition_amt) && !isFirstPurchase) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            if(pay_mode == '2' && payMethod == '1' && moneys && moneys > 0 && parseFloat(moneys) > parseFloat(pay_modelimit)){
                return layerUtils.iAlert(pay_modelimitInfo);
            }
            if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                $(_pageId + " .bast_rate").text("--");
                return
            }
            if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) {
                $(_pageId + " .bast_rate").text("--");
                return
            }
            if (_first_max_amt && moneys > parseFloat(_first_max_amt)) {
                layerUtils.iAlert("超过单笔最高限额");
                return;
            }
            let bank_first_max_amt = bankInfo.single_limit*1
            if(payMethod == '1' && moneys > bank_first_max_amt){
                return layerUtils.iAlert("定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。");
            }
            if(payMethod == '1' && bankInfo.is_exist == '0' && isSend == "true"){
                return layerUtils.iAlert("请获取验证码");
            }
            if (verificationCode.length != 6 && payMethod == '1' && bankInfo.is_exist == '0') {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            if (payMethod == '0') {
                $(_pageId + " .payMethodName").text('晋金宝');
                var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
                $(_pageId + " .payMethodRemark").html(html);
                // $(_pageId + " .payMethodRemark").html('可用金额:'+tools.fmoney(_available_vol) + "元");

            } else if (payMethod == '1') {
                let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
                $(_pageId + " .payMethodName").text(userInfo.bankName + '(尾号' + bankAcct + ')');
                $(_pageId + " .payMethodRemark").text('限额:');
            }
            if (buyflag == "1" && isAdvisoryInvestment != '1') {
                layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>您的风险承受能力为" + userInfo.riskName + "，此产品超过了您的风险承受能力，若仍然选择投资，则表明在上述情况下，您仍自愿投资该产品，并愿意承担可能由此产生的风险</span>", function () {
                    appUtils.pageInit(_pageCode, "safety/riskQuestion");
                }, function funcNo() {
                    $(_pageId + " .pop_layer").show();
                    // $(_pageId + " #payMethod").show();
                    $(_pageId + " .password_box").show();
                    //输入交易密码时的提示
                    setRechargeInfo();
                    passboardEvent();
                    monkeywords.flag = 0;
                    //键盘事件
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "fixedInvestment_startInvestment";
                    param["eleId"] = "jymm";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "4";
                    require("external").callMessage(param);
                    //是否可以购买
                    // isCanBuy();
                }, "重新测评", "继续购买");
                return;
            }
            if (buyflag == "1" && isAdvisoryInvestment == '1') {
                layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>本产品为" + productInfo.comb_risk_name + "等级，超出您的风险承受能力（" + userInfo.riskName + "），若仍选择投资，请重新测评。</span>", function () {
                }, function funcNo() {
                    // $(_pageId + " .pop_layer").show();
                    // $(_pageId + " #payMethod").show();
                    // //是否可以购买
                    // isCanBuy();
                    appUtils.pageInit(_pageCode, "safety/riskQuestion");
                }, "取消", "重新测评");
                return;
            }
            $(_pageId + " .pop_layer").show();
            // $(_pageId + " #payMethod").show();
            //是否可以购买
            // isCanBuy();
            $(_pageId + " #payMethod").hide();
            
            // if ($(this).hasClass("noactive")) {
            //     return;
            // }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fixedInvestment_startInvestment";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        // 验证码失去焦点
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            // $(_pageId + " .thfundBtn").show();
            // window.scrollTo(0, 0);
            // $(_pageId).scrollTop(0)
            // $(_pageId + " article").scrollTop(0);
        }, "blur");
        // 验证码获取焦点
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            // console.log(333)
            // window.scrollTo(0, 0);
            // $(_pageId).scrollTop(0)
            // $(_pageId+ " article").scrollTop(0);
            // $(_pageId + " .thfundBtn").hide();
        }, "focus");
        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            // $(_pageId + " .pop_layer").hide();
            $(_pageId + " #payMethod").hide();
            if (payMethod == '0') {
                $(_pageId + " .payMethodName").text('晋金宝');
                var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
                $(_pageId + " .payMethodRemark").html(html);
                // $(_pageId + " .payMethodRemark").html('可用金额:'+tools.fmoney(_available_vol) + "元");

            } else if (payMethod == '1') {
                let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
                $(_pageId + " .payMethodName").text(userInfo.bankName + '(尾号' + bankAcct + ')');
                $(_pageId + " .payMethodRemark").text('限额:');
            }
            // if ($(this).hasClass("noactive")) {
            //     return;
            // }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fixedInvestment_startInvestment";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        // 验证码 控制全文字
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            // jymm1 = '123123'
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //进行充值
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                investmoney: trans_amt, //交易金额
                transpwd: jymm1, //交易密码
                custno: userInfo.custNo,
                fundcode: $(_pageId + " .fund_code").html(),
                // paymethod: '0',
                paymethod:payMethod,
                isexist:bankInfo.is_exist,
                messagecode:$(_pageId + " #verificationCode").val(),
                investdate: investdate,
                investcycle: investcycle,
                agreementsign: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.transpwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.transpwd);
                let prodType = appUtils.getSStorageInfo("prodType");
                if (prodType && prodType == "gather") {
                    service.reqFun106014(param, function (resultVo) {
                        if (resultVo.error_no == "0") {
                            if(isAdvisoryInvestment == '1'){
                                //投顾开始定投
                                advisorPurchase(param)
                            }else{
                                purchase(param);
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(resultVo.error_info);
                        }
                    }, { isLastReq: false });
                } else {
                    if(isAdvisoryInvestment == '1'){
                        //投顾开始定投
                        advisorPurchase(param)
                    }else{
                        purchase(param);
                    }
                }

            }, { isLastReq: false });
        });
    }
    //查询投顾产品是否首次购买
    async function reqFun106058() {
        var param = {
            fund_code: productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code
        }
        return new Promise((resolve, reject) => {
            service.reqFun106058(param, function (data) {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    if (results.is_can_add == 0) {
                        isFirstPurchase = false;    //非首次
                        resolve(isFirstPurchase);
                    } else {
                        isFirstPurchase = true;     //首次
                        resolve(isFirstPurchase);
                    }
                } else {
                    isFirstPurchase = true;
                    resolve(isFirstPurchase);
                    layerUtils.iAlert(data.error_info);
                }
                //获取产品详情
                // getFundInfo();
            }, { isLastReq: false })
        })
        
    }
    // 投顾产品详情
    async function advisorFundInfo(){
        let params = {
            comb_code: productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code
        }
        let res = await reqFun106058();
        isFirstPurchase = res;
        service.reqFun102165(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                // let money1,money2
                // money1 = result.fixed_invest_min*1;
                // money2 = result.first_per_min*1;
                threshold_amount = result.first_per_min; // 起购
                addition_amt = result.fixed_invest_min; // 定投
                step_amt = result.step_unit; // 递增
                $(_pageId + " .prod_sname").html(result.nick_comb_name ? result.nick_comb_name : result.comb_sname);//产品简称
                $(_pageId + " .header_inner h1").text(result.nick_comb_name ? result.nick_comb_name : result.comb_sname);
                // $(_pageId + " .fund_code").html(result.fund_code);//产品编码
                product_risk_level = result.comb_risk_level;
                $(_pageId + " .fund_type_name").html('本策略由' + result.mechanism + '提供');//remark
                // $(_pageId + " .fund_type_name").html(result.fund_type_name);//产品类型
                //持有不足天数
                // var tip_max_day = result.tip_max_day;
                // $(_pageId + " #tip_max_day").html(tip_max_day);
                // $(_pageId + " .buy_tip").html(result.buy_tip);
                // if (result.important_tip_buy && result.important_tip_buy == "1") {
                //     $(_pageId + " .important_hint").show();
                //     //赎回费率
                //     initRateInfo();
                // }

                var str = "";
                if (threshold_amount && isFirstPurchase) {
                    str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
                } else if (addition_amt && !isFirstPurchase) {
                    str += (addition_amt >= 10000 ? (addition_amt / 10000 + "万") : tools.fmoney(addition_amt)) + '元起购';
                }
                if (step_amt && step_amt > 0) {
                    str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                        tools.fmoney(step_amt + ''))
                        + "元递增"
                }
                if (str) {
                    $(_pageId + " #inputspanid span").text(str);
                    $(_pageId + " #inputspanid span").attr("text", str);
                } else {
                    $(_pageId + " #inputspanid span").text("请输入定投金额");
                    $(_pageId + " #inputspanid span").attr("text", "请输入定投金额");
                }
                _first_max_amt = result.first_max_amt;
                buy_state = result.buy_state;
                pdfParam = {
                    agreement_type: 'prod',
                    agreement_sub_type: '1',
                    fund_code: result.comb_code,
                    fixed_invest_flag: '1'
                }
                //渲染银行卡
                setBankInfo()
                // tools.getPdf(param); //获取协议
                //比较风险等级
                advisorCompareRiskLevel();
                productInfo = result;
                // reqFun102045();
                //获取费率区间
                // getRateList()
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, { isLastReq: false })
    }
    // 产品详情查询
    async function getFundInfo() {
        var params = {
            fund_code: _fund_code
        }
        let res = await reqFun102103();
        isFirstPurchase = res;
        service.reqFun102113(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                threshold_amount = result.threshold_amount;
                if(isAdvisoryInvestment && isAdvisoryInvestment == '2') threshold_amount = result.investment_step_amt;
                addition_amt = result.fixed_min_amt;
                day_purchase_limit_show = result.day_purchase_limit_show ? result.day_purchase_limit_show : '0';
                td_sum_max_amt = result.td_sum_max_amt ? result.td_sum_max_amt : '';
                step_amt = result.step_amt;
                $(_pageId + " .prod_sname").html(result.prod_sname);//产品简称
                $(_pageId + " .header_inner h1").text(result.prod_sname);
                $(_pageId + " .fund_code").html(result.fund_code);//产品编码
                product_risk_level = result.risk_level;
                $(_pageId + " .risk_level_name").html(result.risk_level_name);//风险
                $(_pageId + " .fund_type_name").html(result.fund_type_name);//产品类型
                //持有不足天数
                var tip_max_day = result.tip_max_day;
                $(_pageId + " #tip_max_day").html(tip_max_day);
                $(_pageId + " .buy_tip").html(result.buy_tip);
                if (result.important_tip_buy && result.important_tip_buy == "1") {
                    $(_pageId + " .important_hint").show();
                    //赎回费率
                    initRateInfo();
                }

                var str = "";
                if (threshold_amount && isFirstPurchase) {
                    str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
                } else if (addition_amt && !isFirstPurchase) {
                    str += (addition_amt >= 10000 ? (addition_amt / 10000 + "万") : tools.fmoney(addition_amt)) + '元起购';
                }
                if (step_amt && step_amt > 0) {
                    str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                        tools.fmoney(step_amt + ''))
                        + "元递增"
                }

                if (str) {
                    $(_pageId + " #inputspanid span").text(str);
                    $(_pageId + " #inputspanid span").attr("text", str);
                } else {
                    $(_pageId + " #inputspanid span").text("请输入定投金额");
                    $(_pageId + " #inputspanid span").attr("text", "请输入定投金额");
                }
                _first_max_amt = result.first_max_amt;
                buy_state = result.buy_state;
                pdfParam = {
                    agreement_type: 'prod',
                    agreement_sub_type: '1',
                    fund_code: result.fund_code,
                    fixed_invest_flag: '1'
                }
                //渲染银行卡
                setBankInfo()
                // tools.getPdf(param); //获取协议
                //比较风险等级
                compareRiskLevel();
                productInfo = result;
                // reqFun102045();
                //获取费率区间
                getRateList()
                //是否展示单日限额
                showDayLimit();
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, { isLastReq: false })
    }
    function showDayLimit(){
        if(day_purchase_limit_show == '1'){
            let str = `单日累计限额${tools.fmoney(td_sum_max_amt*1)}元`
            $(_pageId + " .showLimit").html(str);
            $(_pageId + " .showLimit").show();
        }else{
            $(_pageId + " .showLimit").hide();
        }
    }
    // 赎回费率查询
    function initRateInfo() {
        var params = {
            fund_code: _fund_code
        }
        service.reqFun102003(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                var redeemRateStr = "";
                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr = '<div class="hint_title"><span>持有期限</span><span>赎回费率</span></div>';
                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var datestr = "";
                        var fcitem_lval = result.redeemRate[i].fcitem_lval; //最小
                        var fcitem_lvunit = result.redeemRate[i].fcitem_lvunit;//最小单位
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;//最大
                        var fcitem_tvunit = result.redeemRate[i].fcitem_tvunit;//最大单位
                        if (fcitem_tval == "-1") { //最大
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                        } else if (fcitem_lval == "0") { //最小
                            datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                        } else {
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                        }
                        redeemRateStr += '<div class="hint_item">' +
                            '<span>' + datestr + '</span>' +
                            '<span>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</span>' +
                            '</div>';
                    }
                }
                $(_pageId + " .hint_table").html(redeemRateStr);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //比较风险等级
    function compareRiskLevel() {
        var userRiskLevel = userInfo.riskLevel;
        userRiskLevel = +(userRiskLevel.substr(-1))
        product_risk_level = (+product_risk_level.substr(-1));
        if (product_risk_level == 1) return buyflag = "0";
        if (product_risk_level > userRiskLevel) {
            buyflag = "1";
        }else{
            buyflag ="0"
        }
    }
    //投顾风险等级
    function advisorCompareRiskLevel(){
        var userRiskLevel = userInfo.riskLevel;
        userRiskLevel = +(userRiskLevel.substr(-1))
        if (product_risk_level > userRiskLevel) {
            buyflag = "1";
        }else{
            buyflag = "0";
        }
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //投顾定投
    function advisorPurchase(param){
        sms_mobile.clear();
        $(_pageId + " #verificationCode").val("");
        param.bank_serial_no = bank_serial_no;
        param.fundcode =  productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code;
        service.reqFun106059(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            let params = {
                payMethodName:$(_pageId + " .backName").text(),
                nextdate: nextdate,
                firstText: firstText,
                payMethod:payMethod,
                secoundText: secoundText,
                prod_sname: $(_pageId + " .prod_sname").html(),
                money: tools.fmoney($(_pageId + " #czje").val().replace(/,/g, ""))
            };
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            $(_pageId + " #inputspanid span").css({ color: "rgb(153, 153, 153)" });
            $(_pageId + " .grid_03").hide();
            appUtils.setSStorageInfo("tranType",'');
            productInfo.fund_code  = productInfo.comb_code;
            productInfo = appUtils.setSStorageInfo("productInfo",productInfo);
            appUtils.pageInit("login/userIndexs", "fixedInvestment/addResults", params);
        })
    }
    //现金宝定投
    function purchase(param) {
        sms_mobile.clear();
        $(_pageId + " #verificationCode").val("");
        param.bank_serial_no = bank_serial_no;
        service.reqFun106042(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            let params = {
                payMethodName:$(_pageId + " .backName").text(),
                nextdate: nextdate,
                firstText: firstText,
                payMethod:payMethod,
                secoundText: secoundText,
                prod_sname: $(_pageId + " .prod_sname").html(),
                money: tools.fmoney($(_pageId + " #czje").val().replace(/,/g, ""))
            };
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            $(_pageId + " #inputspanid span").css({ color: "rgb(153, 153, 153)" });
            $(_pageId + " .grid_03").hide();
            appUtils.setSStorageInfo("tranType",'');
            appUtils.pageInit("login/userIndexs", "fixedInvestment/addResults", params);
        })
    }
    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");

                if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                if (moneys) {
                    // moneys = tools.fmoney(moneys);
                    //获取单日限购金额
                    let sum_max_amt = td_sum_max_amt ? td_sum_max_amt*1 : '';
                    if(day_purchase_limit_show == '1' && sum_max_amt && (moneys*1 > sum_max_amt)){
                        //字体标红
                        $(_pageId + " .showLimit").addClass("m_text_red");
                        $(_pageId + " #inputspanid span").addClass('m_text_red')
                        $(_pageId + " .thfundBtn .buy").addClass('no_active');
                    }else{
                        $(_pageId + " .showLimit").removeClass("m_text_red");
                        $(_pageId + " #inputspanid span").removeClass('m_text_red');
                        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
                    }
                }
                $(_pageId + " #czje").val(moneys?tools.fmoney(moneys):'');
                reqFun102045();
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                if(bankInfo.is_bank_fixed_investment == '1' && bankInfo.bank_state == '1' && payMethod == '1'  && (parseFloat(single_limit) < parseFloat(curVal) && single_limit >= 0)){
                    layerUtils.iAlert('定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。');
                }
                if(pay_mode == '2' && payMethod == '1' && curVal && parseFloat(curVal) > parseFloat(pay_modelimit)){
                    $(_pageId + " #czje").val('');
                    layerUtils.iAlert(pay_modelimitInfo);
                }
                if (!curVal) return reqFun102045()
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                }
                reqFun102045();
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                    //获取单日限购金额
                    let sum_max_amt = td_sum_max_amt ? td_sum_max_amt*1 : '';
                    if(day_purchase_limit_show == '1' && sum_max_amt && (moneys*1 > sum_max_amt)){
                        //字体标红
                        $(_pageId + " .showLimit").addClass("m_text_red");
                        $(_pageId + " #inputspanid span").addClass('m_text_red');
                        $(_pageId + " .thfundBtn .buy").addClass('no_active');
                    }else{
                        $(_pageId + " .showLimit").removeClass("m_text_red");
                        $(_pageId + " #inputspanid span").removeClass('m_text_red')
                        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
                    }
                }
                if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                    $(_pageId + " .bast_rate").text("--");
                    return
                }
                reqFun102045();
            }
        })
    }

    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];

            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
            $(_pageId + " .payMethodRemark").html(html);
            $(_pageId + " .pay_bank").html(html);
        })
    }

    //查询产品是否首次购买 普通产品调用
    async function reqFun102103() {
        var param = {
            fund_code: _fund_code,
        }
        return new Promise((resolve, reject) => {
            service.reqFun102103(param, function (data) {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    if (results.is_can_add == 0) {
                        isFirstPurchase = false;
                        resolve(isFirstPurchase)
                    } else {
                        isFirstPurchase = true;
                        resolve(isFirstPurchase)
                    }
                } else {
                    isFirstPurchase = true;
                    resolve(isFirstPurchase)
                    layerUtils.iAlert(data.error_info);
                }
                //获取产品详情
                // getFundInfo();
            }, { isLastReq: false })
        })
    }

    //获取产品费率
    async function reqFun102045(str) {
        var money = $(_pageId + " #czje").val().replace(/,/g, "");
        if (!money || money == '0') money = 0
        let results = await tools.setRate(money, rateList);
        let rateTxt = ''
        if (!results.chgrate_tval) return
        if (str == 'init' || !money || money <= 0) {  //初始化
            if (!results.discount_ratevalue || results.discount_ratevalue == '') {
                rateTxt = `申购费率：<span class=''>${tools.fmoney(results.chgrate_tval) + results.chgrate_unit}</span>`
            } else {
                rateTxt = `申购费率：<span style='text-decoration: line-through' class=''>${tools.fmoney(results.chgrate_tval) + results.chgrate_unit}</span> <span>${tools.fmoney(results.discount_ratevalue) + results.chgrate_unit}</span>`
            }
            return $(_pageId + " .rate_text").html(rateTxt);
        }
        //判断费率是固定费率还是 %费率
        if (results.chgrate_unit == '元/笔') {
            if (results.chgrate_tval && !results.discount_ratevalue) {
                rateTxt = `估算费用：<span class='text_red'>${tools.fmoney(results.chgrate_tval)}元</span><span>(申购费率:${results.chgrate_tval + results.chgrate_unit})</span>`
                return $(_pageId + " .rate_text").html(rateTxt);
            } else if (results.chgrate_tval && results.discount_ratevalue) {
                rateTxt = `估算费用：<span class='text_red'>${tools.fmoney(results.discount_ratevalue) + '元'}</span > (申购费率:<span style='text-decoration: line-through'>${results.chgrate_tval + results.chgrate_unit}</span> <span>${tools.fmoney(results.discount_ratevalue) + results.chgrate_unit}<span>)<span class=''> 省${tools.fmoney(results.chgrate_tval - results.discount_ratevalue)}元</span>`
                return $(_pageId + " .rate_text").html(rateTxt);
            }
        } else if (results.chgrate_unit == '%') {
            let unit = results.chgrate_unit;
            let estimateMoneyMax = common.floatSub(money, common.floatDivide(money, (1 + results.chgrate_tval / 100)))   //估算费用最大值
            if (results && results.chgrate_tval && !results.discount_ratevalue) {   //没有优惠费率
                rateTxt = `估算费用：<span class='text_red'>${tools.fmoney(estimateMoneyMax)}元</span><span>(申购费率:${tools.fmoney(results.chgrate_tval) + unit})</span>`
            }
            if (results && results.discount_ratevalue) {    //拥有优惠费率
                let estimateMoneyMin = common.floatSub(money, common.floatDivide(money, (1 + ((results.discount_ratevalue / 100)))))  //估算费用最小值
                let newMoney = common.floatSub(estimateMoneyMax, estimateMoneyMin)
                rateTxt = `估算费用：<span class="text_red"> ${tools.fmoney(estimateMoneyMin)}元</span>(申购费率:<span style='text-decoration: line-through'>${tools.fmoney(results.chgrate_tval) + unit}</span>  ${tools.fmoney(results.discount_ratevalue) + unit})<span class=''> 省${tools.fmoney(newMoney)}元</span>`;
            }
            $(_pageId + " .rate_text").html(rateTxt);
        }
        // var money = $(_pageId + " #czje").val().replace(/,/g, "");
        // if (!money || money <= 0) {
        //     money = productInfo.threshold_amount
        // }
        // service.reqFun102045({
        //     fund_code: _fund_code,
        //     amt: money
        // }, function (data) {
        //     var rateTxt = "";
        //     if (data.error_no = "0") {
        //         var results = data.results;
        //         var unit = "";
        //         if (results.length > 0 && results[0].rate) {
        //             unit = results[0].unit || "";
        //             rateTxt = "买入费率：<span class='text_red'>" + results[0].rate + unit + "</span>";
        //         }
        //         if (results.length > 0 && results[0].discount_ratevalue) {
        //             unit = results[0].unit || "";
        //             rateTxt = "买入费率：<span style='text-decoration: line-through'>" + results[0].rate + unit + "</span>，<span class='text_red'>" + results[0].discount_ratevalue + unit + "</span>";
        //         }
        //         $(_pageId + " .rate_text").html(rateTxt);
        //     }
        // })
    }

    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            //可以购买
            // $(_pageId + " #insufficient_fund").hide();
            // $(_pageId + " #amount_enough").show();
            // $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            // $(_pageId + " #insufficient_fund").show();
            // $(_pageId + " #amount_enough").hide();
            // $(_pageId + " .model_bottom").addClass("noactive");
        }
    }


    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () { }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        $(_pageId + " #recharge_name").text(prod_sname);
        $(_pageId + " #recharge_money").text(tools.fmoney($(_pageId + " #czje").val().replace(/,/g, "")));
        $(_pageId + " #cycle").html('<em>' + $(_pageId + " .investmentText").text() + '</em>' + '扣款。')
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .grid_03").hide();
        $(_pageId + " .chooseJjb").removeClass('borderActive');
        $(_pageId + " .chooseBank").hide();
        $(_pageId + " .chooseBank").removeClass('borderActive');
        $(_pageId + " #chooseJjb .img").removeClass('active');
        $(_pageId + " .chooseBank .img").removeClass('active');
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        $(_pageId + " #inputspanid span").text("").css({ color: "rgb(153, 153, 153)" });
        get_pdf_file.clearTime();
        investcycle = '2'
        investdate = new Date().getDate() < 28 ?  new Date().getDate() + 1 + '' : '1';
        guanbi();
        $(_pageId + ' .nextTimes').text('')
        initialization();
        $(_pageId + " .agreement1").hide();
        $(_pageId + " .agreement").attr("isVerify", "false");
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");//产品编码
        $(_pageId + " .fund_type_name").html("--");//产品编码
        $(_pageId + " #czje").val("");
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " .agreement1 i").removeClass("active");
        $(_pageId + " .hint_table").html("");
        $(_pageId + " .rate_text").html("");
        $(_pageId + " .important_hint").hide();
        $(_pageId + " .van-overlay").hide();
        buyflag = "";
        $(_pageId + " .buy_rule").hide();
        buy_state = "";
        sms_mobile.destroy();
        monkeywords.destroy();
        _first_max_amt = "";
        productInfo = null;
        $(_pageId + " .jjs_yue").hide();
        $(_pageId + " .agreement_layer").hide();
        $(_pageId + " .showLimit").removeClass("m_text_red");
        $(_pageId + " #inputspanid span").removeClass('m_text_red');
        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
        $(_pageId + " .startInvestment_financial-calculator-btn").off('click');
    }

    function pageBack() {
        $(_pageId + " .grid_03").hide();
        appUtils.pageBack();
    }
    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
