// 邮箱设置
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_setEmial";
    var param = {};
    var ut = require("../common/userUtil");
    var userInfo;

    function init() {
        userInfo = ut.getUserInf();
        initData();
    }

    function initData() {
        $(_pageId + " #set_emial").val("");
        $(_pageId + " #userInfo").html("您好！您正在为账户 " + userInfo.mobile + " 设置电子邮箱。");
    }


    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击确定
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            var email = $(_pageId + " #set_emial").val();
            if (email == "") {
                layerUtils.iMsg(-1, "电子邮箱不能为空");
                return;
            }
            if (!validatorUtil.isEmail(email)) {
                layerUtils.iMsg(-1, "邮箱格式输入不正确");
                return;
            }
            updateEmial(email);
        });
    }

    function updateEmial(email) {
        service.reqFun101030({email: email}, function (data) {
            if (data.error_no == "0") {
                layerUtils.iMsg(-1, "邮箱设置成功");
                userInfo.email = email;
                ut.saveUserInf(userInfo);
                appUtils.pageBack();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var setEmial = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setEmial;
});
