// 买入卖出规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        cfdUtils = require("cfdUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        tools = require("../common/tools"),
        common = require("common"),
        vIscroll = {
            "scroll": null,
            "_init": false
        },
        _pageId = "#inclusive_BuyingSellingRule";
    var _pageCode = "inclusive/BuyingSellingRule";
    var ut = require("../common/userUtil");
    var productInfo;
    var qr;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        tools.initFundBtn(productInfo, _pageId);
        if(productInfo.qrParameter && productInfo.qrParameter != ""){
            var qr = productInfo.qrParameter;
        }else{
            qr = 1;
        }
        $(_pageId + " #qrDate").html(qr);
        $(_pageId + " #syDate").html((+qr)+1);

        if(productInfo.prod_sub_type2 == "200"){
            $(_pageId + " .seven").hide();
            $(_pageId + " .buy_rule").html(productInfo.open_redeem_rule);
        }else{
            $(_pageId + " .buy_rule").hide();
            $(_pageId + " .seven").show();
        }
        getRateInfo();
        //查询产品赎回提示 
        reqFun102047(productInfo.fund_code);
    }


    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.preBindEvent($(_pageId + " .highFinancialRate"),".rule_tip", function () {
            layerUtils.iAlert("客户维护费指基金管理人与基金销售机构通过基金销售协议约定，依据销售机构销售基金的保有量，从基金管理费中列支一定比例，用以向基金销售机构支付客户服务及销售活动中产生的相关费用。");
        });

        //点击买入规则
        appUtils.bindEvent($(_pageId + " #payRule"), function () {
            if(productInfo.prod_sub_type2 == "200"){
                $(_pageId + " .seven").hide();
                $(_pageId + " .buy_rule").html(productInfo.open_redeem_rule);
            }else{
                $(_pageId + " .buy_rule").hide();
                $(_pageId + " .seven").show();
            }

            $(_pageId + " #payRule").addClass("active");
            $(_pageId + " #enchashmentRule").removeClass("active");

            $(_pageId + " #payRuleBox").show();
            $(_pageId + " #enchashmentRuleBox").hide();

        });
        //点击卖出规则
        appUtils.bindEvent($(_pageId + " #enchashmentRule"), function () {
            if(productInfo.prod_sub_type2 == "200"){
                $(_pageId + " .seven").hide();
                $(_pageId + " .sold_rule").html(productInfo.redeem_rules);
            }else{
                $(_pageId + " .sold_rule").hide();
                $(_pageId + " .seven").show();
            }
            $(_pageId + " #payRule").removeClass("active");
            $(_pageId + " #enchashmentRule").addClass("active");

            $(_pageId + " #enchashmentRuleBox").show();
            $(_pageId + " #payRuleBox").hide();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
            // appUtils.pageInit(_pageCode, "customerService/onlineCustomerService");
        });

    }

    function getRateInfo() {
        // 产品详情查询--费率查询
        service.reqFun102003({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                var purchaseRateStr = "";
                var operateRateStr = "";
                var redeemRateStr = "";
                if (result.purchaseRate && result.purchaseRate.length > 0) {
                    purchaseRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.purchaseRate[0].chgrate_type_desc + '</h1><div><p>适用金额</p>' +
                        '<p>申购费率</p></div>';
                    for (var i = 0; i < result.purchaseRate.length; i++) {
                        var fcitem_tval = result.purchaseRate[i].fcitem_tval; //最大
                        var fcitem_lval = result.purchaseRate[i].fcitem_lval; //最小
                        var discount_ratevalue = result.purchaseRate[i].discount_ratevalue?tools.fmoney(result.purchaseRate[i].discount_ratevalue):''; //折扣率
                        var purchaseStr = "";
                        if (fcitem_lval == 0) { //最小为0
                            purchaseStr += fcitem_tval / 10000 + '万元以下';
                        } else if (fcitem_tval == "-1") { //最大
                            purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                        } else {
                            purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                        }
                        var rateStr = "";
                        if(discount_ratevalue) {
                            rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit + "</span>" + discount_ratevalue + result.purchaseRate[i].chgrate_unit;
                        } else {
                            rateStr = result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit;
                        }
                        purchaseRateStr += '<div>' +
                            '<p>' + purchaseStr + '</p>' +
                            '<p>' + rateStr + '</p>' +
                            '</div>';
                    }
                    purchaseRateStr += "</div>"
                }
                if (result.operateRate && result.operateRate.length > 0) {
                    operateRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.operateRate[0].chgrate_type_desc + '</h1><div>';
                    for (var i = 0; i < result.operateRate.length; i++) {
                        if(result.operateRate[i].chgrate_type == "11"){
                            if(result.operateRate[i].commission_per_p && result.operateRate[i].commission_per_p > 0){
                                var str = '<p style="height:0.6rem;line-height: 0.6rem;">' + result.operateRate[i].chgrate_item_desc + '</p>' +
                                    '<p style="height: auto;line-height: 0.3rem;"><em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                    '<em class="proportion" >·客户维护费占' + (+result.operateRate[i].commission_per_p).toFixed(2)/1 + '%<img src="../mall/images/rule_tip.png" class="rule_tip" operationType="1" operationId="rule_tip" operationName="规则"></em></p></div><div>';
                            }else{
                                var str = '<p style="">' + result.operateRate[i].chgrate_item_desc + '</p>' +
                                    '<p style=""><em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                    '</p></div><div>';
                            }

                            result.operateRate.splice(i,1);
                        }
                        var chgrate_tval = (+result.operateRate[i].chgrate_tval).toFixed(4);
                        operateRateStr += '<p>' + result.operateRate[i].chgrate_item_desc + '</p>' +
                            '<p>' + chgrate_tval + result.operateRate[i].chgrate_unit + '</p></div><div>';

                    }
                    operateRateStr += str + "</div>"

                }


                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.redeemRate[0].chgrate_type_desc + '</h1><div><p>适用期限</p>' +
                        '<p>赎回费率</p></div>';

                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var datestr = "";
                        var fcitem_lval = result.redeemRate[i].fcitem_lval; //最小
                        var fcitem_lvunit = result.redeemRate[i].fcitem_lvunit;//最小单位
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;//最大
                        var fcitem_tvunit = result.redeemRate[i].fcitem_tvunit;//最大单位
                        if (fcitem_tval == "-1") { //最大
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                        } else if (fcitem_lval == "0") { //最小
                            datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                        } else {
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                        }
                        redeemRateStr += '<div>' +
                            '<p>' + datestr + '</p>' +
                            '<p>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</p>' +
                            '</div>';
                    }
                    redeemRateStr += "</div>";
                }
                $(_pageId + " #payRuleBox .highFinancialRate").html(purchaseRateStr + operateRateStr);
                $(_pageId + " #enchashmentRuleBox .highFinancialRate").html(redeemRateStr);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    //查询产品赎回提示 
    function reqFun102047(fund_code) {
        var param = {
            fund_code: fund_code,
        }
        service.reqFun102047(param, function (data) {
            if (data.error_no == 0) {
                if (!data.results || data.results.length == 0) {
                    return;
                }
                var results = data.results[0];

                var redeem_desc = results.redeem_desc;
                var redeem_confirm = results.redeem_confirm;
                if (redeem_desc) {
                    $(_pageId + " #redeem_desc_parent").show();
                    $(_pageId + " #redeem_confirm").html(redeem_confirm);
                    $(_pageId + " #redeem_desc").html(redeem_desc);
                } else {
                    $(_pageId + " #redeem_desc_parent").hide();
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #payRule").addClass("active");
        $(_pageId + " #enchashmentRule").removeClass("active");
        $(_pageId + " #redeem_desc").html("--");
        $(_pageId + " #payRuleBox").show();
        $(_pageId + " #enchashmentRuleBox").hide();
        $(_pageId + " .highFinancialRate").html("");
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
