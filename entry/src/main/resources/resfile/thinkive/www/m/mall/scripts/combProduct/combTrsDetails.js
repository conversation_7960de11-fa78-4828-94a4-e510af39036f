// 交易记录详情页  - 购买
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#combProduct_combTrsDetails ";
    _pageCode = "combProduct/combTrsDetails"
    var tools = require("../common/tools");
    var ut = require("../common/userUtil");
    var monkeywords = require("../common/moneykeywords");
    var jymm, trans_serno, prod_sub_type2, nextPageData, detailsInfo,pay_method,expectdate,combTrsDetails;

    function init() {
        
        var trans_serno = appUtils.getSStorageInfo('trans_serno');
        nextPageData = appUtils.getSStorageInfo('nextPageData');
        let data = appUtils.getPageParam();
        pageType = data.pageType;
        pay_method = data.pay_method;
        if(pageType == 'buy'){
            //买入明细
            $(_pageId + " .detailsTitle").text('买入明细');
            //页面埋点初始化
            tools.initPagePointData({pageName:'买入明细'});
        }else{
            //卖出明细
            $(_pageId + " .detailsTitle").text('卖出明细');
            //页面埋点初始化
            tools.initPagePointData({pageName:'卖出明细'});
        }
        //申购交易详情查询
        detail(trans_serno)
    }

    function bindPageEvent() {
     
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

   

    function detail(trans_serno) {
        var callback = function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0].data;
            if (!results || results.length == 0) {
                if(pageType == 'buy'){
                    //买入明细
                    $(_pageId + " .pageName").text('买入金额');
                    if(nextPageData.trans_amt){
                        $(_pageId + " .all_money").text(nextPageData.trans_amt + '元')
                    }else{
                        $(_pageId + " .all_money").text('--')
                    }
                }else if(pageType == 'sell'){
                    //卖出明细
                    $(_pageId + " .pageName").text('卖出确认金额');
                    if(nextPageData.ack_amt && nextPageData.ack_amt != '--'){
                        $(_pageId + " .all_money").text(nextPageData.ack_amt + '元');
                        
                    }else{
                        $(_pageId + " .all_money").text('确认中')
                    }
                }
                let html = `<ul class="card_title flex">
                                <li class="m_width_35">基金名称</li>
                                <li class="m_width_25">${pageType == 'buy' ? '买入金额' : '确认金额'}(元)</li>
                                <li class="m_width_20">${pageType == 'sell' ? '预计到账时间' : '确认时间'}</li>
                                <li class="m_width_20">状态</li>
                            </ul>
                            <ul class="card_main flex">
                                <li class="m_width_35">--</li>
                                <li class="m_width_25">--</li>
                                <li class="m_width_20">--</li>
                                <li class="m_width_20">--</li>
                            </ul>
                        `
                $(_pageId + " .card").html(html);
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(data.results[0].data);
            var str = "";
            let sellMoney = 0;
                for (var i = 0; i < results.length; i++) {
                    if(pageType == 'sell'){
                        if(results[i].trans_status == '8'){
                            //已入账合集
                            let money = results[i].ack_amt*1
                            sellMoney = sellMoney + money;
                        }
                    }
                    str += tradeList(results[i]);
                }
                let html = `<ul class="card_title flex">
                                <li class="m_width_35">基金名称</li>
                                <li class="m_width_25">${pageType == 'buy' ? '买入金额' : '确认金额'}(元)</li>
                                <li class="m_width_20">${pageType == 'sell' ? '预计到账时间' : '确认时间'}</li>
                                <li class="m_width_20">状态</li>
                            </ul>`
                str = html + str
                if(pageType == 'buy'){
                    //买入明细
                    $(_pageId + " .pageName").text('买入金额');
                    if(nextPageData.trans_amt){
                        $(_pageId + " .all_money").text(nextPageData.trans_amt + '元')
                    }else{
                        $(_pageId + " .all_money").text('--')
                    }
                }else if(pageType == 'sell'){
                    //卖出明细
                    $(_pageId + " .pageName").text('卖出确认金额');
                    // console.log(nextPageData)
                    if(nextPageData.ack_amt && nextPageData.ack_amt != '--'){
                        $(_pageId + " .all_money").text(nextPageData.ack_amt + '元');
                        $(_pageId + " .remark").html(`
                            <ul>${nextPageData.pay_date}资金陆续到${pay_method}<ul>
                            <ul>当前已入账${tools.fmoney(sellMoney)}元</ul>
                        `)
                    }else{
                        $(_pageId + " .all_money").text('确认中')
                        $(_pageId + " .remark").html(`
                            <ul>${nextPageData.pay_date}资金陆续到${pay_method}<ul>
                            <ul>当前已入账--元</ul>
                        `)
                    }
                }
                // $(_pageId + " .finance_pro").html(str);
                
                $(_pageId + " .card").html(str);
        }
        service.reqFun102175({ trans_serno: trans_serno }, callback);
    }

 	 //交易记录列表
    function tradeList(data) {
        var amt = "";
        var ack = "";
        var fee = "";
        //交易状态
        var trans_status = data.trans_status;
        var trans_status_name ;
        if (data.sub_busi_code == "12218" || data.sub_busi_code == "12221" || data.sub_busi_code == "12222" ||data.sub_busi_code == "12219") { //买入
            trans_status_name = tools.fundDataDict(trans_status, "pri_trans_status_name");
        	amt =  tools.fmoney(data.app_amt);     	
        	if(trans_status == '8'){
                ack = '确认份额：'+ tools.fmoney(data.ack_vol) + "份";
                fee = '手续费：' +  tools.fmoney(data.fee_amt) + "元";
            }else{
            	ack = '确认份额：--份';
            	fee = '手续费：--元';
            }
            
        }else if (data.sub_busi_code == "12419" || data.sub_busi_code == "12423" || data.sub_busi_code == "12424") { // 赎回
            trans_status_name = tools.fundDataDict(trans_status, "pub_redeem_status_name");
            amt =  tools.fmoney(data.ack_amt);
            if(trans_status == '3' || trans_status == '8'){
            	ack = '确认金额：'+ tools.fmoney(data.ack_amt) + "元";
            	fee = '手续费：' +  tools.fmoney(data.fee_amt) + "元";
            }else{
            	ack = '确认金额：--份';
            	fee = '手续费：--元';
            }
            
        } 
            
        let textCocle = (trans_status == '3' || trans_status == '0') ? 'color_ccc' : ''
        
        var html =  `
            <ul class="card_main flex">
                <li class="m_width_35">${data.prod_name}</li>
                <li class="m_width_25">${trans_status == '0' ? '--' :amt}</li>
                <li class="m_width_20">${tools.ftime(data.expectdate)}</li>
                <li class="m_width_20 ${textCocle}">${trans_status_name}</li>
            </ul>
        `
        // '<div class="trade_box">' +          
        //     '<div class="fundInfo">' +
        //     '<p class="info" style="display: none">' + JSON.stringify(data) + '</p>' +
         
        //     '<p style="width:2rem;font-size:0.14rem;line-height: inherit;padding-top: 0;">' + data.prod_name + '('+data.fund_code+')</p>' +
        //     '<p style="color: #3f4e59;">' + ack + '&nbsp;&nbsp;&nbsp;' + '<span>'+fee + '</span></p>' +
        //     '</div>' +
        //     '<div style="height: 100%;float: right;">'+  
        //     '<p style="font-size: 0.12rem;float:right">' + trans_status_name + '</p>' +
        //     '<p>' + amt + '</p>' +   
           
        //     '</div>' +
           
        // '</div>'
        return html;
    }
    function pageBack() {
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .card").html("");
        $(_pageId + " .all_money").text('--');
        $(_pageId + " .remark").html('')
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
