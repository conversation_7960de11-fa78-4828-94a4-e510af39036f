/*
* jQuery Mobile Framework
* Copyright (c) jQuery Project
* Dual licensed under the MIT (MIT-LICENSE.txt) or GPL (GPL-LICENSE.txt) licenses.
*/
.in {
	-webkit-animation-timing-function: ease-out;
	-webkit-animation-duration: 750ms;
	animation-timing-function: ease-out;
	animation-duration: 750ms;
}
.pop {
	-webkit-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
}
.pop.in {
	-webkit-transform: scale(1);
	transform: scale(1);
	opacity: 1;
	-webkit-animation-name: popin;
	animation-name: popin;
	-webkit-animation-duration: 750ms;
	animation-duration: 750ms;
}
@-webkit-keyframes popin {
	from {
		-webkit-transform: scale(0.8);
		opacity: 0;
	}
	to {
		-webkit-transform: scale(1);
		opacity: 1;
	}
}
@keyframes popin {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}
.slidedown.in {
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfromtop;
	transform: translateY(0);
	animation-name: slideinfromtop;
	-webkit-animation-duration: 750ms;
	animation-duration: 750ms;
}
@-webkit-keyframes slideinfromtop {
	from {
		-webkit-transform: translateY(-100%);
	}
	to {
		-webkit-transform: translateY(0);
	}
}
@keyframes slideinfromtop {
	from {
		transform: translateY(-100%);
	}
	to {
		transform: translateY(0);
	}
}
.slideup.in {
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfrombottom;
	transform: translateY(0);
	animation-name: slideinfrombottom;
	-webkit-animation-duration: 750ms;
	animation-duration: 750ms;
}
@-webkit-keyframes slideinfrombottom {
	from {
		-webkit-transform: translateY(100%);
	}
	to {
		-webkit-transform: translateY(0);
	}
}
@keyframes slideinfrombottom {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}
.flip {
	-webkit-backface-visibility: hidden;
	-webkit-transform: translateX(0);
	backface-visibility: hidden;
	transform: translateX(0);
}
.flip.in {
	-webkit-animation-name: flipintoright;
	-webkit-animation-duration: 725ms;
	animation-name: flipintoright;
	animation-duration: 725ms;
}
@-webkit-keyframes flipintoright {
	from {
		-webkit-transform: rotateY(90deg) scale(0.9);
	}
	to {
		-webkit-transform: rotateY(0);
	}
}
@keyframes flipintoright {
	from {
		transform: rotateY(90deg) scale(0.9);
	}
	to {
		transform: rotateY(0);
	}
}
.turn {
	-webkit-backface-visibility: hidden;
	-webkit-transform: translateX(0);
	-webkit-transform-origin: 0;

	backface-visibility: hidden;
	transform: translateX(0);
	transform-origin: 0;
}
.turn.in {
	-webkit-animation-name: turnintoright;
	animation-name: turnintoright;
	-webkit-animation-duration: 750ms;
	animation-duration: 750ms;
}
@-webkit-keyframes turnintoright {
	from {
		-webkit-transform: rotateY(90deg);
	}
	to {
		-webkit-transform: rotateY(0);
	}
}
@keyframes turnintoright {
	from {
		transform: rotateY(90deg);
	}
	to {
		transform: rotateY(0);
	}
}
.flow {
	-webkit-transform-origin: 50% 30%;
	transform-origin: 50% 30%;
}
.flow.in {
	-webkit-transform: translateX(0) scale(1);
	-webkit-animation-name: flowinfromright;
	-webkit-animation-timing-function: ease;
	-webkit-animation-duration: 750ms;
	transform: translateX(0) scale(1);
	animation-name: flowinfromright;
	animation-timing-function: ease;
	animation-duration: 750ms;
}
@-webkit-keyframes flowinfromright {
	0% {
		-webkit-transform: translateX(100%) scale(0.7);
	}
	30%,
	40% {
		-webkit-transform: translateX(0) scale(0.7);
	}
	100% {
		-webkit-transform: translateX(0) scale(1);
	}
}
@keyframes flowinfromright {
	0% {
		transform: translateX(100%) scale(0.7);
	}
	30%,
	40% {
		transform: translateX(0) scale(0.7);
	}
	100% {
		transform: translateX(0) scale(1);
	}
}
@-webkit-keyframes slideInUp {
	0% {
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
		visibility: visible;
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
@keyframes slideInUp {
	0% {
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
		visibility: visible;
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
.slideInUp {
	-webkit-animation: slideInUp 1s;
	animation: slideInUp 1s;
}
@-webkit-keyframes zoomIn {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(0.3, 0.3, 0.3);
		transform: scale3d(0.3, 0.3, 0.3);
	}
	50% {
		opacity: 1;
	}
}
@keyframes zoomIn {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(0.3, 0.3, 0.3);
		transform: scale3d(0.3, 0.3, 0.3);
	}
	50% {
		opacity: 1;
	}
}
.zoomIn {
	-webkit-animation: zoomIn 1s;
	animation: zoomIn 1s;
}
@-webkit-keyframes slideInUp {
	0% {
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
		visibility: visible;
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
@keyframes slideInUp {
	0% {
		-webkit-transform: translate3d(0, 100%, 0);
		transform: translate3d(0, 100%, 0);
		visibility: visible;
	}
	100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
}
.slideInUp {
	-webkit-animation: slideInUp 1s;
	animation: slideInUp 1s;
}

/*����ֵ��ʾ��*/
.recharge .newtips_topup {
	font-size: 0.14rem;
	color: #666666;
	padding: 0 0.1rem;
	margin-top: 0.1rem;
}
.recharge .newtips_topup span {
	font-size: 0.14rem;
	color: #e5443c;
}
.recharge .newtips_topup a {
	display: inline-block;
	padding: 0rem 0.02rem;
	border: 1px solid #99a3a5;
	background: #deebf5;
	color: #319ef2;
	border-radius: 0.03rem;
	margin-left: 0.04rem;
	font-weight: 700;
}
/*����ֵ�˻���Ϣ*/
.reward_rule .rule_inner h3.account_information_i {
	color: #666666;
	font-size: 0.16rem;
	margin-top: 0.2rem;
	line-height: 0.24rem;
}
.reward_rule .rule_inner img.account_information_img {
	width: 100%;
	margin-top: 0.14rem;
	margin-bottom: 0.2rem;
}
.account_infor_prompt {
	line-height: 0.24rem;
	font-size: 0.16rem;
	color: #666666;
	margin-top: 0.15rem;
}
.account_infor_prompt p {
	padding-top: 0.14rem;
}
.account_infor_prompt p span {
	color: #e5443c;
}
.pop_tip a.limit_bank {
	font-size: 0.14rem;
	color: #1199ee;
	display: block;
	text-align: center;
	margin-top: 0.08rem;
}

/*****红包雨*******/
/* CSS Document */
/*点赞*/
@-webkit-keyframes niceIn {
	0% {
		opacity: 1;
		-webkit-transform: scale(1);
		transform: scale(1);
	}
	50% {
		opacity: 1;
		-webkit-transform: scale(1.5);
		transform: scale(1.5);
	}
	70% {
		-webkit-transform: scale(0.8);
		transform: scale(0.8);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}
@keyframes niceIn {
	0% {
		opacity: 1;
		-webkit-transform: scale(1);
		-ms-transform: scale(1);
		transform: scale(1);
	}
	50% {
		opacity: 1;
		-webkit-transform: scale(1.5);
		-ms-transform: scale(1.5);
		transform: scale(1.5);
	}
	70% {
		-webkit-transform: scale(0.8);
		-ms-transform: scale(0.8);
		transform: scale(0.8);
	}
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		-ms-transform: scale(1);
		transform: scale(1);
	}
}

@-o-keyframes niceIn {
	0% {
		opacity: 1;
		-o-transform: scale(1);
		transform: scale(1);
	}
	50% {
		opacity: 1;
		-o-transform: scale(1.5);
		transform: scale(1.5);
	}
	70% {
		-o-transform: scale(0.8);
		transform: scale(0.8);
	}
	100% {
		opacity: 1;
		-o-transform: scale(1);
		transform: scale(1);
	}
}

@-moz-keyframes niceIn {
	0% {
		opacity: 1;
		-moz-transform: scale(1);
		transform: scale(1);
	}
	50% {
		opacity: 1;
		-moz-transform: scale(1.5);
		transform: scale(1.5);
	}
	70% {
		-o-transform: scale(0.8);
		transform: scale(0.8);
	}
	100% {
		opacity: 1;
		-moz-transform: scale(1);
		transform: scale(1);
	}
}
@keyframes qq {
	0% {
		opacity: 1;
		transform: rotate(7deg);
	}
	50% {
		opacity: 0.9;
		transform: rotate(90deg);
	}
	70% {
		opacity: 0.2;
		transform: rotate(180deg);
	}
	100% {
		opacity: 0;
		transform: rotate(270deg);
	}
}
.newYearAnimation {
	position: relative;
	animation: newYearMove 2s infinite;
	-webkit-animation: newYearMove 2s infinite; /*Safari and Chrome*/
	animation-direction: alternate; /*轮流反向播放动画。*/
	animation-timing-function: ease-in-out; /*动画的速度曲线*/
	/* Safari 和 Chrome */
	-webkit-animation: newYearMove 2s infinite;
	-webkit-animation-direction: alternate; /*轮流反向播放动画。*/
	-webkit-animation-timing-function: ease-in-out; /*动画的速度曲线*/
}
.newYearAnimation-logo {
	position: relative;
	animation: newYearMove 1s infinite;
	-webkit-animation: newYearMove 1s infinite; /*Safari and Chrome*/
	animation-direction: alternate; /*轮流反向播放动画。*/
	animation-timing-function: ease-in-out; /*动画的速度曲线*/
	/* Safari 和 Chrome */
	-webkit-animation: newYearMove 1s infinite;
	-webkit-animation-direction: alternate; /*轮流反向播放动画。*/
	-webkit-animation-timing-function: ease-in-out; /*动画的速度曲线*/
}
.new-year-main .activity-main .activity-lantern-top li .bg {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	position: absolute;
	top: 0.2rem;
	left: 0.1rem;
	animation: pulse 4s infinite;
}
@keyframes newYearMove {
	0% {
		transform: scale(1); /*开始为原始大小*/
	}

	50% {
		transform: scale(1.2);
	}
	100% {
		transform: scale(1);
	}
}

@-webkit-keyframes newYearMove /*Safari and Chrome*/ {
	0% {
		transform: scale(1); /*开始为原始大小*/
	}

	50% {
		transform: scale(1.2);
	}
	100% {
		transform: scale(1);
	}
}
@keyframes pulse {
	0% {
		transform: scale(1); /*开始为原始大小*/
	}
	25% {
		box-shadow: 0 0 20px 10px #f2dba7;
	}

	50% {
		box-shadow: 0 0 25px 10px #F7C15D;
		transform: scale(1.2);
	}
	75% {
		box-shadow: 0 0 20px 10px #f5d189;
	}
	100% {
		box-shadow: 0 0 20px 10px #fcebd7;
		transform: scale(1);
	}
}
