//场景产品定投
define(function (require, exports, module) {
    require('../common/vue.min');
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        service = require("mobileService"),
        _pageId = "#scene_sceneHold ";
        _pageCode = "scene/sceneHold";
        gconfig = require("gconfig"),
        global = gconfig.global;
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    let sceneHold //new 一个 vue 实例
    let plan_type;
    let sceneInfo;
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    async function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //缓存当前是否为系列投顾从产品
        appUtils.setSStorageInfo("isSeriesComb", '1');
        sceneInfo = appUtils.getSStorageInfo("sceneInfo");//获取场景产品信息
        plan_type = sceneInfo.plan_type;//获取场景产品类型
        let hold_html = await getTemplate({templateId:sceneInfo.hold_id}) //拿到模板数据
        $(".scene_sceneHold").html(hold_html)   //渲染模板
        $(_pageId + " #newHold").attr("style", `background:${sceneInfo.bgColor}`);//设置背景色
        $(_pageId + " .pageTitle").text(sceneInfo.pageTitle);//设置标题
        // if($(_pageId + " .holdTitle")) $(_pageId + " .holdTitle").attr("style", `background:${$(_pageId + " .holdList").attr("bgColor")}`);
        appUtils.setSStorageInfo("financial_prod_type", '07');
        sceneHold = new Vue({
            el: '#newHold',
            data() {
                return{
                    oss_url: global.oss_url,
                    holdList:[],
                    plan_type:''
                }
            },
            //视图 渲染前
            created(){
                //获取持仓数据
                this.getHoldProdect();
                //保存产品类型
                this.plan_type = plan_type;
            },
            //渲染完成后
            mounted() {
                
            },
            //计算属性
            computed:{

            },
            //绑定事件
            methods:{
                //跳转持仓详情
                holdPage(item){
                    tools.recordEventData('1','holdPage','持仓详情');
                    let productInfo = {
                        fund_code:item.comb_code
                    }
                    //缓存当前是持仓还是开启计划
                    appUtils.setSStorageInfo("productInfo",productInfo);
                    appUtils.pageInit(_pageCode, "combProduct/combHoldHeightDetail",{});
                },
                //获取持仓列表
                getHoldProdect(){
                    let data = {
                        plan_type: plan_type
                    }
                    service.reqFun102212(data, async (data) => {
                        if (data.error_no == '0') {
                            let list = data.results;
                            this.holdList = list;
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //去定投
                fixed_page(item){
                    tools.recordEventData('1','fixed_page','定投');
                    let productInfo = {
                        comb_code:item.comb_code
                    }
                    appUtils.setSStorageInfo("isAdvisoryInvestment",'1');
                    appUtils.setSStorageInfo("comb_code",item.comb_code);
                    appUtils.setSStorageInfo("productInfo",productInfo);
                    appUtils.setSStorageInfo("plan_type",plan_type);
                    appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment");
                },
                //买入
                buy_page(item){
                    tools.recordEventData('1','buy_page','买入');
                    appUtils.setSStorageInfo("comb_code",item.comb_code);
                    appUtils.setSStorageInfo("plan_type",plan_type);
                    appUtils.pageInit(_pageCode, "combProduct/combProdBuy");
                },
                buy(){
                    tools.recordEventData('1','startPlan','开启计划');
                    // appUtils.pageInit(_pageCode, "scene/snowballPlan",{});
                    appUtils.setSStorageInfo("userChooseAnswer",'');
                    appUtils.pageInit(_pageCode, "scene/selectProduct",{});
                }
            }
        })
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function pageBack() {
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .pageTitle").text('');
    }
    var scene_sceneHold = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    
    // 暴露对外的接口
    module.exports = scene_sceneHold;
});
