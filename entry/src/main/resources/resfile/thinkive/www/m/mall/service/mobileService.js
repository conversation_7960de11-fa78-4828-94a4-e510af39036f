/**
 * 手机前端service层调用接口
 **/
define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var service = require("service");
    var validata = require("validatorUtil");
    var ssoUtils = require("ssoUtils");
    var external = require("external");
    var serviceSingleton = new service.Service();
    var platform = require("gconfig").platform;
    var layerUtils = require("layerUtils");
    var errorfilter = require("errorfilter");
    var serviceConstants = require("constants");
    var ut = require("../scripts/common/userUtil");
    var endecryptUtils = require("endecryptUtils");
    var devversion = platform;
    /*
             统一添加 devversion系统ios android
             deviceSysVersion系统版本号
             deviceToken唯一标识
             用于用户分析，日志分析
             * */
    var devresult ;
    var deviceSysVersion ;
    var deviceTokenresult ;
    var deviceToken ;

    /* 获取用户手机号码 */
    function getPhone() {
        var phone = "";
        if (global.phone) {
            phone = global.phone;
        } else {
            // 获取壳子里的手机号
            var key = {
                key: "phone"
            };
            //			phone = require("external").callFunction("55601",JSON.stringify(key));
            global.phone = phone;
        }
        return phone;
    }

    /********************************公共代码部分********************************/
    function parseQueryString(url) {
        var items = url.split("&");
        var result = {};
        for (var i = 0, len = items.length; i < len; i++) {
            var obj = items[i];
            var startIndex = obj.indexOf('=');
            var key = obj.substring(0, startIndex);
            var value = obj.substring(startIndex + 1);
            result[key] = value;
        }

        return result;
    }

    //用于存储所有请求对象
    var flowNoMap = {};
    //暴露方法供原生调用H5方法(传回服务器返回数据结果集)
    var httpsCallback = function (flowNo, resultVo) {
        layerUtils.iLoading(false);
        //如果不是采用50118调用，则注释这段话
        if (resultVo.error_no == "-999" || resultVo.error_no == "-99903" || resultVo.error_no == "-88801") {
            delete flowNoMap[flowNo];
            errorfilter.filterLoginOut(resultVo);
        } else if (resultVo.error_no == "-99900") {
            delete flowNoMap[flowNo];
            errorfilter.filterCardOut(resultVo);
        } else if (resultVo.error_no == "-99901") {
            delete flowNoMap[flowNo];
            errorfilter.filterActiveOut(resultVo);
        } else if (resultVo.error_no == "-566") {
            delete flowNoMap[flowNo];
            errorfilter.filterUpdate(resultVo);
        } else if (resultVo.error_no == "7001") {
            delete flowNoMap[flowNo];
            errorfilter.transPassError(resultVo);
        }else {
            if (resultVo.error_info == null || resultVo.error_info == "") {
                resultVo.error_info = "网络异常，请稍后重试";
            }
            //针对IOS处理
            if (resultVo.error_info.indexOf("https") >= 0 || resultVo.error_info.indexOf("java") >= 0) {
                resultVo.error_info = "网络异常，请稍后重试";
            }
            //针对ANDROID处理
            if (resultVo.error_info.indexOf("服务器") >= 0) {
                resultVo.error_info = "网络异常，请稍后重试";
            }
            if (resultVo.error_no != "0") {
                flowNoMap[flowNo](resultVo);
                delete flowNoMap[flowNo];
                return;
            }
            var results = resultVo.results;
            if (results && results.length > 0 && results[0].encryptUserInfo) { //存在 encryptUserInfo 字段
                if (typeof results[0].encryptUserInfo == "string") { // encryptUserInfo 内容加密，做解密处理
                    var encryptUserInfo = JSON.stringify(results[0].encryptUserInfo).replace(/\\n/g, '').replace(/\"/g, "");
                    results[0] = JSON.parse(endecryptUtils.aesDecrypt("thinkive_mallaes", encryptUserInfo));
                }
            }
            flowNoMap[flowNo](resultVo);
            delete flowNoMap[flowNo];
        }
    };
    window.httpsCallback = httpsCallback;

    function commonInvoke(paraMap, callback, ctrlParam, reqParamVo) {
        paraMap.version_code = require("gconfig").global.version_code;
        //    	if(paraMap.version_code=="1"||paraMap.version_code==""||paraMap.version_code==null){
        //    		var oVersion = require("external").callMessage({funcNo: "50010"});
        // 		   	oVersion = oVersion.results ? oVersion.results[0] : {versionSn: global.version_code};
        // 		    var version = oVersion.versionSn;
        // 		    paraMap.version_code=version;
        //    	}
        paraMap.platform = platform;
        reqParamVo.setReqParam(paraMap);
        ctrlParam = ctrlParam ? ctrlParam : {};
        ctrlParam["errorFunc"] = errorFunc;
        reqParamVo.setIsLastReq(ctrlParam.isLastReq);
        reqParamVo.setIsAsync(ctrlParam.isAsync);
        reqParamVo.setIsShowWait(ctrlParam.isShowWait);
        reqParamVo.setTimeOutFunc(ctrlParam.timeOutFunc);
        reqParamVo.setIsShowOverLay(ctrlParam.isShowOverLay);
        reqParamVo.setTipsWords(ctrlParam.tipsWords);
        reqParamVo.setDataType(ctrlParam.dataType);
        reqParamVo.setIsGlobal(ctrlParam.isGlobal);
        reqParamVo.setProtocol(ctrlParam.protocol);
        reqParamVo.setErrorFunc(ctrlParam.errorFunc);
        if (global.isSign == 1) {
            var param = reqParamVo.getReqParam();
            /*
             统一添加 devversion系统ios android
             deviceSysVersion系统版本号
             deviceToken唯一标识
             * */
            //处理devicecode符合隐私权
            if(!devresult){
            	devresult = external.callMessage({
                    funcNo: "50001"
                });
            	deviceSysVersion = devresult.results ? devresult.results[0].deviceSysVersion : "";
            }
            
            if(!deviceTokenresult){
            	deviceTokenresult = external.callMessage({
                    funcNo: "50022"
                });
            	deviceToken = deviceTokenresult.results ? deviceTokenresult.results[0].deviceToken : "";
            }
            
            
            param.devversion = platform;
            param.deviceSysVersion = deviceSysVersion;
            param.deviceToken = deviceToken;
            if (global.isFordHttpReq == 1 && platform != 0) {
                var flowNo = (Math.random() + "").substring(2, 10);
                flowNoMap[flowNo] = callback;
                if (reqParamVo.obj.isShowWait) {
                    layerUtils.iLoading(true, reqParamVo.obj.tipsWords, true);
                } else {
                    layerUtils.iLoading(false);
                }
                function50118(reqParamVo.obj.url, reqParamVo.obj.reqParam, flowNo, reqParamVo.reqType, 30);
            } else {
                var bizcode = param['funcNo'];
                delete param['funcNo'];
                var signStr = ssoUtils.ssoSignFunc(bizcode, param);
                var param = parseQueryString(signStr);
                reqParamVo.setReqParam(param);
                serviceSingleton.invoke(reqParamVo, callback);
            }
        } else {
            serviceSingleton.invoke(reqParamVo, callback);
        }
    }

    function function50118(url, paramMap, flowNo, isPost, timeOut) {
        var result = null;
        var param = {};
        param['funcNo'] = "50118";
        param['url'] = url + "?";
        param['moduleName'] = "mall";
        param['protocol'] = "0";
        param['paramMap'] = paramMap;
        param['isAutoAddSysComParam'] = '0';
        param['flowNo'] = flowNo;
        param['isPost'] = isPost ? isPost : 1;
        param['timeOut'] = timeOut ? timeOut : 30;
        param['mode'] = "3";
        result = external.callMessage(param);
        //		return result;
    }

    function errorFunc() {
        layerUtils.iMsg(-1, "网络繁忙，请稍后重试");
    }

    function destroy() {
        serviceSingleton.destroy();
    }

    var mobileService = {
        "getInstance": getInstance, //为了避免以前调用getInstance方法报错
        "destroy": destroy
    };

    function getInstance() {
        return mobileService;
    }

    module.exports = mobileService;

    /***************************** 数据字典start ********************************/

    /**
     * desensitize_fields:"reg_mobile:1|yl_mobile:2|id_card:3|bank_no:4|acct_no:5" 手机号、银行卡号、交易账号脱米处理
     * 入参:prod_type(1-公募基金 2-私募基金)
     *
     * */

    /***************************** 数据字典end ********************************/

    /********************************本地接口********************************/
    /**
     * 功能:加密(1000000)
     */
    mobileService.getRSAKey = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1000000";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：校验用户信息
     * */
    mobileService.reqFun1100001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100001";
        paraMap["value"] = param.value;
        paraMap["type"] = param.type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：校验银行卡
     * */
    mobileService.reqFun11000010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100001";
        paraMap["value"] = param.value;
        paraMap["type"] = param.type;
        paraMap["desensitize_fields"] = "value:4"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：校验预留手机号
     * */
    mobileService.reqFun11000011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100001";
        paraMap["value"] = param.value;
        paraMap["type"] = param.type;
        paraMap["desensitize_fields"] = "value:2"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取版本信息
     * */
    mobileService.reqFun1100002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100002";
        paraMap["versionsn"] = param.versionsn;
        paraMap["channel"] = param.channel;
        paraMap["soft_no"] = param.soft_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：校验短信验证码
     * */
    mobileService.reqFun1100003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100003";
        paraMap["sms_mobile"] = param.sms_mobile;
        paraMap["sms_code"] = param.sms_code;
        if (param.mobile_type) {
            paraMap['desensitize_fields'] = "sms_mobile:" + param.mobile_type; // mobile_type 1注册 2 预留，如果是完整手机号，不需要传值
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：退出登录
     * */
    mobileService.reqFun1100004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100004";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：图形验证码
     * */
    mobileService.reqFun1100005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100005";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：图片文件上传
     * */
    mobileService.reqFun1100006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100006";
        paraMap["img_data"] = param.img_data;
        paraMap["img_type"] = param.img_type; // 4身份证反面照   5 身份证正面照  6旧银行卡照  7 银行卡正面照  8 新银行卡照  10 新银行卡正面照  11 新银行卡反面照  12 手持身份证银行卡照
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查redis用户信息
     * */
    mobileService.reqFun1100007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100007";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：下载app前根据url渠道获取标识码
     * */
    mobileService.reqFun1100009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100009";
        paraMap["ename"] = param.ename;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：首次打开app根据剪切板标识码获取渠道代码
     * */
    mobileService.reqFun1100010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "1100010";
        paraMap["uuid"] = param.uuid;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     * 功能：发送短信验证码（无图形验证码校验）
     * */
    mobileService.reqFun199001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199001";
        paraMap["mobile_phone"] = param.mobile_phone; // 手机号
        paraMap["sms_id"] = param.type; // 短信模板
        paraMap["send_type"] = param.send_type; // 0 短信  1 语音
        if (param.mobile_type && param.mobile_phone.indexOf("*") > -1) {
            paraMap['desensitize_fields'] = "mobile_phone:" + param.mobile_type; // mobile_type 1注册 2 预留，如果是完整手机号，不需要传值
        }
        if (param.bank_abbr) {
            paraMap["bank_abbr"] = param.bank_abbr; // 银行产品名称
        }
        if (param.bank_electron_name) {
            paraMap["bank_electron_name"] = param.bank_electron_name; // 银行电子
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：发送短信验证码（有图形验证码校验）
     * */
    mobileService.reqFun9199001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9199001";
        paraMap["mobile_phone"] = param.mobile_phone; // 手机号
        paraMap["sms_id"] = param.type; // 短信模板
        paraMap["send_type"] = param.send_type; // 0 短信  1 语音
        if (param.mobile_type && param.mobile_phone.indexOf("*") > -1) {
            paraMap['desensitize_fields'] = "mobile_phone:" + param.mobile_type; // mobile_type 1注册 2 预留，如果是完整手机号，不需要传值
        }
        paraMap['ticket'] = param.ticket; // 图形验证码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：客户留言
     * */
    mobileService.reqFun199002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199002";
        paraMap["mobile"] = param.mobile; // 注册手机号
        paraMap["content"] = param.content; // 反馈内容
        paraMap["desensitize_fields"] = "mobile:1"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：晋金所授权发短信
     * */
    mobileService.reqFun199003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199003";
        paraMap["mobile_phone"] = param.mobile_phone; // 手机号
        paraMap["sms_id"] = param.type; // 短信模板
        paraMap["send_type"] = param.send_type; // 0 短信  1 语音
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：头像上传OSS
     * */
    mobileService.reqFun199010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199010";
        paraMap["base_data"] = param.base_data; // base64图片编码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：身份证上传oss
     * */
    mobileService.reqFun199011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199011";
        paraMap["base_data"] = param.base_data; // base64图片编码
        paraMap["flag"] = param.flag; // 0正面 1反面
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：换卡上传oss
     * */
    mobileService.reqFun199012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199012";
        paraMap["base_data"] = param.base_data; // base64图片编码
        paraMap["type"] = param.type; // 0正面 1反面
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：身份证上传
     * */
    mobileService.reqFun199013 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199013";
        paraMap["front"] = param.front; // 正面照
        paraMap["back"] = param.back; // 背面照
        paraMap["registered_mobile"] = param.registered_mobile; // 注册手机号
        paraMap["cert_no"] = param.cert_no; // 证件号
        paraMap["vaild_date"] = param.vaild_date.replace(/\//g, ""); // 有效期
        paraMap["cust_name"] = param.cust_name; // 姓名
        paraMap["sex"] = param.sex; // 性别
        paraMap["address"] = param.address; // 地址
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; // 预留手机号
        paraMap["desensitize_fields"] = "registered_mobile:1|bank_reserved_mobile:2"; //拖米字段
        paraMap["cert_type"] = "0"; //证件类型
        paraMap['living_address_province'] = param.living_address_province;//省
        paraMap['living_address_city'] = param.living_address_city;//市
        paraMap['living_address_county'] = param.living_address_county;//区
        paraMap['year_icome'] = param.year_icome;//年收入
        paraMap['occupation'] = param.occupation;//职业
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   完善信息职业查询
     */
    mobileService.reqFun199014 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "199014";
        paraMap['code'] = param.code;//父级字典编码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   银行限额查询
     */
    mobileService.reqFun199015 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "199015";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：卡bin查询
     * */
    mobileService.BankByCard = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101060";
        paraMap["bank_acct"] = param.bin_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：生成财富token
     * */
    mobileService.reqFun101051 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101051";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /************************************ 功能接口 **************************************/


    /************************************* 账户类 **************************************/
    /**
     * 功能：注册
     */
    mobileService.reqFun101001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101001";
        paraMap["registered_mobile"] = param.registered_mobile; //注册手机号
        paraMap["login_pwd"] = param.login_pwd; // 登录密码
        paraMap["recommend"] = param.recommend; //邀请人
        paraMap["labelId"] = param.labelId; //渠道标签ID
        paraMap["cust_cnl"] = param.custCnl; //渠道ID
        paraMap["sms_mobile"] = param.sms_mobile; //手机号
        paraMap["sms_code"] = param.sms_code; //短信验证码
        paraMap["source"] = param.source; //短信验证码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：开户、绑卡
     */
    mobileService.reqFun101002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101002";
        paraMap["cust_address"] = param.cust_address; //客户地址
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["cert_type"] = param.cert_type; //证件类型
        paraMap["message_code"] = param.message_code; //验证码
        paraMap["sms_code"] = param.sms_code; //验证码
        paraMap["pay_type"] = param.pay_type; //健全方式
        paraMap["vaild_date"] = param.vaild_date.replace(/\//g, ""); //证件有效期
        paraMap["sex"] = param.sex; //性别
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["bank_name"] = param.bank_name; //银行名称
        paraMap["payorg_id"] = param.payorg_id; //支付机构ID
        paraMap["pay_type"] = param.pay_type; //支付方式
        paraMap["message_code"] = param.message_code; //验证码
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["vocation_code"] = param.vocation_code;//职业
        paraMap["income"] = param.income;//年收入
        paraMap["living_address_province"] = param.living_address_province;//居住地址-省
        paraMap["living_address_city"] = param.living_address_city;//居住地址-市
        paraMap["living_address_county"] = param.living_address_county;//居住地址-县
        paraMap["recommend"] = param.recommend;//邀请人
        paraMap["cerditrecord"] = param.bad_credit;//不良诚信记录
        paraMap["actualisself"] = param.actualisself;//控制人
        paraMap["actualctrlname"] = param.actualctrlname;//控制人姓名
        paraMap["actualctrlidcode"] = param.actualctrlidcode;//控制人证件号
        paraMap["benefisself"] = param.beneficiary;//受益人
        paraMap["beneficiary"] = param.beneficiaryname;//受益人姓名
        paraMap["beneficiaryno"] = param.beneficiaryno;//受益人证件号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：登录密码修改
     */
    mobileService.reqFun101004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101004";
        paraMap["old_login_pwd"] = param.old_login_pwd; //原登录密码
        paraMap["new_login_pwd"] = param.new_login_pwd; // 新登录密码
        paraMap["pwd_fields"] = "new_login_pwd|old_login_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：交易密码设置
     */
    mobileService.reqFun101005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101005";
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：交易密码修改
     */
    mobileService.reqFun101006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101006";
        paraMap["old_trans_pwd"] = param.old_trans_pwd; //原登录密码
        paraMap["new_trans_pwd"] = param.new_trans_pwd; // 新登录密码
        paraMap["pwd_fields"] = "old_trans_pwd|new_trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：交易密码重置
     */
    mobileService.reqFun101007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101007";
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["sms_mobile"] = param.sms_mobile; //预留手机号
        paraMap["sms_code"] = param.sms_code; //预留手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：客户风险测评
     */
    mobileService.reqFun101008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101008";
        paraMap["cust_type"] = param.cust_type; //客户类型
        paraMap["cust_risk_result"] = param.cust_risk_result; //答案
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：注册手机号变更
     */
    mobileService.reqFun101010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101010";
        paraMap["cert_type"] = param.cert_type; //证件类型
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["registered_mobile"] = param.registered_mobile; //手机号码
        paraMap["old_registered_mobile"] = param.old_registered_mobile; //原手机号码
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["sms_code"] = param.sms_code; //
        paraMap["sms_mobile"] = param.sms_mobile; //
        paraMap["desensitize_fields"] = "cert_no:3"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：预留手机号变更
     */
    mobileService.reqFun101011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101011";
        paraMap["cert_type"] = param.cert_type; //证件类型
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号码
        paraMap["org_bank_reserved_mobile"] = param.org_bank_reserved_mobile; //原预留手机号码
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["sms_mobile"] = param.sms_mobile;
        paraMap["sms_code"] = param.sms_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：快捷换卡--银联校验验证码
     */
    mobileService.reqFun101012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101012";
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["org_bank_code"] = param.org_bank_code; //原银行编码102016
        paraMap["org_bank_reserved_mobile"] = param.org_bank_reserved_mobile; //原预留手机号
        paraMap["org_bank_acct"] = param.org_bank_acct; //原银行卡号
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["message_code"] = param.message_code;
        paraMap["pay_type"] = param.pay_type; //鉴权方式
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["payorg_id"] = param.payorg_id;
        paraMap["desensitize_fields"] = "org_bank_reserved_mobile:2|org_bank_acct:4"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：别名设置
     */
    mobileService.reqFun101013 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101013";
        paraMap["by_name"] = param.by_name;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：账户冻结
     */
    mobileService.reqFun101014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101014";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：账户解冻
     */
    mobileService.reqFun101015 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101015";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：普通换卡申请
     */
    mobileService.reqFun101016 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101016";
        paraMap["org_bank_reserved_mobile"] = param.org_bank_reserved_mobile; //原预留手机号
        paraMap["org_bank_acct"] = param.org_bank_acct; //原银行卡号
        paraMap["desensitize_fields"] = "org_bank_reserved_mobile:2|org_bank_acct:4"; //拖米字段
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["org_bank_code"] = param.org_bank_code; //原银行编码
        paraMap["attached_url"] = param.attached_url; //附件
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["message_code"] = param.message_code;
        paraMap["payorg_id"] = param.payorg_id;
        paraMap["pay_type"] = param.pay_type;
        paraMap["bankserialno"] = param.bank_serial_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：普通换卡确认
     */
    mobileService.reqFun101017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101017";
        paraMap["org_bank_acct"] = param.org_bank_acct; //原银行卡号
        paraMap["org_bank_reserved_mobile"] = param.org_bank_reserved_mobile; //原预留手机号
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["desensitize_fields"] = "cert_no:3"; //拖米字段
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["org_bank_code"] = param.org_bank_code; //原银行编码
        paraMap["sms_mobile"] = param.sms_mobile; //预留手机号
        paraMap["sms_code"] = param.sms_code; //预留手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：登录
     */
    mobileService.reqFun101018 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101018";
        paraMap["login_acc"] = param.login_acc; //登录账号
        paraMap["login_pwd"] = param.login_pwd; //密码
        paraMap["type"] = param.type; // 0手机号 1身份证 2别名
        paraMap["pwd_fields"] = "login_pwd"; //加密字段
        paraMap["device_token"] = param.device_token;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：重置登录密码（忘记密码）
     */
    mobileService.reqFun101019 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101019";
        paraMap['new_login_pwd'] = param.new_login_pwd; //新密码
        paraMap["pwd_fields"] = "new_login_pwd"; //加密字段
        paraMap["registered_mobile"] = param.registered_mobile; //注册手机号
        paraMap['sms_code'] = param.sms_code; //手机验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询用户信息（查数据库）
     * 场景：绑卡成功之后调用
     */
    mobileService.reqFun101020 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101020";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：风险等级试题
     */
    mobileService.reqFun101022 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101022";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：用户头像上传
     */
    mobileService.reqFun101023 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101023";
        paraMap['photo_url'] = param.photo_url; //头像地址
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：换卡状态查询
     */
    mobileService.reqFun101024 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101024";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：交易密码校验
     */
    mobileService.reqFun101025 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101025";
        paraMap['trans_pwd'] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：换卡确认信息查询
     */
    mobileService.reqFun101026 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101026";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：快捷换卡校验
     */
    mobileService.reqFun101027 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101027";
        paraMap["bank_acct"] = param.bank_acct;
        paraMap["desensitize_fields"] = "bank_acct:4";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询未读消息队列
     */
    mobileService.reqFun101028 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101028";
        paraMap["type"] = param.type;//银行消息 0
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：阅读未读消息队列
     */
    mobileService.reqFun101029 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101029";
        paraMap["id"] = param.id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：邮箱设置、修改
     */
    mobileService.reqFun101030 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101030";
        paraMap["email"] = param.email;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：短信鉴权发送
     */
    mobileService.reqFun101031 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101031";
        paraMap["payorg_id"] = param.payorg_id;//支付机构
        paraMap["pay_type"] = param.pay_type;//支付方式
        paraMap["sms_type"] = param.sms_type; //短信模板类型
        paraMap["bank_code"] = param.bank_code;//银行编码
        paraMap["bank_name"] = param.bank_name;//银行名称
        paraMap["cust_name"] = param.cust_name;//客户名称
        paraMap["bank_acct"] = param.bank_acct;//银行卡号
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile;//预留手机号
        paraMap["cert_no"] = param.cert_no;//证件号
        paraMap["trans_amt"] = param.trans_amt;//充值金额
        var desensitize_fieldsStr = "";
        if (param.cert_no.indexOf("*") > -1) {
            desensitize_fieldsStr += "cert_no:3|"; //拖米字段
        }
        if (param.bank_acct.indexOf("*") > -1) {
            desensitize_fieldsStr += "bank_acct:4|"; //拖米字段
        }
        if (param.bank_reserved_mobile.indexOf("*") > -1) {
            desensitize_fieldsStr += "bank_reserved_mobile:2|"; //拖米字段
        }
        if (desensitize_fieldsStr) {
            paraMap['desensitize_fields'] = desensitize_fieldsStr.substr(0, desensitize_fieldsStr.length - 1);
        }
        paraMap["cert_type"] = param.cert_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /********************************** 查询类 ************************************************/
    /**
     * 功能：现金宝资产查询101901
     */
    mobileService.reqFun101901 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101901";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：在途资金查询接口
     */
    mobileService.reqFun101910 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101910";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：晋金专区产品列表查询
     */
    mobileService.reqFun102001 = function (param, callback, ctrlParam) {
        var paraMap = param;
        paraMap["funcNo"] = "102001";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：晋金优选产品详情查询
     */
    mobileService.reqFun102002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102002";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：晋金优选产品详情--费率查询
     */
    mobileService.reqFun102003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102003";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：晋金优选产品详情--公告查询
     */
    //    mobileService.reqFun102004 = function (param, callback, ctrlParam) {
    //        var paraMap = {};
    //        paraMap["funcNo"] = "102004";
    //        paraMap['fund_code'] = param.fund_code;
    //        var reqParamVo = new service.ReqParamVo();
    //        reqParamVo.setUrl(global.serverPath);
    //        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    //    }
    /**
     * 功能：查询产品公告
     */
    mobileService.reqFun102004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102004";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询手机号是否注册
     */
    mobileService.reqFun102005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102005";
        paraMap['mobile'] = param.mobile;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询手机号是否注册 加图形验证码校验
     */
    mobileService.reqFun9102005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9102005";
        paraMap['mobile'] = param.mobile;
        paraMap['ticket'] = param.ticket; // 图形验证码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：历史七日年化
     */
    mobileService.reqFun102006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102006";
        paraMap['fund_code'] = param.fund_code;
        paraMap['cur_page'] = param.cur_page;
        paraMap['num_per_page'] = param.num_per_page;
        //        paraMap['prod_sub_type'] = param.prod_sub_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：业绩表现
     */
    mobileService.reqFun102007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102007";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询确认及查看收益日期
     */
    mobileService.reqFun102008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102008";
        if (param.fund_code) {
            paraMap['fund_code'] = param.fund_code;
        }
        if (param.comb_code) {
            paraMap['comb_code'] = param.comb_code;
        }
        if (param.dqDate) {
            paraMap['dqDate'] = param.dqDate + "";
        }
        paraMap['type'] = param.type; // 1充值前提示 2 充值成功 3 普通取现 4 普通取现成功 5实时取现成功 6 基金页面提示
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询资产配置
     */
    mobileService.reqFun102009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102009";
        paraMap['fund_code'] = param.fund_code;
        paraMap['type'] = param.type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询重仓持债
     */
    mobileService.reqFun102010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102010";
        paraMap['fund_code'] = param.fund_code;
        paraMap['type'] = param.type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：广告查询
     */
    mobileService.reqFun102011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102011";
        paraMap['group_no'] = param.group_no;
        paraMap['group_id'] = param.group_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：分享信息查询
     */
    mobileService.reqFun102012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102012";
        paraMap['tem_type'] = param.tem_type;//分享类型
        paraMap['share_template'] = param.share_template;//四个活动专属-分享类型
        paraMap['registered_mobile'] = param.registered_mobile;//注册手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：二维码短链接获取
     */
    mobileService.reqFun101073 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101073";
        paraMap['long_url'] = param.long_url;//分享类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行卡限额列表查询
     */
    mobileService.reqFun102014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102014";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取协议
     */
    mobileService.reqFun102016 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102016";
        paraMap['agreement_type'] = param.agreement_type;//协议类型
        paraMap['fund_code'] = param.fund_code;//产品id
        paraMap['agreement_sub_type'] = param.agreement_sub_type;//子类型
        paraMap['bank_code'] = param.bank_code;//银行编码
        paraMap['acct_no'] = param.acct_no;//银行卡号
        paraMap['bankcard_fixedinvest_flag'] = param.bankcard_fixedinvest_flag;
        paraMap["desensitize_fields"] = "acct_no:4";
        paraMap["fixed_invest_flag"] = param.fixed_invest_flag
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询取现额度
     */
    mobileService.reqFun102017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102017";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：弹框
     */
    mobileService.reqFun102018 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102018";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取银行scheme
     */
    mobileService.reqFun102019 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102019";
        paraMap['bank_code'] = param.bank_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：首页列表查询
     */
    mobileService.reqFun102020 = function (param, callback, ctrlParam) {
        var paraMap = param;
        paraMap["funcNo"] = "102101";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询基金经理信息
     */
    mobileService.reqFun102021 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102021";
        paraMap['fund_code'] = param.fund_code;
        paraMap['type'] = "1";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询基金公司信息
     */
    mobileService.reqFun102022 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102022";
        paraMap['mgrcomp_sname'] = param.mgrcomp_sname;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询历史分红信息
     */
    mobileService.reqFun102023 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102023";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询基金经理历任基金信息
     */
    mobileService.reqFun102025 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102025";
        paraMap['fund_code'] = param.fund_code;
        paraMap['code'] = param.code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询债基产品详情信息
     */
    mobileService.reqFun102026 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102026";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询晋金30天产品详情信息
     */
    mobileService.reqFun102027 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102027";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询定制产品详情信息
     */
    mobileService.reqFun102028 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102028";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：晋金宝交易结果查询
     */
    mobileService.reqFun102030 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102030";
        paraMap['trans_serno'] = param.trans_serno;//订单号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：私募 公告查询
     */
    mobileService.reqFun102033 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102033";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：基金文件
     */
    mobileService.reqFun102083 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102083";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：债基 查询产品历史封闭期收益
     */
    mobileService.reqFun102034 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102034";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：基金收益走势
     */
    mobileService.reqFun102040 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102040";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询产品公告详情接口
     */
    mobileService.reqFun102044 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102044";
        paraMap['id'] = param.id; //公告id
        paraMap['text'] = param.text; //文本
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：费率预算
     */
    mobileService.reqFun102045 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102045";
        if (param.amt) {
            paraMap['amt'] = param.amt;
        }
        if (param.vol) {
            paraMap['vol'] = param.vol;
        }
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询产品购买状态
     */
    mobileService.reqFun102049 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102049";
        paraMap['fund_code'] = param.fund_code;
        if (param.due_date) {
            paraMap['due_date'] = param.due_date;
        }
        if (param.vir_fundcode) {
        	paraMap['vir_fundcode'] = param.vir_fundcode;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询公告列表
     */
    mobileService.reqFun102050 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102050";
        paraMap['cur_page'] = param.cur_page + "";//当前页
        paraMap['num_per_page'] = param.num_per_page; //每页条数
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询公告详情
     */
    mobileService.reqFun102051 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102051";
        paraMap['id'] = param.id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：公募交易记录列表查询
     */
    mobileService.reqFun102052 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102052";
        paraMap['start_date'] = param.start_date;//开始时间
        paraMap['end_date'] = param.end_date;//结束时间
        paraMap['cnl_trans_no'] = param.cnl_trans_no;
        paraMap['cur_page'] = param.cur_page + "";
        paraMap['busi_type'] = param.busi_type; //业务吗
        paraMap['num_per_page'] = param.num_per_page;
        paraMap['desensitize_fields'] = "acct_no:5";
        if (param.fund_code) {
            paraMap["fund_code"] = param.fund_code;
        }
        if (param.ack_date) {
            paraMap["ack_date"] = param.ack_date;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  私募交易记录列表查询(102053)
     */
    mobileService.reqFun102053 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102053";
        paraMap['start_date'] = param.start_date;//开始时间
        paraMap['end_date'] = param.end_date;//结束时间
        paraMap['cnl_trans_no'] = param.cnl_trans_no;
        paraMap['cur_page'] = param.cur_page + "";
        paraMap['num_per_page'] = param.num_per_page + "";
        paraMap['busi_type'] = param.busi_type;
        paraMap['desensitize_fields'] = "acct_no:5";
        if (param.fund_code) {
            paraMap["fund_code"] = param.fund_code;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 现金宝交易记录列表查询（102054）
     */
    mobileService.reqFun102054 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102054";
        paraMap['start_date'] = param.start_date.replace(/-/g, ""); //开始时间
        paraMap['end_date'] = param.end_date.replace(/-/g, ""); //结束时间
        paraMap['cur_page'] = param.cur_page + ""; //当前页
        paraMap['busi_type'] = param.busi_type; //业务吗
        paraMap['num_per_page'] = param.num_per_page; //当前页数个数
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo; //交易账号
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询新闻列表
     */
    mobileService.reqFun102055 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102055";
        paraMap['cur_page'] = param.cur_page + "";//当前页
        paraMap['num_per_page'] = param.num_per_page; //每页条数
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询新闻详情
     */
    mobileService.reqFun102056 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102056";
        paraMap['id'] = param.id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询图片新闻列表
     */
    mobileService.reqFun102057 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102057";
        paraMap['cur_page'] = param.cur_page + "";//当前页
        paraMap['num_per_page'] = param.num_per_page; //每页条数
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询新闻详情
     */
    mobileService.reqFun102058 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102058";
        paraMap['id'] = param.id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 现金宝交易类型查询
     */
    mobileService.reqFun102059 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102059";
        paraMap["trans_type"] = param.trans_type; //交易类型（1.现金宝；2.公募；3.私募）
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询是否存在最新资讯
     */
    mobileService.reqFun102060 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102060";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：根据关键字搜索文章
     */
    mobileService.reqFun102061 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102061";
        paraMap['key'] = param.key;
        paraMap['num_per_page'] = param.num_per_page + "";
        paraMap['cur_page'] = param.cur_page + "";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：根据栏目号查询文章内容
     */
    mobileService.reqFun102062 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102062";
        paraMap['catalog_id'] = param.catalog_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：帮助中心列表查询
     */
    mobileService.reqFun102063 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102063";
        paraMap['parentId'] = param.parentId;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询身份证号是否黑名单
     */
    mobileService.reqFun102064 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102064";
        paraMap['cert_no'] = param.cert_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：申购交易详情查询
     */
    mobileService.reqFun102065 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102065";
        paraMap['trans_serno'] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：赎回交易详情查询
     */
    mobileService.reqFun102066 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102066";
        paraMap['trans_serno'] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：修改分红方式交易详情查询
     */
    mobileService.reqFun102067 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102067";
        paraMap['trans_serno'] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：分红方式交易详情查询（现金分红-红利再投）
     */
    mobileService.reqFun102068 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102068";
        paraMap['trans_serno'] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：预约申购交易详情查询
     */
    mobileService.reqFun102069 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102069";
        paraMap['trans_serno'] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取当前版本信息
     */
    mobileService.reqFun102070 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102070";
        paraMap["versionsn"] = param.versionsn;
        paraMap["channel"] = param.channel;
        paraMap["soft_no"] = param.soft_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：app下载地址查询
     * */
    mobileService.reqFun102071 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102071";
        paraMap["channel"] = param.channel;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：获取产品是否首次购买
     */
    mobileService.reqFun102072 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102072";
        paraMap["fund_code"] = param.fund_code;
        paraMap["prod_sub_type2"] = param.prod_sub_type2;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询汇款充值账户
     */
    mobileService.reqFun102073 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102073";
        paraMap["fund_code"] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：分红方式交易详情查询（现金分红-分红到宝）
     */
    mobileService.reqFun102074 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102074";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：强制赎回交易详情查询
     */
    mobileService.reqFun102075 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102075";
        paraMap['trans_serno'] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：客户是否存在未确认的普通取现交易
     * */
    mobileService.reqFun102076 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102076";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询客户是否签约与是否超出限额
     * */
    mobileService.reqFun102077 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102077";
        paraMap['acct_no'] = param.acct_no;//银行卡号
        paraMap['bank_reserved_mobile'] = param.bank_reserved_mobile;//预留手机号
        paraMap['bank_code'] = param.bank_code;//注册手机号
        if (param.pay_mode) {
            paraMap['pay_mode'] = param.pay_mode;
        }//注册手机号 // 1银行卡  2份额
        paraMap['desensitize_fields'] = "acct_no:4|bank_reserved_mobile:2";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 会员总积分查询-108001
     */
    mobileService.reqFun108001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108001";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 会员积分明细查询-108002
     */
    mobileService.reqFun108002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108002";
        paraMap['start_date'] = param.start_date.replace(/-/g, ""); //开始时间
        paraMap['end_date'] = param.end_date.replace(/-/g, ""); //结束时间
        paraMap['cur_page'] = param.cur_page + ""; //当前页
        paraMap['busi_type'] = param.busi_type; //业务吗
        paraMap['num_per_page'] = param.num_per_page; //当前页数个数
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo; //交易账号
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 会员积分明细查询-新-108006
     */
    mobileService.reqFun108006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108006";
        paraMap['start_date'] = param.start_date.replace(/-/g, ""); //开始时间
        paraMap['end_date'] = param.end_date.replace(/-/g, ""); //结束时间
        paraMap['cur_page'] = param.cur_page + ""; //当前页
        paraMap['num_per_page'] = param.num_per_page; //当前页数个数
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 会员活动图标类型-102080
     */
    mobileService.reqFun102080 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102080";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 会员赚积分活动查询-102081
     */
    mobileService.reqFun102081 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102081";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 活动列表查询-108008
     */
    mobileService.reqFun108008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108008";
        paraMap["mobile"] = param.mobile;
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 记录用户行为-108009 
     */
    mobileService.reqFun108009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108009";
        paraMap['id'] = param.id; //id
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 修改活动状态-108010 1 - 4
     */
    mobileService.reqFun108010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108010";
        paraMap["id"] = param.id;
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 修改活动状态-108011 4 - 5
     */
    mobileService.reqFun108011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108011";
        paraMap["id"] = param.id;
        paraMap["activity_type"] = param.activity_type
        paraMap["mobile"] = param.mobile
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 积分商品查询-108004
     */
    mobileService.reqFun108004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108004";
        paraMap['exchange_type'] = param.exchange_type; //卡券类型 1为话费券，2为京东券
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 积分商品兑换-108003
     */
    mobileService.reqFun108003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108003";
        paraMap['id'] = param.id; //商品id
        paraMap['mobile'] = param.mobile; //手机号
        paraMap['type'] = param.type; //运营商
        paraMap['trans_pwd'] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 运营商判断接口-108007
     */
    mobileService.reqFun108007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108007";
        paraMap['mobile'] = param.mobile; //手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 会员商品兑换结果查询-108005
     */
    mobileService.reqFun108005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108005";
        paraMap['trans_serno'] = param.trans_serno; //商品id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     * 功能：查询用户白名单类型
     * */
    mobileService.reqFun102078 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102078";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /********************************* 交易类接口 ******************************/
    /**
     * 功能：现金宝申购-已签约  本地验证码校验
     */
    mobileService.reqFun106001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106001";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['bank_code'] = param.bank_code;//交易密码
        paraMap['sms_code'] = param.sms_code;
        paraMap['message_code'] = param.message_code;
        paraMap['is_exist'] = param.is_exist;
        paraMap['payorg_id'] = param.payorg_id;
        paraMap['sms_mobile'] = param.sms_mobile;
        paraMap['acct_no'] = param.acct_no;
        paraMap['pay_type'] = param.pay_type;
        paraMap['bank_serial_no'] = param.bank_serial_no;
        paraMap['desensitize_fields'] = "acct_no:5|sms_mobile:2";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['bankserialno'] = param.bank_serial_no;
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        if (param.pay_mode) {//注册手机号 // 1银行卡  2份额
            paraMap['pay_mode'] = param.pay_mode;
        }
        if (param.check_trans_pwd) {//是否需要校验交易密码 1校验交易密码
            paraMap['check_trans_pwd'] = param.check_trans_pwd;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：现金宝申购 -未签约 银行验证码校验
     */
    mobileService.reqFun9106001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9106001";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['bank_code'] = param.bank_code;//交易密码
        paraMap['pay_type'] = param.pay_type;
        paraMap['sms_code'] = param.sms_code;
        paraMap['message_code'] = param.message_code;
        paraMap['is_exist'] = param.is_exist;
        paraMap['payorg_id'] = param.payorg_id;
        paraMap['sms_mobile'] = param.sms_mobile;
        paraMap['bankserialno'] = param.bank_serial_no;
        paraMap['acct_no'] = param.acct_no;
        paraMap['desensitize_fields'] = "acct_no:5|sms_mobile:2";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        if (param.pay_mode) {//注册手机号 // 1银行卡  2份额
            paraMap['pay_mode'] = param.pay_mode;
        }
        if (param.check_trans_pwd) {//是否需要校验交易密码 1校验交易密码
            paraMap['check_trans_pwd'] = param.check_trans_pwd;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
    * 功能：建行直连  本地验证码校验
    */
    mobileService.reqFun106027 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106027";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['bank_code'] = param.bank_code;//交易密码
        paraMap['sms_code'] = param.sms_code;
        paraMap['message_code'] = param.message_code;
        paraMap['is_exist'] = param.is_exist;
        paraMap['payorg_id'] = param.payorg_id;
        paraMap['sms_mobile'] = param.sms_mobile;
        paraMap['acct_no'] = param.acct_no;
        paraMap['pay_type'] = param.pay_type;
        paraMap['bank_serial_no'] = param.bank_serial_no;
        paraMap['desensitize_fields'] = "acct_no:5|sms_mobile:2";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['bankserialno'] = param.bank_serial_no;
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        if (param.pay_mode) {//注册手机号 // 1银行卡  2份额
            paraMap['pay_mode'] = param.pay_mode;
        }
        if (param.check_trans_pwd) {//是否需要校验交易密码 1校验交易密码
            paraMap['check_trans_pwd'] = param.check_trans_pwd;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：现金宝认购、申购产品
     */
    mobileService.reqFun106003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106003";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['app_amt'] = param.app_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        if (param.period) {
            paraMap['period'] = param.period;//期数
        }
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['desensitize_fields'] = "acct_no:5";
        if (param.buyflag) {
            paraMap["buy_flag"] = param.buyflag; //产品风险等级高于用户等级
        } else {
            paraMap["buy_flag"] = ""; //产品风险等级高于用户等级
        }
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：现金宝预约产品
     */
    mobileService.reqFun106008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106008";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['app_amt'] = param.app_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['desensitize_fields'] = "acct_no:5";
        if (param.period) {
            paraMap['period'] = param.period;//期数
        }
        if (param.buyflag) {
            paraMap["buy_flag"] = param.buyflag; //产品风险等级高于用户等级
        } else {
            paraMap["buy_flag"] = ""; //产品风险等级高于用户等级
        }
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：支付路由查询
     */
    mobileService.reqFun106901 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106901";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取签约短信
     */
    mobileService.reqFun106902 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106902";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行卡签约并申购宝宝
     */
    mobileService.reqFun106002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106002";
        paraMap['fund_code'] = param.fund_code; //基金代码
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['app_amt'] = param.app_amt;//申请金额
        paraMap['dividend_method'] = param.dividend_method;//分红方式
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo; //交易账号
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行卡签约并申购宝宝
     */
    mobileService.reqFun106004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106004";
        paraMap['fund_code'] = "000709"; //基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['message_code'] = param.message_code;//短信验证码
        paraMap['bank_serial_no'] = param.bank_serial_no;//银行流水
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo; //交易账号
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：现金宝实时赎回
     */
    mobileService.reqFun106006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106006";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['sms_code'] = param.sms_code;
        paraMap['sms_mobile'] = param.sms_mobile;
        paraMap['acct_no'] = param.acct_no;
        paraMap['desensitize_fields'] = "acct_no:5|sms_mobile:2";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：现金宝普通赎回
     */
    mobileService.reqFun106007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106007";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['sms_code'] = param.sms_code;
        paraMap['sms_mobile'] = param.sms_mobile;
        paraMap['acct_no'] = param.acct_no;
        paraMap['desensitize_fields'] = "acct_no:5|sms_mobile:2";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /******************************* 银行理财类 *********************************/
    /**
     * 功能：用户注册+开户
     */
    mobileService.reqFun151001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151001";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        // paraMap['face_img'] = param.face_img;//人脸图片
        paraMap['bank_acct_no'] = param.bank_acct_no;//绑定银行卡号
        paraMap['bank_reserved_mobile'] = param.bank_reserved_mobile;//预留手机号
        paraMap['message_code'] = param.message_code;//短信验证码
        paraMap['bank_code'] = param.bank_code;//银行编码
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//短信验证码
        paraMap['ocp'] = param.ocp; //职业
        paraMap['bank_name'] = param.bank_name; //银行名称
        var str = "";
        if (paraMap.bank_acct_no.indexOf("*") > -1) str += "bank_acct_no:4|";
        if (paraMap.bank_reserved_mobile.indexOf("*") > -1) str += "bank_reserved_mobile:2|sms_mobile:2|";
        if (str) {
            paraMap['desensitize_fields'] = str.substr(0, str.length - 1);
        }
        paraMap['front_file_id'] = param.front_file_id; //身份证正面
        paraMap['back_file_id'] = param.back_file_id; //身份证背面
        paraMap['face_file_id'] = param.face_file_id; //人脸
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：更换绑定卡
     */
    mobileService.reqFun151002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151002";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['bank_acct_no'] = param.bank_acct_no;//绑定银行卡号
        paraMap['bank_reserved_mobile'] = param.bank_reserved_mobile;//预留手机号
        // paraMap['message_code'] = param.message_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//预留手机号
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['bank_code'] = param.bank_code;
        paraMap['bank_name'] = param.bank_name;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：发送短信验证码
     */
    mobileService.reqFun151003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151003";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        //1001-开户 1002-更换绑定卡 1003-更换手机号 1004-充值交易 1005-提现交易 1006-密码重置 1007-密码修改
        // 1008-购买众邦宝/多帮利 1009-赎回众邦宝/多帮利 1010-绑定卡维护 1011-销户 1012-投融资
        // 1013-众邦融项目购买 1014-自动续投 1015-信用卡还款 1016-挂单 1017-取消挂单
        paraMap['bus_type'] = param.bus_type;//业务类型 01 绑定 02 解绑
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['bank_reserved_mobile'] = param.bank_reserved_mobile;//预留手机号
        if (param.mobile_type) {
            paraMap['desensitize_fields'] = "bank_reserved_mobile:" + param.mobile_type; // mobile_type 1注册 2 预留，如果是完整手机号，不需要传值
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：充值
     */
    mobileService.reqFun9151004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9151004";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：签约充值
     */
    mobileService.reqFun151004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151004";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['message_code'] = param.message_code;//短信验证码
        paraMap['sign_mes_code'] = param.sign_mes_code;//签约短信关联码
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：取现
     */
    mobileService.reqFun151005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151005";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：购买
     */
    mobileService.reqFun151006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151006";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['trans_type'] = param.trans_type;//交易类型 1：原始购买; 2：转让购买SD001 必输
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：提前支取
     */
    mobileService.reqFun151007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151007";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['brnd_sris'] = paraMap.brnd_sris;//品牌系列 SD001：智能存款-众力存;SD002：智能存款-众惠存
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['message_code'] = param.message_code;//短信验证码
        paraMap['order_no'] = param.order_no;//订单号
        paraMap['sms_code'] = param.sms_code;//订单号
        paraMap['sms_mobile'] = param.sms_mobile;//订单号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：提前支取试算
     */
    mobileService.reqFun151008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151008";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['brnd_sris'] = param.brnd_sris;//品牌系列 SD001：智能存款-众力存;SD002：智能存款-众惠存
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['order_no'] = param.order_no;//订单号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：签约申请
     */
    mobileService.reqFun151009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151009";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['bank_reserved_mobile'] = param.bank_reserved_mobile;
        paraMap['message_code'] = param.message_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：挂单
     */
    mobileService.reqFun151011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151011";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['hang_list_amt'] = param.hang_list_amt;//交易金额
        paraMap['order_no'] = param.order_no;//订单号
        paraMap['brnd_sris'] = param.brnd_sris;//
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：取消挂单
     */
    mobileService.reqFun151012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151012";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['hang_list_grp_srlno'] = param.hang_list_grp_srlno;//挂单分组流水号
        paraMap['order_no'] = param.order_no;//订单号
        paraMap['brnd_sris'] = param.brnd_sris;//品牌系列
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：挂单试算
     */
    mobileService.reqFun151013 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151013";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['hang_list_amt'] = param.hang_list_amt;//交易金额
        paraMap['order_no'] = param.order_no;//订单号
        paraMap['brnd_sris'] = param.brnd_sris;//品牌系列
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：购买转让
     */
    mobileService.reqFun151014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151014";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['brnd_sris'] = param.brnd_sris;//品牌系列 SD001：智能存款-众力存;SD002：智能存款-众惠存
        paraMap['hang_list_grp_srlno'] = param.hang_list_grp_srlno;//挂单分组流水号
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['hanglist_no'] = param.hang_list_no;//挂单号
        paraMap['sms_code'] = param.sms_code;//
        paraMap['sms_mobile'] = param.sms_mobile;//订单号
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：撤单并支取
     */
    mobileService.reqFun151015 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151015";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['brnd_sris'] = param.brnd_sris;//品牌系列 SD001：智能存款-众力存;SD002：智能存款-众惠存
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['hang_list_grp_srlno'] = param.hang_list_grp_srlno;//挂单分组流水号
        paraMap['order_no'] = param.order_no;//订单号
        paraMap['sms_code'] = param.sms_code;
        paraMap['sms_mobile'] = param.sms_mobile;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询产品列表
     */
    mobileService.reqFun151101 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151101";
        paraMap['pay_int_hz'] = param.pay_int_hz;//付息期限 M
        paraMap['page'] = param.page + "";//当前页数
        paraMap['num_per_page'] = param.num_per_page;//每页条数
        paraMap['recommend_type'] = param.recommend_type;//推荐
        if (param.bank_channel_code) {
            paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：用户资产持仓查询
     */
    mobileService.reqFun151102 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151102";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['prod_code'] = param.prod_code;//产品代码 M 如不送就返回所有数据
        paraMap['fund_type'] = param.fund_type;//产品类型 N
        paraMap['dt_info_flag'] = param.dt_info_flag;//详细信息标志 N 0-否 1-是 不传默认显示详细信息
        paraMap['trans_seq_no'] = param.trans_seq_no;//订单号 N
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：账户信息查询
     */
    mobileService.reqFun151103 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151103";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['prod_code'] = param.prod_code;//产品代码 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：交易状态查询
     */
    mobileService.reqFun151104 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151104";
        paraMap['trans_serno'] = param.trans_serno;//交易流水号 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：交易记录查询
     */
    mobileService.reqFun151105 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151105";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['str_date'] = param.str_date;//交易起始日期 M
        paraMap['end_date'] = param.end_date;//交易结束日期 M
        paraMap['busi_code'] = param.busiCode;//交易类型
        paraMap['pgrec_num'] = param.pgrec_num;//每页记录数   N
        paraMap['curr_page'] = param.curr_page + "";//当前页N
        paraMap['tol_num'] = param.tol_num;//总笔数 N
        paraMap['tol_page'] = param.tol_page;//总页数 N N
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：赎回利息试算
     */
    mobileService.reqFun151106 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151106";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['trans_seq_no'] = param.trans_seq_no;//订单号 M
        paraMap['drw_amt'] = param.drw_amt;//支取金额 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：我的银行总资产查询
     */
    mobileService.reqFun151107 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151107";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：支持银行及限额查询
     */
    mobileService.reqFun151108 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151108";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行产品交易记录
     */
    mobileService.reqFun151109 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151109";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['order_no'] = param.order_no;//订单号 M
        paraMap['brnd_sris'] = param.brnd_sris;//SD002：智能存款-众惠存  M
        if (param.bus_type) {
            paraMap['bus_type'] = param.bus_type;//240-购买   241-提前支取 242-到期系统自动兑付  244-到期支取 245-周期付息 N
        }
        if (param.str_date) {
            paraMap['str_date'] = param.str_date;//起始日期 N
        }
        if (param.end_date) {
            paraMap['end_date'] = param.end_date;//结束日期 N
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询是否已开银行户
     */
    mobileService.reqFun151110 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151110";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：图像上传众邦
     */
    mobileService.reqFun151111 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151111";
        paraMap['base64'] = param.base64;//Base64码 上传类型为0时必传
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询银行限额
     */
    mobileService.reqFun151112 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151112";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['bank_code'] = param.bank_code;//银行编号 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：阿里卡Bin
     */
    mobileService.reqFun151114 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151114";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        paraMap['bank_acct'] = param.bank_acct;//银行卡号 M
        if (param.bank_acct.indexOf("*") > -1) {
            paraMap['desensitize_fields'] = "bank_acct:4";
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询是否需要签约
     */
    mobileService.reqFun151115 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151115";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码 M
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询客户名下电子账户列表
     */
    mobileService.reqFun151116 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151116";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询产品详情
     */
    mobileService.reqFun151117 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151117";
        paraMap['bank_channel_code'] = param.bank_channel_code;
        paraMap['prod_code'] = param.prod_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询转让产品列表
     */
    mobileService.reqFun151118 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151118";
        paraMap['sort_field'] = param.sort_field;//排序规则 M
        paraMap['page'] = param.page + "";//当前页数
        paraMap['num_per_page'] = param.num_per_page;//每页条数
        if (param.bank_channel_code) {
            paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询我的转让
     */
    mobileService.reqFun151119 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151119";
        paraMap['page'] = param.page + "";//当前页数
        paraMap['num_per_page'] = param.num_per_page;//每页条数
        if (param.hang_list_st) {
            paraMap['hang_list_st'] = param.hang_list_st;//挂单状态 N 1:挂单中；2：已成交；3：已取消；4：购买中（不传默认查全部）
        }
        if (param.order_no) {
            paraMap['order_no'] = param.order_no;//订单号 N
        }
        if (param.brnd_sris) {
            paraMap['brnd_sris'] = param.brnd_sris;//订单号 M
        }
        if (param.bank_channel_code) {
            paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取银行协议
     */
    mobileService.reqFun151120 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151120";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        paraMap['acct_no'] = param.acct_no;//银行卡号
        paraMap['bank_code'] = param.bank_code;//银行编码
        if (param.prod_code) {
            paraMap['prod_code'] = param.prod_code;//产品编码
            paraMap['agreement_sub_type'] = param.agreement_sub_type;//协议类型
        } else {
            paraMap['agreement_type'] = param.agreement_type;//协议类型
            paraMap['prod_code'] = "";//产品编码
        }

        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行简介
     */
    mobileService.reqFun151121 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151121";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行列表查询
     */
    mobileService.reqFun151122 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151122";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：重置银行开户状态
     */
    mobileService.reqFun151123 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151123";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道编码
        paraMap['acct_status'] = param.acct_status || "1";//客户状态
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：银行协议签署
     */
    mobileService.reqFun151124 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "151124";
        paraMap["cust_type"] = param.cust_type || "0"; //客户类型 0:个人； 1:机构；
        paraMap["agreement_id"] = param.agreement_id; // 协议编号 按,分隔
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   公私募持仓列表 101902
     */
    mobileService.reqFun101902 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101902";
        paraMap['cur_page'] = param.cur_page + "";//当前页
        paraMap['num_per_page'] = param.num_per_page + "";//每页个数
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   客户基金产品详情查询 101903
     */
    mobileService.reqFun101903 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101903";
        paraMap['fund_code'] = param.fund_code;
        if (param.ack_date) {
            paraMap['ack_date'] = param.ack_date;
        }
        if (param.due_date) {
            paraMap['due_date'] = param.due_date;
        } else {
            paraMap['due_date'] = "";
        }
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   定开产品持仓详情 101905
     */
    mobileService.reqFun101905 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101905";
        paraMap['fund_code'] = param.fund_code;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   持有期产品持仓详情 101906
     */
    mobileService.reqFun101906 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101906";
        paraMap['fund_code'] = param.fund_code;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   持有期产品锁定份额 102087
     */
    mobileService.reqFun102087 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102087";
        paraMap['fund_code'] = param.fund_code;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   30天债基持仓详情 150001
     */
    mobileService.reqFun150001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "150001";
        paraMap['fund_code'] = param.fund_code;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /******************************* 晋金高端 *********************************/


    /**
     *   私募产品列表查询（102042）
     */
    mobileService.reqFun102042 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102042";
        paraMap['start'] = param.start.toString();//分页参数-开始下标
        paraMap['count'] = param.count.toString();//分页参数-每页条数
        if (param.custLabelCnlCode) {
            paraMap['custLabelCnlCode'] = param.custLabelCnlCode;
        }
        if (param.recommend_type) {
            paraMap['recommend_type'] = param.recommend_type;
        }

        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   私募产品详情查询（102043）
     */
    mobileService.reqFun102043 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102043";
        paraMap['fund_code'] = param.fund_code;//产品id
        if (param.prod_sub_type2) {
            paraMap['prod_sub_type2'] = param.prod_sub_type2;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     *   获取用户导航状态信息 101032
     */
    mobileService.reqFun101032 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "101032";
        paraMap['fund_code'] = param.fund_code;//产品id
        paraMap['type'] = param.type;//查询类型 90:普通私募 91:小集合
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   完善信息
     */
    mobileService.reqFun101033 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "101033";
        paraMap['occupation'] = param.occupation;//职业
        paraMap['address'] = param.address;//地址
        if (param.email) {
            paraMap['email'] = param.email;//邮箱
        }
        paraMap['bank_reserved_mobile'] = param.bank_reserved_mobile;//预留手机号
        paraMap['desensitize_fields'] = "bank_reserved_mobile:2";
        paraMap['year_icome'] = param.year_icome;//年收入
        paraMap['living_address_province'] = param.living_address_province;//省
        paraMap['living_address_city'] = param.living_address_city;//市
        paraMap['living_address_county'] = param.living_address_county;//区
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   申请合格投资人认证
     */
    mobileService.reqFun101034 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "101034";
        paraMap['type'] = param.type;//类型  2:资产证明 3:收入证明
        paraMap['objective'] = param.objective;//期望状态 90:普通私募 91:小集合
        if (param.type == "2") {
            paraMap['certia_mount'] = param.certia_mount;// 剩余金额
        }
        paraMap['base64_list'] = JSON.stringify(param.base64_list).replace(/\\n/g, "");//图片
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   适当性评估结果确认 101035
     */
    mobileService.reqFun101035 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "101035";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   风险揭示书确认 101036
     */
    mobileService.reqFun101036 = function (param, callback, ctrlParam) {
        var paraMap = param || {};
        paraMap["funcNo"] = "101036";
        paraMap["transfer_flag"] = param.transfer_flag
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 查询私募产品风险揭示书（102046）
     */
    mobileService.reqFun102046 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102046";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     *   用户合格投资人弹窗状态(101037)
     */
    mobileService.reqFun101037 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101037";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   用户合格投资人弹窗状态(101038)
     */
    mobileService.reqFun101038 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101038";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  查询是否是合格投资人并更改状态
     */
    mobileService.reqFun101039 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101039";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  查询是否是合格投资人
     */
    mobileService.reqFun101040 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101040";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  修改分红方式（101043）
     */
    mobileService.reqFun101043 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101043";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['dividend_method'] = param.dividend_method;
        paraMap['trans_pwd'] = param.trans_pwd;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  变更产品到期处理方式（106015）
     */
    mobileService.reqFun106015 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106015";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['end_flag'] = param.end_flag;
        paraMap['trans_pwd'] = param.trans_pwd;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['vir_fundcode'] = param.vir_fundcode;
        if (param.due_date) {
            paraMap['due_date'] = param.due_date;
        } else {
            paraMap['due_date'] = "";
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  查询是否存在在途资金
     */
    mobileService.reqFun101044 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101044";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  产品赎回（106010）
     */
    mobileService.reqFun106010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106010";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap['redem_method'] = param.redem_method;//赎回到宝:1,赎回到卡：2
        if (param.due_date) {
            paraMap['due_date'] = param.due_date;//到期日
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  查询客户总资产（101999）
     */
    mobileService.reqFun101999 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101999";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  修改分红方式（106011）
     */
    mobileService.reqFun106011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106011";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['dividend_method'] = param.dividend_method;//基金代码
        paraMap['trans_pwd'] = param.trans_pwd;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  预约私募（106012）
     */
    mobileService.reqFun106012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106012";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//基金产品代码
        paraMap['app_amt'] = param.app_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['buyflag'] = param.buyflag;//风险等级是否匹配  1不匹配
        paraMap["agreement_sign_no"] = param.agreement_sign_no;//协议流水
        paraMap["period"] = param.period;//期号
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  购买私募（106013）
     */
    mobileService.reqFun106013 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106013";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//基金产品代码
        paraMap['app_amt'] = param.app_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["buyflag"] = param.buyflag;//风险等级是否匹配
        paraMap["agreement_sign_no"] = param.agreement_sign_no;//协议流水
        paraMap["period"] = param.period;//期号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  修改分红方式（101043）
     */
    mobileService.reqFun101043 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101043";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//产品代码
        paraMap['dividend_method'] = param.dividend_method;//分红方式
        if (param.ack_date) {
            paraMap['ack_date'] = param.ack_date;//确认日期
        }
        if (param.due_date) {
            paraMap['due_date'] = param.due_date;// 到期日
        }
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  签署合同
     */
    mobileService.reqFun106014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106014";
        paraMap['fund_code'] = param.fund_code;//基金产品代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  客户私募资产查询(101904)
     */
    mobileService.reqFun101904 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101904";
        paraMap['cur_page'] = param.cur_page + "";//当前页
        paraMap['num_per_page'] = param.num_per_page + "";//每页个数
        paraMap['cust_fund_type'] = param.cust_fund_type;//0-持有  1-在途
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  查询产品赎回提示 (102047)
     */
    mobileService.reqFun102047 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102047";
        paraMap['fund_code'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  查询推送消息详情接口 (102048)
     */
    mobileService.reqFun102048 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102048";
        paraMap['template_id'] = param.template_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  客户私募撤单（106017）
     */
    mobileService.reqFun106017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106017";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['order_no'] = param.order_no;//预约订单号
        paraMap['trans_pwd'] = param.trans_pwd;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  客户销户（101045）
     */
    mobileService.reqFun101045 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101045";
        paraMap["cert_type"] = param.cert_type; //证件类型
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号码
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["sms_mobile"] = param.sms_mobile;
        paraMap["sms_code"] = param.sms_code;
        var reqParamVo = new service.ReqParamVo();
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     * 功能：销户资产查询101046
     */
    mobileService.reqFun101046 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101046";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：实名认证
     */
    mobileService.reqFun101047 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101047";
        paraMap["bank_acct"] = param.bank_acct; //银行卡号s
        paraMap["cert_no"] = param.cert_no; //身份证号
        if (!param.cert_no || param.cert_no.indexOf("*") > -1) {
            paraMap['desensitize_fields'] = "cert_no:3";
        }
        paraMap["message_code"] = param.message_code; //短信
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //银行预留手机号
        paraMap["sms_mobile"] = param.sms_mobile; //银行预留手机号
        paraMap["sms_code"] = param.sms_code; //银行预留手机号
        paraMap["bank_code"] = param.bank_code;
        paraMap["pay_type"] = param.pay_type;
        paraMap["payorg_id"] = param.payorg_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：实名认证
     */
    mobileService.reqFun101061 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101061";
        paraMap["bank_acct"] = param.bank_acct; //银行卡号s
        paraMap["cert_no"] = param.cert_no; //身份证号
        if (!param.cert_no || param.cert_no.indexOf("*") > -1) {
            paraMap['desensitize_fields'] = "cert_no:3";
        }
        paraMap["message_code"] = param.message_code; //短信
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bankserialno"] = param.bank_serial_no; //流水号
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //银行预留手机号
        paraMap["sms_mobile"] = param.sms_mobile; //银行预留手机号
        paraMap["sms_code"] = param.sms_code; //银行预留手机号
        paraMap["bank_code"] = param.bank_code;
        paraMap["pay_type"] = param.pay_type;
        paraMap["payorg_id"] = param.payorg_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：引流注册-设置登录密码
     */
    mobileService.reqFun101048 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101048";
        paraMap["mobile"] = param.mobile; //注册手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：引流注册-设置登录密码
     */
    mobileService.reqFun101049 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101049";
        paraMap["registered_mobile"] = param.registered_mobile; //注册手机号
        paraMap["recommend"] = param.recommend; //邀请人手机号
        paraMap["labelId"] = param.labelId; //渠道ID
        paraMap["login_pwd"] = param.login_pwd; // 登录密码
        paraMap["pwd_fields"] = "login_pwd"; //加密字段
        paraMap["device_token"] = param.device_token;
        paraMap["source"] = param.source;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：身份证有效性校验
     */
    mobileService.reqFun102079 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102079";
        paraMap["idcard"] = param.idcard; //身份证号
        paraMap["name"] = param.name; // 姓名
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取风险揭示书提示信息
     */
    mobileService.reqFun102082 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102082";
        paraMap["fund_code"] = param.fund_code; //产品编码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：协议签署
     */
    mobileService.reqFun106020 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106020";
        paraMap["cust_type"] = param.cust_type || "0"; //客户类型 0:个人； 1:机构；
        paraMap["agreement_id"] = param.agreement_id; // 协议编号 按,分隔
        paraMap["fund_code"] = param.fund_code; // 产品代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：私募 购买交易详情
     */
    mobileService.reqFun102084 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102084";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        paraMap["prod_sub_type2"] = param.prod_sub_type2; // 产品类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：私募 预约购买交易详情
     */
    mobileService.reqFun102085 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102085";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        paraMap["prod_sub_type2"] = param.prod_sub_type2; // 产品类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：私募 分红交易详情
     */
    mobileService.reqFun102086 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102086";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        paraMap["prod_sub_type2"] = param.prod_sub_type2; // 产品类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   私募到期赎回交易详情查询 102088
     */
    mobileService.reqFun102088 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102088";
        paraMap['trans_serno'] = param.trans_serno;//交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   私募撤单交易详情查询 102089
     */
    mobileService.reqFun102089 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102089";
        paraMap['trans_serno'] = param.trans_serno;//交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   小集合赎回交易详情查询 102090
     */
    mobileService.reqFun102090 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102090";
        paraMap['trans_serno'] = param.trans_serno;//交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   私募-小集合強赎赎回交易详情查询 102091
     */
    mobileService.reqFun102091 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102091";
        paraMap['trans_serno'] = param.trans_serno;//交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /*
     * 查询用户电子签名的合同
     */
    mobileService.reqFun102092 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102092";
        paraMap['fund_code'] = param.fund_code;//产品id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /*
     * 获取oss私密文件读取权限
     */
    mobileService.reqFun102093 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102093";
        paraMap['privateUrl'] = param.privateUrl;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /*
     * 银行专享交易
     */
    mobileService.reqFun109001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "109001";
        paraMap['bank_channel_code'] = param.bank_channel_code;//银行渠道代码
        paraMap['prod_code'] = param.prod_code;//产品代码
        paraMap['trans_amt'] = param.trans_amt;//交易金额
        paraMap['trans_type'] = param.trans_type;//交易类型 1：原始购买; 2：转让购买SD001 必输
        paraMap['sms_code'] = param.sms_code;//短信验证码
        paraMap['sms_mobile'] = param.sms_mobile;//手机号
        paraMap['prod_type'] = param.prod_type;//专享类型
        paraMap['prod_name'] = param.prod_name;//产品名称
        paraMap['prod_in_table_id'] = param.id;//专享产品id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /*
     * 专享产品列表
     */
    mobileService.reqFun109002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "109002";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /*
     * 是否是新手
     */
    mobileService.reqFun109003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "109003";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：私募 定开购买交易详情
     */
    mobileService.reqFun102094 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102094";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        paraMap["prod_sub_type2"] = param.prod_sub_type2; // 产品类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：私募 定开持仓详情
     */
    mobileService.reqFun101907 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101907";
        paraMap["fund_code"] = param.fund_code; //基金代码
        paraMap["due_date"] = param.due_date; // 到期日
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：活动 分享加次数
     */
    mobileService.reqFun110002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "110002";
        paraMap["activity_id"] = param.activity_id; //活动id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     * 功能：查询用户是否已上传居住地址-省市区
     */
    mobileService.reqFun101053 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101053";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：源晖产品列表
     */
    mobileService.reqFun102095 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102095";
        paraMap["recommend_type"] = param.recommend_type; // 范围
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：源晖产品 收益走势图
     */
    mobileService.reqFun102036 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102036";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap["section"] = param.section; // 范围
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：源晖产品 查询源晖产品对标指数名称
     */
    mobileService.reqFun102037 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102037";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：源晖产品 查询各区间涨幅
     */
    mobileService.reqFun102038 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102038";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：源晖产品 源晖持仓详情
     */
    mobileService.reqFun101920 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101920";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap["ack_date"] = param.ack_date; // 确认日期
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：源晖产品 赎回
     */
    mobileService.reqFun106021 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106021";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['trans_amt'] = param.trans_amt;//申请金额
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['redem_method'] = param.redem_method;//赎回到宝:1,赎回到卡：2
        paraMap['ack_date'] = param.ack_date;//确认日期
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['vir_fundcode'] = param.vir_fundcode;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   源晖赎回交易详情查询 102096
     */
    mobileService.reqFun102096 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102096";
        paraMap['trans_serno'] = param.trans_serno;//交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   标签id校验
     */
    mobileService.reqFun102097 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102097";
        paraMap['prod_sub_type'] = param.prod_sub_type;//产品子类型
        paraMap['label_id'] = param.label_id;//标签id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   专享产品申购
     */
    mobileService.reqFun106022 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106022";
        paraMap['acct_no'] = param.acct_no;//交易账号
        paraMap['fund_code'] = param.fund_code;//基金产品代码
        paraMap['app_amt'] = param.app_amt;//申请金额
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        if (param.buyflag) {
            paraMap["buy_flag"] = param.buyflag; //产品风险等级高于用户等级
        } else {
            paraMap["buy_flag"] = ""; //产品风险等级高于用户等级
        }
        paraMap['prod_in_table_id'] = param.id;//专享产品id
        paraMap['exclusive_product_type'] = param.exclusive_product_type;//专享产品id
        paraMap['exclusive_type'] = param.exclusive_type;//专享类型
        if (param.buy_again) {
            paraMap['buy_again'] = param.buy_again;//是否可以追加
        }
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   专享产品赎回
     */

    mobileService.reqFun106023 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106023";
        paraMap['acct_no'] = param.acct_no;//交易账号
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_amt'] = param.trans_amt;//交易密码
        paraMap['trans_pwd'] = param.trans_pwd;//产品子类型
        paraMap['redem_method'] = param.redem_method;//赎回方式
        paraMap['redem_type'] = "1";// 1.主动赎回 2.自动赎回
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap['vir_fundcode'] = param.vir_fundcode
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   专享 持仓详情
     */
    mobileService.reqFun101922 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101922";
        paraMap['exclusive_type'] = param.exclusive_type;//专享类型
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['prod_in_table_id'] = param.prod_in_table_id;//
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   专享 是否已持有货基产品
     */
    mobileService.reqFun150004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "150004";
        paraMap['fund_code'] = param.fund_code;//基金代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   计算 T+N日期
     */
    mobileService.reqFun102098 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102098";
        paraMap['increase_term'] = param.increase_term;//N
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   私募-查询是否是黑名单用户
     */
    mobileService.reqFun101057 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101057";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：私募-持有期持仓详情
     */
    mobileService.reqFun101923 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101923";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap["lock_day"] = param.lock_day; // 锁定期
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   私募-政金债持仓详情
     */
    mobileService.reqFun101924 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101924";
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   私募-持有期收益率
     */
    mobileService.reqFun102099 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102099";
        paraMap["prod_id"] = param.fund_code; // 基金代码
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   源晖分销-银行卡支付
     */
    mobileService.reqFun106002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106002";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap["bank_acct"] = param.bank_acct; // 银行卡号
        paraMap["app_amt"] = param.app_amt; // 申请金额
        paraMap["trans_pwd"] = param.trans_pwd; // 交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["agreement_sign_no"] = param.agreement_sign_no;//协议流水
        paraMap["buy_state"] = param.buy_state; //购买状态
        paraMap['period'] = param.period;//期数
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   私募-申港小集合持仓详情
     */
    mobileService.reqFun101925 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101925";
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   源晖分销-待支付订单
     */
    mobileService.reqFun102100 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102100";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能： 源晖分销-银行卡购买交易详情
     */
    mobileService.reqFun102102 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102102";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        paraMap["prod_sub_type2"] = param.prod_sub_type2; // 产品类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：获取产品是否可以追加
     */
    mobileService.reqFun102103 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102103";
        paraMap["fund_code"] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   一创、浦信产品持仓详情 101926
     */
    mobileService.reqFun101926 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101926";
        paraMap['fund_code'] = param.fund_code;
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }


    /**
     *  白名单控制是否展示互转入口 101071
     */
    mobileService.reqFun101071 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101071";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  互转晋金所查询可转金额 
     */
    mobileService.reqFun177002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "177002";
        paraMap['jjs_cust_no'] = param.jjs_cust_no;//晋金所的客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *   互转判断四要素一致和是否做过风险测评 177003
     */
    mobileService.reqFun177003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "177003";
        paraMap['bank_leave_phone'] = param.bank_leave_phone;
        paraMap['cardno'] = param.card_no;
        paraMap['user_name'] = param.user_name;
        paraMap['identity_num'] = param.identity_num;
        paraMap["desensitize_fields"] = "bank_leave_phone:2|identity_num:3|cardno:4"; //拖米字段1-平台注册手机号, 2-银行预留手机号, 3-身份证号  4-卡号 5-资金账号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *   财富H5调用晋金所功能号 177004
     */
    mobileService.reqFun177004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "177004";
        paraMap['func_no'] = param.func_no;
        paraMap['jjs_cust_no'] = param.jjs_cust_no;
        if (param.func_no == "901121") {
            paraMap["cust_risk_result"] = param.cust_risk_result;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     *  102105 根据渠道代码查询渠道信息
     */
    mobileService.reqFun102105 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102105";
        paraMap['channel_code'] = param.channel_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取展示的PDF文件信息
     */
    mobileService.reqFun102106 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102106";
        paraMap["fund_code"] = param.fund_code;
        paraMap["type"] = param.type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取是否需要展示
     */
    mobileService.reqFun102107 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102107";
        paraMap["fund_code"] = param.fund_code;
        paraMap["version"] = param.version;
        paraMap["type"] = param.type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取年度账单
     */
    mobileService.reqFun101927 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101927";
        paraMap["fund_code"] = param.fund_code;
        paraMap["version"] = param.version
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取高端持仓列表
     */
    mobileService.reqFun101929 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101929";
        paraMap['cur_page'] = param.cur_page + "";//当前页
        paraMap['num_per_page'] = param.num_per_page + "";//每页个数
        paraMap['cust_fund_type'] = param.cust_fund_type;//0-持有  1-在途
        paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取高端持仓详情数据
     */
    mobileService.reqFun101930 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101930";
        paraMap["cust_no"] = param.cust_no
        paraMap["acct_no"] = param.acct_no
        paraMap["fund_code"] = param.fund_code
        paraMap["is_stages_flag"] = param.is_stages_flag
        paraMap["due_date"] = param.due_date
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取产品详情
     * @param {*} param 
     * @param {*} callback 
     * @param {*} ctrlParam 
     */

    mobileService.reqFun102108 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102108";
        paraMap["fund_code"] = param.fund_code
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取模板
     * @param {*} param 
     * @param {*} callback 
     * @param {*} ctrlParam 
     */
    mobileService.reqFun102109 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102109";
        paraMap["fund_code"] = param.fund_code
        paraMap["type"] = param.type
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 绑卡成功结果页调用 获取推广信息参数
     */
    mobileService.reqFun108012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108012";
        paraMap["lq"] = param.lq;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 每日首次交易后活动查询
    */
    mobileService.reqFun108013 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108013";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 每日首次交易后活动领取积分
    */
    mobileService.reqFun108014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108014";
        paraMap["act_id"] = param.act_id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 消息推送标识设置
     */
    mobileService.reqFun101058 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101058";
        paraMap["message_push_flag"] = param.message_push_flag;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取强增强减交易详情(新)
     */
    mobileService.reqFun102110 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102110";
        paraMap["trans_serno"] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 产品转让挂单基本信息查询
     */
    mobileService.reqFun107001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107001";
        paraMap["fund_code"] = param.fund_code;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 转让挂单接口
     */
    mobileService.reqFun107002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107002";
        paraMap["fund_code"] = param.fund_code;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap["trans_pwd"] = param.trans_pwd
        // paraMap["transfer_amt"] = param.transfer_amt; //转让价格
        paraMap["give_profit"] = param.give_profit //让出收益
        paraMap["costmoney"] = param.costmoney //本金
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询当前产品是否可以转让
     */
    mobileService.reqFun107003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107003";
        paraMap["fund_code"] = param.fund_code
        paraMap["vir_fundcode"] = param.vir_fundcode;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 转让撤单接口
     */
    mobileService.reqFun107004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107004";
        paraMap["entrust_no"] = param.entrust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 转让中列表（自己，委托中）
     */
    mobileService.reqFun107005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107005";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 转让中列表（全部）
     */
    mobileService.reqFun107006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107006";
        paraMap["order_by"] = param.order_by
        paraMap['start'] = param.start.toString();//分页参数-开始下标
        paraMap['count'] = param.count.toString();//分页参数-每页条数
        paraMap["sort"] = param.sort
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 历史委托
     */
    mobileService.reqFun107007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107007";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取实际转让价格
     */
    mobileService.reqFun107008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107008";
        paraMap["vir_fundcode"] = param.vir_fundcode
        paraMap["give_profit"] = param.give_profit
        paraMap["fund_code"] = param.fund_code
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // /**
    //  * 获取实际转让价格
    //  */
    // mobileService.reqFun106026 = function (param, callback, ctrlParam) {
    //     var paraMap = {};
    //     paraMap["funcNo"] = "106026";
    //     paraMap["cust_no"] = param.cust_no;
    //     paraMap["acct_no"] = param.acct_no;
    //     paraMap["fund_code"] = param.fund_code;s
    //     paraMap["app_vol"] = param.app_vol;
    //     paraMap["trans_pwd"] = param.trans_pwd;
    //     paraMap["entrust_no"] = param.entrust_no;
    //     paraMap["deal_amt"] = param.deal_amt;
    //     paraMap["period"] = param.period;
    //     var reqParamVo = new service.ReqParamVo();
    //     reqParamVo.setUrl(global.serverPath);
    //     commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    // }
    /**
     * 功能：迁移到晋金财富后的活动 分享加次数
     */
    mobileService.reqFun108016 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108016";
        paraMap["activity_id"] = param.activity_id; //活动id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 周五大转盘抽奖-查询抽奖次数接口
     */

    mobileService.reqFun108015 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108015";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
     * 周五大转盘抽奖-抽奖
     */

    mobileService.reqFun108017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108017";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
     * 周五大转盘抽奖-抽奖记录接口108018
     */

    mobileService.reqFun108018 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108018";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    //交易接口
    mobileService.reqFun106026 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106026";
        paraMap["cust_no"] = param.cust_no;
        paraMap["acct_no"] = ut.getUserInf().fncTransAcctNo;
        paraMap["fund_code"] = param.fund_code;
        paraMap["app_vol"] = param.app_vol;
        paraMap["trans_pwd"] = param.trans_pwd;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["entrust_no"] = param.entrust_no;
        paraMap["deal_amt"] = param.deal_amt;
        paraMap["period"] = param.period;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //查询手机号是否被注册（短信验证码校验）
    mobileService.reqFun10102005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "10102005";
        paraMap['mobile'] = param.mobile;
        paraMap['sms_code'] = param.sms_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //点击事件埋点
    mobileService.reqFun101080 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101080";
        paraMap['page'] = param.page;
        paraMap['button'] = param.button;
        paraMap['channel_code'] = param.channel_code;
        paraMap['cust_no'] = param.cust_no;
        // paraMap['source'] = param.source;   //来源
        paraMap['activity_id'] = param.activity_id; //活动ID
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //判断用户是否拥有邀请人
    mobileService.reqFun101059 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101059";
        paraMap["mobile"] = param.mobile;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新理念竞猜活动信息接口查询
    mobileService.reqFun108025 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108025";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新理念竞猜答题活动参与接口
    mobileService.reqFun108026 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108026";
        paraMap["answer"] = param.answer;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //营销活动查询所有活动信息
    mobileService.reqFun108019 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108019";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新年活动查询所有活动信息
    mobileService.reqFun108030 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108030";
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新年活动连续分享接口
    mobileService.reqFun108031 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108031";
        paraMap["cust_no"] = param.cust_no;
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新年活动接口
    mobileService.reqFun108032 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108032";
        paraMap["cust_no"] = param.cust_no;
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 新手活动领取积分 108020
    mobileService.reqFun108020 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108020";
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 连续签到活动签到接口 108021
    mobileService.reqFun108021 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108021";
        paraMap["sysdate"] = param.sysdate;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 活动信息查询接口(红包雨、盲盒)108022
    mobileService.reqFun108022 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108022";
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 盲盒抽奖接口(大转盘、盲盒)108017-需登录
    mobileService.reqFun9108017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9108017";
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    // 红包雨接口108023
    mobileService.reqFun108023 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108023";
        paraMap["activity_id"] = param.activity_id;
        paraMap['amount'] = param.amount;
        paraMap['points'] = param.points;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 二维码邀请活动规则108024
    mobileService.reqFun108024 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108024";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 公募整合详情102113
    mobileService.reqFun102113 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102113";
        paraMap["fund_code"] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    // 公募整合持仓详情101934
    mobileService.reqFun101934 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101934";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap["fund_code"] = param.fund_code;
        paraMap["if_period"] = param.if_period;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap["due_date"] = param.due_date;
        paraMap["respect_income"] = param.respect_income;
        paraMap['financial_prod_type'] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
 * 获取整合后私募持仓详情
 */
    mobileService.reqFun101935 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101935";
        paraMap["acct_no"] = param.acct_no;
        paraMap["fund_code"] = param.fund_code;
        paraMap["if_period"] = param.if_period;
        paraMap["due_date"] = param.due_date;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap["hold_term"] = param.hold_term;
        paraMap["trans_pwd"] = param.trans_pwd;

        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
 * 功能：产品整合交易记录
 */
    mobileService.reqFun102116 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102116";
        paraMap['series_id'] = param.series_id;//系列ID
        paraMap['start_date'] = param.start_date;//开始时间
        paraMap['end_date'] = param.end_date;//结束时间
        paraMap['cnl_trans_no'] = param.cnl_trans_no;
        paraMap['cur_page'] = param.cur_page + "";
        paraMap['busi_type'] = param.busi_type; //业务吗
        paraMap['num_per_page'] = param.num_per_page;
        paraMap['financial_prod_type'] = param.financial_prod_type;
        paraMap['desensitize_fields'] = "acct_no:5";
        if (param.fund_code) {
            paraMap["fund_code"] = param.fund_code;
        }
        if (param.ack_date) {
            paraMap["ack_date"] = param.ack_date;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 持仓收益率
    mobileService.reqFun120116 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "120116";
        paraMap["custno"] = param.custno;
        paraMap["fundcode"] = param.fundcode
        paraMap["virfundcode"] = param.virfundcode
        paraMap["remark"] = param.remark
        paraMap["virfundcode"] = param.virfundcode
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 资产列表
    mobileService.reqFun101932 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101932";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取公募二级页面列表
    mobileService.reqFun101933 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101933";
        paraMap["cust_no"] = param.cust_no;
        paraMap["financial_prod_type"] = param.financial_prod_type;
        paraMap["num_per_page"] = param.num_per_page;
        paraMap["cur_page"] = param.cur_page;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 首页一级分类
    mobileService.reqFun102111 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102111";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 二级页面一级分类
    mobileService.reqFun102112 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102112";
        paraMap["financial_prod_type"] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 收益一级分类下 产品列表集合
    mobileService.reqFun102114 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102114";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 二级页面一级分类下的产品列表集合
    mobileService.reqFun102115 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102115";
        paraMap["financial_prod_type"] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 营销评论查询接口
    mobileService.reqFun108027 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108027";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 营销评论新增接口
    mobileService.reqFun108028 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108028";
        paraMap["content"] = param.content;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 营销评论点赞接口
    mobileService.reqFun108029 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108029";
        paraMap["id"] = param.id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取是否为白名单用户，是否点击过认证弹窗
    mobileService.reqFun101062 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101062";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取首页所有产品
    mobileService.reqFun102117 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102117";
        paraMap["scene_code"] = param.scene_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取二级页面所有产品
    mobileService.reqFun102118 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102118";
        paraMap["financial_prod_type"] = param.financial_prod_type;
        paraMap["scene_code"] = param.scene_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //图片URL转BASE64
    mobileService.reqFun102119 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102119";
        paraMap["img_url"] = param.img_url
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取用户未读消息数量
    mobileService.reqFun105001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "105001";
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //将消息置为已读状态
    mobileService.reqFun105002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "105002";
        paraMap["cust_no"] = param.cust_no
        paraMap["hash_key"] = param.hash_key
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //查询客户消息列表
    mobileService.reqFun105003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "105003";
        paraMap["cust_no"] = param.cust_no
        paraMap["page_num"] = param.page_num
        paraMap['page_size'] = param.page_size
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //查询客户消息详情
    mobileService.reqFun105004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "105004";
        paraMap["mail_id"] = param.mail_id;
        paraMap["mail_send_type"] = param.mail_send_type;
        paraMap["hash_key"] = param.hash_key;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //查询客户消息详情
    mobileService.reqFun105999 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "105999";
        paraMap["cust_no"] = param.cust_no
        paraMap["page_num"] = param.page_num
        paraMap['page_size'] = param.page_size
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //邀请人明细
    mobileService.reqFun101074 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101074";
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取客户定投数量
    mobileService.reqFun106041 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106041";
        paraMap["custno"] = param.custno;
        paraMap["fundcode"] = param.fundcode;
        paraMap["virfundcode"] = param.virfundcode;
        paraMap["investtype"] = '0';
        paraMap["querystatus"] = '0';
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新增定投
    mobileService.reqFun106042 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106042";
        paraMap["custno"] = param.custno;
        paraMap["fundcode"] = param.fundcode;
        paraMap["virfundcode"] = param.virfundcode;
        paraMap["paymethod"] = param.paymethod;
        paraMap["messagecode"] = param.messagecode;
        paraMap["isexist"] = param.isexist;
        paraMap["investcycle"] = param.investcycle;
        paraMap["investdate"] = param.investdate;
        paraMap["investmoney"] = param.investmoney;
        paraMap["transpwd"] = param.transpwd;
        paraMap["agreementsign"] = param.agreementsign;
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["pwd_fields"] = "transpwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //终止定投
    mobileService.reqFun106043 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106043";
        paraMap["custno"] = param.custno;
        paraMap["planid"] = param.planid;
        paraMap["fundcode"] = param.fundcode;
        paraMap["transpwd"] = param.transpwd;
        paraMap["operationcode"] = '0';
        paraMap["pwd_fields"] = "transpwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //定投展示
    mobileService.reqFun106044 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106044";
        paraMap["custno"] = param.custno;
        paraMap["fundcode"] = param.fundcode;
        paraMap["virfundcode"] = param.virfundcode;
        paraMap["querystatus"] = param.querystatus;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = '10',
            paraMap["investdetaile"] = param.investdetaile
        paraMap["investtype"] = param.investtype;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //定投记录
    mobileService.reqFun102121 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102121";
        paraMap["planid"] = param.planid;
        paraMap["custno"] = param.custno;
        paraMap["fundcode"] = param.fundcode;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = '10'
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //定投记录详情
    mobileService.reqFun102122 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102122";
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取下一交易日
    mobileService.reqFun106045 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106045";
        paraMap["investcycle"] = param.investcycle;
        paraMap["investdate"] = param.investdate;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取工作日
    mobileService.reqFun106050 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106050";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //周五活动查询所有活动信息
    mobileService.reqFun108033 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108033";
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //周五活动查询所有活动信息
    mobileService.reqFun108034 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108034";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //周五活动查询所有活动信息
    mobileService.reqFun108035 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108035";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //周五活动查询所有活动信息
    mobileService.reqFun108036 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108036";
        paraMap["comment_id"] = param.comment_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //周五活动查询所有活动信息
    mobileService.reqFun199018 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199018";
        paraMap["cust_no"] = param.cust_no;
        paraMap["activity_id"] = param.activity_id;
        paraMap["base_data"] = param.base_data;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取客户经理信息
    mobileService.reqFun101075 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101075";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //上报绑卡全部信息
    mobileService.reqFun101081 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101081";
        paraMap["cust_address"] = param.cust_address; //客户地址
        paraMap["bank_acct"] = param.bank_acct; //银行卡号
        paraMap["cert_type"] = param.cert_type; //证件类型
        paraMap["message_code"] = param.message_code; //验证码
        paraMap["sms_code"] = param.sms_code; //验证码
        paraMap["pay_type"] = param.pay_type; //健全方式
        paraMap["vaild_date"] = param.vaild_date.replace(/\//g, ""); //证件有效期
        paraMap["sex"] = param.sex; //性别
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap["cert_no"] = param.cert_no; //证件号码
        paraMap["cust_name"] = param.cust_name; //客户姓名
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //预留手机号
        paraMap["trans_pwd"] = param.trans_pwd; //交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["bank_name"] = param.bank_name; //银行名称
        paraMap["payorg_id"] = param.payorg_id; //支付机构ID
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["vocation_code"] = param.vocation_code;//职业
        paraMap["income"] = param.income;//年收入
        paraMap["living_address_province"] = param.living_address_province;//居住地址-省
        paraMap["living_address_city"] = param.living_address_city;//居住地址-市
        paraMap["living_address_county"] = param.living_address_county;//居住地址-县
        paraMap["recommend"] = param.recommend;//邀请人
        paraMap["cerditrecord"] = param.bad_credit;//不良诚信记录
        paraMap["actualisself"] = param.actualisself;//控制人
        paraMap["actualctrlname"] = param.actualctrlname;//控制人姓名
        paraMap["actualctrlidcode"] = param.actualctrlidcode;//控制人证件号
        paraMap["benefisself"] = param.beneficiary;//受益人
        paraMap["beneficiary"] = param.beneficiaryname;//受益人姓名
        paraMap["beneficiaryno"] = param.beneficiaryno;//受益人证件号
        paraMap["back_url"] = param.back_url;//身份证反面URL
        paraMap["front_url"] = param.front_url;//身份证正面URL
        paraMap["error_reason"] = param.error_reason;//身份证正面URL


        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //异常开户驳回信息弹窗查询
    mobileService.reqFun101082 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101082";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //102123 理财账单查询接口
    mobileService.reqFun102123 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102123";
        paraMap["bill_type"] = param.bill_type;
        paraMap["month"] = param.month;
        paraMap["year"] = param.year;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = param.num_per_page;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //102127 我的页面是否展示某菜单
    mobileService.reqFun102127 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102127";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 持仓已完成列表查询
    mobileService.reqFun102125 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102125";
        paraMap["cust_no"] = param.cust_no;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = param.num_per_page;
        paraMap["financial_prod_type"] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 持仓已完成详情查询
    mobileService.reqFun102126 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102126";
        paraMap["cust_no"] = param.cust_no;
        paraMap["start_date"] = param.start_date;
        paraMap["end_date"] = param.end_date;
        paraMap["fund_code"] = param.fund_code;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = param.num_per_page;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //产品消息已读
    mobileService.reqFun101083 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101083";
        paraMap["msg_id"] = param.msg_id;
        paraMap["prod_id"] = param.prod_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取活动详情 //19
    mobileService.reqFun108037 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108037";
        paraMap["activity_type"] = param.activity_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    mobileService.reqFun108038 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108038";
        paraMap["activity_id"] = param.activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //产品持有收益率
    mobileService.reqFun102128 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102128";
        paraMap["fund_code"] = param.fund_code;
        paraMap["section"] = param.section;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //判断当前页面是否可以被分享
    mobileService.reqFun102129 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102129";
        paraMap["page_type"] = param.page_type;
        paraMap["busi_id"] = param.busi_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //基金超市分区列表 102132
    mobileService.reqFun102132 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102132";
        paraMap["fund_market_type"] = param.fund_market_type;
        // paraMap["busi_id"] = param.busi_id; 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //修改持有期 106051
    mobileService.reqFun106051 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106051";
        paraMap["fund_code"] = param.fund_code;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap["hold_term"] = param.hold_term;
        paraMap["trans_pwd"] = param.trans_pwd;
        paraMap["due_date"] = param.due_date;
        // paraMap["busi_id"] = param.busi_id; 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 百亿活动-终极大奖抽奖
     */
    mobileService.reqFun108041 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108041";
        paraMap["activity_id"] = param.activity_id;
        paraMap["main_activity_id"] = param.main_activity_id;
        // paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //百亿活动详情 108039
    mobileService.reqFun108039 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108039";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //发送寄语 108043s
    mobileService.reqFun108043 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108043";
        paraMap["send_word_id"] = param.send_word_id;
        paraMap["activity_id"] = param.activity_id;
        paraMap["main_activity_id"] = param.main_activity_id;
        paraMap["send_word_content"] = param.send_word_content;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //观看视频成功
    mobileService.reqFun108042 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108042";
        paraMap["activity_id"] = param.activity_id;
        paraMap["main_activity_id"] = param.main_activity_id;
        paraMap["video_id"] = param.video_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    //参与寄语
    mobileService.reqFun108044 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108044";
        paraMap["activity_id"] = param.activity_id;
        paraMap["main_activity_id"] = param.main_activity_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 一键购买产品查询
    mobileService.reqFun102136 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102136";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询一键购买基金概要文件
    mobileService.reqFun102137 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102137";
        paraMap["fund_code_list"] = param.fund_code_list;
        paraMap["cust_no"] = param.cust_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 一键购买基金概要文件弹窗确认
    mobileService.reqFun102138 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102138";
        paraMap["fund_list"] = param.fund_list;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 一键购买买入明细 
    mobileService.reqFun102139 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102139";
        paraMap["fund_code"] = param.fund_code;
        paraMap["fund_money"] = param.fund_money;
        paraMap["fund_list"] = param.fund_list;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 一键购买相关协议 
    mobileService.reqFun102140 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102140";
        paraMap["agreement_sub_type"] = param.agreement_sub_type;
        paraMap["bankcard_fixedinvest_flag"] = param.bankcard_fixedinvest_flag
        paraMap["fund_code_list"] = param.fund_code_list;
        paraMap["cust_no"] = param.cust_no;
        paraMap["fixed_invest_flag"] = param.fixed_invest_flag;
        paraMap["bank_code"] = param.bank_code;
        paraMap["acct_no"] = param.acct_no;
        paraMap["desensitize_fields"] = "acct_no:4"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 一键购买相关协议 留痕
    mobileService.reqFun102141 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102141";
        paraMap["agreement_sub_type"] = param.agreement_sub_type;
        paraMap["fund_code_list"] = param.fund_code_list;
        paraMap["cust_no"] = param.cust_no;
        paraMap["cust_type"] = param.cust_type || "0"; //客户类型 0:个人； 1:机构；
        paraMap["fixed_invest_flag"] = param.fixed_invest_flag;
        paraMap["bankcard_fixedinvest_flag"] = param.bankcard_fixedinvest_flag;
        paraMap["bank_code"] = param.bank_code;
        paraMap["acct_no"] = param.acct_no;
        paraMap["desensitize_fields"] = "acct_no:4"; //拖米字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
   * 功能：基金超市购买
   */
    mobileService.reqFun106047 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106047";
        paraMap['custno'] = param.custno;
        paraMap['totalmoney'] = param.totalmoney;
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['agreementsign'] = param.agreementsign;//协议签署流水号
        paraMap['purchase_list'] = param.purchase_list;//协议签署流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);


        // var paraMap = {};
        // paraMap["funcNo"] = "106003";
        // paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        // paraMap['fund_code'] = param.fund_code;//基金代码
        // paraMap['app_amt'] = param.app_amt;//申请金额
        // paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        // if (param.period) {
        //     paraMap['period'] = param.period;//期数
        // }
        // paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        // paraMap['desensitize_fields'] = "acct_no:5";
        // if (param.buyflag) {
        //     paraMap["buy_flag"] = param.buyflag; //产品风险等级高于用户等级
        // } else {
        //     paraMap["buy_flag"] = ""; //产品风险等级高于用户等级
        // }
        // paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        // var reqParamVo = new service.ReqParamVo();
        // reqParamVo.setUrl(global.serverPath);
        // commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 一键定投定投相关
     */
    // 多基金总定投记录列表
    mobileService.reqFun102142 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102142";
        paraMap["custno"] = param.custno;
        paraMap["fundcode"] = param.fundcode;
        paraMap["virfundcode"] = '';
        paraMap["planid"] = param.planid;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = '10';
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 多基金定投记录详情列表
     */
    mobileService.reqFun102143 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102143";
        paraMap["fundcode"] = param.fundcode;
        paraMap["recordserno"] = param.recordserno; //多基金定投流水号
        paraMap["virfundcode"] = '';
        paraMap["custno"] = param.custno;
        paraMap["planid"] = param.planid;
        paraMap["cur_page"] = param.cur_page + '';
        paraMap["num_per_page"] = '10';
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    // 查询行业分布
    mobileService.reqFun102134 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102134";
        paraMap["fund_code"] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询特色数据
    mobileService.reqFun102135 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102135";
        paraMap["fund_code"] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 收益明细
    mobileService.reqFun102144 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102144";
        paraMap['start_date'] = param.start_date;//开始时间
        paraMap['end_date'] = param.end_date;//结束时间
        // paraMap['cnl_trans_no'] = param.cnl_trans_no;
        paraMap['cur_page'] = param.cur_page + "";
        paraMap['num_per_page'] = param.num_per_page;
        // paraMap['financial_prod_type'] = param.financial_prod_type;
        // paraMap['desensitize_fields'] = "acct_no:5";
        if (param.fund_code) {
            paraMap["fund_code"] = param.fund_code;
        }
        if (param.ack_date) {
            paraMap["ack_date"] = param.ack_date;
        }
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //新增一键定投
    mobileService.reqFun106049 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106049";
        paraMap["custno"] = param.custno;
        paraMap["totalmoney"] = param.totalmoney;
        paraMap["paymethod"] = param.payMethod;
        paraMap["messagecode"] = param.messagecode;
        paraMap["isexist"] = param.isexist;
        paraMap["investcycle"] = param.investcycle;
        paraMap["investdate"] = param.investdate;
        paraMap["transpwd"] = param.transpwd;
        paraMap["purchase_list"] = param.purchase_list;
        paraMap["bankserialno"] = param.bank_serial_no
        paraMap["pwd_fields"] = "transpwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询基金超市估值净值
    mobileService.reqFun102145 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102145";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：产品详情收益走势图
     */
    mobileService.reqFun102146 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102146";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap["section"] = param.section; // 范围
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
   * 功能：产品详情净值走势图
   */
    mobileService.reqFun102147 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102147";
        paraMap["fund_code"] = param.fund_code; // 基金代码
        paraMap["section"] = param.section; // 范围
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
* 功能：多基金买入营销模板
*/
    mobileService.reqFun102148 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102148";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：多基金买入基金业绩表现
    */
    mobileService.reqFun102149 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102149";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 查询银行卡是否支持定投
    */
    mobileService.reqFun101084 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101084";
        paraMap["acct_no"] = param.acct_no; // 银行卡号
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //银行预留手机号
        paraMap["bank_code"] = param.bank_code; //银行编码
        paraMap['desensitize_fields'] = "acct_no:4|bank_reserved_mobile:2";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 撤单
    */
    mobileService.reqFun106055 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106055";
        paraMap["trans_serno"] = param.trans_serno; // 交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：弹框详情模板
    */
    mobileService.reqFun102150 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102150";
        paraMap["templateId"] = param.templateId; // 模板id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 校验注册手机号/登录密码
    */
    mobileService.reqFun101085 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101085";
        paraMap["login_pwd"] = param.login_pwd; // 模板id
        paraMap["registered_mobile"] = param.registered_mobile; // 模板id
        paraMap["pwd_fields"] = "login_pwd"; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 未绑卡修改注册手机号
    */
    mobileService.reqFun101086 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101086";
        paraMap["old_registered_mobile"] = param.old_registered_mobile; // 模板id
        paraMap["registered_mobile"] = param.registered_mobile; // 模板id
        paraMap["sms_code"] = param.sms_code;//验证码
        paraMap["sms_mobile"] = param.sms_mobile;//验证码手机号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
    * 功能：查询套利策略精选指数走势图数据
    */
    mobileService.reqFun102155 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102155";
        paraMap['fundCode'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
    * 功能：查询套利策略产品投向走势图数据
    */
    mobileService.reqFun102156 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102156";
        paraMap['fundCode'] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 定投计算器 计算
     */
    mobileService.reqFun102157 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102157";
        paraMap['fundCode'] = param.fundCode;
        paraMap['cycle'] = param.cycle;
        paraMap['programday'] = param.programday;
        paraMap['amt'] = param.amt;
        paraMap['begin_date'] = param.begin_date;
        paraMap['end_date'] = param.end_date;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询定投产品历史业绩
     */
    mobileService.reqFun102158 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102158";
        paraMap['fundCode'] = param.fundCode;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询定投产品详情
     */
    mobileService.reqFun102159 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102159";
        paraMap['fundCode'] = param.fundCode;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询投顾列表
     */
    mobileService.reqFun102164 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102164";
        paraMap['investment_term'] = param.investment_term;
        paraMap['investment_objectives'] = param.investment_objectives;
        paraMap['level_match'] = param.level_match;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询投顾产品详情
     */
    mobileService.reqFun102165 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102165";
        paraMap['comb_code'] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询投顾持仓分布
     */
    mobileService.reqFun102166 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102166";
        paraMap['comb_code'] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 查询投顾持仓分布详情
    */
    mobileService.reqFun102167 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102167";
        paraMap['comb_code'] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询买入卖出状态
     */
    mobileService.reqFun102168 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102168";
        paraMap['comb_code'] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：投顾申购
    */
    mobileService.reqFun106057 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106057";
        paraMap['cust_no'] = ut.getUserInf().custNo;
        paraMap['tgcust_no'] = param.tgcust_no;//客户号
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['trans_amt'] = param.trans_amt;//交易密码
        paraMap['fund_code'] = param.fund_code;//交易密码
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["buy_flag"] = param.buy_flag; // 投顾测评与产品风险等级是否匹配 0 不匹配 1 匹配
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：投顾赎回
    */
    mobileService.reqFun106060 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106060";
        paraMap['cust_no'] = ut.getUserInf().custNo;
        // paraMap['tgcust_no'] = param.tgcust_no;//客户号
        paraMap['trans_amt'] = param.trans_amt;//赎回比例
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['redem_method'] = param.redem_method;//赎回到宝:1,赎回到卡：2
        paraMap['redeem_fee'] = param.redeem_fee;//赎回手续费
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：投顾交易记录列表
    */
    mobileService.reqFun102162 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102162";
        paraMap['cust_no'] = ut.getUserInf().custNo;
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["fund_code"] = param.fund_code;
        paraMap['start_date'] = param.start_date;//开始时间
        paraMap['end_date'] = param.end_date;//结束时间
        paraMap['cur_page'] = param.cur_page + "";
        paraMap['sub_busi_code'] = param.sub_busi_code; //业务吗
        paraMap['num_per_page'] = param.num_per_page;
        paraMap["is_transit"] = param.is_transit;
        paraMap["busi_code"] = param.busi_code;
        paraMap["financial_prod_type"] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
      * 功能：投顾交易记录详情
      */
    mobileService.reqFun102163 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102163";
        paraMap['trans_serno'] = param.trans_serno;// 交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
      * 功能：查询测评题目
      */
    mobileService.reqFun101089 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101089";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
      * 功能：投顾问卷测评
      */
    mobileService.reqFun101088 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101088";
        paraMap['cust_type'] = param.cust_type;// 客户类型
        paraMap['cust_no'] = param.cust_no;// 客户类型
        paraMap['comb_code'] = param.comb_code;// 产品代码
        paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
   * 功能：获取投顾产品是否可以追加
   */
    mobileService.reqFun106058 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106058";
        paraMap["fund_code"] = param.fund_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
   * 功能：101937 投顾资产详情查询（持仓详情）
   */
    mobileService.reqFun101937 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101937";
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap["fund_code"] = param.fund_code;
        paraMap["if_period"] = param.if_period;
        paraMap["vir_fundcode"] = param.vir_fundcode;
        paraMap["due_date"] = param.due_date;
        paraMap["respect_income"] = param.respect_income;
        paraMap['financial_prod_type'] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：获取投顾产品详情业绩走势
    */
    mobileService.reqFun102169 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102169";
        paraMap["comb_code"] = param.comb_code; // 基金代码
        paraMap["section"] = param.section; // 范围
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        // console.log(paraMap);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
   * 功能：获取投顾产品详情业绩表现
   */
    mobileService.reqFun102170 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102170";
        paraMap["comb_code"] = param.comb_code; // 基金代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取套利产品业绩表现
     */
    mobileService.reqFun102171 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102171";
        paraMap['fund_code'] = param.fund_code; // 产品代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取赎回费率
     */
    mobileService.reqFun102172 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102172";
        paraMap['fund_code'] = param.fund_code; // 产品代码
        paraMap['cust_no'] = param.cust_no; // 客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
   * 查询用户是否展示"我的"页面投顾入口
   */
    mobileService.reqFun102173 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102173";
        paraMap['cust_no'] = param.cust_no; // 客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：投顾交易记录确认明细
     */
    mobileService.reqFun102175 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102175";
        paraMap['trans_serno'] = param.trans_serno;// 交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
    * 功能：查询投顾资产明细
    */
    mobileService.reqFun106061 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106061";
        paraMap['cust_no'] = param.cust_no;// 客户号
        paraMap['fund_code'] = param.fund_code;// 产品代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 弹窗查询接口
     */
    mobileService.reqFun101091 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101091";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 弹窗标记接口
     */
    mobileService.reqFun101090 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101090";
        paraMap['pop_id'] = param.pop_id; // 客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询用户是否持有该产品
    */
    mobileService.reqFun102174 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102174";
        paraMap['fund_code'] = param.fund_code; // 产品代码
        paraMap['vir_fundcode'] = param.vir_fundcode; // 虚拟产品子代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 功能：查询连续签到当日的卡片和文章内容
    */
    mobileService.reqFun108046 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108046";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    /**
     * 功能：查询是否已通过风险测评
     */
    mobileService.reqFun102178 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102178";
        paraMap["comb_code"] = param.comb_code;// 组合代码 
        paraMap["cust_no"] = param.cust_no;// 客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 新版一键购买
    */
    mobileService.reqFun102179 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102179";
        paraMap["financial_prod_type"] = param.financial_prod_type;
        paraMap["zone_prod_type"] = param.zone_prod_type;
        paraMap["fund_market_type"] = param.fund_market_type;
        paraMap["series_id"] = param.series_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
     /**
     * 功能：查询备选库
     */
    mobileService.reqFun102183 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102183";
        paraMap["comb_code"] = param.comb_code;// 组合代码 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询调仓历史
     */
    mobileService.reqFun102182 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102182";
        paraMap["comb_code"] = param.comb_code;// 组合代码 
        paraMap["count"] = param.count;// 每页条数 
        paraMap["current"] = param.current;// 当前页数 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：投顾调仓动态
     */
    mobileService.reqFun102181 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102181";
        paraMap["comb_code"] = param.comb_code;// 组合代码 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：系列产品持仓列表查询
     */
    mobileService.reqFun101939 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101939";
        paraMap["series_id"] = param.series_id;// 系列ID 
        paraMap["num_per_page"] = param.num_per_page;
        paraMap["cur_page"] = param.cur_page;
        paraMap["financial_prod_type"] = param.financial_prod_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：查询分散购买模板id
     */
    mobileService.reqFun102180 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102180";
        paraMap["prod_code"] = param.prod_code;// 组合代码 
        paraMap["series_id"] = param.series_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //产品持有收益率 系列产品营销页特制
    mobileService.reqFun102184 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102184";
        paraMap["fund_code"] = param.fund_code;
        paraMap["section"] = param.section;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  投教相关开始
     * 
     */
    /**
     * 学投资首页列表查询接口 102001
     */
    mobileService.reqFun112001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112001";
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 某类型投教内容查询接口（查看更多，支持图文、视频、音频） 102002
     */
    mobileService.reqFun112002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112002";
        paraMap["type"] = param.type;
        paraMap["current"] = param.current;
        paraMap["count"] = "10"
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 直播间列表查询接口 102003
     */
    mobileService.reqFun112003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112003";
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 某类型直播间查询接口（查看更多） 112004
     */
    mobileService.reqFun112004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112004";
        paraMap["room_type"] = param.room_type;
        paraMap["current"] = param.current;
        paraMap["count"] = "10"
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 用于判断用户是否可以观看当前视频 112005
     */
    mobileService.reqFun112005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112005";
        paraMap["white_label_id"] = param.white_label_id;
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 直播首页列表轮播图查询 112006
     */
    mobileService.reqFun112006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112006";
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 参与竞猜活动 112007
     */
    mobileService.reqFun112007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112007";
        paraMap["activity_id"] = param.activity_id;
        paraMap["guess_answer"] = param.guess_answer;
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查看竞猜记录 112008
     */
    mobileService.reqFun112008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112008";
        paraMap["activity_id"] = param.activity_id;
        paraMap["current"] = param.current;
        paraMap["count"] = "20"
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查看叫投教内容详情
     */
    mobileService.reqFun112009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112009";
        paraMap["invest_teach_id"] = param.invest_teach_id;
        // paraMap['tg_cust_question_result'] = param.tg_cust_question_result;// 客户类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 产品持有收益率 系列产品营销页特制
     */
    mobileService.reqFun102184 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102184";
        paraMap["fund_code"] = param.fund_code;
        paraMap["section"] = param.section;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 投顾新增定投
     */
    mobileService.reqFun106059 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106059";
        paraMap["fundcode"] = param.fundcode;
        paraMap["virfundcode"] = param.virfundcode;
        paraMap["paymethod"] = param.paymethod;
        paraMap["investcycle"] = param.investcycle;
        paraMap["investdate"] = param.investdate;
        paraMap["investmoney"] = param.investmoney;
        paraMap["transpwd"] = param.transpwd;
        paraMap["agreementsign"] = param.agreementsign;
        paraMap["isexist"] = param.isexist;
        paraMap["messagecode"] = param.messagecode;
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["pwd_fields"] = "transpwd"; //加密字段
        // paymethod
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 产品定投结果试算 计算器
     */
    mobileService.reqFun102185 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102185";
        paraMap["amt"] = param.amt; // 首投金额
        paraMap["invest_amt"] = param.invest_amt; // 定投金额
        paraMap["term"] = param.term; // 定投年限
        paraMap["yield"] = param.yield; // 年化收益率
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 产品持有收益率 系列产品营销页特制
     */
    mobileService.reqFun102186 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102186";
        paraMap["comb_code"] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查询投顾定投投资者相关信息
     */
    mobileService.reqFun102187 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102187";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["comb_code"] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 新增投顾定投计划
     */
    mobileService.reqFun106063 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106063";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["acct_no"] = ut.getUserInf().fncTransAcctNo;
        paraMap["fund_code"] = param.fund_code;
        paraMap["m_fund_code"] = param.m_fund_code;
        paraMap["trans_amt"] = param.trans_amt;
        paraMap["buy_flag"] = param.buy_flag;
        paraMap["invest_money"] = param.invest_money;
        paraMap["invest_date"] = param.invest_date;
        paraMap["invest_cycle"] = param.invest_cycle;
        paraMap["pay_method"] = param.pay_method;
        paraMap["trans_pwd"] = param.trans_pwd;
        paraMap["bankserialno"] = param.bankserialno;
        paraMap["messagecode"] = param.messagecode;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["agreement_sign_no"] = param.agreement_sign_no;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["is_exist"] = param.is_exist;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // “财富成长计划”活动列表查询
    mobileService.reqFun112011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112011";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["comb_code"] = param.comb_code; // 客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // “财富成长计划”积分详情记录查询
    mobileService.reqFun112012 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112012";
        paraMap["cust_no"] = param.cust_no; // 客户号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // “财富成长计划”查询全年各月的投资明细列表
    mobileService.reqFun112015 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112015";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["year"] = param.year; // 年份
        paraMap["activity_id"] = param.activity_id; // 活动id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // “财富成长计划”留言列表查询
    mobileService.reqFun112013 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112013";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["activity_id"] = param.activity_id; // 活动id
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // “财富成长计划”发布留言
    mobileService.reqFun112014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112014";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["activity_id"] = param.activity_id; // 活动id
        paraMap["send_word_content"] = param.send_word_content; // 留言内容
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 投顾查询收益明细 新
    mobileService.reqFun101940 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101940";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["fund_code"] = param.fund_code;
        paraMap['cur_page'] = param.cur_page;
        paraMap['num_per_page'] = param.num_per_page;
        paraMap['start_date'] = param.start_date;
        paraMap['end_date'] = param.end_date;

        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 投顾预约申购
     */
    mobileService.reqFun106068 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106068";
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["acct_no"] = ut.getUserInf().fncTransAcctNo;
        paraMap["fund_code"] = param.fund_code;
        paraMap["trans_amt"] = param.trans_amt;
        paraMap["trans_pwd"] = param.trans_pwd;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["agreement_sign_no"] = param.agreement_sign_no
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 投顾交易记录详情
     */
    mobileService.reqFun101941 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101941";
        paraMap["trans_serno"] = param.trans_serno; // 交易流水号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 工行签约
     */
    mobileService.reqFun106071 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106071";
        paraMap["isexist"] = param.isexist;
        paraMap["messagecode"] = param.messagecode;
      //  paraMap["transpwd"] = param.transpwd;
        paraMap["bankserialno"] = param.bankserialno;
        paraMap["payorgid"] = param.payorgid; //加密字段
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 2024春节活动大转盘抽奖-抽奖
     */

    mobileService.reqFun108047 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108047";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
    * 转让人挂单接口（小集合）
    */
    mobileService.reqFun107014 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107014";
        paraMap['fund_code'] = param.fund_code;// 产品代码
        paraMap['trans_pwd'] = param.trans_pwd;// 交易密码
        paraMap["give_profit"] = param.give_profit //让出收益
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 转让人撤单接口（小集合）
    */
    mobileService.reqFun107015 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107015";
        paraMap['entrust_no'] = param.entrust_no;// 转让订单号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 受让人提交购买订单接口
    */
    mobileService.reqFun107016 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107016";
        paraMap['entrust_no'] = param.entrust_no;// 转让订单号
        paraMap['trans_pwd'] = param.trans_pwd;// 交易密码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 受让人取消购买接口
    */
    mobileService.reqFun107017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107017";
        paraMap['trans_serno'] = param.trans_serno;// 转让订单号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 世纪银行账户信息查询
    */
    mobileService.reqFun107018 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107018";
        paraMap['trans_serno'] = param.trans_serno;// 转让订单号
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 上传充值汇款凭证
    */
    mobileService.reqFun107019 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107019";
        paraMap['trans_serno'] = param.trans_serno;// 转让订单号
        paraMap['url'] = param.img_url;// 转让图片集合
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 我的转让-正在购买-查询列表接口
    */
    mobileService.reqFun107020 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "107020";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * base64转地址
     */
    mobileService.reqFun199019 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "199019";
        paraMap["file_type"] = "png";
        paraMap["dir_name"] = "transfer_certificates";
        paraMap['base64_list'] = JSON.stringify(param.base64_list).replace(/\\n/g, "");//图片
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 查看转让交易记录详情 买入，卖出
     */
    mobileService.reqFun102190 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102190";
        paraMap["trans_serno"] = param.trans_serno; //交易流水号
        paraMap["prod_sub_type2"] = param.prod_sub_type2; // 产品类型
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取投顾历史净值
    mobileService.reqFun102193 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102193";
        paraMap["comb_code"] = param.comb_code;
        paraMap["cur_page"] = param.cur_page;
        paraMap["num_per_page"] = param.num_per_page;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 2024春节活动AI问答
    */
    mobileService.reqFun161001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "161001";
        paraMap["question_content"] = param.question_content; // 问题内容
        paraMap["question_type"] = param.question_type; // 问题类型
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    //2024春节活动分享
    mobileService.reqFun108048 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108048";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //2024春节活动
    mobileService.reqFun108049 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108049";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //投顾费用赎回预估
    mobileService.reqFun102192 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102192";
        paraMap["comb_code"] = param.comb_code;
        paraMap["redeem_rate"] = param.redeem_rate; //赎回比例 0-1
        paraMap["redeem_type"] = param.redeem_type; //是否全赎 0否 1是
        paraMap["cust_no"] = param.cust_no;
        // paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        // paraMap['desensitize_fields'] = "acct_no:5";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //调用晋金所的功能号
    mobileService.reqFun177005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "177005";
        paraMap["func_no"] = param.func_no;// 晋金所的功能号
        paraMap["page"] = param.page + "";
        paraMap["numPerPage"] = param.numPerPage;
        // paraMap["cust_no"] = param.cust_no;// 晋金所的功能号
        paraMap["period_type"] = param.period_type;
        paraMap["prodCode"] = param.prodCode;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**代言人 */
    //查询代言人申请状态
    mobileService.reqFun113001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113001";
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //申请成为代言人
    mobileService.reqFun113002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113002";
        paraMap["cust_no"] = param.cust_no;
        paraMap["duty"] = param.duty;
        paraMap["advantage"] = param.advantage;
        paraMap["province_code"] = param.province_code;
        paraMap["province_name"] = param.province_name;
        paraMap["city_code"] = param.city_code;
        paraMap["city_name"] = param.city_name;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //财富签约 + 授权
    mobileService.reqFun177006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "177006";
        paraMap["isexist"] = param.isexist;
        paraMap["messagecode"] = param.messagecode;
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["paytype"] = param.paytype;
        paraMap["jjs_cust_no"] = param.jjs_cust_no;
        paraMap["payorgid"] = param.payorgid;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //财富签约 + 授权 校验短信
    mobileService.reqFun9177006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9177006";
        paraMap["isexist"] = param.isexist;
        paraMap["sms_code"] = param.messagecode;
        paraMap["bankserialno"] = param.bank_serial_no;
        paraMap["paytype"] = param.paytype;
        paraMap["jjs_cust_no"] = param.jjs_cust_no;
        paraMap["payorgid"] = param.payorgid;
        paraMap["sms_mobile"] = param.sms_mobile;
        paraMap['desensitize_fields'] = "sms_mobile:2"; // mobile_type 1注册 2 预留，如果是完整手机号，不需要传值
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询代言人活动信息
    mobileService.reqFun113003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113003";
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询提现记录汇总
    mobileService.reqFun113004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113004";
        paraMap["cust_no"] = param.cust_no;
        paraMap["current"] = param.current + "";
        paraMap["count"] = param.count;
        paraMap["order_no"] = param.order_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 手动添加好友
    mobileService.reqFun113006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113006";
        paraMap["cust_no"] = param.cust_no;
        paraMap["invite_cust_name"] = param.invite_cust_name;
        paraMap["invite_registered_mobile"] = param.invite_registered_mobile;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询已邀请好友
    mobileService.reqFun113007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113007";
        paraMap["cust_no"] = param.cust_no;
        paraMap["current"] = param.current + '';
        paraMap["cust_type"] = param.cust_type;
        paraMap["count"] = param.count;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询添加记录
    mobileService.reqFun113008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113008";
        paraMap["cust_no"] = param.cust_no;
        paraMap["current"] = param.current + "";
        paraMap["count"] = param.count;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询提现记录明细
    mobileService.reqFun113009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "113009";
        paraMap["cust_no"] = param.cust_no;
        paraMap["current"] = param.current + "";
        paraMap["count"] = param.count;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询投顾历史净值
    mobileService.reqFun102194 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102194";
        paraMap["comb_code"] = param.comb_code;
        paraMap["section"] = param.section;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 昵称是否存在
    mobileService.reqFun102195 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102195";
        paraMap["series_id"] = param.series_id;
        paraMap["nick_name"] = param.nick_name;
        paraMap["nick_comb_name"] = param.nick_comb_name;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 已投资列表
    mobileService.reqFun102196 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102196";
        paraMap["series_id"] = param.series_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 是否持有该系列
    mobileService.reqFun102197 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102197";
        paraMap["series_id"] = param.series_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 开启新计划
    mobileService.reqFun102198 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102198";
        paraMap["nick_name"] = param.nick_name;
        paraMap["series_id"] = param.series_id;
        paraMap["cust_no"] = param.cust_no; // 客户号
        paraMap["acct_no"] = ut.getUserInf().fncTransAcctNo;
        paraMap["fund_code"] = param.fund_code;
        paraMap["m_fund_code"] = param.m_fund_code;
        paraMap["trans_amt"] = param.trans_amt;
        paraMap["buy_flag"] = param.buy_flag;
        paraMap["invest_money"] = param.invest_money;
        paraMap["invest_date"] = param.invest_date;
        paraMap["invest_cycle"] = param.invest_cycle;
        paraMap["pay_method"] = param.pay_method;
        paraMap["trans_pwd"] = param.trans_pwd;
        paraMap["bankserialno"] = param.bankserialno;
        paraMap["nick_comb_name"] = param.nick_comb_name,
        paraMap["plan_type"] = param.plan_type;
        paraMap["remark"] = param.remark;
        paraMap["tg_cust_question_result"] = param.tg_cust_question_result;
        paraMap["messagecode"] = param.messagecode;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["agreement_sign_no"] = param.agreement_sign_no;
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["is_exist"] = param.is_exist
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }

    // 修改子女信息
    mobileService.reqFun102199 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102199";
        paraMap["comb_code"] = param.comb_code;
        paraMap["nick_name"] = param.nick_name;
        paraMap["avatar_url"] = param.avatar_url;
        paraMap["birthday"] = param.birthday;
        paraMap["hobby"] = param.hobby;
        paraMap["nick_comb_name"] = param.nick_comb_name;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 查询子女信息
    mobileService.reqFun102200 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102200";
        paraMap["comb_code"] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 同行好友投教活动参与
    mobileService.reqFun108050 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108050";
        paraMap["activity_id"] = param.activity_id;
        paraMap["task_id"] = param.task_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 周五活动竞猜模块接口
    mobileService.reqFun112017 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112017";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 投顾申购撤单
    mobileService.reqFun106069 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106069";
        paraMap["trans_serno"] = param.trans_serno;
        paraMap["fund_code"] = param.fund_code;
        paraMap["cust_no"] = param.cust_no;
        paraMap["cancel_flag"] = '1';
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 投顾赎回撤单
    mobileService.reqFun106070 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106070";
        paraMap["trans_serno"] = param.trans_serno;
        paraMap["fund_code"] = param.fund_code;
        paraMap["cust_no"] = param.cust_no;
        paraMap["cancel_flag"] = '1';
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    
    // 实物奖励领奖信息录入
    mobileService.reqFun108051 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108051";
        paraMap["reward_log_id"] = param.reward_log_id;
        paraMap["receive_type"] = param.receive_type; // 1 大厅自取 2 他人代领 3 邮寄
        paraMap["receiver_name"] = param.receiver_name;
        paraMap["receiver_phone"] = param.receiver_phone;
        paraMap["receiver_address"] = param.receiver_address;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 直播间保持心跳，防止用户退出登录无法观看
     */
    mobileService.reqFun112010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112010";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
       /**
     * 功能：私募历史规模
     */
    mobileService.reqFun102202 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102202";
        paraMap['fund_code'] = param.fund_code;
        paraMap['cur_page'] = param.cur_page;
        paraMap['num_per_page'] = param.num_per_page;

        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    // 埋点数据上传
    mobileService.reqFun101079 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101079";
        paraMap["list"] = param.list;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
    * 2024春节活动大转盘抽奖-抽奖 新
    */

    mobileService.reqFun108052 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108052";
        paraMap["activity_id"] = param.activity_id;
        paraMap["cust_no"] = param.cust_no;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
     * 年终活动获取推荐产品
     */
    mobileService.reqFun102204 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102204";
        paraMap["recommendType"] = param.recommendType;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
     * 获取财富顾问
     */
    mobileService.reqFun112019 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "112019";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
     * 场景营销页列表
     */
    mobileService.reqFun102206 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102206";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    /**
     * 查询投顾持仓分布
     */
    mobileService.reqFun9102166 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9102166";
        paraMap['comb_code'] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    } 
    /**
     * 查询投顾持仓分布
     */
    mobileService.reqFun9102165 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9102165";
        paraMap['comb_code'] = param.comb_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    } 
    /**
     * 功能：查询是否已通过风险测评
     */
    mobileService.reqFun9102178 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "9102178";
        paraMap["comb_code"] = param.comb_code;// 组合代码 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：获取持仓列表（营销页产品）
     */
    mobileService.reqFun102212 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102212";
        paraMap["plan_type"] = param.plan_type;// 组合代码 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    mobileService.reqFun102205 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102205";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    mobileService.reqFun102209 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102209";
        paraMap["answer"] = param.answer;// 投顾答案
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    
    
    /**
    * 功能：投顾存钱罐追加购买
    */
     mobileService.reqFun102210 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102210";
        paraMap['cust_no'] = ut.getUserInf().custNo;
        paraMap['tgcust_no'] = param.tgcust_no;//客户号
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap['trans_amt'] = param.trans_amt;//交易密码
        paraMap['fund_code'] = param.fund_code;//交易密码
        paraMap['agreement_sign_no'] = param.agreement_sign_no;//协议签署流水号
        paraMap['acct_no'] = ut.getUserInf().fncTransAcctNo;
        paraMap['desensitize_fields'] = "acct_no:5";
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap["buy_flag"] = param.buy_flag; // 投顾测评与产品风险等级是否匹配 0 不匹配 1 匹配
        paraMap["remark"] = param.remark;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
     /**
    * 功能：投顾存钱罐赎回
    */
     mobileService.reqFun102211 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102211";
        paraMap['cust_no'] = ut.getUserInf().custNo;
        // paraMap['tgcust_no'] = param.tgcust_no;//客户号
        paraMap['trans_amt'] = param.trans_amt;//赎回比例
        paraMap['fund_code'] = param.fund_code;//基金代码
        paraMap['trans_pwd'] = param.trans_pwd;//交易密码
        paraMap["pwd_fields"] = "trans_pwd"; //加密字段
        paraMap['redem_method'] = param.redem_method;//赎回到宝:1,赎回到卡：2
        paraMap['redeem_fee'] = param.redeem_fee;//赎回手续费
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    

    /**
    * 功能：存钱罐记账本
    */
    mobileService.reqFun102207 = function (param, callback, ctrlParam) {
         var paraMap = {};
         paraMap["funcNo"] = "102207";
         paraMap['cust_no'] = ut.getUserInf().custNo;
         paraMap['comb_code'] = param.comb_code;      
         var reqParamVo = new service.ReqParamVo();
         reqParamVo.setUrl(global.serverPath);
         commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
     }
     /**
     * 场景策略 计算器
     */
    mobileService.reqFun102213 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102213";
        paraMap["amt"] = param.amt; // 首投金额
        paraMap["invest_amt"] = param.invest_amt; // 定投金额
        paraMap["term"] = param.term; // 定投年限
        paraMap["yield"] = param.yield; // 年化收益率
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取场景模板ID
     */
    mobileService.reqFun102214 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102214";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     *  计算收益
     */
    mobileService.reqFun102208 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102208";
        paraMap["amt"] = param.amt; // 首投金额
        paraMap["invest_amt"] = param.invest_amt; // 定投金额
        paraMap["term"] = param.term; // 定投年限
        paraMap["yield"] = param.yield; // 年化收益率
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 功能：短信鉴权发送
     */
    mobileService.reqFun106080 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106080";
        paraMap["payorg_id"] = param.payorg_id;//支付机构
        paraMap["trans_amt"] = param.trans_amt;//充值金额
        paraMap["pay_type"] = param.pay_type;//支付方式
        paraMap["sms_type"] = param.sms_type; //短信模板类型
        paraMap["bank_code"] = param.bank_code;//银行编码
        paraMap["bank_name"] = param.bank_name;//银行名称
        paraMap["cust_name"] = param.cust_name;//客户名称
        paraMap["bank_acct"] = param.bank_acct;//银行卡号
        paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile;//预留手机号
        paraMap["cert_no"] = param.cert_no;//证件号
        paraMap["trans_amt"] = param.trans_amt;//充值金额
        paraMap["fund_code"] = param.fund_code;
        paraMap["cust_no"] = param.cust_no;
        paraMap["pay_mode"] = param.pay_mode;
        var desensitize_fieldsStr = "";
        if (param.cert_no.indexOf("*") > -1) {
            desensitize_fieldsStr += "cert_no:3|"; //拖米字段
        }
        if (param.bank_acct.indexOf("*") > -1) {
            desensitize_fieldsStr += "bank_acct:4|"; //拖米字段
        }
        if (param.bank_reserved_mobile.indexOf("*") > -1) {
            desensitize_fieldsStr += "bank_reserved_mobile:2|"; //拖米字段
        }
        if (desensitize_fieldsStr) {
            paraMap['desensitize_fields'] = desensitize_fieldsStr.substr(0, desensitize_fieldsStr.length - 1);
        }
        paraMap["cert_type"] = param.cert_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /**
     * 获取协议
    */
    mobileService.reqFun106081 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "106081";
        paraMap["cust_no"] = param.cust_no;
        paraMap["payorg_id"] = param.payorg_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    
    
    /**
     * 套利滚入本期前投资情况
    */
    mobileService.reqFun101950 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101950";
        paraMap["cust_no"] = param.cust_no;
        paraMap["fund_code"] = param.fund_code
        paraMap["vir_fundcode"] = param.vir_fundcode
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //判断要撤单的当笔购投顾买交易有没有同一交易日的设置定投（15:00-15:00）
    mobileService.reqFun102215 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102215";
        paraMap["trans_serno"] = param.trans_serno;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
	//注册+登录 后台判断具体走登录还是注册+登录流程
	mobileService.reqFun101094 = function (param, callback, ctrlParam) {
		var paraMap = {};
		paraMap["funcNo"] = "101094";
		paraMap["registered_mobile"] = param.registered_mobile; //注册手机号
		paraMap["login_pwd"] = param.login_pwd; // 登录密码
		paraMap["recommend"] = param.recommend; //邀请人
		paraMap["labelId"] = param.labelId; //渠道标签ID
		paraMap["cust_cnl"] = param.custCnl; //渠道ID
		paraMap["sms_mobile"] = param.sms_mobile; //手机号
		paraMap["sms_code"] = param.sms_code; //短信验证码
		paraMap["source"] = param.source; //用户来源
		var reqParamVo = new service.ReqParamVo();
		reqParamVo.setUrl(global.serverPath);
		commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
	}
	/**
	 * 功能：重置登录密码（忘记密码）不校验验证码
	 */
	mobileService.reqFun9101019 = function (param, callback, ctrlParam) {
		var paraMap = {};
		paraMap["funcNo"] = "9101019";
		paraMap['new_login_pwd'] = param.new_login_pwd; //新密码
		paraMap["pwd_fields"] = "new_login_pwd"; //加密字段
		paraMap["registered_mobile"] = param.registered_mobile; //注册手机号
		paraMap['sms_mobile'] = param.sms_mobile;//手机号
		var reqParamVo = new service.ReqParamVo();
		reqParamVo.setUrl(global.serverPath);
		commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
	}
	/**
	 * 功能：实名认证不校验验证码
	 */
	mobileService.reqFun9101061 = function (param, callback, ctrlParam) {
		var paraMap = {};
		paraMap["funcNo"] = "9101061";
		paraMap["bank_acct"] = param.bank_acct; //银行卡号s
		paraMap["cert_no"] = param.cert_no; //身份证号
		if (!param.cert_no || param.cert_no.indexOf("*") > -1) {
			paraMap['desensitize_fields'] = "cert_no:3";
		}
		paraMap["cust_name"] = param.cust_name; //客户姓名
		paraMap["bankserialno"] = param.bank_serial_no; //流水号
		paraMap["bank_reserved_mobile"] = param.bank_reserved_mobile; //银行预留手机号
		paraMap["sms_mobile"] = param.sms_mobile; //银行预留手机号
		paraMap["bank_code"] = param.bank_code;
		paraMap["pay_type"] = param.pay_type;
		paraMap["payorg_id"] = param.payorg_id;
		var reqParamVo = new service.ReqParamVo();
		reqParamVo.setUrl(global.serverPath);
		commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
	}
	//注册+登录 后台判断具体走登录还是注册+登录流程 （不校验验证码）
	mobileService.reqFun9101094 = function (param, callback, ctrlParam) {
		var paraMap = {};
		paraMap["funcNo"] = "9101094";
		paraMap["registered_mobile"] = param.registered_mobile; //注册手机号
		var reqParamVo = new service.ReqParamVo();
		reqParamVo.setUrl(global.serverPath);
		commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
	}
    //理财账单查询（列表）（102220）（新增）
    mobileService.reqFun102220 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102220";
        paraMap["bill_type"] = param.bill_type; //账单类型：0:单年度 1:全部
        paraMap["year"] = param.year; //bill_type为0时必填
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //理财账单查询（详情）（102221）（新增）
    mobileService.reqFun102221 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102221";
        paraMap["bill_type"] = param.bill_type; //账单类型：0:单年度 1:全部
        paraMap["querydate"] = param.querydate; //bill_type为0时必填
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //合格投资人认证的时间
    mobileService.reqFun181010 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181010";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //专属财顾信息查询
    mobileService.reqFun181004 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181004";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //提交留言
    mobileService.reqFun181005 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181005";
        paraMap["leaveword"] = param.leaveword;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //尊享服务相关详细信息查询
    mobileService.reqFun181001 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181001";
        paraMap["service_type"] = param.service_type;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //公募短债系列
    mobileService.reqFun181009 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181009";
        paraMap["series_id"] = param.series_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取推荐列表
    mobileService.reqFun181006 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181006";
        paraMap["catalog_id"] = param.catalog_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    mobileService.reqFun181007 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181007";
        paraMap["catalog_id"] = param.catalog_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    mobileService.reqFun181008 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181008";
        paraMap["catalog_id"] = param.catalog_id;
        paraMap["start"] = param.current;
        paraMap["count"] = "10"
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取文章详情
    mobileService.reqFun181011 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181011";
        paraMap["article_id"] = param.article_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //尊享服务提交信息
    mobileService.reqFun181003 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181003";
        paraMap["service_type"] = param.service_type;
        paraMap["phone"] = param.phone;//手机号
        paraMap["receiver_name"] = param.receiver_name;//姓名
        paraMap["post_address"] = param.post_address;//地址
        paraMap["appointment_date"] = param.appointment_date;
        paraMap["appointment_time"] = param.appointment_time;
        paraMap["risk"] = param.risk;
        paraMap["rate"] = param.rate;
        paraMap["invest_duration"] = param.invest_duration;
        paraMap["invest_amt"] = param.invest_amt;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //查询未来n个工作日日期列表
    mobileService.reqFun181002 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "181002";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //根据视频ID
    mobileService.reqFun102222 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102222";
        paraMap["video_id"] = param.video_id;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //获取基金超市产品类型（二级）
    mobileService.reqFun102133 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102133";
        paraMap["fund_market_type"] = param.fund_market_type;
        // paraMap["busi_id"] = param.busi_id; 
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //查询产品列表（新）
    mobileService.reqFun102223 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102223";
        paraMap["fund_market_type"] = param.fund_market_type;
        paraMap["zone_prod_type"] = param.zone_prod_type ? param.zone_prod_type : '';
        paraMap["start"] = param.current + '';
        paraMap["count"] = "10"
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //员工切换版本
    mobileService.reqFun101095 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "101095";
        paraMap["scene_code"] = param.scene_code;
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //邀好友赚积分活动信息查询接口
    mobileService.reqFun108053 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108053";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //邀请明细接口
    mobileService.reqFun108054 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108054";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //攒钱加油站
    mobileService.reqFun108058 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108058";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //攒钱加油站柱状图
    mobileService.reqFun108056 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "108056";
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    //攒钱理财计算器
    mobileService.reqFun102225 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102225";
        paraMap["amt"] = param.amt; // 首投金额
        paraMap["invest_amt"] = param.invest_amt; // 定投金额
        paraMap["term"] = param.term; // 年限
        paraMap["yield"] = param.yield; // 年化收益率（字符串）
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    };
    //获取陪伴服务文章列表
    mobileService.reqFun102120 = function (param, callback, ctrlParam) {
        var paraMap = {};
        paraMap["funcNo"] = "102120";
        paraMap["location"] = param.location; // 位置 1产品详情 2持仓详情
        paraMap["prod_id"] = param.prod_id; // 产品代码
        var reqParamVo = new service.ReqParamVo();
        reqParamVo.setUrl(global.serverPath);
        commonInvoke(paraMap, callback, ctrlParam, reqParamVo);
    }
    /********************************应用接口结束********************************/
});