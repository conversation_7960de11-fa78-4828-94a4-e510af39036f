// 晋金高端列表页
define(function (require, exports, module) {


    /*
     * tab切换
     * @param {jqueryEls} tabs 导航项
     * @param {jqueryEls} contents 内容项
     * @param {function} after 切换后回调
     */
    function tabSwitch(tabs, contents, after) {
        tabs.on('click', function () {
            var index = tabs.index($(this))
            $(this).addClass('active')
                .siblings().removeClass('active')
            contents.eq(index).addClass('active')
                .siblings().removeClass('active')
            after && after(this, index)
        })
    }

    /*
    * 资产信息
    * */
    var assetInfo = {
        "90": {
            averageIncome: "40",
            normal: "block",
            small: "hidden",
            familyIncome: 0,
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元",
        },
        "91": {
            averageIncome: "40",
            familyIncome: "500",
            normal: "hidden",
            small: "block",
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元，或家庭金融资产<span class='familySurplusIncome'>--</span>元",
        },
        "92": {
            averageIncome: "40",
            normal: "block",
            small: "hidden",
            familyIncome: 0,
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元",
        },
        "93": {
            averageIncome: "40",
            normal: "block",
            small: "hidden",
            familyIncome: 0,
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元",
        },
        "94": {
            averageIncome: "40",
            normal: "block",
            small: "hidden",
            familyIncome: 0,
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元",
        },
        "95": {
            averageIncome: "40",
            familyIncome: "500",
            normal: "hidden",
            small: "block",
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元，或家庭金融资产<span class='familySurplusIncome'>--</span>元",
        },
        "96": {
            averageIncome: "40",
            familyIncome: "500",
            normal: "hidden",
            small: "block",
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元，或家庭金融资产<span class='familySurplusIncome'>--</span>元",
        },
        "81": {
            averageIncome: "40",
            familyIncome: "500",
            normal: "hidden",
            small: "block",
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元，或家庭金融资产<span class='familySurplusIncome'>--</span>元",
        },
        "97": {
            averageIncome: "40",
            normal: "block",
            small: "hidden",
            familyIncome: 0,
            asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元",
        },
        "100":{
            "80":{
                averageIncome: "40",
                familyIncome: "500",
                normal: "hidden",
                small: "block",
                asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元，或家庭金融资产<span class='familySurplusIncome'>--</span>元",
            },
            "90":{
                averageIncome: "40",
                normal: "block",
                small: "hidden",
                familyIncome: 0,
                asset_proof: "您还需上传个人金融资产证明<span class='surplusAmount'>--</span>元",
            }
        }
    }

    // 暴露对外的接口
    module.exports = {
        tabSwitch: tabSwitch,
        assetInfo: assetInfo
    }
});
