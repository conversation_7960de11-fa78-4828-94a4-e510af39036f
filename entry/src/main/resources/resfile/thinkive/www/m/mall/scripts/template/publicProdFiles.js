// 产品详情模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "template/publicProdFiles",
        _pageId = "#template_publicProdFiles";
    require("chartsUtils");
    var ut = require("../common/userUtil");
    var _mgrcomp_sname = "";
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        tools.initFundBtn(productInfo, _pageId);
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        // console.log(productInfo);
        _mgrcomp_sname = appUtils.getSStorageInfo("mgrcomp_sname"); 
        // console.log(_mgrcomp_sname);
        $(_pageId + " #fund_managers").html(productInfo.fund_managers);
        $(_pageId + " #mgrcomp_name").html(productInfo.mgrcomp_name);

    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 基金档案
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailRecord"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailRecord")
        });
        // 基金经理
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailManager"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailManager")
        });
        // 基金公司
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailCompany"), function () {
            appUtils.setSStorageInfo("mgrcomp_sname", _mgrcomp_sname);
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailCompany")
        });
        // 资产配置
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailConfiguration"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailConfiguration")
        });
        // 基金公告
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailNotice"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailNotice")
        });
        // 基金文件
        appUtils.bindEvent($(_pageId + " #jjThirtyDetailFile"), function () {
            appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailFile")
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('4','destroy','页面销毁');
            tools.saveAlbum(_pageCode)
        });
    }
    //页面销毁
    function destroy() {
        $(_pageId + " #kefu").hide();
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    let publicOfferingDetailModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = publicOfferingDetailModule;
});
