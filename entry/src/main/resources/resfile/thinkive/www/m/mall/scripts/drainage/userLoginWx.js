// 手机登录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#drainage_userLoginWx";
    var _pageCode = "drainage/userLoginWx";
    var mobile; //用于存储手机号
    var invitationMobile;
    var sms_mobile = require("../common/sms_mobile");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    function init() {
        common.systemKeybord(); // 解禁系统键盘
        $(_pageId + " #pwd").val("");
        $(_pageId + " #phontNum").val("");
        sms_mobile.init(_pageId)
        mobile = appUtils.getSStorageInfo("mobile");
        activity_id = appUtils.getSStorageInfo("activity_id");
        if (!mobile) {
            appUtils.pageInit(_pageCode, "drainage/userInvitationWx");
            return;
        }
        $(_pageId + " #phontNum").val(mobile);
        //获取邀请人手机号
        invitationMobile=  appUtils.getSStorageInfo("invitationMobile");
        reqFun102016('12')
    }

    //绑定事件
    function bindPageEvent() {
        //点击登录
        appUtils.bindEvent($(_pageId + " #registered"), function () {
            var phoneNum = $(_pageId + " #phontNum").val();
            /************校验用户信息************/
            if (validatorUtil.isEmpty(phoneNum)) {
                layerUtils.iMsg(-1, "账号不能为空");
                return;
            }
            var vaild = $.trim($(_pageId + " #pwd").val());

            if (validatorUtil.isEmpty(vaild)) {
                layerUtils.iMsg(-1, "验证码不能为空");
                return false;
            }
            //检查验证码获取按钮
            var isSend = $(_pageId + " #getYzm").attr("data-state");
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            //检查协议勾选
            var isChecked = $(_pageId + " #input_radio2").attr("checked");
            if (!isChecked) {
                layerUtils.iMsg(-1, "请阅读协议并同意签署");
                return;
            }
            //校验短信验证码
            var param = {
                "sms_mobile": phoneNum,
                "sms_code": vaild,
            };
            service.reqFun1100003(param, function(data) {
                if(data.error_no == "0") {
                    appUtils.pageInit("drainage/userLoginWx", "drainage/loginPwdWx");
                } else {
                    sms_mobile.clear();
                    $(_pageId + " #pwd").val("");
                    layerUtils.iAlert(data.error_info);
                }
            });
        });

        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            //验证手机号input
            var mobile = $(_pageId + " #phontNum").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            //验证获取验证码按钮可用性
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            var param = {
                mobile_phone: mobile,
                type: common.sms_type.loginDrain,
                send_type: "0"
            };
            sms_mobile.sendDrainPhoneCode(param);
        });

        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });

    }

    function destroy() {
        service.destroy();
        sms_mobile.destroy();
        $(_pageId + " #pwd").val("");
        $(_pageId + " #phontNum").val("");
        $(_pageId + " #input_radio2").attr("checked", "checked");

    }
    //获取协议
    function reqFun102016(param1) {
        var param = {
            agreement_type: param1,
        }
        service.reqFun102016(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    return;
                }
                var html = "";
                for (var i = 0; i < results.length; i++) {
                    html += '<a href="' + global.oss_url + results[i].url + '" target="_blank" style="color:#319ef2">《'+ results[i].agreement_title + '》</a>';
                }
                $(_pageId + " .deal_box").html(html);

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function pageBack() {
        appUtils.pageInit("drainage/userLoginWx", "drainage/userInvitationWx");
    }
    var userInfo = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userInfo;
});
