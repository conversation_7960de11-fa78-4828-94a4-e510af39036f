// 交易记录详情 - 分红
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            _pageId = "#highEnd_trsDetailsProfit ";
    var tools = require("../common/tools");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        var param = appUtils.getPageParam();
        //分红方式交易详情查询 14301 红利再投
        if (param.sub_busi_code_e == "14301") {
            $(_pageId + " .dividend_vol_text").html("分红");
            $(_pageId + " .dividends_vol_name").html("分红份额");
            $(_pageId + " .dividend_vol_amt_item").show();
            $(_pageId + " .remark").show();
            $(_pageId + " .apponit_box").hide();
            
        } else {
            $(_pageId + " .dividend_vol_text").html("分红");
            $(_pageId + " .dividends_vol_name").html("分红");
            $(_pageId + " .apponit_box").show();
            $(_pageId + " .dividend_vol_amt_item").hide();
            $(_pageId + " .remark").hide();
        }
        reqFun102086(param);

    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    //分红：
    function reqFun102086(param) {
        service.reqFun102086(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);
            //交易状态
            var trans_status = results.trans_status;
            var trans_status_name = tools.fundDataDict(trans_status, "pri_bonus_trans_status_name");
            //产品名称
            var prod_name = results.prod_name;
            //产品代码
            var prod_code = "(" + results.prod_code + ")";
            //分红份额单位
            let dividend_method_remark = results.dividend_method == '14301' ? '份' : '元'
            //份额对应金额 14301
            let dividend_vol_amt = tools.fmoney(results.dividend_amt) + '元'
            //分红
            var dividend_amt = tools.fmoney(results.dividend_method == '14301' ? results.dividend_vol : results.dividend_amt);
            dividend_amt = dividend_amt + dividend_method_remark;
            //交易流水号
            var trans_serno = results.trans_serno;
            //分红日期
            var dividend_date = results.dividend_date;
            if (dividend_date != "--") {
                dividend_date = tools.ftime(dividend_date.substring(4, 8));
            }
            //分红方式
            var dividend_method = results.dividend_method;
            var dividend_method_name = tools.fundDataDict(dividend_method, "dividend_method");
            if (dividend_method == "14302" || dividend_method == "14303") {
                $(_pageId + " #bonus_arrive_box").show();
            } else {
                $(_pageId + " #bonus_arrive_box").hide();
            }
            //到账日期
            var to_account_date = results.to_account_date;
            if (to_account_date && to_account_date != "--") {
                to_account_date = tools.ftime(to_account_date.substring(4, 8));
            }else if(results.to_account_num){
                to_account_date = results.to_account_num;
            }
            //产品类型
            // if (param.prod_sub_type2 == "90") { // 普通私募
            //     $(_pageId + " .end_text").html(results.to_account_num + "个工作日回款到晋金宝");
            // } else { //其他
            //     $(_pageId + " .end_text").html("<span class='date'>" + tools.ftime(results.to_account_date.substring(4, 8)) + "</span>资金到账，回款到晋金宝");
            // }
            $(_pageId + " #bonus_arrive").html(dividend_method == '14302' ? '银行卡' : '晋金宝');
            $(_pageId + " .remarkType").html(dividend_method == '14302' ? '资金到账，回款到银行卡' : '资金到账，回款到晋金宝')
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #prod_code").html(prod_code);
            $(_pageId + " .dividend_amt").html(dividend_amt);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " .dividend_date").html(dividend_date);
            $(_pageId + " #dividend_method").html(dividend_method_name);
            $(_pageId + " #to_account_date").html(to_account_date);
            $(_pageId + " .dividend_vol_amt").html(dividend_vol_amt);
            
        });
    }


    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " .dividend_vol").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " .dividend_date").html("--");
        $(_pageId + " #dividend_method").html("--");
        $(_pageId + " .dividend_vol_text").html("");
        $(_pageId + " .end_text").html("");
        $(_pageId + " .dividend_amt").html("--");
        $(_pageId + " .apponit_box").hide();
        $(_pageId + " .dividend_vol_amt_item").hide();
        $(_pageId + " .remark").hide();

    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
