// 忘记密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        putils = require("putils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        endecryptUtils = require("endecryptUtils"),
        monkeywords = require("mall/scripts/common/passwordKeywords"),
        common = require("common"),
        gconfig = require("gconfig"),
        _pageId = "#safety_userPassword";
    var tools = require("../common/tools");
    var platform = require("gconfig").platform;
    var global = gconfig.global;
    var i = 120;
    var timer = "";
    var resultYzm = "";
    var isSendPhone = false;
    var Millisecond;
    var external = require("external");

    var backPage = "";

    function init() {
        backPage = appUtils.getSStorageInfo("_prePageCode");
        common.systemKeybord(); // 解禁系统键盘
        $(_pageId + " #newPwd").val("");
        $(_pageId + " #newPwd2").val("");
        isSendPhone = false;
        //清空定时器
        window.clearInterval(timer);
        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.css("background-color", "#C1E3B6");
        $yzm.html("获取验证码");
        setImgCode();
        initYanZma();
        i = 120;
        $(_pageId + " #phoneNum").val("");
        $(_pageId + " #verificationCode").val("");
    }
    /**
     * 渲染密码
     */
    function setPassword(_fatherPageId,_childPageId){
        let spacing = platform == 1 ? 0.06 : 0.075
        let pwd = $(_pageId + _childPageId).val();
        if(!pwd){
            $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#aaa');
            $(_pageId + _fatherPageId + " .placeholderPsd").html('请输入密码');
            $(_pageId + _fatherPageId + " .cursor-bink").css("left",'1rem');
            return
        }  
        $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#666');
        let length = pwd.length*1
        if(length > 16) return
        let str = ''
        for(let i = 0; i<length; i++){
            str = str + '*'
        }
        $(_pageId + _fatherPageId + " .placeholderPsd").html(str);
        $(_pageId + _childPageId ).val(pwd);
        // let leftValue = 1 + (length*spacing)
        let leftValue = ($(_pageId + _fatherPageId + " .placeholderPsd")[0].clientWidth/100) + 0.9;
        $(_pageId + _fatherPageId + " .cursor-bink").css("left",leftValue+'rem');
    }
    function moneyboardEvent(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #newPwd"),
            endcallback: function () {
                setPassword(" #new_password"," #newPwd")
            },
            inputcallback: function () {
                setPassword(" #new_password"," #newPwd")
            },
            keyBoardHide: function () {
                setPassword(" #new_password"," #newPwd")
            }
        })
    }
    function moneyboardEvent1(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #newPwd2"),
            endcallback: function () {
                setPassword(" #new_password_repeat"," #newPwd2")
            },
            inputcallback: function () {
                setPassword(" #new_password_repeat"," #newPwd2")
            },
            keyBoardHide: function () {
                setPassword(" #new_password_repeat"," #newPwd2")
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        // 切换图形验证码
        appUtils.bindEvent($(_pageId + " #getCode"), function () {
            setImgCode();
        });
        // 验证码输入控制
        appUtils.bindEvent(_pageId + " #verificationCode", function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        // 手机号输入控制
        appUtils.bindEvent(_pageId + " #phoneNum", function () {
            var curVal = this.value;
            if (curVal.length == "11") {
                var param = {
                    mobile: curVal,
                };
                if (!validatorUtil.isMobile(curVal)) {
                    return;
                }
                service.reqFun102005(param, function (data) {
                    if (data.error_no == "-********") {//******** 账号已注册
                    } else if (data.error_no == "-********") {//黑名单
                        layerUtils.iAlert("网络繁忙,请稍后重试!");
                    } else if (data.error_no == "-10200504") {//晋金所已开户
                        layerUtils.iAlert("该手机号未注册");
                    } else if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        }, "input");

        //新密码获得焦点修改提示
        appUtils.bindEvent(_pageId + " #newPwd", function () {
            $(this).attr("placeholder", "6-16位字母，数字，字符组合");
        }, "focus");
        //新密码失去焦点修改提示
        appUtils.bindEvent(_pageId + " #newPwd", function () {
            $(this).attr("placeholder", "请输入新密码");
        }, "blur");
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " #new_password .cursor-bink").hide()
            $(_pageId + " #new_password_repeat .cursor-bink").hide()
            monkeywords.close();
        });
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #new_password"), function (event) {
            event.stopPropagation();
            $(_pageId + " #new_password .cursor-bink").show()
            $(_pageId + " #new_password_repeat .cursor-bink").hide()
            moneyboardEvent()
            // $(_pageId + " #newPwd").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "safety_userPassword";
            param["eleId"] = "newPwd";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //二次确认弹出
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #new_password_repeat"), function (event) {
            event.stopPropagation();
            $(_pageId + " #new_password .cursor-bink").hide()
            $(_pageId + " #new_password_repeat .cursor-bink").show()
            moneyboardEvent1()
            // $(_pageId + " #newPwd2").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "safety_userPassword";
            param["eleId"] = "newPwd2";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //点击确定
        appUtils.bindEvent(_pageId + " #nextStep", function () {
            var phoneNum = $(_pageId + " #phoneNum").val();
            var yzm = $(_pageId + " #verificationCode").val();
            var isSend = $(_pageId + " #getYzm").attr("data-state");
            var newPwd = $(_pageId + " #newPwd").val();
            var newPwd2 = $(_pageId + " #newPwd2").val();
            var result = checkInput(newPwd, newPwd2);
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (!validatorUtil.isMobile(phoneNum)) {
                layerUtils.iMsg(-1, "您输入的手机号有误");
                return;
            }
            if (result) {
                //密码加密
                service.getRSAKey({}, function (data) {
                    if (data.error_no != "0") {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var modulus = data.results[0].modulus;
                    var publicExponent = data.results[0].publicExponent;
                    newPwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, newPwd);
                    var param = {
                        new_login_pwd: newPwd,
                        sms_mobile: phoneNum,
                        sms_code: yzm,
                        registered_mobile: phoneNum
                    }

                    service.reqFun102005({mobile: phoneNum}, function (data) {
                        if (data.error_no == "-********") { //黑名单
                            layerUtils.iLoading(false);
                            layerUtils.iAlert("网络繁忙,请稍后重试!");
                        } else if (data.error_no == "-10200504") {//晋金所已开户
                            layerUtils.iLoading(false);
                            layerUtils.iAlert("该手机号未注册");
                        } else if (data.error_no == "-********") {//******** 账号已注册
                            changePwd(param);
                        } else if(data.error_no != "0") { //已注销或者其他错误
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info);
                        }

                    }, {isLastReq: false});
                }, {isLastReq: false});
            }
        });

        //点击获取验证码
        appUtils.bindEvent(_pageId + " #getYzm", function () {
            //解决安卓图形验证码session不一致问题 2018-1-12
            // if (require("gconfig").platform == "1") {
            //     var Param = {
            //         "funcNo": "50502",
            //         "moduleName": "mall",
            //         "url": global.serverUrl
            //     };
            //     external.callMessage(Param);
            // }
            var imgCode = $(_pageId + " #tuxingCode").val();
            var phoneNum = $(_pageId + " #phoneNum").val();
            var data = $(this).attr("data-state");

            if (!imgCode) {
                layerUtils.iMsg(-1, "请输入图形验证码");
                return
            }
            if (!phoneNum) {
                layerUtils.iMsg(-1, "请输入手机号码");
                return
            }
            if (!validatorUtil.isMobile(phoneNum)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            if (data == "false") {
                return
            }

            window.clearInterval(timer);
            //获取验证码
            var param = {mobile: phoneNum, ticket: imgCode};
            sendVerificationCode(param);
        });

        //点击密码管理
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

    }


    function destroy() {
        window.clearInterval(timer);
        $(_pageId + " #phoneNum").val("");
        setImgCode();
        $(_pageId + " #tuxingCode").val("");
        $(_pageId + "#verificationCode").val("");
    }

    function pageBack() {
        loginType = '1'
        appUtils.setSStorageInfo("loginType", '1');
        $(_pageId + " #new_password .cursor-bink").hide()
        $(_pageId + " #new_password_repeat .cursor-bink").hide()
        monkeywords.close();
        appUtils.pageBack();
    }

//=============================== 自定义方法，写在destroy后面 =======================================
    //获取语音验证码
    function getCodeOFTalk() {
        var mobile = $(_pageId + " #phoneNum").val();
        if (mobile) {
            var param = {
                "mobile_phone": mobile,
                "type": common.sms_type.resetLoginPwd,
                "send_type": "1",
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(_pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        } else {
            if (!mobile) {
                layerUtils.iMsg(-1, "请输入手机号");
            }
        }
    }

    //生成图形验证码
    function setImgCode() {
        service.reqFun1100005({}, function (data) {
            if (data.error_no == 0) {
                var base64 = data.results[0].base64;
                $(_pageId + " #getCode img").attr("src", base64);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     * 调用接口修改密码
     */
    function changePwd(param) {
        service.reqFun101019(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            //验证码重置
            var $code = $(_pageId + " #getYzm");
            window.clearInterval(timer);
            i = 120;
            initYanZma();
            $(_pageId + " #talkCode").show();

            if (error_no == "0") {
                var result = data.result;
                // 存登录号 和 登录加密密码 方便手势密码
                var paramlist = {
                    funcNo: "50042",
                    key: "account_password",
                    isEncrypt: "1",
                    value: param.registered_mobile + "_" + param.new_login_pwd
                };
                external.callMessage(paramlist);
                layerUtils.iAlert("密码修改成功", 0, function () {
                    appUtils.pageInit("safety/userPassword", "login/userLogin", {});
                });
            } else {
                setImgCode();
                $(_pageId + " #verificationCode").val("");
                initYanZma();
                layerUtils.iMsg(-1, error_info);
            }
        });
    }

    /**
     * 检测手机号是否注册
     */
    function sendVerificationCode(param) {
        service.reqFun102005(param, function (data) {
            var error_no = data.error_no;
            if (error_no == "-********") {//******** 账号已注册
                sendPhoneCode(param.mobile, param.ticket); //发送短信,获取验证码
            } else if (error_no == "-********") {//黑名单
                $(_pageId + " input").blur();
                layerUtils.iAlert("网络繁忙,请稍后重试!");
            }  else if (data.error_no == "-10200504") {//晋金所已开户
                layerUtils.iLoading(false);
                layerUtils.iAlert("该手机号未注册");
            } else if (error_no != "0") {
                layerUtils.iAlert(data.error_info);
            } else if(error_no == "0") {
                layerUtils.iLoading(false);
                layerUtils.iConfirm("手机号码未注册，是否去注册？", function () {
                    appUtils.setSStorageInfo("loginType",'0')
                    appUtils.pageInit("safety/userPassword", "login/userLogin", {});
                }, function () {
                    return;
                }, "好的", "取消");
            }
        }, {isLastReq: false});
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iMsg(-1, "登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iMsg(-1, "确认密码不能为空");
            return false;
        }

        if (pwd1 !== pwd2) {
            layerUtils.iMsg(-1, "两次密码不相同");
            return false;
        }

        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iMsg(-1, "登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iMsg(-1, "登录密码请输入6-16位字母和数字组合");
            return false;
        }
        return true;
    }

    /**
     * 发送手机验码
     * */
    function sendPhoneCode(mobile, ticket) {
        var param = {
            mobile_phone: mobile,
            type: common.sms_type.resetLoginPwd,
            ticket: ticket,
            send_type: "0"
        };
        service.reqFun9199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info,
                results = null;
            results = data.results;
            if (error_no == "0") {

                isSendPhone = true;
                timer = setInterval(function () {
                    shows();
                }, 1000);
                $(_pageId + " #talkCode").show();
            } else {
                isSendPhone = false;
                setImgCode();
                $(_pageId + " #tuxingCode").val("");
                $(_pageId + " #talkCode").hide();
                layerUtils.iAlert(error_info);
            }

        });
    }

    /**
     * 显示读秒
     * */
    function shows() {
        var $code = $(_pageId + " #getYzm");
        $code.attr("data-state", "false");//点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.css("background-color", "yellow");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            initYanZma();
            $(_pageId + " #talkCode").show();
        }
    }

    /*
     *初始化语音验证码
     * */
    function initYanZma() {
        monkeywords.close();
        $(_pageId + " #getYzm").text("获取验证码");
        $(_pageId + " #getYzm").attr("data-state", "true");
        $(_pageId + " #getYzm").css({backgroundColor: "#C1E3B6"})
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
        $(_pageId + " #new_password .placeholderPsd").css('color','#aaa');
        $(_pageId + " #new_password_repeat .placeholderPsd").css('color','#aaa');
        $(_pageId + " #new_password .cursor-bink").css("left",'1rem');
        $(_pageId + " #new_password_repeat .cursor-bink").css("left",'1rem');
        $(_pageId + " #new_password .cursor-bink").hide()
        $(_pageId + " #new_password_repeat .cursor-bink").hide()
        $(_pageId + " #new_password .placeholderPsd").html('请输入密码');
        $(_pageId + " #new_password_repeat .placeholderPsd").html('请输入密码');
    }

    var userPassword = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userPassword;
});
