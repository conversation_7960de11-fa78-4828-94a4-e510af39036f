// 交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#fixedInvestment_addResults ";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var tranType;//交易类型，01-一键买入  02-一键定投
    let showInfo = '';
    function init() {
        showInfo = appUtils.getPageParam();
        tranType = appUtils.getSStorageInfo("tranType");
        //页面埋点初始化
        tools.initPagePointData();
        if(showInfo.payMethod == '0'){
            $(_pageId + " .payMethodName").text('晋金宝')
        }else{
            $(_pageId + " .payMethodName").text(showInfo.payMethodName);
        }
        $(_pageId + " .timeText").text(showInfo.firstText+showInfo.secoundText);
        $(_pageId + " .nextdate").text(showInfo.nextdate);
        $(_pageId + " .money").text(tools.fmoney(showInfo.money));
        if(validatorUtil.isEmpty(showInfo.prod_sname)){
            $(_pageId + " .prod_sname").parent(".flex").hide();
            appUtils.setSStorageInfo("pageSource", "1");//1多基金定投
        }else {
            appUtils.setSStorageInfo("pageSource", "0");//1多基金定投
            $(_pageId + " .prod_sname").text(showInfo.prod_sname).parent(".flex").show();
        }
        appUtils.setSStorageInfo("fixed_investment_list",'0');
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转定投计划页面
        appUtils.bindEvent($(_pageId + " .btn_next"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-2);
            appUtils.setSStorageInfo("routerList", routerList);
            if (tranType == '02'){
                appUtils.clearSStorage("productInfo");
                appUtils.setSStorageInfo("singlePlan", '0');
            }else{
                appUtils.setSStorageInfo("singlePlan", '1');
            }
            appUtils.pageInit("login/userIndexs", "fixedInvestment/investmentList");
        });
    }
    function pageBack() {
        var routerList = appUtils.getSStorageInfo("routerList");
        routerList.splice(-1);
        appUtils.setSStorageInfo("routerList", routerList);
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    	$(_pageId + " .timeText").text('--');
        $(_pageId + " .nextdate").text('--');
        $(_pageId + " .money").text('--');
        $(_pageId + " .prod_sname").text('--');
        showInfo = '';
    }
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
