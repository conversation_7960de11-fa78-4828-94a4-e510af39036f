// 身份证信息验证
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_yanzheng ";
        _pageCode = "safety/yanzheng";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");

    function init() {
        
        var hkUserInfo = appUtils.getSStorageInfo("hkUserInfo");
        if (hkUserInfo) {
            appUtils.clearSStorage("hkUserInfo");
            $(_pageId + " #realName").val(hkUserInfo.name);
            $(_pageId + " #idCard").val(hkUserInfo.cardNum);
        } else {
            $(_pageId + " input").val("");
        }
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #tradeNum").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #jiatradeNum").addClass("active");
                    }
                } else {
                    passflag = "请输入交易密码";
                    $(_pageId + " #jiatradeNum").removeClass("active");
                }
                $(_pageId + " #jiatradeNum").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #tradeNum").val(shuru);
                    $(_pageId + " #jiatradeNum").text(passflag);
                }
            } // 键盘的输入事件
        };

        common.systemKeybord(); // 解禁系统键盘
        // user_id = appUtils.getSStorageInfo("custid");
        // custno = appUtils.getSStorageInfo("custNo");


    }

    //验证身份证号，姓名
    function nameYZ() {
        var realName = $(_pageId + " #realName").val();
        var cardNum = $(_pageId + " #idCard").val().toUpperCase();
        var SysTransPwd = $(_pageId + " #tradeNum").val();
        // var SysTransPwd = '123123'
        if ($.trim(realName) == "" || $.trim(realName) == null) {
            layerUtils.iMsg(-1, "姓名不能为空");
            return;
        }
        if (realName != ut.getUserInf().name) {
            layerUtils.iMsg(-1, "请输入本人姓名");
            return;
        }
        if ($.trim(cardNum).length <= 0) {
            layerUtils.iMsg(-1, "证件号码不能为空");
            return;
        }
        if (!validatorUtil.isCardID(cardNum)) {
            layerUtils.iMsg(-1, "证件号码格式错误");
            return;
        }
        if ($.trim(SysTransPwd) == "" || $.trim(SysTransPwd) == null) {
            layerUtils.iMsg(-1, "交易密码不能为空");
            return;
        }
        if ($.trim(SysTransPwd).length != 6) {
            layerUtils.iMsg(-1, "交易密码为6位数字");
            return;
        }
        service.reqFun1100001({"type": "cert_no", "value": cardNum}, function (data) {//校验身份证
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, "验证信息不通过,请重新验证!");
                return;
            }
            var hkUserInfo = {
                "name": realName,
                "cardNum": cardNum
            }
            appUtils.setSStorageInfo("hkUserInfo", hkUserInfo);
            changePwYZ();
        }, {isLastReq: false});
    }

    //验证交易密码
    function changePwYZ() {
        var SysTransPwd = $(_pageId + " #tradeNum").val();
        //密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no == "0") {
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                SysTransPwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, SysTransPwd);
                var param = {
                    "trans_pwd": SysTransPwd
                }
                var callBack = function (resultsVo) {
                    if (resultsVo.error_no == 0) {
                        appUtils.pageInit("safety/yanzheng", "safety/oldcardinfo");
                    } else {
                        layerUtils.iAlert(resultsVo.error_info);
                    }
                };
                service.reqFun101025(param, callBack)
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        }, {isLastReq: false});


    }

    //绑定事件
    function bindPageEvent() {
        //点击开启键盘
        appUtils.bindEvent($(_pageId + " #tradeNum1"), function () {
            if ($(_pageId + " #tradeNum").val() == "") {
                $(_pageId + " #jiatradeNum").removeClass("unable");
            } else {
                $(_pageId + " #jiatradeNum").removeClass("unable").addClass("active");
            }
            kaiqi("tradeNum");
        }, "click");
        //支持的银行卡
        appUtils.bindEvent($(_pageId + " .supportedBankCards"), function () {
            let routerList = appUtils.getSStorageInfo("routerList");
            routerList = routerList.join(',');
            routerList = routerList.replace('safety/bankInfo,','')
            routerList = routerList.split(',')
            appUtils.setSStorageInfo("routerList",routerList);
            let hkUserInfo = {
                "name":  $(_pageId + " #realName").val(),
                "cardNum": $(_pageId + " #idCard").val()
            }
            appUtils.setSStorageInfo("hkUserInfo", hkUserInfo);
            appUtils.setSStorageInfo("isShowChangeBank",'0')
            appUtils.pageInit(_pageCode, "safety/bankInfo",{
                // isShowChangeBank:1
            });
        });
        //
        appUtils.bindEvent($(_pageId + " #realName"), function () {
            $(_pageId + " #jiatradeNum").removeClass("active").addClass("unable");
            guanbi();
        }, "focus");
        //
        appUtils.bindEvent($(_pageId + " #idCard"), function () {
            $(_pageId + " #jiatradeNum").removeClass("active").addClass("unable");
            guanbi();
        }, "focus");

        //失去焦点然后就关闭键盘
        appUtils.bindEvent($(_pageId + " #tradeNum"), function () {
            guanbi()
        }, "blur");

        //关闭换卡必读
        appUtils.bindEvent($(_pageId + " .grid_02 a"), function () {
            $(_pageId + " .card_rules").hide();
            $(_pageId + " .pop_layer4").hide();
        });
        //换卡必读
        appUtils.bindEvent($(_pageId + " .right_btn"), function () {
            $(_pageId + " .card_rules").show();
            $(_pageId + " .pop_layer4").show();
        });
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //下一步
        appUtils.bindEvent($(_pageId + " #xyb"), function () {
            nameYZ();
        });

        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            tools.openCamera("zm");
            return;
            var external = require("external");
            var Param = {
                "funcNo": "60302",
                "moduleName": "mall"
            };
            external.callMessage(Param);
        });
    }

    //页面清理
    function clearPage() {
        $(_pageId + " input").attr("value", "");
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);

    }

    function kaiqi(jjb_pwd) {
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_yanzheng";
        param["eleId"] = jjb_pwd;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param)
    }

    function destroy() {
        guanbi();
        $(_pageId + " #jiatradeNum").text("请输入交易密码");
        $(_pageId + " #tradeNum").val("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
