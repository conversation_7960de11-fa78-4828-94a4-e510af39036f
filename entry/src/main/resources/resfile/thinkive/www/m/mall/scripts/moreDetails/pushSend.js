//消息推送
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        _pageId = "#moreDetails_pushSend ";

    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        var template_id = appUtils.getPageParam("template_id");
        if (template_id != null && template_id != "") {
            service.reqFun102048({"template_id": template_id}, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var title = result[0].title;
                    var description = result[0].description;
                    $(_pageId + " .no_padding #content #sendTitle").html(title);
                    $(_pageId + " .no_padding #content #send_content").html(description);
                }
            })
        }
        var myDate = new Date();
        var year = myDate.getFullYear();
        var month = myDate.getMonth() + 1;
        var day = myDate.getDate();
        $(_pageId + " #content #nowDate").html(year + "年" + month + "月" + day + "日");
    }

    // 绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageInit("moreDetails/pushSend", "login/userIndexs");
    }

    var pushSend = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = pushSend;
});
