<div class="page" id="safety_setOtherName" data-pageTitle="设置别名">
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">设置别名</h1>
            </div>
        </header>
        <article>
            <!-- FORGET_PSD START -->
            <div class="forget_psd">
                <div class="check_tips slidedown in">
                    <p id="userInfo">您好！您正在为账户 设置别名。</p>
                </div>
                <div class="grid_03 grid_02 grid">
                    <div class="ui field text rounded input_box2" id="oldPwd11">
                        <label class="short_label">别名</label>
                        <input readonly="readonly" maxlength="20" max="20" id="oldPwd" type="text" class="ui input"
                               placeholder="别名为英文和数字组合" style="display:none"/>
                        <div class="simulate_input no_border">
                            <span class="unable" id="oldPwd1" style="padding: 0;"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid_02 mt20">
                <a href="javascript:void(0);" id="submit" class="ui button block rounded btn_register pop in">确定</a>
            </div>
            <!-- FORGET_PSD END -->
        </article>
    </section>
</div>
