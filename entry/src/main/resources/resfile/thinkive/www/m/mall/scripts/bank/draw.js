//部分支取
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_draw ";
    var gconfig = require("gconfig");
    var ut = require("../common/userUtil");
    var _pageCode = "bank/draw";
    var validatorUtil = require("validatorUtil");
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var sms_mobile = require("../common/sms_mobile");
    var hold_amt; //持有金额
    var bankElectornReservedMobile;
    var productInfo;// 产品信息
    var bankElectronName;
    var bankElectronNo;
    var acct_keep_min; //保留金额
    function init() {
        userInfo = ut.getUserInf();
        // tools.getPdf("1"); //获取协议
        sms_mobile.init(_pageId);
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        initProductInfo();
        getAccountInfo();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "bank/bankDetail");
        });
        //全部
        appUtils.bindEvent($(_pageId + " .allMoney"), function () {
            $(_pageId + " #money").val(hold_amt);
            $(_pageId + " #inputspanid span").html(tools.fmoney(hold_amt)).css({color: "#000000"}).addClass("active");
            getRrialAmount(hold_amt);
        });


        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "bank_draw";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            var money = $(_pageId + " #money").val().replace(/,/g, "");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                if (money <= 0 || !money) {
                    layerUtils.iAlert("请输入支取金额");
                    return;
                }
                if (parseFloat(money.replace(/,/g, "")) < parseFloat(productInfo.drw_low_amt)) {
                    layerUtils.iAlert("最小支取金额" + tools.fmoney(productInfo.drw_low_amt) + "元");
                    return;
                }
                if (money && productInfo.drw_low_amt && productInfo.drw_long_amt && Math.round((parseFloat(money.replace(/,/g, "")) - productInfo.drw_low_amt) % productInfo.drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("支取递增金额为" + tools.fmoney(productInfo.drw_long_amt) + "元");
                    return
                }
                if (money && acct_keep_min && (parseFloat(hold_amt * 100 - money * 100) < parseFloat(acct_keep_min * 100) && parseFloat(hold_amt * 100 - money * 100) != 0)) { //持有金额 - 输入金额 == 0 || 持有金额 - 输入金额 > 保留金额
                    layerUtils.iAlert("剩余金额不能低于" + tools.fmoney(acct_keep_min) + "元");
                    return
                }
                var param = {
                    "send_type": "0",
                    "mobile_phone": bankElectornReservedMobile,
                    "type": common.sms_type.bankDraw,
                    "bank_abbr": productInfo.prod_name
                };
                sms_mobile.sendPhoneCode(param)
            }
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var money = $(_pageId + " #money").val().replace(/,/g, "");

            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (money <= 0 || !money) {
                layerUtils.iAlert("请输入支取金额");
                return;
            }
            if (parseFloat(money.replace(/,/g, "")) < parseFloat(productInfo.drw_low_amt)) {
                layerUtils.iAlert("最小支取金额" + tools.fmoney(productInfo.drw_low_amt) + "元");
                return;
            }
            if (money && productInfo.drw_low_amt && productInfo.drw_long_amt && Math.round((parseFloat(money.replace(/,/g, "")) - productInfo.drw_low_amt) % productInfo.drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                layerUtils.iAlert("支取递增金额为" + tools.fmoney(productInfo.drw_long_amt) + "元");
                return
            }
            if (money && acct_keep_min && (parseFloat(hold_amt * 100 - money * 100) < parseFloat(acct_keep_min * 100) && parseFloat(hold_amt * 100 - money * 100) != 0)) { //持有金额 - 输入金额 == 0 || 持有金额 - 输入金额 > 保留金额
                layerUtils.iAlert("剩余金额不能低于" + tools.fmoney(acct_keep_min) + "元");
                return
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            //进行存入
            var trans_amt = $(_pageId + " #money").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                trans_amt: trans_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                prod_code: productInfo.prod_code,
                trans_type: "1",
                message_code: verificationCode,
                sms_code: verificationCode,
                sms_mobile: bankElectornReservedMobile,
                order_no: productInfo.order_no,
                brnd_sris: productInfo.brnd_sris,
            };
            trade(param);
        });
    }

    function trade(param) {
        service.reqFun151007(param, function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/drawResult", {
                trans_serno: trans_serno,
                bankElectronName: bankElectronName,
                bankElectronNo: bankElectronNo,
            });
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #money").val(moneys);
                if (parseFloat(moneys.replace(/,/g, "")) < parseFloat(productInfo.drw_low_amt)) {
                    layerUtils.iAlert("最小支取金额" + tools.fmoney(productInfo.drw_low_amt) + "元");
                    return;
                }
                if (moneys && productInfo.drw_low_amt && productInfo.drw_long_amt && Math.round((parseFloat(moneys.replace(/,/g, "")) - productInfo.drw_low_amt) % productInfo.drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("支取递增金额为" + tools.fmoney(productInfo.drw_long_amt) + "元");
                    return;
                }
                getRrialAmount(moneys.replace(/,/g, ""));
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #money").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (parseFloat(moneys) < parseFloat(productInfo.drw_low_amt)) {
                    layerUtils.iAlert("最小支取金额" + tools.fmoney(productInfo.drw_low_amt) + "元")
                    return;
                }
                if (moneys && productInfo.drw_low_amt && productInfo.drw_long_amt && Math.round((parseFloat(moneys.replace(/,/g, "")) - productInfo.drw_low_amt) % productInfo.drw_long_amt * 100) != 0) { // (当前金额 - 起投金额) % 递增金额 == 0
                    layerUtils.iAlert("支取递增金额为" + tools.fmoney(productInfo.drw_long_amt) + "元");
                    return;
                }
                getRrialAmount(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    curVal = curVal.substring(0, curVal.length - 1)
                    // $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }

                if (parseFloat(curVal) > parseFloat(hold_amt)) {
                    $(_pageId + " #money").val(hold_amt);
                } else {
                    $(_pageId + " #money").val(curVal);
                }
            }
        })
    }

    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                $(_pageId + " .bankIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "(尾号" + results.acct_no.substr(-4) + ")");
                // avail_amt = results.avail_amt;
                bankElectornReservedMobile = results.mobile_phone;
                $(_pageId + " .avail_amt").text(tools.fmoney(results.avail_amt) + "元");

                bankElectronName = results.bank_channel_name;
                bankElectronNo = results.acct_no;

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function initProductInfo() {
        $(_pageId + " .prod_sname").text(productInfo.prod_name);
        hold_amt = productInfo.hold_amt;
        $(_pageId + " #inputspanid span").text("可支取金额" + tools.fmoney(hold_amt) + "元");
        $(_pageId + " #inputspanid span").attr("text", "可支取金额" + tools.fmoney(hold_amt) + "元");
        $(_pageId + " .rate").text(tools.fmoney((productInfo.base_lnt_rate)) + "%");
        $(_pageId + " #money").val("");
        var params = {
            bank_channel_code: productInfo.bank_channel_code,
            prod_code: productInfo.prod_code
        };
        service.reqFun151117(params, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.prod_info) {
                    var prod_info = results.prod_info;
                    acct_keep_min = prod_info.acct_keep_min;
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function getRrialAmount(trans_amt) {
        if (!trans_amt) {
            $(_pageId + " .bank_advc_Int").text("--");
            $(_pageId + " .anlz_lnt_day").text("*");
            $(_pageId + " .trl_rate").text("--");
            $(_pageId + " .act_get_prncplnt").text("--");
            return;
        }
        if (trans_amt && acct_keep_min && (parseFloat(hold_amt * 100 - trans_amt * 100) < parseFloat(acct_keep_min * 100) && parseFloat(hold_amt * 100 - trans_amt * 100) != 0)) { //持有金额 - 输入金额 == 0 || 持有金额 - 输入金额 > 保留金额
            $(_pageId + " .bank_advc_Int").text("--");
            $(_pageId + " .anlz_lnt_day").text("*");
            $(_pageId + " .trl_rate").text("--");
            $(_pageId + " .act_get_prncplnt").text("--");
            layerUtils.iAlert("剩余金额不能低于" + tools.fmoney(acct_keep_min) + "元");
            return
        }
        service.reqFun151008({
            bank_channel_code: productInfo.bank_channel_code,
            prod_code: productInfo.prod_code,
            order_no: productInfo.order_no,
            brnd_sris: productInfo.brnd_sris,
            trans_amt: trans_amt,
        }, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                $(_pageId + " .bank_advc_Int").text(tools.fmoney(results.bank_advc_Int));
                $(_pageId + " .trl_rate").text(tools.fmoney(results.trl_rate + "") + "%");
                $(_pageId + " .act_get_prncplnt").text(tools.fmoney(results.act_get_prncplnt));
                $(_pageId + " .anlz_lnt_day").text(results.anlz_lnt_day);
            } else {
                $(_pageId + " .bank_advc_Int").text("--");
                $(_pageId + " .anlz_lnt_day").text("*");
                $(_pageId + " .trl_rate").text("--");
                $(_pageId + " .act_get_prncplnt").text("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        $(_pageId + " .prod_sname").html("--");//产品全称
        // $(_pageId + " .header_inner h1").text("--");
        $(_pageId + " .prod_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");//产品编码
        $(_pageId + " .fund_type_name").html("--");//产品编码
        $(_pageId + " .threshold_amount").html("--元起购");//产品编码
        $(_pageId + " .purchaseBtn").text("--");
        $(_pageId + " .isShow").hide();
        $(_pageId + " #inputspanid span").text("请输入支取金额").css({color: "#999999"}).removeClass("active");
        $(_pageId + " #inputspanid span").attr("text", "请输入支取金额");
        $(_pageId + " #money").val("");
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " .bankIcon img").attr("src", "");
        $(_pageId + " .bank_advc_Int").text("--");
        $(_pageId + " .trl_rate").text("--");
        $(_pageId + " .act_get_prncplnt").text("--");
        $(_pageId + " .anlz_lnt_day").text("*");
        $(_pageId + " .bank_electron_info").html("--");
        guanbi();
        sms_mobile.destroy();
        monkeywords.destroy();
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
