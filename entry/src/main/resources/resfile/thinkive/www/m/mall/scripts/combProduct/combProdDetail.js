// 产品详情模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "combProduct/combProdDetail",
        _pageId = "#combProduct_combProdDetail";
    require("chartsUtils");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var ut = require("../common/userUtil");
    let publicOfferingDetail //new 一个 vue 实例
    let productInfo;
    let activeClass;
    let activeClassTg;
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];
    var time1;  //观看秒数定时器获取
    var t1 = 0;
    var player = ''
    var isShow = false;
    var vipBenefitsTaskData, startTime, timer = null;
    // let shareData;
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '0',
                fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code)
            }
            // if(appUtils.getPageParam("busi_id")) productInfo.fund_code = appUtils.getPageParam("busi_id")
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res.template_content)
            })
        })
    }
    async function init() {
        
        if (timer) {
            clearTimeout(timer)
            timer = null;
        }
        $(_pageId + " .thfundBtn").hide();
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code)});
        vipBenefitsTaskData = appUtils.getPageParam();
        if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
            startTime = Date.now();
            var readingTime = vipBenefitsTaskData.duration && parseFloat(vipBenefitsTaskData.duration) * 1000;
            if (vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3')) {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, readingTime)
                }
            }
        }
        let html = await setTemplate() //拿到模板数据
        $(".main_combProdDetail").html(html)   //渲染模板
        activeClass = $(_pageId + " .chartContent").attr("activeClass") ? $(_pageId + " .chartContent").attr("activeClass") : 0;
        publicOfferingDetail = new Vue({
            el: '#main_combProdDetail',
            data() {
                return {
                    oss_url: global.oss_url,
                    is_special_pro: '',
                    // updateVesion:false,
                    publicDetatilData: {},//产品整合详情
                    fndmgrList: [],
                    spliceDate: -1100,//默认展示7天数据 // TODO:不知道干啥用
                    tips: '', //默认提示为累计收益走势
                    timeOptions: '',//绘制折线图配置
                    activeClass: activeClass,  //高亮时间（7天）
                    activeClassSecond: '1', //高亮业绩表现，历史净值
                    activeClassTg: '1',
                    initShowChat: "0", // 0 业绩走势 1 净值走势
                    moreName: "更多",
                    adjuestDetail: {},
                    targetprofit: '',
                    timeListNew: [  //区间
                        {
                            name: '近1个月',
                            section: "1"
                        },
                        {
                            name: '近3个月',
                            section: "3"
                        },
                        {
                            name: '近6个月',
                            section: "6"
                        },
                        {
                            name: '近一年',
                            section: "12"
                        },
                    ],
                    timeListMore: [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        // {
                        //     name: '近五年',
                        //     section: "60",
                        //     index: "5"
                        // },
                        {
                            name: '上线后',
                            section: "",
                            index: "6"
                        },
                    ]
                }
            },
            //视图 渲染前
            created() {
                //获取产品详情
                this.getPublicDetatils();
                // this.getTcData();

            },
            //渲染完成后
            mounted() {
                let prodType = $(_pageId + " .public_top").attr("prodType");
                this.targetprofit = $(_pageId + " .public_top").attr("targetprofit"); //是否为目标盈产品
                this.is_special_pro = $(_pageId + " .public_top").attr("is_special_pro") ? $(_pageId + " .public_top").attr("is_special_pro") : ''; //是否为特殊产品 标识 （折线图展示特殊）
                appUtils.setSStorageInfo("prodType", prodType);
                appUtils.setSStorageInfo("is_special_pro", this.is_special_pro);
                if (this.is_special_pro == '1') {
                    this.timeListMore = [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        {
                            name: '上线后',
                            section: "97",
                            index: "6"
                        },
                    ]
                } else {
                    this.timeListMore = [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        // {
                        //     name: '近五年',
                        //     section: "60",
                        //     index: "5"
                        // },
                        {
                            name: '成立来',
                            section: "99",
                            index: "6"
                        },
                    ]
                }
                // //获取业绩表现
                // this.getPerformance();
                this.reqFun102166(); // 获取持仓分布
                this.getHistory();//获取历史净值
                //判断是否展示业绩走势
                if ($(_pageId + " #trajectory").is(":visible")) this.initTrajectoryData(); // 是否调用业绩走势接口
                //是否展示业绩表现
                if ($(_pageId + " #performance").is(":visible")) this.getPerformance(); // 是否调用业绩表现接口

            },
            //计算属性
            computed: {
                //日期处理
                setTimeData: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        return time.substr(num, num1)
                    }
                },
                /**
                 * 格式化时间
                 */
                setTime: () => {
                    return (date, symbol) => {
                        if (!date) return '--'
                        return tools.ftime(date, symbol)
                    }
                },
                setDateText: () => {
                    return (str) => {
                        if (!str) return '--'
                        return tools.FormatDateText(str)
                    }
                },
                setNum1: () => {
                    return (str, len) => {
                        if (!str || str == '--') return '--'
                        return (+str).toFixed(len)
                    }
                },
                setNum2: ()=>{
                    return (str, len) => {
                        if (!str || str == '--') return '--'
                        return (+str).toFixed(len) + '%'
                    }
                }

            },
            //绑定事件
            methods: {
                //弹窗提示
                riskToast(id,title){
                    tools.recordEventData('1',id,title);
                    $(_pageId + " #" + id).show();
                },
                btnOk(){
                    tools.recordEventData('1','special_data_pop','关闭弹窗');
                    $(_pageId + " .special_data_pop").hide();
                },
                //获取历史净值
                getHistory() {
                    var params = {
                        comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code),
                        cur_page: "1",
                        num_per_page: "5"
                    }
                    service.reqFun102193(params, (data) => {
                        if (data.error_no == 0) {
                            let results = data.results[0].data;
                            if (!results || results.length == 0) {
                                return;
                            }
                            let html = "";
                            results.forEach(item => {
                                let end_date = item.end_date;
                                if (end_date && end_date != "--" && end_date.length == 8) {
                                    end_date = tools.ftime(end_date.substring(0, 8));
                                } else {
                                    end_date = '20210908'
                                    end_date = tools.ftime(end_date.substring(0, 8));
                                }
                                //单位净值 
                                let nav = item.nav;
                                if (nav != "--") {
                                    nav = (+nav).toFixed(4);
                                }

                                //累计净值
                                let accunav = item.accunav;
                                if (accunav != "--") {
                                    accunav = (+accunav).toFixed(4);
                                }

                                //日涨跌幅
                                let rateClass = "add";
                                let daily_return = item.daily_return;
                                if (daily_return != "--") {
                                    daily_return = (+daily_return).toFixed(2);
                                    if (daily_return == -0.00) daily_return = 0.00
                                    rateClass = this.addMinusClass(daily_return);
                                    daily_return = (+daily_return).toFixed(2) + "%";
                                }

                                html += '<div class="item">' +
                                    '<span>' + end_date + '</span>' +
                                    '<span class="">' + nav + '</span>' +
                                    '<span class="">' + accunav + '</span>' +
                                    '<span class=' + rateClass + '>' + daily_return + '</span>' +
                                    '</div>';
                            })
                            $(_pageId + " #historyContent .list_content").html(html);
                            if ($(_pageId + ' #performanceContent').length > 0) {
                                //存在业绩表现
                                $(_pageId + " #historyContent").hide();
                            } else {
                                $(_pageId + " #historyContent").show();
                            }
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //跳转定投计算器
                pageCalculator() {
                    tools.recordEventData('1','calculator','定投计算器');
                    if (!common.loginInter(_pageCode)) return;
                    appUtils.pageInit(_pageCode, "fixedInvestment/calculator");
                },
                // 点击定投收益
                investmentIncome(index) {
                    $(_pageId + " #performanceContent").hide();
                    $(_pageId + " #historyContent").hide();
                    $(_pageId + " #riskIndicatorsContent").hide();
                    $(_pageId + " #investmentYield").show();
                    tools.recordEventData('1','investmentYield','定投收益');
                    this.activeClassSecond = index;
                },
                //获取分享状态
                async getPageShareStatus(is_share) {
                    let data = {
                        busi_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : this.publicDetatilData.comb_code,
                        page_type: '8',
                        pageId: _pageId,
                        pageCode: _pageCode,
                    }
                    if(vipBenefitsTaskData && vipBenefitsTaskData.activity_id) data.activity_id = vipBenefitsTaskData.activity_id;
                    tools.isShowShare(data, is_share, vipBenefitsTaskData, startTime)
                },
                async getTcData() {
                    //根据个数不同取不同的top值渲染
                    let topData = {
                        "1":"70%",
                        "2":"65%",
                        "3":"60%",
                        "4":"55%",
                        "5":"50%",
                        "6":"45%"
                    }
                    service.reqFun102181({
                        comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code)
                    }, async (data) => {
                        if (data.error_no == '0') {
                            var result = data.results[0];
                            if (data.results.length == 0) {
                                $(_pageId + " #has_data").hide();
                                $(_pageId + " #adjustDynamicContent").html("<div class='text-center m_padding_10_10'>暂无数据</div>")
                                return;
                            }
                            $(_pageId + " #has_data").show();
                            this.adjuestDetail = result;
                            this.adjuestDetail.adjustDate = tools.ftime(this.adjuestDetail.adjustDate.substr(0, 8))
                            if (this.adjuestDetail.preList && this.adjuestDetail.preList.length) {
                                let preOption = combTcAdjuest;
                                let perArr = [];
                                this.adjuestDetail.preList.forEach(item => {
                                    perArr.push({ value: (item.proportion * 100).toFixed(2), name: item.investment_name })
                                })
                                preOption.legend.top = topData[this.adjuestDetail.preList.length+''];
                                preOption.series[0].data = perArr;
                                preOption.legend.formatter = (name) => {
                                    var target;
                                    for (var i = 0, l = perArr.length; i < l; i++) {
                                        if (perArr[i].name == name) {
                                            target = perArr[i].value;
                                        }
                                    }
                                    return name + '：' + target + '%';
                                }
                                let dom = $(_pageId + " #tc_before")[0];
                                let myChart = echarts.init(dom, null, {
                                    renderer: 'canvas',
                                    useDirtyRect: false
                                });
                                if (preOption && typeof preOption === 'object') {
                                    myChart.setOption(preOption);
                                }
                            }
                            if (this.adjuestDetail.list && this.adjuestDetail.list.length) {
                                let option = combTcAdjuest;
                                let arr = [];
                                this.adjuestDetail.list.forEach(item => {
                                    arr.push({ value: (item.proportion * 100).toFixed(2), name: item.investment_name })
                                })
                                option.legend.top = topData[this.adjuestDetail.list.length+''];
                                option.series[0].data = arr;
                                option.legend.formatter = (name) => {
                                    var target;
                                    for (var i = 0, l = arr.length; i < l; i++) {
                                        if (arr[i].name == name) {
                                            target = arr[i].value;
                                        }
                                    }
                                    return name + '：' + target + '%';
                                }
                                let dom = $(_pageId + " #tc_after")[0];
                                let myChart = echarts.init(dom, null, {
                                    renderer: 'canvas',
                                    useDirtyRect: false
                                });
                                if (option && typeof option === 'object') {
                                    myChart.setOption(option);
                                }
                            }

                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //获取业绩表现
                getPerformance() {
                    var isShowAnnualized = $(_pageId + " #performanceContent").attr("data-show-annualized") == 1 ? true : false; // 是否展示年化收益率
                    service.reqFun102170({ comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code }, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            var dataArr = ["近一月", "近三月", "近半年", "近一年"];
                            //列表数据重构
                            var numData = [
                                {
                                    //月涨跌幅
                                    riseRange: results.month,
                                    //月年化收益
                                    annualizedIncome: results.month_annualized
                                },
                                {
                                    //三月涨跌幅
                                    riseRange: results.season,
                                    //月年化收益
                                    annualizedIncome: results.season_annualized
                                },
                                {
                                    //半年涨跌幅
                                    riseRange: results.six_month,
                                    //半年年化收益
                                    annualizedIncome: results.six_month_annualized
                                },
                                {
                                    //一年涨跌幅
                                    riseRange: results.year,
                                    //一年年化收益
                                    annualizedIncome: results.year_annualized
                                }
                            ];

                            //近一周
                            // numData.push(results.week);
                            //近一月
                            // numData.push(results.month);
                            //近三月
                            // numData.push(results.season);
                            //近半年
                            // numData.push(results.six_month);
                            //近一年
                            // numData.push(results.year);
                            //涨跌幅
                            var html = "";
                            for (var i = 0; i < numData.length; i++) {

                                //空数据处理
                                numData[i].riseRange = tools.FormatNull(numData[i].riseRange);  //处理涨跌幅福

                                //涨跌幅
                                var riseRangClass = "add";
                                var rate = numData[i].riseRange;
                                if (rate != "--") {
                                    riseRangClass = this.addMinusClass(numData[i].riseRange);
                                    rate = (+rate).toFixed(2);
                                    rate = rate + "%";
                                }
                                //年化收益率
                                if (isShowAnnualized) {
                                    //年化收益率需要展示
                                    numData[i].annualizedIncome = tools.FormatNull(numData[i].annualizedIncome); //处理年化收益
                                    var annualizedIncomeClass = "add";
                                    var annualizedIncome = numData[i].annualizedIncome;
                                    if (annualizedIncome != "--") {
                                        annualizedIncomeClass = this.addMinusClass(numData[i].annualizedIncome);
                                        annualizedIncome = (+annualizedIncome).toFixed(2);
                                        annualizedIncome = annualizedIncome + "%";
                                    }
                                }
                                html += '<div class="item">' +
                                    '<span class="m_width_50">' + dataArr[i] + '</span>' +
                                    '<span id="m_width_50" class=' + riseRangClass + '>' + rate + '</span>' +
                                    `${isShowAnnualized ? `<span id="m_width_50" class='${annualizedIncomeClass}'>${annualizedIncome}</span>` : ''}` +
                                    '</div>';
                            }
                            $(_pageId + " #performanceContent .list_content").html(html);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //选择7天 业绩表现，历史净值
                performance7(index) {
                    tools.recordEventData('1','performance7','选择7天 业绩表现');
                    $(_pageId + " #historyContent").hide();
                    $(_pageId + " #investmentYield").hide();
                    $(_pageId + " #riskIndicatorsContent").hide();
                    $(_pageId + " #performanceContent").show();
                    
                    this.activeClassSecond = index
                },
                riskIndicators(index){
                    tools.recordEventData('1','riskIndicators','风险');
                    $(_pageId + " #performanceContent").hide();
                    $(_pageId + " #historyContent").hide();
                    $(_pageId + " #investmentYield").hide();
                    $(_pageId + " #riskIndicatorsContent").show();
                    this.activeClassSecond = index
                },
                history7(index) {
                    tools.recordEventData('1','performance7','选择7天 历史净值');
                    $(_pageId + " #performanceContent").hide();
                    $(_pageId + " #investmentYield").hide();
                    $(_pageId + " #riskIndicatorsContent").hide();
                    $(_pageId + " #historyContent").show();

                    this.activeClassSecond = index
                },
                //获取产品详情
                async getPublicDetatils() {
                    let res = await this.getDetails()
                    let info = Object.assign(productInfo, res)
                    // let info = res;
                    appUtils.setSStorageInfo("productInfo", info);
                    appUtils.setSStorageInfo("comb_code", res.comb_code);
                    appUtils.setSStorageInfo("financial_prod_type", info.financial_prod_type);
                    // res.purchase_confirm = tools.FormatDateText(res.purchase_confirm.substr(4));//确认日
                    // res.view_revenue = tools.FormatDateText(res.view_revenue);//收益日


                    // //建议持有
                    // $(_pageId + " #tip_max_day").html(res.tip_max_day);
                    // //若持有不满
                    // $(_pageId + " #tip_min_day").html(res.tip_min_day);
                    // //赎回费率不低于
                    // $(_pageId + " #tip_max_rate").html(res.tip_max_rate);
                    // res.p_expected_yield = (+res.p_expected_yield).toFixed(2) + '%';
                    // res.qrDate = tools.FormatDateText(res.qrDate.substr(4));//确认日


                    // if (res.nav_date && res.nav_date != '--') res.nav_date = tools.ftime(res.nav_date).substring(5);
                    // if (res.nav != '--') res.nav = (+res.nav).toFixed(4) + "元";//单位净值
                    // if (res.scale_fe != '--') res.scale_fe = (res.scale_fe / 100000000).toFixed(2) + '亿元';
                    // if (res.scale_fund != '--') res.scale_fund = (res.scale_fund / 100000000).toFixed(2) + '亿元';
                    // res.scale_fe_date = tools.ftime(res.scale_fe_date.substr(4, 4))

                    this.publicDetatilData = res;
                    if (this.publicDetatilData.comb_navdate && this.publicDetatilData.comb_navdate != '--') { //是否存在净值日期字段
                        let formattedDate = `${'(' + this.publicDetatilData.comb_navdate.slice(4, 6)}-${this.publicDetatilData.comb_navdate.slice(6) + ')'}`;//处理日期数据
                        this.publicDetatilData.comb_navdate = formattedDate;
                    } else {
                        this.publicDetatilData.comb_navdate = '';
                    }
                    let observation_support = this.publicDetatilData.observation_support; //是否支持设置观察期
                    if (observation_support == '1') {
                        $(_pageId + " .progress_time").removeClass("observation_support_hide");
                        $(_pageId + " .progress_text").removeClass("observation_support_hide");
                        $(_pageId + " .progress_text").addClass("observation_support_show");
                        $(_pageId + " .progress_time").addClass("observation_support_show")
                    } else {
                        $(_pageId + " .progress_time").removeClass("observation_support_show");
                        $(_pageId + " .progress_text").removeClass("observation_support_show");
                        $(_pageId + " .progress_text").addClass("observation_support_hide");
                        $(_pageId + " .progress_time").addClass("observation_support_hide");
                    }
                    this.publicDetatilData.updateVesion = true;
                    // 查询是否测评
                    this.reqFun102178(res);
                    let is_share = $(_pageId + " .pro_share").attr("is_share")
                    if (is_share == "1") {
                        this.getPageShareStatus(is_share)
                    }
                    tools.setPageTop("#inclusive_jjThirtyDetail");
                },
                reqFun102178(productInfo) {
                    service.reqFun102178({
                        comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code)
                    }, async (data) => {
                        if (data.error_no == '0') {
                            var result = data.results[0];
                            if (result.risk_state && result.risk_state == '0') {
                                let _html = '';
                                result.question_list && result.question_list.forEach(item => {
                                    _html += `<li style="list-style: disc;">${item.question_name}：${item.answer}</li>`
                                })
                                if ($("#combProduct_combProdDetail").attr("data-display") == 'block') {
                                    //埋点ID
                                    let operationId = 'investmentIntention'
                                    layerUtils.iConfirm(`<span style='text-align: left;padding:0;display:inline-block'>请您确认以下信息是否符合您的投资意向 <ul style="padding: 0 0.46rem;
                                text-align: left; margin-top: -0.2rem;margin-bottom: -0.2rem;color: #2e2e2e;font-size: 16px">${_html}</ul></span>`, () => {
                                        pageBack();
                                    }, () => {
                                        var param101088 = {
                                            tg_cust_question_result: result.answer,
                                            // tg_cust_question_result: "B,B",
                                            cust_type: "1",
                                            comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code)
                                        };
                                        service.reqFun101088(param101088, function (data) {
                                            if (data.error_no == "0") {

                                            } else {
                                                layerUtils.iMsg(-1, data.error_info);
                                            }
                                        });
                                    }, "返回", "确认",operationId);
                                }
                            }
                            //是否为投顾系列产品
                            let isSeriesComb = appUtils.getSStorageInfo("isSeriesComb");
                            if (isSeriesComb != '1') tools.initCombFundBtn(productInfo, _pageId);
                            // layerUtils.iConfirm.close_pop();
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //获取产品详情
                async getDetails() {
                    return new Promise(async (resolve) => {
                        let data = {
                            comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code)
                        }
                        service.reqFun102165(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                chooseTrajectoryData(item) {
                    if (item.section == this.activeClass) {
                        return;
                    }
                    this.activeClass = item.section
                    this.moreName = "更多"
                    if (this.initShowChat == 1) {
                        tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                        this.earningsChart1(); // 获取净值走势
                    } else if (this.initShowChat == 0) {
                        tools.recordEventData('1','initTrajectoryData_'+item.section,item.name+'-业绩走势');
                        this.initTrajectoryData(); // 获取业绩走势
                    }
                },
                chooseMoreList(item) {
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    this.moreName = item.name;
                    this.activeClass = item.section;
                    if (this.initShowChat == 1) { // 净值走势
                        tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                        this.earningsChart1();
                    } else { // 业绩走势
                        tools.recordEventData('1','initTrajectoryData_'+item.section,item.name+'-业绩走势');
                        this.initTrajectoryData(); // 获取业绩走势
                    }
                },

                initializationData() {
                    this.activeClass = 0
                    this.fixEarningsChart()
                },

                // 点击获取单位净值走势
                initializationData7() {
                    $(_pageId + " #navTrend").show();
                    $(_pageId + " #trajectory").hide();
                    this.initShowChat = 1;
                    tools.recordEventData('1','earningsChart','净值走势');
                    this.earningsChart1()
                },
                // 净值走势 新
                earningsChart1() {
                    section = this.activeClass;
                    if (!section || section == '') section = '99'
                    service.reqFun102194({ comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code), section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        // results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        //数据颠倒
                        results[0].date = '"' + JSON.stringify(JSON.parse(results[0].date.slice(1, -1)).reverse()) + '"';
                        results[0].nav = '"' + JSON.stringify(JSON.parse(results[0].nav.slice(1, -1)).reverse()) + '"';
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: function (params) {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    return `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;">${year}-${month}-${day}</div><div class="chart_tooltip_item" style="margin-top:5px;height:20px;">单位净值：${parseFloat(params[0].value).toFixed(4)}</div>`
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc"
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    /* formatter: function (e) {
                                         return tools.FormatDateText(e, 1);
                                         
                                     },*/

                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                splitNumber: 5,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: function (value, index) {
                                        return value.toFixed(4)
                                    }
                                }
                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '5%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                            },
                            series: [],
                        };
                        let series = [];

                        results.forEach((item, i) => {
                            item.nav = JSON.parse(item.nav.substring(1, item.nav.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                // name: item.indexName,
                                data: item.nav,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series
                        // 显示第一个和最后一个日期
                        // config.xAxis.axisLabel.interval = 244
                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = $(_pageId + " #combChartContainer1")[0];
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                // 获取业绩走势折线图
                initTrajectoryData(section) {
                    section = this.activeClass;
                    $(_pageId + " #navTrend").hide();
                    $(_pageId + " #trajectory").show();
                    this.initShowChat = 0;
                    service.reqFun102169({ comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code), section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer1").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer1").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: (params) => {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">本产品：</span><span style="color:${t.color}"><b>${(t.value * 100).toFixed(2)}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                hideDelay: 10
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                // axisLabel: {
                                //     formatter: '{value} %'
                                // },
                                splitNumber: 3,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: (value, index) => {
                                        return (value * 100).toFixed(2) + "%"
                                    }
                                },

                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '5%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                                // data: ['实际巡检', '计划巡检', '漏检次数'],
                            },
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                // name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series;
                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = $(_pageId + " #combChartContainer")[0];
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },

                addMinusClass(str) {
                    var numClass = "text_red";

                    if (str < 0) {
                        numClass = "text_green";
                    } else if (str > 0) {
                        numClass = "text_red";
                    } else {
                        numClass = "text_gray"
                    }
                    return numClass;
                },

                //设置时间为highChart所需时间格式
                datearr(data) {
                    for (var i = 0; i < data.length; i++) {
                        var x = data[i].x.toString();
                        Date.UTC()
                        data[i].x = Date.UTC(x.substring(0, 4), x.substring(4, 6) - 1, x.substring(6, 8));
                    }
                    return data;
                },

                //更多业绩表现
                page_performance() {
                    tools.recordEventData('1','performanceList','更多业绩表现');
                    var isShowAnnualized = $(_pageId + " #performanceContent").attr("data-show-annualized") == 1 ? true : false;
                    appUtils.setSStorageInfo("_isShowAnnualized", isShowAnnualized); //存储是否展示年化收益率
                    appUtils.pageInit(_pageCode, "inclusive/performanceList");
                },
                //更多历史净值
                page_history() {
                    tools.recordEventData('1','historyValueList','更多历史净值');
                    appUtils.pageInit(_pageCode, "inclusive/historyValueList");
                },
                // 查看详细持仓
                page_holdDistribution() {
                    tools.recordEventData('1','combHoldDistribution','查看详细持仓');
                    appUtils.pageInit(_pageCode, "combProduct/combHoldDistribution");
                },

                //交易规则
                page_rule() {
                    tools.recordEventData('1','tradingRules','交易规则');
                    appUtils.pageInit(_pageCode, "combProduct/buyingSellingRule", { targetprofit: this.targetprofit })
                },

                pop_prod_income() {
                    $(_pageId + " #prod_income").show();
                },
                pop_prod_sharp() {
                    $(_pageId + " #prod_sharp").show();
                },
                pop_prod_retreat() {
                    $(_pageId + " #prod_retreat").show();
                },
                close_pop() {
                    $(_pageId + " .special_data_pop").hide();
                },
                //获取持仓分布
                reqFun102166() {
                    var param = {
                        comb_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : (productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code),
                    }
                    service.reqFun102166(param, (data) => {
                        if (data.error_no == 0) {
                            if (data.results && data.results.length) {
                                let option = combHoldDistOption;
                                let arr = [];
                                data.results.forEach(item => {
                                    arr.push({ value: (item.proportion * 100).toFixed(2), name: item.investment_name })
                                })
                                option.series[0].data = arr;
                                option.legend.formatter = (name) => {
                                    var target;
                                    for (var i = 0, l = arr.length; i < l; i++) {
                                        if (arr[i].name == name) {
                                            target = arr[i].value;
                                        }
                                    }
                                    return name + '：' + target + '%';
                                }
                                let dom = $(_pageId + " #comb_chartContainer")[0];
                                let myChart = echarts.init(dom, null, {
                                    renderer: 'canvas',
                                    useDirtyRect: false
                                });
                                if (option && typeof option === 'object') {
                                    myChart.setOption(option);
                                }
                            }
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })

                },
                // 买入卖出状态
                reqFun102168() {

                },
                // 显示更多区间
                showMoreSpliceDate() {
                    //$(_pageId + " #tooltip").parent().css({ "z-index": "999" })
                    tools.recordEventData('1','moreSpliceDate','显示更多区间');
                    $(_pageId + " .thfundBtn").hide();
                    $(_pageId + " #moreSpliceDate").show();
                },
                cancelMoreSpliceDate() {
                    tools.recordEventData('1','thfundBtn','隐藏更多区间');
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    $(_pageId + " #moreSpliceDate").hide();
                },
                onClickPlayVideo() {
                    tools.recordEventData('1','onClickPlayVideo','点击视频');
                    isShow = true;
                    let param = this.publicDetatilData.video_info //存储数据格式
                    player = videojs('new_example_video', {
                    }, function onPlayerReady() {
                        //结束和暂时时清除定时器，并向后台发送数据
                        this.on('ended', function () {
                            window.clearInterval(time1);
                        });
                        this.on('pause', function () {
                            window.clearInterval(time1);
                        });
                        this.on('waiting', function () {
                            window.clearInterval(time1);
                        })
                        //开始播放视频时，设置一个定时器，每100毫秒调用一次aa(),观看时长加1秒
                        this.on('playing', function () {
                            window.clearInterval(time1);
                            time1 = setInterval(function () {
                                t1 += 1;
                                if (t1 >= 20) {
                                    //调用接口 清空时间 初始化时间0
                                    window.clearInterval(time1);
                                    t1 = 0;
                                    // seeVideo();
                                }
                            }, 1000);
                        });
                    });
                    t1 = 0;
                    window.clearInterval(time1);
                    // playVideoData = param;  //选中当前选择的播放视频
                    player.reset();
                    player.src({ src: param.video_path });
                    player.load(param.video_path);
                    $(_pageId + " video").attr("poster", param.cover_path);
                    $(_pageId + " #showVideo").show();
                },
                getBack() {
                    tools.recordEventData('1','closeVideo','暂停视频');
                    window.clearInterval(time1);
                    player.pause();
                    $(_pageId + " #showVideo").hide()
                },
                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                },
                pageMsgDetail(item) {
                    if (item.msg_type == '1') { // 文章
                        var param = item;
                        tools.recordEventData('1','prodMsgDetail','文章');
                        appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);
                        return;
                    } else if (item.msg_type == '2') {
                        let room_type = item.live_type; //直播间类型
                        sessionStorage.room_type = room_type;   //利用sessionStorage缓存类型
                        sessionStorage.msg_title = item.live_type_desc;//直播间类型名称
                        //进入更多直播列表页面
                        var param = item;
                        tools.recordEventData('1','semihList','直播列表');
                        appUtils.pageInit(_pageCode, "liveBroadcast/semihList", param);
                    }
                },
                page_liveBroadcast() {
                    tools.recordEventData('1','liveBroadcast','学投资');
                    tools.setPageToUrl('thfund/inputRechargePwd', '5')
                    if (!common.loginInter()) return;
                    //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
                    if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
                    //页面跳转前存储当前时间戳
                    localStorage.toPageTime = new Date().getTime();
                    appUtils.pageInit(_pageCode, "liveBroadcast/index", {});
                },
                // 基金备选库
                page_alternative_fund() {
                    tools.recordEventData('1','combAlternativeFund','基金备选库');
                    appUtils.pageInit(_pageCode, "combProduct/combAlternativeFund");
                },
                holdDist(index) {
                    tools.recordEventData('1','holdDist','持仓分布');
                    $(_pageId + " #adjustDynamicContent").hide();
                    $(_pageId + " #holdDistContent").show();
                    this.activeClassTg = index
                },
                adjustDynamic(index) {
                    tools.recordEventData('1','adjustDynamic','调仓动态');
                    $(_pageId + " #holdDistContent").hide();
                    $(_pageId + " #adjustDynamicContent").show();
                    this.getTcData();
                    this.activeClassTg = index
                },
                page_tcHistory() {
                    tools.recordEventData('1','tcHistory','调仓历史');
                    appUtils.pageInit(_pageCode, "combProduct/combTcHistory");
                }
            },

        })

    }

    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .close"), function () {
            // tools.recordEventData('1','share_WeChat','微信好友分享',{activityId:chooseData.activity_id,shareTemplate:chooseData.share_template});
            tools.recordEventData('1','close_video','关闭视频弹窗');
            window.clearInterval(time1);
            player.pause();
            $(_pageId + " #showVideo").hide()
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        // appUtils.bindEvent($(_pageId + " #cancelShare"), async function () {
        //     $(_pageId + " #pop_layer").hide()
        // });
        // //分享到微信
        // appUtils.bindEvent($(_pageId + " #share_WeChat"), async function () {
        //     // let data = {
        //     //     shareTypeList:'22'
        //     // }
        //     tools.pageShare(shareData,'22',_pageId,_pageCode)
        // });
        // //分享到朋友圈
        // appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), async function () {
        //     // let data = {
        //     //     shareTypeList:'23'
        //     // }
        //     tools.pageShare(shareData,'23',_pageId,_pageCode)
        // });
        // 点击风险提示，我知道了
        appUtils.bindEvent($(_pageId + " .risk_alert .know"), function () {
            tools.recordEventData('1','know','我知道了');
            hideRiskAlert()
        });
        //立即打开
        appUtils.bindEvent($("#open_tips .immediately-open"), function () {
            tools.recordEventData('1','immediately-open','立即打开');
            tools.jumpApp(pageParam);
        });
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #showVideo").hide();
        $(_pageId + " .thfundBtn").hide();
        $(_pageId + " #moreSpliceDate").hide();
        $(_pageId + " #has_data").hide()
        $(_pageId + " #adjustDynamicContent").hide();
        // $(_pageId + " #investmentYield").hide();
        window.clearInterval(time1);
        if (player) {
            player.pause();
        }
        if (timer) {
            clearTimeout(timer)
            timer = null;
        }
        vipBenefitsTaskData = ""; startTime = "";
        isShow = false;
        $(_pageId + " #share").removeAttr("has-share");
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        if (player) {
            player.pause();
        }
        if (timer) {
            clearTimeout(timer)
            timer = null;
        }
        if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3') && !tools.getStayTime(startTime, vipBenefitsTaskData.duration)) {
            var remainTime = tools.getRemainTime(startTime, vipBenefitsTaskData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                startTime = Date.now();
                vipBenefitsTaskData.duration = remainTime / 1000;
                let is_share = $(_pageId + " .pro_share").attr("is_share")
                let data = {
                    busi_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.comb_code,
                    page_type: '8',
                    pageId: _pageId,
                    pageCode: _pageCode
                }
                if(vipBenefitsTaskData && vipBenefitsTaskData.activity_id) data.activity_id = vipBenefitsTaskData.activity_id;
                tools.isShowShare(data, is_share, vipBenefitsTaskData, startTime)
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_pageCode, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }
    let combProdDetailModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = combProdDetailModule;
});
