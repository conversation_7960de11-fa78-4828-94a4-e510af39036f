// 高端版本认证首页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        ut = require("../common/userUtil"),
        service = require("mobileService"),
        _pageId = "#highVersion_custProducts ",
        _page_code = "highVersion/custProducts";
    var series_info_high,list;
    //判断当前版本是否是高端版
    let isHigh;
    //版本数据初始化
    var userChooseVerson,scene_code;
    function init() {
        //获取当前用户选中的版本
        userChooseVerson = common.getLocalStorage("userChooseVerson"); //判断用户是否登陆过
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        //获取当前用户初始版本
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        scene_code = scene_code ? scene_code : '';
        // scene_code = '3'
        if(userChooseVerson && userChooseVerson !=''){
            if(userChooseVerson == '3'){
                isHigh = true;
                $(_pageId).removeClass("standard_custProducts");
                $(_pageId + " article").addClass("high");
            }else{
                isHigh = false;
                $(_pageId + " article").removeClass("high");
                $(_pageId).addClass("standard_custProducts");
            }
        }else{
            if(scene_code == '3'){
                isHigh = true;
                $(_pageId).removeClass("standard_custProducts");
                $(_pageId + " article").addClass("high");
            }else{
                isHigh = false;
                $(_pageId + " article").removeClass("high");
                $(_pageId).addClass("standard_custProducts");
            }
        }
        // $(_pageId + " .list").show();
        series_info_high = appUtils.getSStorageInfo("series_info_high");
        let series_name = series_info_high.series_name;
        $(_pageId + ".series_name").text(series_name);
        //页面埋点初始化
        tools.initPagePointData();
        //获取页面数据列表
        pageInit();
    }
    function pageInit(){
        service.reqFun181009({series_id:series_info_high.series_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            list = datas.results;
            setList(list);
        })
    }
    //渲染列表
    function setList(list){
        let html = ``;
        if (!list || !list.length) return;
        list.forEach(item => {
            let fundCode = item.fund_code ? item.fund_code : '';
            var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
            var prod_per_min_amt = item.prod_per_min_amt //投资金额
            var prod_sname = item.prod_name_list ? item.prod_name_list : item.prod_sname ? item.prod_sname : item.prod_exclusive_name
            var this_year_rate = tools.fmoney(item.this_year_rate ? item.this_year_rate : item.annu_yield);
            var rate = tools.fmoney(item.rate);
            var transferable = item.transferable;//是否可转让
            var recommend_info = item.recommend_info;
            var prod_per_min_amt = item.prod_per_min_amt;
            var increase_term = item.increase_term;
            var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
            var str = "";
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + item.url + "' style='width:0.32rem;margin-left: 0.04rem;margin-top: -0.04rem;'>"
            }
            if (item.prod_sub_type2 == '200'){
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                // var income_period_list_chg = item.income_period_list_chg == '1' ? '' : 'display_none' //是否展示近X月涨跌幅
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                var risklevel_name = item.risklevel_name.split('(') ? item.risklevel_name.split("(")[0] : '--'
                //数据展示
                var preincomerate = item.preincomerate ? tools.fmoney(item.preincomerate) : '--' //年化标准
                var threshold_amount = item.threshold_amount ? item.threshold_amount : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                var nav = item.nav ? tools.fmoney(item.nav, 4) : '--'
                var holding_days = item.holding_days ? item.holding_days : '--'
                var per_yield = item.per_yield ? tools.fmoney(item.per_yield) : '--'
                var buy_state = item.buy_state
                var buy_state_name, btnClass;
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                    if(userChooseVerson && userChooseVerson !=''){
                        buy_state_name =  userChooseVerson == '3' ? `敬请<br>期待` : '敬请期待'
                    }else{
                        buy_state_name =  scene_code == '3' ? `敬请<br>期待` : '敬请期待'
                    }
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                html += `
                    <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                        <em style="display:none">${JSON.stringify(item)}</em>
                        <li class="main_flxe vertical_line flex_1 ${isHigh ? '' : 'display_none'}">
                            <p class="m_font_size16 color_000 prod_sname m_bold_500">${prod_sname}</p>
                            <p class="m_font_size12  flex">
                                <span class="high_fixed_width main_flxe vertical_line ${compare_benchmark_list}"> 
                                    <span class="high_color m_font_size18">${preincomerate}%</span>
                                    <span>业绩计提基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${performance_benchmarks_list}"> 
                                    <span class="high_color m_font_size18">${preincomerate}%</span>
                                    <span>业绩比较基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${nav_list}"> 
                                    <span class="high_color m_font_size18">${nav}</span>
                                    <span>单位净值</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                    <span class="high_color m_font_size18">${dk_income_rate}%</span>
                                    <span>${income_period_type_desc}年化</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                    <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                                    <span>${income_period_type_desc}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${per_yield_list}"> 
                                    <span class="high_color m_font_size18">${per_yield}%</span>
                                    <span>上一封闭期年化收益率</span>
                                </span>
                                <span class="main_flxe vertical_line flex_1">
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">建议持有${holding_days}天以上</span>
                                    <span><span>${risklevel_name}</span> | <span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                                </span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="main_flxe vertical_line ${isHigh ? 'display_none' : ''}"">
                            <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                <span class="${closed_period_list}">期限:${inrest_term}</span>
                                <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                                <span class="${recommended_holding_list}">建议持有${holding_days}天以上</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${btnClass}">
                                ${buy_state_name}
                            </span>
                        </li>
                    </ul>
                `

            }else if (item.prod_source == '2'){
                var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
                var prod_per_min_amt = item.prod_per_min_amt //投资金额
                var comb_sname = item.comb_sname;
                var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none'  //是否展示特点
                var corner_marker_list = item.corner_marker_list == '1' ? '' : 'display_none'  //是否展示角标
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none'  //是否展示策略特点
                var buy_deadline_list = item.buy_deadline_list == '1' ? '' : 'display_none';
                var page_threshold_amount_list = item.page_threshold_amount_list == '1' ? '' : 'display_none'; //是否展示营销页首投金额
                var comb_risk_name = item.comb_risk_name ? item.comb_risk_name : '--';
                //数据展示
                var page_first_per_min = item.page_first_per_min ? item.page_first_per_min : '';
                var income_period_type_desc = item.income_period_type ? item.income_period_type : '--' //近多少年化

                var annual_income = item.annual_income; // 目标年化收益
                var holding_time = item.holding_time ? item.holding_time : '--' // 建议持有时长
                var characteristic = item.characteristic; // 特点
                var strategic_characteristic = item.strategic_characteristic; // 策略特点   
                var threshold_amount = item.first_per_min ? item.first_per_min : '--' //起购金额
                var corner_marker = item.corner_marker; // 角标描述
                var mechanism = item.mechanism; // 投顾机构
                var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                var buy_state = item.purchase_state;
                var buy_state_name, btnClass;
                var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅
                var income_name = item.income_name ? item.income_name : ''; //目标收益率文案
                var holding_time_name = item.holding_time_name ? item.holding_time_name : '';
                var buy_deadline = item.buy_deadline ? item.buy_deadline : '';
                // threshold_amount = page_threshold_amount_list == '1' ? page_first_per_min : threshold_amount;
                if(buy_deadline){
                    buy_deadline = tools.ftime(buy_deadline.substr(4, 4), "月") + "日 " + tools.ftime(buy_deadline.substr(8, 8), ".").substr(0, 5)
                }
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                    if(userChooseVerson && userChooseVerson !=''){
                        buy_state_name =  userChooseVerson == '3' ? `敬请<br>期待` : '敬请期待'
                    }else{
                        buy_state_name =  scene_code == '3' ? `敬请<br>期待` : '敬请期待'
                    }
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "运作中";
                    btnClass = "sold_out";
                }
                html += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                    <em style="display:none">${JSON.stringify(item)}</em>
                    <li class="main_flxe vertical_line flex_1 ${isHigh ? '' : 'display_none'}">
                        <p class="m_font_size16 color_000 prod_sname m_bold_500">${comb_sname}</p>
                        <p class="m_font_size12  flex">
                            <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                <span class="high_color m_font_size18">${establish_rate}%</span>
                                <span>${income_period_type_desc}年化</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                <span class="high_color m_font_size18">${income_rate_chg}%</span>
                                <span>${income_period_type_desc}</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${annual_income_list}"> 
                                <span class="high_color m_font_size18">${annual_income}</span>
                                <span>${income_name}</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${buy_deadline_list}"> 
                                <span class="high_color m_font_size18">${buy_deadline}</span>
                                <span>截止时间</span>
                            </span>
                            <span class="high_fixed_width main_flxe vertical_line ${strategic_characteristic_list}"> 
                                <span class="high_color m_font_size18">${strategic_characteristic}</span>
                                <span>策略特点</span>
                            </span>
                            <span class="main_flxe vertical_line flex_1">
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">${holding_time_name}:${holding_time}</span>
                                <span>${comb_risk_name} | <span class="${page_threshold_amount_list}">起购:${page_first_per_min}元</span><span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                            </span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                            <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </li>
                    <li class="main_flxe vertical_line ${isHigh ? 'display_none' : ''}">
                            <p class="m_font_size16 color_000 prod_sname">${comb_sname}</p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${establish_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${annual_income_list}">${income_name}:<span class="m_text_red m_font_size18">${annual_income}</span></p>
                            <p class="m_font_size12 ${buy_deadline_list}">截止时间:<span class="m_text_red m_font_size14">${buy_deadline}</span></p>
                            <p class="m_font_size12 ${strategic_characteristic_list}">策略特点:<span class="m_text_red">${strategic_characteristic}</span></p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${page_threshold_amount_list}">起购:${page_first_per_min}元</span>
                                <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                <span class="${recommended_holding_list}">${holding_time_name}:${holding_time}</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                                <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${btnClass}">
                            ${buy_state_name}
                        </span>
                    </li>
                </ul>
            `
            }
        });
        
        $(_pageId + " .list").html(html);
        $(_pageId + " .list").show();
    }
    //去测评
    function pageTo_evaluation() {
        let operationId = "riskAssessment"
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击产品
        appUtils.preBindEvent($(_pageId + " .list"), " .classificationList_card_main", function () {
            var productInfo = JSON.parse($(this).find("em").text()); //存储数据格式
            tools.recordEventData('1','detail_' + productInfo.fund_code,'点击产品',{fund_code:productInfo.fund_code ? productInfo.fund_code : productInfo.comb_code});
            // console.log(productInfo)
            appUtils.setSStorageInfo("productInfo",productInfo);
            if (productInfo.prod_source == '2'){
                
                appUtils.setSStorageInfo("combProductInfo", productInfo);   //存储分类一级内容
                if (productInfo.prod_propagate_temp) {
                    tools.setPageToUrl('combProduct/combProdMarketing', '1')
                    
                } else {
                    tools.setPageToUrl('combProduct/combProdDetail', '1')
                }
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                // var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        let operationId = 'riskAssessment'
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            // tools.recordEventData('1','riskQuestion','风险测评');
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    } else if (invalidFlag == '1') {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        let operationId = 'replaceIdCard'
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            // tools.recordEventData('1','updateIDCard','更新身份照片');
                            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                        }, "取消", "更换",operationId);
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.comb_code);
                    if (productInfo.prod_propagate_temp) {
                        appUtils.pageInit(_page_code, "combProduct/combProdMarketing");
                    } else {
                        //缓存当前是否为系列投顾从产品
                        appUtils.setSStorageInfo("isSeriesComb", '0');
                        appUtils.pageInit(_page_code, "combProduct/combProdDetail");
                    }
                });
                return;
            }else if(productInfo.prod_sub_type2 == '200'){
                
                if (productInfo.prod_propagate_temp) {
                    tools.jumpMarketingPage(_page_code, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
                }
            }
        },"click")
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .list").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var highVersion_custProducts = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_custProducts;
});
