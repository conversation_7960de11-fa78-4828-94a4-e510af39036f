//人脸识别页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        serviceConstants = require("constants"),
        service = require("mobileService"),
        _pageUrl = "bank/faceRecognition",
        _pageId = "#bank_faceRecognition ";
    var gconfig = require("gconfig");
    var tools = require("../common/tools");
    var userInfo;
    var ut = require("../common/userUtil");
    var bank_channel_code;
    var productInfo;

    function init() {
    	productInfo = appUtils.getSStorageInfo("productInfo");
        bank_channel_code = productInfo.bank_channel_code;
        setBankInfo();
    }
    /**
     * 银行信息
     * */
    function setBankInfo() {
        $(_pageId + " .bank_electron_name").html(productInfo.bank_channel_name);
        $(_pageId + " .bank_electron_icon").attr("src", tools.judgeBankImg(bank_channel_code).logo);
    }

    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
        //拍照，人脸识别
        appUtils.bindEvent($(_pageId + " #startShoot"), function () {
            var paramExt = {
                type: "bank_face",
                multi:false
            }
            var param = {};
            param["funcNo"] = "50273";
            param["moduleName"] = "mall";
            param["fileName"] = "changeCard";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#111111";
            }
            param["cutFlag"] = "0";
            param["compress"] = "0.5";
            param["width"] = "500";
            param["height"] = "500";
            param["paramExt"] = paramExt;
            require("external").callMessage(param);
        });
    }

    function destroy() {
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = result;
});
