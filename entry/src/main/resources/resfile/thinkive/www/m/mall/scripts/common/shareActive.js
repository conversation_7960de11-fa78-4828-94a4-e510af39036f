define(function(require, exports, module) {
	var appUtils = require("appUtils"),
		layerUtils = require("layerUtils"),
		gconfig = require("gconfig"),
		global = gconfig.global,
		platform = gconfig.platform,
		service = require("mobileService"),
		msgFunction = require("msgFunction"),
		des = require("des");

	var shareActive = {
		pageId: '',
		//创建分享
		shareCreated: function(type, pageId, dom) {
			var that = this;
			shareActive.pageId = pageId;
			if(!dom) {
				dom = '';
			}
			$(shareActive.pageId + ' article #shareActivecont').remove();
			service.reqFun9089026({
				joinStatus: shareActive.getActivityId(type)
			}, function(data) {
				var error_no = data.error_no,
					error_info = data.error_info;
				//2017-8-8 jiaxr 修改原因:晋金宝金额应包含冻结金额
				if(error_no == "0") {
					var result = data.results[0];
					that.activityId = result.id;
					that.link = shareActive.getLink(result.trade_shared_url) //分享链接
					//				that.link = that.getLink(result.link_url);
					that.content = result.share_content;
					that.title = result.share_title;
					$(shareActive.pageId + ' article ' + dom).append('<div id="shareActivecont"></div>');
					$(shareActive.pageId + ' article #shareActivecont').append('<div id="shareActive"><h3>' + result.activity_tips + '</h3><img src="' + gconfig.global.serverUrl + result.picture_url + '" alt="" class="shareImg" /></div>');
					$(shareActive.pageId + ' article #shareActivecont').append('<div class = "pop_layer" style = "display:none;" id = "pop_layer" >' +
						'<div class="share_box slidedown in"><a href="javascript:void(0);" class="btn_shareto text-center">分享赢好礼</a>' +
						'<ul><li><a href="javascript:void(0);" id="share_WeChat"><em></em><span>微信好友</span></a></li>' +
						'<li><a href="javascript:void(0);" id="share_WeChatFriend"><em></em><span>微信朋友圈</span></a></li></ul>' +
						'<a href="javascript:void(0);" class="btn_cancel text-center" id="cancelShare">取消</a></div></div>');
					//显示分享
					appUtils.bindEvent($(shareActive.pageId + " #shareActive .shareImg"), function() {
						$(shareActive.pageId + ' #pop_layer').show();
					});
					//隐藏分享
					appUtils.bindEvent($(shareActive.pageId + " #cancelShare"), function() {
						$(shareActive.pageId + ' #pop_layer').hide();
					});
					//微信好友
					appUtils.bindEvent($(shareActive.pageId + " #share_WeChat"), function() {
						shareActive.share("22");
					});
					//微信朋友圈
					appUtils.bindEvent($(shareActive.pageId + " #share_WeChatFriend"), function() {
						shareActive.share("23");
					});
					//分享成功回调
					window.msg50232callback = function() {

						shareActive.shareSuccess();
					}
				} else {
					//layerUtils.iAlert(error_info);
				}

			});
		},
		shareSuccess: function() {
			var that = this;
			service.reqFun9089003({
				activity_id: that.activityId,
				phoneNum: appUtils.getSStorageInfo("user").mobileWhole
			}, function(data) {
				var error_no = data.error_no,
					error_info = data.error_info;
				if(error_no == "0") {
					var result = data.results[0];
					if(result.money && result.money != 0) {
						$(shareActive.pageId + " #shareActive").html('<h3>恭喜您获得<span>' + shareActive.rewardType(result.reward_code) + '</span></h3>' +
							'<h2><span>' + result.money + '</span>元</h2><img src="images/hb01.jpg" alt="" class="shareImg">');
						//去看看
						appUtils.preBindEvent($(shareActive.pageId + " #shareActive"),' .shareImg', function() {
							appUtils.pageInit(shareActive.pageId.replace('_','/').replace('#','').trim(),that.gotosee);
						},'click');
					} else {
						$(shareActive.pageId + " #shareActive").html('<img src="images/hb02.jpg" alt=""/>');
					}
				} else {
					$(shareActive.pageId + " #shareActive").html('<img src="images/hb02.jpg" alt=""/>');
				}
			});
		},
		share: function(shareType) {
			// if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
			var that = this;
			layerUtils.iMsg(-1, "启动分享中...请稍后！", 2);
			$(shareActive.pageId + ' #pop_layer').hide();
			var param = {};
			param["moduleName"] = "mall";
			param["funcNo"] = "50231";
			param["shareType"] = shareType; //平台字典
			param["title"] = that.title;
			param["link"] = that.link;
			var content_url = that.content.replace(/<[^>]+>/g, "").replace(/&nbsp/g, "").replace(/\n/g, "").replace(/;/g, "").replace(/\ +/g, "").replace(/[ ]/g, "");
			var description = content_url;
			var des = "";
			if(description.length > 40) {
				des = description.substring(0, 40) + "...";
			} else {
				des = description;
			}
			param["content"] = des; //分享文本
			param["imgUrl"] = gconfig.global.imgUrl;
			require("external").callMessage(param);

			setTimeout(function() {
				layerUtils.iMsg(-1, "分享成功！");
				window.msg50232callback();
			}, 6000);
		},
		getLink: function(link) {
			var mobile = appUtils.getSStorageInfo("user").mobileWhole;
			var keyHex = des.enc.Utf8.parse('mobile');
			var valueHex = des.enc.Utf8.parse(mobile);
			var encrypted = des.DES.encrypt(valueHex, keyHex, {
				mode: des.mode.ECB,
				padding: des.pad.Pkcs7
			});
			if(!link) {
				link = global.link;
			}
			if(link.indexOf("?") != -1) {
				return link + "&mobile=" + encrypted.toString();
			} else {
				return link + "?mobile=" + encrypted.toString();
			}
		},
		rewardType: function(reward_code) {
			var that = this;
			if(reward_code == 0) {
				that.gotosee = 'redPack/redPackage';
				return '红包';
			}
			if(reward_code == 4) {
				that.gotosee = 'redPack/cashVolume';
				return '现金券';
			}
			if(reward_code == 3) {
				that.gotosee = 'redPack/tyj';
				return '体验金';
			}
		},
		getActivityId: function(type) {
			/*
			 *防止重复修改
			 *对应关系如下
			 * */
			/*1注册后 2绑卡后 3充值后4取现后5认购新手理财后 6认购普通理财后7购买转让产品后8奖励提取后9首页弹窗详情页 */
			/*21注册后 22绑卡后 23充值后24取现后25认购新手理财后26认购普通理财后 27购买转让产品后 28奖励提取后 29首页弹窗详情页 */
			switch(type) {
				case 1:
					return '21';
				case 2:
					return '22';
				case 3:
					return '23';
				case 4:
					return '24';
				case 5:
					return '25';
				case 6:
					return '26';
				case 7:
					return '27';
				case 8:
					return '28';
				case 9:
					return '29';
				case 10:
					return '30';
				default:
					return '';
			}
		}

	}

	// 暴露对外的接口
	module.exports = shareActive;
});
