// 线下入金
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageId = "#myTransfer_uploadCredentials ";
    var ut = require("../common/userUtil");
    var _pageCode = "myTransfer/uploadCredentials";
    require("../common/clipboard.min.js");
    let info;
    let pageToInfo;//跳转处理
    var user;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        info = appUtils.getSStorageInfo("buyTransferData"); //拿到产品基本信息
        pageToInfo = appUtils.getPageParam();
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel); //渲染客服电话
        
        getFundInfo(true);//获取汇款充值账户信息
        user = ut.getUserInf();
        var bankName = user.bankName;
        var bankCard = user.bankAcct;
        $(_pageId + " #bankName").html(bankName);
        $(_pageId + " #bankCard").html(bankCard.substring(bankCard.length - 4, bankCard.length));
        copyContent("hmCopy");
        copyContent("zhCopy");
        copyContent("khhCopy");
        copyContent("iphone_bank");
    }
    function bindPageEvent() {
        // 上传图片
        appUtils.preBindEvent($(_pageId + " .uploadImg"), ".uploadBtn", function (e) {
            var param = {};
            param["funcNo"] = "50277";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#ffffff";
            }
            param['paramExt'] = {
                type: "qualifiedInvestor",
                name: "uploadBtn",
                uploadType: "2",
                multi:true
            };
            // param["compress"] = "0.5";
            param["compressSize"] = '200';
            param["width"] = "1600";
            param["height"] = "900";
            param["cutFlag"] = "0";
            // require("external").callMessage(param);
            tools.fileImg(_pageId,param)
        });
        // 批量上传图片
        appUtils.bindEvent($(_pageId + " .firstAdd"), function () {
            // let str = '<div class="pop_layer camera_pop_layer">\n' +
            //         '            <div  class="slideup in">\n' +
            //         '                <div class="camera">拍照</div>\n' +
            //         '                <div class="album">从相册中选取</div>\n' +
            //         '                <div class="cancel_camera">取消</div>\n' +
            //         '            </div>\n' +
            //         '        </div>'
            // if ($(_pageId + " .camera_pop_layer").length > 0) {
            //     $(_pageId + " .camera_pop_layer").show();
            // } else {
            //     $(_pageId).append(str);
            // }
            var param = {};
            param["funcNo"] = "50277";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#ffffff";
            }
            param['paramExt'] = {
                type: "qualifiedInvestor",
                name: "firstAdd",
                uploadType: "2",
                multi:true
            };
            // param["compress"] = "0.5";
            param["compressSize"] = '200';
            param["width"] = "1600";
            param["height"] = "900";
            param["cutFlag"] = "0";
            tools.fileImg(_pageId,param)
            // //调用相册
            // appUtils.bindEvent($(_pageId + " .album"), function () {
            //     param["mode"] = 1
            //     require("external").callMessage(param);
            // });
            // //调用相机
            // appUtils.bindEvent($(_pageId + " .camera"), function () {
            //     param["mode"] = 2
            //     require("external").callMessage(param);
            // });
            // //关闭弹框
            // appUtils.bindEvent($(_pageId + " .camera_pop_layer"), function (e) {
            //     e.stopPropagation();
            //     $(_pageId + ' .camera_pop_layer').hide();
            // });
            // appUtils.bindEvent($(_pageId + " .cancel_camera"), function (e) {
            //     $(_pageId + ' .camera_pop_layer').hide();
            // });
        });
        //上传付款凭证
        appUtils.bindEvent($(_pageId + " .fillImage"), function () {
            var base64_list = [];
            for (var i = 0; i < $(_pageId + " .uploadImg img").length; i++) {
                base64_list.push($(_pageId + " .uploadImg img").eq(i).attr("src").replace("data:image/gif;base64,", ""));
            }
            if (base64_list.length == 0) {
                layerUtils.iAlert("请上传图片");
                return;
            }
            
            //上传视频
            service.reqFun199019({
                base64_list: base64_list,
            }, function (data) {
                if (data.error_no == "0") {
                    // layerUtils.iLoading(false);
                    let img_url = data.results;
                    submit(img_url)
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info)
                }
            }, { isLastReq: false })
        })
        appUtils.bindEvent($(_pageId + " #sure"), function () {
            $(_pageId + " #tit_hkcz").hide();
            $(_pageId + " #mask_hkcz").hide();
        })
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //点击取消
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            $(_pageId + " .pop_layer").hide();
        });
        //点击继续
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            tools.openBankApp(user.bankCode);
            $(_pageId + " .pop_layer").hide();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum("account/payRecharge")
        });
        //打开手机银行
        // appUtils.bindEvent($(_pageId + " .iphone_bank"), function () {
        //     let account = $(_pageId + " #zh").text().trim();
        //     layerUtils.iConfirm(`已复制<span style="color:#e5443c">${account}</span>收款账号,去银行转账即可`, function () {
        //         tools.openBankApp(user.bankCode);
        //     }, function () {
        //     }, "继续", "取消");
        // });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .web_bank"), function () {
            layerUtils.iAlert("请登录" + user.bankName + "网站进行转账汇款");
        });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .counter_bank"), function () {
            layerUtils.iAlert("请您到" + user.bankName + "网点进行转账汇款");
        });
    }
    function getFundInfo(flag) {
        service.reqFun107018({trans_serno: info.trans_serno}, function (data) {
            if (data.error_no == "0") {
                var resutls =data.results[0];
                let time = tools.ftime(resutls.date).split('-');
                let timeStr = `${time[1]}-${time[2]} 17:00`
                $(_pageId + " .entrust_date").html(timeStr); //渲染汇款截止日期
                $(_pageId + " #hm").html(resutls.manager_account_name);
                $(_pageId + " #hmCopy").attr("data-clipboard-text", resutls.manager_account_name);
                $(_pageId + " #zh").html(resutls.manager_account_number);
                $(_pageId + " #zhCopy").attr("data-clipboard-text", resutls.manager_account_number);
                $(_pageId + " #iphone_bank").attr("data-clipboard-text", resutls.manager_account_number);
                $(_pageId + " #khh").html(resutls.manager_account_bank);
                $(_pageId + " #khhCopy").attr("data-clipboard-text", resutls.manager_account_bank);
                $(_pageId + " #remittanceAmount").html(tools.fmoney(info.total_amt) + '元');
            } else {
                if(flag) {
                    layerUtils.iAlert(data.error_info);
                }
            }
        })
    }
    //上传付款凭证
    function submit(img_url){
        let imgList = [];   //拿到的后台数据 URL数组集合
        img_url.map(item=>{
            imgList.push(item.dir)
        })
        img_url = imgList.toString().replace(/,/g,';');
        service.reqFun107019({trans_serno: info.trans_serno,img_url:img_url}, function (data) {
            if (data.error_no == "0") {
                layerUtils.iLoading(false);
                //上传凭证成功后进入结果页
                appUtils.pageInit(_pageCode, "myTransfer/hangingOrderSuccess");
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        },{ isLastReq: false })
    }
    // 复制
    function copyContent(id) {
        // console.log(id)
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);
        clipboard.on('success', function (e) {
            // console.log(e)
            if(!e.text.trim()) {
                layerUtils.iAlert("复制失败，请重试", -1, function () {
                    getFundInfo(false)
                });
                return;
            }
            // console.log(id,e)
            if(id == "zhCopy" || id == "iphone_bank"){
                // console.log(e.text.trim())
                let account = e.text.trim();
                $(_pageId + " .account").text(account);
                $(_pageId + " .pop_layer").show();
                // layerUtils.iConfirm(`已复制<span style="color:#e5443c">${account}</span>收款账号,去银行转账即可`, function () {
                //     // $("#pop_tip_confirm").prepend('<div style="margin-top: 0.2rem;font-size: 0.18rem;font-weight: 600;">即将打开银行APP</div>')
                //     tools.openBankApp(user.bankCode);
                // }, function () {
                // }, "继续", "取消");
            }else{
                layerUtils.iAlert("复制成功，可粘贴");
            }
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    function pageBack() {
        // appUtils.pageBack();
        if(pageToInfo && pageToInfo.pageTo == 'myTransfer'){
            //用户首次小集合转让
            var routerList = appUtils.getSStorageInfo("routerList")
            routerList = ["login/userIndexs","account/myAccount"]
            appUtils.setSStorageInfo("routerList",routerList)
            appUtils.setSStorageInfo("_cust_fund_type", '0');
            appUtils.pageInit(_pageCode, "myTransfer/index");
        }else{
            appUtils.pageBack();
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .account").text('');
        $(_pageId + " #bankName").html("");
        $(_pageId + " #bankCard").html("");
        $(_pageId + " #hm").html("");
        $(_pageId + " #hmCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #zh").html("");
        $(_pageId + " #zhCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #iphone_bank").attr("data-clipboard-text", " ");
        $(_pageId + " #khh").html("");
        $(_pageId + " #khhCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #remittanceAmount").html("")
    }


    var uploadCredentials = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = uploadCredentials;
});
