// 源晖  我的页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageUrl = "yuanhui/myAccount",
        _pageId = "#yuanhui_myAccount ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var external = require("external");
    var tools = require("../common/tools");//升级
    var ut = require("../common/userUtil");
    var userInfo;

    function init() {
        userInfo = ut.getUserInf();
        if (ut.getUserInf().custLabelCnlCode == "yh") {
            $(_pageId + " .bankDeposit_region").show();
            $(_pageId + " .gdDeposit_region").hide();
        }else {
            $(_pageId + " .gdDeposit_region").show();
            $(_pageId + " .bankDeposit_region").hide();
        }

        nameOrPhone();
        //添加头像
        testHead();

        initRisk();//风险测评提示

        id_card_info()
    }
    //身份证认证提示
    function id_card_info(){
        //身份证认证状态 0:未完善 1:已完善 2:证件到期3:到期前3个月 4:到期后3个月
        let perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        let parentHtml = $(_pageId + ' .tip') //主节点
        let tip_mainText = $(_pageId + ' .tip' + ' .tip_mainText') //文案
        switch (perfect_info) {
            case '3':
                parentHtml.css("display",'block')
                break;
            case '2':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display",'block')
                break;
            case '4':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display",'block')
                break;
            default:
                break;
        }
    }
    //绑定事件
    function bindPageEvent() {
        //认证身份证
        appUtils.bindEvent($(_pageId + " .tip .uploadIDCard"),()=>{
            tools.clickPoint(_pageId,_pageId,_pageUrl,'expirationReplacementIdCard')
            appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
        })
        //消息
        appUtils.bindEvent($(_pageId + " #message_img"), function () {
            tools.clickPoint(_pageId,_pageUrl,'message_img')
            appUtils.pageInit(_pageUrl, "moreDetails/msg");
        });
        //个人中心
        appUtils.bindEvent($(_pageId + " .my_account"), function () {
            tools.clickPoint(_pageId,_pageUrl,'my_account')
            appUtils.pageInit(_pageUrl, "account/personMessage")
        });
        //晋金宝
        appUtils.bindEvent($(_pageId + " #thfund"), function () {
            tools.clickPoint(_pageId,_pageUrl,'thfund')
            appUtils.pageInit(_pageUrl, "thfund/myProfit");
        });

        //安全中心
        appUtils.bindEvent($(_pageId + " #passWordManage"), function () {
            tools.clickPoint(_pageId,_pageUrl,'passWordManage')
            appUtils.pageInit(_pageUrl, "safety/passwordManage");
        });

        //首页
        appUtils.bindEvent($(_pageId + " #shouye"), function () {
            if (ut.getUserInf().custLabelCnlCode == "yh") {
                appUtils.pageInit(_pageUrl, "yuanhui/userIndexs", {});
                return;
            }else if(ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) ||ut.getUserInf().custLabelCnlCode == "jjdx"){
                appUtils.pageInit(_pageUrl, "login/userIndexs", {});
            } else{
                appUtils.pageInit(_pageUrl, "hengjipy/userIndexs", {});
                return;
            }
        });
        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            appUtils.pageInit(_pageUrl, "moreDetails/more", {});
        });
        //风险测评
        appUtils.bindEvent($(_pageId + " #risk"), function () {
            tools.clickPoint(_pageId,_pageUrl,'risk')
            appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
        });
        //源晖专享
        appUtils.bindEvent($(_pageId + " #yuanhui"), function () {
            tools.clickPoint(_pageId,_pageUrl,'yuanhui')
            appUtils.pageInit(_pageUrl, "yuanhui/hold", {});
        });
        //恒基专享
        appUtils.bindEvent($(_pageId + " #gdfund"), function () {
            tools.clickPoint(_pageId,_pageUrl,'gdfund')
            appUtils.pageInit(_pageUrl, "highEnd/hold", {});
        });

    }

    function destroy() {
        $(_pageId + " #riskShow").hide();
        $(_pageId + " #wdyy").hide();
        $(_pageId + " .tip").hide();
        $(_pageId + " .wxfund_box").hide();
        $(_pageId + " .empty").html("")
    }

    /****************自定义方法，写到destroy后面**********************/

    //添加头像
    function testHead() {
        service.reqFun1100007({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var result = (data.results)[0];
                $(_pageId + " .riskName").html(result.riskName ? result.riskName : "--");
                $(_pageId + " .custRiskDate").html(result.custRiskDate ? tools.ftime(result.custRiskDate) : "--");
                var photo_url = result.photoUrl;//用户头像
                //var photo_url = data.results[0].photo_url;//用户头像
                if (photo_url) {
                    $(_pageId + " #headPortrait img").attr("src", global.oss_url + photo_url + "?key=" + new Date().getTime());
                    var params = {
                        funcNo: "50042",
                        key: "photo_url",
                        isEncrypt: "1",
                        value: global.oss_url + photo_url + "?key=" + new Date().getTime(),
                    };
                    external.callMessage(params);
                } else {
                    $(_pageId + " #headPortrait img").attr("src", "./images/highEnd/high_headerImg.png");
                    var params = {
                        funcNo: "50042",
                        key: "photo_url",
                        value: "",
                        isEncrypt: "1"
                    };
                    external.callMessage(params);
                }
            } else {
                layerUtils.iAlert(error_info);
            }
            //资产查询
            getAssetInfo();
        }, {isLastReq: false});
    }

    //取名称作为我的第一列展示，如果没有就去手机号码
    function nameOrPhone() {
        var nameCheck = userInfo.name;
        if (nameCheck) {
            var name_Check = nameCheck.substr(0, 1);
            for (var i = 2; i <= nameCheck.length; i++) {
                name_Check += "*";
            }
            $(_pageId + " .custName").html(name_Check);
        } else {
            $(_pageId + " .custName").html(userInfo.mobile);
        }
    }

    //风险测评提示
    function initRisk() {
        //风险测评设置
        if (userInfo.riskName && userInfo.riskLevel) {
            $(_pageId + " #riskShow").hide();
        } else {
            $(_pageId + " #riskShow").show();
        }
    }

    //基金用户资产查询
    function getAssetInfo() {
        service.reqFun101999({}, function (data) {
            if (data.error_no == "0") {
                var result = data.results[0];
                var totalAssets = result.totalAssets ? common.fmoney(result.totalAssets, 2) : "--";//总资产
                var prifundAssets = result.prifundAssets ? common.fmoney(result.prifundAssets, 2) : "--"; //私募
                var mfundAssets = result.mfundAssets ? common.fmoney(result.mfundAssets, 2) : "--";//现金宝
                $(_pageId + " .total_assets em").html(totalAssets + "元");
                $(_pageId + " #thfund em").html(mfundAssets + "元");
                $(_pageId + " #yuanhui em").html(prifundAssets + "元");
                $(_pageId + " #gdfund em").html(prifundAssets + "元");
            } else {
                $(_pageId + " .total_assets em").html("--");
                $(_pageId + " #bankDeposit em").html("--");
                $(_pageId + " #jinjibao em").html("--");
                $(_pageId + " #gdfund em").html("--");
                $(_pageId + " #yuanhui em").html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = myAccount;
})
