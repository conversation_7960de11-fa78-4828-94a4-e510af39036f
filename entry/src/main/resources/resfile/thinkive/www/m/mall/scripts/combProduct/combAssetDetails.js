// 买入卖出规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        common = require("common"),
        _pageId = "#combProduct_combAssetDetails",
        _pageCode = "combProduct/combAssetDetails";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var ut = require("../common/userUtil");
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        reqFun106061();
    }

    //获取持仓分布
    function reqFun106061() {
        var param = {
            fund_code: productInfo.comb_code ? productInfo.comb_code : productInfo.fund_code,
        }
        service.reqFun106061(param, (data) => {
            if (data.error_no == 0) {
                if (data.results && data.results.length) {
                    var dataArr = data.results[0].fund_list;
                    let option = combHoldDistOption;
                    let arr = [];
                    var html = ``;
                    dataArr.forEach(item => {
                        // console.log(item.crt_date);
                        arr.push({ value: item.prod_proportion, name: item.invest_type })
                        var str = ` <div><div class="hold_detail_item">${item.invest_type}</div><div class="hold_detail_th">`
                        item.asset_list = JSON.parse(item.asset_list);
                        if (item.asset_list && item.asset_list.length && item.asset_list instanceof Array) {
                            item.asset_list.forEach(fund => {
                                console.log(fund,111)
                                fund._prod_proportion = (fund.prod_proportion * 100).toFixed(2) + "%";
                                var text_c = fund.accumulatedincome >= 0 ? "m_text_red" : "m_text_green";
                                str += `
                                    <div class="hold_detail_tr" style="padding: 0.1rem 0.15rem">
                                        <div>
                                            <div class="m_text_darkgray">
                                                <span class="m_bold">${fund.fund_name}</span>
                                                <span class="m_text_gray">(${fund.fund_code})</span>
                                            </div>
                                        </div>
                                        <div class="flex">
                                            <div class="flex asset_item">
                                                <span class="m_bold">${tools.fmoney(fund.totalvol)}</span>
                                                <span>持有金额(元)</span>
                                            </div>
                                            
                                            <div class="flex asset_item">
                                                <span class="m_bold">${fund.asset_proportion}%</span>
                                                <span>持有占比</span>
                                            </div>
                                            <div class="flex asset_item">
                                                
                                            </div>
                                        </div>
                                        ${item.invest_type !='货币型' ? ` <div class="flex">
                                            <div class="flex asset_item">
                                                <span class="m_bold">${fund.holdvol}</span>
                                                <span>持有份额(份)</span>
                                            </div>
                                            
                                            <div class="flex asset_item">
                                                <span class="m_bold">${fund.nav}</span>
                                                <span>最新净值${setData(fund.navdate)}</span>
                                            </div>
                                            <div class="flex asset_item">
                                                
                                            </div>
                                        </div>` : ''}
                                       
                                    </div>
                                `;
                            })
                        }
                        str += '</div></div>';
                        html += str;
                    })
                    $(_pageId + " .hold_detail").html(html);
                    option.series[0].data = arr;
                    option.legend.formatter = (name) => {
                        var target;
                        for (var i = 0, l = arr.length; i < l; i++) {
                            if (arr[i].name == name) {
                                target = arr[i].value;
                            }
                        }
                        return name + '：' + target + '%';
                    }
                    let dom = document.getElementById(`comb_asset_container1`);
                    let myChart = echarts.init(dom, null, {
                        renderer: 'canvas',
                        useDirtyRect: false
                    });
                    if (option && typeof option === 'object') {
                        myChart.setOption(option);
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    };

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //处理日期字段
    function setData(str){
        if(str && str.length){
            return '(' + str.substring(4, 6) + '-' + str.substring(6, 8) + ')'
        }else{
            return ''
        }
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
