/*创建时间hSea 2015-12-31 15:36:59 PM */
define(function(require,exports,module){function a(a,b,c,d){var f={value:b,time:c,createDate:(new Date).getTime()},g=function(a){try{var b=e.enc.Utf8.parse("iloveyou"),c=e.enc.Utf8.parse(a),d=e.enc.Utf8.parse("iloveyou"),f=e.AES.encrypt(c,b,{iv:d,mode:e.mode.CBC});a=f.toString()}catch(g){}return a};switch(d){case"h5_session":f=g(JSON.stringify(f)),window.sessionStorage.setItem(a,f);break;case"h5_local":f=g(JSON.stringify(f)),window.localStorage.setItem(a,f)}}function b(a,b){switch(b){case"h5_session":window.sessionStorage.removeItem(a);break;case"h5_local":window.localStorage.removeItem(a)}}function c(a,b){var c=function(a){if(null===a||"null"===a||""===a)return null;try{var b=e.enc.Utf8.parse("iloveyou"),c=e.enc.Utf8.parse("iloveyou"),d=e.enc.Base64.parse(a),f=e.AES.decrypt({ciphertext:d},b,{iv:c,mode:e.mode.CBC});return JSON.parse(f.toString(e.enc.Utf8))}catch(g){return{time:0,value:a}}},f=function(b){var c=null;return b&&(b.hasOwnProperty("time")&&b.hasOwnProperty("value")?c=b.value:d.iAlert("cacheUtils.getItem 取值失败，失败原因：存储时，未调用 cacheUtils.setItem 函数，当前 key："+a)),c},g=null;switch(b){case"h5_session":g=window.sessionStorage.getItem(a);break;case"h5_local":g=window.localStorage.getItem(a)}return g=c(g),g=f(g)}var d=require("layerUtils"),e=require("aes"),f={setItem:a,removeItem:b,getItem:c};module.exports=f});
/*创建时间 2015-12-31 15:36:59 PM */