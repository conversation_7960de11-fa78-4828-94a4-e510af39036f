// 买入卖出规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        common = require("common"),
        _pageId = "#combProduct_combHoldDistribution",
        _pageCode = "combProduct/combHoldDistribution";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var ut = require("../common/userUtil");
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        // tools.initCombFundBtn(productInfo, _pageId);
        //是否为投顾系列产品
        let isSeriesComb = appUtils.getSStorageInfo("isSeriesComb");
        if(isSeriesComb != '1') tools.initCombFundBtn(productInfo, _pageId);
        // getHoldDetail();
        reqFun102166();
    }

    //获取持仓分布
    function reqFun102166() {
        var param = {
            comb_code: productInfo.comb_code,
        }
        service.reqFun102166(param, (data) => {
            if (data.error_no == 0) {
                if (data.results && data.results.length) {
                    let option = combHoldDistOption;
                    let arr = [];
                    var html = `
                        <div class="hold_detail_th m_font_size16 m_bold">
                            <span>明细</span>
                            <span class="m_title_right">占比</span>
                        </div>`;
                    data.results.forEach(item => {
                        // console.log(item.crt_date);
                        $(_pageId + " #crt_date").html(tools.ftime(data.results[0].crt_date));
                        arr.push({ value: (item.proportion * 100).toFixed(2), name: item.investment_name })
                        var str = ` <div><div class="hold_detail_item">${item.investment_name}</div><div>`
                        if (item.fund_list && item.fund_list.length && item.fund_list instanceof Array) {
                            item.fund_list.forEach(fund => {
                                fund._prod_proportion = (fund.prod_proportion * 100).toFixed(2) + "%";
                                str += `
                                    <div class="hold_detail_tr">
                                        <div>
                                            <div>${fund.prod_name}</div>
                                            <span>${fund.prod_code}</span>
                                        </div>
                                        <div>${fund._prod_proportion}</div>
                                    </div>
                                `;
                            })
                        }
                        str += '</div></div>';
                        html += str;
                    })
                    $(_pageId + " .hold_detail").html(html);
                    option.series[0].data = arr;
                    option.legend.formatter = (name) => {
                        var target;
                        for (var i = 0, l = arr.length; i < l; i++) {
                            if (arr[i].name == name) {
                                target = arr[i].value;
                            }
                        }
                        return name + '：' + target + '%';
                    }
                    let dom = document.getElementById(`chart-container1`);
                    let myChart = echarts.init(dom, null, {
                        renderer: 'canvas',
                        useDirtyRect: false
                    });
                    if (option && typeof option === 'object') {
                        myChart.setOption(option);
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    };

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .thfundBtn").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
