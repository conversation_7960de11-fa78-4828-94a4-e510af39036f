// 修改登录密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        serviceConstants = require("constants"),
        service = require("mobileService"),
        monkeywords = require("mall/scripts/common/passwordKeywords"),
        common = require("common"),
        _pageId = "#safety_updateUserPassword";
    var gconfig = require("gconfig");
    var platform = require("gconfig").platform;
    var ut = require("../common/userUtil");
    var external = require("external");
    var tools = require("../common/tools");

    function init() {
          //页面埋点初始化
        tools.initPagePointData();
        common.systemKeybord(); // 解禁系统键盘
        $(_pageId + " #userInfo").html("您好！您正在为账户 " + ut.getUserInf().mobile + "修改登录密码。");

    }
    /**
     * 渲染密码
     */
     function setPassword(_fatherPageId,_childPageId){
        let spacing = platform == 1 ? 0.06 : 0.075
        let pwd = $(_pageId + _childPageId).val();
        if(!pwd){
            $(_pageId + _fatherPageId + " .placeholderPsd").html('');
            $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#aaa');
            $(_pageId + _fatherPageId + " .cursor-bink").css("left",'1rem');
            return
        }  
        $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#666');
        let length = pwd.length*1
        if(length > 16) return
        let str = ''
        for(let i = 0; i<length; i++){
            str = str + '*'
        }
        $(_pageId + _fatherPageId + " .placeholderPsd").html(str);
        $(_pageId + _childPageId ).val(pwd);
        // let leftValue = 1 + (length*spacing)
        let leftValue = ($(_pageId + _fatherPageId + " .placeholderPsd")[0].clientWidth/100) + 0.9;
        $(_pageId + _fatherPageId + " .cursor-bink").css("left",leftValue+'rem');
    }
    function moneyboardEvent(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #oldPwd"),
            endcallback: function () {
                setPassword(" #password1"," #oldPwd")
            },
            inputcallback: function () {
                setPassword(" #password1"," #oldPwd")
            },
            keyBoardHide: function () {
                setPassword(" #password1"," #oldPwd")
            }
        })
    }
    function moneyboardEvent1(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #newPwd1"),
            endcallback: function () {
                setPassword(" #password2"," #newPwd1")
            },
            inputcallback: function () {
                setPassword(" #password2"," #newPwd1")
            },
            keyBoardHide: function () {
                setPassword(" #password2"," #newPwd1")
            }
        })
    }
    function moneyboardEvent2(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #newPwd2"),
            endcallback: function () {
                setPassword(" #password3"," #newPwd2")
            },
            inputcallback: function () {
                setPassword(" #password3"," #newPwd2")
            },
            keyBoardHide: function () {
                setPassword(" #password3"," #newPwd2")
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " #password1 .cursor-bink").hide()
            $(_pageId + " #password2 .cursor-bink").hide()
            $(_pageId + " #password3 .cursor-bink").hide()
            monkeywords.close();
        });
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #password1"), function (event) {
            event.stopPropagation();
            $(_pageId + " #password1 .cursor-bink").show()
            $(_pageId + " #password2 .cursor-bink").hide()
            $(_pageId + " #password3 .cursor-bink").hide()
            moneyboardEvent()
            // $(_pageId + " #oldPwd").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "safety_updateUserPassword";
            param["eleId"] = "oldPwd";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #password2"), function (event) {
            event.stopPropagation();
            $(_pageId + " #password2 .cursor-bink").show()
            $(_pageId + " #password1 .cursor-bink").hide()
            $(_pageId + " #password3 .cursor-bink").hide()
            moneyboardEvent1()
            // $(_pageId + " #newPwd1").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "safety_updateUserPassword";
            param["eleId"] = "newPwd1";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #password3"), function (event) {
            event.stopPropagation();
            $(_pageId + " #password3 .cursor-bink").show()
            $(_pageId + " #password2 .cursor-bink").hide()
            $(_pageId + " #password1 .cursor-bink").hide()
            moneyboardEvent2()
            // $(_pageId + " #newPwd2").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "safety_updateUserPassword";
            param["eleId"] = "newPwd2";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            var oldPwd = $(_pageId + " #oldPwd").val();
            var newPwd1 = $(_pageId + " #newPwd1").val();
            var newPwd2 = $(_pageId + " #newPwd2").val();
            if (oldPwd == null || oldPwd == "") {
                layerUtils.iMsg(-1, "请确定您输入的原密码");
                return;
            }
            if (!checkInput(newPwd1, newPwd2)) {
                return;
            }
            if (newPwd1 == oldPwd) {
                layerUtils.iMsg(-1, "新密码与原密码相同");
                return;
            }
            var param = {
                old_login_pwd: oldPwd,
                new_login_pwd: newPwd1
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no == "0") {
                    var modulus = data.results[0].modulus;
                    var publicExponent = data.results[0].publicExponent;
                    var endecryptUtils = require("endecryptUtils");
                    param.old_login_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.old_login_pwd);
                    param.new_login_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.new_login_pwd);
                    //修改密码
                    updatePwd(param);
                }
            }, {isLastReq: false});
        });
    }
    /**
     * 修该登录密码
     * */
    function updatePwd(param) {
        var sys_trans_pwd = param.new_login_pwd;
        var phoneNum = ut.getUserInf().mobileWhole;
        service.reqFun101004(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                // 存登录号 和 登录加密密码 方便手势密码
                var paramlist = {
                    funcNo: "50042",
                    key: "account_password",
                    isEncrypt: "1",
                    value: phoneNum + "_" + sys_trans_pwd
                };
                external.callMessage(paramlist);
                layerUtils.iAlert("密码修改成功", 0, function () {
                    appUtils.clearSStorage(true);// 清除所有缓存
                    appUtils.pageInit("safety/updateUserPassword", "login/userLogin");
                });
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(error_info);
            }
        });
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iMsg(-1, "修改登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iMsg(-1, "确认登录密码不能为空");
            return false;
        }
        if (pwd1 != pwd2) {
            layerUtils.iMsg(-1, "两次修改密码不相同");
            return false;
        }

        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iMsg(-1, "登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iMsg(-1, "登录密码请输入6-16位字母和数字组合");
            return false;
        }
        return true;
    }

    function destroy() {
        $(_pageId + " #oldPwd").val("");
        $(_pageId + " #newPwd1").val("");
        $(_pageId + " #newPwd2").val("");
        monkeywords.close();
        $(_pageId + " #password1 .cursor-bink").hide()
        $(_pageId + " #password2 .cursor-bink").hide()
        $(_pageId + " #password3 .cursor-bink").hide()
        $(_pageId + " #password1 .placeholderPsd").css('color','#aaa');
        $(_pageId + " #password2 .placeholderPsd").css('color','#aaa');
        $(_pageId + " #password3 .placeholderPsd").css('color','#aaa');
        $(_pageId + " #password1 .cursor-bink").css("left",'1rem');
        $(_pageId + " #password2 .cursor-bink").css("left",'1rem');
        $(_pageId + " #password3 .cursor-bink").css("left",'1rem');
        $(_pageId + " #password1 .placeholderPsd").html('');
        $(_pageId + " #password2 .placeholderPsd").html('');
        $(_pageId + " #password3 .placeholderPsd").html('');
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        $(_pageId + " #password1 .cursor-bink").hide()
        $(_pageId + " #password2 .cursor-bink").hide()
        $(_pageId + " #password3 .cursor-bink").hide()
        monkeywords.close();
        appUtils.pageBack();
    }

    var updateUserPassword = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = updateUserPassword;
});
