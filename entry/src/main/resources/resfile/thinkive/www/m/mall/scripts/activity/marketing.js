// 广告模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        gconfig = require("gconfig"),
        _page_code = "activity/marketing",
        _pageId = "#activity_marketing ";
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    let heighEndProduct //new 一个 vue 实例
    var bottomImg
    var headImg
    var contentImg
    let fundcode

    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150({ templateId: appUtils.getPageParam().templateId}, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "")
            })
        })
    }

    async function init() {
        // console.log(appUtils.getPageParam().templateId,111)
        let html =  await setTemplate()
        $(".main_marketing").html(html)   //渲染模板
      
        heighEndProduct = new Vue({
            el: '#main_marketing',
            data() {
                return {
                    detailsInfo: {}//详情信息
                }
            },
            //视图 渲染前
            created() {

               
            },
            //渲染完成后
            mounted() {

                bottomImg = global.oss_url + $(".main_marketing #bottom_img_f").html();
                headImg = global.oss_url + $(".main_marketing #head_img_f").html();
                contentImg = global.oss_url + $(".main_marketing #content_img_f").html();
                $(_pageId + " .main_marketing .marketing_bac").attr("style", "background:url("+headImg+")");
                $(_pageId + ".main_marketing #content_img").attr("src", contentImg);
                $(_pageId + ".main_marketing #bottom_img").attr("src", bottomImg);
                fundcode = $(_pageId + " .marketing_desc").attr("fundcode");
                if(fundcode && fundcode !=""){
                    this.getDetails(); // 获取特色数据
                }
            },
            //计算属性
            computed: {
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
            },
            //绑定事件
            methods: {

                //获取产品详情
                getDetails() {
                    let data = {
                        fund_code: fundcode
                    }
                    service.reqFun102113(data, (datas) => {
                        if (datas.error_no == '0') {
                            let res = datas.results[0]
                            this.detailsInfo = res
                            res.nav_date = tools.FormatDateText(res.nav_date.substr(4));//收益日
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                },
                toOtherPage(){
                    let pageName = $(_pageId + " .marketing_desc").attr("pageName");
                    let busi_id = $(_pageId + " .marketing_desc").attr("fundcode");
                    let pageInfo = $(_pageId + " .marketing_desc").attr("pageInfo");
                    var urlInfo = {};//url的参数信息
                    urlInfo["busi_id"] = busi_id
                    appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                    if(pageInfo !=""){	
                    	 appUtils.pageInit(_page_code, pageName,{pageInfo: pageInfo});
                    }else{
                    	 appUtils.pageInit(_page_code, pageName,urlInfo);
                    	 }
                   
                },

            	
            }
               
             
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });


    }
    //页面销毁
    function destroy() {
        $(_pageId + " .header_inner #title").html("");
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        headImg = "";
        bottomImg = "";
        contentImg= "";
        $(".main_marketing").html()
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
