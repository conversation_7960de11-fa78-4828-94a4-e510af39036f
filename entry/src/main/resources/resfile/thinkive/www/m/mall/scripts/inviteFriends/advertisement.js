 // 外链文章
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		common = require("common"),
		validatorUtil = require("validatorUtil"),
		des = require("des"),
		service = require("mobileService"),
		_pageId = "#guide_advertisement ";
	var share_url = "";
	var article_id = "";
	var prePage_code = "";
	var name = "";// 分享标题
    var description = "";// 分享描述
//    var small_picture = appUtils.getPageParam("small_picture");// 小图片地址
    var small_picture = "";// 小图片地址
	var gconfig = require("gconfig");
	var global = gconfig.global;
	var platform = gconfig.platform;
	function init(){
//		var flag=appUtils.getPageParam("flag");

		 var  url = appUtils.getPageParam("url");

		 if(url==""||url==null){
	        	url=appUtils.getSStorageInfo("extra_url");
	     }
		        var first_split = url.split("//");

				var without_resource = first_split[1];

				var second_split = without_resource.split("/");
				   var host_url = second_split[0];

				   var cust_no=appUtils.getSStorageInfo("custNo");

				   if(cust_no == undefined || cust_no==null || cust_no=="null" || cust_no=="" ){
					   url=setUrlParam("r",Math.random(),url);
					   share_url=url;
					   $(_pageId+" #guangaolianjie").attr("src",url);
				   }else{
					   var param={"cust_no":cust_no,"host_url":host_url};
						service.getUserAuthorization(param,function(data){
							if(data.error_no == 0){
								var app_secret=data.results[0].app_secret;
								url=setUrlParam("token",app_secret,url);
							}
							url=setUrlParam("r",Math.random(),url);
							 share_url=url;
							$(_pageId+" #guangaolianjie").attr("src",url);
						});

				   }


        article_id = appUtils.getPageParam("article_id");// 文章id
        prePage_code = appUtils.getPageParam("prePage_code");// 文章id
        name = appUtils.getPageParam("name");//
        description = appUtils.getPageParam("description");//
        small_picture = appUtils.getPageParam("small_picture");//

	}

	//绑定事件
	function bindPageEvent(){
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		},"click");
		//邀请好友
		appUtils.bindEvent($(_pageId+" #share_img"),function(){
			// 判断是否登录
			 var loginBs = appUtils.getSStorageInfo("isAuthenticated");
			 var indexLogin = appUtils.getPageParam("indexLogin");
			 if(loginBs||indexLogin!="yes"){
				 // 登录跳转到对应的页面
					$(_pageId+" #pop_layer").show();
			 }else{
				 common.gestureLogin("guide/advertisement");
				// appUtils.pageInit( "inviteFriends/friendInvitation","login/userLogin",{"skipURL":"inviteFriends/friendInvitation"});
			 }
		});
		//微信好友
		appUtils.bindEvent($(_pageId+" #share_WeChat"),function(){
			share("22","0");
		});
		//微信朋友圈
		appUtils.bindEvent($(_pageId+" #share_WeChatFriend"),function(){
			share("23","1");
		});
		//腾讯QQ
		appUtils.bindEvent($(_pageId+" #share_qq"),function(){
			share("24","2");
		});
		//新浪微博
		appUtils.bindEvent($(_pageId+" #share_sinaWeibo"),function(){
			share("1","4");
		});
		//腾讯微博
		appUtils.bindEvent($(_pageId+" #share_tenxun"),function(){
			share("2","5");
		});

		//取消邀请好友
		appUtils.bindEvent($(_pageId+" #cancelShare"),function(){
			$(_pageId+" #pop_layer").hide();
		});
	}

	function setUrlParam(paramName,value,url)
	{

		if (url.indexOf("?") != -1)
		{
			url = url + "&"+paramName+"="+value;
		}
		else
		{
			url = url + "?"+paramName+"="+value;
		}
		return url;
	}


	function share(shareTypeList,share_type){
		// if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
		// 简化分享链接
		var mobile = appUtils.getSStorageInfo("user").mobileWhole;
		var query_params = {};
		query_params["mobile"] = mobile;
		query_params["share_type"]=share_type;
		service.getShareInfo(query_params,function(data)
		{
			var error_no = data.error_no,
			error_info = data.error_info;
			if(error_no == "0"){
				if(data.results!=undefined && data.results.length>0)
				{
					mobile=desEncrypt("mobile",mobile);

					var result=data.results[0];

					var share_url=result.share_url;

					if (validatorUtil.isEmpty(share_url)){
						share_url=global.link;
					}


					if (share_url.indexOf("?") != -1)
					{
						share_url = share_url + "&mobile="+mobile;
					}
					else
					{
						share_url = share_url + "?mobile="+mobile;
					}

					var img_url=result.img_url;

					if (validatorUtil.isEmpty(img_url)){
						img_url=global.imgUrl;
					}

					var content=result.content;

					var title=result.title;

					var params = {};
					params["url"] = share_url;
//					content="国有理财晋金所，安全稳健收益佳！";
					if(shareTypeList == "23"){
						layerUtils.iMsg(-1,"启动分享中...请稍后！",2);
						$(_pageId+" #pop_layer").hide();
						var param = {};
						param["moduleName"] = "mall";
				        param["funcNo"] = "50231";
				        param["shareType"] =shareTypeList;//平台字典
				        param["title"] = title;
				        param["link"] =share_url;
				        param["content"] =content; //分享文本
				        param["imgUrl"] =img_url;
				        require("external").callMessage(param);
					}else{
						service.simplifyURL(params,function(data) {
							var error_no = data.error_no,
								error_info = data.error_info;
							if(error_no == "0"){
								share_url = data.results[0].shortUrl;
								layerUtils.iMsg(-1,"启动分享中...请稍后！",2);
								$(_pageId+" #pop_layer").hide();
								var param = {};
								param["moduleName"] = "mall";
						        param["funcNo"] = "50231";
						        param["shareType"] =shareTypeList;//平台字典
						        param["title"] = title;
						        param["link"] =share_url;
						        param["content"] =content; //分享文本
						        param["imgUrl"] =img_url;
						        require("external").callMessage(param);

							}else{
								layerUtils.iAlert(error_info);
							}
						});
					}

				}
			}
		});

	}

	function destroy(){
		$(_pageId+" #guangaolianjie").attr("src","");
		$(_pageId+" #pop_layer").hide();
	}

	function desEncrypt(key,value) {
	    var keyHex = des.enc.Utf8.parse(key);
	    var valueHex = des.enc.Utf8.parse(value);
	    var encrypted = des.DES.encrypt(valueHex, keyHex, {
	        mode: des.mode.ECB,
	        padding: des.pad.Pkcs7
	    });
		return encrypted.toString();
	}
	function pageBack() {
		appUtils.pageBack();
	}

	var advertisement = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = advertisement;
});
