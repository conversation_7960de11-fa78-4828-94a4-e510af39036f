// 引流注册-基金开户
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        service = require("mobileService"),
        _pageId = "#drainage_openAccount ",
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        _pageCode = "drainage/openAccount";
    var ut = require("../common/userUtil");
    var sms_mobile = require("../common/sms_mobile");
    var tools = require("../common/tools");
    var jjsInfo,bank_serial_no; //晋金所授权信息
    var send_sms_flag;
    function init() {
        if (appUtils.getSStorageInfo("openLeavePage")) { //如果从当前页面暂时离开，页面所有数据不销毁、重置
            appUtils.clearSStorage("openLeavePage");
            return;
        }
        sms_mobile.init(_pageId);
        // tools.getPdf("6");
        if (appUtils.getSStorageInfo("reUploadIdCard") == "1") {
            $(_pageId + " #idCard").attr("disabled", "disabled");
            $(_pageId + " .uploadStatus").text("已上传").removeClass("text_red");
            $(_pageId + " .retryUpload").text("").hide();
        } else if (appUtils.getSStorageInfo("reUploadIdCard") == "0") {
            $(_pageId + " .uploadStatus").text("上传失败").addClass("text_red");
            $(_pageId + " .retryUpload").text("重新上传").show();
        } else {
            $(_pageId + " .retryUpload").text("去上传").show();
            $(_pageId + " .uploadStatus").text("未上传").addClass("text_red");
        }
        // 已上传过照片，不二次渲染晋金所信息
        if (appUtils.getSStorageInfo("reUploadIdCard")) {
            appUtils.clearSStorage("reUploadIdCard");
            return;
        }
        getUserInfo();
    }

    // 绑定事件
    function bindPageEvent() {
        // 点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class");
            if (classname == "active") {
                $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
                $(_pageId + " #bk").css({backgroundColor: "#D1D4D5"});
            } else {
                $(_pageId + " .rule_check #xuanzeqi i").addClass("active");
                $(_pageId + " #bk").css({backgroundColor: "#E5433B"});

            }
        });
        // 点击下一步
        appUtils.bindEvent($(_pageId + " #bk"), function () {
            // var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            var bankCode = $(_pageId + " #bankname").attr("bank_code");
            var bankCard = $(_pageId + " #bankCard").val();
            var inviter = $(_pageId + " #inviter").val(); //邀请人手机号
            bankCard = bankCard.indexOf("*") > -1 ? jjsInfo.bank_acct : bankCard.replace(/ /g, "");
            var bank_reserved_mobile = $(_pageId + " #yhmPhone").val();
            bank_reserved_mobile = bank_reserved_mobile.indexOf("*") > -1 ? jjsInfo.bank_reserved_mobile : bank_reserved_mobile;
            var idcard = $(_pageId + " #idCard").val(); //身份证
            idcard = idcard.indexOf("*") > -1 ? jjsInfo.cert_no : idcard;
            if (!validatorUtil.isEmpty(inviter) && !validatorUtil.isMobile(inviter)) { //邀请人不为空
                layerUtils.iMsg(-1, "请输入正确邀请人号码");
                return;
            }
            if(inviter == ut.getUserInf().mobileWhole) {
                layerUtils.iMsg(-1, "邀请人不能为本人");
                return;
            }
            if (!validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确银行卡号");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").html())) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (validatorUtil.isEmpty(bankCode)) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (!validatorUtil.isMobile(bank_reserved_mobile)) {
                layerUtils.iMsg(-1, "请输入正确手机号码");
                return;
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            // if (classname != "active") {
            //     layerUtils.iAlert("请您签署协议");
            //     return;
            // }
            var idCardInfo = appUtils.getSStorageInfo("idCardInfo");
            // var live_address = idCardInfo.living_address.code.split(" ");
            var param = {
                "bank_code": $(_pageId + " #bankname").attr("bank_code"),     //银行编码
                "cardNo": bankCard,//银行卡号
                "cust_name": jjsInfo.name,
                "cert_no": idcard,
                "bank_reserved_mobile": bank_reserved_mobile,
                "sms_mobile": bank_reserved_mobile,
                "mobile": bank_reserved_mobile,
                "sms_code": verificationCode,
                "message_code":verificationCode,
                "bank_serial_no":bank_serial_no,
                "recommend": $(_pageId + " #inviter").val(),
                "cert_type": "0",
                "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                // "vocation_code": idCardInfo["vocation_code"].id,
                // "cust_address": idCardInfo.address,
                // "sex": idCardInfo.sex,
                // "vaild_date": idCardInfo.vaild_date,
                "bank_acct": bankCard,
                "bank_name": $(_pageId + " #bankname").text(),
                // "income": idCardInfo["year_income"].money*10000,
                // "living_address_province":(live_address[0]),
                // "living_address_city":(live_address[1]),
                // "living_address_county":(live_address[2]),
            };

            service.reqFun101061(param, function (data) {
                if (data.error_no == "0") {
                    checkValidIdCard({
                        idcard: idcard, //身份证号
                        name: jjsInfo.name //姓名
                    }, function (valid,error_no,error_info) { //校验成功进入下一页
                        if(valid == "0") {
                            if(error_no == "2" || error_no == "103"){
                                let setBankInfo = {
                                    error_reason:error_info
                                }
                                appUtils.setSStorageInfo("setBankInfo",setBankInfo)
                                layerUtils.iConfirm("您的身份资料可能有误是否进行下一步", function () {
                                }, function () {
                                    appUtils.setSStorageInfo("bankAccInfo", param);
                                    appUtils.setSStorageInfo("reUploadIdCard", "1");//阻止页面回退上产信息消失
                                    appUtils.pageInit(_pageCode, "drainage/uploadIDCard");
                                    //点击取消无操作
                                }, "是", "否");
                            }else{
                                return
                            }
                        }else{
                            appUtils.setSStorageInfo("bankAccInfo", param);
                            appUtils.setSStorageInfo("reUploadIdCard", "1");//阻止页面回退上产信息消失
                            appUtils.pageInit(_pageCode, "drainage/uploadIDCard");
                        }
                    })
                } else {
                    layerUtils.iLoading(false);
                    sms_mobile.clear();
                    $(_pageId + " #verificationCode").val("");
                    layerUtils.iAlert(data.error_info);
                }
            }, {isLastReq: false})

        });

        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            var idcard = $(_pageId + " #idCard").val(); //身份证
            idcard = idcard.indexOf("*") > -1 ? jjsInfo.cert_no : idcard;
            var bankCard = $(_pageId + " #bankCard").val();
            bankCard = bankCard.indexOf("*") > -1 ? jjsInfo.bank_acct : bankCard.replace(/ /g, "");
            var bank_reserved_mobile = $(_pageId + " #yhmPhone").val();
            bank_reserved_mobile = bank_reserved_mobile.indexOf("*") > -1 ? jjsInfo.bank_reserved_mobile : bank_reserved_mobile;
            if (!validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确银行卡号");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").html())) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #bankname").attr("bank_code"))) {
                layerUtils.iMsg(-1, "不支持的银行卡");
                return;
            }
            if (!validatorUtil.isMobile(bank_reserved_mobile)) {
                layerUtils.iMsg(-1, "请输入正确手机号码");
                return;
            }
            // if ($(_pageId + " .uploadStatus").text() != "已上传") {
            //     layerUtils.iConfirm("您还未上传身份证照片", function () {
            //         appUtils.setSStorageInfo("openLeavePage", true);
            //         appUtils.pageInit(_pageCode, "drainage/uploadIDCard", {});
            //     }, function () {
            //         return;
            //     }, "上传", "取消");
            //     return;
            // }
            // 获取验证码
            var param = {
                "bank_code": $(_pageId + " #bankname").attr("bank_code"),//银行编码
                "pay_type": $(_pageId + " #bankname").attr("pay_type"),
                "payorg_id": $(_pageId + " #bankname").attr("payorg_id"),
                "bank_acct": bankCard,     // 用户卡号
                "cert_no": idcard,   // 用户身份证
                "bank_reserved_mobile":bank_reserved_mobile,
                "bank_name":$(_pageId + " #bankname").text(),
                "sms_type":common.sms_type.bindCard,
                "send_type": "0",//发送短信验证码
                "cust_name": jjsInfo.name, // 用户姓名
                "cert_type": "0", //证件类型
                "mobile_phone": bank_reserved_mobile,
                "type": common.sms_type.bindCard,
            };
            // let payorg_id = $(_pageId + " #bankname").attr("payorg_id")
            if(send_sms_flag == '1'){
                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
                
            }else{
                sms_mobile.sendPhoneCode(param);
            }
            // sms_mobile.sendPhoneCode(param);
        });

        // 重新上传身份证照片
        appUtils.bindEvent($(_pageId + " .retryUpload"), function () {
            var idcard = $(_pageId + " #idCard").val();
            idcard = idcard.indexOf("*") > -1 ? jjsInfo.cert_no : idcard;
            if (!idcard) {
                layerUtils.iAlert("请输入身份证号");
                return;
            }
            if (!validatorUtil.isCardID(idcard)) {
                layerUtils.iMsg(-1, "证件号码格式错误");
                return;
            }

            var userInfo = ut.getUserInf();
            userInfo.identityNum = idcard;
            ut.saveUserInf(userInfo);
            appUtils.setSStorageInfo("openLeavePage", true);
            appUtils.pageInit(_pageCode, "drainage/uploadIDCard");
        });
        // 查看银行卡限额
        appUtils.bindEvent($(_pageId + " .bankInfo"), function () {
            appUtils.setSStorageInfo("openLeavePage", true);
            appUtils.setSStorageInfo("isShowChangeBank",'0')
            appUtils.pageInit(_pageCode, "safety/bankInfo");
        });
        // 点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        // 重新开户
        appUtils.bindEvent($(_pageId + " #retryOpenAccount"), function () {
            appUtils.pageInit(_pageCode, "account/setBankCard");
        });
        //银行卡号获得焦点
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            // 控制全数字输入
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");
        // 银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            bankDistinguish(true);
        }, "blur");
        // 更换银行卡
        appUtils.bindEvent($(_pageId + " .changeBankCard"), function () {
            layerUtils.iConfirm("修改银行卡将影响晋金宝互转功能", function () {
                $(_pageId + " #bankCard").val("").removeAttr("disabled");
                $(_pageId + " #bankname").text("").removeAttr("bank_code");
                $(_pageId + " .limit").html("");
            }, function () {
                return;
            }, "继续修改", "取消");
            return;
            
        });
        // 更换银行预留手机号
        appUtils.bindEvent($(_pageId + " .changeBankMobile"), function () {
            $(_pageId + " #yhmPhone").val("").removeAttr("disabled");
        });
        // 身份证输入事件
        appUtils.bindEvent($(_pageId + " #idCard"), function() {
            var curVal = this.value;
            curVal = curVal.replace(/[\u4E00-\u9FA5\uF900-\uFA2D]/g, "");
            curVal = curVal.replace(/[^\a-\z\A-\Z0-9]/g, '');
            this.value = curVal;
        }, "input");

    }
    function BankByCard(param){
        service.BankByCard(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {
                $(_pageId + " .place").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    var bank_name = result.bank_name;
                    var bank_code = result.bank_code;
                    send_sms_flag = result.send_sms_flag;
                    $(_pageId + " #bankname").attr("payorg_id", result.payorg_id);//支付机构ID
                    $(_pageId + " #bankname").attr("pay_type", result.pay_type);//支付方式
                    var limitStr = "";
                    if (result.single_limit) {
                        if (result.single_limit < 0) {
                            limitStr += '银行卡单笔限额：<span style="color: #000;font-weight: bold;">不限</span>';
                        } else {
                            limitStr += '银行卡单笔限额：<span style="color: #000;font-weight: bold;">' + result.single_limit + '元</span>';
                        }
                    }
                    if (result.day_limit) {
                        if (result.day_limit < 0) {
                            limitStr += '单日限额：<span style="color: #000;font-weight: bold;">不限</span>';
                        } else {
                            limitStr += '单日限额：<span style="color: #000;font-weight: bold;">' + result.day_limit + '元</span>';
                        }
                    }
                    $(_pageId + " .limit").html(limitStr);
                    // 识别出来填写银行卡编号 和银行名称
                    $(_pageId + " #bankname").html(bank_name);
                    $(_pageId + " #bankname").attr("bank_code", bank_code);
                } else {
                    $(_pageId + " .limit").html("");
                    $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }

            } else {
                // 识别失败时候 去掉银行信息
                $(_pageId + " .limit").html("");
                $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                layerUtils.iMsg(-1, error_info);
            }
        });
    }
    // 失去焦点时候验证银行限额 银行名称
    function bankDistinguish() {
        $(_pageId + " #pop_view").css("visibility", "hidden");
        $(_pageId + " #big_show_bank").html("");
        var bankCard = $(_pageId + " #bankCard").val();
        bankCard = bankCard.replaceAll(" ", "")
        if (validatorUtil.isEmpty(bankCard)) {
            $(_pageId + " #bankname").html("").removeAttr("bank_code");
            layerUtils.iMsg(-1, "银行卡号不能为空");
            return;
        }
        if (!validatorUtil.isBankCode(bankCard)) {
            $(_pageId + " #oneMoney").html("");
            $(_pageId + " #drxe").html("");
            layerUtils.iMsg(-1, "请输入正确银行卡号");
            return;
        }
        var bin_id = $(_pageId + " #bankCard").val();
        bin_id = bin_id.replaceAll(" ", "");
        var param = {
            "bin_id": bin_id
        };

        service.BankByCard(param, function (data) {
            var error_info = data.error_info,
                error_no = data.error_no;
            if (error_no == "0") {
                $(_pageId + " .place").show();
                if (data.results.length > 0) {
                    var result = data.results[0];
                    var bank_name = result.bank_name;
                    var bank_code = result.bank_code;
                    send_sms_flag = result.send_sms_flag;
                    $(_pageId + " #bankname").attr("payorg_id", result.payorg_id);//支付机构ID
                    $(_pageId + " #bankname").attr("pay_type", result.pay_type);//支付方式
                    var limitStr = "";
                    if (result.single_limit) {
                        if (result.single_limit < 0) {
                            limitStr += '银行卡单笔限额：<span style="color: #000;font-weight: bold;">不限</span>';
                        } else {
                            limitStr += '银行卡单笔限额：<span style="color: #000;font-weight: bold;">' + result.single_limit + '元</span>';
                        }
                    }
                    if (result.day_limit) {
                        if (result.day_limit < 0) {
                            limitStr += '单日限额：<span style="color: #000;font-weight: bold;">不限</span>';
                        } else {
                            limitStr += '单日限额：<span style="color: #000;font-weight: bold;">' + result.day_limit + '元</span>';
                        }
                    }
                    $(_pageId + " .limit").html(limitStr);
                    // 识别出来填写银行卡编号 和银行名称
                    $(_pageId + " #bankname").html(bank_name);
                    $(_pageId + " #bankname").attr("bank_code", bank_code);
                } else {
                    $(_pageId + " .limit").html("");
                    $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                    layerUtils.iMsg(-1, "不支持的银行卡");
                    return;
                }

            } else {
                // 识别失败时候 去掉银行信息
                $(_pageId + " .limit").html("");
                $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
                layerUtils.iMsg(-1, error_info);
            }
        });
    }

    // 获取晋金所信息
    function getUserInfo() {
        if (appUtils.getSStorageInfo("bankAccInfo")) {
            return;
        }
        service.reqFun101048({mobile: ut.getUserInf().mobileWhole}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                // console.log(results)
                let param = {
                    "bin_id": results.bank_acct
                }
                var userInfo = ut.getUserInf();
                userInfo.name = results.name;
                userInfo.identityNum = results.cert_no;
                userInfo.bankReservedMobile = results.bank_reserved_mobile;
                ut.saveUserInf(userInfo);
                jjsInfo = results;
                var inviter = results.inviter; // 是否有邀请人  1 有  0没有
                if (inviter == "1") {
                    $(_pageId + " #inviterBox").hide();
                } else {
                    $(_pageId + " #inviterBox").show();
                }
                
                if (results.bank_acct && results.bank_code && results.bank_name && results.single_limit) {
                    BankByCard(param)
                    $(_pageId + " #bankCard").val(results.bank_acct.substr(0, 4) + "****" + results.bank_acct.substr(-4)).attr("disabled", "disabled");
                    // $(_pageId + " #bankname").html(results.bank_name).attr("bank_code", results.bank_code);
                    $(_pageId + " #yhmPhone").val(results.bank_reserved_mobile.substr(0, 3) + "****" + results.bank_reserved_mobile.substr(-4)).attr("disabled", "disabled");
                } else {
                    $(_pageId + " #yhmPhone").val("").removeAttr("disabled");
                    $(_pageId + " #bankCard").val("").removeAttr("disabled");
                    $(_pageId + " #bankname").html("").removeAttr("bank_code");
                }
                var limitStr = "";
                // if (results.single_limit) {
                //     if (results.single_limit < 0) {
                //         limitStr += '银行卡单笔限额：<span style="color: #000;font-weight: bold;">不限</span>';
                //     } else {
                //         limitStr += '银行卡单笔限额：<span style="color: #000;font-weight: bold;">' + results.single_limit + '元</span>';
                //     }
                // }
                // if (results.day_limit) {
                //     if (results.day_limit < 0) {
                //         limitStr += '单日限额：<span style="color: #000;font-weight: bold;">不限</span>';
                //     } else {
                //         limitStr += '单日限额：<span style="color: #000;font-weight: bold;">' + results.day_limit + '元</span>';
                //     }
                // }
                // $(_pageId + " .limit").html(limitStr);
                $(_pageId + " #cardPerson").val(results.name);
                $(_pageId + " #idCard").val(results.cert_no.substr(0, 4) + "****" + results.cert_no.substr(-4));
                // checkValidIdCard({
                //     idcard: results.cert_no, //身份证号
                //     name: results.name //姓名
                // }, function (valid) {
                //     if(valid == "1") {
                //         $(_pageId + " #idCard").attr("disabled", "disabled");
                //     } else {
                        $(_pageId + " #idCard").removeAttr("disabled")
                //     }
                // })
            } else {
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, data.error_info);
            }
        });
    }

    /* 校验身份证有效性*/
    function checkValidIdCard(param, callback) {
        service.reqFun102079(param, function (data) {
            // if (data.error_no == 0) {
            //     if (callback) callback("1");
            // } else {
            //     if (callback) callback("0",data.error_no,data.error_info);
            //     layerUtils.iAlert(data.error_info);
            // }
            if (data.error_no != "0") {
                if(data.error_no == "2" || data.error_no == "103"){
                    let setBankInfo = {
                        error_reason:data.error_info
                    }
                    layerUtils.iLoading(false);
                    appUtils.setSStorageInfo("setBankInfo",setBankInfo)
                    layerUtils.iConfirm("您的身份资料可能有误是否进行下一步？", function () {
                        if (callback) callback("1");
                    }, function () {
                        //点击取消无操作
                    }, "是", "否");
                    
                }else{
                    if (callback) callback("0",data.error_no,data.error_info);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
            }else{
                if (callback) callback("1");
            }
        })
    }

    function destroy() {
        if (appUtils.getSStorageInfo("openLeavePage")) {
            return;
        }
        send_sms_flag = null;
        if (appUtils.getSStorageInfo("bankAccInfo")) {
            sms_mobile.destroy();
            $(_pageId + " #verificationCode").val("");
            return;
        }

        $(_pageId + " #bankname").text("").removeAttr("bank_code").removeAttr("payorg_id").removeAttr("pay_type");
        $(_pageId + " .limit").html("");
        $(_pageId + " input").val("");
        $(_pageId + " .retryUpload").text("").hide();
        $(_pageId + " .uploadStatus").text("");
        $(_pageId + " #inviterBox").hide();
        $(_pageId + " #bankCard").attr("disabled", "disabled");
        $(_pageId + " #yhmPhone").attr("disabled", "disabled");
        $(_pageId + " #idCard").attr("disabled", "disabled");

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var setBankCardInfo = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
// 暴露对外的接口
    module.exports = setBankCardInfo;
})
;
