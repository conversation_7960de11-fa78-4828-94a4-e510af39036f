<div class="page" id="jjsfund_toJJCF" data-pageTitle="转入晋金财富" data-isSaveDom="false" data-refresh="true"
     style="-webkit-overflow-scrolling : touch;">
    <div class="pop_layer" style="display:none;position: absolute;">
        <div class="password_box">
            <div class="password_inner slidedown in">
                <a href="javascript:void(0);" id="close" class="close_btn"></a>
                <h4>请输入<span>6</span>位交易密码</h4>
                <h6 id="rechargeInfo"></h6>
                <div class="password_input">
                    <span id="span00"></span>
                    <span id="span01"></span>
                    <span id="span02"></span>
                    <span id="span03"></span>
                    <span id="span04"></span>
                    <span id="span05"></span>
                    <input type="text" id="jymm" maxlength="6" style="display:none;">
                </div>
                <a href="javascript:void(0);" id="queDing" class="sure_btn text-center">确定</a>
            </div>
        </div>
    </div>
    <section class="main fixed" data-page="home" id="product" style="padding-bottom: 0;padding-top: 0">
        <header class="header">
            <div class="header_inner bg_header">
                <a herf="#" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">转入晋金财富</h1>
            </div>
        </header>
        <article>
            <!-- CASH_BOX START -->
            <div class="cash_box">
                <div class="available_cash">
                    <h2 class="text-center">可转金额(元)</h2>
                    <strong class="block text-center" id="available_vol">--</strong>
                </div>
                <div class="grid_03 grid_02 grid">
                    <div class="ui field text input_box2 has_border">
                        <label class="short_label">转入金额</label>
                        <span style="position:absolute;height: 0.44rem;left:0.8rem ;right: 0;"
                              id='inputspanid'><span
                                style="position: absolute;left: 0px;bottom: 0;height: 0.44rem;width: auto;line-height: 0.44rem;"
                                text='请输入转入金额'>请输入转入金额</span></span>
                        <input custom_keybord="0" type="text" onkeyup="value=value.replace(/[^\d\.\,]/g,'')"
                               onblur="value=value.replace(/[^\d\.\,]/g,'')" id="money" maxlength="12"
                               class="ui input" placeholder="请输入转入金额"
                               style="outline: none;border: none;margin-left:5px;display: none;"
                               readonly="readonly">
                    </div>
                </div>
                <div class="grid_03 grid_02 grid mt10">
                    <div class="ui field text input_box2 has_border" id="yzmBox">
                        <label class="short_label2">验证码</label>
                        <input type="tel" class="ui input code_input" id="yzm" type="tel" maxlength="6" placeholder="">
                        <a id="getYzm" data-state="true">获取验证码</a>
                    </div>
                </div>
                <div class="finance_det recharge_det">
                    <dl class="bank_limit">
                        <dd id="weihao" style="display:none"><dd>
                    </dl>
                </div>
                <div class="finance_det recharge_det">
                    <dl class="bank_limit">
                        <dd id="talkCode" style="display: block;">晋金财富将致电您的手机语音告知验证码</dd>
                    </dl>
                </div>
                <div class="agreement">

                </div>
                <div class="grid_02">
                    <a href="javascript:void(0);" id="nextStep" class="ui button block rounded btn_next pop in">下一步</a>
                </div>
                <!-- 协议相关弹窗 -->
                <div class="agreement_layer display_none">
                    <div class="agreement_popup in">
                        <div class="agreement_popup_header">
                            <div class="new_close_btn"></div>
                            <h3>相关协议</h3>
                        </div>
                        <ul class="agreement_list flex vertical_line"></ul>
                    </div>
                </div>
            </div>
            <!-- CASH_BOX END -->
        </article>
    </section>
</div>
