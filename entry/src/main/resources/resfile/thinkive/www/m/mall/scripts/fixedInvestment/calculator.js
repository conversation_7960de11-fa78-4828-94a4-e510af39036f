// 定投计算器
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        common = require("common"),
        vIscroll = {"scroll": null, "_init": false},
        _pageCode = "fixedInvestment/calculator",
        _pageId = "#fixedInvestment_calculator ";
    var ut = require("../common/userUtil");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var monkeywords = require("../common/moneykeywords");
    var tools = require("../common/tools");
    let weekList = ['', '周一', '周二', '周三', '周四', '周五'] //每周定投日期
    let investcycle = '2';
    let investdate = '1';
    let firstText = '';
    let secoundText = '';
    var productInfo;
    let getProDetailData; //定投计算器产品相关参数
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #inputspanid").html('');
        $(_pageId + " #inputspanid").removeClass('m_text_darkgray');
        $(_pageId + " #inputspanid").addClass('m_text_999');
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " .prod_sname").text(productInfo.prod_sname ? productInfo.prod_sname : productInfo.fund_sname);
        firstText = '每月';
        investcycle = '3';
        investdate = '1';
        secoundText = '1日';
        $(_pageId + ".olay").remove();
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        $(_pageId + " .investmentText").text('每月 1日');
        //渲染每月 每周 每两周 日期数据
        setChooseTime(0);
        //获取定投限额，限定日期
        getProDetail();
        //初始化
        resetInputDate();
    }
    function getProDetail(){
        let fundCode = productInfo.fund_code
        service.reqFun102159({fundCode:fundCode}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            let result = data.results[0]
            getProDetailData = result;
            $(_pageId + " #inputspanid").html('最低定投金额' + tools.fmoney(getProDetailData.minAmt) + '元') 
            selectDate(_pageId + '#startTime', 0, _pageId,result.minStartDate);
            selectDate(_pageId + '#endTime', 1, _pageId,result.minStartDate);
        })
    }
    //重置时间框
    function resetInputDate() {
        $(_pageId + '#startTime').attr("time", "");
        $(_pageId + '#startTime').val("");
        $(_pageId + '#endTime').attr("time", "");
        $(_pageId + '#endTime').val("");
    }
    //初始化
    function setChooseTime(num) {
        let str = ''
        if (num == 0) {
            //月
            secoundText = '1日'
            for (let i = 1; i <= 28; i++) {
                let childStr = `<li operationType="1" operationId="date_${i}" operationName="${i + '日'}" class="${i == 1 ? 'active' : ''}" id="${i}">${i + '日'}</li>`
                str += childStr
            }
        } else {
            //周
            secoundText = '周一'
            for (let i = 1; i < weekList.length; i++) {
                let childStr = `<li operationType="1" operationId="week_${i}" operationName="${weekList[i]}" class="${i == 1 ? 'active' : ''}" id="${i}">${weekList[i]}</li>`
                str += childStr
            }
        }
        investdate = '1'
        $(_pageId + ' .listRight').html(str)
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //选择周，两周，月
        appUtils.preBindEvent($(_pageId + " .listLeft"), "li", function () {
            $(_pageId + " .listLeft li").removeClass('active')
            $(this).addClass('active')
            investcycle = $(this).attr('id')
            firstText = $(this).text()
            if (investcycle == 3) {
                setChooseTime(0)
            } else {
                setChooseTime()
            }
        }, 'click');
    }
    //选择日期
    appUtils.preBindEvent($(_pageId + " .listRight"), "li", function () {
        $(_pageId + " .listRight li").removeClass('active');
        $(this).addClass('active');
        //文案初始化
        investdate = $(this).attr('id');
        secoundText = $(this).text();
        //参数初始化
        // investcycle = '2';
        // investdate = '1';
    }, 'click');
    //点击取消时间控件
    appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
        //取消冒泡
        if (e || e.stopPropagation) {
            e.stopPropagation();
        } else {
            window.event.CancelBubble = true;
        }
        //获取事件源
        var node = $(e.srcElement || e.target);
        if (node.hasClass("olay")) {
            $(_pageId + " .olay").remove();
        }
    }, "click");
    //选择定投日期
    appUtils.bindEvent($(_pageId + " .cycleClick"), function () {
        $(_pageId + " .pop_layer").show();
        $(_pageId + " #cycleModel").show()
    });
    //确定选择定投日期
    appUtils.bindEvent($(_pageId + " .determine"), function () {
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #cycleModel").hide()
        $(_pageId + " .investmentText").text(firstText + ' ' + secoundText)
        // getNextTime(investcycle, investdate)
    });
    //关闭数字键盘
    appUtils.bindEvent($(_pageId), function () {
        monkeywords.close();
    });
    //输入金额弹出数字键盘
    appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
        $(_pageId + " .pop_text").show();
        event.stopPropagation();
        $(_pageId + " #czje").val('');
        //键盘事件
        moneyboardEvent();
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "fixedInvestment_calculator";
        param["eleId"] = "czje";
        param["doneLable"] = "确定";
        param["keyboardType"] = "3";
        require("external").callMessage(param);
    });
    //点击立即定投
    appUtils.bindEvent($(_pageId + " .fixedInvestment"), function () {
        appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
        if (!common.loginInter(_pageCode)) return;
        if (!ut.hasBindCard(_pageCode)) return;
        //校验用户是否上传过身份证照片
        if(!ut.getUploadStatus()){
            let operationId = 'noUploadIdCard'
            appUtils.setSStorageInfo("noUploadIdCard", '1');
            return layerUtils.iConfirm("您还未上传身份证照片", function () {
                appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
            }, function () {
            }, "去上传", "取消",operationId);
        }
        let info = ut.getUserInf()
        if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation(_pageCode)
        if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
        common.changeCardInter(() => {
            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                layerUtils.iConfirm("您还未进行风险测评", function () {
                    appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                }, function () {
                }, "去测评", "取消");
                return;
            }
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.setSStorageInfo("isAdvisoryInvestment", '');
            appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
        });
    });
    //关闭周期弹框
    appUtils.bindEvent($(_pageId + " #closeCycle"), function () {
        $(_pageId + " #cycleModel").hide();
        $(_pageId + " .pop_layer").hide();
    });
    //立即计算
    appUtils.bindEvent($(_pageId + " .calculation"), function () {
        let startTime = $(_pageId + " #startTime").attr("time");
        let endTime = $(_pageId + " #endTime").attr("time");
        var curVal = $(_pageId + " #czje").val();
        var moneys = curVal.replace(/,/g, "");
        moneys = moneys*1
        // moneys = '1000';
        if (moneys <= 0 || !moneys) {
            layerUtils.iAlert("请输入定投金额");
            return;
        }
        // if(moneys > 100000){
        //     layerUtils.iAlert("试算金额不能超过10万元，请重新输入");
        //     return
        // }
        let maxAmt = getProDetailData.maxAmt ? getProDetailData.maxAmt : 100000;   //最高限额
        let minAmt = getProDetailData.minAmt ? getProDetailData.minAmt : 10;   //最低限额
        minAmt = minAmt*1;
        maxAmt = maxAmt*1
        if (maxAmt && moneys > parseFloat(maxAmt)) {
            layerUtils.iAlert("试算金额不能超过"+ maxAmt/10000 +"万元，请重新输入");
            return;
        }
        if (minAmt && moneys < parseFloat(minAmt)) {
            layerUtils.iAlert("试算金额不能低于" + minAmt + "元，请重新输入");
            return;
        }
        if(!startTime) return layerUtils.iAlert('请选择开始时间');
        if(!endTime) return layerUtils.iAlert('请选择结束时间');
        
        let param = {
            fundCode:productInfo.fund_code,
            cycle:investcycle,
            programday:investdate,
            amt:Number(moneys) + '',
            begin_date:startTime.replace(/-/g,''),
            end_date:endTime.replace(/-/g,'')
        }
        if(param.cycle == '3'){
            param.programday = param.programday*1 < 10 ? '0' + param.programday : param.programday 
        }
        service.reqFun102157(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            let result = data.results[0]
            $(_pageId + " .capital").text(tools.fmoney(result.capital));
            $(_pageId + " .periods").text(result.periods?result.periods + '期' : '');
            $(_pageId + " .income").text(result.income?tools.fmoney(result.income):'--');
            $(_pageId + " .income_per").text(result.income_per?tools.fmoney(result.income_per)+'%':'--');
            $(_pageId + " .calculationResult").show();
            $(_pageId + " .fixedInvestment").show();
            $(_pageId + " .remark").show();
        })
    });
    
    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                // if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                //     $(_pageId + " .bast_rate").text("--");
                //     return
                // }
                // if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) {
                //     $(_pageId + " .bast_rate").text("--");
                //     return
                // }
                if (moneys && moneys > 0) {
                    moneys = tools.fmoney(moneys);
                }else{
                    $(_pageId + " #inputspanid").removeClass('m_text_darkgray');
                    return $(_pageId + " #inputspanid").text('最低定投金额' + tools.fmoney(getProDetailData.minAmt) + '元');
                }
                $(_pageId + " #czje").val(moneys);
                $(_pageId + " #inputspanid").addClass('m_text_darkgray');
                $(_pageId + " #inputspanid").text(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                // if(bankInfo.is_bank_fixed_investment == '1' && bankInfo.bank_state == '1' && payMethod == '1'  && (parseFloat(single_limit) < parseFloat(curVal) && single_limit >= 0)){
                //     layerUtils.iAlert('定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。');
                // }
                var moneys = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}$/.test(curVal))) {
                    moneys = moneys.substring(0, curVal.length - 1)
                }
                $(_pageId + " #inputspanid").addClass('m_text_darkgray');
                $(_pageId + " #inputspanid").text(Number(moneys));
                $(_pageId + " #czje").val(Number(moneys));
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys && moneys > 0) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid").addClass('m_text_darkgray');
                    $(_pageId + " #inputspanid").text(tools.fmoney(moneys));
                }else{
                    $(_pageId + " #inputspanid").removeClass('m_text_darkgray');
                    return $(_pageId + " #inputspanid").text('最低定投金额' + tools.fmoney(getProDetailData.minAmt) + '元');
                }
                // if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                //     $(_pageId + " .bast_rate").text("--");
                //     return
                // }
            }
        })
    };
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    	$(_pageId + " .pop_layer").hide();
        $(_pageId + " #cycleModel").hide();
        $(_pageId + " .calculationResult").hide();
        $(_pageId + " .fixedInvestment").hide();
        $(_pageId + " .remark").hide();
        $(_pageId + " .chooseTime").val('')
        $(_pageId + " #inputspanid").removeClass('m_text_darkgray');
        monkeywords.destroy();
        $(_pageId + " #inputspanid").html('');
        $(_pageId + " .capital").text('--');
        $(_pageId + " .periods").text('');
        $(_pageId + " .income").text('--');
        $(_pageId + " .income_per").text('--');
        $(_pageId + " #czje").val('');
        resetInputDate()
    };
    function pageBack() {
        appUtils.pageBack();
    }
    var calculator = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = calculator;
});
