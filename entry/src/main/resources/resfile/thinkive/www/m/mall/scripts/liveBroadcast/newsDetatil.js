//消息列表页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#liveBroadcast_newsDetatil ";
    var _pageCode = "liveBroadcast/newsDetatil";
    var vipBenefitsTaskData; // 新手/月底投教活动数据
    var invest_teach_id, title;
    let newsData;
    var startTime, timer = null;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        invest_teach_id = appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : appUtils.getPageParam("invest_teach_id") ? appUtils.getPageParam("invest_teach_id") : sessionStorage.invest_teach_id ? sessionStorage.invest_teach_id : '';// 文章id
        vipBenefitsTaskData = appUtils.getPageParam();
        sessionStorage.invest_teach_id = invest_teach_id;
        title = appUtils.getPageParam("title");
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
            var readingTime = vipBenefitsTaskData.duration && parseFloat(vipBenefitsTaskData.duration) * 1000;
            startTime = Date.now();
            if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3')) {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, readingTime)
                }
            }
        }
        setData(invest_teach_id);//渲染数据
        getPageShareStatus();
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        //进页面首页清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        startTime = '';
        vipBenefitsTaskData = null;
        $(_pageId + " #share").removeAttr("has-share");
    };
    //获取分享状态
    function getPageShareStatus() {
        let data = {
            busi_id: invest_teach_id,
            invest_teach_id: invest_teach_id,
            page_type: '6',
            pageId: _pageId,
            pageCode: _pageCode
        }
        if(vipBenefitsTaskData && vipBenefitsTaskData.activity_id) data.activity_id = vipBenefitsTaskData.activity_id;
        tools.isShowShare(data, '1', vipBenefitsTaskData, startTime)
    }
    //初始化渲染
    function setData(invest_teach_id) {
        service.reqFun112009({ invest_teach_id: invest_teach_id }, (data) => {
            if (data.error_no == '0') {
                var res = data.results[0];
                let guess_date = res.crt_date.slice(0, 4) + '-' + res.crt_date.slice(4, 6) + '-' + res.crt_date.slice(6, 8) + ' ' + res.crt_time.slice(0, 2) + ':' + res.crt_time.slice(2, 4);
                $(_pageId + " .liveBroadcast_newsDetatil_title").text(res.title);
                $(_pageId + " .liveBroadcast_newsDetatil_name").text(title);
                $(_pageId + " .liveBroadcast_newsDetatil_time").text(guess_date);
                let html = res.html_content;
                $(_pageId + " .list").html(html);
                $(_pageId + " video").attr("height", "");
                $(_pageId + " video").attr("controlsList", "nodownload");
                $(_pageId + " video").attr("disablePictureInPicture", "true");
                // $(_pageId + " video").css("width","100%");
                $(_pageId + " audio").css("width", "100%");
                $(_pageId + " video").attr("poster", $(_pageId + " video").attr('src') + '?x-oss-process=video/snapshot,t_1,m_fast');

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    /*
     * 返回
     */
    function pageBack() {
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        // 同行好友投教活动退出机制，判断浏览时长
        if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3') && !tools.getStayTime(startTime, vipBenefitsTaskData.duration)) {
            var remainTime = tools.getRemainTime(startTime, vipBenefitsTaskData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                startTime = Date.now();
                vipBenefitsTaskData.duration = remainTime / 1000;
                getPageShareStatus();
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_pageCode, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }


    }
    var liveBroadcast_newsDetatil = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_newsDetatil;
});
