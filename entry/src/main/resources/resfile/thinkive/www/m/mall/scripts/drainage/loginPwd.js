// 引流注册-设置登录密码
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#drainage_loginPwd";
    var _pageCode = "drainage/loginPwd";
    var external = require("external");
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var mobile;
    function init() {
        mobile = appUtils.getPageParam("mobile");
    }

    //绑定事件
    function bindPageEvent() {
        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            //检查登录密码
            var pwd1 = $(_pageId + " #pwd1").val();
            var pwd2 = $(_pageId + " #pwd2").val();
            if (!checkInput(pwd1, pwd2)) {
                return;
            }
            //注册
            var param = {
                registered_mobile: mobile,
                login_pwd: pwd1,
                source:'1', //app自主注册
            };
            regAndlogin(param);
        });
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iAlert("登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iAlert("确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iAlert("两次密码不相同");
            return false;
        }
        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }

        return true;
    }


    /**
     * 设置登录密码
     */
    function regAndlogin(paramObj) {
        //对登录密码进行加密*/
        var pwdCallBack = function (pwdRSAStr) {
            paramObj.login_pwd = pwdRSAStr;
            paramObj.login_acc = paramObj.registered_mobile;
            // 存登录号 和 登录加密密码 方便手势密码
            var platform = require("gconfig").platform;
            if (platform != "0") {
                common.setLocalStorage("account_password", paramObj.registered_mobile + "_" + pwdRSAStr);
                common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
                common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
                common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
            }
            //获取token
            var param = {
                funcNo: "50041",
                key: "deviceTokenKey"
            };
            if (platform != "0") {
                var data = external.callMessage(param);
                var device_token = data.results[0].value;
                paramObj["device_token"] = device_token;
            }

            service.reqFun101049(paramObj, function(data) {
                if(data.error_no == "0") {
                    var results = data.results[0];
                    let cust_no = results.cust_no
                    tools.clickPoint(_pageId,_pageCode,'nextStep',null,'app','',cust_no)
                    common.setLocalStorage("mobileWhole", results.mobileWhole);
                    ut.saveUserInf(results);
                    appUtils.setSStorageInfo("isAuthenticated", results.mobile + "@|@|@" + new Date().getTime());
                    appUtils.pageInit(_pageCode, "login/userIndexs");
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        };
        common.rsaEncrypt(paramObj.login_pwd, pwdCallBack);
    }


    function destroy() {
        service.destroy();
        $(_pageId + " input").val("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    /*****************************************************************************************************************************/


    /*****************************************************************************************************************************/
    var drainageLoginPwd = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = drainageLoginPwd;
});
