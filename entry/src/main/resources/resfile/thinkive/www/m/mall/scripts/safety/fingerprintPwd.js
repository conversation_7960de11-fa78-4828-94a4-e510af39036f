// 指纹密码管理
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_fingerprintPwd";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var fingerprintPwd_flag;
    var external = require("external");
    function init() {
        tools.getPdf("17");
        fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");
        if(!fingerprintPwd_flag || fingerprintPwd_flag == '0'){ //未开启指纹登录
            $(_pageId + " .switch").removeClass("switch_check");
            $(_pageId + " .switch").addClass("switch_uncheck");
        }else if(fingerprintPwd_flag == '1'){//已开启指纹登录
            $(_pageId + " .switch").removeClass("switch_uncheck");
            $(_pageId + " .switch").addClass("switch_check");
        }
        let box = document.querySelector('#safety_fingerprintPwd .switch'); // 监听对象
        let startTime = '' // 触摸开始时间
        let startDistanceX = '' // 触摸开始X轴位置
        let startDistanceY = '' // 触摸开始Y轴位置
        let endTime = '' // 触摸结束时间
        let endDistanceX = '' // 触摸结束X轴位置
        let endDistanceY = '' // 触摸结束Y轴位置
        let moveTime = '' // 触摸时间
        let moveDistanceX = '' // 触摸移动X轴距离
        let moveDistanceY = '' // 触摸移动Y轴距离
        box.addEventListener("touchstart", (e) => {
            startTime = new Date().getTime()
            startDistanceX = e.touches[0].screenX
            startDistanceY = e.touches[0].screenY
        })
        box.addEventListener("touchend", (e) => {
            endTime = new Date().getTime()
            endDistanceX = e.changedTouches[0].screenX
            endDistanceY = e.changedTouches[0].screenY
            moveTime = endTime - startTime
            moveDistanceX = startDistanceX - endDistanceX
            moveDistanceY = startDistanceY - endDistanceY
            fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");
            // console.log(moveDistanceX, moveDistanceY)    
            //需判断滑动的长度大于10 左右滑动幅度大于上下滑动幅度
            // 判断滑动距离超过40 且 时间小于500毫秒
            if (Math.abs(moveDistanceX) > 10) {
                // 判断X轴移动的距离是否大于Y轴移动的距离
                if (Math.abs(moveDistanceX) > Math.abs(moveDistanceY)) {
                    if(moveDistanceX > 0){
                        if(fingerprintPwd_flag == '0') return;
                        common.setLocalStorage("fingerprintPwd_flag",'0');
                        $(_pageId + " .switch").removeClass("switch_check");
                        $(_pageId + " .switch").addClass("switch_uncheck");
                    }else if(moveDistanceX < 0){
                        if(fingerprintPwd_flag == '1') return;
                        let setFingerprintInfo =  common.getLocalStorage("setFingerprintInfo");
                        let timeChange = new Date().getTime();
                        let date = tools.setTimeData(timeChange,'YY-MM-DD');   //当前日期 年月日
                        if(setFingerprintInfo && setFingerprintInfo.length){    //今日有值
                            let newArr = setFingerprintInfo.split(','); //第一个参数未日期 第二个参数为次数
                            let num = newArr[1]*1
                            if(date == newArr[0] && num >= 4){
                                return layerUtils.iAlert('失败次数过多，请明日再试');
                            }
                        }
                        tools.fingerprintPwd();
                    }else if(moveDistanceX == 0){
                        fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");
                        // fingerprintPwd_flag = 1;
                        if(fingerprintPwd_flag == '1'){
                            //取消指纹登录
                            common.setLocalStorage("fingerprintPwd_flag",'0');
                            $(_pageId + " .switch").removeClass("switch_check");
                            $(_pageId + " .switch").addClass("switch_uncheck");
                            // $(_pageId + " .buttonBox").toggleClass("active");
                        }else{
                            let setFingerprintInfo =  common.getLocalStorage("setFingerprintInfo");
                            let timeChange = new Date().getTime();
                            let date = tools.setTimeData(timeChange,'YY-MM-DD');   //当前日期 年月日
                            if(setFingerprintInfo && setFingerprintInfo.length){    //今日有值
                                let newArr = setFingerprintInfo.split(','); //第一个参数未日期 第二个参数为次数
                                let num = newArr[1]*1
                                if(date == newArr[0] && num >= 4){
                                    return layerUtils.iAlert('失败次数过多，请明日再试');
                                }
                            }
                            tools.fingerprintPwd();
                        }
                    }
                } 
            }
        })
    }

    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //点击完成
        appUtils.bindEvent($(_pageId + " .switch"), function () {
            fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");
            // fingerprintPwd_flag = 1;
            if(fingerprintPwd_flag == '1'){
                //取消指纹登录
                common.setLocalStorage("fingerprintPwd_flag",'0');
                $(_pageId + " .switch").removeClass("switch_check");
                $(_pageId + " .switch").addClass("switch_uncheck");
                // $(_pageId + " .buttonBox").toggleClass("active");
            }else{
                let setFingerprintInfo =  common.getLocalStorage("setFingerprintInfo");
                let timeChange = new Date().getTime();
                let date = tools.setTimeData(timeChange,'YY-MM-DD');   //当前日期 年月日
                if(setFingerprintInfo && setFingerprintInfo.length){    //今日有值
                    let newArr = setFingerprintInfo.split(','); //第一个参数未日期 第二个参数为次数
                    let num = newArr[1]*1
                    if(date == newArr[0] && num >= 4){
                        return layerUtils.iAlert('失败次数过多，请明日再试');
                    }
                }
                tools.fingerprintPwd();
            }
        });
    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack()
    }

    var setInformManager = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setInformManager;
});
