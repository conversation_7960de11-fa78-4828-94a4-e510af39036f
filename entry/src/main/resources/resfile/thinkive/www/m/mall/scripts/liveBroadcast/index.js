//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        gconfig = require("gconfig"),
        des = require("des"),
        common = require("common"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#liveBroadcast_index ";
    var external = require("external");
    var timer;
    var global = gconfig.global;
    let userInfo, cust_no, mobile
    var _pageCode = "liveBroadcast/index";
    let guess_state, guess_words, activity_tips, activity_id;
    var colorList = ['rgb(247,239,229)', 'rgb(241,233,239)', 'rgb(229,228,244)', 'rgb(223,235,243)', 'rgb(245,235,229)', 'rgb(244,238,242)'];
    let activityData;
    var sign_data, sign_type = null; //1普通签到 2分享卡片 3分享文章 // 签到
    var new_pop_data = {};//新版签到活动弹窗数据 (卡片/文章)
    var pageTouchTimer = null;
    var platform = gconfig.platform;
    require("../common/html2canvas.min");
    var current_state = {
        '1': '立即签到',
        '2': '参与红包雨',
        '3': '抽盲盒',
        '4': '已完成',
    };

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        window.clearInterval(timer);
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        mobile = userInfo.mobileWhole
        tools.guanggao({ _pageId: _pageId, group_id: "39" });
        //初始化页面数据
        getIndexPageData()
        reqFun108019();//查询签到活动状态
    }

    //营销活动查询所有活动信息
    function reqFun108019() {
        service.reqFun108019({}, (data) => {
            if (data.error_no == 0) {
                var results = data.results[0];
                // 签到sign 0:不展示 1:展示
                if (validatorUtil.isNotEmpty(results.sign)) {
                    $(_pageId + ' #signIn').show();
                    // 签到信息展示
                    sign_data = results.sign;
                    sign_type = sign_data.sign_type;//缓存今日签到类型，每日更新
                    link_open = sign_data.link_open;//普通签到是否需要打开
                    article_open = sign_data.article_open;//文章签到是否需要打开
                    reqFun108046(sign_type)
                    reqFunSign(sign_data);
                } else {
                    $(_pageId + ' #signIn').hide();
                }
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 签到活动展示
    function reqFunSign(sign_data2) {
        var htmls = "";
        // 签到周期cycle:单位:天,现定7天
        // continuity_days:已连续签到X天
        // current_days:当前为第几天
        sign_data = $.extend(sign_data, sign_data2);
        if (validatorUtil.isNotEmpty(sign_data.cycle) && sign_data.cycle > 0) {
            for (var i = 0; i < sign_data.cycle; i++) {
                var line = '<span class="line"></span>'
                var daysShow = i + 1;//展示天数
                if (sign_data.cycle == daysShow) { var line = '' }
                if (sign_data.continuity_days < daysShow) { var liClass = '' } else { var liClass = 'active' }
                var sign = "0";//是否是特殊签到，0为日常签到，1是特殊签到
                for (var j = 0; j < sign_data.reward_list.length; j++) {
                    if (sign_data.reward_list[j].days == daysShow) {
                        var reward_list_data = sign_data.reward_list[j]
                        sign = '1'
                        var reward = sign_data.reward_list[j].reward_name;
                    }
                }
                if (sign == '1') {
                    htmls += '<li class="' + liClass + '" data=' + JSON.stringify(reward_list_data) + '>' +
                        '                                <span class="point"><em></em></span>' +
                        '                                <span class="number">第<em>' + daysShow + '</em>天</span>'
                        + line +
                        '                                <div class="frame">' + reward + '</div>' +
                        '                                <span class="triangle"></span>';
                    '</li>';
                } else {
                    htmls += '<li class="' + liClass + '">' +
                        '                                <span class="point"><em></em></span>' +
                        '                                <span class="number">第<em>' + daysShow + '</em>天</span>'
                        + line +
                        '</li>';
                }
            }
            if (sign_data.cycle > 7) {
                $(_pageId + " #signIn .date ul").html(htmls).css('justify-content', 'flex-start');
            } else {
                $(_pageId + " #signIn .date ul").html(htmls).css('justify-content', 'center');
            }
            var signInBtnClass = sign_data.state == '1' ? '' : 'grey';
            $(_pageId + " #signIn .signInBtn #signInBtn").html(current_state[sign_data.current_state]).addClass(signInBtnClass)
            sign_data.share_template = sign_data.share_template ? sign_data.share_template : '2';
            var sign_inform = {
                cycle: sign_data.cycle,//单位:天,现定7天
                current_state: sign_data.current_state,//当前签到状态，1:立即签到 2:参与红包雨 3:抽盲盒 4已完成
                state: sign_data.state,//活动状态:1:进行中 2:未开始 3:已结束
                current_days: sign_data.current_days,//当前为第几天
                share_template: sign_data.share_template ? sign_data.share_template : '2',//分享模板
                activity_id: sign_data.activity_id,
            }
            $(_pageId + " #signIn .signInBtn #signInBtn").attr('sign_inform', JSON.stringify(sign_inform));
            $(_pageId + " #signIn .signInBtn h4 span").html(sign_data.continuity_days)
            $(_pageId + " #signIn .signInBtn #signInBtn").append(`<em style="display:none">${JSON.stringify(sign_inform)}</em>`)
            $(_pageId + " #activityRulesSign .sign").html(sign_data.introduce ? sign_data.introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>')

        } else { }
    }

    function start_sign(time) {
        service.reqFun108021({ sysdate: time ? time : new_pop_data.sysdate }, (data) => {
            if (data.error_no == 0) {
                pageTouchTimer = setTimeout(() => {
                    // layerUtils.iAlert('分享成功');
                    var results = data.results[0];
                    // current_state 1:立即签到 2:参与红包雨 3:拆盲盒 4已完成
                    if (results.current_state == '4' && results.points_amount == '0') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('您已成功签到</br>奖励随后发放');
                        $(_pageId + " .sign_succeed .sureBtn span").html('好的')
                        $(_pageId + " .sign_succeed").show();
                    }
                    if (results.current_state == '4' && results.points_amount != '0') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('恭喜您获得</br><span>' + (results.points_amount ? results.points_amount : '--') + '</span>积分');
                        $(_pageId + " .sign_succeed .sureBtn span").html('好的')
                        $(_pageId + " .sign_succeed").show();
                    }
                    if (results.current_state == '2') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次参与红包雨机会');
                        $(_pageId + " .sign_succeed .sureBtn").attr({ 'url': results.url, 'reward_activity_id': results.reward_activity_id }).find('span').html('去参与');
                        $(_pageId + " .sign_succeed").show();
                    }
                    if (results.current_state == '3') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次拆礼盒机会');
                        $(_pageId + " .sign_succeed .sureBtn").attr({ 'url': results.url, 'reward_activity_id': results.reward_activity_id }).find('span').html('去参与');
                        $(_pageId + " .sign_succeed").show();
                    }
                    sign_data = $.extend(sign_data, results);
                    reqFunSign(sign_data);
                }, 6000);
            } else {
                layerUtils.iLoading(false);
                $(_pageId + " .xubox_shade").hide();
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function sign_pop_new(url) {    //新版签到流程
        url = url ? url : global.oss_url + new_pop_data.pop_img_url
        let html = '<div class="activityDialog pop_layer" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;"><div class="index-popup" style="width: 75%;z-index: 2100;position: static;"><img operationType="1" operationId="new_close" operationName="关闭签到弹窗" id="new_close" src="./images/new_close.png"></img><img id="src_Image" class="popup-description" src= ' + url + ' style="width:100%;height:100%;"></div></div>'
        $(_pageId + " #activityDialog").append(html);
    }
    function article_check_in(shareType, article_open) {    //文章签到
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        mobile = common.desEncrypt("mobile", mobile);//加密
        let param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50231";
        param["shareType"] = shareType;//平台字典
        param["title"] = new_pop_data.article_title;
        param["link"] = global.serverUrl + '/m/mall/index.html#!/moreDetails/articleDetails.html?mobile=' + mobile + '&is_open=' + article_open + "&sysdate=" + new_pop_data.sysdate;
        param["content"] = new_pop_data.content; //分享文本
        param["imgUrl"] = "https://m.xintongfund.com/m/mall/images/icon_app.png";
        $(_pageId + " .pop_layer").hide();
        require("external").callMessage(param);
    }
    function sign_card(shareType) {   //卡片签到
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        $(_pageId + " .pop_layer").hide();
        var father = document.querySelector("#content");
        var _fatherHTML = document.querySelectorAll("#content .page");
        var cur = document.querySelector("#liveBroadcast_index");
        father.innerHTML = "";
        father.appendChild(cur);
        let dom = document.querySelector(_pageId + " .shareImg")
        html2canvas(dom, {
            scale: 4
        }).then(canvas => {
            var base64 = canvas.toDataURL("image/png");
            var _base64 = base64.split(",")[1];
            father.innerHTML = "";
            for (let i = 0; i < _fatherHTML.length; i++) {
                father.appendChild(_fatherHTML[i]);
            }
            param = {
                "funcNo": "50231",
                "imgUrl": _base64,
                "shareType": shareType,
                "imageShare": "1",
                "imageType":"base64"
            }
            require("external").callMessage(param);
        })
    }

    //获取今日签到详情 卡片/文章 新功能
    function reqFun108046(sign_type) {
        service.reqFun108046({}, (data) => {
            if (data.error_no == 0) {
                //缓存新弹窗数据信息（文章/卡片弹窗）
                if (data.results && data.results[0] && data.results[0].signContentInfo) {
                    let res = data.results[0].signContentInfo;
                    res.map(item => {
                        if (sign_type == '2' && item.type == '1') {
                            new_pop_data = item;
                            //渲染分享图片
                            setShareImg(new_pop_data);
                        }
                        if (sign_type == '3' && item.type == '2') new_pop_data = item;
                    })
                    // new_pop_data = (res[0].type == sign_type) ? res[0] : res[1];
                }
            }
        })
    }

    //渲染分享卡片图片
    function setShareImg(chooseData) {
        let bgImg = gconfig.global.oss_url + chooseData.share_img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #liveBroadcast_qr_img").show();
                    $(_pageId + " #liveBroadcast_code").hide();
                    $(_pageId + " #liveBroadcast_qr_img").empty();
                    let qr_code_img = gconfig.global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #liveBroadcast_qr_img").html(` <img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                    })
                } else {
                    $(_pageId + " #liveBroadcast_qr_img").hide();
                    $(_pageId + " #liveBroadcast_code").show();
                    qrcode(chooseData);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染短链二维码
    function qrcode(chooseData) {
        let mobile = ut.getUserInf().mobileWhole;
        mobile = common.desEncrypt("mobile", mobile);//加密
        let long_url = chooseData.link_url + '?mobile=' + mobile;
        $(_pageId + " #vipBenefits_code").empty();
        service.reqFun101073({ long_url: long_url }, function (res) {
            if (res.error_no == "0") {
                if (res.results != undefined && res.results.length > 0) {
                    var short_url = res.results[0].shortUrl;
                    require("../common/jquery.qrcode.min");
                    $(_pageId + " #liveBroadcast_code").qrcode({
                        render: "canvas", //设置渲染方式，有table和canvas
                        text: short_url, //扫描二维码后自动跳向该链接
                        width: 70, //二维码的宽度
                        height: 70, //二维码的高度
                        imgWidth: 20,
                        imgHeight: 20,
                        src: '../mall/images/icon_app.png'
                    });
                }
            } else {
                layerUtils.iAlert(res.error_info);
            }
        })
    }

    //会员总积分查询(108001)
    function reqFun108001() {
        if (appUtils.getPageParam("luckflag")) {
            appUtils.setSStorageInfo("luckflag", appUtils.getPageParam("luckflag"));
        }
        luckflag = appUtils.getSStorageInfo("luckflag");
        if (userInfo && !userInfo.bankAcct) {
            $(_pageId + ' #integral').html('0');
            return;
        }
        service.reqFun108001({ cust_no: cust_no, mobile: mobile }, (data) => {
            if (data.error_no == 0) {
                let results = data.results[0];
                usable_vol = results.usable_vol;
                threshold = results.threshold * 1;
                $(_pageId + ' .bg_white_yue_new .jifen span em').html(usable_vol);
                $(_pageId + ' .bg_white_yue_new .num span em').html(data.results[0].recommended_count ? data.results[0].recommended_count : '--');
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function bindPageEvent() {
        //点击我的
        appUtils.bindEvent($(_pageId + " #wode"), function () {
            tools.setPageToUrl('thfund/inputRechargePwd', '4')
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            if (ut.getUserInf().bankAcct) {
                appUtils.pageInit(_pageCode, "account/myAccount", {});
            } else {
                appUtils.pageInit(_pageCode, "account/myAccountNoBind", {});
            }
        });
        //首页
        appUtils.bindEvent($(_pageId + " #shouye"), function () {
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageCode, "login/userIndexs", {});
        });
        //点击我的
        appUtils.bindEvent($(_pageId + " #wode"), function () {
            tools.setPageToUrl('thfund/inputRechargePwd', '4')
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            if (ut.getUserInf().bankAcct) {
                appUtils.pageInit(_pageCode, "account/myAccount", {});
            } else {
                appUtils.pageInit(_pageCode, "account/myAccountNoBind", {});
            }
        });
        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageCode, "moreDetails/more", {});
        });
        //查看竞猜规则
        appUtils.bindEvent($(_pageId + " .guessRules"), function () {
            $(_pageId + " #activityRules").show();
        });
        //查看领取记录
        appUtils.bindEvent($(_pageId + " .record"), function () {
            //查看领取记录
            let live_activity_id = activity_id;
            sessionStorage.live_activity_id = live_activity_id; //缓存竞猜活动ID
            appUtils.pageInit(_pageCode, "liveBroadcast/guessingRecord", {});
        });
        //关闭竞猜规则
        appUtils.bindEvent($(_pageId + " .ruleSure"), function () {
            $(_pageId + " #activityRules").hide();
        });
        //猜涨
        appUtils.bindEvent($(_pageId + " .bg_red"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            if (activityData.state == "1") {
                if (guess_state == '2' || guess_state == '3') {
                    return layerUtils.iAlert(guess_words);
                }
                if (activityData.guess_answer == '1') return layerUtils.iAlert("您已选择看涨，下一交易日21时开奖");
                if (activityData.guess_answer == '2') return layerUtils.iAlert("您已选择看跌，下一交易日21时开奖");
                let operationId = 'rise'
                layerUtils.iConfirm(`是否选择看涨`, function () {
                    competition('1')
                }, function () {

                }, "确定", "取消",operationId);

            } else {
                if (activityData.state == "2") return layerUtils.iAlert("活动未开始");
                if (activityData.state == "3") return layerUtils.iAlert("活动已结束");
            }

        });
        //猜跌
        appUtils.bindEvent($(_pageId + " .bg_green"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            if (activityData.state == "1") {
                if (guess_state == '2' || guess_state == '3') {
                    return layerUtils.iAlert(guess_words);
                }
                if (activityData.guess_answer == '1') return layerUtils.iAlert("您已选择看涨，下一交易日21时开奖");
                if (activityData.guess_answer == '2') return layerUtils.iAlert("您已选择看跌，下一交易日21时开奖");
                // competition('2')
                let operationId = 'fall'
                layerUtils.iConfirm(`是否选择看跌`, function () {
                    competition('2')
                }, function () {

                }, "确定", "取消",operationId);
            } else {
                if (activityData.state == "2") return layerUtils.iAlert("活动未开始");
                if (activityData.state == "3") return layerUtils.iAlert("活动已结束");
            }
        });
        //点击直播banner活动图片 首页
        appUtils.bindEvent($(_pageId + " .banner_activity img"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            let ad_activity_state = activityData.ad_activity_state;
            if (ad_activity_state == '2') return layerUtils.iAlert("活动未开始");
            if (ad_activity_state == "3") return layerUtils.iAlert("活动已结束");
            //进入直播列表页面
            let file_type = activityData.ad_file_type;
            let url = activityData.ad_url;
            let file_state = activityData.ad_file_state;
            let name = activityData.ad_name;
            let description = activityData.description;
            let group_id = activityData.ad_group_id;
            let banner_id = activityData.banner_id;
            banner_page_to(file_type, url, file_state, name, description, group_id, banner_id)
        });
        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");
            var picture = $(this).attr("picture");
            var group_id = $(this).attr("group_id");
            var banner_id = $(this).attr("banner_id");
            banner_page_to(file_type, url, file_state, name, description, group_id, banner_id)
        }, 'click');
        //点击直接进入直播间
        appUtils.preBindEvent($(_pageId + " .textList"), ".card .more", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let code = $(this).attr("code");
            let name = $(this).attr("name");
            let data = { code, name };
            sessionStorage.newsData = JSON.stringify(data);
            appUtils.pageInit(_pageCode, "liveBroadcast/newsList");
        }, 'click');
        //点击直接进入详情页
        appUtils.preBindEvent($(_pageId + " .textList"), ".card .frame", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let invest_teach_id = $(this).attr("id");
            let name = $(this).attr("name");
            // let html_content = $(this).attr("html_content");
            // sessionStorage.newsDetatil = html_content;
            appUtils.pageInit(_pageCode, "liveBroadcast/newsDetatil", { invest_teach_id: invest_teach_id, title: name });
        }, 'click');
        //签到活动，立即签到
        appUtils.preBindEvent($(_pageId + " #signIn"), " #signInBtn", function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            var sign_inform = $(this).attr('sign_inform');
            var btn_sign_inform = sign_inform ? JSON.parse(sign_inform) : '';
            chooseData = {
                share_template: btn_sign_inform.share_template ? btn_sign_inform.share_template : '',
                activity_id: btn_sign_inform.activity_id ? btn_sign_inform.activity_id : ''
            }
            tools.clickPoint(_pageId, _pageCode, 'signInBtn', chooseData.activity_id);
            if (btn_sign_inform.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if (btn_sign_inform.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            if (btn_sign_inform.state == '1' && btn_sign_inform.current_state == '1') {
                if (!sign_type || sign_type == '1') { //普通签到流程
                    sign_pop_new('./images/share_sign.png');
                } else { //新版签到流程
                    sign_pop_new();
                }
            } else if (btn_sign_inform.state == '1' && (btn_sign_inform.current_state == '2' || btn_sign_inform.current_state == '3')) {
                var current_inform = $(_pageId + " #signIn .date li").eq(btn_sign_inform.current_days - 1).attr('data')
                if (!current_inform) { layerUtils.iMsg(-1, "活动ID未配置或url未配置！", 2) }
                current_inform = JSON.parse(current_inform)
                if (!current_inform.url || !current_inform.reward_activity_id) { layerUtils.iMsg(-1, "活动ID未配置或url未配置！", 2); }
                appUtils.pageInit(_pageCode, current_inform.url, { 'reward_activity_id': current_inform.reward_activity_id });
                return;
            } else if (btn_sign_inform.state == '1' && btn_sign_inform.current_state == '4') {
                layerUtils.iAlert("今日签到已完成，请明日再来!");
            }
        });
        // 签到成功
        appUtils.bindEvent($(_pageId + " .sign_succeed .sureBtn span"), function () {
            var url_btn = $(this).parent().attr('url');
            var reward_activity_id_btn = $(this).parent().attr('reward_activity_id');
            tools.recordEventData('1','sign_succeed','签到成功',{activityId:reward_activity_id_btn});
            $(_pageId + " .sign_succeed").hide().find('.index_info_tishi').html('');
            $(_pageId + " .sign_succeed .sureBtn").removeAttr("url").removeAttr("reward_activity_id").find('span').html('')
            if (validatorUtil.isNotEmpty(url_btn) && validatorUtil.isNotEmpty(reward_activity_id_btn)) {
                appUtils.pageInit(_pageCode, url_btn, { 'reward_activity_id': reward_activity_id_btn });
            }
            reqFun108001();
        })
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            tools.recordEventData('1','share_WeChatFriend','微信朋友圈分享',{activityId:chooseData.activity_id,shareTemplate:chooseData.share_template});
            // tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', chooseData.activity_id);
            //根据现有字段区分新老逻辑 单独抽出新逻辑
            setUser() //记录用户行为
            //1.普通签到，需用户主动打开 普通签到直接
            if (sign_type == '1' && link_open == '1') return common.share("23", chooseData.share_template, '', '', '', link_open);
            //2.文章签到 需要直接调用分享 且用户打开才算分享
            if (sign_type == '3' && article_open == '1') {
                return article_check_in("23", article_open)
            }
            //2.文章签到 点击分享就算分享
            if (sign_type == '3' && article_open == '0') {
                article_check_in("23", article_open);
                start_sign();
            } else if (sign_type == '2') { //卡片签到
                sign_card("23");
                start_sign();
            } else {
                var query_params = {};
                query_params["registered_mobile"] = mobile;
                query_params["tem_type"] = chooseData.share_template;
                service.reqFun102012(query_params, function (data) {
                    if (data.error_no == "0" && data.results && data.results[0]) {
                        let time = data.results[0].sysdate
                        start_sign(time);
                    }
                })
                common.share("23", chooseData.share_template);
            }
            // // 连续签到活动签到接口
            sessionStorage.sign = '1'
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            tools.recordEventData('1','share_WeChat','微信好友分享',{activityId:chooseData.activity_id,shareTemplate:chooseData.share_template});
            tools.clickPoint(_pageId, _pageCode, 'share_WeChat', chooseData.activity_id);
            //2.文章签到 需要直接调用分享 且用户打开才算分享
            if (sign_type == '3' && article_open == '1') {
                return article_check_in("22", article_open)
            }
            common.share("22", chooseData.share_template);
            setUser() //记录用户行为
        });
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
        // 签到规则
        appUtils.preBindEvent($(_pageId + " #signIn"), " .sign_rules", function (e) {
            $(_pageId + " #activityRulesSign").show().find('.sign').show()
        })
        appUtils.preBindEvent($(_pageId + " #activityRulesSign"), " .okBtn", function (e) {
            $(_pageId + " #activityRulesSign").hide();
        })
        //取消弹窗
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog #new_close", function () {
            // tools.recordEventData('1','new_close','关闭弹窗');
            $(this).parents(".activityDialog").remove();
            $(_pageId + " #pop_layer #share_WeChat").hide();
        }, 'click');
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog #src_Image", function () {
            tools.recordEventData('1','src_Image','打开签到弹窗');
            $(this).parents(".activityDialog").remove();
            $(_pageId + " #pop_layer #share_WeChat").hide();
            $(_pageId + " #pop_layer").show();
        }, 'click');

    }

    //记录用户行为
    function setUser() {
        service.reqFun108009({ id: chooseData.activity_id, cust_no: cust_no }, (data) => {
            if (data.error_no == 0) {
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //点击开始竞猜
    function competition(guess_answer) {
        let data = {
            guess_answer: guess_answer,
            activity_id: activity_id
        }
        service.reqFun112007(data, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (results.status == '2') {
                    activityData.guess_answer = results.guess_answer;
                    let guess_fall_count = results.guess_fall_count * 1;
                    let guess_rise_count = results.guess_rise_count * 1;
                    let total = guess_fall_count + guess_rise_count;
                    if (guess_answer == "2") {    //看跌 涨灰色
                        $(_pageId + " .bg_red").css("background", "#ccc");
                    } else if (guess_answer == "1") {  //看涨 跌灰色
                        $(_pageId + " .bg_green").css("background", "#ccc");
                    }
                    //看涨的概率
                    let guess_rise = Math.round(((guess_rise_count / total).toFixed(2)) * 100);
                    //看跌的概率
                    let guess_fall = 100 - guess_rise;
                    $(_pageId + " .marvellous .pro .add").css("width", guess_rise + '%');
                    $(_pageId + " .marvellous .pro .add").html('&nbsp;&nbsp;&nbsp;' + guess_rise + '%');
                    $(_pageId + " .marvellous .pro .lightning").css("left", ('calc(' + guess_rise + "% - 16px)"));
                    $(_pageId + " .marvellous .pro .lightning").show();
                    $(_pageId + " .marvellous .pro .reduce").css("width", guess_fall + "%");
                    if (guess_fall >= 14) $(_pageId + " .marvellous .pro .reduce").html(guess_fall + "%" + '&nbsp;&nbsp;&nbsp;');

                    layerUtils.iAlert('竞猜成功');
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #activityRules").hide();
        window.clearInterval(timer);
        $(_pageId + " .marvellous .pro .lightning").hide();
        $(_pageId + " .marvellous").hide();
        clearTimeout(pageTouchTimer);
        $(_pageId + " #liveBroadcast_qr_img").hide();
        $(_pageId + " #liveBroadcast_code").show();
        $(_pageId + " #signIn .date ul").html()
        $(_pageId + " .sign_succeed .index_info_tishi").html();
        // $(_pageId + " .banner_activity").hide();
    }
    //获取首页接口
    function getIndexPageData() {
        service.reqFun112001({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                let ad_state = results.ad_state;    //1展示
                activityData = results;
                
                if (ad_state == "1") {
                    $(_pageId + " .banner_activity img").attr("src", global.oss_url + results.ad_picture);
                    $(_pageId + " .banner_activity em").html(JSON.stringify(activityData))
                    $(_pageId + " .banner_activity").show();
                } else {
                    $(_pageId + " .banner_activity").hide();
                }
                // if(results.state == "1"){ //当前活动进行中
                if (results.guess_answer == "2") {    //看跌 涨灰色
                    $(_pageId + " .bg_red").css("background", "#ccc");
                } else if (results.guess_answer == "1") {  //看涨 跌灰色
                    $(_pageId + " .bg_green").css("background", "#ccc");
                }
                let endTime = results.guess_date + results.guess_end_time;
                if (endTime) {
                    $(_pageId + " .marvellous").show();
                    endTime = endTime.slice(0, 4) + '-' + endTime.slice(4, 6) + '-' + endTime.slice(6, 8) + ' ' + endTime.slice(8, 10) + ':' + endTime.slice(10, 12) + ':' + endTime.slice(12, 14);
                    // var inputTime = +new Date(endTime);
                    guess_words = results.guess_words;
                    if (guess_words.length > 12) {
                        let last10 = guess_words.slice(-10);
                        let hour = last10.slice(0, 2);
                        let minute = last10.slice(3, 5);
                        let firstRemark = guess_words.slice(0, -10);
                        let str = `${firstRemark}<span style="background:#000;color:#fff;padding: 0rem 0.02rem;border-radius: 0.03rem;">${hour}</span>时<span style="background:#000;color:#fff;padding: 0rem 0.02rem;border-radius: 0.03rem;">${minute}</span>分结束竞猜`
                        guess_words = str;
                    }
                    // let last10 = str.slice(-10);
                    //拿到banner活动

                    // countDown(inputTime);
                    //定时器 每隔一秒变化一次
                    // setInterval(countDown(inputTime), 1000);   //首先调用一次防止页面初始化不调用
                    // timer = setInterval(function () {
                    //     countDown(inputTime)
                    // }, 1000);
                    $(_pageId + " .guess_words").html(guess_words);
                    $(_pageId + " .activityName").text(results.name);
                    let guess_fall_count = results.guess_fall_count * 1;
                    let guess_rise_count = results.guess_rise_count * 1;
                    if (!guess_fall_count && !guess_rise_count) { //没人参与活动
                        let guess_fall = guess_rise = 50;
                        $(_pageId + " .marvellous .pro .add").css("width", guess_rise + '%');
                        $(_pageId + " .marvellous .pro .add").html('&nbsp;&nbsp;&nbsp;' + guess_rise + '%');
                        $(_pageId + " .marvellous .pro .lightning").css("left", ('calc(' + guess_rise + "% - 16px)"));
                        $(_pageId + " .marvellous .pro .lightning").show();
                        $(_pageId + " .marvellous .pro .reduce").css("width", guess_fall + "%");
                        if (guess_fall >= 14) $(_pageId + " .marvellous .pro .reduce").html(guess_fall + "%" + '&nbsp;&nbsp;&nbsp;');
                    } else {
                        let total = guess_fall_count + guess_rise_count;
                        //看涨的概率
                        let guess_rise = Math.round(((guess_rise_count / total).toFixed(2)) * 100);
                        //看跌的概率
                        let guess_fall = 100 - guess_rise;
                        $(_pageId + " .marvellous .pro .add").css("width", guess_rise + '%');
                        $(_pageId + " .marvellous .pro .add").html('&nbsp;&nbsp;&nbsp;' + guess_rise + '%');
                        $(_pageId + " .marvellous .pro .lightning").css("left", ('calc(' + guess_rise + "% - 16px)"));
                        $(_pageId + " .marvellous .pro .lightning").show();
                        $(_pageId + " .marvellous .pro .reduce").css("width", guess_fall + "%");
                        if (guess_fall >= 14) $(_pageId + " .marvellous .pro .reduce").html(guess_fall + "%" + '&nbsp;&nbsp;&nbsp;');
                    }
                    guess_state = results.guess_state;
                    activity_tips = results.activity_tips;
                    activity_id = results.id;
                    $(_pageId + " .tip").text(activity_tips);   //提示语
                    $(_pageId + " .bg_red em").html(JSON.stringify(activityData))
                    $(_pageId + " .bg_green em").html(JSON.stringify(activityData))
                    $(_pageId + " #activityRules .b_con").html(results.introduce); //渲染活动规则
                } else {
                    $(_pageId + " .marvellous").hide();
                }
                // }else{ //活动已接受或未开始
                // $(_pageId + " .marvellous").hide();

                // }
                //处理数据做一次性渲染
                let arr = results.invest_teach_dict;
                if (!results.invest_teach_dict) return;
                for (let i = 0; i < arr.length; i++) {
                    if (!results.invest_index_list) {
                        arr[i].list = []
                    } else {
                        arr[i].list = results.invest_index_list[results.invest_teach_dict[i].code * 1];
                    }
                    arr[i].bg_color = colorList[i]
                }
                setData(arr)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function banner_page_to(file_type, url, file_state, name, description, group_id, banner_id) {
        if (file_type == "0" && file_state == "1" && url) {
            // let pageUrl = url.split("?")[0];
            // let pageToData = {
            //     "group_id": group_id,
            //     "banner_id": banner_id,
            // }

            // appUtils.pageInit("login/userIndexs", pageUrl, pageToData);
            // return;
            if (url.indexOf("?") > -1) {
                let skip_url = url.split("?")[0];
                let parameter = url.split("?")[1];
                let parameter_arr = parameter.split("&"); //各个参数放到数组里
                let urlInfo = {};//url的参数信息
                for (let i = 0; i < parameter_arr.length; i++) {
                    num = parameter_arr[i].indexOf("=");
                    if (num > 0) {
                        name = parameter_arr[i].substring(0, num);
                        value = parameter_arr[i].substr(num + 1);
                        urlInfo[name] = value;
                        urlInfo["group_id"] = group_id;
                        urlInfo["banner_id"] = banner_id;
                    }
                }
                if (url.indexOf("activity/fundLuckdrawnew") > -1) { urlInfo['channel'] = "jjcf_app"; urlInfo['type'] = "luckDraw" }
                appUtils.pageInit("login/userIndexs", skip_url, urlInfo);

            } else {
                appUtils.pageInit("login/userIndexs", url, {
                    "group_id": group_id,
                    "banner_id": banner_id
                });
            }
        }
        // 是否是有效内外链接
        if (file_type == "1" && file_state == "1" && url) {
            appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                "url": url,
                "group_id": group_id,
                "banner_id": banner_id,
                "name": name,
                "description": description,
            });
            return;
        }
        // 登录授权
        if (file_type == "2" && file_state == "1") {
            if (!common.loginInter()) return;
            if (!/^(http|https):/.test(url)) {
                if (url.indexOf("?") > -1) {
                    var skip_url = url.split("?")[0];
                    var parameter = url.split("?")[1];
                    var parameter_arr = parameter.split("&"); //各个参数放到数组里
                    var urlInfo = {};//url的参数信息
                    for (var i = 0; i < parameter_arr.length; i++) {
                        num = parameter_arr[i].indexOf("=");
                        if (num > 0) {
                            name = parameter_arr[i].substring(0, num);
                            value = parameter_arr[i].substr(num + 1);
                            urlInfo[name] = value;
                            urlInfo["group_id"] = group_id;
                            urlInfo["banner_id"] = banner_id;
                            urlInfo["cust_no"] = ut.getUserInf().custNo;
                        }
                    }
                    if (url.indexOf("activity/fundLuckdrawnew") > -1) { urlInfo['channel'] = "jjcf_app"; urlInfo['type'] = "luckDraw" }
                    appUtils.pageInit("login/userIndexs", skip_url, urlInfo);

                } else {
                    appUtils.pageInit("login/userIndexs", url);
                }
                return;
            }
            if (url.indexOf("activity/fundLuckdraw") > -1) {
                common.setLocalStorage("activityInfo", {
                    activity_id: "2505",
                    group_id: group_id,
                    banner_id: banner_id,
                    cust_no: ut.getUserInf().custNo,
                    channel: "jjcf_app",
                    type: "luckDraw",
                    mobile: ut.getUserInf().mobileWhole,
                })
                var data = external.callMessage({
                    "funcNo": "50043",
                    "moduleName": "mall",
                    "key": "activityInfo",
                })
            }
            appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                "url": url,
                "name": name,
                "description": description,
                "group_id": group_id,
                "banner_id": banner_id
            });
        }
    }
    function countDown(inputTime) {
        var hour = $(_pageId + " .hour")
        var minute = $(_pageId + " .minute")
        var second = $(_pageId + " .second")
        //获取当前时间的时间戳（单位毫秒）
        var nowTime = +new Date();
        //把剩余时间毫秒数转化为秒
        var times = (inputTime - nowTime) / 1000;
        //计算小时数 转化为整数
        var h = parseInt(times / 60 / 60 % 24);
        //如果小时数小于 10，要变成 0 + 数字的形式 赋值给盒子
        $(_pageId + " .hour").text(h < 10 ? "0" + h : h);
        //计算分钟数 转化为整数
        var m = parseInt(times / 60 % 60);
        //如果分钟数小于 10，要变成 0 + 数字的形式 赋值给盒子
        $(_pageId + " .minute").text(m < 10 ? "0" + m : m);
        //计算描述 转化为整数
        var s = parseInt(times % 60);
        //如果秒钟数小于 10，要变成 0 + 数字的形式 赋值给盒子
        $(_pageId + " .second").text(s < 10 ? "0" + s : s);
        // second.innerHTML = s < 10 ? "0" + s : s;
    }
    //渲染 消息列表 一次性渲染
    function setData(arr) {
        //循环外层大集合
        let html = '';   //父级模板
        arr.map(item => {
            let title = item.name;
            let bg_color = item.bg_color;
            let code = item.code;
            let htmlChildren = '';
            if (!item.list.length) {
                //$(_pageId + " .textList").html(html)
                //htmlChildren += ` <div class="m_center m_width_100 m_padding_20_20 bg_fff"> 暂无数据 </div> `
            } else {
                item.list.map(items => {
                    let bg_img = items.img_url ? global.oss_url + items.img_url : "./images/sfz_01.png";
                    let titleChildren = items.title;//教投内容标题
                    let id = items.id; //教投内容唯一标识
                    // let html_content = items.html_content;
                    //<img class="m_width_100" src="${bg_img ? bg_img : "./images/sfz_01.png"}">
                    htmlChildren += `
                        <ul class="bg_fff flex m_padding_10_10 frame" contentType="12" operationType="1" operationId="frame" operationName="学投资文章详情" name="${title}" id="${id}">
                            <em style="display:none">${JSON.stringify(items)}</em>
                            <li class="m_width_70 main_flxe vertical_center">
                                ${titleChildren}
                            </li>
                            <li class="m_width_30">
                                <img class="m_width_100" src="${bg_img ? bg_img : "./images/sfz_01.png"}">
                            </li>
                        </ul>
                    `        
                })
                html += `
                <div class="card m_radius" style="background:${bg_color}">
                    <ul class="flex">
                        <li  class="m_text_darkgray m_bold m_font_size16">${title}</li>
                        <li operationType="1" operationId="${'more_' + code}" operationName = "${title + '更多'}" code="${code}" name="${title}" class="m_marginTop_02 more">${item.list.length ? "查看更多" : '查看更多'}</li>
                    </ul>
                    ${htmlChildren}
                </div>
                `
                $(_pageId + " .textList").html(html)
            }
           
        })
        
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var liveBroadcast_index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "reqFunSign": reqFunSign
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_index;
});
