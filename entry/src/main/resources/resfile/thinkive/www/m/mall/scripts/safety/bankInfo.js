// 信息验证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _page_code = "safety/bankInfo",
        _pageId = "#safety_bankInfo ";
        
    var backParam = {};
    var backPage = "";
    var transfer_flag = "";
    var ut = require("../common/userUtil");
    var userInfo;

    function init() {
        // let isShowChangeBank = appUtils.getPageParam("isShowChangeBank");
        var isShowChangeBank = appUtils.getSStorageInfo("isShowChangeBank")
        if(isShowChangeBank == 1){
            $(_pageId + " #pageBankCard").show()
        }else{
            $(_pageId + " #pageBankCard").hide()
        }
        userInfo = ut.getUserInf();
        backPage = appUtils.getSStorageInfo("_prePageCode");
        transfer_flag = appUtils.getPageParam("transfer_flag");
        BankCardInformation();
    }

    //查出支持的银行卡
    function BankCardInformation() {
        service.reqFun102014({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            var str = "";
            if (error_no == "0") {
                for (var i = 0; i < data.results.length; i++) {
                    var bank_name = data.results[i].bank_name;
                    var day_limit = data.results[i].day_limit;
                    var single_limit = data.results[i].single_limit;
                    var remark = data.results[i].remark;
                    if (day_limit < 0) {
                        day_limit = "不限";
                    }
                    if (single_limit < 0) {
                        single_limit = "不限";
                    }
                    if (day_limit >= 10000) {
                        day_limit = day_limit / 10000 + "万";
                    }
                    if (single_limit >= 10000) {
                        single_limit = single_limit / 10000 + "万";
                    }

                    var bank_transfer_flag = data.results[i].transfer_flag;
                    var recommend_flag = data.results[i].recommend_flag;//是否推荐 0不推荐1推荐
                    if (transfer_flag == "1" && bank_transfer_flag == "1") {
                        if(recommend_flag == '1'){
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add recommend'><img src='images/star.png'>"+remark+"</td></tr>";
                        }else {
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add'>"+remark+"</td></tr>";
                        }
                    } else {
                        if(recommend_flag == '1'){
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add recommend'><img src='images/star.png'>"+remark+"</td></tr>";
                        }else {
                            str += "<tr><td>"+bank_name+"</td><td>"+single_limit+"</td><td>"+day_limit+"</td><td class='add'>"+remark+"</td></tr>";
                        };
                    }

                }
                $(_pageId + " #mainInfo").html(str);
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .grid_02"), function () {
            $(_pageId + " .pop_layer4").hide();
            $(_pageId + " .card_rules1").hide();
        });
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //去换卡页面
        appUtils.bindEvent($(_pageId + " #pageBankCard"), function () {
            var param = {
                "bank_acct": userInfo.bankAcct
            }
            service.reqFun101044({}, function (data) { // 查询是否存在在途资金
                if (data.error_no == "0") {
                    service.reqFun101027(param, function (data) { //查询资产是否为0
                        if (data.error_no == "0") {
                            layerUtils.iLoading(false);
                            appUtils.pageInit(_page_code, "safety/kjyanzheng");
                            return;
                        }else{
                            appUtils.pageInit(_page_code, "safety/yanzheng");
                            return;
                        }
                    }, { isLastReq: false });
                }else{
                    layerUtils.iConfirm("您有在途资金尚未到账，请到账后再试", function () {
                        return;
                    }, function () {
                        $(_pageId + " .pop_layer4").show();
                        $(_pageId + " .card_rules1").show();
                    }, "", "查看规则");
                }
            },{ isLastReq: false });
            
        });
    }


    function destroy() {
        $(_pageId + " #pageBankCard").hide()
        $(_pageId + " .pop_layer4").hide();
        $(_pageId + " .card_rules1").hide();
    }
    function pageBack() {
        appUtils.pageBack();
    }
    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
