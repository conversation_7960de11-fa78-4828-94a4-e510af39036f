// 历史净值
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        cfdUtils = require("cfdUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        tools = require("../common/tools"),
        common = require("common"),
        vIscroll = {
            "scroll": null,
            "_init": false
        },
        _pageId = "#thfund_HAHistoryValueList";

    var _pageCode = "thfund/HAHistoryValueList";
    var ut = require("../common/userUtil");
    var _fund_code;

    function init() {
        _fund_code = appUtils.getSStorageInfo("fund_code");
        var productInfo = appUtils.getSStorageInfo("productInfo_jjb") || appUtils.getSStorageInfo("productInfo");
        //获取历史净值
        getHistory();
        tools.initFundBtn(productInfo, _pageId);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

    }

    //获取历史净值
    function getHistory() {
        var params = {
            fund_code: _fund_code
        }
        service.reqFun102006(params, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    return;
                }
                var html = "";
                for (var i = 0; i < results.length; i++) {
                    //空数据处理
                    results[i] = tools.FormatNull(results[i]);

//                	//日涨跌幅
//                	var rateClass="add";
//                	if(!results[i].annu_yield){
//                		results[i].annu_yield = "--";
//                	}else{
//                		rateClass = addMinusClass(results[i].annu_yield);
//                	}


                    var end_date = results[i].end_date;
                    if (end_date != "--") {
                        end_date = tools.ftime(end_date.substring(0, 8));
                    }

                    //单位净值 
                    var nav = results[i].nav;
                    if (nav != "--") {
                        nav = (+nav).toFixed(4);
                    }

                    //累计净值
                    var accunav = results[i].accunav;
                    if (accunav != "--") {
                        accunav = (+accunav).toFixed(4);
                    }

                    //日涨跌幅
                    var rateClass = "add";
                    var daily_return = results[i].daily_return;
                    if (daily_return != "--") {
                        rateClass = addMinusClass(daily_return);
                        daily_return = (+daily_return).toFixed(2) + "%";
                    }


                    html += '<div class="item">' +
                        '<span>' + end_date + '</span>' +
                        '<span class="">' + nav + '</span>' +
                        '<span class="">' + accunav + '</span>' +
                        '<span class=' + rateClass + '>' + daily_return + '</span>' +
                        '</div>';
                }
                $(_pageId + " #historyContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function addMinusClass(str) {
        var numClass = "add";

        if (str < 0) {
            numClass = "text_green";
        } else if (str > 0) {
            numClass = "text_red";
        } else {
            numClass = "text_grey";
        }
        return numClass;
    }

    function destroy() {
        $(_pageId + " #historyContent .list_content").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
