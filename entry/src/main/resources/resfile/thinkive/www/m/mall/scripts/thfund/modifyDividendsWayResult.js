//公募：修改分红方式结果页
define(function (require, exports, module) {
    var tools = require("../common/tools");
    var appUtils = require("appUtils"),
        _pageUrl = "thfund/modifyDividendsWayResult",
        _pageId = "#thfund_modifyDividendsWayResult ";
        var ut = require("../common/userUtil");
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + "#done_btn"), function () {
            // var routerList = appUtils.getSStorageInfo("routerList");
            // if (routerList.indexOf("template/positionList") >= 0){
            //     appUtils.pageInit(_pageUrl, "template/positionList");
            // }else{
            //     if(ut.getUserInf().custLabelCnlCode == "yh"){
            //         appUtils.setSStorageInfo("routerList", ["yuanhui/userIndexs"]);
            //         appUtils.pageInit(_pageUrl, "yuanhui/hold");
            //         return;
            //     }else if(ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) ||ut.getUserInf().custLabelCnlCode == "jjdx"){
            //         appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
            //         appUtils.pageInit(_pageUrl, "template/positionList");
            //         return;
            //     }else{
            //         appUtils.setSStorageInfo("routerList", ["hengjipy/userIndexs"]);
            //         appUtils.pageInit(_pageUrl, "template/positionList");
            //         return;
            //     }
            // }
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageBack();
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    }
    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
