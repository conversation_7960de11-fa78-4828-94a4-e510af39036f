//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        _pageId = "#combProduct_combProdList ";
    _page_code = "combProduct/combProdList";
    var common = require("common");
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #viewMore").css({ "display": "block" });
        let reqFun102164Param = {
            level_match: "0", // 1 匹配 0 不匹配
        };
        reqFun102164(reqFun102164Param);
    }

    // 查询列表
    function reqFun102164(param) {
        service.reqFun102164(param, (data) => {
            if (data.error_no == 0) {
                var prodList = data.results;
                var html = "";
                if (prodList && prodList.length) {
                    prodList.forEach(item => {
                        var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
                        var buy_state = item.purchase_state;
                        var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                        var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                        var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                        var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none'  //是否展示特点
                        var corner_marker_list = item.corner_marker_list == '1' ? '' : 'display_none'  //是否展示角标
                        var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                        var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                        var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none'  //是否展示策略特点
                        //数据展示
                        var income_period_type_desc = item.income_period_type ? item.income_period_type : '--' //近多少年化
                        var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅
                        var annual_income = item.annual_income; // 目标年化收益
                        var holding_time = item.holding_time ? item.holding_time : '--' // 建议持有时长
                        var threshold_amount = item.first_per_min ? item.first_per_min : '--' //起购金额
                        var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                        var daily_rise = item.daily_rise ? tools.fmoney(item.daily_rise) : '--'
                        //1-购买  2-预约 3-敬请期待  4-售罄
                        if (buy_state == "1") {
                            buy_state_name = "购买";
                            btnClass = "";
                        }
                        if (buy_state == "2") {
                            buy_state_name = "预约";
                            btnClass = "";
                        }
                        if (buy_state == "3") {
                            buy_state_name = "敬请期待";
                            btnClass = "";
                        }
                        if (buy_state == "4") { //其他产品为售罄
                            buy_state_name = "封闭中";
                            btnClass = "sold_out";
                        }
                        if (buy_state == "5") { //其他产品为售罄
                            buy_state_name = "售罄";
                            btnClass = "sold_out";
                        }
                        if (buy_state == "6") { //定制产品买入置灰
                            buy_state_name = "购买";
                            btnClass = "";
                        }
                        html += `
                        <li class="comb_prod_item" buy_state="${buy_state}">
                            <span style='display: none' class='productInfo'>${productInfoSon}</span>
                            <div>
                                <p class="m_font_size16 color_000">${item.comb_sname}</p>
                                <div class="annual m_font_size12 ${annual_income_list}"  style="font-size: 12px;">目标年化收益:<span class="m_text_red m_font_size18">${annual_income}</span></div>
                                <div class="annual m_font_size12 ${income_period_list}"  style="font-size: 12px;">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${establish_rate}</span>%</div>
                                <div class="annual m_font_size12 ${income_period_list_chg}"  style="font-size: 12px;">${income_period_type_desc}:<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</div>
                                <p style="margin-top: 0.04rem;" class="m_font_size12 ${strategic_characteristic_list}">策略特点：<span class="m_text_red">${item.strategic_characteristic}</span></p>
                                <div class="purchase m_text_999" style="font-size: 12px;">                 
                                    <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                    <span class="${recommended_holding_list}">建议持有:${holding_time}</span>
                                </div>
                                <p class="m_font_size12 m_golden ${characteristic_list}">${item.characteristic}</p>
                            </div>
                            <div class="action-btn ${btnClass}">
                                ${buy_state_name}
                            </div>
                        </li>`
                    })
                } else {
                    html += ` < li class="no_data" >
                            <p class="m_center">暂无更多投顾产品</p>
                    </li > `
                }
                $(_pageId + "#combProdList").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
                return;
            }
        });
    }

    function bindPageEvent() {
        // 跳转详情
        appUtils.preBindEvent($(_pageId + " .comb_prod_list"), ".comb_prod_item", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text()); //存储数据格式
            appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
            appUtils.setSStorageInfo("fund_code", productInfo.comb_code);
            tools.recordEventData('1','comb_prod_item_' + productInfo.financial_prod_type,'投顾详情',{fundCode:productInfo.comb_code});
            appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
            var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            let operationId = 'riskAssessment'
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                //到期3个月后提示
                if (perfect_info == 4) {
                    return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, "取消", "更换");
                }
                appUtils.pageInit(_page_code, "combProduct/combProdDetail");
            });
        }, 'click');

        //点击返回
        appUtils.bindEvent(_pageId + " #getBack", function () {
            pageBack();
        });
    }

    //去测评
    function pageTo_evaluation() {
        let operationId = 'riskAssessment'
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }

    function pageBack() {
        appUtils.pageBack();
        // appUtils.pageInit("account/riskResult", "login/userIndexs");
        // var routerList = appUtils.getSStorageInfo("routerList");
        // if (routerList.indexOf("combProduct/combRiskQuestion") > -1) {
        //     routerList.splice(-2);
        //     appUtils.pageInit("account/riskResult", routerList[routerList.length - 1]);
        // } else {
        //     appUtils.pageBack();
        // }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + "#combProdList").html("");
    }

    var comProdListModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = comProdListModule;
});
