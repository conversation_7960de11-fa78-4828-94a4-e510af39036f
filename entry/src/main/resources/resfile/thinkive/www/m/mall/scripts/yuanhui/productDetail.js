// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "yuanhui/productDetail",
        _pageId = "#yuanhui_productDetail ";
    var productInfo;
    var ut = require("../common/userUtil");
    var chartsUtils = require("chartsUtils");
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        smDetails(); //详情渲染
        renderPdf(); //产品合同
        renderHistory(); //渲染历史净值
        renderChart("1"); // 渲染收益走势
        getTrend(); //查询各区间涨幅
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        /* 基金详情 */
        appUtils.bindEvent($(_pageId + " .fundInfo"), function () {
            appUtils.pageInit(_page_code, "yuanhui/fundInfo");
        });

        /* 历史净值 更多*/
        appUtils.bindEvent($(_pageId + " #HistoryList span"), function () {
            appUtils.pageInit(_page_code, "highEnd/historyValueList");
        });

        /* 交易规则 */
        appUtils.bindEvent($(_pageId + " .trade_rule"), function () {
            appUtils.pageInit(_page_code, "yuanhui/tradeRule");
        });

        //展示限定时间的收益
        appUtils.bindEvent(_pageId + " .spliceDate li", function () {
            $(_pageId + " .spliceDate").find("li").removeClass("active").filter(this).addClass("active");
            var section = $(this).attr("section");
            renderChart(section);
        })

        appUtils.bindEvent($(_pageId + " .tabbar-line .tab-item"), function () {
            $(_pageId + " .tabbar-line .tab-item").removeClass("active").filter(this).addClass("active");
            $(_pageId + " .content").hide();
            $(_pageId + " .content").eq($(this).index()).show()
        });


    }

    //私募产品详情查询（102043）
    function smDetails() {
        var fund_code = productInfo.fund_code;
        var param = {
            fund_code: fund_code,
            prod_sub_type2: productInfo.prod_sub_type2
        };
        service.reqFun102043(param, function (datas) {
            if (datas.error_no == 0) {
                var preincomerate = datas.results[0].preincomerate;//年化业绩基准
                preincomerate = (+preincomerate).toFixed(2);
                var inrest_term = datas.results[0].inrest_term;//期限
                var risk_level_desc = datas.results[0].risk_level_desc;//风险等级描述
                var threshold_amount = datas.results[0].threshold_amount;//起投金额
                var interest_method = datas.results[0].interest_method;//付息方式
                var detailed_pictures = datas.results[0].detailed_pictures;//查看更多图片
                var used_per = datas.results[0].used_per;//募集进度
                var surplus_amount = datas.results[0].surplus_amount;//剩余额度
                var issue_start_time = datas.results[0].issue_start_time;//募集开始时间
                var issue_end_time = datas.results[0].issue_end_time;//募集结束时间
                var return_visit_start_date = datas.results[0].return_visit_start_date;//回访开始
                var return_visit_end_date = datas.results[0].return_visit_end_date;//回访结束
                var purconfirm_days = datas.results[0].purconfirm_days;//购买确认
                var redconfirm_days = datas.results[0].redconfirm_days;//赎回确认
                var surplus_num = datas.results[0].surplus_num;//剩余认购人数
                var prod_sname = datas.results[0].prod_sname;//产品简称
                var risk_level = datas.results[0].risk_level;//风险等级
                var return_visit_date = datas.results[0].return_visit_date;//回访日期
                var mgrcompName = datas.results[0].mgrcomp_name;//管理人
                var trusteeName = datas.results[0].trustee_name;//托管人
                var fund_code = datas.results[0].fund_code;//产品代码
                var establishDate = datas.results[0].establish_date;//成立时间
                var nav = datas.results[0].nav;//净值
                var nav_date = datas.results[0].nav_date;//净值日期
                var this_year_rate = datas.results[0].this_year_rate;//今年来收益
                var found_rate = datas.results[0].found_rate;//成立来收益
                var yh_prod_features = datas.results[0].yh_prod_features;//产品特点
                var drawdown = datas.results[0].drawdown;//最大回撤
                var sharpe_ratio = datas.results[0].sharpe_ratio;//夏普比率

//                var str = "<div class='fund_type_name riskDesc empty'>" + risk_level_desc + "</div>";
                var str='';
                if (yh_prod_features) {
                    yh_prod_features = yh_prod_features.split("|");
                    for (var i = 0; i < yh_prod_features.length; i++) {
                    	str += "<div class='fund_type_name yh_prod_features empty' style='color:#e5443c;border-color:#e5443c'>" + yh_prod_features[i] + "</div>";
                    }
                }
                $(_pageId + " .product_tip").html(str);

                $(_pageId + " .mgrcompName").text(mgrcompName);
                $(_pageId + " .trusteeName").text(trusteeName);
                $(_pageId + " .fund_code").text(fund_code);
                $(_pageId + " .establishDate").text(tools.ftime(establishDate));
                $(_pageId + " .nav").text(tools.fmoney(nav));
                $(_pageId + " .nav_date").text(nav_date ? "(" + tools.ftime(nav_date) + ")" : "");
                $(_pageId + " .this_year_rate").text(this_year_rate ? tools.fmoney(this_year_rate) + "%" : "--");
                $(_pageId + " .found_rate").text(found_rate ? tools.fmoney(found_rate) + "%" : "--");
                $(_pageId + " .drawdown").text(tools.fmoney(drawdown));
                $(_pageId + " .sharpe_ratio").text(tools.fmoney(sharpe_ratio));


                //适当性评估需要字段
                appUtils.setSStorageInfo("productInfo", {...datas.results[0],...productInfo});
                productInfo = datas.results[0];
                tools.initPriFundBtn(productInfo, _pageId);
                $(_pageId + " header h1").html(productInfo.prod_sname || "产品详情");

                $(_pageId + " .annual").text(preincomerate + '%');
                $(_pageId + " .expires").text(inrest_term)
                if (risk_level_desc) {
                    if (risk_level.substr(1) >= 4) {
                        $(_pageId + " .riskDesc").css({color: "#e5443c", borderColor: "#e5443c"});
                    } else {
                        $(_pageId + " .riskDesc").css({color: "#00c35e", borderColor: "#00c35e"});
                    }
                    $(_pageId + " .riskDesc").text(risk_level_desc).show();
                } else {
                    $(_pageId + " .riskDesc").hide()
                }
                if (threshold_amount) {
                    $(_pageId + " .thresholdAmount").text(threshold_amount / 10000 + '万元').show();
                } else {
                    $(_pageId + " .thresholdAmount").hide();

                }
                if (interest_method) {
                    $(_pageId + " .interestMethod").text(interest_method).show();

                } else {
                    $(_pageId + " .interestMethod").hide();

                }
                $(_pageId + " .usedPer").text(tools.fmoney(used_per * 100) + '%');
                $(_pageId + " .bar").css({width: used_per * 100});
                $(_pageId + " .surplusAmount").text(tools.fmoney(surplus_amount) + '万元');
                $(_pageId + " .issueEndtime").text(tools.ftime(issue_end_time));

                if (issue_start_time && issue_end_time) {
                    $(_pageId + " .issue").text(tools.ftime(issue_start_time.substr(4, 4), ".") + "-" + tools.ftime(issue_end_time.substr(4, 4), "."));
                }
                if (return_visit_start_date && return_visit_end_date) {
                    $(_pageId + " .visit").text(tools.ftime(return_visit_start_date.substr(4, 4), ".") + "-" + tools.ftime(return_visit_end_date.substr(4, 4), "."));
                }
                if (purconfirm_days && redconfirm_days) {
                    // $(_pageId + " .sealing").text(tools.ftime(purconfirm_days.substr(0,8), ".") + "-" + tools.ftime(redconfirm_days.substr(0,8), "."))
                    $(_pageId + " .sealing").text(purconfirm_days.substr(2, 2) + "." + purconfirm_days.substr(4, 2) + "." + purconfirm_days.substr(6, 2) +
                        "-" + redconfirm_days.substr(2, 2) + "." + redconfirm_days.substr(4, 2) + "." + redconfirm_days.substr(6, 2));
                }
                if (purconfirm_days) {
                    $(_pageId + " .qxr").text(tools.FormatDateText(purconfirm_days.substr(4, 4)) + '确认')
                } else {
                    $(_pageId + " .qxr").text('-月-日确认')

                }
                $(_pageId + " .nav").html(tools.fmoney(productInfo.nav, 4) + "元");
                $(_pageId + " .nav_date").html(productInfo.nav_date ? "(" + tools.ftime(productInfo.nav_date.substr(4, 4)) + ")" : "");
                getRateInfo();//费率
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });

    }

    //费率
    function getRateInfo() {
        // 产品详情查询--费率查询
        service.reqFun102003({fund_code: productInfo.fund_code}, function (data) {
            var businesscode = productInfo.businesscode; // 22申购 20认购
            if (data.error_no == 0) {
                var result = data.results[0];
                appUtils.setSStorageInfo("productRate", data.results[0]);
                var purchaseRateStr = "";
                var operateRateStr = "";
                var redeemRateStr = "";
                if (result.purchaseRate && result.purchaseRate.length > 0 && businesscode == "22") {
                    if (result.purchaseRate.length == 1 && (result.purchaseRate[0].chgrate_tval == 0 || result.purchaseRate[0].fcitem_lval == 0)) {
                        var rateStr = "";
                        var discount_ratevalue = result.purchaseRate[0].discount_ratevalue;
                        if (discount_ratevalue) {
                            rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.purchaseRate[0].chgrate_tval + result.purchaseRate[0].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + result.purchaseRate[0].chgrate_unit;
                        } else {
                            rateStr = result.purchaseRate[0].chgrate_tval + result.purchaseRate[0].chgrate_unit;
                        }
                        purchaseRateStr +=
                                '<div class="highFinancialInfo-item"><p class="item-left ">' + result.purchaseRate[0].chgrate_item_desc + '</p><p class="item-right g_fontSize14 text_darkgray">' + rateStr + '</p></div>';
                    } else {
                        purchaseRateStr += '<div class="highFinancialRateInfo">' +
                            '<h1 style="margin-top: 0.1rem;color: #282828;">' + result.purchaseRate[0].chgrate_item_desc + '</h1><div><p>适用金额</p>' +
                            '<p>' + result.purchaseRate[0].chgrate_item_desc + '</p></div>';
                        for (var i = 0; i < result.purchaseRate.length; i++) {
                            var fcitem_tval = result.purchaseRate[i].fcitem_tval; //最大
                            var fcitem_lval = result.purchaseRate[i].fcitem_lval; //最小
                            var discount_ratevalue = result.purchaseRate[i].discount_ratevalue;
                            var purchaseStr = "";
                            if (fcitem_lval == 0) { //最小为0
                                purchaseStr += fcitem_tval / 10000 + '万元以下';
                            } else if (fcitem_tval == "-1") { //最大
                                purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                            } else {
                                purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                            }
                            var rateStr = "";
                            if (discount_ratevalue) {
                                rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit + "</span>" + discount_ratevalue + result.purchaseRate[i].chgrate_unit;
                            } else {
                                rateStr = result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit;
                            }
                            purchaseRateStr += '<div>' +
                                '<p>' + purchaseStr + '</p>' +
                                '<p>' + rateStr + '</p>' +
                                '</div>';
                        }
                    }
                    purchaseRateStr += "</div>"
                } else if (result.subscription && result.subscription.length > 0 && businesscode == "20") {
                    if (result.subscription.length == 1 && (result.subscription[0].chgrate_tval == 0 || result.subscription[0].fcitem_lval == 0)) {
                        var rateStr = "";
                        var discount_ratevalue = result.subscription[0].discount_ratevalue;
                        if (discount_ratevalue) {
                            rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.subscription[0].chgrate_tval + result.subscription[0].chgrate_unit + "</span>" + tools.fmoney(discount_ratevalue) + result.purchaseRate[0].chgrate_unit;
                        } else {
                            rateStr = result.subscription[0].chgrate_tval + result.subscription[0].chgrate_unit;
                        }
                        purchaseRateStr +=
                                '<div class="highFinancialInfo-item"><p class="item-left ">' + result.subscription[0].chgrate_item_desc + '</p><p  class="item-right g_fontSize14 text_darkgray">' + rateStr + '</p></div>';
                    } else {
                        purchaseRateStr += '<div class="highFinancialRateInfo">' +
                            '<h1 style="margin-top: 0.1rem;color: #282828;">' + result.subscription[0].chgrate_item_desc + '</h1><div><p>适用金额</p>' +
                            '<p>' + result.subscription[0].chgrate_item_desc + '</p></div>';
                        for (var i = 0; i < result.subscription.length; i++) {
                            var fcitem_tval = result.subscription[i].fcitem_tval; //最大
                            var fcitem_lval = result.subscription[i].fcitem_lval; //最小
                            var discount_ratevalue = result.subscription[i].discount_ratevalue;
                            var purchaseStr = "";
                            if (fcitem_lval == 0) { //最小为0
                                purchaseStr += fcitem_tval / 10000 + '万元以下';
                            } else if (fcitem_tval == "-1") { //最大
                                purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                            } else {
                                purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                            }
                            var rateStr = "";
                            if (discount_ratevalue) {
                                rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.subscription[i].chgrate_tval + result.subscription[i].chgrate_unit + "</span>" + discount_ratevalue + result.subscription[i].chgrate_unit;
                            } else {
                                rateStr = result.subscription[i].chgrate_tval + result.subscription[i].chgrate_unit;
                            }
                            purchaseRateStr += '<div>' +
                                '<p>' + purchaseStr + '</p>' +
                                '<p>' + rateStr + '</p>' +
                                '</div>';
                        }
                    }
                    purchaseRateStr += "</div>"
                } else {
                    purchaseRateStr += "<div class='title'><span>买入费率</span>" +
                        "<span class='title_right text_darkgray'>无</p>"
                }
                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1 style="margin-top: 0.1rem;color: #282828;">卖出费率</h1><div><p>适用期限</p>' +
                        '<p>卖出费率</p></div>';

                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var datestr = "";
                        var fcitem_lval = result.redeemRate[i].fcitem_lval; //最小
                        var fcitem_lvunit = result.redeemRate[i].fcitem_lvunit;//最小单位
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;//最大
                        var fcitem_tvunit = result.redeemRate[i].fcitem_tvunit;//最大单位
                        if (fcitem_tval == "-1") { //最大
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                        } else if (fcitem_lval == "0") { //最小
                            datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                        } else {
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                        }
                        redeemRateStr += '<div>' +
                            '<p>' + datestr + '</p>' +
                            '<p>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</p>' +
                            '</div>';
                    }
                    redeemRateStr += "</div>";
                } else {
                    redeemRateStr += "<div class='highFinancialInfo-item'><p class='item-left '>卖出费率</p>" +
                        "<p class='item-right g_fontSize14 shfl'>--</p>"
                }
                $(_pageId + " .buy_item_box").html(purchaseRateStr + operateRateStr);
                $(_pageId + " .sell_item_box").html(redeemRateStr);

                for (var i = 0; i < result.operateRate.length; i++) {
                    if (result.operateRate[i].chgrate_type == '11') {
                        $(_pageId + " .glf").text(tools.fmoney(result.operateRate[i].chgrate_tval) + result.operateRate[i].chgrate_unit)
                    }
                    if (result.operateRate[i].chgrate_type == '10') {
                        $(_pageId + " .fwf").text(tools.fmoney(result.operateRate[i].chgrate_tval) + result.operateRate[i].chgrate_unit)
                    }
                    if (result.operateRate[i].chgrate_type == '12') {
                        $(_pageId + " .tgf").text(tools.fmoney(result.operateRate[i].chgrate_tval) + result.operateRate[i].chgrate_unit)
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    function renderPdf() {
        /* 产品合同 */
        var buy_state = productInfo.buy_state;
        if (buy_state == "4") {
            buy_state = "1";
        } else if (buy_state == "3") {
            buy_state = "2";
        }
        tools.getPdf({
            agreement_type: "prod",
            fund_code: productInfo.fund_code,
            bookTitleMark: "0",
            agreement_sub_type: buy_state
        }); //获取协议
    }

    function renderHistory() {

        //累计净值走势
        service.reqFun102006({fund_code: productInfo.fund_code}, function (result) {
            if (result.error_no != "0") {
                layerUtils.iAlert(result.error_info);
                return;
            }
            var results = result.results;

            if (results.length == 0) {
                $(_pageId + " .accunav").html("--");
                $(_pageId + " #HistoryList").hide();
                $(_pageId + " #historyContent .list_content").html("<div class='nodata'>暂无数据</div>").css({"margin-bottom": "0.2rem"});
                return;
            }
            $(_pageId + " .accunav").html(tools.fmoney(results[0].accunav, 4)); //最新累计净值
            var html = "";
            for (var i = 0; i < results.length; i++) {
                if (i < 5) {
                    html += '<div class="item">' +
                        '<span>' + tools.ftime(results[i].end_date.substring(0, 8)) + '</span>' +
                        '<span>' + tools.fmoney(results[i].nav, 4) + '</span>' +
                        '<span>' + tools.fmoney(results[i].accunav, 4) + '</span>' +
                        '<span class=' + tools.addMinusClass(results[i].daily_return) + '>' + tools.fmoney(results[i].daily_return) + '%</span>' +
                        '</div>';
                } else {
                    break;
                }
            }
            $(_pageId + " #historyContent .list_content").html(html).css({
                "margin-bottom": "0",
                "padding-top": "0"
            });
            $(_pageId + " #HistoryList").show();
        });
    }

    function renderChart(section) {
        //查询源晖产品对标指数名称
        service.reqFun102037({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no != "0") {
                $(_pageId + " #chartContainer").html("暂无数据").css({
                    "line-height": "2rem",
                    "text-align": "center",
                    "padding-top": "0.1rem"
                });
                $(_pageId + " .spliceDate").hide();
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            var keyList = data.results;
            keyList.unshift({
                index_code: "fund",
                index_name: "本基金"
            });
            service.reqFun102036({fund_code: productInfo.fund_code, section: section}, function (datas) {
                if (datas.error_no != "0") {
                    $(_pageId + " #chartContainer").html("暂无数据").css({
                        "line-height": "2rem",
                        "text-align": "center",
                        "padding-top": "0.1rem"
                    });
                    $(_pageId + " .spliceDate").hide();
                    layerUtils.iAlert(datas.error_info);
                    return;
                }
                var results = datas.results, dataArr = [];
                results = results.reverse();
                if (datas.results.length == 0) {
                    $(_pageId + " #chartContainer").html("暂无数据").css({
                        "line-height": "2rem",
                        "text-align": "center",
                        "padding-top": "0.1rem"
                    });
                    $(_pageId + " .spliceDate").hide();
                    return;
                }
                $(_pageId + " .spliceDate").show();

                var datalist = [];

                for (var i = 0; i < keyList.length; i++) {
                    var singleArr = [];
                    for (var j = 0; j < results.length; j++) {
                        singleArr.push([Date.UTC(results[j].date.substr(0, 4), results[j].date.substr(4, 2) - 1, results[j].date.substr(6, 2)), Number((results[j][keyList[i].index_code + '_accunav'] - results[0][keyList[i].index_code + '_accunav']) / results[0][keyList[i].index_code])]);
                    }
                    var obj = {
                        name: keyList[i].index_name,
                        data: singleArr,
                    }
                    if (keyList[i].index_code == "fund") {
                        obj["fillColor"] = {
                            linearGradient: {
                                y1: 0,
                                x1: 0,
                                y2: 1,
                                x2: 0
                            },
                            stops: [[0, "rgba(632,64,63,0.3)"], [1, "rgba(255,255,255,0.5)"]]
                        }
                    } else {
                        obj["fillColor"] = "rgba(0, 0, 0, 0)";
                    }
                    datalist.push(obj)
                }

                var config = {
                    colors: colorList,
                    chart: {
                        type: "area"
                    },
                    title: {
                        text: ""
                    },
                    subtitle: {
                        text: ""
                    },
                    xAxis: {
                        type: "datetime",
                        tickLength: 0,
                        lineWidth: 1,
                        lineColor: "#e1e1e1",
                        labels: {
                            formatter: function () {
                                var t = new Date(this.value);
                                return "".concat(t.getFullYear(), "-").concat(t.getMonth() + 1, "-").concat(t.getDate())
                            }
                        }
                    },
                    yAxis: {
                        title: {
                            text: ""
                        },
                        gridLineDashStyle: "dash",
                        labels: {
                            formatter: function () {
                                return (this.value * 100).toFixed(2) + "%"
                            }
                        }
                    },
                    plotOptions: {
                        series: {
                            threshold: null,
                            marker: {
                                radius: 4,
                                enabled: !1,
                                symbol: "circle"
                            },
                            states: {
                                hover: {
                                    lineWidthPlus: 0,
                                    radiusPlus: 2
                                }
                            },
                            connectNulls: !0,
                            turboThreshold: 0
                        }
                    },
                    tooltip: {
                        backgroundColor: "rgba(255,255,255,0.85)",
                        style: {
                            opacity: 1,
                            lineHeight: "24px",
                        },
                        shared: !0,
                        useHTML: !0,
                        crosshairs: [{
                            width: 1,
                            color: "#cccccc"
                        }],
                        formatter: function () {
                            var t = 0;
                            this.points.forEach((function (e, i) {
                                    var n = e.series.name.length;
                                    n > t && (t = n)
                                }
                            ))
                            var n = 90 + 14 * t, o = new Date(this.x);
                            var s = '<div style="width:' + n + 'px;height:20px;font-size: 12px;color: #353535;margin-left: 9px">' + o.getFullYear() + "-" + (o.getMonth() + 1) + "-" + o.getDate() + "</div>";
                            this.points.forEach(function (t, i) {
                                s += '<div class="chart_tooltip_item" style="width:' + n + 'px;margin-top:5px;height:20px;"><i style="background:' + t.series.color + ';    display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">' + t.series.name + '： </span><span style="color: ' + t.series.color + '"><b>' + (t.y * 100).toFixed(2) + "%</b></span></div>"
                            })
                            return s
                        },
                        borderColor: "#cccccc"
                    },
                    legend: {
                        borderColor: "transparent",
                        enabled: true,
                    },
                    series: datalist
                }
                $(_pageId + ' #chartContainer').highcharts(config);
            })


        }, {isLastReq: false})

    }

    function getTrend() {
        service.reqFun102038({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var result = data.results[0];
            $(_pageId + " .section1").html(result.section1 ? tools.fmoney(result.section1) + "%" : "--");
            $(_pageId + " .section3").html(result.section3 ? tools.fmoney(result.section3) + "%" : "--");
            $(_pageId + " .section6").html(result.section6 ? tools.fmoney(result.section6) + "%" : "--");
            $(_pageId + " .section12").html(result.section12 ? tools.fmoney(result.section12) + "%" : "--");
            $(_pageId + " .establish").html(result.establish ? tools.fmoney(result.establish) + "%" : "--");
        })
    }

    function destroy() {
        $(_pageId + " .spliceDate").hide();
        $(_pageId + " #chartContainer").html("");
        $(_pageId + " .tabbar-line .tab-item").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .content").hide().eq(0).show();
        $(_pageId + " .empty").html("");
        $(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .thfundBtn").html("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var yuanhuiDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = yuanhuiDetail;
});
