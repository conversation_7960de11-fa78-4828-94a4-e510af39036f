// 会员福利 - 积分兑换
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        ut = require("../common/userUtil");
    var _pageId = "#vipBenefits_phoneVouchersConfirm";
    var _pageCode = "vipBenefits/phoneVouchersConfirm";
    var tools = require("../common/tools");
    var info;
    var phone;
    var operatortype;

    function init() {
        info = appUtils.getSStorageInfo("exchangeInfo");
        $(_pageId + " .pvConfirm .left img").attr("src", require("gconfig").global.oss_url + info.img);
        $(_pageId + " .pvConfirmGoods .top").text(info.name);
        $(_pageId + " .pvConfirmGoods .jifen_one em").text(info.points);
        if(validatorUtil.isNotEmpty(info.type) && info.type == '1'){
            //类型,1为话费券，2为京东券
            $(_pageId + " .card").hide();
            $(_pageId + " .phone_securities").show();
        }else if(validatorUtil.isNotEmpty(info.type) && info.type == '2'){
            $(_pageId + " .card").hide();
            $(_pageId + " .Jingdong_card").show();
        }
    }


    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            appUtils.setSStorageInfo("cardtType", info.type);
            pageBack();
        });
        //跳转京东E卡兑换使用流程说明页面
        appUtils.bindEvent($(_pageId + " .jdUsingProcess"), function () {
            appUtils.setSStorageInfo("previouspage", _pageCode);
            appUtils.pageInit(_pageCode, "vipBenefits/jdUsingProcess",info);
        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #rechargeInfo").empty();
            $(_pageId + " #jymm").val("");
            guanbi();
        });
        //点击确认兑换按钮-话费劵
        appUtils.bindEvent($(_pageId + " #phone_btns"), function () {
            //检查手机号
            phone = $.trim($(_pageId + " #topUp").val());
            if (validatorUtil.isEmpty(phone)) {
                layerUtils.iMsg(-1, "请输入手机号码");
                return;
            }
            if (!validatorUtil.isMobile(phone)) {
                layerUtils.iMsg(-1, "请确定您输入的手机号是否正确");
                return;
            }
            reqFun108007({mobile: phone});//运营商判断,1移动 2电信 3联通
        });
        //点击确认兑换按钮-京东券
        appUtils.bindEvent($(_pageId + " #phone_btns_jd"), function () {
            // 交易密码框
            $(_pageId + " #rechargeInfo").html("使用<em>" + info.points + "</em>积分，兑换<em>" + info.name + "</em>");
            $(_pageId + " .pop_layer").show();
            passboardEvent();
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "vipBenefits_phoneVouchersConfirm";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);

        });
        //交易密码确定
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            var jymm = $(_pageId + " #jymm").val();
            guanbi();
            if (jymm.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            if(validatorUtil.isNotEmpty(info.type) && info.type == '2'){
                // 如果兑换京东券
                phone = ut.getUserInf().mobileWhole;
            }
            var param = {
                id: info.id,
                mobile: phone,
                trans_pwd: jymm, //交易密码
                type:operatortype,//运营商
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                reqFun108003(param);

            }, {isLastReq: false});
        });
    }

    //运营商判断(108007),1移动 2电信 3联通。
    // yd_status 移动,dx_status 电信,lt_status 联通，0启用 1维护中 2关闭
    function reqFun108007(param) {
        service.reqFun108007(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                operatortype = results.type;
                if (operatortype == '1') {//移动
                    if (info.yd_status == '1') {
                        layerUtils.iAlert("运营商维护中");
                        return;
                    } else if (info.yd_status == '2') {
                        layerUtils.iAlert("移动用户不支持此话费兑换，请选择其它话费券");
                        return;
                    }
                }else if(operatortype == '2'){//电信
                    if (info.dx_status == '1') {
                        layerUtils.iAlert("运营商维护中");
                        return;
                    } else if (info.dx_status == '2') {
                        layerUtils.iAlert("电信用户不支持此话费兑换，请选择其它话费券");
                        return;
                    }
                }else if(operatortype == '3'){//联通
                    if (info.lt_status == '1') {
                        layerUtils.iAlert("运营商维护中");
                        return;
                    } else if (info.lt_status == '2') {
                        layerUtils.iAlert("联通用户不支持此话费兑换，请选择其它话费券");
                        return;
                    }
                }
                // 交易密码框
                $(_pageId + " #rechargeInfo").html("使用<em>" + info.points + "</em>积分，兑换<em>" + info.name + "</em></br>"+"兑换手机号:<em>"+phone+"</em>");
                $(_pageId + " .pop_layer").show();
                passboardEvent();
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "vipBenefits_phoneVouchersConfirm";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //商品兑换(108003)
    function reqFun108003(param) {
        service.reqFun108003(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                results[0]["type"] = info.type;
                appUtils.pageInit(_pageCode, "vipBenefits/phoneVouchersResult", results[0]);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
        //关闭键盘
        function guanbi() {
            $(_pageId + " #jymm").val("");
            for (var i = 0; i < 6; i++) {
                var idspan = "#span0" + i
                $(_pageId + " " + idspan).removeClass("point");
            }
            var param = {};
            param["funcNo"] = "50211";
            require("external").callMessage(param);
        }
        //交易密码
        function passboardEvent() {
            window.customKeyboardEvent = {
                keyBoardFinishFunction: function () {
                }, // 键盘完成按钮的事件
                keyBoardInputFunction: function () {
                    var jymm = $(_pageId + " #jymm").val();
                    var len = jymm.length;
                    if (len <= 6) {
                        for (var i = 0; i < 6; i++) {
                            var idspan = "#span0" + i
                            if (i < len) {
                                $(_pageId + " " + idspan).attr("class", "point");
                                continue;
                            }
                            $(_pageId + " " + idspan).removeAttr("class", "point");
                        }
                    } else {
                        jymm = jymm.substring(0, 6);
                        $(_pageId + " #jymm").val(jymm);
                    }
                } // 键盘的输入事件
            };
        }



    function destroy() {
        $(_pageId + " .pvConfirm .left img").attr("src", "");
        $(_pageId + " .pvConfirmGoods .top").text("");
        $(_pageId + " .pvConfirmGoods .jifen_one em").text("");
        guanbi();
        phone =  null;
        operatortype = null;
    }


    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();

    }


    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});