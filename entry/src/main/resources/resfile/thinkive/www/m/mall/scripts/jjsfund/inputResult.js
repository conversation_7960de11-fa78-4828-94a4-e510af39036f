//晋金宝转入结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageUrl = "jjsfund/inputResult",
        _pageId = "#jjsfund_inputResult ";
    var outputInfo;
    var num;
    var t;

    function init() {
        num = 5;
        outputInfo = appUtils.getPageParam();
        countDown();
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " #goCompleted"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            if(routerList.length == "3") {
                appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                appUtils.pageInit(_pageUrl, "thfund/myProfit");
            } else {
                routerList.splice(-1);
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.pageBack();
            }
        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            if(routerList.length == "3") {
                appUtils.setSStorageInfo("routerList", ["login/userIndexs", "thfund/myProfit"]);
                appUtils.pageInit(_pageUrl, "thfund/JJBtransaction");
            } else {
                routerList.splice(-2);
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.pageInit(_pageUrl, "thfund/JJBtransaction");
            }
        })
    }

    function destroy() {
        $(_pageId).find(".tc").val("--");//所有填充内容清理
        clearInterval(t);
        $(_pageId + ' #failinf').text("");
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').show();
        $(_pageId + " .application_firststage").html("");
    }

    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun102030(outputInfo, function (data) {
                    if (data.error_no == "0") {
                        $(_pageId + ".load").hide();
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败 8 确认成功 9 确认失败
                        if (data.results[0].trans_status == "8") {
                            showTime(data.results[0].trans_date, data.results[0].trans_time);//显示充值受理/收益到账等时间
                            $(_pageId + " .success").show();//充值成功
                        } else if (data.results[0].trans_status == "9" || data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            if (data.results[0].host_desc) {
                                $(_pageId + ' #failinf').text('失败原因：' + data.results[0].host_desc);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，交易失败');
                            }
                            $(_pageId + " .fail").show();//充值失败
                        } else {
                        	showTime(data.results[0].trans_date, data.results[0].trans_time);//显示充值受理/收益到账等时间
                            $(_pageId + " .wait").show();//充值无结果
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info)
                    }
                })
            }

        }, 1000)
    }

    //显示充值相关时间日期
    function showTime(date, time) {
        // application_firststage
        var html = ""
        html = '<ul>' +
            '       <li>' +
            '            <img class="activity" src="images/application_one.png" alt=""/>\n' +
            '            <h3>' + fTime(date) + " " + fTime(time) + '，转入申请已受理</h3>\n' +
            '        </li>' +
            '        <li>' +
            '             <img src="images/application_two.png" alt=""/>\n' +
            '             <h3>预计十分钟资金到账</h3>\n' +
            '        </li>\n' +
            '    </ul>';
        $(_pageId + " .application_firststage").html(html);
        
    }

    function fTime(time) {
        if(!time) {
            return "";
        }
        if(time.length == 6) {
            return time.substr(0,2) + ":" +  time.substr(2,2) + ":" + time.substr(4,2);
        }
        if(time.length == 8) {
            return time.substr(4,2) + "月" +  time.substr(6,2) + "日";
        }
        if (time.length > 8 && time.length <= 14) {
            var hour = time.substring(8, 10);
            var minute = time.substring(10, 12);
            var seconds = time.substring(12, 14);
            return time.substr(4,2) + "月" +  time.substr(6,2) + "日" + hour + ":" + minute;
        }
    }
    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
