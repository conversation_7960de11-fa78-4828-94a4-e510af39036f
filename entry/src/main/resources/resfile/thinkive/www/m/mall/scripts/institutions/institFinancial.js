// 机构理财 页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils");
    var _pageId = "#institutions_institFinancial";
    let common = require("common");
    var _pageCode = "institutions/institFinancial";
    var tools = require("../common/tools");
    require("../common/clipboard.min.js");
    function init() {
        copyContent("urlCopy");
        $(_pageId + " #custServiceTel .last").text(require("gconfig").global.custServiceTel);
    }
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), ()=> {
            pageBack();
        });
        //拨打电话
        appUtils.preBindEvent($(_pageId), " #custServiceTel", function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = require("gconfig").global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //二维码打开
        appUtils.bindEvent($(_pageId + " #erCode"), ()=> {
            $(_pageId + " #erCodeTip").show();
        });
        //隐藏二维码
        appUtils.bindEvent($(_pageId + " #erCodeTip"), function () {
            $(_pageId + " #erCodeTip").hide();
        });
    }
    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            layerUtils.iAlert("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }
    //销毁事件
    function destroy() {

    }
    /*
     * 返回
     */
    function pageBack() {
         appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});