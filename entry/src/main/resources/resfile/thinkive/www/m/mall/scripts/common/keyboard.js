/**
 * 密码键盘插件
 */
define(function (require, exports, module) {
    var keyPanel = require('keyPanel'); // 键盘插件
    var myKeyPanel = keyPanel(); // H5密码键盘
    //var myKeyPanel = new $.KeyPanel(); // H5密码键盘
    var option = {};
    var appUtils = require("appUtils");

    /**
     * 初始化密码控件
     * 适用于弹出密码窗口
     * @param pageId ：页面ID
     * @param  pwdInputId： 密码文本框ID
     * @param callBack:密码支付完成后执行的回调函数
     *
     */
    function initPwdWindow(opt) {
        option = {
            "pageId": "", // 页面ID 必传
            //"pwdWindowId": "", // 密码窗口ID 必传
            "pwdInputId": "", // 密码文本框ID 必传
            "clickObj": "", // 点击对象，点击该对象呼出密码框，默认密码文本框父节点
            "keyboardType":1,//键盘的类型,1是顺序数字键盘
            "callBack": null // 密码输入完成后需要调用的函数
        }

        // 合并外部入参
        $.extend(option, opt);

        var pwdInputEle = option.pageId + " " + option.pwdInputId;
        var clickObj = option.clickObj || $(pwdInputEle).parent();
        clickObj = option.pageId + " " + clickObj;
        appUtils.bindEvent($(clickObj), function (e) {
            e.stopPropagation();
            // 执行初始化
            myKeyPanel.init(pwdInputEle, option.keyboardType, true, {skinName: "black"});
        }, "click");

    }

    /**
     * 弹出支付窗口 注：此方法依赖于initPwdWindow
     */
    function showPwdWindow() {
        var inputId = option.pageId + " " + option.pwdInputId;
        option.pwdWindowId && $(option.pageId + " " + option.pwdWindowId).show();
        //$(inputId).siblings("span").html("");
        myKeyPanel.init(inputId, option.keyboardType, true, {skinName: "black"}); // 执行初始化
    }

    /**
     * 隐藏支付窗口 注：此方法依赖于initPwdWindow
     */
    function hidePwdWindow() {
    	option.pwdWindowId && $(option.pageId + " " + option.pwdWindowId).hide();
        hideKeyBoard();
    }

    /**
     *  初始化密码文本框
     */
    function initPwdInput(pageId, pwdId, callBack) {
        // 点击密码弹出键盘 PC：调用H5键盘，Android & ios 调用原生键盘
        appUtils.bindEvent($(pageId + " #" + pwdId), function (e) {
            e.stopPropagation();
            myKeyPanel.init(this, option.keyboardType, true, {skinName: "black"}); // 执行初始化
        }, "click");
        // PC：绑定文本框input事件 ，Android & Ios 绑定原生键盘监听事件
        appUtils.bindEvent($(pageId + " #" + pwdId), function (e) {
            var values = $(this).attr("value");
            $(this).val(values);
            e.stopPropagation();
            if (typeof(callBack) == "function") {
                callBack();
            }
        }, "input");

        // 点击页面关闭键盘
//        appUtils.bindEvent(pageId, function () {
//            hideKeyBoard();
//        });
    }
    /**
     * 隐藏键盘
     */
    function hideKeyBoard() {
        if (myKeyPanel) {
            myKeyPanel.close(); // 隐藏h5键盘
        }
    }

    var keyboardUtils = {
        "initPwdWindow": initPwdWindow, // 初始化密码输入窗口，弹出窗
        "showPwdWindow": showPwdWindow, // 显示密码窗口
        "hidePwdWindow": hidePwdWindow, // 隐藏密码窗口
        "initPwdInput": initPwdInput, // 初始化密码文本框
        "hideKeyBoard": hideKeyBoard // 隐藏键盘
    };

    // 暴露对外的接口
    module.exports = keyboardUtils;

});