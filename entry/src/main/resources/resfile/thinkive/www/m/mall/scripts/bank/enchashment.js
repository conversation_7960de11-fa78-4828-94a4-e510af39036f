define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_enchashment ";
    var gconfig = require("gconfig");
    var ut = require("../common/userUtil");
    var _pageCode = "bank/enchashment";
    var validatorUtil = require("validatorUtil");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var sms_mobile = require("../common/sms_mobile");
    var avail_amt;
    var tools = require("../common/tools");
    var productInfo;
    var bankElectornReservedMobile;

    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId);
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        getAccountInfo();

    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #money").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "bank_enchashment";
            param["eleId"] = "money";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //全部
        appUtils.bindEvent($(_pageId + " .allMoney "), function () {
            $(_pageId + " #inputspanid span").text(tools.fmoney(avail_amt)).css({color: "#000000"}).addClass("active");
            $(_pageId + " #money").val(avail_amt);
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                if ($(_pageId + " #money").val().replace(/,/g, "") <= 0 || !$(_pageId + " #money").val().replace(/,/g, "")) {
                    layerUtils.iAlert("请输入取现金额");
                    return;
                }
                // 获取验证码
                var param = {
                    "mobile_phone": bankElectornReservedMobile,
                    "type": common.sms_type.bankEchash,
                    "send_type": "0",
                    "mobile_type": "2",
                    "bank_electron_name": productInfo.bank_channel_name


                };
                sms_mobile.sendPhoneCode(param)
            }
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if ($(_pageId + " #money").val().replace(/,/g, "") <= 0 || !$(_pageId + " #money").val().replace(/,/g, "")) {
                layerUtils.iAlert("请输入取现金额");
                return;
            }
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            //进行取现
            var trans_amt = $(_pageId + " #money").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                trans_amt: trans_amt, //交易金额
                message_code: verificationCode,
                bank_channel_code: productInfo.bank_channel_code,
                sms_mobile: bankElectornReservedMobile,
                sms_code: verificationCode
            };
            trade(param);
        });
    }

    function trade(param) {
        service.reqFun151005(param, function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no == "-********") {
                layerUtils.iConfirm("您的身份证已过期，请重新上传后取现", function () {

                }, function () {
                    appUtils.pageInit(_pageCode, "account/uploadIDCard");
                }, "取消", "确定")
                return;
            }
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/echashResult", {trans_serno: trans_serno});
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #money"),
            endcallback: function () {
                var curVal = $(_pageId + " #money").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #money").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #money").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #money").val(curVal.substring(0, curVal.length - 1));
                }
                if (parseFloat(curVal) > avail_amt) {
                    $(_pageId + " #money").val(avail_amt);
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #money").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #money").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            }
        })
    }

    //查询电子银行信息
    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-2);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                $(_pageId + " .bankElectronIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                $(_pageId + " .bankIcon img").attr("src", "./images/bank_" + results.bd_bank_code + ".png");
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bankInfo").find("p").eq(0).html(results.bd_bank_name + "(尾号" + results.bank_acct_no.substr(-4) + ")");
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "（尾号" + results.acct_no.substr(-4) + "）");
                $(_pageId + " .avail_amt").text(tools.fmoney(results.avail_amt) + "元");
                avail_amt = results.avail_amt;
                bankElectornReservedMobile = results.mobile_phone;
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        $(_pageId + " .prod_sname").html("--");//产品全称
        // $(_pageId + " .header_inner h1").text("--");
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");//产品编码
        $(_pageId + " .fund_type_name").html("--");//产品编码
        $(_pageId + " .threshold_amount").html("--元起购");//产品编码
        $(_pageId + " .purchaseBtn").text("--");
        $(_pageId + " .isShow").hide();
        $(_pageId + " .bankElectronIcon img").attr("src", "");
        $(_pageId + " #inputspanid span").text("请输入取现金额").css({color: "#999999"}).removeClass("active");
        $(_pageId + " #inputspanid span").attr("text", "请输入取现金额");
        $(_pageId + " #money").val("");
        $(_pageId + " #verificationCode").val("");
        sms_mobile.destroy(_pageId)
        monkeywords.destroy();
        guanbi();
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankEnch = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankEnch;
});
