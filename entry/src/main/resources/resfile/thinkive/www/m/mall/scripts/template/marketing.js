// 产品营销页模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        _page_code = "template/marketing",
        _pageId = "#template_marketing";
    var global = gconfig.global;
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];
    // let productInfo;
    var ut = require("../common/userUtil");
    require("chartsUtils");
    require('../common/echarts.min');
    let heighEndProduct //new 一个 vue 实例
    let createdData;
    let activeClass;
    var bottomImg
    var headerImg
    var vipBenefitsTaskData, startTime, timer = null;
    let yjProdId;
    //获取产品详情
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '2',
                fund_code: createdData.fund_code
            }
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "")
            })
        })
    }
    async function init() {
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        createdData = appUtils.getSStorageInfo("productInfo"); //产品信息
        //页面埋点初始化
        tools.initPagePointData({fundCode:createdData.fund_code});
        vipBenefitsTaskData = appUtils.getPageParam();
        if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
            startTime = Date.now();
            var readingTime = vipBenefitsTaskData.duration && parseFloat(vipBenefitsTaskData.duration) * 1000;
            if (vipBenefitsTaskData.task_id && vipBenefitsTaskData.task_type == '1') {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                    }, readingTime)
                }
            }
        }
        tools.getStepsInfo(createdData.fund_code)
        let html = await setTemplate() //拿到模板数据
        $(".main_marketProduct").html(html)   //渲染模板
        activeClass = $(_pageId + " .chartContent").attr("activeClass") ? $(_pageId + " .chartContent").attr("activeClass") : 0;
        heighEndProduct = new Vue({
            el: '#main_marketProduct',
            data() {
                return {
                    oss_url: global.oss_url,
                    // productInfo:{},//用户信息
                    detailsInfo: {},//详情信息
                    activeClass: activeClass,  //高亮时间（7天）
                    moreName: "更多",
                    bottomImg: null,
                    headerImg: null,
                    option: {
                        title: {
                            //   text: 'Stacked Line'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['Email', 'Union Ads'],
                            //   bottom:"-30px",
                            //   padding:[0,0,30,0]
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        toolbox: {
                            feature: {
                                saveAsImage: {}
                            }
                        },
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: 'Email',
                                type: 'line',
                                stack: 'Total',
                                data: [120, 132, 101, 134, 90, 230, 210]
                            },
                            {
                                name: 'Union Ads',
                                type: 'line',
                                stack: 'Total',
                                data: [220, 182, 191, 234, 290, 330, 310]
                            },
                        ],                     
                    },
                    timeListNew: [  //区间
                        {
                            name: '近1个月',
                            section: "1"
                        },
                        {
                            name: '近3个月',
                            section: "3"
                        },
                        {
                            name: '近6个月',
                            section: "6"
                        },
                        {
                            name: '近一年',
                            section: "12"
                        },
                    ],
                    timeListMore: [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        {
                            name: '近五年',
                            section: "60",
                            index: "5"
                        },
                        {
                            name: '成立来',
                            section: "",
                            index: "6"
                        },
                    ]
                }
            },
            //视图 渲染前
            created() {
                this.detailsInfo = createdData
                bottomImg = global.oss_url + $(".main_marketProduct #bottom_img_f").html();
                headerImg = global.oss_url + $(".main_marketProduct #head_img_f").html();
                this.smDetails();
                $(".main_marketProduct #head_img").attr("src", headerImg);
                $(".main_marketProduct #bottom_img").attr("src", bottomImg);
                // $(".main_marketProduct .product_bottom_img").css("background-image", `url(${this.bottomImg})`);
            },
            //渲染完成后
            mounted() {
                var appletEnterImg = global.oss_url + $(_pageId + " #applet_enter").html();
                $(_pageId + " #applet_enter_img").attr("src", appletEnterImg);
                let prodType = $(_pageId + " .marketing_desc").attr("prodType");
                let prodId = $(_pageId + " .marketing_desc").attr("prodId");
                if (prodType && (prodType == "taoli")) {
                    this.chartOne();
                    this.chartTwo();
                    this.getPerforTableData();
                   
                } else if (prodType && (prodType == "lianghua")) {
                    this.getPerforTableData();
                }else if(prodType && (prodType == "taoliNew")){               	
                	if(prodId){
                		yjProdId = prodId                    	
                    }else{
                    	yjProdId = this.detailsInfo.fund_code;                   	
                    }
                    this.getPerforTableDataLimit1(yjProdId);
                    this.initTrajectoryData(yjProdId);
                 }
            },
            //计算属性
            computed: {
                setNum1: () => {
                    return (str, len) => {
                        if (!str) return '--'
                        return (+str).toFixed(len)
                    }
                },
                //日期处理
                timeResult: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        if (num1) return tools.ftime(time.substr(num, num1), "-")
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
                //日期处理/
                dateResult: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        if (num1) return tools.ftime(time.substr(num, num1), "/")
                        return tools.ftime(time.substr(0, num), "/")
                    }
                },
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
                threshold_amount_Result: () => {
                    return (threshold_amount) => {
                        if (!threshold_amount) return '--元'
                        threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万元" : tools.fmoney(threshold_amount) + '元';
                        return threshold_amount
                    }
                },
                savePoint: () => {
                    return (value, num) => {
                        if (!value || value == '--') return '--'
                        return (+value).toFixed(num)
                    }
                }
            },
            //绑定事件
            methods: {
                //点击业绩计提基准说明
                annual_details() {
                    tools.recordEventData('1','annual_details','业绩说明');
                    layerUtils.iAlert("<span style='text-align:left;display:block'>本产品业绩计提基准为" + tools.fmoney(this.detailsInfo.p_expected_yield, 2) + "%（不构成最低收益保证）</span><span style='text-align:left;display:block'>" + this.detailsInfo.administrator_accrual + "</span>")
                },
                //颜色
                text_color(risk_level) {
                    if (!risk_level) return ''
                    if (risk_level.substr(1) >= 4) {
                        return 'm_text_red'
                    } else {
                        return 'm_text_green'
                    }
                },
                //跳转产品详情
                page_details() {
                    // this.detailsInfo.prod_sub_type2 = "100"
                    tools.recordEventData('1','page_details','产品详情');
                    tools.jumpPriDetailPage(_page_code, this.detailsInfo.prod_sub_type2);
                },
                //私募产品详情查询（102043）
                smDetails() {
                    service.reqFun102108(this.detailsInfo, (datas) => {
                        if (datas.error_no == 0) {
                            let newData = datas.results[0]
                            this.detailsInfo = { ...this.detailsInfo, ...newData };
                            appUtils.setSStorageInfo("productInfo", this.detailsInfo); //产品信息
                            appUtils.setSStorageInfo("fund_code", this.detailsInfo.fund_code);
                            appUtils.setSStorageInfo("prod_sub_type", this.detailsInfo.prod_sub_type);
                            appUtils.setSStorageInfo("financial_prod_type", this.detailsInfo.financial_prod_type);
                            this.detailsInfo.prod_sub_type2 = "100"
                            this.detailsInfo.transferShowDay = this.detailsInfo.transferable == '0' ? '否' : '满' + (this.detailsInfo.transfer_start_close_period ? this.detailsInfo.transfer_start_close_period : '--') + '天可转让'
                            this.detailsInfo.surplus_amount = this.detailsInfo.surplus_amount + '万元'
                            this.detailsInfo.p_expected_yield = tools.fmoney(this.detailsInfo.p_expected_yield) + '%';    //业绩计提
                            this.detailsInfo.nav_date = datas.results[0].nav_date
                            this.detailsInfo.per_yield = tools.fmoney(this.detailsInfo.per_yield)
                            tools.initPriFundBtn(this.detailsInfo, _pageId);
                            let prod_sname = this.detailsInfo.prod_name_list ? this.detailsInfo.prod_name_list : this.detailsInfo.prod_sname ? this.detailsInfo.prod_sname : this.detailsInfo.prod_exclusive_name
                            $(_pageId + " .header_inner #title").html(prod_sname);
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    });
                },
                chartOne() {
                    service.reqFun102155(this.detailsInfo, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartOne").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        // results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartOne").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            return;
                        }
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: function (params) {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">${t.seriesName}：</span><span style="color:${t.color}"><b>${t.value}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                position: [10, 10]
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].data.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                splitNumber: 5,
                                name: '收益率',
                                nameTextStyle: {
                                    padding: [-10, 0, 0, -40]
                                },
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: function (value, index) {
                                        return value + '%'
                                    }
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '5%',
                                bottom: '8%',
                                top: '18%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 0,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                            },
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                            item.data = JSON.parse(item.data.substring(1, item.data.length - 1))
                            series.push({
                                type: 'line',
                                name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].data;
                        config.series = series

                        config.xAxis.axisLabel.interval = results[0].data.length - 2
                        let dom = document.getElementById(`chartOne`);
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                chartTwo() {
                    service.reqFun102156(this.detailsInfo, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartOne").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        // results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartOne").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            return;
                        }
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: function (params) {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">${t.seriesName}：</span><span style="color:${t.color}"><b>${t.value}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                config: true,
                                //position: [10, 10]//绝对位置，相对于容器左侧 10px, 上侧 10 px
                                //position: function(point, params, dom, rect, size){ return [0,point[1]]; }//设置x轴固定不动 y轴上下跟随spa
                                position: function (point, params, dom, rect, size) {
                                    return [params[0] - 220, '10%'];
                                }
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].data.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                splitNumber: 5,
                                name: '收益率',
                                nameTextStyle: {
                                    padding: [-10, 0, 0, -20]
                                },
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: function (value, index) {
                                        return value + '%'
                                    }
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '5%',
                                bottom: '16%',
                                top: '18%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 0,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                            },
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                            item.data = JSON.parse(item.data.substring(1, item.data.length - 1))
                            series.push({
                                type: 'line',
                                name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].data;
                        config.series = series
                        config.xAxis.axisLabel.interval = results[0].data.length - 2
                        let dom = document.getElementById(`chartTwo`);
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                getPerforTableData() {
                    var html = "<tr>";
                    service.reqFun102171({ fund_code: this.detailsInfo.fund_code }, function (datas) {
                        if (datas.error_no == 0) {
                            var results = datas.results;
                            results.forEach((item, index) => {
                                item.found_date = tools.ftime(item.found_date, 0, 8);
                                let prodName = $(_pageId + " .perfor_table #tbody").attr("data-name")
                                if (index == 0) {
                                    html += `<td rowspan=${results.length} style="vertical-align: middle;">${prodName}</td>`
                                }
                                html += ` 
                                    <td>${item.found_date}</td>
                                    <td>${item.hold_date}天</td>
                                    <td>${tools.fmoney(item.income_abs)}%</td>
                                    <td style="color: rgb(229, 69, 60);">${tools.fmoney(item.income_ann)}%</td>
                                </tr>`;
                            })
                            $(_pageId + " .perfor_table #tbody").html(html);
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                },
                
                getPerforTableDataLimit1(fundcode) {
                   	let self = this; // 在进入异步操作之前保存对 this 的引用
                   	
                    service.reqFun102171({ fund_code: fundcode}, function (datas) {
                        if (datas.error_no == 0) {
                            var resultLimit1 = datas.results[0];
                            
                            
                            if(resultLimit1 ){
                            	self.$set(self.detailsInfo, 'found_date', resultLimit1.found_date);
        						self.$set(self.detailsInfo, 'hold_date', resultLimit1.hold_date);
        						self.$set(self.detailsInfo, 'income_ann', resultLimit1.income_ann);
                            }
                            let match = self.detailsInfo.prod_sname.match(/套利\d+号/); // 使用正则表达式匹配“套利”后跟一个或多个数字再加“号”
							if (match) {
   								self.$set(self.detailsInfo, 'found_name', match[0]);
							}
                           	
                                                   
                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    })
                },
                
                
               chooseTrajectoryData(item) {
                    if (item.section == this.activeClass) {
                        return;
                    }
                    tools.recordEventData('1','chooseTrajectoryData-' + item.section,item.name);
                    this.activeClass = item.section
                    this.moreName = "更多"
                    this.initTrajectoryData(yjProdId); // 获取业绩走势
                },
                chooseMoreList(item) {
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    tools.recordEventData('1','chooseMoreList-' + item.section,item.name);
                    this.moreName = item.name;
                    this.activeClass = item.section;
                    this.initTrajectoryData(yjProdId); // 获取业绩走势
                },
                // 获取业绩走势折线图
                initTrajectoryData(fundcode,section) {
                    section = this.activeClass;
                    $(_pageId + " #trajectory").show();
                    this.initShowChat = 0;
                    service.reqFun102146({ fund_code: fundcode, section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContaineryj").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContaineryj").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: (params) => {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">${t.seriesName}：</span><span style="color:${t.color}"><b>${(t.value * 100).toFixed(2)}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                hideDelay: 10
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                // axisLabel: {
                                //     formatter: '{value} %'
                                // },
                                splitNumber: 3,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: (value, index) => {
                                        return (value * 100).toFixed(2) + "%"
                                    }
                                },

                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '14%',
                                top: '10%',
                                containLabel: true,
                            },
                            /*legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                                // data: ['实际巡检', '计划巡检', '漏检次数'],
                            },*/
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            if (item.achievement) {
                                item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                                item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            }

                            series.push({
                                type: 'line',
                                name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series;
                        if (results[0].date) {
                            config.xAxis.axisLabel.interval = results[0].date.length - 2
                        }

                        let dom = document.getElementById(`chartContaineryj`);
                        // let dom = $(_pageId + " #chartContainer1");
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                 // 显示更多区间
                showMoreSpliceDate() {
                   tools.recordEventData('1','moreSpliceDate','显示更多区间');
                    //$(_pageId + " #tooltip").parent().css({ "z-index": "999" })
                    $(_pageId + " .thfundBtn").hide();
                    $(_pageId + " #moreSpliceDate").show();
                },
                cancelMoreSpliceDate() {
                    tools.recordEventData('1','thfundBtn','隐藏更多区间');
                    $(_pageId + " .thfundBtn").show();
                    $(_pageId + " #moreSpliceDate").hide();
                },
                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                },
                drop_down(){
                    tools.recordEventData('1','drop_down','下拉');
                    $(_pageId + " .box2").show();
                    $(_pageId + " .box1").hide();
                },
                retract(){
                    tools.recordEventData('1','retract','收起');
                    $(_pageId + " .box1").show();
                    $(_pageId + " .box2").hide();
                }
            },
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        // //点击计提问号
        // appUtils.bindEvent($(_pageId + " .annual_details"), function () {
        //     layerUtils.iAlert("<span style='text-align:left;display:block'>本产品业绩计提基准为" + $(_pageId + " .annual").text() + "（不构成最低收益保证）</span><span style='text-align:left;display:block'>" + productInfo.administrator_accrual + "</span>")
        // });
        //查看更多
        appUtils.bindEvent($(_pageId + " .more"), function () {
            if ($(_pageId + " .detailImgBox").height() == 200) {
                $(_pageId + " .detailImgBox").css({ height: 'auto' });
                $(_pageId + " .more").text("收起");
            } else {
                $(_pageId + " .detailImgBox").css({ height: 200 });
                tools.recordEventData('1','close_more','收起');
                $(_pageId + " .more").text("查看更多");
            }
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_page_code)
        });
     
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .header_inner #title").html("");
        headerImg = "";
        bottomImg = ""
        $(_pageId + " .thfundBtn").hide();
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        vipBenefitsTaskData = ""; startTime = "";
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && vipBenefitsTaskData.task_type == '1' && !tools.getStayTime(startTime, vipBenefitsTaskData.duration)) {
            var remainTime = tools.getRemainTime(startTime, vipBenefitsTaskData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_page_code, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
