<!--引流注册-->
<div class="page" id="drainage_openAccount" data-pageTitle="绑定银行卡" data-refresh="true">
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray" id="getBack"><span>返回</span></a>
                <h1 class="text_gray text-center">绑定银行卡</h1>
            </div>
        </header>
        <article class="bg_blue">
            <!--            <p class="tips" style="padding: 0.1rem">监测到您已在晋金所绑卡，是否使用晋金所信息开户，<span id="retryOpenAccount" class="blue">重新开户</span></p>-->
            <div class="check_tips slidedown in " style="background: none;border-bottom:none">
                <p id="userInfo" style="font-size: 0.14rem;">根据监管要求为确保资金安全需收集与银行卡一致的身份信息。</p>
            </div>
            <div class="bank_form">
                <div class="input_box">
                    <div class="ui field text">
                        <label class="ui label">姓名</label>
                        <input id="cardPerson" type="text" class="ui input" disabled
                               style="color: #666666;"/>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">身份证号</label>
                        <input id="idCard" maxlength="18" type="text"
                               class="ui input" disabled style="color: #666666;"/>
                    </div>
                    <!-- <div class="ui field text">
                        <label class="ui label">证件照片</label>
                        <div class="ui dropdown text_red uploadStatus" style="margin-left:0.1rem;"></div>
                        <div class="retryUpload ft blue"></div>
                    </div> -->
                    <div class="ui field text">
                        <div class="pop_view" id="pop_view" style="visibility:hidden;">
                            <p id="big_show_bank"></p>
                        </div>
                        <label class="ui label">银行卡号</label>
                        <input id="bankCard" maxlength="19" type="tel"
                               class="ui input" style="color: #666666;" disabled/>
                        <div class="changeBankCard">修改</div>

                    </div>

                    <div class="ui field text">
                        <label class="ui label">银行名称</label>
                        <div class="ui dropdown" id="bankname" style="margin-left:0.1rem;color: #666666;">
                        </div>
                    </div>
                    <div class="place" style="padding-left: 1rem;height: auto">
                        <span class="limit"></span>
                        <span class="bankInfo" style="color: #319ef2">支持的银行卡</span>
                    </div>
                </div>

                <div class="input_box">
                    <div class="ui field text">
                        <label class="ui label">预留手机号</label>
                        <input id="yhmPhone" maxlength="11" type="tel" placeholder="" class="ui input" disabled
                               style="color: #666666;padding-left: 0.1rem"/>
                        <div class="changeBankMobile ft blue">修改</div>

                    </div>
                    <div class="grid_03 grid_02 grid">
                        <div class="ui field text rounded input_box2" id="yzmBox">
                            <label class="short_label2 text-right"
                                   style="width:0.9rem;padding-right:0.1rem;">验证码</label>
                            <input custom_keybord="0" id="verificationCode" type="tel" maxlength="6"
                                   class="ui input code_input"
                                   placeholder=""/>
                            <a id="getYzm" data-state="true">获取验证码</a>
                        </div>
                    </div>
                    <div class="finance_det recharge_det">
                        <dl class="bank_limit">
                            <dt></dt>
                            <dd id="weihao" style="display:none"></dd>
                            <dd>
                            </dd>
                        </dl>
                    </div>
                    <!-- 语音验证码 -->
                    <div class="finance_det recharge_det">
                        <dl class="bank_limit">
                            <dt></dt>
                            <dd id="talkCode" style="display: block;">晋金财富将致电您的手机语音告知验证码
                            </dd>
                            <dd>
                            </dd>
                        </dl>
                    </div>

                    <!-- <div class="ui field text" id="inviterBox" style="display: none">
                        <label class="ui label">邀请人手机号</label>
                        <input id="inviter" maxlength="11" type="tel" placeholder="选填" class="ui input"
                               style="color: #666666;"/>
                    </div> -->
                </div>

<!--                <div class="rule_check">-->
<!--                    <span id="xuanzeqi"><i></i>我已阅读并同意签署</span> <span class="deal_box"></span>-->
<!--                    &lt;!&ndash; <a href="javascript:void(0);">《业务说明书》</a><a href="javascript:void(0);">《定向委托管理协议》</a><a href="javascript:void(0);">《资金结算协议》</a> &ndash;&gt;-->
<!--                </div>-->
                <div class="btn">
                    <a href="javascript:void(0);" class="ui button block rounded" id="bk">下一步</a>
                </div>
                <!-- 协议相关弹窗 -->
                <div class="agreement_layer display_none">
                    <div class="agreement_popup in">
                        <div class="agreement_popup_header">
                            <div class="new_close_btn"></div>
                            <h3>相关协议</h3>
                        </div>
                        <ul class="agreement_list flex vertical_line"></ul>
                    </div>
                </div>
            </div>
        </article>
    </section>
</div>
