// 晋金宝取现主界面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common");
    var _pageId = "#thfund_enchashmentHome";
    var _page_code = "thfund/enchashmentHome";
    var tools = require('../common/tools');

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        getFundAssetInfo();
        getBankAssetInfo()
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //晋金宝取现
        appUtils.bindEvent($(_pageId + " .enchashment"), function () {
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "thfund/enchashment", {});
            })
        });
        //银行电子账户取现
        appUtils.bindEvent($(_pageId + " .bankEnchashment"), function () {
            appUtils.setSStorageInfo("bankEntrytype", "enchash");
            appUtils.pageInit(_page_code, "bank/bankElectron");
        });

    }

    function getFundAssetInfo() {
        service.reqFun101999({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                $(_pageId + ' .mfundAssets').html(tools.fmoney(results.mfundAssets));
            } else {
                $(_pageId + ' .mfundAssets').html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getBankAssetInfo() {
        service.reqFun151122({}, function (data) { //查询银行总余额
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                $(_pageId + ' .bankfundAssets').html("--");
                return;
            }
            var results = data.results[0];
            $(_pageId + ' .bankfundAssets').html(tools.fmoney(results.total));

        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailCompany = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailCompany;
});
