// 住房专享 -持有详情页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "inclusive/moneytaryHousingMochikura",
        _pageId = "#inclusive_moneytaryHousingMochikura ";
    var ut = require("../common/userUtil");
    var holdObj;
    var calculator = require("../common/calculator");

    function init() {
        holdObj = appUtils.getSStorageInfo("holdObj");
        let newType = appUtils.getSStorageInfo("newType");  //是否是住房或其他
        if(newType == 'new'){
            //新版
            $(_pageId + " .newProShow").show();
            $(_pageId + " #transaction").show();
            $(_pageId + " .increase_term").text(holdObj.closed_length + '天');
            $(_pageId + " .redeem").css("width","50%");
        }else{
            $(_pageId + " .newProShow").hide();
            $(_pageId + " #transaction").show();
            $(_pageId + " .redeem").css("width","100%");
        }
        appUtils.setSStorageInfo("trsFundCode", holdObj.fund_code);

        $(_pageId + " .fund_name").text(holdObj.prod_exclusive_name || holdObj.fund_name);

        $(_pageId + " .fund_vol").text(tools.fmoney(holdObj.total_fund_vol));//资产
        $(_pageId + " .cost_money").text(tools.fmoney(holdObj.cost_money));//本金
        $(_pageId + " .hold_income").text(tools.fmoney(holdObj.hold_income)).addClass(tools.addMinusClass(holdObj.hold_income)) //收益
        $(_pageId + " .due_date").text(tools.ftime(holdObj.due_date)); //到期日

        if (parseFloat(calculator.plus(holdObj.fund_in_way_vol, holdObj.fund_out_way_vol)) == parseFloat(holdObj.total_fund_vol)) { //纯在途
            $(_pageId + " #sell").addClass("no_active");
        } else {
        	if(holdObj.frozen_flag == "0"){//未冻结
                $(_pageId + " #sell").removeClass("no_active");
        	}else{
        		$(_pageId + " #sell").addClass("no_active");
        	}

        }
        if (holdObj.fund_in_way_vol > 0) { // 买入在途
            $(_pageId + " #fund_way_vol_box").show();
            $(_pageId + " #fund_way_vol").html(tools.fmoney(holdObj.fund_in_way_vol));
        }
        if (holdObj.fund_out_way_vol > 0) { //卖出在途
            $(_pageId + " #fund_out_way_vol_box").show();
            $(_pageId + " #fund_out_way_vol").html(tools.fmoney(holdObj.fund_out_way_vol));
        }

    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

        //产品详情
        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            appUtils.setSStorageInfo("productInfo", holdObj);
            appUtils.setSStorageInfo("fund_code", holdObj.fund_code);
            if(holdObj.exclusive_product_type == '05'){
                sessionStorage.vip_buttonShow = false;
                return appUtils.pageInit(_pageCode, "template/publicOfferingDetail");
            }
            appUtils.pageInit(_pageCode, "inclusive/moneytaryDetail");
        });

        //交易记录
        appUtils.bindEvent($(_pageId + " #transaction"), function () {
            appUtils.setSStorageInfo("series_id",'');
            appUtils.pageInit(_pageCode, "template/transaction");
        });

        //卖出
        appUtils.bindEvent($(_pageId + " #sell"), function () {
            if ($(this).hasClass("no_active")) return;
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageCode)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            if (holdObj.available_vol && holdObj.available_vol == 0) {
                layerUtils.iConfirm("已提交赎回申请，请勿重复卖出", function () {
                }, function () {
                    appUtils.setSStorageInfo("series_id",'');
                    appUtils.pageInit(_pageCode, "template/transaction");
                }, "确定", "查看交易记录");
                return;
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "inclusive/moneytaryHousingSale");
            });
        });

    }

    function destroy() {
        $(_pageId + " .data-line").html("--");
        $(_pageId + " .empty").html("");
        $(_pageId + " .hold_income").removeClass("text_red").removeClass("text_green").removeClass("text_gray");//收益
        $(_pageId + " #fund_way_vol_box").hide();
        $(_pageId + " #fund_out_way_vol_box").hide();
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.clearSStorage("holdObj");
        appUtils.pageBack();
    }

    var inclusiveDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
// 暴露对外的接口
    module.exports = inclusiveDetail;
})
;
