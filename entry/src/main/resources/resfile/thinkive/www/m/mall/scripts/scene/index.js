//攒钱计划
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools.js"),
        validatorUtil = require("validatorUtil");
    var _pageId = "#scene_index ";
    var _page_code = "scene/index"
    var layerUtils = require("layerUtils");
    var global = gconfig.global;
    var ut = require("../common/userUtil.js");
    var video_id,videoInfo;
    var source;
    /**
     * 初始化
     */
    async function init() {
        source = '3';
        //页面埋点初始化
        tools.initPagePointData();
        //根据模板ID获取模板
        let templateId = await getTemplateId({});
        let listHtml = await getTemplate({templateId:templateId});
        $(_pageId + ' .snowballList').html(listHtml);
        video_id = $(_pageId + " .walletTitle").attr("video_id");
        //获取视频信息
        getVideoInfo(video_id)
    };
    //获取场景首页模板ID
    async function getTemplateId(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102214(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results
                let matchedItems = res.filter(item => item.scene_code === '2');
                let template_id = matchedItems[0].template_id
                resolve(template_id ? template_id : "");
            })
        })
    }
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    //去测评
    function pageTo_evaluation() {
        let operationId = "riskAssessment"
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }
    //获取视频信息
    async function getVideoInfo(video_id) {
        service.reqFun102222({video_id:video_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let res = datas.results[0]
            videoInfo = res;
            // console.log(res,111)background: url(./images/index_poster.png) center center no-repeat;
            //渲染视频封面
            // $(_pageId + " .videoShow").attr("video_id");
            // cover_path = global.oss_url + res.cover_path;
            // $(_pageId + " .cover_path").css({
            //     "background": `url(${global.oss_url + res.cover_path}) center center no-repeat`,
            //     "background-size": "cover"
            // })
        })
    }
    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //点击播放
        appUtils.bindEvent($(_pageId + " #accompany"), function () {
            // tools.recordEventData('1','accompany','点击播放按钮');
            let html = `<video id="userIndex_new_example_video" style="width:100%;height:100%" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
            webkit-playsinline="true" autoplay playsinline="true" height="100%" controls preload="auto" poster=""
            data-setup="{}">
            </video>`
            $(_pageId + " #new_example_div").html(html);
            //初始化视频
            high_player = videojs('userIndex_new_example_video', {
            }, function onPlayerReady() {
                //结束和暂时时清除定时器，并向后台发送数据
                this.on('ended', function () {
                    // window.clearInterval(time1);
                });
                this.on('pause', function () {
                    // window.clearInterval(time1);
                });
                this.on('waiting', function () {
                    // window.clearInterval(time1);
                })
            });
            high_player.reset();
            high_player.src({ src: global.video_oss_url + videoInfo.video_path, type: 'video/mp4' })
            high_player.load(global.video_oss_url + videoInfo.video_path)
            $(_pageId + " video").attr("poster", global.oss_url + videoInfo.cover_path)
            $(_pageId + " #showVideo").show()
        });
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            // tools.recordEventData('1','close_video','关闭视频弹窗');
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        });
        //返回首页
        appUtils.bindEvent($(_pageId + " #icon_back"), () => {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            // tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_pageCode)
        });
        //跳转积分奖励
        appUtils.bindEvent($(_pageId + " #reward"), function () {
            tools.recordEventData('1','gasStation','攒钱加油站');
            if (!common.loginInter()) return;
            appUtils.pageInit(_page_code, "scene/gasStation");
        });
        //跳转邀请好友
        appUtils.bindEvent($(_pageId + " #invitation"), function () {
            tools.recordEventData('1','invitation','邀请好友');
            if (!common.loginInter()) return;
            if (!ut.getUserInf().bankAcct || ut.getUserInf().bankAcct == '') return layerUtils.iAlert("仅注册客户暂未开通");
            appUtils.pageInit(_page_code, "vipBenefits/friendInvitation",{source:source});
        });
        //跳转攒钱策略
        appUtils.bindEvent($(_pageId + " #strategy"), function () {
            tools.recordEventData('1','strategy','攒钱策略');
            if (!common.loginInter()) return;
            appUtils.setSStorageInfo("productType", '07') //存储类型
            appUtils.pageInit(_page_code, "scene/listMorePage", {});
        });
        //跳转开启定投页面
        appUtils.preBindEvent($(_pageId + " .snowballList"), ".walletCard_product",  function(e) {
            let that_ = $(this);    
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            
            //到期3个月后提示
            if (perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    // tools.recordEventData('1','updateIDCard','更新身份照片');
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            common.changeCardInter( function() {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    let operationId = 'riskAssessment'
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        // tools.recordEventData('1','riskQuestion','风险测评');
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                //重置开启计划持仓
                appUtils.setSStorageInfo("pageInputCasting", {});
                let sceneInfo = {
                    plan_type:that_.attr("plan_type"),
                    plan_id:that_.attr("plan_id"),
                    hold_id:that_.attr("hold_id"),
                    pageTitle:that_.attr("pageTitle"),
                    bgColor:that_.attr("bgColor"),
                    select_id:that_.attr("select_id"),
                }
                appUtils.setSStorageInfo("sceneInfo",sceneInfo);
                service.reqFun102212({plan_type: sceneInfo.plan_type}, async (data) => {
                    if (data.error_no == '0') {
                        let list = data.results;
                        if(list && list.length){
                            //跳转持仓
                            tools.recordEventData('1','snowball_' + that_.attr("plan_type"),'攒钱计划持仓');
                            appUtils.pageInit(_page_code, "scene/sceneHold");
                        }else{
                            //跳转开启
                            tools.recordEventData('1','snowball_' + that_.attr("plan_type"),'开启攒钱计划');
                            appUtils.setSStorageInfo("userChooseAnswer",'');
                            appUtils.pageInit(_page_code, "scene/selectProduct");
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                })
            });
        });
        // 理财计算器按钮点击事件
        appUtils.preBindEvent($(_pageId), ".savingsCalculator", function () {
            tools.recordEventData('1','savingsCalculator','跳转到理财计算器页面');
            appUtils.setSStorageInfo("calculator_productType", "5");
            appUtils.pageInit(_page_code, "scene/financialCalculator");
        }, 'click');
    }

    function pageBack() {
        if($(_pageId + " #showVideo").is(":visible")){
            high_player.pause();
            setTimeout(function() {
                high_player.dispose();
                high_player = '';
            }, 0);
            return $(_pageId + " #showVideo").hide();
        }
        $(_pageId + " #showVideo").hide();
        $(_pageId + " .loginDig").hide();
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #showVideo").hide();
        tools.recordEventData('4','destroy','页面销毁');
    };
    var scene_index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = scene_index;
});
