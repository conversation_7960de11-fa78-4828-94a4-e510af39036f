/**
 * 模块名：首页（导航页） 作者： 刘珂 时间：2015年5月13日13:08:49 简述：首页
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        ut = require("../common/userUtil");
    /* 常量 */
    var _pageCode = "redPack/luckDraw", _pageId = "#redPack_luckDraw";
    /* 变量 */
    var activity_type = "";
    var reward_num;
    var userInfo;
    var luck;
    var demo = "";
    var child_span;
    var t;
    /**
     * 初始化
     */
    function init() {
        reward_num = 1;
        userInfo = ut.getUserInf();
        activity_type = appUtils.getPageParam("activitiesType") || "10";
        $("#activityRules").hide();
        $("#myRewards").hide();
        $("#rewardSuccess").hide();
        $("#rewardFailed").hide();
        // 初始化转盘
        luck = {
            index: 0, // 当前转动到哪个位置，起点位置
            count: 0, // 总共有多少个位置
            timer: 0, // setTimeout的ID，用clearTimeout清除
            speed: 20, // 初始转动速度
            times: 0, // 转动次数
            cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
            prize: -1, // 中奖位置
            picH: 20,
            tmpH: 0,
            scrollstep: 3,//移动步幅,越大越快
            scrolltime: 50,//移动频度(毫秒)越大越慢
            stoptime: 2500,//间断时间(毫秒)
            init: function (id) {
                if ($("#" + id).find(".luck-unit").length > 0) {
                    $luck = $("#" + id);
                    $units = $luck.find(".luck-unit");
                    this.obj = $luck;
                    this.count = $units.length;
                    $luck.find(".luck-unit-" + this.index).addClass("active");
                }
            },
            roll: function () {
                var index = this.index;
                var count = this.count;
                var luck = this.obj;
                $(luck).find(".luck-unit-" + index).removeClass("active");
                index += 1;
                if (index > count - 1) {
                    index = 0;
                }
                $(luck).find(".luck-unit-" + index).addClass("active");
                this.index = index;
                return false;
            },
            stop: function (index) {
                this.prize = index;
                return false;
            }
        };


        luck.init('luck');
        // roller

        t=setTimeout(start, luck.stoptime);
        /* 获取获奖人信息*/
        service.queryReward50({reward_activity_type: "12"}, function (data) {
            var error_no = data.error_no;
            var error_info = data.error_info;
            var results = data.results;
            if (error_no == "0" && results.length > 0) {
                var html = "";
                for (var int = 0; int < results.length; int++) {
                    var money = results[int].money;
                    var mobile_no = results[int].mobileno;
                    if (money == 3800) {
                        html += "<span class='notice' >客户" + mobile_no + "抽中了华为手机P30</span>";
                    } else if (money == 200) {
                        html += "<span class='notice' >客户" + mobile_no + "抽中了小米手环</span>";
                    } else if (money == 100) {
                        html += "<span class='notice' >客户" + mobile_no + "抽中了高级雨伞</span>";
                    } else {
                        html += "<span class='notice' >客户" + mobile_no + "抽中了红包奖励 " + money + " 元</span>";
                    }
                }
                $("#demo").html(html);
                return;
            }
        }, {"isShowWait": false, "isLastReq": false});
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
    	appUtils.bindEvent($(_pageId + " #getBack"),function(){
    		pageBack();
    	})
        appUtils.bindEvent($(_pageId + " #closePopLayer"), function () {
            $("#activityRules").hide();
            $("#rulesDiv").hide();
        }, "click");
        appUtils.bindEvent($(_pageId + " #closePopLayer1"), function () {
            $("#myRewards").hide();
            $("#rewardDiv").hide();
        }, "click");
        /*查询我的奖品信息*/
        $("#clickMyRewards").on("click", function (e) {
        	$("#my_reward_info").html("");
            var QueryParam = {
                "reward_activity_type": "12",
                "is_order": "1",
                "is_show": "2"
            };
            service.queryUserReward(QueryParam, function (data) {
                var error_no = data.error_no;
                var error_info = data.error_info;
                var results = data.results;
                if (error_no == "0" && results.length > 0) {
                    var html = "";
                    var money = "";
                    var date = "";
                    var no;
                    for (var i = 0; i < results.length; i++) {
                        money = results[i].money;
                        date = results[i].create_date.substring(5, 10);
                        no = i + 1;
                        if (money == 3800) {
                            html += "<p>" + no + "." + date + " 获得华为手机P30</p>";
                        } else if (money == 200) {
                            html += "<p>" + no + "." + date + " 获得小米手环</p>";
                        } else if (money == 100) {
                            html += "<p>" + no + "." + date + " 获得高级雨伞</p>";
                        } else {
                            if (money == 0) {
                                html += "<p>" + no + "." + date + " 谢谢参与 </p>";
                            } else {
                                html += "<p>" + no + "." + date + " 获得红包奖励 " + money + " 元</p>";
                            }
                        }
                    }
                    $("#my_reward_info").html(html);
                    return;
                } else {
                    return;
                }
            }, {"isShowWait": false, "isLastReq": false});

            $("#myRewards").show();
            $("#rewardDiv").show();
            $(document).one("click", function () {
                $("#rewardDiv").hide();
                $("#myRewards").css("display", "none");
            });
            e.stopPropagation();
        });
        $("#rewardDiv").on("click", function (e) {
            e.stopPropagation();
        });

        $("#clickActivityRules").on("click", function (e) {
            $("#activityRules").show();
            $("#rulesDiv").show();
            $(document).one("click", function () {
                $("#rulesDiv").hide();
                $("#activityRules").css("display", "none");
            });
            e.stopPropagation();
        });
        $("#rulesDiv").on("click", function (e) {
            e.stopPropagation();
        });
        $(".bt_ok").on("click", function (e) {
            $("#rewardSuccess").hide();
            $("#rewardFailed").hide();
            e.stopPropagation();
        });
        $("#reward_ok").on("click", function (e) {
            $("#rewardSuccess").hide();
            e.stopPropagation();
        });
        $("#rewardfaid").on("click", function (e) {
            $("#rewardFailed").hide();
            e.stopPropagation();
        });
        /*抽奖*/
        appUtils.bindEvent($(_pageId + " #start"), function () {
            if (userInfo.cust_no == undefined || userInfo.cust_no == null || userInfo.cust_no == "null" || userInfo.cust_no == "") {
                appUtils.pageInit(_pageCode, "login/unLoginIndexPage");
                return;
            } else {
                // 按钮禁用
                if(reward_num == 0) {
                    layerUtils.iMsg(-1, "抽奖次数已用完", 2);
                    return;
                }
                $("#start").addClass("notclick");
                $("#start").html("<img src='images/three_years/p_start_end.png'>");
                luck.speed = 100;
                var param = {
                    "activity_type": activity_type,
                    "mobile": userInfo.mobile,
                    "reward_activity_type": "12",
                };
                service.reqFun9089030(param, function (data) {
                    var error_no = data.error_no;
                    var error_info = data.error_info;
                    var results = data.results;
                    if (error_no == "0") {
                        reward_num = 0;
                        if (results[0].money == 3800) {
                            index = 1;
                            $("#index_info").html("华为P30手机");
                        } else if (results[0].money == 200) {
                            var rand = [3, 7];
                            index = rand[Math.floor((Math.random() * rand.length))];
                            $("#index_info").html("小米手环");
                        } else if (results[0].money == 100) {
                            index = 5;
                            $("#index_info").html("高级雨伞");
                        } else if (results[0].money == 0) {
                            index = 4;
                            $("#index_info").html("谢谢参与");
                        } else {
                            var rand = [0, 2, 6];
                            index = rand[Math.floor((Math.random() * rand.length))];
                            $("#index_info").html("红包奖励 " + results[0].money + " 元");
                        }
                        roll();
                        return false;
                    } else if (error_no == "-9089003") {
                        index = 0;
                        layerUtils.iMsg(-1, error_info, 2);
                    } else if (error_no == "-90890031") {
                        index = 0;
                        layerUtils.iMsg(-1, error_info, 2);
                    } else if (error_no == "-908900302") {
                        index = 0;
                        layerUtils.iMsg(-1, error_info, 2);
                    } else {
                        reward_num = 0;
                        index = 4;
                        $("#index_info").html("谢谢参与");
                        roll();
                        return false;
                    }
                }, {"isShowWait": false, "isLastReq": false});
            }
        }, "click");


    }
    /* 所有用户奖励轮播效果 */
    function start() {
        demo = document.querySelector("#demo");
        child_span = demo.getElementsByTagName("span");
        if (luck.tmpH < luck.picH) {
            luck.tmpH += luck.scrollstep;
            if (luck.tmpH > luck.picH)
                luck.tmpH = luck.picH;
            demo.scrollTop = luck.tmpH;
            t=setTimeout(start, luck.scrolltime);
        } else {
            luck.tmpH = 0;
            child_span.length >0 && demo.appendChild(child_span[0]);
            demo.scrollTop = 0;
            t=setTimeout(start, luck.stoptime);
        }
    }
    function roll() {
        luck.times += 1;
        luck.roll();
        if (luck.times > luck.cycle + 10 && luck.prize == luck.index) {
            clearTimeout(luck.timer);
            luck.prize = -1;
            luck.times = 0;
            if (index !== 4) {
                setTimeout(function () {
                    $("#rewardSuccess").show();
                }, 600);
            } else {
                setTimeout(function () {
                    $("#rewardFailed").show();
                }, 600);
            }
            $("#start").removeClass("notclick");
            $("#start").html("<img src='images/three_years/p_start.png'>");
        } else {
            if (luck.times < luck.cycle) {
                luck.speed -= 10;
            } else if (luck.times == luck.cycle) {
                luck.prize = index;
            } else {
                if (luck.times > luck.cycle + 10 && ((luck.prize == 0 && luck.index == 7) || luck.prize == luck.index + 1)) {
                    luck.speed += 110;
                } else {
                    luck.speed += 20;
                }
            }
            if (luck.speed < 40) {
                luck.speed = 40;
            }
            luck.timer = setTimeout(roll, luck.speed);
        }
        return false;
    }
    
    function pageBack(){
    	appUtils.pageInit(_pageCode, "numberBenefits/benefitsList",{});
    }
    /**
     * 销毁
     */
    function destroy() {
        service.destroy();
        clearTimeout(t);
        $("#my_reward_info").html("");
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack":pageBack
    };
    module.exports = index;


});