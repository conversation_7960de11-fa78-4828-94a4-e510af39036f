define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        gconfig = require("gconfig"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _page_code = "fundSupermarket/index",
        _pageId = "#fundSupermarket_index ";
    var fund_market_type = ''; //当前所选tab
    var zone_prod_type = '';
    let tabList = [];
    var ut = require("../common/userUtil");
    require('../common/echarts.min');
    require('../common/echartsData.js')
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    VIscroll = require("vIscroll");
    vIscroll = {"scroll": null, "_init": false};
    var isEnd = false;
    var cur_page;
    var global = gconfig.global;
    function init() {
        cur_page = 1;
        //页面埋点初始化
        tools.initPagePointData();
        //渲染轮播图
        // tools.guanggao({ _pageId: _pageId, group_id: "48" });
        //渲染列表
        getTitleList();
        
    }
    //获取页面titleList
    function getTitleList(){
        let param = {code: 'fund_market_type'}
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                fund_market_type = dataArr[0].id;
                let titleList = ``; //顶部title 集合
                if(dataArr && dataArr.length){
                    dataArr.map((item,index)=>{
                        titleList += `<li operationType="1" operationId="active_${item.id}" operationName="${item.value}" class="active_${item.id} ${index == 0 ? 'titleActive' : ''}" data-id="${item.id}">
                            <span class="${index == 0 ? 'active' : ''}"></span>
                            ${item.value}
                        </li>`
                    })
                    $(_pageId + ' #tab').html(titleList);
                    let params = appUtils.getPageParam() ? appUtils.getPageParam() : {};
                    if (params && params.pageInfo) {
                        fund_market_type = params.pageInfo;
                        let active_choose = 'active_' + fund_market_type
                        $(_pageId + ' .tabs li span').removeClass('active')
                        $(_pageId + ' .tabs .' + active_choose + ' span').addClass('active');
                    }
                    if(appUtils.getSStorageInfo("fund_market_type") && appUtils.getSStorageInfo("fund_market_type").length){
                        fund_market_type = appUtils.getSStorageInfo("fund_market_type"); //标记当前TAB
                        let active_choose = 'active_' + fund_market_type;
                        $(_pageId + ' .tabs li span').removeClass('active');
                        $(_pageId + ' .tabs li').removeClass('titleActive');
                        $(_pageId + ' .tabs .' + active_choose + ' span').addClass('active');
                        $(_pageId + ' .tabs .' + active_choose).addClass('titleActive');
                        appUtils.setSStorageInfo("fund_market_type","")
                    }
                    $(_pageId + ' .tabs').show();
                    $(_pageId + ' .tabs').scrollLeft(0);
                }
                
                initData();
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })
    }
    //渲染二级列表
    async function initData(isTop) {
        let fund_market_zone_prod_type = appUtils.getSStorageInfo("fund_market_zone_prod_type") ? appUtils.getSStorageInfo("fund_market_zone_prod_type") : '';
        zone_prod_type = fund_market_zone_prod_type;
        $(_pageId + ' .main_02 .list').html('')
        let html = ``;//主模板
        // let defaultHtml = `
        //     <li class="${fund_market_zone_prod_type ? '' : 'secondActive'}">
        //         全部
        //     </li>
        // `
        let res = await getSecondTitle(fund_market_type)
        let secondArr = res     //二级数组集合

        //渲染列表
        let data = {
            fund_market_type:fund_market_type,
            zone_prod_type:zone_prod_type,
            current:cur_page
        }
        // console.log(data,111)
        
        if(secondArr && secondArr.length && secondArr.length > 1){
            secondArr.map(item => {
                html += `
                    <li operationType="1" operationId="second_${item.zone_prod_type}" operationName="${item.zone_name}" zone_prod_type="${item.zone_prod_type}" class="${(fund_market_zone_prod_type == item.zone_prod_type) ? 'secondActive' : ''} ${'second_' + item.zone_prod_type}">
                        ${item.zone_name}
                    </li>
                `
            })
            // html = defaultHtml + html;
            $(_pageId + ' .secondRow').html(html);
            $(_pageId + ' .secondRow').show();
            $(_pageId + ' #v_container_productList').addClass('haveScend');
            if(!data.zone_prod_type && !data.zone_prod_type.length){
                data.zone_prod_type = secondArr[0].zone_prod_type;
                zone_prod_type = secondArr[0].zone_prod_type;
                // $(_pageId + " .secondRow")[0].addClass('secondActive')
                //渲染secondRow下的第一个li 新增类名 secondActive
                $(_pageId + " .secondRow li").eq(0).addClass('secondActive');
            } 
            getSecondData(data,'haveScend',false,isTop)
        }else{
            $(_pageId + ' #v_container_productList').removeClass('haveScend');
            $(_pageId + ' .secondRow').hide();
            getSecondData(data,'',false,isTop)
        }
        
        
    }
    //获取二级列表
    async function getSecondData(params,className,flag,isTop){
        service.reqFun102223(params, async (datas) => {
            if (datas.error_no == 0) {
                var totalPages = datas.results[0].totalPages; //总页数
                let html = ``;
                let childrenListHtml = ``;
                let results = datas.results[0];
                let arr = results.data ? results.data : [];
                childrenListHtml = await getSeoundCard(arr);
                // html += `
                //     <div class="list classificationList_card ${className}">
                //         ${childrenListHtml}
                //     </div>
                // `
                html += `
                    ${childrenListHtml}
                `
                if (totalPages == cur_page) {
                    isEnd = true;
                    html += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && arr.length == 0) {
                    isEnd = true;
                    html = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                if (flag) {
                    $(_pageId + " .main_02 .list").append(html);
                } else {
                    $(_pageId + " .main_02 .list").html(html);
                }
                // $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit(isTop);
                // $(_pageId + " .visc_scroller")[0].style.transform = 'translate(0px, -40px) scale(1) translateZ(0px)';
                // $(_pageId + ' .main_02').html(html);
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit(isTop) {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    endTime = "";
                    isEnd = false;
                    appUtils.setSStorageInfo("second_catalog_id",'')
                    let param = {
                        fund_market_type:fund_market_type,
                        zone_prod_type:zone_prod_type ? zone_prod_type : '',
                        current:cur_page
                    }
                    getSecondData(param,'',false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        let param = {
                            fund_market_type:fund_market_type,
                            zone_prod_type:zone_prod_type ? zone_prod_type : '',
                            current:cur_page
                        }
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getSecondData(param,'',true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
            // if(isTop)$(_pageId + " .visc_scroller")[0].style.transform = 'translate(0px, -40px) scale(1) translateZ(0px)';
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
            if(isTop){
                setTimeout(function () {
                    vIscroll.scroll.destroy(); 
                    vIscroll._init = null;
                    pageScrollInit()
                });
            }
        }
    }
    //获取二级页面列表card
    async function getSeoundCard(arr) {
        let html = ''
        arr.map(item => {
            if (item.prod_sub_type2 == '200') { //只渲染公募
                let financial_prod_type = item.financial_prod_type;
                let productInfoSon = JSON.stringify(item) //二级列表接口传递数据
                let prod_per_min_amt = item.prod_per_min_amt //投资金额
                let prod_sname = item.prod_name_list ? item.prod_name_list : item.prod_sname ? item.prod_sname : item.prod_exclusive_name
                let this_year_rate = tools.fmoney(item.this_year_rate ? item.this_year_rate : item.annu_yield)
                let transferable = item.transferable;//是否可转让
                let recommend_info = item.recommend_info;
                let recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
                let str = "";

                let compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                let nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                let per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                let threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                let closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                let recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                let lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                let recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var day_purchase_limit_show = item.day_purchase_limit_show == '1' ? '' : 'display_none';
                var decrease_max_show = item.decrease_max_show == '1' ? '' : 'display_none';//是否展示最大回撤
                var differ_date_show = item.differ_date_show == '1' ? '' : 'display_none';//是否展示成立时间
                //数据展示
                let preincomerate = item.preincomerate ? tools.fmoney(item.preincomerate) : '--' //年化标准
                let threshold_amount = item.threshold_amount ? item.threshold_amount : '--' //起购金额
                let inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                let income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                let dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                let nav = item.nav ? tools.fmoney(item.nav, 4) : '--'
                let holding_days = item.holding_days ? item.holding_days : '--'
                let per_yield = item.per_yield ? tools.fmoney(item.per_yield) : '--'
                var risklevel_name = item.risklevel_name.split('(') ? item.risklevel_name.split("(")[0] : '--';
                var td_sum_max_amt = item.td_sum_max_amt ? item.td_sum_max_amt : '--';
                var differ_date = item.differ_date ? item.differ_date : '--';
                var decrease_max_data = item.decrease_max_data ? tools.fmoney(item.decrease_max_data) : '--';
                var decrease_max_desc = item.decrease_max_desc ? item.decrease_max_desc : '';
                let buy_state = item.buy_state
                let buy_state_name, btnClass
                //埋点所需fundcode
                let fund_code = item.fund_code ? item.fund_code : '';
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = "敬请期待";
                    btnClass = "";
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                let threeStyle = 'align-items: flex-end'
                //公募列表展示
                html += `
                    <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fund_code="${fund_code}">
                        <li class="main_flxe vertical_line">
                            <em style='display: none' class='productInfo'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000">${prod_sname}</p>
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化<span class="${differ_date_show}">(${differ_date})</span>:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>
                            <p style="padding-bottom:0.02rem" class="m_font_size12 m_text_999 ${decrease_max_show}">${decrease_max_desc}最大回撤:${decrease_max_data}%</p>
                            <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                            <p class="m_text_999 m_font_size12">
                                
                                <span class="${closed_period_list}">期限:${inrest_term}</span>
                                <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                                <span class="${recommended_holding_list}">建议持有${holding_days}天以上</span>
                                <span><span style="padding:0;">${risklevel_name}</span> | <span class="${day_purchase_limit_show}">单日购买上限:${td_sum_max_amt}元</span><span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                            </p>
                            ${fund_market_type == '02' ? `<p class="m_font_size12 m_golden  ${recommend_info_list}">${recommend_info}</p>` : ''}
                            ${fund_market_type != '02' ? `<p class="m_font_size12 m_golden ${recommend_info_list}">${recommend_info}</p>` : ''}
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${btnClass}">
                                ${buy_state_name}
                            </span>
                        </li>
                    </ul>
                `
            }
        })
        return html;
    }
    //拿到数据
    async function getSecondTitle(fund_market_type) {
        let params = {
            fund_market_type: fund_market_type
        }
        return new Promise(async (resolve, reject) => {
            service.reqFun102133(params, (datas) => {
                if (datas.error_no == 0) {
                    let results = datas.results;
                    resolve(results)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            })
        })
    }
          //多基金购买页
    function pageTo_fundsBuy() {
        tools.setPageToUrl('fundSupermarket/fundsBuy','1','fundSupermarket/index');
	    if (!common.loginInter()) return;
	    if (!ut.hasBindCard(_page_code)) return;
        //校验用户是否上传过身份证照片
        if(!ut.getUploadStatus()){
            let operationId = 'noUploadIdCard'
            appUtils.setSStorageInfo("noUploadIdCard", '1');
            return layerUtils.iConfirm("您还未上传身份证照片", function () {
                appUtils.pageInit(_page_code, "account/uploadIDCard", {});
            }, function () {
            }, "去上传", "取消",operationId);
        }
		var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
		var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
		//到期3个月后提示
		if (perfect_info == 4) {
			return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
				appUtils.pageInit(_page_code, "account/uploadIDCard", {});
			}, "取消", "更换");
		}
		common.changeCardInter(function () {
			if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
				 layerUtils.iConfirm("您还未进行风险测评", function () {
				      appUtils.pageInit(_page_code, "safety/riskQuestion", {});
				 }, function () {
			   }, "去测评", "取消");
				return;
			} else if (invalidFlag == '1') {
				pageTo_evaluation()
				return
			}              
			appUtils.pageInit(_page_code, "fundSupermarket/fundsBuy");
		});
    }
    //点击事件
    function bindPageEvent() {
        //跳转详情
        appUtils.preBindEvent($(_pageId + "main"), ".classificationList_card_main", function () {
            let productInfo = JSON.parse($(this).find("em").text())
            appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
            appUtils.setSStorageInfo("fund_market_type", fund_market_type); //标记当前TAB
            appUtils.setSStorageInfo("fund_market_zone_prod_type", zone_prod_type); //标记当前 二级 TAB
            tools.pageTo200(productInfo, _page_code)
            tools.recordEventData('1','classificationList_card_main_' + productInfo.fund_code,'产品详情',{fundCode:productInfo.fund_code});
        }, 'click');
        //点击二级菜单
        appUtils.preBindEvent($(_pageId + " .secondRow"), "li", function (e) {
            zone_prod_type = $(this).attr('zone_prod_type') ? $(this).attr('zone_prod_type') : '';
            $(_pageId + " .secondRow li").removeClass("secondActive");
            $(this).addClass("secondActive");
            isEnd = false;
            cur_page = 1;
            let data = {
                zone_prod_type:zone_prod_type,
                fund_market_type:fund_market_type,
                current:cur_page
            }
            // $(_pageId + " .visc_scroller").css("transform", "translate(0px, -40px) scale(1) translateZ(0px);")
            getSecondData(data,'','',true);
        }, 'click');
        //收起
        appUtils.preBindEvent($(_pageId + " .main_01"), ".echarts_1 .pack_up", function () {
            $(this).parent().find(".echarts_content").addClass("couplet");
            $(this).parent().find(".pack_down").show()
            $(this).parent().find(".pack_up").hide()

        }, 'click');

        //展开
        appUtils.preBindEvent($(_pageId + " .main_01"), ".echarts_1 .pack_down", function () {
            // console.log($(this).parent());
            $(this).parent().find(".echarts_content").removeClass("couplet");
            $(this).parent().find(".pack_down").hide()
            $(this).parent().find(".pack_up").show()

        }, 'click');

        //返回页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 点击tab
        appUtils.preBindEvent($(_pageId + "  #tab"), "li", function () {
            $(_pageId + " #tab li").removeClass('titleActive');
            $(_pageId + " #tab li").each(function () {
                $(this).find("span").removeClass("active")
            })
            $(this).find("span").addClass("active");
            $(this).addClass("titleActive");
            fund_market_type = $(this).attr("data-id");  //当前tab标识
            appUtils.setSStorageInfo("fund_market_zone_prod_type", '');
            zone_prod_type = '';
            isEnd = false;
            cur_page = 1;
            initData(true);
            // 滚动逻辑
            var $li = $(this);
            var $container = $li.closest('.tabs');

            if ($container.length) {
                // 1. 获取容器实际宽度（含滚动条）
                var containerWidth = $container[0].offsetWidth; // 优先使用 offsetWidth
                // 或使用 getBoundingClientRect（需处理小数）
                // var containerWidth = Math.round($container[0].getBoundingClientRect().width);

                // 2. 获取元素实际位置（考虑当前滚动位置）
                var liOffset = $li[0].offsetLeft; // 元素左侧到容器左侧的距离（含滚动）
                var liWidth = $li[0].offsetWidth; // 元素实际宽度（含 padding 和 border）

                // 3. 计算目标滚动位置
                var targetScroll = liOffset + liWidth / 2 - containerWidth / 2;

                // 4. 边界保护（避免滚动越界）
                var maxScroll = $container[0].scrollWidth - containerWidth;
                targetScroll = Math.max(0, Math.min(targetScroll, maxScroll));

                // 5. 执行滚动动画
                $container.animate(
                    { scrollLeft: targetScroll },
                    { duration: 300, easing: 'swing' }
                );
            }
            // $(_pageId + " .visc_scroller")[0].style.transform = 'translate(0px, -40px) scale(1) translateZ(0px)';
        }, 'click');
        // 多基金买入
        appUtils.bindEvent($(_pageId + " .buy"), function () {
        	 service.reqFun102148({ }, function (data) {
                	if (data.error_no == 0) {
                    	if(data.results !=''){
                    		appUtils.pageInit(_page_code, "fundSupermarket/fundsMarketing");
                    	}else{
                    		pageTo_fundsBuy()
                    	}
                	}else{
                		pageTo_fundsBuy()
                	}
                })
          

        });

        //”中证500“提示
        appUtils.preBindEvent($(_pageId + "main"), ".zone_name", function () {
            let info = JSON.parse($(this).find(".info").text())
            // console.log(info)
            tools.recordEventData('1','zone_name','中证500提示');
            if(info.zone_desc !="" && info.is_show_title == "1"){
                layerUtils.iAlert(info.zone_desc)
            }

        }, 'click');
        //”低估正常高估“提示
        appUtils.preBindEvent($(_pageId + "main"), ".marketing_tip", function () {
            tools.recordEventData('1','marketing_tip','低估正常高估提示');
            let info = JSON.parse($(this).find(".info").text());
            let con ="";

            if(info.pe_evaluation !=""){
                layerUtils.iAlert( common.indexTips(info.pe_evaluation,info.zone_name,info.order_per));
            }

        }, 'click');
    }
    //去测评
    function pageTo_evaluation() {
        let operationId = 'riskAssessment'
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }

    function destroy() {
        $(_pageId + ' #v_container_productList').removeClass('haveScend');
        isEnd = false;
        cur_page = 1;
        // zone_prod_type = '';
        $(_pageId + ' .tabs').hide();
        $(_pageId + ' .secondRow').hide()
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + ' .main_02 .list').html('');
        $(_pageId + ' .tabs .' + 'active_' + fund_market_type + ' span').removeClass('active');
        //默认隐藏所有tab
        // $(_pageId + " main").hide();
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }
    var thfundList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thfundList;
});
