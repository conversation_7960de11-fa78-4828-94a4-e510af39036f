//充值结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        serviceConstants = require("constants"),
        service = require("mobileService"),
        common = require("common"),
        _pageUrl = "thfund/inputResult",
        _pageId = "#thfund_inputResult ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var tools = require("../common/tools");//升级
    var outputInfo;
    var num;
    var t;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //清除页面残留
        clearPageData();
        tools.getActivityInfo(_pageId,_pageUrl)
        showTime();//显示充值受理/收益到账等时间
        num = 5;
        outputInfo = appUtils.getPageParam();
        countDown();
        //
        // /*分享获取奖励*/
        // var shareActive = require("../common/shareActive");
        // shareActive.shareCreated(3, _pageId);

    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + "#goCompleted"), function () {
            //判断是否存在类名 bg_ccc
            let bg_ccc = $(_pageId + "#goCompleted").hasClass("bg_ccc");
            if(bg_ccc) return;
        	var routerList = appUtils.getSStorageInfo("routerList");
        	if(routerList.length == "3") {
        		appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
        		appUtils.pageInit(_pageUrl, "thfund/myProfit");
        	} else {
        		routerList.splice(-1);
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.pageBack();
        	}
        	 
        })
        //跳转我的晋金宝
       /* appUtils.bindEvent($(_pageId + "#myJJB"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-2);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit(_pageUrl, "thfund/myProfit");
        })*/
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            if(routerList.length == "3") {
                appUtils.setSStorageInfo("routerList", ["login/userIndexs", "thfund/myProfit"]);
                appUtils.pageInit(_pageUrl, "thfund/JJBtransaction");
            } else {
                routerList.splice(-2);
                appUtils.setSStorageInfo("routerList", routerList);
                appUtils.pageInit(_pageUrl, "thfund/JJBtransaction");
            }

        })
        //跳转至合格投资人认证
//        appUtils.bindEvent($(_pageId + " #goHighEnd"), function () {
//            appUtils.pageInit(_pageUrl, "highEnd/qualifiedInvestor1");
//        })

    }
    function clearPageData(){
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId).find(".tc").val("--");//所有填充内容清理
        if(t) clearInterval(t);
        $(_pageId + ' #failinf').text("");
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').show();
        $(_pageId + " #goCompleted").addClass("bg_ccc");
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        clearPageData()
    }

    //倒计时
    function countDown() {
        // $(_pageId + " #goCompleted").addClass("bg_ccc");
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun102030(outputInfo, function (data) {
                    if (data.error_no == "0") {
                        $(_pageId + ".load").hide();
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败 8 确认成功 9 确认失败
                        if (data.results[0].trans_status == "8") {
                            $(_pageId + " .success").show();//充值成功
                        } else if (data.results[0].trans_status == "9" || data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            if (data.results[0].host_desc) {
                                $(_pageId + ' #failinf').text('失败原因：' + data.results[0].host_desc);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，充值失败');
                            }
                            $(_pageId + " .fail").show();//充值失败
                        } else {
                            $(_pageId + " .wait").show();//充值无结果
                        }
                        $(_pageId + " #goCompleted").removeClass("bg_ccc");
                    } else {
                        $(_pageId + " #goCompleted").removeClass("bg_ccc");
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info)
                    }
                })
            }

        }, 1000)
    }

    //显示充值相关时间日期
    function showTime() {
        service.reqFun102008({type: "2"}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var operatetime = data.results[0].operatetime;
                var revenueDate = data.results[0].revenueDate;
                var receivedDate = data.results[0].receivedDate;
                operatetime = common.dateStyle2(operatetime);
                revenueDate = common.dateStyle2(revenueDate);
                receivedDate = common.dateStyle2(receivedDate);
                $(_pageId + " #czTime").text(operatetime);
                $(_pageId + " #syStartTime").text(revenueDate);
                $(_pageId + " #syDay").text(receivedDate);
            } else {
                layerUtils.iAlert(error_info);
            }
        })
    }

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
