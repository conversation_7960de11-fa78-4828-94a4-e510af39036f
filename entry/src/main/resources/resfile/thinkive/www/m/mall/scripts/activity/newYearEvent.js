/**
 * 模块名：晋金财富新年活动页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        tools = require("../common/tools");
    var external = require("external");
    var ut = require("../common/userUtil");
    /* 常量 */
    var _pageCode = "activity/newYearEvent", _pageId = "#activity_newYearEvent";
    /* 变量  活动信息*/
    var activityRange = 1; // 0 不可参与活动  1 可参加活动
    var userInfo, cust_no, mobile;
    var unusedTimes, usedTimes; // 待使用次数、已点亮个数
    var chooseData  //调用原生分享传参
    var newYearActivity, sharedActivity, investActivity, inviteActivity;
    var isFlag = true;
    var productInfo;
    /**
     * 初始化
     */
    function init() {
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        mobile = userInfo.mobileWhole
        reqFun108030();//查询所有活动状态
        reqFun102113();// 查询90天产品详情
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
    }

    function reqFun108030() {
        service.reqFun108030({ cust_no: cust_no }, (data) => {
            // data.error_no = -6;
            if (data.error_no == 0) {
                var results = data.results[0];
                newYearActivity = results;
                // 活动规则
                var rule = newYearActivity.introduce;
                activityRange = newYearActivity.join_flag;
                $(_pageId + " .sign").html(rule);
                $(_pageId + " .rule-explain").html(newYearActivity.activityText);
                // 春节活动 14   连续分享(春节)15   投资活动(春节)16   邀请好友投资活动(春节)17
                results.final_reward && $(_pageId + " #get_award").html(`领取${results.final_reward}元积分红包`)
                // 最终奖励
                $(_pageId + " #activity_tips_extra").html(`${newYearActivity.final_reward}元`)
                $(_pageId + " #newYearActivityResultSingle #layer_total_score").html(`${newYearActivity.final_reward}`);
                if (results.status == '2') { // 已完成
                    $(_pageId + " #get_award").css({ 'display': "inline-block" })
                    $(_pageId + " #lantern_lighted").css({ 'display': "none" })
                    $(_pageId + " #done").css({ 'display': "none" })
                } else if (results.status == '3') {
                    $(_pageId + " #done").css({ 'display': "inline-block" })
                    $(_pageId + " #lantern_lighted").css({ 'display': "none" })
                    $(_pageId + " #get_award").css({ 'display': "none" })
                } else {
                    $(_pageId + " #lantern_lighted").css({ 'display': "inline-block" })
                    $(_pageId + " #get_award").css({ 'display': "none" })
                    $(_pageId + " #done").css({ 'display': "none" })
                }
                unusedTimes = results.unused_times; usedTimes = results.used_times;
                // unusedTimes = 4; usedTimes = 0;// TODO;
                // $(_pageId + " #not_light_num").html(4 - parseFloat(usedTimes));
                if (unusedTimes && unusedTimes > 0) {
                    $(_pageId + " #lantern_lighted span").addClass("newYearAnimation-logo")
                } else {
                    $(_pageId + " #lantern_lighted span").removeClass("newYearAnimation-logo")
                }
                $(_pageId + " #lantern_lighted span").html(unusedTimes);
                if (results.prepose_list && results.prepose_list instanceof Array && results.prepose_list.length) {
                    results.prepose_list.forEach(item => {
                        if (item.activity_type == '15') { // 去分享
                            sharedActivity = item;
                            // 1 未完成   3 已完成
                            if (sharedActivity.status == "3") {
                                $(_pageId + " #to_Shared_done").show();
                                $(_pageId + " #to_Shared").hide();
                                $(_pageId + " #to_Shared_done .alread-shared-tips").html(`已连续分享3天`)
                            } else {
                                $(_pageId + " #to_Shared").html(`${sharedActivity.btn_name}<span class="alread-shared-tips">已连续分享${sharedActivity.schedule}天</span>`);
                                $(_pageId + " #to_Shared").show();
                                $(_pageId + " #to_Shared_done").hide();
                            }

                        } else if (item.activity_type == "16") { // 去投资
                            investActivity = item;
                            if (investActivity.status == "3") {
                                $(_pageId + " #to_Invest_done").show();
                                $(_pageId + " #to_Invest").hide();
                            } else {
                                $(_pageId + " #to_Invest").html(`${investActivity.btn_name}`);
                                $(_pageId + " #to_Invest_done").hide();
                                $(_pageId + " #to_Invest").show();
                            }
                        } else if (item.activity_type == "17") {
                            inviteActivity = item; // 去邀请
                            if (inviteActivity.status == "3") {
                                $(_pageId + " #to_Invite_done").show();
                                $(_pageId + " #to_Invite").hide();
                            } else {
                                $(_pageId + " #to_Invite").html(`${inviteActivity.btn_name}`);
                                $(_pageId + " #to_Invite_done").hide();
                                $(_pageId + " #to_Invite").show();
                            }
                        }
                    })
                }

                // 已点亮灯笼显示
                if (usedTimes && usedTimes > 0) {
                    for (let i = 0; i < usedTimes; i++) {
                        $(_pageId + ` .new-year-main .activity-main .activity-lantern-top li:nth-child(${i + 1})`).html(`<img src="../mall/images/activity/newYearImg/dl${5 + i}.png" alt="">`);
                    }
                }
                if (newYearActivity.join_flag == 0) {
                    layerUtils.iAlert("当前活动参与人数较多，如有疑问，请联系客服。");
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }
    function reqFun102113() {
        let data = {
            fund_code: "G22002"
        }
        service.reqFun102113(data, (datas) => {
            if (datas.error_no == '0') {
                let res = datas.results[0]
                productInfo = res;
                appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.setSStorageInfo("financial_prod_type", "02");
                let prod_name = productInfo.prod_sname;
                let term = productInfo.term ? productInfo.term : '--'   //封闭期/锁定期
                let threshold_amount = threshold_amount_Result(productInfo.threshold_amount)//起购金额
                let dk_income_rate = productInfo.dk_income_rate ? toolsMoney(productInfo.dk_income_rate) + '%' : '--'
                $(_pageId + " #prod_detail .prod-name").html(`${prod_name}`);
                $(_pageId + " #prod_detail #term").html(`${term}`);
                $(_pageId + " #prod_detail #income_period_type_desc").html(`${productInfo.income_period_type_desc}年化`);
                $(_pageId + " #prod_detail #threshold_amount").html(`${threshold_amount}`);
                $(_pageId + " #prod_detail #dk_income_rate").html(`${dk_income_rate}`);
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    function threshold_amount_Result(threshold_amount) {
        if (!threshold_amount) return '--元'
        threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万元" : tools.fmoney(threshold_amount) + '元';
        return threshold_amount
    }

    function toolsMoney(time, num) {
        if (!time || time == '--') return '--'
        return tools.fmoney(time, num)
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        // 活动规则
        appUtils.bindEvent($(_pageId + " #activity_rule"), function (e) {
            $(_pageId + " #newYearActivityRules").show().find('.sign').show()
        })
        appUtils.bindEvent($(_pageId + " #score_record"), function (e) {
            appUtils.pageInit(_pageCode, "vipBenefits/records");
        })
        // 点亮灯笼
        appUtils.bindEvent($(_pageId + " #lantern_lighted"), function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            if (activityRange == 0) {
                layerUtils.iAlert("当前活动参与人数较多，如有疑问，请联系客服。");
                return;
            } else {
                if (newYearActivity.state == '2') { // 未开始
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                } else if (newYearActivity.state == '3') {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                } else if (newYearActivity.state == "1" && newYearActivity.unused_times == "0") {
                    // layerUtils.iAlert("参与下方活动可获得点亮机会");
                    // return;
                }
                if (isFlag) {
                    service.reqFun108032({ cust_no: cust_no, activity_id: newYearActivity.id }, (data) => {
                        if (data.error_no == 0) {
                            isFlag = !isFlag;
                            var results = data.results[0];
                            var time; // 当前点亮第几个
                            time = results.used_times;
                            var li = $(_pageId + ` .new-year-main .activity-main .activity-lantern-top li:nth-child(${time})`);
                            li.html(`<div class="bg"></div><img class="newYearAnimation"  src="../mall/images/activity/newYearImg/dl${parseFloat(time) + 4}.png" alt="">`)
                            unusedTimes = results.unused_times; usedTimes = results.used_times;
                            $(_pageId + " #lantern_lighted span").html(unusedTimes);
                            $(_pageId + " #newYearActivityResultSingle .result-score").html(results.points);
                            $(_pageId + " #newYearActivityResultSingle #not_light_num").html(4 - parseFloat(usedTimes));
                            if (usedTimes == "4") {
                                $(_pageId + " #newYearActivityResultSingle .single_layer_desc").html(`已成功点亮1盏灯笼并获得<span class="result-score">${results.points}</span>积分.当前灯笼已全部点亮,立即领取${newYearActivity.final_reward}元积分奖励.`);
                            }
                            setTimeout(() => {
                                $(_pageId + " #newYearActivityResultSingle").show();
                                reqFun108030();
                                isFlag = true;
                            }, 2000)
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                }

            }
        })
        // 领取50元积分
        appUtils.bindEvent($(_pageId + " #get_award"), function (e) {
            // $(_pageId + " #newYearActivityResultSingle").show()
            service.reqFun108032({ cust_no: cust_no, activity_id: newYearActivity.id }, (data) => {
                if (data.error_no == 0) {
                    var results = data.results[0];
                    var money = parseFloat(results.points) / 100;
                    $(_pageId + " #newYearActivityResult .result-money").html(`${money}元`);
                    $(_pageId + " #newYearActivityResult").show();
                    reqFun108030();
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })

        })
        // 去分享
        appUtils.bindEvent($(_pageId + " #to_Shared"), function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            if (activityRange == 0) {
                layerUtils.iAlert("当前活动参与人数较多，如有疑问，请联系客服。");
                return;
            } else {
                // 新年活动未开始或者新年活动进行中，去分享活动未开始
                if (newYearActivity.state == '2' || (newYearActivity.state == "1" && sharedActivity.state == "2")) { // 未开始
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                    // 新年活动已结束或者去分享活动已结束  新年活动进行中
                } else if (newYearActivity.state == '3' || sharedActivity.state == "3") {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                }
                appUtils.setSStorageInfo("toSharedId", sharedActivity.activity_id);
                return appUtils.pageInit(_pageCode, "activity/newYearInvitation");
            }

        })
        // 去投资
        appUtils.bindEvent($(_pageId + " #to_Invest"), function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            if (activityRange == 0) {
                layerUtils.iAlert("当前活动参与人数较多，如有疑问，请联系客服。");
                return;
            } else {
                if (newYearActivity.state == '2' || (newYearActivity.state == "1" && investActivity.state == "2")) { // 未开始
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                } else if (newYearActivity.state == '3' || investActivity.state == "3") {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                }
                appUtils.pageInit(_pageCode, "login/userIndexs", {});
                return;
            }

        })
        // 去邀请 跳到二维码页面
        appUtils.bindEvent($(_pageId + " #to_Invite"), function (e) {
             if (!ut.hasBindCard(_pageCode)) return;
            if (activityRange == 0) {
                layerUtils.iAlert("当前活动参与人数较多，如有疑问，请联系客服。");
                return;
            } else {
                if (newYearActivity.state == '2' || (newYearActivity.state == "1" && inviteActivity.state == "2")) { // 未开始
                    layerUtils.iMsg(-1, "活动尚未开始！", 2);
                    return;
                } else if (newYearActivity.state == '3' || inviteActivity.state == "3") {
                    layerUtils.iMsg(-1, "活动已结束！", 2);
                    return;
                }
                return appUtils.pageInit(_pageCode, "vipBenefits/friendInvitation");
            }

        })
        appUtils.preBindEvent($(_pageId + " #newYearActivityRules"), " .okBtn", function (e) {
            $(_pageId + " #newYearActivityRules").hide();
        })
        // 立即领取
        appUtils.preBindEvent($(_pageId + " #newYearActivityResult"), " #successBtn", function (e) {
            $(_pageId + " #newYearActivityResult").hide();
        })
        // 立即领取
        appUtils.preBindEvent($(_pageId + " #newYearActivityResultSingle"), " #successBtn", function (e) {
            $(_pageId + " #newYearActivityResultSingle").hide();
        })

        appUtils.bindEvent($(_pageId + " #prod_detail .prod_to_button"), function (e) {
            // console.log(productInfo);
            tools.jumpDetailPage(_pageCode, productInfo.prod_sub_type, productInfo.prod_sub_type2)

        })
    }
    /**
     * 销毁
     */
    function destroy() {
        activityRange = null;
        $(_pageId + " #newYearActivityResultSingle").hide();
        $(_pageId + " #newYearActivityResult").hide();
        $(_pageId + " #newYearActivityRules").hide();

        $(_pageId + " #lantern_lighted").css({ 'display': "inline-block" })
        $(_pageId + " #get_award").css({ 'display': "none" })
        $(_pageId + " #done").css({ 'display': "none" })
        $(_pageId + " #lantern_lighted span").html(0);

        $(_pageId + " #to_Shared").html(`去分享<span class="alread-shared-tips">已连续分享0天</span>`);
        $(_pageId + " #to_Shared").show();
        $(_pageId + " #to_Shared_done").hide();

        $(_pageId + " #to_Invest").html(`去投资`);
        $(_pageId + " #to_Invest_done").hide();
        $(_pageId + " #to_Invest").show();
        $(_pageId + " #to_Invite").html('去邀请');
        $(_pageId + " #to_Invite_done").hide();
        $(_pageId + " #to_Invite").show();
        $(_pageId + ` .new-year-main .activity-main .activity-lantern-top li:nth-child(1)`).html(`<img src="../mall/images/activity/newYearImg/dl1.png" alt="">`);
        $(_pageId + ` .new-year-main .activity-main .activity-lantern-top li:nth-child(2)`).html(`<img src="../mall/images/activity/newYearImg/dl2.png" alt="">`);
        $(_pageId + ` .new-year-main .activity-main .activity-lantern-top li:nth-child(3)`).html(`<img src="../mall/images/activity/newYearImg/dl3.png" alt="">`);
        $(_pageId + ` .new-year-main .activity-main .activity-lantern-top li:nth-child(4)`).html(`<img src="../mall/images/activity/newYearImg/dl4.png" alt="">`);

    };
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
