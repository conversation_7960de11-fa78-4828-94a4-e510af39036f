/**
 * 模块名：晋金财富抽奖外链页面
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        common = require("common"),
        validatorUtil = require("validatorUtil");
    var ut = require("../common/userUtil");
    var global = gconfig.global;
    require("../common/html2canvas.min");
    require("../common/jquery.qrcode.min");
    /* 常量 */
    var _pageCode = "activity/posterShareInvitation", _pageId = "#activity_posterShareInvitation";
    /* 变量  活动信息*/
    var state, cust_no;
    var activity_id;
    var fridayActivityInfo;
    var comment_content;
    var pageTouchTimer = null;
    /*** 初始化*/
    function init() {
        $(_pageId + " #code").html("");
        cust_no = ut.getUserInf().custNo;
        fridayActivityInfo = appUtils.getSStorageInfo("friday_data");
        comment_content = appUtils.getSStorageInfo("comment_content");
        let textLen;
        let clientH = document.body.clientHeight;
        if (clientH >= 640 && clientH < 740) {
            textLen = 40;
        } else if (clientH >= 740) {
            textLen = 58;
        }
        if (comment_content && comment_content.length > textLen) {
            comment_content = comment_content.substring(0, textLen) + "...";
        }

        if (fridayActivityInfo.friday_activity_sub_type == "2") {
            $(_pageId + " .comment-bg").css("display", "block");
            $(_pageId + " .earnings-bg").css("display", "none");
            $(_pageId + " .comment-bg .c-detail").html("“" + comment_content + "”")
        } else if (fridayActivityInfo.friday_activity_sub_type == "1") {
            $(_pageId + " .comment-bg").css("display", "none");
            $(_pageId + " .earnings-bg").css("display", "block");
            $(_pageId + " .earnings-bg .activity-text").html(fridayActivityInfo.content)
            $(_pageId + " .earnings-bg .time").html(fridayActivityInfo.date);
        }
        let requestData = {
            img_url: global.oss_url + fridayActivityInfo.img_url
        }
        let base64Str;
        service.reqFun102119(requestData, function (data) {
            if (data.error_no == "0") {
                base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                if (fridayActivityInfo.friday_activity_sub_type == "2") {
                    fridayActivityInfo.img_url && $(_pageId + " #comment_bg_img").attr("src", base64Str)
                } else if (fridayActivityInfo.friday_activity_sub_type == "1") {
                    fridayActivityInfo.img_url && $(_pageId + " #earnings_bg_img").attr("src", base64Str)
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
        activity_id = appUtils.getSStorageInfo("friday_data").activity_id;
        qrcode();
    }

    //动态生成二位码
    function qrcode() {
        var mobile = ut.getUserInf().mobileWhole;
        let share_type = sessionStorage.share_template ? sessionStorage.share_template : "7"
        share(mobile, share_type);
    }

    function share(mobile, share_type) {
        var query_params = {};
        query_params["registered_mobile"] = mobile;
        query_params["tem_type"] = share_type;
        service.reqFun102012(query_params, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results != undefined && data.results.length > 0) {
                    mobile = common.desEncrypt("mobile", mobile);//加密
                    var result = data.results[0];
                    var share_url = result.share_url;
                }
                if (validatorUtil.isEmpty(share_url)) {
                    share_url = global.link;
                }
                if (share_url.indexOf("?") != -1) {
                    share_url = share_url + "&mobile=" + mobile;
                } else {
                    share_url = share_url + "?mobile=" + mobile;
                }
                service.reqFun101073({ long_url: share_url }, function (res) {
                    if (res.error_no == "0") {
                        if (res.results != undefined && res.results.length > 0) {
                            var short_url = res.results[0].shortUrl;
                            $(_pageId + " #code").qrcode({
                                render: "canvas", //设置渲染方式，有table和canvas
                                text: short_url, //扫描二维码后自动跳向该链接
                                width: 80, //二维码的宽度
                                height: 80, //二维码的高度
                                imgWidth: 20,
                                imgHeight: 20,
                                src: '../mall/images/icon_app.png'
                            });
                        }
                    } else {
                        layerUtils.iAlert(res.error_info);
                    }
                })

            } else {
                layerUtils.iAlert(error_info);
            }
        });

    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_posterShareInvitation");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " .friday-poster-page")
            html2canvas(dom, {
                scale: 4,
                height: dom.offsetHeight, //注意 下面解决当页面滚动之后生成图片出现白边问题
                width: dom.offsetWidth,  //注意 下面解决当页面滚动之后生成图片出现白边问题
            }).then(canvas => {
                var base64 = canvas.toDataURL("image/png");
                var _base64 = base64.split(",")[1];
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                // let img = document.createElement('img')
                // img.src = base64
                // img.style.height = canvas.style.height
                // img.style.width = canvas.style.width
                // img.id = 'canvasImg'
                // document.querySelector('body').append(img)
                service.reqFun108034({ cust_no: cust_no, activity_id: activity_id }, (data) => {
                    if (data.error_no == 0) {
                        common.share("22", sessionStorage.share_template ? sessionStorage.share_template : "0", "", true, _base64);
                        var results = data.results[0];
                        if (results) {
                            pageTouchTimer = setTimeout(() => {
                                // current_state 0 未发放 1 已发放 2 稍后发放
                                if (results.rewardStatus == '1' && results.pointsAmount) {
                                    $(_pageId + " #pointsResult .single_layer_desc").html(results.pointsAmount + "积分已发放至您的账户");
                                    $(_pageId + " #pointsResult").show();
                                }
                                if (results.rewardStatus == '2') {
                                    $(_pageId + " #pointsResult .single_layer_desc").html("奖励随后发放");
                                    $(_pageId + " #pointsResult").show();
                                }
                            }, 4000);
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                })
            })
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_posterShareInvitation");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " .friday-poster-page")
            html2canvas(dom, {
                scale: 4,
                height: dom.offsetHeight, //注意 下面解决当页面滚动之后生成图片出现白边问题
                width: dom.offsetWidth,  //注意 下面解决当页面滚动之后生成图片出现白边问题
            }).then(canvas => {
                var base64 = canvas.toDataURL("image/png");
                var _base64 = base64.split(",")[1];
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                // let img = document.createElement('img')
                // img.src = base64
                // img.style.height = canvas.style.height
                // img.style.width = canvas.style.width
                // img.id = 'canvasImg'
                // document.querySelector('body').append(img)
                service.reqFun108034({ cust_no: cust_no, activity_id: activity_id }, (data) => {
                    if (data.error_no == 0) {
                        common.share("23", sessionStorage.share_template ? sessionStorage.share_template : "1", "", true, _base64);
                        var results = data.results[0];
                        if (results) {
                            pageTouchTimer = setTimeout(() => {
                                // current_state 0 未发放 1 已发放 2 稍后发放
                                if (results.rewardStatus == '1' && results.pointsAmount) {
                                    $(_pageId + " #pointsResult .single_layer_desc").html(results.pointsAmount + "积分已发放至您的账户");
                                    $(_pageId + " #pointsResult").show();
                                }
                                if (results.rewardStatus == '2') {
                                    $(_pageId + " #pointsResult .single_layer_desc").html("奖励随后发放");
                                    $(_pageId + " #pointsResult").show();
                                }
                            }, 4000);
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                })
            })
        });
        // 知道了
        appUtils.bindEvent($(_pageId + " #successBtn"), function () {
            $(_pageId + " #pointsResult").hide();
        });
    }

    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " #pointsResult .single_layer_desc").html("");
        $(_pageId + " #pointsResult").hide();
        clearTimeout(pageTouchTimer);
    };
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
