/*作者：   Alt+  时间： 2016-08-16 18:10:33 PM 修改了框架原来的结构，by 汪卫中 2016-12-06*/
define(function(require, exports, module) { +
        function(a) {
            function b() {
                var team = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
                var res = [];
                for (var i = 0,
                         len = team.length; i < len; i++) {
                    // 随机叫个
                    var randomIndex = Math.floor(Math.random() * team.length);
                    // 出列到新队伍
                    res[i] = team[randomIndex];
                    // 出来了不能继续叫了哦
                    team.splice(randomIndex, 1);
                }
                return d && (d = null),
                    d = [],
                    d.push('<div id="KeyPanel1" class="keyword_table">'),
                    d.push('<table width="100%" border="0" cellspacing="0" cellpadding="0" class="random">'),
                    d.push("<tbody><tr>"),
                    d.push('<td><a herf="javascript:void(0)">' + res[0] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[1] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[2] + '</a></td>'),
                    d.push("</tr>"),
                    d.push("<tr>"),
                    d.push('<td><a herf="javascript:void(0)">' + res[3] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[4] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[5] + '</a></td>'),
                    d.push("</tr>"),
                    d.push("<tr>"),
                    d.push('<td><a herf="javascript:void(0)">' + res[6] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[7] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[8] + '</a></td>'),
                    d.push("</tr>"),
                    d.push("<tr>"),
                    d.push('<td><a herf="javascript:void(0)" class="btn ok">关闭</a></td>'),
                    d.push('<td><a herf="javascript:void(0)">' + res[9] + '</a></td>'),
                    d.push('<td><a herf="javascript:void(0)" class="del">删除</a></td>'),
                    d.push("</tr></tbody>"),
                    d.push("</table>"),
                    d.push("</div>"),
                    d.join("")
            }
            var c = "KeyPanel1",
                d = null,
                e = {
                    createKeyPanelHtml: b,
                    keyPanelId: c
                };
            a.keyType1 = e
        } ($)
});
/*出品单位：深圳市思迪信息技术股份有限公司-前端Html5开发小组*/
//<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 84 84" enable-background="new 0 0 84 84" xml:space="preserve"><rect opacity="0.5" fill="none" width="84" height="84"></rect><g><g><path fill="#E6E6E6" d="M76,25v34H23L8.9,42L23,25H76 M79,22H21.6L5,42l16.6,20H79V22L79,22z"></path></g><polygon fill="#E6E6E6" points="41.3,53 49.6,44.7 57.9,53 60.6,50.3 52.3,42 60.6,33.7 57.9,31 49.6,39.3 41.3,31 38.6,33.7 '),d.push('46.9,42 38.6,50.3 	"></polygon></g></svg>
