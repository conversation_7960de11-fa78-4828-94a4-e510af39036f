//多基金定投记录详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#fixedInvestment_moreInvestmentIndexDetail";
        require('../../js/zepto.min.js');
        require('../../js/iscroll.js');
        require('../../js/iosSelect.js');
        VIscroll = require("vIscroll");
        require('../../js/zepto.min.js');
        require('../../js/iscroll.js');
        require('../../js/iosSelect.js');
    var tools = require("../common/tools");
    var _pageCode = "fixedInvestment/moreInvestmentIndexDetail";
    var ut = require("../common/userUtil");
    var params; //上个页面携带参数
    let userInfo;
    var ut = require("../common/userUtil");
    let cur_page;   //当前页
    var isEnd = false;
    var status = ['受理成功','受理失败','','交易成功','交易失败','','退款成功','退款失败','确认成功','确认失败']
    function init() {
        // params = appUtils.getPageParam(); 
        //页面埋点初始化
        tools.initPagePointData();
        vIscroll = {"scroll": null, "_init": false};
        params = appUtils.getSStorageInfo("moreListInfo")
        // console.log(params)
        cur_page = 1;
        userInfo = ut.getUserInf();
        getList(false)
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转详情
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".trade_box", function (e) {
            var info = JSON.parse($(this).find(".info").text());
            info.payMethod = '0'
            appUtils.setSStorageInfo("planInfo", info);
            //跳转详情
            appUtils.pageInit(_pageCode, 'fixedInvestment/investmentDetails',{payMethod:'0'});
        }, "click");
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //获取分页数据
    function getList(flag){
        isEnd = false;
        let data = {
            planid : params.planid,
            custno : userInfo.custNo,
            fundcode : params.fundcode,
            cur_page:cur_page,
            recordserno:params.recordserno
        };
        var gettransActionCallBack = function (datas) {
            var error_no = datas.error_no,
                error_info = datas.error_info;
            if (error_no == "0") {
                var totalPages = datas.results[0].data.totalPages; //总页数
                var results = datas.results[0].data.data;
                var str = "";
                for (var i = 0; i < results.length; i++) {
                    let card = results[i]
                    str += '<div class="trade_box" operationType="1" operationId="trade_box" operationName="定投详情">' +
                        '<div class="fundInfo">' +
                        '<p class="info" style="display: none">' + JSON.stringify(results[i]) + '</p>' +
                        '<p>定投</p>' +
                        '<p style="width:1.6rem">' + card.fundname + '</p>' +
                        '<p>' + card.crtdate + ' ' + card.crttime + '</p>' +
                        '</div>' +
                        '<div class="result right_icon" style="height: 100%">' +
                        '<p>' + tools.fmoney(card.investmoney) + '元</p>' +
                        '<p>' + tools.fundDataDict(card.exestatus,"pub_purchase_status_name") + '</p>' +
                        '</div>' +
                    '</div>'
                }
                if (totalPages == cur_page) {
                    isEnd = true;
                    str += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && results.length == 0) {
                    isEnd = true;
                    str = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                // console.log(flag)
                if (flag) {
                    $(_pageId + " .finance_pro").append(str);
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
            } else {
                layerUtils.iAlert(error_info);
            }
        };
        service.reqFun102143(data, gettransActionCallBack);
    }
    /**
     * 上下滑动刷新事件
     * */

     function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    endTime = "";
                    getList(false);
                    // $(_pageId + " .visc_pullUp").show();
                    // $(_pageId + " .visc_pullUpIcon").hide();
                    // $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getList(true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        isEnd = false;
        $(_pageId + " .finance_pro").html("");
        cur_page = 1;
    }
    function pageBack() {
        appUtils.pageBack();
    }

    var bondFixMochikura = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bondFixMochikura;
});
