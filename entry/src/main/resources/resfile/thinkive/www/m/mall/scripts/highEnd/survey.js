// 晋金高端概况
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#highEnd_survey ";
    var productInfo;
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " .prodName").text(productInfo.prod_name);
        $(_pageId + " .fund_code").text(productInfo.fund_code);
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        //产品整合相关
        let tem_confirm_date = tools.ftime(productInfo.confirm_date)    //确认日
        if(productInfo.prod_sub_type2 == "94"){
        	$(_pageId + " .purconfirm_days").text(tools.ftime(productInfo.lock_start));
        }else if(productInfo.prod_sub_type2 == "100"){
            // console.log(tem_confirm_date,222)
            $(_pageId + " .purconfirm_days").text(tem_confirm_date);
        }else{
            $(_pageId + " .purconfirm_days").text(tools.ftime(productInfo.purconfirm_days));
        }
        $(_pageId + " .riskDesc").text(productInfo.risk_level_desc);
        //100
        if(productInfo.prod_sub_type2 == "100") $(_pageId + " .riskDesc").text(productInfo.risk_level_name);
        $(_pageId + " .issuingScale").text(productInfo.issuing_scale? tools.fmoney(productInfo.issuing_scale / 10000 )+'万': "--");
        $(_pageId + " .mgrcompName").text(productInfo.mgrcomp_name);
        $(_pageId + " .mgrcompSname").text(productInfo.mgrcomp_sname);
        $(_pageId + " .trusteeName").text(productInfo.trustee_name);//托管人
        $(_pageId + " .fundManagers").text(productInfo.fund_managers);
        if(productInfo.record_prod && productInfo.record_prod.length > 0){
           $(_pageId + " .filingCode").text(productInfo.record_prod);
        }else{
            $(_pageId + " .filingCode").text('');
        }
        
        if(productInfo.prod_sub_type == '80'){
        	$(_pageId + " .stratergy").hide()
        }else{
            $(_pageId + " .stratergy").show()
            $(_pageId + " .investStratergy").text(productInfo.invest_stratergy);
        }
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .prodName").text("");
        $(_pageId + " .fund_code").text("");
        $(_pageId + " .establishDate").text("");
        $(_pageId + " .riskDesc").text("");
        $(_pageId + " .issuingScale").text("");
        $(_pageId + " .mgrcompName").text("");
        $(_pageId + " .filingCode").text("");
        $(_pageId + " .mgrcompSname").text("");
        $(_pageId + " .trusteeName").text("");
        $(_pageId + " .fundManagers").text("");
        $(_pageId + " .investStratergy").text("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thsurvey = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thsurvey;
});
