//债基 持有期： 锁定份额
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageId = "#template_lockInShare";
    var _pageCode = "template/lockInShare";
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("holdObj");
        tools.initPagePointData();
        // console.log(productInfo.closed_length,111)
        let day_name = productInfo.lock_period_unit == '0' ? '年' : productInfo.lock_period_unit == '1' ? '月' : '天'
        $(_pageId + " .lock_date").html(productInfo.closed_length + day_name);
        //渲染锁定
        getlockingList();
    }

    function bindPageEvent() {
    	appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //获取锁定份额
    function getlockingList() {
        let arr = productInfo.lock_vol_list
        if (arr.length == 0) {
            $(_pageId + " #performanceContent .list_content").html("<div class='nodata'>暂无数据</div>");
            $(_pageId + " .warm-tip").hide();
            return;
        }
        var html = "";
        for (var i = 0; i < arr.length; i++) {
            var lock_vol = arr[i].vol;
            var due_date = arr[i].due_date;

            html += '<div class="item">' +
                '<span style="width: 50%">' + lock_vol + '份</span>' +
                '<span class="">' + tools.ftime(due_date.substr(0, 8)) + '</span>' +
                '</div>';
        }
        $(_pageId + " #performanceContent .list_content").html(html);
        $(_pageId + " .warm-tip").show();
    }

    function destroy() {
        $(_pageId + " .header_inner h1").html("--");
        $(_pageId + " #performanceContent .list_content").html("");
        $(_pageId + " .warm-tip").hide();
        $(_pageId + " .lock_date").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
