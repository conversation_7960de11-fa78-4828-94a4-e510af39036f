define(function(require, exports, module) {
	window.iBrowser = window.iBrowser || {};
	window.iBrowser.android = navigator.userAgent.indexOf('thinkive_android') > -1;
	window.iBrowser.ios = navigator.userAgent.indexOf('thinkive_ios') > -1;
	window.iBrowser.harmony = navigator.userAgent.indexOf('thinkive_harmony') > -1;
    function a(a, b) {
        var g;
        try {
            var h = Object.assign(a, d.publicNativeParam || {});
            a = h;
            var i = sessionStorage.getItem("_tkuuid");
            if (i && (a.tkuuid = i), b) {
                var j = Number((Math.random() + "").substring(2, 10) + "") + "";
                f[j] = b,
                a.flowNo = j,
                a.isJsCallBack = "1"
            }
            if (1 == c.isDebug, window.iBrowser.android && window.navigator.userAgent.indexOf("/thinkive_android") >= 0) g = !(window.external && window.external.callMessage) ? prompt("callMessage", JSON.stringify(a)) : window.external.callMessage(JSON.stringify(a)),
            "string" == typeof g && (g = JSON.parse(g));
            else if (window.iBrowser.ios && window.navigator.userAgent.indexOf("/thinkive_ios") >= 0) try {
                g = prompt("js-invoke://external:callMessage", JSON.stringify(a)),
                g && "null" !== g && "string" == typeof g && (g = JSON.parse(g));
            } catch(l) {}
			else if (window.iBrowser.harmony) {
			   g = window.external.callMessage(JSON.stringify(a)),
			   g && "null" !== g && "string" == typeof g && (g = JSON.parse(g));
			}
        } catch(l) {
            e.iAlert("H5调用原生external.callMessage方法出错：" + l.message)
        }
        return g ? g: {}
    }
    function b(a, b) {
        var f;
        try {
            var g = Object.assign(paramMap, d.publicNativeParam || {});
            paramMap = g;
            var h = sessionStorage.getItem("_tkuuid");
            h && (paramMap.tkuuid = h),
            1 == c.isDebug;
            var i = $.extend({},
            b);
            if ("undefined" == typeof b.error_no && (b.error_no = "0", b.error_info = ""), "undefined" == typeof b.results && (b.results = [i]), b.flowNo = a, window.iBrowser.android && window.navigator.userAgent.indexOf("/thinkive_android") >= 0) f = !(window.external && window.external.callback) ? prompt("callback", JSON.stringify(b)) : window.external.callback(JSON.stringify(b)),
            "string" == typeof f && (f = JSON.parse(f));
            else if (window.iBrowser.ios && window.navigator.userAgent.indexOf("/thinkive_ios") >= 0) try {
                if (window.webkit && window.webkit.messageHandlers) f = prompt("js-invoke://callback:callMessage", JSON.stringify(b)),
                f && "null" !== f && "string" == typeof f && (f = JSON.parse(f));
                else {
                    var j = new XMLHttpRequest;
                    j.open("GET", window.location.protocol + "//" + window.location.host + "//js-invoke://callback:callMessage:" + encodeURIComponent(JSON.stringify(b)), !1),
                    j.send(""),
                    f = JSON.parse(j.responseText)
                }
            } catch(k) {}
			else if (window.navigator.userAgent.indexOf("/thinkive_harmony") >= 0)(f = window.external.callback(JSON.stringify(b))),f && "null" !== f && "string" == typeof f && (f = JSON.parse(f));
        } catch(k) {
            e.iAlert("H5调用原生external.callback方法出错：" + k.message)
        }
        return f ? f: {}
    }
    var c = require("gconfig"),
    d = c.global,
    e = require("layerUtils"),
    f = {};
    Object.assign || Object.defineProperty(Object, "assign", {
        enumerable: !1,
        configurable: !0,
        writable: !0,
        value: function(a) {
            "use strict";
            if (void 0 === a || null === a) throw new TypeError("Cannot convert first argument to object");
            for (var b = Object(a), c = 1; c < arguments.length; c++) {
                var d = arguments[c];
                if (void 0 !== d && null !== d) for (var e = Object.keys(Object(d)), f = 0, g = e.length; g > f; f++) {
                    var h = e[f],
                    i = Object.getOwnPropertyDescriptor(d, h);
                    void 0 !== i && i.enumerable && (b[h] = d[h])
                }
            }
            return b
        }
    }),
    window.callMessage = function(a, d) {
        var f = null,
        g = null;
        a = a || {},
        1 == c.isDebug;
        try {
            f = "function" + a.funcNo,
            require.async(c.projPath + "shellFunction/msgFunction",
            function(module) {
                if (g = module, 1 == d && g[f] && a.hasOwnProperty("flowNo")) {
                    var h = a.flowNo,
                    i = function(a) {
                        setTimeout(function() {
                            a = a || {},
                            a.flowNo = h,
                            a.isJsCallBack = "1",
                            b(h, a)
                        },
                        1)
                    };
                    g[f](a, i)
                } else g[f] ? g[f](a) : e.iAlert("原生调用H5模块" + c.projName + "中的msgFunction模块中" + f + "方法不存在！")
            })
        } catch(h) {
            e.iAlert("原生调用H5的callMessage(功能号【" + a.funcNo + "】)出错：" + h.message)
        }
    },
    window.callMessageByFlowNo = function(a, b) {
        if (b = b || {},
        a) {
            var c = f[a];
            c && c(b),
            delete f[a]
        }
    },
    window.$.callMessage = a,
    module.exports = {
        callMessage: a,
        callNativeByFlowNo: b
    }
});