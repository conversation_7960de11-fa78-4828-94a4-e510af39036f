// 代言人申请页
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        _pageCode = "spokesperson/applicationPage",
        _pageId = "#spokesperson_applicationPage ";
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    require("../../js/prov_city.js");
    require("../../js/city.js");
    require("../../js/picker.min.js");
    var userInfo;
    var first = []; /* 省，直辖市 */
    var second = []; /* 市 */
    var third = []; /* 镇 */
    var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */
    var checked = [0, 0, 0]; /* 已选选项 */
    var picker = ""; //地址选择器

    function init() {
        userInfo = ut.getUserInf();
        $(_pageId + " #custInfo #name").val(userInfo.name);
        selectorArea();
    }

    /**
   * 地域选择
   */
    function selectorArea() {
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }
        creatList(city, first);
        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{ text: '', value: 0 }];
        }
        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{ text: '', value: 0 }];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });
        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            value = text1 + ' ' + text2;
            var code1 = first[selectedIndex[0]].code;
            var code2 = second[selectedIndex[1]].code;
            var code3 = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            var code = code1 + ' ' + code2;
            $(_pageId + " #live_address").attr("data-code", code);
            let flag = await tools.is_region(code1, code2, code3, city)
            if (flag) {
                $(_pageId + " #live_address").val(value);
            } else {
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }
            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);
                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{ text: '', value: 0 }];
                        checked[2] = 0;
                    }
                } else {
                    second = [{ text: '', value: 0 }];
                    third = [{ text: '', value: 0 }];
                    checked[1] = 0;
                    checked[2] = 0;
                }
                picker.refillColumn(1, second);
                picker.scrollColumn(1, 0)
            }
            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                } else {
                    third = [{ text: '', value: 0 }];
                    checked[2] = 0;
                }
            }
        });
        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        })
        // 提交申请信息
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            if (validatorUtil.isEmpty($(_pageId + " #live_address").val())) {
                layerUtils.iMsg(-1, "请选择地域");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #duty").val())) {
                layerUtils.iMsg(-1, "请填写职业");
                return;
            }
            if (validatorUtil.isEmpty($(_pageId + " #advantage").val())) {
                layerUtils.iMsg(-1, "请填写优势");
                return;
            }
            var param = {
                duty: $(_pageId + " #duty").val(),
                advantage: $(_pageId + " #advantage").val(),
                province_code: $(_pageId + " #live_address").attr("data-code").split(" ")[0],
                province_name: $(_pageId + " #live_address").val().split(" ")[0],
                city_code: $(_pageId + " #live_address").attr("data-code").split(" ")[1],
                city_name: $(_pageId + " #live_address").val().split(" ")[1],
                // living_address:{name: $(_pageId + " #live_address").val(),code:$(_pageId + " #live_address").attr("data-code")},
            }
            service.reqFun113002(param, function (data) {
                if (data.error_no == '0') {
                    var results = data.results[0]
                    if (results.is_eligible == '0') {
                        layerUtils.iAlert(`您的${results.apply_prod_name}持仓不足${results.apply_amt}元，暂不符合申请条件，请尽快充值`, () => { }, () => {
                            appUtils.pageInit(_pageCode, "spokesperson/publicityPage");
                        }, '', '确定')
                    } else {
                        layerUtils.iAlert(`2个工作日内完成审核，如有疑问，详询${global.custServiceTel}`, () => { }, function () {
                            appUtils.pageInit(_pageCode, "spokesperson/publicityPage");
                        }, '', '确定')

                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //页面销毁
    function destroy() {
        $(_pageId + " #kefu").hide();
        $(_pageId + " #duty").val("");
        $(_pageId + " #advantage").val("");
        $(_pageId + " #live_address").val("");
        $(".picker").remove();
        first = [];
        second = [];
        third = [];
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    let applicationPageModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = applicationPageModule;
});
