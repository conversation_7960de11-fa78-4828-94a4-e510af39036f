// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "highEnd/contract",
        _pageId = "#highEnd_contract ";
    var productInfo;
    var platform = require("gconfig").platform;
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        var isSign = appUtils.getPageParam("isSign");
        var fund_code = productInfo.fund_code;
        var buy_state = productInfo.buy_state;
        if (buy_state == "4") {
            buy_state = "1";
        } else if (buy_state == "3") {
            buy_state = "2";
        }
        if (isSign) {
            getSignPdf();
        } else {
            tools.getPdf({
                agreement_type: "prod",
                fund_code: fund_code,
                bookTitleMark: "0",
                agreement_sub_type: buy_state,
            },null,null,null,null); //获取协议
        }

    }

    //获取协议
    function getSignPdf() {
        service.reqFun102092({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return
            }
            if(data.results.length == 0) { //没有签署协议展示产品购买协议
                if(sessionStorage.transferable == 1) buy_state = '1,6'
                tools.getPdf({
                    agreement_type: "prod",
                    fund_code: productInfo.fund_code,
                    bookTitleMark: "0",
                    agreement_sub_type: "1"
                },null,null,null,null,true); //获取协议
                return;
            }
            var html = "";
            for (var i = 0; i < data.results.length; i++) {
                var privateUrl = data.results[i].ossurl;
                html += '<a href="javascript:void(0);" class="xy" url="' + privateUrl + '">';
                html += data.results[i].pdf_title;
                html += "</a>";
            }
            $(_pageId + " .deal_box").html(html);
            //查看PDF文件
            appUtils.preBindEvent($(_pageId + " .deal_box"), ".xy", function (e) {
                e.preventDefault();
                e.stopPropagation();
                var privateurl = $(this).attr("url");
                let title = '内容查看'
                let statusColor = '#2F4D80'
                let titleColor = '#2F4D80'
                service.reqFun102093({privateUrl: privateurl}, function (data) {
                    if (data.error_no != 0) {
                        layerUtils.iAlert(data.error_info);
                        return
                    }
                    var publicUrl = data.results[0].urlPublic;
                    var param = {};
                    param["funcNo"] = "50240";
                    param["title"] = title
                    param["url"] = publicUrl;
                    param["statusColor"] = statusColor
                    param["titleColor"] = titleColor
                    if(platform == '2' || platform == '5'){
                        param["rightBtnTxt"] = 'tk_webview_download';
                        param["rightBtnMode"] = '1';
                        param["rightBtnAction"] = "note_download_pdf";
                    }
                    require("external").callMessage(param);
                });

            }, "click");
        });
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        sessionStorage.transferable = null
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thcontract = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thcontract;
});
