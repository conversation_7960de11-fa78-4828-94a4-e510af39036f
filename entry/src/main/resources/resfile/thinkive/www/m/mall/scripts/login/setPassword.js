// 引流注册-设置登录密码
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        endecryptUtils = require("endecryptUtils"),
        monkeywords = require("mall/scripts/common/passwordKeywords"),
        common = require("common"),
        _pageId = "#login_setPassword";
    var _pageCode = "login/setPassword";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var external = require("external");
    
    var platform = require("gconfig").platform;
    var mobile,mobile_save,param;
    var invitationMobile,userInfo;
    var labelId,label_show;//渠道ID
    function init() {
        // param = appUtils.getPageParam(); //拿到注册页面缓存
        // mobile = param.registered_mobile
        // labelId = appUtils.getSStorageInfo("labelId");// 获取渠道ID
        // if (!mobile) {
        //     appUtils.pageInit(_pageCode, "login/userRegistered");
        //     return;
        // }
        //是否有本地存储的渠道代码,有渠道,先查询对应的标签id
        // var channelCode = common.getLocalStorage("download_channel_code");
        // if(channelCode){
        //     service.reqFun102105({"channel_code":channelCode}, function (data) {
        // 	if (data.error_no == "0") {
        // 	    if(data.results && data.results.length>0){
        // 		var result = data.results[0];
        // 		//是否要展示邀请码
        // 		label_show = result.label_show;
        // 		if(result.label_show == "1"){
        // 		    $(_pageId + " #labelId_div").show();
        // 		}
        // 		//邀请码是否有默认值
        // 		if(result.label){
        // 		    $(_pageId + " #labelId").val(result.label);
        // 		}
        // 	    }
        //         } else {
        //             layerUtils.iAlert(data.error_info);
        //         }
        //     });
        // }
        //获取用户个性信息
        userInfo = appUtils.getSStorageInfo("user");
        // console.log(userInfo)
    }
    /**
     * 渲染密码
     */
     function setPassword1(_fatherPageId,_childPageId){
        let spacing = platform == 1 ? 0.06 : 0.075
        let pwd = $(_pageId + _childPageId).val();
        if(!pwd){
            $(_pageId + _fatherPageId + " .placeholderPsd").html('请输入密码');
            $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#aaa');
            $(_pageId + _fatherPageId + " .cursor-bink").css("left",'1rem');
            return
        }  
        $(_pageId + _fatherPageId + " .placeholderPsd").css('color','#666');
        let length = pwd.length*1
        if(length > 16) return
        let str = ''
        for(let i = 0; i<length; i++){
            str = str + '*'
        }
        $(_pageId + _fatherPageId + " .placeholderPsd").html(str);
        $(_pageId + _childPageId ).val(pwd);
        // let leftValue = 1 + (length*spacing)
        let leftValue = ($(_pageId + _fatherPageId + " .placeholderPsd")[0].clientWidth/100) + 0.9;
        $(_pageId + _fatherPageId + " .cursor-bink").css("left",leftValue+'rem');
    }
    function moneyboardEvent(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #pwd1"),
            endcallback: function () {
                setPassword1(" #new_password"," #pwd1")
            },
            inputcallback: function () {
                setPassword1(" #new_password"," #pwd1")
            },
            keyBoardHide: function () {
                setPassword1(" #new_password"," #pwd1")
            }
        })
    }
    function moneyboardEvent1(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #pwd2"),
            endcallback: function () {
                setPassword1(" #new_password_repeat"," #pwd2")
            },
            inputcallback: function () {
                setPassword1(" #new_password_repeat"," #pwd2")
            },
            keyBoardHide: function () {
                setPassword1(" #new_password_repeat"," #pwd2")
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " #new_password .cursor-bink").hide()
            $(_pageId + " #new_password_repeat .cursor-bink").hide()
            monkeywords.close();
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        }, "click");
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #new_password"), function (event) {
            event.stopPropagation();
            $(_pageId + " #new_password .cursor-bink").show()
            $(_pageId + " #new_password_repeat .cursor-bink").hide()
            moneyboardEvent()
            // $(_pageId + " #pwd1").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "login_setPassword";
            param["eleId"] = "pwd1";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //二次确认弹出
        //弹出新密码输入框
        appUtils.bindEvent($(_pageId + " #new_password_repeat"), function (event) {
            event.stopPropagation();
            $(_pageId + " #new_password_repeat .cursor-bink").show()
            $(_pageId + " #new_password .cursor-bink").hide()
            moneyboardEvent1()
            // $(_pageId + " #pwd1").val('');
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "login_setPassword";
            param["eleId"] = "pwd2";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
        });
        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            //检查登录密码
            var pwd1 = $(_pageId + " #pwd1").val();
            var pwd2 = $(_pageId + " #pwd2").val();
            if (!checkInput(pwd1, pwd2)) {
                return;
            }
            // //请求reqFun9101019 设置登录密码
            // let data = {
            //     new_login_pwd:pwd1,
            //     registered_mobile:userInfo.mobileWhole,
            //     sms_mobile:userInfo.mobileWhole,
            // }
            //密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                newPwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, pwd1);
                var param = {
                    new_login_pwd: newPwd,
                    sms_mobile: userInfo.mobileWhole,
                    registered_mobile: userInfo.mobileWhole
                }
                service.reqFun9101019(param, function (data) {
                    if (data.error_no == "0") {
                        userInfo.login_pwd_state = '1';
                        ut.saveUserInf(userInfo);
                        pageBack();
                    }
                }, {isLastReq: false});
            }, {isLastReq: false});
            // param.source = '1';//app用户自主注册
            //邀请码为展示状态,需用户填写,需校验邀请码的有效性
            // if(label_show == "1"){
            //     var label = $(_pageId + " #labelId").val();
            //     if(!label){
            //         layerUtils.iAlert("邀请码不能为空");
            //         return false;
            //     }
            //     var channelCode = common.getLocalStorage("download_channel_code");
            //     if(channelCode == 'yh_jjdx') channelCode = 'yh'
            //     service.reqFun102097({prod_sub_type: channelCode, label_id: label}, function(data) {
            //         if(data.error_no != 0) {
            //             layerUtils.iLoading(false);
            //             layerUtils.iAlert(data.error_info);
            //             return;
            //         }
            //         var result = data.results[0];
            //         if(result.match == "0") {
            //             layerUtils.iLoading(false);
            //             layerUtils.iAlert("邀请码不匹配，请重新输入");
            //             return;
            //         }
            //         //邀请码匹配,验证手机验证码
            //         registerAccount(param);
            //     }, {isLastReq: false});
            // }else{
        	//验证手机验证码
        	// registerAccount(param);
            // }
            
        });
    }
    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iAlert("登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iAlert("确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iAlert("两次密码不相同");
            return false;
        }
        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        return true;
    }
    /**
     * 商城用户注册
     *   mobile: 手机号,
     *   sys_trans_pwd: 密码,
     *   yqrPhoneNum:邀请人电话
     */
     function registerAccount(paramObj) {
        paramObj.labelId = $(_pageId + " #labelId").val();
        paramObj.custCnl = common.getLocalStorage("download_channel_code");
        service.reqFun101001(paramObj, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + " #pwd1").val('')
                $(_pageId + " #pwd2").val('')
                $(_pageId + " #new_password .cursor-bink").hide()
                $(_pageId + " #new_password_repeat .cursor-bink").hide()
                $(_pageId + " #labelId").val();
                $(_pageId + " #new_password .placeholderPsd").css('color','#aaa');
                $(_pageId + " #new_password .cursor-bink").css("left",'1rem');
                $(_pageId + " #new_password_repeat .cursor-bink").css("left",'1rem');
                $(_pageId + " #new_password_repeat .placeholderPsd").css('color','#aaa');
                $(_pageId + " #new_password .placeholderPsd").html('16位字母、数字、字符组合');
                $(_pageId + " #new_password_repeat .placeholderPsd").html('请输入密码');
                //对登录密码进行加密*/
                var pwdCallBack = function (pwdRSAStr) {
                    paramObj.login_pwd = pwdRSAStr;
                    paramObj.login_acc = paramObj.registered_mobile;
                    paramObj.type = "0";
                    // 存登录号 和 登录加密密码 方便手势密码
                    var platform = require("gconfig").platform;
                    if (platform != "0") {
                        var paramlist = {
                            funcNo: "50042",
                            key: "account_password",
                            isEncrypt: "1",
                            value: paramObj.registered_mobile + "_" + pwdRSAStr
                        };
                        external.callMessage(paramlist);
                    }
                    //获取token
                    var param = {
                        funcNo: "50041",
                        key: "deviceTokenKey"
                    };
                    if (platform != "0") {
                        var data = external.callMessage(param);
                        var device_token = data.results[0].value;
                        paramObj["device_token"] = device_token;
                    }
                    mobile_save = paramObj.registered_mobile;
                    service.reqFun101018(paramObj, loginCallBackFun);
                };
                common.rsaEncrypt(paramObj.login_pwd, pwdCallBack);
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(error_info);
            }
        }, {"isLastReq": false});
    }
    //登录回调函数
    function loginCallBackFun(resultVo) {
        var error_no = resultVo.error_no;
        var error_info_login = resultVo.error_info;
        if (error_no == "0") {
            var results = resultVo.results[0];
            common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
            common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
            common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
            common.setLocalStorage("snowballMarketShow",'');
            sessionStorage.pop_up_otification = "";
            common.setLocalStorage("mobileWhole", results.mobileWhole);//获取完整手机号保存到本地
            // 存储是否是异常客户-开户审核中
            appUtils.setSStorageInfo("is_open_acct_excp",results.is_open_acct_excp)
            appUtils.setSStorageInfo("isAuthenticated", results.mobile + "@|@|@" + new Date().getTime());
            ut.saveUserInf(resultVo.results[0]);
            common.setLocalStorage("userChooseRefresh",'1'); //刷新用户版本
            common.setLocalStorage("userChooseVerson",''); //置空用户选择版本
            //登录后保存用户场景
            common.setLocalStorage("scene_code", results.scene_code);
            //存储渠道代码到本地
            common.setLocalStorage("download_channel_code", results.custLabelCnlCode);
            //存储用户场景首页列表模板ID
            common.setLocalStorage("scene_template_id", results.scene_template_id);
            tools.loginQy()
            //银行账户已冻结 （N：正常; C：销户; F：冻结）
            if (results.custStatus == "F") {
                layerUtils.iMsg(-1, "账户已冻结，请联系" + require("gconfig").global.custServiceTel);
            }
            appUtils.pageInit("login/userRegistered", "login/userRegisteredSuccess", {});
        } else {
            layerUtils.iAlert(error_info_login);
        }
    }
    function destroy() {
        
        service.destroy();
        monkeywords.close();
        $(_pageId + " #labelId_div").hide();
        $(_pageId + " #labelId").val();
        $(_pageId + " input").val("");
        label_show = ''
        $(_pageId + " #pwd1").val('')
        $(_pageId + " #pwd2").val('')
        $(_pageId + " #new_password .cursor-bink").hide()
        $(_pageId + " #new_password_repeat .cursor-bink").hide()
        $(_pageId + " #labelId").val();
        $(_pageId + " #new_password .placeholderPsd").css('color','#aaa');
        $(_pageId + " #new_password .cursor-bink").css("left",'1rem');
        $(_pageId + " #new_password_repeat .cursor-bink").css("left",'1rem');
        $(_pageId + " #new_password_repeat .placeholderPsd").css('color','#aaa');
        $(_pageId + " #new_password .placeholderPsd").html('16位字母、数字、字符组合');
        $(_pageId + " #new_password_repeat .placeholderPsd").html('请输入密码');
    }
    function pageBack() {
        $(_pageId + " #new_password .cursor-bink").hide()
        $(_pageId + " #new_password_repeat .cursor-bink").hide()
        monkeywords.close();
        appUtils.pageBack();
    }
    var setPassword = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setPassword;
});
