//源晖-银行卡交易结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageUrl = "yuanhui/purchaseResult",
        tools = require("../common/tools"),
        _pageId = "#yuanhui_purchaseResult ";
    var ut = require("../common/userUtil");
    function init() {
    	var productInfo = appUtils.getSStorageInfo("productInfo") ;
    	var endTime = productInfo.issue_end_time;
    	$(_pageId + " .endTime").html(tools.FormatDateText(endTime.substr(4, 4)) + tools.ftime(endTime.substr(8, 4), ':') );
    }

    //绑定事件
    function bindPageEvent() {
        //汇款账号
        appUtils.bindEvent($(_pageId + ".done_btn"), function () {
            var routerList = "";
        	if(ut.getUserInf().custLabelCnlCode == "yh"){
                routerList = ["yuanhui/userIndexs", "yuanhui/myAccount","yuanhui/hold"];
        	} else {
                routerList = ["login/userIndexs", "account/myAccount","highEnd/hold"];
        	}
        	appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit(_pageUrl, "yuanhui/payRecharge");
        })
    }
 
    function destroy() {
    	$(_pageId + " .endTime").html("--");
    }


    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
