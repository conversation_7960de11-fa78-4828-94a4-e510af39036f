// 业绩表现
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        cfdUtils = require("cfdUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        tools = require("../common/tools"),
        common = require("common"),
        vIscroll = {
            "scroll": null,
            "_init": false
        },
        _pageId = "#thfund_HAPerformanceList";

    var _pageCode = "thfund/HAPerformanceList";
    var _fund_code = "";

    function init() {
        _fund_code = appUtils.getSStorageInfo("jjbFundCode") || appUtils.getSStorageInfo("fund_code");
        //页面埋点初始化
        tools.initPagePointData({fundCode:_fund_code});
        //获取业绩表现
        getPerformance();
        var productInfo = appUtils.getSStorageInfo("productInfo_jjb") || appUtils.getSStorageInfo("productInfo");

        tools.initFundBtn(productInfo, _pageId);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //获取业绩表现
    function getPerformance() {
        var prams = {
            fund_code: _fund_code,
        }
        service.reqFun102007(prams, function (data) {
            if (data.error_no == 0) {
                if (data.results.length == 0) {
                    return;
                }

                var results = data.results[0];
                var dateName = {
                    "week": "近一周",
                    "month": "近一月",
                    "season": "近三月",
                    "six_month": "近半年",
                    "year": "近一年",
                    "two_year": "近两年",
                    "three_year": "近三年",
                    "this_year": "今年以来",
                    "found": "成立以来",
                }
                var dataArr = ["近一周", "近一月", "近三月", "近半年", "近一年", "近两年", "近三年", "今年以来", "成立以来"];
                var numData = [];
                numData.push(results.week);
                numData.push(results.month);
                numData.push(results.season);
                numData.push(results.six_month);
                numData.push(results.year);
                numData.push(results.two_year);
                numData.push(results.three_year);
                numData.push(results.this_year);
                numData.push(results.found);
                var html = "";

                for (var i = 0; i < numData.length; i++) {
                    //空数据处理
                    numData[i] = tools.FormatNull(numData[i]);
                    var rateClass = "";

                    if (!numData[i]) {
                        break;
                    }

                    if (numData[i].rate < 0) {
                        rateClass = "text_green";
                    } else if(numData[i].rate > 0){
                        rateClass = "text_red";
                    } else {
                        rateClass = "text_grey";
                    }

                    if (numData[i].rate != "--") {
                        numData[i].rate = numData[i].rate;
                        numData[i].rate = (+numData[i].rate).toFixed(2) + "%";
                    }

                    html += '<div class="item">' +
                        '<span>' + dataArr[i] + '</span>' +
                        '<span class=' + rateClass + '>' + numData[i].rate + '</span>' +
                        '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #performanceContent .list_content").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
