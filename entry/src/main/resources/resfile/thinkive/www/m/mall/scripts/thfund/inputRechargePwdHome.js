// 晋金宝充值主界面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        common = require("common");
    var _pageId = "#thfund_inputRechargePwdHome";
    var _page_code = "thfund/inputRechargePwdHome";
    var tools = require('../common/tools');
    var ut = require("../common/userUtil");

    function init() {
        getFundAssetInfo();
        getBankAssetInfo()
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //晋金宝充值
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消");
                    return;
                }
                appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
            });
        });
        //银行电子账户充值
        appUtils.bindEvent($(_pageId + " .bankRecharge"), function () {
            appUtils.setSStorageInfo("bankEntrytype", "recharge");
            appUtils.pageInit(_page_code, "bank/bankElectron");
        });

    }

    function getFundAssetInfo() {
        service.reqFun101999({}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                $(_pageId + ' .mfundAssets').html(tools.fmoney(results.mfundAssets));
            } else {
                $(_pageId + ' .mfundAssets').html("--");
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getBankAssetInfo() {
        service.reqFun151122({}, function (data) { //查询银行总余额
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                $(_pageId + ' .bankfundAssets').html("--");
                return;
            }
            var results = data.results[0];
            $(_pageId + ' .bankfundAssets').html(tools.fmoney(results.total));

        })
    }

    function destroy() {
        $(_pageId + ' .bankfundAssets').html("--");
        $(_pageId + ' .mfundAssets').html("--");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailCompany = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailCompany;
});
