// 分享链接注册
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#yuanhui_register ";
    var _pageCode = "yuanhui/register";
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");
    var invitationMobile;
    var endecryptUtils = require("endecryptUtils");

    function init() {
        // 解禁系统键盘
        common.systemKeybord();
        //banner轮播
        tools.guanggao({_pageId: _pageId, group_id: "26"});
        //刷新图形验证码
        setImgCode();
        sms_mobile.init(_pageId);
    }

    //绑定事件
    function bindPageEvent() {
        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");

//            // 是否是有效内链
            if (file_type == "0" && file_state == "1" && url) {
                appUtils.pageInit(_pageCode, url, {});
                return;
            }
            // 是否是有效外链接
            if (file_type == "1" && file_state == "1" && url) {
                window.open(url);
                return;
            }
        }, 'click');

        /* 切换图形验证码 */
        appUtils.bindEvent($(_pageId + " #getCode"), function () {
            setImgCode();
        });
        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #registered"), function () {
            //检查手机号
            var phone = $.trim($(_pageId + " #phoneNum").val());
            if (validatorUtil.isEmpty(phone)) {
                layerUtils.iMsg(-1, "请输入手机号码");
                return;
            }
            if (!validatorUtil.isMobile(phone)) {
                layerUtils.iMsg(-1, "请确定您输入的手机号是否正确");
                return;
            }
            var recommd = $(_pageId + " #recommd").val(); // 邀请码

            //检查登录密码
            var pwd1 = $(_pageId + " #pwd1").val();
            if (!(checkInput(pwd1))) {
                return;
            }
            //验证图形验证码input
            var imgCode = $(_pageId + " #tuxingCode").val();
            if (!imgCode) {
                layerUtils.iMsg(-1, "请输入图形验证码");
                return;
            }

            //检查验证码获取按钮
            var isSend = $(_pageId + " #getYzm").attr("data-state");
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            // // //检查验证码input
            var mobile_vf_code = $.trim($(_pageId + " #verificationCode").val());//验证码
            if (validatorUtil.isEmpty(mobile_vf_code)) {
                layerUtils.iMsg(-1, "请输入手机验证码");
                return;
            }
            if (!validatorUtil.isNumeric(mobile_vf_code)) {
                layerUtils.iMsg(-1, "验证码格式为纯数字");
                return;
            }
            if (validatorUtil.isEmpty(recommd)) {
                layerUtils.iMsg(-1, "邀请码不能为空");
                return;
            }

            //注册
            var param = {
                registered_mobile: phone,
                login_pwd: pwd1,
                sms_mobile: phone,
                sms_code: mobile_vf_code,
                labelId: recommd,
                custCnl: "yh",
                source:'1'
            };
            service.reqFun102097({prod_sub_type: "93", label_id: recommd}, function(data) {
                if(data.error_no != 0) {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var result = data.results[0];
                if(result.match == "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert("邀请码不匹配，请重新输入");
                    return;
                }
                //验证手机验证码
                registerAccount(param);
            }, {isLastReq: false});
        });

        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            //验证手机号input
            var mobile = $(_pageId + " #phoneNum").val();
            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            var pwd1 = $(_pageId + " #pwd1").val();
            if (!(checkInput(pwd1))) {
                return;
            }
            //验证图形验证码input
            var imgCode = $(_pageId + " #tuxingCode").val();
            if (!imgCode) {
                layerUtils.iMsg(-1, "请输入图形验证码");
                return;
            }
            //验证获取验证码按钮可用性
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            var param = {
                mobile_phone: mobile,
                type: common.sms_type.yuanhuiRegister,
                send_type: "0",
                ticket: imgCode,
            };
            sms_mobile.sendRegisterPhoneCode(param, function() {
                setImgCode();
            });
        });
    }

    /**
     * 商城用户注册
     *   mobile: 手机号,
     *   sys_trans_pwd: 密码,
     *   yqrPhoneNum:邀请人电话
     */
    function registerAccount(paramObj) {
        service.reqFun101001(paramObj, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            //验证码重置
            $(_pageId + " #verificationCode").val("");//验证码
            setImgCode()//刷新图像验证码
            $(_pageId + " #tuxingCode").val("");
            sms_mobile.clear();
            if (error_no == "0") {
                common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
                common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
                common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
                layerUtils.iAlert("恭喜您，注册成功！", "", function () {
                    appUtils.pageInit(_pageCode, "appdown/index");
                }, "立即下载");
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert(error_info);
            }
        }, {"isLastReq": false});
    }

    function destroy() {
        $(_pageId + " input").val("");
        service.destroy();
        sms_mobile.destroy();
    }

    function pageBack() {
    }

    //刷新图形验证码
    function setImgCode() {
        service.reqFun1100005({}, function (data) {
            if (data.error_no == 0) {
                var base64 = data.results[0].base64;
                $(_pageId + " #getCode img").attr("src", base64);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iAlert("登录密码不能为空");
            return false;
        }
        if (pwd1.length < 6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        return true;
    }

    var userInfo = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userInfo;
});
