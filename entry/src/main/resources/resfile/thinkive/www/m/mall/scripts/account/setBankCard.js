// 绑定银行卡--输入个人信息
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        
        pageCode = "account/setBankCard",
        _pageId = "#account_setBankCard";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var saveImgNum = 0;
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    require("../../js/prov_city.js");
    require("../../js/city.js");
    require("../../js/picker.min.js");
    var first = []; /* 省，直辖市 */
    var second = []; /* 市 */
    var third = []; /* 镇 */
    var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */
    var checked = [0, 0, 0]; /* 已选选项 */
    var picker = ""; //地址选择器

    function init() {
        common.systemKeybord(); // 解禁系统键盘
        //清空页面的信息
        if (!appUtils.getSStorageInfo("idCardInfo")) {
            appUtils.clearSStorage("bankAccInfo");
            $(_pageId + " .cust_address").hide()
            $(_pageId + " input").val("");
            $(_pageId + " #realName").attr("disabled", "disabled");
            $(_pageId + " #idCard").attr("disabled", "disabled");
            $(_pageId + " #cust_address").attr("disabled", "disabled");
            $(_pageId + " .zm").removeAttr("isAllow");
            $(_pageId + " .fm").removeAttr("isAllow");
            $(_pageId + " #zm_img").attr("src", "./images/sfz_01.png");
            $(_pageId + " #fm_img").attr("src", "./images/sfz_02.png");
            $(_pageId + " #realName").attr("placeholder",'');
            $(_pageId + " #idCard").attr("placeholder",'');
            // $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
            $(_pageId + " .noIdCard").hide();
            $(_pageId + " .haveIdCard").show();
            // queryJob();
            // queryIncome();
            // selectorArea();
        } else {
            var idCardInfo = appUtils.getSStorageInfo("idCardInfo");
            appUtils.clearSStorage("idCardInfo");
            $(_pageId + " #idCard").val(idCardInfo.idCard).removeAttr("disabled");
            $(_pageId + " #realName").val(idCardInfo.realname).removeAttr("disabled");
            $(_pageId + " #cust_address").val(idCardInfo.cust_address)
            $(_pageId + " #sex").val(idCardInfo.sex);
            $(_pageId + " #vaild_date").val(idCardInfo.vaild_date);
            // $(_pageId + " .rule_check #xuanzeqi i").addClass("active");
            if($(_pageId + " #cust_address").val().length < 10){
                $(_pageId + " .cust_address").show()
                $(_pageId + " #cust_address").removeAttr("disabled");
            }
            if(idCardInfo.isNeedIdCard){
                //展示上传身份证
                $(_pageId + " .cust_address").hide()
                $(_pageId + " .noIdCard").hide();
                $(_pageId + " .haveIdCard").show();
            }else{
                $(_pageId + " .haveIdCard").hide();
                $(_pageId + " .noIdCard").show();
                $(_pageId + " .cust_address").show()
                $(_pageId + " #cust_address").removeAttr("disabled");
            }
            // queryJob(idCardInfo.vocation_code);
            // queryIncome(idCardInfo.year_income);
            // selectorArea(idCardInfo.living_address);
        }
    }

    //绑定事件
    function bindPageEvent() {
        //2017-7-10 jiaxr 添加客服功能
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            // var param = {};
            // param["backPageUrl"] = pageCode;
            // appUtils.pageInit(pageCode, "customerService/onlineCustomerService", param);
            tools.saveAlbum(pageCode)
        });
        //点击稍后上传
        appUtils.bindEvent($(_pageId + " #waitUpload"), function () {
            $(_pageId + " #cust_address").attr("placeholder",'请输入身份证地址');
            $(_pageId + " #cust_address").removeAttr("disabled");
            $(_pageId + " #idCard").removeAttr("disabled");
            $(_pageId + " #realName").removeAttr("disabled");
            $(_pageId + " .cust_address").show()
            $(_pageId + " #realName").attr("placeholder",'请输入真实姓名');
            $(_pageId + " #idCard").attr("placeholder",'请输入身份证号');
            //重置数据
            $(_pageId + " input").val("");
            $(_pageId + " .zm").removeAttr("isAllow");
            $(_pageId + " .fm").removeAttr("isAllow");
            $(_pageId + " #zm_img").attr("src", "./images/sfz_01.png");
            $(_pageId + " #fm_img").attr("src", "./images/sfz_02.png");
            $(_pageId + " .haveIdCard").hide();
            $(_pageId + " .noIdCard").show();
            
        });
        //点击上传身份证
        appUtils.bindEvent($(_pageId + " #uploadId"), function () {
            $(_pageId + " #cust_address").attr("placeholder",'证件地址识别有误，请手动输入');
            $(_pageId + " .cust_address").hide()
            $(_pageId + " #realName").attr("placeholder",'');
            $(_pageId + " #idCard").attr("placeholder",'');
            //重置数据
            $(_pageId + " input").val("");
            $(_pageId + " #realName").attr("disabled", "disabled");
            $(_pageId + " #idCard").attr("disabled", "disabled");
            $(_pageId + " #cust_address").attr("disabled", "disabled");
            $(_pageId + " .zm").removeAttr("isAllow");
            $(_pageId + " .fm").removeAttr("isAllow");
            $(_pageId + " #zm_img").attr("src", "./images/sfz_01.png");
            $(_pageId + " #fm_img").attr("src", "./images/sfz_02.png");
            $(_pageId + " .noIdCard").hide();
            $(_pageId + " .haveIdCard").show();
        });
        
        //操作指南
        appUtils.bindEvent($(_pageId + " .operate_guide"), function () {
            appUtils.pageInit(pageCode, "guide/uploadGuide");
        });
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        })
        //点击下一步
        appUtils.bindEvent($(_pageId + " #xyb"), function () {
            //是否需要判断身份证
            let isNeedIdCard = $(_pageId + " .noIdCard").is(":hidden");
            // console.log(isNeedIdCard)
            if (!$(_pageId + " .zm").attr("isAllow") && isNeedIdCard) {
                layerUtils.iMsg(-1, "请拍摄身份证人像面");
                return;
            }
            if (!$(_pageId + " .fm").attr("isAllow") && isNeedIdCard) {
                layerUtils.iMsg(-1, "请拍摄身份证国徽面");
                return;
            }
            var realname = $(_pageId + " #realName").val();
            var idCard = $(_pageId + " #idCard").val();

            var sex = $(_pageId + " #sex").val();
            var cust_address = $(_pageId + " #cust_address").val();
            // var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议         
            // var income_id = $(_pageId + " #income").attr("data-id");
            // if (income_id == "04") {
            //     var income_money = $(_pageId + " #income").val();
            // } else {
            //     var income_money = $(_pageId + " #income").attr("data-money");
            // }
            var vaild_date = $(_pageId + " #vaild_date").val();
            if (new Date(vaild_date + " 23:59:59").getTime() < new Date().getTime()) {
                layerUtils.iAlert("身份证已过期，请重新上传");
                return;
            }
            if ($.trim(realname) == "" || $.trim(realname) == null) {
                layerUtils.iMsg(-1, "姓名不能为空");
                return;
            }
            if (!checkName($.trim(realname))) {
                layerUtils.iMsg(-1, "请输入真实姓名");
                return;
            }
            if ($.trim(idCard).length <= 0) {
                layerUtils.iMsg(-1, "证件号码不能为空");
                return;
            }
            if (!validatorUtil.isCardID(idCard)) {
                layerUtils.iMsg(-1, "证件号码格式错误");
                return;
            }
            if (!isGrown_up(idCard)) {
                layerUtils.iMsg(-1, "身份证年龄未满18周岁");
                return;
            }
            if (cust_address.length < 10){
                layerUtils.iAlert("请输入十个字及以上的地址");
                return 
            }
            // if (validatorUtil.isEmpty($(_pageId + " #occp").val())) {
            //     layerUtils.iMsg(-1, "请选择职业");
            //     return;
            // }
            // if (validatorUtil.isEmpty($(_pageId + " #income").val())) {
            //     layerUtils.iMsg(-1, "请选择年收入");
            //     return;
            // }
            // if (validatorUtil.isEmpty($(_pageId + " #live_address").val())) {
            //     layerUtils.iMsg(-1, "请选择地址");
            //     return;
            // }
            // if (classname != "active") {
            //     layerUtils.iAlert("请确认个人税收居民类型");
            //     return;
            // }
            //处理msgFunction缓存问题
            if(vaild_date.split("-").length == 2) {
                vaild_date = vaild_date.split("-")[1].replace(/\./g, "/");
            }
            layerUtils.iLoading(true);
            // console.log(vaild_date)
            var param = {
                realname: realname.replace(/\s/g,""),
                idCard: idCard.replace(/\s/g,""),
                vaild_date: vaild_date ? vaild_date.replace(/\s/g,"") : '20351231',
                cust_address: cust_address.replace(/\s/g,""),
                sex: sex ? sex.replace(/\s/g,"") : '',
                isNeedIdCard:isNeedIdCard
                // vocation_code: {name: $(_pageId + " #occp").val(), id: $(_pageId + " #occp").attr("data-value")},
                // year_income: {
                //     money: income_money,
                //     id: $(_pageId + " #income").attr("data-id"),
                //     value: $(_pageId + " #income").attr("data-value")
                // },
                // living_address:{name: $(_pageId + " #live_address").val(),code:$(_pageId + " #live_address").attr("data-code")}
            }
            
            //验证身份证黑名单
            service.reqFun102064({cert_no: idCard}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                service.reqFun102079({
                    idcard: idCard, 
                    name: realname,
                }, function (data) { //身份证、姓名是否一致
                    //特殊处理，存储错误码，描述，弹窗给用户确认
                    if (data.error_no != "0") {
                        if(data.error_no == "2" || data.error_no == "103"){
                            let setBankInfo = {
                                error_reason:data.error_info
                            }
                            layerUtils.iLoading(false);
                            appUtils.setSStorageInfo("setBankInfo",setBankInfo)
                            layerUtils.iConfirm("您的身份资料可能有误是否进行下一步？", function () {
                                //将用户的部分信息保存到session中
                                appUtils.setSStorageInfo("idCardInfo", param);
                                if(!isNeedIdCard) return appUtils.pageInit(pageCode, "account/setBankCardInfo");
                                saveIdCard(param);
                            }, function () {
                                //点击取消无操作
                            }, "是", "否");
                            
                        }else{
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                    }else{
                        appUtils.setSStorageInfo("setBankInfo",{})
                        //将用户的部分信息保存到session中
                        appUtils.setSStorageInfo("idCardInfo", param);
                        if(!isNeedIdCard) return appUtils.pageInit(pageCode, "account/setBankCardInfo");
                        //留存身份证正反面
                        saveIdCard(param);
                    }
                }, {isLastReq: false})
            }, {isLastReq: false})
        });

        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            var external = require("external");
            var Param = {
                "funcNo": "60302",
                "moduleName": "mall"
            };
            external.callMessage(Param);
        });
        // 身份证正面拍照
        appUtils.bindEvent($(_pageId + " .zm"), function () {
            tools.openCamera("zm");
        });
        // 身份证反面拍照
        appUtils.bindEvent($(_pageId + " .fm"), function () {
            tools.openCamera("zm");
        });
        // 点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active");
                $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
            } else {
                $(this).find("i").addClass("active");
                $(_pageId + " #next").css({backgroundColor: "#E5433B"});

            }
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId + " #moneyBox"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "account_setBankCard";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);

        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " .input_box2"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "account_setBankCard";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
    }

    function isGrown_up(cid) {
        var year;
        if (cid.length == 18) {
            year = parseInt(cid.substring(6, 10));
        } else {
            year = parseInt("19" + cid.substring(6, 8));
        }
        var day1 = new Date(parseFloat(year) + 18 + "/" + cid.substring(10, 12) + "/" + cid.substring(12, 14) + " 00:00:00"); //出生日期 + 18年
        var day2 = new Date();
        if (day1 >= day2) {
            return false;
        }
        return true;
    }

    /**
     * 查询职业信息
     * */
    function queryJob(info) {
        var param = {code: 'occupation_jz'}
        if ($(".mobileSelect").length > 0) {
            return;
        }
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                if (info) {
                    $(_pageId + " #occp").val(info.name);
                    $(_pageId + " #occp").attr("data-value", info.id);
                } else {
                    $(_pageId + " #occp").val();
                    $(_pageId + " #occp").attr("data-value", "");
                }
                tools.mobileSelect({
                    trigger: _pageId + " #occp",
                    title: "请选择职业",
                    dataArr: dataArr,
                    callback: function (data) {
                        $(_pageId + " #occp").val(data[0].value);
                        $(_pageId + " #occp").attr("data-value", data[0].id);
                    }
                })
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })
    }
 
    /**
     * 查询年收入信息
     * */
    function queryIncome(info) {
    	 dataArr_income = [
    		 {id: "01", value: "0~10（含）", money: "10"},
             {id: "02", value: "10~50（含）", money: "50"},
             {id: "03", value: "50~100（含）", money: "100"},
             {id: "04", value: "其他", money: ""}
    	 ]
        var position = 0;
        if (info) {
        	if(info.id == "04") {
        		$(_pageId + " #income").val(info.money);
        	} else {
        		$(_pageId + " #income").val(info.value);
        	}
        	$(_pageId + " #income").attr("data-id", info.id).attr("data-value", info.value).attr("data-money", info.money);
        } else {
            $(_pageId + " #income").val();
            $(_pageId + " #income").attr("data-id", "");
        }
        tools.mobileSelect({
            trigger: _pageId + " #income",
            title: "请选择年收入（万元）",
            dataArr: dataArr_income,
            callback: function (data) {
                $(_pageId + " #income").val(data[0].value);
                $(_pageId + " #income").attr("data-id", data[0].id);
                $(_pageId + " #income").attr("data-value", data[0].value);
                $(_pageId + " #income").attr("data-money", data[0].money);
                if (data[0].id == "04") {
                    $(_pageId + " .pop_layer1").show();
                    $(_pageId + " .password_box").show();
                    event.stopPropagation();
                    $(_pageId + " #srje").val('');
            		$(_pageId + " #inputspanid span").html('');
                    //键盘事件
                    moneyboardEvent();
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "account_setBankCard";
                    param["eleId"] = "srje";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "3";
                    require("external").callMessage(param);
                    var srje = $(_pageId + " #srje").val();
                    $(_pageId + " #income").val(srje);
                    $(_pageId + " #income").attr("data-id", "04").attr("data-money", srje).attr("data-value", dataArr_income[3].value);
                    $(_pageId + " #inputspanid span").html(srje);
                }
            }
        });

    }

    //金额输入数字键盘
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #srje"),
            endcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                } else if(moneys<=0){
                	layerUtils.iAlert("请输入大于零的金额");
                }else {
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .password_box").hide();
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}$/.test(curVal))) {
                    moneys = moneys.substring(0, curVal.length - 1)
                }
                $(_pageId + " #income").val(Number(moneys));
                $(_pageId + " #income").attr("data-id", "04").attr("data-value", dataArr_income[3].value).attr("data-money", moneys);
                $(_pageId + " #inputspanid span").html(Number(moneys));
                $(_pageId + " #srje").val(Number(moneys));


            }
        })
    }
    /**
     * 居住地址选择
     */
    function selectorArea(info) {
//        var nameEl = document.getElementById(id);

    	 if (info) {
             $(_pageId + " #live_address").val(info.name);
             $(_pageId + " #live_address").attr("data-code", info.code);
         } 
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            value = text1 + ' ' + text2 + ' ' + text3;
            // $(_pageId + " #live_address").val(value);
            var code1 = first[selectedIndex[0]].code;
            var code2 = second[selectedIndex[1]].code;
            var code3 = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            var code = code1 + ' ' + code2 + ' ' + code3;
            $(_pageId + " #live_address").attr("data-code", code);
            let flag = await tools.is_region(code1,code2,code3,city)
            if(flag){
                $(_pageId + " #live_address").val(value);
            }else{
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }
            
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
    }


    //校验姓名
    var checkName = function (name) {
        var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/;
        var reg1 = /^[\u4E00-\u9FA5\uf900-\ufa2d.s]{2,20}$/;
        if (reg.test(name) || reg1.test(name)) return true;
        return false;
    }


    //留存身份证正反面
    function saveIdCard(param) {
        //身份证正面，去掉base64编码 头
        var base64ZM = $(_pageId + " #zm_img").attr("src");
        if (base64ZM.indexOf("data:image/jpeg;base64,") != -1) {
            base64ZM = base64ZM.replace("data:image/jpeg;base64,", "");
        }
        if (base64ZM.indexOf("data:image/png;base64,") != -1) {
            base64ZM = base64ZM.replace("data:image/png;base64,", "");
        }

        //身份证反面，去掉base64编码 头
        var base64FM = $(_pageId + " #fm_img").attr("src");
        if (base64FM.indexOf("data:image/jpeg;base64,") != -1) {
            base64FM = base64FM.replace("data:image/jpeg;base64,", "");
        }
        if (base64FM.indexOf("data:image/png;base64,") != -1) {
            base64FM = base64FM.replace("data:image/png;base64,", "");
        }


        var paramZM = {
            "base_data": base64ZM,
            "flag": "0",
        }
        let idInfo = {}
        service.reqFun199011(paramZM, function (data) {
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            idInfo.front_url = data.results[0].url    //正面
            var paramFM = {
                "base_data": base64FM,
                "flag": "1",
            }
            service.reqFun199011(paramFM, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                idInfo.back_url = data.results[0].url    //反面
                appUtils.setSStorageInfo("idInfo", idInfo);
                //将用户的部分信息保存到session中
                appUtils.setSStorageInfo("idCardInfo", param);
                appUtils.pageInit(pageCode, "account/setBankCardInfo");
            })
        }, {isLastReq: false})

    }
      

    function destroy() {
        saveImgNum = 0;
        $(_pageId + " .camera_pop_layer").remove();
        $(_pageId + " input").val("");
        $(_pageId + " #idCard").attr("disabled","disabled");
        $(_pageId + " #realName").attr("disabled","disabled");
        $(_pageId + " #cust_address").attr("disabled", "disabled");
        $(".mobileSelect").remove();
        $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        $(_pageId + " #srje").val("");
        $(".picker").remove();
        $(_pageId + " #live_address").val("");
        first = [];
        second = [];
        third = [];
    }

    function pageBack() {
        appUtils.clearSStorage("idCardInfo");
        appUtils.clearSStorage("bankAccInfo");
        var routerList = appUtils.getSStorageInfo("routerList");
        if (routerList.indexOf("account/myAccountNoBind") > -1) {
            appUtils.pageBack();
        } else { //直接注册登陆返回首页
            appUtils.pageInit(pageCode, "login/userIndexs", {});
        }
    }

    var setBankCard = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setBankCard;
});
