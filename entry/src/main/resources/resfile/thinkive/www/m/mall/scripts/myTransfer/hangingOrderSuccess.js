// 交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#myTransfer_hangingOrderSuccess ";
        _pageUrl = "myTransfer/hangingOrderSuccess";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
    }

    function bindPageEvent() {
        //完成
        appUtils.bindEvent($(_pageId + " .btn_next"), function () {
            pageBack();
        });
    }
 
    
    function pageBack() {
        var routerList = appUtils.getSStorageInfo("routerList")
        routerList = ["login/userIndexs","account/myAccount"];
        appUtils.setSStorageInfo("routerList",routerList);
        appUtils.setSStorageInfo("_cust_fund_type", '0');
        appUtils.pageInit(_pageUrl, "myTransfer/index");
    }
    function destroy() {
    	tools.recordEventData('4','destroy','页面销毁');
    }
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
