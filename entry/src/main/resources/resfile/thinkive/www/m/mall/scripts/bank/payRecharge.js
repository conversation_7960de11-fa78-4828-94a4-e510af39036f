// 汇款充值
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        commTool = require("mall/scripts/common/commTool"),
        tools = require("../common/tools"),
        _pageId = "#bank_payRecharge ";
    var _pageCode = "bank/payRecharge";
    var ut = require("../common/userUtil");
    require("../common/clipboard.min.js");
    var user;
    var productInfo;
    var bankCode;
    var bankName;

    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);

        user = ut.getUserInf();
        productInfo = appUtils.getSStorageInfo("productInfo");


        copyContent("hmCopy");
        copyContent("zhCopy");
        copyContent("khhCopy");
        getAccountInfo();
    }

    function bindPageEvent() {

        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

        //打开手机银行
        appUtils.bindEvent($(_pageId + " .iphone_bank"), function () {
            tools.openBankApp(bankCode);
        });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .web_bank"), function () {
            layerUtils.iAlert("请登录" + bankName + "网站进行转账汇款");
        });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .counter_bank"), function () {
            layerUtils.iAlert("请您到" + bankName + "网点进行转账汇款");
        });

    }

    //查询电子银行信息
    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                // acct_no 电子银行    bank_acct_no 绑定银行
                bankCode = results.bd_bank_code;
                bankName = results.bd_bank_name;

                $(_pageId + " .bank_electron_name").text(results.bank_channel_name);
                $(_pageId + " #zh").text(results.acct_no);
                $(_pageId + " #zhCopy").attr("data-clipboard-text", results.acct_no);
                $(_pageId + " #hm").text(user.name);
                $(_pageId + " #hmCopy").attr("data-clipboard-text", user.name);
                $(_pageId + " #khh").text(results.acct_openbank_name);
                $(_pageId + " #khhCopy").attr("data-clipboard-text", results.acct_openbank_name);

                $(_pageId + " #bankName").html(bankName);
                $(_pageId + " #bank_act_int").html(results.bank_act_int);
                $(_pageId + " #bankCard").html(results.bank_acct_no.substr(-4));
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            layerUtils.iAlert("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #bankName").html("");
        $(_pageId + " #bankCard").html("");
        $(_pageId + " #zh").text("");
        $(_pageId + " #zhCopy").attr("data-clipboard-text", "");
        $(_pageId + " #hm").text("");
        $(_pageId + " #hmCopy").attr("data-clipboard-text", "");
        $(_pageId + " #khh").text("");
        $(_pageId + " #khhCopy").attr("data-clipboard-text", "");
        $(_pageId + " .bank_electron_name").text("");

    }


    var payRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = payRecharge;
});
