// 上传身份证照片
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        pageCode = "account/uploadIDCard",
        ut = require("../common/userUtil"),
        _pageId = "#account_uploadIDCard";
    var saveImgNum;
    var userInfo;
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    require("../../js/prov_city.js");
    require("../../js/city.js");
    require("../../js/picker.min.js");
    var first = []; /* 省，直辖市 */
    var second = []; /* 市 */
    var third = []; /* 镇 */
    var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */
    var checked = [0, 0, 0]; /* 已选选项 */
    var picker = ""; //地址选择器
    var live_province;
    var live_city;
    var live_county;
    var noUploadIdCard;

    function init() {
         //页面埋点初始化
        tools.initPagePointData();
        noUploadIdCard = appUtils.getSStorageInfo("noUploadIdCard");
        $(_pageId + " .cust_address").hide()
        userInfo = ut.getUserInf();
        saveImgNum = 0;
        //清空页面的信息
        $(_pageId + " .zm").removeAttr("isAllow");
        $(_pageId + " .fm").removeAttr("isAllow");
        $(_pageId + " #zm_img").attr("src", "./images/sfz_01.png");
        $(_pageId + " #fm_img").attr("src", "./images/sfz_02.png");
        if(noUploadIdCard == '1'){
            $(_pageId + " .noUpload").hide();
        }else{
            $(_pageId + " .noUpload").show();
        }
        queryJob();
        queryIncome()
        selectorArea();
        
    }

    //绑定事件
    function bindPageEvent() {
        //2017-7-10 jiaxr 添加客服功能
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = pageCode;
            tools.saveAlbum(pageCode,param)
        });
        //操作指南
        appUtils.bindEvent($(_pageId + " .operate_guide"), function () {
            appUtils.pageInit(pageCode, "guide/uploadGuide");
        });
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        });
        appUtils.bindEvent($(_pageId + " #moneyBox"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "account_uploadIDCard";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);

        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " .input_box2"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "account_uploadIDCard";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //点击下一步
        appUtils.bindEvent($(_pageId + " #xyb"), function () {
            if (!$(_pageId + " .zm").attr("isAllow")) {
                layerUtils.iMsg(-1, "请拍摄身份证人像面");
                return;
            }
            if (!$(_pageId + " .fm").attr("isAllow")) {
                layerUtils.iMsg(-1, "请拍摄身份证国徽面");
                return;
            }
            var realname = $(_pageId + " #realName").val();
            var idCard = $(_pageId + " #idCard").val();
            var vaild_date = $(_pageId + " #vaild_date").val();
            if (new Date(vaild_date + " 23:59:59").getTime() < new Date().getTime()) {
                layerUtils.iAlert("身份证已过期，请重新上传");
                return;
            }
            var sex = $(_pageId + " #sex").val();
            var cust_address = $(_pageId + " #cust_address").val();
            if(cust_address.length < 10){
                return layerUtils.iAlert("请输入十个字及以上的地址");
            }
            if (realname != userInfo.name) {
                layerUtils.iMsg(-1, "请上传本人身份证");
                return;
            }
            if (idCard.substr(0, 4) != userInfo.identityNum.substr(0, 4) || idCard.substr(-4) != userInfo.identityNum.substr(-4)) {
                layerUtils.iMsg(-1, "请上传本人身份证");
                return;
            }
            var occupation = $(_pageId + " #ocp").attr("data-value");
            var year_income = $(_pageId + " #income").attr("data-money");
            if (validatorUtil.isEmpty(year_income)) {
                layerUtils.iMsg(-1, "请选择个人年收入");
                return;
            }
            if (validatorUtil.isEmpty(occupation)) {
                layerUtils.iMsg(-1, "请选择职业");
                return;
            }

            if (validatorUtil.isEmpty($(_pageId + " #live_address").val())) {
                layerUtils.iMsg(-1, "请选择居住地址");
                return;
            }
            
            var param = {
                "cert_no": idCard,
                "vaild_date": vaild_date,
                "cust_name": realname,
                "sex": sex,
                "address": cust_address,
                "occupation": occupation,
                "year_icome": year_income*10000 +"",
                "living_address_province":live_province,
                "living_address_city":live_city,
                "living_address_county":live_county
            }
            //留存身份证正反面
            saveIdCard(param);

        });

        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        // 身份证正面拍照
        appUtils.bindEvent($(_pageId + " .zm"), function () {
            appUtils.setSStorageInfo("ocrType", "upload");
            tools.openCamera("zm");
        });
        // 身份证反面拍照
        appUtils.bindEvent($(_pageId + " .fm"), function () {
            tools.openCamera("zm");
        });
    }

    //留存身份证正反面
    function saveIdCard(param) {
        //身份证正面，去掉base64编码 头
        var base64ZM = $(_pageId + " #zm_img").attr("src");
        //文件后缀
        if (base64ZM.indexOf("data:image/jpeg;base64,") != -1) {
            base64ZM = base64ZM.replace("data:image/jpeg;base64,", "");
        }
        if (base64ZM.indexOf("data:image/png;base64,") != -1) {
            base64ZM = base64ZM.replace("data:image/png;base64,", "");
        }

        //身份证反面，去掉base64编码 头
        var base64FM = $(_pageId + " #fm_img").attr("src");
        if (base64FM.indexOf("data:image/jpeg;base64,") != -1) {
            base64FM = base64FM.replace("data:image/jpeg;base64,", "");
        }
        if (base64FM.indexOf("data:image/png;base64,") != -1) {
            base64FM = base64FM.replace("data:image/png;base64,", "");
        }

        //发请求 留存身份证反面
        param.front = base64ZM;
        param.back = base64FM;
        param.registered_mobile = userInfo.mobile;
        param.bank_reserved_mobile = userInfo.bankReservedMobile;
        param.cert_type = "0";

        service.reqFun199013(param, function (data) {
            appUtils.clearSStorage("openLeavePage");
            if (data.error_no != "0") {
                appUtils.setSStorageInfo("reUploadIdCard", "0");
                layerUtils.iAlert(data.error_info,"", function() {
                });
                return;
            }
            appUtils.setSStorageInfo("reUploadIdCard", "1");
            saveCallBackFun();
            
        },{"isLastReq": false})
    }

    /**
     * 查询职业信息
     * */
    function queryJob() {
        var param = {code: 'occupation_jz'}
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                var duty = userInfo.duty;
                var position;
                if (duty) {
                    position = dataArr.findIndex(function (item) {
                        return duty == item.id;
                    });
                    $(_pageId + " #ocp").val(dataArr[position].value);
                    $(_pageId + " #ocp").attr("data-value", dataArr[position].id);
                } else {
                    position = 0;
                    $(_pageId + " #ocp").val();
                    $(_pageId + " #ocp").attr("data-value", "");
                }

                tools.mobileSelect({
                    trigger: _pageId + " #ocp",
                    title: "请选择职业",
                    dataArr: dataArr,
                    position: position + "",
                    callback: function (data, index) {
                        $(_pageId + " #ocp").val(data[0].value);
                        $(_pageId + " #ocp").attr("data-value", data[0].id);
                    }
                })
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })

    }

    /**
     * 查询年收入信息
     * */
    function queryIncome() {
        dataArr_income = [
            {id: "01", value: "0~10（含）", money: "10"},
            {id: "02", value: "10~50（含）", money: "50"},
            {id: "03", value: "50~100（含）", money: "100"},
            {id: "04", value: "其他", money: ""}
        ]
        var year_icome = userInfo.year_icome / 10000;
        var position;
        if(year_icome){
            var position = dataArr_income.findIndex(function (item) {
                return year_icome == item.money;
            });
            if(position == -1){
                position = 3;
                $(_pageId + " #income").val(year_icome);
                $(_pageId + " #income").attr("data-id", "04").attr("data-money", year_icome).attr("data-value", dataArr_income[3].value);
            }else {
                $(_pageId + " #income").val(dataArr_income[position].value);
                $(_pageId + " #income").attr("data-money", dataArr_income[position].money);
            }
        } else {
            position = 0;
            $(_pageId + " #income").val();
            $(_pageId + " #income").attr("data-money", "");
        }


        tools.mobileSelect({
            trigger: _pageId + " #income",
            title: "请选择年收入（万元）",
            dataArr: dataArr_income,
            position: position + "",
            callback: function (data) {
                $(_pageId + " #income").val(data[0].value);
                $(_pageId + " #income").attr("data-id", data[0].id);
                $(_pageId + " #income").attr("data-value", data[0].value);
                $(_pageId + " #income").attr("data-money", data[0].money);
                if (data[0].id == "04") {
                    $(_pageId + " .pop_layer1").show();
                    $(_pageId + " .password_box").show();
                    event.stopPropagation();
                    $(_pageId + " #srje").val('');
                    $(_pageId + " #inputspanid span").html('');
                    //键盘事件
                    moneyboardEvent();
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "account_uploadIDCard";
                    param["eleId"] = "srje";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "3";
                    require("external").callMessage(param);
                    var srje = $(_pageId + " #srje").val();
                    $(_pageId + " #income").val(srje);
                    $(_pageId + " #income").attr("data-id", "04").attr("data-money", srje).attr("data-value", dataArr_income[3].value);
                    $(_pageId + " #inputspanid span").html(srje);
                }
            }
        });

    }
    //金额输入数字键盘
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #srje"),
            endcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                } else if(moneys<=0){
                    layerUtils.iAlert("请输入大于零的金额");
                }else {
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .password_box").hide();
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}$/.test(curVal))) {
                    moneys = moneys.substring(0, curVal.length - 1)
                }
                $(_pageId + " #income").val(Number(moneys));
                $(_pageId + " #income").attr("data-id", "04").attr("data-value", dataArr_income[3].value).attr("data-money", moneys);
                $(_pageId + " #inputspanid span").html(Number(moneys));
                $(_pageId + " #srje").val(Number(moneys));


            }
        })
    }


    /**
     * 居住地址选择
     */
    function selectorArea() {
        live_province = userInfo.living_address_province;
        live_city= userInfo.living_address_city;
        live_county =userInfo.living_address_county;
        var position;
        var position1;
        var position2;
        if (live_province) {
            try {
                position = city.findIndex(function (item) {
                    return live_province == item.code;
                });
                position1 = city[position].sub.findIndex(function (item) {
                    return live_city == item.code;
                });
                position2 = city[position].sub[position1].sub.findIndex(function (item) {
                    return live_county == item.code;
                });
                var value = city[position].name + ' ' + city[position].sub[position1].name + ' ' + city[position].sub[position1].sub[position2].name;
                $(_pageId + " #live_address").val(value);
            } catch (error) {
                position = 0;
                position1 = 0;
                position2 = 0;
            }
        } else {
            position = 0;
            position1 = 0;
            position2 = 0;
        }
        selectedIndex = [position, position1, position2];
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择',
            // callback: function (data, index) {
            //     console.log(data,index,111)
            // }
        });
        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            // console.log(first,second,third)
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            value = text1 + ' ' + text2 + ' ' + text3;
            $(_pageId + " #live_address").val(value);
            live_province = first[selectedIndex[0]].code;
            live_city = second[selectedIndex[1]].code;
            live_county = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            let flag = await tools.is_region(live_province,live_city,live_county,city)
            // console.log(flag)
            if(flag){
                $(_pageId + " #live_address").val(value);
            }else{
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }

        });
        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }
            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
    }

    function saveCallBackFun() {
        service.reqFun101020({}, function (data) {
            var error_no = data.error_no;
            if (error_no == "0") {
                var results = data.results[0];
                common.setLocalStorage("mobileWhole", results.mobileWhole);
                ut.saveUserInf(results);
                layerUtils.iLoading(false);
                appUtils.pageBack();
            } else {
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, "网络繁忙,请重新登录");
                appUtils.clearSStorage("_loginInPageCode");
                appUtils.clearSStorage("_loginInPageParam");
                appUtils.clearSStorage("_isLoginIn");
                appUtils.clearSStorage();
                common.gestureLogin();
            }
        })
    }
    function destroy() {
        saveImgNum = 0;
        $(_pageId + " .camera_pop_layer").remove();
        $(_pageId + " .noUpload").hide();
        $(_pageId + " .idCardShow").hide();
        $(".mobileSelect").remove();
        $(".picker").remove();
        $(_pageId + " #live_address").val("");
        $(_pageId + " #ocp").val("");
        $(_pageId + " #income").val("");
        first = [];
        second = [];
        third = [];
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.setSStorageInfo("noUploadIdCard",'');
        appUtils.pageBack();
    }

    var setBankCard = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setBankCard;
});
