/*创建时间hSea 2016-05-26 11:04:51 AM */
define(function(require,exports,module){function a(a,b){try{var c=h.enc.Utf8.parse(a),d=h.enc.Utf8.parse(b),e=h.AES.encrypt(d,c,{mode:h.mode.ECB,padding:h.pad.Pkcs7});return e.toString()}catch(f){}return""}function b(a,b){try{var c=h.enc.Utf8.parse(a),d=(h.enc.Utf8.parse(a),h.enc.Base64.parse(b)),e=h.AES.decrypt({ciphertext:d},c,{mode:h.mode.ECB,padding:h.pad.Pkcs7});return e.toString(h.enc.Utf8)}catch(f){}return""}function c(a,b){try{var c=i.enc.Utf8.parse(a),d=i.enc.Utf8.parse(b),e=i.DES.encrypt(d,c,{mode:i.mode.ECB,padding:i.pad.Pkcs7});return e.toString()}catch(f){}return""}function d(a,b){try{var c=i.enc.Utf8.parse(a),d=i.enc.Base64.parse(b),e=i.DES.decrypt({ciphertext:d},c,{mode:i.mode.ECB,padding:i.pad.Pkcs7});return e.toString(i.enc.Utf8)}catch(f){}return""}function e(a,b,c){try{return j.setPublic(a,b),"encrypt_rsa:"+j.encrypt(c)}catch(d){}return""}function f(a){return k.encoder(a)}function g(a){return k.decoder(a)}var h=require("aes"),i=require("des"),j=require("rsa"),k=require("base64"),l={aesEncrypt:a,aesDecrypt:b,desEncrypt:c,desDecrypt:d,rsaEncrypt:e,encoderBase64:f,decoderBase64:g};module.exports=l});
/*创建时间 2016-05-26 11:04:51 AM */