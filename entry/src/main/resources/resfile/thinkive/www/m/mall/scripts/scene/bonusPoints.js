//活动 - 理财账单
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools.js"),
        validatorUtil = require("validatorUtil");
    var _pageId = "#scene_bonusPoints ";
    var _pageCode = "scene/bonusPoints"
    var layerUtils = require("layerUtils");
    var global = gconfig.global;
    var ut = require("../common/userUtil.js");

    /**
     * 初始化
     */
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        getActivityInfo();
        
    };

    /**
     * 获取活动信息
     * */

    //测试id1903
    //生产130
    function getActivityInfo() {
        service.reqFun108015({
            cust_no: ut.getUserInf().custNo,
            activity_id: "130"
        }, function (data) {
            var error_no = data.error_no;
            if (error_no == 0) {
                var result = data.results[0];
                var introduce = result.introduce;
                $(_pageId + ' .introduce').html(introduce);       
               
            }
        });
    }

  

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            // tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_pageCode)
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    };
    var scene_bonusPoints = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = scene_bonusPoints;
});
