// 晋金定制 产品详情页
define(function (require, exports, module) {
	var appUtils = require("appUtils"),
		dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		cfdUtils = require("cfdUtils"),
		tools = require("../common/tools"),
		service = require("mobileService"),
		VIscroll = require("vIscroll"),
		common = require("common"),
		vIscroll = { "scroll": null, "_init": false },
		_pageId = "#thfund_HAHistoryList",
		_pageCode = "thfund/HAHistoryList";
	var _fund_code = "";
	var ut = require("../common/userUtil");
	var isEnd = false;
	var _cur_page = 1;
	var _num_per_page = "20";
	function init() {
		vIscroll = { "scroll": null, "_init": false };
		_fund_code = appUtils.getSStorageInfo("jjbFundCode") || appUtils.getSStorageInfo("fund_code");
		var productInfo = appUtils.getSStorageInfo("productInfo_jjb") || appUtils.getSStorageInfo("productInfo");
		//获取历史七日年化
		getHistory(false);
		tools.initFundBtn(productInfo, _pageId);
	}

	function bindPageEvent() {
		appUtils.bindEvent($(_pageId + " .icon_back"), function () {
			pageBack();
		});
		appUtils.bindEvent($(_pageId + " #kefu"), function () {
			tools.saveAlbum(_pageCode)
		});

	}

	//获取历史七日年化
	function getHistory(isAppendFlag) {
		isEnd = false;
		$(_pageId + " .new_none").hide();
		var params = {
			fund_code: _fund_code,
			cur_page: _cur_page + "",
			num_per_page: _num_per_page + "",
		}
		var callback = function (resultVo) {
			if (resultVo.error_no == "0") {
				var results = resultVo.results;
				var html = "";
				if (results && results.length > 0) {
					for (var i = 0; i < results.length; i++) {

						results[i] = tools.FormatNull(results[i]);
						var end_date = results[i].end_date;
						end_date = tools.ftime(end_date.substring(0, 8));

						var return_pertt = results[i].return_pertt;
						if (return_pertt != "--") {
							return_pertt = (+return_pertt).toFixed(4);
						}

						var annu_yield = results[i].annu_yield;
						if (annu_yield != "--") {
							annu_yield = (+annu_yield).toFixed(4) + "%";
						}

						html += '<div class="item">' +
							'<span>' + end_date + '</span>' +
							'<span class="">' + return_pertt + '</span>' +
							'<span class="add">' + annu_yield + '</span>' +
							'</div>';
					}
				} else if (!results) {
					isEnd = true;
					$(_pageId + " .new_none").show();
				}

				if (results && results.length < _num_per_page) {
					isEnd = true;
					$(_pageId + " .new_none").show();
				}
			} else {
				layerUtils.iAlert(resultVo.error_info);
			}

			if (isAppendFlag) {
				$(_pageId + " #concent").append(html);
				$(_pageId + " .visc_pullUpIcon").hide();
				$(_pageId + " .visc_pullUpDiv").hide();
			} else {
				$(_pageId + " #concent").html(html);
			}
			pageScrollInit();
		}
		service.reqFun102006(params, callback);
	}

	//上下滑动事件
	function pageScrollInit() {
		var height = $(_pageId + " #v_container_productList").offset().top;
		var height2 = $(window).height() - height - 50 + 44 - 90;
		if (!vIscroll._init) {
			var config = {
				"isPagingType": false,
				"visibleHeight": height2, //这个是中间数据的高度
				"container": $(_pageId + " #v_container_productList"),
				"wrapper": $(_pageId + " #v_wrapper_productList"),
				"downHandle": function () {
					_cur_page = 1;
					getHistory(false);
					$(_pageId + " .visc_pullUp").show();
					$(_pageId + " .visc_pullUpIcon").hide();
					$(_pageId + " .visc_pullUpDiv").hide();
				},
				"upHandle": function () {
					if (!isEnd) {
						_cur_page += 1;
						$(_pageId + " .visc_pullUpIcon").show();
						$(_pageId + " .visc_pullUpDiv").show();
						getHistory(true);
					} else {
						$(_pageId + " .new_none").show();
					}
				},
				"wrapperObj": null
			};

			vIscroll.scroll = new VIscroll(config); //初始化
			vIscroll._init = true;
		} else {
			vIscroll.scroll.refresh();
		}
		if (isEnd) {//可能有当前页为1总页数为0的情况
			$(_pageId + " .visc_pullUp").hide();
		} else {
			$(_pageId + " .visc_pullUp").show();
		}
	}

	function destroy() {
		_cur_page = 1;
		$(_pageId + " .new_none").hide();
		isEnd = false;
		$(_pageId + " #concent").html("");
	}

	function pageBack() {
		appUtils.pageBack();
	}
	var jjThirtyDetail = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = jjThirtyDetail;
});
