// 手机登录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        monkeywords = require("mall/scripts/common/passwordKeywords"),
        _pageId = "#login_userLogin ";
    var phoneNum = "";
    var trade_pwd = "";
    var type = "";
    var external = require("external");
    
    var platform = require("gconfig").platform;
    var mobile_save; //用于本地存储手机号
    var ut = require("../common/userUtil");
    var password = "";
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");
    var userInfoType = '0';
    var timer = null;//计时器
    var loginType = '1'; //用户选中的登录方式
    var i = 120;//倒计时长
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        loginType = appUtils.getSStorageInfo("loginType") ? appUtils.getSStorageInfo("loginType") : '1';
        $(_pageId + " .drainShow").hide();
        common.systemKeybord(); // 解禁系统键盘
        //首次进入页面，获取更新状态
        common.updateVers(_pageId);
        // 苹果暗黑模式设置
        tools.getSystemMode(_pageId);
        $(_pageId + " #pwd").val("");
        $(_pageId + " #phontNum").val("");
        $(_pageId + " #phoneNum").val("");
        sms_mobile.init(_pageId);
        mobile_save = "";
        var mobileWhole = common.getLocalStorage("mobileWhole");
        $(_pageId + " .placeholderPsd").show();
        $(_pageId + " .YzmShow").hide();
        $(_pageId + " #yzm").val('');
        if(appUtils.getPageParam("mobile")) {
            $(_pageId + " #phontNum").val(appUtils.getPageParam("mobile"));
            $(_pageId + " #phoneNum").val(appUtils.getPageParam("mobile"));
        } else if (mobileWhole) {
            if (mobileWhole.indexOf("*") >= 0) {
                mobileWhole = "";
            } else {
                if (mobileWhole.length < 9) {
                    mobileWhole = mobileWhole.substring(0, 3) + "***" + mobileWhole.substring(mobileWhole.length - 2, mobileWhole.length);
                } else {
                    mobileWhole = mobileWhole.substring(0, 3) + "*****" + mobileWhole.substring(mobileWhole.length - 3);
                }
            }
            $(_pageId + " #phontNum").val(mobileWhole);
            $(_pageId + " #phoneNum").val(mobileWhole);
        } else {
            $(_pageId + " #phontNum").val("");
            $(_pageId + " #phoneNum").val("");
        }

        // $(_pageId + " .register_box").show();
        // $(_pageId + " .login_page").hide();

        tools.getPdf("5")
        //判断用户的登录方式
        if(loginType == '0'){
            $(_pageId + " .login_psd").removeClass('active');
            $(_pageId + " .login_yzm").addClass('active');
            $(_pageId + " .login_page").hide()
            $(_pageId + " .register_box").show();
        }else if(loginType == '1'){
            $(_pageId + " .register_box").hide();
            $(_pageId + " .login_yzm").removeClass('active');
            $(_pageId + " .login_psd").addClass('active');
            $(_pageId + " .login_page").show()
            
        }
    }

    //绑定事件
    function bindPageEvent() {


        /**
         * 切换登录方式
         */

        
        appUtils.bindEvent($(_pageId + " .login_psd"), function () {
            $(_pageId + " .login_page").show()
            $(_pageId + " .register_box").hide();
            $(_pageId + " .login_yzm").removeClass('active');
            $(_pageId + " .login_psd").addClass('active');
            loginType = '1'
        });

        appUtils.bindEvent($(_pageId + " .login_yzm"), function () {
            $(_pageId + " .login_page").hide()
            $(_pageId + " .register_box").show();
            $(_pageId + " .login_psd").removeClass('active');
            $(_pageId + " .login_yzm").addClass('active');
            loginType = '0'
        });


        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " .cursor-bink").hide()
            monkeywords.close();
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #yzmBox"), function (event) {
            // tools.recordEventData('1','yzmBox','弹出数字键盘');
            // if($(_pageId + " #getYzm").css('display') == 'none'){
            $(_pageId + " .cursor-bink").show()
            moneyboardEvent()
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "login_userLogin";
            param["eleId"] = "pwd";
            param["doneLable"] = "确定";
            param["keyboardType"] = "60";
            param["keyboardFramework"] = "1"
            external.callMessage(param);
            // }else{
                // $(_pageId + " .cursor-bink").hide()
            // }
            event.stopPropagation();
            
        });


        /**
         * 验证码登录相关
         */

        // 验证码登录手机号码输入事件
        appUtils.bindEvent($(_pageId + " #phoneNum"), function () {
            var curVal = this.value;
            curVal = curVal.replace(/[\u4E00-\u9FA5\uF900-\uFA2D]/g, "");
            curVal = curVal.replace(/[^\a-\z\A-\Z0-9]/g, '');
            if (curVal.length > 20) {
                curVal = curVal.substring(0, 20);
            }
            this.value = curVal;
        }, "input");


        //点击注册/登录按钮
        appUtils.bindEvent($(_pageId + " #registered"), function () {
            //检查协议勾选
            var isChecked = $(_pageId + " #input_radio2").attr("checked");
            if (!isChecked) {
                layerUtils.iMsg(-1, "请阅读协议并同意签署");
                return;
            }
            //检查手机号
            var phone = $.trim($(_pageId + " #phoneNum").val());
            var phone = $(_pageId + " #phoneNum").val();
            var mobileWhole = common.getLocalStorage("mobileWhole");
            if (phone && mobileWhole) {
                if (mobileWhole.length < 9) {
                    mobileWhole = mobileWhole.substring(0, 3) + "***" + mobileWhole.substring(mobileWhole.length - 2, mobileWhole.length);
                } else {
                    mobileWhole = mobileWhole.substring(0, 3) + "*****" + mobileWhole.substring(mobileWhole.length - 3);
                }
                if (phone == mobileWhole) {
                    phone = common.getLocalStorage("mobileWhole");
                }
            }
            if (validatorUtil.isEmpty(phone)) {
                layerUtils.iMsg(-1, "请输入手机号码");
                return;
            }
            if (!validatorUtil.isMobile(phone)) {
                layerUtils.iMsg(-1, "请确定您输入的手机号是否正确");
                return;
            }
            //检查验证码获取按钮
            var isSend = $(_pageId + " #getYzmNew").attr("data-state");
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            // // //检查验证码input
            var mobile_vf_code = $.trim($(_pageId + " #verificationCode").val());//验证码
            if (validatorUtil.isEmpty(mobile_vf_code)) {
                layerUtils.iMsg(-1, "请输入手机验证码");
                return;
            }
            
            if (!validatorUtil.isNumeric(mobile_vf_code)) {
                layerUtils.iMsg(-1, "验证码格式为纯数字");
                return;
            }
            if (mobile_vf_code.length != 6) {
                layerUtils.iMsg(-1, "请输入六位数验证码");
                return;
            }
            let param = {
                registered_mobile: phone,
                sms_code: mobile_vf_code,
                sms_mobile:phone,
                source:'1'//app自主注册
            };
            // if(userInfoType == "0"){
            //     //注册并登录
                

            // }else if(userInfoType == "1"){
            //     //登录
            //     service.reqFun101094(param, loginCallback);
            // }
            service.reqFun101094(param, loginCallback);
        });



        
        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzmNew"), function () {
            var isChecked = $(_pageId + " #input_radio2").attr("checked");
            if (!isChecked) {
                layerUtils.iMsg(-1, "请阅读协议并同意签署");
                return;
            }
            //验证手机号input
            var mobile = $(_pageId + " #phoneNum").val();
            var mobileWhole = common.getLocalStorage("mobileWhole");
            if (mobile && mobileWhole) {
                if (mobileWhole.length < 9) {
                    mobileWhole = mobileWhole.substring(0, 3) + "***" + mobileWhole.substring(mobileWhole.length - 2, mobileWhole.length);
                } else {
                    mobileWhole = mobileWhole.substring(0, 3) + "*****" + mobileWhole.substring(mobileWhole.length - 3);
                }
                if (mobile == mobileWhole) {
                    mobile = common.getLocalStorage("mobileWhole");
                }
            }


            if (!validatorUtil.isMobile(mobile)) {
                layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                return;
            }
            //验证获取验证码按钮可用性
            var $code = $(_pageId + " #getYzmNew");
            if ($code.attr("data-state") == "false") {
                return;
            }
           
            service.reqFun102005({mobile:mobile}, function (data) {
                if (data.error_no == "-10200502") {//10200501 账号已注册
                    userInfoType = '1' //用户已注册
                    var param = {
                        mobile_phone: mobile,
                        type: common.sms_type.register,
                        send_type: "0"
                    };
                    service.reqFun199001(param, function (data) {
                        var error_no = data.error_no,
                            error_info = data.error_info;
                        if (error_no == "0") {
                            $(_pageId + " #talkCode").show();
                            timer = setInterval(function () {
                                shows();
                            }, 1000);
                        } else {
                            $(_pageId + ' #verificationCode').attr("disabled",true);
                            window.clearInterval(timer);
                            i = 120;
                            initYanZma();
                            $(_pageId + " #talkCode").show();
                            layerUtils.iAlert(error_info);
                        }
                    });                    
                } else if (data.error_no == "-10200501") {//黑名单
                    userInfoType = '2' //该用户为黑名单用户
                    $(_pageId + " input").blur();
                    return layerUtils.iAlert("网络繁忙,请稍后重试!"); 
                } else if (data.error_no != "0") {
                    userInfoType = '3' //发生其他未知错误
                    layerUtils.iAlert(data.error_info);
                    return
                }else{
                    var param = {
                        mobile_phone: mobile,
                        type: common.sms_type.register,
                        send_type: "0"
                    };
                    service.reqFun199001(param, function (data) {
                        var error_no = data.error_no,
                            error_info = data.error_info;
                        if (error_no == "0") {
                            $(_pageId + " #talkCode").show();
                            timer = setInterval(function () {
                                shows();
                            }, 1000);
                        } else {
                            $(_pageId + ' #verificationCode').attr("disabled",true);
                            window.clearInterval(timer);
                            i = 120;
                            initYanZma();
                            $(_pageId + " #talkCode").show();
                            layerUtils.iAlert(error_info);
                        }
                    });
                }
            });
        });


        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

        //获取语音验证码
        function getCodeOFTalk() {
            var mobile = $(_pageId + " #phoneNum").val();
            if (mobile) {
                var param = {
                    "mobile_phone": mobile,
                    "type": common.sms_type.register,
                    "send_type": "1",
                }
                service.reqFun199001(param, function (data) {
                    if (data.error_no == "0") {
                        var result = data.results;
                        var talk_mobile = result[0].orgphone;
                        var $dd = "晋金财富将致电您的手机语音告知验证码";
                        $(_pageId + " #talkCode").html($dd);
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            } else {
                if (mobile == "") {
                    layerUtils.iMsg(-1, "请输入手机号");
                }

            }
        }

        //初始化语音验证码
        function initYanZma() {
            var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
            $(_pageId + " #talkCode").html($dd);
            $(_pageId + " #talkCode").hide();
        }


        //点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " #isChecked"), function () {
            var chencked = $(_pageId + " #input_radio2").attr("checked");
            if (chencked == "checked") {
                $(_pageId + " #input_radio2").removeAttr("checked", "checked");
            } else {
                $(_pageId + " #input_radio2").attr("checked", "checked");
            }
        });
        /**------------------------------------------------------------- */
        /**
         * 账号密码登录
         */
         // 手机号码输入事件
         appUtils.bindEvent($(_pageId + " #phontNum"), function () {
            var curVal = this.value;
            curVal = curVal.replace(/[\u4E00-\u9FA5\uF900-\uFA2D]/g, "");
            curVal = curVal.replace(/[^\a-\z\A-\Z0-9]/g, '');
            if (curVal.length > 20) {
                curVal = curVal.substring(0, 20);
            }
            this.value = curVal;
        }, "input");
       
        //点击登录
        appUtils.bindEvent($(_pageId + " #login"), function () {
            // tools.recordEventData('1','login','登录');
            phoneNum = $(_pageId + " #phontNum").val();
            
            var mobileWhole = common.getLocalStorage("mobileWhole");
            if (phoneNum && mobileWhole) {
                if (mobileWhole.length < 9) {
                    mobileWhole = mobileWhole.substring(0, 3) + "***" + mobileWhole.substring(mobileWhole.length - 2, mobileWhole.length);
                } else {
                    mobileWhole = mobileWhole.substring(0, 3) + "*****" + mobileWhole.substring(mobileWhole.length - 3);
                }
                if (phoneNum == mobileWhole) {
                    phoneNum = common.getLocalStorage("mobileWhole");
                }
            }

            /************校验用户信息************/
            if (validatorUtil.isEmpty(phoneNum)) {
                layerUtils.iMsg(-1, "账号不能为空");
                return;
            }

            if (phoneNum.length < 6 || phoneNum.length > 20) {
                layerUtils.iMsg(-1, "账号长度保持在6-20位之间");
                return;
            }

            var reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
            if (reg.test(phoneNum)) {
                layerUtils.iMsg(-1, "账号不包含中文字符");
                return;
            }


            //验证传递登录名类型        0：手机号   1：身份证   2：别名
            if (validatorUtil.isMobile(phoneNum)) {
                type = "0";
            } else if (validatorUtil.isCardID(phoneNum)) {
                type = "1";
            } else if (checkOtherName(phoneNum)) {
                type = "2";
            } else {
                layerUtils.iMsg(-1, "账号输入有误");
                return;
            }

            /****************end****************/

            if (validatorUtil.isEmpty($(_pageId + " #pwd").val())) {
                layerUtils.iMsg(-1, "密码不能为空");
                return false;
            }
            var pwd = $.trim($(_pageId + " #pwd").val());
            //密码加密/**/
            var pwdCallBack = function (pwdRSAStr) {
                var param = {};
                if (validatorUtil.isNotEmpty(phoneNum)) {
                    var device_token = "";
                    // 获取token
                    if (platform == "2" || platform == "1" || platform == '5') {
                        var param = {
                            funcNo: "50041",
                            key: "deviceTokenKey"
                        };
                        var data = external.callMessage(param);
                        device_token = data.results[0].value;
                    }
                    if (platform == "2" || platform == "1" || platform == '5') {
                        var paramyz = {
                            "funcNo": "50263",
                            "account": phoneNum
                        };
                        var data = external.callMessage(paramyz);
                        var flag = data.results[0].flag;
                        if (flag != "1") {
                            var setParam = {
                                "funcNo": "50261",
                                "moduleName": "mall",
                                "style": "1", //style   String  手势密码的样式类型(0：不显示中心小圆，1：显示）
                                "account": phoneNum,
                                "errorNum": "5",
                                "lockSenconds": "300",
                                "userImage": "",
                                "isShow": "0"
                            };
                            external.callMessage(setParam);
                        }
                    }
                    //发送登录请求
                    var reqParam101018 = {
                        "login_acc": phoneNum,
                        "login_pwd": pwdRSAStr,
                        "device_token": device_token,
                        "type": type
                    };
                    password = pwdRSAStr; //存储登录密码，后续手势密码登录调用
                    service.reqFun101018(reqParam101018, loginCallback);
                }
            };
            common.rsaEncrypt(pwd, pwdCallBack);
        });

      

        //点击忘记密码
        appUtils.bindEvent($(_pageId + " #wjmm"), function () {
            
            appUtils.setSStorageInfo("loginType", loginType);
            // tools.recordEventData('1','wjmm',$(_pageId + " #wjmm").text());
            appUtils.pageInit("login/userLogin", "safety/userPassword", {});
        });

        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            // tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        //点击注册
        appUtils.bindEvent($(_pageId + " #register"), function () {
            $(_pageId + " .login_page").hide();
            $(_pageId + " .register_box").show();
            $(_pageId + " .login_psd").removeClass('active');
            $(_pageId + " .login_yzm").addClass('active');
        });


    }
    /**
     * 渲染密码
     */
    function setPassword(_pageId){
        let spacing = platform == 1 ? 0.06 : 0.075
        let pwd = $(_pageId + " #pwd").val();
        if(!pwd){
            $(_pageId + " .placeholderPsd").html('请输入密码');
            $(_pageId + " .placeholderPsd").css('color','#aaa');
            $(_pageId + " .cursor-bink").css("left",'0.5rem');
            return
        }  
        $(_pageId + " .placeholderPsd").css('color','#666');
        let length = pwd.length*1
        if(length > 16) return
        let str = ''
        for(let i = 0; i<length; i++){
            str = str + '*'
        }
        $(_pageId + " .placeholderPsd").html(str);
        $(_pageId + " #pwd").val(pwd);
        let leftValue
        if($(_pageId + " .placeholderPsd")[0]) {
            leftValue = ($(_pageId + " .placeholderPsd")[0].clientWidth/100) + 0.4;
        }
        $(_pageId + " .cursor-bink").css("left",leftValue +  'rem');
    }
    /*************新增方法**********************/
    //键盘事件
    function moneyboardEvent(){
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #pwd"),
            endcallback: function () {
                setPassword(_pageId)
            },
            inputcallback: function () {
                setPassword(_pageId)
            },
            keyBoardHide: function () {
                setPassword(_pageId)
            }
        })
    }
    //校验别名方法
    function checkOtherName(phoneNum) {
        var flag = 0;

        //首个字母不能为数字
        if (validatorUtil.isNumeric(phoneNum.charAt(0))) {
            return false;
        }

        for (var i = 0; i < phoneNum.length; i++) {
            if (phoneNum[i].match(/^[a-z]|[A-Z]$/)) {
                flag = flag + 1;
                break;
            }
        }

        for (var i = 0; i < phoneNum.length; i++) {
            if (phoneNum[i].match(/^[0-9]$/)) {
                flag = flag + 1;
                break;
            }
        }

        if (flag != 2) {
            return false;
        }

        return true;
    }

    
    /*************新增方法**********************/


    //登录回调函数
    function loginCallback(resultVo) {
        var error_no = resultVo.error_no;
        var error_info_login = resultVo.error_info;
        if (error_no == "0") {
            var results = resultVo.results[0];
            // 登录成功后，存登录号 和 登录加密密码 方便手势密码
            var paramlist = {
                funcNo: "50042",
                key: "account_password",
                isEncrypt: "1",
                value: results.mobileWhole + "_" + password
            };
            external.callMessage(paramlist);
            let mobile = common.getLocalStorage("mobileWhole");
            if(mobile != results.mobileWhole){
                //切换账号登录
                common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
                common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
                common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
                sessionStorage.pop_up_otification = "";
            }
            //存储用户信息
            common.setLocalStorage("mobileWhole", results.mobileWhole);
            //记录密码状态
            if(!results.login_pwd_state && results.login_pwd_state != '') results.login_pwd_state = '1';
            //保存用户已经登陆过
            ut.saveUserInf(results);
            let snowballMarketShow = appUtils.getPageParam("snowballMarketShow"); //获取用户是否通过营销页进入的
            common.setLocalStorage("snowballMarketShow", snowballMarketShow ? snowballMarketShow : '');
            common.setLocalStorage("userChooseRefresh",'1'); //刷新用户版本
            common.setLocalStorage("userChooseVerson",''); //置空用户选择版本
            //登录后保存用户场景
            common.setLocalStorage("scene_code", results.scene_code);
            //存储用户场景首页列表模板ID
            common.setLocalStorage("scene_template_id", results.scene_template_id);
            // 存储是否是异常客户-开户审核中
            appUtils.setSStorageInfo("is_open_acct_excp",results.is_open_acct_excp);
            appUtils.setSStorageInfo("isAuthenticated", results.mobile + "@|@|@" + new Date().getTime());
            //银行账户已冻结 （N：正常; C：销户; F：冻结）
            if (results.custStatus == "F") {
                layerUtils.iMsg(-1, "账户已冻结，请联系" + require("gconfig").global.custServiceTel);
            }
            var skpUrl = appUtils.getSStorageInfo("skipURL");
            appUtils.clearSStorage("skipURL");
            //同步最新指纹
            let param = {
                funcNo: "80321",
            };
            external.callMessage(param);
            let flag;
            let custLabelCnlCode = common.getLocalStorage("download_channel_code");
            common.setLocalStorage("isLoginUser",'0');
            if(!custLabelCnlCode && custLabelCnlCode !=''){
                flag = true;
            }else{
                let pageTopUrlInfo = appUtils.getSStorageInfo("pageTopUrlInfo");
                flag = (custLabelCnlCode == results.custLabelCnlCode) ? true : false;
                if(results.custLabelCnlCode == 'yh_jjdx' && (custLabelCnlCode == 'jjdx' || !custLabelCnlCode)){
                    
                    if(pageTopUrlInfo == 'yuanhui/fundList_1'){
                        flag = false;
                    }else{
                        flag = true;
                    }
                } 
                if((results.custLabelCnlCode == 'jjdx' || !results.custLabelCnlCode) && custLabelCnlCode == 'yh_jjdx'){
                    if(pageTopUrlInfo == 'yuanhui/fundList_1'){
                        flag = false;
                    }else{
                        flag = true;
                    }
                }
            }
            //存储渠道代码到本地
            common.setLocalStorage("download_channel_code", results.custLabelCnlCode);
            tools.loginQy();
            //登录成功跳转到主界面
            if (skpUrl) {
                appUtils.pageInit("login/userLogin", skpUrl, {});
            } else {
                backFunction()
            	tools.pageToUrl(flag)
            }
        } else {
            layerUtils.iAlert(error_info_login);
            if(error_no == '-10005'){
                //验证码错误 重置校验
                var $code = $(_pageId + " #getYzmNew");
                window.clearInterval(timer);
                i = 120;
                $code.attr("data-state", "true");
                $code.html("重新获取验证码");
                // initYanZma();
                return $(_pageId + " #talkCode").show();
            }
        }
    }

 
    /**
     * 显示读秒
     * */
    function shows() {
        $(_pageId + ' #verificationCode').attr("disabled",false);
        var $code = $(_pageId + " #getYzmNew");
        $(_pageId + " #getYzmNew").show()
        $code.attr("data-state", "false");//点击不能发送
        // var myDate = new Date();
        // var TimeDifference = myDate.getTime();

        // if (i == 120) {
        //     Millisecond = TimeDifference + 120000;
        // }
        // i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        // i = 120 --;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            // initYanZma();
            $(_pageId + " #talkCode").show();
        }
    }

    //离开事件
    function backFunction(){
        service.destroy();
        trade_pwd = null;
        mobile_save = null;
        sms_mobile.destroy();
        window.clearInterval(timer);
        var $yzm = $(_pageId + " #getYzmNew");
        $yzm.attr("data-state", "true");
        $yzm.html("获取验证码");
        $(_pageId + " #input_radio2").removeAttr("checked", "checked");
        $(_pageId + " #input_radio2").attr("isSelect", "true");
        i = 120;
        $(_pageId + " .drainShow").hide();
        $(_pageId + " .drainHide").show();
        $(_pageId + " .cursor-bink").hide()
        $(_pageId + " .cursor-bink").css("left",'0.5rem');
        $(_pageId + " .placeholderPsd").css('color','#aaa');
        $(_pageId + " .placeholderPsd").show();
        $(_pageId + " .YzmShow").hide();
        $(_pageId + " .cursor-bink").hide();
        $(_pageId + " #getYzmNew").val('')
        $(_pageId + " .placeholderPsd").html('请输入密码');
        $(_pageId + " #pwd").attr("placeholder", "请输入登录密码").attr("type", "password");
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #verificationCode").val('')
        backFunction();
    }

    function pageBack() {
        $(_pageId + " .cursor-bink").hide()
        $(_pageId + " .drainShow").hide();
        monkeywords.close();
        loginType = '1'
        appUtils.setSStorageInfo("loginType", '1');
        // appUtils.pageInit("login/userLogin", "login/userIndexs", {});
        appUtils.pageBack();
    }

    var userLogin = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = userLogin;
});
