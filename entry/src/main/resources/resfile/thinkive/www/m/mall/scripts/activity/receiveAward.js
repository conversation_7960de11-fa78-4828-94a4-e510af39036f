/**
 * 实物领取地址填写
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil");
    var _pageCode = "activity/receiveAward",
        _pageId = "#activity_receiveAward";
    var receive_type = "1"; // 默认为大厅自取
    var is_entered = "0"; // 是否已提交
    var receivceDetails;
    var luckflag;
    function init() {
        if (appUtils.getPageParam("luckflag")) {
            appUtils.setSStorageInfo("luckflag", appUtils.getPageParam("luckflag"));
        }
        luckflag = appUtils.getSStorageInfo("luckflag");
        receivceDetails = appUtils.getPageParam().recordInfo;
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        if (receivceDetails && receivceDetails.receiver_address) { // 有邮寄地址时，默认为邮寄
            receivceDetails.receive_type = "3"
        }
        if (receivceDetails && receivceDetails.receive_type) {
            receive_type = receivceDetails.receive_type
            $(_pageId + " .select_tab span").removeClass("activity");
            $(_pageId + ` .select_tab span[data-tab=${receivceDetails.receive_type}]`).addClass("activity");
            $(_pageId + " .select_content .item").css("display", "none");
            $(_pageId + ` .select_content .tab${receivceDetails.receive_type}`).css("display", "block")
            if (receivceDetails.receiver_name && receivceDetails.receive_type == "2") {
                $(_pageId + " .select_content .tab2 #receiver_name").val(receivceDetails.receiver_name);
            }
            if (receivceDetails.receive_type == "3") {
                receivceDetails.receiver_phone && $(_pageId + " .select_content .tab3 #receiver_phone").val(receivceDetails.receiver_phone);
                receivceDetails.receiver_address && $(_pageId + " .select_content .tab3 #receiver_address").val(receivceDetails.receiver_address);
                receivceDetails.receiver_name && $(_pageId + " .select_content .tab3 #receiver_name").val(receivceDetails.receiver_name);
            }
        }

        if (receivceDetails && receivceDetails.is_entered == '1') {
            is_entered = receivceDetails.is_entered;
            $(_pageId + " #receiveAwardSubmit").addClass("no_active")
            $(_pageId + " #receiveAwardSubmit span").html("已提交")
            $(_pageId + " .select_content .tab3 #receiver_name").attr("disabled", true)
            $(_pageId + " .select_content .tab3 #receiver_phone").attr("disabled", true)
            $(_pageId + " .select_content .tab3 #receiver_address").attr("disabled", true)
            $(_pageId + " .select_content .tab2 #receiver_name").attr("disabled", true)
        }

    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " .select_tab"), "span", function () {
            if (is_entered == '1') return;
            var type = $(this).attr("data-tab")
            receive_type = type;
            if (receivceDetails && receivceDetails.is_entered == "0") {
                $(_pageId + " .select_content .tab3 #receiver_name").val("");
                $(_pageId + " .select_content .tab3 #receiver_phone").val("");
                $(_pageId + " .select_content .tab3 #receiver_address").val("");
                $(_pageId + " .select_content .tab2 #receiver_name").val("")
            }
            $(_pageId + " .select_tab span").removeClass("activity");
            $(this).addClass("activity");
            $(_pageId + " .select_content .item").css("display", "none");
            $(_pageId + ` .select_content .tab${type}`).css("display", "block")
        })

        appUtils.bindEvent($(_pageId + " #receiveAwardSubmit"), () => {
            if ($(_pageId + " #receiveAwardSubmit").hasClass("no_active")) return;
            var params = {};
            if (receive_type == "1") {
                params = {
                    reward_log_id: receivceDetails.reward_log_id,
                    receive_type: receive_type
                }
            }
            if (receive_type == "2") {
                var receiver_name = $(_pageId + " .select_content .tab2 #receiver_name").val()
                if (validatorUtil.isEmpty(receiver_name)) {
                    layerUtils.iMsg(-1, "代领人姓名不能为空");
                    return;
                }
                params = {
                    reward_log_id: receivceDetails.reward_log_id,
                    receive_type: receive_type,
                    receiver_name: receiver_name
                }
            }
            if (receive_type == "3") {
                var receiver_name = $(_pageId + " .select_content .tab3 #receiver_name").val()
                var receiver_phone = $(_pageId + " .select_content .tab3 #receiver_phone").val()
                var receiver_address = $(_pageId + " .select_content .tab3 #receiver_address").val()
                if (validatorUtil.isEmpty(receiver_name)) {
                    layerUtils.iMsg(-1, "收货人姓名不能为空");
                    return;
                }
                if (!validatorUtil.isMobile(receiver_phone)) {
                    layerUtils.iMsg(-1, "请输入正确手机号码");
                    return;
                }
                if (validatorUtil.isEmpty(receiver_address)) {
                    layerUtils.iMsg(-1, "收货人邮寄地址不能为空");
                    return;
                }
                if (receiver_address.length > 100) {
                    layerUtils.iMsg(-1, "收货人邮寄地址限制输入为100字");
                    return;
                }
                params = {
                    reward_log_id: receivceDetails.reward_log_id,
                    receive_type: receive_type,
                    receiver_name: receiver_name,
                    receiver_phone: receiver_phone,
                    receiver_address: receiver_address
                }
            }
            service.reqFun108051(params, function (data) {
                if (data.error_no == 0) {
                    $(_pageId + " #receiveAwardSubmit").addClass("no_active")
                    $(_pageId + " #receiveAwardSubmit span").html("已提交")
                    layerUtils.iAlert("提交成功");
                    is_entered = "1"
                    $(_pageId + " .select_content .tab3 #receiver_name").attr("disabled", true)
                    $(_pageId + " .select_content .tab3 #receiver_phone").attr("disabled", true)
                    $(_pageId + " .select_content .tab3 #receiver_address").attr("disabled", true)
                    $(_pageId + " .select_content .tab2 #receiver_name").attr("disabled", true)
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }


    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " #receiveAwardSubmit").removeClass("no_active")
        $(_pageId + " .select_content .tab3 #receiver_name").val("");
        $(_pageId + " .select_content .tab3 #receiver_phone").val("");
        $(_pageId + " .select_content .tab3 #receiver_address").val("");
        $(_pageId + " .select_content .tab2 #receiver_name").val("")
        $(_pageId + " .select_tab span").removeClass("activity");
        $(_pageId + ` .select_tab span[data-tab='1']`).addClass("activity");
        $(_pageId + " .select_content .item").css("display", "none");
        $(_pageId + ` .select_content .tab1`).css("display", "block")
        $(_pageId + " #receiveAwardSubmit span").html("提交");
        $(_pageId + " .select_content .tab3 #receiver_name").attr("disabled", false)
        $(_pageId + " .select_content .tab3 #receiver_phone").attr("disabled", false)
        $(_pageId + " .select_content .tab3 #receiver_address").attr("disabled", false)
        $(_pageId + " .select_content .tab2 #receiver_name").attr("disabled", false)
        receive_type = "1";
        is_entered = "0"
    };
    /*
     * 返回
     */
    function pageBack() {
        if (luckflag == 'dargonYearDraw') {
            let url = appUtils.getSStorageInfo("front_pageUrl")
            if (url.indexOf("?") > -1) {
                var skip_url = url.split("?")[0];
                var parameter = url.split("?")[1];
                var parameter_arr = parameter.split("&"); //各个参数放到数组里
                var urlInfo = {};//url的参数信息
                for (var i = 0; i < parameter_arr.length; i++) {
                    num = parameter_arr[i].indexOf("=");
                    if (num > 0) {
                        name = parameter_arr[i].substring(0, num);
                        value = parameter_arr[i].substr(num + 1);
                        urlInfo[name] = value;
                    }
                }
                appUtils.pageInit(_pageCode, skip_url, urlInfo);
            }
            appUtils.clearSStorage("luckflag");
            appUtils.clearSStorage("front_pageUrl");
        } else {
            appUtils.pageBack();
        }

    }

    var receiveAwardModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = receiveAwardModule;


});
