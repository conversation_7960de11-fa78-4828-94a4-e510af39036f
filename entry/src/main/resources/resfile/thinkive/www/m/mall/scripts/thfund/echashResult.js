//取现结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        serviceConstants = require("constants"),
        service = require("mobileService"),
        common = require("common"),
        _pageUrl = "thfund/echashResult",
        _pageId = "#thfund_echashResult ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var tools = require("../common/tools");//升级
    var outputInfo;
    var num = 5;
    var type;
    var t;

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        tools.getActivityInfo(_pageId,_pageUrl)
        /*分享获取奖励*/
        outputInfo = appUtils.getPageParam();
        type = outputInfo.type;
        countDown();
        if (type == "pt") {//普通取现
            showTime({type: "4"});
        } else if (type == "ss") {//实时取现
            showTime({type: "5"});
        }
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + "#goHome"), function () {
            appUtils.pageInit(_pageUrl, "login/userIndexs");
        })
        //跳转我的晋金宝
        appUtils.bindEvent($(_pageId + "#myJJB"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit(_pageUrl, "thfund/myProfit");
        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit(_pageUrl, "thfund/JJBtransaction");
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId).find(".tc").val("--");//所有填充内容清理
        clearInterval(t);
        $(_pageId + ' #failinf').text("");
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').show();
        num = 5;
    }

    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun102030({"trans_serno": outputInfo.trans_serno}, function (data) {
                    if (data.error_no == "0") {
                        $(_pageId + ".load").hide();
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败 8 确认成功 9 确认失败
                        if (data.results[0].trans_status == "8") {
                            $(_pageId + " .success").show();//取现成功
                        } else if (data.results[0].trans_status == "9" || data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            if (data.results[0].host_desc) {
                                $(_pageId + ' #failinf').text('失败原因：' + data.results[0].host_desc);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，取现失败');
                            }
                            $(_pageId + " .fail").show();//取现失败
                        } else {
                            $(_pageId + " .wait").show();//取现无结果
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info, "-1", function () {
                            appUtils.pageInit(_pageUrl, "thfund/enchashment");
                        })
                    }
                })
            }

        }, 1000)
    }

    //显示取现相关时间日期
    function showTime(param) {
        service.reqFun102008(param, function (data) {
            checkResourtCallback(data);
        })
        var checkResourtCallback = function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var operatetime = data.results[0].operatetime;
                var revenueDate = data.results[0].revenueDate;
                $(_pageId + " .operateTime").text(common.dateStyle2(operatetime));
                if (type == "ss") {
                    $(_pageId + " .toAcountTime").html("十分钟");
                } else {
                    $(_pageId + " .toAcountTime").text(common.dateStyle2(revenueDate) + "12:00左右");
                }
            }
        }
    }

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
