//定投列表页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        common = require("common"),
        vIscroll = {"scroll": null, "_init": false},
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        _pageId = "#fixedInvestment_investmentList";
        _pageCode = "fixedInvestment/investmentList";
        require('../../js/zepto.min.js');
        require('../../js/iscroll.js');
        require('../../js/iosSelect.js');
    var ut = require("../common/userUtil");
    var productInfo;
    var monkeywords = require("../common/moneykeywords");
    var userInfo;
    var cur_page , num_per_page = 10;
    let querystatus = '0';
    var jymm,fixed_investment_list,singlePlan;
    var isEnd = false;
    var pageSource;
    var chooseData,workdate;
    var dataList = ["","周一",'周二','周三','周四','周五']
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        fixed_investment_list = appUtils.getSStorageInfo("fixed_investment_list");
        singlePlan = appUtils.getSStorageInfo("singlePlan");
        if(singlePlan == '1'){  //获取基金定投入口 持仓进入 true  我的页面 false
            singlePlan = true;
        }else{
            singlePlan = false;
        }
        pageSource = appUtils.getSStorageInfo("pageSource");  //缓存页面从哪里来
        productInfo = appUtils.getSStorageInfo("productInfo");  //缓存的列表详情
        if(!productInfo || productInfo == 'undefined') productInfo = {}
        // productInfo = {};  //缓存的列表详情 持仓页面进入定投列表展示全部计划 => 根据产品代码展示当前产品相关定投计划
        if(productInfo && productInfo.fund_code) $(_pageId + " .thfundBtn").show();
        if(fixed_investment_list == 0) $(_pageId + " .thfundBtn").hide();
        userInfo = ut.getUserInf()
        //获取列表
        cur_page = 1;//初始化分页
        let chooseQuerystatus = appUtils.getSStorageInfo("querystatus");
        if(chooseQuerystatus && chooseQuerystatus == '1'){
            $(_pageId + " .tab_box a").addClass("current");
            $(_pageId + " .tab_box a").first().removeClass('current');
            querystatus = '1'
            getList(querystatus,false)
        }else{
            getList(querystatus,false)
        }
        appUtils.setSStorageInfo("querystatus", null);
        //获取下个交易日
        getTransaction()
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    //获取下一工作日
    function getTransaction(){
        service.reqFun106050({}, (datas) => {
            if (datas.error_no != '0') {
                layerUtils.iAlert(datas.error_info);
                return;
            }
            let result = datas.results[0];
            workdate = result.workdate
        })
    }
    function getList(type,isAppendFlag){
        isEnd = false;
        $(_pageId + " .new_none").hide();
        //0进行中 1已终止
        //进行中
        let data = {
            custno:userInfo.custNo,
            fundcode:productInfo.fund_code ? productInfo.fund_code : '',
            virfundcode:productInfo.vir_fundcode ? productInfo.vir_fundcode : '',
            querystatus:type,
            cur_page:cur_page,
            investtype:'0'
        }
        service.reqFun106044(data, (datas) => {
            let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
            let bankNameRemark = userInfo.bankName + '(尾号' + bankAcct + ')';
            let result = datas.results[0].data
            let list = result.data
            if(list.length == 0){
                isEnd = true;
                $(_pageId + " .new_none").show();
                $(_pageId + " .new_none").html('暂无数据')
                hidePullUp();
                return;
            }
            if (list.length < num_per_page) {
                isEnd = true;
                $(_pageId + " .new_none").show();
                $(_pageId + " .new_none").html('没有更多数据')
            }
            cur_page = (+cur_page);
            cur_page += 1;
            let html = ''
            for(let i = 0; i< list.length; i++){
                let nextdate = list[i].nextdate ? (tools.FormatDateText(list[i].nextdate)).split('年')[1] : '';
                var productInfoSon = JSON.stringify(list[i]);
                let investtype = list[i].investtype;
                let paymethod = list[i].paymethod;
                
                html += `<ul class="card main_flxe vertical_line border_top" operationType="1" operationId="card_${list[i].fundcode}" contentType="1" fundCode="${list[i].fundcode}" operationName="${list[i].fundname}">
                            <em style='display: none' class='productInfo'>${productInfoSon}</em>
                            <li style="${(singlePlan && list[i].investtype == 2) ? 'display:block;' : 'display:none;'}" class="singlePlanMore">一键分散定投</li>
                            <li class="flex ">
                                <div class="main_flxe">
                                    <span class="m_bold">${list[i].fundname} </span>
                                    ${(investtype == '2' && !singlePlan) ? `<span class="details">详情</span>` : ``}
                                    
                                </div>
                                ${type == 0 ? `${(investtype == '2' && !singlePlan) ? `<span class="m_agreement_color more_termination operationType="1" operationId="termination_${list[i].planid}" operationName="终止定投" m_font_size12">终止定投</span>` : `<span id="manage" class="m_agreement_color m_font_size12" operationId="manage_${list[i].planid}" operationType="1" operationName="定投管理">定投管理</span>`}` : `<span operationType="1" operationId="record_${list[i].planid}" operationName="定投记录" class="m_font_size12 record m_agreement_color">定投记录</span>`}
                            </li>
                            <li class="main_flxe m_paddingTop_10">
                                <span class="m_width_25 m_center m_color_777 m_font_size12">扣款周期</span>
                                <span class="m_width_25 m_center m_color_777 m_font_size12">每期定投(元)</span>
                                <span class="m_width_25 m_center m_color_777 m_font_size12">已定投</span>
                                <span class="m_width_25 m_center m_color_777 m_font_size12">累计定投(元)</span>
                            </li>
                            <li class="main_flxe m_paddingTop_10">
                                <span class="m_width_25 m_center m_font_size12">${setTime(list[i].investcycle,list[i].investdate)}</span>
                                <span class="m_width_25 m_center m_font_size12">${tools.fmoney(list[i].investmoney)}</span>
                                <span class="m_width_25 m_center m_font_size12">${list[i].investsuccess}期</span>
                                <span class="m_width_25 m_center m_font_size12">${tools.fmoney(list[i].investtotal)}</span>
                            </li>
                            ${type == 0 ? `<li class="flex m_font_size12 m_paddingTop_10 m_marginTop_10 border_top">
                                                <span class="m_text_gray">下一扣款日：<strong class="m_text_red">${nextdate}</strong>  <strong class="m_text_darkgray">${paymethod == '0' ? '晋金宝' : bankNameRemark}</strong></span>
                                                <span class="record m_agreement_color">定投记录</span>
                            </li>` :''}
                            
                        </ul>`
            }
            if (isAppendFlag) {
                $(_pageId + ' .list').append(html)
            } else {
                $(_pageId + ' .list').html(html)
            }
            hidePullUp();
        })
    }
    function hidePullUp() {
   	    $(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }
    /**上下滑动刷新事件**/
    function pageScrollInit() {
    var height = $(_pageId + " #v_container_productList").offset().top;
    var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    // getHoldProd(false);
                    getList(querystatus,false)
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        $(_pageId + " .new_none").hide();
                        // getHoldProd(true);
                        getList(querystatus,true)
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                    hidePullUp();
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
    }}
    //根据接口数据展示下一定投日
    function setTime(investcycle,investdate){
        let str
        if(investcycle == '2'){
            str = '每月 ' + investdate + '日'
        }else if(investcycle == '0'){
            str = '每周 ' + dataList[investdate]
            
        }else{
            str = '每两周 ' + dataList[investdate]
        }
        return str;
    }
    //关闭数字键盘
    appUtils.bindEvent($(_pageId), function () {
        monkeywords.close();
    });
    //密码输入框初始化
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {}, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    function bindPageEvent() {
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        appUtils.preBindEvent($(_pageId + " .list"), ".card .record", function (e) {
            e.stopPropagation();
            e.preventDefault();
            chooseData = JSON.parse($(this).parents('ul').find("em").text())
            let param = {
                fundcode:chooseData.fundcode,
                planid:chooseData.planid,
                custno:userInfo.custNo,
                fundname:chooseData.fundname,
                paymethod:chooseData.paymethod
            }
            appUtils.setSStorageInfo("listInfo", param);
            if(chooseData.investtype == '2'){
                appUtils.setSStorageInfo("investtype",'2');
                appUtils.setSStorageInfo("querystatus",querystatus);
                appUtils.pageInit(_pageCode, "fixedInvestment/moreInvestmentIndex");
            }else{
                appUtils.setSStorageInfo("investtype",'1');
                appUtils.setSStorageInfo("querystatus",querystatus);
                appUtils.pageInit(_pageCode, "fixedInvestment/investmentIndex");
            }
        },'click');
        //跳转多基金详情
        appUtils.preBindEvent($(_pageId + " .list"), ".card", function (e) {
            chooseData = JSON.parse($(this).find("em").text())    //拿到当前多基金定投集合数据
            if(chooseData.investtype != '2' || singlePlan)return;
            appUtils.setSStorageInfo("investtype",'2');
            appUtils.pageInit(_pageCode, "fixedInvestment/moreInvestmentDetails",{  //跳转定投详情
                planid:chooseData.planid,
                fundcode:chooseData.fundcode
            });
            sessionStorage.more_planid = chooseData.planid  //缓存多基金定投相关ID
        }, 'click');
        // 点击多基金终止定投
        appUtils.preBindEvent($(_pageId + " .list"), ".card .more_termination", function (e) {
            e.stopPropagation();
            e.preventDefault();
            chooseData = JSON.parse($(this).parents('ul').find("em").text());
            $(_pageId + " #rechargeInfo em").text(chooseData.fundname)
            let strStore = (singlePlan && chooseData.investtype == '2') ? "<span style='display:block;padding-top:0.1rem;'>一键分散定投计划中的其他产品不受影响</span>" : ''
            // if(singlePlan)
            $(_pageId + " .investmentManagement-popup").hide();
            layerUtils.iConfirm("定投计划将于"+ '<span class="m_text_red">'+ tools.FormatDateText(workdate) +'</span>' + "终止，终止日当天如有扣款仍将执行。" + strStore, function () {
            }, function () {
                //吊起密码输入框
                $(_pageId + " .pop_layer").show();
                $(_pageId + " .password_box").show();
                monkeywords.flag = 0;
                passboardEvent();
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "fixedInvestment_investmentList";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
            }, "取消", "确定终止");
        }, 'click');
        // 点击定投管理
        appUtils.preBindEvent($(_pageId + " .list"), ".card #manage", function (e) {
            e.stopPropagation();
            e.preventDefault();
            chooseData = JSON.parse($(this).parents('ul').find("em").text());
            //弹出定投管理弹窗
            $(_pageId + " .investmentManagement-popup").show();
        }, 'click');
        //取消定投弹窗
        appUtils.bindEvent($(_pageId + " .cancel"), function (e) { 
            e.stopPropagation();
            e.preventDefault();
            //弹出定投管理弹窗
            $(_pageId + " .investmentManagement-popup").hide();
        });
        //点击修改定投按钮
        appUtils.bindEvent($(_pageId + " .edit"), function (e) {
            //跳转修改定投页面
            console.log(chooseData,1111)
        }, 'click');
        //点击终止定投按钮
        appUtils.bindEvent($(_pageId + " .termination"), function (e) {
            // chooseData = JSON.parse($(this).parents('ul').find("em").text())
            // console.log(chooseData)
            $(_pageId + " #rechargeInfo em").text(chooseData.fundname)
            let strStore = (singlePlan && chooseData.investtype == '2') ? "<span style='display:block;padding-top:0.1rem;'>一键分散定投计划中的其他产品不受影响</span>" : ''
            // if(singlePlan)
            $(_pageId + " .investmentManagement-popup").hide();
            layerUtils.iConfirm("定投计划将于"+ '<span class="m_text_red">'+ tools.FormatDateText(workdate) +'</span>' + "终止，终止日当天如有扣款仍将执行。" + strStore, function () {
            }, function () {
                //吊起密码输入框
                $(_pageId + " .pop_layer").show();
                $(_pageId + " .password_box").show();
                monkeywords.flag = 0;
                passboardEvent();
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "fixedInvestment_investmentList";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
            }, "取消", "确定终止");
        }, 'click');
        //确定终止
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            // jymm1 = '123123'
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            let param = {
                planid:chooseData.planid + '',  //基金定投计划ID 数字转字符串
                custno:userInfo.custNo,
                fundcode:chooseData.fundcode,
                transpwd:jymm1,
                operationcode:'0'
            }
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.transpwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.transpwd);
                service.reqFun106043(param, (datas) => {
                    if (datas.error_no != '0') {
                        layerUtils.iAlert(datas.error_info);
                        return;
                    }
                    chooseData.exedate = datas.results[0].exedate;
                    chooseData.enddate = datas.results[0].enddate;
                    // chooseData.paymethod = chooseData.paymethod;
                    // console.log(chooseData,333)
                    $(_pageId + ' .list').html('')
                    cur_page = 1;
                    appUtils.pageInit(_pageCode, "fixedInvestment/stopResults",chooseData);
                    // getList(querystatus)
                })
            }, {isLastReq: false});
        });
        //新增定投计划
        appUtils.bindEvent($(_pageId + " .thfundBtn"), function () {
            let info = ut.getUserInf()
            if (!common.loginInter(_pageCode)) return;
            if (!ut.hasBindCard(_pageCode)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if (invalidFlag == '1') return tools.pageTo_evaluation(_pageCode)
            common.changeCardInter(() => {
                let isAdvisoryInvestment = appUtils.getSStorageInfo("isAdvisoryInvestment");
                appUtils.setSStorageInfo("isAdvisoryInvestment", isAdvisoryInvestment);
                appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
            });
            // if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
            // if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            // common.changeCardInter(function () {
            //     if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
            //         layerUtils.iConfirm("您还未进行风险测评", function () {
            //             appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
            //         }, function () {
            //         }, "去测评", "取消");
            //         return;
            //     }
            //     appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
            // });
            // appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment");
        });
        
        //点击进行或终止按钮
        appUtils.bindEvent($(_pageId + " .tab_box a"), function () {
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            if(querystatus != $(this).attr("querystatus")){
                $(_pageId + ' .list').html('')
                querystatus = $(this).attr("querystatus");
                cur_page = 1;
                getList(querystatus)
            }
        }, "click");
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    //页面销毁事件
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .thfundBtn").hide();
        monkeywords.destroy();
        $(_pageId + " .investmentManagement-popup").hide();
        appUtils.setSStorageInfo("querystatus",'0');
        querystatus = '0';
        $(_pageId + " .new_none").html('没有更多数据');
        // isEnd = true;
        isEnd = false;
        cur_page = 1;
        $(_pageId + " .new_none").hide();
        jymm = "";
        $(_pageId + " #jymm").val("");
        $(_pageId + " .list").html("");
        $(_pageId + " .tab_box a").removeClass("current");
        $(_pageId + " .tab_box a").first().addClass('current');    
    }
    function pageBack() {
        if(pageSource == '1' || pageSource == '0'){
            //去个人中心
            pageSource = null;
            appUtils.clearSStorage("pageSource");
            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
            appUtils.pageInit(_pageCode, "account/myAccount");
        }else{
            appUtils.pageBack();
        }
    }

    var investmentList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = investmentList;
});
