//场景产品定投
define(function (require, exports, module) {
    require('../common/vue.min');
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        common = require("common"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        global = gconfig.global,
        //买入定投初始化参数
        fixed_invest_min=0,
        threshold_amount='',
        startBuyMoney='',
        addition_amt='',
        step_amt='',
        product_risk_level='',
        //支付方式
        payMethod=0,
        bank_serial_no = '',
        //日期相关
        investdate='',
        firstText='',
        productInfo={},
        investcycle='',
        investdate='',
        secoundText='',
        //是否开启定投
        buyIsOpenFixed=true,
        //每周定投日期
        weekList = ['', '周一', '周二', '周三', '周四', '周五'],
        //可用份额
        isOpenFixed = true,
        _available_vol='',
        //是否首次购买
        isFirstPurchase=true,
        //补充内容
        supplementaryContent='',
        //风险等级比较
        buyflag='',
        fundCode='',//当前选中的产品
        //银行卡信息
        bankInfo={},
        bank_state='',
        purchase_state='',
        plan_type='',
        //页面 标记页面类型 0持仓 1计划
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#scene_snowballPlan ";
        _pageCode = "scene/snowballPlan";
    var ut = require("../common/userUtil");
    var newstr,str; //
    var get_pdf_file = require("../common/StrongHintPdf");
    var monkeywords = require("../common/moneykeywords");   
    var sms_mobile = require("../common/sms_mobile");
    var tools = require("../common/tools");
    let sceneSnowballPlan //new 一个 vue 实例
    let userInfo,isSeriesComb,bankNameRemark,pdfParam,single_limit,day_limit;
    //银行签约金额限制相关
    let pay_sendsms_amt,pay_mode,pay_modelimitInfo
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    async function init() {
        //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
        bankNameRemark = userInfo.bankName + '(尾号' + bankAcct + ')';
        $(_pageId + " #inputspanidAmt span").addClass("unable").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
        $(_pageId + " #inputspanidInvestAmt span").addClass("unable").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
        //是否为投顾系列产品
        isSeriesComb = '1'
        userInfo = ut.getUserInf();
        let sceneInfo = appUtils.getSStorageInfo("sceneInfo");
        //获取场景产品类型
        plan_type = sceneInfo.plan_type;
        let plan_html = await getTemplate({templateId:sceneInfo.plan_id}) //拿到模板数据
        $("#newPlan").html(plan_html)   //渲染模板
        appUtils.setSStorageInfo("financial_prod_type", '07');
        $(_pageId + " #newPlan").attr("style", `background:${sceneInfo.bgColor}`);
        $(_pageId + " .pageTitle").text(sceneInfo.pageTitle);
        sceneSnowballPlan = new Vue({
            el: '#newPlan',
            data() {
                return {
                    oss_url: global.oss_url,
                    holdList:[],
                    plan_type:plan_type,
                    //客户选中的答案
                    answer:'',
                    //试算比例
                    trial_income_rate:'',
                    //产品名称
                    series_name:'',
                    //产品描述
                    strategy_choice_desc:'',
                    //是否开启定投
                    isOpenFixed:true,
                    //产品列表
                    tgjhList:[],
                    list:[],
                    //当前进行到哪一步
                    // stepValue:1,
                    //计算器相关
                    selectedYear: '30', // 默认选择10年
                    dataList:{
                    "5": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["1.4万",  "7.6万",  "31万"],
                            double: ["1.5万", "8.1万", "33万"],
                            stock: ["1.6万", "8.6万",  "35万"]
                        },
                        "10": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["3万",  "15万",  "55万"],
                            double: ["3.2万", "17万", "63万"],
                            stock: ["3.6万", "19万",  "71万"]
                        },
                        "15": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["4.7万",  "24万",  "83万"],
                            double: ["5.6万", "29万", "101万"],
                            stock: ["6.6万", "34万",  "123万"]
                        },
                        "20": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["6万",  "34万",  "116万"],
                            double: ["8万", "44万",  "151万"],
                            stock: ["10万",  "56万",  "195万"]
                        },
                        "25": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["9万",  "47万",  "155万"],
                            double: ["12万", "63万", "214万"],
                            stock: ["16万", "86万",  "297万"]
                        },
                        "30": { 
                            first_investment: [1000, 10000, 100000], 
                            fixed_investment: [200, 1000, 3000],
                            bond: ["12万", "61万", "200万"],
                            double: ["17万", "89万",  "296万"],
                            stock: ["25万",  "129万",  "441万"]
                        }
                    },
                }
            },
            //视图 渲染前
            created() {
                //获取选中的攒钱计划产品
                productInfo = appUtils.getSStorageInfo("senceProduct");
                this.trial_income_rate = productInfo.trial_income_rate;
                this.series_name = productInfo.series_sname
                fundCode = productInfo.income_prod;
                //初始化页面数据
                pdfParam = {
                    agreement_type: 'prod',
                    agreement_sub_type: '1',
                    fund_code: fundCode,
                    fixed_invest_flag: '1'
                }
            },
            //渲染完成后
            mounted() {
                supplementaryContent = plan_type == '4' ? '存钱罐' : '攒钱计划';
                this.planJoinTimeSelect();
                this.setData(productInfo,{})
            },
            //计算属性
            computed: {
                filteredDataList() {
                    const selectedYearData = this.dataList[this.selectedYear];
                    if (!selectedYearData) return {};
        
                    // 将数据转换为适合表格显示的格式
                    let items = [];
                    for (let i = 0; i < selectedYearData.first_investment.length; i++) {
                        items.push({
                            first_investment: selectedYearData.first_investment[i],
                            fixed_investment: selectedYearData.fixed_investment[i],
                            bond: selectedYearData.bond[i],
                            double: selectedYearData.double[i],
                            stock: selectedYearData.stock[i]
                        });
                    }
        
                    return {[this.selectedYear]: {items}};
                },
                //日期处理
                timeResult: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        if (num1) return tools.ftime(time.substr(num, num1), "-")
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
                threshold_amount_Result: () => {
                    return (threshold_amount) => {
                        if (!threshold_amount) return '--元'
                        threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万元" : tools.fmoney(threshold_amount) + '元';
                        return threshold_amount
                    }
                },
                setNum1: () => {
                    return (str, len) => {
                        if (!str) return '--'
                        return (+str).toFixed(len)
                    }
                }
            },
            //绑定事件
            methods:{
                planJoinTimeSelect() {                   
				    dataArr = [
                        { id: "5", value: "5年", year: "5" },
			            { id: "10", value: "10年", year: "10" },
                        { id: "15", value: "15年", year: "15" },
			            { id: "20", value: "20年", year: "20" },
                        { id: "25", value: "25年", year: "25" },
			            { id: "30", value: "30年", year: "30" }
			        ]
			        
			        tools.mobileSelect({
			            trigger: _pageId + " #planJoinTime",
			            title: "",
			            position:$(_pageId + " #joinTimeData").attr("data-position"),
			            dataArr: dataArr,
			            callback: data => {
                            tools.recordEventData('1','select','选中年限');
			                $(_pageId + " #planJoinTime").html(`<span class="m_font_size16">收益（${data[0].year}年▼）</span>`);
			                $(_pageId + " #planJoinTime").attr("data-id", data[0].id);
			                $(_pageId + " #planJoinTime").attr("data-value", data[0].year);
                            cleartData();
			                this.selectedYear = data[0].id;
			            }
			        });
      
            	},
                //计算财富
                figure_it_out(){
                    tools.recordEventData('1','figure_it_out','计算财富');
                    clickGenerateResult_cal();
                },
                //打开弹框
                // calculatorBtn(){
                //     tools.recordEventData('1','calculatorBtn','打开弹框');
                //     $(_pageId + " .pop_layer1").show();
                //     $(_pageId + " .calculator").show();
                // },
                close_btn(){
                    tools.recordEventData('1','close_btn','关闭弹框');
                    cleartData();
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .calculator").hide();
                },
                // 打开理财计算器页面
                calculatorBtn(){
                    inputInfo(true);
                    tools.recordEventData('1','calculatorBtn','跳转到理财计算器页面');
                    appUtils.setSStorageInfo("calculator_productType", productInfo.trial_income_rate);
                    appUtils.pageInit(_pageCode, "scene/financialCalculator"); 
                },

                //获取验证码
                async getYzm(){
                    var czje_InvestAmt = $(_pageId + " #czje_InvestAmt").val(); //定投金额
                    if(czje_InvestAmt) czje_InvestAmt = czje_InvestAmt.replace(/,/g, "");
                    if(pay_mode == '2' && payMethod == '1' && czje_InvestAmt && parseFloat(czje_InvestAmt) > 0 &&  (parseFloat(czje_InvestAmt) > parseFloat(pay_modelimit))){
                        return layerUtils.iAlert(pay_modelimitInfo);
                    }
                    tools.recordEventData('1','getYzm','获取验证码');
                    var $code = $(_pageId + " #getYzm");
                    if ($code.attr("data-state") == "false") {
                        return;
                    } else {
                        var czje_Amt = $(_pageId + " #czje_Amt").val(); //购买金额
                        var czje_InvestAmt = $(_pageId + " #czje_InvestAmt").val(); //定投金额
                        var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
                        czje_Amt = czje_Amt ? czje_Amt.replace(/,/g, "") : '';
                        if(czje_Amt)
                        if(czje_InvestAmt) czje_InvestAmt = czje_InvestAmt.replace(/,/g, "");
                        //购买相关
                        let name = $(_pageId + " .nick_name").val().trim();
                        if(isSeriesComb == '1' && !name){
                            return layerUtils.iAlert(`请输入${sceneInfo.plan_type == 4 ? '存钱罐名称' : '计划名称'}`);
                        }
                        if(isSeriesComb == '1' && name && name.length > 0){
                            let regex = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
                            if(!regex.test(name)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
                            if(!tools.validateInputName(name)) return layerUtils.iAlert(`${sceneInfo.plan_type == 4 ? '存钱罐名称' : '计划名称'}字数不能超过5个字符，请修改`);
                            let res = await getUserIsNickName() //判断昵称是否重复
                            if(res.existNick > 0) return layerUtils.iAlert(`${sceneInfo.plan_type == 4 ? '存钱罐名称' : '计划名称'}已存在，请重新输入`);
                        }
                        if (czje_Amt <= 0 || !czje_Amt) {
                            layerUtils.iAlert("请输入首次投入金额");
                            return;
                        }
                        if (startBuyMoney && parseFloat(czje_Amt) < parseFloat(startBuyMoney)) {
                            layerUtils.iAlert("购买金额不能低于起购金额");
                            return;
                        }
                        //判断是否提示去充值
                        _available_vol = _available_vol*1; //字符串转数字
                        if(_available_vol < czje_Amt){
                            let operationId = "recharge";
                            return layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>晋金宝可用余额不足，请充值</span>", function () {
                            }, function funcNo() {
                                inputInfo(true);
                                appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
                            }, "取消", "去充值",operationId);
                        }
                        if (tools.isMatchAddAmt(czje_Amt, startBuyMoney, step_amt)) {
                            return;
                        }
                        let bank_first_max_amt = bankInfo.single_limit*1
                        //定投相关
                        if(czje_InvestAmt && czje_InvestAmt >0 && this.isOpenFixed){
                            if (fixed_invest_min && parseFloat(czje_InvestAmt) < parseFloat(fixed_invest_min)) {
                                layerUtils.iAlert("定投金额不能低于起投金额");
                                return;
                            }
                            if (tools.isMatchAddAmt(czje_InvestAmt, fixed_invest_min, step_amt)) {
                                return;
                            }
                            if(pay_mode == '2' && payMethod == '1' && parseFloat(czje_InvestAmt) > parseFloat(pay_modelimit)){
                                return layerUtils.iAlert(pay_modelimitInfo);
                            }
                        }
                        
                        if(payMethod == '1' && czje_InvestAmt > bank_first_max_amt && this.isOpenFixed){
                            return layerUtils.iAlert("定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。");
                        }
                        var param = {
                            "bank_code": userInfo.bankCode,//银行编码
                            "pay_type": bankInfo.pay_type,
                            "payorg_id": bankInfo.payorg_id,
                            "bank_acct": userInfo.bankAcct,     // 用户卡号
                            "bank_reserved_mobile":userInfo.bankReservedMobile,
                            "cert_no": userInfo.identityNum,   // 用户身份证
                            "bank_name":userInfo.bankName,
                            "sms_type":common.sms_type.bankInvestment,
                            "send_type": "0",
                            "cust_name": userInfo.name, // 用户姓名
                            "cert_type": "0", //证件类型
                            "mobile_phone": userInfo.mobileWhole,
                            "type": common.sms_type.bankInvestment,//发送短信验证码
                        }
                        
                        sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                            if (data.error_no == "0") {
                                bank_serial_no = data.results[0].bank_serial_no
                            }else{
                                layerUtils.iAlert(data.error_info);
                            }
                        });
                    }
                },
                //点击购买
                async buy(){
                    tools.recordEventData('1','buy','下一步');
                    // if(this.stepValue && this.stepValue == 1){
                    //     if(!this.answer) return layerUtils.iAlert("请选择产品");
                    //     return this.stepValue = 2;
                    // } 
                    // if(this.holdList && this.holdList.length && $(_pageId + ' .holdList').is(':visible')){
                    //     //如果有持仓且正在展示持仓
                    //     $(_pageId + ' .holdList').hide();
                    //     $(_pageId + ' .mainPage').show();
                    //     //获取定投列表数据
                    //     return this.getSceneList();
                        
                    // }
                    if ($(_pageId + " .buy").hasClass("no_active")) {
                        if (purchase_state == "6") {
                            layerUtils.iAlert("当前时间不支持购买");
                        }
                        return;
                    }
                    //获取验证码
                    var verificationCode = $(_pageId + " #verificationCode").val();
                    //获取首次投入金额
                    var czje_Amt = $(_pageId + " #czje_Amt").val(); //购买金额
                    var czje_InvestAmt = $(_pageId + " #czje_InvestAmt").val(); //定投金额
                    var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
                    czje_Amt = czje_Amt ? czje_Amt.replace(/,/g, "") : '';
                    if(czje_Amt)
                    if(czje_InvestAmt) czje_InvestAmt = czje_InvestAmt.replace(/,/g, "");
                    if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0')) return tools.is_show_c0(_pageCode)
                    if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                        layerUtils.iAlert("请阅读协议并同意签署");
                        return;
                    }
                    //购买相关
                    let name = $(_pageId + " .nick_name").val().trim();
                    
                    if(isSeriesComb == '1' && !name){
                        return layerUtils.iAlert(`请输入${sceneInfo.plan_type == 4 ? '存钱罐名称' : '计划名称'}`);
                    }
                    if(isSeriesComb == '1' && name && name.length > 0){
                        let regex = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
                        if(!regex.test(name)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
                        if(!tools.validateInputName(name)) return layerUtils.iAlert(`${sceneInfo.plan_type == 4 ? '存钱罐名称' : '计划名称'}字数不能超过5个字符，请修改`);
                        let res = await getUserIsNickName() //判断昵称是否重复
                        if(res.existNick > 0) return layerUtils.iAlert(`${sceneInfo.plan_type == 4 ? '存钱罐名称' : '计划名称'}已存在，请重新输入`);
                    }
                    if (czje_Amt <= 0 || !czje_Amt) {
                        // console.log(plan_type)
                        if(plan_type == '4') return layerUtils.iAlert("请输入存入金额");
                        layerUtils.iAlert("请输入投入金额");
                        return;
                    }
                    if (startBuyMoney && parseFloat(czje_Amt) < parseFloat(startBuyMoney)) {
                        layerUtils.iAlert("购买金额不能低于起购金额");
                        return;
                    }
                    //存钱罐
                    // if(sceneInfo.plan_type == '4'){
                    //     let remark = $(_pageId + " .remark").val().trim();
                    //     if(isSeriesComb == '1' && !remark){
                    //         return layerUtils.iAlert("请输入记账内容");
                    //     }
                    // }
                    //判断是否提示去充值
                    _available_vol = _available_vol*1; //字符串转数字
                    if(_available_vol < czje_Amt){
                        let operationId = "recharge";
                        return layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>晋金宝可用余额不足，请充值</span>", function () {
                        }, function funcNo() {
                            inputInfo(true);
                            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
                        }, "取消", "去充值",operationId);
                    }
                    if (tools.isMatchAddAmt(czje_Amt, startBuyMoney, step_amt)) {
                        return
                    }
                    
                    //定投相关
                    if(czje_InvestAmt && czje_InvestAmt >0 && this.isOpenFixed){
                        if (fixed_invest_min && parseFloat(czje_InvestAmt) < parseFloat(fixed_invest_min)) {
                            layerUtils.iAlert("定投金额不能低于起投金额");
                            return;
                        }
                        if (tools.isMatchAddAmt(czje_InvestAmt, fixed_invest_min, step_amt)) {
                            return;
                        }
                        if(pay_mode == '2' && payMethod == '1' && parseFloat(czje_InvestAmt) > parseFloat(pay_modelimit)){
                            return layerUtils.iAlert(pay_modelimitInfo);
                        }
                    }
                    if (buyflag == "1") {
                        let operationId = "riskAssessment";
                        layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>本产品为" + productInfo.comb_risk_name + "等级，超出您的风险承受能力（" + userInfo.riskName + "），若仍选择投资，请重新测评。</span>", function () {
                        }, function funcNo() {
                            // $(_pageId + " .pop_layer").show();
                            // $(_pageId + " #payMethod").show();
                            // //是否可以购买
                            // isCanBuy();
                            inputInfo(true);
                            appUtils.pageInit(_pageCode, "safety/riskQuestion");
                        }, "取消", "重新测评",operationId);
                        return;
                    }
                    let bank_first_max_amt = bankInfo.single_limit*1
                    if(payMethod == '1' && czje_InvestAmt > bank_first_max_amt && this.isOpenFixed){
                        return layerUtils.iAlert("定投金额超过银行卡限额，请选择晋金宝支付或者修改定投金额。");
                    }
                    if(this.isOpenFixed && czje_InvestAmt && payMethod == '1' && bankInfo.is_exist == '0' && isSend == "true"){
                        return layerUtils.iAlert("请获取验证码");
                    }
                    if(this.isOpenFixed && czje_InvestAmt && payMethod == '1' && verificationCode.length != 6 && bankInfo.is_exist == '0') {
                        layerUtils.iMsg(-1, "请输入完整的验证码");
                        return;
                    }
                    
                    if (payMethod == '0') {
                        $(_pageId + " .payMethodName").text('晋金宝');
                        var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
                        $(_pageId + " .payMethodRemark").html(html);
                        // $(_pageId + " .payMethodRemark").html('可用金额:'+tools.fmoney(_available_vol) + "元");
        
                    } else if (payMethod == '1') {
                        let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
                        // $(_pageId + " .payMethodName").text(userInfo.bankName + '(尾号' + bankAcct + ')');
                        $(_pageId + " .payMethodName").text('晋金宝');
                        $(_pageId + " .payMethodRemark").text('限额:');
                    }
                    //渲染密码弹窗
                    let prod_sname;
                    if(isSeriesComb == '1'){
                        //特殊子女产品
                        prod_sname = $(_pageId + " .nick_name").val().trim() + '的' + supplementaryContent + `(${this.series_name})`
                    }else{
                        prod_sname = $(_pageId + " .startCasting_title").html();
                    }
                    $(_pageId + " #recharge_name").text(prod_sname);
                    $(_pageId + " #recharge_money").text($(_pageId + " #czje_Amt").val()?(tools.fmoney($(_pageId + " #czje_Amt").val().replace(/,/g, ""))):'');
                    if(czje_InvestAmt && czje_InvestAmt >0 && this.isOpenFixed){
                        $(_pageId + " .investmentRemark").show();
                        $(_pageId + " .investmentMoney").text($(_pageId + " #czje_InvestAmt").val() ? (tools.fmoney($(_pageId + " #czje_InvestAmt").val().replace(/,/g, ""))) : '');
                        $(_pageId + " .investmentDate").text($(_pageId + " .newData").text());
                        $(_pageId + " .cycle_remark").text(firstText);
                        
                    }else{
                        $(_pageId + " .investmentRemark").hide();
                    }
                    //保存是否开启定投
                    buyIsOpenFixed = this.isOpenFixed;
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " .password_box").show();
                    passboardEvent();
                    monkeywords.flag = 0;
                    //键盘事件
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "scene_snowballPlan";
                    param["eleId"] = "jymm";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "4";
                    require("external").callMessage(param);
                    // $(_pageId + " #cycle").html('<em>' + $(_pageId + " .investmentText").text() + '</em>' + '扣款。')
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " .password_box").show();
                },
                //点击定投金额
                inputspanidInvestAmt(){
                    tools.recordEventData('1','inputspanidInvestAmt','定投金额');
                    let invest_money = $(_pageId + " #czje_InvestAmt").val();
                    let trans_amt = $(_pageId + " #czje_Amt").val();
                    if(!trans_amt || trans_amt == ''){
                        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(200, 200, 200)" });
                        $(_pageId + " #inputspanidAmt span").addClass("unable");
                        $(_pageId + " #inputspanidAmt span").text($(_pageId + " #inputspanidAmt span").attr("text"));
                    }
                    if(!invest_money || invest_money == ''){
                        $(_pageId + " #inputspanidInvestAmt span").attr("text",newstr)
                    }
                    if(!this.isOpenFixed) return layerUtils.iAlert('请开启定投开关');
                    inputSpanEvent("InvestAmt",event)
                },
                inputspanidAmt_cal(){
                    tools.recordEventData('1','inputspanidAmt_cal','首投');
                    let invest_money = $(_pageId + " #czje_InvestAmt_cal").val();
                    let trans_amt = $(_pageId + " #czje_Amt_cal").val();
                    if(!invest_money || invest_money == ''){
                        $(_pageId + " #inputspanidInvestAmt_cal span").css({ color: "rgb(200, 200, 200)" });
                        $(_pageId + " #inputspanidInvestAmt_cal span").addClass("unable");
                        $(_pageId + " #inputspanidInvestAmt_cal span").text($(_pageId + " #inputspanidInvestAmt_cal span").attr("text"));
                    }
                    if(!trans_amt || trans_amt == ''){
                        $(_pageId + " #inputspanidAmt_cal span").attr("text","请输入")
                    }
                    inputSpanEvent_cal("Amt_cal",event)
                },
                inputspanidInvestAmt_cal(){
                    tools.recordEventData('1','inputspanidInvestAmt_cal','定投');
                    let invest_money = $(_pageId + " #czje_InvestAmt_cal").val();
                    let trans_amt = $(_pageId + " #czje_Amt_cal").val();
                    if(!trans_amt || trans_amt == ''){
                        $(_pageId + " #inputspanidAmt_cal span").css({ color: "rgb(200, 200, 200)" });
                        $(_pageId + " #inputspanidAmt_cal span").addClass("unable");
                        $(_pageId + " #inputspanidAmt_cal span").text($(_pageId + " #inputspanidAmt_cal span").attr("text"));
                    }
                    if(!invest_money || invest_money == ''){
                        $(_pageId + " #inputspanidInvestAmt_cal span").attr("text","请输入")
                    }
                    inputSpanEvent_cal("InvestAmt_cal",event)
                },
                //点击投入金额
                inputspanidAmt(){
                    tools.recordEventData('1','inputspanidAmt','投入金额');
                    let invest_money = $(_pageId + " #czje_InvestAmt").val();
                    let trans_amt = $(_pageId + " #czje_Amt").val();
                    if(!invest_money || invest_money == ''){
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(200, 200, 200)" });
                        $(_pageId + " #inputspanidInvestAmt span").addClass("unable");
                        $(_pageId + " #inputspanidInvestAmt span").text($(_pageId + " #inputspanidInvestAmt span").attr("text"));
                    }
                    if(!trans_amt || trans_amt == ''){
                        $(_pageId + " #inputspanidAmt span").attr("text",str)
                    }
                    inputSpanEvent("Amt",event)
                },
                
                //选择定投周期
                cycleClick(){
                    tools.recordEventData('1','cycleClick','定投周期');
                    if(!this.isOpenFixed) return layerUtils.iAlert('请开启定投开关');
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " #cycleModel").show();
                    let positionNum = investdate*1 - 1;
                    $(_pageId + " .listRight").eq(0).scrollTop(45*positionNum);
                },
                //选择支付方式
                choosePayType(){
                    tools.recordEventData('1','choosePayType','支付方式');
                    if(!this.isOpenFixed) return layerUtils.iAlert('请开启定投开关');
                    $(_pageId + " #payMethod").show();
                    $(_pageId + " .pop_layer").show();
                },
                //初始化数据
                async setData(productInfo,info){
                    $(_pageId + " #czje_Amt").val("");
                    $(_pageId + " #czje_InvestAmt").val("");
                    //判断是否首投
                    isFirstPurchase = true; //是否首次购买
                    purchase_state = productInfo.holidays_product_status; //产品状态
                    let purchase_state_class = (purchase_state == "1" || purchase_state == "0") ? "" : "no_active";
                    $(_pageId + " .thfundBtn .buy").removeClass("no_active");
                    $(_pageId + " .thfundBtn .buy").addClass(purchase_state_class);
                    let result = productInfo;
                    threshold_amount = result.page_first_per_min ? result.page_first_per_min : result.first_per_min; // 起购
                    startBuyMoney = threshold_amount;
                    addition_amt = result.con_per_min; // 追加
                    step_amt = result.step_unit; // 递增
                    // let money1 = result.first_per_min*1;
                    // let money2 = result.fixed_invest_min*1;
                    fixed_invest_min = result.fixed_invest_min; // 起投
                    product_risk_level = result.comb_risk_level;
                    /**买入渲染 */
                    str = "";
                    str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
                    if (step_amt && step_amt > 0) {
                        str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                            tools.fmoney(step_amt + ''))
                            + "元递增"
                    }
                    //定投渲染
                    newstr = "";
                    if (threshold_amount && isFirstPurchase) {
                        threshold_amount = result.first_per_min;
                        newstr += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
                    } else if (fixed_invest_min && !isFirstPurchase) {
                        newstr += (fixed_invest_min >= 10000 ? (fixed_invest_min / 10000 + "万") : tools.fmoney(fixed_invest_min)) + '元起购';
                    }
                    if (step_amt && step_amt > 0) {
                        newstr += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                            tools.fmoney(step_amt + ''))
                            + "元递增"
                    }
                    //无试算金额初始化
                    $(_pageId + " #inputspanidAmt span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                    $(_pageId + " #inputspanidAmt span").text(str ? str : '请输入首投金额');
                    $(_pageId + " #inputspanidAmt span").attr("text", str ? str : '请输入首投金额');
                    $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                    $(_pageId + " #inputspanidInvestAmt span").text(newstr ? newstr : '请输入定投金额');
                    $(_pageId + " #inputspanidInvestAmt span").attr("text", newstr ? newstr : '请输入定投金额');
                    //默认使用晋金宝
                    payMethod = '0';
                    let pageInputCasting = appUtils.getSStorageInfo("pageInputCasting") ? appUtils.getSStorageInfo("pageInputCasting") : {};
                    // investdate = pageInputCasting.investdate ? pageInputCasting.investdate : new Date().getDate() < 28 ?  new Date().getDate() + 1 + '' : '1'
                    investdate = pageInputCasting.investdate ? pageInputCasting.investdate : new Date().getDate() < 28 ?  new Date().getDate() + 1 + '' : '1'
                    //渲染默认选中日期
                    $(_pageId + " .newData").text('每月'+ investdate +'日');
                    //渲染日期
                    firstText = '每月';
                    investcycle = '2';
                    secoundText = investdate + '日';
                    //标记客户选中的
                    $(_pageId + " .newData").attr("investcycle",investcycle)
                    $(_pageId + " .newData").attr("investdate",investdate)
                    
                    $(_pageId + " .listLeft li").removeClass('active');
                    $(_pageId + " .listLeft li").first().addClass('active');
                    this.setChooseTime(0);
                    //获取下一扣款日
                    this.getNextTime(investcycle, investdate);
                    //渲染银行卡
                    this.setBankInfo();
                    //可用份额
                    this.reqFun101901();
                    tools.whiteList(_pageId);//白名单
                    sms_mobile.init(_pageId);//初始化发短信
                    //PDF相关，走公共方法
                    // this.is_show_paf();
                    //比较风险等级
                    this.compareRiskLevel();
                    //回填缓存的输入内容
                    pageInfo();
                },
                //比较风险等级
                compareRiskLevel() {
                    var userRiskLevel = userInfo.riskLevel;
                    userRiskLevel = +(userRiskLevel.substr(-1))
                    // product_risk_level = (+product_risk_level.substr(-1));
                    // if (product_risk_level == 1) return;
                    // console.log(userRiskLevel,product_risk_level)
                    if ((product_risk_level*1) > userRiskLevel) {
                        buyflag = "1";
                    }else{
                        buyflag = '0'
                    }
                },
                //立即充值
                rechargeNow(){
                    tools.recordEventData('1','rechargeNow','立即充值');
                    inputInfo(true);
                    appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
                },
                //PDF相关，走公共方法
                is_show_paf() {
                    if(fundCode) get_pdf_file.get_file(fundCode, _pageId, "7")
                },
                reqFun101901() {
                    service.reqFun101901({}, function (data) {
                        if (data.error_no != 0) {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        var results = data.results[0];
                        //可用份额
                        _available_vol = results.available_vol*1;
                        let buymoney = $(_pageId + " #czje_Amt").val();
                        buymoney = buymoney ? (buymoney.replace(/,/g, "")) : '';
                        if(buymoney && _available_vol < buymoney){
                            $(_pageId + " .rechargeNow").show();
                        }else{
                            $(_pageId + " .rechargeNow").hide();
                        }
                        $(_pageId + " .save_available").text(tools.fmoney(_available_vol + ""));
                        var html = '可用金额:<em class="money" style="color:#e5443c">' + tools.fmoney(_available_vol + "") + '</em>元';
                        $(_pageId + " .payMethodRemark").html(html);
                        $(_pageId + " .pay_bank").html(html);
                    })
                },
                //获取银行卡限额
                setBankInfo() {
                    this.get_single_limit()
                    let imgUrl = "images/bank_" + userInfo.bankCode + ".png";
                    let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
                    $(_pageId + " .bankImg").css({ "background": "url('" + imgUrl + "') no-repeat" });
                    $(_pageId + " .backName").html(userInfo.bankName + '(尾号' + bankAcct + ')');
                },
                //获取银行卡限额
                get_single_limit() {
                    let params = {
                        acct_no:userInfo.bankAcct,
                        bank_code:userInfo.bankCode,
                        bank_reserved_mobile:userInfo.bankReservedMobile
                    }
                    service.reqFun101084(params,  (data)=> {
                        if (data.results && data.results.length > 0) {
                            var result = data.results[0];
                            bankInfo = result;
                            let is_bank_fixed_investment = result.is_bank_fixed_investment;//是否支持银行卡定投
                            let fixed_investment_priority = result.fixed_investment_priority;//优先级判断 0 晋金宝 1 银行卡
                            let is_exist = result.is_exist; //是否签约 0未签约 1已签约
                            bank_state = result.bank_state; //0银行维护中，1银行正常
                            pay_mode= result.pay_mode;  //支付模式， 0 签约+支付 1 单独签约
                            pay_modelimit= result.pay_sendsms_amt;  // 单独签约模式下发短信限额
                            pay_modelimitInfo = `定投金额超过${pay_modelimit*1}元，请您使用晋金宝定投。`
                            let backData = appUtils.getSStorageInfo("backData");    //缓存中的页面数据
                            if(is_bank_fixed_investment == '0'){    //不支持
                                pdfParam.bankcard_fixedinvest_flag = '0'
                                payMethod = '0'
                                tools.getPdf(pdfParam); //获取协议
                                $(_pageId + " .payRemark").text("晋金宝")
                                $(_pageId + " .chooseBank").hide();
                                $(_pageId + " .grid_03").hide();
                                $(_pageId + " .chooseBank .img").removeClass('active');
                                $(_pageId + " .chooseJjb .img").addClass('active');
                                $(_pageId + " .chooseJjb").removeClass('borderActive');
                                return;
                            }else{
                                // $(_pageId + " .chooseJjb").addClass('borderActive');
                                // $(_pageId + " .chooseBank").show();
                                // pdfParam.bankcard_fixedinvest_flag = '1'
                                // tools.getPdf(pdfParam); //获取协议
                                $(_pageId + " .chooseBank").show();
                                $(_pageId + " .grid_03").show();
                                $(_pageId + " .chooseJjb").addClass('borderActive');
                                $(_pageId + " .chooseBank .img").addClass('active');
                                $(_pageId + " .chooseJjb .img").removeClass('active');
                            };
                            if(fixed_investment_priority == '0'){   //优先晋金宝
                                pdfParam.bankcard_fixedinvest_flag = '0';
                                payMethod = '0';
                                // tools.getPdf(pdfParam);
                                $(_pageId + " .payRemark").text("晋金宝")
                                $(_pageId + " .chooseBank .img").removeClass('active');
                                $(_pageId + " .chooseJjb .img").addClass('active');
                                $(_pageId + " .grid_03").hide();
                            }else{
                                if(is_exist == '0'){    //未签约，展示验证码
                                    $(_pageId + " .chooseBank").addClass('borderActive');
                                    $(_pageId + " .grid_03").show();
                                }else{
                                    $(_pageId + " .grid_03").hide();
                                }
                                $(_pageId + " .payRemark").text(bankNameRemark)
                                $(_pageId + " .chooseJjb .img").removeClass('active');
                                $(_pageId + " .chooseBank .img").addClass('active');
                                let data = pdfParam;
                                
                                // payMethod = '1'; 
                                if(!backData || backData.backShow != "1") payMethod = '1';
                                data.bankcard_fixedinvest_flag = '1'
                                // tools.getPdf(data);
                            }
                            if(bank_state == '0'){  //维护中
                                bank_state = '0';
                                pdfParam.bankcard_fixedinvest_flag = '0'
                                payMethod = '0'
                                // tools.getPdf(pdfParam); //获取协议
                                
                                $(_pageId + " .payRemark").text('晋金宝');
                                $(_pageId + " .chooseBank .img").removeClass('active');
                                $(_pageId + " .chooseJjb .img").addClass('active');
                                $(_pageId + " .chooseBank").addClass('color_ccc')
                                $(_pageId + " .single_limit").addClass('color_ccc')
                                $(_pageId + " .day_limit").addClass('color_ccc')
                                
                                
                            }else{
                                // tools.getPdf(pdfParam);
                                bank_state = '1';
                                // payMethod = '1';
                                // if(!backData || backData.backShow != "1") payMethod = '1';
                                $(_pageId + " .chooseBank").removeClass('color_ccc')
                                $(_pageId + " .single_limit").removeClass('color_ccc')
                                $(_pageId + " .day_limit").removeClass('color_ccc')
                            }
                            
                            single_limit = result.single_limit;
                            day_limit = result.day_limit;
                            if (single_limit) {
                                if (single_limit < 0) {
                                    $(_pageId + " .single_limit").html("不限");
                                } else {
                                    if (single_limit.length > 4) {
                                        $(_pageId + " .single_limit").html(single_limit / 10000 + "万元");
                                    } else {
                                        $(_pageId + " .single_limit").html(single_limit + "元");
                                    }
                                }
                            }
                            if (day_limit) {
                                if (day_limit < 0) {
                                    $(_pageId + " .day_limit").html("不限");
                                } else {
                                    if (day_limit.length > 4) {
                                        $(_pageId + " .day_limit").html(day_limit / 10000 + "万元");
                                    } else {
                                        $(_pageId + " .day_limit").html(day_limit + "元");
                                    }
                                }
                            }
                            if(backData && backData.backShow == '1'){
                                if(backData.payMethod == '0'){
                                    $(_pageId + " .chooseBank .img").removeClass('active');
                                    $(_pageId + " .chooseJjb .img").addClass('active');
                                    $(_pageId + " .grid_03").hide();
                                    payMethod = '0'
                                }else if(backData.payMethod == '1'){
                                    $(_pageId + " .chooseJjb .img").removeClass('active');
                                    $(_pageId + " .chooseBank .img").addClass('active');
                                    payMethod = '1'
                                    if(backData.is_exist == '0'){
                                        //未签约
                                        $(_pageId + " .chooseBank").addClass('borderActive');
                                        $(_pageId + " .grid_03").show();
                                    }else{
                                        $(_pageId + " .grid_03").hide();
                                        $(_pageId + " .chooseBank").removeClass('borderActive');
                                    }
                                }
                            }
                            let pageInputCasting = appUtils.getSStorageInfo("pageInputCasting") ? appUtils.getSStorageInfo("pageInputCasting") : {};
                            if(pageInputCasting.payMethod == '0'){
                                payMethod = '0'
                                $(_pageId + " .payRemark").text("晋金宝")
                                $(_pageId + " .chooseBank .img").removeClass('active');
                                $(_pageId + " .chooseJjb .img").addClass('active');
                                $(_pageId + " .grid_03").hide();
                            }
                            if(pageInputCasting.payMethod == '1'){
                                payMethod = '1'
                                $(_pageId + " .payRemark").text(bankNameRemark)
                                $(_pageId + " .chooseJjb .img").removeClass('active');
                                $(_pageId + " .chooseBank .img").addClass('active');
                                if(bankInfo.is_exist == '0'){
                                    //未签约
                                    $(_pageId + " .chooseBank").addClass('borderActive');
                                    $(_pageId + " .grid_03").show();
                                }else{
                                    $(_pageId + " .grid_03").hide();
                                    $(_pageId + " .chooseBank").removeClass('borderActive');
                                }
                            }
                            tools.getPdf(pdfParam);
                            if(!backData || backData == 'null') return;
                            backData.backShow = '0'
                            appUtils.setSStorageInfo("backData",backData);
                        } else {
                            layerUtils.iAlert(error_info);
                        }
                    })
                },
                //获取下一扣款日
                getNextTime(investcycle, investdate) {
                    let data = {
                        investcycle: investcycle,
                        investdate: investdate
                    }
                    service.reqFun106045(data, function (datas) {
                        if (datas.error_no != '0') {
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        nextdate = (tools.FormatDateText(datas.results[0].nextdate, 1))
                        // console.log(nextdate)
                        $(_pageId + ' .nextdate').text('下一扣款日' + nextdate)
                    })
                },
                //渲染日
                setChooseTime(num) {
                    let str = ''
                    if (num == 0) {
                        //月
                        if($(_pageId + ' .newData').attr("investcycle") == '2'){
                            investdate = $(_pageId + ' .newData').attr("investdate")
                            
                        }else{
                            investdate = '1'
                        }
                        
                        secoundText = `${investdate}日`
                        for (let i = 1; i <= 28; i++) {
                            let childStr = `<li class="${i == investdate ? 'active' : ''}" id="${i}">${i + '日'}</li>`
                            str += childStr
                        }
                    } else {
                        //周
                        if($(_pageId + ' .newData').attr("investcycle") != '2'){
                            investdate = $(_pageId + ' .newData').attr("investdate")
                        }else{
                            investdate = '1'
                        }
                        secoundText = `${weekList[investdate*1]}`
                        for (let i = 1; i < weekList.length; i++) {
                            let childStr = `<li class="${i == investdate ? 'active' : ''}" id="${i}">${weekList[i]}</li>`
                            str += childStr
                        }
                    }
                    // investdate = '1'
                    $(_pageId + ' .listRight').html(str);
                    if(num == 0){
                        let positionNum = investdate*1 - 1;
                        $(_pageId + " .listRight").eq(0).scrollTop(45*positionNum);
                    }
                },
            }
        })
    }
    //渲染日
    function setChooseTime(num) {
        let str = ''
        if (num == 0) {
            //月
            if($(_pageId + ' .newData').attr("investcycle") == '2'){
                investdate = $(_pageId + ' .newData').attr("investdate")
                
            }else{
                investdate = '1'
            }
            secoundText = `${investdate}日`
            for (let i = 1; i <= 28; i++) {
                let childStr = `<li class="${i == investdate ? 'active' : ''}" id="${i}">${i + '日'}</li>`
                str += childStr
            }
        } else {
            //周
            if($(_pageId + ' .newData').attr("investcycle") != '2'){
                investdate = $(_pageId + ' .newData').attr("investdate")
            }else{
                investdate = '1'
            }
            secoundText = `${weekList[investdate*1]}`
            for (let i = 1; i < weekList.length; i++) {
                let childStr = `<li class="${i == investdate ? 'active' : ''}" id="${i}">${weekList[i]}</li>`
                str += childStr
            }
        }
        // investdate = '1'
        $(_pageId + ' .listRight').html(str)
        if(num == 0){
            let positionNum = investdate*1 - 1;
            $(_pageId + " .listRight").eq(0).scrollTop(45*positionNum);
        }
    }
    //判断昵称是否重复
    async function getUserIsNickName(){
        return new Promise(async (resolve) => {
            let data = {
                series_id:productInfo.prod_id ? productInfo.prod_id : '',
                nick_name:$(_pageId + " .nick_name").val(),
                nick_comb_name:$(_pageId + " .nick_name").val().trim() +`的${supplementaryContent}`
            }
            service.reqFun102195(data, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0])
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    // 生成预算结果
    function clickGenerateResult() {
        tools.recordEventData('1','clickGenerateResult','生成预算结果');
        let amt = $(_pageId + " #czje_Amt").val() ? ($(_pageId + " #czje_Amt").val().replace(/,/g, "")) : '';
        let invest_amt = $(_pageId + " #czje_InvestAmt").val() ? ($(_pageId + " #czje_InvestAmt").val().replace(/,/g, "")) :'';
        let yield = productInfo.trial_income_rate;
        if (amt <= 0 || !amt) {
            // layerUtils.iAlert("请输入首投金额");
            return;
        }
        if (threshold_amount && parseFloat(amt) < parseFloat(threshold_amount)) { //首次购买
            // layerUtils.iAlert(`首投金额不能低于${threshold_amount}元`);
            return;
        }
        if (tools.isMatchAddAmt(amt, threshold_amount, step_amt)) {
            return
        }
        if (invest_amt > 0) {
            if (fixed_invest_min && parseFloat(invest_amt) < parseFloat(fixed_invest_min)) { //首次购买
                // layerUtils.iAlert(`定投金额不能低于${fixed_invest_min}元`);
                return;
            }
            if (tools.isMatchAddAmt(invest_amt, fixed_invest_min, step_amt)) {
                return
            }
        }
        service.reqFun102208({
            amt: amt,
            invest_amt: (invest_amt && invest_amt > 0) ? invest_amt : "0",
            term: '10,30',
            yield: yield
        }, async (data) => {
            if (data.error_no == '0') {
                let results = data.results;
                $(_pageId + " .income10").text((results[0]&&results[0].income)?Math.trunc(results[0].income):'--');
                $(_pageId + " .income30").text((results[1]&&results[1].income)?Math.trunc(results[1].income):'--');
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function pageInfo(){
        let pageInputCasting = appUtils.getSStorageInfo("pageInputCasting") ? appUtils.getSStorageInfo("pageInputCasting") : {};
        if(!pageInputCasting || pageInputCasting == {}) return; //无数据
        $(_pageId + " .income10").text(pageInputCasting.income10 ? pageInputCasting.income10 : '--');
        $(_pageId + " .income30").text(pageInputCasting.income30 ? pageInputCasting.income30 : '--');
        if(pageInputCasting.remark) $(_pageId + " .remark").val(pageInputCasting.remark);
        if(pageInputCasting.nick_name) $(_pageId + " #name").val(pageInputCasting.nick_name);
        if(pageInputCasting.trans_amt){
            $(_pageId + ` #czje_Amt`).val(pageInputCasting.trans_amt ? (tools.fmoney(pageInputCasting.trans_amt).replace(/,/g, "")):'');
            $(_pageId + ` #inputspanidAmt span`).html(tools.fmoney(pageInputCasting.trans_amt));
            $(_pageId + " #inputspanidAmt span").css({ color: "#000" });//默认输入框失去焦点
        } 
        if(pageInputCasting.invest_money){
            $(_pageId + ` #czje_InvestAmt`).val(pageInputCasting.invest_money ? (tools.fmoney(pageInputCasting.invest_money).replace(/,/g, "")):'');
            $(_pageId + ` #inputspanidInvestAmt span`).html(tools.fmoney(pageInputCasting.invest_money));
            $(_pageId + " #inputspanidInvestAmt span").css({ color: "#000" });//默认输入框失去焦点
        }
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () { }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //输入首投/每月定投总额弹出数字键盘
    function inputSpanEvent(id,event) {
        event.stopPropagation();
        $(_pageId + ` #czje_${id}`).val('');
        //键盘事件
        moneyboardEvent(id);
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "scene_snowballPlan";
        param["eleId"] = `czje_${id}`;
        param["doneLable"] = "确定";
        param["keyboardType"] = "3";
        require("external").callMessage(param);
    }
    //页面数据初始化
    function initialization() {
        $(_pageId + " .listLeft li").removeClass('active');
        $(_pageId + " .listLeft li").first().addClass('active');
        $(_pageId + " #amount_enough").removeClass('active');
        firstText = '每月';
        secoundText = investdate + '日';
        payMethod = '0';
    }
    // 金额键盘事件
    function moneyboardEvent(id) {
        _available_vol = _available_vol*1;
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () { // 键盘完成
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var moneys = curVal ? (curVal.replace(/,/g, "")) : '';
                if (id == "Amt") {
                    if(_available_vol < curVal){
                        $(_pageId + " .rechargeNow").show();
                    }else{
                        $(_pageId + " .rechargeNow").hide();
                    }
                    if (tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) {
                        return
                    }
                } else if (id == "") {
                    if (tools.isMatchAddAmt(moneys, fixed_invest_min, step_amt)) {
                        return
                    }
                }
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text(str ? str : '请输入首投金额');
                        $(_pageId + " #inputspanidAmt span").attr("text", str ? str : '请输入首投金额');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text(newstr ? newstr : '请输入定投金额');
                        $(_pageId + " #inputspanidInvestAmt span").attr("text", newstr ? newstr : '请输入定投金额');
                    }
                }
                clickGenerateResult();
                $(_pageId + ` #czje_${id}`).val(moneys);
                
            },
            inputcallback: function () {// 键盘输入
                // 处理单个选择产品金额变化
                var curVal = $(_pageId + ` #czje_${id}`).val();
                curVal = curVal ? (curVal.replace(/,/g, "")) : '';
                if (id == "Amt"){
                    if(_available_vol < curVal){
                        $(_pageId + " .rechargeNow").show();
                    }else{
                        $(_pageId + " .rechargeNow").hide();
                    }
                }else{
                    //定投输入相关
                    if(pay_mode == '2' && payMethod == '1' && parseFloat(curVal) > parseFloat(pay_modelimit)){
                        $(_pageId + ` #czje_${id}`).val('');
                        layerUtils.iAlert(pay_modelimitInfo);
                    }
                }
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + ` #czje_${id}`).val(curVal.substring(0, curVal.length - 1));
                }

            }, // 键盘隐藏
            keyBoardHide: function () {
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                curVal = curVal ? (curVal.replace(/,/g, "")) : '';
                curVal = curVal*1;
                if (id == "Amt"){
                    if(_available_vol < curVal){
                        $(_pageId + " .rechargeNow").show();
                    }else{
                        $(_pageId + " .rechargeNow").hide();
                    }
                }
                var moneys = curVal;
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text(str ? str : '请输入首投金额');
                        $(_pageId + " #inputspanidAmt span").attr("text", str ? str : '请输入首投金额');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text(newstr ? newstr : '请输入定投金额');
                        $(_pageId + " #inputspanidInvestAmt span").attr("text", newstr ? newstr : '请输入定投金额');
                    }
                }
                if (moneys) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(moneys));
                }
            },
        })
    }
    //flag true 存 false 清
    function inputInfo(flag){
        let pageInputCasting
        //缓存当前页面输入内容
        if(flag){
            pageInputCasting = {
                invest_money : $(_pageId + " #czje_InvestAmt").val(),
                trans_amt : $(_pageId + " #czje_Amt").val(),
                nick_name: $(_pageId + " .nick_name").val(),
                remark: $(_pageId + " .remark").val(),
                income10:$(_pageId + " .income10").text(),
                income30:$(_pageId + " .income30").text(),
                investdate:investdate,
                payMethod:payMethod,
                answer:sceneSnowballPlan.answer,
                isOpenFixed:sceneSnowballPlan.isOpenFixed,
                // stepValue:sceneSnowballPlan.stepValue
                // answer:
            } 
        }else{
            pageInputCasting = {}
        }
        appUtils.setSStorageInfo("pageInputCasting", pageInputCasting);
    }
    //获取下一扣款日
    function getNextTime(investcycle, investdate) {
        let data = {
            investcycle: investcycle,
            investdate: investdate
        }
        service.reqFun106045(data, function (datas) {
            if (datas.error_no != '0') {
                layerUtils.iAlert(data.error_info);
                return;
            }
            nextdate = (tools.FormatDateText(datas.results[0].nextdate, 1))
            // console.log(nextdate)
            $(_pageId + ' .nextdate').text('下一扣款日' + nextdate)
        })
    }
    function buy_success(){
        let params
        if(buyIsOpenFixed){
            //已经开启定投
            params = {
                plan_type:plan_type,
                payMethodName:$(_pageId + " .payRemark").text(),
                nextdate: $(_pageId + " .newData").text(),
                payMethod:payMethod,
                remark:$(_pageId + " .remark").val() ? $(_pageId + " .remark").val().trim() : '',
                pageTitle:supplementaryContent,
                prod_sname: isSeriesComb == '1' ? $(_pageId + " .nick_name").val().trim() + `的${supplementaryContent}(${sceneSnowballPlan.series_name})` : $(_pageId + " .startCasting_title").html(),
                czje_Amt: $(_pageId + " #czje_Amt").val() ? (tools.fmoney($(_pageId + " #czje_Amt").val().replace(/,/g, ""))):'', //购买
                czje_InvestAmt: $(_pageId + " #czje_InvestAmt").val() ? (tools.fmoney($(_pageId + " #czje_InvestAmt").val().replace(/,/g, ""))) : '' //定投
            };
        }else{
            //未开启定投
            params = {
                plan_type:plan_type,
                payMethodName:$(_pageId + " .payRemark").text(),
                payMethod:payMethod,
                pageTitle:supplementaryContent,
                prod_sname: isSeriesComb == '1' ? $(_pageId + " .nick_name").val().trim() + `的${supplementaryContent}(${sceneSnowballPlan.series_name})` : $(_pageId + " .startCasting_title").html(),
                czje_Amt: $(_pageId + " #czje_Amt").val() ? (tools.fmoney($(_pageId + " #czje_Amt").val().replace(/,/g, ""))) : '', //购买
                remark:$(_pageId + " .remark").val() ? $(_pageId + " .remark").val().trim() : '',
            };
        }
        
        $(_pageId + " #inputspanidAmt span").addClass("unable");//默认输入框失去焦点
        $(_pageId + " #inputspanidAmt span").css({ color: "rgb(200, 200, 200)" });
        $(_pageId + " #inputspanidInvestAmt span").addClass("unable");//默认输入框失去焦点
        $(_pageId + " #inputspanidInvestAmt span").css({ color: "rgb(200, 200, 200)" });
        $(_pageId + " .grid_03").hide();
        appUtils.setSStorageInfo("tranType",'');
        inputInfo(false);
        appUtils.pageInit("login/userIndexs", "combProduct/castingResult", params);
    }
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //选择周，两周，月
        appUtils.preBindEvent($(_pageId + " .listLeft"), "li", function () {
            $(_pageId + " .listLeft li").removeClass('active')
            $(this).addClass('active')
            investcycle = $(this).attr('id')
            firstText = $(this).text();
            if (investcycle == 2) {
                setChooseTime(0)
            } else {
                setChooseTime()
            }
        }, 'click');
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            let backData = {
                payMethod:payMethod,
                backShow:'1',
                is_exist:bankInfo.is_exist
            }
            appUtils.setSStorageInfo("backData", backData);
            tools.intercommunication(_pageCode);
        });
        //关闭选择支付方式
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //选中支付方式
        appUtils.bindEvent($(_pageId + " .model_bottom"), function (){
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //选择支付方式
        appUtils.preBindEvent($(_pageId + " .payList"), "#amount_enough", function () {
            if($(this).attr('payMethod') == '1' && bank_state == '0') return;
            $(_pageId + " #amount_enough .img").removeClass('active')
            $(this).find(".img").addClass('active')
            payMethod = $(this).attr('payMethod');
            tools.recordEventData('1','amount_enough_' + payMethod,payMethod == 1 ? '银行卡' : '晋金宝');
            if(payMethod == '1'){   //选中银行卡
                $(_pageId + ".payRemark").text(bankNameRemark)
                let data = pdfParam;
                data.bankcard_fixedinvest_flag = '1'
                tools.getPdf(data);
                if(bankInfo.is_exist == '0'){    //未签约，展示验证码
                    $(_pageId + " .chooseBank").addClass('borderActive');
                    $(_pageId + " .grid_03").show();
                }else{
                    $(_pageId + " .grid_03").hide();
                }
                
            }else{
                $(_pageId + ".payRemark").text('晋金宝')
                pdfParam.bankcard_fixedinvest_flag = '0'
                tools.getPdf(pdfParam);
                $(_pageId + " .chooseBank").removeClass('borderActive');
                $(_pageId + " .grid_03").hide();
            }
        }, 'click');
        //选择日期
        appUtils.preBindEvent($(_pageId + " .listRight"), "li", function () {
            $(_pageId + " .listRight li").removeClass('active');
            $(this).addClass('active');
            //文案初始化
            investdate = $(this).attr('id');
            //标记当前选中日期
            secoundText = $(this).text();
            tools.recordEventData('1','chooseData' + investdate,'选择日期_' + secoundText);
        }, 'click');
        //确定选择定投日期
        appUtils.bindEvent($(_pageId + " .determine"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " #cycleModel").hide()
            $(_pageId + " .newData").text(firstText + ' ' + secoundText)
            $(_pageId + " .newData").attr("investcycle",investcycle);
            $(_pageId + " .newData").attr("investdate",investdate);
            getNextTime(investcycle, investdate)
        });
        //关闭周期选择弹框
        appUtils.bindEvent($(_pageId + " #closeCycle"), function () {
            $(_pageId + " #cycleModel").hide();
            $(_pageId + " .pop_layer").hide();
            //重置客户选中的
            investcycle = $(_pageId + " .newData").attr("investcycle");
            investdate = $(_pageId + " .newData").attr("investdate");
            // firstText = ($(_pageId + " .newData").text()).split(' ')[0];
            // secoundText = ($(_pageId + " .newData").text()).split(' ')[1];
            //周期对应文案列表
            let investcycleData = {
                "0":"每周",
                "1":"每两周",
                "2":"每月"
            }
            firstText = investcycleData[investcycle];
            secoundText = investcycle == '2' ? investdate + '日' : '周' + investdate;
            //还原用户之前选择的
            $(_pageId + " .listLeft li").removeClass("active");
            $(_pageId + " .listRight li").removeClass("active");
            // $('#myDiv ul li:nth-child(2)').addClass('new-class');
            if(investcycle == '0'){  //每周
                $(_pageId + " .listLeft li").eq(1).addClass("active");
            }else if(investcycle == '1'){//每两周
                $(_pageId + " .listLeft li").eq(2).addClass("active");
            }else if(investcycle == '2'){//
                $(_pageId + " .listLeft li").eq(0).addClass("active");
            }
            let num = investcycle == '2' ? 0 : 1;
            setChooseTime(num);
        });
        //选择定投日期
        // appUtils.bindEvent($(_pageId + " .cycleClick"), function () {
        //     $(_pageId + " .pop_layer").show();
        //     $(_pageId + " #cycleModel").show()
        // });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "scene_snowballPlan";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //购买
            let invest_money = $(_pageId + " #czje_InvestAmt").val() ? $(_pageId + " #czje_InvestAmt").val() : '';
            var trans_amt = $(_pageId + " #czje_Amt").val();
            invest_money = invest_money ? (invest_money.replace(/,/g, "")) : '';
            trans_amt = trans_amt ? (trans_amt.replace(/,/g, "")) : '';
            var param = {
                fund_code:fundCode,
                m_fund_code:"000709",
                remark:$(_pageId + " .remark").val() ? $(_pageId + " .remark").val().trim() : '',
                nick_name:$(_pageId + " .nick_name").val().trim(),
                nick_comb_name:$(_pageId + " .nick_name").val().trim() +`的${supplementaryContent}`,
                buy_flag: '1',
                series_id:productInfo.prod_id ? productInfo.prod_id : '',
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
                invest_money:(invest_money && buyIsOpenFixed) ? invest_money : '0',
                bankserialno : bank_serial_no,
                trans_amt:trans_amt,
                invest_cycle:investcycle,
                pay_method:payMethod,
                invest_date:investdate,
                trans_pwd:jymm1,
                tg_cust_question_result:productInfo.risk_answer,
                is_exist:bankInfo.is_exist,
                messagecode:$(_pageId + " #verificationCode").val(),
                plan_type:plan_type
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                if(isSeriesComb == '1'){
                    //特殊子女产品
                    service.reqFun102198(param, function (datas) {
                        if(datas.error_no == '-3') return layerUtils.iAlert('超过计划个数上线，详询客服 400-167-8888');
                        if(datas.error_no != '0') {
                            layerUtils.iAlert(datas.error_info);
                            return;
                            //定投+购买成功 跳转结果页
                        }
                        buy_success()
                    })
                }else{
                    service.reqFun106063(param, function (datas) {
                        if (datas.error_no != '0') {
                            layerUtils.iAlert(datas.error_info);
                            return;
                            //定投+购买成功 跳转结果页
                        }
                        buy_success()
                        
                    })
                }
            }, { isLastReq: false });
        });
    }
    function cleartData(){
        $(_pageId + " #inputspanidAmt_cal span").css({ color: "rgb(200, 200, 200)" });
        $(_pageId + " #inputspanidAmt_cal span").addClass("unable");
        $(_pageId + " #inputspanidAmt_cal span").text($(_pageId + " #inputspanidAmt_cal span").attr("text"));
        $(_pageId + " #inputspanidInvestAmt_cal span").css({ color: "rgb(200, 200, 200)" });
        $(_pageId + " #inputspanidInvestAmt_cal span").addClass("unable");
        $(_pageId + " #inputspanidInvestAmt_cal span").text($(_pageId + " #inputspanidInvestAmt_cal span").attr("text"));
        $(_pageId + " .income3").text("--");
        $(_pageId + " .income5").text("--");
        $(_pageId + " .income7").text("--");
        $(_pageId + " #czje_Amt_cal").val('');
        $(_pageId + " #czje_InvestAmt_cal").val('');

    }
      // 生成预算结果
    function clickGenerateResult_cal() {
        tools.recordEventData('1','clickGenerateResult_cal','算一算');
        var threshold_amount = "200";
        var invest_amount = "200";
        var amt = $(_pageId + " #czje_Amt_cal").val().replace(/,/g, "");
        var invest_amt = $(_pageId + " #czje_InvestAmt_cal").val().replace(/,/g, "");
        var term = $(_pageId + " #planJoinTime").attr("data-value");
        if (amt <= 0 || !amt) {
            layerUtils.iAlert("请输入首投金额");
            return;
        }
        if (threshold_amount && parseFloat(amt) < parseFloat(threshold_amount)) { //首次购买
            layerUtils.iAlert(`首投金额不能低于${threshold_amount}元`);
            return;
        }
        if (tools.isMatchAddAmt(amt, threshold_amount, "1")) {
            return
        }
        if (invest_amt > 0) {
            if (invest_amount && parseFloat(invest_amt) < parseFloat(invest_amount)) { //首次购买
                layerUtils.iAlert(`定投金额不能低于${invest_amount}元`);
                return;
            }
            if (tools.isMatchAddAmt(invest_amt, invest_amount, "1")) {
                return
            }
        }
        service.reqFun102213({
            amt: amt,
            invest_amt: invest_amt && invest_amt > 0 ? invest_amt : "0",
            term: term,
            yield: "3%,5%,7%"
        }, async (data) => {
            if (data.error_no == '0') {
                var results = data.results;
                $(_pageId + " .income3").text(tools.fmoney(results[0].income/10000)+"万");
                $(_pageId + " .income5").text(tools.fmoney(results[1].income/10000)+"万");
                $(_pageId + " .income7").text(tools.fmoney(results[2].income/10000)+"万");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }




      //输入首投/每月定投总额弹出数字键盘
    function inputSpanEvent_cal(id,event) {
        event.stopPropagation();
        $(_pageId + ` #czje_${id}`).val('');
        //键盘事件
        moneyboardEvent_cal(id);
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "scene_snowballPlan";
        param["eleId"] = `czje_${id}`;
        param["doneLable"] = "确定";
        param["keyboardType"] = "3";
        $(_pageId + " article").addClass("scene_snowStory_article");
        $(_pageId + " .scene_snowStory_article").scrollTop(300);
        require("external").callMessage(param);
    }
    // 金额键盘事件
    function moneyboardEvent_cal(id) {
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () { // 键盘完成
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var moneys = curVal.replace(/,/g, "");
                if(!moneys && moneys != '0'){
                    if(id == 'Amt_cal') {
                        $(_pageId + " #inputspanidAmt_cal span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt_cal span").text('请输入');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt_cal span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt_cal span").text('请输入');
                    }
                }   
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }              
                $(_pageId + ` #czje_${id}`).val(moneys);
                $(_pageId + " article").removeClass("scene_snowStory_article");
            },
            inputcallback: function () {// 键盘输入
                var curVal = $(_pageId + ` #czje_${id}`).val();
                curVal = curVal.replace(/,/g, "");
               
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + ` #czje_${id}`).val(curVal.substring(0, curVal.length - 1));
                }

            }, // 键盘隐藏
            keyBoardHide: function () {
                $(_pageId + " article").removeClass("scene_snowStory_article");
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                curVal = curVal.replace(/,/g, "");
                curVal = curVal*1;   
                            
                var moneys = curVal;   
                if(!moneys && moneys != '0'){
                    if(id == 'Amt_cal') {
                        $(_pageId + " #inputspanidAmt_cal span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt_cal span").text('请输入');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt_cal span").css({ color: "rgb(200,200,200)" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt_cal span").text('请输入');
                    }
                }             
                if (moneys) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(moneys));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(moneys));
                }
            },
        })
    }
    function pageBack() {
        investcycle = '2';
        investdate = new Date().getDate() < 28 ?  new Date().getDate() + 1 + '' : '1';
        monkeywords.close();
        inputInfo(false);
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .buy span").html('');
        $(_pageId + " .cycle_remark").text('');
        initialization();
        sms_mobile.destroy();
        // if(start_player) start_player.dispose();
        $(_pageId + " .thfundBtn .buy").removeClass('no_active');
        investcycle = '2';
        investdate = new Date().getDate() < 28 ?  new Date().getDate() + 1 + '' : '1';
        monkeywords.close();
        $(_pageId + " .newData").text('每月1日');
        $(_pageId + " #inputspanidAmt span").text("").css({ color: "rgb(200, 200, 200)" });
        $(_pageId + " #inputspanidInvestAmt span").text("").css({ color: "rgb(200, 200, 200)" });
        $(_pageId + " #czje_Amt").val("");
        $(_pageId + " #czje_InvestAmt").val("");
        $(_pageId + " .investmentRemark").hide();
        $(_pageId + " #jymm").val("");
        buyflag = "";
        buy_state = "";
        $(_pageId + " .startCasting_banner").hide();
        monkeywords.destroy();
        _first_max_amt = "";
        productInfo = null;
        $(_pageId + " .jjs_yue").hide();
        $(_pageId + " .agreement_layer").hide();
        $(_pageId + " .rechargeNow").hide();
        $(_pageId + " #nick_name").hide();
        $(_pageId + " .nick_name").val('');
        $(_pageId + " .remark").val('');
        //财富计算器相关
        $(".mobileSelect").remove();
        $(_pageId + " .pop_layer1").hide();
        $(_pageId + " .calculator").hide();
        cleartData();
        // monkeywords.close();
    }
    var scene_snowballPlan = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "inputInfo":inputInfo
    };
    
    // 暴露对外的接口
    module.exports = scene_snowballPlan;
});
