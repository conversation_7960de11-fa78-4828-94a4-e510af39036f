// 联系我们
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        _pageId = "#aboutUs_lxwm ";
    require("../common/clipboard.min.js");

    function init() {
        $(_pageId + " .custServiceTel").text(require("gconfig").global.custServiceTel);
        copyContent("emailCopy");
        copyContent("homepageCopy");
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
        //拨打电话
        appUtils.preBindEvent($(_pageId), ".custServiceTel", function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = require("gconfig").global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });

    }


    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            layerUtils.iAlert("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var aboutJJS = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = aboutJJS;
});