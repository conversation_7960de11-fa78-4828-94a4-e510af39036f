// 分享赢好礼
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        des = require("des"),
        _pageId = "#inviteFriends_friendInvitation ";
    var common = require("common");
    
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var platform = require("gconfig").platform;
    var activities_type;//分享邀请链接的活动类型
    var userInfo;
    var ut = require("../common/userUtil");

    function init() {
        userInfo = ut.getUserInf();
        var symbol = appUtils.getPageParam("symbol");
        activities_type = appUtils.getPageParam("activities_type") || "";
        if (symbol == "show") {
            $(_pageId + " #pop_layer").show();
        } else {
            $(_pageId + " #pop_layer").hide();
        }

        var isShowLayer = appUtils.getPageParam("isShowLayer");
        if (isShowLayer == "show") {
            if (platform == "1") {
                $("#inviteFriends_friendInvitation #app_quanxian").show();
                $("#inviteFriends_friendInvitation #android_quanxian").show();
            }
            if (platform == "2" || platform == "5") {
                $("#inviteFriends_friendInvitation #app_quanxian").show();
                $("#inviteFriends_friendInvitation #ios_quanxian").show();
            }
        }

    }

    //绑定事件
    function bindPageEvent() {

        //2017-7-10 jiaxr 添加客服功能
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = "inviteFriends/friendInvitation";
            tools.saveAlbum("inviteFriends/friendInvitation",param)
        });

        //点击进入我的推荐码
        appUtils.bindEvent($(_pageId + " #clickPersonMessage"), function () {
            // 判断是否登录
            var loginBs = appUtils.getSStorageInfo("isAuthenticated");
            var indexLogin = appUtils.getPageParam("indexLogin");
            if (loginBs || indexLogin != "yes") {
                // 登录跳转到对应的页面
                appUtils.pageInit("inviteFriends/friendInvitation", "account/exhibitionQrcode", {"indexLogin": indexLogin});
            } else {
                common.gestureLogin("account/exhibitionQrcode");
            }
        });
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide();
        });
        //我的奖励
        appUtils.bindEvent($(_pageId + " #myReward"), function () {
            var loginBs = appUtils.getSStorageInfo("isAuthenticated");
            var indexLogin = appUtils.getPageParam("indexLogin");
            if (loginBs) {
                appUtils.setSStorageInfo("saveMoneyFlag",'0');
                appUtils.pageInit("inviteFriends/friendInvitation", "inviteFriends/recommendedEarnings", {"indexLogin": indexLogin});
            } else {
                appUtils.setSStorageInfo("saveMoneyFlag",'0');
                //没登录跳往登陆页面
                common.gestureLogin("inviteFriends/recommendedEarnings");
                //appUtils.pageInit("account/aboutJJS", "login/userLogin",{"skipURL":"account/inputRechargePwd"});
            }
        });
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            common.share("22", "0");
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            common.share("23", "1");
        });
        //腾讯QQ
        appUtils.bindEvent($(_pageId + " #share_qq"), function () {
            common.share("24", "2");
        });

        //新浪微博
        appUtils.bindEvent($(_pageId + " #share_sinaWeibo"), function () {
            common.share("1", "4");
        });
        //腾讯微博
        appUtils.bindEvent($(_pageId + " #share_tenxun"), function () {
            common.share("2", "5");
        });

        //通讯录
        appUtils.bindEvent($(_pageId + " #share_addressBook"), function () {
            appUtils.pageInit("inviteFriends/friendInvitation", "inviteFriends/addressBook");
        });
        //发送邀请图片
        appUtils.bindEvent($(_pageId + " #share_InvitImg"), function () {
            appUtils.pageInit("inviteFriends/friendInvitation", "account/exhibitionImage");
        });
        //邀请好友
        appUtils.bindEvent($(_pageId + " #yq_xhb"), function () {
            // 判断是否登录
            var loginBs = appUtils.getSStorageInfo("isAuthenticated");
            var indexLogin = appUtils.getPageParam("indexLogin");
            if (loginBs || indexLogin != "yes") {
                // 登录跳转到对应的页面
                $(_pageId + " #pop_layer").show();
            } else {
                common.gestureLogin("inviteFriends/friendInvitation");
                // appUtils.pageInit( "inviteFriends/friendInvitation","login/userLogin",{"skipURL":"inviteFriends/friendInvitation"});
            }
        });

        //关闭安卓权限弹框
        appUtils.bindEvent($(_pageId + " #android_quanxian .pop_sure"), function () {
            $(_pageId + " #app_quanxian").hide();
            $(_pageId + " #android_quanxian").hide();
        });

        //关闭IOS权限弹框
        appUtils.bindEvent($(_pageId + " #ios_quanxian .pop_sure"), function () {
            $(_pageId + " #app_quanxian").hide();
            $(_pageId + " #ios_quanxian").hide();
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #app_quanxian").hide();
        $(_pageId + " #android_quanxian").hide();
        $(_pageId + " #ios_quanxian").hide();
    }

    var friendInvitation = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
// 暴露对外的接口
    module.exports = friendInvitation;
})
;
