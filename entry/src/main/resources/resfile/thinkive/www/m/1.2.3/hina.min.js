!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self)["hina-epm"]=t()}(this,(function(){function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?e(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}var h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function d(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var f={exports:{}};!function(e){
/*!mobile-detect v1.4.5 2021-03-13*/
/*!@license Copyright 2013, Heinrich Goebl, License: MIT, see https://github.com/hgoebl/mobile-detect.js*/
!function(e,t){e((function(){var e,n={mobileDetectRules:{phones:{iPhone:"\\biPhone\\b|\\biPod\\b",BlackBerry:"BlackBerry|\\bBB10\\b|rim[0-9]+|\\b(BBA100|BBB100|BBD100|BBE100|BBF100|STH100)\\b-[0-9]+",Pixel:"; \\bPixel\\b",HTC:"HTC|HTC.*(Sensation|Evo|Vision|Explorer|6800|8100|8900|A7272|S510e|C110e|Legend|Desire|T8282)|APX515CKT|Qtek9090|APA9292KT|HD_mini|Sensation.*Z710e|PG86100|Z715e|Desire.*(A8181|HD)|ADR6200|ADR6400L|ADR6425|001HT|Inspire 4G|Android.*\\bEVO\\b|T-Mobile G1|Z520m|Android [0-9.]+; Pixel",Nexus:"Nexus One|Nexus S|Galaxy.*Nexus|Android.*Nexus.*Mobile|Nexus 4|Nexus 5|Nexus 5X|Nexus 6",Dell:"Dell[;]? (Streak|Aero|Venue|Venue Pro|Flash|Smoke|Mini 3iX)|XCD28|XCD35|\\b001DL\\b|\\b101DL\\b|\\bGS01\\b",Motorola:"Motorola|DROIDX|DROID BIONIC|\\bDroid\\b.*Build|Android.*Xoom|HRI39|MOT-|A1260|A1680|A555|A853|A855|A953|A955|A956|Motorola.*ELECTRIFY|Motorola.*i1|i867|i940|MB200|MB300|MB501|MB502|MB508|MB511|MB520|MB525|MB526|MB611|MB612|MB632|MB810|MB855|MB860|MB861|MB865|MB870|ME501|ME502|ME511|ME525|ME600|ME632|ME722|ME811|ME860|ME863|ME865|MT620|MT710|MT716|MT720|MT810|MT870|MT917|Motorola.*TITANIUM|WX435|WX445|XT300|XT301|XT311|XT316|XT317|XT319|XT320|XT390|XT502|XT530|XT531|XT532|XT535|XT603|XT610|XT611|XT615|XT681|XT701|XT702|XT711|XT720|XT800|XT806|XT860|XT862|XT875|XT882|XT883|XT894|XT901|XT907|XT909|XT910|XT912|XT928|XT926|XT915|XT919|XT925|XT1021|\\bMoto E\\b|XT1068|XT1092|XT1052",Samsung:"\\bSamsung\\b|SM-G950F|SM-G955F|SM-G9250|GT-19300|SGH-I337|BGT-S5230|GT-B2100|GT-B2700|GT-B2710|GT-B3210|GT-B3310|GT-B3410|GT-B3730|GT-B3740|GT-B5510|GT-B5512|GT-B5722|GT-B6520|GT-B7300|GT-B7320|GT-B7330|GT-B7350|GT-B7510|GT-B7722|GT-B7800|GT-C3010|GT-C3011|GT-C3060|GT-C3200|GT-C3212|GT-C3212I|GT-C3262|GT-C3222|GT-C3300|GT-C3300K|GT-C3303|GT-C3303K|GT-C3310|GT-C3322|GT-C3330|GT-C3350|GT-C3500|GT-C3510|GT-C3530|GT-C3630|GT-C3780|GT-C5010|GT-C5212|GT-C6620|GT-C6625|GT-C6712|GT-E1050|GT-E1070|GT-E1075|GT-E1080|GT-E1081|GT-E1085|GT-E1087|GT-E1100|GT-E1107|GT-E1110|GT-E1120|GT-E1125|GT-E1130|GT-E1160|GT-E1170|GT-E1175|GT-E1180|GT-E1182|GT-E1200|GT-E1210|GT-E1225|GT-E1230|GT-E1390|GT-E2100|GT-E2120|GT-E2121|GT-E2152|GT-E2220|GT-E2222|GT-E2230|GT-E2232|GT-E2250|GT-E2370|GT-E2550|GT-E2652|GT-E3210|GT-E3213|GT-I5500|GT-I5503|GT-I5700|GT-I5800|GT-I5801|GT-I6410|GT-I6420|GT-I7110|GT-I7410|GT-I7500|GT-I8000|GT-I8150|GT-I8160|GT-I8190|GT-I8320|GT-I8330|GT-I8350|GT-I8530|GT-I8700|GT-I8703|GT-I8910|GT-I9000|GT-I9001|GT-I9003|GT-I9010|GT-I9020|GT-I9023|GT-I9070|GT-I9082|GT-I9100|GT-I9103|GT-I9220|GT-I9250|GT-I9300|GT-I9305|GT-I9500|GT-I9505|GT-M3510|GT-M5650|GT-M7500|GT-M7600|GT-M7603|GT-M8800|GT-M8910|GT-N7000|GT-S3110|GT-S3310|GT-S3350|GT-S3353|GT-S3370|GT-S3650|GT-S3653|GT-S3770|GT-S3850|GT-S5210|GT-S5220|GT-S5229|GT-S5230|GT-S5233|GT-S5250|GT-S5253|GT-S5260|GT-S5263|GT-S5270|GT-S5300|GT-S5330|GT-S5350|GT-S5360|GT-S5363|GT-S5369|GT-S5380|GT-S5380D|GT-S5560|GT-S5570|GT-S5600|GT-S5603|GT-S5610|GT-S5620|GT-S5660|GT-S5670|GT-S5690|GT-S5750|GT-S5780|GT-S5830|GT-S5839|GT-S6102|GT-S6500|GT-S7070|GT-S7200|GT-S7220|GT-S7230|GT-S7233|GT-S7250|GT-S7500|GT-S7530|GT-S7550|GT-S7562|GT-S7710|GT-S8000|GT-S8003|GT-S8500|GT-S8530|GT-S8600|SCH-A310|SCH-A530|SCH-A570|SCH-A610|SCH-A630|SCH-A650|SCH-A790|SCH-A795|SCH-A850|SCH-A870|SCH-A890|SCH-A930|SCH-A950|SCH-A970|SCH-A990|SCH-I100|SCH-I110|SCH-I400|SCH-I405|SCH-I500|SCH-I510|SCH-I515|SCH-I600|SCH-I730|SCH-I760|SCH-I770|SCH-I830|SCH-I910|SCH-I920|SCH-I959|SCH-LC11|SCH-N150|SCH-N300|SCH-R100|SCH-R300|SCH-R351|SCH-R400|SCH-R410|SCH-T300|SCH-U310|SCH-U320|SCH-U350|SCH-U360|SCH-U365|SCH-U370|SCH-U380|SCH-U410|SCH-U430|SCH-U450|SCH-U460|SCH-U470|SCH-U490|SCH-U540|SCH-U550|SCH-U620|SCH-U640|SCH-U650|SCH-U660|SCH-U700|SCH-U740|SCH-U750|SCH-U810|SCH-U820|SCH-U900|SCH-U940|SCH-U960|SCS-26UC|SGH-A107|SGH-A117|SGH-A127|SGH-A137|SGH-A157|SGH-A167|SGH-A177|SGH-A187|SGH-A197|SGH-A227|SGH-A237|SGH-A257|SGH-A437|SGH-A517|SGH-A597|SGH-A637|SGH-A657|SGH-A667|SGH-A687|SGH-A697|SGH-A707|SGH-A717|SGH-A727|SGH-A737|SGH-A747|SGH-A767|SGH-A777|SGH-A797|SGH-A817|SGH-A827|SGH-A837|SGH-A847|SGH-A867|SGH-A877|SGH-A887|SGH-A897|SGH-A927|SGH-B100|SGH-B130|SGH-B200|SGH-B220|SGH-C100|SGH-C110|SGH-C120|SGH-C130|SGH-C140|SGH-C160|SGH-C170|SGH-C180|SGH-C200|SGH-C207|SGH-C210|SGH-C225|SGH-C230|SGH-C417|SGH-C450|SGH-D307|SGH-D347|SGH-D357|SGH-D407|SGH-D415|SGH-D780|SGH-D807|SGH-D980|SGH-E105|SGH-E200|SGH-E315|SGH-E316|SGH-E317|SGH-E335|SGH-E590|SGH-E635|SGH-E715|SGH-E890|SGH-F300|SGH-F480|SGH-I200|SGH-I300|SGH-I320|SGH-I550|SGH-I577|SGH-I600|SGH-I607|SGH-I617|SGH-I627|SGH-I637|SGH-I677|SGH-I700|SGH-I717|SGH-I727|SGH-i747M|SGH-I777|SGH-I780|SGH-I827|SGH-I847|SGH-I857|SGH-I896|SGH-I897|SGH-I900|SGH-I907|SGH-I917|SGH-I927|SGH-I937|SGH-I997|SGH-J150|SGH-J200|SGH-L170|SGH-L700|SGH-M110|SGH-M150|SGH-M200|SGH-N105|SGH-N500|SGH-N600|SGH-N620|SGH-N625|SGH-N700|SGH-N710|SGH-P107|SGH-P207|SGH-P300|SGH-P310|SGH-P520|SGH-P735|SGH-P777|SGH-Q105|SGH-R210|SGH-R220|SGH-R225|SGH-S105|SGH-S307|SGH-T109|SGH-T119|SGH-T139|SGH-T209|SGH-T219|SGH-T229|SGH-T239|SGH-T249|SGH-T259|SGH-T309|SGH-T319|SGH-T329|SGH-T339|SGH-T349|SGH-T359|SGH-T369|SGH-T379|SGH-T409|SGH-T429|SGH-T439|SGH-T459|SGH-T469|SGH-T479|SGH-T499|SGH-T509|SGH-T519|SGH-T539|SGH-T559|SGH-T589|SGH-T609|SGH-T619|SGH-T629|SGH-T639|SGH-T659|SGH-T669|SGH-T679|SGH-T709|SGH-T719|SGH-T729|SGH-T739|SGH-T746|SGH-T749|SGH-T759|SGH-T769|SGH-T809|SGH-T819|SGH-T839|SGH-T919|SGH-T929|SGH-T939|SGH-T959|SGH-T989|SGH-U100|SGH-U200|SGH-U800|SGH-V205|SGH-V206|SGH-X100|SGH-X105|SGH-X120|SGH-X140|SGH-X426|SGH-X427|SGH-X475|SGH-X495|SGH-X497|SGH-X507|SGH-X600|SGH-X610|SGH-X620|SGH-X630|SGH-X700|SGH-X820|SGH-X890|SGH-Z130|SGH-Z150|SGH-Z170|SGH-ZX10|SGH-ZX20|SHW-M110|SPH-A120|SPH-A400|SPH-A420|SPH-A460|SPH-A500|SPH-A560|SPH-A600|SPH-A620|SPH-A660|SPH-A700|SPH-A740|SPH-A760|SPH-A790|SPH-A800|SPH-A820|SPH-A840|SPH-A880|SPH-A900|SPH-A940|SPH-A960|SPH-D600|SPH-D700|SPH-D710|SPH-D720|SPH-I300|SPH-I325|SPH-I330|SPH-I350|SPH-I500|SPH-I600|SPH-I700|SPH-L700|SPH-M100|SPH-M220|SPH-M240|SPH-M300|SPH-M305|SPH-M320|SPH-M330|SPH-M350|SPH-M360|SPH-M370|SPH-M380|SPH-M510|SPH-M540|SPH-M550|SPH-M560|SPH-M570|SPH-M580|SPH-M610|SPH-M620|SPH-M630|SPH-M800|SPH-M810|SPH-M850|SPH-M900|SPH-M910|SPH-M920|SPH-M930|SPH-N100|SPH-N200|SPH-N240|SPH-N300|SPH-N400|SPH-Z400|SWC-E100|SCH-i909|GT-N7100|GT-N7105|SCH-I535|SM-N900A|SGH-I317|SGH-T999L|GT-S5360B|GT-I8262|GT-S6802|GT-S6312|GT-S6310|GT-S5312|GT-S5310|GT-I9105|GT-I8510|GT-S6790N|SM-G7105|SM-N9005|GT-S5301|GT-I9295|GT-I9195|SM-C101|GT-S7392|GT-S7560|GT-B7610|GT-I5510|GT-S7582|GT-S7530E|GT-I8750|SM-G9006V|SM-G9008V|SM-G9009D|SM-G900A|SM-G900D|SM-G900F|SM-G900H|SM-G900I|SM-G900J|SM-G900K|SM-G900L|SM-G900M|SM-G900P|SM-G900R4|SM-G900S|SM-G900T|SM-G900V|SM-G900W8|SHV-E160K|SCH-P709|SCH-P729|SM-T2558|GT-I9205|SM-G9350|SM-J120F|SM-G920F|SM-G920V|SM-G930F|SM-N910C|SM-A310F|GT-I9190|SM-J500FN|SM-G903F|SM-J330F|SM-G610F|SM-G981B|SM-G892A|SM-A530F",LG:"\\bLG\\b;|LG[- ]?(C800|C900|E400|E610|E900|E-900|F160|F180K|F180L|F180S|730|855|L160|LS740|LS840|LS970|LU6200|MS690|MS695|MS770|MS840|MS870|MS910|P500|P700|P705|VM696|AS680|AS695|AX840|C729|E970|GS505|272|C395|E739BK|E960|L55C|L75C|LS696|LS860|P769BK|P350|P500|P509|P870|UN272|US730|VS840|VS950|LN272|LN510|LS670|LS855|LW690|MN270|MN510|P509|P769|P930|UN200|UN270|UN510|UN610|US670|US740|US760|UX265|UX840|VN271|VN530|VS660|VS700|VS740|VS750|VS910|VS920|VS930|VX9200|VX11000|AX840A|LW770|P506|P925|P999|E612|D955|D802|MS323|M257)|LM-G710",Sony:"SonyST|SonyLT|SonyEricsson|SonyEricssonLT15iv|LT18i|E10i|LT28h|LT26w|SonyEricssonMT27i|C5303|C6902|C6903|C6906|C6943|D2533|SOV34|601SO|F8332",Asus:"Asus.*Galaxy|PadFone.*Mobile",Xiaomi:"^(?!.*\\bx11\\b).*xiaomi.*$|POCOPHONE F1|MI 8|Redmi Note 9S|Redmi Note 5A Prime|N2G47H|M2001J2G|M2001J2I|M1805E10A|M2004J11G|M1902F1G|M2002J9G|M2004J19G|M2003J6A1G",NokiaLumia:"Lumia [0-9]{3,4}",Micromax:"Micromax.*\\b(A210|A92|A88|A72|A111|A110Q|A115|A116|A110|A90S|A26|A51|A35|A54|A25|A27|A89|A68|A65|A57|A90)\\b",Palm:"PalmSource|Palm",Vertu:"Vertu|Vertu.*Ltd|Vertu.*Ascent|Vertu.*Ayxta|Vertu.*Constellation(F|Quest)?|Vertu.*Monika|Vertu.*Signature",Pantech:"PANTECH|IM-A850S|IM-A840S|IM-A830L|IM-A830K|IM-A830S|IM-A820L|IM-A810K|IM-A810S|IM-A800S|IM-T100K|IM-A725L|IM-A780L|IM-A775C|IM-A770K|IM-A760S|IM-A750K|IM-A740S|IM-A730S|IM-A720L|IM-A710K|IM-A690L|IM-A690S|IM-A650S|IM-A630K|IM-A600S|VEGA PTL21|PT003|P8010|ADR910L|P6030|P6020|P9070|P4100|P9060|P5000|CDM8992|TXT8045|ADR8995|IS11PT|P2030|P6010|P8000|PT002|IS06|CDM8999|P9050|PT001|TXT8040|P2020|P9020|P2000|P7040|P7000|C790",Fly:"IQ230|IQ444|IQ450|IQ440|IQ442|IQ441|IQ245|IQ256|IQ236|IQ255|IQ235|IQ245|IQ275|IQ240|IQ285|IQ280|IQ270|IQ260|IQ250",Wiko:"KITE 4G|HIGHWAY|GETAWAY|STAIRWAY|DARKSIDE|DARKFULL|DARKNIGHT|DARKMOON|SLIDE|WAX 4G|RAINBOW|BLOOM|SUNSET|GOA(?!nna)|LENNY|BARRY|IGGY|OZZY|CINK FIVE|CINK PEAX|CINK PEAX 2|CINK SLIM|CINK SLIM 2|CINK +|CINK KING|CINK PEAX|CINK SLIM|SUBLIM",iMobile:"i-mobile (IQ|i-STYLE|idea|ZAA|Hitz)",SimValley:"\\b(SP-80|XT-930|SX-340|XT-930|SX-310|SP-360|SP60|SPT-800|SP-120|SPT-800|SP-140|SPX-5|SPX-8|SP-100|SPX-8|SPX-12)\\b",Wolfgang:"AT-B24D|AT-AS50HD|AT-AS40W|AT-AS55HD|AT-AS45q2|AT-B26D|AT-AS50Q",Alcatel:"Alcatel",Nintendo:"Nintendo (3DS|Switch)",Amoi:"Amoi",INQ:"INQ",OnePlus:"ONEPLUS",GenericPhone:"Tapatalk|PDA;|SAGEM|\\bmmp\\b|pocket|\\bpsp\\b|symbian|Smartphone|smartfon|treo|up.browser|up.link|vodafone|\\bwap\\b|nokia|Series40|Series60|S60|SonyEricsson|N900|MAUI.*WAP.*Browser"},tablets:{iPad:"iPad|iPad.*Mobile",NexusTablet:"Android.*Nexus[\\s]+(7|9|10)",GoogleTablet:"Android.*Pixel C",SamsungTablet:"SAMSUNG.*Tablet|Galaxy.*Tab|SC-01C|GT-P1000|GT-P1003|GT-P1010|GT-P3105|GT-P6210|GT-P6800|GT-P6810|GT-P7100|GT-P7300|GT-P7310|GT-P7500|GT-P7510|SCH-I800|SCH-I815|SCH-I905|SGH-I957|SGH-I987|SGH-T849|SGH-T859|SGH-T869|SPH-P100|GT-P3100|GT-P3108|GT-P3110|GT-P5100|GT-P5110|GT-P6200|GT-P7320|GT-P7511|GT-N8000|GT-P8510|SGH-I497|SPH-P500|SGH-T779|SCH-I705|SCH-I915|GT-N8013|GT-P3113|GT-P5113|GT-P8110|GT-N8010|GT-N8005|GT-N8020|GT-P1013|GT-P6201|GT-P7501|GT-N5100|GT-N5105|GT-N5110|SHV-E140K|SHV-E140L|SHV-E140S|SHV-E150S|SHV-E230K|SHV-E230L|SHV-E230S|SHW-M180K|SHW-M180L|SHW-M180S|SHW-M180W|SHW-M300W|SHW-M305W|SHW-M380K|SHW-M380S|SHW-M380W|SHW-M430W|SHW-M480K|SHW-M480S|SHW-M480W|SHW-M485W|SHW-M486W|SHW-M500W|GT-I9228|SCH-P739|SCH-I925|GT-I9200|GT-P5200|GT-P5210|GT-P5210X|SM-T311|SM-T310|SM-T310X|SM-T210|SM-T210R|SM-T211|SM-P600|SM-P601|SM-P605|SM-P900|SM-P901|SM-T217|SM-T217A|SM-T217S|SM-P6000|SM-T3100|SGH-I467|XE500|SM-T110|GT-P5220|GT-I9200X|GT-N5110X|GT-N5120|SM-P905|SM-T111|SM-T2105|SM-T315|SM-T320|SM-T320X|SM-T321|SM-T520|SM-T525|SM-T530NU|SM-T230NU|SM-T330NU|SM-T900|XE500T1C|SM-P605V|SM-P905V|SM-T337V|SM-T537V|SM-T707V|SM-T807V|SM-P600X|SM-P900X|SM-T210X|SM-T230|SM-T230X|SM-T325|GT-P7503|SM-T531|SM-T330|SM-T530|SM-T705|SM-T705C|SM-T535|SM-T331|SM-T800|SM-T700|SM-T537|SM-T807|SM-P907A|SM-T337A|SM-T537A|SM-T707A|SM-T807A|SM-T237|SM-T807P|SM-P607T|SM-T217T|SM-T337T|SM-T807T|SM-T116NQ|SM-T116BU|SM-P550|SM-T350|SM-T550|SM-T9000|SM-P9000|SM-T705Y|SM-T805|GT-P3113|SM-T710|SM-T810|SM-T815|SM-T360|SM-T533|SM-T113|SM-T335|SM-T715|SM-T560|SM-T670|SM-T677|SM-T377|SM-T567|SM-T357T|SM-T555|SM-T561|SM-T713|SM-T719|SM-T813|SM-T819|SM-T580|SM-T355Y?|SM-T280|SM-T817A|SM-T820|SM-W700|SM-P580|SM-T587|SM-P350|SM-P555M|SM-P355M|SM-T113NU|SM-T815Y|SM-T585|SM-T285|SM-T825|SM-W708|SM-T835|SM-T830|SM-T837V|SM-T720|SM-T510|SM-T387V|SM-P610|SM-T290|SM-T515|SM-T590|SM-T595|SM-T725|SM-T817P|SM-P585N0|SM-T395|SM-T295|SM-T865|SM-P610N|SM-P615|SM-T970|SM-T380|SM-T5950|SM-T905|SM-T231|SM-T500|SM-T860",Kindle:"Kindle|Silk.*Accelerated|Android.*\\b(KFOT|KFTT|KFJWI|KFJWA|KFOTE|KFSOWI|KFTHWI|KFTHWA|KFAPWI|KFAPWA|WFJWAE|KFSAWA|KFSAWI|KFASWI|KFARWI|KFFOWI|KFGIWI|KFMEWI)\\b|Android.*Silk/[0-9.]+ like Chrome/[0-9.]+ (?!Mobile)",SurfaceTablet:"Windows NT [0-9.]+; ARM;.*(Tablet|ARMBJS)",HPTablet:"HP Slate (7|8|10)|HP ElitePad 900|hp-tablet|EliteBook.*Touch|HP 8|Slate 21|HP SlateBook 10",AsusTablet:"^.*PadFone((?!Mobile).)*$|Transformer|TF101|TF101G|TF300T|TF300TG|TF300TL|TF700T|TF700KL|TF701T|TF810C|ME171|ME301T|ME302C|ME371MG|ME370T|ME372MG|ME172V|ME173X|ME400C|Slider SL101|\\bK00F\\b|\\bK00C\\b|\\bK00E\\b|\\bK00L\\b|TX201LA|ME176C|ME102A|\\bM80TA\\b|ME372CL|ME560CG|ME372CG|ME302KL| K010 | K011 | K017 | K01E |ME572C|ME103K|ME170C|ME171C|\\bME70C\\b|ME581C|ME581CL|ME8510C|ME181C|P01Y|PO1MA|P01Z|\\bP027\\b|\\bP024\\b|\\bP00C\\b",BlackBerryTablet:"PlayBook|RIM Tablet",HTCtablet:"HTC_Flyer_P512|HTC Flyer|HTC Jetstream|HTC-P715a|HTC EVO View 4G|PG41200|PG09410",MotorolaTablet:"xoom|sholest|MZ615|MZ605|MZ505|MZ601|MZ602|MZ603|MZ604|MZ606|MZ607|MZ608|MZ609|MZ615|MZ616|MZ617",NookTablet:"Android.*Nook|NookColor|nook browser|BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2",AcerTablet:"Android.*; \\b(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700|A701|W500|W500P|W501|W501P|W510|W511|W700|G100|G100W|B1-A71|B1-710|B1-711|A1-810|A1-811|A1-830)\\b|W3-810|\\bA3-A10\\b|\\bA3-A11\\b|\\bA3-A20\\b|\\bA3-A30|A3-A40",ToshibaTablet:"Android.*(AT100|AT105|AT200|AT205|AT270|AT275|AT300|AT305|AT1S5|AT500|AT570|AT700|AT830)|TOSHIBA.*FOLIO",LGTablet:"\\bL-06C|LG-V909|LG-V900|LG-V700|LG-V510|LG-V500|LG-V410|LG-V400|LG-VK810\\b",FujitsuTablet:"Android.*\\b(F-01D|F-02F|F-05E|F-10D|M532|Q572)\\b",PrestigioTablet:"PMP3170B|PMP3270B|PMP3470B|PMP7170B|PMP3370B|PMP3570C|PMP5870C|PMP3670B|PMP5570C|PMP5770D|PMP3970B|PMP3870C|PMP5580C|PMP5880D|PMP5780D|PMP5588C|PMP7280C|PMP7280C3G|PMP7280|PMP7880D|PMP5597D|PMP5597|PMP7100D|PER3464|PER3274|PER3574|PER3884|PER5274|PER5474|PMP5097CPRO|PMP5097|PMP7380D|PMP5297C|PMP5297C_QUAD|PMP812E|PMP812E3G|PMP812F|PMP810E|PMP880TD|PMT3017|PMT3037|PMT3047|PMT3057|PMT7008|PMT5887|PMT5001|PMT5002",LenovoTablet:"Lenovo TAB|Idea(Tab|Pad)( A1|A10| K1|)|ThinkPad([ ]+)?Tablet|YT3-850M|YT3-X90L|YT3-X90F|YT3-X90X|Lenovo.*(S2109|S2110|S5000|S6000|K3011|A3000|A3500|A1000|A2107|A2109|A1107|A5500|A7600|B6000|B8000|B8080)(-|)(FL|F|HV|H|)|TB-X103F|TB-X304X|TB-X304F|TB-X304L|TB-X505F|TB-X505L|TB-X505X|TB-X605F|TB-X605L|TB-8703F|TB-8703X|TB-8703N|TB-8704N|TB-8704F|TB-8704X|TB-8704V|TB-7304F|TB-7304I|TB-7304X|Tab2A7-10F|Tab2A7-20F|TB2-X30L|YT3-X50L|YT3-X50F|YT3-X50M|YT-X705F|YT-X703F|YT-X703L|YT-X705L|YT-X705X|TB2-X30F|TB2-X30L|TB2-X30M|A2107A-F|A2107A-H|TB3-730F|TB3-730M|TB3-730X|TB-7504F|TB-7504X|TB-X704F|TB-X104F|TB3-X70F|TB-X705F|TB-8504F|TB3-X70L|TB3-710F|TB-X704L",DellTablet:"Venue 11|Venue 8|Venue 7|Dell Streak 10|Dell Streak 7",YarvikTablet:"Android.*\\b(TAB210|TAB211|TAB224|TAB250|TAB260|TAB264|TAB310|TAB360|TAB364|TAB410|TAB411|TAB420|TAB424|TAB450|TAB460|TAB461|TAB464|TAB465|TAB467|TAB468|TAB07-100|TAB07-101|TAB07-150|TAB07-151|TAB07-152|TAB07-200|TAB07-201-3G|TAB07-210|TAB07-211|TAB07-212|TAB07-214|TAB07-220|TAB07-400|TAB07-485|TAB08-150|TAB08-200|TAB08-201-3G|TAB08-201-30|TAB09-100|TAB09-211|TAB09-410|TAB10-150|TAB10-201|TAB10-211|TAB10-400|TAB10-410|TAB13-201|TAB274EUK|TAB275EUK|TAB374EUK|TAB462EUK|TAB474EUK|TAB9-200)\\b",MedionTablet:"Android.*\\bOYO\\b|LIFE.*(P9212|P9514|P9516|S9512)|LIFETAB",ArnovaTablet:"97G4|AN10G2|AN7bG3|AN7fG3|AN8G3|AN8cG3|AN7G3|AN9G3|AN7dG3|AN7dG3ST|AN7dG3ChildPad|AN10bG3|AN10bG3DT|AN9G2",IntensoTablet:"INM8002KP|INM1010FP|INM805ND|Intenso Tab|TAB1004",IRUTablet:"M702pro",MegafonTablet:"MegaFon V9|\\bZTE V9\\b|Android.*\\bMT7A\\b",EbodaTablet:"E-Boda (Supreme|Impresspeed|Izzycomm|Essential)",AllViewTablet:"Allview.*(Viva|Alldro|City|Speed|All TV|Frenzy|Quasar|Shine|TX1|AX1|AX2)",ArchosTablet:"\\b(101G9|80G9|A101IT)\\b|Qilive 97R|Archos5|\\bARCHOS (70|79|80|90|97|101|FAMILYPAD|)(b|c|)(G10| Cobalt| TITANIUM(HD|)| Xenon| Neon|XSK| 2| XS 2| PLATINUM| CARBON|GAMEPAD)\\b",AinolTablet:"NOVO7|NOVO8|NOVO10|Novo7Aurora|Novo7Basic|NOVO7PALADIN|novo9-Spark",NokiaLumiaTablet:"Lumia 2520",SonyTablet:"Sony.*Tablet|Xperia Tablet|Sony Tablet S|SO-03E|SGPT12|SGPT13|SGPT114|SGPT121|SGPT122|SGPT123|SGPT111|SGPT112|SGPT113|SGPT131|SGPT132|SGPT133|SGPT211|SGPT212|SGPT213|SGP311|SGP312|SGP321|EBRD1101|EBRD1102|EBRD1201|SGP351|SGP341|SGP511|SGP512|SGP521|SGP541|SGP551|SGP621|SGP641|SGP612|SOT31|SGP771|SGP611|SGP612|SGP712",PhilipsTablet:"\\b(PI2010|PI3000|PI3100|PI3105|PI3110|PI3205|PI3210|PI3900|PI4010|PI7000|PI7100)\\b",CubeTablet:"Android.*(K8GT|U9GT|U10GT|U16GT|U17GT|U18GT|U19GT|U20GT|U23GT|U30GT)|CUBE U8GT",CobyTablet:"MID1042|MID1045|MID1125|MID1126|MID7012|MID7014|MID7015|MID7034|MID7035|MID7036|MID7042|MID7048|MID7127|MID8042|MID8048|MID8127|MID9042|MID9740|MID9742|MID7022|MID7010",MIDTablet:"M9701|M9000|M9100|M806|M1052|M806|T703|MID701|MID713|MID710|MID727|MID760|MID830|MID728|MID933|MID125|MID810|MID732|MID120|MID930|MID800|MID731|MID900|MID100|MID820|MID735|MID980|MID130|MID833|MID737|MID960|MID135|MID860|MID736|MID140|MID930|MID835|MID733|MID4X10",MSITablet:"MSI \\b(Primo 73K|Primo 73L|Primo 81L|Primo 77|Primo 93|Primo 75|Primo 76|Primo 73|Primo 81|Primo 91|Primo 90|Enjoy 71|Enjoy 7|Enjoy 10)\\b",SMiTTablet:"Android.*(\\bMID\\b|MID-560|MTV-T1200|MTV-PND531|MTV-P1101|MTV-PND530)",RockChipTablet:"Android.*(RK2818|RK2808A|RK2918|RK3066)|RK2738|RK2808A",FlyTablet:"IQ310|Fly Vision",bqTablet:"Android.*(bq)?.*\\b(Elcano|Curie|Edison|Maxwell|Kepler|Pascal|Tesla|Hypatia|Platon|Newton|Livingstone|Cervantes|Avant|Aquaris ([E|M]10|M8))\\b|Maxwell.*Lite|Maxwell.*Plus",HuaweiTablet:"MediaPad|MediaPad 7 Youth|IDEOS S7|S7-201c|S7-202u|S7-101|S7-103|S7-104|S7-105|S7-106|S7-201|S7-Slim|M2-A01L|BAH-L09|BAH-W09|AGS-L09|CMR-AL19",NecTablet:"\\bN-06D|\\bN-08D",PantechTablet:"Pantech.*P4100",BronchoTablet:"Broncho.*(N701|N708|N802|a710)",VersusTablet:"TOUCHPAD.*[78910]|\\bTOUCHTAB\\b",ZyncTablet:"z1000|Z99 2G|z930|z990|z909|Z919|z900",PositivoTablet:"TB07STA|TB10STA|TB07FTA|TB10FTA",NabiTablet:"Android.*\\bNabi",KoboTablet:"Kobo Touch|\\bK080\\b|\\bVox\\b Build|\\bArc\\b Build",DanewTablet:"DSlide.*\\b(700|701R|702|703R|704|802|970|971|972|973|974|1010|1012)\\b",TexetTablet:"NaviPad|TB-772A|TM-7045|TM-7055|TM-9750|TM-7016|TM-7024|TM-7026|TM-7041|TM-7043|TM-7047|TM-8041|TM-9741|TM-9747|TM-9748|TM-9751|TM-7022|TM-7021|TM-7020|TM-7011|TM-7010|TM-7023|TM-7025|TM-7037W|TM-7038W|TM-7027W|TM-9720|TM-9725|TM-9737W|TM-1020|TM-9738W|TM-9740|TM-9743W|TB-807A|TB-771A|TB-727A|TB-725A|TB-719A|TB-823A|TB-805A|TB-723A|TB-715A|TB-707A|TB-705A|TB-709A|TB-711A|TB-890HD|TB-880HD|TB-790HD|TB-780HD|TB-770HD|TB-721HD|TB-710HD|TB-434HD|TB-860HD|TB-840HD|TB-760HD|TB-750HD|TB-740HD|TB-730HD|TB-722HD|TB-720HD|TB-700HD|TB-500HD|TB-470HD|TB-431HD|TB-430HD|TB-506|TB-504|TB-446|TB-436|TB-416|TB-146SE|TB-126SE",PlaystationTablet:"Playstation.*(Portable|Vita)",TrekstorTablet:"ST10416-1|VT10416-1|ST70408-1|ST702xx-1|ST702xx-2|ST80208|ST97216|ST70104-2|VT10416-2|ST10216-2A|SurfTab",PyleAudioTablet:"\\b(PTBL10CEU|PTBL10C|PTBL72BC|PTBL72BCEU|PTBL7CEU|PTBL7C|PTBL92BC|PTBL92BCEU|PTBL9CEU|PTBL9CUK|PTBL9C)\\b",AdvanTablet:"Android.* \\b(E3A|T3X|T5C|T5B|T3E|T3C|T3B|T1J|T1F|T2A|T1H|T1i|E1C|T1-E|T5-A|T4|E1-B|T2Ci|T1-B|T1-D|O1-A|E1-A|T1-A|T3A|T4i)\\b ",DanyTechTablet:"Genius Tab G3|Genius Tab S2|Genius Tab Q3|Genius Tab G4|Genius Tab Q4|Genius Tab G-II|Genius TAB GII|Genius TAB GIII|Genius Tab S1",GalapadTablet:"Android [0-9.]+; [a-z-]+; \\bG1\\b",MicromaxTablet:"Funbook|Micromax.*\\b(P250|P560|P360|P362|P600|P300|P350|P500|P275)\\b",KarbonnTablet:"Android.*\\b(A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2)\\b",AllFineTablet:"Fine7 Genius|Fine7 Shine|Fine7 Air|Fine8 Style|Fine9 More|Fine10 Joy|Fine11 Wide",PROSCANTablet:"\\b(PEM63|PLT1023G|PLT1041|PLT1044|PLT1044G|PLT1091|PLT4311|PLT4311PL|PLT4315|PLT7030|PLT7033|PLT7033D|PLT7035|PLT7035D|PLT7044K|PLT7045K|PLT7045KB|PLT7071KG|PLT7072|PLT7223G|PLT7225G|PLT7777G|PLT7810K|PLT7849G|PLT7851G|PLT7852G|PLT8015|PLT8031|PLT8034|PLT8036|PLT8080K|PLT8082|PLT8088|PLT8223G|PLT8234G|PLT8235G|PLT8816K|PLT9011|PLT9045K|PLT9233G|PLT9735|PLT9760G|PLT9770G)\\b",YONESTablet:"BQ1078|BC1003|BC1077|RK9702|BC9730|BC9001|IT9001|BC7008|BC7010|BC708|BC728|BC7012|BC7030|BC7027|BC7026",ChangJiaTablet:"TPC7102|TPC7103|TPC7105|TPC7106|TPC7107|TPC7201|TPC7203|TPC7205|TPC7210|TPC7708|TPC7709|TPC7712|TPC7110|TPC8101|TPC8103|TPC8105|TPC8106|TPC8203|TPC8205|TPC8503|TPC9106|TPC9701|TPC97101|TPC97103|TPC97105|TPC97106|TPC97111|TPC97113|TPC97203|TPC97603|TPC97809|TPC97205|TPC10101|TPC10103|TPC10106|TPC10111|TPC10203|TPC10205|TPC10503",GUTablet:"TX-A1301|TX-M9002|Q702|kf026",PointOfViewTablet:"TAB-P506|TAB-navi-7-3G-M|TAB-P517|TAB-P-527|TAB-P701|TAB-P703|TAB-P721|TAB-P731N|TAB-P741|TAB-P825|TAB-P905|TAB-P925|TAB-PR945|TAB-PL1015|TAB-P1025|TAB-PI1045|TAB-P1325|TAB-PROTAB[0-9]+|TAB-PROTAB25|TAB-PROTAB26|TAB-PROTAB27|TAB-PROTAB26XL|TAB-PROTAB2-IPS9|TAB-PROTAB30-IPS9|TAB-PROTAB25XXL|TAB-PROTAB26-IPS10|TAB-PROTAB30-IPS10",OvermaxTablet:"OV-(SteelCore|NewBase|Basecore|Baseone|Exellen|Quattor|EduTab|Solution|ACTION|BasicTab|TeddyTab|MagicTab|Stream|TB-08|TB-09)|Qualcore 1027",HCLTablet:"HCL.*Tablet|Connect-3G-2.0|Connect-2G-2.0|ME Tablet U1|ME Tablet U2|ME Tablet G1|ME Tablet X1|ME Tablet Y2|ME Tablet Sync",DPSTablet:"DPS Dream 9|DPS Dual 7",VistureTablet:"V97 HD|i75 3G|Visture V4( HD)?|Visture V5( HD)?|Visture V10",CrestaTablet:"CTP(-)?810|CTP(-)?818|CTP(-)?828|CTP(-)?838|CTP(-)?888|CTP(-)?978|CTP(-)?980|CTP(-)?987|CTP(-)?988|CTP(-)?989",MediatekTablet:"\\bMT8125|MT8389|MT8135|MT8377\\b",ConcordeTablet:"Concorde([ ]+)?Tab|ConCorde ReadMan",GoCleverTablet:"GOCLEVER TAB|A7GOCLEVER|M1042|M7841|M742|R1042BK|R1041|TAB A975|TAB A7842|TAB A741|TAB A741L|TAB M723G|TAB M721|TAB A1021|TAB I921|TAB R721|TAB I720|TAB T76|TAB R70|TAB R76.2|TAB R106|TAB R83.2|TAB M813G|TAB I721|GCTA722|TAB I70|TAB I71|TAB S73|TAB R73|TAB R74|TAB R93|TAB R75|TAB R76.1|TAB A73|TAB A93|TAB A93.2|TAB T72|TAB R83|TAB R974|TAB R973|TAB A101|TAB A103|TAB A104|TAB A104.2|R105BK|M713G|A972BK|TAB A971|TAB R974.2|TAB R104|TAB R83.3|TAB A1042",ModecomTablet:"FreeTAB 9000|FreeTAB 7.4|FreeTAB 7004|FreeTAB 7800|FreeTAB 2096|FreeTAB 7.5|FreeTAB 1014|FreeTAB 1001 |FreeTAB 8001|FreeTAB 9706|FreeTAB 9702|FreeTAB 7003|FreeTAB 7002|FreeTAB 1002|FreeTAB 7801|FreeTAB 1331|FreeTAB 1004|FreeTAB 8002|FreeTAB 8014|FreeTAB 9704|FreeTAB 1003",VoninoTablet:"\\b(Argus[ _]?S|Diamond[ _]?79HD|Emerald[ _]?78E|Luna[ _]?70C|Onyx[ _]?S|Onyx[ _]?Z|Orin[ _]?HD|Orin[ _]?S|Otis[ _]?S|SpeedStar[ _]?S|Magnet[ _]?M9|Primus[ _]?94[ _]?3G|Primus[ _]?94HD|Primus[ _]?QS|Android.*\\bQ8\\b|Sirius[ _]?EVO[ _]?QS|Sirius[ _]?QS|Spirit[ _]?S)\\b",ECSTablet:"V07OT2|TM105A|S10OT1|TR10CS1",StorexTablet:"eZee[_']?(Tab|Go)[0-9]+|TabLC7|Looney Tunes Tab",VodafoneTablet:"SmartTab([ ]+)?[0-9]+|SmartTabII10|SmartTabII7|VF-1497|VFD 1400",EssentielBTablet:"Smart[ ']?TAB[ ]+?[0-9]+|Family[ ']?TAB2",RossMoorTablet:"RM-790|RM-997|RMD-878G|RMD-974R|RMT-705A|RMT-701|RME-601|RMT-501|RMT-711",iMobileTablet:"i-mobile i-note",TolinoTablet:"tolino tab [0-9.]+|tolino shine",AudioSonicTablet:"\\bC-22Q|T7-QC|T-17B|T-17P\\b",AMPETablet:"Android.* A78 ",SkkTablet:"Android.* (SKYPAD|PHOENIX|CYCLOPS)",TecnoTablet:"TECNO P9|TECNO DP8D",JXDTablet:"Android.* \\b(F3000|A3300|JXD5000|JXD3000|JXD2000|JXD300B|JXD300|S5800|S7800|S602b|S5110b|S7300|S5300|S602|S603|S5100|S5110|S601|S7100a|P3000F|P3000s|P101|P200s|P1000m|P200m|P9100|P1000s|S6600b|S908|P1000|P300|S18|S6600|S9100)\\b",iJoyTablet:"Tablet (Spirit 7|Essentia|Galatea|Fusion|Onix 7|Landa|Titan|Scooby|Deox|Stella|Themis|Argon|Unique 7|Sygnus|Hexen|Finity 7|Cream|Cream X2|Jade|Neon 7|Neron 7|Kandy|Scape|Saphyr 7|Rebel|Biox|Rebel|Rebel 8GB|Myst|Draco 7|Myst|Tab7-004|Myst|Tadeo Jones|Tablet Boing|Arrow|Draco Dual Cam|Aurix|Mint|Amity|Revolution|Finity 9|Neon 9|T9w|Amity 4GB Dual Cam|Stone 4GB|Stone 8GB|Andromeda|Silken|X2|Andromeda II|Halley|Flame|Saphyr 9,7|Touch 8|Planet|Triton|Unique 10|Hexen 10|Memphis 4GB|Memphis 8GB|Onix 10)",FX2Tablet:"FX2 PAD7|FX2 PAD10",XoroTablet:"KidsPAD 701|PAD[ ]?712|PAD[ ]?714|PAD[ ]?716|PAD[ ]?717|PAD[ ]?718|PAD[ ]?720|PAD[ ]?721|PAD[ ]?722|PAD[ ]?790|PAD[ ]?792|PAD[ ]?900|PAD[ ]?9715D|PAD[ ]?9716DR|PAD[ ]?9718DR|PAD[ ]?9719QR|PAD[ ]?9720QR|TelePAD1030|Telepad1032|TelePAD730|TelePAD731|TelePAD732|TelePAD735Q|TelePAD830|TelePAD9730|TelePAD795|MegaPAD 1331|MegaPAD 1851|MegaPAD 2151",ViewsonicTablet:"ViewPad 10pi|ViewPad 10e|ViewPad 10s|ViewPad E72|ViewPad7|ViewPad E100|ViewPad 7e|ViewSonic VB733|VB100a",VerizonTablet:"QTAQZ3|QTAIR7|QTAQTZ3|QTASUN1|QTASUN2|QTAXIA1",OdysTablet:"LOOX|XENO10|ODYS[ -](Space|EVO|Xpress|NOON)|\\bXELIO\\b|Xelio10Pro|XELIO7PHONETAB|XELIO10EXTREME|XELIOPT2|NEO_QUAD10",CaptivaTablet:"CAPTIVA PAD",IconbitTablet:"NetTAB|NT-3702|NT-3702S|NT-3702S|NT-3603P|NT-3603P|NT-0704S|NT-0704S|NT-3805C|NT-3805C|NT-0806C|NT-0806C|NT-0909T|NT-0909T|NT-0907S|NT-0907S|NT-0902S|NT-0902S",TeclastTablet:"T98 4G|\\bP80\\b|\\bX90HD\\b|X98 Air|X98 Air 3G|\\bX89\\b|P80 3G|\\bX80h\\b|P98 Air|\\bX89HD\\b|P98 3G|\\bP90HD\\b|P89 3G|X98 3G|\\bP70h\\b|P79HD 3G|G18d 3G|\\bP79HD\\b|\\bP89s\\b|\\bA88\\b|\\bP10HD\\b|\\bP19HD\\b|G18 3G|\\bP78HD\\b|\\bA78\\b|\\bP75\\b|G17s 3G|G17h 3G|\\bP85t\\b|\\bP90\\b|\\bP11\\b|\\bP98t\\b|\\bP98HD\\b|\\bG18d\\b|\\bP85s\\b|\\bP11HD\\b|\\bP88s\\b|\\bA80HD\\b|\\bA80se\\b|\\bA10h\\b|\\bP89\\b|\\bP78s\\b|\\bG18\\b|\\bP85\\b|\\bA70h\\b|\\bA70\\b|\\bG17\\b|\\bP18\\b|\\bA80s\\b|\\bA11s\\b|\\bP88HD\\b|\\bA80h\\b|\\bP76s\\b|\\bP76h\\b|\\bP98\\b|\\bA10HD\\b|\\bP78\\b|\\bP88\\b|\\bA11\\b|\\bA10t\\b|\\bP76a\\b|\\bP76t\\b|\\bP76e\\b|\\bP85HD\\b|\\bP85a\\b|\\bP86\\b|\\bP75HD\\b|\\bP76v\\b|\\bA12\\b|\\bP75a\\b|\\bA15\\b|\\bP76Ti\\b|\\bP81HD\\b|\\bA10\\b|\\bT760VE\\b|\\bT720HD\\b|\\bP76\\b|\\bP73\\b|\\bP71\\b|\\bP72\\b|\\bT720SE\\b|\\bC520Ti\\b|\\bT760\\b|\\bT720VE\\b|T720-3GE|T720-WiFi",OndaTablet:"\\b(V975i|Vi30|VX530|V701|Vi60|V701s|Vi50|V801s|V719|Vx610w|VX610W|V819i|Vi10|VX580W|Vi10|V711s|V813|V811|V820w|V820|Vi20|V711|VI30W|V712|V891w|V972|V819w|V820w|Vi60|V820w|V711|V813s|V801|V819|V975s|V801|V819|V819|V818|V811|V712|V975m|V101w|V961w|V812|V818|V971|V971s|V919|V989|V116w|V102w|V973|Vi40)\\b[\\s]+|V10 \\b4G\\b",JaytechTablet:"TPC-PA762",BlaupunktTablet:"Endeavour 800NG|Endeavour 1010",DigmaTablet:"\\b(iDx10|iDx9|iDx8|iDx7|iDxD7|iDxD8|iDsQ8|iDsQ7|iDsQ8|iDsD10|iDnD7|3TS804H|iDsQ11|iDj7|iDs10)\\b",EvolioTablet:"ARIA_Mini_wifi|Aria[ _]Mini|Evolio X10|Evolio X7|Evolio X8|\\bEvotab\\b|\\bNeura\\b",LavaTablet:"QPAD E704|\\bIvoryS\\b|E-TAB IVORY|\\bE-TAB\\b",AocTablet:"MW0811|MW0812|MW0922|MTK8382|MW1031|MW0831|MW0821|MW0931|MW0712",MpmanTablet:"MP11 OCTA|MP10 OCTA|MPQC1114|MPQC1004|MPQC994|MPQC974|MPQC973|MPQC804|MPQC784|MPQC780|\\bMPG7\\b|MPDCG75|MPDCG71|MPDC1006|MP101DC|MPDC9000|MPDC905|MPDC706HD|MPDC706|MPDC705|MPDC110|MPDC100|MPDC99|MPDC97|MPDC88|MPDC8|MPDC77|MP709|MID701|MID711|MID170|MPDC703|MPQC1010",CelkonTablet:"CT695|CT888|CT[\\s]?910|CT7 Tab|CT9 Tab|CT3 Tab|CT2 Tab|CT1 Tab|C820|C720|\\bCT-1\\b",WolderTablet:"miTab \\b(DIAMOND|SPACE|BROOKLYN|NEO|FLY|MANHATTAN|FUNK|EVOLUTION|SKY|GOCAR|IRON|GENIUS|POP|MINT|EPSILON|BROADWAY|JUMP|HOP|LEGEND|NEW AGE|LINE|ADVANCE|FEEL|FOLLOW|LIKE|LINK|LIVE|THINK|FREEDOM|CHICAGO|CLEVELAND|BALTIMORE-GH|IOWA|BOSTON|SEATTLE|PHOENIX|DALLAS|IN 101|MasterChef)\\b",MediacomTablet:"M-MPI10C3G|M-SP10EG|M-SP10EGP|M-SP10HXAH|M-SP7HXAH|M-SP10HXBH|M-SP8HXAH|M-SP8MXA",MiTablet:"\\bMI PAD\\b|\\bHM NOTE 1W\\b",NibiruTablet:"Nibiru M1|Nibiru Jupiter One",NexoTablet:"NEXO NOVA|NEXO 10|NEXO AVIO|NEXO FREE|NEXO GO|NEXO EVO|NEXO 3G|NEXO SMART|NEXO KIDDO|NEXO MOBI",LeaderTablet:"TBLT10Q|TBLT10I|TBL-10WDKB|TBL-10WDKBO2013|TBL-W230V2|TBL-W450|TBL-W500|SV572|TBLT7I|TBA-AC7-8G|TBLT79|TBL-8W16|TBL-10W32|TBL-10WKB|TBL-W100",UbislateTablet:"UbiSlate[\\s]?7C",PocketBookTablet:"Pocketbook",KocasoTablet:"\\b(TB-1207)\\b",HisenseTablet:"\\b(F5281|E2371)\\b",Hudl:"Hudl HT7S3|Hudl 2",TelstraTablet:"T-Hub2",GenericTablet:"Android.*\\b97D\\b|Tablet(?!.*PC)|BNTV250A|MID-WCDMA|LogicPD Zoom2|\\bA7EB\\b|CatNova8|A1_07|CT704|CT1002|\\bM721\\b|rk30sdk|\\bEVOTAB\\b|M758A|ET904|ALUMIUM10|Smartfren Tab|Endeavour 1010|Tablet-PC-4|Tagi Tab|\\bM6pro\\b|CT1020W|arc 10HD|\\bTP750\\b|\\bQTAQZ3\\b|WVT101|TM1088|KT107"},oss:{AndroidOS:"Android",BlackBerryOS:"blackberry|\\bBB10\\b|rim tablet os",PalmOS:"PalmOS|avantgo|blazer|elaine|hiptop|palm|plucker|xiino",SymbianOS:"Symbian|SymbOS|Series60|Series40|SYB-[0-9]+|\\bS60\\b",WindowsMobileOS:"Windows CE.*(PPC|Smartphone|Mobile|[0-9]{3}x[0-9]{3})|Windows Mobile|Windows Phone [0-9.]+|WCE;",WindowsPhoneOS:"Windows Phone 10.0|Windows Phone 8.1|Windows Phone 8.0|Windows Phone OS|XBLWP7|ZuneWP7|Windows NT 6.[23]; ARM;",iOS:"\\biPhone.*Mobile|\\biPod|\\biPad|AppleCoreMedia",iPadOS:"CPU OS 13",SailfishOS:"Sailfish",MeeGoOS:"MeeGo",MaemoOS:"Maemo",JavaOS:"J2ME/|\\bMIDP\\b|\\bCLDC\\b",webOS:"webOS|hpwOS",badaOS:"\\bBada\\b",BREWOS:"BREW"},uas:{Chrome:"\\bCrMo\\b|CriOS|Android.*Chrome/[.0-9]* (Mobile)?",Dolfin:"\\bDolfin\\b",Opera:"Opera.*Mini|Opera.*Mobi|Android.*Opera|Mobile.*OPR/[0-9.]+$|Coast/[0-9.]+",Skyfire:"Skyfire",Edge:"\\bEdgiOS\\b|Mobile Safari/[.0-9]* Edge",IE:"IEMobile|MSIEMobile",Firefox:"fennec|firefox.*maemo|(Mobile|Tablet).*Firefox|Firefox.*Mobile|FxiOS",Bolt:"bolt",TeaShark:"teashark",Blazer:"Blazer",Safari:"Version((?!\\bEdgiOS\\b).)*Mobile.*Safari|Safari.*Mobile|MobileSafari",WeChat:"\\bMicroMessenger\\b",UCBrowser:"UC.*Browser|UCWEB",baiduboxapp:"baiduboxapp",baidubrowser:"baidubrowser",DiigoBrowser:"DiigoBrowser",Mercury:"\\bMercury\\b",ObigoBrowser:"Obigo",NetFront:"NF-Browser",GenericBrowser:"NokiaBrowser|OviBrowser|OneBrowser|TwonkyBeamBrowser|SEMC.*Browser|FlyFlow|Minimo|NetFront|Novarra-Vision|MQQBrowser|MicroMessenger",PaleMoon:"Android.*PaleMoon|Mobile.*PaleMoon"},props:{Mobile:"Mobile/[VER]",Build:"Build/[VER]",Version:"Version/[VER]",VendorID:"VendorID/[VER]",iPad:"iPad.*CPU[a-z ]+[VER]",iPhone:"iPhone.*CPU[a-z ]+[VER]",iPod:"iPod.*CPU[a-z ]+[VER]",Kindle:"Kindle/[VER]",Chrome:["Chrome/[VER]","CriOS/[VER]","CrMo/[VER]"],Coast:["Coast/[VER]"],Dolfin:"Dolfin/[VER]",Firefox:["Firefox/[VER]","FxiOS/[VER]"],Fennec:"Fennec/[VER]",Edge:"Edge/[VER]",IE:["IEMobile/[VER];","IEMobile [VER]","MSIE [VER];","Trident/[0-9.]+;.*rv:[VER]"],NetFront:"NetFront/[VER]",NokiaBrowser:"NokiaBrowser/[VER]",Opera:[" OPR/[VER]","Opera Mini/[VER]","Version/[VER]"],"Opera Mini":"Opera Mini/[VER]","Opera Mobi":"Version/[VER]",UCBrowser:["UCWEB[VER]","UC.*Browser/[VER]"],MQQBrowser:"MQQBrowser/[VER]",MicroMessenger:"MicroMessenger/[VER]",baiduboxapp:"baiduboxapp/[VER]",baidubrowser:"baidubrowser/[VER]",SamsungBrowser:"SamsungBrowser/[VER]",Iron:"Iron/[VER]",Safari:["Version/[VER]","Safari/[VER]"],Skyfire:"Skyfire/[VER]",Tizen:"Tizen/[VER]",Webkit:"webkit[ /][VER]",PaleMoon:"PaleMoon/[VER]",SailfishBrowser:"SailfishBrowser/[VER]",Gecko:"Gecko/[VER]",Trident:"Trident/[VER]",Presto:"Presto/[VER]",Goanna:"Goanna/[VER]",iOS:" \\bi?OS\\b [VER][ ;]{1}",Android:"Android [VER]",Sailfish:"Sailfish [VER]",BlackBerry:["BlackBerry[\\w]+/[VER]","BlackBerry.*Version/[VER]","Version/[VER]"],BREW:"BREW [VER]",Java:"Java/[VER]","Windows Phone OS":["Windows Phone OS [VER]","Windows Phone [VER]"],"Windows Phone":"Windows Phone [VER]","Windows CE":"Windows CE/[VER]","Windows NT":"Windows NT [VER]",Symbian:["SymbianOS/[VER]","Symbian/[VER]"],webOS:["webOS/[VER]","hpwOS/[VER];"]},utils:{Bot:"Googlebot|facebookexternalhit|Google-AMPHTML|s~amp-validator|AdsBot-Google|Google Keyword Suggestion|Facebot|YandexBot|YandexMobileBot|bingbot|ia_archiver|AhrefsBot|Ezooms|GSLFbot|WBSearchBot|Twitterbot|TweetmemeBot|Twikle|PaperLiBot|Wotbox|UnwindFetchor|Exabot|MJ12bot|YandexImages|TurnitinBot|Pingdom|contentkingapp|AspiegelBot",MobileBot:"Googlebot-Mobile|AdsBot-Google-Mobile|YahooSeeker/M1A1-R2D2",DesktopMode:"WPDesktop",TV:"SonyDTV|HbbTV",WebKit:"(webkit)[ /]([\\w.]+)",Console:"\\b(Nintendo|Nintendo WiiU|Nintendo 3DS|Nintendo Switch|PLAYSTATION|Xbox)\\b",Watch:"SM-V700"}},detectMobileBrowsers:{fullPattern:/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,shortPattern:/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,tabletPattern:/android|ipad|playbook|silk/i}},r=Object.prototype.hasOwnProperty;function i(e,t){return null!=e&&null!=t&&e.toLowerCase()===t.toLowerCase()}function o(e,t){var n,r,i=e.length;if(!i||!t)return!1;for(n=t.toLowerCase(),r=0;r<i;++r)if(n===e[r].toLowerCase())return!0;return!1}function a(e){for(var t in e)r.call(e,t)&&(e[t]=new RegExp(e[t],"i"))}function s(e,t){this.ua=function(e){return(e||"").substr(0,500)}(e),this._cache={},this.maxPhoneWidth=t||600}return n.FALLBACK_PHONE="UnknownPhone",n.FALLBACK_TABLET="UnknownTablet",n.FALLBACK_MOBILE="UnknownMobile",e="isArray"in Array?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},function(){var t,i,o,s,c,l,u=n.mobileDetectRules;for(t in u.props)if(r.call(u.props,t)){for(i=u.props[t],e(i)||(i=[i]),c=i.length,s=0;s<c;++s)(l=(o=i[s]).indexOf("[VER]"))>=0&&(o=o.substring(0,l)+"([\\w._\\+]+)"+o.substring(l+5)),i[s]=new RegExp(o,"i");u.props[t]=i}a(u.oss),a(u.phones),a(u.tablets),a(u.uas),a(u.utils),u.oss0={WindowsPhoneOS:u.oss.WindowsPhoneOS,WindowsMobileOS:u.oss.WindowsMobileOS}}(),n.findMatch=function(e,t){for(var n in e)if(r.call(e,n)&&e[n].test(t))return n;return null},n.findMatches=function(e,t){var n=[];for(var i in e)r.call(e,i)&&e[i].test(t)&&n.push(i);return n},n.getVersionStr=function(e,t){var i,o,a,s,c=n.mobileDetectRules.props;if(r.call(c,e))for(a=(i=c[e]).length,o=0;o<a;++o)if(null!==(s=i[o].exec(t)))return s[1];return null},n.getVersion=function(e,t){var r=n.getVersionStr(e,t);return r?n.prepareVersionNo(r):NaN},n.prepareVersionNo=function(e){var t;return 1===(t=e.split(/[a-z._ \/\-]/i)).length&&(e=t[0]),t.length>1&&(e=t[0]+".",t.shift(),e+=t.join("")),Number(e)},n.isMobileFallback=function(e){return n.detectMobileBrowsers.fullPattern.test(e)||n.detectMobileBrowsers.shortPattern.test(e.substr(0,4))},n.isTabletFallback=function(e){return n.detectMobileBrowsers.tabletPattern.test(e)},n.prepareDetectionCache=function(e,r,i){if(e.mobile===t){var o,a,c;if(a=n.findMatch(n.mobileDetectRules.tablets,r))return e.mobile=e.tablet=a,void(e.phone=null);if(o=n.findMatch(n.mobileDetectRules.phones,r))return e.mobile=e.phone=o,void(e.tablet=null);n.isMobileFallback(r)?(c=s.isPhoneSized(i))===t?(e.mobile=n.FALLBACK_MOBILE,e.tablet=e.phone=null):c?(e.mobile=e.phone=n.FALLBACK_PHONE,e.tablet=null):(e.mobile=e.tablet=n.FALLBACK_TABLET,e.phone=null):n.isTabletFallback(r)?(e.mobile=e.tablet=n.FALLBACK_TABLET,e.phone=null):e.mobile=e.tablet=e.phone=null}},n.mobileGrade=function(e){var t=null!==e.mobile();return e.os("iOS")&&e.version("iPad")>=4.3||e.os("iOS")&&e.version("iPhone")>=3.1||e.os("iOS")&&e.version("iPod")>=3.1||e.version("Android")>2.1&&e.is("Webkit")||e.version("Windows Phone OS")>=7||e.is("BlackBerry")&&e.version("BlackBerry")>=6||e.match("Playbook.*Tablet")||e.version("webOS")>=1.4&&e.match("Palm|Pre|Pixi")||e.match("hp.*TouchPad")||e.is("Firefox")&&e.version("Firefox")>=12||e.is("Chrome")&&e.is("AndroidOS")&&e.version("Android")>=4||e.is("Skyfire")&&e.version("Skyfire")>=4.1&&e.is("AndroidOS")&&e.version("Android")>=2.3||e.is("Opera")&&e.version("Opera Mobi")>11&&e.is("AndroidOS")||e.is("MeeGoOS")||e.is("Tizen")||e.is("Dolfin")&&e.version("Bada")>=2||(e.is("UC Browser")||e.is("Dolfin"))&&e.version("Android")>=2.3||e.match("Kindle Fire")||e.is("Kindle")&&e.version("Kindle")>=3||e.is("AndroidOS")&&e.is("NookTablet")||e.version("Chrome")>=11&&!t||e.version("Safari")>=5&&!t||e.version("Firefox")>=4&&!t||e.version("MSIE")>=7&&!t||e.version("Opera")>=10&&!t?"A":e.os("iOS")&&e.version("iPad")<4.3||e.os("iOS")&&e.version("iPhone")<3.1||e.os("iOS")&&e.version("iPod")<3.1||e.is("Blackberry")&&e.version("BlackBerry")>=5&&e.version("BlackBerry")<6||e.version("Opera Mini")>=5&&e.version("Opera Mini")<=6.5&&(e.version("Android")>=2.3||e.is("iOS"))||e.match("NokiaN8|NokiaC7|N97.*Series60|Symbian/3")||e.version("Opera Mobi")>=11&&e.is("SymbianOS")?"B":(e.version("BlackBerry")<5||e.match("MSIEMobile|Windows CE.*Mobile")||e.version("Windows Mobile"),"C")},n.detectOS=function(e){return n.findMatch(n.mobileDetectRules.oss0,e)||n.findMatch(n.mobileDetectRules.oss,e)},n.getDeviceSmallerSide=function(){return window.screen.width<window.screen.height?window.screen.width:window.screen.height},s.prototype={constructor:s,mobile:function(){return n.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.mobile},phone:function(){return n.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.phone},tablet:function(){return n.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.tablet},userAgent:function(){return this._cache.userAgent===t&&(this._cache.userAgent=n.findMatch(n.mobileDetectRules.uas,this.ua)),this._cache.userAgent},userAgents:function(){return this._cache.userAgents===t&&(this._cache.userAgents=n.findMatches(n.mobileDetectRules.uas,this.ua)),this._cache.userAgents},os:function(){return this._cache.os===t&&(this._cache.os=n.detectOS(this.ua)),this._cache.os},version:function(e){return n.getVersion(e,this.ua)},versionStr:function(e){return n.getVersionStr(e,this.ua)},is:function(e){return o(this.userAgents(),e)||i(e,this.os())||i(e,this.phone())||i(e,this.tablet())||o(n.findMatches(n.mobileDetectRules.utils,this.ua),e)},match:function(e){return e instanceof RegExp||(e=new RegExp(e,"i")),e.test(this.ua)},isPhoneSized:function(e){return s.isPhoneSized(e||this.maxPhoneWidth)},mobileGrade:function(){return this._cache.grade===t&&(this._cache.grade=n.mobileGrade(this)),this._cache.grade}},"undefined"!=typeof window&&window.screen?s.isPhoneSized=function(e){return e<0?t:n.getDeviceSmallerSide()<=e}:s.isPhoneSized=function(){},s._impl=n,s.version="1.4.5 2021-03-13",s}))}(function(t){if(e.exports)return function(t){e.exports=t()};if("undefined"!=typeof window)return function(e){window.MobileDetect=e()};throw new Error("unknown environment")}())}(f);var T=d(f.exports),g=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],p=["www.baidu.","m.baidu.","m.sm.cn","so.com","sogou.com","youdao.com","google.","yahoo.com/","bing.com/","ask.com/"],m=["weibo.com","renren.com","kaixin001.com","douban.com","qzone.qq.com","zhihu.com","tieba.baidu.com","weixin.qq.com"],S={baidu:["wd","word","kw","keyword"],google:"q",bing:"q",yahoo:"p",sogou:["query","keyword"],so:"q",sm:"q"},v="hinasdk_domain_test",b=Array.prototype.slice,P=Object.prototype.toString,y=Array.prototype.forEach,A=Object.prototype.hasOwnProperty,M=window,k=M.location,E=M.screen,H=M.localStorage,C=M.history,G=M.navigator,w=function(){function e(){i(this,e)}return a(e,null,[{key:"log",value:function(){if(!this.showLog)return!1;if("object"===("undefined"==typeof console?"undefined":r(console))&&console.log)try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}}}]),e}(),I=function(){function e(){i(this,e)}return a(e,null,[{key:"checkProtocolIsSame",value:function(e,t){try{if(B.URL(e).protocol!==B.URL(t).protocol)return!1}catch(e){return w.log("The _.URL method is not supported"),!1}return!0}},{key:"checkServerUrl",value:function(){return B.check.isString(this.serverUrl)&&""!==B.trim(this.serverUrl)?(B.check.isString(this.serverUrl)&&""!==this.serverUrl&&!this.checkProtocolIsSame(this.serverUrl,k.href)&&w.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。\n因为：1、https 下面发送 http 的图片请求会失败。2、http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。"),!0):(w.log("当前 serverUrl 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 serverUrl！"),!1)}},{key:"checkAjax",value:function(e){if(e===this.serverUrl)return!1;B.check.isString(e)&&""!==e&&!this.checkProtocolIsSame(e,k.href)&&w.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。因为 http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。")}}]),e}(),B={MAX_REFERRER_STRING_LENGTH:2e3,PV_LIB_VERSION:"3.1.6",EPM_LIB_VERSION:"1.0.0"};B.utmTypes=g,B.searchTypes=p,B.socialTypes=m,B.searchKeywords=S,B.each=function(e,t,n){if(null!==e)if(y&&e.forEach===y)e.forEach(t,n);else if(e.length===+e.length){for(var r=0,i=e.length;r<i;r++)if(r in e&&!1===t.call(n,e[r],r,e))return}else for(var o in e)if(A.call(e,o)&&!1===t.call(n,e[o],o,e))return},B.map=function(e,t){var n=[];return null==e?n:Array.prototype.map&&e.map===Array.prototype.map?e.map(t):(B.each(e,(function(e,r,i){n.push(t(e,r,i))})),n)},B.extend=function(e){return B.each(b.call(arguments,1),(function(t){for(var n in t)void 0!==t[n]&&((B.check.isString(t[n])||B.check.isDate(t[n]))&&B.transformUTCTime(t[n])?e[n]=B.transformUTCTime(t[n]):e[n]=t[n])})),e},B.transformUTCTime=function(e){if(B.check.isDate(e)||null!=e&&e.includes("GMT")||/^\d{4}-\d{2}-\d{2}(?: |T)\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(e)){var t=new Date(e);return t.setHours(t.getHours()-t.getTimezoneOffset()/60),t.toISOString().replace("T"," ").replace("Z"," ").slice(0,-5)}return!1},B.indexOf=function(e,t){var n=e.indexOf;if(n)return n.call(e,t);for(var r=0;r<e.length;r++)if(t===e[r])return r;return-1},B.trim=function(e){return e.replace(/(^[\s\uFEFF\xA0]+)|([\s\uFEFF\xA0]+$)/g,"")},B.arrayify=function(e){return Array.isArray(e)?e:[e]},B.isNil=function(e){return null==e},B.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+function(e){return e<10?"00"+e:e<100?"0"+e:e}(e.getMilliseconds())},B.formatTimeZone=function(e,t){if("number"!=typeof t)return e;var n=e.getTime(),r=6e4*e.getTimezoneOffset();return new Date(n+r+36e5*t)},B.formatJsonString=function(e){try{return JSON.stringify(e,null,8)}catch(t){return JSON.stringify(e)}},B.searchObjDate=function(e,t){(B.check.isObject(e)||B.check.isArray(e))&&B.each(e,(function(n,r){B.check.isObject(n)||B.check.isArray(n)?B.searchObjDate(e[r],t):B.check.isDate(n)&&(e[r]=B.formatDate(B.formatTimeZone(n,t)))}))},B.paramType=function(e){return Object.prototype.toString.call(e).replace("[object ","").replace("]","")},B.check={isUndefined:function(e){return void 0===e},isObject:function(e){return"[object Object]"===P.call(e)&&null!==e},isEmptyObject:function(e){if(B.check.isObject(e)){for(var t in e)if(A.call(e,t))return!1;return!0}return!1},isArray:function(e){return"[object Array]"===P.call(e)},isString:function(e){return"[object String]"===P.call(e)},isDate:function(e){return"[object Date]"===P.call(e)},isNumber:function(e){return"[object Number]"===P.call(e)},isBoolean:function(e){return"[object Boolean]"===P.call(e)},isFunction:function(e){if(!e)return!1;var t=Object.prototype.toString.call(e);return"[object Function]"===t||"[object AsyncFunction]"===t},isJSONString:function(e){try{JSON.parse(e)}catch(e){return!1}return!0},isElement:function(e){return!(!e||1!==e.nodeType)}},B.UUID=function(){var e=function(){for(var e=1*new Date,t=0;e===1*new Date;)t++;return e.toString(16)+t.toString(16)};return function(){var t=String(E.height*E.width);t=t&&/\d{5,}/.test(t)?t.toString(16):String(31242*Math.random()).replace(".","").slice(0,8);var n=e()+"-"+Math.random().toString(16).replace(".","")+"-"+function(){var e,t,n=G.userAgent,r=[],i=0;function o(e,t){var n,i=0;for(n=0;n<t.length;n++)i|=r[n]<<8*n;return e^i}for(e=0;e<n.length;e++)t=n.charCodeAt(e),r.unshift(255&t),r.length>=4&&(i=o(i,r),r=[]);return r.length>0&&(i=o(i,r)),i.toString(16)}()+"-"+t+"-"+e();return n||(String(Math.random())+String(Math.random())+String(Math.random())).slice(2,15)}}(),B.getReferrer=function(e){var t=e||document.referrer;return"string"!=typeof t?"referrer exception"+String(t):(0===t.indexOf("https://www.baidu.com/")&&(t=t.split("?")[0]),"string"==typeof(t=t.slice(0,2e3))?t:"")},B.getCookielDomain=function(e){e=e||k.hostname,B.check.isString(e)&&e.match(/^[a-zA-Z0-9\u4e00-\u9fa5\-\.]+$/)||(e="");var t=e.split(".");if(B.check.isArray(t)&&t.length>=2&&!/^(\d+\.)+\d+$/.test(e))for(var n="."+t.splice(t.length-1,1);t.length>0;)if(n="."+t.splice(t.length-1,1)+n,document.cookie=v+"=true; path=/; domain="+n,-1!==document.cookie.indexOf(v+"=true")){var r=new Date;return r.setTime(r.getTime()-1e3),document.cookie=v+"=true; expires="+r.toGMTString()+"; path=/; SameSite=Lax; domain="+n,n}return""},B.getCurrentDomain=function(e){var t=B.getCookielDomain();return""===e||""===t?"url解析失败":t},B.hashCode=function(e){if("string"!=typeof e)return 0;var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return t},B.base64Decode=function(e){var t="";try{t=decodeURIComponent(escape(atob(e))).replace(/\s/g,"+")}catch(n){t=e}return t},B.base64Encode=function(e){try{return btoa(unescape(encodeURIComponent(e))).replace(/\s/g,"+")}catch(t){return e}},B.decodeURIComponent=function(e){var t="";try{t=decodeURIComponent(e)}catch(n){t=e}return t},B.encodeURIComponent=function(e){var t="";try{t=encodeURIComponent(e)}catch(n){t=e}return t},B.cookie={get:function(e){for(var t=e+"=",n=document.cookie.split(";"),r=0;r<n.length;r++){for(var i=n[r];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(t))return B.decodeURIComponent(i.substring(t.length,i.length))}return null},set:function(e,t,n,r,i,o,a){var s=a,c="",l="",u="";if(0!==(n=null==n?73e3:n)){var h=new Date;"s"===String(n).slice(-1)?h.setTime(h.getTime()+1e3*Number(String(n).slice(0,-1))):h.setTime(h.getTime()+24*n*60*60*1e3),c="; expires="+h.toGMTString()}function d(e){return!!e&&e.replace(/\r\n/g,"")}B.check.isString(i)&&""!==i&&(u="; SameSite="+i),o&&(l="; secure");var f="",T="",g="";e&&(f=d(e)),T=t&&B.check.isString(t)?d(t):t,s&&(g=d(s)),f&&T&&(document.cookie=f+"="+encodeURIComponent(T)+c+"; path=/"+g+u+l)},setDomain:function(e,t,n,r){var i="";if(r=!!B.check.isUndefined(r)||r){var o=B.getCurrentDomain(k.href);"url解析失败"===o&&(o=""),i=o?"; domain="+o:""}return this.set(e,t,n,r,null,null,i)},remove:function(e,t){this.set(e,"1",-1,t)},isSupport:function(e,t){e=e||"cookie_support_test",t=t||"1";var n=this;return G.cookieEnabled&&(n.set(e,t),n.get(e)===t&&(n.remove(e),!0))}},B.localStorage={get:function(e){return H.getItem(e)},parse:function(e){var t;try{t=JSON.parse(B.localStorage.get(e))||null}catch(e){w.log("parse localStorage failed")}return t},set:function(e,t){try{H.setItem(e,t)}catch(e){w.log("localStorage is not support")}},remove:function(e){H.removeItem(e)},isSupport:function(){var e=!0;try{var t="__localStorageSupport__",n="testIsSupportStorage";B.localStorage.set(t,n),B.localStorage.get(t)!==n&&(e=!1),B.localStorage.remove(t)}catch(t){e=!1}return e},key:function(e){return H.key(e)},length:H.length},B.memory={data:{},get:function(e){var t=this.data[e];return B.check.isUndefined(t)?null:B.check.isUndefined(t.expireTime)?t:t.expireTime<B.now()?null:t.value},set:function(e,t,n){if(n){var r,i=B.now();r="s"===String(n).slice(-1)?i+1e3*Number(String(n).slice(0,-1)):i+24*n*60*60*1e3,this.data[e]={value:t,expireTime:r}}this.data[e]=t},setDomain:function(e,t,n){this.set(e,t,n)}},B.now=function(){return Date.now&&B.check.isFunction(Date.now)?Date.now():(new Date).getTime()},B.getRandom=function(){return(new Date).getTime()+"_"+Math.floor(1e6*Math.random())},B.get32RandomString=function(){for(var e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",t="",n=0;n<32;n++){t+=e[Math.floor(62*Math.random())]}return t},B.safeJSONParse=function(e){var t=null;try{t=JSON.parse(e)}catch(t){return e}return t},B.saveObjectVal=function(e,t,n){B.check.isString(t)||(t=JSON.stringify(t)),B.localStorage.set(e,t)},B.readObjectVal=function(e,t){var n=B.localStorage.get(e);return n?B.safeJSONParse(n):null},B.stripEmptyProperties=function(e){var t={};return B.each(e,(function(e,n){B.check.isString(e)&&e.length>0&&(t[n]=e)})),t},B.info={os:function(){var e=G.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)&&!window.MSStream?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"Mac OS X":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"Chrome OS":"取值异常"},browser:function(){var e={type:"",version:""};try{var t,n=null===(t=G.userAgent)||void 0===t?void 0:t.toLowerCase(),r=[];if(null!==n.match(/baidubrowser/)?(e.type="baidu",r.push(/baidubrowser\/([\d.]+)/)):null!==n.match(/bidubrowser/)?(e.type="baidu",r.push(/bidubrowser\/([\d.]+)/)):null!==n.match(/edg/)?(e.type="edge",r.push(/edg\/([\d.]+)/)):null!==n.match(/edgios/)?(e.type="edge",r.push(/edgios\/([\d.]+)/)):null!==n.match(/liebaofast/)?(e.type="liebao",r.push(/liebaofast\/([\d.]+)/)):null!==n.match(/sogoumobilebrowser/)?(e.type="sogou",r.push(/sogoumobilebrowser\/([\d.]+)/)):null!==n.match(/lbbrowser/)?(e.type="liebao",r.push(/lbbrowser\/([\d.]+)/)):null!==n.match(/crios/)?(e.type="chrome",r.push(/crios\/([\d.]+)/)):null!==n.match(/qihoobrowser/)?(e.type="360",r.push(/qihoobrowser\/([\d.]+)/)):null!==n.match(/mxios/)?(e.type="maxthon",r.push(/mxios\/([\d.]+)/)):null!==n.match(/fxios/)?(e.type="firefox",r.push(/fxios\/([\d.\w]+)/)):null!==n.match(/edge/)?(e.type="edge",r.push(/edge\/([\d.]+)/)):null!==n.match(/metasr/)?(e.type="sogou",r.push(/metasr ([\d.]+)/)):null!==n.match(/micromessenger/)?(e.type="micromessenger",r.push(/micromessenger\/([\d.]+)/)):null!==n.match(/mqqbrowser/)?(e.type="qq",r.push(/mqqbrowser\/([\d.]+)/)):null!==n.match(/qqbrowserlite/)?(e.type="qq",r.push(/qqbrowserlite\/([\d.]+)/)):null!==n.match(/tencenttraveler/)?(e.type="qq",r.push(/tencenttraveler\/([\d.]+)/)):null!==n.match(/qqbrowser/)?(e.type="qq",r.push(/qqbrowser\/([\d.]+)/)):null!==n.match(/maxthon/)?(e.type="maxthon",r.push(/maxthon\/([\d.]+)/)):null!==n.match(/ubrowser/)?(e.type="uc",r.push(/ubrowser\/([\d.]+)/)):null!==n.match(/ucbrowser/)?(e.type="uc",r.push(/ucbrowser\/([\d.]+)/)):null!==n.match(/firefox/)?(e.type="firefox",r.push(/firefox\/([\d.]+)/)):null!==n.match(/opera/)?(e.type="opera",r.push(/opera\/([\d.]+)/)):null!==n.match(/opr/)?(e.type="opera",r.push(/opr\/([\d.]+)/)):null!==n.match(/chrome/)?(e.type="chrome",r.push(/chrome\/([\d.]+)/)):null!==n.match(/safari/)?(e.type="safari",r.push(/version\/([\d.]+)/)):null===n.match(/trident/)&&null===n.match(/msie/)||(e.type="ie"),"ie"===e.type){var i=n.match(/trident\/([\d.]+)/)?n.match(/trident\/([\d.]+)/)[1]:"",o=n.match(/msie ([\d.]+)/)?n.match(/msie ([\d.]+)/)[1]:"";""!==i?e.version=String(parseInt(i)+4):""!==o&&(e.version=o)}else r&&(e.version=n.match(r[0])?n.match(r[0])[1]:"")}catch(t){e.type="取值异常",w.log("getting browser info failed due to ",t)}return e},modelInfo:function(){var e=new T(G.userAgent),t=e.os();if("iOS"===t)return e.mobile();if("AndroidOS"===t)return e.mobile();var n=G.userAgent.match(/Mac/);if(n)return n[0];var r=G.userAgent.match(/Windows/);if(r)return r[0];var i=G.userAgent.match(/Linux/);return i?i[0]:"取值异常"},osVersion:function(){var e=G.userAgent;if(/Windows/i.test(e)){var t=/Windows NT ([\d.]+)/;return e.match(t)?e.match(t)[1]:""}if(/(iPhone|iPad|iPod)/.test(e)&&!window.MSStream){var n=/OS ([\d_]+)/;return e.match(n)?e.match(n)[1].replace(/_/g,"."):""}if(/Android/.test(e)){var r=/Android ([\d.]+)/;return e.match(r)?e.match(r)[1]:""}if(/Mac/i.test(e)){var i=/Mac OS X ([\d_]+)/;return e.match(i)?e.match(i)[1].replace(/_/g,"."):""}return""},properties:function(e){var t,n=B.info.browser();return B.extend({H_os:B.info.os(),H_os_version:B.info.osVersion(),H_lib_version:B.PV_LIB_VERSION,H_lib:"js",H_lib_method:"code",H_screen_height:Number(E.height)||0,H_screen_width:Number(E.width)||0,H_browser:n.type,H_browser_version:n.version,H_network_type:B.info.networkType(),H_language:B.check.isString(G.language)?null===(t=G.language)||void 0===t?void 0:t.toLowerCase():"取值异常",H_model:B.info.modelInfo()},e)},epmProperties:function(){return this.properties({H_lib_version:B.EPM_LIB_VERSION,H_url:k.href,H_title:document.title})},pageProperties:function(){var e=B.getReferrer(),t=B.getCurrentDomain(k.href);return B.stripEmptyProperties({H_referrer:e,H_referrer_host:e?B.getHostname(e):"",H_url:k.href,H_url_host:B.getHostname(k.href,"url_host取值异常"),H_url_domain:t,H_url_path:k.pathname,H_url_hash:k.hash,H_title:document.title})},getElementInfo:function(e,t){var n;if(!B.check.isElement(e))return{};var r=null===(n=e.tagName)||void 0===n?void 0:n.toLowerCase(),i={H_element_type:r,H_element_name:e.getAttribute("name"),H_element_id:e.getAttribute("id"),H_element_target_url:e.getAttribute("href"),H_element_class_name:B.check.isString(e.className)?e.className:null,H_element_content:B.getElementContent(e,r,t)};return B.stripEmptyProperties(i)},networkType:function(){if(void 0===G.connection)return"unknown";var e=G.connection;return e.effectiveType?e.effectiveType:e.type?e.type:"取值异常"}},B.getElementContent=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("input"===t)return(["button","submit"].includes(e.type)||n)&&e.value||"";var r="";return e.textContent?r=B.trim(e.textContent):e.innerText&&(r=B.trim(e.innerText)),r&&(r=r.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)),r||""},B.getHostname=function(e,t){if(t&&"string"==typeof t||(t="hostname解析异常"),!e)return t;var n=null;try{n=B.URL(e).hostname}catch(e){w.log("getHostname传入的url参数不合法！")}return n||t},B.isReferralTraffic=function(e){return""===(e=e||document.referrer)||B.getCookielDomain(B.getHostname(e))!==B.getCookielDomain()},B.getUtm=function(){var e={};return B.each(g,(function(t){var n=B.getQueryParam(k.href,t);n.length&&(e[t]=n)})),e},B.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=B.decodeURIComponent(e);var n=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===n||n&&"string"!=typeof n[1]&&n[1].length?"":B.decodeURIComponent(n[1])};var O=function(){function e(t){i(this,e),this.ele=t}return a(e,[{key:"addClass",value:function(e){return-1===(" "+this.ele.className+" ").indexOf(" "+e+" ")&&(this.ele.className=this.ele.className+(""===this.ele.className?"":" ")+e),this}},{key:"removeClass",value:function(e){var t=" "+this.ele.className+" ";return-1!==t.indexOf(" "+e+" ")&&(this.ele.className=t.replace(" "+e+" "," ").slice(1,-1)),this}},{key:"hasClass",value:function(e){return-1!==(" "+this.ele.className+" ").indexOf(" "+e+" ")}},{key:"attr",value:function(e,t){return"string"==typeof e&&B.check.isUndefined(t)?this.ele.getAttribute(e):("string"==typeof e&&(t=String(t),this.ele.setAttribute(e,t)),this)}},{key:"offset",value:function(){var e=this.ele.getBoundingClientRect();if(e.width||e.height){var t=this.ele.ownerDocument.documentElement;return{top:e.top+window.pageYOffset-t.clientTop,left:e.left+window.pageXOffset-t.clientLeft}}return{top:0,left:0}}},{key:"getSize",value:function(){if(!window.getComputedStyle)return{width:this.ele.offsetWidth,height:this.ele.offsetHeight};try{var e=this.ele.getBoundingClientRect();return{width:e.width,height:e.height}}catch(e){return{width:0,height:0}}}},{key:"getStyle",value:function(e){return this.ele.currentStyle?this.ele.currentStyle[e]:this.ele.ownerDocument.defaultView.getComputedStyle(this.ele,null).getPropertyValue(e)}},{key:"wrap",value:function(e){var t=document.createElement(e);return this.ele.parentNode.insertBefore(t,this.ele),t.appendChild(this.ele),B.getDomElementInfo(t)}},{key:"getCssStyle",value:function(e){var t=this.ele.style.getPropertyValue(e);if(t)return t;var n=null;if("function"==typeof window.getMatchedCSSRules&&(n=window.getMatchedCSSRules(this.ele)),!n||!B.check.isArray(n))return null;for(var r=n.length-1;r>=0;r--){if(t=n[r].style.getPropertyValue(e))return t}}},{key:"sibling",value:function(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}},{key:"next",value:function(){return this.sibling(this.ele,"nextSibling")}},{key:"prev",value:function(){return this.sibling(this.ele,"previousSibling")}},{key:"siblingsFn",value:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}},{key:"siblings",value:function(){return this.siblingsFn((this.ele.parentNode||{}).firstChild,this.ele)}},{key:"children",value:function(){return this.siblingsFn(this.ele.firstChild)}},{key:"parent",value:function(){var e=this.ele.parentNode;return e=e&&11!==e.nodeType?e:null,B.getDomElementInfo(e)}},{key:"previousElementSibling",value:function(){var e=this.ele;if("previousElementSibling"in document.documentElement)return B.getDomElementInfo(e.previousElementSibling);for(;e=e.previousSibling;)if(1===e.nodeType)return B.getDomElementInfo(e);return B.getDomElementInfo(null)}},{key:"getSameTypeSiblings",value:function(){for(var e,t=this.ele,n=t.parentNode,r=null===(e=t.tagName)||void 0===e?void 0:e.toLowerCase(),i=[],o=0;o<n.children.length;o++){var a,s=n.children[o];1===s.nodeType&&(null===(a=s.tagName)||void 0===a?void 0:a.toLowerCase())===r&&i.push(n.children[o])}return i}},{key:"getParents",value:function(){try{var e=this.ele;if(!B.check.isElement(e))return[];var t=[e];if(null===e||null===e.parentElement)return[];for(;null!==e.parentElement;)e=e.parentElement,t.push(e);return t}catch(e){return[]}}}]),e}();B.getDomElementInfo=function(e){return new O(e)},B.addEvent=function(e,t,n,r){function i(e){return e&&(e.preventDefault=i.preventDefault,e.stopPropagation=i.stopPropagation,e._getPath=i._getPath),e}i._getPath=function(){return this.path||B.getDomElementInfo(this.target).getParents()},i.preventDefault=function(){this.returnValue=!1},i.stopPropagation=function(){this.cancelBubble=!0};(function(e,t,n){if(void 0===r&&"click"===t&&(r=!0),e&&e.addEventListener)e.addEventListener(t,(function(e){e._getPath=i._getPath,n.call(this,e)}),r);else{var o="on"+t,a=e[o];e[o]=function(e,t,n,r){var o=function(o){if(o=o||i(window.event)){o.target=o.srcElement;var a,s,c=!0;return"function"==typeof n&&(a=n(o)),s=t.call(e,o),"beforeunload"!==r?(!1!==a&&!1!==s||(c=!1),c):void 0}};return o}(e,n,a,t)}}).apply(null,arguments)},B.addCaptureEvent=function(e,t,n){return this.addEvent(e,t,n,"click"===t)},B.hasCircularReference=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;if("object"!==r(e)||null===e)return!1;if(t.has(e))return!0;for(var n in t.add(e),e)if(B.hasCircularReference(e[n],t))return!0;return t.delete(e),!1},B.parseSuperProperties=function(e){var t=e.properties||{},n=JSON.parse(JSON.stringify(e));B.check.isObject(t)&&B.each(t,(function(e,r){if(B.check.isFunction(e))try{t[r]=e(n),B.check.isFunction(t[r])&&(w.log("属性--"+r+" 格式不满足要求, 已被删除"),delete t[r])}catch(e){delete t[r],w.log("属性--"+r+" 格式不满足要求, 已被删除")}}))},B.getURLSearchParams=function(e){for(var t={},n=(e=e||"").substring(1).split("&"),r=0;r<n.length;r++){var i=n[r].indexOf("=");if(-1!==i){var o=n[r].substring(0,i),a=n[r].substring(i+1);o=B.decodeURIComponent(o),a=B.decodeURIComponent(a),t[o]=a}}return t},B.urlParse=function(e){var t=function(e){this._fields={Username:4,Password:5,Port:7,Protocol:2,Host:6,Path:8,URL:0,QueryString:9,Fragment:10},this._values={},this._regex=/^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/,void 0!==e&&this._parse(e)};return t.prototype.setUrl=function(e){this._parse(e)},t.prototype._initValues=function(){for(var e in this._fields)this._values[e]=""},t.prototype.addQueryString=function(e){if("object"!==r(e))return!1;var t=this._values.QueryString||"";for(var n in e)t=new RegExp(n+"[^&]+").test(t)?t.replace(new RegExp(n+"[^&]+"),n+"="+e[n]):"&"===t.slice(-1)?t+n+"="+e[n]:""===t?n+"="+e[n]:t+"&"+n+"="+e[n];this._values.QueryString=t},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:"",e+=this._values.Fragment?"#"+this._values.Fragment:""},t.prototype._parse=function(e){this._initValues();var t=this._regex.exec(e);t||w.i("URLParser::_parse -> Invalid URL");var n=e.split("#"),r=n[0],i=n.slice(1).join("#");for(var o in t=this._regex.exec(r),this._fields)void 0!==t[this._fields[o]]&&(this._values[o]=t[this._fields[o]]);this._values.Hostname=this._values.Host.replace(/:\d+$/,""),this._values.Origin=this._values.Protocol+"://"+this._values.Hostname,this._values.Fragment=i},new t(e)},B.URL=function(e){var t,n={};if("function"==typeof window.URL&&function(){try{return"http://modernizr.com/"===new URL("http://modernizr.com/").href}catch(e){return!1}}())(n=new URL(e)).searchParams||(n.searchParams=(t=B.getURLSearchParams(n.search),{get:function(e){return t[e]}}));else{B.check.isString(e)||(e=String(e)),e=B.trim(e);if(!1===/^https?:\/\/.+/.test(e))return void w.log("Invalid URL");var r=B.urlParse(e);n.hash=r._values.Fragment,n.host=r._values.Host?r._values.Host+(r._values.Port?":"+r._values.Port:""):"",n.href=r._values.URL,n.password=r._values.Password,n.pathname=r._values.Path,n.port=r._values.Port,n.search=r._values.QueryString?"?"+r._values.QueryString:"",n.username=r._values.Username,n.hostname=r._values.Hostname,n.protocol=r._values.Protocol?r._values.Protocol+":":"",n.origin=r._values.Origin?r._values.Origin+(r._values.Port?":"+r._values.Port:""):"",n.searchParams=function(){var e=B.getURLSearchParams("?"+r._values.QueryString);return{get:function(t){return e[t]}}}()}return n};var D=function(){function e(){i(this,e)}return a(e,null,[{key:"getSourceFromReferrer",value:function(){function e(e,t){for(var n=0;n<e.length;n++)if(-1!==t.split("?")[0].indexOf(e[n]))return!0}var t="("+g.join("|")+")\\=[^&]+",n=document.referrer||"",r=k.href;if(r){var i=r.match(new RegExp(t));return i&&i[0]?"付费广告流量":e(p,n)?"自然搜索流量":e(m,n)?"社交网站流量":""===n?"直接流量":"引荐流量"}return"获取url异常"}},{key:"getReferSearchEngine",value:function(e){var t=B.getHostname(e);if(!t||"hostname解析异常"===t)return"";var n={baidu:[/^.*\.baidu\.com$/],bing:[/^.*\.bing\.com$/],google:[/^www\.google\.com$/,/^www\.google\.com\.[a-z]{2}$/,/^www\.google\.[a-z]{2}$/],sm:[/^m\.sm\.cn$/],so:[/^.+\.so\.com$/],sogou:[/^.*\.sogou\.com$/],yahoo:[/^.*\.yahoo\.com$/]},r={};for(var i in n)r[i]=n[i].map((function(e){return new RegExp(e)}));for(var o in r){var a,s=u(r[o]);try{for(s.s();!(a=s.n()).done;){if(a.value.test(t))return o}}catch(e){s.e(e)}finally{s.f()}}return"未知搜索引擎"}},{key:"getKeywordFromReferrer",value:function(e,t){if(e=e||document.referrer,document&&B.check.isString(e)){if(0===e.indexOf("http")){var n=this.getReferSearchEngine(e),r=B.getURLSearchParams(e);if(B.check.isEmptyObject(r))return"未取到值";var i=null;for(var o in S)if(n===o&&B.check.isObject(r))if(i=S[o],B.check.isArray(i))for(o=0;o<i.length;o++){var a=r[i[o]];if(a)return t?{active:a}:a}else if(r[i])return t?{active:r[i]}:r[i];return"未取到值"}return""===e?"未取到值_直接打开":"未取到值_非http的url"}return"取值异常_referrer异常_"+String(e)}}]),e}();function x(e){return!!B.check.isFunction(e)||!(!e||!B.check.isObject(e))&&x(e.callback)}var L=function(){function e(){return i(this,e),e.instance||(e.instance=this,this.events={}),e.instance}return a(e,[{key:"on",value:function(e,t){if(!e||!t)return!1;if(!x(t))throw new Error("callback must be a fcuntion");return this.events[e]=this.events[e]||[],B.check.isObject(t)?this.events[e].push(t):this.events[e].push({callback:t,once:!1}),this}},{key:"prepend",value:function(e,t){if(!e||!t)return!1;if(!x(t))throw new Error("callback must be a fcuntion");return this.events[e]=this.events[e]||[],B.check.isObject(t)?this.events[e].unshift(t):this.events[e].unshift({callback:t,once:!1}),this}},{key:"prependOnce",value:function(e,t){return this.prepend(e,{callback:t,once:!0})}},{key:"once",value:function(e,t){return this.on(e,{callback:t,once:!0})}},{key:"off",value:function(e,t){var n=this.events[e];if(!n)return!1;if(B.check.isNumber(t))n.splice(t,1);else if(B.check.isFunction(t))for(var r=0;r<n.length;r++)n[r]&&n[r].callback===t&&n.splice(r,1);return this}},{key:"emit",value:function(e,t){var n=this.events[e];if(!n)return!1;var r,i=u(n);try{for(i.s();!(r=i.n()).done;){var o=r.value;B.check.isObject(o)&&(o.callback.call(this,t||{}),o.once&&this.off(e,o.callback))}}catch(e){i.e(e)}finally{i.f()}return this}},{key:"clear",value:function(e){e&&this.events(e)?this.events[e]=[]:this.events={}}},{key:"getEvent",value:function(e){return e&&this.events[e]?this.events[e]:this.events}}]),e}();function R(e){I.checkAjax(e.url),e.timeout=e.timeout||2e4;var t=void 0!==window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest?new XMLHttpRequest:void 0!==window.XDomainRequest?new(0,window.XDomainRequest):null;if(!t)return!1;var n,r=(e=B.extend({success:function(){},error:function(){}},e)).success,i=e.error,o=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];n&&(clearTimeout(n),n=null,e&&(t.onreadystatechange=null,t.onload=null,t.onerror=null))};e.success=function(e){r(e),o()},e.error=function(e){i(e),o()},n=setTimeout((function(){try{t&&B.check.isObject(t)&&t.abort&&t.abort()}catch(e){w.log(e)}o(!0)}),e.timeout);var a=function(e){return e?B.safeJSONParse(e):""};"undefined"!=typeof XDomainRequest&&t instanceof window.XDomainRequest&&(t.onload=function(){e.success&&e.success(a(t.responseText)),t.onreadystatechange=null,t.onload=null,t.onerror=null},t.onerror=function(){e.error&&e.error(a(t.responseText),t.status),t.onreadystatechange=null,t.onerror=null,t.onload=null}),t.open("post",e.url,!0),e.credentials&&(t.withCredentials=!0),t.setRequestHeader&&t.setRequestHeader("Content-type","application/x-www-form-urlencoded"),t.onreadystatechange=function(){try{4===t.readyState&&(t.status>=200&&t.status<300||304===t.status?e.success(a(t.responseText)):e.error("网络异常, 请求失败",t.status),t.onreadystatechange=null,t.onload=null)}catch(e){t.onreadystatechange=null,t.onload=null}},t.send(e.data||null)}B.mitt=new L,B.initUrlChange=function(){var e=k.href,t=C.pushState,n=C.replaceState;B.check.isFunction(t)&&(C.pushState=function(){t.apply(C,arguments),B.mitt.emit("urlChange",e),e=k.href}),B.check.isFunction(n)&&(C.replaceState=function(){n.apply(C,arguments),B.mitt.emit("urlChange",e),e=k.href});var r=t?"popstate":"hashchange";B.addEvent(window,r,(function(){B.mitt.emit("urlChange",e),e=k.href}))},B.listenPageState=function(e){({visibleHandler:B.check.isFunction(e.visible)?e.visible:function(){},hiddenHandler:B.check.isFunction(e.hidden)?e.hidden:function(){},visibilityChange:null,hidden:null,isSupport:function(){return!B.check.isUndefined(document[this.hidden])},init:function(){B.check.isUndefined(document.hidden)?B.check.isUndefined(document.msHidden)?B.check.isUndefined(document.webkitHidden)?B.check.isUndefined(document.mozHidden)||(this.hidden="mozHidden",this.visibilityChange="mozvisibilitychange"):(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"):(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):(this.hidden="hidden",this.visibilityChange="visibilitychange"),this.listen()},listen:function(){var e=this;this.isSupport()?B.addEvent(document,this.visibilityChange,(function(){document[e.hidden]?e.hiddenHandler():e.visibleHandler()})):(B.addEvent(window,"focus",this.visibleHandler),B.addEvent(window,"blur",this.hiddenHandler))}}).init()},B.hash=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0};var N=function(){function e(t){i(this,e),this.callback=t.callback,this.serverUrl=t.serverUrl,this.data=t.data,this.dataSendTimeout=t.dataSendTimeout}return a(e,[{key:"run",value:function(){var e=this;R({url:this.serverUrl,data:this.data,timeout:this.dataSendTimeout,success:function(){return e.cb("success")},error:function(t){return e.cb("fail",t)}})}},{key:"cb",value:function(e,t){if(this.callback){if(!B.check.isFunction(this.callback))return void w.log("sdk callback is not a function");var n=new URLSearchParams(this.data).get("data"),r=B.base64Decode(n);if("track"===r.type&&"H_WebPageLeave"===r.event)return;this.callback(this.serverUrl,r,e,t)}}}]),e}(),U=function(){function e(t){i(this,e),this.callback=t.callback,this.serverUrl=t.serverUrl,this.data=t.data,this.dataSendTimeout=t.dataSendTimeout,this.img=document.createElement("img"),this.img.width=1,this.img.height=1,t.imgUseCrossOrigin&&(this.img.crossOrigin="anonymous")}return a(e,[{key:"run",value:function(){var e=this,t=function(t,n){e.img&&!e.img.complete&&(e.img.complete=!0,setTimeout((function(){var t=B.info.browser().type;e.img.src="ie"===t?"about:blank":""}),e.dataSendTimeout)),e.cb(t,n)};-1!==this.serverUrl.indexOf("?")?this.img.src=this.serverUrl+"&"+this.data:this.img.src=this.serverUrl+"?"+this.data,this.img.onload=function(){this.onload=null,t("success")},this.img.onerror=function(){this.onerror=null,t("error")},this.img.onabort=function(){this.onabort=null,t("abort")}}},{key:"cb",value:function(e){if(this.callback){if(!B.check.isFunction(this.callback))return void w.log("sdk callback is not a function");var t=new URLSearchParams(this.data).get("data");this.callback(this.serverUrl,B.base64Decode(t),e)}}}]),e}(),F=function(){function e(t){i(this,e),this.callback=t.callback,this.serverUrl=t.serverUrl,this.data=t.data}return a(e,[{key:"run",value:function(){var e=this;if(B.check.isObject(navigator)&&B.check.isFunction(navigator.sendBeacon)){var t=new FormData;t.append("data",B.base64Encode(this.data)),navigator.sendBeacon(this.serverUrl,t)}setTimeout((function(){e.cb()}))}},{key:"cb",value:function(){if(this.callback){if(!B.check.isFunction(this.callback))return void w.log("sdk callback is not a function");this.callback()}}}]),e}(),V="hinasdk_data_",X=function(){function e(t){i(this,e),this.timer=null,this.sendTimeStamp=0,this.batchConfig=B.extend({dataSendTimeout:6e3,sendInterval:6e3,storageLimit:200},t.batchSend),this.tabKey="hinasdk_tab",this.config=t}return a(e,[{key:"batchInterval",value:function(){var e=this;this.timer=setTimeout((function(){e.recycle(),e.send(),clearTimeout(e.timer),e.batchInterval()}),this.batchConfig.sendInterval)}},{key:"request",value:function(e,t){var n=this;0!=(e=e.filter((function(e){return null!=e}))).length&&R({url:this.config.serverUrl,data:"data_list="+encodeURIComponent(B.base64Encode(JSON.stringify(e))),timeout:this.batchConfig.dataSendTimeout,success:function(){n.remove(t),n.sendTimeStamp=0},error:function(){n.sendTimeStamp=0}})}},{key:"send",value:function(){if(!(this.sendTimeStamp&&B.now()-this.sendTimeStamp<this.batchConfig.dataSendTimeout)){var e=B.localStorage.get(this.tabKey);if(e){this.sendTimeStamp=B.now();var t=B.safeJSONParse(e)||[];if(t.length){for(var n=[],r=[],i=t.length<this.batchConfig.storageLimit?t.length:this.batchConfig.storageLimit,o=0;o<i;o++){var a=B.readObjectVal(t[o].dataKey);r.push(t[o].dataKey),n.push(a)}this.request(n,r)}}}}},{key:"remove",value:function(e){var t=B.localStorage.get(this.tabKey);if(t){for(var n=B.safeJSONParse(t)||[],r=null==n?void 0:n.map((function(e){return e.dataKey})),i=0;i<e.length;i++){var o=B.indexOf(r,e[i]);o>-1&&(n.splice(o,1),B.localStorage.remove(e[i]))}B.localStorage.set(this.tabKey,JSON.stringify(n))}}},{key:"add",value:function(e){var t=V+B.getRandom(),n=B.localStorage.get(this.tabKey);if((n=null==n?[]:B.safeJSONParse(n)||[]).push({dataKey:t,expireTime:B.now()+2*this.batchConfig.sendInterval}),B.localStorage.set(this.tabKey,JSON.stringify(n)),B.saveObjectVal(t,e),n.length>this.batchConfig.storageLimit){for(var r=n.slice(0,20),i=[],o=0;o<r.length;o++){var a=B.readObjectVal(r[o].dataKey);i.push(a)}this.request(i,r.map((function(e){return e.dataKey})))}"track_signup"!==e.type&&"H_pageview"!==e.event||this.send()}},{key:"recycle",value:function(){var e=B.localStorage.get(this.tabKey);if(e){var t=B.safeJSONParse(e)||[];if(t.length){for(var n=[],r=0;r<t.length;r++)B.now()>t[r].expireTime&&n.push(t[r].dataKey);this.remove(n)}else{for(var i=[],o=0;o<B.localStorage.length;o++){var a=B.localStorage.key(o);0===(null==a?void 0:a.indexOf(V))&&i.push({dataKey:a,expireTime:B.now()+2*this.batchConfig.sendInterval})}i.length>0&&B.localStorage.set(this.tabKey,JSON.stringify(i))}}}}]),e}();function j(){return B.cookie.isSupport()?null!=B.cookie.get("hinasdk_isNewUser"):null!=B.memory.get("hinasdk_isNewUser")}var W={name:"hinasdk_crossdata",state:{deviceId:null,accountId:null,firstId:null,sessionId:null,firstVisitTime:B.now(),props:{},sessionIdUpdateTime:null},isFirstTime:!1,isFirstDay:j(),isFirstVisit:!0,isSetFirstVisit:!0,load:function(){var e=null,t=null;if(B.cookie.isSupport()?this.storage=B.cookie:(w.log("Cookie storage is not supported, SDK internal cache has been enabled"),this.storage=B.memory),B.localStorage.isSupport()?this.storageLocal=B.localStorage:w.log("localStorage is not supported, SDK internal cache has been enabled"),e||(e=this.storage.get(this.name)),t||(t=this.storageLocal.get(this.name)),e&&B.check.isJSONString(e)&&(this.state=B.extend({},JSON.parse(e))),t&&B.check.isJSONString(t)&&(this.state=B.extend({},JSON.parse(t))),e||t?(this.save(),this.isSetFirstVisit=!1,this.isFirstVisit=!1):(this.isSetFirstVisit=!0,this.isFirstVisit=!0),this.isFirstVisit){var n=new Date,r={h:23-n.getHours(),m:59-n.getMinutes(),s:59-n.getSeconds()};this.storage.set("hinasdk_isNewUser",!0,3600*r.h+60*r.m+r.s+"s"),this.isFirstDay=!0,this.isFirstTime=!0}else this.checkIsFirstTime=function(e){"track"===e.type&&"H_pageview"===e.event&&(e.properties.H_is_first_time=!1)};if(!this.getAccountId()){var i=B.UUID();this.setDeviceId(i),this.setAccountId(i)}},checkIsFirstTime:function(e){"track"===e.type&&"H_pageview"===e.event&&(j()&&this.isFirstTime?(e.properties.H_is_first_time=!0,this.isFirstTime=!1):e.properties.H_is_first_time=!1)},checkIsFirstSign:function(e){"track"===e.type&&(j()&&this.isFirstDay?e.properties.H_is_first_day=!0:(this.isFirstDay=!1,e.properties.H_is_first_day=!1))},setDeviceId:function(e){this.state.deviceId?w.log("Current deviceId is "+this.getDeviceId()+", it has been set"):this.set("deviceId",e)},setAccountId:function(e){this.set("accountId",e)},getDeviceId:function(){return this.state.deviceId},getAccountId:function(){return this.state.__accountId||this.state.accountId},getCookie:function(){return this.storage.get(this.name)},setProps:function(e,t){var n={};for(var r in n=t?e:B.extend(this.state.props||{},e))"string"==typeof n[r]&&(n[r]=n[r].slice(0,B.MAX_REFERRER_STRING_LENGTH));this.set("props",n)},getFirstId:function(){return this.state.__firstId||this.state.firstId},getSessionId:function(){return this.state.sessionId},getSessionIdUpdateTime:function(){return this.state.sessionIdUpdateTime||this.getSessionId().split("_")[0]},setSessionId:function(e){this.set("sessionId",e)},getAnonymousId:function(){var e=this.getAccountId(),t=this.getFirstId();return e&&t?t:e},change:function(e,t){this.state["__"+e]=t},set:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;this.state=this.state||{},["accountId","firstId"].indexOf(e)>-1&&delete this.state["__"+e],this.state[e]=t,this.save()},save:function(){this.storage.setDomain(this.name,JSON.stringify(this.state),null,!0),this.storageLocal.set(this.name,JSON.stringify(this.state))},clear:function(){this.state={},this.save()}},K={pageProp:{},currentProps:{},register:function(e){B.extend(K.currentProps,e)},getPresetProperties:function(){var e=window.innerHeight||document.documentElement.clientHeight||document.body&&document.body.clientHeight||0,t=window.innerWidth||document.documentElement.clientWidth||document.body&&document.body.clientWidth||0,n={H_timezone_offset:(new Date).getTimezoneOffset(),H_viewport_height:e,H_viewport_width:t};return B.extend(n,B.info.properties()),n},getPageProperties:function(){return B.extend(this.pageProp,B.info.pageProperties()),this.pageProp},getUmtsParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=B.getUtm(),r={},i={};return B.each(n,(function(n,o,a){B.utmTypes.includes(o)?r[e+o]=a[o]:i[t+o]=a[o]})),W.state.props&&W.state.props.H_latest_utm_source&&B.extend(W.state.props,r),{allUtms:r,otherUtms:i}}};function q(e){var t=W.state.props||{};if(B.each(e.presetProperties,(function(e,n){if(-1===n.indexOf("latest_"))return!1;if(n=n.slice(7),e){var r=B.getCurrentDomain(window.location.href);if("utm"!==n&&"url解析失败"===r)t["H_latest_"+n]="url的domain解析失败";else if(B.isReferralTraffic(document.referrer))switch(n){case"traffic_source_type":t.H_latest_traffic_source_type=D.getSourceFromReferrer();break;case"referrer":t.H_latest_referrer=K.pageProp.referrer||"";break;case"search_keyword":D.getKeywordFromReferrer()&&(t.H_latest_search_keyword=D.getKeywordFromReferrer())}}})),e.presetProperties.latest_utm){var n=K.getUmtsParams("H_latest_",""),r=n.allUtms,i=n.otherUtms;B.check.isEmptyObject(r)||B.extend(t,r),B.check.isEmptyObject(i)||B.extend(t,i)}K.register(t),W.setProps(t)}function z(e,n,r){if(W.isSetFirstVisit){var i,o=K.getUmtsParams("H_","").allUtms,a=B.getReferrer(null,n);e.call(r,B.extend(t({H_first_visit_time:B.now(),H_first_referrer:a,H_first_host:a?B.getHostname(a,"取值异常"):"",H_first_browser_language:B.check.isString(navigator.languages[1])?null===(i=navigator.languages[1])||void 0===i?void 0:i.toLowerCase():"取值异常",H_first_traffic_source_type:D.getSourceFromReferrer(),H_first_search_keyword:D.getKeywordFromReferrer()},o))),W.isSetFirstVisit=!1}}function Q(e,n){n.config;var r=K.getUmtsParams("H_",""),i=r.allUtms,o=r.otherUtms,a={anonymous_id:W.getAnonymousId(),properties:t({device_id:W.getDeviceId()},K.currentProps),type:e.type,event:e.event,time:B.now(),_track_id:Number(String(B.getRandom()).slice(2,5)+String(B.getRandom()).slice(2,4)+String(B.now()).slice(-4))};return W.getAccountId()!==W.getAnonymousId()&&(a.account_id=W.getAccountId()),e.type&&"user"===e.type.slice(0,4)?a.properties=B.extend(a.properties,e.properties):a.properties=B.extend(a.properties,i,o,K.currentProps,K.getPageProperties(),K.getPresetProperties(),e.properties),B.parseSuperProperties(a),W.checkIsFirstSign(a),W.checkIsFirstTime(a),a}var J=window.location,Y=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"],Z=["a","div","input","button","textarea"],$=function(){function e(t,n,r){i(this,e),this.autoTrackIsUsed=!1,this.otherTag=[],this.isTrackList={a:!0,button:!0},this.autoTrackConfig=B.extend({clickAutoTrack:!0,stayAutoTrack:!0,isCollectUrl:function(){return!0},isCollectElement:function(){return!0},isCollectInput:function(){return!1},addCustomProperty:function(){},stayDelayTime:4e3,maxStayPageDuration:18e3,collectTags:{div:!1},trackAttr:[]},t),this.stayAutoTrackConfig=B.extend({isCollectUrl:function(){return!0}},n),this.ctx=r,this.load(t)}return a(e,[{key:"load",value:function(e){var t=this;if(B.check.isArray(e.trackAttr)?(this.autoTrackConfig.trackAttr=e.trackAttr.filter((function(e){return B.check.isString(e)})),this.autoTrackConfig.trackAttr.push("hn-click")):this.autoTrackConfig.trackAttr=["hn-click"],B.check.isObject(e.collectTags)){if(!0===e.collectTags.div)this.autoTrackConfig.collectTags.div={ignoreTags:Y,maxLevel:1};else if(B.check.isObject(e.collectTags.div))if(this.autoTrackConfig.collectTags.div.ignoreTags=Y,B.check.isNumber(e.collectTags.div.maxLevel)){[1,2,3].includes(e.collectTags.div.maxLevel)||(this.autoTrackConfig.collectTags.div.maxLevel=1)}else this.autoTrackConfig.collectTags.div.maxLevel=1;else this.autoTrackConfig.collectTags.div=!1;B.each(e.collectTags,(function(e,n){"div"!==n&&e&&t.otherTag.push(n)})),!0===this.autoTrackConfig.clickAutoTrack&&B.each(this.otherTag,(function(e){e in t.isTrackList&&(t.isTrackList[e]=!0)}))}else this.autoTrackConfig.collectTags={div:!1}}},{key:"autoTrack",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;this.ctx.track("H_pageview",B.extend({H_referrer:B.getReferrer(null,!0),H_url:J.href,H_url_path:J.pathname,H_url_hash:J.hash,H_title:document.title},e),t),z(this.ctx.userSetOnce,!0,this.ctx),this.autoTrackIsUsed=!0}},{key:"autoTrackSinglePage",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;e=this.autoTrackIsUsed?J.href:B.getReferrer(),this.ctx.track("H_pageview",B.extend({H_referrer:e,url:J.href,H_url_path:J.pathname,H_url_hash:J.hash,H_title:document.title},t),n),z(this.ctx.userSetOnce,!1,this.ctx)}},{key:"listenSinglePage",value:function(){var e=this,t=this.ctx.getConfig("isSinglePage");t&&B.mitt.on("hasInit",(function(){e.onUrlChange((function(n){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(n!==J.href){K.pageProp.H_referrer=n;var r=B.extend({H_url:J.href,H_referrer:n},t);e.autoTrack(r)}};if(B.check.isBoolean(t))r();else if(B.check.isFunction(t)){var i=t();B.check.isObject(i)?r(i):!0===i&&r()}}))}))}},{key:"initWebClick",value:function(){var e=this;if(!0===this.autoTrackConfig.clickAutoTrack){var t=!0;B.check.isFunction(this.autoTrackConfig.isCollectUrl)&&this.onUrlChange((function(){t=!!e.autoTrackConfig.isCollectUrl()})),B.addCaptureEvent(document,"click",(function(n){if(t){var r=n||window.event;if(r){var i=r.target||r.srcElement,o=e.getTargetElement(i,r);o&&e.emitClick(r,o)}}}))}}},{key:"emitClick",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(B.check.isFunction(this.autoTrackConfig.isCollectElement)&&!this.autoTrackConfig.isCollectElement(t))return!1;var i=this.getClickElementInfo(t);if(B.check.isFunction(this.autoTrackConfig.addCustomProperty)){var o=this.autoTrackConfig.addCustomProperty(t);B.check.isObject(o)&&(i=B.extend(i,o))}i=B.extend(i,this.getPageXYInfo(e,t),n),this.ctx.track("H_WebClick",i,r)}},{key:"initWebStay",value:function(){var e=this;if(!0===this.autoTrackConfig.stayAutoTrack){var t=!0;B.check.isFunction(this.stayAutoTrackConfig.isCollectUrl)&&this.onUrlChange((function(){t=!!e.stayAutoTrackConfig.isCollectUrl()}));var n,r=this.autoTrackConfig.stayDelayTime,i=this.autoTrackConfig.maxStayPageDuration,o=this.ctx,a=(n={timer:null,timeout:1e3,callback:function(e,t){var n,a=(null===(n=document.documentElement)||void 0===n?void 0:n.scrollTop)||window.pageYOffset||document.body.scrollTop||0,s=e.H_viewport_position,c=new Date,l=c-this.nowTime;(l>r&&a-s!=0||t)&&(B.extend(e,{event_duration:Math.min(parseInt(l)/1e3,i)},B.info.pageProperties()),o.track("H_WebStay",e)),this.nowTime=c},run:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t={};if(!n.timer){var r,i=(null===(r=document.documentElement)||void 0===r?void 0:r.scrollTop)||window.pageYOffset||document.body.scrollTop||0;t.H_viewport_position=Math.round(i)||0,e?(n.callback(t,!0),n.timer=null):n.timer=setTimeout((function(){n.callback(t,!1),n.timer=null}),n.timeout)}}},n);a.nowTime=new Date,B.addCaptureEvent(window,"scroll",(function(){t&&a.run()})),B.addCaptureEvent(window,"unload",(function(){t&&a.run(!0)}))}}},{key:"onUrlChange",value:function(e){B.check.isFunction(e)&&(e(),B.mitt.on("urlChange",e))}},{key:"getTargetElement",value:function(e,t){var n;if(!B.check.isElement(e))return null;if(!B.check.isString(e.tagName))return null;var r,i,o=null===(n=e.tagName)||void 0===n?void 0:n.toLowerCase();if(["body","html"].includes(o))return null;if(["a","button","input","textarea"].concat(this.otherTag).includes(o))return e;if("div"===o&&this.autoTrackConfig.collectTags.div&&this.isDivLevelValid(e)&&(((null===(r=this.autoTrackConfig.collectTags)||void 0===r||null===(i=r.div)||void 0===i?void 0:i.maxLevel)||1)>1||this.isCollectableDiv(e)))return e;if(this.isStyleTag(o)&&this.autoTrackConfig.collectTags.div){var a=this.getCollectableParent(e);if(a&&this.isDivLevelValid(a))return a}return this.hasElement({event:(null==t?void 0:t.originalEvent)||t,element:e})||null}},{key:"isDivLevelValid",value:function(e){for(var t,n,r=(null===(t=this.autoTrackConfig.collectTags)||void 0===t||null===(n=t.div)||void 0===n?void 0:n.maxLevel)||1,i=e.getElementsByTagName("div"),o=i.length-1;o>=0;o--)if(this.getDivLevel(i[o],e)>r)return!1;return!0}},{key:"getDivLevel",value:function(e,t){var n=this.getElementPath(e,!0,t).split(" > "),r=0;return n.forEach((function(e){"div"===e&&r++})),r}},{key:"getElementPath",value:function(e,t,n){for(var r=[];e.parentNode&&B.check.isElement(e);){if(e.id&&!t&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.id)){var i;r.unshift((null===(i=e.tagName)||void 0===i?void 0:i.toLowerCase())+"#"+e.id);break}if(n&&e===n){var o;r.unshift(null===(o=e.tagName)||void 0===o?void 0:o.toLowerCase());break}if(e===document.body){r.unshift("body");break}var a;r.unshift(null===(a=e.tagName)||void 0===a?void 0:a.toLowerCase()),e=e.parentNode}return r.join(" > ")}},{key:"isCollectableDiv",value:function(e){try{var t=e.children||[];if(0===(null==t?void 0:t.length))return!0;for(var n=0;n<t.length;n++){var r,i,o;if(1===t[n].nodeType){var a=null===(r=t[n].tagName)||void 0===r?void 0:r.toLowerCase(),s=(null===(i=this.autoTrackConfig.collectTags)||void 0===i||null===(o=i.div)||void 0===o?void 0:o.maxLevel)||1;if(!("div"===a&&s>1||this.isStyleTag(a)))return!1;if(!this.isCollectableDiv(t[n]))return!1}}return!0}catch(e){w.log(e)}return!1}},{key:"getCollectableParent",value:function(e){try{var t,n,r,i=e.parentNode,o=i?null===(t=i.tagName)||void 0===t?void 0:t.toLowerCase():"";if("body"===o)return!1;var a=(null===(n=this.autoTrackConfig.collectTags)||void 0===n||null===(r=n.div)||void 0===r?void 0:r.maxLevel)||1;if("div"===o&&(a>1||this.isCollectableDiv(i)))return i;if(i&&this.isStyleTag(o))return this.getCollectableParent(i)}catch(e){w.log(e)}return!1}},{key:"isStyleTag",value:function(e){var t,n,r;return!Z.includes(e)&&!(null===(t=this.autoTrackConfig.collectTags)||void 0===t||null===(n=t.div)||void 0===n||null===(r=n.ignoreTags)||void 0===r||!r.includes(e))}},{key:"hasElement",value:function(e){var t;if(e.event){var n=e.event;t=n.path||(null==n?void 0:n._getPath())}else e.element&&(t=B.getDomElementInfo(e.element).getParents());var r=this.autoTrackConfig.trackAttr;if(B.check.isArray(t)&&t.length>0){var i,o=u(t);try{for(o.s();!(i=o.n()).done;){var a,s=i.value,c=s.tagName&&(null===(a=s.tagName)||void 0===a?void 0:a.toLowerCase());if(B.check.isElement(s)&&1===s.nodeType&&(this.isTrackList[c]||this.hasAttributes(s,r)))return s}}catch(e){o.e(e)}finally{o.f()}}}},{key:"hasAttribute",value:function(e,t){return e.hasAttribute?e.hasAttribute(t):e.attributes?!(null===(n=e.attributes[t])||void 0===n||!n.value):void 0;var n}},{key:"hasAttributes",value:function(e,t){if(B.check.isArray(t)){for(var n=!1,r=0;r<t.length;r++){if(this.hasAttribute(e,t[r])){n=!0;break}}return n}}},{key:"getPageXYInfo",value:function(e,t){if(!e)return{};function n(){return{scrollLeft:document.body.scrollLeft||document.documentElement.scrollLeft||0,scrollTop:document.body.scrollTop||document.documentElement.scrollTop||0}}function r(){if(document.documentElement.getBoundingClientRect){var e=t.getBoundingClientRect();return{targetEleX:e.left+n().scrollLeft||0,targetEleY:e.top+n().scrollTop||0}}}function i(e){return Number(Number(e).toFixed(3))}return function(e){var t=e.pageX||e.clientX+n().scrollLeft||e.offsetX+r().targetEleX||0,o=e.pageY||e.clientY+n().scrollTop||e.offsetY+r().targetEleY||0;return{H_page_x:i(t),H_page_y:i(o)}}(e)}},{key:"getClickElementInfo",value:function(e){var t=this.autoTrackConfig.isCollectInput(),n=this.getDomSelector(e),r=B.info.getElementInfo(e,t);return r.H_element_selector=n||"",r.H_element_path=this.getElementPath(e,!1),r}},{key:"getDomSelector",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!e||!e.parentNode||!e.parentNode.children)return!1;var r=null===(t=e.nodeName)||void 0===t?void 0:t.toLowerCase();return e&&"body"!==r&&1===e.nodeType?(n.unshift(this.getSelector(e)),null!=e&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))?n.join(" > "):this.getDomSelector(e.parentNode,n)):(n.unshift("body"),n.join(" > "))}},{key:"getSelector",value:function(e){var t,n,r=null===(t=e.tagName)||void 0===t?void 0:t.toLowerCase(),i=-1;return 9!==(null==e||null===(n=e.parentNode)||void 0===n?void 0:n.nodeType)&&(i=this.getDomIndex(e)),null!=e&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))?"#"+e.getAttribute("id"):r+(~i?":nth-of-type("+(i+1)+")":"")}},{key:"getDomIndex",value:function(e){var t;if(!e.parentNode)return-1;for(var n=0,r=null===(t=e.tagName)||void 0===t?void 0:t.toLowerCase(),i=e.parentNode.children,o=0;o<i.length;o++){var a,s;if((null===(a=i[o])||void 0===a||null===(s=a.tagName)||void 0===s?void 0:s.toLowerCase())===r){if(i[o]===e)return n;n++}}return-1}}]),e}(),ee=window.location,te=function(){function e(){i(this,e),this.ctx={},this.option={},this.isInited=!1}return a(e,[{key:"init",value:function(e,t){this.ctx=e,this.option=t,this.isInited||(this.isInited=!0,B.check.isObject(t)&&B.check.isArray(t.linker)&&t.linker.length>0?(this.setRefferId(t),this.addClickListen(),this.option=function(e){for(var t=e.length,n=[],r=0;r<t;r++)/[A-Za-z0-9]+\./.test(e[r].part_url)&&B.check.isBoolean(e[r].after_hash)?n.push(e[r]):w.log("The configuration of linker "+(r+1)+" is not supported.Please check format");return n}(t.linker)):w.log("siteLinker plugin: Please configure the linker parameter"))}},{key:"getPartUrl",value:function(e){var t=this.option.length;if(t)for(var n=0;n<t;n++)if(e.indexOf(this.option[n].part_url)>-1)return!0;return!1}},{key:"getPartHash",value:function(e){var t=this.option.length;if(t)for(var n=0;n<t;n++)if(e.indexOf(this.option[n].part_url)>-1)return this.option[n].after_hash;return!1}},{key:"getCurrentId",value:function(){var e=this.ctx.store.getAccountId()||"",t=this.ctx.store.getFirstId()||""?"f"+e:"d"+e;return B.encodeURIComponent(t)}},{key:"rewriteUrl",value:function(e,t){var n=/([^?#]+)(\?[^#]*)?(#.*)?/.exec(e),r="";if(n){var i,o=n[1]||"",a=n[2]||"",s=n[3]||"",c="_hnsdk="+this.getCurrentId();if(this.getPartHash(e))i=s.indexOf("_hnsdk"),r=s.indexOf("?")>-1?i>-1?o+a+"#"+s.substring(1,i)+l(s.substring(i,s.length)):o+a+"#"+s.substring(1)+"&"+c:o+a+"#"+s.substring(1)+"?"+c;else i=a.indexOf("_hnsdk"),r=/^\?(\w)+/.test(a)?i>-1?o+"?"+l(a.substring(1))+s:o+"?"+a.substring(1)+"&"+c+s:o+"?"+a.substring(1)+c+s;return t&&(t.href=r),r}function l(e){var t=e.split("&"),n=[];return B.each(t,(function(e){e.indexOf("_hnsdk=")>-1?n.push(c):n.push(e)})),n.join("&")}}},{key:"getUrlId",value:function(){var e=ee.href.match(/_hnsdk=([aufd][^\?\#\&\=]+)/);return B.check.isArray(e)&&e[1]?B.decodeURIComponent(e[1]):""}},{key:"setRefferId",value:function(e){var t=this.ctx.store.getAccountId(),n=this.getUrlId();if(""===n)return!1;var r="d"===n.substring(0,1);(n=n.substring(1))!==t&&(r?(this.ctx.setDeviceUId(n,!0),this.ctx.store.getFirstId()&&this.ctx.sendRequest({anonymous_id:n,account_id:t,type:"track_signup",event:"H_SignUp",properties:{}})):this.ctx.store.getFirstId()&&!e.re_login||this.ctx.setUserUId(n))}},{key:"addClickListen",value:function(){var e=this,t=function(t){var n,r,i,o,a=t.target,s=null===(n=a.tagName)||void 0===n?void 0:n.toLowerCase(),c=a.parentNode,l=null==c||null===(r=c.tagName)||void 0===r?void 0:r.toLowerCase();if("a"===s&&a.href||"a"===l&&c.href){"a"===s&&a.href?(i=a.href,o=a):(i=c.href,o=c);var u=B.URL(i).protocol;"http:"!==u&&"https:"!==u||e.getPartUrl(i)&&e.rewriteUrl(i,o)}};B.addEvent(document,"mousedown",t),window.PointerEvent&&"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>=0&&B.addEvent(document,"pointerdown",t)}}]),e}(),ne=function(){function e(){i(this,e),this.ctx={},this.option={},this.isInited=!1}return a(e,[{key:"init",value:function(e,t){var n=this;this.ctx=e,this.option=t;var r=function e(){var t=0,r=window.performance||window.webkitPerformance||window.msPerformance||window.mozPerformance,i=window.location,o={H_url:i.href,H_title:document.title,H_url_path:i.pathname,H_url_hash:i.hash,H_referrer:B.getReferrer(null,!0)};if(r){if(t=n.getDuration(r)||n.getDurationCompatible(r),n.getPageSize(r,o),t>0){var a,s,c=1800;if(B.check.isObject(n.option)&&null!==(a=n.option)&&void 0!==a&&a.max_duration)c=null===(s=n.option)||void 0===s?void 0:s.max_duration;t=Number((t/1e3).toFixed(3)),(!B.check.isNumber(c)||c<=0||t<=c)&&(o.event_duration=t)}n.isInited||(n.ctx.track("H_WebPageLoad",o),n.isInited=!0),window.removeEventListener&&window.removeEventListener("load",e)}else w.log("your browser not support performance API")};"complete"===document.readyState?r():window.addEventListener&&window.addEventListener("load",r)}},{key:"getPageSize",value:function(e,t){if(e.getEntries&&B.check.isFunction(e.getEntries)){for(var n=e.getEntries(),r=0,i=0;i<n.length;i++)"transferSize"in n[i]&&(r+=n[i].transferSize);B.check.isNumber(r)&&r>=0&&r<10737418240&&(t.H_page_resource_size=Number((r/1024).toFixed(3)))}}},{key:"getDurationCompatible",value:function(e){var t=0;if(e.timing){var n=e.timing;0!==n.fetchStart&&B.check.isNumber(n.fetchStart)&&0!==n.domContentLoadedEventEnd&&B.check.isNumber(n.domContentLoadedEventEnd)?t=n.domContentLoadedEventEnd-n.fetchStart:w.log("performance data parsing exception")}return t}},{key:"getDuration",value:function(e){var t=0;B.check.isFunction(e.getEntriesByType)&&(t=((e.getEntriesByType("navigation")||[{}])[0]||{}).domContentLoadedEventEnd||0);return t}}]),e}(),re=function(){function e(){i(this,e),this.startTime=B.now(),this.currentPageUrl=document.referrer,this.url=ee.href,this.title=document.title||"",this.pageShowStatus=!0,this.pageHiddenStatus=!1,this.timer=null,this.heartbeatIntervalTime=5e3,this.heartbeatIntervalTimer=null,this.pageId=null,this.maxDuration=432e3,this.storageName="hinasdk_pageleave_"}return a(e,[{key:"init",value:function(e,t){if(this.ctx=e,t){this.option=t;var n=t.heartbeat_interval_time;n&&B.check.isNumber(1*n)&&1*n>0&&(this.heartbeatIntervalTime=1e3*n);var r=t.max_duration;r&&B.check.isNumber(1*r)&&1*r>0&&(this.maxDuration=r)}this.pageId=Number(String(B.getRandom()).slice(2,5)+String(B.getRandom()).slice(2,4)+String(B.now()).slice(-4)),this.addPageLeaveEventListener(),document.hidden?this.pageShowStatus=!1:this.addHeartBeatInterval()}},{key:"refreshPageEndTimer",value:function(){var e=this;this.timer&&(clearTimeout(this.timer),this.timer=null),this.timer=setTimeout((function(){e.pageHiddenStatus=!1}),5e3)}},{key:"hiddenStatusHandler",value:function(){clearTimeout(this.timer),this.timer=null,this.pageHiddenStatus=!1}},{key:"pageStartHandler",value:function(){this.startTime=B.now(),!0===document.hidden?this.pageShowStatus=!1:this.pageShowStatus=!0,this.url=ee.href,this.title=document.title}},{key:"pageEndHandler",value:function(){if(!this.pageHiddenStatus){var e=this.getPageLeaveProperties();this.pageShowStatus||delete e.event_duration,this.pageShowStatus=!1,this.pageHiddenStatus=!0,this.isCollectUrl(this.url)&&this.ctx.track("H_WebPageLeave",e),this.refreshPageEndTimer(),this.delHeartBeatData()}}},{key:"addPageLeaveEventListener",value:function(){this.addPageStartListener(),this.addPageSwitchListener(),this.addSinglePageListener(),this.addPageEndListener()}},{key:"addPageStartListener",value:function(){var e=this;"onpageshow"in window&&B.addEvent(window,"pageshow",(function(){e.pageStartHandler(),e.hiddenStatusHandler()}))}},{key:"addPageSwitchListener",value:function(){var e=this;B.listenPageState({visible:function(){e.pageStartHandler(),e.hiddenStatusHandler(),e.addHeartBeatInterval()},hidden:function(){e.url=ee.href,e.title=document.title,e.pageEndHandler(),e.stopHeartBeatInterval()}})}},{key:"addSinglePageListener",value:function(){var e=this;B.mitt.prepend("urlChange",(function(t){t!==ee.href&&(e.url=t,e.pageEndHandler(),e.stopHeartBeatInterval(),e.currentPageUrl=t,e.pageStartHandler(),e.hiddenStatusHandler(),e.addHeartBeatInterval())}))}},{key:"addPageEndListener",value:function(){var e=this;B.each(["pagehide","beforeunload","unload"],(function(t){"on"+t in window&&B.addEvent(window,t,(function(){e.pageEndHandler(),e.stopHeartBeatInterval()}))}))}},{key:"addHeartBeatInterval",value:function(){B.localStorage.isSupport()&&this.startHeartBeatInterval()}},{key:"startHeartBeatInterval",value:function(){var e=this;this.heartbeatIntervalTimer&&this.stopHeartBeatInterval();var t=!0;this.isCollectUrl(this.url)||(t=!1),t&&(this.heartbeatIntervalTimer=setInterval((function(){e.saveHeartBeatData()}),this.heartbeatIntervalTime),this.saveHeartBeatData("first")),this.reissueHeartBeatData()}},{key:"reissueHeartBeatData",value:function(){for(var e=B.localStorage.length-1;e>=0;e--){var t=B.localStorage.key(e);if(t&&t!==this.storageName+this.pageId&&t.indexOf(this.storageName)>-1){var n=B.readObjectVal(t);B.check.isObject(n)&&B.now()-n.time>n.heartbeat_interval_time+5e3&&(delete n.heartbeat_interval_time,this.ctx.sendRequest(n),this.delHeartBeatData(t))}}}},{key:"stopHeartBeatInterval",value:function(){this.heartbeatIntervalTimer&&clearInterval(this.heartbeatIntervalTimer),this.heartbeatIntervalTimer=null}},{key:"saveHeartBeatData",value:function(e){var t=this.getPageLeaveProperties();t.H_time=B.now(),"first"===e&&(t.event_duration=3);var n=Q({type:"track",event:"H_WebPageLeave",properties:t},this.ctx);n.heartbeat_interval_time=this.heartbeatIntervalTime,B.saveObjectVal(this.storageName+this.pageId,n)}},{key:"delHeartBeatData",value:function(e){B.localStorage.isSupport()&&B.localStorage.remove(e||this.storageName+this.pageId)}},{key:"isCollectUrl",value:function(e){var t,n;return!B.check.isFunction(null===(t=this.option)||void 0===t?void 0:t.isCollectUrl)||(!B.check.isString(e)||(null===(n=this.option)||void 0===n?void 0:n.isCollectUrl(e)))}},{key:"getPageLeaveProperties",value:function(){var e,t,n,r=(B.now()-this.startTime)/1e3;(!B.check.isNumber(r)||r<0||r>this.maxDuration)&&(r=0),r=Number(r.toFixed(3));var i=B.getReferrer(this.currentPageUrl),o=(null===(e=document.documentElement)||void 0===e?void 0:e.scrollTop)||window.pageYOffset||(null===(t=document.body)||void 0===t?void 0:t.scrollTop)||0;o=Math.round(o)||0;var a={H_title:this.title,H_url:this.url,H_url_path:B.URL(this.url).pathname,H_url_hash:B.URL(this.url).hash,H_referrer_host:i?B.getHostname(i):"",H_referrer:i,H_viewport_position:o};return r&&(a.event_duration=r),a=B.extend(a,null===(n=this.option)||void 0===n?void 0:n.custom_props)}}]),e}(),ie={SiteLinker:te,PageLoad:ne,PageLeave:re},oe={name:"",showLog:!1,autoTrackConfig:{clickAutoTrack:!1,stayAutoTrack:!1,pageviewAutoTrack:!1,pageLeaveAutoTrack:!1},stayAutoTrackConfig:{},imgUseCrossOrigin:!1,isSinglePage:!1,batchSend:!1,appJsBridge:!1,sendType:"image",dataSendTimeout:3e3,isTrackDeviceId:!1,presetProperties:{latest_utm:!0,latest_utm_source:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,url:!0,title:!0}},ae=function(){function e(){return i(this,e),e.instance||(e.instance=this,this.config={},this.initialized=!1,this._=B),e.instance}return a(e,[{key:"setConfig",value:function(e){B.check.isObject(e)&&B.extend(this.config,e)}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"init",value:function(e){if(B.check.isEmptyObject(this.config)){if(this.setConfig(B.extend({},oe,e)),w.showLog=this.getConfig("showLog"),I.serverUrl=this.getConfig("serverUrl"),!I.checkServerUrl(this.getConfig.serverUrl))return;B.initUrlChange(),W.load(this.config),q(this.config),this.store=W;var t=this.getConfig("sendType");["image","ajax","beacon"].includes(t)||this.setConfig({sendType:"image"}),(!0===this.getConfig("batchSend")||B.check.isObject(this.getConfig("batchSend")))&&(this.batchSender=new X(this.config),this.batchSender.batchInterval());var n=this.getConfig("autoTrackConfig"),r=this.getConfig("stayAutoTrackConfig"),i=n.pageviewAutoTrack,o=n.pageLeaveAutoTrack,a=new $(n,r,this);this.autoTrackInstance=a,a.initWebClick(),a.initWebStay(),"auto"===i?a.autoTrack():("singlePage"===i||this.getConfig("isSinglePage"))&&(this.config.isSinglePage=!0,a.listenSinglePage()),o&&(B.check.isObject(o)?this.use("PageLeave",o):this.use("PageLeave")),this.initialized=!0,w.log("hinaSDK initialized successfully"),B.mitt.emit("hasInit")}else w.log("hinaSDK has been initialized")}},{key:"sendRequest",value:function(e,t){if((e=Q(e,this)).send_time=B.now(),w.log(e),this.getConfig("appJsBridge")){var n=window.Hina_Cloud_H5_Bridge||{};if(B.check.isObject(n)&&n.track)return n.track(e.event,e.type,JSON.stringify(e)),B.check.isFunction(t)&&t(),void w.log("The data has been sent to the Android side");if("iOS"===B.info.os()){var r,i,o=null===(r=window.webkit)||void 0===r||null===(i=r.messageHandlers)||void 0===i?void 0:i.hinaNativeTracker;if(null!=o&&o.postMessage){var a=JSON.stringify({eventName:e.event,eventType:e.type,properties:e});return o.postMessage(a),B.check.isFunction(t)&&t(),void w.log("The data has been sent to the iOS side")}}w.log("The app JSBridge data transmission has failed.")}if(this.getConfig("batchSend"))new X(this.config).add(e);else{B.check.isString(e)||(e=JSON.stringify(e));var s=B.base64Encode(e),c="crc="+B.hashCode(s),l="data="+B.encodeURIComponent(s)+"&ext="+B.encodeURIComponent(c),u=this.getConfig("sendType"),h={callback:this.getConfig("globalCallback"),data:l,serverUrl:this.getConfig("serverUrl"),dataSendTimeout:this.getConfig("dataSendTimeout")};switch(u){case"ajax":new N(h).run();break;case"beacon":new F(h).run();break;default:new U(B.extend(h,{imgUseCrossOrigin:this.getConfig("imgUseCrossOrigin")})).run()}}}},{key:"quick",value:function(e){for(var t={autoTrack:this.autoTrackInstance.autoTrack,autoTrackSinglePage:this.autoTrackInstance.autoTrackSinglePage},n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t[e].call(this.autoTrackInstance,r)}},{key:"track",value:function(e,t,n){var r=B.check.isFunction(n)?n:function(){};B.check.isString(e)&&(B.check.isObject(t)||B.check.isUndefined(t))?this.sendRequest({type:"track",event:e,properties:t},r):w.log("eventName must be a sting and properties must be an object")}},{key:"setUserUId",value:function(e,t){if(B.check.isNumber(e)||B.check.isString(e)){e=String(e);var n=this.store.getFirstId(),r=this.store.getAccountId();e!==r?(n||this.store.set("firstId",r),this.store.setAccountId(e),this.sendRequest({account_id:this.store.getAccountId(),type:"track_signup",event:"H_SignUp",properties:{}},t)):console.log("setUserUId: uid is equal to account_id, , failed to execute setUserUId")}else w.log("setUserUId: uid must be string or number")}},{key:"getDeviceUId",value:function(){return this.store.getAnonymousId()}},{key:"setDeviceUId",value:function(e,t){var n=this.store.getFirstId();if(B.check.isUndefined(e)){var r=B.UUID();n?this.store.set("firstId",r):this.store.setAccountId(r)}else(B.check.isNumber(e)||B.check.isString(e))&&(e=String(e),!0===t?n?this.store.set("firstId",e):this.store.set("accountId",e):n?this.store.change("firstId",e):this.store.change("accountId",e))}},{key:"userSet",value:function(e,t){B.check.isObject(e)&&!B.check.isEmptyObject(e)&&this.sendRequest({type:"user_set",properties:e},t)}},{key:"userSetOnce",value:function(e,t){B.check.isObject(e)&&!B.check.isEmptyObject(e)&&this.sendRequest({type:"user_setOnce",properties:e},t)}},{key:"userAdd",value:function(e,t){B.check.isString(e)&&(e=s({},e,1));B.check.isObject(e)&&!B.check.isEmptyObject(e)&&function(e){for(var t in e)if(t in e&&!/-*\d+/.test(String(e[t])))return w.log("userAdd: value is must be number"),!1;return!0}(e)&&this.sendRequest({type:"user_add",properties:e},t)}},{key:"userUnset",value:function(e,t){var n={};B.check.isString(e)&&(e=[e]);B.check.isArray(e)?(B.each(e,(function(e){B.check.isString(e)?n[e]=!0:w.log("userUnset: value inside the array must be string and have already been filtered out",e)})),this.sendRequest({type:"user_unset",properties:n},t)):w.log("userUnset: param must be an array or string")}},{key:"userDelete",value:function(e){this.sendRequest({type:"user_delete"},e),this.store.setAccountId(B.UUID()),this.store.set("firstId","")}},{key:"registerCommonProperties",value:function(e){B.extend(K.currentProps,e)}},{key:"getPresetProperties",value:function(){return e=K.getUmtsParams("H_",""),n=e.allUtms,r=e.otherUtms,i={H_is_first_day:W.isFirstDay,H_is_first_time:W.isFirstTime,device_id:W.getDeviceId(),anonymous_id:W.getAnonymousId(),account_id:W.getAccountId(),properties:t({},K.currentProps)},B.extend(i.properties,n,r,K.getPresetProperties(),K.getPageProperties()),i;var e,n,r,i}},{key:"use",value:function(e,t){B.check.isString(e)?e in ie?(new ie[e]).init(this,t):w.log("please write a valid plugin name"):w.log("pluginName must be string")}}]),e}(),se=new Proxy(new ae,{get:function(e,t){return B.check.isFunction(e[t])?function(){if(e.initialized||"init"===t){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e[t].apply(e,r)}console.log("sdk not yet initialized!")}:e[t]}});window.hinaDataStatistic=se;var ce,le,ue,he,de=-1,fe=function(e){addEventListener("pageshow",(function(t){t.persisted&&(de=t.timeStamp,e(t))}),!0)},Te=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},ge=function(){var e=Te();return e&&e.activationStart||0},pe=function(e,t){var n=Te(),r="navigate";return de>=0?r="back-forward-cache":n&&(document.prerendering||ge()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},me=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},Se=function(e,t,n,r){var i,o;return function(a){t.value>=0&&(a||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},ve=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},be=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},Pe=function(e){var t=!1;return function(n){t||(e(n),t=!0)}},ye=-1,_e=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},Ae=function(e){"hidden"===document.visibilityState&&ye>-1&&(ye="visibilitychange"===e.type?e.timeStamp:0,ke())},Me=function(){addEventListener("visibilitychange",Ae,!0),addEventListener("prerenderingchange",Ae,!0)},ke=function(){removeEventListener("visibilitychange",Ae,!0),removeEventListener("prerenderingchange",Ae,!0)},Ee=function(){return ye<0&&(ye=_e(),Me(),fe((function(){setTimeout((function(){ye=_e(),Me()}),0)}))),{get firstHiddenTime(){return ye}}},He=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},Ce=[1800,3e3],Ge=function(e,t){t=t||{},He((function(){var n,r=Ee(),i=pe("FCP"),o=me("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-ge(),0),i.entries.push(e),n(!0)))}))}));o&&(n=Se(e,i,Ce,t.reportAllChanges),fe((function(r){i=pe("FCP"),n=Se(e,i,Ce,t.reportAllChanges),ve((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},we=[.1,.25],Ie=[1800,3e3],Be={passive:!0,capture:!0},Oe=new Date,De=function(e,t){ce||(ce=t,le=e,ue=new Date,Re(removeEventListener),xe())},xe=function(){if(le>=0&&le<ue-Oe){var e={entryType:"first-input",name:ce.type,target:ce.target,cancelable:ce.cancelable,startTime:ce.timeStamp,processingStart:ce.timeStamp+le};he.forEach((function(t){t(e)})),he=[]}},Le=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){De(e,t),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,Be),removeEventListener("pointercancel",r,Be)};addEventListener("pointerup",n,Be),addEventListener("pointercancel",r,Be)}(t,e):De(t,e)}},Re=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,Le,Be)}))},Ne=[100,300],Ue=[2500,4e3],Fe={},Ve=window,Xe=Ve.PerformanceObserver,je=Ve.performance,We=function(){function e(t,n){i(this,e),this.config=t,this.ctx=n,this.submitData={},this.performanceCallback=this.performanceCallback.bind(this)}return a(e,[{key:"init",value:function(){this.initVitails()}},{key:"setSubmitData",value:function(e,t){"string"==typeof e?this.submitData[e]=t:"object"===r(e)&&(this.submitData=_.extend(this.config,e))}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"onLongTask",value:function(e){Xe.supportedEntryTypes.includes("longtask")&&new Xe((function(t){var n=t.getEntries(),r=n.length,i=0;n.forEach((function(e){i+=Math.round(e.duration)})),e({H_long_task_time:i,H_long_task:r})})).observe({entryTypes:["longtask"]})}},{key:"onPageLoad",value:function(){var e=this;me("navigation",(function(t){t.forEach((function(t){var n={H_page_load_time:Math.round(t.loadEventEnd-t.startTime)};if(0!==n.H_page_load_time){e.ctx.sendRequest("track","H_performance_page",n);var r={H_url:t.name,H_first_byte_time:Math.round(t.responseStart-t.startTime),H_unload_time:Math.round(t.unloadEventEnd-t.unloadEventStart),H_redirect_time:Math.round(t.redirectEnd-t.redirectStart),H_cache_check_time:Math.round(t.domainLookupStart-t.fetchStart),H_dns_time:Math.round(t.domainLookupEnd-t.domainLookupStart),H_tcp_time:Math.round(t.connectEnd-t.connectStart),H_ssl_time:t.secureConnectionStart>0?Math.round(t.connectEnd-t.secureConnectionStart):0,H_first_byte_response_time:Math.round(t.responseStart-t.requestStart),H_dom_ready_time:Math.round(t.domContentLoadedEventEnd-t.fetchStart),H_content_transfer_time:Math.round(t.responseEnd-t.responseStart),H_dom_parsing_time:Math.round(t.domInteractive-t.responseEnd),H_page_load_time:Math.round(t.loadEventEnd-t.startTime),H_resource_load_time:Math.round(t.responseEnd-t.startTime),H_load_event_time:Math.round(t.loadEventEnd-t.startTime)};e.ctx.sendRequest("track","H_performance_loading",r)}}))}))}},{key:"onPageUnlaod",value:function(){window.addEventListener("beforeunload",(function(){W.setSessionId("")}))}},{key:"initVitails",value:function(){!function(e,t){t=t||{},He((function(){var n,r=Ee(),i=pe("FP"),o=me("paint",(function(e){e.forEach((function(e){"first-paint"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-ge(),0),i.entries.push(e),n(!0)))}))}));o&&(n=Se(e,i,Ie,t.reportAllChanges),fe((function(r){i=pe("FP"),n=Se(e,i,Ie,t.reportAllChanges),ve((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))}(this.performanceCallback),Ge(this.performanceCallback),function(e,t){t=t||{},Ge(Pe((function(){var n,r=pe("CLS",0),i=0,o=[],a=function(e){e.forEach((function(e){if(!e.hadRecentInput){var t=o[0],n=o[o.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}})),i>r.value&&(r.value=i,r.entries=o,n())},s=me("layout-shift",a);s&&(n=Se(e,r,we,t.reportAllChanges),be((function(){a(s.takeRecords()),n(!0)})),fe((function(){i=0,r=pe("CLS",0),n=Se(e,r,we,t.reportAllChanges),ve((function(){return n()}))})),setTimeout(n,0))})))}(this.performanceCallback),function(e,t){t=t||{},He((function(){var n,r=Ee(),i=pe("FID"),o=function(e){e.startTime<r.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0))},a=function(e){e.forEach(o)},s=me("first-input",a);n=Se(e,i,Ne,t.reportAllChanges),s&&be(Pe((function(){a(s.takeRecords()),s.disconnect()}))),s&&fe((function(){var r;i=pe("FID"),n=Se(e,i,Ne,t.reportAllChanges),he=[],le=-1,ce=null,Re(addEventListener),r=o,he.push(r),xe()}))}))}(this.performanceCallback),this.onLongTask(this.performanceCallback),this.onPageLoad(this.performanceCallback),this.onPageUnlaod(),function(e,t){t=t||{},He((function(){var n,r=Ee(),i=pe("LCP"),o=function(e){var t=e[e.length-1];t&&t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-ge(),0),i.entries=[t],n())},a=me("largest-contentful-paint",o);if(a){n=Se(e,i,Ue,t.reportAllChanges);var s=Pe((function(){Fe[i.id]||(o(a.takeRecords()),a.disconnect(),Fe[i.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,s,!0)})),be(s),fe((function(r){i=pe("LCP"),n=Se(e,i,Ue,t.reportAllChanges),ve((function(){i.value=performance.now()-r.timeStamp,Fe[i.id]=!0,n(!0)}))}))}}))}(this.performanceCallback)}},{key:"getMetricTime",value:function(){if(je&&je.timing)return new Promise((function(e){if(Xe.supportedEntryTypes.includes("navigation"))me("navigation",(function(t){t.forEach((function(t){var n={H_url:t.name,H_first_byte_time:Math.round(t.responseStart-t.startTime),H_unload_time:Math.round(t.unloadEventEnd-t.unloadEventStart),H_redirect_time:Math.round(t.redirectEnd-t.redirectStart),H_cache_check_time:Math.round(t.domainLookupStart-t.fetchStart),H_dns_time:Math.round(t.domainLookupEnd-t.domainLookupStart),H_tcp_time:Math.round(t.connectEnd-t.connectStart),H_ssl_time:t.secureConnectionStart>0?Math.round(t.connectEnd-t.secureConnectionStart):0,H_first_byte_response_time:Math.round(t.responseStart-t.requestStart),H_dom_ready_time:Math.round(t.domContentLoadedEventEnd-t.fetchStart),H_content_transfer_time:Math.round(t.responseEnd-t.responseStart),H_dom_parsing_time:Math.round(t.domInteractive-t.responseEnd),H_page_load_time:Math.round(t.loadEventEnd-t.startTime),H_resource_load_time:Math.round(t.responseEnd-t.startTime),H_load_event_time:Math.round(t.loadEventEnd-t.startTime)};e(n)}))}));else{var t=je.timing,n={H_url:location.href,H_first_byte_time:Math.round(t.responseStart-t.startTime),H_unload_time:Math.round(t.unloadEventEnd-t.unloadEventStart),H_redirect_time:Math.round(t.redirectEnd-t.redirectStart),H_cache_check_time:Math.round(t.domainLookupStart-t.fetchStart),H_dns_time:Math.round(t.domainLookupEnd-t.domainLookupStart),H_tcp_time:Math.round(t.connectEnd-t.connectStart),H_ssl_time:t.secureConnectionStart>0?Math.round(t.connectEnd-t.secureConnectionStart):0,H_first_byte_response_time:Math.round(t.responseStart-t.requestStart),H_dom_ready_time:Math.round(t.domContentLoadedEventEnd-t.fetchStart),H_content_transfer_time:Math.round(t.responseEnd-t.responseStart),H_dom_parsing_time:Math.round(t.domInteractive-t.responseEnd),H_page_load_time:Math.round(t.loadEventEnd-t.fetchStart),H_resource_load_time:Math.round(t.responseEnd-t.startTime),H_load_event_time:Math.round(t.loadEventEnd-t.startTime)};e(n)}}));console.log("performance api is not supported in your browser")}},{key:"getSourceTime",value:function(){if(je&&je.getEntries)return new Promise((function(e){var t=je.getEntries(),n=[];t&&0!==(null==t?void 0:t.length)||e(n),t.forEach((function(e){var t={};"resource"===e.entryType&&(t.name=e.name,t.initiatorType=e.initiatorType,t.nextHopProtocol=e.nextHopProtocol,t.redirectTime=(e.redirectEnd-e.redirectStart).toFixed(2),t.dnsTime=(e.domainLookupEnd-e.domainLookupStart).toFixed(2),t.tcpTime=(e.connectEnd-e.connectStart).toFixed(2),t.firstByteResponseTime=(e.responseStart-e.requestStart).toFixed(2),t.totalResponseTime=(e.responseEnd-e.requestStart).toFixed(2),n.push(t))})),e(n)}));console.log("performance.getEntries api is not supported in your browser")}},{key:"performanceCallback",value:function(e){var t=e.name;e.rating;var n=e.value,r=Math.round(n),i={};i="FP"===t?{H_first_paint_time:r}:"FCP"===t?{H_first_contentful_paint_time:r}:"LCP"===t?{H_largest_contentful_paint_time:r}:"FID"===t?{H_first_input_delay:r}:"CLS"===t?{H_cumulative_layout_shift:n}:e,this.ctx.sendRequest("track","H_performance_page",i)}}]),e}(),Ke={exports:{}},qe={exports:{}};!function(e,t){function n(e,t){for(var n=0;n<e.length;++n)if(e[n]===t)return n;return-1}function r(e,t){var r=[],i=[];return null==t&&(t=function(e,t){return r[0]===t?"[Circular ~]":"[Circular ~."+i.slice(0,n(r,t)).join(".")+"]"}),function(o,a){if(r.length>0){var s=n(r,this);~s?r.splice(s+1):r.push(this),~s?i.splice(s,1/0,o):i.push(o),~n(r,a)&&(a=t.call(this,o,a))}else r.push(a);return null==e?a instanceof Error?function(e){var t={stack:e.stack,message:e.message,name:e.name};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}(a):a:e.call(this,o,a)}}(e.exports=function(e,t,n,i){return JSON.stringify(e,r(t,i),n)}).getSerialize=r}(qe);var ze=qe.exports,Qe=ze,Je="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{};function Ye(e){return void 0===e}function Ze(e){return"[object Object]"===Object.prototype.toString.call(e)}function $e(e){return"[object String]"===Object.prototype.toString.call(e)}function et(e){return"[object Array]"===Object.prototype.toString.call(e)}function tt(){if(!("fetch"in Je))return!1;try{return new Headers,new Request(""),new Response,!0}catch(e){return!1}}function nt(e,t){var n,r;if(Ye(e.length))for(n in e)it(e,n)&&t.call(null,n,e[n]);else if(r=e.length)for(n=0;n<r;n++)t.call(null,n,e[n])}function rt(e,t){if("number"!=typeof t)throw new Error("2nd argument to `truncate` function should be a number");return"string"!=typeof e||0===t||e.length<=t?e:e.substr(0,t)+"…"}function it(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function ot(e){for(var t,n=[],r=0,i=e.length;r<i;r++)$e(t=e[r])?n.push(t.replace(/([.*+?^=!:${}()|\[\]\/\\])/g,"\\$1")):t&&t.source&&n.push(t.source);return new RegExp(n.join("|"),"i")}function at(e){var t,n,r,i,o,a=[];if(!e||!e.tagName)return"";if(a.push(e.tagName.toLowerCase()),e.id&&a.push("#"+e.id),(t=e.className)&&$e(t))for(n=t.split(/\s+/),o=0;o<n.length;o++)a.push("."+n[o]);var s=["type","name","title","alt"];for(o=0;o<s.length;o++)r=s[o],(i=e.getAttribute(r))&&a.push("["+r+'="'+i+'"]');return a.join("")}function st(e,t){return!!(!!e^!!t)}function ct(e,t){if(st(e,t))return!1;var n,r,i=e.frames,o=t.frames;if(void 0===i||void 0===o)return!1;if(i.length!==o.length)return!1;for(var a=0;a<i.length;a++)if(n=i[a],r=o[a],n.filename!==r.filename||n.lineno!==r.lineno||n.colno!==r.colno||n.function!==r.function)return!1;return!0}function lt(e){return function(e){return~-encodeURI(e).split(/%..|./).length}(JSON.stringify(e))}function ut(e){if("string"==typeof e){return rt(e,40)}if("number"==typeof e||"boolean"==typeof e||void 0===e)return e;var t=Object.prototype.toString.call(e);return"[object Object]"===t?"[Object]":"[object Array]"===t?"[Array]":"[object Function]"===t?e.name?"[Function: "+e.name+"]":"[Function]":e}function ht(e,t){return 0===t?ut(e):Ze(e)?Object.keys(e).reduce((function(n,r){return n[r]=ht(e[r],t-1),n}),{}):Array.isArray(e)?e.map((function(e){return ht(e,t-1)})):ut(e)}var dt={isObject:function(e){return"object"==typeof e&&null!==e},isError:function(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return e instanceof Error}},isErrorEvent:function(e){return"[object ErrorEvent]"===Object.prototype.toString.call(e)},isDOMError:function(e){return"[object DOMError]"===Object.prototype.toString.call(e)},isDOMException:function(e){return"[object DOMException]"===Object.prototype.toString.call(e)},isUndefined:Ye,isFunction:function(e){return"function"==typeof e},isPlainObject:Ze,isString:$e,isArray:et,isEmptyObject:function(e){if(!Ze(e))return!1;for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},supportsErrorEvent:function(){try{return new ErrorEvent(""),!0}catch(e){return!1}},supportsDOMError:function(){try{return new DOMError(""),!0}catch(e){return!1}},supportsDOMException:function(){try{return new DOMException(""),!0}catch(e){return!1}},supportsFetch:tt,supportsReferrerPolicy:function(){if(!tt())return!1;try{return new Request("pickleRick",{referrerPolicy:"origin"}),!0}catch(e){return!1}},supportsPromiseRejectionEvent:function(){return"function"==typeof PromiseRejectionEvent},wrappedCallback:function(e){return function(t,n){var r=e(t)||t;return n&&n(r)||r}},each:nt,objectMerge:function(e,t){return t?(nt(t,(function(t,n){e[t]=n})),e):e},truncate:rt,objectFrozen:function(e){return!!Object.isFrozen&&Object.isFrozen(e)},hasKey:it,joinRegExp:ot,urlencode:function(e){var t=[];return nt(e,(function(e,n){t.push(encodeURIComponent(e)+"="+encodeURIComponent(n))})),t.join("&")},uuid4:function(){var e=Je.crypto||Je.msCrypto;if(!Ye(e)&&e.getRandomValues){var t=new Uint16Array(8);e.getRandomValues(t),t[3]=4095&t[3]|16384,t[4]=16383&t[4]|32768;var n=function(e){for(var t=e.toString(16);t.length<4;)t="0"+t;return t};return n(t[0])+n(t[1])+n(t[2])+n(t[3])+n(t[4])+n(t[5])+n(t[6])+n(t[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},htmlTreeAsString:function(e){for(var t,n=[],r=0,i=0;e&&r++<5&&!("html"===(t=at(e))||r>1&&i+3*n.length+t.length>=80);)n.push(t),i+=t.length,e=e.parentNode;return n.reverse().join(" > ")},htmlElementAsString:at,isSameException:function(e,t){return!st(e,t)&&(e=e.values[0],t=t.values[0],e.type===t.type&&e.value===t.value&&(!function(e,t){return Ye(e)&&Ye(t)}(e.stacktrace,t.stacktrace)&&ct(e.stacktrace,t.stacktrace)))},isSameStacktrace:ct,parseUrl:function(e){if("string"!=typeof e)return{};var t=e.match(/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/),n=t[6]||"",r=t[8]||"";return{protocol:t[2],host:t[4],path:t[5],relative:t[5]+n+r}},fill:function(e,t,n,r){if(null!=e){var i=e[t];e[t]=n(i),e[t].__raven__=!0,e[t].__orig__=i,r&&r.push([e,t,i])}},safeJoin:function(e,t){if(!et(e))return"";for(var n=[],r=0;r<e.length;r++)try{n.push(String(e[r]))}catch(e){n.push("[value cannot be serialized]")}return n.join(t)},serializeException:function e(t,n,r){if(!Ze(t))return t;r="number"!=typeof(n="number"!=typeof n?3:n)?51200:r;var i=ht(t,n);return lt(Qe(i))>r?e(t,n-1):i},serializeKeysForMessage:function(e,t){if("number"==typeof e||"string"==typeof e)return e.toString();if(!Array.isArray(e))return"";if(0===(e=e.filter((function(e){return"string"==typeof e}))).length)return"[object has no keys]";if(t="number"!=typeof t?40:t,e[0].length>=t)return e[0];for(var n=e.length;n>0;n--){var r=e.slice(0,n).join(", ");if(!(r.length>t))return n===e.length?r:r+"…"}return""},sanitize:function(e,t){if(!et(t)||et(t)&&0===t.length)return e;var n,r=ot(t);try{n=JSON.parse(Qe(e))}catch(t){return e}return function e(t){return et(t)?t.map((function(t){return e(t)})):Ze(t)?Object.keys(t).reduce((function(n,i){return r.test(i)?n[i]="********":n[i]=e(t[i]),n}),{}):t}(n)}},ft=dt,Tt={collectWindowErrors:!0,debug:!1},gt="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{},pt=[].slice,mt="?",St=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;function vt(){return"undefined"==typeof document||null==document.location?"":document.location.href}Tt.report=function(){var e,t,n=[],r=null,i=null,o=null;function a(e,t){var r=null;if(!t||Tt.collectWindowErrors){for(var i in n)if(n.hasOwnProperty(i))try{n[i].apply(null,[e].concat(pt.call(arguments,2)))}catch(e){r=e}if(r)throw r}}function s(t,n,r,i,s){var l=ft.isErrorEvent(s)?s.error:s,u=ft.isErrorEvent(t)?t.message:t;if(o)Tt.computeStackTrace.augmentStackTraceWithInitialElement(o,n,r,u),c();else if(l&&ft.isError(l))a(Tt.computeStackTrace(l),!0);else{var h,d={url:n,line:r,column:i},f=void 0;if("[object String]"==={}.toString.call(u))(h=u.match(St))&&(f=h[1],u=h[2]);d.func=mt,a({name:f,message:u,url:vt(),stack:[d]},!0)}return!!e&&e.apply(this,arguments)}function c(){var e=o,t=r;r=null,o=null,i=null,a.apply(null,[e,!1].concat(t))}function l(e,t){var n=pt.call(arguments,1);if(o){if(i===e)return;c()}var a=Tt.computeStackTrace(e);if(o=a,i=e,r=n,setTimeout((function(){i===e&&c()}),a.incomplete?2e3:0),!1!==t)throw e}return l.subscribe=function(r){!function(){if(t)return;e=gt.onerror,gt.onerror=s,t=!0}(),n.push(r)},l.unsubscribe=function(e){for(var t=n.length-1;t>=0;--t)n[t]===e&&n.splice(t,1)},l.uninstall=function(){!function(){if(!t)return;gt.onerror=e,t=!1,e=void 0}(),n=[]},l}(),Tt.computeStackTrace=function(){function e(e){if(void 0!==e.stack&&e.stack){var t,n,r,i=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[a-z]:|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,o=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx(?:-web)|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,a=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|moz-extension).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js))(?::(\d+))?(?::(\d+))?\s*$/i,s=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,c=/\((\S*)(?::(\d+))(?::(\d+))\)/,l=e.stack.split("\n"),u=[];/^(.*) is undefined$/.exec(e.message);for(var h=0,d=l.length;h<d;++h){if(n=i.exec(l[h])){var f=n[2]&&0===n[2].indexOf("native");n[2]&&0===n[2].indexOf("eval")&&(t=c.exec(n[2]))&&(n[2]=t[1],n[3]=t[2],n[4]=t[3]),r={url:f?null:n[2],func:n[1]||mt,args:f?[n[2]]:[],line:n[3]?+n[3]:null,column:n[4]?+n[4]:null}}else if(n=o.exec(l[h]))r={url:n[2],func:n[1]||mt,args:[],line:+n[3],column:n[4]?+n[4]:null};else{if(!(n=a.exec(l[h])))continue;n[3]&&n[3].indexOf(" > eval")>-1&&(t=s.exec(n[3]))?(n[3]=t[1],n[4]=t[2],n[5]=null):0!==h||n[5]||void 0===e.columnNumber||(u[0].column=e.columnNumber+1),r={url:n[3],func:n[1]||mt,args:n[2]?n[2].split(","):[],line:n[4]?+n[4]:null,column:n[5]?+n[5]:null}}if(!r.func&&r.line&&(r.func=mt),r.url&&"blob:"===r.url.substr(0,5)){var T=new XMLHttpRequest;if(T.open("GET",r.url,!1),T.send(null),200===T.status){var g=T.responseText||"",p=(g=g.slice(-300)).match(/\/\/# sourceMappingURL=(.*)$/);if(p){var m=p[1];"~"===m.charAt(0)&&(m=("undefined"==typeof document||null==document.location?"":document.location.origin?document.location.origin:document.location.protocol+"//"+document.location.hostname+(document.location.port?":"+document.location.port:""))+m.slice(1)),r.url=m.slice(0,-4)}}}u.push(r)}return u.length?{name:e.name,message:e.message,url:vt(),stack:u}:null}}function t(e,t,n,r){var i={url:t,line:n};if(i.url&&i.line){if(e.incomplete=!1,i.func||(i.func=mt),e.stack.length>0&&e.stack[0].url===i.url){if(e.stack[0].line===i.line)return!1;if(!e.stack[0].line&&e.stack[0].func===i.func)return e.stack[0].line=i.line,!1}return e.stack.unshift(i),e.partial=!0,!0}return e.incomplete=!0,!1}function n(e,i){for(var o,a,s=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,c=[],l={},u=!1,h=n.caller;h&&!u;h=h.caller)if(h!==r&&h!==Tt.report){if(a={url:null,func:mt,line:null,column:null},h.name?a.func=h.name:(o=s.exec(h.toString()))&&(a.func=o[1]),void 0===a.func)try{a.func=o.input.substring(0,o.input.indexOf("{"))}catch(e){}l[""+h]?u=!0:l[""+h]=!0,c.push(a)}i&&c.splice(0,i);var d={name:e.name,message:e.message,url:vt(),stack:c};return t(d,e.sourceURL||e.fileName,e.line||e.lineNumber,e.message||e.description),d}function r(t,r){var i=null;r=null==r?0:+r;try{if(i=e(t))return i}catch(e){if(Tt.debug)throw e}try{if(i=n(t,r+1))return i}catch(e){if(Tt.debug)throw e}return{name:t.name,message:t.message,url:vt()}}return r.augmentStackTraceWithInitialElement=t,r.computeStackTraceFromStackProp=e,r}();var bt=Tt;function Pt(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function yt(e,t,n,r,i,o){return Pt((a=Pt(Pt(t,e),Pt(r,o)))<<(s=i)|a>>>32-s,n);var a,s}function _t(e,t,n,r,i,o,a){return yt(t&n|~t&r,e,t,i,o,a)}function At(e,t,n,r,i,o,a){return yt(t&r|n&~r,e,t,i,o,a)}function Mt(e,t,n,r,i,o,a){return yt(t^n^r,e,t,i,o,a)}function kt(e,t,n,r,i,o,a){return yt(n^(t|~r),e,t,i,o,a)}function Et(e,t){var n,r,i,o,a;e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;var s=1732584193,c=-271733879,l=-1732584194,u=271733878;for(n=0;n<e.length;n+=16)r=s,i=c,o=l,a=u,s=_t(s,c,l,u,e[n],7,-680876936),u=_t(u,s,c,l,e[n+1],12,-389564586),l=_t(l,u,s,c,e[n+2],17,606105819),c=_t(c,l,u,s,e[n+3],22,-1044525330),s=_t(s,c,l,u,e[n+4],7,-176418897),u=_t(u,s,c,l,e[n+5],12,1200080426),l=_t(l,u,s,c,e[n+6],17,-1473231341),c=_t(c,l,u,s,e[n+7],22,-45705983),s=_t(s,c,l,u,e[n+8],7,1770035416),u=_t(u,s,c,l,e[n+9],12,-1958414417),l=_t(l,u,s,c,e[n+10],17,-42063),c=_t(c,l,u,s,e[n+11],22,-1990404162),s=_t(s,c,l,u,e[n+12],7,1804603682),u=_t(u,s,c,l,e[n+13],12,-40341101),l=_t(l,u,s,c,e[n+14],17,-1502002290),s=At(s,c=_t(c,l,u,s,e[n+15],22,1236535329),l,u,e[n+1],5,-165796510),u=At(u,s,c,l,e[n+6],9,-1069501632),l=At(l,u,s,c,e[n+11],14,643717713),c=At(c,l,u,s,e[n],20,-373897302),s=At(s,c,l,u,e[n+5],5,-701558691),u=At(u,s,c,l,e[n+10],9,38016083),l=At(l,u,s,c,e[n+15],14,-660478335),c=At(c,l,u,s,e[n+4],20,-405537848),s=At(s,c,l,u,e[n+9],5,568446438),u=At(u,s,c,l,e[n+14],9,-1019803690),l=At(l,u,s,c,e[n+3],14,-187363961),c=At(c,l,u,s,e[n+8],20,1163531501),s=At(s,c,l,u,e[n+13],5,-1444681467),u=At(u,s,c,l,e[n+2],9,-51403784),l=At(l,u,s,c,e[n+7],14,1735328473),s=Mt(s,c=At(c,l,u,s,e[n+12],20,-1926607734),l,u,e[n+5],4,-378558),u=Mt(u,s,c,l,e[n+8],11,-2022574463),l=Mt(l,u,s,c,e[n+11],16,1839030562),c=Mt(c,l,u,s,e[n+14],23,-35309556),s=Mt(s,c,l,u,e[n+1],4,-1530992060),u=Mt(u,s,c,l,e[n+4],11,1272893353),l=Mt(l,u,s,c,e[n+7],16,-155497632),c=Mt(c,l,u,s,e[n+10],23,-1094730640),s=Mt(s,c,l,u,e[n+13],4,681279174),u=Mt(u,s,c,l,e[n],11,-358537222),l=Mt(l,u,s,c,e[n+3],16,-722521979),c=Mt(c,l,u,s,e[n+6],23,76029189),s=Mt(s,c,l,u,e[n+9],4,-640364487),u=Mt(u,s,c,l,e[n+12],11,-421815835),l=Mt(l,u,s,c,e[n+15],16,530742520),s=kt(s,c=Mt(c,l,u,s,e[n+2],23,-995338651),l,u,e[n],6,-198630844),u=kt(u,s,c,l,e[n+7],10,1126891415),l=kt(l,u,s,c,e[n+14],15,-1416354905),c=kt(c,l,u,s,e[n+5],21,-57434055),s=kt(s,c,l,u,e[n+12],6,1700485571),u=kt(u,s,c,l,e[n+3],10,-1894986606),l=kt(l,u,s,c,e[n+10],15,-1051523),c=kt(c,l,u,s,e[n+1],21,-2054922799),s=kt(s,c,l,u,e[n+8],6,1873313359),u=kt(u,s,c,l,e[n+15],10,-30611744),l=kt(l,u,s,c,e[n+6],15,-1560198380),c=kt(c,l,u,s,e[n+13],21,1309151649),s=kt(s,c,l,u,e[n+4],6,-145523070),u=kt(u,s,c,l,e[n+11],10,-1120210379),l=kt(l,u,s,c,e[n+2],15,718787259),c=kt(c,l,u,s,e[n+9],21,-343485551),s=Pt(s,r),c=Pt(c,i),l=Pt(l,o),u=Pt(u,a);return[s,c,l,u]}function Ht(e){var t,n="",r=32*e.length;for(t=0;t<r;t+=8)n+=String.fromCharCode(e[t>>5]>>>t%32&255);return n}function Ct(e){var t,n=[];for(n[(e.length>>2)-1]=void 0,t=0;t<n.length;t+=1)n[t]=0;var r=8*e.length;for(t=0;t<r;t+=8)n[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return n}function Gt(e){var t,n,r="0123456789abcdef",i="";for(n=0;n<e.length;n+=1)t=e.charCodeAt(n),i+=r.charAt(t>>>4&15)+r.charAt(15&t);return i}function wt(e){return unescape(encodeURIComponent(e))}function It(e){return function(e){return Ht(Et(Ct(e),8*e.length))}(wt(e))}function Bt(e,t){return function(e,t){var n,r,i=Ct(e),o=[],a=[];for(o[15]=a[15]=void 0,i.length>16&&(i=Et(i,8*e.length)),n=0;n<16;n+=1)o[n]=909522486^i[n],a[n]=1549556828^i[n];return r=Et(o.concat(Ct(t)),512+8*t.length),Ht(Et(a.concat(r),640))}(wt(e),wt(t))}var Ot=function(e,t,n){return t?n?Bt(t,e):function(e,t){return Gt(Bt(e,t))}(t,e):n?It(e):function(e){return Gt(It(e))}(e)};function Dt(e){this.name="RavenConfigError",this.message=e}Dt.prototype=new Error,Dt.prototype.constructor=Dt;var xt=dt,Lt=function(e,t,n){var r=e[t],i=e;if(t in e){var o="warn"===t?"warning":t;e[t]=function(){var e=[].slice.call(arguments),a=xt.safeJoin(e," "),s={level:o,logger:"console",extra:{arguments:e}};"assert"===t?!1===e[0]&&(a="Assertion failed: "+(xt.safeJoin(e.slice(1)," ")||"console.assert"),s.extra.arguments=e.slice(1),n&&n(a,s)):n&&n(a,s),r&&Function.prototype.apply.call(r,i,e)}}},Rt=bt,Nt=ze,Ut=Ot,Ft=Dt,Vt=dt.isErrorEvent,Xt=dt.isDOMError,jt=dt.isDOMException,Wt=dt.isError,Kt=dt.isObject,qt=dt.isPlainObject,zt=dt.isUndefined,Qt=dt.isFunction,Jt=dt.isString,Yt=dt.isArray,Zt=dt.isEmptyObject,$t=dt.each,en=dt.objectMerge,tn=dt.truncate,nn=dt.objectFrozen,rn=dt.hasKey,on=dt.joinRegExp,an=dt.urlencode,sn=dt.uuid4,cn=dt.htmlTreeAsString,ln=dt.isSameException,un=dt.isSameStacktrace,hn=dt.parseUrl,dn=dt.fill,fn=dt.supportsFetch,Tn=dt.supportsReferrerPolicy,gn=dt.serializeKeysForMessage,pn=dt.serializeException,mn=dt.sanitize,Sn=Lt,vn="source protocol user pass host port path".split(" "),bn=/^(?:(\w+):)?\/\/(?:(\w+)(:\w+)?@)?([\w\.-]+)(?::(\d+))?(\/.*)/;function Pn(){return+new Date}var yn="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{},_n=yn.document,An=yn.navigator;function Mn(e,t){return Qt(t)?function(n){return t(n,e)}:t}function kn(){for(var e in this._hasJSON=!("object"!=typeof JSON||!JSON.stringify),this._hasDocument=!zt(_n),this._hasNavigator=!zt(An),this._lastCapturedException=null,this._lastData=null,this._lastEventId=null,this._globalServer=null,this._globalKey=null,this._globalProject=null,this._globalContext={},this._globalOptions={release:yn.SENTRY_RELEASE&&yn.SENTRY_RELEASE.id,logger:"javascript",ignoreErrors:[],ignoreUrls:[],whitelistUrls:[],includePaths:[],headers:null,collectWindowErrors:!0,captureUnhandledRejections:!0,maxMessageLength:0,maxUrlLength:250,stackTraceLimit:50,autoBreadcrumbs:!0,instrument:!0,sampleRate:1,sanitizeKeys:[]},this._fetchDefaults={method:"POST",referrerPolicy:Tn()?"origin":""},this._ignoreOnError=0,this._isRavenInstalled=!1,this._originalErrorStackTraceLimit=Error.stackTraceLimit,this._originalConsole=yn.console||{},this._originalConsoleMethods={},this._plugins=[],this._startTime=Pn(),this._wrappedBuiltIns=[],this._breadcrumbs=[],this._lastCapturedEvent=null,this._keypressTimeout,this._location=yn.location,this._lastHref=this._location&&this._location.href,this._resetBackoff(),this._originalConsole)this._originalConsoleMethods[e]=this._originalConsole[e]}kn.prototype={VERSION:"3.27.2",debug:!1,TraceKit:Rt,config:function(e,t){var n=this;if(n._globalServer)return this._logDebug("error","Error: Raven has already been configured"),n;if(!e)return n;var r=n._globalOptions;t&&$t(t,(function(e,t){"tags"===e||"extra"===e||"user"===e?n._globalContext[e]=t:r[e]=t})),n.setDSN(e),r.ignoreErrors.push(/^Script error\.?$/),r.ignoreErrors.push(/^Javascript error: Script error\.? on line 0$/),r.ignoreErrors=on(r.ignoreErrors),r.ignoreUrls=!!r.ignoreUrls.length&&on(r.ignoreUrls),r.whitelistUrls=!!r.whitelistUrls.length&&on(r.whitelistUrls),r.includePaths=on(r.includePaths),r.maxBreadcrumbs=Math.max(0,Math.min(r.maxBreadcrumbs||100,100));var i={xhr:!0,console:!0,dom:!0,location:!0,sentry:!0},o=r.autoBreadcrumbs;"[object Object]"==={}.toString.call(o)?o=en(i,o):!1!==o&&(o=i),r.autoBreadcrumbs=o;var a={tryCatch:!0},s=r.instrument;return"[object Object]"==={}.toString.call(s)?s=en(a,s):!1!==s&&(s=a),r.instrument=s,Rt.collectWindowErrors=!!r.collectWindowErrors,n},install:function(){var e=this;return e.isSetup()&&!e._isRavenInstalled&&(Rt.report.subscribe((function(){e._handleOnErrorStackInfo.apply(e,arguments)})),e._globalOptions.captureUnhandledRejections&&e._attachPromiseRejectionHandler(),e._patchFunctionToString(),e._globalOptions.instrument&&e._globalOptions.instrument.tryCatch&&e._instrumentTryCatch(),e._globalOptions.autoBreadcrumbs&&e._instrumentBreadcrumbs(),e._drainPlugins(),e._isRavenInstalled=!0),Error.stackTraceLimit=e._globalOptions.stackTraceLimit,this},setDSN:function(e){var t=this,n=t._parseDSN(e),r=n.path.lastIndexOf("/"),i=n.path.substr(1,r);t._dsn=e,t._globalKey=n.user,t._globalSecret=n.pass&&n.pass.substr(1),t._globalProject=n.path.substr(r+1),t._globalServer=t._getGlobalServer(n),t._globalEndpoint=t._globalServer+"/"+i+"api/"+t._globalProject+"/store/",this._resetBackoff()},context:function(e,t,n){return Qt(e)&&(n=t||[],t=e,e={}),this.wrap(e,t).apply(this,n)},wrap:function(e,t,n){var r=this;if(zt(t)&&!Qt(e))return e;if(Qt(e)&&(t=e,e=void 0),!Qt(t))return t;try{if(t.__raven__)return t;if(t.__raven_wrapper__)return t.__raven_wrapper__}catch(e){return t}function i(){var i=[],o=arguments.length,a=!e||e&&!1!==e.deep;for(n&&Qt(n)&&n.apply(this,arguments);o--;)i[o]=a?r.wrap(e,arguments[o]):arguments[o];try{return t.apply(this,i)}catch(t){throw r._ignoreNextOnError(),r.captureException(t,e),t}}for(var o in t)rn(t,o)&&(i[o]=t[o]);return i.prototype=t.prototype,t.__raven_wrapper__=i,i.__raven__=!0,i.__orig__=t,i},uninstall:function(){return Rt.report.uninstall(),this._detachPromiseRejectionHandler(),this._unpatchFunctionToString(),this._restoreBuiltIns(),this._restoreConsole(),Error.stackTraceLimit=this._originalErrorStackTraceLimit,this._isRavenInstalled=!1,this},_promiseRejectionHandler:function(e){this._logDebug("debug","Raven caught unhandled promise rejection:",e),this.captureException(e.reason,{mechanism:{type:"onunhandledrejection",handled:!1}})},_attachPromiseRejectionHandler:function(){return this._promiseRejectionHandler=this._promiseRejectionHandler.bind(this),yn.addEventListener&&yn.addEventListener("unhandledrejection",this._promiseRejectionHandler),this},_detachPromiseRejectionHandler:function(){return yn.removeEventListener&&yn.removeEventListener("unhandledrejection",this._promiseRejectionHandler),this},captureException:function(e,t){if(t=en({trimHeadFrames:0},t||{}),Vt(e)&&e.error)e=e.error;else{if(Xt(e)||jt(e)){var n=e.name||(Xt(e)?"DOMError":"DOMException"),r=e.message?n+": "+e.message:n;return this.captureMessage(r,en(t,{stacktrace:!0,trimHeadFrames:t.trimHeadFrames+1}))}if(Wt(e));else{if(!qt(e))return this.captureMessage(e,en(t,{stacktrace:!0,trimHeadFrames:t.trimHeadFrames+1}));t=this._getCaptureExceptionOptionsFromPlainObject(t,e),e=new Error(t.message)}}this._lastCapturedException=e;try{var i=Rt.computeStackTrace(e);this._handleStackInfo(i,t)}catch(t){if(e!==t)throw t}return this},_getCaptureExceptionOptionsFromPlainObject:function(e,t){var n=Object.keys(t).sort(),r=en(e,{message:"Non-Error exception captured with keys: "+gn(n),fingerprint:[Ut(n)],extra:e.extra||{}});return r.extra.__serialized__=pn(t),r},captureMessage:function(e,t){if(!this._globalOptions.ignoreErrors.test||!this._globalOptions.ignoreErrors.test(e)){var n,r=en({message:e+=""},t=t||{});try{throw new Error(e)}catch(e){n=e}n.name=null;var i=Rt.computeStackTrace(n),o=Yt(i.stack)&&i.stack[1];o&&"Raven.captureException"===o.func&&(o=i.stack[2]);var a=o&&o.url||"";if((!this._globalOptions.ignoreUrls.test||!this._globalOptions.ignoreUrls.test(a))&&(!this._globalOptions.whitelistUrls.test||this._globalOptions.whitelistUrls.test(a))){if(this._globalOptions.stacktrace||t.stacktrace||""===r.message){r.fingerprint=null==r.fingerprint?e:r.fingerprint,(t=en({trimHeadFrames:0},t)).trimHeadFrames+=1;var s=this._prepareFrames(i,t);r.stacktrace={frames:s.reverse()}}return r.fingerprint&&(r.fingerprint=Yt(r.fingerprint)?r.fingerprint:[r.fingerprint]),this._send(r),this}}},captureBreadcrumb:function(e){var t=en({timestamp:Pn()/1e3},e);if(Qt(this._globalOptions.breadcrumbCallback)){var n=this._globalOptions.breadcrumbCallback(t);if(Kt(n)&&!Zt(n))t=n;else if(!1===n)return this}return this._breadcrumbs.push(t),this._breadcrumbs.length>this._globalOptions.maxBreadcrumbs&&this._breadcrumbs.shift(),this},addPlugin:function(e){var t=[].slice.call(arguments,1);return this._plugins.push([e,t]),this._isRavenInstalled&&this._drainPlugins(),this},setUserContext:function(e){return this._globalContext.user=e,this},setExtraContext:function(e){return this._mergeContext("extra",e),this},setTagsContext:function(e){return this._mergeContext("tags",e),this},clearContext:function(){return this._globalContext={},this},getContext:function(){return JSON.parse(Nt(this._globalContext))},setEnvironment:function(e){return this._globalOptions.environment=e,this},setRelease:function(e){return this._globalOptions.release=e,this},setDataCallback:function(e){var t=this._globalOptions.dataCallback;return this._globalOptions.dataCallback=Mn(t,e),this},setBreadcrumbCallback:function(e){var t=this._globalOptions.breadcrumbCallback;return this._globalOptions.breadcrumbCallback=Mn(t,e),this},setShouldSendCallback:function(e){var t=this._globalOptions.shouldSendCallback;return this._globalOptions.shouldSendCallback=Mn(t,e),this},setTransport:function(e){return this._globalOptions.transport=e,this},lastException:function(){return this._lastCapturedException},lastEventId:function(){return this._lastEventId},isSetup:function(){return!!this._hasJSON&&(!!this._globalServer||(this.ravenNotConfiguredError||(this.ravenNotConfiguredError=!0,this._logDebug("error","Error: Raven has not been configured.")),!1))},afterLoad:function(){var e=yn.RavenConfig;e&&this.config(e.dsn,e.config).install()},showReportDialog:function(e){if(_n){if(!(e=en({eventId:this.lastEventId(),dsn:this._dsn,user:this._globalContext.user||{}},e)).eventId)throw new Ft("Missing eventId");if(!e.dsn)throw new Ft("Missing DSN");var t=encodeURIComponent,n=[];for(var r in e)if("user"===r){var i=e.user;i.name&&n.push("name="+t(i.name)),i.email&&n.push("email="+t(i.email))}else n.push(t(r)+"="+t(e[r]));var o=this._getGlobalServer(this._parseDSN(e.dsn)),a=_n.createElement("script");a.async=!0,a.src=o+"/api/embed/error-page/?"+n.join("&"),(_n.head||_n.body).appendChild(a)}},_ignoreNextOnError:function(){var e=this;this._ignoreOnError+=1,setTimeout((function(){e._ignoreOnError-=1}))},_triggerEvent:function(e,t){var n,r;if(this._hasDocument){for(r in t=t||{},e="raven"+e.substr(0,1).toUpperCase()+e.substr(1),_n.createEvent?(n=_n.createEvent("HTMLEvents")).initEvent(e,!0,!0):(n=_n.createEventObject()).eventType=e,t)rn(t,r)&&(n[r]=t[r]);if(_n.createEvent)_n.dispatchEvent(n);else try{_n.fireEvent("on"+n.eventType.toLowerCase(),n)}catch(e){}}},_breadcrumbEventHandler:function(e){var t=this;return function(n){if(t._keypressTimeout=null,t._lastCapturedEvent!==n){var r;t._lastCapturedEvent=n;try{r=cn(n.target)}catch(e){r="<unknown>"}t.captureBreadcrumb({category:"ui."+e,message:r})}}},_keypressEventHandler:function(){var e=this;return function(t){var n;try{n=t.target}catch(e){return}var r=n&&n.tagName;if(r&&("INPUT"===r||"TEXTAREA"===r||n.isContentEditable)){var i=e._keypressTimeout;i||e._breadcrumbEventHandler("input")(t),clearTimeout(i),e._keypressTimeout=setTimeout((function(){e._keypressTimeout=null}),1e3)}}},_captureUrlChange:function(e,t){var n=hn(this._location.href),r=hn(t),i=hn(e);this._lastHref=t,n.protocol===r.protocol&&n.host===r.host&&(t=r.relative),n.protocol===i.protocol&&n.host===i.host&&(e=i.relative),this.captureBreadcrumb({category:"navigation",data:{to:t,from:e}})},_patchFunctionToString:function(){var e=this;e._originalFunctionToString=Function.prototype.toString,Function.prototype.toString=function(){return"function"==typeof this&&this.__raven__?e._originalFunctionToString.apply(this.__orig__,arguments):e._originalFunctionToString.apply(this,arguments)}},_unpatchFunctionToString:function(){this._originalFunctionToString&&(Function.prototype.toString=this._originalFunctionToString)},_instrumentTryCatch:function(){var e=this,t=e._wrappedBuiltIns;function n(t){return function(n,r){for(var i=new Array(arguments.length),o=0;o<i.length;++o)i[o]=arguments[o];var a=i[0];return Qt(a)&&(i[0]=e.wrap({mechanism:{type:"instrument",data:{function:t.name||"<anonymous>"}}},a)),t.apply?t.apply(this,i):t(i[0],i[1])}}var r=this._globalOptions.autoBreadcrumbs;function i(n){var i=yn[n]&&yn[n].prototype;i&&i.hasOwnProperty&&i.hasOwnProperty("addEventListener")&&(dn(i,"addEventListener",(function(t){return function(i,o,a,s){try{o&&o.handleEvent&&(o.handleEvent=e.wrap({mechanism:{type:"instrument",data:{target:n,function:"handleEvent",handler:o&&o.name||"<anonymous>"}}},o.handleEvent))}catch(e){}var c,l,u;return r&&r.dom&&("EventTarget"===n||"Node"===n)&&(l=e._breadcrumbEventHandler("click"),u=e._keypressEventHandler(),c=function(e){if(e){var t;try{t=e.type}catch(e){return}return"click"===t?l(e):"keypress"===t?u(e):void 0}}),t.call(this,i,e.wrap({mechanism:{type:"instrument",data:{target:n,function:"addEventListener",handler:o&&o.name||"<anonymous>"}}},o,c),a,s)}}),t),dn(i,"removeEventListener",(function(e){return function(t,n,r,i){try{n=n&&(n.__raven_wrapper__?n.__raven_wrapper__:n)}catch(e){}return e.call(this,t,n,r,i)}}),t))}dn(yn,"setTimeout",n,t),dn(yn,"setInterval",n,t),yn.requestAnimationFrame&&dn(yn,"requestAnimationFrame",(function(t){return function(n){return t(e.wrap({mechanism:{type:"instrument",data:{function:"requestAnimationFrame",handler:t&&t.name||"<anonymous>"}}},n))}}),t);for(var o=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],a=0;a<o.length;a++)i(o[a])},_instrumentBreadcrumbs:function(){var e=this,t=this._globalOptions.autoBreadcrumbs,n=e._wrappedBuiltIns;function r(t,n){t in n&&Qt(n[t])&&dn(n,t,(function(n){return e.wrap({mechanism:{type:"instrument",data:{function:t,handler:n&&n.name||"<anonymous>"}}},n)}))}if(t.xhr&&"XMLHttpRequest"in yn){var i=yn.XMLHttpRequest&&yn.XMLHttpRequest.prototype;dn(i,"open",(function(t){return function(n,r){return Jt(r)&&-1===r.indexOf(e._globalKey)&&(this.__raven_xhr={method:n,url:r,status_code:null}),t.apply(this,arguments)}}),n),dn(i,"send",(function(t){return function(){var n=this;function i(){if(n.__raven_xhr&&4===n.readyState){try{n.__raven_xhr.status_code=n.status}catch(e){}e.captureBreadcrumb({type:"http",category:"xhr",data:n.__raven_xhr})}}for(var o=["onload","onerror","onprogress"],a=0;a<o.length;a++)r(o[a],n);return"onreadystatechange"in n&&Qt(n.onreadystatechange)?dn(n,"onreadystatechange",(function(t){return e.wrap({mechanism:{type:"instrument",data:{function:"onreadystatechange",handler:t&&t.name||"<anonymous>"}}},t,i)})):n.onreadystatechange=i,t.apply(this,arguments)}}),n)}t.xhr&&fn()&&dn(yn,"fetch",(function(t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;++r)n[r]=arguments[r];var i,o=n[0],a="GET";if("string"==typeof o?i=o:"Request"in yn&&o instanceof yn.Request?(i=o.url,o.method&&(a=o.method)):i=""+o,-1!==i.indexOf(e._globalKey))return t.apply(this,n);n[1]&&n[1].method&&(a=n[1].method);var s={method:a,url:i,status_code:null};return t.apply(this,n).then((function(t){return s.status_code=t.status,e.captureBreadcrumb({type:"http",category:"fetch",data:s}),t})).catch((function(t){throw e.captureBreadcrumb({type:"http",category:"fetch",data:s,level:"error"}),t}))}}),n),t.dom&&this._hasDocument&&(_n.addEventListener?(_n.addEventListener("click",e._breadcrumbEventHandler("click"),!1),_n.addEventListener("keypress",e._keypressEventHandler(),!1)):_n.attachEvent&&(_n.attachEvent("onclick",e._breadcrumbEventHandler("click")),_n.attachEvent("onkeypress",e._keypressEventHandler())));var o=yn.chrome,a=!(o&&o.app&&o.app.runtime)&&yn.history&&yn.history.pushState&&yn.history.replaceState;if(t.location&&a){var s=yn.onpopstate;yn.onpopstate=function(){var t=e._location.href;if(e._captureUrlChange(e._lastHref,t),s)return s.apply(this,arguments)};var c=function(t){return function(){var n=arguments.length>2?arguments[2]:void 0;return n&&e._captureUrlChange(e._lastHref,n+""),t.apply(this,arguments)}};dn(yn.history,"pushState",c,n),dn(yn.history,"replaceState",c,n)}if(t.console&&"console"in yn&&console.log){var l=function(t,n){e.captureBreadcrumb({message:t,level:n.level,category:"console"})};$t(["debug","info","warn","error","log"],(function(e,t){Sn(console,t,l)}))}},_restoreBuiltIns:function(){for(var e;this._wrappedBuiltIns.length;){var t=(e=this._wrappedBuiltIns.shift())[0],n=e[1],r=e[2];t[n]=r}},_restoreConsole:function(){for(var e in this._originalConsoleMethods)this._originalConsole[e]=this._originalConsoleMethods[e]},_drainPlugins:function(){var e=this;$t(this._plugins,(function(t,n){var r=n[0],i=n[1];r.apply(e,[e].concat(i))}))},_parseDSN:function(e){var t=bn.exec(e),n={},r=7;try{for(;r--;)n[vn[r]]=t[r]||""}catch(t){throw new Ft("Invalid DSN: "+e)}if(n.pass&&!this._globalOptions.allowSecretKey)throw new Ft("Do not specify your secret key in the DSN. See: http://bit.ly/raven-secret-key");return n},_getGlobalServer:function(e){var t="//"+e.host+(e.port?":"+e.port:"");return e.protocol&&(t=e.protocol+":"+t),t},_handleOnErrorStackInfo:function(e,t){(t=t||{}).mechanism=t.mechanism||{type:"onerror",handled:!1},this._ignoreOnError||this._handleStackInfo(e,t)},_handleStackInfo:function(e,t){var n=this._prepareFrames(e,t);this._triggerEvent("handle",{stackInfo:e,options:t}),this._processException(e.name,e.message,e.url,e.lineno,n,t)},_prepareFrames:function(e,t){var n=this,r=[];if(e.stack&&e.stack.length&&($t(e.stack,(function(t,i){var o=n._normalizeFrame(i,e.url);o&&r.push(o)})),t&&t.trimHeadFrames))for(var i=0;i<t.trimHeadFrames&&i<r.length;i++)r[i].in_app=!1;return r=r.slice(0,this._globalOptions.stackTraceLimit)},_normalizeFrame:function(e,t){var n={filename:e.url,lineno:e.line,colno:e.column,function:e.func||"?"};return e.url||(n.filename=t),n.in_app=!(this._globalOptions.includePaths.test&&!this._globalOptions.includePaths.test(n.filename)||/(Raven|TraceKit)\./.test(n.function)||/raven\.(min\.)?js$/.test(n.filename)),n},_processException:function(e,t,n,r,i,o){var a,s=(e?e+": ":"")+(t||"");if((!this._globalOptions.ignoreErrors.test||!this._globalOptions.ignoreErrors.test(t)&&!this._globalOptions.ignoreErrors.test(s))&&(i&&i.length?(n=i[0].filename||n,i.reverse(),a={frames:i}):n&&(a={frames:[{filename:n,lineno:r,in_app:!0}]}),(!this._globalOptions.ignoreUrls.test||!this._globalOptions.ignoreUrls.test(n))&&(!this._globalOptions.whitelistUrls.test||this._globalOptions.whitelistUrls.test(n)))){var c=en({exception:{values:[{type:e,value:t,stacktrace:a}]},transaction:n},o),l=c.exception.values[0];null==l.type&&""===l.value&&(l.value="Unrecoverable error caught"),!c.exception.mechanism&&c.mechanism&&(c.exception.mechanism=c.mechanism,delete c.mechanism),c.exception.mechanism=en({type:"generic",handled:!0},c.exception.mechanism||{}),this._send(c)}},_trimPacket:function(e){var t=this._globalOptions.maxMessageLength;if(e.message&&(e.message=tn(e.message,t)),e.exception){var n=e.exception.values[0];n.value=tn(n.value,t)}var r=e.request;return r&&(r.url&&(r.url=tn(r.url,this._globalOptions.maxUrlLength)),r.Referer&&(r.Referer=tn(r.Referer,this._globalOptions.maxUrlLength))),e.breadcrumbs&&e.breadcrumbs.values&&this._trimBreadcrumbs(e.breadcrumbs),e},_trimBreadcrumbs:function(e){for(var t,n,r,i=["to","from","url"],o=0;o<e.values.length;++o)if((n=e.values[o]).hasOwnProperty("data")&&Kt(n.data)&&!nn(n.data)){r=en({},n.data);for(var a=0;a<i.length;++a)t=i[a],r.hasOwnProperty(t)&&r[t]&&(r[t]=tn(r[t],this._globalOptions.maxUrlLength));e.values[o].data=r}},_getHttpData:function(){if(this._hasNavigator||this._hasDocument){var e={};return this._hasNavigator&&An.userAgent&&(e.headers={"User-Agent":An.userAgent}),yn.location&&yn.location.href&&(e.url=yn.location.href),this._hasDocument&&_n.referrer&&(e.headers||(e.headers={}),e.headers.Referer=_n.referrer),e}},_resetBackoff:function(){this._backoffDuration=0,this._backoffStart=null},_shouldBackoff:function(){return this._backoffDuration&&Pn()-this._backoffStart<this._backoffDuration},_isRepeatData:function(e){var t=this._lastData;return!(!t||e.message!==t.message||e.transaction!==t.transaction)&&(e.stacktrace||t.stacktrace?un(e.stacktrace,t.stacktrace):e.exception||t.exception?ln(e.exception,t.exception):!e.fingerprint&&!t.fingerprint||Boolean(e.fingerprint&&t.fingerprint)&&JSON.stringify(e.fingerprint)===JSON.stringify(t.fingerprint))},_setBackoffState:function(e){if(!this._shouldBackoff()){var t=e.status;if(400===t||401===t||429===t){var n;try{n=fn()?e.headers.get("Retry-After"):e.getResponseHeader("Retry-After"),n=1e3*parseInt(n,10)}catch(e){}this._backoffDuration=n||(2*this._backoffDuration||1e3),this._backoffStart=Pn()}}},_send:function(e){var t=this._globalOptions,n={project:this._globalProject,logger:t.logger,platform:"javascript"},r=this._getHttpData();r&&(n.request=r),e.trimHeadFrames&&delete e.trimHeadFrames,(e=en(n,e)).tags=en(en({},this._globalContext.tags),e.tags),e.extra=en(en({},this._globalContext.extra),e.extra),e.extra["session:duration"]=Pn()-this._startTime,this._breadcrumbs&&this._breadcrumbs.length>0&&(e.breadcrumbs={values:[].slice.call(this._breadcrumbs,0)}),this._globalContext.user&&(e.user=this._globalContext.user),t.environment&&(e.environment=t.environment),t.release&&(e.release=t.release),t.serverName&&(e.server_name=t.serverName),e=this._sanitizeData(e),Object.keys(e).forEach((function(t){(null==e[t]||""===e[t]||Zt(e[t]))&&delete e[t]})),Qt(t.dataCallback)&&(e=t.dataCallback(e)||e),e&&!Zt(e)&&(Qt(t.shouldSendCallback)&&!t.shouldSendCallback(e)||(this._shouldBackoff()?this._logDebug("warn","Raven dropped error due to backoff: ",e):"number"==typeof t.sampleRate?Math.random()<t.sampleRate&&this._sendProcessedPayload(e):this._sendProcessedPayload(e)))},_sanitizeData:function(e){return mn(e,this._globalOptions.sanitizeKeys)},_getUuid:function(){return sn()},_sendProcessedPayload:function(e,t){var n=this,r=this._globalOptions;if(this.isSetup())if(e=this._trimPacket(e),this._globalOptions.allowDuplicates||!this._isRepeatData(e)){this._lastEventId=e.event_id||(e.event_id=this._getUuid()),this._lastData=e,this._logDebug("debug","Raven about to send:",e);var i={sentry_version:"7",sentry_client:"raven-js/"+this.VERSION,sentry_key:this._globalKey};this._globalSecret&&(i.sentry_secret=this._globalSecret);var o=e.exception&&e.exception.values[0];this._globalOptions.autoBreadcrumbs&&this._globalOptions.autoBreadcrumbs.sentry&&this.captureBreadcrumb({category:"sentry",message:o?(o.type?o.type+": ":"")+o.value:e.message,event_id:e.event_id,level:e.level||"error"});var a=this._globalEndpoint;(r.transport||this._makeRequest).call(this,{url:a,auth:i,data:e,options:r,onSuccess:function(){n._resetBackoff(),n._triggerEvent("success",{data:e,src:a}),t&&t()},onError:function(r){n._logDebug("error","Raven transport failed to send: ",r),r.request&&n._setBackoffState(r.request),n._triggerEvent("failure",{data:e,src:a}),r=r||new Error("Raven send failed (no additional details provided)"),t&&t(r)}})}else this._logDebug("warn","Raven dropped repeat event: ",e)},_makeRequest:function(e){var t=e.url+"?"+an(e.auth),n=null,r={};if(e.options.headers&&(n=this._evaluateHash(e.options.headers)),e.options.fetchParameters&&(r=this._evaluateHash(e.options.fetchParameters)),fn()){r.body=Nt(e.data);var i=en({},this._fetchDefaults),o=en(i,r);return n&&(o.headers=n),yn.fetch(t,o).then((function(t){if(t.ok)e.onSuccess&&e.onSuccess();else{var n=new Error("Sentry error code: "+t.status);n.request=t,e.onError&&e.onError(n)}})).catch((function(){e.onError&&e.onError(new Error("Sentry error code: network unavailable"))}))}var a=yn.XMLHttpRequest&&new yn.XMLHttpRequest;a&&(("withCredentials"in a||"undefined"!=typeof XDomainRequest)&&("withCredentials"in a?a.onreadystatechange=function(){if(4===a.readyState)if(200===a.status)e.onSuccess&&e.onSuccess();else if(e.onError){var t=new Error("Sentry error code: "+a.status);t.request=a,e.onError(t)}}:(a=new XDomainRequest,t=t.replace(/^https?:/,""),e.onSuccess&&(a.onload=e.onSuccess),e.onError&&(a.onerror=function(){var t=new Error("Sentry error code: XDomainRequest");t.request=a,e.onError(t)})),a.open("POST",t),n&&$t(n,(function(e,t){a.setRequestHeader(e,t)})),a.send(Nt(e.data))))},_evaluateHash:function(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];t[n]="function"==typeof r?r():r}return t},_logDebug:function(e){this._originalConsoleMethods[e]&&(this.debug||this._globalOptions.debug)&&Function.prototype.apply.call(this._originalConsoleMethods[e],this._originalConsole,[].slice.call(arguments,1))},_mergeContext:function(e,t){zt(t)?delete this._globalContext[e]:this._globalContext[e]=en(this._globalContext[e]||{},t)}},kn.prototype.setUser=kn.prototype.setUserContext,kn.prototype.setReleaseContext=kn.prototype.setRelease;var En=kn,Hn="undefined"!=typeof window?window:void 0!==h?h:"undefined"!=typeof self?self:{},Cn=Hn.Raven,Gn=new En;Gn.noConflict=function(){return Hn.Raven=Cn,Gn},Gn.afterLoad(),Ke.exports=Gn,Ke.exports.Client=En;var wn=d(Ke.exports),In=function(){function e(t,n){return i(this,e),e.instance||(e.instance=this,this.config=t,this.ctx=n,this.initialized=!1),e.instance}return a(e,[{key:"setConfig",value:function(e){B.check.isObject(e)&&B.extend(this.config,e)}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"init",value:function(){var e=this.getConfig("serverUrl");B.check.isString(e)&&""!==B.trim(e)?(this.initCommon(),this.initialized=!0,w.log("hinaEpmSDK initialized successfully")):w.log("当前 serverUrl 为空或不正确，请配置正确的 serverUrl！")}},{key:"initCommon",value:function(){this.ravenInit()}},{key:"ravenInit",value:function(){var e=this,t=this.config,n=t.sourcemapVersion,r=t.serverUrl;n||(n="1.0.0"),wn.config(r).install(),wn.setTransport((function(t){var r,i,o,a,s=t.data.exception,c={H_js_error_type:(null==s||null===(r=s.values)||void 0===r||null===(i=r[0])||void 0===i?void 0:i.type)||"",H_js_error_summary:(null==s||null===(o=s.values)||void 0===o||null===(a=o[0])||void 0===a?void 0:a.value)||"",H_js_error_id:s?B.hash(JSON.stringify(s)):"",H_js_error_content:s?JSON.stringify(s):"",H_js_sourcemap_version:n};e.ctx.sendRequest("track","H_performance_js",c),t.onSuccess()}))}},{key:"vueInit",value:function(e,t){var n=this,r=e.config.errorHandler;e.config.errorHandler=function(i,o,a){n.captureVueError(i,o,a),"function"==typeof r&&r.call(e,i,o,a),t.showVueError&&console.error(i)}}},{key:"ReactInit",value:function(e,t){var n=this;e.errorHandler=function(e,r,i){n.captureReactError(e,r,i),t.showError&&console.error(e)}}},{key:"captureVueError",value:function(e,t,n){var r={componentName:this.formatComponentName(t),lifecycleHook:n};t&&(t.$options&&t.$options.propsData?r.propsData=t.$options.propsData:t.$props&&(r.propsData=t.$props)),setTimeout((function(){wn.captureException(e,{extra:r})})),w.log(e,n)}},{key:"captureReactError",value:function(e,t,n){formatComponentName(t),t&&t.props&&t.props,setTimeout((function(){wn.captureException(e,{extra:info})}))}},{key:"formatComponentName",value:function(e){if(!e)return"<Anonymous>";if(e.$root===e)return"<Root>";if(!e.$options)return"<Anonymous>";var t=e.$options,n=t.name||t._componentTag,r=t.__file;if(!n&&r){var i=r.match(/([^/\\]+)\.vue$/);i&&(n=i[1])}return(n?"<".concat(n.replace(/(?:^|[-_])(\w)/g,(function(e){return e.toUpperCase()})).replace(/[-_]/g,""),">"):"<Anonymous>")+(r?" at ".concat(r):"")}},{key:"captureError",value:function(){return wn.captureException.apply(wn,arguments)}}]),e}(),Bn=window.location,On=function(){function e(t,n){i(this,e),e.instance=this,this.config=t,this.ctx=n,this.initialized=!0,this.load(t)}return a(e,[{key:"load",value:function(e){this.initialized||(this.config=e,this.initialized=!0)}},{key:"autoTrack",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.ctx.track("H_pageview",B.extend(t(t({},e),{},{H_referrer:B.getReferrer(null,!0),H_url:Bn.href,H_url_path:Bn.pathname,H_url_hash:Bn.hash,H_title:document.title})),n)}},{key:"autoTrackSinglePage",value:function(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;e=this.autoTrackIsUsed?Bn.href:B.getReferrer(),this.ctx.track("H_pageview",B.extend(t(t({},n),{},{H_referrer:e,url:Bn.href,H_url_path:Bn.pathname,H_url_hash:Bn.hash,H_title:document.title})),r)}},{key:"listenSinglePage",value:function(){var e=this,t=this.ctx.getConfig("isSinglePage");t&&B.mitt.on("hasInitEpm",(function(){e.onUrlChange((function(n){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(n!==Bn.href){K.pageProp.H_referrer=n;var r=B.extend({H_url:Bn.href,H_referrer:n},t);e.autoTrack(r)}};if(B.check.isBoolean(t))r();else if(B.check.isFunction(t)){var i=t();B.check.isObject(i)?r(i):!0===i&&r()}}))}))}},{key:"onUrlChange",value:function(e){B.check.isFunction(e)&&(e(),B.mitt.on("urlChange",e))}}]),e}(),Dn={errorCapture:!0,performance:!0,presetProperties:{latest_utm:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,url:!0,title:!0},autoTrack:{},isSinglePage:!1},xn=function(){function e(){return i(this,e),e.instance||(e.instance=this,this.config={},this.initialized=!1,this._=B),e.instance}return a(e,[{key:"setConfig",value:function(e){B.check.isObject(e)&&B.extend(this.config,e)}},{key:"getConfig",value:function(e){return this.config[e]}},{key:"sendRequest",value:function(e,n,r){var i={properties:t(t({},B.info.epmProperties()),r),type:e,event:n,time:B.now(),_track_id:Number(String(B.getRandom()).slice(2,5)+String(B.getRandom()).slice(2,4)+String(B.now()).slice(-4))};if(W.getAnonymousId()&&(i.anonymous_id=W.getAnonymousId()),W.getSessionId())if(B.now()-W.getSessionIdUpdateTime()>18e5){var o=B.getRandom();i.properties.H_session_id=o,W.setSessionId(o)}else i.properties.H_session_id=W.getSessionId();else i.properties.H_session_id=B.getRandom(),W.setSessionId(i.properties.H_session_id);W.getAccountId()!==W.getAnonymousId()&&(i.account_id=W.getAccountId()),i.send_time=B.now(),w.log(i),B.check.isString(i)||(i=JSON.stringify(i));var a=B.base64Encode(i),s="crc="+B.hashCode(a),c="data="+B.encodeURIComponent(a)+"&ext="+B.encodeURIComponent(s),l={callback:this.getConfig("globalCallback"),data:c,serverUrl:this.getConfig("serverUrl"),endServerUrl:this.getConfig("serverUrl"),dataSendTimeout:this.getConfig("dataSendTimeout")};new N(l).run()}},{key:"track",value:function(e,t,n){var r=B.check.isFunction(n)?n:function(){};B.check.isString(e)&&(B.check.isObject(t)||B.check.isUndefined(t))?this.sendRequest("track",e,t,r):w.log("eventName must be a sting and properties must be an object")}},{key:"quick",value:function(e){for(var t={autoTrack:this.autoTrack.autoTrack,autoTrackSinglePage:this.autoTrack.autoTrackSinglePage},n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t[e].call(this.autoTrack,r)}},{key:"init",value:function(e){if(B.check.isEmptyObject(this.config)){if(this.setConfig(B.extend(Dn,e)),w.showLog=this.getConfig("showLog"),I.serverUrl=this.getConfig("serverUrl"),this.isSinglePage=this.getConfig("isSinglePage"),!I.checkServerUrl(this.getConfig("serverUrl")))return;if(W.load(this.config),q(this.config),this.store=W,this.config.performance)new We(this.config,this).init();if(this.config.errorCapture&&new In(this.config,this).init(),this.initialized=!0,!this.config.autoTrackConfig){B.initUrlChange();var t=new On(this.config,this);this.autoTrack=t,t.listenSinglePage(),B.mitt.emit("hasInitEpm")}}else w.log("EmpMonitor has been initialized")}}]),e}(),Ln=new Proxy(new xn,{get:function(e,t){return B.check.isFunction(e[t])?function(){if(e.initialized||"init"===t){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e[t].apply(e,r)}console.log("performanceErrorSdk not yet initialized!")}:e[t]}});window.hinaEpmStatistic=Ln;var Rn=["serverUrl"];return new Proxy({init:function(e){var n=e.name,r=e.serverUrl,i=e.showLog,o=e.autoTrackConfig,a=e.performanceErrorConfig,s=e.globalCallback,l=e.dataSendTimeout,u=e.isSinglePage;if(o&&se.init(e),a){var h=a.serverUrl,d=c(a,Rn);Ln.init(t({name:n,isSinglePage:u,serverUrl:h||r,showLog:i,autoTrackConfig:o,globalCallback:s,dataSendTimeout:l},d))}}},{get:function(e,t){return"init"!==t?se.initialized?se[t]:Ln.initialized&&Ln[t]?Ln[t]:(console.log("sdk not yet initialized!"),function(){}):e[t]}})}));
