//陪伴服务
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        _pageId = "#template_escortService ";
    var tools = require("../common/tools");
    var _pageCode = "template/escortService";
    var ut = require("../common/userUtil");
    let getData;
    let userInfo;
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var escortService_data;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //获取页面缓存信息
        escortService_data = appUtils.getSStorageInfo("escortService_data");
        // console.log(escortService_data,111)
        getList();    
    }
    function getList(){
        service.reqFun102120(escortService_data, async (data) => {
            if (data.error_no == '0') {
                var res = data.results;
                if (res.length > 0) { 
                    setData(res)
                }else{

                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function setData(list){
        let html = ``;
        list.map(item=>{
            console.log(JSON.stringify(item,111))
            html += `<ul class="flex" prod_id="${item.prod_id}" id="${item.id}" msg_id="${item.msg_id}" html_content="${encodeURIComponent(item.html_content)}" msg_title="${item.msg_title}" create_date="${item.create_date}">
                        <li>${item.msg_title}</li>
                        <li class="right_icon">
                        
                        </li>
                    </ul>`
        })
        $(_pageId + ".list").html(html);
    }
    function bindPageEvent() {
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " .list"), "ul", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var param = {
                prod_id: $(this).attr("prod_id"),
                id: $(this).attr("id"),
                msg_id: $(this).attr("msg_id"),
                html_content: $(this).attr("html_content"),
                msg_title: $(this).attr("msg_title"),
                create_date: $(this).attr("create_date")
            };
            console.log(param)
            tools.recordEventData('1','msg_list','资讯详情');
            appUtils.pageInit(_pageCode, "template/prodMsgDetail", param);

        }, 'click');
    }
    function destroy() {

    }
    function pageBack() {
        appUtils.pageBack();
    }

    var escortService = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = escortService;
});
