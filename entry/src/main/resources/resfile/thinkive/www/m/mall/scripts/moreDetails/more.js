// 更多
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#moreDetails_more ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var external = require("external");
    var platform = gconfig.platform;
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var userChooseVerson,scene_code,mobileWhole;
    function init() {
        //获取当前用户选中的版本
        userChooseVerson = common.getLocalStorage("userChooseVerson"); //判断用户是否登陆过
        //获取当前用户初始版本
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        mobileWhole = common.getLocalStorage("mobileWhole");
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        scene_code = scene_code ? scene_code : '';
        mobileWhole = mobileWhole ? mobileWhole : '';
        //获取用户当前切换的版本
        // chooseVerson = appUtils.getSStorageInfo("chooseVerson") ? appUtils.getSStorageInfo("chooseVerson") : '';
        // scene_code = '3'
        if(userChooseVerson && userChooseVerson !=''){
            if(userChooseVerson == '3'){
                $(_pageId).addClass("high");
                $(_pageId + " .other_more").hide();
                $(_pageId + " .high_more").show();
            }else{
                $(_pageId).removeClass("high");
                $(_pageId + " .high_more").hide();
                $(_pageId + " .other_more").show();
            }
        }else{
            if(scene_code == '3'){
                $(_pageId).addClass("high");
                $(_pageId + " .other_more").hide();
                $(_pageId + " .high_more").show();
            }else{
                $(_pageId).removeClass("high");
                $(_pageId + " .high_more").hide();
                $(_pageId + " .other_more").show();
            }
        }
        //页面埋点初始化
        tools.initPagePointData();
        //查询当前展示版本号
        var oVersion = external.callMessage({ funcNo: "50010" });
        oVersion = oVersion.results ? oVersion.results[0] : { versionSn: global.version_code };
        var version = oVersion.version;
        // 假如查询版本号  不为空 展示出来
        if (version) {
            $(_pageId + ' #versionNumber').html("当前版本号" + version);
        }
       
        //我的-是否有新消息 2017-8-24 jiaxr-add
        // if (appUtils.getLStorageInfo("hasNewMsg") == "false" || !appUtils.getLStorageInfo("hasNewMsg")) {
        //     $(_pageId + " .hasNewMsg").removeClass("on");
        // } else {
        //     $(_pageId + " .hasNewMsg").addClass("on");
        // }
        //获取协议
        tools.getPdf("8", "0", null, null, null);
        //渠道
        let userInfo = ut.getUserInf();
        // console.log(userInfo.custLabelCnlCode,1111)
        if(userInfo.custLabelCnlCode == "yh_jjdx" || !(userInfo.custLabelCnlCode) || userInfo.custLabelCnlCode == "jjdx"){
            $(_pageId + " .Learn_father").show();
        }else{
            $(_pageId + " .Learn_father").hide();
        }
        tools.footerShow(_pageId,userChooseVerson,scene_code,mobileWhole);
        // $(_pageId + " .footer_inner").show();
    }

    //绑定事件
    function bindPageEvent() {

        // 清除缓存
        appUtils.bindEvent($(_pageId + " #cache"), function () {
            layerUtils.iConfirm("系统立即帮您清除多余的缓存", function () {
                layerUtils.iLoading(true, "清除中...");
                var timercache = setInterval(function () {
                    layerUtils.iLoading(false);
                    layerUtils.iMsg(-1, "清除成功", 1);
                    // 暂时只清除保存电话号码
                    appUtils.clearLStorage("mobileWhole");
                    window.clearInterval(timercache);
                }, 1000);
            }, function () {
            }, "立即清除", "取消");
        });
        // 去我的介绍页面
        appUtils.bindEvent($(_pageId + " #guide"), function () {
            appUtils.pageInit("moreDetails/more", "guide/myGuidePage", {});
        });
        // 去关于我们页面
        appUtils.bindEvent($(_pageId + " #aboutUs"), function () {
            appUtils.pageInit("moreDetails/more", "aboutUs/index", {});
        });
        // 去 公告页面
        appUtils.bindEvent($(_pageId + " #clicknotice"), function () {
            appUtils.pageInit("moreDetails/more", "moreDetails/notice", {});
        });

        // 去 咨询页面
        appUtils.bindEvent($(_pageId + " #consultation"), function () {
            appUtils.pageInit("moreDetails/more", "moreDetails/consultation", {});
        });

        //协议查看
        appUtils.bindEvent($(_pageId + " #agreementcheck"), function () {
            appUtils.pageInit("moreDetails/more", "moreDetails/agreementcheck", {});
        });

        // 去帮助中心
        appUtils.bindEvent($(_pageId + " #help"), function () {
            //appUtils.pageInit("moreDetails/more","guide/advertisement",{"url":"https://weixin.sxfae.com/m/wx/#!/other/helpcenter.html","prePage_code":"moreDetails/more"});
            appUtils.pageInit("moreDetails/more", "moreDetails/helpCenter", {});
        });
        // 去投资页面
        appUtils.bindEvent($(_pageId + " #touzi"), function () {
            appUtils.pageInit("moreDetails/more", "investment/choiceInvestment", {});
        });
        // 判断是否登录 去首页
        appUtils.bindEvent($(_pageId + " #shouye"), function () {
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            if (ut.getUserInf().custLabelCnlCode == "yh") {
                appUtils.pageInit("moreDetails/more", "yuanhui/userIndexs", {});
                return;
            } else if (ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) || ut.getUserInf().custLabelCnlCode == "jjdx") {
                appUtils.pageInit("moreDetails/more", "login/userIndexs", {});
                return;
            } else {
                appUtils.pageInit("moreDetails/more", "hengjipy/userIndexs", {});
            }

        });
        // 去我的页面 判断是否登录 和绑卡
        appUtils.bindEvent($(_pageId + " #wode"), function () {
            tools.setPageToUrl('thfund/inputRechargePwd', '4')
            if (!common.loginInter()) return;
            var cardno = ut.getUserInf().bankAcct;
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            if (cardno == null || cardno == "") {
                appUtils.pageInit("moreDetails/more", "account/myAccountNoBind", {});
            } else if (ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) || ut.getUserInf().custLabelCnlCode == "jjdx") {
                appUtils.setSStorageInfo("toProfit", "index");
                appUtils.pageInit("moreDetails/more", "account/myAccount", {});
            } else {
                appUtils.pageInit("moreDetails/more", "yuanhui/myAccount", {});
            }
        });
        //跳转到学投资页面
        appUtils.bindEvent($(_pageId + " #Learn"), function () {
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit("moreDetails/more", "liveBroadcast/index", {});
        });
        // 点击版本更新
        appUtils.bindEvent($(_pageId + " #versionUpdate"), function () {

            var chancel = null;
            var soft_no = global.soft_no;
            var oVersion = external.callMessage({ funcNo: "50010" });
            oVersion = oVersion.results ? oVersion.results[0] : { versionSn: global.version_code };
            var version = oVersion.versionSn;
            var zd_version = oVersion.version;
            // if (platform == "1") {
            //     chancel = "1";//手机
            // } else if (platform == "0") {
            //     chancel = "0";//PC
            // } else if (platform == "2") {
            //     chancel = "2";//IOS
            // } else {

            // }
            chancel = platform;
            var queryParam = {
                "versionsn": version,
                "channel": chancel,
                "soft_no": soft_no
            };
            service.reqFun102070(queryParam, function (data) {
                if (data.error_no == 0) {
                    if (data.results.length != 0) {
                        var version_name = data.results[0].versionName;  //版本应用名称
                        var description = data.results[0].description;  //更新说明
                        var version_size = data.results[0].versionSize; //版本更新大小
                        var app_version = data.results[0].versionCode; //
                        var url = data.results[0].downloadUrl; //下载地址
                        var versionStoreUpdate = data.results[0].versionStoreUpdate;
                        var typeFlag = data.results[0].typeFlag;  //下载类型(0：原生，1：H5）
                        $(_pageId + ' #edition').html(version_name);
                        $(_pageId + ' #description').html(description);
                        $(_pageId + ' #Size').html(version_size);
                        if (data.results[0].updateFlag == "0") { //1 强制更新0普通更新
                            if (parseFloat(version) < parseFloat(app_version)) { // 客户端版本小于系统版本
                                $(_pageId + ' .pop_layer').show();
                                $(_pageId + ' .update_prompt').show();
                                $(_pageId + ' #close').show();
                                $(_pageId + ' #immediateUpdates').css('width', '50%');
                                // 点击更新事件
                                appUtils.bindEvent($(_pageId + " #immediateUpdates"), function () {
                                    //缓存用户选中版本
                                    common.setLocalStorage("sceneRefresh",'0');
                                    common.setLocalStorage("userChooseRefresh",'0');
                                    $(_pageId + ' .pop_layer').hide();
                                    $(_pageId + ' .update_prompt').hide();
                                    var invokeParam = {
                                        funcNo: "50201", // String	功能号	Y
                                        url: url, // 更新地址
                                        type: typeFlag, //下载类型(0：原生，1：H5）
                                        version: version_name,  //app版本
                                        isShowUpdateTip: "1",//非静默升级
                                        versionSn: app_version //app版本序列号
                                    };
                                    external.callMessage(invokeParam);
                                });
                            }
                        } else if (data.results[0].updateFlag == "1") {
                            if (parseFloat(version) < parseFloat(app_version)) { // 客户端版本小于系统版本
                                $(_pageId + ' .pop_layer').show();
                                $(_pageId + ' .update_prompt').show();
                                $(_pageId + ' #close').hide();
                                $(_pageId + ' #immediateUpdates').css('width', '100%');
                                // 点击更新事件
                                appUtils.bindEvent($(_pageId + " #immediateUpdates"), function () {
                                    //缓存用户选中版本
                                    common.setLocalStorage("sceneRefresh",'0');
                                    common.setLocalStorage("userChooseRefresh",'0');
                                    $(_pageId + ' .pop_layer').hide();
                                    $(_pageId + ' .update_prompt').hide();
                                    if (typeFlag == "0" && platform == "1") { //安卓原生升级
                                        if (versionStoreUpdate == 1) {
                                            invokeParam = {
                                                funcNo: "80318", // String	功能号	Y
                                                url: url, //下载地址）
                                            };
                                        } else {
                                            invokeParam = {
                                                funcNo: "80050", // String	功能号	Y
                                                downloadLink: url, //下载地址）
                                            };
                                        }
                                    } else {
                                        var invokeParam = {
                                            funcNo: "50201", // String	功能号	Y
                                            url: url, // 更新地址
                                            type: typeFlag, //下载类型(0：原生，1：H5）
                                            version: version_name,  //app版本
                                            isShowUpdateTip: "1",
                                            versionSn: app_version //app版本序列号
                                        };
                                    }
                                    external.callMessage(invokeParam);
                                });
                            }
                        }
                    } else {
                        layerUtils.iAlert("当前已是最新版本");
                        return;
                    }

                }
                if (data.error_no == -90191601) {
                    layerUtils.iAlert("当前已是最新版本");
                }
            }, false);

        });
        //关闭更新
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + ' .pop_layer').hide();
            $(_pageId + ' .update_prompt').hide();
        });
        //意见反馈
        appUtils.bindEvent($(_pageId + " #leaveword"), function () {
            appUtils.pageInit("moreDetails/more", "moreDetails/leaveword", {});
        });

        //联系我们
        appUtils.bindEvent($(_pageId + " #contactUs"), function () {
            appUtils.pageInit("moreDetails/more", "account/contactUs", {});
        });

        //协议其它查看
        // 个人信息收集清单
        appUtils.bindEvent($(_pageId + " .personal_data"), function (e) {
            tools.getOtherPdf("14", e);
        });
        // 第三方共享清单
        appUtils.bindEvent($(_pageId + " .third_party"), function (e) {
            tools.getOtherPdf("15", e);
        });
        // 产品隐私政策摘要
        appUtils.bindEvent($(_pageId + " .product_summary"), function (e) {
            tools.getOtherPdf("16", e);
        });

        appUtils.bindEvent($(_pageId + " #xsd"), function () {
            // window.location.href = "http://101.132.158.40:8281/m/mall/index.html#!/outside/middlePage.html";
            window.location.href  = "https://securities.sxfae.com/m/mall/index.html#!/login/unLoginIndexPage.html"
        });

    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #Learn_father").hide();
        $(_pageId + " .other_more").hide();
        $(_pageId + " .high_more").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var more = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = more;
});
