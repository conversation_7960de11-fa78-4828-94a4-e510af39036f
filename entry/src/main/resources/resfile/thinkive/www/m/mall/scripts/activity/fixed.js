//活动 - 年度账单
define(function (require, exports, module) {
    let appUtils = require("appUtils");
    let service = require("mobileService")
    let tools = require("../common/tools");
    var layerUtils = require("layerUtils");
    let _pageId = "#activity_fixed ";
    let _page_code = "activity/fixed";
    var ut = require("../common/userUtil");
    let common = require("common"); 
    let validatorUtil = require("validatorUtil");
    var external = require("external");
    var productInfo;
    var fund_id;
    var join_flag;
    var MainState;
    var moneyObj = [
        {
            title:'华为手机',
            src: "../mall/images/activity/phone.png",
        },
        {
            title:'小米台灯',
            src: "../mall/images/activity/deskLamp.png",
        },
        {
            title:'小米加湿器',
            src: "../mall/images/activity/humidifier.png",
        },
        {
            title:'200+积分',
            src: "../mall/images/activity/integral.png",
        }
    ];
    var activity_id = [];
    function init() {
        setImg()
        //初始化
        setData()
        // choujiang()
        // lingqu()
        // liushhui()
        //获取产品详情
        
    }
    //点返回
    appUtils.bindEvent($(_pageId + " .icon_back"), function () {
        pageBack();
    });
    function getInfo(){
        let data = {
            fund_code: fund_id
        }
        service.reqFun102113(data, (datas) => {
            if (datas.error_no == '0') {
                let res = datas.results[0] 
                productInfo = res;
                let dk_income_rate = toolsMoney(res.dk_income_rate)
                appUtils.setSStorageInfo("productInfo", productInfo);
                $(_pageId + " .infoTitle").html(productInfo.prod_name_list)
                appUtils.setSStorageInfo("financial_prod_type",'02');
                appUtils.setSStorageInfo("fund_code", res.fund_code);
                // $(_pageId + ' .fofActivity_main_pro_main_list_firstPro_name').text(res.prod_name_list)
                $(_pageId + ' .fofActivity_main_pro_main_list_firstPro_remark').text(res.income_period_type_desc + '年化')
                $(_pageId + ' .term').text(res.term)
                $(_pageId + ' .threshold_amount').text(res.threshold_amount + '元')
                // if(dk_income_rate == '--') return $(_pageId + ' .dk_income_rate').text('--')
                $(_pageId + ' .dk_income_rate').text(dk_income_rate + '%')
                $(_pageId + ' .secound_prod .prod_name_list span').text(res.prod_name_list)
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    };
    function toolsMoney(time,num){
        if(!time || time == '--') return '--'
        return tools.fmoney(time,num)
    };
    //渲染图片
    function setImg(){
        let list = moneyObj
        let html = ''
        for(var i=0;i< list.length;i++){
            html += `<li class="main_flxe vertical_line">
                        <img src="${list[i].src}" alt="">
                        <span>${list[i].title}</span>
                    </li>`
        }
        $(_pageId + " .prize").html(html)
    };
    //初始化
    function setData(){
        let requestData = {
            activity_type:"19"
        }
        service.reqFun108037(requestData, async(data) =>{
            if(data.error_no == '0'){
                var res =  data.results[0]
                // console.log(res)
                //渲染描述
                $(_pageId + " .secound_remark").html(res.activity_text)
                $(_pageId + " .remark .right").html(res.activity_words)
                //渲染活动规则
                // introduce
                $(_pageId + " .b_con").html(res.introduce)
                // console.log(res,222)
                //渲染活动列表
                let list  = res.prepose_list
                fund_id = res.hold_prod_id
                join_flag = res.join_flag
                MainState = res.state
                btnClass = '';
                // 获取产品
                getInfo()
                let html = ''
                if(list == "" ||!list || !list.length){
                    $(_pageId + " .activity_list").html(html)
                    return $(_pageId + " #rewardRecord .top").html('<p class="m_center">暂无数据</p>');
                }
                for(var i=0;i< list.length;i++){
                    let btnName = ''
                    // list[i].status = '2'
                    activity_id.push(list[i].activity_id)
                    if(list[i].activity_type == '21'){
                        btnName = list[i].status == '1' ? list[i].btn_name :  list[i].status == '2' ? '去领取' : '已完成'
                    }else{
                        btnName = list[i].status == '1' ? list[i].btn_name :  list[i].status == '2' ? '去抽奖' : '已完成'
                    }
                    // 1.未完成 2领奖励3已完成
                    btnClass = list[i].status == "1" ? 'initial' : list[i].status == '2' ? '' :'m_bg_aaa';

                    // html += `<ul class="flex third">
                    //             <li class="left m_font_size14 main_flxe vertical_center">${list[i].activity_tips}</li>
                    //             <li class="right main_flxe flex_1 flex_center">
                    //                 <span activity_id="${list[i].activity_id}" join_flag="${list[i].join_flag}" activity_type="${list[i].activity_type}" state="${list[i].state}" status="${list[i].status}" class="${list[i].status == "3" ?'m_bg_aaa' : ''}">${btnName}</span>
                    //             </li>
                    //         </ul>`
                    html += `<ul class="flex third">                              
                                <li class="right main_flxe flex_1 flex_center">
                                    <span activity_id="${list[i].activity_id}" join_flag="${list[i].join_flag}" activity_type="${list[i].activity_type}" state="${list[i].state}" status="${list[i].status}" class="${btnClass}">${btnName}</span>
                                </li>
                            </ul>`
                }
                $(_pageId + " .activity_list").html(html)
                getList()
            }else{
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //设置定投相关
    function setInvestment(data){
        if(data.status == 1){
            //跳转定投页面
            var _pageCode = _pageId.replace("_", "/").substr(1).trim();
            if (!common.loginInter(_pageCode)) return;
            if (!ut.hasBindCard(_pageCode)) return;
            let info = ut.getUserInf()
            // if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
            // if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            // common.changeCardInter(function () {
            //     if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
            //         layerUtils.iConfirm("您还未进行风险测评", function () {
            //             appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
            //         }, function () {
            //         }, "去测评", "取消");
            //         return;
            //     }
            //     appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
            // });
            appUtils.pageInit(_pageCode, "login/userIndexs", {});
        }else if(data.status == 2){
            //去抽奖页面
            let saveData = {
                activity_id: data.activity_id,
                cust_no: ut.getUserInf().custNo,
                channel: "jjcf_app",
                type: "luckDraw",
                mobile: ut.getUserInf().mobileWhole,
            }
            common.setLocalStorage("investment_activityInfo", saveData)
            appUtils.setSStorageInfo("investment_activityInfo",saveData);
            // common.setLocalStorage("activity_id", data.activity_id)
            // appUtils.setSStorageInfo("activity_id",data.activity_id);
            // var data = external.callMessage({
            //     "funcNo": "50043",
            //     "moduleName": "mall",
            //     "key": "activityInfo",
            // })
            // console.log(data,3333)
            appUtils.pageInit("login/userIndexs", "activity/investmentActivity", {
                "activity_id":  data.activity_id,
                "name": '定投活动',
                "description": '定投活动',
            });
        }
    }
    //首次购买相关
    function buyPro(data){
        
        if(data.status == 1){
            tools.jumpMarketingPage(_page_code,200)
            //跳转购买页面
            // var _pageCode = _pageId.replace("_", "/").substr(1).trim();
            // var buy_state = productInfo.buy_state; //产品状态
            // if ($(this).hasClass("no_active")) {
            //     if (buy_state == "6") {
            //         layerUtils.iAlert("当前时间不支持购买");
            //     }
            //     return;
            // }
            // if (!common.loginInter(_pageCode)) return;
            // if (!ut.hasBindCard(_pageCode)) return;
            // let info = ut.getUserInf()
            // if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
            // if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            // common.changeCardInter(function () {
            //     if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
            //         layerUtils.iConfirm("您还未进行风险测评", function () {
            //             appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
            //         }, function () {
            //         }, "去测评", "取消");
            //         return;
            //     }
            //     tools.jumpBuyPage(_pageCode, productInfo.prod_sub_type, productInfo.prod_sub_type2);
            // });
        }else if(data.status == 2){
            //点击领取积分
            getIntegral(data)
        }
    }
    //去抽奖
    function choujiang(){
        let requestData = {
            activity_id:'131',
            cust_no:'0002880'
        }
        service.reqFun108017(requestData, async(data) =>{
            if(data.error_no == '0'){
                var res = data.results
                // console.log(data)
            }else{
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //领积分
    function getIntegral(data){
        let requestData = {
            activity_id:data.activity_id,
        }
        service.reqFun108038(requestData, async(data) =>{
            if(data.error_no == '0'){
                var res = data.results[0]
                // console.log(data)
                let points = res.points
                $(_pageId+ ' .integral').html(points)
                $(_pageId + " #rewardResult").show();
            }else{
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //领奖记录
    function getList(){
        let requestData = {
            activity_id:activity_id.toString(),
        }
        service.reqFun108018(requestData, async(data) =>{
            if(data.error_no == '0'){
                var res = data.results
                if(!res[0] || !res[0].data){
                    return $(_pageId + " #rewardRecord .top").html('<p class="m_center">暂无数据</p>')
                }
                let list = res[0].data
                // console.log(list)
                let html = ''
                if(!list.length) return
                for(var i=0;i< list.length;i++){
                    html += `<p class="">
                                <span>${i+1 + '.' +list[i].crt_time.substr(0, 4) + '年' + list[i].crt_time.substr(4, 2) + '月' + list[i].crt_time.substr(6, 2)+ '日'}</span>
                                <span style="margin-left:0.2rem">${list[i].reward_name}</span>
                            </p>`
                }
                $(_pageId + " #rewardRecord .top").html(html)
            }else{
                $(_pageId + " #rewardRecord .top").html('<span class="m_center">暂无数据</span>')
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //绑定事件
    function bindPageEvent() {
        //点击活动按钮
        appUtils.preBindEvent($(_pageId + " .activity_list"), ".third .right span", function () {
            let data = {
                activity_type:$(this).attr('activity_type'),
                state:$(this).attr('state'),
                status:$(this).attr('status'),
                activity_id:$(this).attr('activity_id'),
                join_flag:$(this).attr('join_flag'),
                el:$(this)
            }
            if (!ut.hasBindCard(_page_code)) return;
            if(MainState == 2){
                return layerUtils.iAlert('活动还没开始呢！'); 
            }
            
            if(MainState == 3){
                return layerUtils.iAlert('活动已结束！'); 
            }
            //活动还没开始呢
            if(data.state == 2){
                return layerUtils.iAlert('活动还没开始呢！'); 
            }
            //活动已结束
            if(data.state == 3){
                return layerUtils.iAlert('活动已结束！'); 
            }
            //是否白名单
            if(join_flag == 0) { //|| data.join_flag == 0
                return layerUtils.iAlert("当前参与人数较多，请稍后再试。");
            }
            //活动已完成
            if(data.status == 3){
                return layerUtils.iAlert('您已完成活动');
            }
            if(data.activity_type == "20"){
                //设置定投活动
                setInvestment(data)
            }else if(data.activity_type == "21"){
                //定投购买活动
                buyPro(data)
            }
        },'click');
        //查看领取记录
        //record
        appUtils.bindEvent($(_pageId + " .oneRight"), function () {
            $(_pageId + " #activityRules").show();
        });
        appUtils.bindEvent($(_pageId + " .ruleSure"), function () {
            $(_pageId + " .activity_pop_layer").hide();
            $(_pageId + " .activityRules").hide();
        });
        appUtils.bindEvent($(_pageId + " .record"), function () {
            $(_pageId + " #rewardRecord").show();
        });
        appUtils.bindEvent($(_pageId + " .recordSure"), function () {
            $(_pageId + " .rewardRecord").hide();
            $(_pageId + " .activity_pop_layer").hide();
        });
        appUtils.bindEvent($(_pageId + " .activityRulesBonce_btn"), function () {
            $(_pageId + " .rewardResult").hide();
            $(_pageId + " .activity_pop_layer").hide();
            setData()
        });
        
    }
    //选择年度
    function destroy() {
        $(_pageId + " .activity_pop_layer").hide();
        $(_pageId + " .activityRules").hide();
        $(_pageId + " .rewardRecord").hide();
        activity_id = [];
    }
    function pageBack() {
        appUtils.pageBack();
    }
    var caitongimage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = caitongimage;
});
