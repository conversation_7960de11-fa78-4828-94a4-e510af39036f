// 交易记录详情页  - 赎回
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#combProduct_combTrsDetailsSell ";
    _pageCode = "combProduct/combTrsDetailsSell"
    var tools = require("../common/tools");
    var ut = require("../common/userUtil");
    var monkeywords = require("../common/moneykeywords");
    var jymm, trans_serno, prod_sub_type2, nextPageData, detailsInfo;
    var accountObj = {
        "12423": "银行卡",
        "12419": "晋金宝",
        "12425": "晋金宝"
    }
    function init() {
        var param = appUtils.getPageParam()?appUtils.getPageParam():appUtils.getSStorageInfo("nextPageData");
        nextPageData = param;
        //页面埋点初始化
        tools.initPagePointData();
        prod_sub_type2 = param.prod_sub_type2;
        $(_pageId + " .statusName").text('赎回')
        $(_pageId + " .trans_name").text('赎回')
        $(_pageId + " #fh_detail").hide();
        $(_pageId + " .rule_box").addClass("block");
        $(_pageId + " .sub_name_type").html(param.sub_busi_code)
        //申购交易详情查询
        detail(param)
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #cancelorderBtn"), function () {
            layerUtils.iConfirm("<span style='color:#508cee'>" + tools.FormatDateText(detailsInfo.its_date.substring(4, 8)) + "14:00</span>前可以撤单", function () {
                // if (prod_sub_type2 == '200') {
                    

                // } else {
                //     let param = {};
                //     param["funcNo"] = "50220";
                //     param["telNo"] = require("gconfig").global.custServiceTel;
                //     param["callType"] = "0";
                //     require("external").callMessage(param);
                // }
                let money = $(_pageId + " .money").text();
                let trans_pay = $(_pageId + " #trans_pay").text();
                let str;
                // str = `申请撤单，资金<em>${money}</em>将退回至<em>${trans_pay}</em>`
                str = `赎回申请将撤销`
                $(_pageId + " #rechargeInfo").html(str);
                $(_pageId + " .pop_layer").show();
                //呼出交易密码
                passboardEvent();
                monkeywords.flag = 0;
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "combProduct_combTrsDetailsSell";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
            }, function () {
            }, "继续撤单", "取消");
        });
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .password_input"), function () {
            passboardEvent();
            monkeywords.flag = 0;
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "combProduct_combTrsDetailsSell";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").hide()
            // $(_pageId + " .password_box").hide()
            $(_pageId + " #jymm").val("");
            guanbi();
        });
        //调用撤单
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide()
            // $(_pageId + " .password_box").hide()
            var trans_pwd = $(_pageId + " #jymm").val()
            guanbi();
            if (trans_pwd.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            //验证交易密码
            service.getRSAKey({}, function (data) {
                if (data.error_no == "0") {
                    var modulus = data.results[0].modulus;
                    var publicExponent = data.results[0].publicExponent;
                    var endecryptUtils = require("endecryptUtils");
                    trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, trans_pwd);
                    var param = {
                        "trans_pwd": trans_pwd
                    }
                    var callBack = function (resultsVo) {
                        if (resultsVo.error_no == 0) {
                            //交易密码验证成功
                            let paramData = {
                                trans_serno:trans_serno,
                                fund_code:detailsInfo.comb_code,
                                cust_no:ut.getUserInf().custNo
                            }
                            service.reqFun106070(paramData, (datas) => {
                                if (datas.error_no == 0) {
                                    let results = datas.results;
                                    nextPageData.sell = true;
                                    appUtils.pageInit(_pageCode, "combProduct/cancelOrderResult", nextPageData);
                                } else {
                                    layerUtils.iAlert(datas.error_info);
                                }
                            })
                        } else {
                            layerUtils.iAlert(resultsVo.error_info);
                        }
                    };
                    appUtils.setSStorageInfo("nextPageData", detailsInfo);
                    service.reqFun101025(param, callBack)
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                }
            }, { isLastReq: false });
        });
        
        //跳转详情
        appUtils.bindEvent($(_pageId + " #chakan"), function () {     
            appUtils.setSStorageInfo("trans_serno", trans_serno);
            appUtils.setSStorageInfo("nextPageData", detailsInfo);
            let pay_method = $(_pageId + " #trans_pay").text() ? $(_pageId + " #trans_pay").text() : '';
            appUtils.pageInit(_pageCode, "combProduct/combTrsDetails",{pageType:'sell',pay_method:pay_method});
        });
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            // 键盘完成按钮的事件
            keyBoardFinishFunction: function () {
            },
            // 键盘的输入事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }
                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            }
        };
    }

    function detail(param) {
        var callback = function (data) {
            
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            // results = tools.FormatNull(results);
            detailsInfo = results;
            //卖出比例
            if (results.sub_busi_code == '14301') {
                var trans_amt = tools.fmoney(results.ack_vol) + "份";
            } else {
                var trans_amt = common.floatMultiply(results.trans_amt, 100);
                trans_amt = trans_amt + "%";
            }

            //交易状态
            var trans_status = results.trans_status;
            // if (param.sub_busi_code_e != "12218") {
            //     tools.isShowCancelorderBtn(_pageId, trans_status, "pri_trans_status_name", results.its_date, results.its_flag, prod_sub_type2, '1');
            // }
            let cancelorder = results.cancelorder;
            if(cancelorder == '1'){
                $(_pageId + " #overtimeBtn").hide();
                $(_pageId + " #cancelorderBtn").show();
            }else if(cancelorder == '2'){
                $(_pageId + " #cancelorderBtn").hide();
                $(_pageId + " #overtimeBtn").show();
            }else{
                $(_pageId + " #cancelorderBtn").hide();
                $(_pageId + " #overtimeBtn").hide();
            }
            var trans_status_name = tools.fundDataDict(trans_status, "pri_redeem_status_name");
            if (trans_status == "1" || trans_status== "4") {
                $(_pageId + " #chakan").hide();
            } else {
                $(_pageId + " #chakan").show();
            }
            var item = $(_pageId + " .rule_box .item")[1];
            if (trans_status != "8") {
                $(item).find(".name").addClass("undone");
            } else {
                $(item).find(".name").removeClass("undone");
            }
            //产品名称
            var prod_name = results.comb_name;
            //交易流水号
            trans_serno = results.trans_serno;
            //确认金额
            var ack_amt = tools.fmoney(results.ack_amt);
            ack_amt = ack_amt + "元";

            //撤单备注
            if (results.remark && results.move_date) {
                $(_pageId + " #remark").html(tools.ftime(results.move_time) + " " + results.remark);
                $(_pageId + " .remark_box").show();
            } else {
                $(_pageId + " .remark_box").hide();
            }
            //赎回超最小保留金额展示特殊备注
            if (results.remark) {
                $(_pageId + " #remark1").html(results.remark);
                $(_pageId + " .remark_box1").show();
            } else {
                $(_pageId + " .remark_box1").hide();
            }
            //手续费
            var feet_amt = tools.fmoney(results.feet_amt);
            feet_amt = feet_amt + "元";
            //交易时间
            var trans_time = results.trans_time;
            trans_time = tools.ftime(trans_time);
            //转出到
            let paymethod = results.pay_method;
            if (paymethod == 'null' || !paymethod || paymethod == '--') paymethod = '晋金宝'
            $(_pageId + " #trans_pay").text(paymethod);
            $(_pageId + " .trans_amt").html(trans_amt);
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #trans_date").html(results.trans_date);
            $(_pageId + " #trans_time").html(results.trans_time);
            $(_pageId + " #ack_date").html(results.ack_date);
            $(_pageId + " #pay_date").html(results.pay_date);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " #ack_amt").html(ack_amt);
            $(_pageId + " #feet_amt").html(feet_amt);
            $(_pageId + " #trans_time").html(trans_time);
            $(_pageId + " #fh_comb_name").html(results.remark);
        }
        service.reqFun102163({ trans_serno: param.trans_serno }, callback);
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .trans_amt").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #trans_date").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_amt").html("--");
        $(_pageId + " #feet_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " .remark_box").hide();
        $(_pageId + " #cancelorderBtn").hide();
        $(_pageId + " #overtimeBtn").hide();
        $(_pageId + " .remark_box1").hide();
        $(_pageId + " #fh_comb_name").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " #chakan").hide();
    }

    var combTrsDetailsSellModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = combTrsDetailsSellModule;
});
