// 交易记录详情   強赎
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            _pageId = "#highEnd_trsDetailsFore ";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    // 备注
    var remark_bj = {
        "15002": "产品清盘",
        "15003": "产品清盘",
        "14201": "管理人赎回",
        "14202": "管理人赎回"
    }
    var accountObj = {
        "15002": "银行卡",
        "15003": "晋金宝",
        "14201": "晋金宝",
        "14202": "银行卡"
    }

    function init() {
        var param = appUtils.getPageParam();
        if (param.prod_sub_type2 == "90") { //
            $(_pageId + " .normal").removeClass("hidden").addClass("block");
            $(_pageId + " .small").addClass("hidden");
            //私募赎回详情
            service.reqFun102088(param, function (data) {
                render(data, "90");
            });
        } else {
            $(_pageId + " .small").removeClass("hidden").addClass("block");
            $(_pageId + " .normal").addClass("hidden");
            //小集合強赎赎回详情
            service.reqFun102091(param, function (data) {
                render(data, param.prod_sub_type2);
            });
        }
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function render(data, prod_sub_type2) {
        if (data.error_no != "0") {
            layerUtils.iAlert(data.error_info);
            return;
        }
        var results = data.results[0];
        if (!results || results.length == 0) {
            return;
        }
        //数据处理 空 和 --
        results = tools.FormatNull(results);
        //小集合赎回交易记录不展示手续费
        if(results.prod_sub_type == '80'){
            $(_pageId + " .handlingFees").removeClass("block").addClass("hidden");
        }else{
            $(_pageId + " .handlingFees").removeClass("hidden").addClass("block");
        }
        //确认日期
        var ack_date = results.ack_date;
        if (ack_date != "--" && ack_date) {
            ack_date = tools.FormatDateText(ack_date.substring(4, 8));
        }
        // 赎回日期
        var redeem_date = results.redeem_date;
        if (redeem_date != "--" && redeem_date) {
            redeem_date = tools.FormatDateText(redeem_date.substring(4, 8));
        }
        //赎回到账日期
        var to_account_date = results.to_account_date;
        if (to_account_date != "--" && to_account_date) {
            to_account_date = tools.FormatDateText(to_account_date.substring(4, 8));
        }
        //到账天数
        var to_account_num = results.to_account_num;
        var end_text = "";
        var trans_vol = "";
        var start_text = "";
        //备注
        if (prod_sub_type2 == "90") { // 私募
            end_text = to_account_num + "个工作日回款到晋金宝";
            // 私募赎回份额
            trans_vol = tools.fmoney(results.redeem_amt);
            trans_vol = trans_vol + "元";
            start_text = '<sapn class="date">' + redeem_date + '</sapn>赎回';
            $(_pageId + " #remark").html("到期自动赎回");
        } else { //小集合
            //小集合赎回份额
            trans_vol = tools.fmoney(results.trans_vol);
            trans_vol = trans_vol + "份";
            end_text = "<span class='date'>" + tools.FormatDateText(results.to_account_date.substring(4, 8)) + "</span>资金到账，回款到" + accountObj[results.sub_busi_code];
            start_text = '<sapn class="date">' + ack_date + '</sapn>确认';
            $(_pageId + " #remark").html(remark_bj[results.sub_busi_code]);
        }

        //交易时间
        var trans_time = results.trans_time;
        trans_time = tools.ftime(trans_time);

        //交易状态
        var trans_status = results.trans_status;
        var trans_status_name = tools.fundDataDict(trans_status, "pri_bonus_trans_status_name");

        //产品名称
        var prod_name = results.prod_name;
        //产品代码
        var prod_code = "(" + results.prod_code + ")";
        //交易流水号
        var trans_serno = results.trans_serno;
        //确认份额
        var ack_vol = tools.fmoney(results.ack_vol);
        ack_vol = ack_vol + "份";
        //确认金额
        var ack_amt = tools.fmoney(results.ack_amt);
        ack_amt = ack_amt + "元";
        //确认净值
        var ack_nav = tools.fmoney(results.ack_nav, 4);
        ack_nav = ack_nav + "元";
        //手续费
        var feet_amt = tools.fmoney(results.feet_amt);
        feet_amt = feet_amt + "元";

        $(_pageId + " #redeem_date").html(redeem_date);

        $(_pageId + " .trans_vol").html(trans_vol);
        $(_pageId + " #trans_status").html(trans_status_name);
        $(_pageId + " #prod_name").html(prod_name);
        $(_pageId + " #prod_code").html(prod_code);
        // $(_pageId + " .trans_date").html(trans_date);
        $(_pageId + " #to_account_date").html(to_account_date);
        $(_pageId + " .sell_info").html(accountObj[results.sub_busi_code]);
        $(_pageId + " #trans_serno").html(trans_serno);
        $(_pageId + " #ack_vol").html(ack_vol);
        $(_pageId + " #ack_amt").html(ack_amt);
        $(_pageId + " #ack_nav").html(ack_nav);
        $(_pageId + " #feet_amt").html(feet_amt);
        $(_pageId + " #trans_time").html(trans_time);
        $(_pageId + " .end_text").html(end_text)
        $(_pageId + " .start_text").html(start_text)


    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        $(_pageId + " .trans_vol").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " #to_account_date").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " #ack_amt").html("--");
        $(_pageId + " #ack_nav").html("--");
        $(_pageId + " #feet_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " .end_text").html("");
        $(_pageId + " .start_text").html("");
        $(_pageId + " .small").removeClass("block").addClass("hidden");
        $(_pageId + " .normal").removeClass("block").addClass("hidden");
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
