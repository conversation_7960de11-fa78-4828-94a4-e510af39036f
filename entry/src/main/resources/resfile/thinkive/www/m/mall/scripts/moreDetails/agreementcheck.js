//协议查看
//create by wangjf
//2016-5-27
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        gconfig = require("gconfig"),
        _pageId = "#moreDetails_agreementcheck ";

    function init() {

    }

    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });

        //注册协议
        appUtils.bindEvent(_pageId + " #register_agreement", function () {
            appUtils.pageInit("moreDetails/agreementcheck", "agreementCheck/registerAgreement");
        });

        //绑卡协议
        appUtils.bindEvent(_pageId + " #bindcard_agreement", function () {
            appUtils.pageInit("moreDetails/agreementcheck", "agreementCheck/bindcardAgreement");
        });

        //晋金宝协议
        appUtils.bindEvent(_pageId + " #jjb_agreement", function () {
            appUtils.pageInit("moreDetails/agreementcheck", "agreementCheck/jjsAgreement");
        });

    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var agreementcheck = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = agreementcheck;
});
