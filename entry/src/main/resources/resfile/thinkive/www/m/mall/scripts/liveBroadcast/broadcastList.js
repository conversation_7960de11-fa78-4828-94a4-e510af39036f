//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#liveBroadcast_broadcastList ";
    var _pageCode = "liveBroadcast/broadcastList";
    var colorList = ['rgb(247,239,229)', 'rgb(241,233,239)', 'rgb(229,228,244)', 'rgb(223,235,243)', 'rgb(245,235,229)', 'rgb(244,238,242)'];
    var timer = null;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        //渲染投顾列表
        getList();
        //渲染轮播图
        setBanner()
        //进页面首页清除定时器
        if(timer){
            clearInterval(timer);
            timer = null;
        }
    }
    function setBanner(){
        service.reqFun112006({},function (data) {
            if (data.error_no == 0) {
                if(!data.results[0]) return $(_pageId + " .banner_box").hide();
                var results = data.results[0];
                tools.liveGuanggao({_pageId:_pageId,list:results.live_top_list})
            }
        })
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击直接进入更多直播间
        appUtils.preBindEvent($(_pageId + " .list"), ".card .more", function (e) {
            // console.log(e)
            e.stopPropagation();
            e.preventDefault();
            let room_type = $(this).attr("room_type");  //直播间类型
            sessionStorage.room_type = room_type;   //利用sessionStorage缓存类型
            sessionStorage.msg_title = $(this).attr("name");
            //进入更多直播列表页面
            appUtils.pageInit(_pageCode, "liveBroadcast/semihList");
        }, 'click');
        //点击直接进入直播间
        appUtils.preBindEvent($(_pageId + " .list"), ".card .listCard .listCard_item", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let uid = ut.getUserInf().custNo;
            let name = ut.getUserInf()?ut.getUserInf().name:'';
            /** 姓名格式化处理 */
            let trueName = tools.format(name);
            let h5Url = $(this).attr("h5Url");
            let room_id = $(this).attr("room_id");
            let white_label_id = $(this).attr("white_label_id");
            if(!white_label_id || white_label_id == ''){
                if(!timer || timer == null){
                    timer = setInterval(()=> {
                        //保持心跳
                        service.reqFun112010({},function (data) {},{"isShowWait": false});
                    },10000);
                }
                return tools.livePageTo(uid,trueName,h5Url,room_id,_pageCode);
            }
            service.reqFun112005({white_label_id:white_label_id},function (data) {
                // console.log(data.results[0],111)
                if(!data || !data.results || !data.results[0] || data.results[0].is_white_label_user == '0') return layerUtils.iAlert('抱歉，您不能观看此次直播');
                if(!timer || timer == null){
                    timer = setInterval(()=> {
                        //保持心跳
                        service.reqFun112010({},function (data) {},{"isShowWait": false});
                    },10000);
                }
                return tools.livePageTo(uid,trueName,h5Url,room_id,_pageCode);
            })
        }, 'click');
        //点击直播banner
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let uid = ut.getUserInf().custNo;
            let name = ut.getUserInf()?ut.getUserInf().name:'';
            /** 姓名格式化处理 */
            let trueName = tools.format(name);
            let h5Url = $(this).attr("h5Url");
            let room_id = $(this).attr("room_id");
            let white_label_id = $(this).attr("white_label_id");
            if(!white_label_id || white_label_id == ''){
                if(!timer || timer == null){
                    timer = setInterval(()=> {
                        //保持心跳
                        service.reqFun112010({},function (data) {},{"isShowWait": false});
                    },10000);
                }
                return tools.livePageTo(uid,trueName,h5Url,room_id,_pageCode);
            }
            service.reqFun112005({white_label_id:white_label_id},function (data) {
                if(!data || !data.results || !data.results[0] || data.results[0].is_white_label_user == '0') return layerUtils.iAlert('抱歉，您不能观看此次直播');
                if(!timer || timer == null){
                    timer = setInterval(()=> {
                        //保持心跳
                        service.reqFun112010({},function (data) {},{"isShowWait": false});
                    },10000);
                }
                 return tools.livePageTo(uid,trueName,h5Url,room_id,_pageCode);
            })
        }, 'click');
    }
    // function startTime(){
       
    // }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .banner_box").show();
        //进页面首页清除定时器
        if(timer){
            clearInterval(timer);
            timer = null;
        }
    }
    //获取页面数据
    function getList(){
        service.reqFun112003({},function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                let arr = results.live_content_dict;
                for (let i = 0; i < arr.length ; i++) {
                    arr[i].list = results.live_index_list[1] ? results.live_index_list[results.live_content_dict[i].code*1] : [];
                    arr[i].bg_color = colorList[i];
                }
                // let caifubanxiaoshi_list = results.caifubanxiaoshi_list;//财富半小时
                // let caifuguwenmianduimian_list = results.caifuguwenmianduimian_list; //财富顾问面对面
                // let shichanglingjuli_list = results.shichanglingjuli_list; //市场零距离
                //合并数据
                // let arr = [ //集体统一渲染
                //     {
                //         bigTitle:'财富半小时',
                //         list:caifubanxiaoshi_list,
                //         color:'rgb(247,239,229)',
                //         room_type:1
                //     },
                //     {
                //         bigTitle:'财富顾问面对面',
                //         list:caifuguwenmianduimian_list,
                //         color:'rgb(241,233,239)',
                //         room_type:2
                //     },
                //     {
                //         bigTitle:'市场零距离',
                //         list:shichanglingjuli_list,
                //         color:'rgb(229,228,244)',
                //         room_type:3
                //     }
                // ];
                // console.log(arr)
                setHtml(arr)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染HTML模板
    function setHtml(arr){
        let html = ''
        arr.map(item=>{
            var color = item.bg_color; //卡片背景色
            var childrenListHtml = '';
            // console.log(item.list.length == 0)
            if (!item.list.length) {
                childrenListHtml += ` <div class="m_center m_width_100 m_padding_10_10"> 暂无数据 </div> `
            }else{
                item.list.map(items=>{
                    // console.log(items)
                    let statusRemark = items.status == "0" ? '未开始' : items.status == "1" ? '直播中' : '回放'
                    let statusClassName = items.status == "0" ? "noStart" : items.status == "1" ? "doing" : "backIng"
                    //是否展示时间
                    let isShowTime = items.status == "0" ? "" : "display_none";
                    //是否展示img
                    let imgShow = items.status == "1" ? "" : "display_none";
                    childrenListHtml += `
                        <li operationType="1" operationId="listCard_item_${items.room_id}" operationName="直接进入直播间" class="main_flxe m_padding_10_05 listCard_item vertical_line m_width_50" white_label_id="${items.white_label_id}" room_id="${items.room_id}" h5Url="${items.h5Url}" style="position: relative;">
                            <img class="" src="${items.cover}" style="height:1rem;">
                            <span class="m_bold color_000">${items.title}</span>
                            <span class="m_font_size12">${items.company} | ${items.hostname}</span>
                            <p class="labelIcon m_font_size12 m_center ${statusClassName}">
                                <img style="width:0.12rem;height:0.12rem" class="${imgShow}" src="images/liveing.png" alt=""/>
                                <em>${statusRemark}</em>
                            </p>
                            <p class="liveTime ${isShowTime}">${items.startTime.slice(5)}</p>
                        </li>
                    `
                });
            }
            html += `
                <div style="background:${color}" class="card m_radius">
                    <ul class="flex">
                        <li class="m_text_darkgray m_bold m_font_size16">${item.name}</li>
                        <li room_type="${item.code}" name="${item.name}" class="m_marginTop_02 more" operationType="1" operationId="more_${item.code}" operationName="查看更多">${item.list.length ? '查看更多' : '查看更多'}</li>
                    </ul>
                    <ul class="bg_fff flex m_padding_10_10 m_width_100 flxe listCard flex_wrap">
                        ${childrenListHtml}
                    </ul>
                </div>
            `
            
        })
        $(_pageId + ' .list').html(html)
    }
    /*
     * 返回
     */
    function pageBack() {
        //进页面首页清除定时器
        if(timer){
            clearInterval(timer);
            timer = null;
        }
        appUtils.pageBack();
    }
    var liveBroadcast_broadcastList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_broadcastList;
});
