// 身份证信息验证
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        putils = require("putils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        service = require("mobileService"),
        _pageId = "#safety_icbcSign ";
        _pageCode = "safety/icbcSign";
    let bankInfo  = {};//缓存的银行卡信息
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    let _fund_code = '000709';
    var sms_mobile = require("../common/sms_mobile");
    let is_exist;
    let bank_serial_no;
    var payorg_id;
    var pay_mode;
    var sms_time;

    function init() {
        userInfo = ut.getUserInf();
//        console.log(userInfo)
        if (userInfo) {
            $(_pageId + " #bankCard").val(userInfo.bankAcct);
            $(_pageId + " #bankName").val(userInfo.bankName);
            $(_pageId + " #phoneNum").val(userInfo.bankReservedMobile);
        } else {
            pageBack();
        }
        sms_mobile.init(_pageId);//初始化发短信
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #tradeNum").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #jiatradeNum").addClass("active");
                    }
                } else {
                    passflag = "请输入交易密码";
                    $(_pageId + " #jiatradeNum").removeClass("active");
                }
                $(_pageId + " #jiatradeNum").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #tradeNum").val(shuru);
                    $(_pageId + " #jiatradeNum").text(passflag);
                }
            } // 键盘的输入事件
        };
        common.systemKeybord(); // 解禁系统键盘
        get_single_limit();//获取银行卡限额
        
        
       

    }

    

    function get_single_limit() {
        service.reqFun101024({}, function (resultVo) {
            // replace_status 0:待审核 1:已审核待确认 2:换卡成功 3:换卡失败 4: 非换卡期间
            if (resultVo.error_no == "0") {
                var dataList = resultVo.results[0];
                if (dataList.replace_status == "0") { //代审核
                        layerUtils.iAlert("换卡期间不允许此操作", function () {}, function () {
                            pageBack();
                        });
                };
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }
        },)

        //获取银行卡限额
        let params = {
            acct_no:userInfo.bankAcct,
            bank_code:userInfo.bankCode,
            bank_reserved_mobile:userInfo.bankReservedMobile
        }
        service.reqFun101084(params, function (data) {
            if (data.results && data.results.length > 0) {
                var result = data.results[0];
                bankInfo = result;
                let is_bank_fixed_investment = result.is_bank_fixed_investment;//是否支持银行卡定投
                let fixed_investment_priority = result.fixed_investment_priority;//优先级判断 0 晋金宝 1 银行卡
                is_exist = result.is_exist; //是否签约 0未签约 1已签约
                bank_state = result.bank_state; //0银行维护中，1银行正常
                sms_time = result.sms_time;
                pay_mode= result.pay_mode;  //支付模式， 0 签约+支付 1 单独签约
                payorg_id = result.payorg_id;
                if(pay_mode=="2"){
                    let params = {
                        cust_no:userInfo.cust_no,
                        payorg_id:result.payorg_id,
                        fund_code:_fund_code,
                        page_code:_pageCode
                    }
                    tools.getPayPdf(params); //获取协议
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //验证交易密码
    function changePwYZ() {
       // var SysTransPwd = $(_pageId + " #tradeNum").val();
        var verificationCode = $(_pageId + " #verificationCode").val();
        //密码加密
        // service.getRSAKey({}, function (data) {
        //     if (data.error_no == "0") {
        //         var modulus = data.results[0].modulus;
        //         var publicExponent = data.results[0].publicExponent;
        //         var endecryptUtils = require("endecryptUtils");
        //         SysTransPwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, SysTransPwd);
        //         // var param = {
                //     "trans_pwd": SysTransPwd
                // }
                // var callBack = function (resultsVo) {
                //     if (resultsVo.error_no == 0) {
                        //发送登录请求
                        var reqParam = {
                            // "transpwd": SysTransPwd,
                            "isexist":'0',
                            "messagecode":verificationCode,
                            "bankserialno":bank_serial_no,
                            "payorgid":payorg_id
                        };
                        service.reqFun106071(reqParam, function(data) {
                            if(data.error_no == "0") {
                                layerUtils.iAlert("银行卡签约成功", function () {}, function () {
                                    pageBack();
                                });
                            } else {
                                sms_mobile.clear();
                                layerUtils.iAlert(data.error_info);
                            }
                        });
                    // } else {
                    //     layerUtils.iAlert(resultsVo.error_info);
                    // }
                // };
                // service.reqFun101025(param, callBack)
            // } else {
            //     layerUtils.iLoading(false);
            //     layerUtils.iAlert(data.error_info);
            // }
        // }, {isLastReq: false});


    }

    //绑定事件
    function bindPageEvent() {
        //点击开启键盘
        appUtils.bindEvent($(_pageId + " #tradeNum1"), function () {
            if ($(_pageId + " #tradeNum").val() == "") {
                $(_pageId + " #jiatradeNum").removeClass("unable");
            } else {
                $(_pageId + " #jiatradeNum").removeClass("unable").addClass("active");
            }
            kaiqi("tradeNum");
        }, "click");
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            $(_pageId + " #jiatradeNum").removeClass("active").addClass("unable");
            guanbi();
        }, "focus");
        //失去焦点然后就关闭键盘
        appUtils.bindEvent($(_pageId + " #tradeNum"), function () {
            guanbi()
        }, "focus");
        //获取验证码焦点然后就关闭键盘
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            guanbi()
        }, "blur");
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            guanbi()
        }, "click");
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //确认签约
        appUtils.bindEvent($(_pageId + " #ok"), function () {
           // var SysTransPwd = $(_pageId + " #tradeNum").val();
           if (pay_mode=='2'&&!$(_pageId + " .agreement2 i").hasClass("active")) {
            layerUtils.iAlert("请阅读协议并同意签署");
            return;
        }    
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            guanbi();
            if( isSend == "true"){
                return layerUtils.iAlert("请获取验证码");
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            // if ($.trim(SysTransPwd) == "" || $.trim(SysTransPwd) == null) {
            //     layerUtils.iMsg(-1, "交易密码不能为空");
            //     return;
            // }
            // if ($.trim(SysTransPwd).length != 6) {
            //     layerUtils.iMsg(-1, "交易密码为6位数字");
            //     return;
            // }
            changePwYZ();
        });

        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            if (pay_mode=='2'&&!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }    

            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                guanbi();
                var param = {
                    "bank_code": userInfo.bankCode,//银行编码
                    "pay_type": bankInfo.pay_type,
                    "payorg_id": bankInfo.payorg_id,
                    "bank_acct": userInfo.bankAcct,     // 用户卡号
                    "bank_reserved_mobile":userInfo.bankReservedMobile,
                    "cert_no": userInfo.identityNum,   // 用户身份证
                    "bank_name":userInfo.bankName,
                    "sms_type":common.sms_type.bankInvestment,
                    "send_type": "0",
                    "cust_name": userInfo.name, // 用户姓名
                    "cert_type": "0", //证件类型
                    "mobile_phone": userInfo.mobileWhole,
                    "sms_time":sms_time,
                    "type": common.sms_type.bankInvestment,//发送短信验证码
                }
                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
        // 验证码 控制全文字
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            // guanbi();
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        appUtils.bindEvent($(_pageId + " #product .agreement2  i"), function () {
            if ($(this).hasClass('active')) {
                $(this).removeClass('active');
            } else {
                $(this).addClass('active');
            }
        }, "click");
       
    }

    //页面清理
    function clearPage() {
        $(_pageId + " input").attr("value", "");
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);

    }

    function kaiqi(jjb_pwd) {
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_icbcSign";
        param["eleId"] = jjb_pwd;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param)
    }

    function destroy() {
        guanbi();
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " #tradeNum").val("");
        $(_pageId + " #jiatradeNum").text("请输入交易密码");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
