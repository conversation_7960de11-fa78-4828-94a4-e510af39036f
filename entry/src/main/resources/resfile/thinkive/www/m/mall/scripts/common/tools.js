/**
 * 创建于2017.12.29
 * @hasNewMsg 是否有新消息
 * @guanggao  轮播广告推送
 * @checkInput 检验输入密码是否符合规范
 * @fmoney 格式化金额
 * @checkOtherName 校验别名方法
 * @openAPPSkip 外链打开App的页面跳转
 * @openBankApp 打开各大银行app
 * @openLiveBroadcast 跳转直播间
 */

define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var gconfig = require("gconfig");
    var service = require("mobileService"); //业务层接口，请求数据
    var external = require("external");
    var platform = require("gconfig").platform;
    var global = gconfig.global;
    var common = require("common");
    var HIscroll = require("hIscroll");
    var swiper = require("./swiper");
    var layerUtils = require("layerUtils");
    var hIscroll = null;
    var validatorUtil = require("validatorUtil");
    var swipeInstance = null;
    // var homePageIndex = require("../login/userIndexs"); //引入首页js，触发init事件
    var ut = require("./userUtil");
    require("../../js/mobileSelect.min");
    require('./bignumber');
    /**
     * 全局监听滑动事件
     */
    $('body').on('touchstart', function(e) {
        const touch = e.originalEvent.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
    });

    $('body').on('touchmove', function(e) {
        e.preventDefault(); 
    });

    $('body').on('touchend', function(e) {
        let whitePage = ['login_userIndexs','login_userLogin','login_userRegistered','thfund_inputRechargePwd','thfund_enchashment','liveBroadcast_index','account_myAccount','moreDetails_more','vipBenefits_index','account_myAccountNoBind','moreDetails_message'];
        let downPage = $("body .page[data-display='block']") ? $("body .page[data-display='block']").attr("id") : '';
        let index = whitePage.indexOf(downPage);
        if(index < 0) return;
        const touch = e.originalEvent.changedTouches[0];
        endX = touch.clientX;
        endY = touch.clientY;
        //调用滑动事件
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);
        if (absDeltaX > absDeltaY) {
          if (deltaX > 20) {
            tools.recordEventData('2','swipedRight','右滑');
          } else if(deltaX < -20) {
            tools.recordEventData('2','swipedLeft','左滑');
          }
        } else {
          if (deltaY > 20) {
            tools.recordEventData('2','swipedDown','上滑');
          } else if(deltaY < -20) {
            tools.recordEventData('2','swipedUp','下滑');
          }
        }
    });
    var tools = {
        //高端版本尊享服务轮播图
        highBanner: function (param) {
            let _pageId = param._pageId;
            $(_pageId + " #scroller_index2").html('');
            let list = param.list
            // console.log(list)
            swipeInstance = null
            swipeInstance && swipeInstance.destroy(false); //如果存在swiper对象，清空
            let callback = param.callback;
            let str = ""; //轮播内容
            // let picture = "";
            let pagination = ""; //轮播导航
            for (let i = 0; i < list.length; i++) {
                pagination += "<li></li>";
                let picture = global.oss_url + list[i].top_pic_url;
                let h5Url = list[i].h5Url;
                let room_id = list[i].room_id;
                let white_label_id = list[i].white_label_id;
                let statusRemark = list[i].status == 0 ? '未开始' : list[i].status == 1 ? '直播中' : '回放';
                let statusClassName = list[i].status == "0" ? "noStart" : list[i].status == "1" ? "doing" : "backIng"
                //是否展示时间
                let isShowTime = list[i].status == "0" ? "" : "new_display_none";
                let startTime = list[i].startTime.slice(5);
                //是否展示img
                let imgShow = list[i].status == "1" ? "" : "new_display_none";
                str += "<div class='swiper-slide banner_list' operationType='1' operationId='banner' operationName='轮播图' room_id='" + room_id + "' h5Url='" + h5Url + "'  white_label_id='" + white_label_id + "'><img src=" + picture + "><p class='main_flxe flex_center m_font_size12 m_center labelIconBanner " + statusClassName + "'><img style='width:0.12rem;height:0.12rem' class='" + imgShow + "' src='images/liveing.png'/><em style='padding-left:0.02rem'>" + statusRemark + "</em></p><p class='liveTime " + isShowTime + "'>" + startTime + "</p></div>";
            }
            var autoplays, loop;
            if (list.length == 1) {
                $(_pageId + " #scroller_index2").html(str).addClass("swiper-no-swiping");
                $(_pageId + " .swiper-pagination").addClass("swiper-no-swiping");
                autoplays = false;
                loop = false;
                pagination = "";
            } else {
                $(_pageId + " #scroller_index2").html(str).removeClass("swiper-no-swiping");
                $(_pageId + " .swiper-pagination").removeClass("swiper-no-swiping");
                autoplays = 5000;
                loop = true;
                pagination = ".swiper-pagination";
            }

            $(_pageId + " .index_list").html(pagination);
            $(_pageId + " #scroller_index2").html(str);
            $(_pageId + " #scroller_index2 .banner_list").show();
            setTimeout(() => {
                swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                    pagination: pagination,
                    autoplay: autoplays,
                    paginationElement: "li",
                    bulletActiveClass: "check",
                    autoplayDisableOnInteraction: false,
                    observer: true, // 启动动态检查器(OB/观众/观看者)
                    observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                    loop: loop,
                    onImagesReady: function () {
                        if (callback) callback();
                    },
                    beforeDestroy: function () {
                        swipeInstance = null
                    }
                });
            }, 0)

        },
        // 是否有新消息
        hasNewMsg: function (_pageId) {
            service.reqFun102060({}, function (data) {
                if (data.error_no == 0) {
                    appUtils.setLStorageInfo("hasNewMsg_2", data.results[0].noticesId);//最新公告id
                    appUtils.setLStorageInfo("hasNewMsg_4", data.results[0].newsId);//最新资讯id
                    var hasNewMsg_1 = appUtils.getLStorageInfo("hasNewMsg_1")//用户已看的公告id
                    var hasNewMsg_2 = appUtils.getLStorageInfo("hasNewMsg_2")//最新公告id
                    var hasNewMsg_3 = appUtils.getLStorageInfo("hasNewMsg_3")//用户已看的资讯id
                    var hasNewMsg_4 = appUtils.getLStorageInfo("hasNewMsg_4")//最新资讯id
                    hasNewMsg_1 = hasNewMsg_1 ? hasNewMsg_1 : 0;
                    hasNewMsg_3 = hasNewMsg_3 ? hasNewMsg_3 : 0;
                    if (hasNewMsg_1 != hasNewMsg_2 || hasNewMsg_3 != hasNewMsg_4) {
                        appUtils.setLStorageInfo("hasNewMsg", "true");
                        $(_pageId + " #message_img").addClass("on");
                        $(_pageId + ".hasNewMsg").addClass("on");
                    } else {
                        appUtils.setLStorageInfo("hasNewMsg", "false");
                        $(_pageId + " #message_img").removeClass("on");
                        $(".hasNewMsg").removeClass("on");
                    }
                } else {
                    return;
                }
            });
        },
        //直播间首页轮播图
        liveGuanggao: function (param) {
            let _pageId = param._pageId;
            $(_pageId + " #scroller_index2").html('');
            let list = param.list
            swipeInstance = null
            swipeInstance && swipeInstance.destroy(false); //如果存在swiper对象，清空
            let callback = param.callback;
            let str = ""; //轮播内容
            // let picture = "";
            let pagination = ""; //轮播导航
            for (let i = 0; i < list.length; i++) {
                pagination += "<li></li>";
                let picture = global.oss_url + list[i].top_pic_url;
                let h5Url = list[i].h5Url;
                let room_id = list[i].room_id;
                let white_label_id = list[i].white_label_id;
                let statusRemark = list[i].status == 0 ? '未开始' : list[i].status == 1 ? '直播中' : '回放';
                let statusClassName = list[i].status == "0" ? "noStart" : list[i].status == "1" ? "doing" : "backIng"
                //是否展示时间
                let isShowTime = list[i].status == "0" ? "" : "new_display_none";
                let startTime = list[i].startTime.slice(5);
                //是否展示img
                let imgShow = list[i].status == "1" ? "" : "new_display_none";
                str += "<div class='swiper-slide banner_list' operationType='1' operationId='banner' operationName='轮播图' room_id='" + room_id + "' h5Url='" + h5Url + "'  white_label_id='" + white_label_id + "'><img src=" + picture + "><p class='main_flxe flex_center m_font_size12 m_center labelIconBanner " + statusClassName + "'><img style='width:0.12rem;height:0.12rem' class='" + imgShow + "' src='images/liveing.png'/><em style='padding-left:0.02rem'>" + statusRemark + "</em></p><p class='liveTime " + isShowTime + "'>" + startTime + "</p></div>";
            }
            var autoplays, loop;
            if (list.length == 1) {
                $(_pageId + " #scroller_index2").html(str).addClass("swiper-no-swiping");
                $(_pageId + " .swiper-pagination").addClass("swiper-no-swiping");
                autoplays = false;
                loop = false;
                pagination = "";
            } else {
                $(_pageId + " #scroller_index2").html(str).removeClass("swiper-no-swiping");
                $(_pageId + " .swiper-pagination").removeClass("swiper-no-swiping");
                autoplays = 5000;
                loop = true;
                pagination = ".swiper-pagination";
            }

            $(_pageId + " .index_list").html(pagination);
            $(_pageId + " #scroller_index2").html(str);
            $(_pageId + " #scroller_index2 .banner_list").show();
            setTimeout(() => {
                swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                    pagination: pagination,
                    autoplay: autoplays,
                    paginationElement: "li",
                    bulletActiveClass: "check",
                    autoplayDisableOnInteraction: false,
                    observer: true, // 启动动态检查器(OB/观众/观看者)
                    observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                    loop: loop,
                    onImagesReady: function () {
                        if (callback) callback();
                    },
                    beforeDestroy: function () {
                        swipeInstance = null
                    }
                });
            }, 0)

        },
        // 渲染尊享服务轮播图
        renderVipServiceSwipe: function (data) { 
            if(!data.results || data.results.length == 0) return;
             var _pageId = data._pageId;
            $(_pageId + " #scroller_index2").html('');
            swipeInstance = null
            swipeInstance && swipeInstance.destroy(false); //如果存在swiper对象，清空
            var callback = data.callback;
            if (data.results != undefined && data.results.length > 0) {
                var str = ""; //轮播内容
                var picture = "";
                var pagination = ""; //轮播导航
                for (var i = 0; i < data.results.length; i++) {
                    pagination += "<li></li>";
                    picture = global.oss_url + data.results[i].img_url;
                    var banner_id = data.results[i].ad_id
                    let name = data.results[i].name; //banner 名称
                    //file_type 0 外链 1 内链 2 授权登录
                    var file_type = data.results[i].file_type;
                    //url 链接地址  链接地址如果为空也无效
                    // var url = data.results[i].url + "&key=" + Math.random();
                    var url = data.results[i].img_url ? data.results[i].img_url : '';
                    var group_id = data.results[i].group_id;
                    //file_state  0  链接地址无效  1  链接地址有效
                    var file_state = data.results[i].file_state;
                    //是否弹合格投资者确认弹窗
                    var qualified_investor_visible = data.results[i].qualified_investor_visible ? data.results[i].qualified_investor_visible : '';
                    //尊享服务活动id
                    var service_id = data.results[i].service_id;
                    str += `
                        <div class='swiper-slide banner_list'
                            operationType='1'
                            contentType='2'
                            operationId='banner'
                            operationName='轮播图'
                            name='${name}'
                            file_type='${file_type}'
                            banner_id='${banner_id}'
                            qualified_investor_visible='${qualified_investor_visible}'
                            group_id='${group_id}'
                            url='${url}'
                            service_id='${service_id}'
                            file_state='${file_state}'>
                            <img src='${picture}'>
                            <em style='display:none'>${JSON.stringify(data.results[i])}</em>
                        </div>
                    `;
                }
                var autoplays, loop;
                var pagination;
                if (data.results.length == 1) {
                    $(_pageId + " #scroller_index2").html(str).addClass("swiper-no-swiping");
                    // $(_pageId + " .swiper-pagination").addClass("swiper-no-swiping");
                    autoplays = false;
                    loop = false;
                    pagination = "";
                } else {
                    $(_pageId + " #scroller_index2").html(str).removeClass("swiper-no-swiping");
                    // $(_pageId + " .swiper-pagination").removeClass("swiper-no-swiping");
                    autoplays = 5000;
                    loop = true;
                    // pagination = ".swiper-pagination";
                }

                $(_pageId + " .index_list").html(pagination);
                $(_pageId + " #scroller_index2").html(str);
                $(_pageId + " #scroller_index2 .banner_list").show();
                $(_pageId + " .banner_box").show();
                
                setTimeout(() => {
                    swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                        pagination: '',
                        autoplay: autoplays,
                        paginationElement: "li",
                        bulletActiveClass: "check",
                        // el: '.swiper-pagination',
                        autoplayDisableOnInteraction: false,
                        observer: true, // 启动动态检查器(OB/观众/观看者)
                        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                        loop: loop,
                        onImagesReady: function () {
                            if (callback) callback();
                        },
                        beforeDestroy: function () {
                            swipeInstance = null
                        }
                    });
                }, 0)

            }
        },
        // 只渲染广告列表，不调用接口
        guanggaoList: function (data) {
            var _pageId = data._pageId;
            $(_pageId + " #scroller_index2").html('');
            swipeInstance = null
            swipeInstance && swipeInstance.destroy(false); //如果存在swiper对象，清空
            var callback = data.callback;
            if (data.results != undefined && data.results.length > 0) {
                var str = ""; //轮播内容
                var picture = "";
                var pagination = ""; //轮播导航
                for (var i = 0; i < data.results.length; i++) {
                    pagination += "<li></li>";
                    picture = global.oss_url + data.results[i].picture;
                    var banner_id = data.results[i].ad_id
                    let name = data.results[i].name; //banner 名称
                    //file_type 0 外链 1 内链 2 授权登录
                    var file_type = data.results[i].file_type;
                    //url 链接地址  链接地址如果为空也无效
                    // var url = data.results[i].url + "&key=" + Math.random();
                    var url = data.results[i].url ? data.results[i].url : '';
                    var group_id = data.results[i].group_id;
                    //file_state  0  链接地址无效  1  链接地址有效
                    var file_state = data.results[i].file_state;
                    //是否弹合格投资者确认弹窗
                    var qualified_investor_visible = data.results[i].qualified_investor_visible ? data.results[i].qualified_investor_visible : '';
                    //尊享服务活动id
                    var service_id = data.results[i].service_id;
                    str += `
                        <div class='swiper-slide banner_list'
                            operationType='1'
                            contentType='2'
                            operationId='banner'
                            operationName='轮播图'
                            name='${name}'
                            file_type='${file_type}'
                            banner_id='${banner_id}'
                            qualified_investor_visible='${qualified_investor_visible}'
                            group_id='${group_id}'
                            url='${url}'
                            service_id='${service_id}'
                            file_state='${file_state}'>
                            <img src='${picture}'>
                            <em style='display:none'>${JSON.stringify(data.results[i])}</em>
                        </div>
                    `;
                }
                var autoplays, loop;
                var pagination;
                if (data.results.length == 1) {
                    $(_pageId + " #scroller_index2").html(str).addClass("swiper-no-swiping");
                    // $(_pageId + " .swiper-pagination").addClass("swiper-no-swiping");
                    autoplays = false;
                    loop = false;
                    pagination = "";
                } else {
                    $(_pageId + " #scroller_index2").html(str).removeClass("swiper-no-swiping");
                    // $(_pageId + " .swiper-pagination").removeClass("swiper-no-swiping");
                    autoplays = 5000;
                    loop = true;
                    // pagination = ".swiper-pagination";
                }

                $(_pageId + " .index_list").html(pagination);
                $(_pageId + " #scroller_index2").html(str);
                $(_pageId + " #scroller_index2 .banner_list").show();
                $(_pageId + " .banner_box").show();
                
                setTimeout(() => {
                    swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                        pagination: '',
                        autoplay: autoplays,
                        paginationElement: "li",
                        bulletActiveClass: "check",
                        // el: '.swiper-pagination',
                        autoplayDisableOnInteraction: false,
                        observer: true, // 启动动态检查器(OB/观众/观看者)
                        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                        loop: loop,
                        onImagesReady: function () {
                            if (callback) callback();
                        },
                        beforeDestroy: function () {
                            swipeInstance = null
                        }
                    });
                }, 0)

            }
        },
        // 轮播广告推送
        guanggao: function (param) {
            var _pageId = param._pageId;
            $(_pageId + " #scroller_index2").html('');
            swipeInstance = null
            swipeInstance && swipeInstance.destroy(false); //如果存在swiper对象，清空
            var callback = param.callback;

            service.reqFun102011({ group_id: param.group_id,group_no:param.group_no }, function (data) {
                if (data.error_no == 0) {
                    if (data.results != undefined && data.results.length > 0) {
                        var str = ""; //轮播内容
                        var picture = "";
                        var pagination = ""; //轮播导航
                        for (var i = 0; i < data.results.length; i++) {
                            pagination += "<li></li>";
                            picture = global.oss_url + data.results[i].picture;
                            var banner_id = data.results[i].ad_id
                            let name = data.results[i].name; //banner 名称
                            //file_type 0 外链 1 内链 2 授权登录
                            var file_type = data.results[i].file_type;
                            //url 链接地址  链接地址如果为空也无效
                            // var url = data.results[i].url + "&key=" + Math.random();
                            var url = data.results[i].url ? data.results[i].url : '';
                            var group_id = data.results[i].group_id;
                            //file_state  0  链接地址无效  1  链接地址有效
                            var file_state = data.results[i].file_state;
                            //是否弹合格投资者确认弹窗
                            var qualified_investor_visible = data.results[i].qualified_investor_visible ? data.results[i].qualified_investor_visible : '';
                            //尊享服务活动id
                            var service_id = data.results[i].service_id;
                            str += `
                                <div class='swiper-slide banner_list'
                                    operationType='1'
                                    contentType='2'
                                    operationId='banner'
                                    operationName='轮播图'
                                    name='${name}'
                                    file_type='${file_type}'
                                    banner_id='${banner_id}'
                                    qualified_investor_visible='${qualified_investor_visible}'
                                    group_id='${group_id}'
                                    url='${url}'
                                    service_id='${service_id}'
                                    file_state='${file_state}'>
                                    <img src='${picture}'>
                                    <em style='display:none'>${JSON.stringify(data.results[i])}</em>
                                </div>
                            `;
                        }
                        var autoplays, loop;
                        var pagination;
                        if (data.results.length == 1) {
                            $(_pageId + " #scroller_index2").html(str).addClass("swiper-no-swiping");
                            // $(_pageId + " .swiper-pagination").addClass("swiper-no-swiping");
                            autoplays = false;
                            loop = false;
                            pagination = "";
                        } else {
                            $(_pageId + " #scroller_index2").html(str).removeClass("swiper-no-swiping");
                            // $(_pageId + " .swiper-pagination").removeClass("swiper-no-swiping");
                            autoplays = 5000;
                            loop = true;
                            // pagination = ".swiper-pagination";
                        }

                        $(_pageId + " .index_list").html(pagination);
                        $(_pageId + " #scroller_index2").html(str);
                        $(_pageId + " #scroller_index2 .banner_list").show();
                        setTimeout(() => {
                            swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                                pagination: '',
                                autoplay: autoplays,
                                paginationElement: "li",
                                bulletActiveClass: "check",
                                // el: '.swiper-pagination',
                                autoplayDisableOnInteraction: false,
                                observer: true, // 启动动态检查器(OB/观众/观看者)
                                observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                                loop: loop,
                                onImagesReady: function () {
                                    if (callback) callback();
                                },
                                beforeDestroy: function () {
                                    swipeInstance = null
                                }
                            });
                        }, 0)

                    } else {
                        if (callback) callback();
                    }
                } else {
                    if (callback) callback();
                    layerUtils.iAlert(data.error_info);
                }

            });
        },

        //校验别名方法
        checkOtherName: function (phoneNum) {
            var flag = 0;

            //首个字母不能为数字
            if (validatorUtil.isNumeric(phoneNum.charAt(0))) {
                return false;
            }

            for (var i = 0; i < phoneNum.length; i++) {
                if (phoneNum[i].match(/^[a-z]|[A-Z]$/)) {
                    flag = flag + 1;
                    break;
                }
            }

            for (var i = 0; i < phoneNum.length; i++) {
                if (phoneNum[i].match(/^[0-9]$/)) {
                    flag = flag + 1;
                    break;
                }
            }

            if (flag != 2) {
                return false;
            }

            return true;
        },
        /**
         * 检验输入密码是否符合规范
         * 长度，格式等
         */
        checkInput: function (pwd1, pwd2) {
            if (validatorUtil.isEmpty(pwd1)) {
                layerUtils.iAlert("登录密码不能为空");
                return false;
            }
            if (validatorUtil.isEmpty(pwd2)) {
                layerUtils.iAlert("确认密码不能为空");
                return false;
            }
            if (pwd1 !== pwd2) {
                layerUtils.iAlert("两次密码不相同");
                return false;
            }
            var flag = 0;
            for (var i = 0; i < pwd1.length; i++) {
                if (pwd1[i].match(/^[a-zA-z]+$/)) {
                    flag = flag + 1;
                    break;
                }
            }
            for (var i = 0; i < pwd1.length; i++) {
                if (pwd1[i].match(/^\d+$/)) {
                    flag = flag + 1;
                    break;
                }
            }
            if (flag != 2) {
                layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
                return false;
            }

            if (!userZCregister.data.isAgree) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return false;
            }
            return true;
        },

        /**
         * 计算日期之间的天数
         */
        countDays: function (type) {
            var date = new Date();
            var end_time = new Date(date.getFullYear(), date.getMonth(), date.getDay());
            date.setMonth(date.getMonth() - type);
            var new_date = new Date(date.getFullYear(), date.getMonth(), 1); //取几个月前的第一天  
            var maxDay = (new Date(new_date.getTime() - 1000 * 60 * 60 * 24)).getDate();
            var begin_time;
            if (date.getDate() > maxDay) {
                begin_time = new Date(date.getFullYear(), date.getMonth(), maxDay);
            } else {
                begin_time = new Date(date.getFullYear(), date.getMonth(), date.getDay());
            }
            return (Date.parse(end_time) - Date.parse(begin_time)) / (1000 * 60 * 60 * 24);
        },
        /**
         * 格式化金额
         */
        fmoney: function (s, n) {
            if (s == 0) {
                s = s + "";
            }
            if (s == "--" || s == "-") return "--"
            if (!s) return "--"; // 避免传参为空方法调用失败
            if (s == ".") return "0.00"; // 避免传参为空方法调用失败
            s = s + "";
            n = n > 0 && n <= 20 ? n : 2;
            var startStr = "";
            if (s.substr(0, 1) == "-" || s.substr(0, 1) == "+") { //兼容负数、证书处理,截取 “-” “+”，数据处理完成再添加
                startStr = s.substr(0, 1);
                s = s.substr(1, s.length - 1);
            }
            s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";//更改这里n数也可确定要保留的小数位
            var l = s.split(".")[0].split("").reverse(),
                r = s.split(".")[1];
            var t = "";
            for (var i = 0; i < l.length; i++) {
                t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
            }
            return startStr + t.split("").reverse().join("") + "." + r.substring(0, n);//保留2位小数  如果要改动 把substring 最后一位数改动就可
        },
        //补全小数点后两位
        changeTwoDecimal_f: function (x, n) {
            if (!x || x == '--') return '--'
            if (!n) n = 2
            x = x * 1
            let f_x = parseFloat(x);
            if (isNaN(f_x)) {
                // alert('function:changeTwoDecimal->parameter error');
                return false;
            }
            f_x = Math.round(x * 10000) / 10000;
            let s_x = f_x.toString();
            let pos_decimal = s_x.indexOf('.');
            if (pos_decimal < 0) {
                pos_decimal = s_x.length;
                s_x += '.';
            }
            while (s_x.length <= pos_decimal + n) {
                s_x += '0';
            }
            return s_x;
        },
        /**
         * 格式化时间
         */
        ftime: function (date, symbol) {
            if (date == "--") return date;
            if (!date) return "--"; //日期无效，返回 --
            var date = date + "";
            var symbol = symbol || "-";
            if (date && date.length == 8) {
                return date.substr(0, 4) + symbol + date.substr(4, 2) + symbol + date.substr(6, 2)
            } else if (date && date.length == 6) {
                return date.substr(0, 2) + ":" + date.substr(2, 2) + ":" + date.substr(4, 2);
            } else if (date && date.length == 14) {
                return date.substr(0, 4) + symbol + date.substr(4, 2) + symbol + date.substr(6, 2) + " " + date.substr(8, 2) + ":" +
                    date.substr(10, 2) + ":" + date.substr(12, 2);
            } else if (date && date.length == 4) {
                return date.substr(0, 2) + symbol + date.substr(2, 2);
            } else if (date) {
                return date;
            } else {
                return "";
            }
        },
        //格式化时间 年 月 日
        FormatDateText: function (str, type) {
            var num = (+str);
            var isNum = typeof num === 'number' && !isNaN(num);
            if (!isNum) {
                return str;
            }
            var resultDate = "";
            if (str.length == 8) {
                var year = str.substring(0, 4);
                var month = Number(str.substring(4, 6));
                var day = Number(str.substring(6, 8));
                if (type == 1) {  //定投
                    resultDate = year + "-" + month + "-" + day + "";
                } else {
                    resultDate = year + "年" + month + "月" + day + "日";
                }
            } else if (str.length == 4) {
                var month = Number(str.substring(0, 2));
                var day = Number(str.substring(2, 4));
                resultDate = month + "月" + day + "日";
            }
            return resultDate;
        },
        /**
         * 功能: 外链打开app的页面跳转
         */
        openAPPSkip: function () {
            //是否外链打开的,获取有效的跳转信息
            var outsideChain = appUtils.getSStorageInfo("outsideChain");
            appUtils.clearSStorage("outsideChain");
            if (!outsideChain || !outsideChain.page) {
                return;
            }
            //页面信息
            var pageId = $("body .page[data-display='block']").attr("id");//当前页面id
            var pageCode = pageId.replace("_", "/").replace("-", "/");//当前页面
            var page = outsideChain.page;//待跳转页面
            if (pageCode == page) {
                return;
            }
            //跳转页面所需参数封装
            var param = {}
            if (page == "financialProducts/productDetails") {
                param["_prePageCode"] = "login/userIndexs";
                param["product_id"] = outsideChain.id;
                param["moreDetailPage"] = "financialProducts/productMoreDetailsRGN";
                appUtils.setSStorageInfo("productInfo", param);
            } else if (page == "moreDetails/noticeDetails") {
                param["article_id"] = outsideChain.id;
            } else if (page == "guide/advertisement") {
                param["url"] = outsideChain.url;
            }
            appUtils.pageInit(pageCode, page, param);
        },
        /**
         * 功能：打开各大银行app
         * 时间：2019.7.22
         * */
        openBankApp: function (bank_code) {
            var bankLinkInfo = {
                "0001": {
                    bankName: "邮政储蓄银行",
                    iosScheme: "psbcmbank",
                    androidScheme: "com.yitong.mbank.psbc",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id493489515",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.yitong.mbank.psbc"
                },
                "0002": {
                    bankName: "中国工商银行",
                    iosScheme: "com.icbc.iphoneclient",
                    androidScheme: "com.icbc",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id423514795",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.icbc"
                },
                "0003": {
                    bankName: "中国农业银行",
                    iosScheme: "bankabc",
                    androidScheme: "com.android.bankabc",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id515651240",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.android.bankabc"
                },
                "0004": {
                    bankName: "中国银行",
                    iosScheme: "BOCMBCIphone",
                    androidScheme: "com.chinamworld.bocmbci",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id399608199",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.chinamworld.bocmbci"
                },
                "0005": {
                    bankName: "中国建设银行",
                    iosScheme: "ccbmobilebank",
                    androidScheme: "com.chinamworld.main",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id391965015",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.chinamworld.main"
                },
                "0006": {
                    bankName: "中国交通银行",
                    iosScheme: "bocom",
                    androidScheme: "com.bankcomm.Bankcomm",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id337876534",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.bankcomm.Bankcomm"
                },
                "0007": {
                    bankName: "中信银行",
                    iosScheme: "citic",
                    androidScheme: "com.ecitic.bank.mobile",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id422844108",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.ecitic.bank.mobile"
                },
                "0008": {
                    bankName: "光大银行",
                    iosScheme: "com.cebbank.ebank",
                    androidScheme: "com.cebbank.mobile.cemb",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id447733826",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.cebbank.mobile.cemb"
                },
                "0009": {
                    bankName: "华夏银行",
                    iosScheme: "com.hx.hxbank",
                    androidScheme: "com.rytong.app.bankhx",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id666188756",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.rytong.app.bankhx"
                },
                "0010": {
                    bankName: "民生银行",
                    iosScheme: "com.cmbc.cn.iphone",
                    androidScheme: "cn.com.cmbc.newmbank",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id523091708",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=cn.com.cmbc.newmbank"
                },
                "0011": {
                    bankName: "广东发展银行",
                    iosScheme: "cgb",
                    androidScheme: "com.cgbchina.xpt",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id415872960",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.cgbchina.xpt"
                },
                "0012": {
                    bankName: "招商银行",
                    iosScheme: "cmbmobilebank",
                    androidScheme: "cmb.pb",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id392899425",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=cmb.pb"
                },
                "0013": {
                    bankName: "兴业银行",
                    iosScheme: "cibmb",
                    androidScheme: "com.cib.cibmb",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id433592972",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.cib.cibmb"
                },
                "0014": {
                    bankName: "浦发银行",
                    iosScheme: "spdbbank",
                    androidScheme: "cn.com.spdb.mobilebank.per",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id471855847",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=cn.com.spdb.mobilebank.per"
                },
                "0017": {
                    bankName: "平安银行",
                    iosScheme: "paebqw",
                    androidScheme: "com.pingan.paces.ccms",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id1085016815",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.pingan.paces.ccms"
                },
                "0026": {
                    bankName: "渤海银行",
                    iosScheme: "wxcc0b1c78c5bebdb5",
                    androidScheme: "cn.com.bhbc.mobilebank.per",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id557825942",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=cn.com.bhbc.mobilebank.per&ADTAG=mobile"
                },
                "0030": {
                    bankName: "晋商银行",
                    iosScheme: "jshbank",
                    androidScheme: "com.csii.jsh.ui",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id728307742",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.csii.jsh.ui"
                },
                "0032": {
                    bankName: "山西省农村信用社联合社",
                    iosScheme: "comeToLiaoNing",
                    androidScheme: "com.ydnsh.buy.mobileBank",
                    iosDownloadLink: "https://apps.apple.com/cn/app/id1068941256",
                    androidDownloadLink: "https://android.myapp.com/myapp/detail.htm?apkName=com.ydnsh.buy.mobileBank"
                },
            };
            var param = {
                funcNo: "80050",
            };
            service.reqFun102019({ bank_code: bank_code }, function (data) {
                if (data.error_no === "0") {
                    var results = data.results;
                    if (results.length > 0) {
                        if (platform == 1) {
                            param.scheme = results[0].packagename;
                            param.downloadLink = results[0].android_down_address;
                        } else if (platform == 2) {
                            param.scheme = results[0].url_scheme;
                            param.downloadLink = results[0].ios_down_address;
                        }
                    }
                } else {
                    if (platform == 1) {
                        param.scheme = bankLinkInfo[bank_code].androidScheme;
                        param.downloadLink = bankLinkInfo[bank_code].androidDownloadLink;
                    } else if (platform == 2) {
                        param.scheme = bankLinkInfo[bank_code].iosScheme;
                        param.downloadLink = bankLinkInfo[bank_code].iosDownloadLink;
                    }
                }
                external.callMessage(param);
            })
        },

        /*
        * 功能： 初始化基金产品按钮样式
        * */
        initFundBtn: function (productInfo, _pageId) {
            console.log(productInfo,11111);
            //是否展示海外节假日提醒
            let not_workday = ``;
            if(productInfo.not_workday_start && productInfo.not_workday_start.length){
                not_workday = `<div class="not-workday">根据基金公司公告，${tools.FormatDateText(productInfo.not_workday_start)}${(productInfo.not_workday_end&&productInfo.not_workday_end.length)?`~${productInfo.not_workday_end.substr(6,2)*1}日`:''}为境外证券交易所非交易日，涉及境外投资的基金产品暂停交易，请知悉</div>`;
            }
            //是否展示单日限购
            if (sessionStorage.vip_buttonShow === "false" || sessionStorage.vip_buttonShow === false) {
                $(_pageId + " .thfundBtn").hide()
            } else {
                $(_pageId + " .thfundBtn").show()
            }
            let day_purchase_limit_remark = `${(productInfo.day_purchase_limit_show && productInfo.day_purchase_limit_show == '1') ? '，限大额' : ''}`
            //判断参数包含是否可定投
            let fixed_investment_list = productInfo.fixed_investment_list
            // let fixed_investment_list = 1
            if (productInfo.exclusive_type && productInfo.prod_sub_type == "10") { // 专享 && 货基 不展示购买按钮
                $(_pageId + " .thfundBtn").html("").css({ height: 0 });
                $(_pageId + " #product").css({ "padding-bottom": "0.1rem" });
                if (productInfo.fund_code != "000709") { //非华安汇财通页面，删除华安汇财通本地信息，防止本地信息混淆
                    appUtils.clearSStorage("jjbFundCode");
                    appUtils.clearSStorage("productInfo_jjb");
                }
                return;
            }
            $(_pageId + " #product").css({ "padding-bottom": "0.86rem" });
            service.reqFun102045({
                fund_code: productInfo.fund_code,
                amt: productInfo.threshold_amount
            }, function (data) {
                var rateTxt = "，买入费率为0"
                if (data.error_no = "0") {
                    var results = data.results;
                    var unit = "";
                    if (results.length > 0 && results[0].rate) {
                        unit = results[0].unit || "";
                        rateTxt = "，买入费率：" + results[0].rate + unit;
                    }
                    if (results.length > 0 && results[0].discount_ratevalue) { //折扣率
                        unit = results[0].unit || "";
                        rateTxt = "，买入费率" + results[0].discount + "折：<span style='text-decoration: line-through;margin-right: 0.03rem'>" + results[0].rate + unit + "</span>" + results[0].discount_ratevalue + unit;
                    }
                }

                var _pageCode = _pageId.replace("_", "/").substr(1).trim(); //页面code
                var buy_state = productInfo.buy_state; //产品状态
                var buy_state_obj = {
                    "1": { name: "买入", class: "" },
                    "2": { name: "预约", class: "" },
                    "3": { name: "敬请期待", class: "no_active" },
                    "4": { name: "封闭中", class: "no_active" },
                    "5": { name: "售罄", class: "no_active" },
                    "6": { name: "买入", class: "no_active" },
                }
                var isRedemptionFeeStr = "";
                if (productInfo.prod_sub_type2 == "200" && productInfo.buy_button) {
                    // $(_pageId + " .header_inner h1").html(productInfo.prod_name_list ? productInfo.prod_name_list : productInfo.prod_sname);
                    isRedemptionFeeStr = productInfo.buy_button;
                } else if (productInfo.prod_sub_type == "30") {
                    isRedemptionFeeStr = '<span>(建议持有30天以上)</span>';
                    $(_pageId + " .header_inner h1").html("晋金宝" + require("gconfig").global.holding_days + "天")
                } else if (productInfo.prod_sub_type == "50") { // 债基
                    if (productInfo.prod_sub_type2 == "52") {
                        isRedemptionFeeStr = '<span>(持有' + (productInfo.maturity_days || productInfo.p_inrest_term) + '随时可取)</span>';
                    } else if (productInfo.prod_sub_type2 == "01") {
                        isRedemptionFeeStr = '<span>(封闭期' + (productInfo.maturity_days || productInfo.p_inrest_term) + ')</span>';
                    }

                } else {
                    // $(_pageId + " .header_inner h1").html(productInfo.prod_name_list ? productInfo.prod_name_list : productInfo.prod_sname);
                }
                if (productInfo.fund_code != "000709" && productInfo.prod_sub_type) { //非华安汇财通页面，删除华安汇财通本地信息，防止本地信息混淆
                    appUtils.clearSStorage("jjbFundCode");
                    appUtils.clearSStorage("productInfo_jjb");
                }
                var threshold_amount = productInfo.threshold_amount;
                if (threshold_amount != "--") {
                    threshold_amount = tools.fmoney(threshold_amount);
                }
                
                var str = not_workday + '<div class="purchase">\n' +
                    '         <span>' + threshold_amount + '元起购</span>\n' + day_purchase_limit_remark +
                    '         <span id="buy_rate_box">' + rateTxt + '</span>\n' +
                    '      </div>\n' +
                    // fixed_investment_list
                    '' +
                    '<div class="calculator ' + (fixed_investment_list == 1 ? '' : 'display_none') + '">' +
                    '<img class="" src="./images/calculator.png"></img>'
                    + '</div>'
                    +
                    '<div class="fixedInvestment ' + (fixed_investment_list == 1 ? '' : 'display_none') + '">' +
                    '定投' +
                    '</div>' +
                    '      <div style="' + (fixed_investment_list == 1 ? 'left:50%;width:50%' : 'left:0;width:100%;') + '" class="buy  ' + buy_state_obj[buy_state].class + '">\n' +
                    '          <span class="f18" id="buy_state">' + buy_state_obj[buy_state].name + '</span>\n' +
                    isRedemptionFeeStr +
                    '      </div>';
                $(_pageId + " .thfundBtn").html(str).css({ height: "0.86rem" });
                appUtils.bindEvent($(_pageId + " .calculator"), function () {
                    tools.recordEventData('1','calculator','定投计算器');
                    if (!common.loginInter(_pageCode)) return;
                    appUtils.pageInit(_pageCode, "fixedInvestment/calculator");
                });
                appUtils.bindEvent($(_pageId + " .fixedInvestment"), function () {
                    tools.recordEventData('1','fixedInvestment','定投');
                    if (!common.loginInter(_pageCode)) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let info = ut.getUserInf()
                    if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
                    if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
                    common.changeCardInter(function () {
                        if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                            let operationId = "riskAssessment";
                            layerUtils.iConfirm("您还未进行风险测评", function () {
                                appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                            }, function () {
                            }, "去测评", "取消",operationId);
                            return;
                        }
                        appUtils.setSStorageInfo("isAdvisoryInvestment", '');
                        appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
                    });
                });
                appUtils.bindEvent($(_pageId + " .buy"), function () {
                    tools.recordEventData('1','buy','购买');
                    if ($(this).hasClass("no_active")) {
                        if (buy_state == "6") {
                            layerUtils.iAlert("当前时间不支持购买");
                        }
                        return;
                    }
                    if (!common.loginInter(_pageCode)) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let info = ut.getUserInf()
                    if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
                    if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
                    common.changeCardInter(function () {
                        if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                            let operationId = "riskAssessment";
                            layerUtils.iConfirm("您还未进行风险测评", function () {
                                appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                            }, function () {
                            }, "去测评", "取消",operationId);
                            return;
                        }
                        if (productInfo.fund_code == "000709") { // 华安汇财通跳转充值页
                            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
                        } else {
                            tools.jumpBuyPage(_pageCode, productInfo.prod_sub_type, productInfo.prod_sub_type2);
                        }
                    });
                });
            })

        },

        /**
         * 初始化投顾产品按钮
         */

        initCombFundBtn: function (productInfo, _pageId) {
            $(_pageId + " .thfundBtn").show()
            //判断参数包含是否可定投
            let fixed_investment_list = productInfo.fixed_investment_list;
            $(_pageId + " #product").css({ "padding-bottom": "0.5rem" });
            var _pageCode = _pageId.replace("_", "/").substr(1).trim(); //页面code
            var purchase_state = productInfo.purchase_state; //产品状态
            // var purchase_state_class = purchase_state == "1" ? "" : "no_active";
            var buy_state = productInfo.purchase_state; //产品状态
            var buy_state_obj = {
                "1": { name: "买入", class: "" },
                "2": { name: "预约", class: "" },
                "3": { name: "敬请期待", class: "no_active" },
                "4": { name: "封闭中", class: "no_active" },
                "5": { name: "售罄", class: "no_active" },
                "6": { name: "买入", class: "no_active" },
            }
            if (productInfo.comb_code != "000709") { //非华安汇财通页面，删除华安汇财通本地信息，防止本地信息混淆
                appUtils.clearSStorage("jjbFundCode");
                appUtils.clearSStorage("productInfo_jjb");
            }
            var threshold_amount = productInfo.threshold_amount;
            if (threshold_amount != "--") {
                threshold_amount = tools.fmoney(threshold_amount);
            }
            var buy_deadline = productInfo.open_period_end_time ? productInfo.open_period_end_time : '';
            if ((buy_state == '1' || buy_state == '2') && buy_deadline) {
                buy_deadline = tools.ftime(buy_deadline.substr(4, 4), "月") + "日 " + tools.ftime(buy_deadline.substr(8, 8), ".").substr(0, 5);
                buy_deadline = ' (截止至 ' + buy_deadline + ')';
            } else {
                buy_deadline = '';
            }
            // fixed_investment_list
            var str =
                '<div style="width:50%;left: 0" class="fixedInvestment ' + (fixed_investment_list == 1 ? '' : 'display_none') + '">' +
                '定投' +
                '</div>' +
                '      <div style="' + (fixed_investment_list == 1 ? 'left:50%;width:50%' : 'left:0;width:100%;') + '" class="buy  ' + buy_state_obj[buy_state].class + '">\n' +
                '          <span class="f18" id="buy_state">' + `${buy_state_obj[buy_state].name}${buy_deadline ? buy_deadline : ''}` + '</span>\n' +
                '      </div>';
            $(_pageId + " .thfundBtn").html(str).css({ height: "0.5rem" });
            appUtils.bindEvent($(_pageId + " .buy"), function () {
                tools.recordEventData('1','buy','购买');
                if ($(this).hasClass("no_active")) {
                    if (purchase_state == "6") {
                        layerUtils.iAlert("当前时间不支持购买");
                    }
                    return;
                }
                if (!common.loginInter(_pageCode)) return;
                if (!ut.hasBindCard(_pageCode)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                let info = ut.getUserInf()
                if (productInfo.purchase_state == '1' && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        let operationId = 'riskAssessment';
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    }
                    appUtils.pageInit(_pageCode, "combProduct/combProdBuy");
                    return;
                });
            });
            appUtils.bindEvent($(_pageId + " .fixedInvestment"), function () {
                tools.recordEventData('1','fixedInvestment','定投');
                if (!common.loginInter(_pageCode)) return;
                if (!ut.hasBindCard(_pageCode)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                let info = ut.getUserInf()
                if (productInfo.purchase_state == '1' && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        let operationId = 'riskAssessment';
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    }
                    appUtils.setSStorageInfo("isAdvisoryInvestment", '1');
                    appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment", {});
                });
            });
        },

        /*
        * 功能： 初始化高端产品按钮样式
        * */

        initPriFundBtn: function (productInfo, _pageId) {
            $(_pageId + " .thfundBtn").show()
            service.reqFun102045({
                fund_code: productInfo.fund_code,
                amt: productInfo.threshold_amount
            }, function (data) {
                var rateTxt = productInfo.businesscode == "22" ? "，买入费率为0" : "，买入费率为0"; //22申购 20认购
                if (data.error_no = "0") {
                    var results = data.results;
                    var unit = "";
                    if (results.length > 0 && results[0].rate) {
                        unit = results[0].unit || "";
                        rateTxt = "，买入费率：" + results[0].rate + unit;
                    }
                    if (results.length > 0 && results[0].discount_ratevalue) { //折扣率
                        unit = results[0].unit || "";
                        rateTxt = "，买入费率" + results[0].discount + "折：<span style='text-decoration: line-through;margin-right: 0.03rem'>" + results[0].rate + unit + "</span>" + results[0].discount_ratevalue + unit;
                    }
                }

                var _pageCode = _pageId.replace("_", "/").substr(1).trim(); //页面code
                var buy_state = productInfo.buy_state; //产品状态
                let addressPage = productInfo.prod_sub_type2 == '100' ? productInfo.prod_sub_type == "90" ? "highEnd/index" : "highEnd/indexPremise" : null //判断是否是最新
                var page_obj = {
                    "81": "highEnd/indexPremise",
                    "90": "highEnd/index",
                    "91": "highEnd/indexPremise",
                    "92": "highEnd/index",
                    "93": "highEnd/index",
                    "94": "highEnd/index",
                    "95": "highEnd/indexPremise",
                    "96": "highEnd/indexPremise",
                    "97": "highEnd/index",
                    "100": addressPage

                }
                appUtils.clearSStorage("jjbFundCode");
                appUtils.clearSStorage("productInfo_jjb");
                var threshold_amount = productInfo.threshold_amount;
                if (threshold_amount != "--") {
                    threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万" : tools.fmoney(threshold_amount);
                }
                var str = '<div class="purchase">\n' +
                    '         <span>' + threshold_amount + '元起购</span>\n' +
                    '         <span id="buy_rate_box">' + rateTxt + '</span>\n' +
                    '      </div>\n' +
                    '      <div class="buy ' + tools.priBtnObj(buy_state, productInfo.prod_sub_type2, "no_active").btnClass + '">\n' +
                    '          <span class="f18" id="buy_state">' + tools.priBtnObj(buy_state, productInfo.prod_sub_type2, "no_active").btnText + '</span>\n' +
                    '      </div>';
                $(_pageId + " .thfundBtn").html(str);
                appUtils.bindEvent($(_pageId + " .buy"), function () {
                    tools.recordEventData('1','buy','购买');
                    if ($(this).hasClass("no_active")) {
                        return;
                    }
                    if (parseFloat(productInfo.surplus_amount * 10000) < parseFloat(productInfo.threshold_amount)) {
                        layerUtils.iMsg(-1, '剩余额度不足');
                        return
                    }
                    // 源晖、持有期、政金债
                    // if (productInfo.surplus_num == "0" && (productInfo.prod_sub_type2 == "93" || productInfo.prod_sub_type2 == "94" || productInfo.prod_sub_type2 == "95")) {
                    //     layerUtils.iMsg(-1, '购买人数已达上限');
                    //     return
                    // }
                    if (!common.loginInter(_pageCode)) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    //校验用户是否上传过身份证照片
                    if(!ut.getUploadStatus()){
                        let operationId = 'noUploadIdCard'
                        appUtils.setSStorageInfo("noUploadIdCard", '1');
                        return layerUtils.iConfirm("您还未上传身份证照片", function () {
                            appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                        }, function () {
                        }, "去上传", "取消",operationId);
                    }
                    let info = ut.getUserInf();
                    if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (info.invalidFlag == '1')) return tools.pageTo_evaluation()
                    if ((info.riskLevel == '00' || info.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
                    common.changeCardInter(function () {
                        setTimeout(function () {
                            appUtils.pageInit(_pageCode, page_obj[productInfo.prod_sub_type2]);
                        }, 0)
                    });
                });
            })

        },


        /**
         * 功能：调用相机、相册进行ocr识别
         * @praam type zm | fm 身份证正反面
         * */
        openCamera: function (type) {
            let phoneType = platform
            var pageId = "#" + $("body .page[data-display='block']").attr("id");
            var str
            if (phoneType == 2) {
                str = '<div class="pop_layer camera_pop_layer">\n' +
                    '            <div  class="slideup in">\n' +
                    '                <div class="camera">拍照</div>\n' +
                    '                <div class="album">从相册中选取</div>\n' +
                    '                <div class="cancel_camera">取消</div>\n' +
                    '            </div>\n' +
                    '        </div>'
            } else {
                str = '<div class="pop_layer android_dig camera_pop_layer">\n' +
                    '            <div  class="slideup in">\n' +
                    '                <div class="camera">拍照</div>\n' +
                    '                <div class="album">从相册中选取</div>\n' +
                    '                <div class="cancel_camera">取消</div>\n' +
                    '            </div>\n' +
                    '        </div>'
            }
            if ($(pageId + " .camera_pop_layer").length > 0) {
                $(pageId + " .camera_pop_layer").show();
            } else {
                $(pageId).append(str);
            }

            //调用相册 ocr识别
            appUtils.bindEvent($(pageId + " .album"), function () {
                tools.recordEventData('1','album','相册');
                var external = require("external");
                var Param = {
                    "funcNo": "60302",
                    "moduleName": "mall",
                    "ocrType": "2",
                    "paramExt": {
                        type: type
                    },
                };
                external.callMessage(Param);
            });
            //调用相机 ocr识别
            appUtils.bindEvent($(pageId + " .camera"), function () {
                tools.recordEventData('1','camera','相机');
                var external = require("external");
                var Param = {
                    "funcNo": "60302",
                    "moduleName": "mall",
                    "ocrType": "1",
                    "paramExt": {
                        type: type
                    },
                };
                external.callMessage(Param);
            });
            //关闭弹框
            appUtils.bindEvent($(pageId + " .camera_pop_layer"), function (e) {
                tools.recordEventData('1','camera_pop_layer','关闭弹框');
                e.stopPropagation();
                $(pageId + ' .camera_pop_layer').hide();
            });
            appUtils.bindEvent($(pageId + " .cancel_camera"), function (e) {
                tools.recordEventData('1','cancel_camera','关闭弹框');
                $(pageId + ' .camera_pop_layer').hide();
            });
        },

        /**
         * 功能：获取协议内容，并调用原生打开协议
         * @param obj 若为字符串，表示协议类型，若为对象，表示入参集  M
         * @param other1 obj=="prod"是为产品编码fund_code，其他表示是否有书名号 N
         * @param other2 产品子业务吗 N   1:预约 2:购买 3:转让 4:赎回
         * @param isShowFullProtocol 是否展示具体协议 false 展示相关协议
         * */
        getPdf: function (obj, other1, other2, other3, other4, isShowDown, isShowFullProtocol) {
            var agreement_type, //协议类型
                fund_code, //产品Code
                fixed_invest_flag,//定投协议相关参数
                agreement_sub_type, //子类型
                bookTitleMark,// 是否有书名号，1 有 0 无，默认有
                stratStr, //协议开始语
                endStr,//协议结束语
                bankcard_fixedinvest_flag;
            if (typeof obj === "object") { //入参为对象
                agreement_type = obj.agreement_type;
                bookTitleMark = obj.bookTitleMark || "1";
                bankcard_fixedinvest_flag = obj.bankcard_fixedinvest_flag || '0';
                fund_code = obj.fund_code;
                agreement_sub_type = obj.agreement_sub_type;
                stratStr = obj.stratStr || "我已仔细阅读并同意签署";
                endStr = obj.endStr || "";
                fixed_invest_flag = obj.fixed_invest_flag ? obj.fixed_invest_flag : '';
            } else if (typeof obj === "string" && obj == "prod") { //入参为字符串,并且根据产品查协议
                agreement_type = obj;
                fund_code = other1;
                agreement_sub_type = other2;
                stratStr = other3 || "我已仔细阅读并同意签署";
                endStr = other4 || "";
            } else { //不区分产品查协议
                agreement_type = obj;
                bookTitleMark = other1 || "1";
            }
            if (obj == "13") {
                stratStr = "我已仔细阅读并同意签署";
                endStr = "";
            }
            var pageId = "#" + $("body .page[data-display='block']").attr("id");
            $(pageId + " .agreement_layer").hide();
            // 产品协议增加留痕

            if ($(pageId + ' .agreement').length > 0 && !isShowFullProtocol && pageId != "#template_holdHeighDetail") {
                var agrStr = '<div class="agreement2">\n' +
                    '             <i></i>\n' +
                    '             <div style="margin-left: 0.2rem;">' + stratStr + '<span operationType="1" operationId="click_agreement" operationName="相关协议" class="m_agreement_color click_agreement">《相关协议》</span>' + endStr + '</div>' +
                    '</div>'
                $(pageId + " .agreement").html(agrStr);
            }
            var param = {
                agreement_type: agreement_type, //'prod'
                fund_code: fund_code,
                fixed_invest_flag: fixed_invest_flag ? fixed_invest_flag : '',
                agreement_sub_type: agreement_sub_type, //agreement_sub_type
                bankcard_fixedinvest_flag: bankcard_fixedinvest_flag
            };
            if (ut.getUserInf()) {
                param.bank_code = ut.getUserInf().bankCode;
                param.acct_no = ut.getUserInf().bankAcct;
            }
            //获取协议
            service.reqFun102016(param, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return
                }
                var html = "";
                var agreement_id = [];
                var agreement_id_pay = [];
                // console.log(data.results)
                for (var i = 0; i < data.results.length; i++) {
                    let emHtml = `<em style="display:none">${JSON.stringify(data.results[i])}</em>`
                    if (pageId == '#moreDetails_more' || pageId == '#account_logOff1' || pageId == '#highEnd_contract' || pageId == '#yuanhui_productDetail' || pageId == "#login_userRegistered" || pageId == "#login_userLogin" || pageId == "#highEnd_modifyExpireWay" || pageId == '#safety_fingerprintPwd') {
                        //保留之前
                        html += '<a href="javascript:void(0);" operationType="1" operationId="xy" contentType="9" operationName="协议" class="xy" url="' + global.oss_url + data.results[i].url + '">';
                        if (bookTitleMark == "0") {
                            html += data.results[i].agreement_title;
                        } else {
                            html += "《" + data.results[i].agreement_title + "》";
                        }
                        html += emHtml;
                        html += '</a>';
                    } else {
                        html += '<li operationType="1" operationId="xy" contentType="9" operationName="协议" href="javascript:void(0);" class="flex xy" url="' + global.oss_url + data.results[i].url + '">';
                        if (bookTitleMark == "0") {
                            html += "<span>" + data.results[i].agreement_title + "</span>"
                        } else {
                            html += "<span>《" + data.results[i].agreement_title + "》</span>";
                        }
                        html += emHtml;
                        html += '</li>';
                    }
                    agreement_id.push(data.results[i].agreement_id);
                    if (data.results[i].agreement_type == "pay") { // 是支付协议
                        agreement_id_pay.push(data.results[i].agreement_id);
                    }
                }
                // console.log(html)
                if (html == '' || !html) {
                    $(pageId + " .radio_box").hide()
                } else {
                    $(pageId + " .radio_box").show()
                }
                $(pageId + " .agreement_list").html(html);
                //deal_box
                //点击相关协议
                appUtils.bindEvent($(pageId + " .click_agreement"), function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // tools.recordEventData('1','click_agreement','点击相关协议');
                    $(pageId + " .agreement_layer").show();
                });
                //关闭协议
                appUtils.bindEvent($(pageId + " .new_close_btn"), function (e) {
                    tools.recordEventData('1','new_close_btn','关闭协议');
                    $(pageId + " .agreement_layer").hide();

                });
                //查看PDF文件
                appUtils.preBindEvent($(pageId + " .agreement_list"), ".xy", function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // tools.recordEventData('1','xy','查看PDF文件');
                    var url = $(this).attr("url");
                    let title = '内容查看'
                    let statusColor = '#2F4D80'
                    let titleColor = '#ffffff'
                    var param = {};
                    param["funcNo"] = "50240";
                    param["url"] = url;
                    param["title"] = title;
                    param["statusColor"] = statusColor;
                    param["titleColor"] = titleColor;
                    // param["isShowDownLoadBtn"] = '1'
                    if (isShowDown && platform == '2') {
                        param["rightBtnTxt"] = 'tk_webview_download';
                        param["rightBtnMode"] = '1';
                        param["rightBtnAction"] = "note_download_pdf";
                    }
                    if ($(this).html().indexOf('隐私') > -1) {
                        param.rightBtnActionParam = {
                            promtpText: '撤回同意协议将自动退出晋金财富APP,是否执行撤回操作?',
                        };
                        param.rightBtnAction = 'undoPrivacy';
                        param.rightBtnTxt = '撤回';
                        param.rightBtnTxt = 'tk_plugin_50240_privacyMenu';
                        param.rightBtnMode = '1';
                        param.goBackMode = '1';
                        param.rightBtnAction = 'privacyMenu';
                        param.rightBtnActionParam.downloadUrl = url;
                        param.url = 'https://m.xintongfund.com/m/mall/index.html#!/privacyAgreement/index.html';
                        if(platform == '1'){
                            param.suffix = 'html';
                        }
                    }
                    require("external").callMessage(param);
                }, "click");
                //勾选协议
                appUtils.preBindEvent($(pageId + " .agreement"), ".agreement2", function (e) {
                    // e.stopPropagation();
                    // e.preventDefault();
                    tools.recordEventData('1','agreement2','勾选协议');
                    var _self = this;
                    if ($(this).find("i").hasClass("active")) {
                        $(this).find("i").removeClass("active")
                    } else {
                        if (fund_code == "000709" && agreement_id_pay.length == 0) {
                            $(this).find("i").addClass("active");
                            return;
                        }
                        if (fund_code == "000709" && agreement_id_pay.length > 0) { //晋金宝 协议支付
                            agreement_id = agreement_id_pay; //只签署支付协议
                        }
                        if (agreement_id.length == 0) {
                            layerUtils.iAlert("该产品未配置协议");
                        } else {
                            //银行卡定投新增产品ID
                            let newParam = {
                                agreement_id: agreement_id.join(","),
                                fund_code: (obj && obj.bankcard_fixedinvest_flag == "1") ? fund_code : ''
                            }
                            service.reqFun106020(newParam, function (datas) {
                                if (datas.error_no == "0") {
                                    var results = datas.results;
                                    if (datas.length == 0) {
                                        layerUtils.iAlert("网络繁忙，请稍后重试");
                                        return;
                                    }
                                    var agreement_sign_no = results[0].agreement_sign_no;
                                    $(_self).find("i").addClass("active");
                                    $(pageId + " .agreement2").attr("agreement_sign_no", agreement_sign_no)
                                } else {
                                    layerUtils.iAlert(datas.error_info);
                                }
                            })
                        }

                    }
                }, 'click');
            });
        },
        /**
         * 功能：获取协议内容，并调用原生打开协议
         * @param obj 若为字符串，表示协议类型，若为对象，表示入参集  M
         * @param other1 obj=="prod"是为产品编码fund_code，其他表示是否有书名号 N
         * @param other2 产品子业务吗 N   1:预约 2:购买 3:转让 4:赎回
         * @param isShowFullProtocol 是否展示具体协议 false 展示相关协议
        * */
        getPayPdf: function (inParam,isShowDown) {
           var pageId = "#" + $("body .page[data-display='block']").attr("id");
            $(pageId + " .agreement_layer").hide();
            // 产品协议增加留痕
            if ($(pageId + ' .agreement').length > 0 ) {
                var agrStr = '<div class="agreement2" style="position: relative;text-align: initial;margin: 0 0.2rem;">\n' +
                    '             <i></i>\n' +
                    '             <div style="margin-left: 0.2rem;">'+'我已阅读并同意签署' + '<span operationType="1" operationId="click_agreement" operationName="相关协议" class="m_agreement_color click_agreement">《相关协议》</span>'   + '</div>' +
                    '</div>'
                $(pageId + " .agreement").html(agrStr);
            }
            var param = {
                cust_no:inParam.cust_no,
                payorg_id:inParam.payorg_id
            };
            let fund_code = inParam.fund_code;
            let page_code = inParam.page_code;
            //获取协议
            service.reqFun106081(param, function (data) {
                if (data.results && data.results.length > 0) {
                    // var result = data.results;
                    // var UnionAgreeName=result.UnionAgreeName.split(';');
                    // var UnionAgreeUrl=result.UnionAgreeUrl.split(';');
                    // console.log(result);
                    var html="";
                var agreement_id = [];
                var agreement_id_pay = [];
                // console.log(data.results)
                for (var i = 0; i < data.results.length; i++) {
                    let emHtml = `<em style="display:none">${JSON.stringify(data.results[i])}</em>`
                    // 当获取到的url后缀为.html时
                    //html += '<li operationType="1" operationId="xy" contentType="9" operationName="协议" href="javascript:void(0);" class="flex xy" url="' + data.results[i].url + '">';
                    if (data.results[i].url.indexOf('.html') > -1) { 
                        html += '<li operationType="1" operationId="xy" contentType="9" operationName="协议" href="javascript:void(0);" class="flex xy" url="' + data.results[i].url + '">';
                    } else {
                        html += '<li operationType="1" operationId="xy" contentType="9" operationName="协议" href="javascript:void(0);" class="flex xy" url="' + global.oss_url + data.results[i].url + '">';
                    }
                    html += "<span>" + data.results[i].agreement_title + "</span>";
                        html += emHtml;
                        html += '</li>';
                    agreement_id.push(data.results[i].agreement_id);
                    if (data.results[i].agreement_type == "unionpay") { // 是支付协议
                        agreement_id_pay.push(data.results[i].agreement_id);
                    }
                }
                    // let emHtml = `<em style="display:none">${JSON.stringify(result)}</em>`
                  
                  //  ${JSON.stringify(data.results[i])}
                    // if(UnionAgreeName.length==UnionAgreeUrl.length)
                    // {
                        
                    //     for(var j=0;j<UnionAgreeName.length;j++)
                    //     {
                    //       var dresult={

                    //       };  
                    //       html += '<li operationType="1" operationId="xy" contentType="9" operationName="协议" href="javascript:void(0);" class="flex xy" url="' + UnionAgreeUrl[j] + '">'; 
                    //       html += "<span>" + UnionAgreeName[j] + "</span>"
                    //       html += emHtml;
                    //       html += '</li>';
                    //     }
                    // }
                    // var BankAgreeName=result.BankAgreeName.split(';');
                    // var BankAgreeUrl=result.BankAgreeUrl.split(';');
                    // if(BankAgreeName.length==BankAgreeUrl.length){
                    //         for(var j=0;j<BankAgreeName.length;j++){
                    //             if(BankAgreeName[j]!='')
                    //             {
                    //                 html += '<li operationType="1" operationId="xy" contentType="9" operationName="协议" href="javascript:void(0);" class="flex xy" url="' + BankAgreeUrl[j] + '">'; 
                    //                 html += "<span>" + BankAgreeName[j] + "</span>"
                    //                 //  html += emHtml;
                    //                 html += emHtml;
                    //                 html += '</li>';
                    //             }
                              
                    //         }
                    // }


                    if (html == '' || !html) {
                        $(pageId + " .radio_box").hide()
                    } else {
                        $(pageId + " .radio_box").show()
                    }
                    $(pageId + " .agreement_list").html(html);
                    //deal_box
                    //点击相关协议
                    appUtils.bindEvent($(pageId + " .click_agreement"), function (e) {
                        // tools.recordEventData('1','click_agreement','点击相关协议');
                        $(pageId + " .agreement_layer").show();
                    });

                     //关闭支付签约弹窗
                    appUtils.bindEvent($(pageId +" #zfqy .new_close_btn"), function () {
                         $(_pageId + " #zfqy").hide();
                     });
                    //关闭协议
                    appUtils.bindEvent($(pageId + " .new_close_btn"), function (e) {
                        
                        e.preventDefault();
                        e.stopPropagation();
                        tools.recordEventData('1','new_close_btn','关闭协议');
                        $(pageId + " .agreement_layer").hide();
    
                    });
                    //查看PDF文件
                    appUtils.preBindEvent($(pageId + " .agreement_list"), ".xy", function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                        tools.recordEventData('1','xy','查看PDF文件');
                        let url = $(this).attr("url");
                        // 正则表达式匹配常见的 URL 格式，支持 .html 后缀
                        // const pattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?\.html$/;
                        const pattern = /^https?:\/\/([\da-z-]+\.)*[a-z]{2,6}(?:\/[\w\.-]*)*\.html$/i;
                        if(pattern.test(url)){
                            let setParam = {
                                "funcNo": "50115",
                                "moduleName": "mall",
                                "url": url,
                                // "statusColor":"#FFFFFF",
                                "title": "内容查看",
                                "titleColor": "#3f4e59",
                                "btnColor": "#3f4e59",
                                "isSupportSwipingBack": "1",
                                "isTKH5": "0",
                                "btnMode": "2",
                                "navbarImage": "tk_nav_bg",
                                "leftBtnColor": "#3f4e59",
                                "rightBtnColor": "#FB0005",
                                "isShowShareBtn":'0',
                                "progressColor":"#0000"
                            };
                            let phoneType = gconfig.platform;    //获取用户手机类型 1安卓 2IOS
                            if (phoneType == '2' || phoneType == '5') setParam.statusColor = '#FFFFFF';
                            return external.callMessage(setParam);
                        }
                        let title = '内容查看'
                        let statusColor = '#2F4D80'
                        let titleColor = '#ffffff'
                        var param = {};
                        param["funcNo"] = "50240";
                        param["url"] = url;
                        param["title"] = title;
                        param["statusColor"] = statusColor;
                        param["titleColor"] = titleColor;
                        // param["isShowDownLoadBtn"] = '1'
                        if (isShowDown && platform == '2') {
                            param["rightBtnTxt"] = 'tk_webview_download';
                            param["rightBtnMode"] = '1';
                            param["rightBtnAction"] = "note_download_pdf";
                        }
                        if ($(this).html().indexOf('隐私') > -1) {
                            param.rightBtnActionParam = {
                                promtpText: '撤回同意协议将自动退出晋金财富APP,是否执行撤回操作?',
                            };
                            param.rightBtnAction = 'undoPrivacy';
                            param.rightBtnTxt = '撤回';
                            param.rightBtnTxt = 'tk_plugin_50240_privacyMenu';
                            param.rightBtnMode = '1';
                            param.goBackMode = '1';
                            param.rightBtnAction = 'privacyMenu';
                            param.rightBtnActionParam.downloadUrl = url;
                            param.url = 'https://m.xintongfund.com/m/mall/index.html#!/privacyAgreement/index.html';
                            if(platform == '1'){
                                param.suffix = 'html';
                            }
                        }
                        require("external").callMessage(param);
                    }, "click");
                    //勾选协议
                appUtils.preBindEvent($(pageId + " .agreement"), ".agreement2", function (e) {
                    e.stopPropagation();
                    e.preventDefault();
                    tools.recordEventData('1','agreement2','勾选协议');
                    var _self = this;
                    if ($(this).find("i").hasClass("active")) {
                        $(this).find("i").removeClass("active")
                    } else {
                        if (fund_code == "000709" && agreement_id_pay.length == 0) {
                            $(this).find("i").addClass("active");
                            return;
                        }
                        if (fund_code == "000709" && agreement_id_pay.length > 0) { //晋金宝 协议支付
                            agreement_id = agreement_id_pay; //只签署支付协议
                        }
                        if (agreement_id.length == 0) {
                            layerUtils.iAlert("该产品未配置协议");
                        } else {
                            //银行卡定投新增产品ID
                            let newParam = {
                                agreement_id: agreement_id.join(","),
                                fund_code: fund_code
                            }
                            service.reqFun106020(newParam, function (datas) {
                                if (datas.error_no == "0") {
                                    var results = datas.results;
                                    if (datas.length == 0) {
                                        layerUtils.iAlert("网络繁忙，请稍后重试");
                                        return;
                                    }
                                    var agreement_sign_no = results[0].agreement_sign_no;
                                    $(_self).find("i").addClass("active");
                                    $(pageId + " .agreement2").attr("agreement_sign_no", agreement_sign_no)
                                } else {
                                    layerUtils.iAlert(datas.error_info);
                                }
                            })
                        }

                    }
                }, 'click');
                }else{
                        layerUtils.iAlert(data.error_info);                        
                }
               
                // console.log(html)
                
            });
        },
        /**
         * 功能：获取银行协议内容，并调用原生打开协议
         * @param obj 若为字符串，表示协议类型，若为对象，表示入参集  M
         * @param other1 银行渠道编码 M
         * @param other2 产品编码 N
         * @param other3 绑定银行卡号 N
         * @param other4 银行卡编码 N
         * */
        getBankPdf: function (obj, other1, other2, other3, other4) {
            var agreement_type, //协议类型
                prod_code, //产品Code
                bank_channel_code, //银行渠道名称
                acct_no, //绑定银行卡号
                bank_code; //银行编码
            if (typeof obj === "object") { //入参为对象
                agreement_type = obj.agreement_type;
                prod_code = obj.prod_code;
                bank_channel_code = obj.bank_channel_code;
                acct_no = obj.acct_no;
                bank_code = obj.bank_code;
            } else if (typeof obj === "string") { //入参为字符串,并且根据产品查协议
                agreement_type = obj;
                bank_channel_code = other1;
                prod_code = other2;
                acct_no = other3;
                bank_code = other4;
            }
            var pageId = "#" + $("body .page[data-display='block']").attr("id");
            $(pageId + " .agreement_layer").hide();
            if ($(pageId + ' .agreement').length > 0) { //生成协议文字
                var agrStr = '<div class="agreement2">\n' +
                    '             <i class=""></i>\n' +
                    '             <div style="margin-left: 0.2rem;color: #333;">我已仔细阅读并同意签署<span class="m_agreement_color click_agreement">《相关协议》</span></div>' +
                    '</div>'
                $(pageId + " .agreement").html(agrStr);
            }
            var param = {
                agreement_type: agreement_type,
                prod_code: prod_code,
                bank_channel_code: bank_channel_code,
                acct_no: acct_no,
                bank_code: bank_code,
            };
            if (prod_code) { //产品传子业务码
                param.agreement_sub_type = agreement_type;
            } else { //平台传业务吗
                param.agreement_type = agreement_type;
            }
            //获取协议
            service.reqFun151120(param, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return
                }
                var html = "";
                for (var i = 0; i < data.results.length; i++) {
                    html += '<li href="javascript:void(0);" class="xy" url="' + global.oss_url + data.results[i].url + '">';
                    html += "<span>《" + data.results[i].agreement_title + "》</span>";
                    html += '</li>';
                }
                $(pageId + " .agreement_list").html(html);
                //点击相关协议
                appUtils.bindEvent($(pageId + " .click_agreement"), function (e) {
                    // e.preventDefault();
                    // e.stopPropagation();
                    tools.recordEventData('1','click_agreement','点击相关协议');
                    $(pageId + " .agreement_layer").show();

                });
                //关闭协议
                appUtils.bindEvent($(pageId + " .new_close_btn"), function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    tools.recordEventData('1','new_close_btn','关闭协议');
                    $(pageId + " .agreement_layer").hide();

                });
                //查看PDF文件
                appUtils.preBindEvent($(pageId + " .agreement_list"), ".xy", function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    tools.recordEventData('1','xy','查看PDF文件');
                    var url = $(this).attr("url");
                    let title = '内容查看'
                    let statusColor = '#2F4D80'
                    let titleColor = '#2F4D80'
                    var param = {};
                    param["funcNo"] = "50240";
                    param["url"] = url;
                    param["title"] = title;
                    param["statusColor"] = statusColor;
                    param["titleColor"] = titleColor;
                    require("external").callMessage(param);
                }, "click");
                //勾选协议
                appUtils.preBindEvent($(pageId + " .agreement"), ".agreement2", function (e) {
                    e.stopPropagation();
                    e.preventDefault();
                    tools.recordEventData('1','agreement2','勾选协议');
                    if ($(this).find("i").hasClass("active")) {
                        $(this).find("i").removeClass("active")
                    } else {
                        $(this).find("i").addClass("active")
                    }
                }, 'click');
            });
        },
        /**
         * 功能：获取其它协议内容，并调用原生打开协议（不提前获取协议，点击时获取并打开）
         * @param obj 若为字符串，表示协议类型，若为对象，表示入参集  M
         * 14-个人信息收集清单
         * 15-第三方共享清单
         * 16-隐私摘要
         * */
        getOtherPdf: function (obj, e) {
            var param = {
                agreement_type: obj, //协议类型
            };
            //获取协议
            service.reqFun102016(param, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return
                }
                var results = data.results[0];
                e.preventDefault();
                e.stopPropagation();
                var url = global.oss_url + results.url;
                let title = '内容查看'
                let statusColor = '#2F4D80'
                let titleColor = '#ffffff'
                var param = {};
                param["funcNo"] = "50240";
                param["url"] = url;
                param["title"] = title;
                param["statusColor"] = statusColor;
                param["titleColor"] = titleColor;
                require("external").callMessage(param);
            })
        },
        //空数据展示--
        FormatNull: function (param) {
            if (param == 0) {
                param = param + "";
            }
            if (!param) {
                return "--";
            } else if (param == "-") {
                return "--";
            } else if (param == "null") {
                return "--";
            } else if (Array.isArray(param)) {
                for (var i = 0; i < param.length; i++) {
                    if (!param[i] || param[i] == "-" || param[i] == "null") {
                        param[i] = "--";
                    }
                }
            } else if (param instanceof Object) {
                for (var key in param) {
                    if (!param[key] || param[key] == "-" || param[key] == "null") {
                        param[key] = "--";
                    }
                }
            }
            return param;
        },
        //转换基金类型
        transformFundType: function (type) {
            var prod_type_obj = {
                "00": "综合性",
                "10": "股票型",
                "20": "混合型",
                "30": "债券型",
                "40": "货币型",
                "50": "商品型",
                "60": "FOF 型",
                "70": "QDII 型",
                "80": "其它"
            }
            return prod_type_obj[type];
        },
        //取小数  默认保存两位
        saveDecimal: function (num, places) {
            var change = Number(num);
            if (!places) {
                places = 2;
            }
            if (typeof change === 'number' && !isNaN(change)) {
                return change.toFixed(places);
            } else {
                return num;
            }
        },
        //公募跳转购买页
        jumpBuyPage: function (page_code, prod_sub_type, prod_sub_type2) {
            sessionStorage.vip_buttonShow = true;
            //20 大集合 ，30晋金30天，50债基
            var obj = {
                "10": "inclusive/moneytaryPurchase",
                "20": "thfund/gatherBuy",
                "30": "inclusive/jjThirtyBuy",
                "50": {
                    "00": "thfund/bondBuy",
                    "01": "inclusive/bondFixBuy",
                    "52": "inclusive/bondFixBuy",
                }
            }
            if (prod_sub_type2 == "200") {
                appUtils.pageInit(page_code, "template/templateBuy");
                return;
            }
            if (prod_sub_type == "50") {
                if (prod_sub_type2 && prod_sub_type2 != "--") {
                    appUtils.pageInit(page_code, obj[prod_sub_type][prod_sub_type2]);
                    return;
                }
                appUtils.pageInit(page_code, "thfund/bondBuy");
            } if (prod_sub_type == "52") {
                appUtils.pageInit(page_code, "inclusive/bondFixBuy");
            } else {
                appUtils.pageInit(page_code, obj[prod_sub_type]);
            }
        },
        //公募判断跳转详情页
        jumpDetailPage: function (page_code, prod_sub_type, prod_sub_type2, params) {
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(page_code, "template/publicOfferingDetail", params);
            //20 大集合 ，30晋金30天，50债基
            // console.log(page_code, prod_sub_type, prod_sub_type2);
            // var obj = {
            //     "10": "inclusive/moneytaryDetail",
            //     "20": "thfund/gatherDetail",
            //     "30": "inclusive/jjThirtyDetail",
            //     "50": {
            //         "00": "thfund/bondDetail",
            //         "01": "inclusive/bondFixDetail",
            //         "52": "inclusive/holdsDetail",
            //     }
            // }
            // if (prod_sub_type == "50") {
            //     if (prod_sub_type2 && prod_sub_type2 != "--") {
            //         appUtils.pageInit(page_code, obj[prod_sub_type][prod_sub_type2]);
            //         return;
            //     }
            //     appUtils.pageInit(page_code, "thfund/bondDetail");
            // } else {
            //     appUtils.pageInit(page_code, obj[prod_sub_type]);
            // }
        },
        //私募判断跳转详情页
        jumpPriDetailPage: function (page_code, prod_sub_type2, params) {
            var obj = {
                "81": "highEnd/productPolicyDetail",//一创小集合
                "90": "highEnd/productDetail",//普通私募
                "91": "highEnd/smallgatherDetail",//小集合
                "92": "highEnd/productPolicyDetail",//私募定开
                "93": "yuanhui/productDetail", //源晖
				"94": "highEnd/productLockDetail",//私募 持有期
                "95": "highEnd/productPolicyDetail",//私募 政金债
                "96": "highEnd/productPolicyDetail",//私募 申港小集合
                "97": "highEnd/productPolicyDetail",//普信定开
                "100": "template/heighEndProduct",//产品整合相关
            }
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(page_code, obj[prod_sub_type2], params);
        },
        // 跳转产品营销页
        jumpMarketingPage: function (page_code, prod_sub_type2, params) {
            var obj = {
                "100": "template/marketing",
                "200": "template/publicMarketing"
            }
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(page_code, obj[prod_sub_type2], params);
        },
        //判断使用哪个银行图标
        judgeBankImg: function (bank_channel_code) {
            var obj = {
                "Y01": {
                    "icon": "images/zbicon.png",
                    "logo": "images/zblogo.png",
                    "logo2": "images/zblogo2.png",
                },
            }
            return obj[bank_channel_code];
        },
        //数据字典  晋金宝交易状态数据字典
        fundDataDict: function (value, type) {
            var data = {
                // 交易子业务吗
                sub_busi_name: {
                    "12201": "充值",
                    "12203": "汇款充值",
                    "12401": "实时取现",
                    "12402": "普通取现",
                    "12002": "购买",
                    "143": "分红",
                    "121": "购买",
                    "120": "购买",
                    "12102": "购买",
                    "12302": "购买",
                    "12204 ": "转入",
                    "12407 ": "转出",
                },
                // 交易状态
                trans_status_name: {
                    "0": "受理成功", //可撤单
                    "2": "撤单中",
                    "1": "受理失败",
                    "3": "交易成功",
                    "4": "交易失败",
                    "6": "已撤单", //私募撤单成功
                    "7": "确认失败", // 私募撤单失败
                    "8": "确认成功",
                    "9": "确认失败",
                    "10": "交易成功"
                },
                // 公募产品交易状态
                pub_trans_status_name: {
                    "0": "受理成功",
                    "1": "受理失败",
                    // "2": "撤单中",
                    "3": "待确认",
                    "4": "交易失败",
                    "8": "已确认",
                    "9": "确认失败",
                },
                // 公募产品-申购交易状态
                pub_purchase_status_name: {
                    "0": "受理成功",
                    "1": "受理失败",
                    "2": "撤单中",
                    "3": "待确认",
                    "4": "交易失败",
                    "7": "撤单失败",
                    "8": "已确认",
                    "9": "确认失败",
                    "6": "已撤单", //私募撤单成功
                },
                // 公募产品-赎回交易状态
                pub_redeem_status_name: {
                    "0": "待确认",
                    "1": "受理失败",
                    "2": "撤单中",
                    "3": "待入账",
                    "4": "交易失败",
                    "8": "已入账",
                    "6": "已撤单",
                    "9": "确认失败",
                    "7": "撤单失败",
                },
                // 公募产品-兑付到宝交易状态
                pub_cashJJB_status_name: {
                    "0": "受理成功",
                    "1": "受理失败",
                    // "2": "撤单中",
                    "3": "待入账",
                    "4": "交易失败",
                    "8": "已入账",
                    "9": "确认失败",
                },
                // 公募产品-分红到宝交易状态
                pub_profitJJB_status_name: {
                    "0": "受理成功",
                    "1": "受理失败",
                    // "2": "撤单中",
                    "3": "待入账",
                    "4": "交易失败",
                    "8": "已入账",
                    "9": "确认失败",
                },
                // 公募产品-修改分红方式交易状态
                pub_updateDivide_status_name: {
                    "0": "受理成功",
                    "1": "受理失败",
                    // "2": "撤单中",
                    "3": "待确认",
                    "4": "交易失败",
                    "8": "已确认",
                    "9": "确认失败",
                },
                // 公募产品-修改到期方式交易状态
                pub_updateExpire_status_name: {
                    "0": "受理成功",//可以撤单
                    "1": "受理失败",
                    // "2": "撤单中",
                    "3": "待确认",  //可以撤单
                    "4": "交易失败",
                    "8": "确认成功",
                    "9": "确认失败",
                },
                // 私募产品-交易状态
                pri_trans_status_name: {
                    "0": "受理成功", //可以撤单
                    "1": "受理失败",
                    "2": "撤单中",
                    "3": "待确认", //可以撤单
                    "4": "交易失败",
                    "6": "已撤单",
                    "7": "撤单失败",
                    "8": "已确认",
                    "9": "确认失败",
                },
                // 私募产品-交易状态 強赎 | 分红
                pri_bonus_trans_status_name: {
                    "0": "受理成功",
                    "1": "受理失败",
                    "2": "撤单中",
                    "3": "待入账",
                    "4": "交易失败",
                    "6": "已撤单",
                    "7": "撤单失败",
                    "8": "已入账",
                    "9": "确认失败",
                },
                // 私募产品-赎回交易状态
                pri_redeem_status_name: {
                    "0": "待确认",
                    "1": "受理失败",
                    "2": "撤单中",
                    "3": "待入账",
                    "4": "交易失败",
                    "6": "已撤单",
                    "7": "撤单失败",
                    "8": "已入账",
                    "9": "确认失败",
                },
                // 分红方式
                dividend_method: {
                    "14301": "红利再投",
                    "14302": "分红到银行卡", //绑卡
                    "14303": "分红到晋金宝"
                },
                // 调仓状态
                tc_trans_status_name: {
                    "0": "调仓中",
                    "8": "调仓结束",
                    "9": "调仓失败"
                },
                comb_qs_trans_status_name: {
                    "3": "已确认",
                    "8": "已入账"
                }
            }

            return data[type][value];
        },
        /**
         * 选择插件，
         * @params trigger 触发id 必填
         * @params title 标题
         * @params dataArr 数据源,需要显示的数据 必填
         * @params position 位置
         * @params callback 回调函数 必填
         * 页面销毁必须移除
         * */
        mobileSelect: function (params) {
            if (params.position && params.position >= 0) {
                params.position = params.position * 1
            } else {
                params.position = 0
            }
            new MobileSelect({
                trigger: params.trigger,
                title: params.title || "",
                wheels: [
                    { data: params.dataArr }
                ],
                position: [params.position], //初始化定位 打开时默认选中的哪个 如果不填默认为0
                transitionEnd: function (indexArr, data) {
                },
                callback: function (indexArr, data) {
                    params.callback(data, indexArr);
                }
            });
        },
        /*
        * 获取手机系统模式
        * */
        getSystemMode: function (_pageId) {
            if (gconfig.platform == "2" || gconfig.platform == "5") {
                var param50125 = {
                    "moduleName": "mall",
                    "funcNo": "50125"
                };
                var data = require("external").callMessage(param50125); //获取当前系统模式
                var param50119 = {
                    "moduleName": "mall",
                    "funcNo": "50119"
                }
                if (data.error_no == "0") {
                    if (data.results[0].theme == "theme2") { //暗黑模式
                        param50119.color = "#000000"; //背景色
                        param50119.style = "1"; //字体颜色 0黑色 1 白色
                        $(_pageId).addClass("blackMode");
                    } else {
                        param50119.color = "#ffffff";
                        param50119.style = "0";
                        $(_pageId).removeClass("blackMode");
                    }
                    require("external").callMessage(param50119);
                }

            }
        }
        ,
        /*
         * 渲染利率
         * */
        addMinusClass: function (rate) {
            var colorStr = "";
            if (rate > 0) {
                colorStr = "text_red";
            } else if (rate < 0) {
                colorStr = "text_green";
            } else {
                colorStr = "text_gray";
            }
            return colorStr
        },
        /*
        * @param buy_state 购买状态
        * @param prod_sub_type2  产品子类型
        * @param noClassName 置灰按钮样式
        * */
        priBtnObj: function (buy_state, prod_sub_type2, noClassName,userChooseVerson,scene_code) {
            noClassName = noClassName || "sold_out";
            var expectClassName = noClassName == "no_active" ? "no_active" : "";
            //获取当前用户选中的版本
            // let userChooseVerson = common.getLocalStorage("userChooseVerson") ? common.getLocalStorage("userChooseVerson") : ''; //判断用户是否登陆过
            // //获取当前用户版本号
            // let scene_code = common.getLocalStorage("scene_code") ? common.getLocalStorage("scene_code") : ''; //页面版本类型 1标准版 X版
            //获取用户当前切换的版本
            // let chooseVerson = appUtils.getSStorageInfo("chooseVerson") ? appUtils.getSStorageInfo("chooseVerson") : '';
            if (buy_state == "1") return { btnClass: "", btnText: "购买" };
            if (buy_state == "2") return { btnClass: "", btnText: "预约" };
            if (buy_state == "3"){
                //判断当前版本
                let buy_state_name;
                if(userChooseVerson && userChooseVerson !=''){
                    buy_state_name =  userChooseVerson == '3' ? `敬请<br>期待` : '敬请期待'
                }else{
                    buy_state_name =  scene_code == '3' ? `敬请<br>期待` : '敬请期待'
                }
                return { btnClass: expectClassName, btnText: buy_state_name };
            } 
            if (buy_state == "4" && (prod_sub_type2 == "91" || prod_sub_type2 == "92" || prod_sub_type2 == "93")) {  //小集合||定开||源晖展示封闭中
                return {
                    btnClass: noClassName,
                    btnText: "封闭中"
                };
            }
            return { btnClass: noClassName, btnText: "售罄" };
        },
        /** 数字金额大写转换(可以处理整数,小数,负数) */
        numtoCN: function (n) {
            if (!n || n == '--' || n == '' || n == 'undefined') return
            n = n.replace(",", "");
            if (n.indexOf(".") == 0 && n.length == 1) return; //如果只有小数点，不做格式化
            var fraction = ['角', '分'];
            var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            var unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
            var head = n < 0 ? '欠' : '';
            n = Math.abs(n);
            var s = '';
            for (var i = 0; i < fraction.length; i++) {
                s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
            }
            s = s || '整';
            n = Math.floor(n);

            for (var i = 0; i < unit[0].length && n > 0; i++) {
                var p = '';
                for (var j = 0; j < unit[1].length && n > 0; j++) {
                    p = digit[n % 10] + unit[1][j] + p;
                    n = Math.floor(n / 10);
                }
                s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
            }
            return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
        },

        /**
         * 是否满足递增金额
         * @param curAmt 当前金额
         * @param thresholdAmount 起投金额 || 追加
         * @param additionAmt 递增金额
         **/
        isMatchAddAmt: function (curAmt, thresholdAmount, additionAmt) {
            if (curAmt && thresholdAmount && additionAmt && additionAmt > 0
                && new BigNumber(curAmt).gte(thresholdAmount)
                && !(new BigNumber(curAmt).modulo(additionAmt).isZero())) { // 当前金额 > 起投金额 && 当前金额 % 递增金额 == 0
                layerUtils.iAlert("递增金额为" + tools.fmoney(additionAmt) + "元");
                return true;
            }
            return false
        },

        /*
        * 晋金所转入晋金财富 白名单+晋金所晋金宝开户
        * */

        whiteList: function (_pageId) {
            return $(_pageId + " .jjs_yue").hide();
            var userInfo = ut.getUserInf();
            service.reqFun101071({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var isWhiteListUser = data.results[0].ht_white_state; // 否互通白名单用户 0否 1是
                if (isWhiteListUser == "0") {
                    $(_pageId + " .jjs_yue").hide();
                    return;
                }
                service.reqFun177003({
                    bank_leave_phone: userInfo.bankReservedMobile,
                    card_no: userInfo.bankAcct,
                    user_name: userInfo.name,
                    identity_num: userInfo.identityNum
                }, function (data) {
                    if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    if (data.results[0].isBind == "0") {
                        $(_pageId + " .jjs_yue").hide();
                        return;
                    } else {
                        $(_pageId + " .jjs_yue").show();
                    }
                }, { isLastReq: false })
            })
        },


        /*
        * 晋金所转入晋金财富前判断是否是互通用户
        * 判断四要素一致和是否做过风险测评
        * */
        intercommunication: function (_pageCode) {
            var userInfo = ut.getUserInf();
            service.reqFun177003({
                bank_leave_phone: userInfo.bankReservedMobile,
                card_no: userInfo.bankAcct,
                user_name: userInfo.name,
                identity_num: userInfo.identityNum
            }, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var result = data.results[0];
                appUtils.setSStorageInfo("jjs_cust_no", result.jjsCustNo);
                if (result.isBind == "0") {
                    layerUtils.iLoading(false)
                    layerUtils.iAlert("您还未在晋金所绑卡");
                    return;
                }

                if (result.isMatching == "0") { //isMatching 是否四要素匹配 0否 1是
                    layerUtils.iLoading(false)
                    layerUtils.iAlert("银行卡一致方可使用此功能");
                    return;
                }
                if (result.isRiskTest == "0") { //是否做过风险测评 0否 1是
                    layerUtils.iLoading(false)
                    layerUtils.iConfirm("您还未在晋金所进行风险测评", function () {
                        appUtils.pageInit(_pageCode, "jjsfund/riskQuestion");
                    }, function () {
                    }, "去测评", "取消");
                    return;
                }
                service.reqFun177004({ jjs_cust_no: appUtils.getSStorageInfo("jjs_cust_no"), func_no: "901503" }, function (resultVo) {
                    if (resultVo.error_no != "0") {
                        layerUtils.iLoading(false)
                        layerUtils.iAlert(resultVo.error_info);
                        return;
                    }
                    var dataList = resultVo.results[0];
                    var cardStatus = dataList.cardStatus;
                    var subState = dataList.subState;
                    if (cardStatus == "1") {
                        if (subState == "0" || subState == "1") {
                            layerUtils.iAlert("换卡期间不允许交易");
                            return;
                        }
                    }
                    appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
                }, { isLastReq: false })


            })

        },
        /**
         * 
         * @param {*} _pageCode 
         * @returns 
         */
        //去测评
        pageTo_evaluation: function (_pageCode) {
            return layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
                appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
            }, '', '确定')
        },

        // 投顾去答题
        pageTo_comb_evaluation: function (_pageCode) {
            return layerUtils.iAlert("您的答题评估已到期，请重新答题", () => { }, () => {
                appUtils.pageInit(_pageCode, "combProduct/combRiskQuestion", {});
            }, '', '确定')
        },
        /**
         * 判断断用户是否为C0 只可购买 R1产品 其他产品弹窗
         * @param {*} productInfo 产品信息
         * @param {*} userInfo    用户信息
         * @returns
         */
        is_show_c0: function (_pageCode) {
            let info = ut.getUserInf();
            // let riskLevel = userInfo.riskLevel
            // let risk_level = productInfo.risk_level
            return layerUtils.iConfirm("您的风险承受能力为" + info.riskName + "，此产品超过了您的风险承受能力，若仍选择投资，请重新测评。", () => { }, () => {
                appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
            }, '取消', '重新测评');
        },
        /**
         * 根据省市区进行判定，选中是否合规
         * @param {*} province_code 省代码
         * @param {*} city_code    市代码
         * @param {*} county_code    县区代码
         * @param {*} city    总的城市列表JSON
         * @returns
         */
        is_region: function (province_code, city_code, county_code, city) {
            let flag1, flag2, flag3
            return new Promise((resolve) => {
                let pro_list = city.filter((item) => {
                    if (province_code == item.code) {
                        flag1 = true
                        return item.sub
                    } else {
                        return null
                    }
                });
                let city_list = pro_list[0].sub.filter((item) => {
                    if (city_code == item.code) {
                        flag2 = true
                        return item.sub
                    } else {
                        return null
                    }
                })
                if (city_list[0] && city_list[0].sub && city_list[0].sub.length) {
                    let county_list = city_list[0].sub.filter((item) => {
                        if (county_code == item.code) {
                            flag3 = true
                            return item.sub
                        } else {
                            return null
                        }
                    })
                } else {
                    flag3 = true
                }
                if (flag1 && flag2 && flag3) {
                    resolve(true)
                } else {
                    resolve(false)
                }
            })
        },
        /**
         * 全局判断是否有交易后领积分活动
         * @param {*} pageId 当前父级元素
         * @param {*} pageUrl 当前页面
         * @param {sharePro} 1分享 2领积分
         */
        getActivityInfo: function (pageId, pageUrl) {
            let userType = ut.getUserInf().custLabelCnlCode
            if (!userType || userType == '' || userType == 'yh_jjdx') {
                service.reqFun108013({}, function (data) {
                    let html = '<ul><li>恭喜您获得随机积分</li><li>分享即可领取</li></ul>'
                    $(pageId + ' .transactionReward ul').html(html)
                    $(pageId + ' .transactionReward button').html('立即分享')
                    sessionStorage.sharePro = 1
                    if (data.error_no == 0) {
                        let results = data.results[0]
                        results.oss_url = global.oss_url + '/' + results.oss_url    //图片真实地址
                        //判断活动存在 && 用户未参与过
                        if (results.hasFirstTimeTradePerDayAct == '1' && results.is_join_act == '0') {
                            $(pageId + ' .transactionReward').css("background-image", "url(" + results.oss_url + ")")
                            $(pageId + ' .transactionReward').show()
                        } else {
                            $(pageId + ' .transactionReward').hide()
                        }
                        //微信好友
                        appUtils.bindEvent($(pageId + " #share_WeChat"), function () {
                            tools.recordEventData('1','share_WeChat','微信好友');
                            common.share("22", results.share_template);
                            tools.getPoints(pageId, pageUrl, results.act_id)

                        });
                        //微信朋友圈
                        appUtils.bindEvent($(pageId + " #share_WeChatFriend"), function () {
                            tools.recordEventData('1','share_WeChatFriend','微信朋友圈');
                            common.share("23", results.share_template,);
                            tools.getPoints(pageId, pageUrl, results.act_id)
                        });
                        //取消邀请好友
                        appUtils.bindEvent($(pageId + " #cancelShare"), function () {
                            tools.recordEventData('1','cancelShare','取消邀请好友');
                            $(pageId + " #pop_layer").hide();
                        });
                        appUtils.bindEvent($(pageId + " .transactionReward"), function () {
                            tools.recordEventData('1','transactionReward','开始分享');
                            if (sessionStorage.sharePro == 1) {
                                //开始进行分享,弹出分享弹窗
                                $(pageId + " #pop_layer").show()
                            } else if (sessionStorage.sharePro == 2) {
                                //已经领取积分跳转营销页面
                                appUtils.pageInit('login/userIndexs', "vipBenefits/index");
                            }
                            // 
                        })
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        },
        /**
         * 每日首次交易领取积分
         */
        getPoints: function (pageId, pageUrl, act_id) {
            service.reqFun108014({ act_id: act_id }, function (data) {
                if (data.error_no == 0) {
                    setTimeout(() => {
                        let points_amount = data.results[0].points_amount ? data.results[0].points_amount : '100'
                        let html = '<span>恭喜您获得' + points_amount + '积分</span>'
                        $(pageId + ' .transactionReward ul').html(html)
                        $(pageId + ' .transactionReward button').text('立即查看')
                        layerUtils.iAlert('分享成功');
                        sessionStorage.sharePro = 2
                        $(pageId + " #pop_layer").hide()
                    }, 5000);
                } else {
                    setTimeout(() => {
                        layerUtils.iAlert(data.error_info);
                    }, 5000);
                }
            });
        },
        /**
         * 获取 认证步骤页信息
         */
        getStepsInfo: function (fund_code) {
            service.reqFun101032({ fund_code: fund_code }, function (data) {
                if (data.error_no == 0) {
                    appUtils.setSStorageInfo("stepsInfo", data.results[0]); //产品信息
                } else {

                }
            });
        },
        /**
         * 
         * @param {*} _pageId 主页面ID
         * @param {*} data 调用接口参数
         */
        fileImg: function (_pageId, data) {
            let external = require("external");
            let phoneType = gconfig.platform    //获取用户手机类型 1安卓 2IOS
            let str = '<div class="pop_layer camera_pop_layer">\n' +
                '            <div  class="slideup in">\n' +
                '                <div class="camera">拍照</div>\n' +
                '                <div class="album">从相册中选取</div>\n' +
                '                <div class="cancel_camera">取消</div>\n' +
                '            </div>\n' +
                '        </div>'

            if (phoneType == '1') {   //安卓
                external.callMessage(data);
            } else {  //IOS
                if ($(_pageId + " .camera_pop_layer").length > 0) {
                    $(_pageId + " .camera_pop_layer").show();
                } else {
                    $(_pageId).append(str);
                }
                //调用相册
                appUtils.bindEvent($(_pageId + " .album"), function () {
                    tools.recordEventData('1','album','相册');
                    data["mode"] = 1
                    external.callMessage(data);
                });
                //调用相机
                appUtils.bindEvent($(_pageId + " .camera"), function () {
                    tools.recordEventData('1','camera','相机');
                    data["mode"] = 2
                    external.callMessage(data);
                });
                //关闭弹框
                appUtils.bindEvent($(_pageId + " .camera_pop_layer"), function (e) {
                    e.stopPropagation();
                    tools.recordEventData('1','camera_pop_layer','关闭弹框');
                    $(_pageId + ' .camera_pop_layer').hide();
                });
                appUtils.bindEvent($(_pageId + " .cancel_camera"), function (e) {
                    tools.recordEventData('1','cancel_camera','关闭弹框');
                    $(_pageId + ' .camera_pop_layer').hide();
                });
            }
        },
        /**
         * 
         * @param {*} page 页面名称
         * @param {*} button_name 按钮名称
         * @param {*} source 来源 app web
         * @param {*} activity_id 活动ID
         * @param {*} channel_code 渠道
         * @cust_no {*} 客户号
         */
        clickPoint(pageId, page, button, activity_id, source, channel_code, cust_no) {
            let param = {
                page: page,
                source: source ? source : 'app',
                activity_id: activity_id ? activity_id : '',
                cust_no: cust_no
            }
            //web端渠道取链接上的字段
            if (source == 'web') {
                param.channel_code = channel_code
            } else {
                let channel_code = common.getLocalStorage("download_channel_code");
                param.channel_code = channel_code ? channel_code : 'jjdx';
            }
            //点击下载按钮时
            if (button == 'download') {
                param.button = 'download';
            } else {
                param.button = pageId ? $(pageId + " #" + button).attr('clickName') : '';
            }
            // service.reqFun101080(param)
        },
        /**
         * 是否展示撤单按钮
         * @param {pageId} 页面ID
         * @param {value} 产品子代码
         * @param {type} 产品交易状态
         * @param {its_date} 撤单日期
         * @param {its_flag} 撤单标识(0 允许撤单  1 不允许撤单 2天风特殊情况无法撤单)
         * @prod_sub_type2 {prod_sub_type2} 区分公募私募
         * @trs_state 0赎回 1其他
         */
        isShowCancelorderBtn(pageId, value, type, its_date, its_flag, prod_sub_type2, trs_state) {
            // console.log(pageId, value, type, its_date, its_flag ,prod_sub_type2);
            // console.log(pageId)
            if (its_flag == '2') return; //天风特殊处理不允许撤单
            if (type == "pub_redeem_status_name" || type == 'pub_cashJJB_status_name' || type == "pub_profitJJB_status_name" || type == "pri_bonus_trans_status_name" || type == "pri_redeem_status_name") {
                //展示撤单按钮
                if (value == '0') {
                    if (its_flag == '0') {
                        $(pageId + " #cancelorderBtn").show()
                    } else if (its_flag == '1') {
                        $(pageId + " #overtimeBtn").show()
                    } else {
                        $(pageId + " #cancelorderBtn").hide()
                        $(pageId + " #overtimeBtn").hide()
                    }
                } else {
                    $(pageId + " #cancelorderBtn").hide()
                    $(pageId + " #overtimeBtn").hide()
                }
            }
            if (type == "pub_trans_status_name" || type == "pri_trans_status_name" || type == 'trans_status_name' || type == "pub_updateExpire_status_name" || type == 'pub_purchase_status_name' || type == "pub_updateDivide_status_name") {
                //展示撤单按钮
                if (value == '0' || value == '3') {
                    if (its_flag == '0') {
                        $(pageId + " #cancelorderBtn").show()
                    } else if (its_flag == '1') {
                        $(pageId + " #overtimeBtn").show()
                    } else {
                        $(pageId + " #cancelorderBtn").hide()
                        $(pageId + " #overtimeBtn").hide()
                    }
                } else {
                    $(pageId + " #cancelorderBtn").hide()
                    $(pageId + " #overtimeBtn").hide()
                }
            }
            appUtils.bindEvent($(pageId + " #overtimeBtn"), function () {
                // tools.recordEventData('1','overtimeBtn','撤单');
                layerUtils.iAlert("已超过撤单时间");
            });
            // //关闭协议
            // appUtils.bindEvent($(pageId + " .close_btn"), function (e) {
            //     $(pageId + " .pop_layer").hide();
            // });
            // appUtils.bindEvent($(pageId + " #cancelorderBtn"), function () {
            //     layerUtils.iConfirm("<span style='color:#508cee'>" + tools.FormatDateText(its_date.substring(4, 8)) + "14:00</span>前可以撤单", function () {
            //         if(prod_sub_type2 == '200'){
            //             let money = $(pageId + " .money").text();
            //             let trans_pay = $(pageId + " #trans_pay").text();
            //             let str;
            //             if(trs_state == '0'){
            //                 str = `赎回申请将撤销`
            //             }else{
            //                 str = `申请撤单，资金<em>${money}</em>将退回至<em>${trans_pay}</em>`
            //             }
            //             $(pageId + " #rechargeInfo").html(str);
            //             $(pageId + " .pop_layer").show();
            //         }else{
            //             let param = {};
            //             param["funcNo"] = "50220";
            //             param["telNo"] = require("gconfig").global.custServiceTel;
            //             param["callType"] = "0";
            //             require("external").callMessage(param);
            //         }
            //     }, function () {
            //     }, "继续撤单", "取消");
            // });
            //呼出交易密码
            // appUtils.bindEvent($(pageId + " .password_input"), function () {
            //     tools.passboardEvent();
            //     var param = {};
            //     param["moduleName"] = "mall";
            //     param["funcNo"] = "50210";
            //     param["pageId"] = pageId.slice(1);
            //     param["eleId"] = "jymm";
            //     param["doneLable"] = "确定";
            //     param["keyboardType"] = "4";
            //     require("external").callMessage(param);
            // });
        },
        //交易密码
        //我的转让 正在委托
        async getManagerFlag() {
            return new Promise(async (resolve, reject) => {
                service.reqFun101075({}, (datas) => {
                    if (datas.error_no == 0) {
                        let results = datas.results;
                        resolve(results)
                    } else {
                        layerUtils.iAlert(datas.error_info);
                    }
                })
            })
        },
        /**
         * 调起客服页面
         */
        // 'https://sxfae.qiyukf.com/client?k=4f8313971eba393352c7507406f9c9d3&wp=1&robotShuntSwitch=1&robotId=1543084&shuntId=0&language=zh-cn&templateId=6148975'
        // 'https://sxfae.qiyukf.com/client?k=4f8313971eba393352c7507406f9c9d3&wp=1&robotShuntSwitch=1&robotId=1543084&shuntId=0&language=zh-cn&templateId=6148975'
        async saveAlbum(pageCode, param) {
            //鸿蒙特殊处理
            if(platform == '5' || platform == '1'){
                return appUtils.pageInit(pageCode, "guide/advertisement", { "url": 'https://sxfae.qiyukf.com/client?k=4f8313971eba393352c7507406f9c9d3&wp=1&robotShuntSwitch=1&robotId=1543084&shuntId=0&language=zh-cn&templateId=6148975',type:"kefu"});;
            }
            //判断用户是否登录 是否在8 - 20 点之间
            //获取今日8.30时间戳
            let startTime = new Date(new Date().toLocaleDateString()).getTime() + 8.5 * 60 * 60 * 1000;
            let endTime = new Date(new Date().toLocaleDateString()).getTime() + 20.5 * 60 * 60 * 1000;
            let nowTime = new Date().getTime();
            //判断当前时间是否在8.30 - 20.30
            let workFlag = startTime <= nowTime && nowTime <= endTime //是否在工作时间内
            //客户是否拥有专属经理,客服组ID
            let managerFlag, group_id, managerData, custmanager_name, mobile_phone;
            if (!ut.getUserInf()) {
                managerData = null
            } else {
                managerData = await tools.getManagerFlag()
            }
            if (managerData && managerData[0] && managerData[0].group_id) {
                managerFlag = true;
                custmanager_name = managerData[0].custmanager_name;
                group_id = managerData[0].group_id;
                mobile_phone = managerData[0].mobile_phone;
            } else {
                managerFlag = false;
                group_id = '993734';
                custmanager_name = '';
                mobile_phone = '';
            }
            let data = {
                title: '在线客服',
                funcNo: "80313",
                isAuthentication: appUtils.getSStorageInfo("isAuthentication") ? appUtils.getSStorageInfo("isAuthentication") : '2',
                groupId: group_id,
                robotId: "1543084",
                telNo: mobile_phone,
                consultantName: custmanager_name,
                showStaffType: !workFlag ? 0 : managerFlag ? 1 : 2,
                openRobotInShuntMode: "1"
            }
            if (data.isAuthentication == 0) {
                let data50503 = {
                    funcNo: "50503",
                    notiName: "notiH5ToVerify",
                    callbackFuncNo: "80317"
                }
                external.callMessage(data50503);
            }
            // $("#login_userIndexs #kefu img").removeClass('active')
            let shouldData = external.callMessage(data);
            // console.log(shouldData)
            // if (platform == 2) {  //IOS
            // appUtils.pageInit(pageCode, "customerService/onlineCustomerService", param);
            // let param50043 = {
            //     funcNo: "50043",
            //     key: "album_ios"
            // };
            // let firstInstall = external.callMessage(param50043);
            // let flag = firstInstall.results[0].value    //是否同意过
            // if (flag) {   //已经同意过
            //     appUtils.pageInit(pageCode, "customerService/onlineCustomerService", param);
            // } else {  //第一次点击
            //     layerUtils.iAlert("晋金财富想访问您的照片，允许晋金财富访问您的照片，您就可以选择或保存图片了。",  ()=> {},()=>{
            //         let params = {
            //             funcNo: "50042",
            //             key: "album_ios",
            //             isEncrypt: "1",  //存值
            //             value: true,
            //         };
            //         external.callMessage(params);
            //         appUtils.pageInit(pageCode, "customerService/onlineCustomerService",param);
            //     },'','确定')
            // }
            // } else if (platform == 1) {    //安卓
            // let param80307 = {
            //     funcNo: "80307",
            //     requestId: "1|2"
            // }
            // external.callMessage(param80307);
            // appUtils.pageInit(pageCode, "customerService/onlineCustomerService", param);
            // }
        },
        setDate(pageId) {
            let firstDate = new Date();
            let startDate = firstDate.getFullYear() + "-" + ((firstDate.getMonth() + 1) < 10 ? "0" : "") + (firstDate.getMonth() + 1) + "-" + "01";
            let date = new Date();
            let currentMonth = date.getMonth();
            let nextMonth = ++currentMonth;
            let nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
            let oneDay = 1000 * 60 * 60 * 24;
            let lastDate = new Date(nextMonthFirstDay - oneDay);
            let endDate = lastDate.getFullYear() + "-" + ((lastDate.getMonth() + 1) < 10 ? "0" : "") + (lastDate.getMonth() + 1) + "-" + (lastDate.getDate() < 10 ? "0" : "") + lastDate.getDate();
            $(pageId + " .more_introduce .rulesDiv .date .content").html(startDate + '~' + endDate)
        },
        jumpPriMarketingPage: function (page_code, prod_sub_type2) {
            var obj = {
                "200": "template/marketing",//产品营销页
            }
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(page_code, obj[prod_sub_type2]);
        },
        //同七鱼建立链接 用户登录时进行操作
        loginQy() {
            let info = ut.getUserInf()
            let data = {
                funcNo: "80309",
                userId: info.custNo,
                userData: [
                    {
                        key: "real_name",
                        value: info.name ? info.name : ''
                    },
                    {
                        key: "mobile_phone",
                        value: info.mobileWhole ? info.mobileWhole : "",
                        hidden: false
                    },
                    {
                        key: "bankcard",
                        value: info.bankAcct ? info.bankAcct : '',
                        label: '绑定银行卡',
                        index: 1
                    },
                    {
                        key: "avatar",
                        value: '',
                    },
                    {
                        key: "email",
                        value: info.email,
                    }
                ]
            }
            data.userData = JSON.stringify(data.userData);
            let res = external.callMessage(data);
        },
        //断开七鱼链接 退出登录进行操作
        loginOutQy() {
            let data = {
                funcNo: "80311",
            }
            external.callMessage(data);
        },
        //获取地址栏参数
        getQueryVariable(url) {
            url = url; //获取url中"?"符后的字串s
            var theRequest = new Object();
            if (url.indexOf("?") != -1) {
                var str = url.substr(1);
                strs = str.split("&");
                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split("=")[0]] = decodeURI(strs[i].split("=")[1]);
                }
            }
            return theRequest;
        },
        //判断如果身份证最后一位存在小写x 改成大写
        hasLowercase(str) {
            let result = str.match(/^.*[a-z]+.*$/);
            if (result == null) return str;
            return str.substr(0, str.length - 1) + 'X'
        },
        //跳转公募详情
        pageTo200(productInfo, _page_code) {
            if (productInfo.prod_propagate_temp) {
                tools.jumpMarketingPage(_page_code, productInfo.prod_sub_type2,);
            } else {
                tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
            }
        },
        //计算费率
        setRate(money, list) {
            money = money * 1
            return new Promise(async (resolve, reject) => {
                let moneyData = {}  //匹配的区间
                for (let index = 0; index < list.length; index++) {
                    let item = list[index]
                    let fcitem_tval = item.fcitem_tval * 1
                    let fcitem_lval = item.fcitem_lval * 1
                    if (!fcitem_lval || fcitem_lval == "0.00") {    //当前区间是最小区间
                        if (money < fcitem_tval) {
                            moneyData = item;
                            break;
                        }
                    } else if (!fcitem_tval || fcitem_tval == "-1") {    //当前区间是最大
                        if (money >= fcitem_tval) {
                            moneyData = item;
                            break;
                        }
                    } else {  //中间区间
                        if (money >= fcitem_lval && money < fcitem_tval) {
                            moneyData = item;
                            break;
                        }
                    }
                }
                resolve(moneyData)
            })
        },
        /**
         * 
         * @param {String} type 当前分享页面类型
         * @param {String} page_code 当前分享页面内链URL
         * @param {String} id 当前详情唯一标识
         */
        //拿到原生回调后的操作
        onShareAppMessage(data) {
            let arr = [
                {
                    "3": "", //banner详情
                    "2": "template/publicMarketing", //营销页
                    "1": "template/publicOfferingDetail", //产品详情
                    "4": "moreDetails/noticeDetails"  //消息详情
                }
            ]
            let page_type = data.page_type;
            if (page_type == '3') {

            } else {
                sessionStorage.vip_buttonShow = true;
                appUtils.pageInit('login/userIndex', arr[0][page_type], {
                    busi_id: data.busi_id //传递唯一标识
                });
            }
        },
        //调用页面是否分享接口
        async shareData(data) {
            return new Promise(async (resolve, reject) => {
                service.reqFun102129(data, (datas) => {
                    if (datas.error_no == 0) {
                        let results = datas.results[0];
                        resolve(results)
                    } else {
                        layerUtils.iAlert(datas.error_info);
                    }
                })
            })
        },
        /**
         * 
         * @param {*} data 
         * @param {*} is_share 
         * @param {*} vipActivityInfo 同行好友新手/月底投教活动数据
         * @param {*} startBrowsingTime 开始浏览时间
         */
        async isShowShare(data, is_share, vipActivityInfo, startBrowsingTime) {
            let results, flag
            if (data.page_type == '1' || data.page_type == '2' || data.page_type == '7' || data.page_type == '8') {
                //营销页面，详情页面
                flag = '1'
                results = {}
            } else {
                //活动，咨询
                results = await tools.shareData(data)
                flag = '0'
            }
            var userInfo = ut.getUserInf();
            if ((results.is_share == '1' && results.state == '1') || (is_share == '1' && flag == '1')) {
                //未绑卡隐藏分享按钮
                if (!userInfo || !userInfo.bankAcct) {
                    $(data.pageId + " #share").hide();
                    $(data.pageId + " #kefu").show()
                } else {
                    $(data.pageId + " #share").show();
                    $(data.pageId + " #kefu").hide()
                }
            } else {
                $(data.pageId + " #kefu").show()
                $(data.pageId + " #share").hide()
            }



            let url;
            //特殊处理大转盘
            results.group_id = data.group_id;
            //特殊处理轮播图
            if (data.page_type == 3 && !data.activity_id) {
                let url_arr = data.banner_url.split('/');
                url = url_arr[6] + '/' + url_arr[7];    //地址
            }
            //微信好友
            appUtils.bindEvent($(data.pageId + " #pop_layer_pageShare #share_WeChat"), function () {
                tools.recordEventData('1','share_WeChat','微信好友');
                // task_type 2分享 3阅读/观看并分享 
                // return console.log(vipActivityInfo,111)
                var hasShareState = $(data.pageId + " #share").attr("has-share");
                let task_id,help_img_url,is_help;
                if(vipActivityInfo){
                    task_id = vipActivityInfo.task_id;
                    help_img_url = vipActivityInfo.help_img_url;
                    is_help = vipActivityInfo.is_help;
                }
                // return console.log(hasShareState)
                if (vipActivityInfo && vipActivityInfo.task_id && vipActivityInfo.task_type == '2' && !hasShareState && vipActivityInfo.is_help != '1') {
                    tools.vipTjActivityIn({ activity_id: vipActivityInfo.activity_id, task_id: vipActivityInfo.task_id }, data.pageId);
                } else if (vipActivityInfo && vipActivityInfo.task_id && vipActivityInfo.task_type == '3' && !hasShareState) {
                    // 先判断分享，再判断时长是否满足
                    if (tools.getStayTime(startBrowsingTime, vipActivityInfo.duration) && vipActivityInfo.is_help != '1') {
                        tools.vipTjActivityIn({ activity_id: vipActivityInfo.activity_id, task_id: vipActivityInfo.task_id }, data.pageId);
                    } else {
                        $(data.pageId + " #share").attr("has-share", true);
                    }
                }
                tools.pageShare(results, '22', data.pageId, data.pageCode, url, data.activity_id,task_id,help_img_url,is_help)
            });
            //微信朋友圈
            appUtils.bindEvent($(data.pageId + " #pop_layer_pageShare #share_WeChatFriend"), function () {
                tools.recordEventData('1','share_WeChatFriend','微信朋友圈');
                var hasShareState = $(data.pageId + " #share").attr("has-share");
                let task_id,help_img_url,is_help;
                if(vipActivityInfo){
                    task_id = vipActivityInfo.task_id;
                    help_img_url = vipActivityInfo.help_img_url;
                    is_help = vipActivityInfo.is_help;
                }
                if (vipActivityInfo && vipActivityInfo.task_id && vipActivityInfo.task_type == '2' && !hasShareState && vipActivityInfo.is_help != '1') {
                    tools.vipTjActivityIn({ activity_id: vipActivityInfo.activity_id, task_id: vipActivityInfo.task_id }, data.pageId);
                } else if (vipActivityInfo && vipActivityInfo.task_id && vipActivityInfo.task_type == '3' && !hasShareState) {
                    // 先判断分享，再判断时长是否满足
                    if (tools.getStayTime(startBrowsingTime, vipActivityInfo.duration) && vipActivityInfo.is_help != '1') {
                        tools.vipTjActivityIn({ activity_id: vipActivityInfo.activity_id, task_id: vipActivityInfo.task_id }, data.pageId);
                    } else {
                        $(data.pageId + " #share").attr("has-share", true);
                    }
                }
                tools.pageShare(results, '23', data.pageId, data.pageCode, url, data.activity_id,task_id,help_img_url,is_help)
            });
            //取消邀请好友
            appUtils.bindEvent($(data.pageId + " #pop_layer_pageShare #cancelShare"), function () {
                tools.recordEventData('1','share','cancelShare');
                $(data.pageId + " #pop_layer_pageShare").hide();
            });
            appUtils.bindEvent($(data.pageId + " #share"), async () => {
                tools.recordEventData('1','share','点击标题栏分享按钮');
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(data.pageCode)) return;
                if (data.page_type == '1' || data.page_type == '2' || data.page_type == '7' || data.page_type == '8') results = await tools.shareData(data)
                if (results.is_share != '1' || results.state != '1') return layerUtils.iAlert('当前页面不可分享');
                $(data.pageId + " #pop_layer_pageShare").show()
            });
        },
        //调用原生分享详情页
        async pageShare(data, shareTypeList, pageId, pageUrl, url, activity_id,task_id,help_img_url,is_help) {
            // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
            let source = ''; //根据 page_type 生成来源
            let randomNum = ('000000' + Math.floor(Math.random() * 999999)).slice(-6);
            let mobile = ut.getUserInf().mobileWhole;   //获取手机号
            mobile = common.desEncrypt("mobile", mobile);
            let pageTypeMapping = {
                '2': '13', // 营销页
                '9': '13', // 营销页
                '7': '13', // 营销页
                '1': '12', // 详情
                '8': '12', // 详情
                '3': '11',  // banner
                '4': '10', // 投教文章
                '6': '10',  // 投教文章
                '10': '17', // 代言人
            };
            source = pageTypeMapping[data.page_type];
            //活动注册来源特殊处理
            if (activity_id && activity_id != '') source = '9';

            let busi_id = data.prod_id ? data.prod_id : data.notices_id ? data.notices_id : data.banner_id ? data.banner_id : data.invest_teach_id;
            let share_url
            if (url && !activity_id) {
                share_url = global.serverUrl + '/m/mall/index.html?' + url.split('?')[1] + '&group_id=' + data.group_id + '&page_type=' + data.page_type + '&is_share=' + data.is_share + '&busi_id=' + busi_id + '&btn_state=' + data.btn_state + '&source=' + source + '&is_help=' + is_help + '&task_id=' + task_id + '&help_img_url=' + help_img_url  + '&mobile=' + mobile + '&time=' + new Date().getTime() + randomNum + '#!/' + url.split('?')[0]
            } else {
                share_url = global.serverUrl + '/m/mall/index.html?page_type=' + data.page_type + '&group_id=' + data.group_id + '&activity_id=' + activity_id + '&is_share=' + data.is_share + '&busi_id=' + (busi_id ? busi_id : activity_id) + '&btn_state=' + data.btn_state + '&source=' + source + '&is_help=' + is_help + '&task_id=' + task_id + '&help_img_url=' + help_img_url + '&mobile=' + mobile + '&time=' + new Date().getTime() + randomNum + '#!/' + pageUrl + '.html'
            }
            var params = {};
            params["url"] = share_url;
            layerUtils.iMsg(-1, "启动分享中...请稍后！");
            $(".pop_layer").hide();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50231";
            param["shareType"] = shareTypeList;//平台字典
            param["title"] = data.title;
            param["link"] = share_url;
            param["content"] = data.content; //分享文本
            param["imgUrl"] = data.img_url
            console.log(param);
            external.callMessage(param);
        },
        //页面重新渲染默认置顶
        setPageTop(pageId) {
            $(pageId).scrollTop(0)
        },
        /** 
         * 时间戳转时间
         * @param {string} format 格式 
         * @param {int} timestamp 要格式化的时间 默认为当前时间 
         * @return {string}   格式化的时间字符串 
         */
        setTimeData(value, str) {
            let date = new Date(value),  //如果date为13位不需要乘1000
                Y, M, D, newTime;
            let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
            let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
            let s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
            if (str === 'YY-MM-DD') {
                Y = date.getFullYear();
                M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
                D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                newTime = value ? `${Y}年${M}月${D}日` : '';
            } else {
                Y = date.getFullYear() + '-';
                M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
                newTime = value ? Y + M + D + h + m + s : '';
            }
            return newTime;
        },
        fingerprintPwd() {
            //判断缓存中时间戳 < 当前时间戳 500 不重复调用
            if (localStorage.fingerprintTime && (new Date().getTime() - localStorage.fingerprintTime <= 50)) return
            //页面跳转前存储当前时间戳
            localStorage.fingerprintTime = new Date().getTime();
            var param80319 = {
                funcNo: "80319",
            };
            sessionStorage.isOpenFingerprint = true;
            external.callMessage(param80319);
        },
        //获取是否设置过手势密码
        isShowGesture(_pageId) {
            // 获取用户账户信息
            var param50043 = {
                funcNo: "50043",
                key: "account_password"
            };
            let flag;
            var firstInstall = external.callMessage(param50043);
            if (firstInstall && firstInstall.results && firstInstall.results[0].value) {
                firstInstall = firstInstall.results[0].value;
                if (firstInstall) {
                    var account = firstInstall.substring(0, firstInstall.indexOf("_"));
                    var param = {
                        "funcNo": "50263",
                        "account": account
                    };
                    var data = external.callMessage(param);
                    flag = data.results[0].flag;
                    if (flag != '1') { //未开启
                        $(_pageId + ".gesture").hide();
                        $(_pageId + " .loginDig_bottom").css('justify-content', 'flex-end')
                    } else {  //已开启
                        $(_pageId + ".gesture").show();
                        $(_pageId + " .loginDig_bottom").css('justify-content', 'space-between')
                    }
                }
            }
            // let gesture_code_data = common.getLocalStorage("gesture_code");
        },
        //缓存目的地页面地址/类型
        setPageToUrl(url, type, stringName) {
            appUtils.setSStorageInfo("pageTopUrlInfo", url + '_' + type + (stringName ? ('_' + stringName) : ''));
        },
        //常规判断跳转
        toUrl(homePageIndex) {
            let userInfo = ut.getUserInf();
            var pageId = $("body .page[data-display='block']").attr("id");
            var pageCode = pageId.replace("_", "/");
            if (pageId.trim().substr(0, 1) != '#') {
                pageId = '#' + pageId
            }
            $(pageId + ' .loginDig').hide()
            if (pageCode == "yuanhui/userIndexs" || pageCode == "hengjipy/userIndexs" || pageCode == "login/userIndexs") {
                // return homePageIndex.init();
                // common.setLocalStorage("snowballMarketShow",'1');
                common.setLocalStorage("sceneRefresh",'0');     
                common.setLocalStorage("userChooseRefresh",'0');
                setTimeout(async () => {
                    homePageIndex.destroy();
                    location.reload();
                }, 0);
                
            }else{
                if (userInfo.custLabelCnlCode == "yh") {
                    appUtils.pageInit("login/userLogin", "yuanhui/userIndexs", {});
                } else if (userInfo.custLabelCnlCode == "yh_jjdx" || !(userInfo.custLabelCnlCode) || userInfo.custLabelCnlCode == "jjdx") {
                    appUtils.pageInit("login/userLogin", "login/userIndexs", {});
                } else {
                    appUtils.pageInit("login/userLogin", "hengjipy/userIndexs", {});
                }
            }
        },
        //真实跳转
        toUrlTrue(url, previous) {
            appUtils.setSStorageInfo("pageTopUrlInfo", "")
            if (previous) appUtils.setSStorageInfo("routerList", ["login/userIndexs", previous]);
            if (!previous) appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
            let pageId = $("body .page[data-display='block']").attr("id");
            if (pageId.trim().substr(0, 1) != '#') {
                pageId = '#' + pageId
            }
            $(pageId + ' .loginDig').hide()
            appUtils.pageInit('login/userIndexs', url, {});
        },
        //跳转相关
        async pageToUrl(isFlag, homePageIndex) {
            let pageTopUrlInfo = appUtils.getSStorageInfo("pageTopUrlInfo");
            let userInfo = ut.getUserInf();
            if (!pageTopUrlInfo || !pageTopUrlInfo.length) return tools.toUrl(homePageIndex);
            let url = pageTopUrlInfo.split('_')[0];
            let type = pageTopUrlInfo.split('_')[1];
            let previous = pageTopUrlInfo.split('_')[2] ? pageTopUrlInfo.split('_')[2] : null;
            if (type == '1') {    //交易类跳转
                let flag = await tools.transactionPageFront();
                if (flag && isFlag) {
                    common.changeCardInter('', '', '', '', true, homePageIndex, () => {
                        tools.toUrlTrue(url, previous)
                    })
                } else {
                    tools.toUrl(homePageIndex)
                }
                // common.changeCardInter('','','','',true,()=> {

                // })
            } else if (type == '2') {  //非交易类
                if (isFlag) {
                    tools.toUrlTrue(url, previous)
                } else {
                    tools.toUrl(homePageIndex)
                }
            } else if (type == '3') {  //取现
                let flag = await tools.transactionPageFront(3);
                if (!flag) return tools.toUrl(homePageIndex)
                //查询是否已开银行账户
                service.reqFun151110({ bank_channel_code: "" }, function (data) {
                    if (data.error_no != "0") {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var results = data.results[0];
                    if (results.bank_flag == "0") { //未开银行账户
                        common.changeCardInter('', '', '', '', true, homePageIndex, () => {
                            appUtils.setSStorageInfo("pageTopUrlInfo", "");
                            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                            appUtils.pageInit('login/userIndexs', "thfund/enchashment");
                        })
                        return;
                    }
                    service.reqFun151107({}, function (data) { //查询银行资产
                        if (data.error_no != "0") {
                            layerUtils.iLoading(false);
                            layerUtils.iAlert(data.error_info);
                            return;
                        }
                        var results = data.results[0];
                        if (results && results.total_yue > 0) { //银行余额大于0，进入取现主页
                            layerUtils.iLoading(false);
                            appUtils.setSStorageInfo("pageTopUrlInfo", "");
                            appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                            appUtils.pageInit('login/userIndexs', "thfund/enchashmentHome");
                        } else {
                            common.changeCardInter('', '', '', '', true, homePageIndex, () => {
                                appUtils.setSStorageInfo("pageTopUrlInfo", "");
                                appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                                appUtils.pageInit('login/userIndexs', "thfund/enchashment");
                            })
                        }
                    }, { isLastReq: false })
                }, { isLastReq: false })
            } else if (type == '4') {  //我的
                appUtils.setSStorageInfo("pageTopUrlInfo", "");
                appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                if (ut.getUserInf().bankAcct) {
                    appUtils.pageInit('login/userIndexs', "account/myAccount", {});
                } else {
                    appUtils.pageInit('login/userIndexs', "account/myAccountNoBind", {});
                }
            } else if (type == "5") {
                appUtils.setSStorageInfo("pageTopUrlInfo", "");
                appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
                appUtils.pageInit('login/userIndexs', "liveBroadcast/index", {});
            } else {
                tools.toUrl(homePageIndex);
            }
        },
        //交易类页面跳转前置校验判断    非交易类直接跳转
        transactionPageFront(type) {
            let flag1, flag2, flag3;//是否满足条件
            let userInfo = ut.getUserInf();
            let bankAcct = userInfo.bankAcct;
            let invalidFlag = userInfo.invalidFlag;
            let soonInvalidFlag = userInfo.soonInvalidFlag;
            let perfect_info = userInfo.perfect_info;
            flag1 = bankAcct ? true : false;    //是否绑卡
            flag2 = perfect_info == 4 ? false : true; //身份证是否到期
            flag3 = (invalidFlag == '1' || validatorUtil.isEmpty(userInfo.riskLevel) || soonInvalidFlag == '1') ? false : true; //是否测评，是否测评过期，风评即将到期
            return new Promise((resolve) => {
                if (type == 3) {  //取现特殊
                    if (flag1 && flag2) {
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                } else if (type != 3) {
                    if (flag1 && flag2 && flag3) {
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                }
            })
        },
        //showLoginDig 展示指纹弹窗并弹出指纹登录
        showLoginDig(page_id, page_code) {
            let domList = $(page_id + ' .loginDig');
            if (domList.length) {
                let firstInstall = common.getLocalStorage("account_password");
                let phoneNum = firstInstall.substring(0, firstInstall.indexOf("_"));
                phoneNum = phoneNum.substr(0, 3) + "****" + phoneNum.substr(-4);
                $(page_id + " .loginDig_phone").text(phoneNum);
                tools.isShowGesture(page_id);
                $(page_id + ' .loginDig').show();
                tools.fingerprintPwd();
                return;
            };
            let html = `
                    <div class="loginDig" style="display: none;">
                    <ul class="loginDig_back" style="padding:0.1rem;display: inline-flex;align-items: center;">
                        <img class="" style="margin: 0 auto;display: block;width:0.2rem;height: 0.18rem;" src="./images/back_left.png">
                        <span style="display: block;height: 0.18rem;line-height: 0.18rem;">返回</span>
                    </ul>
                    <ul class="loginDig_phone" style="text-align: center;margin-top: 20%;font-size: 0.16rem;color:#1E80FF">
                    </ul>
                    <ul class="loginDig_click" style="margin-top:20%">
                        <img class="setFingerprint" style="margin: 0 auto;display: block;width:0.6rem;height: 0.6rem;" src="./images/fingerprint.png">
                    </ul>
                    <div style="text-align: center;margin-top: 0.1rem;">点击验证指纹登录</div>
                    <ul class="loginDig_bottom flex" style="position:absolute;bottom:0;padding:0.1rem;width:100%;color:#d01a06">
                        <li class="gesture" style="display: none;">
                            手势密码登录
                        </li>
                        <li class="pageToLogin">
                            账号密码登录
                        </li>
                    </ul>
                </div>
            `
            $(page_id).append(html);
            $(page_id + " .loginDig").show();
            let firstInstall = common.getLocalStorage("account_password");
            let phoneNum = firstInstall.substring(0, firstInstall.indexOf("_"));
            phoneNum = phoneNum.substr(0, 3) + "****" + phoneNum.substr(-4)
            $(page_id + " .loginDig_phone").text(phoneNum);
            tools.isShowGesture(page_id);
            tools.fingerprintPwd();
            appUtils.bindEvent($(page_id + " .gesture"), function () {
                tools.recordEventData('1','gesture','手势密码登录');
                // 获取用户账户信息
                let param50043 = {
                    funcNo: "50043",
                    key: "account_password"
                };
                let firstInstall = external.callMessage(param50043);
                firstInstall = firstInstall.results[0].value;
                let account = firstInstall.substring(0, firstInstall.indexOf("_"));
                let setParam = {
                    "funcNo": "50261",
                    "moduleName": "mall",
                    "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
                    "account": account,
                    "errorNum": "5",
                    "isCanBack": "1",
                    "lockSenconds": "60",
                    "userImage": ""
                };
                external.callMessage(setParam);
            });
            appUtils.bindEvent($(page_id + " .pageToLogin"), function () {
                tools.recordEventData('1','pageToLogin','账号密码登录');
                appUtils.pageInit(page_code, "login/userLogin", {});
            });
            appUtils.bindEvent($(page_id + " .loginDig_back"), function () {
                tools.recordEventData('1','loginDig_back','关闭');
                $(page_id + " .loginDig").hide()
            });
        },
        //直播昵称格式化
        format(name) {
            if (!name || name == '') {
                //未绑卡或其他异常情况
                name = ut.getUserInf().mobile;
                return name;
            } else {
                if (name.length < 2) {   //姓名长度小于2
                    return name;
                } else if (name.length == 2) {
                    return '*' + name[1]
                } else if (name.length > 2) {
                    let trueName = '';
                    for (let index = 0; index < name.length; index++) {
                        // console.log(name[index]);
                        if (index < name.length - 1) trueName += '*';
                    }
                    return trueName + name[name.length - 1];
                }
            }
        },
        //去直播间
        livePageTo(uid, trueName, h5Url, room_id, _pageCode, title) {
            let url
            if (uid) {
                url = h5Url + '&uid=' + uid + "&nick=" + trueName;
            }
            let setParam = {
                "funcNo": "50115",
                "moduleName": "mall",
                "url": url ? url : h5Url,
                // "statusColor":"#FFFFFF",
                "title": title ? title : "直播间",
                "titleColor": "#3f4e59",
                "btnColor": "#3f4e59",
                "isSupportSwipingBack": "1",
                "isTKH5": "0",
                "btnMode": "2",
                "navbarImage": "tk_nav_bg",
                "leftBtnColor": "#3f4e59",
                "rightBtnColor": "#FB0005",
                "progressColor":"#0000"
            };
            let phoneType = gconfig.platform;    //获取用户手机类型 1安卓 2IOS
            if (phoneType == '2' || phoneType == '5') setParam.statusColor = '#FFFFFF';
            if (!uid) {
                setParam.isShowShareBtn = '0';
                external.callMessage(setParam);
                return;
            }
            service.reqFun102129({ busi_id: room_id, page_type: "5" }, (datas) => {
                if (datas.error_no == 0) {
                    let results = datas.results[0];
                    if (results && results.is_share == '1' && results.state == '1') {
                        //可以分享
                        let share_data = results;
                        setParam.isShowShareBtn = '1';
                        setParam.shareExtParam = {
                            shareTypeList: '22,23',
                            title: share_data.title,
                            link: url,
                            content: share_data.content,
                            imgUrl: share_data.img_url
                        }
                        external.callMessage(setParam);
                        return;
                    } else {
                        setParam.isShowShareBtn = '0';
                        external.callMessage(setParam);
                    }
                } else {
                    // let phoneType = gconfig.platform    //获取用户手机类型 1安卓 2IOS
                    // if(phoneType == '2') setParam.statusColor = '#FFFFFF';
                    setParam.isShowShareBtn = '0';
                    external.callMessage(setParam);
                    return layerUtils.iAlert(datas.error_info);
                }
            })
        },
        //新版弹窗跳转逻辑
        async jump_page(_page_code, data, userAuthenticationStatus, _pageId) {
            if (data.pop_only != 0) return; //仅弹窗
            appUtils.setSStorageInfo("fund_code", data.jump_page_prodid);
            localStorage.series_id = data.jump_page_prodid;
            appUtils.setSStorageInfo("financial_prod_type", data.financial_prod_type);
            let productInfoData = {
                fund_code: data.jump_page_prodid
            }
            appUtils.setSStorageInfo("productInfo", productInfoData);
            let jump_page_prodtype = data.jump_page_prodtype; //1公募 2私募 3投顾 4系列 5投顾系列
            let jump_page_type = data.jump_page_type; //一级页面跳转地址
            let detail_jump_page_type = data.detail_jump_page_type; //二级页面跳转地址
            let public_trueUrlList = ['', 'template/templateBuy', 'template/publicOfferingDetail', 'template/publicMarketing', 'template/publicHoldHeightDetail', data.url, 'template/popupPage', 'template/popupPage']    //公募跳转真实地址 默认第一个为空 0
            let private_trueUrlList = ['', 'template/templateBuy', 'template/heighEndProduct', 'template/marketing', 'template/holdHeighDetail', data.url, 'template/popupPage', 'template/popupPage']    //私募跳转真实地址 默认第一个为空 0
            let series_trueUrlList = ['', 'fundSupermarket/fundsBuy', '', 'template/decentralizedPurchasing', 'template/positionList', data.url, 'template/popupPage', 'template/popupPage'] //系列产品
            let investment_trueUrlList = ['', 'combProduct/combProdBuy', 'combProduct/combProdDetail', 'combProduct/combProdMarketing', 'combProduct/combHoldHeightDetail', data.url, 'template/popupPage', 'template/popupPage'] //投顾产品
            let seriesCombList = ['', '', '', 'template/seriesChildrenMarketing', '', data.url, 'template/popupPage', 'template/popupPage']
            // let trueUrlList = jump_page_prodtype == 1 ? public_trueUrlList : private_trueUrlList    //最终跳转地址集合
            let trueUrlList = jump_page_prodtype == '1' ? public_trueUrlList : jump_page_prodtype == '2' ? private_trueUrlList : jump_page_prodtype == '3' ? investment_trueUrlList : jump_page_prodtype == '4' ? series_trueUrlList : seriesCombList;  //最终跳转地址集合
            if (jump_page_type == '6') data.trueUrl = trueUrlList[detail_jump_page_type];   //如果存在二级页面跳转 缓存跳转地址
            if (jump_page_type == '7') data.detail_bottom_button_show = '0' //仅展示图片
            // if (jump_page_type == '5') {
            //     //自定义页面
            //     return appUtils.pageInit(_page_code, data.url, data);
            // }
            if (jump_page_prodtype == '3') {
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                //投顾产品特殊处理
                let combProductInfo = {
                    comb_code: data.jump_page_prodid
                }
                appUtils.setSStorageInfo("combProductInfo", combProductInfo);
            }
            if (jump_page_prodtype == '5') {
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
            }
            appUtils.setSStorageInfo("pageInfo", data);
            if (userAuthenticationStatus == '0' && jump_page_prodtype == "2") {
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                sessionStorage.digUserAuthenticationStatus = "1";
                sessionStorage.digUserAuthenticationUrl = trueUrlList[jump_page_type];
                return $(_pageId + ".qualifiedInvestor").show();
            }
            if (jump_page_type == '4' || detail_jump_page_type == '4') {
                let res = await tools.getUserPro({ fund_code: data.jump_page_prodid, vir_fundcode: data.jump_page_virfundcode });
                if (res[0].whetherHold == "0") return layerUtils.iAlert("抱歉您没有当前产品持仓");
            }
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(_page_code, trueUrlList[jump_page_type], data);
        },
        //判断用户是否持有该持仓
        async getUserPro(pushData) {
            return new Promise(async (resolve, reject) => {
                service.reqFun102174(pushData, async (data) => {
                    if (data.error_no == '0') {
                        var res = data.results
                        resolve(res)
                    } else {
                        reject('异常抛出')
                        layerUtils.iAlert(data.error_info);
                    }
                })
            })
        },
        // 跳转小程序
        async jump_applet(url) {
            var param = {};
            param["funcNo"] = "60076";
            param["userName"] = global.appletAuthentication.appId;
            param["miniprogramType"] = global.appletAuthentication.miniprogramType;
            param["path"] = url;//平台字典
            param["extData"] = ""
            param["appld"] = "";
            param["universalLink"] = "";
            require("external").callMessage(param);
        },
        //跳转消息详情
        async pageTo_message_datail(that, page_code) {
            // console.log(that.find("em").text())
            if(!that.find("em").text() || that.find("em").text().length <= 0) return;
            var message_pro = JSON.parse(that.find("em").text()); //.attr("message_pro")
            
            await tools.isRead(message_pro.hash_key);
            if (message_pro.mail_url == 'login/listMorePage') {
                appUtils.setSStorageInfo("productType", '06') //存储类型
                if (!common.loginInter()) return;
                appUtils.pageInit(page_code, "login/listMorePage");
                return;
            }
            sessionStorage.detailsText = message_pro.mail_content ? message_pro.mail_content : '';
            sessionStorage.message_pro = JSON.stringify(message_pro);
            sessionStorage.create_date = message_pro.create_date ? message_pro.create_date : '';
            let mail_show_type = message_pro.mail_show_type //展示形式 0文本 1链接
            let mail_link_type = message_pro.mail_link_type //0内链 1外链 2图片
            let mail_send_type = message_pro.mail_send_type //是否取列表 0查详情 1取列表
            let mail_url = message_pro.mail_url
            let name = message_pro.name
            let mail_id = message_pro.mail_id
            let hash_key = message_pro.hash_key
            if (mail_send_type == 0) {  // 调用接口
                tools.getMessageDetails(mail_id, mail_send_type, hash_key, page_code)
            } else {  //取列表
                tools.setLogic(mail_show_type, mail_link_type, mail_url, name, mail_id, page_code)
            }
        },
        //获取详情
        getMessageDetails(mail_id, mail_send_type, hash_key, page_code) {
            let res = {
                mail_send_type: mail_send_type,
                mail_id: mail_id,
                hash_key: hash_key
            }
            service.reqFun105004(res, function (data) {
                let error_no = data.error_no
                let error_info = data.error_info
                if (error_no == '0') {
                    let res = data.results[0]
                    sessionStorage.detailsText = res.mail_content
                    sessionStorage.message_pro = JSON.stringify(res)
                    tools.setLogic(res.mail_show_type, res.mail_link_type, res.mail_url, res.name, res.mail_id, page_code)
                } else {
                    layerUtils.iAlert(error_info);
                }
            })
        },
        //已读
        async isRead(hash_key) {
            return new Promise((resolve) => {
                service.reqFun105002({ hash_key: hash_key }, function (data) {
                    let error_no = data.error_no
                    let error_info = data.error_info
                    var str = "";
                    if (error_no == "0") {
                        resolve(true)
                    } else {
                        resolve(false)
                        layerUtils.iAlert(error_info);

                    }
                });
            })

        },
        setLogic(mail_show_type, mail_link_type, mail_url, name, mail_id, page_code) {
            if (mail_show_type == "1") {  //链接
                if (mail_link_type == "0") {  //内链
                    if (mail_url.indexOf("?") > -1){
                        var skip_url = mail_url.split("?")[0];
                        var parameter = mail_url.split("?")[1];
                        var parameter_arr = parameter.split("&"); //各个参数放到数组里
                        var mailInfo = {};//url的参数信息
                        for (var i = 0; i < parameter_arr.length; i++) {
                            num = parameter_arr[i].indexOf("=");
                            if (num > 0) {
                                name = parameter_arr[i].substring(0, num);
                                value = parameter_arr[i].substr(num + 1);
                                mailInfo[name] = value;
                            }
                        }
                        appUtils.pageInit(page_code, skip_url, mailInfo);
                    }else{
                        appUtils.pageInit(page_code, mail_url,{});
                    }
                    
                } else if (mail_link_type == "1") {    //外链
                    appUtils.pageInit(page_code, "guide/advertisement", {
                        "url": mail_url,
                        "name": name,
                    });
                } else if (mail_link_type == "2") { //图片
                    sessionStorage.showImageUrl = '1'   //新页面只展示图片地址
                    appUtils.pageInit(page_code, "moreDetails/messageDetails");
                } else if (mail_link_type == "3") { // 小程序
                    tools.jump_applet(mail_url);
                }
            } else {  //文本 统一跳转新页面
                appUtils.pageInit(page_code, "moreDetails/messageDetails");
            }
        },
        validateCharName(event) {
            // 允许输入的字符：英文、数字、中文
            const regexAllowedChars = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
            // 如果输入的字符不符合要求，则阻止该输入
            if (!regexAllowedChars.test(event.key) && event.key !== 'Backspace') {
                event.preventDefault();
            }
        },
        validateInputName(name) {
            const regexChinese = /^[\u4e00-\u9fa5]+$/;  // 匹配纯中文
            const regexEnglishNumeric = /^[a-zA-Z0-9]+$/;  // 匹配纯英文或数字
            if (regexChinese.test(name)) {
                // 纯中文，长度不能超过5
                if (name.length > 5) {
                    return false;
                }
            } else if (regexEnglishNumeric.test(name)) {
                // 纯英文或数字，长度不能超过10
                if (name.length > 10) {
                    return false;
                }
            } else {
                // 混合内容，长度不能超过5
                if (name.length > 5) {
                    return false;
                }
            }
            return true;
        },
        // 计算并判断页面停留时间是否达到
        getStayTime(startTime, duration) {
            duration = parseFloat(duration) * 1000;
            var currentTime = Date.now(); // 获取当前时间
            var stayTime = currentTime - startTime; // 计算停留时间
            if (stayTime > duration) return true
        },

        getRemainTime(startTime, duration) {
            duration = parseFloat(duration) * 1000;
            var currentTime = Date.now(); // 获取当前时间
            var stayTime = currentTime - startTime; // 计算停留时间
            if (stayTime > duration) {
                return 0;
            } else {
                return duration - stayTime
            }
        },
        // 同行好友新手/月度投教活动参与
        vipTjActivityIn(param, _pageId) {
            service.reqFun108050(param, function (data) {
                if (data.error_no != '0') {
                    layerUtils.iAlert(data.error_info);
                } else {
                    _pageId && $(_pageId + " #share").attr("has-share", true);
                }
            })
        },
        //app硬件存储
        setAppData(data){
            // let params = {
            //     funcNo : "50040",
            //     key:"burialPointList",
            //     value:JSON.stringify(data)
            // }
            // external.callMessage(params);

            //web 环境测试
            localStorage.burialPointList = JSON.stringify(data);
        },
        //app取值
        getAppdata(){
            // let params = {
            //     funcNo : "50041",
            //     key:"burialPointList",
            // }
            // let data = external.callMessage(params);
            // let burialPointList = (data && data.results && data.results[0] &&  data.results[0].value) ? data.results[0].value : [];

            //web  测试
            let data = localStorage.burialPointList;
            let burialPointList = (data && data.length) ? JSON.parse(data) : [];
            return burialPointList;
        },
        //生成随机UUID
        generateUUID() {
            let dt = new Date().getTime();
            const uuid = 'xxxxxxxx-xxxx-xxxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c)=> {
                const r = (dt + Math.random()*16)%16 | 0;
                dt = Math.floor(dt/16);
                return (c=='x' ? r :(r&0x3|0x8)).toString(16);
            });
            return uuid;
        },
        /**
         * 生成页面埋点公共数据
         * @param {object} mergeObj 合并对象，自定义字段
         */
        async initPagePointData(mergeObj) {
            if(platform == '0') return;
            let userInfo = ut.getUserInf() ? ut.getUserInf() : {};//拿到用户信息
            let allDataList = tools.getAppdata();//取出所有参数
            let pageId = $("body .page[data-display='block']").attr("id");
            if(!pageId) return;
            let phoneData = tools.getPhoneData();//获取手机原生参数
            let pageData = {
                //公共参数
                pageUuid: tools.generateUUID(),//数据唯一标识
                pageId: pageId, //页面ID
                enterTime: new Date().getTime() + '',//进入时间
                eventList: [],//事件埋点集合
                leaveTime: "",//离开时间
                versionCode:tools.getVersionCode(),
                channelId:userInfo.custLabelCnlCode ? userInfo.custLabelCnlCode : '',// 渠道ID
                equipmentId: tools.getEquipmentId(),//设备ID 已有
                equipmentModel:phoneData.devicePlatform ? phoneData.devicePlatform : '',//设备型号
                // netWorkStatus: tools.getNetworkStatus()[0],//网络状态 wife/数据流量
                platform: platform,//1：android手机壳子嵌phonegap、2：ios手机壳子嵌phonegap
                custNo: userInfo.custNo ? userInfo.custNo : '',//客户号 空为未登录状态
                // ip:tools.getNetworkStatus()[1],//IP
                pageName:$("body .page[data-display='block']").attr("data-pagetitle")
            };
        
            mergeObj = mergeObj ? mergeObj : {};
            pageData = {...pageData,...mergeObj};
            // if(allDataList && allDataList.length >= global.buriedPointsNum){
            //     //上报接口 删除旧参数 存新参数
            //     const throttledPointDataUpload = tools.throttle(tools.pointDataUpload, 2000);
            //     throttledPointDataUpload(allDataList,pageData)
            // }else{
            //     //条目数量不够，继续存储
            //     allDataList.push(pageData);
            //     tools.setAppData(allDataList);
            // }
            // if (allDataList?.length >= 15) {
            //     const throttledPointDataUpload = tools.throttle(tools.pointDataUpload, 2000);
            //     throttledPointDataUpload(allDataList, pageData);
            // } else {
            //     allDataList = allDataList || [];
            //     allDataList.push(pageData);
            //     tools.setAppData(allDataList);
            // }
            // return;
            // 检查是否在当天内已达到最大重试次数
            const checkMaxRetryReached = () => {
                const lastFailDate = localStorage.getItem('pointDataLastFailDate');
                const today = new Date().toDateString();
                
                // 如果最后失败日期不是今天，重置失败状态
                if (lastFailDate !== today) {
                    localStorage.removeItem('pointDataLastFailDate');
                    localStorage.removeItem('pointDataMaxRetryReached');
                    global.buriedPointsNum = 15;
                    return false;
                }
                
                return localStorage.getItem('pointDataMaxRetryReached') === 'true';
            };
        
            //处理上传失败的情况
            const handleUploadFail = () => {
                const today = new Date().toDateString();
                localStorage.setItem('pointDataLastFailDate', today);
                
                // 如果数据量超过100，标记为达到最大重试次数
                if(allDataList && allDataList.length > 100) {
                    localStorage.setItem('pointDataMaxRetryReached', 'true');
                }
            };
        
            // // 上传数据的函数
            const uploadData = async (dataList, newData) => {
                try {
                    const throttledPointDataUpload = tools.throttle(tools.pointDataUpload, 2000);
                    await throttledPointDataUpload(dataList, newData);
                    // 上传成功后清除失败状态
                    localStorage.removeItem('pointDataLastFailDate');
                    localStorage.removeItem('pointDataMaxRetryReached');
                } catch (error) {
                    handleUploadFail();
                    // 上传失败时保存数据
                    allDataList = dataList || [];
                    allDataList.push(newData);
                    tools.setAppData(allDataList);
                }
            };
        
            // // 主要逻辑
            if (allDataList && allDataList.length >= global.buriedPointsNum && !checkMaxRetryReached()) {
                uploadData(allDataList, pageData);
            } else {
                allDataList = allDataList || [];
                allDataList.push(pageData);
                tools.setAppData(allDataList);
            }
        },
        //获取设备号
        getEquipmentId(){
            let deviceTokenresult = external.callMessage({
                funcNo: "50022"
            });
            let deviceToken = deviceTokenresult.results ? deviceTokenresult.results[0].deviceToken : "";
            return deviceToken;
        },
        /**
         * @param {string} operationType 事件类型 1点击 2滑动 4 页面销毁
         * @param {string} operationId 事件ID
         * @param {string} operationName 事件名称
         * @param {string} contentType 内容类型 1 产品 2 banner 3 活动ID 4 mailId 消息ID 5 popId 弹窗ID 6 fundMsgId 产品资讯ID 7 fundNoticeId 产品公告ID 8 fundFileId 产品文件ID 9 fundAgreementId 产品协议ID 10 noticeId 平台资讯ID 11 newsId 平台公告ID 12 articlId 学投资文章ID
         */
        recordEventData(operationType,operationId,operationName,mergeObj){
            if(platform == '0') return;
            let allDataList = tools.getAppdata();//取出所有参数
            if(!allDataList || !allDataList.length) return;
            let userInfo = ut.getUserInf() ? ut.getUserInf() : {};
            let pageId = $("body .page[data-display='block']").attr("id");
            if(!pageId) return;
            let clickData = {
                eventId:tools.generateUUID(),
                pageId:pageId, //页面ID
                operationId:operationId,// 当前事件ID
                operationType:operationType ,// 当前事件类型 
                operationName:operationName,// 事件名称
                operationTime:new Date().getTime() + '',//点击时间
                custNo:userInfo.custNo ? userInfo.custNo : '',//客户号
                equipmentId:tools.getEquipmentId(),// 设备号
                channelId:userInfo.custLabelCnlCode ? userInfo.custLabelCnlCode : '',// 渠道ID
                // ip:tools.getNetworkStatus()[1],//IP
                platform:platform,// 设备类型
                // netWorkStatus:tools.getNetworkStatus()[0],// 网络状态
                pageUuid:"",
                // pageUuid:"",// 关联页面埋点ID
                // 特殊参数 唯一标识
                // fundCode: "",// 产品ID
                // bannerId: "",// bannerID
                // adId: "",// 活动ID
                // mailId: "",// 消息ID（站内信）
                // popId: "",// 弹框ID
                // fundMsgId: "",// 产品资讯id
                // fundNoticeId: "",// 产品公告id
                // fundFileId: "",// 产品文件id
                // fundAgreementId: "",// 产品协议id
                // noticeId: "",//平台资讯id
                // newsId: "",//平台公告id
                // articlId: "",//学投资文章id
                // remark: "",//备注
                
            }
            mergeObj = mergeObj ? mergeObj : {};
            clickData = {...clickData,...mergeObj};
            let pageData = allDataList[allDataList.length - 1];//取出最后一条页面参数
            clickData.pageId = pageData.pageId ? pageData.pageId : '';
            if(operationType == '4'){    //离开页面记录离开时间
                pageData.leaveTime = new Date().getTime() + '';
            }
            clickData.pageUuid = pageData.pageUuid //记录父级页面埋点ID 关联页面ID
            pageData.eventList.push(clickData);//生成事件埋点
            allDataList[allDataList.length - 1] = pageData;//重新赋值
            tools.setAppData(allDataList);//存入缓存
        },
        //调用埋点接口上传数据
        pointDataUpload(list,newData){
            let uploadNums = list.slice(0,global.buriedPointsNumTrue);
            let paramList = JSON.stringify(uploadNums)
            service.reqFun101079({list:paramList}, function (data) {
                if (data.error_no != '0') { //接口请求失败
                    list.push(newData);
                    tools.setAppData(list);
                } else {
                    let num = data.results[0].num;//成功上报n条数据
                    //num 0 埋点扩容到100条
                    if(num == 0){
                        if(list && list.length > 100 && (global.buriedPointsNum == global.buriedPointsNumTrue)) {
                            localStorage.setItem('pointDataMaxRetryReached', 'true');
                        }
                        global.buriedPointsNum = 100;
                        list.push(newData);
                        tools.setAppData(list);
                        const today = new Date().toDateString();
                        localStorage.setItem('pointDataLastFailDate', today);
                        // 如果数据量超过100，标记为达到最大重试次数
                        // if(list && list.length > 15) {
                        //     localStorage.setItem('pointDataMaxRetryReached', 'true');
                        // }
                        return;
                    }
                    list = list.slice(num);
                    list.push(newData);
                    tools.setAppData(list);
                    global.buriedPointsNum = 15;
                }
            })
        },
        // 节流函数 避免一直重复调用接口
        throttle(func, wait) {
            let lastCall = 0;
            return function(...args) {
                const now = Date.now();
                if (now - lastCall >= wait) {
                    lastCall = now;
                    func(...args);
                }
            };
        },
        //获取设备内部参数
        getPhoneData(){
            let devresult = external.callMessage({
                funcNo: "50001"
            });
            let data = devresult.results ? devresult.results[0]: {};
            return data;
        },
        //获取系统版本号
        getVersionCode(){
            var oVersion = external.callMessage({
                funcNo: "50010"
            });
            oVersion = oVersion.results ? oVersion.results[0] : { versionSn: global.version_code };
            var version = oVersion.versionSn;
            return version;
        },
        //底部处理处理
        footerShow(pageId,userChooseVerson,scene_code,mobileWhole){
            // let scene_code = common.getLocalStorage("scene_code") ? common.getLocalStorage("scene_code") : ''; //页面版本类型 1标准版 X版
            // let mobileWhole = common.getLocalStorage("mobileWhole") ? common.getLocalStorage("mobileWhole") : ''; //判断用户是否登陆过
            // let userChooseVerson = common.getLocalStorage("userChooseVerson") ? common.getLocalStorage("userChooseVerson") : ''; //判断用户是否登陆过
            // scene_code = '2'
            if(userChooseVerson && userChooseVerson !=''){
                //当前版本为用户手动切换版本
                if(userChooseVerson == '1') {
                    //标准
                    $(pageId + " #standardFooter").show();
                    $(pageId + " #score_rule").show();
                }
                if(userChooseVerson == '2' || userChooseVerson == '3') {
                    //新版
                    $(pageId + " #standardFooter").hide();
                    $(pageId + " #score_rule").hide();
                }
            }else{
                //根据用户版本进行渲染首页
                if((!scene_code && mobileWhole && mobileWhole.length) || scene_code == '1'){
                    //标准版
                    $(pageId + " #standardFooter").show();
                    $(pageId + " #score_rule").show();
                }
                if((!scene_code && !mobileWhole) || scene_code == '2' || scene_code == '3') {
                    //新版
                    $(pageId + " #standardFooter").hide();
                    $(pageId + " #score_rule").hide();
                }
            }
        },
        //更新后保留用户选中版本状态
        holdUpdateVer(){
            common.setLocalStorage("sceneRefresh",'0');
            common.setLocalStorage("userChooseRefresh",'0');
        },
        //时间日期转换
        formatDateString(dateStr) {
            // 转为字符串并拆解年月日
            const str = String(dateStr);
            const year = str.substring(0, 4);
            const month = str.substring(4, 6);
            const day = str.substring(6, 8);
            // 返回中文格式
            return `${year}年${month}月${day}日`;
        },
        //时间日期转换
        formatDateStringNew(dateStr) {
            // 转为字符串并拆解年月日
            const str = String(dateStr);
            const year = str.substring(0, 4);
            const month = str.substring(4, 6);
            const day = str.substring(6, 8);
            // 返回中文格式
            return `${year}-${month}-${day}`;
        },
        //判断风险等级
        getRiskLevel(riskLevel) {
            let riskLevelName = '';
            switch (riskLevel) {
                case '0':
                    riskLevelName = '低风险';
                    break;
                case '1':
                    riskLevelName = '低风险';
                    break;
                case '2':
                    riskLevelName = '低风险、中低风险';
                    break;
                case '3':
                    riskLevelName = '低风险、中低风险、中风险';
                    break;
                case '4':
                    riskLevelName = '低风险、中低风险、中风险、中高风险';
                    break;
                default:
                    riskLevelName = '低风险、中低风险、中风险、中高风险、高风险';
            }
            return riskLevelName;
        },
        isNumber(value) {
            return /^-?\d+(\.\d+)?$/.test(value);
        }
    }
    //暴露对外的接口
    module.exports = tools;
})