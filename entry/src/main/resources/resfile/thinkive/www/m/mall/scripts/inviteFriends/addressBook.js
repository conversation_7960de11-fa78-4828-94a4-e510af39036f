 // 通讯录分享
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		des = require("des"),
		VIscroll = require("vIscroll"),
		vIscroll = {"scroll":null,"_init":false},
		_pageId = "#inviteFriends_addressBook ";
	var common = require("common");
	var external = require("external");
	var gconfig = require("gconfig");
	var global = gconfig.global;
	var currentPage=0;
	var totalPages=0;
	var len=0;

	function init(){
		initPageAddress();
		currentPage=1;totalPages=0;
		initAddressBook();
		pageScrollInit();
	}

	function initPageAddress(){
		$(_pageId + " .friend_search input").val("");
		$(_pageId +" .contact_friends .my_date").hide();
		$(_pageId +" .contact_friends #v_container_productList").show();
	}

	//绑定事件
	function bindPageEvent(){
		//返回
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});

		//点击清空搜索框
		appUtils.bindEvent($(_pageId+" .contact_friends .friend_search .icon_close"),function(){
			$(_pageId + " .friend_search input").val("");
			var lens=$(_pageId + " .contact_friends li").length;
			if(lens>0){
				$(_pageId +" .contact_friends .my_date").hide();
				searchMobile("");
			}else{
				$(_pageId +" .contact_friends .my_date").show();
			}
		});

		//点击搜索
		appUtils.bindEvent($(_pageId+" .contact_friends .friend_search input"),function(e){
			var lens=$(_pageId + " .contact_friends li").length;
			if(lens>0){
				var title=$(this).val();
				$(_pageId +" .contact_friends .my_date").hide();
				searchMobile(title);
			}else{
				$(_pageId +" .contact_friends .my_date").show();
			}
		},"input");

		//点击“邀请”
		appUtils.preBindEvent($(_pageId+" #v_container_productList"),"a", function() {
			if(!$(this).hasClass("gray")){
				var phoneNum=$(this).parent().find("p").html();
				phoneNum=phoneNum.replace(/-/g,"");
				phoneNum=phoneNum.replace(/ /g,"");
				phoneNum=phoneNum.replace(/\+86/g,"");
				phoneNum=phoneNum.replace(/\&nbsp;/g,"");
				share(phoneNum,"6");
			}
		}, "click");
	}

	//调用通讯录
	function initAddressBook(){
		$(_pageId + " .friend_search input").val("");
		$(_pageId + " .contact_friends .friend_list ul li").remove();
		$(_pageId+" .contact_friends .friend_search input").val("");
		var setParam = {
				"funcNo" : "50225",
				"moduleName" : "mall"
		};
		external.callMessage(setParam);
	}


	/** 上下滑动刷新事件* */
	function pageScrollInit(){
		var height = $(_pageId+" #v_container_productList").offset().top;
		var height2 = $(window).height() - height  ;
		if(!vIscroll._init) {
			var config = {
				"isPagingType": false,
				"visibleHeight": height2, // 这个是中间数据的高度
				"container": $(_pageId+" #v_container_productList"),
				"wrapper": $(_pageId+" #v_wrapper_productList"),
				"downHandle": function() {
					currentPage = 1;
					getTotal();
				    $(_pageId+" .visc_pullUp").hide();
					$(_pageId + " .friend_list li").hide();
					getMobileSate();

				},
				"upHandle": function() {
					getTotal();
					if(currentPage<totalPages){
						$(_pageId+" .visc_pullUp").hide();
						currentPage+=1;
						getMobileSate();
					}
				},
				"wrapperObj": null
			};
			vIscroll.scroll = new VIscroll(config); // 初始化
			vIscroll._init = true;
		}else{
			vIscroll.scroll.refresh();
		}
		if(currentPage==totalPages){
			$(_pageId+" .visc_pullUp").hide();
		}else{
			$(_pageId+" .visc_pullUp").show();
		}
	}


	//根据关键字搜索查询手机及姓名
	function searchMobile(title){
		$(_pageId + " .contact_friends .addressName").parent().hide();
		$(_pageId + " .contact_friends .addressName").parent().attr("class","");
		var i=0,page=0;
		if(title!=""&&title!=null){
			$(_pageId + " .contact_friends .addressName:contains(" + title + ")").parent().addClass("pageAddShow");
			$(_pageId + " .contact_friends .addressPhone:contains(" + title + ")").parent().addClass("pageAddShow");
			$(_pageId + " .contact_friends .pageAddShow").each(function(){
				i++;
				if(i%10==1){
					page+=1;
				}
				$(this).addClass("addressPage_"+page);
			})
		}else{
			$(_pageId + " .contact_friends li").each(function(){
				i++;
				if(i%10==1){
					page+=1;
				}
				$(this).addClass("addressPage_"+page);
				$(this).addClass("pageAddShow");
			})
		}
		currentPage=1;totalPages=0;
		getTotal();
		getMobileSate();
		//$(_pageId + " .contact_friends .friend_list ul li.addressPage_1").show();
		pageScrollInit();
		if(len<=10){//隐藏滑动主键
			$(_pageId +" .visc_pullUp").hide();
		}
		if(totalPages==0){
			$(_pageId +" .contact_friends .my_date").show();
		}
		if(currentPage<totalPages){
			$(_pageId+" .visc_pullUp").show();
		}
	}

	//获取总的页数
	function getTotal(){
		if(totalPages==0){
			totalPages=$(_pageId + " .contact_friends .friend_list ul li.pageAddShow").length;
			len=totalPages;
			totalPages=totalPages%10==0?parseInt(totalPages/10):parseInt(totalPages/10)+1;
		}
	}

	//获取手机的状态
	function getMobileSate(){
		if(totalPages==0){
			return;
		}
		var page="addressPage_"+currentPage;
		var mobilestr="";
		$(_pageId + " .contact_friends .friend_list ul li."+page).each(function(){
			var mobile=$(this).find("p").html();
			mobile=mobile.replace(/-/g,"");
			mobile=mobile.replace(/ /g,"");
			mobile=mobile.replace(/\+86/g,"");
			mobile=mobile.replace(/\&nbsp;/g,"");
			mobilestr+=mobile+"$";
		})
		mobilestr=mobilestr.substring(0,mobilestr.length-1);
		var param={
			"mobileStr":mobilestr,
			"user_id":appUtils.getSStorageInfo("userId")
		}
		service.getPageMobileSate(param,function(data){
			if(data.error_no=="0"){
				var result=data.results;
				var i=0;
				$(_pageId + " .contact_friends .friend_list ul li."+page).each(function(){
					var phoneNum=$(this).find("p").html();
					phoneNum=phoneNum.replace(/-/g,"");
					phoneNum=phoneNum.replace(/ /g,"");
					phoneNum=phoneNum.replace(/\+86/g,"");
					phoneNum=phoneNum.replace(/\&nbsp;/g,"");
					if(phoneNum==result[i].plat_reg_mobile){
						if(result[i].flag=="1"){
							$(this).find("a").html("已注册").addClass("gray");
							$(this).removeClass("pageAddShow").addClass("pageAddShow");
						}
					}
					i++;
				})
				$(_pageId + " .friend_list li."+page).show();
				pageScrollInit();
				if(currentPage<totalPages){
					$(_pageId+" .visc_pullUp").show().removeClass("visc_loading");

				}
			}else{
				currentPage-=1;
				layerUtils.iAlert(data.error_info);
			}
		});
	}

	function share(phoneNum,share_type){
		// 简化分享链接
		var mobile = appUtils.getSStorageInfo("user").mobileWhole;
		var query_params = {};
		query_params["mobile"] = mobile;
		query_params["share_type"]=share_type;
		service.getShareInfo(query_params,function(data)
		{
			var error_no = data.error_no,
			error_info = data.error_info;
			if(error_no == "0"){
				if(data.results!=undefined && data.results.length>0)
				{
					mobile=desEncrypt("mobile",mobile);

					var result=data.results[0];

					var share_url=result.share_url;

					if (validatorUtil.isEmpty(share_url)){
						share_url=global.link;
					}


					if (share_url.indexOf("?") != -1)
					{
						share_url = share_url + "&mobile="+mobile;
					}
					else
					{
						share_url = share_url + "?mobile="+mobile;
					}

					var img_url=result.img_url;

					if (validatorUtil.isEmpty(img_url)){
						img_url=global.imgUrl;
					}

					var content=result.content;

					var title=result.title;

					var params = {};
					params["url"] = share_url;
					service.simplifyURL(params,function(data) {
						var error_no = data.error_no,
							error_info = data.error_info;
						if(error_no == "0"){
							share_url = data.results[0].shortUrl;
							layerUtils.iMsg(-1,"启动分享中...请稍后！",2);
							$(_pageId+" #pop_layer").hide();
							var custname=appUtils.getSStorageInfo("custName");
							content=content+share_url;
							var setdunxParam = {
									"funcNo" : "50221",
									"telNo" : phoneNum,
									"content" : content
							};
							external.callMessage(setdunxParam);
						}else{
							layerUtils.iAlert(error_info);
						}
					});
				}
			}
		});

	}


	function desEncrypt(key,value) {
	    var keyHex = des.enc.Utf8.parse(key);
	    var valueHex = des.enc.Utf8.parse(value);
	    var encrypted = des.DES.encrypt(valueHex, keyHex, {
	        mode: des.mode.ECB,
	        padding: des.pad.Pkcs7
	    });
		return encrypted.toString();
	}
	function destroy(){
		currentPage=1;totalPages=0;
	}
	function pageBack() {
		appUtils.pageBack();
	}
	var addressBook = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = addressBook;
});
