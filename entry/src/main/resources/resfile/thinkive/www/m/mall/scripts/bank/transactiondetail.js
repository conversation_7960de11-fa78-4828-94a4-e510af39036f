// 交易记录-详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#bank_transactiondetail ";

    function init() {
        var param = appUtils.getSStorageInfo("getTransation");
        var busiCode = param.busiCode;
        var busiCodeName = {
                "510": "充值",
                "511": "取现",
                "512": "购买",
                "513": "支取",
                "514": "付息",
                "515": "汇款充值",
                "516": "系统结息",
                "517": "挂单",
                "518": "撤单",
                "519": "转让",
                "520": "购买转让",
                "240": "购买",
                "241": "提前支取",
                "242": "到期系统自动兑付",
                "244": "到期支取",
                "245": "周期付息",
            }
            // 0 受理成功 1 受理失败 3 交易成功 4 交易失败
            var transStatusName = {
                "0": "受理成功",
                "1": "受理失败",
                "3": "交易成功",
                "4": "交易失败",
                "6": "撤单成功",
                "7": "撤单失败",
                "8": "确认成功",
                "9": "确认失败",
            }

        $(_pageId + "[data-name=create_date]").html(tools.ftime(param.crtDate));
        $(_pageId + "[data-name=create_time]").html(tools.ftime(param.crtTime));
        if(busiCode == "517"){
        	$(_pageId + "[data-name=tot_price]").html(tools.fmoney(param.transAmt));
        }else{
        	 $(_pageId + "[data-name=tot_price]").html(tools.fmoney(param.totAmt));
        }

        $(_pageId + "[data-name=digest]").html(busiCodeName[busiCode]);
        $(_pageId + "[data-name=order_state]").html(transStatusName[param.transStatus]);
        $(_pageId + "[data-name=remark]").html(param.remark && param.remark.replace(/\|/g, "<br>"));


    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #comeback"), function () {
            pageBack();
        });
    }


    function destroy() {
        $(_pageId + " #errorLog").hide();
        $(_pageId + " #errorLogs").hide();
        $(_pageId + " #remark").css("border-bottom", "1px solid #C8CDCF");

        $(_pageId + "[data-name=create_date]").html("");
        $(_pageId + "[data-name=create_time]").html("");
        $(_pageId + "[data-name=tot_price]").html("");
        $(_pageId + "[data-name=digest]").html("");
        $(_pageId + "[data-name=order_state]").html("");
        $(_pageId + "[data-name=remark]").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
