body,div, ul, li {
	margin: 0;
	padding: 0;
}
ul, li {
	list-style: none outside none;
}

/* layer begin */
@-webkit-keyframes slideinfrombottom {
    from { -webkit-transform: translateY(100%); }
    to { -webkit-transform: translateY(0); }
}
@keyframes slideinfrombottom {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}
@-webkit-keyframes slideouttobottom {
    from { -webkit-transform: translateY(0); }
    to { -webkit-transform: translateY(100%); }
}
@keyframes slideouttobottom {
    from { transform: translateY(0); }
    to { transform: translateY(100%); }
}
.ios-select-widget-box.olay {
	position: absolute;
	z-index: 500;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 1;
	background: rgba(0, 0, 0, 0.5);
	-webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    -moz-perspective: 1000;
    -ms-perspective: 1000;
    perspective: 1000;
}
.ios-select-widget-box.olay > div {
	position: fixed;
	z-index: 1000;
	width: 100%;
	height: 100%;
	background-color: #fff;
	bottom: 0;
	left: 0;
	visibility: visible;
	-webkit-transform: translateY(0);
	-webkit-animation-name: slideinfrombottom;
	transform: translateY(0);
	animation-name: slideinfrombottom;
	-webkit-animation-duration: 400ms;
	animation-duration: 400ms;
}
.ios-select-widget-box header.iosselect-header {
	height: 44px;
	line-height: 44px;
	background-color: #fff;
	width: 100%;
	z-index: 9999;
	text-align: center;
	border-bottom:1px solid #ebebeb;
	color: #333;
	font-size: 16px;
}
.ios-select-widget-box header.iosselect-header a {
	font-size: 14px;
	color: #1199ee;
	text-decoration: none;
	position: absolute;
	top: 0;
	-webkit-tap-highlight-color:rgba(0,0,0,0);
}
.ios-select-widget-box header.iosselect-header a.close {
	padding-left: 15px;
	height: 44px;
	line-height: 44px;
	color:#999;
	left: 0;
	display: none;
}
.ios-select-widget-box header.iosselect-header a.sure {
	width: 44px;
	height: 44px;
	line-height: 44px;
	font-size: 14px;
	right: 5px;
	/*background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAANNSURBVFhHvVdLa1NBFC66UfG10ZW48IVuXUiVkpm0qcbcG0UwtAsXgj+kmwpWEEQUbOsf6N5F8BdUELVdCCIaTHJjayp00YoNSvy+c08aQ16Tm8QPbu7NmfOYc+acMzMjrljPmINF304Fnnke+Ga56Jl1vH/x0e9ljpGHvCrWP/Jpc6bo2cWib7YC31ZhZAPfWRh6gvd9/J/V7yzHyENeylBW1fSOnDH7oOgBlO7Aw228nxXSZrQ6M7NHWZrAMfKQV2V2oGOOupTFDV+T8dPw5B2U/IF3C99uTR7XIWdQhrLUQV3O0Qj88YsQWkMIy4Efv6rkyKAO6qJO6lZya8h6+/Y7BHLF6xPnlNw3qEt0QnfbSORvj+5HJr/vyNQHas4hL1ZoS8l1MFlkvTx7TUkDR7gczCszp6QQmnQVJo2ShgYYn6ethiizZuH5zyjZ3itog7YQiRdCKN+4cgiz2mLtCmEAKKViF0q+Nfq3CdInYJO2ERK0V3QvNhAd7wtI5PN4NuHh741k8rCSGyDNSjqmneIEFiDwo1OHc0XupjmK8H6icni5pOQm0BbG0dKRc9xEEI6sjkVGNZPZi1x6FRq3bwPfP6BDLUGbtM0ELGM2T5UeGVD4WMKKnbGQNCeU3Ba0Sdv82MEzq/RICNL2noYdm0/sspI7gjbJ33UCDCW8ewOepVb7PMI4JjrofdrcVXJX0Cblui5BWKZoofTQt6tr3sQpHYLxxMndMc88UrITaDNcAock1D7+QbyEUMEzlpGBkhWhQZ5JqOxOqCchyxAl0a0MWdMQeKmRQNvGssi3+fg5kTiibE5oKEP8ODciKTVsJDoJGt8seLGzOuyMhkYUpRWXUuYOFKwWUrG4knoCbaFct6UVE9wYSPgfm1EpOXaMtmhTSWGSwSOu67yShgbaoK2mpePaYlY4QPZ/DmyHIB2bFBuefaikOuRIhrJCYgz9SNZ2n6gxodZzg5yE6PXsFyfndo/l0uEGdCwXpxyO5TVwlrIc4QFyPkp1SLZDNlxznIR7jahezdh0KlAiV7O8N36pU8fkGHnIKzJhZfV+NfsXLBes36IqdLqc1g6dUbpkW8j13DPTMIQ6Nq/DHLGV0Et+kyYhn3a/no+M/AXOlX7le14jngAAAABJRU5ErkJggg==") no-repeat center;
	background-size:16px 16px ;*/
}
.ios-select-widget-box {
	padding-top: 0;
}
.ios-select-widget-box .one-level-contain,
.ios-select-widget-box .two-level-contain,
.ios-select-widget-box .three-level-contain,
.ios-select-widget-box .four-level-contain,
.ios-select-widget-box .five-level-contain {
	height: 100%;
	overflow: hidden;
}
.ios-select-widget-box .iosselect-box {
	overflow: hidden;
	padding:  0 10%;
}
.ios-select-widget-box .iosselect-box > div {
	display: block;
	float: left;
}
.ios-select-widget-box ul {
	background-color: #fff;
}
.ios-select-widget-box ul li {
	font-size: 16px;
	height: 35px;
	line-height: 35px;
	background-color: #fff;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: center;
	color: #999;
	font-family: arial;
	transition: all 0.2s ease-in-out; 
}
.ios-select-widget-box ul li.at {
	font-size: 18px;
	color:#000;
}
.ios-select-widget-box ul li.side1 {
	font-size: 16px;
	color: #666;
}
.ios-select-widget-box ul li.side2 {
	font-size: 16px;
	color: #888;
}
.ios-select-widget-box.one-level-box .one-level-contain {
	width: 100%;
}
.ios-select-widget-box.one-level-box .two-level-contain,
.ios-select-widget-box.one-level-box .three-level-contain,
.ios-select-widget-box.one-level-box .four-level-contain,
.ios-select-widget-box.one-level-box .five-level-contain
{
	width: 0;
}
.ios-select-widget-box.two-level-box .one-level-contain, 
.ios-select-widget-box.two-level-box .two-level-contain {
	width: 50%;
}
.ios-select-widget-box.two-level-box .three-level-contain,
.ios-select-widget-box.two-level-box .four-level-contain,
.ios-select-widget-box.two-level-box .five-level-contain
{
	width: 0;
}
.ios-select-widget-box.three-level-box .one-level-contain, 
.ios-select-widget-box.three-level-box .two-level-contain {
	width: 33.333%;
}
.ios-select-widget-box.three-level-box .three-level-contain {
	width: 33.333%;
}
.ios-select-widget-box.three-level-box .four-level-contain
.ios-select-widget-box.three-level-box .five-level-contain {
	width: 0%;
}

.ios-select-widget-box.four-level-box .one-level-contain, 
.ios-select-widget-box.four-level-box .two-level-contain,
.ios-select-widget-box.four-level-box .three-level-contain,
.ios-select-widget-box.four-level-box .four-level-contain {
	width: 25%;
}
.ios-select-widget-box.four-level-box .five-level-contain {
	width: 0%;
}

.ios-select-widget-box.five-level-box .one-level-contain, 
.ios-select-widget-box.five-level-box .two-level-contain,
.ios-select-widget-box.five-level-box .three-level-contain,
.ios-select-widget-box.five-level-box .four-level-contain,
.ios-select-widget-box.five-level-box .five-level-contain {
	width: 20%;
}
.ios-select-widget-box .cover-area1 {
	width: 100%;
	border: none;
	border-top: 1px solid #ebebeb;
	position: absolute;
	top: 149px;
	margin: 0;
	height: 0;
}
.ios-select-widget-box .cover-area2 {
	width: 100%;
	border: none;
	border-top: 1px solid #ebebeb;
	position: absolute;
	top: 183px;
	margin: 0;
	height: 0;
}
.ios-select-widget-box #iosSelectTitle {
	margin: 0;
    padding: 0;
    display: inline-block;
    font-size: 16px;
    font-weight: normal;
    color: #333;
}
.ios-select-body-class {
	overflow: hidden;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.ios-select-widget-box.olay > div > .ios-select-loading-box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0,0,0,.5);
    display: none;
}
.ios-select-widget-box.olay > div > .ios-select-loading-box > .ios-select-loading { 
	width: 50px;
	height: 50px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-top: -25px;
	margin-left: -25px;
	background: url(data:image/png;base64,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) no-repeat 0 0;
    background-size: contain;
    -webkit-animation: loading-keyframe 1s infinite linear;
            animation: loading-keyframe 1s infinite linear;
}
@-webkit-keyframes loading-keyframe {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes loading-keyframe {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}