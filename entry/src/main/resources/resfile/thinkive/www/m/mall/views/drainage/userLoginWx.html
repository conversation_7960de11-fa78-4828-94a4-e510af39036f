<div class="page" id="drainage_userLoginWx" data-pageTitle="晋金财富" data-refresh="true">
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a href="javascript:void(0);" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">注册</h1>
			</div>
		</header>
		<article class="bg_blue">
			<!-- LOGIN_PAGE START -->
			<div class="login_page">
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2">
						<div class="ui label">
							<img src="front/images/label_01.png" width="12" alt="">
						</div>
						<input type="text" readonly="readonly" class="ui input" custom_keybord="0" id="phontNum" maxlength="20" placeholder="" style="background: transparent"/>
					</div>
				</div>
				<div class="grid_06 grid_02">
					<div class="ui field text border login_input input_box2" id="yzmBox">
						<div class="ui label">
							<img src="front/images/label_02.png" width="12" alt="">
						</div>
						<input class="ui input" custom_keybord="0" maxlength="6" type="tel" id="pwd" placeholder="验证码">
						<a id="getYzm" data-state="true" class="drainShow">获取验证码</a>
					</div>
				</div>
				<div class="grid_02 grid drainShow" style="margin-top: -0.1rem;text-align: left">
					<p style="padding-left: 0.3rem">检测到您已在晋金所开户，请授权登录</p>
				</div>
				<!-- 语音验证码 -->
				<div class="finance_det recharge_det" >
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
					</dl>
				</div>
				<div class=" grid_02 text-center drainShow" style="margin-top: -0.06rem;">
					<div class="radio_box">
						<div class="ui radio">
							<input type="radio" id="input_radio2" checked="checked">
							<label id="isChecked" >我已阅读并同意签署<a href="javascript:void(0);" class="deal_box"></a></label>
						</div>
					</div>
				</div>
				<div class="grid_06 drainShow">
					<a href="javascript:void(0);" class="ui button block rounded btn_login " id="registered">注&nbsp;&nbsp;册</a>
				</div>
				<!-- 协议相关弹窗 -->
				<div class="agreement_layer display_none">
					<div class="agreement_popup in">
						<div class="agreement_popup_header">
							<div class="new_close_btn"></div>
							<h3>相关协议</h3>
						</div>
						<ul class="agreement_list flex vertical_line"></ul>
					</div>
				</div>
			</div>
			<!-- LOGIN_PAGE END -->
		</article>
	</section>
</div>