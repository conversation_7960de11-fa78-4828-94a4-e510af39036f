//银行产品-撤单
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_cancelOrder ";
    var ut = require("../common/userUtil");
    var _pageCode = "bank/cancelOrder";
    var tools = require("../common/tools");
    var sms_mobile = require("../common/sms_mobile");
    var productInfo;// 产品信息
    var bankElectornReservedMobile; //电子银行预留手机号
    function init() {
        sms_mobile.init(_pageId);
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        productInfo = appUtils.getSStorageInfo("productInfo");
        initProductInfo();
        getAccountInfo();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "bank/bankDetail");
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                // 获取验证码
                var param = {
                    "mobile_phone": bankElectornReservedMobile,
                    "type": common.sms_type.bankCancelOrder,
                    "send_type": "0",
                    "mobile_type": "2",
                    "bank_abbr": productInfo.prod_name
                };
                sms_mobile.sendPhoneCode(param)
            }
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            var param = {
                bank_channel_code: productInfo.bank_channel_code,
                prod_code: productInfo.prod_code,
                hang_list_grp_srlno: productInfo.hang_list_grp_srlno,
                order_no: productInfo.order_no,
                brnd_sris: productInfo.brnd_sris,
                sms_code: verificationCode,
                sms_mobile: bankElectornReservedMobile,
            };
            trade(param);
        });
    }

    function trade(param) {
        service.reqFun151012(param, function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "bank/cancelOrderResult");
        })
    }

    function initProductInfo() {
        $(_pageId + " .prod_sname").text(productInfo.prod_name);
        $(_pageId + " .rate").text(tools.fmoney(productInfo.bas_int_rate) + "%");
        $(_pageId + " [data-name=hang_list_amt]").text(tools.fmoney(productInfo.hang_list_amt));//挂单金额
        $(_pageId + " [data-name=save_dt]").text(productInfo.save_dt); //持有时间
        $(_pageId + " [data-name=exprd_date]").text(tools.ftime(productInfo.exprd_date)); //到期日期
        $(_pageId + " [data-name=hang_list_day_earn]").text(tools.fmoney(productInfo.hang_list_day_earn)); //到期可获利息
    }

    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                bankElectornReservedMobile = results.mobile_phone;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        $(_pageId + " .prod_sname").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " [data-name=hang_list_amt]").text("");//挂单金额
        $(_pageId + " [data-name=save_dt]").text(""); //持有时间
        $(_pageId + " [data-name=exprd_date]").text(""); //到期日期
        $(_pageId + " [data-name=hang_list_day_earn]").text(""); //到期可获利息
        $(_pageId + " #verificationCode").val("");
        sms_mobile.destroy();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
