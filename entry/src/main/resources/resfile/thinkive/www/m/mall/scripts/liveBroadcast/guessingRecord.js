//消息列表页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#liveBroadcast_guessingRecord ";
    var _pageCode = "liveBroadcast/guessingRecord";
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var cur_page, live_activity_id;
    var isEnd = false;
    var luckflag;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        vIscroll = { "scroll": null, "_init": false };
        cur_page = 1;
        live_activity_id = sessionStorage.live_activity_id;
        if (appUtils.getPageParam("luckflag")) {
            appUtils.setSStorageInfo("luckflag", appUtils.getPageParam("luckflag"));
        }
        luckflag = appUtils.getSStorageInfo("luckflag");
        setData(live_activity_id, false);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        isEnd = false;
        $(_pageId + " .list").html("");
        cur_page = 1;
    };
    //初始化渲染
    function setData(live_activity_id, flag) {
        let requuestData = {
            activity_id: live_activity_id,
            current: cur_page + ''
        };
        service.reqFun112008(requuestData, (data) => {
            if (data.error_no == '0') {
                var results = data.results[0];
                let html = "";
                // let nullHtml =  "<ul  class='m_text_center m_padding_10_10'>暂无数据</ul>"
                let str = `
                <ul class="flex m_text_center" style="background: gainsboro;
                color: #3f4e59;
                font-size: 0.12rem;
                line-height: 0.28rem;
                height: 0.36rem;">
                    <li>
                        开奖日期
                    </li>
                    <li>
                        竞猜答案
                    </li>
                    <li>
                        开奖状态
                    </li>
                    <li>
                        奖励积分
                    </li>
                </ul>
                `
                // console.log(res.guessRecordsList)
                let list = results.data;
                var totalPages = data.results[0].totalPages; //总页数
                // if(!list || list == 'null') html = nullHtml;
                // if(!list.length){
                //     html = nullHtml
                // }else{
                if (!list) list = [];
                list.map(item => {
                    let guess_date = item.guess_clear_date.slice(0, 4) + '-' + item.guess_clear_date.slice(4, 6) + '-' + item.guess_clear_date.slice(6, 8);
                    html += `
                        <ul class="flex m_text_center m_border_bottom_aaa">
                            <li>${guess_date}</li>
                            <li>${item.guess_answer == "1" ? "涨" : "跌"}</li>
                            <li>${item.guess_reward_status == "1" ? "未开奖" : item.guess_reward_status == 2 ? "竞猜正确" : "竞猜错误"}</li>
                            <li>${item.points ? item.points : ''}</li>
                        </ul>
                    `
                })
                // }
                // $(_pageId + " .list").html(html)

                if (totalPages == cur_page) {
                    isEnd = true;
                    html += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && list.length == 0) {
                    isEnd = true;
                    html = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                // console.log(flag)
                if (flag) {
                    $(_pageId + " .list").append(html);
                } else {
                    $(_pageId + " .list").html(str + html);
                }
                // $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    endTime = "";
                    setData(live_activity_id, false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        setData(live_activity_id, true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }
    /*
     * 返回
     */
    function pageBack() {
        if (luckflag == 'dargonYearDraw') {
            let url = appUtils.getSStorageInfo("front_pageUrl")
            if (url.indexOf("?") > -1) {
                var skip_url = url.split("?")[0];
                var parameter = url.split("?")[1];
                var parameter_arr = parameter.split("&"); //各个参数放到数组里
                var urlInfo = {};//url的参数信息
                for (var i = 0; i < parameter_arr.length; i++) {
                    num = parameter_arr[i].indexOf("=");
                    if (num > 0) {
                        name = parameter_arr[i].substring(0, num);
                        value = parameter_arr[i].substr(num + 1);
                        urlInfo[name] = value;
                    }
                }
                appUtils.pageInit(_pageCode, skip_url, urlInfo);
            }
            appUtils.clearSStorage("luckflag");
            appUtils.clearSStorage("front_pageUrl");
        } else {
            appUtils.pageBack();
        }


    }
    var liveBroadcast_guessingRecord = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_guessingRecord;
});
