/**
 * 商城业务常量配置
 */
define(function(require, exports, module) {

	var serviceConstant = {
		register:{
			"account_name":"1"
		},
		
		BusConstants:{ 
			"SUCCESS":"0"
		},
		product_shelf:{ 
			"SHELF_ON":"1",
			"SHELF_OFF":"0"
		},
		
		//上下架
		product_shelf:{ 
			"SHELF_ON":"1",
			"SHELF_OFF":"0"
		},
		
		product_sub_type:{ //产品子类别
			"FUND":"0", //基金
			"FINANCIAL":"1", //理财
			"INFO":"3", //资讯
			"SERVICE":"2" //服务
		},
		service_type:{ //服务类别
			"phone":"0", //电话
			"email":"1", //邮件
			"sms":"2", //短信
			"im":"3", //im
			"youxianji":"4",//优先级
			"iy34":"3,4",//IM优先级
			"yi43":"4,3",//优先级IM
			"mms":"5",//彩信
			"app":"6"//APP
		},
		/**
		 * 用户持久化常量 保存.
		 */
		session:{   
			"USER":"user",
			"ORDER":"order",
			"ORDER_PAY":"order_pay",
			"URL_PARAM":"url_param"
		},
		/**
		 * 是否绑定资金账号
		 */
		is_fundaccount:{ 
			"YES":"1",
			"NO":"0"
		},
		/**
		 * 是否已做风险测评
		 */
		is_risk:{ 
			"YES":"1",
			"NO":"0",
			"LOW":"2",
			"Theree":"3"
		},
		/**
		 * 是否已签署协议
		 */
		is_sign:{ 
			"YES":"1",
			"NO":"0"
		},
		
		/**
		 *
		 */
		entrust_state:{ 
			"NO":"0",//未委托
			"SUCCESS":"1", //委托成功
			"FAIL":"2"//委托失败
		},
		
		/**
		 * 是否是首次购买
		 */
		first_buy:{
		"YES":"1",//追加购买
		"No":"0"  //首次购买
		},
		
		/**
		 * 订单状态
		 */
		order_state:{ 
			"NEW":"0", //新建
			"SUBMIT":"1", //待提交
			"SUBMIT_SUCCESS":"2", //提交成功
			"FAIL":"3",     //失败
			"SUCCESS":"4", //成交
			"CANCEL":"5",  //取消
			"revoke":"6",  //撤单
			"REFUND":"7"   //已退款
		},
		/**
		 * 业务类别
		 */
		business_type:{
			"SUBSCRIBE":"0", //认购
			"PURCHASE":"1", //申购
			"REDEMPTION":"2", //赎回
			"BUY":"3", //买入
			"SELL":"4" //卖出
		},

        /**
         *  需要加密的参数名
         */
		rsaParam :{
            TRADE_PWD:"trade_pwd",
            FUND_PWD:"fund_pwd",
            BANK_PWD:"bank_pwd"
         },
         gender:{
			"UNKNOWN" : "2", //未知
			"MAN" : "0", //男
			"WOMAN" : "1" //女
		},
        thr_pay:{
			"UPOP_PAY_URL" : "/servlet/upop/IndexAction?function=ajaxUpop"  //银联支付
		},
        encrypt_key:{
            "DES":"thinkive_mall",
            "AES":"thinkive_mall"
        }

	};
	
	//暴露对外的接口
	module.exports = serviceConstant;
});