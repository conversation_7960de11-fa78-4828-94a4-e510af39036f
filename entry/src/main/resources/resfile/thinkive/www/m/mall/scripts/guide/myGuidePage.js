define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#guide_myGuidePage";
    require("../common/swiper");

    function init() {
        addGuide();
    }

    function bindPageEvent() {

        appUtils.bindEvent($(_pageId + " .tiaoguo"), function () {
            pageBack();
        });
    }

    function addGuide() {
        new Swiper($(_pageId).find(".guide_container"), {
            pagination: ".swiper-pagination",
            autoplay: 5000,
            loop: true,
            paginationElement: "li",
            bulletActiveClass: "check",
            autoplayDisableOnInteraction: false,
            // autoplayStopOnLast: true
        });
    }

    function destroy() {
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var myGuidePage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = myGuidePage;
});
