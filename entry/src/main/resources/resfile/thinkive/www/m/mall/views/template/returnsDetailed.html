<div class="page" id="template_returnsDetailed" data-pageTitle="收益明细" data-refresh="false" data-pageLevel="0">
    <section class="main fixed add_padding" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a herf="javascript:void(0)" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center">收益明细</h1>
                <a href="javascript:void(0)" class="btn_filter" operationType="1" operationId="btn_filter" operationName="更多查询">更多查询</a>
                <div id="filter_layer" class="pop_layer" operationType="1" operationId="pop_layer" operationName="关闭查询" style="display:none;"></div>
                <div class="record_filter" id="jymx_filter" style="display:none;z-index: 2000">
                    <div class="block">
                        <div style="margin-left: 0.05rem;">按日期</div>
                        <ul id="query_date" style="overflow: hidden;">
                            <li><a href='javascript:void(0)' data-value='' class="active" operationType="1" operationId="query_date" operationName="所有">所有</a></li>
                            <li><a href='javascript:void(0)' data-value='30' operationType="1" operationId="query_date_30" operationName="近1月">近1月</a></li>
                            <li><a href='javascript:void(0)' data-value='90' operationType="1" operationId="query_date_90" operationName="近3月">近3月</a></li>
                            <li><a href='javascript:void(0)' data-value='180' operationType="1" operationId="query_date_180" operationName="近6月">近6月</a></li>
                        </ul>
                        <div class="date_select" id="query_input" style="border-top: 0;margin-top: 0">
                            <div class="date_text">
                                <input type="text" placeholder="选择开始日期" readonly="readonly" id='startTime' value="">
                            </div>
                            <span class="fg_line"></span>
                            <div class="date_text">
                                <input type="text" placeholder="选择结束日期" readonly="readonly" id='endTime' value="">
                            </div>
                        </div>
                    </div>
                    <div class="btn" id='query-button'>
                        <a href="javascript:void(0)" class="active reset" data-value="reset" id="reset" operationType="1" operationId="reset" operationName="重置">重置</a>
                        <a href="javascript:void(0)" class="active" data-value="confirm" id="confirm" operationType="1" operationId="confirm" operationName="确认">确认</a>
                    </div>
                    <!--<div class="block">
                        <div style="margin-left: 0.05rem;">按时间</div>
                        <ul id="query-date" style="overflow: hidden;">
                            <li><a href='javascript:void(0)' class='active' data-value='30' id="init_a">近一个月</a></li>
                            <li><a href='javascript:void(0)' data-value='90'>近三个月</a></li>
                        </ul>
                        <div class="date_select" id="query-input">
							<div class="date_text">
								<input type="text" placeholder="选择开始日期" readonly="readonly" id='startTime'>
							</div>
							<span class="fg_line"></span>
							<div class="date_text">
								<input type="text" placeholder="选择结束日期" readonly="readonly" id='endTime'>
							</div>
						</div>
                    </div>
					<div class="btn" id='query-button'>
						<a href="javascript:void(0)" class="active reset" data-value="reset">重置</a>
						<a href="javascript:void(0)" class="active" data-value="confirm">确认</a>
					</div>-->
                </div>

            </div>
        </header>
        <article class="bg_blue no_padding">
            <!-- TRANSACTION_RECORD START -->
            <div class="transaction_record">
                <div class="record_inner" style="position: fixed;z-index: 1;top:44px;width: 100%;">
                    <table width="100%" cellpadding="0" cellspacing="0">
                        <tr id="title">
                            <th class="m_width_33">日期</th>
                            <!-- <th class="m_width_33">净值/持有份额</th> -->
                            <th class="m_width_33">日收益</th>
                        </tr>
                    </table>
                </div>
                <div style="top:88px;position: fixed;z-index: 1;width:100%;bottom: 0;overflow: auto;">
                    <div id="v_container_productList">
                        <div class="visc_wrapper" id="v_wrapper_productList" data-iscrollPageId="Products_productList">
                            <div class="visc_scroller">
                                <div class="visc_pullDown" style="display:none;">
                                    <span class="visc_pullDownIcon"></span>
                                    <div class="visc_pullDownDiv">
                                        <span class="visc_pullDownLabel">下拉加载上一页</span><br/>
                                        时间更新于：<span class="visc_pullDownTime"></span>
                                    </div>
                                </div>
                                <!-- TRADE_HISTORY START -->
                                <div class="trade_history">
                                    <div class="transaction_record">
                                        <div>
                                            <table width="100%" cellpadding="0" cellspacing="0" id="concent"></table>
                                        </div>
                                    </div>
                                </div>

                                <div class="visc_pullUp" style="display:none;">
                                    <span class="visc_pullUpIcon" style="display:none;"></span>
                                    <div class="visc_pullUpDiv" style="display:none;">
                                        <span class="visc_pullUpLabel">上拉加载下一页</span><br/>
                                        时间更新于：<span class="visc_pullUpTime"></span>
                                    </div>
                                </div>

                                <div class="new_none" style="display:none;">
                                    <div style="display:block;font-size:0.14rem;text-align:center;color:#778590;padding:0.15rem;">
                                        没有更多数据
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <!-- TRANSACTION_RECORD END -->
        </article>
    </section>
</div>
