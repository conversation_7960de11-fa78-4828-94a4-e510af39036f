/**
 *
 * <AUTHOR>
 * @Description 帮助中心内容页
 * @version V1.0
 * @Date 2017-7-26 下午4:56:37
 */

define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        gconfig = require("gconfig"),
        _pageId = "#moreDetails_helpCenterContentpage ";

    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        //显示二级菜单名称
        $(_pageId + "#menu_level2").html(appUtils.getSStorageInfo("helpCenterContentInfo").name);
        showQuestionList();

    }


    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();

        });
        //客服电话
        appUtils.bindEvent($(_pageId + " #phone"), function () {
            $(_pageId + "#phone_div").show();
        });
        //取消拨打电话
        appUtils.bindEvent($(_pageId + " #call_off"), function () {
            $(_pageId + "#phone_div").hide();
        });
        //拨打电话
        appUtils.bindEvent($(_pageId + " #call_up"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = gconfig.global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //在线客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum("moreDetails/helpCenterSubpage")
        });
        //问题点击事件
        appUtils.preBindEvent($(_pageId + " #question_list"), $(_pageId + " #question_list li"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target).closest("li")[0];
            //打开/闭合相应信息
            var node2 = $(node).children("div")
            if (node2.css('display') == "none") {
                $(node).addClass("on");
                node2.show();
            } else {
                $(node).removeClass("on");
                node2.hide();
            }
        });

    }

    //显示问题列表

    function showQuestionList() {
        var callBackFun = function (resultVo) {
            if (resultVo.error_no == 0) {
                var html = "";
                var data = resultVo.results;
                for (var i = 0; i < data.length; i++) {
                    if (data[i].title == "目前支持绑定哪些银行卡？") {
                        html += "<li><h5 class='search_span'>" + data[i].title + "</h5>";
                        html += "<div class='da' style='display:none' id='bank_table'><p>" + data[i].html_content + "</p></div></li>";
                        $(_pageId + "#search_result_ul").html($(_pageId + "#search_result_ul").html() + html);
                        service.reqFun102014({}, bankCallBackFun);

                    } else {
                        if (data[i].title == "旧银行卡和银行挂失证明丢失怎么办？") {
                            data[i].title = "旧银行卡丢失怎么办？";
                            data[i].text_content = "如旧银行卡丢失，需上传挂失证明或扫描件。如旧银行卡和银行卡挂失证明文件均丢失，请登录晋金所官网www.sxfae.com在帮助中心-密码和安全-更换绑定的银行卡，下载《换卡免责声明》模板，填写后拍照或扫描上传。";
                        }
                        html += "<li><h5>" + data[i].title + "</h5>"
                            + "<div class='da' style='display:none'>" + data[i].html_content + "</div></li>";
                    }
                }

                $(_pageId + "#question_list").html(html);
            } else {
                $(_pageId + "#question_list").html("<p style='padding: 0 0.15rem'>还没有相关问题哦,问问客服小金宝吧</p>");
            }

        };
        service.reqFun102062({"catalog_id": appUtils.getSStorageInfo("helpCenterContentInfo").id}, callBackFun);
    }

    //显示银行卡的回调函数
    function bankCallBackFun(resultVo) {
        if (resultVo.error_no == 0) {
            var html_str = "";
            var dataList = resultVo.results;
            html_str += "<p>";
            html_str += "<p><table width='100%' border='0' cellspacing='0' cellpadding='0' style=' border:solid 1px #AAAAAA;'>";
            html_str += "<tr>" +
                "<th scope='col' style='background:#FF7744;width:20%;border-right:solid 1px #AAAAAA;text-align: center;line-height: 3em;'>银行</th>" +
                "<th scope='col' style='background:#FF7744;width:20%;border-right:solid 1px #AAAAAA;text-align: center;line-height: 3em;'>单笔限额</th>" +
                "<th scope='col' style='background:#FF7744;width:20%;border-right:solid 1px #AAAAAA;text-align: center;line-height: 3em;'>当日限额</th>" +
                "<th scope='col' style='background:#FF7744;width:40%;text-align: center;line-height: 3em;'>备注</th>" +
                "</tr>";
            for (var j = 0; j < dataList.length; j++) {
                var bank_name = dataList[j].bank_name;
                var day_limit = dataList[j].day_limit;
                var single_limit = dataList[j].single_limit;
                var remark = dataList[j].remark;
                if (day_limit < 0) {
                    day_limit = "不限";
                }
                if (single_limit < 0) {
                    single_limit = "不限";
                }
                if (day_limit >= 10000) {
                    day_limit = day_limit / 10000 + "万";
                }
                if (single_limit >= 10000) {
                    single_limit = single_limit / 10000 + "万";
                }
                var recommend_flag = dataList[j].recommend_flag;//是否推荐 0不推荐1推荐
                if(recommend_flag == '1') {
                    html_str += "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + bank_name + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + single_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + day_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;color: #e5443c;'><img src='images/star.png' style='color: #e5443c;width: 0.18rem;margin-top: -0.04rem;'>" + remark + "</td></tr>";
                }else{
                    html_str += "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + bank_name + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + single_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + day_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + remark + "</td></tr>";
                }
            }
            html_str += "</table></p>";
            $(_pageId + "#bank_table").html(html_str);
        }
    }

    function destroy() {
        $(_pageId + "#question_list").html("");
        $(_pageId + "#phone_div").hide();

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var helpCenter = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = helpCenter;
});
