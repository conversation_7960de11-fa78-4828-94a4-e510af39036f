
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        gconfig = require("gconfig"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "fundSupermarket/fundsBuy",
    _pageId = "#fundSupermarket_fundsBuy ";
    var ut = require("../common/userUtil");
    var monkeywords = require("../common/moneykeywords");
    var global = gconfig.global;
    var purchaseAmount, increaseAmount,purchaseSingle;
    var prodList;
    var isFirstDistribution = false; // 是否首次分配
    var checkedProdNum = 0;
    var financial_prod_type,zone_prod_type,series_name;
    var str;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #empty").hide();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        $(_pageId + " #inputspanid span").text("").removeClass("active").css({ color: "rgb(153, 153, 153)" });
        common.systemKeybord(); // 解禁系统键盘
        // series_name = appUtils.getSStorageInfo("series_name");
        isFirstDistribution = true;
        // financial_prod_type = appUtils.getSStorageInfo("financial_prod_type_pro");
        // financial_prod_type = localStorage.financial_prod_type_pro;
        // zone_prod_type = localStorage.zone_prod_type_pro?localStorage.zone_prod_type_pro:'';
        pageInitList();
        // $("#czje_G10009").val("2000")
        // $("#czje_G22013").val("1000")
        // $("#czje").val("3000")
    }

    // 初始化页面数据
    async function pageInitList() {
        var arr = await getList();
        var _arr = [];
        let html = "";
        purchaseAmount = 0, increaseAmount = 1;
        if (arr && arr.length) {
            arr.forEach(item => {
                let sub_html = '';
                
                if (item.zone_prod_list && item.zone_prod_list.length) {
                    item.zone_prod_list.forEach((its, i) => {
                        _arr.push(its);
                        its.checked = true; // 默认选中
                        its.buy_money = 0;
                        var income_period_type_desc = its.income_period_type_desc ? its.income_period_type_desc : '--' //近多少年化
                        var income_period_list = (its.income_period_list == '1' && its.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                        var income_period_list_chg = (its.income_period_list == '1' && its.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                        let dk_income_rate = its.dk_income_rate ? tools.fmoney(its.dk_income_rate) : '--'
                        var dk_income_rate_chg = its.dk_income_rate_chg ? tools.fmoney(its.dk_income_rate_chg) : '--'
                        sub_html += `
                        <div class="item">
                            <em style="display: none" class='productInfo'>${JSON.stringify(its)}</em>
                           
                            <div class="prod_detail">
                                <div class="prod_title"> 
                                    <div class="ui checkbox" data-code=${its.fund_code}>
                                        <input type="checkbox" id="checkbox_${its.fund_code}" ${its.checked ? 'checked="checked"' : ''}>
                                        <label style="padding-left: 0.18rem"></label>
                                    </div>
                                    <span class="m_font_size16 m_bold">${its.prod_name_list}(${its.fund_code})</span>
                                    <em style="display: none">${JSON.stringify(its)}</em>
                                    <span class="to_detail" operationType="1" operationId="to_detail_${its.fund_code}" fundCode="${its.fund_code}" contentType="1" operationName="产品详情" style="color: #319ef2;
                                    float: right;font-size:0.12rem;line-height: 0.28rem;">详情 <i></i></span>
                                </div>
                                <div class="prod_desc" >
                                    <div class="prod_desc_item m_text_left prod_desc_item1 ${income_period_list}" style="width:20%">
                                        <span class="m_text_red m_font_size16 m_bold">${dk_income_rate}%</span>
                                        <div class="m_font_size12 m_text_999">${income_period_type_desc}年化</div>
                                    </div>
                                    <div class="prod_desc_item m_text_left prod_desc_item1 ${income_period_list_chg}" style="width:20%">
                                        <span class="m_text_red m_font_size16 m_bold">${dk_income_rate_chg}%</span>
                                        <div class="m_font_size12 m_text_999">${income_period_type_desc}</div>
                                    </div>
                                    <div class="prod_desc_item text-center">
                                        <span>${its.risklevel_name}</span>
                                        <div class="m_font_size12 m_text_999">风险等级</div>
                                    </div>
                                    <div class="prod_desc_item" style="width: 38%;">
                                        <div class="prod_buy">
                                            <i class="symbol">￥</i>
                                            <span class="prod_buy_money" data-id="${its.fund_code}" id='inputspanid${its.fund_code}'><span text=''></span></span>
                                            <input id="czje_${its.fund_code}" style="display: none;" readonly="readonly">
                                            <span class="prod_buy_unit">元</span>
                                        </div>
                                        <div class="m_font_size12 m_text_999" style="padding-left: 0.16rem">购买金额</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        `;
                        purchaseAmount = common.floatAdd(purchaseAmount,purchaseSingle);
                        checkedProdNum += 1;
                    })
                    html += ` <div class="prod_list">
                    <div class="prod_item">${sub_html}</div>
                </div>`
                }

            })
        }
        prodList = _arr;
        $(_pageId + " #prod_list").html(html);
        $(_pageId + " #prod_list .item .prod_buy_money span").addClass("unable");//默认输入框失去焦点
        str = `${tools.changeTwoDecimal_f(purchaseAmount)}元起购，${tools.changeTwoDecimal_f(increaseAmount)}元递增`;

        // 购买金额回显
        if (appUtils.getSStorageInfo("_prePageCode") == "template/publicOfferingDetail" || appUtils.getSStorageInfo("_prePageCode") == "fundSupermarket/purchaseDetail") {
            var _buyMoneys = appUtils.getSStorageInfo("buy_moneys");
            prodList = appUtils.getSStorageInfo("prodList");
            
            if (parseFloat(_buyMoneys) > 0) {  // 单个产品金额变更，总金额肯定变
                $(_pageId + " #czje").val(tools.fmoney(_buyMoneys).replace(/,/g, ""));
                $(_pageId + " #inputspanid span").html(tools.fmoney(_buyMoneys)).addClass("active").addClass("unable").css('color', '#000000');
                recalculateAmount();
                if (prodList && prodList.length) {
                    prodList.forEach(its => {
                        if (its.buy_money && parseFloat(its.buy_money) > 0) {
                            $(_pageId + ` #czje_${its.fund_code}`).val(tools.fmoney(its.buy_money).replace(/,/g, ""));
                            $(_pageId + ` #inputspanid${its.fund_code} span`).html(tools.fmoney(its.buy_money)).addClass("active").addClass("unable").css('color', '#000000');
                            $(_pageId + `#checkbox_${its.fund_code}`).attr("checked", "checked");
                            its.checked = true;
                        } else {
                            $(_pageId + `#checkbox_${its.fund_code}`).removeAttr("checked");
                            its.checked = false
                        }
                    })
                }
            } else {
                recalculateAmount();
                if (prodList && prodList.length) {
                    prodList.forEach(its => {
                        // its.checked = true;
                        // $(_pageId + `#checkbox_${its.fund_code}`).attr("checked", "checked");
                        if (its.checked) {
                            $(_pageId + `#checkbox_${its.fund_code}`).attr("checked", "checked");
                        } else {
                            $(_pageId + `#checkbox_${its.fund_code}`).removeAttr("checked");
                        }
                    })
                }
                $(_pageId + " #inputspanid span").text(str).attr("text", str);
            }
        } else {
            if (str) {
                $(_pageId + " #inputspanid span").text(str);
                $(_pageId + " #inputspanid span").attr("text", str);
            } else {
                $(_pageId + " #inputspanid span").text("请输入购买金额");
                $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
            }
        }
        isAllChecked();

    }

    // 处理数据格式
    function getList() {
        return new Promise(async (resolve, reject) => {
            service.reqFun102180({series_id:localStorage.series_id}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results;
                    $(_pageId + " .funds_Buy_name").text(res[0].series_name);
                    let list;
                    if (res[0]) {
                        list = [
                            {
                                zone_prod_name: "指数基金",
                                zone_prod_type: "01",
                                zone_prod_list: res[0].list
                            },
                        ];
                        $(_pageId + " .qrDate").html(tools.FormatDateText(res[0].qrDate.substring(4, 8)));
                        purchaseSingle = parseFloat(res[0].purchaseAmount);
                    }
                    resolve(list)
                } else {
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }


    function dealDataFormat() {
        prodList.forEach(its => {
            var money = $(_pageId + ` #czje_${its.fund_code}`).val().replace(/,/g, "");
            its.buy_money = money;
        })
        var totalAmount = $(_pageId + " #czje").val().replace(/,/g, "");
        appUtils.setSStorageInfo("prodList", prodList);
        appUtils.setSStorageInfo("buy_moneys", totalAmount);
    }


    //点击事件
    function bindPageEvent() {
        //输入买入总额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fundSupermarket_fundsBuy";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });

        // 输入单个产品金额弹出键盘
        appUtils.preBindEvent($(_pageId + " #prod_list"), ".item .prod_buy_money", function (event) {
            $(_pageId + " #empty").show();
            // $(_pageId + " .funds_Buy").animate({ scrollTop: 280 }, 100);
            // var b_top = $(this).offset().top;
            // var window_height = $(window).height()
            // var b_bottom = window_height - b_top;
            // console.log(b_bottom, b_top);
            // console.log($(this).height());
            // if (b_bottom < 260) {
            //     $(_pageId + " .funds_Buy").animate({ scrollTop: b_top - 260 }, 100);
            // }
            // console.log($(window).height(), b_top);
            // console.log(window_height - b_top);

            isFirstDistribution = false;
            $(_pageId + " .prod_buy_money span").addClass("unable").removeAttr("data-content-before"); // 光标消失
            let id = $(this).attr("data-id");
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + ` #czje_${id}`).val('');
            tools.recordEventData('1','prod_buy_money_' + id,'单个产品金额弹出键盘');
            //键盘事件
            singleMoneyboardEvent(id);
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "fundSupermarket_fundsBuy";
            param["eleId"] = `czje_${id}`;
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        })

        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " #empty").hide();
            var moneys = $(_pageId + " #czje").val().replace(/,/g, "");
            if (parseFloat(moneys) > 0) {
                $(_pageId + " #inputspanid span").addClass("active").addClass("unable").css('color', '#000000');
            } else {
                $(_pageId + " #inputspanid span").text(str).attr("text", str).removeClass("active").css({ color: "rgb(153, 153, 153)" });
            }
            monkeywords.close();
        });

        //返回页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        // 跳转产品详情
        appUtils.preBindEvent($(_pageId + " #prod_list"), ".item .to_detail", function (e) {
            e.stopPropagation();
            var productInfo = JSON.parse($(this).parent().parent().parent().find(".productInfo").text()); //存储数据格式
            appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
            dealDataFormat(); // 存储金额
            tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
        });

        //全选
        appUtils.bindEvent($(_pageId + " #all_checkbox"), function () {
            var checked = $(_pageId + " #all_checkbox input").attr("checked");
            if (checked == "checked") {
                tools.recordEventData('1','close_all_checkbox','取消全选');
                $(_pageId + " #all_checkbox input").removeAttr("checked", "checked");
                $(_pageId + " #prod_list .checkbox input").removeAttr("checked", "checked");
                str = `${tools.changeTwoDecimal_f(purchaseSingle)}元起购，${tools.changeTwoDecimal_f(1)}元递增`;
                prodList.forEach(item => {
                    item.checked = false;
                    $(_pageId + ` #czje_${item.fund_code}`).val("");
                    $(_pageId + ` #inputspanid${item.fund_code} span`).html("");
                    item.buy_money = $(_pageId + ` #czje_${item.fund_code}`).val();
                    var spanElement = $(_pageId + ' #inputspanid' + item.fund_code + ' span');
                    spanElement.text("").removeClass("active").css({ color: "rgb(153, 153, 153)" });
                })
                getTotalAmount();
            } else {
                tools.recordEventData('1','all_checkbox','全选');
                $(_pageId + " #all_checkbox input").attr("checked", "checked");
                $(_pageId + " #prod_list .checkbox input").attr("checked", "checked");

                prodList.forEach(item => {
                    item.checked = true;
                })
                getTotalAmount();
                recalculateAmount()
            }
        });

        // 单个选中
        appUtils.preBindEvent($(_pageId + " #prod_list"), ".item .prod_title", function () {
            // console.log($(this));
            var checked = $(this).find(".checkbox").children('[type="checkbox"]').attr("checked");
            var fund_code = $(this).find(".checkbox").attr("data-code");
            if (checked == "checked") {
                tools.recordEventData('1','close_item_' + fund_code,'取消选中');
                prodList.forEach(its => {
                    if (its.fund_code == fund_code) {
                        its.checked = false;
                        $(_pageId + ` #czje_${its.fund_code}`).val("");
                        $(_pageId + ` #inputspanid${its.fund_code} span`).text("").removeClass("active").css({ color: "rgb(153, 153, 153)" });
                        its.buy_money = "";
                    }
                })
                getTotalAmount();
                $(this).find(".checkbox").children('[type="checkbox"]').removeAttr("checked", "checked");
                $(_pageId + " #all_checkbox input").removeAttr("checked", "checked");
            } else {
                tools.recordEventData('1','item_'+ fund_code,'选中');
                prodList.forEach(its => {
                    if (its.fund_code == fund_code) its.checked = true;
                })
                $(this).find(".checkbox").children('[type="checkbox"]').attr("checked", "checked");
                isAllChecked();
            }
            recalculateAmount();
            var buy_moneys = $(_pageId + " #czje").val().replace(/,/g, "");
            if (parseFloat(buy_moneys) > 0) isFirstDistribution = false;
        });

        // 下一步
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            var curVal = $(_pageId + " #czje").val();
            var moneys = curVal.replace(/,/g, "");
            if (!moneys) {
                layerUtils.iAlert("请输入买入总额");
                return;
            }
            recalculateAmount();
            // 计算分配金额和总额是否一致
            var buy_moneys = 0;
            var isPurchaseAmountOk = true;
            var _isMatchAddAmt = true;
            prodList.forEach(its => {
                var money = $(_pageId + ` #czje_${its.fund_code}`).val().replace(/,/g, "");
                buy_moneys = common.floatAdd(buy_moneys, money);
                its.buy_money = money;
                if (its.checked) {
                    if (parseFloat(money) < purchaseSingle) {
                        isPurchaseAmountOk = false;
                        return;
                    }
                    if (isMatchAddAmt(money, purchaseSingle, 1)) {
                        _isMatchAddAmt = false;
                        return
                    }
                }
            })
            if (!isPurchaseAmountOk) {
                layerUtils.iAlert(`单个产品购买金额不能低于起购金${tools.changeTwoDecimal_f(purchaseSingle)}元`);
                return;
            }
            if (!_isMatchAddAmt) {
                layerUtils.iAlert("单个产品递增金额为1.00元");
                return;
            }
            if (purchaseAmount && parseFloat(moneys) < parseFloat(purchaseAmount)) {
                layerUtils.iAlert(`买入总额不能低于起购金额${tools.changeTwoDecimal_f(purchaseAmount)}元`);
                return;
            }
            if (tools.isMatchAddAmt(moneys, purchaseAmount, increaseAmount)) {
                return
            }
            var totalAmount = $(_pageId + " #czje").val().replace(/,/g, "");
            let diff = common.floatSub(totalAmount, buy_moneys);
            if (diff > 0) {
                layerUtils.iAlert(`买入总额中还有${diff}元未分配`);
                return;
            } else if (diff < 0) {
                let _diff = common.floatSub(buy_moneys, totalAmount);
                layerUtils.iAlert(`分配金额超出买入总额${_diff}元`);
                return;
            }
            appUtils.setSStorageInfo("prodList", prodList);
            appUtils.setSStorageInfo("buy_moneys", totalAmount);
            // appUtils.setSStorageInfo("financial_prod_type", financial_prod_type);
            // appUtils.pageInit(_page_code, "fundSupermarket/fixedInvestmentDetail");
            var tranType = $(this).attr("type")
            appUtils.setSStorageInfo("tranType", tranType);
            appUtils.clearSStorage("fixedDate");
            appUtils.pageInit(_page_code, "fundSupermarket/purchaseDetail");

        });
    }

    // 判断全选按钮是否需要选中
    function isAllChecked() {
        let isAllChecked = true;
        prodList.forEach(its => {
            !its.checked && (isAllChecked = false);
        })
        if (isAllChecked) {
            $(_pageId + " #all_checkbox input").attr("checked", "checked")
        } else {
            $(_pageId + " #all_checkbox input").removeAttr("checked", "checked");
        }
    }

    // 金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () { // 键盘完成
                $(_pageId + " #empty").hide();
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                recalculateAmount();
                if (moneys && isFirstDistribution) {
                    dealEqualAmount(moneys);
                }
                if (purchaseAmount && parseFloat(moneys) < parseFloat(purchaseAmount)) {
                    layerUtils.iAlert(`买入总额不能低于起购金额${tools.changeTwoDecimal_f(purchaseAmount)}元`);
                    return;
                }
                if (tools.isMatchAddAmt(moneys, purchaseAmount, increaseAmount)) {
                    return
                }
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #czje").val(moneys);
            },
            inputcallback: function () {// 键盘输入
                // 处理单个选择产品金额变化
                var money = $(_pageId + " #czje").val().replace(/,/g, "");
                recalculateAmount();
                if (money && isFirstDistribution) {
                    dealEqualAmount(money);
                }
            }, // 键盘隐藏
            keyBoardHide: function () {
                $(_pageId + " #empty").hide();
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            }
        })
    }

    // 重新计算起购金额，追加金额
    function recalculateAmount() {
        checkedProdNum = 0;
        purchaseAmount = 0;
        var moneys = $(_pageId + " #czje").val().replace(/,/g, "");
        let singleMoney = '';
        prodList.forEach(its => {
            its.checked && (
                checkedProdNum += 1,
                purchaseAmount += purchaseSingle);
            if(!moneys || moneys == '0' || moneys == 0){
                $(_pageId + ` #czje_${its.fund_code}`).val('');
                $(_pageId + ` #inputspanid${its.fund_code} span`).html('');
                its.buy_money = $(_pageId + ` #czje_${its.fund_code}`).val();
                var spanElement = $(_pageId + ' #inputspanid' + its.fund_code + ' span');
                spanElement.addClass("active").addClass("unable").css('color', '#000000');
            }
        })
        if (purchaseAmount == 0) { purchaseAmount = purchaseSingle }
        str = `${tools.changeTwoDecimal_f(purchaseAmount)}元起购，${tools.changeTwoDecimal_f(increaseAmount)}元递增`;
        if (parseFloat(moneys) > 0) {
            $(_pageId + " #inputspanid span").addClass("active").addClass("unable").css('color', '#000000');
        } else {
            $(_pageId + " #inputspanid span").text(str).attr("text", str).removeClass("active").css({ color: "rgb(153, 153, 153)" });
        }
        
        return checkedProdNum;
    }

    /**
        * 是否满足递增金额
        * @param curAmt 当前金额
        * @param thresholdAmount 起投金额 || 追加
        * @param additionAmt 递增金额
        **/
    function isMatchAddAmt(curAmt, thresholdAmount, additionAmt) {
        if (curAmt && thresholdAmount && additionAmt && additionAmt > 0
            && new BigNumber(curAmt).gte(thresholdAmount)
            && !(new BigNumber(curAmt).modulo(additionAmt).isZero())) { // 当前金额 > 起投金额 && 当前金额 % 递增金额 == 0
            return true;
        }
        return false
    }

    // 平均分配单个产品金额
    function dealEqualAmount(money) {
        !money && (money = 0)
        let singleMoney = ''; let remainder = 0;
        let prodNum = checkedProdNum;
        prodNum && (singleMoney = parseInt(common.floatDivide(money, prodNum)));
        prodNum && (remainder = common.floatMod(money, prodNum));
        // 获取选中的产品
        let allCheckedFundCodeArr = prodList.filter((item => {
            return item.checked;
        }))
        if (allCheckedFundCodeArr.length) {
            allCheckedFundCodeArr.forEach(its => {
                if (remainder) {
                    let money = common.floatAdd(singleMoney, remainder);
                    $(_pageId + ` #czje_${allCheckedFundCodeArr[0].fund_code}`).val(tools.fmoney(money).replace(/,/g, ""));
                    $(_pageId + ` #inputspanid${allCheckedFundCodeArr[0].fund_code} span`).html(tools.fmoney(money));
                    allCheckedFundCodeArr[0].buy_money = $(_pageId + ` #czje_${allCheckedFundCodeArr[0].fund_code}`).val();
                    var spanElement = $(_pageId + ' #inputspanid' + allCheckedFundCodeArr[0].fund_code + ' span');
                    spanElement.addClass("active").addClass("unable").css('color', '#000000');
                }
                $(_pageId + ` #czje_${its.fund_code}`).val(tools.fmoney(singleMoney).replace(/,/g, ""));
                $(_pageId + ` #inputspanid${its.fund_code} span`).html(tools.fmoney(singleMoney));
                its.buy_money = $(_pageId + ` #czje_${its.fund_code}`).val();
                var spanElement = $(_pageId + ' #inputspanid' + its.fund_code + ' span');
                spanElement.addClass("active").addClass("unable").css('color', '#000000');

            })
        }

    }


    function singleMoneyboardEvent(id) {
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () {
                $(_pageId + " #empty").hide();
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var money = curVal;
                getTotalAmount();
                prodList.filter(its => {
                    if (its.fund_code == id) {
                        // 金额大于0，选中
                        if (parseFloat(money) > 0) {
                            its.checked = true;
                            $(`#checkbox_${id}`).attr("checked", "checked");
                        } else {
                            its.checked = false;
                            $(`#checkbox_${id}`).removeAttr("checked", "checked");
                        }
                        its.buy_money = $(_pageId + ` #czje_${id}`).val()
                    }
                })
                isAllChecked();
                if (100 && parseFloat(money) < purchaseSingle) {
                    layerUtils.iAlert(`单个产品购买金额不能低于起购金额${tools.changeTwoDecimal_f(purchaseSingle)}元`);
                    return;
                }
                if (isMatchAddAmt(money, purchaseSingle, 1)) {
                    layerUtils.iAlert("单个产品递增金额为1.00元");
                    return
                }

                if (money) {
                    money = tools.fmoney(money);
                }
                $(_pageId + ` #czje_${id}`).val(money);

            },
            inputcallback: function () {
                var money = $(_pageId + ` #czje_${id}`).val().replace(/,/g, "");
                getTotalAmount();
                prodList.filter(its => {
                    if (its.fund_code == id) {
                        // 金额大于0，选中
                        if (parseFloat(money) > 0) {
                            its.checked = true;
                            $(`#checkbox_${id}`).attr("checked", "checked");
                        } else {
                            its.checked = false;
                            $(`#checkbox_${id}`).removeAttr("checked", "checked");
                        }
                        its.buy_money = $(_pageId + ` #czje_${id}`).val()
                    }
                })
                isAllChecked();

            },
            keyBoardHide: function () {
                $(_pageId + " #empty").hide();
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                var money = curVal.replace(/,/g, "");
                if (money) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(money).replace(/,/g, ""));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(money));
                }
                if (100 && parseFloat(money) < purchaseSingle) {
                    layerUtils.iAlert(`单个产品购买金额不能低于起购金额${tools.changeTwoDecimal_f(purchaseSingle)}元`);
                    return;
                }
                if (isMatchAddAmt(money, purchaseSingle, 1)) {
                    layerUtils.iAlert("单个产品递增金额为1.00元");
                    return
                }
            }
        })
    }


    // 买入总额累加
    function getTotalAmount() {
        var buy_moneys = 0;
        prodList.forEach(its => {
            var money = $(_pageId + ` #czje_${its.fund_code}`).val().replace(/,/g, "");
            buy_moneys = common.floatAdd(buy_moneys, money);
        })
        if (buy_moneys > 0) {
            $(_pageId + " #czje").val(tools.fmoney(buy_moneys).replace(/,/g, ""));
            $(_pageId + " #inputspanid span").html(tools.fmoney(buy_moneys));
            var spanElement = $(_pageId + ' #inputspanid' + ' span');
            spanElement.addClass("active").addClass("unable").css('color', '#000000');
        } else {
            $(_pageId + " #czje").val("");
            $(_pageId + " #inputspanid span").html("");
            $(_pageId + " #inputspanid span").text(str).attr("text", str).removeClass("active").css({ color: "rgb(153, 153, 153)" });
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        monkeywords.close();
        $(_pageId + " #jymm").val("");
        $(_pageId + " #czje").val("");
        $(_pageId + " #inputspanid span").text("").removeClass("active").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " #empty").hide();
        purchaseAmount = 0,
            increaseAmount = 0;
        isFirstDistribution = false
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thfundList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thfundList;
});
