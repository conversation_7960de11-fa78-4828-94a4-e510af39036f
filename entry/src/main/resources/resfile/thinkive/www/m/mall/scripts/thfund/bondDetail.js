// 晋金定制 产品详情页
define(function (require, exports, module) {
	var appUtils = require("appUtils"),
		dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		cfdUtils = require("cfdUtils"),
		service = require("mobileService"),
		VIscroll = require("vIscroll"),
		tools = require("../common/tools"),
		common = require("common"),
		vIscroll = {
			"scroll": null,
			"_init": false
		},
		_pageId = "#thfund_bondDetail ";
	var _pageCode = "thfund/bondDetail";
	var ut = require("../common/userUtil");
	var backPage = "";
	var _fund_code = "";
	//收益走势数据
	var _series = "";
	
    var chartsUtils=require("chartsUtils");
    var data;
    var spliceDate;//默认展示7天数据
    var tips; //默认提示为累计收益走势
    var timeOptions;//绘制折线图配置
	
	function init() {
		spliceDate = -1100;
        
//		backPage = appUtils.getSStorageInfo("_prePageCode");
 		//var param = appUtils.getPageParam();
        
		_fund_code = appUtils.getSStorageInfo("fund_code");

		//获取产品详情
		getProductDetails();
		//获取业绩表现
		getPerformance();
		//获取历史净值
		getHistory();
		//默认展示累计收益走势
        $(_pageId + " #chartTitle").find("span").removeClass("active").eq(0).addClass("active");
        $(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
		//累计收益走势 折线图
		yieldChart();
		//获取交易时间
		reqFun102008();
	}

	function bindPageEvent() {
		appUtils.bindEvent($(_pageId + " #comeback"), function () {
			pageBack();
		});
		appUtils.bindEvent($(_pageId + " #getBack"), function () {
			pageBack();
		});
		// 问号弹框
		appUtils.bindEvent($(_pageId + " .moveData .performance .questionIcon"), function () {
			//显示弹框
			showQuestionAlert();
		});
		// 问号弹框
		appUtils.bindEvent($(_pageId + " .question .know"), function () {
			//隐藏弹框
			hideQuestionAlert()
		});
		
		// 基金档案
		appUtils.bindEvent($(_pageId + " #jjThirtyDetailRecord"), function () {
			appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailRecord")
		});
		// 基金经理
		appUtils.bindEvent($(_pageId + " #jjThirtyDetailManager"), function () {
			appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailManager")
		});
		// 基金公司
		appUtils.bindEvent($(_pageId + " #jjThirtyDetailCompany"), function () {
			appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailCompany")
		});
		// 资产配置
		appUtils.bindEvent($(_pageId + " #jjThirtyDetailConfiguration"), function () {
			appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailConfiguration")
		});
		// 基金公告
		appUtils.bindEvent($(_pageId + " #jjThirtyDetailNotice"), function () {
			appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailNotice")
		});
		// 基金文件
		appUtils.bindEvent($(_pageId + " #jjThirtyDetailFile"), function () {
			appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailFile")
		});
		// 交易规则
		appUtils.bindEvent($(_pageId + " .trade_rules"), function () {
			appUtils.pageInit(_pageCode, "inclusive/BuyingSellingRule")
		});
		// 点击产品档案
		appUtils.bindEvent($(_pageId + " .product_archives"), function () {
//			var arrow = $(_pageId + " #product #arrow");
//			if (arrow.hasClass("arrow_up")) {
//				$(_pageId + " #product #arrow").addClass("arrow_down");
//				$(_pageId + " #product #arrow").removeClass("arrow_up");
//
//				$(_pageId + " #item_box").hide();
//			}else{
//				$(_pageId + " #product #arrow").addClass("arrow_up");
//				$(_pageId + " #product #arrow").removeClass("arrow_down");
//
//				$(_pageId + " #item_box").show();
//			}
		});
		// 点击风险提示，我知道了
		appUtils.bindEvent($(_pageId + " .risk_alert .know"), function () {
			hideRiskAlert()
		});
		// 点击业绩表现
		appUtils.bindEvent($(_pageId + " #performance"), function () {
			$(_pageId + " #performanceContent").show();
			$(_pageId + " #historyContent").hide();
			
			$(_pageId + " #performance").addClass("active")
			$(_pageId + " #history").removeClass("active")
		});
		// 点击历史净值
		appUtils.bindEvent($(_pageId + " #history"), function () {
			$(_pageId + " #performanceContent").hide();
			$(_pageId + " #historyContent").show();
			
			$(_pageId + " #performance").removeClass("active")
			$(_pageId + " #history").addClass("active")
		});
		// 查看更多 业绩表现
		appUtils.bindEvent($(_pageId + " #PerformanceList"), function () {
			appUtils.pageInit(_pageCode, "thfund/HAPerformanceList")
		});
		// 查看更多 历史净值
		appUtils.bindEvent($(_pageId + " #HistoryList"), function () {
			appUtils.pageInit(_pageCode, "thfund/HAHistoryValueList")
		});
		// 点击累计收益走势
		appUtils.bindEvent($(_pageId + " #yield"), function () {
//			$(this).addClass("active");
//			$(_pageId + " #earnings").removeClass("active");
			$(_pageId + " #chartTitle").find("span").removeClass("active").filter(this).addClass("active");
			yieldChart();
			$(_pageId + " .moveData").show();
			$(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
		});
		// 点击单位净值走势
		appUtils.bindEvent($(_pageId + " #earnings"), function () {
//			$(this).addClass("active");
//			$(_pageId + " #yield").removeClass("active");
			$(_pageId + " #chartTitle").find("span").removeClass("active").filter(this).addClass("active");
			earningsChart();
			$(_pageId + " .moveData").hide();
			$(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
		});
		// 点击累计净值走势
		appUtils.bindEvent($(_pageId + " #accunav"), function () {
			$(_pageId + " #chartTitle").find("span").removeClass("active").filter(this).addClass("active");
//			$(this).addClass("active");
//			$(_pageId + " #yield").removeClass("active");
			//累计净值走势 折线图
			totalChart();
			$(_pageId + " .moveData").hide();
			$(_pageId + " .spliceDate li").removeClass("active").eq(0).addClass("active");
		});
		//展示限定时间的收益
        appUtils.bindEvent(_pageId + " .spliceDate li",function(){
            $(_pageId + " .spliceDate").find("li").removeClass("active").filter(this).addClass("active");
            var index = $(this).index(),
                selected = chat.rangeSelector.selected;
            if(index === selected) {
                return false;
            }
            chat.rangeSelector.clickButton(index);
        })
		
	}
	//隐藏风险提示弹框
	function hideRiskAlert(){
		$(_pageId + " .risk_alert").hide();
	}
	//显示风险提示弹框
	function showRiskAlert(){
		$(_pageId + " .risk_alert").show();
	}

	
	//获取业绩表现
	function getPerformance(){
        service.reqFun102007({fund_code: _fund_code}, function (data) {
            if (data.error_no == 0) {
            	var results = data.results[0];
            	if(!results || results.length == 0){
            		return;
            	}
                var dataArr = ["近一周","近一月","近三月","近半年","近一年"];
                var numData = [];
                //近一周
                numData.push(results.week);
                //近一月
                numData.push(results.month);
                //近三月
                numData.push(results.season);
                //近半年
                numData.push(results.six_month);
                //近一年
                numData.push(results.year); 
                //近两年
                // numData.push(results.two_year);
                //近三年
                // numData.push(results.three_year);
                var html = "";
                
                for(var i=0;i<numData.length;i++){
                	//空数据处理
                	numData[i] = tools.FormatNull(numData[i]);
                	//本基金收益率
                	var rateClass = "add";
                	var rate = numData[i].rate;
                	if(rate != "--"){
                		rateClass = addMinusClass(numData[i].rate);
                		rate = (+rate).toFixed(2);
                		rate = rate+"%";
                	}
                	
                	//沪深300指数
                	var hs300Class = "add";
                	var hs300_rate = numData[i].hs300_rate;
                	if(hs300_rate !="--"){
                		hs300Class = addMinusClass(numData[i].hs300_rate);
                		hs300_rate = (+hs300_rate).toFixed(2);
                		hs300_rate = hs300_rate+"%";
                	}
                	
                	html+='<div class="item">'+
		            	'<span>'+dataArr[i]+'</span>'+
		            	'<span class=value '+rateClass+'>'+rate+'</span>'+
		            	'<span class=value '+hs300Class+'>'+hs300_rate+'</span>'+
		    		 '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
	}
	
	//获取历史净值
	function getHistory(){
		var params = {
			fund_code: _fund_code
		}
        service.reqFun102006(params, function (data) {
            if (data.error_no == 0) {
            	var results = data.results;
            	if(!results || results.length == 0){
            		return;
            	}
                var html = "";
                for(var i=0;i<5;i++){
                	//空数据处理
                    results[i] = tools.FormatNull(results[i]);

                    var end_date = results[i].end_date;
                	if(end_date != "--"){
                		end_date = tools.ftime(end_date.substring(0, 8));
                	}
                    
                    //单位净值 
                	var nav = results[i].nav;
                	if(nav != "--"){
                		nav = (+nav).toFixed(4);
                	}
                	
                	//累计净值
                	var accunav = results[i].accunav;
                	if(accunav != "--"){
                		accunav = (+accunav).toFixed(4);
                	}
                    
                	//日涨跌幅
                	var rateClass="add";
                	var daily_return = results[i].daily_return;
                	if(daily_return != "--"){
                		rateClass = addMinusClass(daily_return);
                		daily_return = (+daily_return).toFixed(2)+"%";
                	}

                	html+='<div class="item">'+
	                	'<span>'+end_date+'</span>'+
	                	'<span class="">'+nav+'</span>'+
	                	'<span class="">'+accunav+'</span>'+
	                	'<span class='+rateClass+'>'+daily_return+'</span>'+
	        		 '</div>';
                }
                $(_pageId + " #historyContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
	}
	
	//获取产品详情
	function getProductDetails(){
        service.reqFun102026({fund_code: _fund_code}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if(!results || results.length == 0){
            		return;
            	}
                appUtils.setSStorageInfo("productInfo", results);
                //数据处理 空 和 --
                results = tools.FormatNull(results);
                
                //日涨跌幅
                var day_rate_class = "";
                var day_rate = results.day_rate;
                if(day_rate < 0){
                	day_rate_class = "green";
                	day_rate = (+day_rate).toFixed(3)+'%';
                }else if(day_rate > 0){
                	day_rate_class = "red";
                	day_rate = (+day_rate).toFixed(3)+'%';
                }
                
                //基金规模
                var scale_fe = results.scale_fe;
                if(scale_fe != "--"){
                	scale_fe = (scale_fe/100000000).toFixed(2)+'亿元';
                }
                
                //单位净值日期
                var nav_date = results.nav_date;
                if(!nav_date){
                	nav_date = "--";
                }
                if(nav_date != "--"){
                	nav_date = tools.ftime(nav_date).substring(5);
                } 
                
                //单位净值
                var nav = results.nav;
                if(nav != "--"){
                	nav = (+nav).toFixed(4)+"元";
                }

                var product_desc = '<div class="fund_name" id="prod_name">'+results.prod_sname+'</div>'+
                '<span class="num" id="fund_code">('+results.fund_code+')</span>'+
                '<div class="product_top">'+
                    '<div>'+
                        '<p>日涨跌幅</p>'+
                        '<p class="compare_benchmark '+day_rate_class+'" id="day_rate">'+day_rate+'</p>'+
                    '</div>'+
                    '<div>'+
                        '<p>单位净值(<span id="nav_date">'+nav_date+'</span>)</p>'+
                        '<p class="black fund_lncome" id="nav">'+nav+'</p>'+
                    '</div>'+
                '</div>';
                
                var product_tip = '<div class="risk_level_name" id="risk_level_name">'+results.risk_level_name+'</div>'+
                '<div class="fund_type_name" id="fund_type_name">'+results.fund_type_name+'</div>'+
                '<div class="money">'+
                    '<span>基金规模：</span>'+
                    '<span id="scale_fe">'+scale_fe+'</span>'+
					'<span id="scale_fe_date">(' + tools.ftime(results.scale_fe_date.substr(4,4)) + ')</span>' +
					'</div>';
                
                appUtils.setSStorageInfo("mgrcomp_sname",results.mgrcomp_sname);
				tools.initFundBtn(results, _pageId);
                $(_pageId + " .product_desc").html(product_desc);
                $(_pageId + " .product_tip").html(product_tip);
                //基金经理
                $(_pageId + " #fund_managers").html(results.fund_managers);
                //基金公司
                $(_pageId + " #mgrcomp_name").html(results.mgrcomp_name);
                //起投金额
                // $(_pageId + " #threshold_amount").html(threshold_amount);

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
	}
	
	//获取交易时间
	function reqFun102008(){
		var param = {
			type:"6"
		}
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if(!results || results.length == 0){
            		return;
            	}
                //空数据处理
                results = tools.FormatNull(results);
                var qrDate = tools.FormatDateText(results.qrDate.substr(4));
                var syDate = tools.FormatDateText(results.syDate.substr(4));
                
                $(_pageId + " #qrDate").html(qrDate);
                $(_pageId + " #syDate").html(syDate);
                $(_pageId + " #ksqrDate").html(qrDate);
                $(_pageId + " #ptqrDate").html(syDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
	}
	

	function addMinusClass(str){
        var numClass = "add";
        
        if(str<0){
        	numClass = "minus";
        }else if(str>0){
        	numClass = "add";
        }
        return numClass;
	}
	
	//累计收益走势  折线图
	function yieldChart(){
		tips = "累计收益走势";
        service.reqFun102040({fund_code: _fund_code},function(result){
            if(result.error_no == 0){
            	var results = result.results;
            	
            	//本金收益率数组
            	var earningsArr = [];
            	//沪深300收益率数组
            	var hs300Arr = [];
            	//业绩比较基准数组
            	var compareArr = [];
            	
            	for(var i=0;i<results.length;i++){
            		var earningsObj = {
            			x:results[i].day.substring(0,8),
            			y:Number(results[i].fund)
            		}
            		earningsArr.push(earningsObj);
            		
            		var hs300Obj = {
            			x:results[i].day.substring(0,8),
            			y:Number(results[i].hs300)
                	}
            		hs300Arr.push(hs300Obj);
            		
            		var compareObj = {
            			x:results[i].day.substring(0,8),
            			y:Number(results[i].compare_benchmark)
                	}
//            		var compareObj = {
//                			x:results[i].day.substring(0,8),
//                			y:Number(results[i].hs300)+1
//                    }
            		compareArr.push(compareObj);
            	}
            	earningsArr=earningsArr.reverse();
            	earningsArr=earningsArr.slice(spliceDate);
            	earningsArr=datearr(earningsArr);
                
            	hs300Arr=hs300Arr.reverse();
            	hs300Arr=hs300Arr.slice(spliceDate);
            	hs300Arr=datearr(hs300Arr);
                
//            	compareArr=compareArr.slice(spliceDate);
//            	compareArr=datearr(compareArr);
            	
            	var data = [
            	    {
//	                  name: "<span style='color:#ffffff'>" + tips + "</span>",
            	      name:"本基金收益",
	                  color:"rgba(632,64,63,1)",
	                  borderWidth: 2,
	                  data: earningsArr
            	    },{
//	                  name: "<span style='color:#ffffff'>" + tips + "</span>",
            	      name:"沪深300",
	                  color:"rgba(153,153,153,1)",
	                  borderWidth: 2,
	                  data: hs300Arr
            	    },
//            	    {
//	                  name: "<span style='color:#ffffff'>" + tips+3 + "</span>",
//	                  color:"rgba(80,140,238,1)",
//	                  borderWidth: 2,
//	                  data: compareArr
//            	    }
            	];
            	
                
                showChart(data,tips,function(c){
                    chat = c;
                    chat.rangeSelector.clickButton(0);
                })
            }else{
                layerUtils.iAlert(result.error_info);
            }
        });
	}
	//单位净值走势 折线图
	function earningsChart(){
		tips = "单位净值走势";
        service.reqFun102006({fund_code: _fund_code},function(result){
            if(result.error_no == 0){
            	var results = result.results;
            	var dataArr = [];
            	for(var i=0;i<results.length;i++){
            		var obj = {
            			x:results[i].end_date.substring(0,8),
            			y:Number(results[i].nav)
            		}
            		dataArr.push(obj);
            	}
            	dataArr=dataArr.reverse();
            	dataArr=dataArr.slice(spliceDate);
            	dataArr=datearr(dataArr);
                
                var data = [
            	    {
            	      name:tips,
	                  color:"rgba(632,64,63,1)",
	                  borderWidth: 2,
	                  data: dataArr
            	    },
                ];
                
                showChart(data,tips,function(c){
                    chat = c;
                    chat.rangeSelector.clickButton(0);
                })
            }else{
                layerUtils.iAlert(result.error_info);
            }
        });
	}
	
	//累计净值走势 折线图
	function totalChart(){
		tips = "累计净值走势";
        service.reqFun102006({fund_code: _fund_code},function(result){
            if(result.error_no == 0){
            	var results = result.results;
            	var dataArr = [];
            	for(var i=0;i<results.length;i++){
            		var obj = {
            			x:results[i].end_date.substring(0,8),
            			y:Number(results[i].accunav)
            		}
            		dataArr.push(obj);
            	}
            	dataArr=dataArr.reverse();
            	dataArr=dataArr.slice(spliceDate);
            	dataArr=datearr(dataArr);
                
                var data = [
            	    {
            	      name:tips,
	                  color:"rgba(632,64,63,1)",
	                  borderWidth: 2,
	                  data: dataArr
            	    },
                ];
                
                showChart(data,tips,function(c){
                    chat = c;
                    chat.rangeSelector.clickButton(0);
                })
            }else{
                layerUtils.iAlert(result.error_info);
            }
        });
	}
	
	
	//设置时间为highChart所需时间格式
    function datearr(data){
        for(var i=0;i<data.length;i++){
            var x=data[i].x.toString();
            Date.UTC()
            data[i].x=Date.UTC(x.substring(0,4), x.substring(4,6)-1, x.substring(6,8));
        }
        return data;
    }
	//显示晋金宝折线图
    function showChart(data,tips,callback){
        tips = tips || "累计收益走势";
        timeOptions = {
            lang: {
                rangeSelectorZoom: null // 不显示 'zoom' 文字
            },
            rangeSelector: {
                inputEnabled: false,
                buttonPosition: '11',
                buttons: [{
                    type: 'month',
                    count: 1,
                    text: '1月'
                }, {
                    type: 'month',
                    count: 3,
                    text: '3月'
                }, {
                    type: 'month',
                    count: 6,
                    text: '6月'
                },{
                    type: 'year',
                    count: 1,
                    text: '近1年'
                },{
                    type: 'all',
                    text: '近3年'
                }],
                buttonTheme:{
                    display:"none"
                }
            },
            scrollbar: {
                enabled: false
            },
            navigator: {
                enabled: false
            },
            chart: {
                type: 'line',
                panning: false, //禁用放大
                pinchType: ''//禁用手势操作
            },
            title: {
                text: null
            },
            xAxis: {
                title: {
                    text: null
                },
                tickPixelInterval:60,
                gridLineWidth:1,
                // type: 'datetime',
                // labels: {
                //     format: '{value:%y/%m/%d}'
                // },
                dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%H:%M',
                    day: '%m/%d',
                    week: '%m/%d',
                    month: '%y/%m',
                    year: '%Y'
                }
            },
            yAxis: {
                title: {
                    text: ''
                },
                gridLineWidth:1,

                options :{
                    startOnTick : false,
                    endOnTick : false,
                },
                labels: {
                    format: '{value}%'
                },
                tickPixelInterval:20,
            },
            legend: {
                enabled: false
            },
            plotOptions: {
            	series:{
                    turboThreshold:0
                },
                areaspline: {
                    fillColor: {
                        linearGradient: {
                            x1: 0,
                            y1: 0,
                            x2: 0,
                            y2: 0,
//                            y2: 1
                        },
                        stops: [
                            [0, "rgba(632,64,63,0.5)"],
                            [1, "rgba(255,255,255,0)"]
//                            [1, "rgba(255,255,255,0.5)"]
                        ]
                    },
                    marker: {
                        radius: 2
                    },
                    lineWidth: 2,
                    states: {
                        hover: {
                            lineWidth: 2
                        }
                    },

                    threshold: null
                }
            },
            tooltip: {
                backgroundColor: '#1E4DA9',   // 背景颜色
                borderColor: '',         // 边框颜色
                borderRadius: 0,             // 边框圆角
                borderWidth: 0,               // 边框宽度
                shadow: false,                 // 是否显示阴影
                animation: true,               // 是否启用动画效果
                crosshairs: "Mixed",
                followTouchMove: true,
                style: {                      // 文字内容相关样式
                    color: "#ffffff",
                    fontSize: "12px",
                    fontWeight: "normal",
                    fontFamily: "Courir new"
                },
                type:"datetime",
                formatter:function(item){
                	var points = this.points;
                	if($(_pageId+' #yield').hasClass('active')){
                		return yieldTextStyle(points);
                	}else{
                    	var time = new Date(this.x)
                    	return "<div>" + time.getFullYear() +'/' + (time.getMonth()+1)+'/' +time.getDate() +"</div>" +
                    			"<div>" + tips + ":" + this.y +"</div>"
                	}
                },
                useHTML: true,
                headerFormat: '<table><small>{point.key}</small><table>',
                pointFormat: '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}%</td></tr>',
                footerFormat: '</table>',

            },
            series: [
//               {
//                name: "<span style='color:#ffffff'>" + tips + "</span>",
//                color:"rgba(632,64,63,1)",
//                borderWidth: 2,
//                data: data
//               }
            ]
        };
        timeOptions.series = data;
        if(tips == "七日年化"){
            timeOptions.yAxis["labels"]["format"] = '{value}%';
            timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}%</td></tr>'
        }else{
            timeOptions.yAxis["labels"]["format"] = '{value}';
            timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                '<td style="text-align: right">{point.y}</td></tr>'
        }
        $(_pageId+' #chartContainer').highcharts('StockChart',timeOptions,callback);
        Highcharts.setOptions({
            lang: {
                rangeSelectorZoom: '' // 不显示 'zoom' 文字
            }
        });
    }
    
    //累计收益走势 触摸时显示样式
    function yieldTextStyle(points){
    	for(var i=0;i<points.length;i++){
    		if(points[i].series.name == "本基金收益"){
    			var fund = points[i].y;
    			var fundClass="add";
    			if(fund<0){
    				fundClass="minus";
    			}
    			fund = (+fund).toFixed(2)+"%";
    		}else if(points[i].series.name == "沪深300"){
    			var hs300 = points[i].y;
    			var hs300Class="add";
    			if(hs300<0){
    				hs300Class="minus";
    			}
    			hs300 = (+hs300).toFixed(2)+"%";
    		}
    	}
    	
    	$(_pageId + " .moveData .fund .value").html(fund);
    	$(_pageId + " .moveData .fund .value").removeClass("minus").removeClass("add");
    	$(_pageId + " .moveData .fund .value").addClass(fundClass);
//    	$(_pageId + " .moveData .performance .value").html(performance);
    	$(_pageId + " .moveData .hs300 .value").html(hs300);
    	$(_pageId + " .moveData .hs300 .value").removeClass("minus").removeClass("add");
    	$(_pageId + " .moveData .hs300 .value").addClass(hs300Class);
    	
    	var time = new Date(points[0].x)
    	return "<div>" + time.getFullYear() +'/' + (time.getMonth()+1)+'/' +time.getDate() +"</div>"
    }
	
	//隐藏弹框
	function hideQuestionAlert(){
		$(_pageId + " .question").hide();
	}
	//显示弹框
	function showQuestionAlert(){
		$(_pageId + " .question").show();
	}
    
	function destroy() {
		$(_pageId + " #prod_name").html("--");
		$(_pageId + " #fund_code").html("--");
        $(_pageId + " #day_rate").html("--");
        $(_pageId + " #nav_date").html("--");
        $(_pageId + " #nav").html("--");
        $(_pageId + " #risk_level_name").html("--");
        $(_pageId + " #fund_type_name").html("--");
        $(_pageId + " #scale_fe").html("--");
        $(_pageId + " #performanceContent .list_content .item .value").html("--");
        $(_pageId + " #historyContent .list_content").html();
        $(_pageId + " #ksqrDate").html("--月--日");
        $(_pageId + " #ptqrDate").html("--月--日");
        $(_pageId + " #fund_managers").html("--");
        $(_pageId + " #mgrcomp_name").html("--");
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
        
        $(_pageId + " #performanceContent").show();
		$(_pageId + " #historyContent").hide();
		$(_pageId + " #performance").addClass("active");
		$(_pageId + " #history").removeClass("active");
	}

	function pageBack() {
		appUtils.pageBack();
	}
	var jjThirtyDetail = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = jjThirtyDetail;
});
