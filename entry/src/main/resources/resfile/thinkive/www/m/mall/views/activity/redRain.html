<div class="page" id="activity_redRain" data-pageTitle="红包雨" data-isSaveDom="false" data-refresh="true"
    style="-webkit-overflow-scrolling : touch;">
    <section class="redRain">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">红包雨</h1>
            </div>
        </header>
        <div class="prepare">
            <a class="prepare-text" id="startUp" href="javascript:;" clickName="redRainClick">我准备好了</a>
        </div>

        <ul class="couten">
            <!--<li>
                    <a href="#"><img src="images/hb_1.png"></a>
                </li>-->
        </ul>
        <div class="mo">
        </div>
        <div class="backward">
            <span></span>
        </div>
        <div class="wait" style="display: none;"></div>
        <div class="getScore" id="getScoreResult" style="">获得积分：<span class="score">20</span></div>
        <div class="result" style="display: none;">
            <div class="result-tip">恭喜您</div>
            <div class="result-message" id="resultMsg">获得142积分</div>
            <div class="result-btn" id="resultBtn"><a href="javascript:;">好的</a></div>
        </div>
    </section>

</div>