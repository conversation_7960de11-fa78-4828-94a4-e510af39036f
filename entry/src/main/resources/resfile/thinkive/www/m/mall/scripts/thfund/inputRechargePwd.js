// 晋金宝充值
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        msgFunction = require("msgFunction"),
        monkeywords = require("mall/scripts/common/moneykeywords"),
        _pageId = "#thfund_inputRechargePwd ";

    var jymm = "";
    var gconfig = require("gconfig");
    var spacing = "";
    var platform = gconfig.platform;
    var single_limit = "",
        day_limit = "";
    var total_bank = 0;
    var ut = require("../common/userUtil");
    var get_pdf_file = require("../common/StrongHintPdf")
    var tools = require("../common/tools");
    var sendMobile = '';
    var userInfo;
    let _fund_code = '000709';
    var  bankInfo;
    var sms_mobile = require("../common/sms_mobile");
    var is_exist; // 是否已签约 0 未签约  1：已签约
    var payorg_id;//支付机构
    var pay_type; //支付方式
    var bank_serial_no;
    var newResult; //产品信息
    var mobilebank_state; // 是否支持手机银行 0不支持 1支持
    var bank_state; // 银行状态  0 银行维护中 1 银行正常
    var isShowMobileBank = false; // 默认false 不支持  true 支持 ps: 支持手机银行且银行维护中、或超过单笔充值限额
    let sms_time; //倒计时
    var pay_mode;  //支付模式， 1 签约+支付 2 单独签约
    var pay_modelimit;  // 单独签约模式下发短信限额
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        sms_mobile.init(_pageId);
        //清除定时器
        clearTime();
        appUtils.setSStorageInfo("jjbFundCode", "000709");
        userInfo = ut.getUserInf();
        $(_pageId + " #inputspanid span").addClass("unable").css({ color: "#999999" });//默认输入框失去焦点
        sendMobile = userInfo.bankReservedMobile;
        tools.getPdf("prod", "000709", "1", "我已阅读并同意签署", "，承诺购买基金行为出于本人真实意愿且资金来源合法，已充分了解产品风险和服务内容"); //获取协议
        common.systemKeybord(); // 解禁系统键盘
        $(_pageId + " #bankName").empty(); //清空银行卡名称
        setBankCardInfo(); //设置银行卡信息
        //显示收益日期信息
        showTime();
        getFundDetail();
        // let perfect_info = userInfo.perfect_info;
        //到期3个月后提示
        // if(perfect_info == 4) {
        //     return layerUtils.iConfirm("您的身份证照片已到期，请先更换", ()=> {}, ()=> {
        //         appUtils.pageInit("thfund/inputRechargePwd", "account/uploadIDCard", {});
        //     }, "取消", "更换");
        // }
        //PDF相关，走公共方法
        is_show_paf()
    }
	//清除定时器
	function clearTime(){
	    if(sms_mobile._timer){
	        window.clearInterval(sms_mobile._timer);
	        sms_mobile._timer = null;
	    }
	}
    function is_show_paf() {
        get_pdf_file.get_file(_fund_code, _pageId)
    }
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //隐藏充值提示
        appUtils.bindEvent($(_pageId), function () {
            $(_pageId + " .pop_text").hide();
        });
        //详情页面
        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            // appUtils.setSStorageInfo("fund_code", $(_pageId + " .fund_code").text());
            appUtils.setSStorageInfo("prod_sub_type", $(_pageId + " .prod_sub_type").text());
            appUtils.pageInit("thfund/inputRechargePwd", "thfund/HADetail");
        });
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var czje = $(_pageId + " #czje").val();
            czje = czje.replace(/,/g, "");
            if (!validatorUtil.isMoney(czje)) {
                layerUtils.iAlert("请输入正确的充值金额");
                return;
            }
            if (parseFloat(czje) == 0) {
                layerUtils.iAlert("请输入正确的充值金额");
                return;
            }
            var rst = 0;
            for (var i = 0; i < czje.length; i++) {
                if (czje[i] == 0) {
                    rst++;
                }
            }
            if (czje.length == rst) {
                layerUtils.iAlert("请输入正确的充值金额");
                return;
            }
            if ($(_pageId + " #getYzm").attr("data-state") == "false") {
                return;
            }

            var trans_amt = czje;
            // console.log(mobilebank_state == "1" && (bank_state == "0" || (bank_state == "1" && (parseFloat(single_limit) < parseFloat(trans_amt) && parseFloat(single_limit) >= 0))));
            if (mobilebank_state == "1" && (bank_state == "0" || (bank_state == "1" && (parseFloat(single_limit) < parseFloat(trans_amt) && parseFloat(single_limit) >= 0)))) {
                var param = {
                    "mobile_phone": sendMobile,
                    "type": common.sms_type.fundInput,
                    "send_type": "0",
                    "mobile_type": "2",
                }
                sms_mobile.sendPhoneCode(param);
            } else {
               if (is_exist == "1") { // 已签约
                    let trans_amt = $(_pageId + " #czje").val();
                    trans_amt = trans_amt.replace(/,/g, "");
                    //新模式而且金额大于5000
                    if(pay_mode == '2'){
                        $(_pageId + " #inputspanid span").css({ color: "#666666" });//输入金额置灰
                        appUtils.setSStorageInfo("icbc_isClick", "0");//不可点击
                    }
                     if(pay_mode=="2"&&parseFloat(trans_amt)>parseFloat(pay_modelimit))  {
                       // cust_no|fund_code|trans_amt|payorg_id|pay_mode|pay_type
                        var param = {
                            "payorg_id": payorg_id,
                            "pay_type": pay_type,
                            "bank_code": userInfo.bankCode,
                            "bank_acct": userInfo.bankAcct,
                            "bank_reserved_mobile": userInfo.bankReservedMobile,
                            "cert_no": userInfo.identityNum,
                            "bank_name": userInfo.bankName,
                            "sms_type": common.sms_type.fundInput,
                            "cert_type": "0",
                            "cust_name": userInfo.name,
                            "sms_time":sms_time,
                            "fund_code":"000709",
                            "cust_no":userInfo.cust_no,
                            "pay_mode":pay_mode,
                            "trans_amt":trans_amt,
                            // "mobile_phone": sendMobile,
                        }
                        // console.log(param);
                        sms_mobile.sendPhoneCodeModeFundProto(param, function (data) {
                            if (data.error_no == "0") {
                                bank_serial_no = data.results[0].proto_serno;
                            }
                        });
                     }else{
                        var param = {
                            "mobile_phone": sendMobile,
                            "type": common.sms_type.fundInput,
                            "send_type": "0",
                            "mobile_type": "2",
                        }
                        sms_mobile.sendPhoneCode(param);
                    }
                } else {  // 未签约
                    var param = {
                        "payorg_id": payorg_id,
                        "pay_type": pay_type,
                        "bank_code": userInfo.bankCode,
                        "bank_acct": userInfo.bankAcct,
                        "bank_reserved_mobile": userInfo.bankReservedMobile,
                        "cert_no": userInfo.identityNum,
                        "bank_name": userInfo.bankName,
                        "sms_type": common.sms_type.fundInput,
                        "cert_type": "0",
                        "cust_name": userInfo.name,
                        "sms_time":sms_time
                    }
                    if(param.payorg_id == '014'){
                        let trans_amt = $(_pageId + " #czje").val();
                        trans_amt = trans_amt.replace(/,/g, "");
                        param.trans_amt = trans_amt;
                    }
                    sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                        if (data.error_no == "0") {
                            bank_serial_no = data.results[0].bank_serial_no
                        }
                    });
                }
            }



        }, 'click');

        //点击确定密码
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            quedingoumai(jymm);
        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .pop_layer").css("display", "none");
            $(_pageId + " #rechargeInfo").empty();
            // $(_pageId + " .excess_layer").show();
            $(_pageId + " #jymm").val("");
            guanbi();
        });
        // appUtils.bindEvent($(_pageId + " .short_label2"), function () {
        //     sms_mobile.clear();
        // });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            if(appUtils.getSStorageInfo("icbc_isClick") == '0') return;
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            if(is_exist=="0"&&pay_mode=="2"){
                $(_pageId + " #zfqyyzm").val('')
                $(_pageId + " #zfqy").show();
                let params = {
                    cust_no:userInfo.cust_no,
                    payorg_id:payorg_id,
                    fund_code:_fund_code,
                    page_code:'thfund/inputRechargePwd'
                }
                return tools.getPayPdf(params);
            }
            // 银行渠道维护中，提示超额
            if (mobilebank_state == 0 && bank_state == 0) {
                //点击汇款充值
                let operationId = 'remittanceRecharge'
                layerUtils.iConfirm("充值超过限额,请汇款充值", function funOk() {
                    
                    monkeywords.empty();
                    $(_pageId + " #nextBtn").hide();
                    $(_pageId + " #nextBtn_one a").html("银行渠道维护中");
                    $(_pageId + " #nextBtn_one").show();
                    $(_pageId + " #xianErXianShi").hide();
                }, function funcNo() {
                    appUtils.pageInit("thfund/inputRechargePwd", "account/payRecharge");
                }, "取消", "确定",operationId);
                $(_pageId + " #xianErXianShi").html("<em><span style='font-weight:bold; color:#e5443c;'>超出银行单笔限额</span></em>");
                $(_pageId + " #xianErXianShi").show();
                $(_pageId + " #nextBtn").hide();
                $(_pageId + " #nextBtn_one").show();

            } else {
                $(_pageId + " #czje").val('');
                $(_pageId + " #nextBtn").show();
                $(_pageId + " #nextBtn_one").hide();
                $(_pageId + " #xianErXianShi").hide();
                $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
                //键盘事件
                moneyboardEvent();
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "thfund_inputRechargePwd";
                param["eleId"] = "czje";
                param["doneLable"] = "确定";
                param["keyboardType"] = "3";
                require("external").callMessage(param);
            }

        });


        // 手机号码输入事件
        appUtils.bindEvent($(_pageId + " #yzm"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");

        //点击下一步
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            monkeywords.close();
            var czje = $(_pageId + " #czje").val();
            czje = czje.replace(/,/g, "");
            var bank_no = appUtils.getSStorageInfo("bankNo");
            var yzmState = $(_pageId + " #getYzm").attr("data-state");
            var inputYzm = $(_pageId + " #yzm").val();
            // console.log(userInfo,newResult)
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (newResult.risk_level != 'R1')) return tools.is_show_c0('thfund/inputRechargePwd')
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (yzmState == "true") {
                layerUtils.iAlert("您还未获取验证码");
                return;
            }
            if (inputYzm.length < 6) {
                layerUtils.iAlert("请确定您的验证码输入的格式是否正确");
                return;
            }
            if (!validatorUtil.isMoney(czje)) {
                layerUtils.iAlert("请输入正确的充值金额");
                return;
            }
            if (parseFloat(czje) == 0) {
                layerUtils.iAlert("请输入正确的充值金额");
                return;
            }
            var rst = 0;
            for (var i = 0; i < czje.length; i++) {
                if (czje[i] == 0) {
                    rst++;
                }
            }
            if (czje.length == rst) {
                layerUtils.iAlert("请输入正确的充值金额");
                return;
            }
            if (czje.split(".").length < 2) {
                czje = czje + ".00";
            }

            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            if (mobilebank_state == 1 && (bank_state == 0 || (bank_state == 1 && (parseFloat(single_limit) < parseFloat(trans_amt) && single_limit >= 0)))) {
                $(_pageId + ".mobile_bank_name").html(userInfo.bankName);
                //获取当前手机类型
                if(platform == '5'){
                    //点击汇款充值
                    let operationId = 'remittanceRecharge'
                    layerUtils.iConfirm("充值超过限额,请汇款充值", function funOk() {
                    }, function funcNo() {
                        appUtils.pageInit("thfund/inputRechargePwd", "account/payRecharge");
                    }, "取消", "确定",operationId);
                }else{
                    $(_pageId + " .excess_layer").show();
                }
                
                return;
            }

            passboardEvent();
            $(_pageId + " #rechargeInfo").html("使用<em>尾号" + userInfo.bankAcct.substr(-4) + " " + userInfo.bankName + "卡，</em>充值<em>" + czje + "</em>元");
            var phoneNum = appUtils.getSStorageInfo("mobile");
            $(_pageId + " .pop_layer").eq(0).show();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_inputRechargePwd";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //汇款充值
        appUtils.bindEvent($(_pageId + " #hkcz"), function () {
            appUtils.pageInit("thfund/inputRechargePwd", "account/payRecharge");
        });
        //支持的银行卡
        appUtils.bindEvent($(_pageId + " .supportedBankCards"), function () {
            appUtils.setSStorageInfo("isShowChangeBank",'1')
            appUtils.pageInit("thfund/inputRechargePwd", "safety/bankInfo");
        });

        // 手机银行充值
        appUtils.bindEvent($(_pageId + " #mobileRecharge"), function () {
            mobileBankRecharge();
        })
        // 点击完成，回到首页
        appUtils.bindEvent($(_pageId + " #complete"), function () {
            $(_pageId + " #jymm").val("");
            jymm = "";
            $(_pageId + " .confirm_layer").hide();
            appUtils.pageInit("thfund/inputRechargePwd", "login/userIndexs", {});
        })
        // 点击继续支付
        appUtils.bindEvent($(_pageId + " #confinuePay"), function () {
            $(_pageId + " #jymm").val("");
            jymm = "";
            $(_pageId + " .confirm_layer").hide();
            mobileBankRecharge();
        })
        //关闭支付签约弹窗
        appUtils.bindEvent($(_pageId +" #zfqy #zfqy_close"), function () {
            tools.getPdf("prod", "000709", "1", "我已阅读并同意签署", "，承诺购买基金行为出于本人真实意愿且资金来源合法，已充分了解产品风险和服务内容"); //获取协议           
            $(_pageId + " #zfqy").hide();
        });
        //确认签约
        appUtils.bindEvent($(_pageId + " #zfqy #zfqy_ok"), function () {
            if (pay_mode=='2'&&!$(_pageId + " #zfqy .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            var isSend = $(_pageId + " #zfqygetYzm").attr("data-state");  // 判断是否获取验证码
            var verificationCode = $(_pageId + " #zfqyyzm").val();   // 判断是否填写验证码
            guanbi();
            if( isSend == "true"){
                return layerUtils.iAlert("请获取验证码");
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            
            var verificationCode = $(_pageId + " #zfqyyzm").val();
            var reqParam = {
                // "transpwd": SysTransPwd,
                "isexist":'0',
                "messagecode":verificationCode,
                "bankserialno":bank_serial_no,
                "payorgid":payorg_id
            };
            service.reqFun106071(reqParam, function(data) {
                if(data.error_no == "0") {
                    layerUtils.iAlert("银行卡签约成功", function () {}, function () {
                        $(_pageId + " #zfqy").hide();
                       //重新加载协议
                        tools.getPdf("prod", "000709", "1", "我已阅读并同意签署", "，承诺购买基金行为出于本人真实意愿且资金来源合法，已充分了解产品风险和服务内容"); //获取协议
                        is_exist="1";
                    });
                } else {
                    let is_zfqy = '#zfqygetYzm';
                    //是否是工行特殊处理
                    sms_mobile.clear(is_zfqy);
                    layerUtils.iAlert(data.error_info);
                }
            });
        });

        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #zfqy #zfqygetYzm"), function () {
            if (pay_mode=='2'&&!$(_pageId + " #zfqy .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            var $code = $(_pageId + " #zfqy #zfqygetYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } else {
                var param = {
                    "bank_code": userInfo.bankCode,//银行编码
                    "pay_type": bankInfo.pay_type,
                    "payorg_id": bankInfo.payorg_id,
                    "bank_acct": userInfo.bankAcct,     // 用户卡号
                    "bank_reserved_mobile":userInfo.bankReservedMobile,
                    "cert_no": userInfo.identityNum,   // 用户身份证
                    "bank_name":userInfo.bankName,
                    "sms_type":common.sms_type.bankInvestment,
                    "send_type": "0",
                    "cust_name": userInfo.name, // 用户姓名
                    "cert_type": "0", //证件类型
                    "mobile_phone": userInfo.mobileWhole,
                    "type": common.sms_type.bankInvestment,//发送短信验证码
                    "page_id":"zfqy",
                    "isDisabled": '0',
                    "sms_time":sms_time
                }
                sms_mobile.sendPhoneCodeFundProto(param, function (data) {
                    if (data.error_no == "0") {
                        bank_serial_no = data.results[0].bank_serial_no
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });

         // 验证码 控制全文字
         appUtils.bindEvent($(_pageId + " #zfqy #zfqyyzm"), function () {
            // guanbi();
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        get_pdf_file.clearTime()
        clearTime();
        $(_pageId + " .pop_text").hide();
        //初始化下一步按钮
        $(_pageId + " #nextBtn_one").hide();
        $(_pageId + " #nextBtn").show();
        $(_pageId + " #xianErXianShi").hide();
        //设置密码框为不显示
        $(_pageId + " #inputspanid span").addClass("unable").css({ color: "#999999" });//默认输入框失去焦点
        $(_pageId + " #rechargeInfo").html("")
        $(_pageId + " #bankName").html("");
        $(_pageId + " #czje").val("");
        $(_pageId + " #yzm").val("");
        sms_mobile.destroy();
        $(_pageId + " #nextBtn_one").hide();
        $(_pageId + " #nextBtn").show();
        guanbi();
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .excess_layer").hide();
        $(_pageId + " .confirm_layer").hide();
        $(_pageId + " #nextStep").html("下一步");
        $(_pageId + " #nextBtn").css("pointer-events", "auto");
        $(_pageId + " .van-overlay").hide();
        $(_pageId + " #zfqy").hide();
        total_bank = 0;
        is_exist = "";
        pay_mode = "";  //支付模式， 0 签约+支付 1 单独签约
        pay_modelimit = "";  // 单独签约模式下发短信限额
        appUtils.setSStorageInfo("icbc_isClick", "1");//可以点击
    }

    function pageBack() {
        appUtils.pageBack();
    }

    /**********************************************************************************************/
    //进行充值 入金 已签约，本地验证码校验
    function userChongZhi_ff(param) {
        var buycallback = function (data) {
            
            if (data.error_no == "0") {
                $(_pageId + " #nextStep").html("下一步");
                var param = data.results[0];
                appUtils.pageInit("thfund/inputRechargePwd", "thfund/inputResult", param);
            } else {
                layerUtils.iAlert(data.error_info);
                $(_pageId + " #nextStep").html("确定");
            }
        }
        sms_mobile.clear();
        $(_pageId + " #nextBtn").css("pointer-events", "auto");
        $(_pageId + " #rechargeInfo").empty();
        $(_pageId + " #jymm").val("");
        service.reqFun106001(param, function (data) {
            
            buycallback(data);
        });
    }
    //手机银行确定
    function mobileBankqd(param) {
        var buycallback = function (data) {
            
            if (data.error_no == "0") {
                $(_pageId + " #nextStep").html("下一步");
                var results = data.results[0];
                let sort = ['MERCHANTID', 'POSID', 'BRANCHID', 'ORDERID', 'PAYMENT', 'CURCODE', 'TXCODE', 'REMARK1', 'REMARK2',
                    'TYPE', 'GATEWAY', 'CLIENTIP', 'REGINFO', 'PROINFO', 'REFERER', 'THIRDAPPINFO', 'TIMEOUT', 'MAC', 'PAYMAP'];
                let sortParams = {};
                for (i = 0; i < sort.length; i++) {
                    sortParams[sort[i]] = results[sort[i]] ? results[sort[i]] : '';
                }
                var paramsString = "";
                for (let i in sortParams) {
                    paramsString += `${i}=${sortParams[i]}&`
                }
                paramsString = paramsString.substring(0, paramsString.lastIndexOf('&'));
                // console.log(paramsString);
                // console.log("https://ibsbjstar.ccb.com.cn/CCBIS/ccbMain?" + paramsString);
                // 调原生
                var param = {};
                param["funcNo"] = "80303";
                param["orderString"] = paramsString;//订单信息
                require("external").callMessage(param);

                // $(_pageId + " .confirm_layer").show();
            } else {
                layerUtils.iAlert(data.error_info);
                $(_pageId + " #nextStep").html("确定");
            }
            
        }
        sms_mobile.clear();
        $(_pageId + " #nextBtn").css("pointer-events", "auto");
        $(_pageId + " #rechargeInfo").empty();
        $(_pageId + " #jymm").val("");
        service.reqFun106027(param, function (data) {
            buycallback(data);
        });
    }

    //进行充值 入金 未签约 银行验证码校验
    function userChong_proto(param) {
        var buycallback = function (data) {
            if (data.error_no == "0") {
                $(_pageId + " #nextStep").html("下一步");
                var param = data.results[0];
                appUtils.pageInit("thfund/inputRechargePwd", "thfund/inputResult", param);
            } else {
                layerUtils.iAlert(data.error_info);
                $(_pageId + " #nextStep").html("确定");
            }
            
        }
        sms_mobile.clear();
        $(_pageId + " #nextBtn").css("pointer-events", "auto");
        $(_pageId + " #rechargeInfo").empty();
        $(sms_mobile._pageId + " #inputspanid span").css({ color: "#000" });//输入金额正常
        appUtils.setSStorageInfo("icbc_isClick", "1");//可以点击
        $(_pageId + " #jymm").val("");
        service.reqFun9106001(param, function (data) {
            buycallback(data);
        });
    }

    //点击确定
    function quedingoumai(jymm1) {
        guanbi();
        $(_pageId + " .pop_layer").hide();
        if (jymm1.length != 6) {
            layerUtils.iAlert("请确定您的交易密码格式正确");
            return;
        }
        // 如果支持手机银行
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");


        $(_pageId + " #nextStep").html("充值中...");
        $(_pageId + " #nextBtn").css("pointer-events", "none"); //禁用标签的事件

        //进行充值
        var sms_code = $(_pageId + " #yzm").val();
        var param = {
            trans_amt: trans_amt, //交易金额
            trans_pwd: jymm1, //交易密码
            sms_mobile: userInfo.bankReservedMobile,
            sms_code: sms_code,
            fund_code: $(_pageId + " .fund_code").html(),
            acct_no: userInfo.fncTransAcctNo,
            is_exist: is_exist,
            payorg_id: payorg_id,
            pay_type: pay_type,
            message_code: sms_code,
            bank_code: userInfo.bankCode,
            bank_serial_no: bank_serial_no,
            agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
            pay_mode:pay_mode
        }
        //交易密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no != "0") {
                $(_pageId + " #nextStep").html("充值中...");
                $(_pageId + " #nextBtn").css("pointer-events", "none"); //禁用标签的事件
                layerUtils.iLoading(false);
                return;
            }
            var modulus = data.results[0].modulus;
            var publicExponent = data.results[0].publicExponent;
            var endecryptUtils = require("endecryptUtils");
            param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
            if (mobilebank_state == 1 && (bank_state == 0 || (bank_state == 1 && (parseFloat(single_limit) < parseFloat(trans_amt) && single_limit >= 0)))) {
                mobileBankqd(param);
            } else {
                if (is_exist == "1") { //已签约
                    if(pay_mode=="2"&&parseFloat(trans_amt)>parseFloat(pay_modelimit)){
                        userChong_proto(param);
                    }else{
                        userChongZhi_ff(param);
                    }
                } else {//未签约
                    userChong_proto(param);
                }
            }

        }, { isLastReq: false });
    }

    // 点击手机银行充值
    function mobileBankRecharge() {
        $(_pageId + " .excess_layer").hide();
        var czje = $(_pageId + " #czje").val();
        czje = czje.replace(/,/g, "");
        passboardEvent();
        $(_pageId + " #rechargeInfo").html("使用<em>尾号" + userInfo.bankAcct.substr(-4) + " " + userInfo.bankName + "卡，</em>充值<em>" + czje + "</em>元");
        var phoneNum = appUtils.getSStorageInfo("mobile");
        $(_pageId + " .pop_layer").show();
        monkeywords.flag = 0;
        //键盘事件
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "thfund_inputRechargePwd";
        param["eleId"] = "jymm";
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    }

    //设置银行卡信息  新晋金宝用户银行卡限额查询查询通联
    function setBankCardInfo() {
        var bankCard = userInfo.bankAcct;
        $(_pageId + " .bankIcon img").attr("src", "./images/bank_" + userInfo.bankCode + ".png");
        $(_pageId + " .bankInfo p").eq(0).html(userInfo.bankName + " <span style='width: 60%;' id='bankCardNum'>" + bankCard.substr(0, 4) + "  ****  ****  " + bankCard.substr(-4));
        $(_pageId + " #zfqy .card_rules .grid_03 div").eq(0).html("开通<a style='font-weight: 700;'>"+userInfo.bankName+"（尾号<span>"+bankCard.substr(-4)+"</span>）</a>支付签约功能，请获取短信验证码完成签约。");
        $(_pageId + " #zfqy #bankcardphone").html(userInfo.bankReservedMobile); 
        service.reqFun102077({ bank_code: userInfo.bankCode }, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                if (data.results && data.results.length > 0) {
                    var result = data.results[0];
                    bankInfo = result;
                    $(_pageId + " #nextBtn_one").hide();
                    $(_pageId + " #nextBtn_one a").html("下一步");
                    $(_pageId + " #nextBtn").show();
                    single_limit = result.single_limit;
                    day_limit = result.day_limit;
                    is_exist = result.is_exist; //是否签约 0 未签约  1 已签约
                    payorg_id = result.payorg_id;//支付机构
                    pay_type = result.pay_type; //支付方式
                    pay_mode= result.pay_mode;  //支付模式， 1 签约+支付 2 单独签约
                    pay_modelimit= result.pay_sendsms_amt;  // 单独签约模式下发短信限额

                    mobilebank_state = result.mobilebank_state;  // 是否支持手机银行 0 不支持  1 支持
                    bank_state = result.bank_state; // 银行状态 0 银行维护中  1  银行正常
                    
                    sms_time = result.sms_time; //验证码倒计时
                    
                    if (mobilebank_state == 0 && bank_state == 0) {
                        $(_pageId + " #nextBtn").hide();
                        $(_pageId + " #nextBtn_one a").html("银行渠道维护中");
                        $(_pageId + " #nextBtn_one").show();
                    }

                    if (single_limit) {
                        if (single_limit < 0) {
                            $(_pageId + " .single_limit").html("不限");
                        } else {
                            if (single_limit.length > 4) {
                                $(_pageId + " .single_limit").html(single_limit / 10000 + "万元");
                            } else {
                                $(_pageId + " .single_limit").html(single_limit + "元");
                            }
                        }
                    }

                    if (day_limit) {
                        if (day_limit < 0) {
                            $(_pageId + " .day_limit").html("不限");
                        } else {
                            if (day_limit.length > 4) {
                                $(_pageId + " .day_limit").html(day_limit / 10000 + "万元");
                            } else {
                                $(_pageId + " .day_limit").html(day_limit + "元");
                            }
                        }
                    }
                    if(is_exist == "0" && pay_mode == "2"){
                        paySign();
                    }
                }
            } else {
                is_exist = "0"
                $(_pageId + " #nextBtn_one").hide();
                $(_pageId + " #nextBtn_one a").html("下一步");
                $(_pageId + " #nextBtn").show();
                $(_pageId + " #getYzm").show();
                $(_pageId + " #getYzmStop").hide();
                $(_pageId + " #czje").removeAttr("readonly");
                $(_pageId + " #hkcz").show();
                $(_pageId + " #hkczStop").hide();
                layerUtils.iAlert(error_info);
            }
        });
    }
    function paySign(){
        let params = {
            cust_no:userInfo.cust_no,
            payorg_id:payorg_id,
            fund_code:_fund_code,
            page_code:"thfund/inputRechargePwd"
        }
        tools.getPayPdf(params); //获取协议
        $(_pageId + " #zfqyyzm").val('');
        $(_pageId + " #zfqy").show();
    }
    //显示收益日期信息
    function showTime() {
        service.reqFun102008({ type: "1" }, function (data) {
            var error_no = data.error_no;
            if (error_no == "0") {
                var beforeDate = data.results[0].beforeDate;
                var afterDate = data.results[0].afterDate;
                beforeDate = common.dateStyle2(beforeDate);
                afterDate = common.dateStyle2(afterDate);
                $(_pageId + " #yqsj").text(beforeDate);
                $(_pageId + " #afterSy").text(afterDate);
            }
        })
    }


    //关闭键盘
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                $(_pageId + " .pop_text").hide();
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #czje").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                    return;
                }
                var czje = $(_pageId + " #czje").val();
                czje = czje.replace(/,/g, "");
                // 如果客户绑定卡不支持手机银行,客户输入金额超过单笔限额时，弹框提示客户可以进行汇款充值
                if (mobilebank_state == 0 || platform == '5') {
                    if (parseFloat(single_limit) < parseFloat(czje) && single_limit >= 0) {
                        //点击汇款充值
                        let operationId = 'remittanceRecharge'
                        layerUtils.iConfirm("充值超过限额,请汇款充值", function funOk() {
                            monkeywords.empty();
                            $(_pageId + " #nextBtn").show();
                            $(_pageId + " #nextBtn_one").hide();
                            $(_pageId + " #xianErXianShi").hide();
                        }, function funcNo() {

                            appUtils.pageInit("thfund/inputRechargePwd", "account/payRecharge");
                        }, "取消", "确定",operationId);
                        $(_pageId + " #xianErXianShi").html("<em><span style='font-weight:bold; color:#e5443c;'>超出银行单笔限额</span></em>");
                        $(_pageId + " #xianErXianShi").show();
                        $(_pageId + " #nextBtn").hide();
                        $(_pageId + " #nextBtn_one").show();
                    }
                } else {
                    $(_pageId + " #nextBtn").show();
                    $(_pageId + " #nextBtn_one").hide();
                    $(_pageId + " #xianErXianShi").hide();
                }
                // if (parseFloat(single_limit) < parseFloat(czje) && single_limit >= 0) {
                //     //点击汇款充值
                //     layerUtils.iConfirm("充值超过限额,请汇款充值", function funOk() {
                //         monkeywords.empty();
                //         $(_pageId + " #nextBtn").show();
                //         $(_pageId + " #nextBtn_one").hide();
                //         $(_pageId + " #xianErXianShi").hide();
                //     }, function funcNo() {

                //         appUtils.pageInit("thfund/inputRechargePwd", "account/payRecharge");
                //     }, "取消", "确定");
                //     $(_pageId + " #xianErXianShi").html("<em><span style='font-weight:bold; color:#e5443c;'>超出银行单笔限额</span></em>");
                //     $(_pageId + " #xianErXianShi").show();
                //     $(_pageId + " #nextBtn").hide();
                //     $(_pageId + " #nextBtn_one").show();
                // } else {
                //     $(_pageId + " #nextBtn").show();
                //     $(_pageId + " #nextBtn_one").hide();
                //     $(_pageId + " #xianErXianShi").hide();
                // }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
            },
            keyBoardHideFunction: function () {
                $(_pageId + " .pop_text").hide();
            },
            keyBoardFinishFunction: function () {
                $(_pageId + " .pop_text").hide();
            }
        })
    }

    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                //						quedingoumai(jymm);
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    // TODO;
    function getFundDetail() {
        service.reqFun102002({ fund_code: "000709" }, function (data) {
            if (data.error_no == "0") {
                newResult = data.results[0];
                $(_pageId + " .fund_code").html(newResult.fund_code); // fund_code代码
                $(_pageId + " .prod_sname").html(newResult.prod_sname);// prod_sname 简称
                $(_pageId + " .prod_sub_type").html(newResult.prod_sub_type);// prod_sub_type 产品子类别
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    var inputRechargePwd = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = inputRechargePwd;
});
