// 引流注册-上传身份证照片
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        pageCode = "drainage/uploadIDCard",
        ut = require("../common/userUtil"),
        validatorUtil = require("validatorUtil"),
        _pageId = "#drainage_uploadIDCard";
    var saveImgNum;
    var userInfo;
    var tools = require("../common/tools");
    var monkeywords = require("../common/moneykeywords");
    require("../../js/prov_city.js");
    require("../../js/city.js");
    require("../../js/picker.min.js");
    var first = []; /* 省，直辖市 */
    var second = []; /* 市 */
    var third = []; /* 镇 */
    var selectedIndex = [0, 0, 0]; /* 默认选中的地区 */
    var checked = [0, 0, 0]; /* 已选选项 */
    var picker = ""; //地址选择器


    function init() {
        $(_pageId + " #next").css({backgroundColor: "#E5433B"});
        userInfo = ut.getUserInf();
        $(_pageId + " .cust_address").hide()
        saveImgNum = 0;
        if (!appUtils.getSStorageInfo("idCardInfo")) {
            // appUtils.clearSStorage("bankAccInfo");
            $(_pageId + " input").val("");
            $(_pageId + " #realName").attr("disabled");
            $(_pageId + " #idCard").attr("disabled");
            $(_pageId + " .zm").removeAttr("isAllow");
            $(_pageId + " .fm").removeAttr("isAllow");
            $(_pageId + " #zm_img").attr("src", "./images/sfz_01.png");
            $(_pageId + " #fm_img").attr("src", "./images/sfz_02.png");
            $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
            // $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
            // queryJob();
            // queryIncome();
            // selectorArea();
        } else {
            var idCardInfo = appUtils.getSStorageInfo("idCardInfo");
            $(_pageId + " .zm").attr("isAllow", true);
            $(_pageId + " .fm").attr("isAllow", true);
            $(_pageId + " #idCard").val(idCardInfo.cert_no).removeAttr("disabled");
            $(_pageId + " #realName").val(idCardInfo.cust_name).removeAttr("disabled");
            $(_pageId + " #sex").val(idCardInfo.sex);
            $(_pageId + " #cust_address").val(idCardInfo.cust_address);
            $(_pageId + " #vaild_date").val(idCardInfo.vaild_date);
            $(_pageId + " .rule_check #xuanzeqi i").addClass("active");
            if($(_pageId + " #cust_address").val().length < 10){
                $(_pageId + " .cust_address").show()
                $(_pageId + " .cust_address").removeAttr("disabled");
            }
            // $(_pageId + " #next").css({backgroundColor: "#E5433B"});
            // queryJob(idCardInfo.vocation_code);
            // queryIncome(idCardInfo.income);
            // selectorArea(idCardInfo.live_address);
        }
    }

    //绑定事件
    function bindPageEvent() {
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            var param = {};
            param["backPageUrl"] = pageCode;
            tools.saveAlbum(pageCode,param)
        });
        //操作指南
        appUtils.bindEvent($(_pageId + " .operate_guide"), function () {
            appUtils.pageInit(pageCode, "guide/uploadGuide");
        });
        // 点击协议书前的checkBox
        appUtils.bindEvent($(_pageId + " .rule_check #xuanzeqi"), function (e) {
            e.stopPropagation();
            e.preventDefault();
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active");
                // $(_pageId + " #next").css({backgroundColor: "#D1D4D5"});
            } else {
                $(this).find("i").addClass("active");
                // $(_pageId + " #next").css({backgroundColor: "#E5433B"});

            }
        });
        appUtils.bindEvent($(_pageId + " #live_address"), function () {
            picker.show();
        })
        //点击下一步
        appUtils.bindEvent($(_pageId + " #next"), function () {
            // var classname = $(_pageId + " .rule_check #xuanzeqi i").attr("class"); // 判断是否签署协议

            var income_id = $(_pageId + " #income").attr("data-id");
            if (income_id == "04") {
                var income_money = $(_pageId + " #income").val();
            } else {
                var income_money = $(_pageId + " #income").attr("data-money");
            }
          
            // if (classname != "active") {
            //     layerUtils.iAlert("请确认个人税收居民类型");
            //     return;
            // }
            if (!$(_pageId + " .zm").attr("isAllow")) {
                layerUtils.iMsg(-1, "请拍摄身份证人像面");
                return;
            }
            if (!$(_pageId + " .fm").attr("isAllow")) {
                layerUtils.iMsg(-1, "请拍摄身份证国徽面");
                return;
            }
            // if (validatorUtil.isEmpty($(_pageId + " #occp").val())) {
            //     layerUtils.iMsg(-1, "请选择职业");
            //     return;
            // }
            // if (validatorUtil.isEmpty($(_pageId + " #income").val())) {
            //     layerUtils.iMsg(-1, "请选择年收入");
            //     return;
            // }
            // if (validatorUtil.isEmpty($(_pageId + " #live_address").val())) {
            //     layerUtils.iMsg(-1, "请选择地址");
            //     return;
            // }
            var realname = $(_pageId + " #realName").val();
            var idCard = $(_pageId + " #idCard").val();
            var vaild_date = $(_pageId + " #vaild_date").val();
            if (new Date(vaild_date + " 23:59:59").getTime() < new Date().getTime()) {
                layerUtils.iAlert("身份证已过期，请重新上传");
                return;
            }
            var sex = $(_pageId + " #sex").val();
            var cust_address = $(_pageId + " #cust_address").val();
            var live_address = $(_pageId+ " #live_address").val();
            if(cust_address.length < 10){
                return layerUtils.iAlert("请输入十个字及以上的地址");
            }
            if (realname != userInfo.name) {
                layerUtils.iMsg(-1, "请上传本人身份证");
                return;
            }
            if (idCard.substr(0, 4) != userInfo.identityNum.substr(0, 4) || idCard.substr(-4) != userInfo.identityNum.substr(-4)) {
                layerUtils.iMsg(-1, "请上传本人身份证");
                return;
            }
            //处理msgFunction缓存问题
            if(vaild_date.split("-").length == 2) {
                vaild_date = vaild_date.split("-")[1].replace(/\./g, "/")
            }
            let bankAccInfo = appUtils.getSStorageInfo('bankAccInfo')
            var param = {            	
                "cert_no": bankAccInfo.cert_no,
                "vaild_date": vaild_date,
                "sex": sex,
                "cust_address":cust_address,
                "bank_code": bankAccInfo.bank_code,     //银行编码
                "cardNo": bankAccInfo.bankCard,//银行卡号
                "cust_name": bankAccInfo.cust_name,
                "bank_reserved_mobile": bankAccInfo.bank_reserved_mobile,
                "sms_mobile": bankAccInfo.sms_mobile,
                "mobile": bankAccInfo.mobile,
                "sms_code": bankAccInfo.sms_code,
                "recommend": bankAccInfo.recommend,
                "cert_type": "0",
                "pay_type": bankAccInfo.pay_type,
                "payorg_id": bankAccInfo.payorg_id,
                "bank_acct": bankAccInfo.bank_acct,
                "bank_name": bankAccInfo.bank_name,
                // "vocation_code": {
                //     id: $(_pageId + " #occp").attr("data-value"),
                //     name: $(_pageId + " #occp").val()
                // },
                // "year_income": {
                //     money: income_money,
                //     id: $(_pageId + " #income").attr("data-id"),
                //     value: $(_pageId + " #income").attr("data-value")
                // },
                // "living_address":{name: $(_pageId + " #live_address").val(),code:$(_pageId + " #live_address").attr("data-code")}
            }
            appUtils.setSStorageInfo("idCardInfo", param);
            //留存身份证正反面
            saveIdCard(param);

        });

        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        // 身份证正面拍照
        appUtils.bindEvent($(_pageId + " .zm"), function () {
            appUtils.setSStorageInfo("ocrType", "upload");
            tools.openCamera("zm");
        });
        // 身份证反面拍照
        appUtils.bindEvent($(_pageId + " .fm"), function () {
            tools.openCamera("zm");
        });
        
        //关闭数字键盘
        appUtils.bindEvent($(_pageId + " #moneyBox"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "drainage_uploadIDCard";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);

        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " .input_box2"), function (e) {
            $(_pageId + " #srje").val('');
            $(_pageId + " #inputspanid span").html('');
            $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "drainage_uploadIDCard";
            param["eleId"] = "srje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
    }

    //留存身份证正反面
    function saveIdCard(param) {
        //身份证正面，去掉base64编码 头
        var base64ZM = $(_pageId + " #zm_img").attr("src");
        if (base64ZM.indexOf("data:image/jpeg;base64,") != -1) {
            base64ZM = base64ZM.replace("data:image/jpeg;base64,", "");
        }
        if (base64ZM.indexOf("data:image/png;base64,") != -1) {
            base64ZM = base64ZM.replace("data:image/png;base64,", "");
        }

        //身份证反面，去掉base64编码 头
        var base64FM = $(_pageId + " #fm_img").attr("src");
        if (base64FM.indexOf("data:image/jpeg;base64,") != -1) {
            base64FM = base64FM.replace("data:image/jpeg;base64,", "");
        }
        if (base64FM.indexOf("data:image/png;base64,") != -1) {
            base64FM = base64FM.replace("data:image/png;base64,", "");
        }
        var paramZM = {
            "base_data": base64ZM,
            "flag": "0",
        }
        let idInfo = {}
        service.reqFun199011(paramZM, function (data) {
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                appUtils.clearSStorage("openLeavePage");
                appUtils.setSStorageInfo("reUploadIdCard", "0");
                layerUtils.iAlert(data.error_info, "", function () {
                    appUtils.pageBack();
                });
                return;
            }
            idInfo.front_url = data.results[0].url    //正面
            var paramFM = {
                "base_data": base64FM,
                "flag": "1",
            }
            service.reqFun199011(paramFM, function (data) {
                appUtils.clearSStorage("openLeavePage");
                if (data.error_no != "0") {
                    appUtils.setSStorageInfo("reUploadIdCard", "0");
                    layerUtils.iAlert(data.error_info, "", function () {
                        appUtils.pageBack();
                    });
                    return;
                }
                idInfo.back_url = data.results[0].url    //反面
                appUtils.setSStorageInfo("idInfo", idInfo);
                appUtils.setSStorageInfo("reUploadIdCard", "1");
                //数据正常跳转 用户信息页面
                sessionStorage.registerType = '1' //用户注册类型为 引流用户
                appUtils.pageInit(pageCode, "account/setUserInfo");
                // appUtils.pageBack();
                
            })
        }, {isLastReq: false})
    }

    /**
     * 查询职业信息
     * */
    function queryJob(info) {
        var param = {code: 'occupation_jz'}
        if ($(".mobileSelect").length > 0) {
            return;
        }
        service.reqFun199014(param, function (res) {
            if (res.error_no == "0") {
                var dataArr = res.results;
                var position;
                if (info) {
                    position = dataArr.findIndex(function (item) {
                        return info.id == item.id;
                    });
                    $(_pageId + " #occp").val(dataArr[position].value);
                    $(_pageId + " #occp").attr("data-value", dataArr[position].id);
                } else {
                    $(_pageId + " #occp").val();
                    $(_pageId + " #occp").attr("data-value", "");
                }

                tools.mobileSelect({
                    trigger: _pageId + " #occp",
                    title: "请选择职业",
                    dataArr: dataArr,
                    position: position,
                    callback: function (data) {
                        $(_pageId + " #occp").val(data[0].value);
                        $(_pageId + " #occp").attr("data-value", data[0].id);
                    }
                })
            } else {
                layerUtils.iAlert(res.error_info)
            }
        })
    }
    
    /**
     * 查询年收入信息
     * */
    function queryIncome(info) {
    	 dataArr_income = [
    		 {id: "01", value: "0~10（含）", money: "10"},
             {id: "02", value: "10~50（含）", money: "50"},
             {id: "03", value: "50~100（含）", money: "100"},
             {id: "04", value: "其他", money: ""}
    	 ]
        var position = 0;
        if (info) {
        	if(info.id == "04") {
        		$(_pageId + " #income").val(info.money);
        	} else {
        		$(_pageId + " #income").val(info.value);
        	}
        	$(_pageId + " #income").attr("data-id", info.id).attr("data-value", info.value).attr("data-money", info.money);
        } else {
            $(_pageId + " #income").val();
            // $(_pageId + " #income").attr("data-id", dataArr_income[position].id).attr("data-value", dataArr_income[position].value).attr("data-money", dataArr_income[position].money);
        }
        tools.mobileSelect({
            trigger: _pageId + " #income",
            title: "请选择年收入（万元）",
            dataArr: dataArr_income,
            callback: function (data) {
                $(_pageId + " #income").val(data[0].value);
                $(_pageId + " #income").attr("data-id", data[0].id);
                $(_pageId + " #income").attr("data-value", data[0].value);
                $(_pageId + " #income").attr("data-money", data[0].money);
                if (data[0].id == "04") {
                    $(_pageId + " .pop_layer1").show();
                    $(_pageId + " .password_box").show();
                    event.stopPropagation();
                    $(_pageId + " #srje").val('');
            		$(_pageId + " #inputspanid span").html('');
                    //键盘事件
                    moneyboardEvent();
                    var param = {};
                    param["moduleName"] = "mall";
                    param["funcNo"] = "50210";
                    param["pageId"] = "drainage_uploadIDCard";
                    param["eleId"] = "srje";
                    param["doneLable"] = "确定";
                    param["keyboardType"] = "3";
                    require("external").callMessage(param);
                    var srje = $(_pageId + " #srje").val();
                    $(_pageId + " #income").val(srje);
                    $(_pageId + " #income").attr("data-id", "04").attr("data-money", srje).attr("data-value", dataArr_income[3].value);
                    $(_pageId + " #inputspanid span").html(srje);
                }
            }
        });

    }

    //金额输入数字键盘
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #srje"),
            endcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                } else if(moneys<=0){
                	layerUtils.iAlert("请输入大于零的金额");
                }else {
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .password_box").hide();
                }
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}$/.test(curVal))) {
                    moneys = moneys.substring(0, curVal.length - 1)
                }
                $(_pageId + " #income").val(Number(moneys));
                $(_pageId + " #income").attr("data-id", "04").attr("data-value", dataArr_income[3].value).attr("data-money", moneys);
                $(_pageId + " #inputspanid span").html(Number(moneys));
                $(_pageId + " #srje").val(Number(moneys));
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #srje").val();
                var moneys = curVal.replace(/,/g, "");
                if (!moneys) {
                    layerUtils.iAlert("请输入金额");
                } else if(moneys<=0){
                	layerUtils.iAlert("请输入大于零的金额");
                }else {
                    $(_pageId + " .pop_layer1").hide();
                    $(_pageId + " .password_box").hide();
                }
            }
        })
    }

    
    /**
     * 居住地址选择
     */
    function selectorArea(info) {
//        var nameEl = document.getElementById(id);

    	 if (info) {
             $(_pageId + " #live_address").val(info.name);
             $(_pageId + " #live_address").attr("data-code", info.code);
         } 
        function creatList(obj, list) {
            obj.forEach(function (item, index, arr) {
                var temp = new Object();
                temp.text = item.name;
                temp.code = item.code;
                temp.value = index;
                list.push(temp);
            })
        }

        creatList(city, first);

        if (city[selectedIndex[0]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub, second);
        } else {
            second = [{text: '', value: 0}];
        }

        if (city[selectedIndex[0]].sub[selectedIndex[1]].hasOwnProperty('sub')) {
            creatList(city[selectedIndex[0]].sub[selectedIndex[1]].sub, third);
        } else {
            third = [{text: '', value: 0}];
        }
        checked = selectedIndex
        picker = new Picker({
            data: [first, second, third],
            selectedIndex: selectedIndex,
            title: '地址选择'
        });

        picker.on('picker.select', async function (selectedVal, selectedIndex) {
            var text1 = first[selectedIndex[0]].text;
            var text2 = second[selectedIndex[1]].text;
            var text3 = third[selectedIndex[2]] ? third[selectedIndex[2]].text : '';
            value = text1 + ' ' + text2 + ' ' + text3;
            // $(_pageId + " #live_address").val(value);
            var code1 = first[selectedIndex[0]].code;
            var code2 = second[selectedIndex[1]].code;
            var code3 = third[selectedIndex[2]] ? third[selectedIndex[2]].code : '';
            var code = code1 + ' ' + code2 + ' ' + code3;
            $(_pageId + " #live_address").attr("data-code", code);
            let flag = await tools.is_region(code1,code2,code3,city)
            if(flag){
                $(_pageId + " #live_address").val(value);
            }else{
                layerUtils.iMsg(-1, "所选地址不匹配，请重新选择");
                $(_pageId + " #live_address").val('');
            }
        });

        picker.on('picker.change', function (index, selectedIndex) {
            if (index === 0) {
                firstChange();
            } else if (index === 1) {
                secondChange();
            }

            function firstChange() {
                second = [];
                third = [];
                checked[0] = selectedIndex;
                var firstCity = city[selectedIndex];
                if (firstCity && firstCity.hasOwnProperty('sub')) {
                    creatList(firstCity.sub, second);

                    var secondCity = city[selectedIndex].sub[0]
                    if (secondCity.hasOwnProperty('sub')) {
                        creatList(secondCity.sub, third);
                    } else {
                        third = [{text: '', value: 0}];
                        checked[2] = 0;
                    }
                } else {
                    second = [{text: '', value: 0}];
                    third = [{text: '', value: 0}];
                    checked[1] = 0;
                    checked[2] = 0;
                }

                picker.refillColumn(1, second);
                picker.refillColumn(2, third);
                picker.scrollColumn(1, 0)
                picker.scrollColumn(2, 0)
            }

            function secondChange() {
                third = [];
                checked[1] = selectedIndex;
                var first_index = checked[0];
                if (city[first_index].sub[selectedIndex].hasOwnProperty('sub')) {
                    var secondCity = city[first_index].sub[selectedIndex];
                    creatList(secondCity.sub, third);
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                } else {
                    third = [{text: '', value: 0}];
                    checked[2] = 0;
                    picker.refillColumn(2, third);
                    picker.scrollColumn(2, 0)
                }
            }

        });

        picker.on('picker.valuechange', function (selectedVal, selectedIndex) {
        });
    }


    function destroy() {
        $(_pageId + " #next").css({backgroundColor: "#E5433B"});
        saveImgNum = 0;
        $(_pageId + " .camera_pop_layer").remove();
        $(_pageId + " input").val("");
        $(_pageId + " #idCard").attr("disabled");
        $(_pageId + " #realName").attr("disabled");
        $(_pageId + " #cust_address").attr("disabled");
        $(".mobileSelect").remove();
        $(_pageId + " .rule_check #xuanzeqi i").removeClass("active");
        $(_pageId + " #srje").val("");
        $(".picker").remove();
        first = [];
        second = [];
        third = [];
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var setBankCard = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = setBankCard;
});
