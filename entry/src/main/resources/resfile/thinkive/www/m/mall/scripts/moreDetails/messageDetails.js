// 公司公告
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        gconfig = require("gconfig"),
        _pageId = "#moreDetails_messageDetails ";
    var global = gconfig.global;
    var tools = require("../common/tools");
    var page = 1;

    function init() {
        let res = JSON.parse(sessionStorage.message_pro)
        // isRead(res.mail_id) //将消息设置为已读
        // console.log(res,111)
        if (res.mail_show_type == "1") {
            if (res.mail_link_type == "2") {
                $(_pageId + ' .moreDetails_messageDetails .title').html(res.mail_title)
                $(_pageId + ' .moreDetails_messageDetails .time').html(sessionStorage.create_date ? tools.ftime(sessionStorage.create_date):'')
                let url = res.mail_url
                $(_pageId + ' .img_details .showDetails').attr("src", global.oss_url + url)
                $(_pageId + ' .showDetails').show()
                $(_pageId + ' .message_details').show()
            }
        } else {
            $(_pageId + ' .moreDetails_messageDetails .title').html(res.mail_title)
            $(_pageId + ' .moreDetails_messageDetails .time').html(sessionStorage.create_date ?tools.ftime(sessionStorage.create_date):'')
            getDetails(res)

        }
    }
    //获取详情
    function getDetails(data) {
        let res = {
            mail_send_type: data.mail_send_type,
            mail_id: data.mail_id,
            hash_key: data.hash_key
        }
        service.reqFun105004(res, function (data) {
            let error_no = data.error_no
            let error_info = data.error_info
            if (error_no == '0') {
                let res = data.results[0]
                $(_pageId + ' .message_details').html(res.mail_content)
                $(_pageId + ' .message_details').show()
                $('#message_details a').remove();
            } else {
                layerUtils.iAlert(error_info);
            }
        })
    }
    //绑定事件
    function bindPageEvent() {

        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击链接
        // 点击进入消息详情页面
        appUtils.preBindEvent($(_pageId + " .message_details"), ".chain", function () {
            let url;
            let cust_fund_type;
            if ($(this).attr('src') && $(this).attr('src') != '') {
                url = $(this).attr('src')
            } else {
                url = $(this).text()
            }
            cust_fund_type = $(this).attr('cust_fund_type') ? $(this).attr('cust_fund_type') : 0;
            if (!url || url == '') return
            let newStr = url.indexOf("http");
            let newStrs = url.indexOf("https");
            // console.log(newStr,newStrs)
            if (newStr == 0 || newStrs == 0) {
                appUtils.pageInit("moreDetails/messageDetails", "guide/advertisement", {
                    "url": url,
                    "name": '',
                });
            } else {
                appUtils.pageInit("moreDetails/messageDetails", url, { cust_fund_type: cust_fund_type });
            }
        }, 'click');
        // 小程序
        appUtils.preBindEvent($(_pageId + " .message_details"), ".applet", function () {
            let url
            if ($(this).attr('src') && $(this).attr('src') != '') {
                url = $(this).attr('src')
            } else {
                url = $(this).text()
            }
            if (!url || url == '') return
            return tools.jump_applet(url);
        }, 'click');
    }
    //已读
    function isRead(mail_id) {
        service.reqFun105002({ hash_key: mail_id }, function (data) {
            let error_no = data.error_no
            let error_info = data.error_info
            var str = "";
            if (error_no == "0") {

            } else {
                layerUtils.iAlert(error_info);

            }
        });
    }
    function destroy() {
        $(_pageId + ' .message_none').hide();
        $(_pageId + ' .message_details').hide();
        $(_pageId + ' .message_details').html('');
        $(_pageId + ' .showDetails').attr("src", "");
        $(_pageId + ' .showDetails').hide();
        $(_pageId + ' .moreDetails_messageDetails .title').html('')
        $(_pageId + ' .moreDetails_messageDetails .time').html('')
        // sessionStorage.create_date = ''
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var notice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = notice;
});
