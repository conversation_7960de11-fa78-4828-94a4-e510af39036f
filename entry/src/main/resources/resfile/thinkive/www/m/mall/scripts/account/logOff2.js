// 注销账户-身份证信息验证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#account_logOff2 ";
    var ut = require("../common/userUtil");
    var sms_mobile = require("../common/sms_mobile");
    var tools = require("../common/tools");
    var keyflag = 0;
    var userInfo;

    function init() {    	
    	//$(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        sms_mobile.init(_pageId);
        userInfo = ut.getUserInf();
    }

    //绑定事件
    function bindPageEvent() {
        //点击开启键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + ' #inputspanid span').text('').addClass('inputspan');
            $(_pageId + ' #tradeNum').val('');
            kaiqi("tradeNum");
        });
        //关闭密码键盘
        appUtils.bindEvent($(_pageId), function () {
            if (keyflag == 1) {
                $(_pageId + ' .bg_blue').css('margin-top', 0);
                guanbi();
            }
        });
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            tools.openCamera("zm");
            // var external = require("external");
            // var Param = {
            //     "funcNo": "60302",
            //     "moduleName": "mall"
            // };
            // external.callMessage(Param);
        });
        // 银行预留手机号码输入控制
        appUtils.bindEvent($(_pageId + " #bankLeavePhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 银行卡号失去焦点
        appUtils.bindEvent($(_pageId + " #cardNo"), function () {
            var cardNo = $(_pageId + " #cardNo").val();
            if (cardNo == '') {
                layerUtils.iMsg(-1, "银行卡号不能为空!");
                return;
            }
            if (!validatorUtil.isBankCode(cardNo)) {
                layerUtils.iMsg(-1, "无效卡号请重新输入");
                return;
            }
        }, "blur");
        appUtils.bindEvent($(_pageId + " #bankLeavePhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 验证码输入控制
        appUtils.bindEvent($(_pageId + " #yzm"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var bankLeavePhone = $(_pageId + " #bankLeavePhone").val(); //
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            } 
            if(!nameYZ()) {
            	return;
            }
            // 获取验证码
            var param = {
                mobile_phone: bankLeavePhone,
                type: common.sms_type.loginOff,
                send_type: "0"
            };
            sms_mobile.sendPhoneCode(param); 
        });
        //提交
        appUtils.bindEvent($(_pageId + " #xyb"), function () {
        	if(!nameYZ()) {
            	return;
            }
            if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                layerUtils.iMsg(-1, "请先获取验证码");
                return
            }
            var sms_code = $(_pageId + " #yzm").val(); //验证码
            if (sms_code == '') {
                layerUtils.iMsg(-1, "验证码不能为空");
                return;
            }
            if (sms_code.length != 6) {
                layerUtils.iMsg(-1, "验证码错误");
                return;
            }
            //校验密码
            submit();
            
        });

    }

    function passwordkey() {
        keyflag = 1;
        $(_pageId + ' #inputspanid span').css('color', '#000000');
        $(_pageId + ' .bg_blue').css('margin-top', '-100px');
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                $(_pageId + ' .bg_blue').css('margin-top', 0);
                keyflag = 0;
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #tradeNum").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                }
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #tradeNum").val(shuru);
                }
                $(_pageId + ' #inputspanid span').text(passflag);
            } // 键盘的输入事件
        };
    }

    //验证身份证号，姓名
    function nameYZ() {
        var name = $(_pageId + " #realName").val(); //真实姓名
        var idCard = $(_pageId + " #idCard").val().toUpperCase(); //身份证
        var cardNo = $(_pageId + " #cardNo").val(); //银行卡号
        var bankLeavePhone = $(_pageId + " #bankLeavePhone").val(); //新预留手机号
        
        if (name == '') {
            layerUtils.iMsg(-1, "姓名不能为空!");
            return false;
        }
        if (name != userInfo.name) {
            layerUtils.iMsg(-1, "姓名输入有误!");
            return false;
        }
        if (idCard == '') {
            layerUtils.iMsg(-1, "身份证不能为空!");
            return false;
        }
        if (!validatorUtil.isCardID(idCard)) {
            layerUtils.iMsg(-1, "证件号码格式错误");
            return false;
        }
       
        //校验银行卡号
        if (cardNo == '') {
            layerUtils.iMsg(-1, "银行卡号不能为空!");
            return false;
        }
        if (!validatorUtil.isBankCode(cardNo)) {
            layerUtils.iMsg(-1, "无效卡号请重新输入");
            return false;
        }
          
        if (bankLeavePhone == '') {
            layerUtils.iMsg(-1, "预留手机号不能为空");
            return false;
        }
        if (!validatorUtil.isMobile(bankLeavePhone)) {
            layerUtils.iMsg(-1, "请输入正确的预留手机号码");
            return false;
        }
        return true
    }

    //验证银行卡号
    function reqFun1100001(parm, callback) {
        service.reqFun1100001(parm, function (data) { //校验身份证
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                if (parm.type == 'bank_leave_phone') {
                    layerUtils.iMsg(-1, '预留手机号错误');
                    return;
                }
                layerUtils.iMsg(-1, data.error_info);
                return;
            }
            if (callback) callback();
        }, {isLastReq: false});
    }

    //注销申请
    function submit() {
    	
        //密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            var modulus = data.results[0].modulus;
            var publicExponent = data.results[0].publicExponent;
            var endecryptUtils = require("endecryptUtils");
            var trans_pwd = $(_pageId + " #tradeNum").val(); 
            trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, trans_pwd);
            
            var name = $(_pageId + " #realName").val(); //真实姓名
            var cert_no = $(_pageId + " #idCard").val(); //身份证号
            var bankLeavePhone = $(_pageId + " #bankLeavePhone").val(); //预留手机号            
            var cardNo = $(_pageId + " #cardNo").val(); //银行卡号
            var sms_code = $(_pageId + " #yzm").val(); //验证码
            var parm = {
            		bank_reserved_mobile: bankLeavePhone, //预留手机号
                    bank_acct: cardNo, //银行卡号
                    cust_name: name, //客户姓名
                    cert_no: cert_no,
                    sms_code: sms_code,
                    trans_pwd: trans_pwd,	
                    sms_mobile: bankLeavePhone,
               
            }
                       
            service.reqFun101045(parm, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                appUtils.pageInit("account/logOff2", "account/logOff3");
            });
        }, {isLastReq: false})
    }

    //关闭键盘
    function guanbi() {
        $(_pageId + ' #inputspanid span').removeClass('inputspan');
        if ($(_pageId + ' #inputspanid span').text() == '') {
            $(_pageId + ' #inputspanid span').text($(_pageId + ' #inputspanid span').attr('text')).css('color', '#999999');
        }
        keyflag = 0;
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);

    }

    function kaiqi(jjb_pwd) {
        passwordkey();
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "account_logOff2";
        param["eleId"] = jjb_pwd;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param)
    }


    function destroy() {
        guanbi();
        $(_pageId + " input").val('');
        $(_pageId + " #jiatradeNum").text("请输入交易密码");
        $(_pageId + " #tradeNum").val("");
        $(_pageId + ' #inputspanid span').text($(_pageId + ' #inputspanid span').attr('text')).css('color', '#999999');
        $(_pageId + ' #weihao').hide();
        $(_pageId + " #talkCode").hide();
        $(_pageId + " article").css({"margin-top": 0});
        sms_mobile.destroy();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var accountLogOff2 = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
// 暴露对外的接口
    module.exports = accountLogOff2;
})
;
