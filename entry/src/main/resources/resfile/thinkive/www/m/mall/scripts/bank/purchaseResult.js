//购买结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageUrl = "bank/purchaseResult",
        _pageId = "#bank_purchaseResult ";
    var num;
    var outputInfo;
    var tools = require("../common/tools");
    var t;

    function init() {
        num = 5;
        outputInfo = appUtils.getPageParam();
        countDown();
        if (outputInfo.is_transfer == "1") {
            $(_pageId + " .dividend").hide();
        } else {
            $(_pageId + " .dividend").show();
        }
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + "#goHome"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount"]);
            appUtils.pageInit(_pageUrl, "bank/bankDeposit");
        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount", "bank/bankDeposit"]);
            appUtils.pageInit(_pageUrl, "bank/transaction");
        })
    }

    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun151104(outputInfo, function (data) {
                    $(_pageId + ".load").hide();
                    if (data.error_no == "0") {
                        var restuls = data.results[0];
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败
                        if (restuls.trans_status == "3") {
                            $(_pageId + " #cry_int_date").text(tools.ftime(restuls.cry_int_date));
                            $(_pageId + " #next_cyc_date").text(tools.ftime(restuls.next_cyc_date));
                            $(_pageId + " .success").show();//购买成功
                        } else if (restuls.trans_status == "4" || restuls.trans_status == "1") {
                            if (restuls.host_msg) {
                                $(_pageId + ' #failinf').text('失败原因：' + restuls.host_msg);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，购买失败');
                            }
                            $(_pageId + " .fail").show();//购买失败
                        } else {
                            $(_pageId + " .wait").show();//购买无结果
                        }
                    } else {
                        $(_pageId + " .wait").show();//购买无结果
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info)
                    }
                })
            }

        }, 1000)
    }

    function destroy() {
        $(_pageId + " .pay_done").hide();
        $(_pageId + ".load").show();
        window.clearInterval(t);
        $(_pageId + " .dividend").hide();
    }

    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
