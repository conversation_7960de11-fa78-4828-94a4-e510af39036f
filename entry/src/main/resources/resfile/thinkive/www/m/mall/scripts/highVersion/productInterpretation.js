// 产品解读
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        gconfig = require("gconfig"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        service = require("mobileService"),
        _pageId = "#highVersion_productInterpretation ",
        _page_code = "highVersion/productInterpretation";
    var ut = require("../common/userUtil");
    var essay_id;//文章id
    var catalog_id;
    var choose_catalog_id;//当前高亮分类
    var global = gconfig.global;
    var second_catalog_id,article_id;
    var userAuthenticationStatus;
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    VIscroll = require("vIscroll");
    vIscroll = {"scroll": null, "_init": false};
    var isEnd = false;
    var cur_page;
    async function init() {
        if(ut.getUserInf()){
            var userStatus = await getUserAuthenticationStatus();
            userAuthenticationStatus = userStatus[0].state //获取用户认证状态
        }
        choose_catalog_id = '';
        cur_page = 1;
        //获取页面名称
        let productInterpretationName = appUtils.getSStorageInfo("productInterpretationName");
        $(_pageId + " .pageTitle").text(productInterpretationName);
        //获取上级页面带的 catalog_id
        catalog_id = appUtils.getSStorageInfo("catalog_id");

        //获取二级页面返回带的 catalog_id
        second_catalog_id = second_catalog_id = appUtils.getSStorageInfo("second_catalog_id");
        //页面埋点初始化
        tools.initPagePointData();
        //获取文章分类
        getProductInterpretationCategory();
    }
    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    function getProductInterpretationCategory(){
        service.reqFun181007({catalog_id:catalog_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let typeList = datas.results;
            let typeHtml = ``;
            let defaultHtml = `<ul id="${catalog_id}">全部</ul>`;
            typeList.forEach((item,index) => {
                item.catalog_id = item.id;
                typeHtml += `
                    <ul id="${item.id}" operationType="1" operationId="chosseCatalog" operationName="选择栏目"  contentType="15" >
                        <em style="display:none">${JSON.stringify(item)}</em>          
                        ${item.catalog_name}
                    </ul>
                `
            });
            // console.log(catalog_id,111)
            catalog_id = (typeList && typeList.length) ? typeList[0].id : catalog_id;
            $(_pageId + " .list-title").html(typeHtml);
            //高亮选中的分类
            $(_pageId + " .list-title ul").each((index,item) => {
                if($(item).attr("id") == (second_catalog_id ? second_catalog_id : catalog_id)){
                    //高亮分类赋值
                    choose_catalog_id = second_catalog_id ? second_catalog_id : catalog_id;
                    $(item).addClass("active");
                }
            })
            
            choose_catalog_id = choose_catalog_id ? choose_catalog_id : catalog_id;
            getList(second_catalog_id ? second_catalog_id : catalog_id,false);
        })
        // service.reqFun181008({}, (datas) => {
        //     if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
        //     console.log(datas,222)
        // })
    }
    function getList(catalog_id,flag){
        service.reqFun181008({catalog_id:catalog_id,current:cur_page + ''}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            var totalPages = datas.results[0].totalPages; //总页数
            let list = datas.results[0].data;
            // list = []
            let html = ``;
            // if(!list || !list.length) return{

            // } $(_pageId + " .productInterpretation .list").html(`<p style="color:#fff;padding:0.2rem 0;text-align: center;">暂无数据</p>`);
            if(!list || !list.length) list = [];
            list.forEach((item, index) => {
                item.essay_id = item.article_id;
                html += `
                    <ul qualified_investor_visible="${item.qualified_investor_visible}" class="list-card flex vertical_center" operationType="1" operationId="articleDetails" operationName="文章详情"  contentType="14"  essay_id="${item.article_id}" catalog_id="${item.catalog_id}">
                        <em style="display:none">${JSON.stringify(item)}</em>        
                        <li>${item.title}</li>
                        <li style="border-radius: 0.05rem;">
                            <img class="${item.img_url ? '' : 'display_none'}" style="width:0.94rem;height:0.64rem;border-radius: 0.05rem;" src="${global.oss_url + item.img_url}" alt="">
                        </li>
                    </ul>
                `
            })
            if (totalPages == cur_page) {
                isEnd = true;
                html += '<div class="nodata">没有更多数据</div>'
            }
            if (totalPages == 0 && list.length == 0) {
                isEnd = true;
                html = '<div class="nodata">暂无数据</div>'
            }
            $(_pageId + " #v_container_productList").show();
            if (flag) {
                $(_pageId + " .list").append(html);
            } else {
                $(_pageId + " .list").html(html);
            }
            // $(_pageId + " .visc_pullUp").hide();
            $(_pageId + " .visc_pullUpIcon").hide();
            $(_pageId + " .visc_pullUpDiv").hide();
            pageScrollInit();
            // $(_pageId + " .productInterpretation .list").html(html);
        })
        // $(_pageId + " .productInterpretation .list")
    }
    
    //绑定事件
    function bindPageEvent() {
        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    $(_pageId + " .qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    appUtils.pageInit(_page_code, "highVersion/articleDetails",{essay_id:essay_id,catalog_id: catalog_id});
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            $(_pageId + " .qualifiedInvestor").hide();
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " .list-title"), "ul", function (e) {
            e.stopPropagation();
            e.preventDefault();
            second_catalog_id = '';
            appUtils.setSStorageInfo("second_catalog_id",'');
            cur_page = 1;
            isEnd = false;
            if($(this).attr("id") == choose_catalog_id) return;
            $(_pageId + " .list-title ul").removeClass("active");
            $(this).addClass("active");
            let catalog_id = $(this).attr("id");
            choose_catalog_id = catalog_id;
            getList(catalog_id,false)
        }, 'click');
        //跳转文章详情
        appUtils.preBindEvent($(_pageId + " .list"), "ul", function (e) {
            catalog_id = $(this).attr("catalog_id");
            essay_id = $(this).attr("essay_id");
            let qualified_investor_visible = $(this).attr("qualified_investor_visible");//是否需要验证合格投资者
            if(qualified_investor_visible == '1'){
                if (!common.loginInter(_page_code)) return;            
            } 
            if(userAuthenticationStatus == '0' && qualified_investor_visible == '1'){
                if (!ut.hasBindCard(_page_code)) return;
                return $(_pageId + " .qualifiedInvestor").show();
            }
            appUtils.setSStorageInfo("second_catalog_id",choose_catalog_id);
            //tools.recordEventData('1','articleDetails','文章详情',{essayId:essay_id});
            appUtils.pageInit(_page_code, "highVersion/articleDetails",{essay_id: essay_id,catalog_id: catalog_id});
        }, 'click');
    }
    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    endTime = "";
                    isEnd = false;
                    appUtils.setSStorageInfo("second_catalog_id",'')
                    second_catalog_id = '';
                    getList(second_catalog_id ? second_catalog_id : choose_catalog_id,false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getList(second_catalog_id ? second_catalog_id : choose_catalog_id,true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }

    function destroy() {
        $(_pageId + " .qualifiedInvestor").hide();
        article_id = '';
        userAuthenticationStatus = '';
        tools.recordEventData('4','destroy','页面销毁');
        isEnd = false;
        $(_pageId + " .list").html("");
        cur_page = 1;
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.setSStorageInfo("second_catalog_id",'');
        second_catalog_id = '';
        appUtils.pageBack();
    }

    var highVersion_productInterpretation = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_productInterpretation;
});
