//货基 - 购买结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageUrl = "inclusive/moneytaryPurchaseResult",
        _pageId = "#inclusive_moneytaryPurchaseResult ";
    var tools = require("../common/tools");//升级
    var outputInfo;
    var num;
    var _fund_code = "";

    function init() {
        //获取交易时间
        reqFun102008();
        tools.getActivityInfo(_pageId,_pageUrl)
        _fund_code = appUtils.getSStorageInfo("fund_code");
        num = 5;
        outputInfo = appUtils.getPageParam();
        countDown();
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + ".done_btn"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount"]);
            appUtils.pageInit(_pageUrl, "template/positionList");
        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount", "template/positionList"]);
            appUtils.setSStorageInfo("series_id",'');
            appUtils.pageInit(_pageUrl, "template/transaction");
        })

    }

    //获取交易时间
    function reqFun102008() {
        var param = {
            type: "6"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                //空数据处理
                results = tools.FormatNull(results);
                var qrDate = results.qrDate;
                if (qrDate != "--") {
                    qrDate = tools.FormatDateText(qrDate.substr(4));
                }

                var syDate = results.syDate;
                if (syDate != "--") {
                    syDate = tools.FormatDateText(syDate.substr(4));
                }

                //确认日期
                $(_pageId + " #ksqrDate").html(qrDate);
                //收益日期
                $(_pageId + " #ptqrDate").html(syDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun102030(outputInfo, function (data) {
                    if (data.error_no == "0") {
                        $(_pageId + ".load").hide();
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败 8 确认成功 9 确认失败
                        if (data.results[0].trans_status == "8") {
                            $(_pageId + " .success").show();//购买成功
                        } else if (data.results[0].trans_status == "9" || data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            if (data.results[0].host_desc) {
                                $(_pageId + ' #failinf').text('失败原因：' + data.results[0].host_desc);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，购买失败');
                            }
                            $(_pageId + " .fail").show();//购买失败
                        } else {
                            $(_pageId + " .wait").show();//购买无结果
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                    }
                })
            }

        }, 1000)
    }


    function destroy() {
        clearInterval(t);
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').show();
    }


    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
