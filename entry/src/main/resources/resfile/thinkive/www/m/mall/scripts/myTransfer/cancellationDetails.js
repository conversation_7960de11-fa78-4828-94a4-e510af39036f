// 撤单
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        serviceConstants = require("constants"),
        monkeywords = require("mall/scripts/common/moneykeywords"),
        ut = require("../common/userUtil");
    var _pageId = "#myTransfer_cancellationDetails";
    var _pageCode = "myTransfer/cancellationDetails";
    let cancelOrderInfo;
    let _cust_fund_type; //判断用户来源 0转让人 2受让人
    function init() {
        cancelOrderInfo = appUtils.getSStorageInfo('cancelOrderInfo');
        _cust_fund_type = appUtils.getSStorageInfo('_cust_fund_type');
        //页面埋点初始化
        tools.initPagePointData();
        if(cancelOrderInfo.same_prod_transfer_all == '1' && _cust_fund_type == '2'){
            $(_pageId + " .myTransfer_cancellationDetails_title").text("购买");
            //小集合多期转让
            getTransferMoney(cancelOrderInfo.details);
            $(_pageId + " .thfundBtn").show();
            // $(_pageId + " .give_profit").show();
            $(_pageId + " .step").show();
            $(_pageId + " .transfer_type_new").show();
            $(_pageId + " .remarkTitle").text('转让信息');
            if(cancelOrderInfo.buy_entrust_status == '0'){
                //未汇款
            }
        }else{
            $(_pageId + " .remarkTitle").text('挂单信息');
            if(cancelOrderInfo.same_prod_transfer_all == '1'){  //是小集合转让
                // $(_pageId + " .give_profit").hide();
                // $(_pageId + " .ungive_profit").hide();
                $(_pageId + " .rate").hide();
                // $(_pageId + " .due_date").hide();
                $(_pageId + " .last_day").hide();
                getTransferMoney(cancelOrderInfo.details);
            }else{ //非小集合转让
                $(_pageId + " .rate").show();
                $(_pageId + " .due_date").hide();
                $(_pageId + " .last_day").show();
                $(_pageId + " .give_profit").show();
                $(_pageId + " .ungive_profit").show();
            }
            $(_pageId + " .thfundBtn").hide();
            $(_pageId + " .myTransfer_cancellationDetails_title").text("撤单");
            $(_pageId + " .step").hide();
            $(_pageId + " .transfer_type_old").show();
        }
        if(cancelOrderInfo.entrust_status != '1') $(".myTransferFooter").hide()
        //渲染数据
        renderInfo()
    }
    //渲染单期，多期数据
    function getTransferMoney(list){
        let listData = list;
        if(listData && listData.length > 1){
            //存在多期
            $(_pageId + " .give_profit").hide();
            $(_pageId + " .ungive_profit").hide();
            $(_pageId + " .see_details").show();
            $(_pageId + " .due_date").hide();
            $(_pageId + " .rate").hide();
            $(_pageId + " .last_day").hide();
            setListData(listData)
        }else{
            //新
            $(_pageId + " .give_profit").show();
            if(_cust_fund_type == '0') {
                $(_pageId + " .ungive_profit").show();
            }else{
                $(_pageId + " .ungive_profit").hide();
            }
            $(_pageId + " #due_date").text(tools.ftime(listData[0].due_date));
            $(_pageId + " .due_date").show();
            $(_pageId + " .rate").show();
            $(_pageId + " .last_day").show();
            //只有一期
            $(_pageId + " .see_details").hide();
        }
    }
    //渲染卡片列表信息
    function setListData(list){
        let html = '<ul class="flex"><li>本金</li><li>到期日</li><li>剩余期限</li><li>业绩计提基准</li></ul>';
        list.map(item=>{
            html += `
            <ul class="flex">
                    <li>${tools.fmoney(item.costmoney)}元</li>
                    <li>${tools.ftime(item.due_date)}</li>
                    <li>${item.surpterm}天</li>
                    <li class="m_text_red">${tools.fmoney(item.rate)}%</li>
            </ul>
            `
        })
        $(_pageId + " .card").html(html);
        $(_pageId + " .card").show();
    }
    function bindPageEvent() {
        //弹出明细列表
        appUtils.bindEvent($(_pageId + " .see_details"),function () {
            //判断卡片是否展示
            let showCard = $(_pageId + " .card").is((':visible'))
            if(!showCard){
                $(_pageId + " .see_details .bottom").hide();
                $(_pageId + " .see_details .top").show();
                $(_pageId + " .card").show();
            }else{
                $(_pageId + " .see_details .top").hide();
                $(_pageId + " .see_details .bottom").show();
                $(_pageId + " .card").hide();
            }
        })
        //弹出转让规则
        appUtils.bindEvent($(_pageId + " #transferRule"),function () {
            $(_pageId + " .rule_dio").show();
            $(_pageId + " .card1").show();
        })
        //关闭转让规则弹窗
        appUtils.bindEvent($(_pageId + " .card_footer"),function () {
            $(_pageId + " .rule_dio").hide();
            $(_pageId + " .card1").hide();
        })
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .myTransferFooter"),function(){
            let operationId = 'cancelorder';
            layerUtils.iConfirm("您确定要进行撤单吗？",
            function () {
            }, function () {
                revokeOrder()
            }, "取消", "确定",operationId);
        })
        //受让人取消购买
        appUtils.bindEvent($(_pageId + " .cancelPurchase"),function(){
            let operationId = 'closeBuy';
            layerUtils.iConfirm("您确定要取消购买吗？",
            function () {
            }, function () {
                cancelPurchase()
            }, "取消", "确定",operationId);
        })
        //受让人跳转转账汇款页面
        appUtils.bindEvent($(_pageId + " .goMoney"),function(){
            appUtils.setSStorageInfo("buyTransferData",cancelOrderInfo); //拿到产品基本信息
            appUtils.pageInit(_pageCode, "myTransfer/uploadCredentials");
        })
        //跳转产品详情
        appUtils.bindEvent($(_pageId + " .myTransferFund_entry"), function () {
            // createdData = appUtils.setSStorageInfo("cancelOrderInfo"); //产品信息
            appUtils.setSStorageInfo("productInfo", cancelOrderInfo);
            tools.jumpPriDetailPage(_pageCode, '100');
        });
    }
    //小集合 受让人取消购买
    function cancelPurchase(){
        let param = cancelOrderInfo;
        service.reqFun107017({trans_serno:param.trans_serno},  (datas)=> {
            if (datas.error_no == 0) {
                //撤单成功
                appUtils.pageInit(_pageCode, "highEnd/cancelOrderResult", {});
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    function revokeOrder(){
        let param = cancelOrderInfo;
        if(cancelOrderInfo.same_prod_transfer_all == '1'){
            return cancelOrders(param.entrust_no);  //小集合撤单
        }
        service.reqFun107004({entrust_no:param.entrust_no},  (datas)=> {
            if (datas.error_no == 0) {
                // let results = datas.results;
                //撤单成功
                appUtils.pageInit(_pageCode, "highEnd/cancelOrderResult", {});
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    //小集合撤单
    function cancelOrders(entrust_no){
        service.reqFun107015({entrust_no:entrust_no},  (datas)=> {
            if (datas.error_no == 0) {
                // let results = datas.results;
                //撤单成功
                appUtils.pageInit(_pageCode, "highEnd/cancelOrderResult", {});
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    function renderInfo(){
        //+ tools.transformFundType(cancelOrderInfo.prod_type2)
        let fund_code = '(' + cancelOrderInfo.fund_code + ')' 
        let rate = tools.fmoney(cancelOrderInfo.rate) + '%' //年化标准
        let entrust_date = tools.ftime(cancelOrderInfo.entrust_date);
        $(_pageId + " #fund_sname").text(cancelOrderInfo.prod_sname);
        $(_pageId + " #fund_code").text(fund_code);
        $(_pageId + " #rate").text(rate);
        $(_pageId + " #last_day").text(cancelOrderInfo.last_day  + '天');
        $(_pageId + " #transfer_vol").text(tools.fmoney(cancelOrderInfo.transfer_vol) + '元');
        $(_pageId + " #costmoney").text(tools.fmoney(cancelOrderInfo.costmoney) + '元');
        $(_pageId + " #give_profit").text(tools.fmoney(cancelOrderInfo.give_profit) + '元');
        $(_pageId + " #ungive_profit").text(tools.fmoney(cancelOrderInfo.ungive_profit) + '元');
        $(_pageId + " #transfer_amt").text(tools.fmoney(cancelOrderInfo.transfer_amt) + '元');
        $(_pageId + " #service_charge").text(tools.fmoney(cancelOrderInfo.service_charge) + '元');
        $(_pageId + " #total_amt").text(tools.fmoney(cancelOrderInfo.total_amt) + '元');
        $(_pageId + " #entrust_date").text(entrust_date);
        

    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .transfer_type_new").hide();
        $(".myTransferFooter").show();
        $(_pageId + " .rule_dio").hide();
        $(_pageId + " .card1").hide();
        $(_pageId + " .thfundBtn").hide();
        $(_pageId + " .card").hide();
        $(_pageId + " .rate").hide();
        $(_pageId + " .due_date").hide();
        $(_pageId + " .last_day").hide();
        $(_pageId + " .transfer_type_old").hide();
        $(_pageId + " .remarkTitle").text('挂单信息');
        $(_pageId + " .give_profit").hide();
        $(_pageId + " .ungive_profit").hide();
        $(_pageId + " .see_details").hide();
    }
    
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});
