/* 晋金财富欢迎您  活动 */
#activity article {
    background: url(../images/activity/activity_bg.png) no-repeat;
    background-position: top;
    background-size: 100% auto;
    padding: 0 0.1rem;
}

#activity .header {
    width: 100%;
}

#activity .header img {
    width: 100%;
}

#activity .title {
    padding: 0.2rem;
    text-align: center;
}

#activity .title img {
    width: 40%;
    text-align: center;
}

#activity .title2 {
    text-align: center;
    color: #e5443c;
    border: 2px dashed #e5443c;
    margin: 0.1rem 0.2rem;
    border-radius: 1rem;
    padding: 0.02rem 0.1rem;
}

#activity .title2 > div {
    border: 2px solid #e5443c;
    border-radius: 1rem;
    font-weight: bold;
}

#activity .bankList {
    padding: 0 0 0.2rem;
}

#activity .bankList .item {
    padding: 0.2rem 0 0 0.2rem;
    position: relative;
    overflow: hidden;
}

#activity .bankList .item:last-child .line2 {
    display: none;
}

#activity .prod_name span:nth-of-type(1) {
    font-size: 18px;
    font-weight: bold;
    color: #000000;
    float: left;
}

#activity .prod_name span:nth-of-type(2) {
    float: right;
    background: #8F5D2A;
    color: #ffffff;
    padding: 0.01rem 0.2rem;
}

#activity .rate span:nth-of-type(1) {
    color: red;
    font-size: 30px;
    margin-right: 0.05rem;
}

#activity .rate span:nth-of-type(2) {
    color: #8F5D2A;
}

#activity .sub_info {
    padding-right: 0.2rem;
    display: flex;
    flex: 1;
}

#activity .sub_info span {
    background: #FAECBF;
    padding: 0.02rem 0.05rem;
    margin-right: 0.08rem;
    border-radius: 0.05rem;
    color: #000000;
}

#activity .sub_info span:last-of-type {
    margin-right: 0;
}

#activity .content {
    background: #ffffff;
    border-radius: 0.1rem;
    box-shadow: 0 0 5px rgba(0, 0, 0, .5);
    padding-bottom: 0.2rem;
    position: relative;
}

#activity .content .line {
    border-top: 1px dashed #e5443c;
    margin: 0.2rem 0.5rem 0;
}

#activity .content .line2 {
    border-top: 1px dashed #e5443c;
    margin-right: 0.2rem;
    margin-top: 0.2rem;
}

#activity .content2 {
    background: #ffffff;
    border-radius: 0.1rem;
    box-shadow: 0 0 5px rgba(0, 0, 0, .5);
    padding: 0.2rem;
    margin: 0.3rem 0 0.2rem;
}

#activity .content2 .item {
    padding: 0.1rem 0.3rem;
    background: #e6e6e6;
    margin: 0.15rem 0;
    border-radius: 0.1rem;
    font-weight: bold;
}

#activity .content2 .item .icon {
    width: 0.5rem;
    margin-right: 0.2rem;
}

#activity .content2 .item .icon img {
    width: 0.5rem;
}

#activity .content2 .item > div {
    float: left;
}

#activity .download {
    background: #e5443c;
    text-align: center;
    width: 2.5rem;
    border-radius: 1rem;
    margin: 0 auto;
    height: 0.4rem;
    line-height: 0.4rem;
    color: #ffffff;
    font-weight: bold;
}
#activity_prodDetailTemp .content{
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
}
#activity_prodDetailTemp .openBtn{
    position: absolute;
    background: #e5443c;
    text-align: center;
    width: 2.73rem;
    border-radius: 0.05rem;
    margin: 0 auto;
    height: 0.42rem;
    line-height: 0.4rem;
    color: #ffffff;
    font-weight: bold;
    left: 0;
    right: 0;
    top: 4.25rem;
}
#activity_prodDetailTemp .openBtn::before{
    content: "";
    position: absolute;
    width: 6px;
    height: 6px;
    background: #ffffff;
    left: 0.9rem;
    top: 50%;
    margin-top: -4px;
    border-radius: 50%;
 }
#activity_prodDetailTemp .openBtn::after{
    content: "";
    position: absolute;
    width: 6px;
    height: 6px;
    background: #ffffff;
    right: 0.9rem;
    top: 50%;
    margin-top: -4px;
    border-radius: 50%;
}
.van-overlay .close_btn {
    width: 0.2rem;
    height: 0.2rem;
    top: 0.1rem;
}