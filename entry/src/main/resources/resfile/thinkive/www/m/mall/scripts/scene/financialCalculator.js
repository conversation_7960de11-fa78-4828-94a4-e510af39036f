// 理财计算器
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools");
        monkeywords = require("../common/moneykeywords");
    var _pageId = "#scene_financialCalculator";
    function init() {
        setupAnnualRate(); // 设置年化利率滑块
        setDefaultValues(); // 设置默认值
        autoCalculateAndDisplay(); // 首次进入页面默认回填
    }
    function bindPageEvent() {
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
         // 输入框点击事件绑定
        appUtils.bindEvent($(_pageId + " #inputspanidAmt"), function (event) {
            tools.recordEventData('1','inputspanidAmt','首投');
            let invest_money = $(_pageId + " #czje_InvestAmt").val();
            let trans_amt = $(_pageId + " #czje_Amt").val();
            if(!invest_money || invest_money == ''){
                $(_pageId + " #inputspanidInvestAmt span").css({ color: "#999999" });
                $(_pageId + " #inputspanidInvestAmt span").addClass("unable");
                $(_pageId + " #inputspanidInvestAmt span").text("0元或200元起");
                $(_pageId + " #inputspanidInvestAmt span").attr("text",'0元或200元起');
            }else{
                monkeywords.close();
            }
            if(!trans_amt || trans_amt == ''){
                $(_pageId + " #inputspanidAmt span").attr("text","1000元起")
            }
            inputSpanEvent("Amt",event)
        });

        appUtils.bindEvent($(_pageId + " #inputspanidInvestAmt"), function (event) {
            tools.recordEventData('1','inputspanidInvestAmt','定投');
            let invest_money = $(_pageId + " #czje_InvestAmt").val();
            let trans_amt = $(_pageId + " #czje_Amt").val();
            if(!trans_amt || trans_amt == ''){
                $(_pageId + " #inputspanidAmt span").css({ color: "#999999" });
                $(_pageId + " #inputspanidAmt span").addClass("unable");
                $(_pageId + " #inputspanidAmt span").text("1000元起");
                $(_pageId + " #inputspanidAmt span").attr("text",'1000元起');
            }else{
                monkeywords.close();
            }
            if(!invest_money || invest_money == ''){
                $(_pageId + " #inputspanidInvestAmt span").attr("text","0元或200元起")
            }
            inputSpanEvent("InvestAmt",event)
        });

        // 计算按钮点击事件
        appUtils.bindEvent($(_pageId + " #calculateButton"), function () {
            let initialAmount = parseFloat($(_pageId + " #czje_Amt").val().replace(/,/g, "")) || 0;
            let monthlyInvestment = parseFloat($(_pageId + " #czje_InvestAmt").val().replace(/,/g, "")) || 0;
            let annualRate = parseFloat($(_pageId + " #annualRate").val()) / 100;

            if (initialAmount <= 0 || !initialAmount) {
                return layerUtils.iAlert("请输入首投金额");
            }

            const threshold_amount = "1000";
            if (parseFloat(initialAmount) < parseFloat(threshold_amount)) {
                return layerUtils.iAlert(`首投金额不能低于${threshold_amount}元`);
            }

            if (tools.isMatchAddAmt(initialAmount, threshold_amount, "1")) {
                return layerUtils.iAlert("递增金额1元");
            }

            if (initialAmount < 1000) initialAmount = 1000;

            if (monthlyInvestment > 0) {
                const invest_amount = "200";
                if (parseFloat(monthlyInvestment) < parseFloat(invest_amount)) {
                    return layerUtils.iAlert(`定投金额不能低于${invest_amount}元`);
                }

                if (tools.isMatchAddAmt(monthlyInvestment, invest_amount, "1")) {
                    return layerUtils.iAlert("递增金额1元");
                }

                if (monthlyInvestment < 200) monthlyInvestment = 200;
            }

            fetchAndDisplayResults(initialAmount, monthlyInvestment, annualRate);
        });

        // 返回按钮点击事件
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
            monkeywords.close();
        });
    }

    // 设置默认输入值
    function setDefaultValues() {
        $(_pageId + " #czje_Amt").val('10000');
        $(_pageId + " #czje_InvestAmt").val('1000');

        $(_pageId + " #inputspanidAmt span").text('10,000.00').css({ color: "#000" });
        $(_pageId + " #inputspanidInvestAmt span").text('1,000.00').css({ color: "#000" });
    }
    function autoCalculateAndDisplay() {
        let productType = Number(appUtils.getSStorageInfo("calculator_productType")) || 5;
        // 获取默认年化利率（优先读取 productType，否则默认 5%）
        let annualRate = [3, 5, 7].includes(productType) ? productType : 5;

        let initialAmount = 10000;
        let monthlyInvestment = 1000;

        $(_pageId + " #annualRate").val(annualRate);
        $(_pageId + " #annualRateValue").text(annualRate + '%');
    }
    // 设置年化利率
    function setupAnnualRate() {
        let productType = Number(appUtils.getSStorageInfo("calculator_productType")) || 5;
        let initialValue = [3, 5, 7].includes(productType) ? productType : 5;

        const minRate = 2,
            maxRate = 10;

        const $slider = $(_pageId + " #annualRate");
        const $label = $(_pageId + " #annualRateValue");

        // 初始化滑块和显示值
        $slider.attr({ min: minRate, max: maxRate }).val(initialValue);
        $label.text(initialValue + '%');

        // 滑块视觉样式更新
        function updateSliderVisuals(val) {
            const thumbPosition = ((val - minRate) / (maxRate - minRate)) * 100;
            $label.css('left', `${thumbPosition}%`);
            $slider.css('background', `linear-gradient(to right, #e5443c ${thumbPosition}%, #ddd ${thumbPosition}%)`);
        }

        updateSliderVisuals(initialValue);

        // 滑块拖动事件
        $slider.on('input', function () {
            let val = Math.max(minRate, $(this).val());
            $(this).val(val);
            $label.text(val + '%');
            updateSliderVisuals(val);
        });
    }
    // 获取并展示计算结果 
    function fetchAndDisplayResults(initialAmount, monthlyInvestment, annualRate) {
        service.reqFun102225({
            amt: initialAmount.toString(),
            invest_amt: monthlyInvestment.toString(),
            term: '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40',
            yield: (annualRate * 100).toFixed(2) + '%'
        }, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }

            const results = data.results.map((item, index) => {
                const year = index + 1;
                return {
                    year: year,
                    total_invest: item.total_invest, 
                    total_income: item.total_income
                };
            });

            displayResults(results);
            bindToggleEvents($(_pageId + " #resultTable tbody"));
        });
    }
    // 展示计算结果表格
    function displayResults(results) {
        // 默认展示的年份列表
        var showYears = [1, 5, 10, 15, 20, 25, 30, 35, 40];

        $(_pageId + " #resultTable tbody").empty();

        for (var i = 0; i < showYears.length; i++) {
            var currentYear = showYears[i];
            var result = results.find(r => r.year === currentYear);

            if (!result) continue;

            $('<tr>')
                .addClass('default-row')
                .attr('data-year', result.year)
                .html(`
                    <td>${result.year}年</td>
                    <td>${(result.total_invest / 10000).toFixed(2)}万</td>
                    <td>${(result.total_income / 10000).toFixed(2)}万</td>
                `)
                .appendTo($(_pageId + " #resultTable tbody"));

            var row = $(_pageId + " #resultTable tbody tr:last");

            var nextDefaultYear = showYears.find(y => y > result.year) || 41;

            for (var y = 1; y <= 4 && (currentYear + y) < nextDefaultYear && (currentYear + y) <= 40; y++) {
                var hiddenResult = results.find(r => r.year === result.year + y);
                if (!hiddenResult) continue;

                $('<tr>')
                    .addClass('toggle-row')
                    .attr('data-parent', result.year)
                    .html(`
                        <td>${hiddenResult.year}年</td>
                        <td>${(hiddenResult.total_invest / 10000).toFixed(2)}万</td>
                        <td>${(hiddenResult.total_income / 10000).toFixed(2)}万</td>
                    `)
                    .appendTo($(_pageId + " #resultTable tbody"));
            }

            if (currentYear !== 40) {
                $('<div>')
                    .addClass('toggle-button-wrapper')
                    .html(`
                        <button class="toggle-button">
                            <span class="arrow-down"></span>
                            <span class="arrow-up"></span>
                        </button>
                    `)
                    .insertAfter(row);
            }
        }

        $(_pageId + " #resultContainer").show();

        bindToggleEvents($(_pageId + " #resultTable tbody"));
    }

    // 绑定展开/收起按钮点击事件
    function bindToggleEvents(tableBody) {
        if (!tableBody.length) return;

        if (tableBody.data('toggleBound')) return;
        tableBody.data('toggleBound', true);

        tableBody.on('click', '.toggle-button', function () {
            var wrapper = $(this).closest('.toggle-button-wrapper');
            var parentRow = wrapper ? wrapper.prev() : null;
            var year = parentRow ? parentRow.data('year') : null;

            if (!year) return;

            var hiddenRows = $(_pageId + ` tr.toggle-row[data-parent="${year}"]`);
            hiddenRows.toggleClass('show');
            
            // 删除当前点击的按钮
            wrapper.remove(); 
        });
    }
    //输入首投/每月定投总额弹出数字键盘
    function inputSpanEvent(id, event) {
        event.stopPropagation();
        $(_pageId + ` #czje_${id}`).val('');
        
        // 键盘事件
        moneyboardEvent(id);

        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "scene_financialCalculator";
        param["eleId"] = `czje_${id}`;
        param["doneLable"] = "确定";
        param["keyboardType"] = "3";
        require("external").callMessage(param);
    }
    function moneyboardEvent(id) {
        monkeywords.open({
            _pageId: _pageId,
            idnum: id,
            domid: $(_pageId + ` #czje_${id}`),
            endcallback: function () { // 键盘完成
                var curVal = $(_pageId + ` #czje_${id}`).val();
                var moneys = curVal.replace(/,/g, "");
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "#999999" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text('1000元起');
                        $(_pageId + " #inputspanidAmt span").attr("text",'1000元起');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "#999999" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text('0元或200元起');
                        $(_pageId + " #inputspanidInvestAmt span").attr("text",'0元或200元起');
                    }
                }   
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }              
                $(_pageId + ` #czje_${id}`).val(moneys);
            },
            inputcallback: function () {// 键盘输入
                var curVal = $(_pageId + ` #czje_${id}`).val();
                curVal = curVal.replace(/,/g, "");
               
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + ` #czje_${id}`).val(curVal.substring(0, curVal.length - 1));
                }

            }, // 键盘隐藏
            keyBoardHide: function () {
                $(_pageId + " article").removeClass("scene_snowStory_article");
                var curVal = $(_pageId + ` #czje_${id}`).val();
                if (!curVal) return
                curVal = curVal.replace(/,/g, "");
                curVal = curVal*1;   
                            
                var moneys = curVal;   
                if(!moneys && moneys != '0'){
                    if(id == 'Amt') {
                        $(_pageId + " #inputspanidAmt span").css({ color: "#999999" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidAmt span").text('1000元起');
                        $(_pageId + " #inputspanidAmt span").attr("text",'1000元起');
                    }else{
                        $(_pageId + " #inputspanidInvestAmt span").css({ color: "#999999" });//默认输入框失去焦点
                        $(_pageId + " #inputspanidInvestAmt span").text('0元或200元起');
                        $(_pageId + " #inputspanidInvestAmt span").attr("text",'0元或200元起');
                    }
                }             
                if (moneys) {
                    $(_pageId + ` #czje_${id}`).val(tools.fmoney(moneys));
                    $(_pageId + ` #inputspanid${id} span`).html(tools.fmoney(moneys));
                }
            },
        })
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        localStorage.removeItem("calculator_productType");
        $(_pageId + " #resultContainer").hide();
        $(_pageId + " #czje_Amt").val('');
        $(_pageId + " #czje_InvestAmt").val('');   
        $(_pageId + " #resultTable tbody").empty();
        monkeywords.destroy();
        setDefaultValues();
        // $(_pageId + " #calculateButton").off('click');
        // $(_pageId + " .icon_back").off('click');
        // $(_pageId + " #annualRate").off('input');
        // $(_pageId + " #inputspanidAmt").off('click');
        // $(_pageId + " #inputspanidInvestAmt").off('click');
    }

    // document.addEventListener('DOMContentLoaded', function () {
    //     init();
    // });

    var financialCalculator = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };

    // 暴露对外的接口
    module.exports = financialCalculator;
});