// 更多
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        common = require("common"),
        _pageUrl = "moreDetails/msg",
        _pageId = "#moreDetails_msg ";
    var hasNewMsg_1 = 0,
        hasNewMsg_2 = 0,
        hasNewMsg_3 = 0,
        hasNewMsg_4 = 0;

    function init() {
        hasNewMsg();
    }

    function hasNewMsg() {
        hasNewMsg_1 = appUtils.getLStorageInfo("hasNewMsg_1");//用户已看的公告id
        hasNewMsg_2 = appUtils.getLStorageInfo("hasNewMsg_2");//最新公告id
        hasNewMsg_3 = appUtils.getLStorageInfo("hasNewMsg_3");//用户已看的资讯id
        hasNewMsg_4 = appUtils.getLStorageInfo("hasNewMsg_4");//最新资讯id

        hasNewMsg_1 = hasNewMsg_1 ? hasNewMsg_1 : 0;
        hasNewMsg_2 = hasNewMsg_2 ? hasNewMsg_2 : 0;
        hasNewMsg_3 = hasNewMsg_3 ? hasNewMsg_3 : 0;
        hasNewMsg_4 = hasNewMsg_4 ? hasNewMsg_4 : 0;

        if (hasNewMsg_1 != hasNewMsg_2) {
            $("#moreDetails_msg #clicknotice span").addClass("on");
        } else {
            $("#moreDetails_msg #clicknotice span").removeClass("on");
        }
        if (hasNewMsg_3 != hasNewMsg_4) {
            $("#moreDetails_msg #consultation span").addClass("on");
        } else {
            $("#moreDetails_msg #consultation span").removeClass("on");
        }
    }

    //绑定事件
    function bindPageEvent() {

        // 去 公告页面
        appUtils.bindEvent($(_pageId + " #clicknotice"), function () {
            //2017-8-17 jiaxr-add 去除新消息提示
            $("#moreDetails_msg #clicknotice").removeClass("on");
            appUtils.setLStorageInfo("hasNewMsg_1", hasNewMsg_2);
            if (hasNewMsg_3 == hasNewMsg_4) {
                $(_pageId + ".hasNewMsg").removeClass("on");
                $("#account_myAccount #message_img").removeClass("on");
                appUtils.setLStorageInfo("hasNewMsg", "false");
            }
            var param = {
                "prePage": "moreDetails/msg"
            };
            appUtils.pageInit("moreDetails/more", "moreDetails/notice", param);
        });

        // 去资讯页面
        appUtils.bindEvent($(_pageId + " #consultation"), function () {
            //2017-8-17 jiaxr-add 去除新消息提示
            $("#moreDetails_msg #consultation span").removeClass("on");
            appUtils.setLStorageInfo("hasNewMsg_3", hasNewMsg_4);
            if (hasNewMsg_1 == hasNewMsg_2) {
                $(_pageId + ".hasNewMsg").removeClass("on");
                $("#account_myAccount #message_img").removeClass("on");
                appUtils.setLStorageInfo("hasNewMsg", "false");
            }
            var param = {
                "prePage": "moreDetails/msg"
            };
            appUtils.pageInit("moreDetails/more", "moreDetails/consultation", param);
        });

        //返回
        appUtils.bindEvent($(_pageId + " #back_btn"), function () {
            pageBack();
        });


    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var msg = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = msg;
});
