/**
 * @Description 帮助中心搜索页面
 */

define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        VIscroll = require("vIscroll"),
        vIscroll = {//上下滑动
            "scroll": null,
            "_init": false
        },
        service = require("mobileService"),
        _pageId = "#moreDetails_helpCenterSearch ",
        _pageUrl = "moreDetails/helpCenterSearch";
    var gconfig = require("gconfig");
    var tools = require("../common/tools");
    var searchText = "";//搜索关键词
    var totalPages = "",//总页数
        cur_page = "1";//要查询的页面z
    var num_per_page = "10";
    var height2;

    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        $(_pageId + " #search_result_ul").css("overflow-y", "auto");
        $(_pageId + "#search_result_ul").css("height", '480px');
        searchText = appUtils.getSStorageInfo("helpSearch").searchText;//获取搜索关键词
        $(_pageId + " #searchText").val(searchText);//展示搜索框内容
        height2 = appUtils.getSStorageInfo("helpSearch").windowHeight - 178;
        searchResult();
    }

    function bindPageEvent() {
        //问题点击事件
        appUtils.preBindEvent($(_pageId + " #search_result_ul"), $(_pageId + " #search_result_ul li"), function (e) {

            //获取事件源
            var node = $(e.srcElement || e.target).closest("li");
            //打开/闭合相应信息
            var node2 = node.children("div");
            if (node2.css('display') == "none") {
                node2.show();
                node.addClass('on');
            } else {
                node2.hide();
                node.removeClass('on');
            }
            setResultHeight();
            //滑动组件-在完成数据处理和显示组件之后初始化
            pageScrollInit();
        }, "click");

        //返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();

        });
        //搜索按钮
        appUtils.bindEvent($(_pageId + " #searchButton"), function () {
            searchText = $(_pageId + " #searchText").val();//展示搜索框内容
            cur_page = 1;
            searchResult();
        });
        //客服电话
        appUtils.bindEvent($(_pageId + " #phone"), function () {
            $(_pageId + "#phone_div").show();
        });
        //取消拨打电话
        appUtils.bindEvent($(_pageId + " #call_off"), function () {
            $(_pageId + "#phone_div").hide();
        });
        //拨打电话
        appUtils.bindEvent($(_pageId + " #call_up"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = gconfig.global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //在线客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            appUtils.setSStorageInfo("helpSearch", {searchText: searchText, windowHeight: height2});//获取搜索关键词
            tools.saveAlbum(_pageUrl)
        });
    }

    function destroy() {
        $(_pageId + "#search_result_ul").html("");
        $(_pageId + "#phone_div").hide();
        totalPages = "";
        $(_pageId + "#searchText").val("");
        cur_page = "1";
    }

    /***********************************自定义方法************************************************************************************/
    function searchResult() {
        var param = {
            key: searchText,
            cur_page: cur_page,
            num_per_page: num_per_page
        }
        service.reqFun102061(param, function (resultVo) {

            if (resultVo.error_no == 0) {
                if (cur_page == 1) {
                    $(_pageId + "#search_result_ul").html("");//清理数据区
                }
                if (cur_page == totalPages) {
                    $(_pageId + " .visc_pullUp").hide();
                }
                var info = resultVo.results[0];
                totalPages = info.totalPages;
                var data = info.data;
                for (var i = 0; i < data.length; i++) {
                    if (data[i].title == "目前支持绑定哪些银行卡？") {
                        var html = "<li><h5 class='search_span'>" + data[i].title + "</h5>";
                        html += "<div class='da' style='display:none' id='bank_table'><p>" + data[i].text_content + "</p></div></li>";
                        $(_pageId + "#search_result_ul").html($(_pageId + "#search_result_ul").html() + html);
                        service.reqFun102014({}, function (resultVo) {
                            bankCallBackFun(resultVo);
                        });
                    } else {
                        if (data[i].title == "旧银行卡和银行挂失证明丢失怎么办？") {
                            data[i].title = "旧银行卡丢失怎么办？";
                            data[i].key_title = data[i].title.replace(searchText, '<strong class="search_span">' + searchText + '</strong>');
                            data[i].text_content = "如旧银行卡丢失，需上传挂失证明或扫描件。如旧银行卡和银行卡挂失证明文件均丢失，请登录晋金所官网www.sxfae.com在帮助中心-密码和安全-更换绑定的银行卡，下载《换卡免责声明》模板，填写后拍照或扫描上传。";
                            data[i].text_content = data[i].text_content.replaceAll(searchText, '<strong class="search_span">' + searchText + '</strong>');
                        }
                        var html = "<li><h5 class='search_span'>" + data[i].title + "</h5>";
                        html += "<div  class='da' style='display:none'>" + data[i].text_content + "</div></li>";
                        $(_pageId + "#search_result_ul").html($(_pageId + "#search_result_ul").html() + html);
                    }
                }
                $(_pageId + "#result_msg").html('&nbsp;&nbsp;' + '关于"' + searchText + '"的搜索结果, 共<strong><font color="red">' + info.totalRows + '</font></strong>条');
                setResultHeight();
                pageScrollInit();
            } else {
                $(_pageId + "#result_msg").html("<p style='padding: 0 0.15rem'>还没有相关问题哦,问问客服小金宝吧</p>");
            }
        });//展示查询结果

    }

    //显示银行卡的回调函数
    function bankCallBackFun(resultVo) {
        if (resultVo.error_no == 0) {
            var html_str = "";
            var dataList = resultVo.results;
            html_str += "<p>";
            html_str += "<p><table width='100%' border='0' cellspacing='0' cellpadding='0' style=' border:solid 1px #AAAAAA;'>";
            html_str += "<tr>" +
                "<th scope='col' style='background:#FF7744;width:20%;border-right:solid 1px #AAAAAA;text-align: center;line-height: 3em;'>银行</th>" +
                "<th scope='col' style='background:#FF7744;width:20%;border-right:solid 1px #AAAAAA;text-align: center;line-height: 3em;'>单笔限额</th>" +
                "<th scope='col' style='background:#FF7744;width:20%;border-right:solid 1px #AAAAAA;text-align: center;line-height: 3em;'>当日限额</th>" +
                "<th scope='col' style='background:#FF7744;width:40%;text-align: center;line-height: 3em;'>备注</th>" +
                "</tr>";
            for (var j = 0; j < dataList.length; j++) {
                var bank_name = dataList[j].bank_name;
                var day_limit = dataList[j].day_limit;
                var single_limit = dataList[j].single_limit;
                var remark = dataList[j].remark;
                if (day_limit < 0) {
                    day_limit = "不限";
                }
                if (single_limit < 0) {
                    single_limit = "不限";
                }
                if (day_limit >= 10000) {
                    day_limit = day_limit / 10000 + "万";
                }
                if (single_limit >= 10000) {
                    single_limit = single_limit / 10000 + "万";
                }
                var recommend_flag = dataList[j].recommend_flag;//是否推荐 0不推荐1推荐
                if(recommend_flag == '1') {
                    html_str += "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + bank_name + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + single_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + day_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;color: #e5443c;'><img src='images/star.png' style='color: #e5443c;width: 0.18rem;margin-top: -0.04rem;'>" + remark + "</td></tr>";
                }else{
                    html_str += "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + bank_name + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + single_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + day_limit + "</td>" +
                        "<td style=' border-bottom:solid 1px #AAAAAA;border-right:solid 1px #AAAAAA;text-align: center;padding: 0.08rem 0;'>" + remark + "</td></tr>";
                }
            }
            html_str += "</table></p>";
            $(_pageId + "#bank_table").html(html_str);
        }
    }


    /**上下滑动刷新事件**/
    function pageScrollInit() {
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //id="visc_wrapper"的高度
                "container": $(_pageId + " #v_container"),
                "wrapper": $(_pageId + " #v_wrapper"),
                "downHandle": function () {
                    cur_page = 1;
                    searchResult();
                },
                "upHandle": function () {
                    if (cur_page == totalPages) {
                        $(_pageId + " .visc_pullUp").hide();
                        return;
                    } else {
                        $(_pageId + " .visc_pullUp").show();
                        cur_page++;
                        searchResult();
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
    }

    //设置结果区ul的高度-解决结果展示不全问题
    function setResultHeight() {
        var resultHeight = 0;
        $(_pageId + "#search_result_ul").find("li").each(function (index, item) {
            resultHeight += (parseInt($(item).height()) + parseInt($(item).css("margin-bottom")));
        })
        resultHeight = resultHeight < $(_pageId + "#v_wrapper").height() ? $(_pageId + "#v_wrapper").height() : resultHeight
        $(_pageId + "#search_result_ul").css("height", resultHeight + 20 + "px");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var helpCenterSearch = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = helpCenterSearch;
});
