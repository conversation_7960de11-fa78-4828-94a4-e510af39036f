//购买结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        validatorUtil = require("validatorUtil"),
        _pageUrl = "vipBenefits_phoneVouchersResult",
        _pageId = "#vipBenefits_phoneVouchersResult";
    var tools = require("../common/tools");//升级
    var outputInfo;
    var num;
    var t;
    require("../common/clipboard.min.js");

    function init() {
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').show();
        $(_pageId + ' .transactionReward').hide();
        num = 5;
        tools.getActivityInfo(_pageId,_pageUrl)
        outputInfo = appUtils.getPageParam();
        countDown();
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .done_btn"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageBack();
        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            var routerList = appUtils.getSStorageInfo("routerList");
            routerList.splice(-2);
            appUtils.setSStorageInfo("routerList", routerList);
            appUtils.pageInit("vipBenefits/phoneVouchersResult", "vipBenefits/records");
        })
    }


    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num);
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun108005({trans_serno: outputInfo.trans_serno}, function (data) {
                    if (data.error_no == "0") {
                        $(_pageId + " .load").hide();
                        // 0受理成功 1受理失败 3交易成功 4交易失败 5交易未知 8确认成功 9 确认失败
                        if (data.results[0].trans_status == "8") {
                            $(_pageId + " .success").show();//购买成功
                            var cardpwd = data.results[0].cardpwd;
                            if(outputInfo.type == '2' && validatorUtil.isNotEmpty(cardpwd)){//如果为京东劵并且有卡密
                                var camilo = '卡密：' + cardpwd + '<span id="cardpwd" data-clipboard-action="copy" data-clipboard-text="' + cardpwd + '" style="color: #319ef2;padding-left: 0.06rem;">复制</span>'
                                $(_pageId + " .success .order_info h2").html(camilo);
                                copyContent("cardpwd");
                            }
                        } else if (data.results[0].trans_status == "9" || data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            // if (data.results[0].desc) {
                                // $(_pageId + ' #failinf').text('失败原因：' + data.results[0].desc);
                            // } else {
                                $(_pageId + ' #failinf').text('很抱歉，兑换失败');
                            // }
                            $(_pageId + " .fail").show();//购买失败
                        } else {
                            $(_pageId + " .wait").show();//购买无结果
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                })
            }

        }, 1000)
    }
    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            if(!e.text.trim()) {
                layerUtils.iAlert("复制失败，请重试", -1, function () {
                    getFundInfo(false)
                });
                return;
            }
            layerUtils.iAlert("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    function destroy() {
        clearInterval(t);
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').show();
        $(_pageId + " #cardpwd").attr("data-clipboard-text", " ");
        $(_pageId + " .success .order_info h2").html("");
    }


    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
