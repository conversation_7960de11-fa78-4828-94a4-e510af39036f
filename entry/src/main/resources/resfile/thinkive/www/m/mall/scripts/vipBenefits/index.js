// 会员福利 - 列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService");
    var _pageId = "#vipBenefits_index ";
    let common = require("common");
    let ut = require("../common/userUtil");
    var _pageCode = "vipBenefits/index";
    var tools = require("../common/tools");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    let userInfo, cust_no, mobile
    var usable_vol, luckflag, xinflyflag = 1;//总积分
    let chooseData  //调用原生分享传参
    var cardVoucherType;//卡券类型 1为话费券，2为京东券
    var sign_data, novice_data, month_data, friday_data;//签到、新手、月度信息、周五活动存储
    var threshold;//限制用户最低兑换金额
    var stopTime = null;
    var pageTouchTimer = null;
    var sign_type = null; //1普通签到 2分享卡片 3分享文章
    var new_pop_data = {};//新版签到活动弹窗数据 (卡片/文章)
    var timer = null;
    var is_help,help_img_url;
    var userAuthenticationStatus; //用户认证状态
    var platform = gconfig.platform;
    require("../common/html2canvas.min");
    var current_state = {
        '1': '立即签到',
        '2': '参与红包雨',
        '3': '抽盲盒',
        '4': '已完成',
        '5': '去领取'
    };
    async function init() {
        //页面埋点初始化
        tools.initPagePointData();
        // return layerUtils.iLoading(true);
        stopTime = null;
        //进页面首页清除定时器
        if (timer) {
            clearInterval(timer);
            timer = null;
        };
        userInfo = ut.getUserInf();
        let res = await getUserAuthenticationStatus()
        userAuthenticationStatus = res[0].state //获取用户认证状态
        cust_no = userInfo.custNo
        mobile = userInfo.mobileWhole
        reqFun108001();//会员总积分查询
        //reqFun108027();//营销评论查询接口
        reqFun102080();//会员活动图标类型
        reqFun108019();//查询所有活动状态
        // reqFunShow();//从各个页面返回的展示
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
        appUtils.clearSStorage("activityInfo");
        // tools.guanggao({ _pageId: _pageId, group_no: "pointRewardBanner"});
        //banner轮播
        // tools.guanggao({_pageId: _pageId, group_id: "1"}); 测试
        // sign_pop_new();
    }

    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }

    function sign_pop_new(url) {    //新版签到流程
        url = url ? url : global.oss_url + new_pop_data.pop_img_url
        let html = '<div class="activityDialog pop_layer" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;"><div class="index-popup" style="width: 75%;z-index: 2100;position: static;"><img id="new_close" src="./images/new_close.png"></img><img operationtype="1" operationid="new_close" operationName="关闭弹窗" id="src_Image" class="popup-description" src= ' + url + ' style="width:100%;height:100%;"></div></div>'
        $(_pageId + " #activityDialog").append(html);
    }
    function article_check_in(shareType, article_open, source) {    //文章签到
        mobile = common.desEncrypt("mobile", mobile);//加密
        let param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50231";
        param["shareType"] = shareType;//平台字典
        param["title"] = new_pop_data.article_title;
        param["link"] = global.serverUrl + '/m/mall/index.html'+ '?mobile=' + mobile + '&is_open=' + article_open + "&sysdate=" + new_pop_data.sysdate + "&activity_id=" + chooseData.activity_id + "&help_img_url=" + help_img_url + "&is_help=" + is_help + "&source=" + source + '#!/moreDetails/articleDetails.html';
        param["content"] = new_pop_data.content; //分享文本
        param["imgUrl"] = "https://m.xintongfund.com/m/mall/images/icon_app.png";
        $(_pageId + " .pop_layer").hide();
        require("external").callMessage(param);
    }
    function sign_card(shareType) {   //卡片签到
        $(_pageId + " .pop_layer").hide();
        //渲染base64 loading
        $(_pageId + " #vip_pop_layer").show();
        let source = '5'; //用户来源签到
        let chooseData = new_pop_data;
        let bgImg = gconfig.global.oss_url + chooseData.share_img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #vipBenefits_qr_img").show();
                    $(_pageId + " #vipBenefits_code").hide();
                    $(_pageId + " #vipBenefits_qr_img").empty();
                    let qr_code_img = gconfig.global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #vipBenefits_qr_img").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                            var father = document.querySelector("#content");
                            var _fatherHTML = document.querySelectorAll("#content .page");
                            var cur = document.querySelector("#vipBenefits_index");
                            father.innerHTML = "";
                            father.appendChild(cur);
                            let dom = document.querySelector(_pageId + " .shareImg");
                            html2canvas(dom, {
                                scale: 4
                            }).then(canvas => {
                                var base64 = canvas.toDataURL("image/png");
                                var _base64 = base64.split(",")[1];
                                father.innerHTML = "";
                                for (let i = 0; i < _fatherHTML.length; i++) {
                                    father.appendChild(_fatherHTML[i]);
                                }
                                param = {
                                    "funcNo": "50231",
                                    "imgUrl": _base64,
                                    "shareType": shareType,
                                    "imageShare": "1",
                                    "imageType":"base64"
                                }
                                setTimeout(() => {
                                    require("external").callMessage(param);
                                    $(_pageId + " #vip_pop_layer").hide();
                                }, 300);


                            })
                        } else {
                            $(_pageId + " #vip_pop_layer").hide();
                            layerUtils.iAlert(d.error_info);
                        }
                    }, { "isShowWait": false })
                } else {
                    $(_pageId + " #vipBenefits_qr_img").hide();
                    $(_pageId + " #vipBenefits_code").show();
                    qrcode(chooseData, source, shareType);
                }

            } else {
                $(_pageId + " #vip_pop_layer").hide();
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    //用户助力签到领积分
    function help_sign() { 
        var query_params = {};
        query_params["registered_mobile"] = mobile;
        query_params["tem_type"] = chooseData.share_template;
        service.reqFun102012(query_params, function (data) {
            if (data.error_no == "0" && data.results && data.results[0]) {
                let time = data.results[0].sysdate
                start_help_sign(time);
            }
        })
    }
    //start_help_sign
    function start_help_sign(time){
        service.reqFun108021({ sysdate: time ? time : new_pop_data.sysdate }, (data) => {
            if (data.error_no == 0) {
                var results = data.results[0];
                // current_state 1:立即签到 2:参与红包雨 3:拆盲盒 4已完成
                if (results.current_state == '4' && results.points_amount == '0') {
                    $(_pageId + " .sign_succeed .index_info_tishi").html('您已成功签到</br>奖励随后发放');
                    $(_pageId + " .sign_succeed .sureBtn span").html('好的')
                    $(_pageId + " .sign_succeed").show();
                }
                if (results.current_state == '4' && results.points_amount != '0') {
                    $(_pageId + " .sign_succeed .index_info_tishi").html('恭喜您获得</br><span>' + (results.points_amount ? results.points_amount : '--') + '</span>积分');
                    $(_pageId + " .sign_succeed .sureBtn span").html('好的')
                    $(_pageId + " .sign_succeed").show();
                }
                if (results.current_state == '2') {
                    $(_pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次参与红包雨机会');
                    $(_pageId + " .sign_succeed .sureBtn").attr({ 'url': results.url, 'reward_activity_id': results.reward_activity_id }).find('span').html('去参与');
                    $(_pageId + " .sign_succeed").show();
                }
                if (results.current_state == '3') {
                    $(_pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次拆礼盒机会');
                    $(_pageId + " .sign_succeed .sureBtn").attr({ 'url': results.url, 'reward_activity_id': results.reward_activity_id }).find('span').html('去参与');
                    $(_pageId + " .sign_succeed").show();
                }
                sign_data = $.extend(sign_data, results);
                reqFunSign(sign_data);
            } else {
                layerUtils.iLoading(false);
                $(_pageId + " .xubox_shade").hide();
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    function start_sign(time) {
        service.reqFun108021({ sysdate: time ? time : new_pop_data.sysdate }, (data) => {
            if (data.error_no == 0) {
                pageTouchTimer = setTimeout(() => {
                    // layerUtils.iAlert('分享成功');
                    var results = data.results[0];
                    // current_state 1:立即签到 2:参与红包雨 3:拆盲盒 4已完成
                    if (results.current_state == '4' && results.points_amount == '0') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('您已成功签到</br>奖励随后发放');
                        $(_pageId + " .sign_succeed .sureBtn span").html('好的')
                        $(_pageId + " .sign_succeed").show();
                    }
                    if (results.current_state == '4' && results.points_amount != '0') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('恭喜您获得</br><span>' + (results.points_amount ? results.points_amount : '--') + '</span>积分');
                        $(_pageId + " .sign_succeed .sureBtn span").html('好的')
                        $(_pageId + " .sign_succeed").show();
                    }
                    if (results.current_state == '2') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次参与红包雨机会');
                        $(_pageId + " .sign_succeed .sureBtn").attr({ 'url': results.url, 'reward_activity_id': results.reward_activity_id }).find('span').html('去参与');
                        $(_pageId + " .sign_succeed").show();
                    }
                    if (results.current_state == '3') {
                        $(_pageId + " .sign_succeed .index_info_tishi").html('签到成功</br>获得一次拆礼盒机会');
                        $(_pageId + " .sign_succeed .sureBtn").attr({ 'url': results.url, 'reward_activity_id': results.reward_activity_id }).find('span').html('去参与');
                        $(_pageId + " .sign_succeed").show();
                    }
                    sign_data = $.extend(sign_data, results);
                    reqFunSign(sign_data);
                }, 6000);
            } else {
                layerUtils.iLoading(false);
                $(_pageId + " .xubox_shade").hide();
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    function bindPageEvent() {
        //点击banner
        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            // if (!common.loginInter()) return;
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");
            var picture = $(this).attr("picture");
            var group_id = $(this).attr("group_id");
            var banner_id = $(this).attr("banner_id");
            var qualified_investor_visible = $(this).attr("qualified_investor_visible");
            /**
             * qualified_investor_visible 合格投资人确认弹窗
             * userAuthenticationStatus 是否认证过
             */
            // if(qualified_investor_visible && qualified_investor_visible == '1' && userAuthenticationStatus && userAuthenticationStatus == '0'){
            //     if (!common.loginInter()) return;
            //     if (!ut.hasBindCard(_page_code)) return;
            //     is_banner_pageTo = true;
            //     bannerPageIntoData = {file_type,url,file_state,name,description,picture,group_id,banner_id,qualified_investor_visible}
            //     return $(_pageId + ".qualifiedInvestor").show();
            // }
            if (file_type == "0" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", url, {
                    "group_id": group_id,
                    "banner_id": banner_id,
                });
                return;
            }
            // 是否是有效内外链接
            if (file_type == "1" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "group_id": group_id,
                    "banner_id": banner_id,
                    "name": name,
                    "description": description,
                });
                return;
            }
            // 登录授权
            if (file_type == "2" && file_state == "1") {
                if (!common.loginInter()) return;
                if (!/^(http|https):/.test(url)) {
                    if (url.indexOf("?") > -1) {
                        var skip_url = url.split("?")[0];
                        var parameter = url.split("?")[1];
                        var parameter_arr = parameter.split("&"); //各个参数放到数组里
                        var urlInfo = {};//url的参数信息
                        for (var i = 0; i < parameter_arr.length; i++) {
                            num = parameter_arr[i].indexOf("=");
                            if (num > 0) {
                                name = parameter_arr[i].substring(0, num);
                                value = parameter_arr[i].substr(num + 1);
                                urlInfo[name] = value;
                                urlInfo["group_id"] = group_id;
                                urlInfo["banner_id"] = banner_id;
                            }
                        }
                        if (url.indexOf("activity/fundLuckdrawnew") > -1) { urlInfo['channel'] = "jjcf_app"; urlInfo['type'] = "luckDraw" }
                        appUtils.pageInit("login/userIndexs", skip_url, urlInfo);

                    } else {
                        appUtils.pageInit("login/userIndexs", url);
                    }
                    return;
                }
                if (url.indexOf("activity/fundLuckdraw") > -1) {
                    common.setLocalStorage("activityInfo", {
                        activity_id: "2505",
                        group_id: group_id,
                        banner_id: banner_id,
                        cust_no: ut.getUserInf().custNo,
                        channel: "jjcf_app",
                        type: "luckDraw",
                        mobile: ut.getUserInf().mobileWhole,
                    })
                    var data = external.callMessage({
                        "funcNo": "50043",
                        "moduleName": "mall",
                        "key": "activityInfo",
                    })
                }
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                    "group_id": group_id,
                    "banner_id": banner_id
                });
            }
            // 小程序跳转
            if (file_type == "3" && url) {
                tools.jump_applet(url);
                return;
            }
        }, 'click');
        //邀请规则
        appUtils.bindEvent($(_pageId + " .friendsRules"), () => {
            $(_pageId + " #activityRules").show().find('.introduce').show()
        });
        //打开签到弹窗
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog #src_Image", function () {
            // tools.recordEventData('1','src_Image','打开签到弹窗');
            $(this).parents(".activityDialog").remove();
            $(_pageId + " #pop_layer #share_WeChat").hide();
            $(_pageId + " #pop_layer").show();
        }, 'click');
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), () => {
            tools.saveAlbum(_pageCode)
        });
        //积分明细
        appUtils.bindEvent($(_pageId + " #integral_records"), () => {
            appUtils.pageInit(_pageCode, "vipBenefits/records")
        });
        //图标控制
        // type 1:积分抽奖 2:赚积分 3:积分兑换  4:我的卡券，5，积分明细;state 1:敬请期待 2:正常使用
        appUtils.preBindEvent($(_pageId + " .vipBenefits_list"), "li", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var str = $(this).attr("id");
            var str_type = str.slice(0, str.indexOf('_'));
            var str_state = str.slice(str.indexOf('_') + 1);
            var title = $(this).find("p").html();
            // tools.recordEventData('1','vipBenefits_list' + '_' + str_type,$(this).find('p').text());
            if (str_state == '1') {
                layerUtils.iAlert("敬请期待!");
            } else if (str_state == '2' && str_type == '2') {//2:赚积分
                // $(_pageId + " #benefitsBox h3").html(title);
                // reqFun108019();//查询活动信息
                $(_pageId + " .type_content").hide();
                $(_pageId + " #benefitsBox").show();
            } else if (str_state == '2' && str_type == '3') {//3:积分兑换
                // $(_pageId + " #benefitsShopType h3").html(title);
                // $(_pageId + " .type_content").hide();
                // $(_pageId + " #benefitsShopType").show();
                appUtils.pageInit(_pageCode, "vipBenefits/exchange", { threshold: threshold })
            } else if (str_state == '2' && str_type == '5') {//3:积分明细
                appUtils.pageInit(_pageCode, "vipBenefits/records")
            }
        }, "click");

        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            // tools.recordEventData('1','cancelShare','取消邀请好友');
            $(_pageId + " #pop_layer").hide();
        });
        //取消弹窗
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog #new_close", function () {
            // tools.recordEventData('1','new_close','关闭弹窗');
            $(this).parents(".activityDialog").remove();
            $(_pageId + " #pop_layer #share_WeChat").hide();
        }, 'click');
        //微信好友
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function () {
            tools.recordEventData('1','share_WeChat','微信好友分享',{activityId:chooseData.activity_id,shareTemplate:chooseData.share_template});
            tools.clickPoint(_pageId, _pageCode, 'share_WeChat', chooseData.activity_id);
            let source = '5'
            //2.文章签到 需要直接调用分享 且用户打开才算分享
            if (sign_type == '3' && article_open == '1') {
                return article_check_in("22", article_open, source);
            }
            common.share("22", chooseData.share_template, '', '', '', '', false, source);
            setUser() //记录用户行为
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            tools.recordEventData('1','share_WeChatFriend','微信朋友圈分享',{activityId:chooseData.activity_id,shareTemplate:chooseData.share_template});
            tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', chooseData.activity_id);
            //根据现有字段区分新老逻辑 单独抽出新逻辑
            setUser() //记录用户行为
            let source = '5'
            // return console.log(sign_type,link_open,article_open)
            //1.普通签到，需用户主动打开 普通签到直接
            if (sign_type == '1' && link_open == '1') return common.share("23", chooseData.share_template, chooseData.activity_id, '', '', link_open, false, source,is_help,help_img_url);
            //2.文章签到 需要直接调用分享 且用户打开才算分享
            if (sign_type == '3' && article_open == '1') {
                return article_check_in("23", article_open, source);
            }
            //2.文章签到 点击分享就算分享
            if (sign_type == '3' && article_open == '0') {
                article_check_in("23", article_open, source);
                if(is_help != '1') start_sign();
            } else if (sign_type == '2') { //卡片签到
                sign_card("23");
                start_sign();
            } else {
                var query_params = {};
                query_params["registered_mobile"] = mobile;
                query_params["tem_type"] = chooseData.share_template;
                service.reqFun102012(query_params, function (data) {
                    if (data.error_no == "0" && data.results && data.results[0]) {
                        let time = data.results[0].sysdate
                        if(is_help != '1') start_sign(time);
                    }
                })
                common.share("23", chooseData.share_template, chooseData.activity_id, '', '', '', false, source,is_help,help_img_url);
            }
            // // 连续签到活动签到接口
            sessionStorage.sign = '1'
            // 开始异步签到
            // start_sign();
            // 本地联调时开启，上线时注释
        });
        //点击话费券
        appUtils.preBindEvent($(_pageId + " #benefitsShop"), "li", function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            e.stopPropagation();
            e.preventDefault();
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            var info = JSON.parse($(this).find(".info").html());//兑换所需积分
            var shopStr_type = info.type;//1为话费券，2为京东券
            var shopStr_points = info.points;//所需积分
            var shopStr_state = info.state;//state-0正常1敬请期待
            if (shopStr_type == '1' || shopStr_type == '2') {//1为话费券，2为京东券
                if (shopStr_state == '1') {
                    layerUtils.iAlert("敬请期待!");
                    return;
                }
                if (parseFloat(usable_vol) < parseFloat(shopStr_points)) {//总积分小于兑换所需积分
                    let operationId = 'insufficientPoints'
                    layerUtils.iConfirm("您的积分不足!", () => {
                        // tools.recordEventData('1','close_integral','关闭积分不足弹窗');
                    }, () => {
                        // tools.recordEventData('1','benefitsBox','赚积分');
                        $(_pageId + " .type_content").hide();
                        $(_pageId + " #benefitsBox").show();
                    }, "确定", "赚积分",operationId);
                    return;
                }
                //缓存积分兑换信息
                let exchangeInfo = info;
                appUtils.setSStorageInfo("exchangeInfo", exchangeInfo);
                appUtils.pageInit(_pageCode, "vipBenefits/phoneVouchersConfirm", info);
            } else {
                layerUtils.iAlert("敬请期待!");
            }
        }, "click");
        //积分换话费
        appUtils.preBindEvent($(_pageId + " #benefitsShopType"), " #phone_securities", function (e) {
            tools.recordEventData('1','phone_securities','积分兑换话费');
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            $(_pageId + " #benefitsShop .placeholder").hide()
            $(_pageId + " #benefitsShop .benefitsCon").html("")
            $(_pageId + " #benefitsShop h3").html('积分兑换——换话费<span class="getback" cardVoucherType="1">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").show();
            reqFun108004();//商品查询
        });
        //积分换京东E卡
        appUtils.preBindEvent($(_pageId + " #benefitsShopType"), " #Jingdong_card", function (e) {
            tools.recordEventData('1','Jingdong_card','积分兑换京东E卡');
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            $(_pageId + " #benefitsShop .placeholder").hide()
            $(_pageId + " #benefitsShop .benefitsCon").html("")
            $(_pageId + " #benefitsShop h3").html('积分兑换——换京东E卡<span  class="getback" cardVoucherType="2">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").hide();
            reqFun108004();//商品查询
        });
        //话费和京东E卡返回
        appUtils.preBindEvent($(_pageId + " #benefitsShop"), " .getback", function (e) {
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShopType").show();
        });
        //签到活动，立即签到
        appUtils.preBindEvent($(_pageId + " #signIn"), " #signInBtn", function (e) {
            var sign_inform = $(this).attr('sign_inform');
            var btn_sign_inform = sign_inform ? JSON.parse(sign_inform) : '';
            chooseData = {
                share_template: btn_sign_inform.share_template ? btn_sign_inform.share_template : '',
                activity_id: btn_sign_inform.activity_id ? btn_sign_inform.activity_id : ''
            }
            
            // getPoints()
            // tools.recordEventData('1','signIn',$(this).text(),{activityId:chooseData.activity_id,shareTemplate:chooseData.share_template});
            if (!ut.hasBindCard(_pageCode)) return;
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            
            tools.clickPoint(_pageId, _pageCode, 'signInBtn', chooseData.activity_id);
            if (btn_sign_inform.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            }
            if (btn_sign_inform.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            if(btn_sign_inform.current_state == '5'){
                // var query_params = {};
                // query_params["registered_mobile"] = mobile;
                // query_params["tem_type"] = chooseData.share_template;
                // service.reqFun102012(query_params, function (data) {
                //     if (data.error_no == "0" && data.results && data.results[0]) {
                //         let time = data.results[0].sysdate
                //         start_sign(time);
                //     }
                // })
                // return start_sign();
                return help_sign();
            }
            if (btn_sign_inform.state == '1' && btn_sign_inform.current_state == '1') {
                if (!sign_type || sign_type == '1') { //普通签到流程
                    // $(_pageId + " #pop_layer #share_WeChat").hide();
                    // $(_pageId + " #pop_layer").show();
                    sign_pop_new('./images/share_sign.png');
                } else { //新版签到流程
                    sign_pop_new();
                }
            } else if (btn_sign_inform.state == '1' && (btn_sign_inform.current_state == '2' || btn_sign_inform.current_state == '3')) {
                var current_inform = $(_pageId + " #signIn .date li").eq(btn_sign_inform.current_days - 1).attr('data')
                if (!current_inform) { layerUtils.iMsg(-1, "活动ID未配置或url未配置！", 2) }
                current_inform = JSON.parse(current_inform)
                if (!current_inform.url || !current_inform.reward_activity_id) { layerUtils.iMsg(-1, "活动ID未配置或url未配置！", 2); }
                appUtils.pageInit(_pageCode, current_inform.url, { 'reward_activity_id': current_inform.reward_activity_id });
                return;
            } else if (btn_sign_inform.state == '1' && btn_sign_inform.current_state == '4') {
                layerUtils.iAlert("今日签到已完成，请明日再来!");
            }
        });
        // 签到成功
        appUtils.bindEvent($(_pageId + " .sign_succeed .sureBtn span"), function () {
            var url_btn = $(this).parent().attr('url');
            var reward_activity_id_btn = $(this).parent().attr('reward_activity_id');
            tools.recordEventData('1','sign_succeed',$(this).text(),{activityId:reward_activity_id_btn});
            $(_pageId + " .sign_succeed").hide().find('.index_info_tishi').html('');
            $(_pageId + " .sign_succeed .sureBtn").removeAttr("url").removeAttr("reward_activity_id").find('span').html('')
            if (validatorUtil.isNotEmpty(url_btn) && validatorUtil.isNotEmpty(reward_activity_id_btn)) {
                appUtils.pageInit(_pageCode, url_btn, { 'reward_activity_id': reward_activity_id_btn });
            }
            reqFun108001();
        })
        //新手活动
        appUtils.preBindEvent($(_pageId + " #novice"), " .noviceli a", function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            var novice_data = $(this).attr('data');
            var novice_num = $(this).attr('num');
            var novice_data_btn = novice_data ? JSON.parse(novice_data) : '';
            tools.recordEventData('1','novice',$(this).text(),{activity_id: novice_data_btn.activity_id ? novice_data_btn.activity_id : '',operation_description:novice_data_btn.activity_name});
            chooseData = {
                share_template: novice_data_btn.share_template ? novice_data_btn.share_template : '',
                activity_id: novice_data_btn.activity_id ? novice_data_btn.activity_id : ''
            }
            tools.clickPoint(_pageId, _pageCode, 'novice', chooseData.activity_id);
            // state：1:进行中 2:未开始 3:已结束
            if (novice_data_btn.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            } else if (novice_data_btn.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            // activity_sub_type:1:新手绑卡 2:新手首投 3:新手首邀
            // activity_state：1:初始 2:领积分 3:已完成
            tools.clickPoint(_pageId, _pageCode, 'novice', chooseData.activity_id);
            if (novice_data_btn.activity_state == '1') {
                if (novice_data_btn.activity_sub_type == '1') {
                    // 1新手绑卡
                    if (!ut.hasBindCard(_pageCode)) return;
                }
                if (novice_data_btn.activity_sub_type == '2') {
                    // 2:新手首投
                    appUtils.pageInit(_pageCode, "login/userIndexs", {});
                    return;
                }
                if (novice_data_btn.activity_sub_type == '3') {
                    // 3:新手首邀，跳到二维码页面
                    sessionStorage.activity_id = chooseData.activity_id;
                    sessionStorage.share_template = chooseData.share_template;
                    let source = '6';
                    return appUtils.pageInit(_pageCode, "vipBenefits/friendInvitation", { type: "shareTemplate", source: source });
                }
            } else if (novice_data_btn.activity_state == '2') {
                // 领积分
                getPoints(novice_data_btn.activity_id, novice_num);
                return;
            } else if (novice_data_btn.activity_state == '3') {
                layerUtils.iMsg(-1, "活动已完成！", 2);
                return;
            }
        })
        //新手活动-更多活动展示
        appUtils.preBindEvent($(_pageId + " #novice"), " .more_introduce_li a", function (e) {
            // tools.recordEventData('1','novice_more','更多福利');
            $(_pageId + " .more_introduce").show()
            tools.clickPoint(_pageId, _pageCode, 'novice_more');
        })
        //新手活动-更多活动展示
        appUtils.preBindEvent($(_pageId + " .more_introduce"), " .okBtn", function (e) {
            tools.recordEventData('1','more_introduce_okBtn',$(this).text());
            $(_pageId + " .more_introduce").hide()
        })
        //营销活动规则
        appUtils.preBindEvent($(_pageId + " #signIn"), " .sign_rules", function (e) {
            // tools.recordEventData('1','sign_rules',$(this).text());
            $(_pageId + " #activityRules").show().find('.sign').show()
        })
        appUtils.preBindEvent($(_pageId + " #saveMoney"), " .saveMoney_rules", function (e) {
            $(_pageId + " #activityRules").show().find('.saveMoney').show()
        })
        appUtils.preBindEvent($(_pageId + " #novice"), " .novice_rules", function (e) {
            // tools.recordEventData('1','novice_rules',$(this).text());
            $(_pageId + " #activityRules").show().find('.novice').show()
        })
        appUtils.preBindEvent($(_pageId + " #monthly"), " .month_rules", function (e) {
            // tools.recordEventData('1','month_rules',$(this).text());
            $(_pageId + " #activityRules").show().find('.month').show()
        })
        appUtils.preBindEvent($(_pageId + " #activityRules"), " .okBtn", function (e) {
            // tools.recordEventData('1','activityRules_okBtn','关闭活动规则');
            $(_pageId + " #activityRules").hide();
            $(_pageId + " #activityRules .rules").hide();
        })
        // 月度活动-去邀请
        appUtils.preBindEvent($(_pageId + " #monthly"), " .hdbtn .monthlyBtn", function (e) {
            if (!ut.hasBindCard(_pageCode)) return;
            if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
            localStorage.toClickTime = new Date().getTime();
            var month_inform = $(this).attr('month_inform');
            month_inform = month_inform ? JSON.parse(month_inform) : '';
            chooseData = {
                share_template: month_inform.share_template ? month_inform.share_template : '',
                activity_id: month_inform.id ? month_inform.id : ''
            }
            tools.recordEventData('1','monthlyBtn','月度活动' + $(this).text(),{activity_id: month_inform.id ? month_inform.id : ''});
            tools.clickPoint(_pageId, _pageCode, 'monthlyBtn', chooseData.activity_id);
            // state：1:进行中 2:未开始 3:已结束
            if (month_inform.state == '2') {
                layerUtils.iMsg(-1, "活动尚未开始！", 2);
                return;
            } else if (month_inform.state == '3') {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            // 3:新手首邀，跳到二维码页面
            sessionStorage.activity_id = chooseData.activity_id;
            sessionStorage.share_template = chooseData.share_template;
            let source = '7';
            appUtils.pageInit(_pageCode, "vipBenefits/friendInvitation", { type: "shareTemplate", source: source });
        })
        // 留言点赞功能
        appUtils.preBindEvent($(_pageId + " #activityComment .commentMain"), " .giveLike", function (e) {
            // tools.recordEventData('1','giveLike','点赞');
            var commentId = $(this).parents('li').attr('data');
            if (!xinflyflag) { return; }
            service.reqFun108029({ id: commentId }, (data) => {
                if (data.error_no == 0) {
                    xinflyflag = 0;
                    var results = data.results[0];
                    $(this).parents('li').find('.num').html(results.amount);
                    $(_pageId + " .commentMain .xinflybox").html('<span class="xinfly" disabled="none"></span>')
                    $(_pageId + " .commentMain .xinfly").css("opacity", 1).animate({
                        top: -20,
                        right: 5,
                        opacity: 0,
                    }, 500, function () {
                        $(_pageId + " .commentMain .xinfly").css({ "top": "0.2rem", "right": "0.15rem" })
                        $(_pageId + " .commentMain .xinflybox").html("")
                        xinflyflag = 1;
                    });
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            }, { "isShowWait": false })
        })
        // 营销评论
        appUtils.preBindEvent($(_pageId + " #activityComment"), ".import", function (e) {
            // tools.recordEventData('1','import','输入内容');
            var addbtnflag = $(_pageId + " #activityComment .import .btn").hasClass("addbtn")
            if (!addbtnflag) {
                $(_pageId + " #activityComment .import .leave").animate({
                    height: 80,
                }, 500, function () {
                    $(_pageId + " #activityComment .import .btn").show().addClass("addbtn")
                });
            }
        })
        // 营销评论
        appUtils.preBindEvent($(_pageId + " #activityComment .import"), " .btn", function (e) {
            e.stopPropagation();
            // tools.recordEventData('1','import_btn',$(this).text());
            var commentContent = $(_pageId + " .leave").val();
            if (validatorUtil.isEmpty(commentContent)) {
                layerUtils.iAlert("留言内容不能为空！");
                return;
            }
            if (commentContent.length > 500) {
                layerUtils.iAlert("留言内容请不要超过500个字！");
                return;
            }
            service.reqFun108028({ content: commentContent }, (data) => {
                if (data.error_no == 0) {
                    $(_pageId + " #activityComment .import .btn").hide().removeClass("addbtn");
                    $(_pageId + " #activityComment .import .leave").val('').css("height", "0.32rem");
                    layerUtils.iAlert("提交成功");
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
        // 周五活动
        appUtils.preBindEvent($(_pageId + " #benefitsBox"), " #friday_event", function (e) {
            // tools.recordEventData('1','friday_event','周五活动');
            if (friday_data.friday_activity_sub_type == "3") { // 分享活动图片赚积分
                appUtils.pageInit(_pageCode, "activity/fridayShareImg", {
                    activity_id: friday_data.activity_id
                });
            } else {
                appUtils.pageInit(_pageCode, "activity/fridayEvent", {
                    activity_id: friday_data.activity_id
                });
            }
            return;
        })
        // 邀请的人
        appUtils.bindEvent($(_pageId + " .people"), function (e) {
            // tools.recordEventData('1','people',$(this).text());
            if (!userInfo.bankAcct || userInfo.bankAcct == '') return layerUtils.iAlert("仅注册客户暂未开通");
            appUtils.pageInit(_pageCode, "inviteFriends/recommendedEarnings", {});
        })

         //攒钱计划投教任务
         appUtils.preBindEvent($(_pageId + " #saveMoney"), " .saveMoneyTaskLi a", function (e) {
            var saveMoneyTaskData = $(this).attr('data');
            var saveMoneyTaskNum = $(this).attr('num');
            saveMoneyTaskData = saveMoneyTaskData ? JSON.parse(saveMoneyTaskData) : {};
            tools.recordEventData('1','saveMoneyTaskLi',$(this).text(),{taskId:saveMoneyTaskData.task_id,activityId:saveMoneyTaskData.activity_id});
            dealTeachTaskJump(saveMoneyTaskData, saveMoneyTaskNum, 'saveMoney');
        })

        //月度投教任务
        appUtils.preBindEvent($(_pageId + " #monthly"), " .monthlyTaskLi a", function (e) {
            var noviceTaskData = $(this).attr('data');
            var noviceTaskNum = $(this).attr('num');
            noviceTaskData = noviceTaskData ? JSON.parse(noviceTaskData) : {};
            tools.recordEventData('1','monthlyTaskLi',$(this).text(),{taskId:noviceTaskData.task_id,activityId:noviceTaskData.activity_id});
            dealTeachTaskJump(noviceTaskData, noviceTaskNum, 'monthly');
        })
        //新手投教任务
        appUtils.preBindEvent($(_pageId + " #novice"), " .noviceTaskLi a", function (e) {
            var noviceTaskData = $(this).attr('data');
            var noviceTaskNum = $(this).attr('num');
            noviceTaskData = noviceTaskData ? JSON.parse(noviceTaskData) : {};
            tools.recordEventData('1','noviceTaskLi',$(this).text(),{taskId:noviceTaskData.task_id,activityId:noviceTaskData.activity_id});
            dealTeachTaskJump(noviceTaskData, noviceTaskNum, 'novice');
        })

        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            // tools.recordEventData('1','verify_btn','合格投资者确认');
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    $(_pageId + " .qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            // tools.recordEventData('1','cancel_btn','合格投资者取消');
            appUtils.clearSStorage("fund_code");
            appUtils.clearSStorage("productInfo");
            $(_pageId + " .qualifiedInvestor").hide();
        });

    }

    // 统一处理新手和月度投教任务
    async function dealTeachTaskJump(noviceTaskData, noviceTaskNum, type) {
        if (!ut.hasBindCard(_pageCode)) return;
        if (localStorage.toClickTime && (new Date().getTime() - localStorage.toClickTime < 500)) return
        localStorage.toClickTime = new Date().getTime();
        // tools.clickPoint(_pageId, _pageCode, 'novice', noviceTaskData.activity_id);
        // state：1:进行中 2:未开始 3:已结束
        if (noviceTaskData.state == '2') {
            layerUtils.iMsg(-1, "活动尚未开始！", 2);
            return;
        } else if (noviceTaskData.state == '3') {
            layerUtils.iMsg(-1, "活动已结束！", 2);
            return;
        }
        //task_type 5 跳转页面
        // task_type 1 阅读/观看  2分享 3阅读/观看并分享 4投资
        // jump_page_type 跳转页类型：1投教页2直播页3详情页4营销页

        if (noviceTaskData.task_state == '1') { // 未完成
            if (noviceTaskData.jump_page_type == '1') { // 投教
                noviceTaskData.title = noviceTaskData.teach_type;
                // appUtils.pageInit(_pageCode, "liveBroadcast/newsDetatil", noviceTaskData);
                appUtils.pageInit(_pageCode, "highVersion/articleDetails", noviceTaskData);
                return;
            } else if (noviceTaskData.jump_page_type == '2') { // 直播
                let uid = ut.getUserInf().custNo;
                let name = ut.getUserInf() ? ut.getUserInf().name : '';
                /** 姓名格式化处理 */
                let trueName = tools.format(name);
                let h5Url = noviceTaskData.live_room_h5_url
                let room_id = noviceTaskData.live_room_id
                if (!timer || timer == null) {
                    timer = setInterval(() => {
                        //保持心跳
                        service.reqFun112010({}, function (data) { }, { "isShowWait": false });
                    }, 10000);
                }
                if (noviceTaskData.task_type == '1') { // 仅观看
                    service.reqFun108050({ activity_id: noviceTaskData.activity_id, task_id: noviceTaskData.task_id }, function (data) {
                        if (data.error_no == '0') {
                            noviceTaskData.task_state = '2';//状态改为去领取
                            $(_pageId + ` #${type} .${type}Task li`).eq(noviceTaskNum).find(`.${type}TaskLi`).addClass('active').find('a').attr('data', JSON.stringify(noviceTaskData)).html('去领取');
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                } else {
                    appUtils.setSStorageInfo("vipBenefitsTaskData", noviceTaskData);
                }
                return tools.livePageTo(uid, trueName, h5Url, room_id, _pageCode);
            } else if (noviceTaskData.jump_page_type == '3' || noviceTaskData.jump_page_type == '4') { // 详情 营销
                let productInfo = '';
                if (noviceTaskData.prod_type == "1" || noviceTaskData.prod_type == "2") { // 公私募
                    productInfo = await getProdDetails(noviceTaskData.prod_code) //拿到模板数据
                    appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
                    appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                    appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
                    appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
                    if (noviceTaskData.jump_page_type == '3') { // 详情
                        if (noviceTaskData.prod_type == "1") { // 公募
                            tools.jumpDetailPage(_pageCode, productInfo.prod_sub_type, productInfo.prod_sub_type2, noviceTaskData)
                        } else if (noviceTaskData.prod_type == '2') { // 私募
                            if (!common.loginInter()) return;
                            if (!ut.hasBindCard(_pageCode)) return;
                            if (userAuthenticationStatus == '0') {
                                return $(_pageId + " .qualifiedInvestor").show();
                            }
                            tools.jumpPriDetailPage(_pageCode, productInfo.prod_sub_type2, noviceTaskData);
                        }
                    } else if (noviceTaskData.jump_page_type == '4') { // 营销
                        // 1公募2私募
                        if (noviceTaskData.prod_type == "1") {
                            tools.jumpMarketingPage(_pageCode, productInfo.prod_sub_type2, noviceTaskData);
                        } else if (noviceTaskData.prod_type == '2') {
                            if (!common.loginInter()) return;
                            if (!ut.hasBindCard(_pageCode)) return;
                            if (userAuthenticationStatus == '0') {
                                return $(_pageId + " .qualifiedInvestor").show();
                            }
                            tools.jumpMarketingPage(_pageCode, productInfo.prod_sub_type2, noviceTaskData);
                        }
                    }
                } else if (noviceTaskData.prod_type == "3") { // 投顾
                    productInfo = await getCombProdDetails(noviceTaskData.prod_code);
                    if (!common.loginInter()) return;
                    if (!ut.hasBindCard(_pageCode)) return;
                    var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                    var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
                    common.changeCardInter(function () {
                        if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                            let operationId = 'riskAssessment'
                            layerUtils.iConfirm("您还未进行风险测评", function () {
                                // tools.recordEventData('1','riskQuestion','风险测评');
                                appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                            }, function () {
                            }, "去测评", "取消",operationId);
                            return;
                        } else if (invalidFlag == '1') {
                            pageTo_evaluation()
                            return
                        }
                        //到期3个月后提示
                        if (perfect_info == 4) {
                            let operationId = 'replaceBankCard'
                            return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                                // tools.recordEventData('1','updateIDCard','更新身份证照片');
                                appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                            }, "取消", "更换",operationId);
                        }
                        appUtils.setSStorageInfo("fund_code", productInfo.comb_code);
                        if (noviceTaskData.jump_page_type == "3") { // 详情
                            appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
                            appUtils.pageInit(_pageCode, "combProduct/combProdDetail", noviceTaskData);
                        } else if (noviceTaskData.jump_page_type == '4') { // 营销页
                            appUtils.setSStorageInfo("combProductInfo", productInfo);   //存储分类一级内容
                            appUtils.pageInit(_pageCode, "combProduct/combProdMarketing", noviceTaskData);
                        }
                    });
                    return;

                } else if (noviceTaskData.prod_type == "4" || noviceTaskData.prod_type == "5") { // 系列
                    productInfo = await getSeriesDetails(noviceTaskData.prod_code);
                    localStorage.series_id = noviceTaskData.prod_code;
                    localStorage.financial_prod_type_pro = productInfo.financial_prod_type;
                    localStorage.zone_prod_type_pro = '';
                    localStorage.templateId_allBuy = productInfo.prod_propagate_temp;
                    appUtils.setSStorageInfo("series_name", productInfo.series_name);
                    appUtils.setSStorageInfo("series_info", productInfo);
                    if (noviceTaskData.prod_type == "4") {
                        noviceTaskData['buy_state'] = productInfo.buy_state;
                        appUtils.pageInit(_pageCode, "template/decentralizedPurchasing", noviceTaskData);
                    } else if (noviceTaskData.prod_type == "5") {
                        //特殊系列产品 子女
                        tools.setPageToUrl('template/seriesChildrenMarketing', '1');
                        if (!common.loginInter()) return;
                        if (!ut.hasBindCard(_pageCode)) return;
                        var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                        var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                        common.changeCardInter(function () {
                            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                                let operationId = 'riskAssessment';
                                layerUtils.iConfirm("您还未进行风险测评", function () {
                                    // tools.recordEventData('1','riskQuestion','风险测评');
                                    appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
                                }, function () {
                                }, "去测评", "取消",operationId);
                                return;
                            } else if (invalidFlag == '1') {
                                pageTo_evaluation()
                                return
                            }
                            //到期3个月后提示
                            if (perfect_info == 4) {
                                let operationId = 'replaceBankCard';
                                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                                    // tools.recordEventData('1','updateIDCard','更新身份照片');
                                    appUtils.pageInit(_pageCode, "account/uploadIDCard", {});
                                }, "取消", "更换",operationId);
                            }
                            appUtils.pageInit(_pageCode, "template/seriesChildrenMarketing", noviceTaskData);
                        });
                    }
                }
            } else if (noviceTaskData.jump_page_type == '5') { // 自定义
                
                if(noviceTaskData.task_type == '5'){  
                    jump_to(noviceTaskData);
                    return;
                }
                service.reqFun108050({
                    activity_id: noviceTaskData.activity_id,
                    task_id: noviceTaskData.task_id
                }, function (data) {
                    if (data.error_no == '0') {
                        jump_to(noviceTaskData);
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                })
            }else if (noviceTaskData.jump_page_type == "6") {//小程序
              	var url = noviceTaskData.link_url;
              	if (noviceTaskData.task_type == '1') { // 仅观看
              		pageTouchTimer = setTimeout(() => {
                    service.reqFun108050({ activity_id: noviceTaskData.activity_id, task_id: noviceTaskData.task_id }, function (data) {                      
                        if (data.error_no == '0') {
                            noviceTaskData.task_state = '2';//状态改为去领取
                            $(_pageId + ` #${type} .${type}Task li`).eq(noviceTaskNum).find(`.${type}TaskLi`).addClass('active').find('a').attr('data', JSON.stringify(noviceTaskData)).html('去领取');                            
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                    },6000);
                } else {
                    appUtils.setSStorageInfo("vipBenefitsTaskData", noviceTaskData);
                }
                return tools.jump_applet(url);
            }
        } else if (noviceTaskData.task_state == '2') {
            // 领积分
            service.reqFun108050({ activity_id: noviceTaskData.activity_id, task_id: noviceTaskData.task_id }, function (data) {
                if (data.error_no == 0) {
                    layerUtils.iMsg(-1, "积分领取成功！", 2);
                    noviceTaskData.task_state = '3';//状态改为已完成
                    $(_pageId + ` #${type} .${type}Task li`).eq(noviceTaskNum).find(`.${type}TaskLi`).removeClass('active').addClass('grey').find('a').attr('data', JSON.stringify(noviceTaskData)).html('已完成');
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
            return;
        } else if (noviceTaskData.task_state == '3') {
            layerUtils.iMsg(-1, "活动已完成！", 2);
            return;
        }
    }


    function jump_to(noviceTaskData){
        var url = noviceTaskData.link_url;
        // 是否是有效内链接
        if (noviceTaskData.link_type == "1" && url != "" && url != undefined && url != null) {
            if(url == 'chooseVerson=2'){
                //弹窗跳转新页面
                appUtils.setSStorageInfo("plan_type",'');
                // common.setLocalStorage("userChooseRefresh",'0');
                // common.setLocalStorage("userChooseVerson",'2');
                // common.setLocalStorage("snowballMarketShow",'1');
                appUtils.pageInit(_pageCode, "scene/index");
                return;
            }
            if (url.indexOf("?") > -1) {
                var skip_url = url.split("?")[0];
                var parameter = url.split("?")[1];
                var parameter_arr = parameter.split("&"); //各个参数放到数组里
                var urlInfo = {};//url的参数信息
                for (var i = 0; i < parameter_arr.length; i++) {
                    num = parameter_arr[i].indexOf("=");
                    if (num > 0) {
                        name = parameter_arr[i].substring(0, num);
                        value = parameter_arr[i].substr(num + 1);
                        urlInfo[name] = value;
                    }
                }
                if (url.indexOf("liveBroadcast/semihList") > -1) {
                    sessionStorage.room_type = urlInfo.code;   // 直播间类型
                    sessionStorage.msg_title = urlInfo.name;//直播间类型名称
                }
                if (url.indexOf("liveBroadcast/newsList") > -1) {
                    let code = urlInfo.code;
                    let name = urlInfo.name;
                    let data = { code, name };
                    sessionStorage.newsData = JSON.stringify(data);
                }
                appUtils.pageInit(_pageCode, skip_url, urlInfo);
            } else {
                appUtils.pageInit(_pageCode, url);
            }
        } else if (noviceTaskData.link_type == "2" && url != "" && url != undefined && url != null) {
            // 外链
            appUtils.pageInit(_pageCode, "guide/advertisement", {
                "url": url,
                "name": null,
                "description": null,
            });
        }
    }
    // 公募 私募产品详情
    async function getProdDetails(prodCode) {
        return new Promise(async (resolve) => {
            let data = {
                fund_code: prodCode
            }
            service.reqFun102113(data, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0])
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }

    //获取投顾产品详情
    async function getCombProdDetails(combCode) {
        return new Promise(async (resolve) => {
            let data = {
                comb_code: combCode
            }
            service.reqFun102165(data, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0])
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }

    // 获取系列详情
    async function getSeriesDetails(series_id) {
        return new Promise(async (resolve) => {
            let data = {
                series_id: series_id
            }
            service.reqFun102180(data, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0])
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }

    //去测评
    function pageTo_evaluation() {
        let operationId = 'riskAssessment';
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            // tools.recordEventData('1','riskQuestion','风险测评');
            appUtils.pageInit(_pageCode, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }
    // 其他页进入展示控制
    function reqFunShow() {
        var cardtType = appUtils.getSStorageInfo("cardtType");
        if (validatorUtil.isNotEmpty(cardtType)) {
            cardtTypeShow(cardtType)
            appUtils.clearSStorage("cardtType");
        }
        var flag = appUtils.getPageParam("flag");
        if (validatorUtil.isNotEmpty(flag)) {
            $(_pageId + " #benefitsShopType h3").html("积分兑换");
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShopType").show();
        }
    }
    //从积分兑换页面返回，展示信息控制
    function cardtTypeShow(cardtType) {
        if (validatorUtil.isNotEmpty(cardtType) && cardtType == '1') {
            //类型,1为话费券，2为京东券
            $(_pageId + " #benefitsShop h3").html('积分兑换——换话费<span class="getback" cardVoucherType="1">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").show();
            reqFun108004();//商品查询
        } else if (validatorUtil.isNotEmpty(cardtType) && cardtType == '2') {
            $(_pageId + " #benefitsShop h3").html('积分兑换——换京东E卡<span  class="getback" cardVoucherType="2">返回</span>');
            $(_pageId + " .type_content").hide();
            $(_pageId + " #benefitsShop").show();
            $(_pageId + " #benefitsShop .warm_prompt").hide();
            reqFun108004();//商品查询
        }
    }
    //领积分
    function getPoints(activity_id, novice_num) {
        service.reqFun108020({ "activity_id": activity_id }, (data) => {
            if (data.error_no == 0) {
                layerUtils.iMsg(-1, "积分领取成功！", 2);
                var novice_data = $(_pageId + " #novice ul li").eq(novice_num).find('.noviceli a').attr('data');
                novice_data = novice_data ? JSON.parse(novice_data) : '';
                novice_data.activity_state = '3';//状态改为已完成
                $(_pageId + " #novice ul li").eq(novice_num).find('.noviceli').removeClass('active').addClass('grey').find('a').attr('data', JSON.stringify(novice_data)).html('已完成');
                reqFun108001()
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //记录用户行为
    function setUser() {
        service.reqFun108009({ id: chooseData.activity_id, cust_no: cust_no }, (data) => {
            if (data.error_no == 0) {
            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    //会员总积分查询(108001)
    function reqFun108001() {
        if (appUtils.getPageParam("luckflag")) {
            appUtils.setSStorageInfo("luckflag", appUtils.getPageParam("luckflag"));
        }
        luckflag = appUtils.getSStorageInfo("luckflag");
        if (userInfo && !userInfo.bankAcct) {
            $(_pageId + ' #integral').html('0');
            return;
        }
        service.reqFun108001({ cust_no: cust_no, mobile: mobile }, (data) => {
            if (data.error_no == 0) {
                let results = data.results[0];
                usable_vol = results.usable_vol;
                threshold = results.threshold * 1;
                $(_pageId + ' .bg_white_yue_new .jifen span em').html(usable_vol);
                $(_pageId + ' .bg_white_yue_new .num span em').html(data.results[0].recommended_count ? data.results[0].recommended_count : '--');
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //会员活动图标类型(102080)
    function reqFun102080() {
        service.reqFun102080({}, (data) => {
            if (data.error_no == 0) {
                var results = data.results;
                var htmls = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        htmls += "<li "+ `operationType="1" operationId="${'vipBenefits_list_' +results[i].type}" operationName="${results[i].name}"`+" id='" + results[i].type + "_" + results[i].state + "'>" +
                            "<img src='" + global.oss_url + results[i].img + "' alt=' '>" +
                            "<p>" + results[i].name + "</p>" +
                            " </li>";
                        if (results[i].type == '2') {//2.赚积分
                            // $(_pageId + " #benefitsBox h3").html(results[i].name);
                        } else if (results[i].type == '3') {//3:积分兑换
                            $(_pageId + " #benefitsShopType h3").html(results[i].name);
                        }
                    }
                }
                $(_pageId + " .vipBenefits_list").html(htmls);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //营销评论查询接口(108027)
    function reqFun108027() {
        service.reqFun108027({}, (data) => {
            if (data.error_no == 0) {
                let results = data.results;
                if (results.length == "0") {
                    $(_pageId + ' #activityComment .commentMain').hide();
                    return;
                } else if (results.length == "1") {
                    var autoplay = false;
                    var pagination = "";
                    $(_pageId + ' #activityComment .commentMain').show();
                } else {
                    var autoplay = 7000;
                    var pagination = ".swiper-pagination";
                    $(_pageId + ' #activityComment .commentMain').show();
                    $(".swiper_scrollbar .swiper-pagination li").css({ "width": (1 / results.length) * 100 + "%" });
                }
                var htmls = "";
                for (var i = 0; i < results.length; i++) {
                    htmls += '<li class="li swiper-slide" data="' + results[i].id + '">' +
                        '                                <div class="title">' +
                        '                                    <h3>' + (results[i].nickName ? results[i].nickName : "匿名") + '</h3>' +
                        '                                    <div operationType="1" operationId="giveLike" operationName="点赞" class="giveLike">\n' +
                        '                                        <span class="num">' + (results[i].amount ? results[i].amount : "--") + '</span><span class="xin"></span>\n' +
                        '                                    </div>\n' +
                        '                                </div>\n' +
                        '                                <div class=" text"><div class="zi">' + (results[i].content ? results[i].content : "暂无数据") + '</div></div>\n' +
                        '                            </li>'
                }

                $(_pageId + ' #activityComment .commentMain #scroller_index2').html(htmls);
                $(_pageId + " .commentMain").show();
                // $(_pageId + ' #wrapper_index2 #scroller_index2').html(htmls);
                swipeInstance = new Swiper($(_pageId).find(".swiper-container"), {
                    pagination: pagination,
                    autoplay: autoplay,
                    paginationElement: "li",
                    bulletActiveClass: "check",
                    autoplayDisableOnInteraction: false,
                    loop: true,
                    onImagesReady: function () {
                        if (callback) callback();
                    },
                    beforeDestroy: function () {
                        swipeInstance = null
                    }
                });
                if (results.length > "1") {
                    // $(".swiper_scrollbar .swiper-pagination li").css({"width" : (1 / results.length) * 100 + "%"});
                    $(".swiper_scrollbar .swiper-pagination").css({ "width": "30%", "left": "35%" });
                    if (results.length < 6) {
                        $(".swiper_scrollbar .swiper-pagination").css({ "width": "10%", "left": "45%" });
                    }
                    if (results.length > 50) {
                        $(".swiper_scrollbar .swiper-pagination").css({ "width": "50%", "left": "25%" });
                    }
                }
            } else {
                layerUtils.iLoading(false);
                $(_pageId + " #xubox_shade1").hide();
                $(_pageId + " .commentMain").hide();
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //获取今日签到详情 卡片/文章 新功能
    function reqFun108046(sign_type) {
        service.reqFun108046({}, (data) => {
            if (data.error_no == 0) {
                //缓存新弹窗数据信息（文章/卡片弹窗）
                if (data.results && data.results[0] && data.results[0].signContentInfo) {
                    let res = data.results[0].signContentInfo;
                    res.map(item => {
                        if (sign_type == '2' && item.type == '1') {
                            new_pop_data = item;
                            //渲染分享图片
                            // setShareImg(new_pop_data);
                        }
                        if (sign_type == '3' && item.type == '2') new_pop_data = item;
                    })
                    // new_pop_data = (res[0].type == sign_type) ? res[0] : res[1];
                }
            }
        })
    }
    //渲染分享卡片图片
    function setShareImg(chooseData) {
        let source = '5'; //用户来源签到
        let bgImg = gconfig.global.oss_url + chooseData.share_img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #vipBenefits_qr_img").show();
                    $(_pageId + " #vipBenefits_code").hide();
                    $(_pageId + " #vipBenefits_qr_img").empty();
                    let qr_code_img = gconfig.global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #vipBenefits_qr_img").html(` <img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                    })
                } else {
                    $(_pageId + " #vipBenefits_qr_img").hide();
                    $(_pageId + " #vipBenefits_code").show();
                    qrcode(chooseData, source);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染短链二维码
    function qrcode(chooseData, source, shareType) {
        let mobile = ut.getUserInf().mobileWhole;
        mobile = common.desEncrypt("mobile", mobile);//加密
        // let long_url = "https://xhxts.sxfae.com/m/mall/index.html#!/drainage/userInvitationWx.html?mobile=" + mobile;
        let long_url = chooseData.link_url + '?mobile=' + mobile + '&source=' + source;
        $(_pageId + " #vipBenefits_code").empty();
        service.reqFun101073({ long_url: long_url }, function (res) {
            if (res.error_no == "0") {
                if (res.results != undefined && res.results.length > 0) {
                    var short_url = res.results[0].shortUrl;
                    require("../common/jquery.qrcode.min");
                    $(_pageId + " #vipBenefits_code").qrcode({
                        render: "canvas", //设置渲染方式，有table和canvas
                        text: short_url, //扫描二维码后自动跳向该链接
                        width: 70, //二维码的宽度
                        height: 70, //二维码的高度
                        imgWidth: 20,
                        imgHeight: 20,
                        src: '../mall/images/icon_app.png'
                    });
                    var father = document.querySelector("#content");
                    var _fatherHTML = document.querySelectorAll("#content .page");
                    var cur = document.querySelector("#vipBenefits_index");
                    father.innerHTML = "";
                    father.appendChild(cur);
                    let dom = document.querySelector(_pageId + " .shareImg")
                    html2canvas(dom, {
                        scale: 4
                    }).then(canvas => {
                        var base64 = canvas.toDataURL("image/png");
                        var _base64 = base64.split(",")[1];
                        father.innerHTML = "";
                        for (let i = 0; i < _fatherHTML.length; i++) {
                            father.appendChild(_fatherHTML[i]);
                        }
                        param = {
                            "funcNo": "50231",
                            "imgUrl": _base64,
                            "shareType": shareType,
                            "imageShare": "1",
                            "imageType":"base64"
                        }
                        setTimeout(() => {
                            require("external").callMessage(param);
                            $(_pageId + " #vip_pop_layer").hide();
                        }, 200);
                    })
                }
            } else {
                $(_pageId + " #vip_pop_layer").hide();
                layerUtils.iAlert(res.error_info);
            }
        }, { "isShowWait": false })
    }
    //营销活动查询所有活动信息
    function reqFun108019() {
        service.reqFun108019({}, (data) => {
            if (data.error_no == 0) {
                var results = data.results[0];
                // 渲染邀好友活动
                let ifpactivity = results.ifpactivity;
                if( userInfo.bankAcct && ifpactivity && ifpactivity.activity_id && ifpactivity.bannerList && ifpactivity.bannerList.length){
                    $(_pageId + ' .inviteFriends').show();
                    tools.guanggaoList({results:ifpactivity.bannerList,_pageId:_pageId})
                    $(_pageId + " .introduce").html(ifpactivity.introduce);
                }else{
                    $(_pageId + ' .inviteFriends').hide();
                }
                // 签到sign，novice新手，month月度：0:不展示 1:展示
                if (validatorUtil.isNotEmpty(results.sign)) {
                    $(_pageId + ' #signIn').show();
                    // 签到信息展示
                    sign_data = results.sign;
                    sign_type = sign_data.sign_type;//缓存今日签到类型，每日更新
                    link_open = sign_data.link_open;//普通签到是否需要打开
                    is_help = sign_data.is_help; //s签到是否需要帮助
                    help_img_url = sign_data.help_img_url;//分享签到图片
                    article_open = sign_data.article_open;//文章签到是否需要打开
                    if(sign_data.activity_words && sign_data.activity_words.length) {
                        $(_pageId + ' .activity_words').html(sign_data.activity_words)
                        $(_pageId + ' .activity_words').show()
                    }
                    reqFun108046(sign_type)
                    // if(sign_type && sign_type != '1') reqFun108046() //查询连续签到当日的卡片和文章内容
                    reqFunSign(sign_data);
                } else {
                    $(_pageId + ' #signIn').hide();
                }
                if (validatorUtil.isNotEmpty(results.saveMoney)) {
                    $(_pageId + ' #saveMoney').show();
                    // 攒钱计划信息展示
                    saveMoney_data = results.saveMoney;
                    reqFunSaveMony(saveMoney_data);
                    $(_pageId + ' #novice').hide();
                    $(_pageId + ' #monthly').hide();
                } else {
                    $(_pageId + ' #saveMoney').hide();
                    if (validatorUtil.isNotEmpty(results.novice)) {
                        $(_pageId + ' #novice').show();
                        // 新手信息展示
                        novice_data = results.novice;
                        reqFunNovice(novice_data);
                    } else {
                        $(_pageId + ' #novice').hide();
                    }
                    if (validatorUtil.isNotEmpty(results.month)) {
                        $(_pageId + ' #monthly').show();
                        month_data = results.month;
                        reqFunMonth(month_data);
                    } else {
                        $(_pageId + ' #monthly').hide();
                    }
                }
               
                if (validatorUtil.isEmpty(results.sign) && (validatorUtil.isEmpty(results.novice) || results.novice.novice_list.length == '0') && validatorUtil.isEmpty(results.month)) {
                    // $(_pageId + " #benefitsBox #placeholder").show();
                }
                // TODO:周五活动 activity_type == "18"
                if (validatorUtil.isNotEmpty(results.friday)) {
                    $(_pageId + ' #friday_event').show();
                    friday_data = results.friday;
                    $(_pageId + " #friday_event img").attr("src", global.oss_url + friday_data.oss_url);
                    appUtils.setSStorageInfo("friday_data", friday_data);
                } else {
                    $(_pageId + ' #friday_event').hide();
                }
            } else {
                layerUtils.iLoading(false);
                $(_pageId + ' .inviteFriends').hide();
                $(_pageId + " #xubox_shade1").hide();
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    // 签到活动展示
    function reqFunSign(sign_data2) {
        var htmls = "";
        // 签到周期cycle:单位:天,现定7天
        // continuity_days:已连续签到X天
        // current_days:当前为第几天
        sign_data = $.extend(sign_data, sign_data2);
        if (validatorUtil.isNotEmpty(sign_data.cycle) && sign_data.cycle > 0) {
            for (var i = 0; i < sign_data.cycle; i++) {
                var line = '<span class="line"></span>'
                var daysShow = i + 1;//展示天数
                if (sign_data.cycle == daysShow) { var line = '' }
                if (sign_data.continuity_days < daysShow) { var liClass = '' } else { var liClass = 'active' }
                var sign = "0";//是否是特殊签到，0为日常签到，1是特殊签到
                for (var j = 0; j < sign_data.reward_list.length; j++) {
                    if (sign_data.reward_list[j].days == daysShow) {
                        var reward_list_data = sign_data.reward_list[j]
                        sign = '1'
                        var reward = sign_data.reward_list[j].reward_name;
                    }
                }
                if (sign == '1') {
                    htmls += '<li class="' + liClass + '" data=' + JSON.stringify(reward_list_data) + '>' +
                        '                                <span class="point"><em></em></span>' +
                        '                                <span class="number">第<em>' + daysShow + '</em>天</span>'
                        + line +
                        '                                <div class="frame">' + reward + '</div>' +
                        '                                <span class="triangle"></span>';
                    '</li>';
                } else {
                    htmls += '<li class="' + liClass + '">' +
                        '                                <span class="point"><em></em></span>' +
                        '                                <span class="number">第<em>' + daysShow + '</em>天</span>'
                        + line +
                        '</li>';
                }
            }
            if (sign_data.cycle > 7) {
                $(_pageId + " #benefitsBox #signIn .date ul").html(htmls).css('justify-content', 'flex-start');
            } else {
                $(_pageId + " #benefitsBox #signIn .date ul").html(htmls).css('justify-content', 'center');
            }
            var signInBtnClass = sign_data.state == '1' ? '' : 'grey';
            $(_pageId + " #benefitsBox #signIn .signInBtn #signInBtn").html(current_state[sign_data.current_state]).addClass(signInBtnClass)
            $(_pageId + " #benefitsBox #signIn .signInBtn #signInBtn").attr('operationName',current_state[sign_data.current_state])
            sign_data.share_template = sign_data.share_template ? sign_data.share_template : '2';
            var sign_inform = {
                cycle: sign_data.cycle,//单位:天,现定7天
                current_state: sign_data.current_state,//当前签到状态，1:立即签到 2:参与红包雨 3:抽盲盒 4已完成
                state: sign_data.state,//活动状态:1:进行中 2:未开始 3:已结束
                current_days: sign_data.current_days,//当前为第几天
                share_template: sign_data.share_template ? sign_data.share_template : '2',//分享模板
                activity_id: sign_data.activity_id,
            }

            $(_pageId + " #benefitsBox #signIn .signInBtn #signInBtn").attr('sign_inform', JSON.stringify(sign_inform));
            $(_pageId + " #benefitsBox #signIn .signInBtn h4 span").html(sign_data.continuity_days)
            $(_pageId + " #benefitsBox #signIn .signInBtn #signInBtn").append(`<em style="display:none">${JSON.stringify(sign_inform)}</em>`)
            $(_pageId + " #activityRules .sign").html(sign_data.introduce ? sign_data.introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>')

        } else {
            $(_pageId + " #benefitsShop .placeholder").show();
            $(_pageId + " #benefitsShop .warm_prompt").hide();
        }
    }

   // 攒钱计划活动展示
    function reqFunSaveMony(saveMoney_data) {
        var saveMoney_htmls = "";
        if (saveMoney_data.saveMoneyList.length == '0') {
            $(_pageId + ' #saveMoney').hide();
            return;
        }
        if (saveMoney_data.saveMoneyList && saveMoney_data.saveMoneyList.length) {
            saveMoney_htmls += '<ul class="saveMonyTask">'
            for (var i = 0; i < saveMoney_data.saveMoneyList.length; i++) {
                saveMoney_data.saveMoneyList[i].activity_id += '', saveMoney_data.saveMoneyList[i].invest_teach_id += '';
                saveMoney_data.saveMoneyList[i].state = saveMoney_data.state;
                // status:1:未完成 2:领积分 3:已完成
                var saveMoney_class = '';
                var btn_name = '去完成';
                if (saveMoney_data.saveMoneyList[i].task_state == '2') {
                    saveMoney_class = 'active';
                    btn_name = '去领取'
                } else if (saveMoney_data.saveMoneyList[i].task_state == '3') {
                    saveMoney_class = 'grey'
                    btn_name = '已完成'
                }
                saveMoney_data.saveMoneyList[i].introduce = ''
                saveMoney_htmls += '<li class="">' +
                    '                                <div class="title">' +
                    '                                    <h4>' +saveMoney_data.saveMoneyList[i].task_title + '</h4>' +
                    '                                    <div class="hint">' + saveMoney_data.saveMoneyList[i].reward_desc + '</div>' +
                    '                                    </div>' +
                    '                                <div class="saveMoneyTaskLi hdbtn ' + saveMoney_class + '">' +
                    '                                    <a clickName="monthlyTask" num=' + i + ' data=' + JSON.stringify(saveMoney_data.saveMoneyList[i]) + '>' + btn_name + '</a>' +
                    '                                </div>' +
                    '                            </li>'

            }
            saveMoney_htmls += '</ul>'
        }
        $(_pageId + " #benefitsBox #saveMoney .noviceMain ul").html(saveMoney_htmls);
        $(_pageId + " #activityRules .saveMoney").html(saveMoney_data.introduce ? saveMoney_data.introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>')
    }

    // 新手活动展示
    function reqFunNovice(novice_data) {
        var novice_htmls = "";
        if (novice_data.novice_list.length == '0' && novice_data.novice_task_list.length == '0') {
            $(_pageId + ' #novice').hide();
            return;
        }
        if (novice_data.novice_list.length) {
            var introduce_novice = novice_data.novice_list[0].introduce ? novice_data.novice_list[0].introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>'
            for (var i = 0; i < novice_data.novice_list.length; i++) {
                if (novice_data.novice_list[i].is_activitypage_show && novice_data.novice_list[i].is_activitypage_show == '1') {
                    // activity_state1:初始 2:领积分 3:已完成
                    var novice_class = '';
                    var btn_name = novice_data.novice_list[i].btn_name;
                    if (novice_data.novice_list[i].activity_state == '2') {
                        novice_class = 'active';
                        btn_name = '领积分'
                    } else if (novice_data.novice_list[i].activity_state == '3') {
                        novice_class = 'grey'
                        btn_name = '已完成'
                    }
                    novice_data.novice_list[i].introduce = ''
                    novice_htmls += '<li class="">' +
                        '                                <div class="title">' +
                        '                                    <h4>' + novice_data.novice_list[i].activity_name + '</h4>' +
                        '                                    <div class="hint">' + novice_data.novice_list[i].activity_tips + '</div>' +
                        '                                    </div>' +
                        '                                <div class="noviceli hdbtn ' + novice_class + '">' +
                        '                                    <a clickName="novice" num=' + i + ' data=' + JSON.stringify(novice_data.novice_list[i]) + '>' + btn_name + '</a>' +
                        '                                </div>' +
                        '                            </li>'
                }

            }
        }
        if (novice_data.novice_task_list && novice_data.novice_task_list.length) {
            novice_htmls += '<ul class="noviceTask">'
            for (var i = 0; i < novice_data.novice_task_list.length; i++) {
                // if (novice_data.novice_task_list[i].task_type != '4') {
                novice_data.novice_task_list[i].activity_id += '', novice_data.novice_task_list[i].invest_teach_id += '';
                novice_data.novice_task_list[i].state = novice_data.mainActState;
                // status:1:未完成 2:领积分 3:已完成
                var novice_class = '';
                var btn_name = '去完成';
                if (novice_data.novice_task_list[i].task_state == '2') {
                    novice_class = 'active';
                    btn_name = '去领取'
                } else if (novice_data.novice_task_list[i].task_state == '3') {
                    novice_class = 'grey'
                    btn_name = '已完成'
                }
                novice_data.novice_task_list[i].introduce = ''
                novice_htmls += '<li class="">' +
                    '                                <div class="title">' +
                    '                                    <h4>' + novice_data.novice_task_list[i].task_title + '</h4>' +
                    '                                    <div class="hint">' + novice_data.novice_task_list[i].reward_desc + '</div>' +
                    '                                    </div>' +
                    '                                <div class="noviceTaskLi hdbtn ' + novice_class + '">' +
                    '                                    <a clickName="noviceTask" num=' + i + ' data=' + JSON.stringify(novice_data.novice_task_list[i]) + '>' + btn_name + '</a>' +
                    '                                </div>' +
                    '                            </li>'
                // }

            }
            novice_htmls += '</ul>'
        }

        if (novice_data.more_introduce) {
            novice_htmls += '<li class="more_introduce_li">' +
                '                                <div class="title">' +
                '                                    <h4>更多福利</h4>' +
                '                                </div>' +
                '                                <div class="hdbtn">' +
                '                                    <a operationType="1" operationId="novice_more" operationName="更多福利" clickName="novice_more" id="novice_more" data="more">立即查看</a>' +
                '                                </div>' +
                '                            </li>'
            tools.setDate(_pageId)
            $(_pageId + " .more_introduce .rulesDiv .content .content").html(novice_data.more_introduce)
        }
        $(_pageId + " #benefitsBox #novice .noviceMain ul").html(novice_htmls)
        $(_pageId + " #activityRules .novice").html(novice_data.introduce ? novice_data.introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>')
    }
    // 月度活动展示
    function reqFunMonth(month_data) {
        var month_htmls = "";
        if (month_data.month_activity_list.length == '0') {
            $(_pageId + ' #monthly').hide();
            return;
        }
        var month_inform = {
            id: month_data.id,//id
            state: month_data.state,//活动状态:1:进行中 2:未开始 3:已结束
            share_template: month_data.share_template,//分享模板
        }
        month_data.month_activity_list = [];//去除月度活动展示
        for (var i = 0; i < month_data.month_activity_list.length; i++) {
            month_htmls += '<li>' +
                '                                <div class="title">' +
                '                                    <h4>' + month_data.month_activity_list[i].money + '元</h4>' +
                '                                    <div class="hint">每邀请' + month_data.month_activity_list[i].num + '位好友首投（晋金宝除外）</div>' +
                '                                </div>' +
                '                                 <div class="hdbtn">' +
                '                                    <a clickName="monthlyBtn" class="monthlyBtn" month_inform=' + JSON.stringify(month_inform) + '>去邀请</a>' +
                '                                 </div>' +
                '                            </li>'
        }
        // month_data.month_task_list = arr
        if (month_data.month_task_list && month_data.month_task_list.length) {
            month_htmls += '<ul class="monthlyTask">'
            for (var i = 0; i < month_data.month_task_list.length; i++) {
                // if (month_data.month_task_list[i].task_type != '4') {
                month_data.month_task_list[i].activity_id += '', month_data.month_task_list[i].invest_teach_id += '';
                month_data.month_task_list[i].state = month_data.state;
                // status:1:未完成 2:领积分 3:已完成
                var month_class = '';
                var btn_name = '去完成';
                if (month_data.month_task_list[i].task_state == '2') {
                    month_class = 'active';
                    btn_name = '去领取'
                } else if (month_data.month_task_list[i].task_state == '3') {
                    month_class = 'grey'
                    btn_name = '已完成'
                }
                month_data.month_task_list[i].introduce = ''
                month_htmls += '<li class="">' +
                    '                                <div class="title">' +
                    '                                    <h4>' + month_data.month_task_list[i].task_title.trim() + '</h4>' +
                    '                                    <div class="hint">' + month_data.month_task_list[i].reward_desc.trim() + '</div>' +
                    '                                    </div>' +
                    '                                <div class="monthlyTaskLi hdbtn ' + month_class + '">' +
                    '                                    <a clickName="monthlyTask" num=' + i + ' data=' + JSON.stringify(month_data.month_task_list[i]) + '>' + btn_name + '</a>' +
                    '                                </div>' +
                    '                            </li>'
                // }

            }
            month_htmls += '</ul>'
        }
        $(_pageId + " #benefitsBox #monthly .noviceMain ul").html(month_htmls);
        $(_pageId + " #benefitsBox #monthly .monthlyBtn h4 .count").html(month_data.count ? month_data.count : '--');
        $(_pageId + " #benefitsBox #monthly .monthlyBtn h4 .money").html(month_data.money ? month_data.money : '--');
        $(_pageId + " #activityRules .month").html(month_data.introduce ? month_data.introduce : '<h5 style="text-align: center;font-weight: normal;">暂无数据</h5>')
    }

    //商品查询-108004
    function reqFun108004() {
        cardVoucherType = $(_pageId + " #benefitsShop h3 .getback").attr('cardvouchertype')
        service.reqFun108004({ exchange_type: cardVoucherType }, (data) => {
            if (data.error_no == 0) {
                var results = data.results;
                var htmls = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        htmls += '<li class="benefitsList">' +
                            '<div class="info" style="display: none">' + JSON.stringify(results[i]) + '</div>' +
                            '<div class="phoneSecurities">' +
                            '<img src="' + global.oss_url + results[i].img + '" alt="">' +
                            '</div>' +
                            '<p>' + results[i].name + '</p>' +
                            '<h4><span class="jifen">' + results[i].points + '</span>积分</h4>' +
                            '</li>';
                    }
                } else {
                    $(_pageId + " #benefitsShop .placeholder").show();
                    $(_pageId + " #benefitsShop .warm_prompt").hide();
                }
                $(_pageId + " #benefitsShop .benefitsCon").html(htmls);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //销毁事件
    function destroy() {
		$(_pageId + " .banner_box").hide();
		$(_pageId + " #scroller_index2").html('');
        $(_pageId + ' .signIn_words').hide();
        $(_pageId + ' .inviteFriends').hide();
        tools.recordEventData('4','destroy','页面销毁');
        sign_type = null;
        stopTime = null;
        $(_pageId + " #vip_pop_layer").hide();
        // $(__pageId + " .activityDialog").remove();
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " .benefitsCon").html("");
        $(_pageId + ' #integral').html("--");
        $(_pageId + " .vipBenefits_list").html("");
        $(_pageId + " #benefitsShop .placeholder").hide();
        $(_pageId + " #benefitsBox #signIn .date ul").html();
        $(_pageId + " .sign_succeed .index_info_tishi").html();
        $(_pageId + " #benefitsBox #novice .noviceMain ul").html();
        $(_pageId + " #benefitsBox #monthly .noviceMain ul").html();
        clearTimeout(pageTouchTimer);
        $(_pageId + " #benefitsBox #placeholder").hide();
        usable_vol = "";
        sessionStorage.sign = '';
        $(_pageId + " .commentMain").hide();
        $(_pageId + " #activityComment .import .btn").hide().removeClass("addbtn");
        $(_pageId + " #activityComment .import .leave").val('').css("height", "0.32rem");
        $(_pageId + " #vipBenefits_qr_img").hide();
        $(_pageId + " #vipBenefits_code").show();
        //进页面首页清除定时器
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
        $(_pageId + " .qualifiedInvestor").hide();
    }
    /*
     * 返回
     */
    function pageBack() {
        //进页面首页清除定时器
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
        if (luckflag == 'fundLuckdrawnew') {
            appUtils.pageInit(_pageCode, "login/userIndexs");
            appUtils.clearSStorage("luckflag");
        } else if (luckflag == 'dargonYearDraw') {
            let url = appUtils.getSStorageInfo("front_pageUrl")
            if (url.indexOf("?") > -1) {
                var skip_url = url.split("?")[0];
                var parameter = url.split("?")[1];
                var parameter_arr = parameter.split("&"); //各个参数放到数组里
                var urlInfo = {};//url的参数信息
                for (var i = 0; i < parameter_arr.length; i++) {
                    num = parameter_arr[i].indexOf("=");
                    if (num > 0) {
                        name = parameter_arr[i].substring(0, num);
                        value = parameter_arr[i].substr(num + 1);
                        urlInfo[name] = value;
                    }
                }
                appUtils.pageInit(_pageCode, skip_url, urlInfo);
            }
            appUtils.clearSStorage("luckflag");
            appUtils.clearSStorage("front_pageUrl");
        } else {
            appUtils.pageBack();
        };
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "reqFunSign": reqFunSign
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});