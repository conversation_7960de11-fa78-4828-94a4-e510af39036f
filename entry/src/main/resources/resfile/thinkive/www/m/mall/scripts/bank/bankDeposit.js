// 银行存换-持仓列表
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        VIscroll = require("vIscroll"),
        common = require("common"),
        vIscroll = {"scroll": null, "_init": false},
        service = require("mobileService"),
        _pageCode = "bank/bankDeposit",
        _pageId = "#bank_bankDeposit ";
    var tools = require("../common/tools");
    var currentPage;
    var totalPages;
    var payIntTypeName = {
        "Y": "年",
        "M": "月",
        "D": "天"
    }
    function init() {
        getOpenAccountInfo();
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }


    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //电子银行账户
        appUtils.bindEvent($(_pageId + " .top"), function () {
            appUtils.setSStorageInfo("bankEntrytype", "");
            appUtils.pageInit(_pageCode, "bank/bankElectron");
        });
        //持仓详情
        appUtils.bindEvent($(_pageId + " .list_box .item"), function () {
            appUtils.pageInit(_pageCode, "bank/holdDetail");
        });
        //去逛逛
        appUtils.bindEvent($(_pageId + " .go_stroll"), function () {
            appUtils.pageInit(_pageCode, "bank/bankList");
        });
        //交易记录
        appUtils.preBindEvent($(_pageId + " .list_box"), ".item .records", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).parents(".item").find(".productInfo").text());
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (productInfo.brnd_sris == "SD002") {
                appUtils.pageInit(_pageCode, "bank/prodTransSD2");
            } else if (productInfo.brnd_sris == "SD001") {
                appUtils.pageInit(_pageCode, "bank/prodTransSD1");
            }
        }, 'click');
        //持有列表
        appUtils.bindEvent($(_pageId + " #myHold"), function () {
            if ($(this).hasClass("current")) return;
            $(_pageId + " .tab_box a").removeClass("current").filter(this).addClass("current");
            $(_pageId + " .holdList").show();
            // $(_pageId + " .list_box").html("")
            $(_pageId + " #v_container_productList").hide();
            // getHoldList();
        }, 'click');
        //转让列表
        appUtils.bindEvent($(_pageId + " #myTransfer"), function (e) {
            if ($(this).hasClass("current")) return;
            $(_pageId + " .holdList").hide();
            $(_pageId + " #v_container_productList").show();
            $(_pageId + " .tab_box a").removeClass("current").filter(this).addClass("current");
            currentPage = 1;
            $(_pageId + " .visc_pullDown").hide();
            getTransferList(currentPage, false);
        }, 'click');
        //我的持有-支取
        appUtils.preBindEvent($(_pageId + " .list_box"), ".item .draw", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).parents(".item").find(".productInfo").text());
            productInfo["isBuy"] = "0";
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.pageInit(_pageCode, "bank/draw");
        }, 'click');
        //我的持有-转让
        appUtils.preBindEvent($(_pageId + " .list_box"), ".item .transfer", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).parents(".item").find(".productInfo").text());
            if (productInfo.trans_amt <= 0) {
                layerUtils.iAlert("该产品可转让份额为0");
                return;
            }
            productInfo["isBuy"] = "0";
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.pageInit(_pageCode, "bank/transferSell");
        }, 'click');
        //我的转让-产品撤单
        appUtils.preBindEvent($(_pageId + " .list_box"), ".item .cancelOrder", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).parents(".item").find(".productInfo").text());
            productInfo["isBuy"] = "0";
            appUtils.setSStorageInfo("productInfo", productInfo);
            appUtils.pageInit(_pageCode, "bank/cancelOrder");
        }, 'click');
        //我的转让-提前支取弹框提示
        appUtils.preBindEvent($(_pageId + " .list_box"), ".item .earlyDraw", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).parents(".item").find(".productInfo").text());
            productInfo["isBuy"] = "0";
            appUtils.setSStorageInfo("productInfo", productInfo);
            service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
                if (data.error_no == "0") {
                    var results = data.results[0];
                    if (results.acct_status == "C") {
                        service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                        })
                        var routerList = appUtils.getSStorageInfo("routerList");
                        routerList.splice(-1);
                        appUtils.setSStorageInfo("routerList", routerList);
                        appUtils.pageInit(_pageCode, "bank/faceRecognition");
                        return;
                    }
                    $(_pageId + " .bank_act_int").html(results.bank_act_int);
                } else {
                    $(_pageId + " .bank_act_int").html("0.35%");
                    layerUtils.iAlert(data.error_info);
                }
                $(_pageId + " .earlyTip").show();
            })
        }, 'click');
        //我的转让-提前支取
        appUtils.bindEvent($(_pageId + " #earlyDraw"), function (e) {
            appUtils.pageInit(_pageCode, "bank/earlyDraw");
        }, 'click');
        //我的转让-提前支取-取现
        appUtils.bindEvent($(_pageId + " #cancel"), function (e) {
            $(_pageId + " .dmd_int_date").html("");
            $(_pageId + " .earlyTip").hide();
        }, 'click');
        //我的持有-打开专享提示
        appUtils.preBindEvent($(_pageId + " .list_box"), ".item .exclusiveTip", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var productInfo = JSON.parse($(this).parents(".item").find(".productInfo").text());
            $(_pageId + " .pay_int_hz").html(productInfo.pay_int_hz + payIntTypeName[productInfo.pay_int_type]);
            $(_pageId + " .increase_term").html(productInfo.increase_term);
            $(_pageId + " .exclusive").show();
        }, 'click');
        //我的转让-提前支取
        appUtils.bindEvent($(_pageId + " .sure"), function (e) {
            $(_pageId + " .exclusive").hide();
        }, 'click');

    }

    //查询开户信息
    function getOpenAccountInfo() {
        service.reqFun151110({bank_channel_code: ""}, function (data) {
            if (data.error_no == "0") {
                var bank_flag = data.results[0].bank_flag;
                if (bank_flag == "1") { //已开户
                    $(_pageId + ".bankElectronInfo").show();
                    getMyAsset();
                    $(_pageId + " .holdList").show();
                    $(_pageId + " .have_data").show();
                    $(_pageId + " .no_data").hide();
                    getHoldList();
                } else { //未开户
                    layerUtils.iLoading(false);
                    $(_pageId + " .no_data").show().css({top: "0.6rem"});
                    $(_pageId + ".bankElectronInfo").hide();
                }
            } else {
                layerUtils.iLoading(false);
                // $(_pageId + " .no_data").show().css({top: "0.6rem"});
                layerUtils.iAlert(data.error_info);
            }
        }, {isLastReq: false})
    }

    //查询用户银行可用余额
    function getMyAsset() {
        service.reqFun151107({bank_channel_code: ""}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                var total_yue = results.total_yue;
                $(_pageId + " .total_yue").text(tools.fmoney(total_yue) + "元");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, {isGlobal: true});
    }

    //查询用户持仓列表
    function getHoldList() {
        service.reqFun151102({}, function (data) {
            if (data.error_no == "0") {
                var user_asst_post_list = data.results[0].user_asst_post_list;
                if (user_asst_post_list && user_asst_post_list.length == 0) { //无持仓
                    var str = "<div class='nodata'>暂无数据</div>"
                    // $(_pageId + " .no_data").show().css({top: "1.8rem"});
                    // $(_pageId + " .have_data").hide();
                } else if (user_asst_post_list) { //有持仓
                    // $(_pageId + " .no_data").hide();
                    // $(_pageId + " .have_data").show();
                    var str = "";
                    for (var i = 0; i < user_asst_post_list.length; i++) {
                        var brnd_sris = user_asst_post_list[i].brnd_sris; //SD001 众力存  SD002众惠存
                        var content1 = "";
                        var content2 = "";
                        var content3 = "";
                        var btnTxt = "";
                        var exclusiveStartStr ="";
                        var exclusiveEndStr ="";
                        var rate = tools.fmoney(user_asst_post_list[i].base_lnt_rate);
                        if(user_asst_post_list[i].url) { //增加新手专享   10点专享标识
                            exclusiveStartStr  = '<span style="margin-left: 0.05rem"><img src="' + require("gconfig").global.oss_url +  user_asst_post_list[i].url + '" style="height: 0.2rem;vertical-align: sub;"></span>';
                            exclusiveEndStr = '<span class="title_icon exclusiveTip" style="position: absolute;margin-right: 0;right: 0.05rem;font-size: 0"><img src="./images/thirty_tips.png" style="line-height: 1"></span>';
                            rate = tools.fmoney(user_asst_post_list[i].rate);
                        }

                        if (brnd_sris == "SD001") { //众力存产品
                            content1 = '<div class="item_content">' +
                                '<div class="content_left">' +
                                '<span>持有金额(元)：</span><span class="text_red">' + tools.fmoney(user_asst_post_list[i].hold_amt) + '</span>\n' +
                                '</div>' +
                                '<div class="content_right">' +
                                '<span>期限：</span><span>' + user_asst_post_list[i].prd_trem + payIntTypeName[user_asst_post_list[i].term_type] + '</span>' +
                                '</div>' +
                                '</div>';
                            content2 = '<div class="item_content">' +
                                '<div class="content_left">' +
                                '<span>认购日期：</span><span>' + tools.ftime(user_asst_post_list[i].order_deal_date) + '</span>\n' +
                                '</div>' +
                                '<div class="content_right" style="width: 60%">' +
                                '<span>持有时间：</span><span>' + user_asst_post_list[i].order_save_date + '天</span>' +
                                '</div>' +
                                '</div>';
                            content3 = '<div class="item_content">' +
                                '<div class="content_left">' +
                                '<span>到期日期：</span><span>' + tools.ftime(user_asst_post_list[i].exprd_date) + '</span>\n' +
                                '</div>' +
                                '</div>';
                            btnTxt = '<span class="content_btn transfer">立即转让</span>';
                        } else {
                            content1 = '<div class="item_content">' +
                                '<div class="content_left">' +
                                '<span>持有金额(元)：</span><span>' + tools.fmoney(user_asst_post_list[i].hold_amt) + '</span>\n' +
                                '</div>' +
                                '<div class="content_right">' +
                                '<span>付息周期：</span><span>' + user_asst_post_list[i].pay_int_hz + payIntTypeName[user_asst_post_list[i].pay_int_type] + '</span>' +
                                '</div>' +
                                '</div>';
                            content2 = '<div class="item_content">' +
                                '<div class="content_left">' +
                                '<span>认购日期：</span><span>' + tools.ftime(user_asst_post_list[i].order_deal_date) + '</span>\n' +
                                '</div>' +
                                '<div class="content_right" style="width: 75%">' +
                                '<span>已派发收益(元)：</span><span>' + tools.fmoney(user_asst_post_list[i].ordr_totexd_amt) + '</span>' +
                                '</div>' +
                                '</div>';
                            content3 = '<div class="item_content">' +
                                '<div class="content_left">' +
                                '<span>下一派息日：</span><span>' + tools.ftime(user_asst_post_list[i].cyc_enddate) + '</span>\n' +
                                '</div>' +
                                '</div>';
                            btnTxt = '<span class="content_btn draw">提前支取</span>'
                        }
                        str += '<div class="item">\n' +
                            '<span class="productInfo" style="display: none">' + JSON.stringify(user_asst_post_list[i]) + '</span>' +
                            '                        <div class="item_title">\n' +
                            '                            <span class="title_icon"><img src="' + tools.judgeBankImg(user_asst_post_list[i].bank_channel_code).icon + '" alt=""></span>\n' +
                            '                            <span class="title_text">' + user_asst_post_list[i].prod_name + '</span>\n' +
                            exclusiveStartStr + exclusiveEndStr +
                            '                            <span class="title_num text_red" style="margin-right: 0.05rem">' + rate + '%</span>\n' +
                            '                        </div>\n' +
                            '                        <div class="item_content_box">\n' +
                            content1 + content2 + content3 +
                            '                            <div class="item_content">\n' +
                            '                                <div class="content_left">\n' +
                            '                                    <span class="records blue">交易记录</span>\n' +
                            '                                </div>\n' +
                            '                                <div class="content_right">\n' + btnTxt +
                            '                                </div>\n' +
                            '                            </div>\n' +
                            '                        </div>\n' +
                            '                    </div>'
                    }

                }
                $(_pageId + " .holdList").html(str);
                pageScrollInit();
            } else {
                layerUtils.iAlert(data.error_info)
            }
        }, {isGlobal: true})
    }

    //查询用户转让列表
    function getTransferList(list_page, isAppendFlag) {
        // $(_pageId + " .no_data").hide();
        var param = {
            page: list_page,
            num_per_page: "5",
        }
        service.reqFun151119(param, function (data) {
            if (data.error_no == "0") {
                var my_hang_list = data.results[0].my_hang_list;
                totalPages = data.results[0].total_pages;//总页数
                var str = "";
                if (my_hang_list.length > 0) { //有持仓
                    var content1 = "";
                    var content2 = "";
                    var content3 = "";
                    for (var i = 0; i < my_hang_list.length; i++) {
                        var status = my_hang_list[i].status; //挂单状态  1:挂单中；2：已成交 3:取消挂单
                        var hang_list_date = my_hang_list[i].hang_list_date; //挂单日期
                        var remind_term = my_hang_list[i].remind_term; //剩余期限
                        var hang_list_amt = my_hang_list[i].hang_list_amt; //挂单金额
                        var deal_date = my_hang_list[i].deal_date; //成交日期
                        var exprd_date = my_hang_list[i].exprd_date; //到期日期
                        var save_dt = my_hang_list[i].save_dt; //持有天数
                        var order_trade_date = my_hang_list[i].order_trade_date; //认购日期
                        var total_amt = my_hang_list[i].total_amt; //到账金额
                        if (status == "1") { //挂单中
                            content1 = '<div class="item_content">\n' +
                                '<div class="content_left" style="width: 60%">\n' +
                                '<span>认购日期：</span><span>' + tools.ftime(order_trade_date) + '</span>\n' +
                                '</div>\n' +
                                '<div class="content_right" style="width: 70%">\n' +
                                '<span>持有时间：</span><span>' + save_dt + '天</span>\n' +
                                '</div>\n' +
                                '</div>';
                            content2 = '<div class="item_content">\n' +
                                '<div class="content_left" style="width: 60%">\n' +
                                '<span>到期日期：</span><span>' + tools.ftime(exprd_date) + '</span>\n' +
                                '</div>\n' +
                                '<div class="content_right" style="width: 70%">\n' +
                                '<span>剩余时间：</span><span>' + remind_term + '天</span>\n' +
                                '</div>\n' +
                                '</div>';
                            content3 = '<div class="item_content">\n' +
                                '<div class="content_left" style="width: 20%">\n' +
                                '</div>\n' +
                                '<div class="content_right" style="width: 80%">\n' +
                                '<span><span class="text_red" style="margin-right: 0.1rem">转让中</span><span class="cancelOrder content_btn">撤单</span><span class="content_btn earlyDraw">提前支取</span></span>\n' +
                                '</div>\n' +
                                '</div>';
                        } else if (status == "2") { //挂单成功
                            content1 = '<div class="item_content">\n' +
                                '<div class="content_left" style="width: 60%">\n' +
                                '<span>委托日期：</span><span>' + tools.ftime(hang_list_date) + '</span>\n' +
                                '</div>\n' +
                                '<div class="content_right" style="width: 70%">\n' +
                                '<span>持有时间：</span><span>' + save_dt + '天</span>\n' +
                                '</div>\n' +
                                '</div>';
                            content2 = '<div class="item_content">\n' +
                                '<div class="content_left" style="width: 60%">\n' +
                                '<span>成交日期：</span><span>' + tools.ftime(deal_date) + '</span>\n' +
                                '</div>\n' +
                                '<div class="content_right" style="width: 70%">\n' +
                                '<span>到账金额：</span><span>' + tools.fmoney(total_amt) + '</span>\n' +
                                '</div>\n' +
                                '</div>';
                            content3 = '<div class="item_content">\n' +
                                '<div class="content_left" style="width: 20%">\n' +
                                '</div>\n' +
                                '<div class="content_right" style="width: 80%">\n' +
                                '<span class="text_red">转让成功</span>\n' +
                                '</div>\n' +
                                '</div>';
                        }
                        str += '<div class="item">\n' +
                            '<span class="productInfo" style="display: none">' + JSON.stringify(my_hang_list[i]) + '</span>' +
                            '                        <div class="item_title">\n' +
                            '                            <span class="title_icon"><img src="' + tools.judgeBankImg(my_hang_list[i].bank_channel_code).icon + '" alt=""></span>\n' +
                            '                            <span class="title_text">' + my_hang_list[i].prod_name + '</span>\n' +
                            '                            <span class="title_num text_red">' + tools.fmoney(my_hang_list[i].bas_int_rate) + '%</span>\n' +
                            '                        </div>\n' +
                            '                        <div class="item_content_box">\n' +
                            '                            <div class="item_content">\n' +
                            '                                 <div class="content_left" style="width: 80%">' +
                            '                                      <span>转让金额(元)：</span><span class="text_red">' + tools.fmoney(hang_list_amt) + '</span>' +
                            '                                 </div>' +
                            '                                 <div class="content_right" style="width: 20%">\n' +
                            '                                </div>\n' +
                            '                            </div>' +
                            content1 +
                            content2 +
                            content3 +
                            '                        </div>\n' +
                            '                    </div>'
                    }
                }

                if (currentPage == 1 && my_hang_list.length == 0) {
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                    str = "<div class='nodata'>暂无数据</div>";
                }
                if (!isAppendFlag) {
                    $(_pageId + " .transferList").html("");
                }
                $(_pageId + " .visc_pullDown").slideUp(100);
                if (isAppendFlag) {
                    $(_pageId + " .transferList").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .transferList").html(str);
                }
                pageScrollInit();
            } else {
                layerUtils.iAlert(data.error_info)
            }
        }, {isGlobal: true})
    }

    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44 - 240;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    currentPage = 1;
                    if ($(_pageId + " #myTransfer").hasClass("current")) {
                        $(_pageId + " .visc_pullDown").show();
                        getTransferList(currentPage, false);
                        $(_pageId + " .visc_pullUp").show();
                        $(_pageId + " .visc_pullUpIcon").hide();
                        $(_pageId + " .visc_pullUpDiv").hide();
                    }

                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        currentPage += 1;
                        if ($(_pageId + " #myTransfer").hasClass("current")) {
                            getTransferList(currentPage, true);
                        }
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }

    function destroy() {
        $(_pageId + " .no_data").hide();
        $(_pageId + " .have_data").hide();
        $(_pageId + " .bankElectronInfo").hide();
        $(_pageId + " .total_yue").text("--");
        $(_pageId + " .list_box").html("");
        $(_pageId + " .dmd_int_date").html("");
        $(_pageId + " .bank_act_int").html("");
        $(_pageId + " .alert").hide();
        $(_pageId + " .holdList").hide();
        $(_pageId + " #v_container_productList").hide();
        $(_pageId + " .tab_box a").removeClass("current").eq(0).addClass("current");
        currentPage = "1";
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
