define(function(require, exports, module) {
	var layerUtils = require("layerUtils");
    var tools = require("../scripts/common/tools");
	function  selectDate(id, type , pageId,minData){
    // console.log(type,111)
	var pageId = pageId;
    var selectDateDom = $(id);
    var showDateDom = $(id);
    	// 初始化时间
    var now = new Date();
    var nowYear
    var nowMonth
    var nowDate
    if(minData === ''){    //定投计算器
        nowYear = (now.getFullYear()) - 1;
        nowMonth = parseInt(now.getMonth() + 1);
        nowDate = now.getDate();
    }else if(minData){
        nowYear = minData.slice(0,4)*1;
        nowMonth = minData.slice(4,6)*1;
        nowDate = minData.slice(6,8)*1;
    }else{
        nowYear = now.getFullYear();
        nowMonth = parseInt(now.getMonth() + 1);
        nowDate = now.getDate();
    }
    if(minData && minData != '' && type == '1'){
        showDateDom.attr('data-year', now.getFullYear());
        showDateDom.attr('data-month', parseInt(now.getMonth() + 1));
        showDateDom.attr('data-date', now.getDate());
        // return;
    }else if(type == '1'){
        showDateDom.attr('data-year', now.getFullYear());
        showDateDom.attr('data-month', parseInt(now.getMonth() + 1));
        showDateDom.attr('data-date', now.getDate());
    }else{
        showDateDom.attr('data-year', nowYear);
        showDateDom.attr('data-month', nowMonth);
        showDateDom.attr('data-date', nowDate);
    }
    // 数据初始化
    function formatYear (nowYear) {
        // console.log(nowYear)
        //定投计算器特殊处理
        let trueMinYear = minData === '' ? 0 : minData ? 0 : 5
        let trueMaxYear =  minData === '' ? 1 : minData ? 0 : 5
        //选择生日特殊处理
        if(pageId == "#combProduct_editChildInfo "){
            trueMinYear = 74;
            trueMaxYear = 0;
        }
        let newNowYear
        if(minData && minData !== ''){
            newNowYear = now.getFullYear()
        }else{
            newNowYear = nowYear
        }
        var arr = [];
        for (var i = nowYear - trueMinYear; i <= newNowYear + trueMaxYear; i++) {
            arr.push({
                id: i + '',
                value: i 
            });
        }
        return arr;
    }
    function formatMonth (year) {
        var arr = [];
        let minNum,maxNum; //起始，终止
        let trueNowTime = now.getFullYear() //真实的截止日 年份
        if((minData || minData === '') && year == nowYear){ //定投计算器 计算最小
            minNum = nowMonth;
        }else{
            minNum = 1;
        }
        if((minData || minData === '') && year == trueNowTime){ //定投计算器 计算最大
            maxNum = parseInt(now.getMonth() + 1);
        }else{
            maxNum = 12;
        }
        //选择生日特殊处理
        if(pageId == "#combProduct_editChildInfo " && year == trueNowTime){
            maxNum = parseInt(now.getMonth() + 1);
        }
        for (var i = minNum; i <= maxNum; i++) {
            arr.push({
                id: i + '',
                value: i 
            });
        }
        return arr;
    }
    function formatDate (count,year, month) {
        var arr = [];
        // let dayNum = (minData || minData === '') ? nowDate : 1;  //定投计算器特殊处理
        let minNum,maxNum; //起始，终止
        let trueNowTime = now.getFullYear() //真实的截止日 年份
        let trueNowMonth = parseInt(now.getMonth() + 1);
        if((minData || minData === '') && year == nowYear && month == nowMonth){
            minNum = nowDate;
        }else{
            minNum = 1;
        }

        if((minData || minData === '') && year == trueNowTime && month == trueNowMonth){
            maxNum = now.getDate();
        }else{
            maxNum = count;
        }
        //选择生日特殊处理
        if(pageId == "#combProduct_editChildInfo " && year == trueNowTime && month == trueNowMonth){
            maxNum = now.getDate();
        }
        for (var i = minNum; i <= maxNum; i++) {
            arr.push({
                id: i + '',
                value: i 
            });
        }
      
        return arr;
    }
    var yearData = function(callback) {
        setTimeout(function() {
            callback(formatYear(nowYear))
        })
    }
    var monthData = function (year, callback) {
        setTimeout(function() {
            callback(formatMonth(year));
        });
    };
    var dateData = function (year, month, callback) {
        setTimeout(function() {
            if (/^11$/.test(month)) {
                callback(formatDate(30,year, month));
            }
            else if (/^4|6|9$/.test(month)) {
                callback(formatDate(30,year, month));
            }
			else if (/^1|3|5|7|8|10|12$/.test(month)) {
				callback(formatDate(31,year, month));
			}
            else if (/^2$/.test(month)) {
                if (year % 4 === 0 && year % 100 !==0 || year % 400 === 0) {
                    callback(formatDate(29,year, month));
                }
                else {
                    callback(formatDate(28,year, month));
                }
            }
            else {
                throw new Error('month is illegal');
            }
        },0);
        // ajax请求可以这样写
        /*
        $.ajax({
            type: 'get',
            url: '/example',
            success: function(data) {
                callback(data);
            }
        });
        */
    };
    selectDateDom.bind('click', function () {
    	$('.olay').remove();
        // console.log(type,2222)
        let recordEventName = type == 0 ? '开始时间' : type == 1 ? '结束时间' : ''
        tools.recordEventData('1','selectDate_'+type,recordEventName);
        var oneLevelId = showDateDom.attr('data-year');
        var twoLevelId = showDateDom.attr('data-month');
        var threeLevelId = showDateDom.attr('data-date');
        var iosSelect = new IosSelect(3, 
            [yearData, monthData, dateData],
            {
                title: '选择日期', 
				headerHeight: 44,
                itemHeight: 44,
                itemShowCount: 5,
                relation: [1, 1],
                layerUtils : layerUtils,
                oneLevelId: oneLevelId,
                twoLevelId: twoLevelId,
                threeLevelId: threeLevelId,
                selectDateDom : selectDateDom,
                pageId : pageId,
                type: type,
                callback: function (selectOneObj, selectTwoObj, selectThreeObj ,selectDateDom) {
                    tools.recordEventData('1','sure_'+type,recordEventName + '-确定');
                    showDateDom.attr('data-year', selectOneObj.id);
                    showDateDom.attr('data-month', selectTwoObj.id);
                    showDateDom.attr('data-date', selectThreeObj.id);
                    // if(selectTwoObj.value<10){
                    // 	selectTwoObj.value='0'+selectTwoObj.value;
                    // }
                    // if(selectThreeObj.value<10){
                    // 	selectThreeObj.value='0'+selectThreeObj.value;
                    // }
                    var selectTime = selectOneObj.value + "-"
                    if(selectTwoObj.value.length<2){
                        selectTime += "0";
                    }
                    selectTime += selectTwoObj.value + "-";
                    if(selectThreeObj.value.length<2){
                        selectTime += "0";
                    }
                    selectTime += selectThreeObj.value;
                    showDateDom.val(selectTime);
                    showDateDom.attr('time',selectTime);
                }
        });
    });
    };
    // 暴露对外的接口
    module.exports = selectDate;
})
