/**
 * 模块名：新理念抢先猜
 */
define(function (require, exports, module) {
    /* 引用模块 */
    var appUtils = require("appUtils"), layerUtils = require("layerUtils"), SHIscroll = require("shIscroll"),
        service = require("mobileService"),  validatorUtil = require("validatorUtil");
    /* 常量 */
    var _pageCode = "activity/leaveWordList", _pageId = "#activity_leaveWordList";
    /* 变量  活动信息*/
    var activityInfo;
    /**
     * 初始化
     */
    function init() {
        activityInfo = appUtils.getPageParam() ? appUtils.getPageParam() : "";
        if(activityInfo && validatorUtil.isEmpty(activityInfo.activity_id)){
            activityInfo = ''
            layerUtils.iAlert('活动不存在,请联系管理人员', -1,function () {
                pageBack();
            });
        }
        reqFun112013();//“财富成长计划”留言列表查询
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), ()=> {
            pageBack();
        });
        // 答题
        appUtils.preBindEvent($(_pageId + " .leaveWord .box"), ".red", function () {
            appUtils.pageInit(_pageCode,"activity/leaveWordPublish", {
                "activity_id":activityInfo.activity_id,
//                "activity_tips":activityInfo.activity_tips,
                "activity_text":activityInfo.activity_text,
                // "activity_text":"发布内容小贴士↵↵·发表简明扼要，逻辑清晰↵·请勿发表不适当言论",
                "flag":"1"
            });
        }, 'click');

    }


    //“财富成长计划”留言列表查询
    function reqFun112013() {
        

        var QueryParam = {
            "activity_id":activityInfo.activity_id,
        }
        service.reqFun112013(QueryParam, (data)=> {
            if (data.error_no == 0) {
                var results = data.results[0];
//                人数
                if(validatorUtil.isNotEmpty(results.activity_tips)){
                	var str=results.activity_tips;
                    var list_num = '<span style="color: #1E80FF">' +  ( str.match(/\d+/g) ? str.match(/\d+/g).join('') : "" )  + '</span>' +
                        ( str.match(/\D/g) ? str.match(/\D/g).join('') : "" )  ;
                    $(_pageId + " .list .num").html(list_num);
                }
                
//                列表
                var html='';
                if(validatorUtil.isNotEmpty(results.self_send_word_list)){
                    html = '<li style="overflow:hidden;margin-top: 0.2rem">\n' +
                        '                        <div class="left">\n' +
                        '                            <img src="images/activity/' + (results.self_send_word_list[0].sex == "1" ? "boy" : "girl") + '.png" alt="" style="width: 0.45rem;">\n' +
                        '                        </div>\n' +
                        '                        <div class="right">\n' +
                        '                            <div class="top">\n' +
                        '                                <span class="name">'+ (results.self_send_word_list[0].cust_name ? results.self_send_word_list[0].cust_name : "***") + '</span>\n' +
                        '                                <span class="label">' + results.self_send_word_list[0].label_desc +'</span>\n' +
                        '                            </div>\n' +
                        '                            <div class="bottom">\n' +
                        '                                <div class="arrow"></div>\n' +
                        '                                <div>\n' +
                        '                                    <span style="padding: 0.1rem">'+ results.self_send_word_list[0].send_word_content + '</span>\n' +
                        '                                </div>\n' +
                        '                            </div>\n' +
                        '                        </div>\n' +
                        '                    </li>'
                }
                if(validatorUtil.isNotEmpty(results.other_send_word_list)){
                    for (let i = 0; i < results.other_send_word_list.length ; i++) {
                        html += '<li style="overflow:hidden;margin-top: 0.2rem">\n' +
                            '                        <div class="left">\n' +
                            '                            <img src="images/activity/' + (results.other_send_word_list[i].sex == "1" ? "boy" : "girl") + '.png" alt="" style="width: 0.45rem;">\n' +
                            '                        </div>\n' +
                            '                        <div class="right">\n' +
                            '                            <div class="top">\n' +
                            '                                <span class="name">'+ (results.other_send_word_list[i].cust_name ? results.other_send_word_list[i].cust_name : "***") + '</span>\n' +
                            '                                <span class="label">' + results.other_send_word_list[i].label_desc +'</span>\n' +
                            '                            </div>\n' +
                            '                            <div class="bottom">\n' +
                            '                                <div class="arrow"></div>\n' +
                            '                                <div>\n' +
                            '                                    <span style="padding: 0.1rem">'+ results.other_send_word_list[i].send_word_content + '</span>\n' +
                            '                                </div>\n' +
                            '                            </div>\n' +
                            '                        </div>\n' +
                            '                    </li>'
                    }
                }
                if(validatorUtil.isEmpty(results.other_send_word_list) && validatorUtil.isEmpty(results.self_send_word_list)){
                    html = '<li style="overflow:hidden;margin-top: 0.2rem;text-align: center">暂无数据 </li>'
                }
                // html = '<li style="overflow:hidden;margin-top: 0.2rem;text-align: center">暂无数据 </li>'
                $(_pageId + " .list ul").html(html)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " .list ul").html("")
        $(_pageId + " .list .num").html('');
        activityInfo = '';

    };
    /*
     * 返回
     */
    function pageBack() {
        // appUtils.pageBack();
        appUtils.pageInit(_pageCode,"combProduct/combProdMarketing");
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
