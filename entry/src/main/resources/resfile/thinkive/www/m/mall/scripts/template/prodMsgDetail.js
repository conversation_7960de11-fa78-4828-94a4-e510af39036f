// 公告详情
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#template_prodMsgDetail ";
    var tools = require("../common/tools");
    var _pageCode = "template/prodMsgDetail";
    var id;
    var htmlContent;
    var createDate;
    var title;
    var prod_id;
    var msg_id;

    function init() {
    	prod_id = appUtils.getPageParam("prod_id");// 产品id
        id = appUtils.getPageParam("id");// 文章id
        msg_id = appUtils.getPageParam("msg_id");// 文章唯一ID
        console.log(appUtils.getPageParam("html_content"))
        htmlContent = decodeURIComponent(appUtils.getPageParam("html_content"));
        title = appUtils.getPageParam("msg_title");
        createDate = appUtils.getPageParam("create_date");
        $(_pageId + " #title").html(title);
        $(_pageId + " #fbTime").html(tools.ftime(createDate));
        $(_pageId + " #content").html(htmlContent);
        productNews();
        //获取页面分享状态
        getPageShareStatus();
        tools.initPagePointData({essayId: msg_id});
    }

    // 绑定事件
    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function getPageShareStatus(){
        let data = {
            busi_id:msg_id,
            page_type:'4',
            pageId:_pageId,
            pageCode:_pageCode
        }
        tools.isShowShare(data,'1')
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #content").html("");
        $(_pageId + " #fbTime").html("");
        $(_pageId + " #title").html("");
        $(_pageId + " #share").hide();
    }
  //产品消息已读
    function productNews(){
        let param = {
        	msg_id: id,//文章id
        	prod_id: prod_id// 产品id
        }
        service.reqFun101083(param, (data) => {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
//            let results = data.results[0];
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var noticeDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = noticeDetails;
});
