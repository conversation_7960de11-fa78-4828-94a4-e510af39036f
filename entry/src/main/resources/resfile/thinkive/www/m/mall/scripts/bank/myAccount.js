// 信息验证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#bank_myAccount ",
        _pageCode = "bank/myAccount";
    var tools = require("../common/tools");
    require("../common/clipboard.min.js");
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        getAccountInfo();
        copyContent("copy");
    }


    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .enchashment"), function () {
            appUtils.pageInit(_pageCode, "bank/enchashment");
        });
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "bank/recharge");
        });
        appUtils.bindEvent($(_pageId + " #full_account"), function () {
            //显示电子账户弹框
            showBankAlert();
        });
        appUtils.bindEvent($(_pageId + " #cancel"), function () {
            //隐藏电子账户弹框
            hideBankAlert();
        });
        appUtils.bindEvent($(_pageId + " #make_money"), function () {
            appUtils.pageInit(_pageCode, "bank/bankProdList", {bank_channel_code: productInfo.bank_channel_code});
        });
        // 账户明细
        appUtils.bindEvent($(_pageId + " .transition"), function () {
            appUtils.pageInit(_pageCode, "bank/transaction");
        });
        // 更换绑定卡
        appUtils.bindEvent($(_pageId + " #changeBindCard"), function () {
            appUtils.pageInit(_pageCode, "bank/changeBindCard1");
        });

    }

    function getAccountInfo() {
        var bank_channel_code = productInfo.bank_channel_code;
        var params = {
            bank_channel_code: bank_channel_code,
        }
        service.reqFun151103(params, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-2);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                appUtils.setSStorageInfo("myAccountInfo", results);
                var logo = tools.judgeBankImg(bank_channel_code).logo;
                $(_pageId + " .top_title .logo").attr("src", logo);
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bank_acct_no").html("****" + results.bank_acct_no.substr(-4) + ")");
                $(_pageId + " .bankInfo").text(results.bd_bank_name + "(尾号" + results.bank_acct_no.substr(-4) + ")");
                $(_pageId + " #copy").attr("data-clipboard-text", results.acct_no);
                $(_pageId + " .acct_no").html("****" + results.acct_no.substr(-4));
                $(_pageId + " #full_acct_no").html(results.acct_no);
                $(_pageId + " .mobile_phone").text(results.mobile_phone.substr(0, 3) + "****" + results.mobile_phone.substr(-4));
                $(_pageId + " .avail_amt").text(tools.fmoney(results.avail_amt) + "元");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);
        clipboard.on('success', function (e) {
            //隐藏电子账户弹框
            hideBankAlert();
            layerUtils.iAlert("复制成功，可粘贴");
        });
        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    //隐藏电子账户弹框
    function hideBankAlert() {
        $(_pageId + " .alert").hide();
    }

    //显示电子账户弹框
    function showBankAlert() {
        $(_pageId + " .alert").show();
    }

    function destroy() {
        hideBankAlert();
        $(_pageId + " .top_title .logo").attr("src", "");
        // acct_no 电子银行    bank_acct_no 绑定银行
        $(_pageId + " .bank_acct_no").html("");
        $(_pageId + " .bankInfo").text("");
        $(_pageId + " #copy").attr("data-clipboard-text", "");
        $(_pageId + " .acct_no").html("");
        $(_pageId + " #full_acct_no").html("");
        $(_pageId + " .mobile_phone").text("");
        $(_pageId + " .avail_amt").text("--");

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
