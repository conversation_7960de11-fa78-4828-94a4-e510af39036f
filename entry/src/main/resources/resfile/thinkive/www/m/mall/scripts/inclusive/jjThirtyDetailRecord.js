// 晋金档案
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        common = require("common"),
        _pageId = "#inclusive_jjThirtyDetailRecord";
    var _pageCode = "inclusive/jjThirtyDetailRecord";
    var productInfo;
    var ut = require("../common/userUtil");
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        //获取详情
        getDetail();
        //获取历史分红
        reqFun102023();
        //初始化按钮
        tools.initFundBtn(productInfo, _pageId);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //点击基金概况
        appUtils.bindEvent($(_pageId + " #situation"), function () {
            $(_pageId + " #situation").addClass("active");
            $(_pageId + " #grade").removeClass("active");
            $(_pageId + " #history").removeClass("active");

            $(_pageId + " #situationContent").show();
            $(_pageId + " #gradeContent").hide();
            $(_pageId + " #historyContent").hide();
        });
        //点击基金评级
        appUtils.bindEvent($(_pageId + " #grade"), function () {
            $(_pageId + " #situation").removeClass("active");
            $(_pageId + " #grade").addClass("active");
            $(_pageId + " #history").removeClass("active");

            $(_pageId + " #situationContent").hide();
            $(_pageId + " #gradeContent").show();
            $(_pageId + " #historyContent").hide();
        });
        //点击历史分红
        appUtils.bindEvent($(_pageId + " #history"), function () {
            $(_pageId + " #situation").removeClass("active");
            $(_pageId + " #grade").removeClass("active");
            $(_pageId + " #history").addClass("active");

            $(_pageId + " #situationContent").hide();
            $(_pageId + " #gradeContent").hide();
            $(_pageId + " #historyContent").show();
        });
    }


    //获取晋金宝列表
    function getSituation(data) {
        var html = "";
        var situationNameArr = ["基金全称", "基金简称", "基金代码", "基金类型" ,"投资类型", "成立日期", "成立规模", "基金规模" ,"份额规模", "基金管理人"];
        var situationValueArr = [];
        situationValueArr.push(data.prod_name);
        situationValueArr.push(data.prod_sname);
        situationValueArr.push(data.fund_code);
        situationValueArr.push(data.operation_type_name);
        situationValueArr.push(data.fund_type_name);

        if (data.establish_date) {
            data.establish_date = tools.ftime(data.establish_date);
        } else {
            data.establish_date = "--";
        }
        situationValueArr.push(data.establish_date);

        if (data.scale) {
            data.scale = (data.scale / 100000000).toFixed(2);
            data.scale = data.scale + "亿份";
        } else {
            data.scale = "--";
        }
        if(data.scale_fe) {
            data.scale_fe = (data.scale_fe / 100000000).toFixed(2);
            data.scale_fe = data.scale_fe + "亿份";
        } else {
            data.scale_fe = "--";
        }
        if (data.scale_fund) {
            data.scale_fund = (data.scale_fund / 100000000).toFixed(2);
            data.scale_fund = data.scale_fund + "亿元";
        } else {
            data.scale_fund = "--";
        }
        situationValueArr.push(data.scale);

        situationValueArr.push(data.scale_fund);
        situationValueArr.push(data.scale_fe);
        situationValueArr.push(data.mgrcomp_name);
        var threshold_amount = data.threshold_amount;

        for (var i = 0; i < situationNameArr.length; i++) {
            html += '<div class="highFinancialInfo-item">' +
                '<p class="item-left ">' + situationNameArr[i] + '</p>' +
                '<p class="item-right fund_name">' + situationValueArr[i] + '</p>' +
                '</div>';
        }

        $(_pageId + " #performance .item-right").html(data.compare_benchmark);
        $(_pageId + " #situationContent").html(html);

        //起购金额
        $(_pageId + " #threshold_amount").html(threshold_amount);
    }

    //获取详情
    function getDetail() {
        service.reqFun102002({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                //数据处理 空 和 --
                results = tools.FormatNull(results);
                //获取基金概况
                getSituation(results);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取历史分红
    function reqFun102023() {
        service.reqFun102023({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                var html = "";

                if (!results || results.length == 0) {
                    $(_pageId + " #historyContent .no_data").show();
                } else {
                    $(_pageId + " #historyContent .no_data").hide();
                }

                for (var i = 0; i < results.length; i++) {
                    //公告日期
                    var create_time = results[i].create_time;
                    if (create_time) {
                        create_time = tools.ftime(create_time.substring(0, 8));
                    } else {
                        create_time = "--";
                    }

                    //红利发放日
                    var divi_pay_date = results[i].divi_pay_date;
                    if (divi_pay_date) {
                        divi_pay_date = tools.ftime(divi_pay_date.substring(0, 8));
                    } else {
                        divi_pay_date = "--";
                    }

                    //每份分红
                    var cash_atax_rmb = results[i].cash_atax_rmb;
                    if (!cash_atax_rmb) {
                        cash_atax_rmb = "--";
                    }


                    html += '<ul class="item">' +
                        '<li class="tl">' + create_time + '</li>' +
                        '<li>' + divi_pay_date + '</li>' +
                        '<li class="tr">' + cash_atax_rmb + '</li>' +
                        '</ul>';
                }
                $(_pageId + " #historyContent .list").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
		$(_pageId + " #situation").addClass("active");
		$(_pageId + " #grade").removeClass("active");
		$(_pageId + " #history").removeClass("active");
		$(_pageId + " #situationContent").show();
		$(_pageId + " #gradeContent").hide();
		$(_pageId + " #historyContent").hide();
		$(_pageId + " #buy_rate_box").html("免申购费");
		$(_pageId + " #situationContent").html("");
		$(_pageId + " #performance .item-right").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailRecord = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailRecord;
});
