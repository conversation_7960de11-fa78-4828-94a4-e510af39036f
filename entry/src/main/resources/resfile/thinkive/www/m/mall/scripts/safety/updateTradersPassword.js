// 修改支付密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_updateTradersPassword";
    //弱密码库
    var weakpwdArr = ['111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999'];
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        common.systemKeybord(); // 解禁系统键盘
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #oldPwd").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #oldPwd1").addClass("active");
                    }
                } else {
                    passflag = "请输入旧密码";
                    $(_pageId + " #oldPwd1").removeClass("active");
                }
                $(_pageId + " #oldPwd1").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #oldPwd").val(shuru);
                    $(_pageId + " #oldPwd1").text(passflag);
                }
                var shuru_2 = $(_pageId + " #newPwd1").val();
                var passflag_2 = "";
                if (shuru_2) {
                    for (var i = 0; i < shuru_2.length; i++) {
                        passflag_2 += "*";
                    }
                    if (shuru_2.length == 1) {
                        $(_pageId + " #newPwd11").addClass("active");
                    }
                } else {
                    passflag_2 = "请输入新密码";
                    $(_pageId + " #newPwd11").removeClass("active");
                }
                $(_pageId + " #newPwd11").text(passflag_2);
                var len = shuru_2.length;
                if (len >= 6) {
                    shuru_2 = shuru_2.substring(0, 6);
                    $(_pageId + " #newPwd1").val(shuru_2);
                    $(_pageId + " #newPwd11").text(passflag_2);
                }

                var shuru_3 = $(_pageId + " #newPwd2").val();
                var passflag_3 = "";
                if (shuru_3) {
                    for (var i = 0; i < shuru_3.length; i++) {
                        passflag_3 += "*";
                    }
                    if (shuru_3.length == 1) {
                        $(_pageId + " #newPwd21").addClass("active");
                    }
                } else {
                    passflag_3 = "请再次输入新密码";
                    $(_pageId + " #newPwd21").removeClass("active");
                }
                $(_pageId + " #newPwd21").text(passflag_3);
                var len = shuru_3.length;
                if (len >= 6) {
                    shuru_3 = shuru_3.substring(0, 6);
                    $(_pageId + " #newPwd2").val(shuru_3);
                    $(_pageId + " #newPwd21").text(passflag_3);
                }

            } // 键盘的输入事件
        };
        $(_pageId + " #oldPwd1").text("请输入旧密码");
        $(_pageId + " #newPwd11").text("请输入新密码");
        $(_pageId + " #newPwd21").text("请再次输入新密码");
        var phoneNum = ut.getUserInf().mobile;
//		 var phoneNum = appUtils.getSStorageInfo("mobile");
        $(_pageId + " #userInfo").html("您好！您正在为账户 " + phoneNum.substr(0, 3) + "****" + phoneNum.substr(7, 4) + "修改交易密码。");

    }

    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            guanbi();
            var oldPwd = $(_pageId + " #oldPwd").val();
            var newPwd1 = $(_pageId + " #newPwd1").val();
            var newPwd2 = $(_pageId + " #newPwd2").val();
            if (oldPwd == null || oldPwd == "") {
                layerUtils.iMsg(-1, "请确定您输入的旧密码");
                return;
            }
            if (!checkInput(newPwd1, newPwd2)) {
                return;
            }
            if (newPwd1 == oldPwd) {
                layerUtils.iMsg(-1, "新密码与旧密码相同");
                return;
            }
            if (!checkTradePwd(newPwd1, "")) {
                return;
            }
            var param = {
                old_trans_pwd: oldPwd,
                new_trans_pwd: newPwd1
            };
            //密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no == "0") {
                    var modulus = data.results[0].modulus;
                    var publicExponent = data.results[0].publicExponent;
                    var endecryptUtils = require("endecryptUtils");
                    param.old_trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.old_trans_pwd);
                    service.getRSAKey({}, function (data) {
                        if (data.error_no == "0") {
                            var modulus = data.results[0].modulus;
                            var publicExponent = data.results[0].publicExponent;
                            var endecryptUtils = require("endecryptUtils");
                            param.new_trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.new_trans_pwd);
                            updateTradersPassword(param);
                        } else {
                            layerUtils.iLoading(false);
                        }
                    }, {isLastReq: false});
                } else {
                    layerUtils.iLoading(false);
                }
            }, {isLastReq: false});
        });

        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #oldPwd11"), function () {
            if ($(_pageId + " #oldPwd").val() == "") {
                $(_pageId + " #oldPwd1").removeClass("unable");
            } else {
                $(_pageId + " #oldPwd1").removeClass("unable").addClass("active");
            }
            $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
            $(_pageId + " #newPwd21").removeClass("active").addClass("unable");
            kaiqi("oldPwd");
        }, "click");


        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #newPwd111"), function () {
            if ($(_pageId + " #newPwd1").val() == "") {
                $(_pageId + " #newPwd11").removeClass("unable");
            } else {
                $(_pageId + " #newPwd11").removeClass("unable").addClass("active");
            }
            $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
            $(_pageId + " #newPwd21").removeClass("active").addClass("unable");
            kaiqi("newPwd1");
        }, "click");


        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #newPwd211"), function () {
            if ($(_pageId + " #newPwd2").val() == "") {
                $(_pageId + " #newPwd21").removeClass("unable");
            } else {
                $(_pageId + " #newPwd21").removeClass("unable").addClass("active");
            }
            $(_pageId + " #newPwd11").removeClass("active").addClass("unable");
            $(_pageId + " #oldPwd1").removeClass("active").addClass("unable");
            kaiqi("newPwd2");
        }, "click");
    }

    /**
     * 修该交易密码
     * */
    function updateTradersPassword(param) {
        service.reqFun101006(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                layerUtils.iAlert("交易密码修改成功", 0, function () {
                    appUtils.pageBack();
                });
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iMsg(-1, "密码不能为空");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iMsg(-1, "确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iMsg(-1, "两次密码不相同");
            return false;
        }
        if (!validatorUtil.isAlphaNumeric(pwd1) || pwd1.length != 6) {
            layerUtils.iMsg(-1, "交易密码应为6位数字");
            return false;
        }
        return true;
    }


    /**
     * 验证密码格式
     * pwd:面码
     * pwd_name:密码名 如支付密码
     * */
    function checkTradePwd(pwd, pwdName) {
        var pwd_Name = "";
        //如果pwdName为空
        if (!pwdName) {
            pwd_Name = "密码";
        }
        if (validatorUtil.isEmpty(pwd)) {
            layerUtils.iMsg(-1, pwd_Name + "不能为空");
            return false;
        }
        if (!/^\d{6}$/.test(pwd)) {
            layerUtils.iMsg(-1, pwd_Name + "为6位数字");
            return false;
        }
        var temp_pwd = 0;
        for (var i = 0; i < pwd.length - 1; i++) {
            if (parseInt(pwd[i]) + 1 == parseInt(pwd[i + 1])) {
                temp_pwd++;
            }
        }
        if (temp_pwd == 5) {
            layerUtils.iMsg(-1, pwd_Name + "不能为顺增的6位数字");
            return false;
        }
        for (var i = 0; i < weakpwdArr.length; i++) {
            var weakpwd = weakpwdArr[i];
            if (weakpwd.indexOf(pwd) > -1) {
                layerUtils.iMsg(-1, pwd_Name + "不能为连续数字!");
                return false;
            }
        }
        layerUtils.iLayerClose();
        return true;
    }


    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //开启键盘
    function kaiqi(eleId) {
        //调用键盘
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_updateTradersPassword";
        param["eleId"] = eleId;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    }

    function destroy() {
        guanbi();
        $(_pageId + " #oldPwd").val("");
        $(_pageId + " #newPwd1").val("");
        $(_pageId + " #newPwd2").val("");
        $(_pageId + " #oldPwd1").text("请输入旧密码");
        $(_pageId + " #newPwd11").text("请输入新密码");
        $(_pageId + " #newPwd21").text("请再次输入新密码");
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var changePassword = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = changePassword;
});
