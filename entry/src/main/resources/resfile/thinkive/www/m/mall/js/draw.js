var draw = {
    times: 3, // 循环次数 默认3
    time: 0.3, //单位s 动画时长,默认0.3
    _posList: [], //位置集合
    moveX: 33, //
    moveY: 1.3,
    col: 3, // 列
    _isMove: false, //是否处于移动中，移动中不允许再次触发
    _elements: [], //移动元素集合
    //初始化
    /**
     * @param.el 目标元素
     * @param.times 移动次数
     * @param.time 动画时长
     * @param.moveY y，默认为元素高度
     * @param.moveX x，默认为元素宽度
     * */
    init: function (obj) {
        this._posList = [];
        this._elements = document.querySelectorAll(obj.el);
        this.time = obj.time || 0.3;
        this.times = obj.times || 3;
        this.moveY = obj.moveY || this._elements[0].offsetWidth;
        this.moveX = obj.moveX || this._elements[0].offsetHeight;
        this._elements.forEach((item, index, arr) => {
            item.style.transition = 'all ' + this.time + 's'
            item.style.transitionOrigin = 'center;';
            let x = index % this.col * this.moveX + '%';
            let y = Math.floor(index / this.col) * this.moveY + 'rem';
            this._posList.push({ x, y});
            item.style.left = x ;
            item.style.top = y;
        })
    },
    start: function () {
        if(this._isMove) return;
        this._move();
        if(this.times < 2) return;
        let num = 1;
        let t = setInterval( () => {
            num++;
            this._move();
            num == this.times && clearInterval(t)
        }, this.time * 1000)

    },
    //位置移动
    _move: function () {
        this._isMove = true
        let len = this._posList.length;
        let newArr = [...this._posList];
        let roundArr = this._getRound();
        for(let i = 0; i < len; i++) {
            this._posList[i] = newArr[roundArr[i]];
        }
        this._posList.forEach((item, index) => {
            this._elements[index].style.left = item.x;
            this._elements[index].style.top = item.y;
        })
        this._isMove = false
    },
    //获取随机数
    _getRound() {
        let team = [];
        this._posList.forEach((_, index) => {
            team.push(index);
        })
        let res = [];
        for (var i = 0,
               len = team.length; i < len; i++) {
            // 随机叫个
            var randomIndex = Math.floor(Math.random() * team.length);
            // 出列到新队伍
            res[i] = team[randomIndex];
            // 出来了不能继续叫了哦
            team.splice(randomIndex, 1);
        }
        return res;
    }
}
