// 预约购买
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        des = require("des"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var gconfig = require("gconfig");
	var global = gconfig.global;
    var _pageId = "#numberBenefits_benefitsList";
    var _pageCode = "numberBenefits/benefitsList";
    var apt_mix_money;//起投金额
	var platform = gconfig.platform;

    function init() {
    	UserActivities();
    	aptMixMoney();//查起投金额
    }


    function bindPageEvent() {

        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //分享邀请链接
        appUtils.preBindEvent($(_pageId), ".share", function() {
        	var obj = $(this);
        	var activitystate = obj.attr("activitystate");
        	var activitiestype=obj.attr("activitiestype");
        	if(activitystate =="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
        	$(_pageId+" #pop_layer").show();
        	$(_pageId+" #pop_layer").find("#share_WeChat").attr({"activitiestype" : activitiestype});
        	$(_pageId+" #pop_layer").find("#share_WeChatFriend").attr({"activitiestype" : activitiestype});
        })

         //邀请好友投资
        appUtils.preBindEvent($(_pageId), "#vipBenefits", function() {
        	var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
        	var activities_type = $(this).attr("activitiesType") || "";
        	var param = {
        		activities_type : activities_type,
        		indexLogin : "benefitsList"
        	};
        	appUtils.pageInit(_pageCode, "vipBenefits/friendInvitation",param);
        })

        //微信好友
		appUtils.bindEvent($(_pageId+" #share_WeChat"),function(){
			var activities_type = $(this).attr("activitiestype");
			share("22","0",activities_type);
		});
		//微信朋友圈
		appUtils.bindEvent($(_pageId+" #share_WeChatFriend"),function(){
			var activities_type = $(this).attr("activitiestype");
			share("23","1",activities_type);
		});

      //汇款充值
		appUtils.preBindEvent($(_pageId), "#hkcz", function() {
			var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
			var bankCard = appUtils.getSStorageInfo("cardNo");
			var bankName = appUtils.getSStorageInfo("bankName");
			appUtils.setSStorageInfo("to_hkcz","numberBenefits/benefitsList");
			appUtils.pageInit(_pageCode, "account/payRecharge", {
				"bankName": bankName,
				"bankCard": bankCard
			});
		});

        //抽奖
        appUtils.preBindEvent($(_pageId), ".lottery", function() {
        	var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
        	var activitiesType = $(this).attr("activitiesType");
			appUtils.pageInit(_pageCode, "redPack/luckDraw", {"activitiesType":activitiesType});
		});

        //去投资晋金投
        appUtils.preBindEvent($(_pageId), "#investment", function() {
        	var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
			//appUtils.pageInit(_pageCode, "login/userIndexs", {});
        	//判断基金状态，确定是否有基金
			if(appUtils.getSStorageInfo("fundsStatus") !== "3"){
				//无基金
				reqFun902229();
			}else{
				reqFun902211();
			}
		});


        //去领奖
        appUtils.preBindEvent($(_pageId), ".receivePrize", function() {
        	var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
        	var thisBtn = $(this);
        	if(thisBtn.hasClass("unactive")){
        		return;
        	}
        	var activitiesType = $(this).attr("activitiesType");
			service.reqFun9089030({
				"activity_type" : activitiesType,
				"reward_activity_type" : activitiesType,
				"mobile": ut.getUserInf().mobile
				},function(data){
				if(data.error_no == "0"){//领取成功
					var results = data.results[0];
					var reward_name = results.reward_name;
					var money = results.money;
					layerUtils.iAlert("领取到"+ reward_name + money +"元", "0", function(){
						thisBtn.addClass("unactive").removeClass("investment active").html("已完成");
						setTimeout(function(){
							appUtils.pageInit(_pageCode, "redPack/index",{"activitiesType":activitiesType});
						},500)

                    });

				}else{
					layerUtils.iAlert(data.error_info);
				}
			})
		});

        //周五答题
        appUtils.preBindEvent($(_pageId), ".answer", function(){
        	var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
        	var url = $(this).attr("dataUrl");
        	appUtils.pageInit(_pageCode, "guide/advertisement", {"url":url,"benefitsList":"benefitsList"});
        })

        //已完成   活动尚未开始
        appUtils.preBindEvent($(_pageId), " .btn.unactive", function(){
        	var obj = $(this);
        	if(obj.attr("activitystate")=="0"){//0未开始  1正在进行
        		layerUtils.iAlert("活动尚未开始!");
        		return;
        	}
        })

      //取消邀请好友
		appUtils.bindEvent($(_pageId+" #cancelShare"),function(){
			$(_pageId+" #pop_layer").hide();
		});

    }

    function destroy() {
    	$(_pageId + " .benefitsCon").html("");
    	$(_pageId+" #pop_layer").find("#share_WeChat").attr({"activitiestype" : ""});
    	$(_pageId+" #pop_layer").find("#share_WeChatFriend").attr({"activitiestype" : ""});
    }

	/**
     * 查询无基金用户资产
     */
	function reqFun902229(){
		var userId = appUtils.getSStorageInfo("userId");
		var custno = appUtils.getSStorageInfo("custNo");
		var cardno = appUtils.getSStorageInfo("cardNo");
		var param = {
			user_id:userId,
			cust_no:custno,
			acct_no:cardno
		};
		service.reqFun902229(param,function(data){
			if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
			var results = data.results[0];

			//晋金余额
			var total_jjye = parseFloat(results.total_jjye||"0.00");

			//总资产
			var total_all = Number(results.total_all);
			//总资产 >= 30万元，跳转新预约页面
			//暂时改为1000，方便测试
			var apt_mix_money_num = apt_mix_money *10000;
			if(total_all >= apt_mix_money_num){
				//查询用户是否为黑名单用户
				reqFun901702();
				//appUtils.pageInit("login/userIndexs", "extraservices/prodAppointmentNew", {});
			}else{  //总资产 < 30万元，跳转旧预约页面
				appUtils.pageInit(_pageCode, "extraservices/prodAppointment", {"benefits":"benefitsList"});
			}
		})
	}

	/**
     * 查询有基金用户资产
     */
	function reqFun902211(){
		var userId = appUtils.getSStorageInfo("userId");
		var custno = appUtils.getSStorageInfo("custNo");
		var cardno = appUtils.getSStorageInfo("cardNo");
		var param = {
				user_id:userId,
				cust_no:custno,
                acct_no:cardno
		};
		service.reqFun902211(param,function(data){
			if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
			var results = data.results[0];

			//总资产
			var total_vol =  Number(results.total_vol);
			//晋金宝总份额
			var total_jjb = parseFloat(results.total_jjb||"0.00");
			//晋金余额
			var total_jjye = parseFloat(results.total_jjye||"0.00");
			//可用金额
			var amount_available = Number(total_jjb) + Number(total_jjye);

			//总资产 >= 30万元，跳转新预约页面
			//暂时改为1000，方便测试
			var apt_mix_money_num = apt_mix_money *10000;
			if(total_vol >= apt_mix_money_num){
				//查询用户是否为黑名单用户
				reqFun901702();
				//appUtils.pageInit("login/userIndexs", "extraservices/prodAppointmentNew", {});
			}else{  //总资产 < 30万元，跳转旧预约页面
				appUtils.pageInit(_pageCode, "extraservices/prodAppointment", {"benefits":"benefitsList"});
			}
		})
	}


	/**
     * 查询用户是否为黑名单用户
     */
	function reqFun901702(){
		//获取客户号
		var custno = appUtils.getSStorageInfo("custNo");
		//黑名单类型  1:晋金财富 0:晋金所
		var type = '1';

		var param = {
			custno:custno,
			type:type,
		};
		service.reqFun901702(param,function(data){
			if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
			var results = data.results[0];

			//是否黑名单  1:是 0:否
			//黑名单跳转到旧页面
			if(results.flag == '1'){
				appUtils.pageInit(_pageCode, "extraservices/prodAppointment", {"benefits":"benefitsList"});
			}else{
				appUtils.pageInit(_pageCode, "extraservices/prodAppointmentNew", {"benefits":"benefitsList"});
			}
		})
	}

	//查询预约起投金额
	function aptMixMoney(){
		service.reqFun902701({
			itemName:"apt_mix_money"
		},function(data){
			if(data.error_no == "0"){
				apt_mix_money = data.results[0].itemValue;
			}else{
				layerUtils.iAlert(data.error_info);
                return;
			}
		})
	}

    function desEncrypt(key,value) {
	    var keyHex = des.enc.Utf8.parse(key);
	    var valueHex = des.enc.Utf8.parse(value);
	    var encrypted = des.DES.encrypt(valueHex, keyHex, {
	        mode: des.mode.ECB,
	        padding: des.pad.Pkcs7
	    });
		return encrypted.toString();
	}

    //查询用户活动
    function UserActivities(){
    	service.reqFun9089028({},function(data){
    		if(data.error_no == "0"){
    			var results = data.results;
    			var activitiesStr = "";
    			var activitiesStrHeader = "<li class='benefitsList'><div class='benefitsTitle'><h4>";
    			var activitiesStrfooter = "</a></div></li>";
    			var activitiesStrCenter = "</div></div><div class='benefitsBtn'>";

    			for(var i=0; i< results.length; i++){
    				var state = results[i].state;
    				if(state == "0"){//0  未开始
    					var ywcStr = "未开始",
    						yqStr = "未开始",
    						cyStr = "未开始",
    						cjStr = "未开始",
    						czStr = "未开始",
    						ljStr = "未开始",
    						fxStr = "未开始",
    					    tzStr = "未开始";

    				}else{
    					var ywcStr = "已完成",
    						yqStr = "去邀请",
    						cyStr = "去参与",
    						cjStr = "去抽奖",
    						czStr = "去充值",
    						ljStr = "去领奖",
    						fxStr = "去分享",
    						tzStr = "去投资";
    				}
    				var userState = "";
    				if(results[i].activity_type == "6"){//邀请好友投资
    					activitiesStr += activitiesStrHeader + results[i].name+"</h4><div class='benefits'>"+results[i].remark+
    					activitiesStrCenter+"<a class='btn active' id ='vipBenefits' activityState = "+results[i].state+">"+yqStr+activitiesStrfooter;
    				}else if(results[i].activity_type == "7"){//周五答题活动
    					activitiesStr += activitiesStrHeader + results[i].name+"</h4><div class='benefits'>"+results[i].remark+
    					activitiesStrCenter + "<a class='btn active answer' dataUrl ="+results[i].link_url+" activityState = "+results[i].state+">"+cyStr+activitiesStrfooter;
    				}else if(results[i].activity_type == "8"){//汇款充值
    					if(results[i].user_stat == "1"){//1:可领奖
        					activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter + "<a class='btn rewardActive lottery' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+ cjStr + activitiesStrfooter;
        				}else if(results[i].user_stat == "2"){//2:已完成
        					activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter + "<a class='btn unactive' activityState = "+results[i].state+">"+ ywcStr + activitiesStrfooter;
        				}else{//未参与
        					activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter + "<a class='btn active' id ='hkcz' activityState = "+results[i].state+">"+czStr+activitiesStrfooter;
        				}
    				}else if(results[i].activity_type == "9"){//投资晋金投
    					if(results[i].user_stat == "1"){//1:可领奖
        					activitiesStr += activitiesStrHeader + results[i].name + "</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter + "<a class='btn rewardActive receivePrize' activitiesType = "+results[i].activity_type+" activityState = "+results[i].state+">"+ ljStr + activitiesStrfooter;
        				}else if(results[i].user_stat == "2"){//2:已完成
        					activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter + "<a class='btn unactive' activityState = "+results[i].state+">"+ ywcStr + activitiesStrfooter;
        				}else{
        					activitiesStr += activitiesStrHeader + results[i].name+ "</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter + "<a class='btn active' id ='investment' activityState = "+results[i].state+">"+tzStr+activitiesStrfooter;
        				}
    				}else if(results[i].activity_type == "10"){//分享邀请链接至朋友圈(一次)
    					if(results[i].user_stat == "1"){
    						activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter+"<a class='btn rewardActive lottery' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+cjStr+activitiesStrfooter;
    					}else if(results[i].user_stat == "2"){
    						activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter+"<a class='btn unactive' id='once' activityState = "+results[i].state+">"+ywcStr+activitiesStrfooter;
    					}else {
    						activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter+"<a class='btn active share' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+fxStr+activitiesStrfooter;
    					}
    				}else if(results[i].activity_type == "11"){//分享邀请链接至朋友圈(每天一次)
    					if(results[i].user_stat == "1"){
    						activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter+"<a class='btn rewardActive lottery' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+cjStr+activitiesStrfooter;
    					}else if(results[i].user_stat == "2"){
    						activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter+"<a class='btn unactive' activityState = "+results[i].state+">"+ywcStr+activitiesStrfooter;
    					}else {
    						activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
        					activitiesStrCenter+"<a class='btn active share' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+fxStr+activitiesStrfooter;
    					}
    				}else if(results[i].activity_type == "13"){//投资晋金投
						if(results[i].user_stat == "1"){
							activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
								activitiesStrCenter+"<a class='btn rewardActive lottery' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+cjStr+activitiesStrfooter;
						}else if(results[i].user_stat == "2"){
							activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
								activitiesStrCenter+"<a class='btn unactive' activityState = "+results[i].state+">"+ywcStr+activitiesStrfooter;
						}else {
							activitiesStr += activitiesStrHeader + results[i].name +"</h4><div class='benefits'>"+results[i].remark+
								activitiesStrCenter+"<a class='btn active share' activitiesType="+results[i].activity_type+" activityState = "+results[i].state+">"+fxStr+activitiesStrfooter;
						}
					}
    			}
    			$(_pageId + " .benefitsCon").html(activitiesStr).show();
    			$(_pageId + " .placeholder").hide();
    		}else{
    			$(_pageId + " .benefitsCon").hide();
    			$(_pageId + " .placeholder").show();
    			layerUtils.iAlert(data.error_info);
    		}
    	})
    }

	function share(shareTypeList,share_type,activities_type){
		// 简化分享链接
		var mobile = appUtils.getSStorageInfo("user").mobileWhole;
		var query_params = {};
		query_params["mobile"] = mobile;
		query_params["share_type"]=share_type;
		service.getShareInfo(query_params,function(data)
		{
			var error_no = data.error_no,
			error_info = data.error_info;
			if(error_no == "0"){
				if(data.results!=undefined && data.results.length>0)
				{
					mobile=desEncrypt("mobile",mobile);

					var result=data.results[0];

					var share_url=result.share_url;

					if (validatorUtil.isEmpty(share_url)){
						share_url=global.link;
					}


					if (share_url.indexOf("?") != -1)
					{
						share_url = share_url + "&mobile="+mobile;
					}
					else
					{
						share_url = share_url + "?mobile="+mobile;
					}

					var img_url=result.img_url;

					if (validatorUtil.isEmpty(img_url)){
						img_url=global.imgUrl;
					}

					var content=result.content;

					var title=result.title;

					var params = {};
					params["url"] = share_url;
//					content="国有理财晋金所，安全稳健收益佳！";
					if(shareTypeList == "23"){
						layerUtils.iMsg(-1,"启动分享中...请稍后！",2);
						$(_pageId+" #pop_layer").hide();
						var param = {};
						param["moduleName"] = "mall";
				        param["funcNo"] = "50231";
				        param["shareType"] =shareTypeList;//平台字典
				        param["title"] = title;
				        param["link"] =share_url;
				        param["content"] =content; //分享文本
				        param["imgUrl"] =img_url;
				        require("external").callMessage(param);
				        activities_type && appUtils.setSStorageInfo("activities_type" ,activities_type);
					}else{
						service.simplifyURL(params,function(data) {
							var error_no = data.error_no,
								error_info = data.error_info;
							if(error_no == "0"){
								share_url = data.results[0].shortUrl;
								layerUtils.iMsg(-1,"启动分享中...请稍后！",2);
								$(_pageId+" #pop_layer").hide();
								var param = {};
								param["moduleName"] = "mall";
						        param["funcNo"] = "50231";
						        param["shareType"] =shareTypeList;//平台字典
						        param["title"] = title;
						        param["link"] =share_url;
						        param["content"] =content; //分享文本
						        param["imgUrl"] =img_url;
						        require("external").callMessage(param);
						        activities_type && appUtils.setSStorageInfo("activities_type" ,activities_type);

							}else{
								layerUtils.iAlert(error_info);
							}
						});
					}


				}
			}
		});

	}



    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageInit(_pageCode, "login/userIndexs", {});

    }


    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});
