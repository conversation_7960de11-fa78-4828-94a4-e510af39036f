// 首页
define(function (require, exports, module) {
    var tools = require("../common/tools");
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        service = require("mobileService"),
        hIscroll = null,
        validatorUtil = require("validatorUtil"),
        _page_code = "login/userIndexs",
        fingerprint = require('../common/fingerprint.js'),
        snowballInit = require('../common/snowballInit.js'),
        _pageId = "#login_userIndexs ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var platform = gconfig.platform;
    var ut = require("../common/userUtil");
    var external = require("external");
    var timer;
    var userLoginStatus; //用户登录状态 0未登录 1已登录
    var userAuthenticationStatus; //用户认证状态
    var newPopupInfo; //新版弹窗
    var is_banner_pageTo;//是否是banner弹出合格投资者跳转
    var bannerPageIntoData;//banner跳转参数
    var chooseVerson,snowballMarketShow; //当前选中的版本
    var index_player;//首页视频
    var source;
    let clickTimer = null;
    //初始化版本相关数据
    var scene_code,mobileWhole,userChooseVerson
    var colorList = ['rgb(247,239,229)', 'rgb(241,233,239)', 'rgb(229,228,244)', 'rgb(223,235,243)', 'rgb(245,235,229)', 'rgb(244,238,242)'];
    var dateObj = {
        "D": "天",
        "M": "月",
        "Y": "年",
    }
    var exclusive_buy_state = {
        // 购买状态 0开放购买前 1可购买 2结束购买之后，
        "0": {
            "btnClass": "",
            "btnText":chooseVerson == '3' ? `敬请<br>期待` : '敬请期待'
        },
        "1": {
            "btnClass": "",
            "btnText": "购买"
        },
        "2": {
            "btnClass": "sold_out",
            "btnText": "售罄"
        }
    }
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        source = '3';//注册来源
        /**
        * 区分首页版本 进行页面渲染（特殊处理）
        */
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        mobileWhole = common.getLocalStorage("mobileWhole"); //判断用户是否登陆过
        userChooseVerson = common.getLocalStorage("userChooseVerson"); //获取用户选中的版本
        scene_code = scene_code ? scene_code : '';
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        // scene_code = '3'
        if(userChooseVerson && userChooseVerson !=''){
            //当前版本为用户手动切换版本
            if(userChooseVerson == '3'){
                return highVersionIndex();// 高端版本
            }else{
                return benchmarkIndex();
            } 
            // if(userChooseVerson == '1' || userChooseVerson == '2') return benchmarkIndex();// 标准首页流程
            // if(userChooseVerson == '2') return snowballIndex(scene_code,userChooseVerson);// 滚雪球版首页流程
        }else{
            //根据用户版本进行渲染首页
            if(scene_code == '3'){
                highVersionIndex();
            }else{
                benchmarkIndex();
            }// 高端版本流程
            // if(scene_code == '1' || scene_code == '2') return benchmarkIndex();// 标准首页流程
            // if((!scene_code && mobileWhole && mobileWhole.length) || scene_code == '1') return benchmarkIndex();// 标准首页流程
            // if((!scene_code && !mobileWhole) || scene_code == '2') return snowballIndex(scene_code,userChooseVerson);//滚雪球版首页流程
        }
    }
    //根据用户是否完成绑卡，是否进行风险测评来展示对应的消息模块
    function isMessage(){
        const MSG_BIND_CARD = '您还未完成绑卡';
        const UPLOAD_ID_CARD = '您还未上传身份证';
        const BTN_BIND_CARD = '去绑卡';
        const MSG_RISK_EVALUATION = '您还未完成风险测评';
        const BTN_RISK_EVALUATION = '去测评';
        const PERFECT_INFO_TIPS = {
            // "1": '您还未上传身份证',
            "2": '您的身份证照片已到期，请尽快更换',
            "3": '您的身份证照片即将到期，请尽快更换',
            "4": '您的身份证照片已到期，请尽快更换'
        };
        const HG_TIPS = {
            "4": '您的合格投资者认证已到期,请尽快认证',
            "6": '您的合格投资者认证即将到期,请尽快认证'
        };
        const userInf = ut.getUserInf();
        const sStorageInfo = appUtils.getSStorageInfo("user");
        // 检查是否绑卡
        if (!userInf.bankAcct) {
            return displayMessage(MSG_BIND_CARD, BTN_BIND_CARD);
        }
        // 处理身份证到期情况
        let perfectInfoTip = PERFECT_INFO_TIPS[sStorageInfo.perfect_info];
        //上传身份证
        if(sStorageInfo.idCardUploadFlag != '1'){
            return displayMessage(UPLOAD_ID_CARD, '去上传');
        }
        // 检查是否完成风险测评
        if (validatorUtil.isEmpty(userInf.riskLevel)) {
            return displayMessage(MSG_RISK_EVALUATION, BTN_RISK_EVALUATION);
        }
        
        if (perfectInfoTip) {
            return displayMessage(perfectInfoTip, '去更换');
        }
        // 合格投资者认证状态
        let hgTip = HG_TIPS[sStorageInfo.hgSoonInvalidState];
        if (hgTip) {
            return displayMessage(hgTip, '去认证');
        }
        getNoReadMsg();
        // 默认显示
        // $(_pageId + " .messgae").show();
    }
    //渲染对应文案
    function displayMessage(tip, btnText) {
        
        if(chooseVerson == '3'){
            //仅查询数量
            getNoReadNum();
            let html = `
                ${tip}，<span class="blue preTransMsg" operationType="1" operationId="preTransMsg" operationName="交易前置消息提醒">${btnText}</span>
            `
            $(_pageId + " .high .info_msg").html(html);
        }else{
            
            $(_pageId + " .preTransMsg_tip").text(tip);
            $(_pageId + " .preTransMsg_btn").text(btnText);
            $(_pageId + " .preTransMsg").show();
            $(_pageId + " .mail").show();
        }
    }
    //获取未读消息数量
    function getNoReadNum(){
        service.reqFun105001({}, function (data) {
            if (data.error_no == "0") {
                var count = (data.results && data.results[0] && data.results[0].count) ? data.results[0].count : 0;
                if(count > 0){
                    $(_pageId + " .high_msg_num").text(count);
                    $(_pageId + " .high_msg_num").show();
                }else{
                    $(_pageId + " .high_msg_num").hide();
                }
            } else {
                $(_pageId + " .high_msg_num").hide();
            }
        });
    }
    function listScroll(){
        const $list = $('.list');
        let isScrolling = false;
        $list.on('scroll', function() {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    const element = this;
                    const scrollLeft = element.scrollLeft;
                    const maxScroll = element.scrollWidth - element.clientWidth;

                    // 计算滚动比例（0~1）
                    const ratio = maxScroll > 0 ? scrollLeft / maxScroll : 0;

                    // 将滚动比例映射到 66%~100% 的宽度范围
                    let widthPercentage = 66 + Math.round(ratio * 34); // 66% + (0%~34%)
                    widthPercentage = Math.max(66, Math.min(100, widthPercentage)); // 严格限制范围

                    // 安全更新样式
                    const $scrollActive = $(_pageId + " .high_index_top .list .scroll_active");
                    if ($scrollActive.length) {
                        $scrollActive.css('margin-left', `${widthPercentage - 66}%`);
                    }
                    isScrolling = false;
                });
            }
        });
    }
    //高端版本首页
    function highVersionIndex(){
        chooseVerson = '3'; //当前选中的是高端版本
        appUtils.setSStorageInfo("chooseVerson", '3');
        $(_pageId)[0].style.backgroundColor = "#27180F";
        //获取高端模板
        let html = snowballInit.getHighVersionIndexHtml();
        $(_pageId + " #templatePage").html(html);
        commonBeforeLogin();
        $(_pageId + " .marketIndexFooter").hide();
        $(_pageId + " .standard").hide();
        $(_pageId + " .highFooter").show();
        let userInfo = ut.getUserInf();
        let channelCode = common.getLocalStorage("download_channel_code");  //获取渠道标识
        if (channelCode == "yh_jjdx" && userInfo) {
            //源晖与财富共有用户,展示源晖入口
            $(_pageId + " #yuanhui").show();
        } else if (!channelCode || channelCode == "jjdx") {
            //晋金财富用户,隐藏源晖入口
            $(_pageId + " #yuanhui").hide();
        }
        //监听混动条事件
        listScroll()
        
        // $(_pageId + " #standardFooter").show();
        //延时加载，避免页面卡死
        setTimeout(async () => {
            //未登录
            if (!ut.getUserInf()) {
                // tools.guanggao({ _pageId: _pageId, group_id: "33" });
                $(_pageId + " .high_index_top .info").hide();
                //未登录
                noLoginCommon();
                return setData(0, 0);
            }
            commonAfterLogin();
            // tools.guanggao({ _pageId: _pageId, group_id: "34" });
            var res = await getUserAuthenticationStatus();
            userAuthenticationStatus = res[0].state //获取用户认证状态
            sm_white_state = res[0].sm_white_state //获取用户白名单状态
            appUtils.setSStorageInfo("isAuthentication", userAuthenticationStatus);
            setDataRemark();
            
            $(_pageId + " .high_index_top .info").show();
            if (sm_white_state == "1" || userAuthenticationStatus == "1") { //白名单用户 || 合格投资人 展示详细数据
                setData();
            } else {
                setData(1, 0);
            }
        }, 0);
    }
    //渲染描述语
    function setDataRemark(){
        //问好描述
        let dataRemark;
        var date = new Date();
        if (date.getHours() >= 6 && date.getHours() < 11) {
            dataRemark = '早上好！'
        } else if (date.getHours() >= 11 && date.getHours() < 14) {
            dataRemark = '中午好！'
        } else if (date.getHours() >= 14 && date.getHours() < 18) {
            dataRemark = '下午好！'
        } else{
            dataRemark = '晚上好！'
        }
        let userInfo = ut.getUserInf();
        //获取用户性别
        let gender = userInfo.gender == "1" ? "先生" : "女士";
        //获取用户姓
        let firstName = userInfo.name ?  userInfo.name.split('')[0] : '';
        let str = `${firstName}${gender}，${dataRemark}`
        $(_pageId + " .high_index_top .info .dataRemark").text(str);
    }
    //旧版跳转新版营销页
    function pageInitIndex(){
        if (!common.loginInter()) return;
        appUtils.setSStorageInfo("plan_type",'');
        common.setLocalStorage("userChooseRefresh",'0');
        common.setLocalStorage("userChooseVerson",'2');
        common.setLocalStorage("snowballMarketShow","1");
        setTimeout(async () => {
            userIndex.destroy();
            location.reload();
        }, 0);
    }
    //获取模板详情
    async function getTemplate(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102150(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res ? res.template_content : "");
            })
        })
    }
    //登录前公共处理
    function commonBeforeLogin(){
        //初始化路径
        clearPath();
        var qualifiedInvestorStartAmount = require("gconfig").global.qualifiedInvestorStartAmount;
        $(_pageId + " .qualifiedInvestorStartAmount").html(qualifiedInvestorStartAmount / 10000 + "万");
        common.clearLocalStorage("activityInfo");
        common.clearLocalStorage("activityInfo_id");
        appUtils.clearSStorage("activityInfo");
        // 苹果暗黑模式设置
        tools.getSystemMode(_pageId);
        //首次进入页面，获取更新状态
        common.updateVers(_pageId);
        //自动更新，轮询查询
        if (!timer) {
            timer = setInterval(function () {
                common.updateVers(_pageId);
            }, global.updateTime);
        }
    }
    //登录后公共处理
    function commonAfterLogin(){
        //已登录
        $(_pageId + " #loginOut").attr("operationId",'loginOut');
        $(_pageId + " #loginOut").attr("operationName",'退出登录');
        $(_pageId + " #loginOut").text('退出');
        //获取最新弹窗
        getNewPopUp();
        //获取未读消息数量
        // if(chooseVerson != '3') getNoReadMsg();
        if (ut.getUserInf().bankAcct) { //查询绑卡状态
            //查询换卡状态，首页提示
            common.changeCardInter(function () {
            }, false);
        }
        //身份证提示
        id_card_info()
        //合格投资者认证状态
        hgSoonInvalidState();
        //根据用户是否完成绑卡，是否进行风险测评来展示对应的消息模块
        isMessage();

    }
    //获取晋金宝产品详情
    function getJjbInfo(){
        let data = { fund_code: "000709" }
        service.reqFun102002(data, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let jjbInfo = datas.results[0];
            $(_pageId + " .JjbName").html(jjbInfo.prod_sname);
            $(_pageId + " .Jjb_annu_yield").html(tools.fmoney(jjbInfo.annu_yield) + '%');
        })
    }
    //获取场景首页模板ID
    async function getTemplateId(data) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102214(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results
                let matchedItems = res.filter(item => item.scene_code === '2');
                let template_id = matchedItems[0].template_id
                resolve(template_id ? template_id : "");
            })
        })
    }
    //noLogin公共
    function noLoginCommon(){
        //未登录
        $(_pageId + " #loginOut").attr("operationId",'login');
        $(_pageId + " #loginOut").attr("operationName",'登录');
        $(_pageId + " #loginOut").text('登录');
    }
    //滚雪球版本首页
    async function snowballIndex(scene_code,userChooseVerson){
        commonBeforeLogin();
        chooseVerson = '2'; //当前选中的是X版
        appUtils.setSStorageInfo("chooseVerson", '2');
        //初始化滚雪球版本首页模板
        let html = ``;
        //区分是否展示欢迎页UI
        snowballMarketShow = common.getLocalStorage("snowballMarketShow"); //是否展示过欢迎页，登录后置为1
        // snowballMarketShow = '1'
        if(snowballMarketShow == '1'){
            //展示滚雪球首页 拿到本地模板数据
            html = snowballInit.getSnowballHtml();
            $(_pageId + " #templatePage").html(html);
            //获取晋金宝产品详情
            getJjbInfo();
            // $(_pageId + " #templatePage").show();
            $(_pageId + " .marketIndexFooter").hide();
            $(_pageId + " .highFooter").hide();
            $(_pageId + " .standard").show();
            $(_pageId + " .footerAll").show();
            $(_pageId + " #standardFooter").hide();
            //根据模板ID获取模板
            let templateId = await getTemplateId({});
            let listHtml = await getTemplate({templateId:templateId});
            $(_pageId + ' .wallet_product').html(listHtml);
            //判断用户底部切换按钮展示
            if((scene_code == userChooseVerson) || (scene_code == '2' && !userChooseVerson)){
                //用户本身是新用户
                $(_pageId + ' .switchVerName').text('查看更多平台产品');
            }else{
                //用户本身是老用户
                $(_pageId + ' .switchVerName').text('返回');
            }
            userIndex.bindPageEvent();
            //延时加载，避免页面卡死
            setTimeout(async () => {
                if (!ut.getUserInf()) {
                    //未登录
                    noLoginCommon();
                    return tools.guanggao({ _pageId: _pageId, group_id: snowballInit.group_id_loginBefore });
                }
                tools.guanggao({ _pageId: _pageId, group_id: snowballInit.group_id_loginAfter });
                var res = await getUserAuthenticationStatus();
                userAuthenticationStatus = res[0].state //获取用户认证状态
                sm_white_state = res[0].sm_white_state //获取用户白名单状态
                appUtils.setSStorageInfo("isAuthentication", userAuthenticationStatus);
                commonAfterLogin();
                $(_pageId + " .switchVer").show();
            }, 0);
        }else{
            //展示滚雪球欢迎页
            html = snowballInit.getSnowballMarketHtml();
            // getSceneMarkList();
            $(_pageId + " #templatePage").html(html);
            $(_pageId + " .footerAll").hide();
            $(_pageId + " footer").hide();
            $(_pageId + " .marketIndexFooter").show();
            $(_pageId + " #standardFooter").hide();
            // $(_pageId + " #templatePage").show();
            userIndex.bindPageEvent();
            if (!ut.getUserInf()) {
                //未登录
                return noLoginCommon();
            }else{
                $(_pageId + " #loginOut").attr("operationId",'loginOut');
                $(_pageId + " #loginOut").attr("operationName",'退出登录');
                $(_pageId + " #loginOut").text('退出');
            }
            // commonAfterLogin();
        }
    }
    //标准版首页逻辑处理
    function benchmarkIndex(){
        commonBeforeLogin();
        let channelCode = common.getLocalStorage("download_channel_code");  //获取渠道标识
        chooseVerson = '1'; //当前选中的是标准版
        appUtils.setSStorageInfo("chooseVerson", '1');
        //获取标准版模板
        let html = snowballInit.getBenchmarkIndexHtml();
        let userInfo = ut.getUserInf();
        $(_pageId + " #templatePage").html(html);
        if (channelCode == "yh") {
            //源晖用户跳转源晖首页
            appUtils.pageInit("", "yuanhui/userIndexs", {});
            return;
        } else if (channelCode == "yh_jjdx") {
            //源晖与财富共有用户,展示源晖入口
            if(userInfo){
                $(_pageId + " #yuanhui").show();
            }else{
                $(_pageId + " #yuanhui").hide();
            }
            // $(_pageId + " #inviteFriends").hide();
        } else if (!channelCode || channelCode == "jjdx") {
            //晋金财富用户,隐藏源晖入口
            $(_pageId + " #yuanhui").hide();
            // $(_pageId + " #inviteFriends").show();
        } else {
            //其他渠道,跳转页面
            appUtils.pageInit("", "hengjipy/userIndexs", {});
            return;
        }
        // $(_pageId + " #templatePage").show();
        $(_pageId + " .marketIndexFooter").hide();
        $(_pageId + " .highFooter").hide();
        $(_pageId + " .standard").show();
        $(_pageId + " #standardFooter").show();
        //延时加载，避免页面卡死
        setTimeout(async () => {
            //未登录
            if (!ut.getUserInf()) {
                tools.guanggao({ _pageId: _pageId, group_id: "33" });
                //未登录
                noLoginCommon();
                return setData(0, 0);
            }
            commonAfterLogin();
            tools.guanggao({ _pageId: _pageId, group_id: "34" });
            var res = await getUserAuthenticationStatus();
            userAuthenticationStatus = res[0].state //获取用户认证状态
            sm_white_state = res[0].sm_white_state //获取用户白名单状态
            appUtils.setSStorageInfo("isAuthentication", userAuthenticationStatus);
            if (sm_white_state == "1" || userAuthenticationStatus == "1") { //白名单用户 || 合格投资人 展示详细数据
                setData();
            } else {
                setData(1, 0);
            }
        }, 0);
    }
    //获取场景营销页产品列表
    function getSceneMarkList(){
        service.reqFun102206({}, function (resultVo) {
            // replace_status 0:待审核 1:已审核待确认 2:换卡成功 3:换卡失败 4: 非换卡期间
            if (resultVo.error_no == "0") {
                if(!resultVo.results || !resultVo.results.length) return;
                let list = resultVo.results;
                let html = setMarkHtmlData(list);
                $(_pageId + " .sceneList").html(html)
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }
        })
    }
    function setMarkHtmlData(list){
        let html = ``
        list.map((item,index)=>{
            let colorList = [
                '253,167,81','184,201,234','253,145,141'
            ]
            let leaflets_income_trait = item.leaflets_income_trait == '0' ? '年化' : ''; //0年化 1涨跌幅
            let leaflets_income_period_desc = item.leaflets_income_period_desc ? item.leaflets_income_period_desc + leaflets_income_trait  : '--'
            let leaflets_income_period_value = (item.leaflets_income_period_value && (!item.leaflets_income_trait || item.leaflets_income_trait == '')) ? item.leaflets_income_period_value : (tools.fmoney(item.leaflets_income_period_value) + '%');
            html += `
                <li class="card flex" style="background:rgb(${colorList[index]});margin-top:0.1rem" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="${item.series_name}" flex" fundCode="${item.series_id}">
                    <em style="display:none">${JSON.stringify(item)}</em>
                    <p class="card_left flex vertical_line">
                        <span class="cardTitle m_bold">${item.series_name ? item.series_name : ''}</span>
                        <span class="annualized main_flxe flex_center">${leaflets_income_period_desc}：<span class="annualizedVal m_font_size22">${leaflets_income_period_value}</span></span>
                        <span class="remark m_font_size12">${item.leaflets_prod_desc ? item.leaflets_prod_desc : '--'}</span>
                    </p>
                    <p class="card_right main_flxe flex_center flex_1">
                        <span class="btn main_flxe flex_center vertical_line">
                            <span>了解</span>
                            <span style="margin-top: -0.08rem;">策略</span>
                        </span>
                    </p>
                </li>
            
            `
        })
        return html;
    }
    function banner_pageTo(){
        var file_type = bannerPageIntoData.file_type; // 链接类型 0 内链 1 外链 2 授权登录
        var url = bannerPageIntoData.url;
        var file_state = bannerPageIntoData.file_state //是否有效 0 无效 1 有效
        var name = bannerPageIntoData.name;
        var description = bannerPageIntoData.description;
        var picture = bannerPageIntoData.picture;
        var group_id = bannerPageIntoData.group_id;
        var banner_id = bannerPageIntoData.banner_id;
        // 登录授权
        if (file_type == "0" && file_state == "1" && url) {
            appUtils.pageInit("login/userIndexs", url, {
                "group_id": group_id,
                "banner_id": banner_id,
            });
            return;
        }
        // 是否是有效内外链接
        if (file_type == "1" && file_state == "1" && url) {
            appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                "url": url,
                "group_id": group_id,
                "banner_id": banner_id,
                "name": name,
                "description": description,
            });
            return;
        }
        if (file_type == "2" && file_state == "1") {
            if (!/^(http|https):/.test(url)) {
                if (url.indexOf("?") > -1) {
                    var skip_url = url.split("?")[0];
                    var parameter = url.split("?")[1];
                    var parameter_arr = parameter.split("&"); //各个参数放到数组里
                    var urlInfo = {};//url的参数信息
                    for (var i = 0; i < parameter_arr.length; i++) {
                        num = parameter_arr[i].indexOf("=");
                        if (num > 0) {
                            name = parameter_arr[i].substring(0, num);
                            value = parameter_arr[i].substr(num + 1);
                            urlInfo[name] = value;
                            urlInfo["group_id"] = group_id;
                            urlInfo["banner_id"] = banner_id;
                        }
                    }
                    if (url.indexOf("activity/fundLuckdrawnew") > -1) { urlInfo['channel'] = "jjcf_app"; urlInfo['type'] = "luckDraw" }
                    appUtils.pageInit("login/userIndexs", skip_url, urlInfo);

                } else {
                    appUtils.pageInit("login/userIndexs", url);
                }
                return;
            }
            if (url.indexOf("activity/fundLuckdraw") > -1) {
                common.setLocalStorage("activityInfo", {
                    activity_id: "2505",
                    group_id: group_id,
                    banner_id: banner_id,
                    cust_no: ut.getUserInf().custNo,
                    channel: "jjcf_app",
                    type: "luckDraw",
                    mobile: ut.getUserInf().mobileWhole,
                })
                var data = external.callMessage({
                    "funcNo": "50043",
                    "moduleName": "mall",
                    "key": "activityInfo",
                })
            }
            appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                "url": url,
                "name": name,
                "description": description,
                "group_id": group_id,
                "banner_id": banner_id
            });
        }
        // 小程序跳转
        if (file_type == "3" && url) {
            tools.jump_applet(url);
            return;
        }
    }
    function clearPath() {
        appUtils.setSStorageInfo("pageTopUrlInfo","");
        appUtils.setSStorageInfo("skipURL", '');
    }
    //获取最新弹窗 是否紧急弹窗
    function getNewPopUp() {
        service.reqFun101091({}, function (data) {
            let popupInfo = data.results[0];
            if (!popupInfo || popupInfo.is_urgent != "1") {  //非紧急
                if (sessionStorage.pop_up_otification == '1') return;
                if (!validatorUtil.isEmpty(ut.getUserInf().bankAcct) && (validatorUtil.isEmpty(ut.getUserInf().duty) || validatorUtil.isEmpty(ut.getUserInf().year_icome) || validatorUtil.isEmpty(ut.getUserInf().living_address_province)) && !appUtils.getSStorageInfo("hasShowUploadTip")) {
                    sessionStorage.pop_up_otification = "1";
                    appUtils.setSStorageInfo("hasShowUploadTip", true);
                    //埋点ID
                    let operationId = 'improveInfo'
                    layerUtils.iConfirm("根据监管要求，需要完善您的信息", function () {
                    }, function () {
                        appUtils.pageInit(_page_code, "account/perfectInfo", {});
                    }, "取消", "确定",operationId);
                    return;
                }
                if (!validatorUtil.isEmpty(ut.getUserInf().soonInvalidFlag) && ut.getUserInf().soonInvalidFlag == '1' && !appUtils.getSStorageInfo("hasShowUploadTip")) {
                    appUtils.setSStorageInfo("hasShowUploadTip", true);
                    sessionStorage.pop_up_otification = "1"
                    let operationId = 'riskAssessment'
                    layerUtils.iConfirm("您的风险测评结果即将到期，到期将会影响交易，请尽快完成风险测评", function () {
                    }, function () {
                        // tools.recordEventData('1','riskQuestion','风险测评');
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});;
                    }, "取消", "去测评",operationId);
                    return;
                }
                if (popupInfo && popupInfo.pop_id) newPopupInfo = popupInfo
                if (!popupInfo || !popupInfo.pop_id) newPopupInfo = null;
                getAbnormalInfo();
                // AppProMsg();
                // setFingerprintPwd();
                return;
            }
            newPopupInfo = popupInfo;
            // if(sessionStorage.pop_up_otification == '1') return;
            activityShow(_pageId, popupInfo.content, popupInfo.type, popupInfo.url_type, popupInfo.url, popupInfo.activityId ? popupInfo.activityId : popupInfo.pop_id, true, true,popupInfo.title,popupInfo.jump_page_type);
            sessionStorage.pop_up_otification = 1;
            dropNewPopUp()
        });
    };
    //标记弹过紧急弹窗
    function dropNewPopUp() {
        service.reqFun101090({ pop_id: newPopupInfo.pop_id }, function (data) {

        });
    };
    //判断用户是否设置指纹相关
    function setFingerprintPwd() {
        // let user = ut.userInfo()
        //新增参数，弹出提示框 去设置指纹次数 最多弹出两次 setFingerprintNum
        let setFingerprintNum = common.getLocalStorage("setFingerprintNum");
        let isLoginUser = common.getLocalStorage("isLoginUser");
        isLoginUser = isLoginUser ? isLoginUser : '0';
        setFingerprintNum = setFingerprintNum ? setFingerprintNum * 1 : 0;
        let fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
        if(setFingerprintNum >= 2 || fingerprintPwd_flag == '1' || platform == '2' || platform == '5') {
            //弹窗次数大于两次走原逻辑
            AppProMsg();
            return mobilePhoneControl();
        }
        if (isLoginUser == '1') return AppProMsg();
        if (platform == "0" || platform == '2'|| platform == '5') { //浏览器端
            return AppProMsg();
        }
        if (!ut.getUserInf()) { //未登录
            return;
        }
        if (appUtils.getPageParam("bindCard")) { //绑卡流程进入首页
            return AppProMsg();
        }
        setFingerprintNum = setFingerprintNum + 1;
        common.setLocalStorage("setFingerprintNum", setFingerprintNum);
        common.setLocalStorage("isLoginUser", '1');
        sessionStorage.pop_up_otification = "1";
        let operationId = 'fingerprint';
        layerUtils.iConfirm('指纹登录已上线，操作更便捷', () => {
            //点击取消关闭弹窗
        }, () => {
            //跳转至指纹弹窗页面
            appUtils.pageInit(_page_code, "safety/fingerprintPwd");
        }, "取消", "去开启",operationId);
    }

    //获取是否异常开户用户
    function getAbnormalInfo() {
        let is_open_acct_excp = appUtils.getSStorageInfo("is_open_acct_excp");
        if (is_open_acct_excp == "1") return setFingerprintPwd();
        service.reqFun101082({}, (data) => {
            if (data.error_no == '0') {
                var res = data.results
                if (res[0] && res[0].id) {
                    let text = res[0].message_text ? res[0].message_text : '';
                    let id = res[0].id;
                    sessionStorage.pop_up_otification = "1";
                    let operationId = 'bindBankCard';
                    layerUtils.iConfirm(text, function () {
                        service.reqFun101029({ "id": id }, function () {
                            var userInfo = ut.getUserInf();
                            if (userInfo && userInfo.bankAcct) {
                                return true;
                            }
                            if (userInfo.isJjsCust == "1") { //isJjsCust 1是晋金所用户 0不是
                                appUtils.clearSStorage("idCardInfo");
                                appUtils.clearSStorage("bankAccInfo");
                                appUtils.pageInit(_page_code, "drainage/openAccount", {});
                                return false;
                            } else {
                                appUtils.pageInit(_page_code, "account/setBankCard", {});
                                return false;
                            }
                        });
                    }, function () {
                        service.reqFun101029({ "id": id }, function () {
                            getAbnormalInfo()
                        });
                    }, "去绑卡", "取消",operationId);
                } else {
                    setFingerprintPwd();
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取用户的认证状态
    async function getUserAuthenticationStatus() {
        return new Promise(async (resolve, reject) => {
            service.reqFun101062({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    $(_pageId + " .footerAll").hide();
                    $(_pageId + ' .homePageIndex_classificationList').html('');
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }

    //获取未读消息数量消息
    function getNoReadMsg() {
        $(_pageId + " .mail").show();
        service.reqFun105001({}, function (data) {
            if (data.error_no == "0") {
                var count = (data.results && data.results[0] && data.results[0].count) ? data.results[0].count : 0;
                if(chooseVerson == '3'){
                    if(count <= 0)  {
                        $(_pageId + " .high_msg_num").hide();
                        return $(_pageId + " .messgae").show();
                        
                    }
                    //高端版本特殊处理
                    $(_pageId + " .high_msg_num").html(count);
                    let latestUnread = data.results[0].latestUnread;
                    if(!latestUnread || !latestUnread.mail_title) return $(_pageId+ " .info_msg").hide(); //不存在删除当前元素
                    $(_pageId + " .info_msg").attr("mail_id",latestUnread.mail_id);
                    let str =   `
                            
                            <span class="msg_details" mail_id="${latestUnread.mail_id}"><em style="display:none">${JSON.stringify(latestUnread)}</em>${latestUnread.mail_title}></span>
                    `
                    $(_pageId + " .info_msg").html(str);
                    $(_pageId + " .high_msg_num").show();
                    $(_pageId+ " .info_msg").show()
                }else{
                    if(count <= 0) $(_pageId + " .benchmark_msg_num").hide();
                    if(count > 0) {
                        $(_pageId + " .benchmark_msg_num").html(count);
                        $(_pageId + " .benchmark_msg_num").show();
                    }
                     $(_pageId + " .msgNum").html(count + '条');
                    let latestUnread = data.results[0].latestUnread;
                    //let tip = `您有<span class="m_text_red msgNum">0</span>条消息需要关注`
                    if(!latestUnread || !latestUnread.mail_title) return $(_pageId + " .messgae").show();;
                    // $(_pageId + " .main_message em").html(JSON.stringify(latestUnread));
                    // $(_pageId + " .main_message p").html(latestUnread.mail_title);
                    // $(_pageId + " .main_message").attr("mail_id",latestUnread.mail_id);
                    // $(_pageId+ " .main_message").show();
                    // let tip = `<span></span>`
                    $(_pageId + " .message_tip").html(latestUnread.mail_title);
                    let emHtml = `<em style="display:none">${JSON.stringify(latestUnread)}</em>`
                    $(_pageId + " .message_tip").append(emHtml)
                    
                }
                $(_pageId + " .messgae").show();
            } else {
                $(_pageId + " .msgNum").html('0' + '条');
                $(_pageId+ " .main_message").hide();
                $(_pageId + " .high_msg_num").hide();
                $(_pageId + " .info_msg").hide();
                $(_pageId + " .messgae").show();
                layerUtils.iAlert(data.error_info);
            }
            
        });
    }

    //去测评
    function pageTo_evaluation() {
        let operationId = "riskAssessment"
        layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        }, '', '确定',operationId)
    }

    // 合格投资人认证状态
    function hgSoonInvalidState() {
        if (!appUtils.getSStorageInfo("user")) return
        // 合格投资人认证状态  4:已过期 6:临期 其他:不提示
        var hgSoonInvalidState = appUtils.getSStorageInfo("user").hgSoonInvalidState;
        if (hgSoonInvalidState == "4") {
            $(_pageId + ' .tip' + ' .exprie_tip .tip_mainText').text("您的合格投资者认证已到期，请尽快").show();
            $(_pageId + " .exprie_tip").css("display", 'block');
            $(_pageId + ' .tip').css("display", 'block');
        } else if (hgSoonInvalidState == "6") {
            $(_pageId + ' .tip' + ' .exprie_tip .tip_mainText').text("您的合格投资者认证即将到期，请尽快").show();
            $(_pageId + " .exprie_tip").css("display", 'block');
            $(_pageId + ' .tip').css("display", 'block');
        }
    }

    //身份证认证提示
    function id_card_info() {
        if (!appUtils.getSStorageInfo("user")) return
        //身份证认证状态 0:未完善 1:已完善 2:证件到期3:到期前3个月 4:到期后3个月
        var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        var parentHtml = $(_pageId + ' .tip') //主节点
        var tip_mainText = $(_pageId + ' .tip' + ' .perfect_tip .tip_mainText') //文案
        switch (perfect_info) {
            case '3':
                $(_pageId + " .perfect_tip").css("display", 'block')
                parentHtml.css("display", 'block')
                break;
            case '2':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                $(_pageId + " .perfect_tip").css("display", 'block')
                parentHtml.css("display", 'block')
                break;
            case '4':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                $(_pageId + " .perfect_tip").css("display", 'block')
                parentHtml.css("display", 'block')
                break;
            default:
                break;
        }
    }

    function getExclusiveList() {
        //获取专享产品列表
        service.reqFun109002({}, function (data) {
            if (data.error_no == 0) {
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //手势密码 控件
    function mobilePhoneControl() {
        if (platform == "0") { //浏览器端
            return;
        }
        if (!ut.getUserInf()) { //未登录
            return;
        }
        if (appUtils.getPageParam("bindCard")) { //绑卡流程进入首页
            return;
        }
        //判断是否弹过指纹相关弹窗
        // let fingerprintDigNum = common.getLocalStorage("fingerprintDigNum");
        // if(!fingerprintDigNum){
        //     common.setLocalStorage("fingerprintDigNum",1);
        //     //弹出指纹弹窗
        //     return $(_pageId + " .loginDig").show();
        // }
        let flag;
        // 获取用户账户信息
        let param50043 = {
            funcNo: "50043",
            key: "account_password"
        };
        let firstInstall = external.callMessage(param50043);
        if (!firstInstall || !firstInstall.results || !firstInstall.results[0] || !firstInstall.results[0].value) {
            flag = '0';
        } else {
            firstInstall = firstInstall.results[0].value;
            let account = firstInstall.substring(0, firstInstall.indexOf("_"));
            let param = {
                "funcNo": "50263",
                "account": account
            };
            let data = external.callMessage(param);
            flag = data.results[0].flag;
        }
        //判断是否弹过手势弹窗
        var gesture_code_data = common.getLocalStorage("gesture_code");
        if (gesture_code_data == "1" || flag == '1') { //首页已弹过 && 已经设置过
            return;
        }
        setGesture()
    }

    function setGesture() {
        //存储首页是否弹过手势密码  1 已弹过
        common.setLocalStorage("gesture_code", "1");
        var account = mobileWhole;
        appUtils.setSStorageInfo("isCanBack", "0");
        var setParam = {
            "funcNo": "50264", //设置手势密码的设置状态
            "moduleName": "mall",
            "flag": 1, //flag	String	状态（0:取消手势，1:设置手势，2:修改手势）
            "style": "1", //style	String	手势密码的样式类型(0:不显示中心小圆，1:显示）
            "account": account,
            "isCanBack": "1",
            "position": "1",
            "errorNum": "5"
        };
        external.callMessage(setParam);
    }

    //处理数据
    async function setData(userLoginStatus, userAuthenticationStatus) {   //登录状态 ，认证状态
        var topLevel //按钮状态
        var arr = await getFirstList()
        var firstArr = arr[0].list    //一级数组集合
        var secondArr = arr[1].data      //二级数组集合
        var three = arr[2].list    //专享列表数据整合
        if (userLoginStatus == 0) topLevel = chooseVerson == '3' ? `登录<br>可见` : '登录可见'
        if (userLoginStatus == 1 && userAuthenticationStatus == 0) topLevel = chooseVerson == '3' ? `认证<br>可见`:'认证可见'
        firstArr.map(item => {
            item.childrenList = []; //正常展示模块
            item.childrenListAllBuy = []; //一键买入新版模块
            secondArr[item.financial_prod_type] = secondArr[item.financial_prod_type] ? secondArr[item.financial_prod_type] : []
            secondArr[item.financial_prod_type].map(newItem => {
                if (newItem.spread_buy && newItem.spread_buy == '1' && newItem.buy_state == '1') {
                    item.childrenListAllBuy.push(newItem)
                } else {
                    item.childrenList.push(newItem)
                }
            })
        })
        var vipData = {
            classify_name: '专享产品',
            childrenList: three
        }
        if (three.length) firstArr.splice(1, 0, vipData)
        var html = ''
        if (firstArr && firstArr.length) {
            for (var i = 0; i < firstArr.length; i++) {
                var childrenListHtml = '';
                var childrenListHtmlAllBuy = '';
                var img_url = firstArr[i].img_url;
                var financial_prod_type = firstArr[i].financial_prod_type;
                var productInfo = JSON.stringify(firstArr[i]); //二级列表接口传递数据
                var color = colorList[i] //卡片背景色
                var classify_name = firstArr[i].classify_name; //标题
                var classify_desc = firstArr[i].classify_desc ? firstArr[i].classify_desc : ''; //remark
                if (!firstArr[i].childrenList.length && !firstArr[i].childrenListAllBuy.length) {
                    childrenListHtml += ` <ul class="tem_null_data flex_center"> 即将上线，敬请期待。 </ul> `
                } 
                // else if (financial_prod_type == "07" || financial_prod_type == "08") { // 基金投顾
                //     firstArr[i].childrenList.map(item => {
                //         var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
                //         var prod_per_min_amt = item.prod_per_min_amt //投资金额
                //         var comb_sname = item.comb_sname;
                //         var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                //         var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                //         var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                //         var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none'  //是否展示特点
                //         var corner_marker_list = item.corner_marker_list == '1' ? '' : 'display_none'  //是否展示角标
                //         var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                //         var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                //         var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none'  //是否展示策略特点
                //         //数据展示
                //         var income_period_type_desc = item.income_period_type ? item.income_period_type : '--' //近多少年化
                //         var annual_income = item.annual_income; // 目标年化收益
                //         var holding_time = item.holding_time ? item.holding_time : '--' // 建议持有时长
                //         var characteristic = item.characteristic; // 特点
                //         var strategic_characteristic = item.strategic_characteristic; // 策略特点   
                //         var threshold_amount = item.first_per_min ? item.first_per_min : '--' //起购金额
                //         var corner_marker = item.corner_marker; // 角标描述
                //         var mechanism = item.mechanism; // 投顾机构
                //         var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                //         var buy_state = item.purchase_state;
                //         var buy_state_name, btnClass;
                //         var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅

                //         //1-购买  2-预约 3-敬请期待  4-售罄
                //         if (buy_state == "1") {
                //             buy_state_name = "购买";
                //             btnClass = "";
                //         }
                //         if (buy_state == "2") {
                //             buy_state_name = "预约";
                //             btnClass = "";
                //         }
                //         if (buy_state == "3") {
                //             buy_state_name = "敬请期待";
                //             btnClass = "";
                //         }
                //         if (buy_state == "4") { //其他产品为售罄
                //             buy_state_name = "封闭中";
                //             btnClass = "sold_out";
                //         }
                //         if (buy_state == "5") { //其他产品为售罄
                //             buy_state_name = "售罄";
                //             btnClass = "sold_out";
                //         }
                //         if (buy_state == "6") { //定制产品买入置灰
                //             buy_state_name = "购买";
                //             btnClass = "";
                //         }
                //         //投顾列表展示
                //         childrenListHtml += `
                //             <ul class="classificationList_card_main flex">
                //                 <li class="main_flxe vertical_line">
                //                     <em style='display: none' class='productInfo'>${productInfoSon}</em>
                //                     <p class="m_font_size16 color_000">${comb_sname}</p>
                //                     <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化：<span class="m_text_red m_font_size18">${establish_rate}</span>%</p>
                //                     <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}：<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</p>
                //                     <p class="m_font_size12 ${annual_income_list}">目标年化收益：<span class="m_text_red m_font_size18">${annual_income}</span></p>
                //                     <p class="m_font_size12 ${strategic_characteristic_list}">策略特点：<span class="m_text_red">${strategic_characteristic}</span></p>
                //                     <p class="m_text_999 m_font_size12">
                //                         <span class="${threshold_amount_list}">起购：${threshold_amount}元</span>
                //                         <span class="${recommended_holding_list}">建议持有${holding_time}</span>
                //                     </p>
                //                     <p class="m_font_size12 m_golden ${characteristic_list}">${characteristic}</p>
                //                 </li>
                //                 <li class="classificationList_card_main_btn main_flxe flex_center">
                //                     <span class="main_flxe buy_state_name flex_center ${btnClass}">
                //                         ${buy_state_name}
                //                     </span>
                //                 </li>
                //             </ul>
                //         `
                //     })
                // } 
                else {
                    //新版处理逻辑
                    childrenListHtml = setChildData(firstArr[i].childrenList, topLevel);
                    childrenListHtmlAllBuy = setChildData(firstArr[i].childrenListAllBuy, topLevel);
                    if (childrenListHtmlAllBuy && childrenListHtmlAllBuy.length && firstArr[i].childrenListAllBuy.length && firstArr[i].childrenListAllBuy.length > 1) {
                        // childrenListHtmlAllBuy = childrenListHtmlAllBuy + `<div class="userIndexBtn" financial_prod_type="${firstArr[i].financial_prod_type}" spread_buy="1">一键分散买 ></div>`
                    }
                }
                html += `
                        <div style="background:${color}" class="classificationList_card">
                            <ul class="classificationList_card_top m_width_100" contentType="1" operationType="1" operationId="${'more_' + financial_prod_type}" operationName = "${classify_name + '-更多'}">
                                <em style='display: none' class='productInfo'>${productInfo}</em>
                                ${firstArr[i].is_show_title == 0 ? "" : `
                                <li class="main_flxe m_width_80">
                                    <span style="width:0.75rem;display: inline-flex;align-items: center;" class="m_font_size16 m_padding_left_0_6 color_000 classify_name">${classify_name}</span>
                                    <span style="display: inline-flex;align-items: center;flex:1" class="m_font_size12 m_margin_left01 ${classify_desc ? '' : 'display_none'}">${classify_desc}</span>
                                </li>
                                <li class="indexPageRightIcon m_width_20 flex_center ${financial_prod_type ? 'main_flxe' : 'display_none'}">
                                    ${(!img_url && firstArr[i].financial_prod_type == '01') ? '' : '<span class="more">更多</span><i></i>'} 
                                </li>
                                `}
                            </ul>
                            <ul class="main_list ${childrenListHtmlAllBuy && childrenListHtml && firstArr[i].childrenListAllBuy.length && firstArr[i].childrenListAllBuy.length == 1 ? 'index_child_one' : 'display_none'}">
                                ${childrenListHtmlAllBuy}
                            </ul>
                            <ul class="main_list ${childrenListHtmlAllBuy && firstArr[i].childrenListAllBuy.length && firstArr[i].childrenListAllBuy.length == 1 ? 'index_child_two' : ''} ${(childrenListHtmlAllBuy && firstArr[i].childrenListAllBuy.length && firstArr[i].childrenListAllBuy.length > 1) ? 'm_marginTop_10' : ''}">
                                ${childrenListHtml}
                            </ul>
                        </div>`
            }
        }
        $(_pageId + ' .homePageIndex_classificationList').html(html);
        $(_pageId + " .footerAll").show();
        if(ut.getUserInf()) $(_pageId + " .switchVer").show();
        userIndex.bindPageEvent();
    }
    //合并处理数据 新增一键分散购买
    function setChildData(list, topLevel) {
        let childrenListHtml = '';
        if (!list || !list.length) return '';
        list.map(item => {
            //获取是否选中高端版本 3 高端版本 其他 低端版本 chooseVerson
            //埋点ID
            let fundCode = item.fund_code ? item.fund_code : '';
            var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
            var prod_per_min_amt = item.prod_per_min_amt //投资金额
            var prod_sname = item.prod_name_list ? item.prod_name_list : item.prod_sname ? item.prod_sname : item.prod_exclusive_name
            var this_year_rate = tools.fmoney(item.this_year_rate ? item.this_year_rate : item.annu_yield);
            var rate = tools.fmoney(item.rate);
            var transferable = item.transferable;//是否可转让
            var recommend_info = item.recommend_info;
            var prod_per_min_amt = item.prod_per_min_amt;
            var increase_term = item.increase_term;
            var recommend_info_str = recommend_info ? "<p class='balance'>" + recommend_info + "</p>" : "";
            var str = "";
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + item.url + "' style='width:0.32rem;margin-left: 0.04rem;margin-top: -0.04rem;'>"
            }
            // TODO;
            if (item.fund_code == '000709') {
                //晋金宝
                var annu_yield = tools.fmoney(item.annu_yield);
                item['new_fund_code'] = item.fund_code;
                item['fund_code'] = "000709";
                productInfoSon = JSON.stringify(item);
                childrenListHtml += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_font_size12 ${chooseVerson == '3' ? 'display_none' : ''}"><span>近七日年化:</span><span class="m_text_red m_font_size18">${annu_yield}</span>%</p>
                        <p class="m_font_size12 ${chooseVerson == '3' ? 'flex' : 'display_none'}">
                            <span class="main_flxe vertical_line high_fixed_width"> 
                                <span class="high_color m_font_size18">${annu_yield}%</span>
                                <span>近七日年化</span>
                            </span>
                            <span class="main_flxe vertical_line">
                                <span class="m_font_size14 m_bold_500 m_text_393939" style="height:32px;line-height:32px">灵活存取</span>
                                <span>低风险 | 0.01元起购</span>
                            </span>
                        </p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center" style="line-height: 0.16rem;">
                            ${chooseVerson == '3' ? '充值<br>取现' : '充值<br>取现'}
                        </span>
                        
                    </li>
                </ul>
                `
            } else if ((item.exclusive_product_type == '05' || item.exclusive_product_type == '03') && item.exclusive_type != '8') {
                //新专享相关
                var img_str = item.prod_img_url ? '<img class="vipTitleImg" src="' + global.oss_url + item.prod_img_url + '">' : "";
                childrenListHtml += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">
                            <span>${prod_sname}</span>
                            ${img_str}
                        </p>
                        <p class="m_font_size12">综合收益率:<span class="m_text_red m_font_size18">${rate}</span>%<img src="../mall/images/thirty_tips.png" class="thirty_tips" style="width: 0.25rem;"></p>
                        
                        <p class="m_text_999 m_font_size12">
                            <span>起购:${prod_per_min_amt}元</span>
                            <span>期限:${increase_term}天</span>
                        </p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${exclusive_buy_state[item.buy_state].btnClass}">
                            ${exclusive_buy_state[item.buy_state].btnText}
                        </span>
                    </li>
                </ul>`
            } else if (item.exclusive_type == '8') {
                //专享相关
                var img_str = item.prod_img_url ? '<img class="vipTitleImg" src="' + global.oss_url + item.prod_img_url + '">' : "";
                childrenListHtml += `
                <ul class="classificationList_card_main contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" flex" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">
                            <span>${prod_sname}</span>
                            ${img_str}
                        </p>
                        <p class="m_font_size12">近七日年化:<span class="m_text_red m_font_size18">${this_year_rate}</span>%</p>
                        <p class="m_text_999 m_font_size12">
                            <span>投资金额:${prod_per_min_amt}元</span>
                        </p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${exclusive_buy_state[item.buy_state].btnClass}">
                            ${exclusive_buy_state[item.buy_state].btnText}
                        </span>
                    </li>
                </ul>
                `
            } else if (item.prod_sub_type2 == '100') {
                //私募列表展示
                var found_rate = item.found_rate ? item.found_rate : '--' //成立以来收益
                var preincomerate = tools.fmoney(item.preincomerate) //年化标准
                var threshold_amount = item.threshold_amount / 10000 //起购金额
                var inrest_term = item.inrest_term   //封闭期/锁定期
                var nav = tools.fmoney(item.nav, 4)
                var interest_rate = tools.fmoney(item.interest_rate_min) + "%" + "-" + tools.fmoney(item.interest_rate_max) + "%";
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var risklevel_name = item.risklevel_name.split('(') ? item.risklevel_name.split("(")[0] : '--'
                //产品整合 是否展示
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var fund_rate_list = item.fund_rate_list == '1' ? '' : 'display_none' //是否展示成立以来收益
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var this_year_rate_list = item.this_year_rate_list == '1' ? '' : 'display_none' //是否展示今年以来收益
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none'
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var stage_yield_list = item.stage_yield_list == '1' ? '' : 'display_none' // 是否展示阶段收益率
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' // 是否展示业绩比较基准
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                childrenListHtml += `
                    <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                        <li class="main_flxe vertical_line ${chooseVerson == '3' ? 'display_none' : ''}">
                            <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000 prod_sname">${prod_sname}${str}</p>
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : preincomerate}</span>%</p>
                            <p class="m_font_size12 ${stage_yield_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : interest_rate}</span></p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${topLevel ? '--' : preincomerate}</span>%</p>
                            <p class="m_font_size12 ${fund_rate_list}">成立以来收益:<span class="m_text_red m_font_size18">${topLevel ? '--' : tools.fmoney(found_rate)}</span>%</p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${topLevel ? '--' : dk_income_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${topLevel ? '--' : dk_income_rate_chg}</span>%</p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${threshold_amount_list}">起购:${topLevel ? '--' : threshold_amount}万元</span>
                                <span class="${closed_period_list}">期限:${topLevel ? '--' : inrest_term}</span>
                                <span class="${lock_period_list}">锁定期:${topLevel ? '--' : inrest_term}</span>
                                <span class="${this_year_rate_list}">今年来收益:${topLevel ? '--' : this_year_rate ? tools.fmoney(this_year_rate) : "--"}</span>
                                <span class="${nav_list}">最新净值:${topLevel ? '--' : nav}</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="main_flxe vertical_line flex_1 ${chooseVerson == '3' ? '' : 'display_none'}">
                            <p class="m_font_size16 color_000 prod_sname">${prod_sname}${str}</p>
                            <p class="m_font_size12 ${chooseVerson == '3' ? 'flex' : 'display_none'}">
                                <span class="high_fixed_width main_flxe vertical_line ${compare_benchmark_list}"> 
                                    <span class="high_color m_font_size18">${topLevel ? '--' : preincomerate}%</span>
                                    <span>业绩计提基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${stage_yield_list}"> 
                                    <span class="high_color m_font_size18">${topLevel ? '--' : interest_rate}</span>
                                    <span>业绩计提基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${performance_benchmarks_list}"> 
                                    <span class="high_color m_font_size18">${topLevel ? '--' : preincomerate}%</span>
                                    <span>业绩比较基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${fund_rate_list}"> 
                                    <span class="high_color m_font_size18">${topLevel ? '--' : tools.fmoney(found_rate)}%</span>
                                    <span>成立以来收益</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                    <span class="high_color m_font_size18">${topLevel ? '--' : dk_income_rate}%</span>
                                    <span>${income_period_type_desc}年化</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                    <span class="high_color m_font_size20">${topLevel ? '--' : dk_income_rate_chg}%</span>
                                    <span>${income_period_type_desc}</span>
                                </span>
                                <span class="main_flxe vertical_line flex_1">
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${topLevel ? '--' : inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${topLevel ? '--' : inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${this_year_rate_list}" style="height:32px;line-height:32px">今年来收益:${topLevel ? '--' : this_year_rate ? tools.fmoney(this_year_rate) : "--"}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${nav_list}" style="height:32px;line-height:32px">最新净值:${topLevel ? '--' : nav}</span>
                                    <span><span>${risklevel_name}</span> | <span class="${threshold_amount_list}">起购:${topLevel ? '--' : threshold_amount}万元</span></span>
                                </span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${topLevel ? 'm_golden' : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                                ${topLevel ? topLevel : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                            </span>
                        </li>
                    </ul>
                    `
            } else if (item.prod_sub_type2 == '200') {
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                // var income_period_list_chg = item.income_period_list_chg == '1' ? '' : 'display_none' //是否展示近X月涨跌幅
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                var risklevel_name = item.risklevel_name.split('(') ? item.risklevel_name.split("(")[0] : '--'
                //数据展示
                var preincomerate = item.preincomerate ? tools.fmoney(item.preincomerate) : '--' //年化标准
                var threshold_amount = item.threshold_amount ? item.threshold_amount : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                var nav = item.nav ? tools.fmoney(item.nav, 4) : '--'
                var holding_days = item.holding_days ? item.holding_days : '--'
                var per_yield = item.per_yield ? tools.fmoney(item.per_yield) : '--'
                var buy_state = item.buy_state
                var buy_state_name, btnClass
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = chooseVerson == '3' ? `敬请<br>期待` : '敬请期待';
                    btnClass = "";
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                //公募列表展示
                childrenListHtml += `
                    <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                        <li class="main_flxe vertical_line ${chooseVerson == '3' ? 'display_none' : ''}">
                            <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                <span class="${closed_period_list}">期限:${inrest_term}</span>
                                <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                                <span class="${recommended_holding_list}">建议持有${holding_days}天以上</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="main_flxe vertical_line flex_1 ${chooseVerson == '3' ? '' : 'display_none'}">
                            <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                            <p class="m_font_size12  ${chooseVerson == '3' ? 'flex' : 'display_none'}">
                                <span class="high_fixed_width main_flxe vertical_line ${compare_benchmark_list}"> 
                                    <span class="high_color m_font_size18">${preincomerate}%</span>
                                    <span>业绩计提基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${performance_benchmarks_list}"> 
                                    <span class="high_color m_font_size18">${preincomerate}%</span>
                                    <span>业绩比较基准(年化)</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${nav_list}"> 
                                    <span class="high_color m_font_size18">${nav}</span>
                                    <span>单位净值</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                    <span class="high_color m_font_size18">${dk_income_rate}%</span>
                                    <span>${income_period_type_desc}年化</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                    <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                                    <span>${income_period_type_desc}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${per_yield_list}"> 
                                    <span class="high_color m_font_size18">${per_yield}%</span>
                                    <span>上一封闭期年化收益率</span>
                                </span>
                                <span class="main_flxe vertical_line flex_1">
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${inrest_term}</span>
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">建议持有${holding_days}天以上</span>
                                    <span><span>${risklevel_name}</span> | <span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                                </span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                                <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${btnClass}">
                                ${buy_state_name}
                            </span>
                        </li>
                    </ul>
                `
            } else if (item.prod_sub_type2 == "95") { //政金债
                var threshold_amount = item.threshold_amount ? item.threshold_amount / 10000 : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none'
                childrenListHtml += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_font_size12 ">业绩计提基准(年化):<span class="m_text_red m_font_size18 ${tools.addMinusClass(item.preincomerate)}">${topLevel ? '--' : tools.fmoney(item.preincomerate)}</span>%</p>
                        <p class="m_text_999 m_font_size12">
                            <span>起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span>期限:${topLevel ? '--' : inrest_term}</span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${topLevel ? 'm_golden' : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                            ${topLevel ? topLevel : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                        </span>
                    </li>
                </ul>
                `
            } else if (item.prod_sub_type2 == "94") { // 持有期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none'
                var threshold_amount = item.threshold_amount ? item.threshold_amount / 10000 : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                childrenListHtml += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_font_size12 ">业绩计提基准(年化):<span class="m_text_red m_font_size18 ${tools.addMinusClass(item.preincomerate)}">${topLevel ? '--' : tools.fmoney(item.preincomerate)}</span>%</p>
                        <p class="m_text_999 m_font_size12">
                            <span>起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span>锁定期:${topLevel ? '--' : inrest_term}</span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${topLevel ? 'm_golden' : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                            ${topLevel ? topLevel : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                        </span>
                    </li>
                </ul>
                `
            } else if (item.prod_source == '2'){ //投顾特殊处理
                var productInfoSon = JSON.stringify(item) //二级列表接口传递数据
                var prod_per_min_amt = item.prod_per_min_amt //投资金额
                var comb_sname = item.comb_sname;
                var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none'  //是否展示特点
                var corner_marker_list = item.corner_marker_list == '1' ? '' : 'display_none'  //是否展示角标
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none'  //是否展示策略特点
                var buy_deadline_list = item.buy_deadline_list == '1' ? '' : 'display_none';
                var page_threshold_amount_list = item.page_threshold_amount_list == '1' ? '' : 'display_none'; //是否展示营销页首投金额
                var comb_risk_name = item.comb_risk_name ? item.comb_risk_name : '--';
                //数据展示
                var page_first_per_min = item.page_first_per_min ? item.page_first_per_min : '';
                var income_period_type_desc = item.income_period_type ? item.income_period_type : '--' //近多少年化

                var annual_income = item.annual_income; // 目标年化收益
                var holding_time = item.holding_time ? item.holding_time : '--' // 建议持有时长
                var characteristic = item.characteristic; // 特点
                var strategic_characteristic = item.strategic_characteristic; // 策略特点   
                var threshold_amount = item.first_per_min ? item.first_per_min : '--' //起购金额
                var corner_marker = item.corner_marker; // 角标描述
                var mechanism = item.mechanism; // 投顾机构
                var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                var buy_state = item.purchase_state;
                var buy_state_name, btnClass;
                var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅
                var income_name = item.income_name ? item.income_name : ''; //目标收益率文案
                var holding_time_name = item.holding_time_name ? item.holding_time_name : '';
                var buy_deadline = item.buy_deadline ? item.buy_deadline : '';
                // threshold_amount = page_threshold_amount_list == '1' ? page_first_per_min : threshold_amount;
                if(buy_deadline){
                    buy_deadline = tools.ftime(buy_deadline.substr(4, 4), "月") + "日 " + tools.ftime(buy_deadline.substr(8, 8), ".").substr(0, 5)
                }
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "2") {
                    buy_state_name = "预约";
                    btnClass = "";
                }
                if (buy_state == "3") {
                    buy_state_name = chooseVerson == '3' ? `敬请<br>期待` : '敬请期待';
                    btnClass = "";
                }
                if (buy_state == "4") { //其他产品为售罄
                    buy_state_name = "封闭中";
                    btnClass = "sold_out";
                }
                if (buy_state == "5") { //其他产品为售罄
                    buy_state_name = "售罄";
                    btnClass = "sold_out";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "运作中";
                    btnClass = "sold_out";
                }
                //投顾列表展示
                childrenListHtml += `
                    <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                        <li class="main_flxe vertical_line ${chooseVerson == '3' ? 'display_none' : ''}">
                            <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                            <p class="m_font_size16 color_000 prod_sname">${comb_sname}</p>
                            <p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${establish_rate}</span>%</p>
                            <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${income_rate_chg}</span>%</p>
                            <p class="m_font_size12 ${annual_income_list}">${income_name}:<span class="m_text_red m_font_size18">${annual_income}</span></p>
                            <p class="m_font_size12 ${buy_deadline_list}">截止时间:<span class="m_text_red m_font_size14">${buy_deadline}</span></p>
                            <p class="m_font_size12 ${strategic_characteristic_list}">策略特点:<span class="m_text_red">${strategic_characteristic}</span></p>
                            <p class="m_text_999 m_font_size12">
                                <span class="${page_threshold_amount_list}">起购:${page_first_per_min}元</span>
                                <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                                <span class="${recommended_holding_list}">${holding_time_name}:${holding_time}</span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                                <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="main_flxe vertical_line flex_1 ${chooseVerson == '3' ? '' : 'display_none'}">
                            <p class="m_font_size16 color_000 prod_sname">${comb_sname}</p>
                            <p class="m_font_size12  ${chooseVerson == '3' ? 'flex' : 'display_none'}">
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                                    <span class="high_color m_font_size18">${establish_rate}%</span>
                                    <span>${income_period_type_desc}年化</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                                    <span class="high_color m_font_size18">${income_rate_chg}%</span>
                                    <span>${income_period_type_desc}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${annual_income_list}"> 
                                    <span class="high_color m_font_size18">${annual_income}</span>
                                    <span>${income_name}</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${buy_deadline_list}"> 
                                    <span class="high_color m_font_size18">${buy_deadline}</span>
                                    <span>截止时间</span>
                                </span>
                                <span class="high_fixed_width main_flxe vertical_line ${strategic_characteristic_list}"> 
                                    <span class="high_color m_font_size18">${strategic_characteristic}</span>
                                    <span>策略特点</span>
                                </span>
                                <span class="main_flxe vertical_line flex_1">
                                    <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">${holding_time_name}:${holding_time}</span>
                                    <span>${comb_risk_name} | <span class="${page_threshold_amount_list}">起购:${page_first_per_min}元</span><span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                                </span>
                            </p>
                            <p class="m_font_size12 m_text_darkgray666 ${characteristic_list}">
                                <span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span>
                            </p>
                        </li>
                        <li class="classificationList_card_main_btn main_flxe flex_center">
                            <span class="main_flxe flex_center ${btnClass}">
                                ${buy_state_name}
                            </span>
                        </li>
                    </ul>
                `
            } else if (item.prod_source == '3') { //系列产品 一键分散买合集
                var income_rate_chg = item.income_rate_chg ? tools.fmoney(item.income_rate_chg) : '--' // 涨跌幅
                var establish_rate = item.establish_rate ? tools.fmoney(item.establish_rate) : '--'
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = (item.income_period_list == '1' && item.income_trait == '0') ? '' : 'display_none' //是否展示近X年化
                var income_period_list_chg = (item.income_period_list == '1' && item.income_trait == '1') ? '' : 'display_none' //是否展示涨跌幅
                var per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                // var income_period_list_chg = item.income_period_list_chg == '1' ? '' : 'display_none' //是否展示近X月涨跌幅
                var threshold_amount_list = item.threshold_amount_list == '1' ? '' : 'display_none'  //是否展示起购
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var recommended_holding_list = item.recommended_holding_list == 1 ? '' : 'display_none' //是否展示建议持有X天
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
				var holding_time = item.holding_time;
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none' //是否展示提示
                var performance_benchmarks_list = item.performance_benchmarks_list == '1' ? '' : 'display_none' //是否展示业绩比较基准
                var strategic_characteristic_list = item.strategic_characteristic_list == '1' ? '' : 'display_none' //是否展示策略特点值
                var characteristic_list = item.characteristic_list == '1' ? '' : 'display_none';//是否展示特点
                var annual_income_list = item.annual_income_list == '1' ? '' : 'display_none'; // 目标年化收益是否展示
                
                //数据展示
                var preincomerate = item.preincomerate ? tools.fmoney(item.preincomerate) : '--' //年化标准
                var threshold_amount = item.threshold_amt ? item.threshold_amt : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var income_period_type_desc = item.income_period_type_desc ? item.income_period_type_desc : '--' //近多少年化
                var dk_income_rate = item.dk_income_rate ? tools.fmoney(item.dk_income_rate) : '--'
                var dk_income_rate_chg = item.dk_income_rate_chg ? tools.fmoney(item.dk_income_rate_chg) : '--'
                var nav = item.nav ? tools.fmoney(item.nav, 4) : '--'
                var holding_days = item.holding_days ? item.holding_days : '--'
                var per_yield = item.per_yield ? tools.fmoney(item.per_yield) : '--'
                var buy_state = item.buy_state
                var buy_state_name, btnClass
                var holding_time = item.holding_time;
                var characteristic = item.characteristic
                var strategic_characteristic = item.strategic_characteristic //策略特点值
                var holding_time_name = item.holding_time_name
                var income_name = item.income_name ? item.income_name : ''; //目标收益率文案
                var annual_income = item.annual_income; // 目标年化收益
                var rate_desc = item.rate_desc; //统计收益率
                //去掉列表前两个 其中去掉 系列投顾
                let income_name_str,strategic_characteristic_str,new_pro_str;
                let risklevel_name = item.risklevel_name ? item.risklevel_name : '';
                if(chooseVerson == '3'){
                    income_name_str = (item.series_type == '2' || item.series_type == '3') ? `
                        <span class="high_fixed_width main_flxe vertical_line ${annual_income_list}"> 
                            <span class="high_color m_font_size18">${annual_income}</span>
                            <span>${income_name}</span>
                        </span>
                    `:``;
                    strategic_characteristic_str = (item.series_type == '2' || item.series_type == '3') ? `
                        <span class="main_flxe vertical_line ${strategic_characteristic_list}"> 
                            <span class="high_color m_font_size18">${strategic_characteristic}</span>
                            <span>策略特点</span>
                        </span>
                    `:``;
                    new_pro_str = (item.series_type == '2' || item.series_type == '3') ? `
                        <span class="high_fixed_width main_flxe vertical_line ${income_period_list}"> 
                            <span class="high_color m_font_size18">${item.series_type == '2' ? (dk_income_rate + '%'):rate_desc}</span>
                            <span>${income_period_type_desc}年化</span>
                        </span>
                        <span class="high_fixed_width main_flxe vertical_line ${income_period_list_chg}"> 
                            <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                            <span>${income_period_type_desc}</span>
                        </span>
                    ` : `
                        <span class="main_flxe vertical_line ${income_period_list}"> 
                            <span class="high_color m_font_size18">${dk_income_rate}%</span>
                            <span>其中${item.income_prod_name}${income_period_type_desc}年化</span>
                        </span>
                        <span class="main_flxe vertical_line ${income_period_list_chg}"> 
                            <span class="high_color m_font_size18">${dk_income_rate_chg}%</span>
                            <span>其中${item.income_prod_name}${income_period_type_desc}</span>
                        </span>
                    `
                }else{
                    income_name_str = (item.series_type == '2' || item.series_type == '3') ?`<p class="m_font_size12 ${annual_income_list}">${income_name}:<span class="m_text_red m_font_size18">${annual_income}</span></p>` : ``;
                    strategic_characteristic_str = (item.series_type == '2' || item.series_type == '3') ?`<p class="m_font_size12 ${strategic_characteristic_list}">策略特点:<span class="m_text_red">${strategic_characteristic}</span></p>`: ``;
                    new_pro_str = (item.series_type == '2' || item.series_type == '3') ? `<p class="m_font_size12 ${income_period_list}">${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${item.series_type == '2' ? (dk_income_rate + '%'):rate_desc}</span></p>
                        <p class="m_font_size12 ${income_period_list_chg}">${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>` : `<p class="m_font_size12 ${income_period_list}">其中${item.income_prod_name}${income_period_type_desc}年化:<span class="m_text_red m_font_size18">${dk_income_rate}</span>%</p>
                        <p class="m_font_size12 ${income_period_list_chg}">其中${item.income_prod_name}${income_period_type_desc}:<span class="m_text_red m_font_size18">${dk_income_rate_chg}</span>%</p>`
                }
                let characteristic_str = (item.series_type == '2' || item.series_type == '3') ? `<p class="m_font_size12 m_text_darkgray666 ${characteristic_list}"><span class="indexRemarkBg">${characteristic}&nbsp;&nbsp;&nbsp; </span></p>`: '';
                //1-购买  2-预约 3-敬请期待  4-售罄
                if (buy_state == "1") {
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if (buy_state == "6") { //定制产品买入置灰
                    buy_state_name = "购买";
                    btnClass = "";
                }
                if(item.series_type == '3'){
                    buy_state_name = '去看看';
                    risklevel_name = item.risklevel_name_customize;
                } 
                if(item.series_type == '2'){
                    risklevel_name = item.comb_risk_name;
                } 
                childrenListHtml += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line ${chooseVerson == '3' ? 'display_none' : ''}">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_text_999 m_font_size12">
                            ${new_pro_str}
                            ${income_name_str}
                            <p class="m_font_size12 ${compare_benchmark_list}">业绩计提基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${performance_benchmarks_list}">业绩比较基准(年化):<span class="m_text_red m_font_size18">${preincomerate}</span>%</p>
                            <p class="m_font_size12 ${nav_list}">单位净值:<span class="m_text_red m_font_size18">${nav}</span></p>
                            <p class="m_font_size12 ${per_yield_list}">上一封闭期年化收益率:<span class="m_text_red m_font_size18">${per_yield}</span>%</p>
                            ${strategic_characteristic_str}
                        </p>
                        <p class="m_text_999 m_font_size12">
                            <span class="${threshold_amount_list}">起购:${threshold_amount}元</span>
                            <span class="${closed_period_list}">期限:${inrest_term}</span>
                            <span class="${lock_period_list}">锁定期:${inrest_term}</span>
                            <span class="${recommended_holding_list}">${item.series_type == '2' ? holding_time_name : '建议持有'}${holding_time}</span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                            ${characteristic_str}
                    </li>
                    <li class="main_flxe vertical_line flex_1 ${chooseVerson == '3' ? '' : 'display_none'}">
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_font_size12  ${chooseVerson == '3' ? 'flex' : 'display_none'}">
                            ${new_pro_str}
                            ${income_name_str}
                            ${strategic_characteristic_str}
                            <span class="high_fixed_width main_flxe vertical_line ${strategic_characteristic_list}"> 
                                <span class="high_color m_font_size18">${strategic_characteristic}</span>
                                <span>策略特点</span>
                            </span>
                            <span class="main_flxe vertical_line flex_1">
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${closed_period_list}" style="height:32px;line-height:32px">期限:${inrest_term}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${lock_period_list}" style="height:32px;line-height:32px">锁定期:${inrest_term}</span>
                                <span class="m_font_size14 m_bold_500 m_text_393939 ${recommended_holding_list}" style="height:32px;line-height:32px">${(item.series_type == '2' || item.series_type == '3') ? holding_time_name : '建议持有'}${holding_time}</span>
                                <span>${risklevel_name} | <span class="${threshold_amount_list}">起购:${threshold_amount}元</span></span>
                            </span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                        ${characteristic_str}
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${btnClass}">
                            ${buy_state_name}
                        </span>
                    </li>
                </ul>
                `
            } else if (!item.exclusive_product_type) {
                var threshold_amount = item.threshold_amount ? item.threshold_amount / 10000 : '--' //起购金额
                var inrest_term = item.inrest_term ? item.inrest_term : '--'   //封闭期/锁定期
                var recommend_info_list = item.recommend_info_list == '1' ? '' : 'display_none'
                childrenListHtml += `
                <ul class="classificationList_card_main flex" contentType="1" operationType="1" operationId="classificationList_card_main" operationName="购买" fundCode="${fundCode}">
                    <li class="main_flxe vertical_line">
                        <em style='display: none' class='productInfo pointData'>${productInfoSon}</em>
                        <p class="m_font_size16 color_000 prod_sname">${prod_sname}</p>
                        <p class="m_font_size12 ">业绩计提基准(年化):<span class="m_text_red m_font_size18 ${tools.addMinusClass(item.preincomerate)}">${topLevel ? '--' : tools.fmoney(item.preincomerate)}</span>%</p>
                        <p class="m_text_999 m_font_size12">
                            <span>起购:${topLevel ? '--' : threshold_amount}万元</span>
                            <span>封闭期:${topLevel ? '--' : inrest_term}</span>
                        </p>
                        <p class="m_font_size12 m_text_darkgray666 ${recommend_info_list}">
                            <span class="indexRemarkBg">${recommend_info}&nbsp;&nbsp;&nbsp; </span>
                        </p>
                    </li>
                    <li class="classificationList_card_main_btn main_flxe flex_center">
                        <span class="main_flxe flex_center ${topLevel ? 'm_golden' : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnClass}">
                            ${topLevel ? topLevel : tools.priBtnObj(item.buy_state, item.prod_sub_type2,'',userChooseVerson,scene_code).btnText}
                        </span>
                    </li>
                </ul>
                `
            }
        })
        return childrenListHtml;
    }
    //判断用户是否持有该持仓
    async function getUserPro(pushData) {
        return new Promise(async (resolve, reject) => {
            service.reqFun102174(pushData, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })

    }
    //获取一级列表分类
    async function getFirstList() {
        return new Promise(async (resolve, reject) => {
            service.reqFun102117({scene_code:chooseVerson}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })

    }

    //所有专享产品
    async function getExclusiveList() {
        //获取专享产品列表
        return new Promise(async (resolve, reject) => {
            service.reqFun109002({}, async (data) => {
                if (data.error_no == '0') {
                    var res = data.results
                    resolve(res)
                } else {
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }

    //活动弹窗
    function AppProMsg() {
        service.reqFun102018({}, (data) => {
            if (data.error_no == 0) {
                var activity = appUtils.getLStorageInfo("app_activity");
                if (!data.results || !data.results[0] || !data.results[0].url || activity) {
                    if (newPopupInfo && newPopupInfo.pop_id) {
                        if (sessionStorage.pop_up_otification == '1') return;
                        activityShow(_pageId, newPopupInfo.content, newPopupInfo.type, newPopupInfo.url_type, newPopupInfo.url,newPopupInfo.pop_id, newPopupInfo.pop_id, true,newPopupInfo.name,newPopupInfo.jump_page_type);
                        dropNewPopUp();
                        return;
                    }
                }
                for (var i = 0; i < data.results.length; i++) {
                    var result = data.results[i];
                    var content = result.content; //展示内容
                    var type = result.type; //0 只弹一次 1.每天提示 2.定向弹窗  3.在某段时间内，登陆一次弹一次
                    var url_type = result.url_type; //1:內连  0:外链
                    var url = result.url; //url
                    var activityId = result.id; //ID
                    let name = result.title;
                    var activity = appUtils.getLStorageInfo("app_activity");
                    if (type == 2 && content) { //定向弹窗
                        var isShowLay = $(_pageId + " .special_pop_layer").css("display");
                        if (isShowLay != "block") {
                            $(_pageId + " #speciaDialog #src_Image img").attr("src", global.oss_url + content);
                            $(_pageId + " #speciaDialog").attr("urls", url);
                            $(_pageId + " #speciaDialog").attr("urlType", url_type);
                            //$(_pageId + " .special_pop_layer").show();
                            $(_pageId + " #speciaDialog").show();
                            $(_pageId + " #speciaDialog").attr("activityId", activityId);
                            $(_pageId + " #speciaDialog").attr("pop_id", result.pop_id);
                        }
                    } else if (type == 3) { //活动弹窗（每次进入页面都弹框）
                        if (!appUtils.getSStorageInfo("isShowAppProMsg")) {
                            appUtils.setSStorageInfo("isShowAppProMsg", true);
                            //老弹窗弹出
                            sessionStorage.old_pop_up = 1;
                            activityShow(_pageId, content, type, url_type, url, activityId,activityId, true,name,newPopupInfo.jump_page_type);
                        }
                    } else {
                        sessionStorage.old_pop_up = 1;
                        if (activity) {
                            var aid = activity.activeId; //活动ID
                            //活动相同
                            if (aid == activityId) {
                                if (type == 1) {
                                    activityShow(_pageId, content, type, url_type, url,activityId, activityId, false,name,newPopupInfo.jump_page_type);
                                }
                            } else {
                                activityShow(_pageId, content, type, url_type, url,activityId,activityId, true,name,newPopupInfo.jump_page_type);
                            }
                        } else {
                            activityShow(_pageId, content, type, url_type, url,activityId,activityId, false,name,newPopupInfo.jump_page_type);
                        }
                    }
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }
    //活动展示-
    function activityShow(_pageId, content, type, url_type, url, activityId, flag, emergentFlag,name,jump_page_type) {
        if (sessionStorage.pop_up_otification == "1" && !emergentFlag) return;
        var date = new Date();
        var isNews = isNewDay(date.getFullYear(), date.getMonth() + 1, date.getDate());
        if (flag || isNews) {
            let pop_id = activityId;
            let operationData = {pop_id:pop_id}
            let str = `<em style="display:none">${JSON.stringify(operationData)}</em>`
            var html = '<div class="activityDialog pop_layer" style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;" urls="' + url + '" name="' + name + '" urltype="' + url_type + '" activityid="' + activityId + '" jump_page_type="' + jump_page_type + '" activetype="' + type + '"><div class="index-popup" style="width: 75%;z-index: 2100;position: static;"><img id="src_Image" operationType="1" operationId="popup-description" contentType="5" operationName="弹窗" class="popup-description" src= ' + global.oss_url + content + ' style="width:100%;height:100%;">'+ str +'<hr><a operationType="1" operationId="index-popup__btn-close" operationName="关闭弹窗" contentType="5" class="index-popup__btn-close"></a></div></div>'
            $(_pageId + " #activityDialog").html(html);
            sessionStorage.pop_up_otification = "1"

        }
    }
    //判断是否是新的一天
    /* @param oyear  当前年
     * @param omonth 当前月
     * @param oday   当前天
     * return true 新的一天
     * 不对手机时间自行修改到账时间混乱判断
     */
    function isNewDay(oyear, omonth, oday) {
        var activedate = appUtils.getLStorageInfo("app_activedate");
        if (activedate) {
            var ayear = activedate.year;
            var amonth = activedate.month;
            var aday = activedate.day;
            if (oyear == ayear && omonth == amonth && oday == aday) {
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    /*
    系列产品
    * */
    function pageTo_fundsBuy(info) {
        localStorage.financial_prod_type_pro = info.financial_prod_type;
        localStorage.zone_prod_type_pro = '';
        localStorage.series_id = info.fund_code;
        localStorage.templateId_allBuy = info.prod_propagate_temp;
        // appUtils.setSStorageInfo("financial_prod_type_pro", financial_prod_type);
        // appUtils.setSStorageInfo("zone_prod_type_pro", ''); //取消二级页面缓存参数
        //公募短债系列 跳转高端定制产品页
        if(info.series_type == '3'){
            //缓存当前系列info
            appUtils.setSStorageInfo("series_info_high", info);
            return appUtils.pageInit(_page_code, "highVersion/custProducts", {});
        }
        //判断是否有营销页
        // let data = {
        //     code:financial_prod_type
        // }
        // service.reqFun199016(data, (datas) => {
        //     if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
        //     let id = datas.results[0].val;

        // })
        if (info.prod_propagate_temp && info.prod_propagate_temp != 'undefined') { //已配营销页面
            appUtils.setSStorageInfo("series_name", info.series_name);
            if(info.series_type == '2'){
                //特殊系列产品 子女
                tools.setPageToUrl('template/seriesChildrenMarketing', '1');
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        let operationId = 'riskAssessment'
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            // tools.recordEventData('1','riskQuestion','风险测评');
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    } else if (invalidFlag == '1') {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        let operationId = 'replaceIdCard'
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            // tools.recordEventData('1','updateIDCard','更新身份照片');
                            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                        }, "取消", "更换",operationId);
                    }
                    appUtils.pageInit(_page_code, "template/seriesChildrenMarketing");
                });
                
            }else{
                appUtils.pageInit(_page_code, "template/decentralizedPurchasing");
            }
        } else {  //未配置营销页面
            tools.setPageToUrl('fundSupermarket/fundsBuy', '1');
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if (perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    // tools.recordEventData('1','updateIDCard','更新身份照片');
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    let operationId = 'riskAssessment'
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        // tools.recordEventData('1','riskQuestion','风险测评');
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                appUtils.pageInit(_page_code, "fundSupermarket/fundsBuy");
            });
        }
    }
    function advisorPage(url){
        service.reqFun112019({}, function (data) {
            let error_no = data.error_no
            let error_info = data.error_info
            if (error_no == "0") {
                if(data && data.results && data.results[0]) appUtils.pageInit(_page_code, url);
            } else {
                layerUtils.iAlert(error_info);
    
            }
        });
    }
    //点击事件
    function bindPageEvent() {
        // //使用手势密码登录
        // appUtils.bindEvent($(_pageId + " .gesture"), function () {  //手势密码登录
        //     // 获取用户账户信息
        //     let param50043 = {
        //         funcNo: "50043",
        //         key: "account_password"
        //     };
        //     let firstInstall = external.callMessage(param50043);
        //     firstInstall = firstInstall.results[0].value;
        //     let account = firstInstall.substring(0, firstInstall.indexOf("_"));
        //     let setParam = {
        //         "funcNo": "50261",
        //         "moduleName": "mall",
        //         "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
        //         "account": account,
        //         "errorNum": "5",
        //         "isCanBack": "1",
        //         "lockSenconds": "60",
        //         "userImage": ""
        //     };
        //     external.callMessage(setParam);
        // });
        // 去消息
        appUtils.bindEvent($(_pageId + " .high_message"), function () {
            appUtils.pageInit(_page_code, "moreDetails/message", {});
        });
        appUtils.bindEvent($(_pageId + " .message_tip"), function (e) {
            e.preventDefault();
            e.stopPropagation();
            tools.pageTo_message_datail($(this),_page_code);
        });
        //点击出现的提示语
        appUtils.bindEvent($(_pageId + " .preTransMsg"), function () {
            const userInf = ut.getUserInf();
            const sStorageInfo = appUtils.getSStorageInfo("user");
            if(!userInf.bankAcct){
                return appUtils.pageInit(_page_code, "account/setBankCard", {});
            }
            if(sStorageInfo.idCardUploadFlag !='1'){
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return appUtils.pageInit(_page_code, "account/uploadIDCard", {});
            } 
            if (validatorUtil.isEmpty(userInf.riskLevel)) {
                return appUtils.pageInit(_page_code, "safety/riskQuestion", {});
            }
            if(sStorageInfo.perfect_info == '2' || sStorageInfo.perfect_info == '3' || sStorageInfo.perfect_info == '4'){
                return appUtils.pageInit(_page_code, "account/uploadIDCard", {});
            }
            if(sStorageInfo.hgSoonInvalidState == '4' || sStorageInfo.hgSoonInvalidState == '6'){
                appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
                return appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor1");
            }
        });
        //认证身份证
        appUtils.bindEvent($(_pageId + " .tip .uploadIDCard"), () => {
            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        })

        //合格投资人认证
        appUtils.bindEvent($(_pageId + " .exprie_tip #decertification"), () => {
            appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
            appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor1");
        })

        //关闭更新
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + ' .update_prompt').hide();
        });
        //点击切换标准版
        // appUtils.bindEvent($(_pageId + " .switchVer"), function () {
        //     if (!common.loginInter()) return;
        //     appUtils.setSStorageInfo("plan_type",'');
        //     //当前用户选中的版本，不重置
        //     common.setLocalStorage("userChooseRefresh",'0');
        //     // let scene_code = common.getLocalStorage("scene_code") ? common.getLocalStorage("scene_code") : ''; //页面版本类型 1标准版 X版
        //     if(scene_code == '3'){
        //         //高端版本特殊处理，只能返回到高端版本
        //         tools.recordEventData('1','snowball_3','返回高端版本');
        //         common.setLocalStorage("userChooseVerson",'3');
        //     }else{
        //         tools.recordEventData('1','snowball_' + (chooseVerson == '1' ? '2' : '1'),$(this).attr("plan_type"),chooseVerson == '1' ? '切换到新版' : '切换到标准版');
        //         common.setLocalStorage("userChooseVerson",chooseVerson == '1' ? '2' : '1');
        //     }
        //     setTimeout(async () => {
        //         userIndex.destroy();
        //         location.reload();
        //     }, 0);
        // });
        //点击进入主页 开启计划
        // appUtils.bindEvent($(_pageId + " .marketIndexFooter"), ()=> {
        //     if (ut.getUserInf()) {
        //         common.setLocalStorage("snowballMarketShow",'1');
        //         common.setLocalStorage("sceneRefresh",'0');     
        //         common.setLocalStorage("userChooseRefresh",'0');
        //         setTimeout(async () => {
        //             userIndex.destroy();
        //             location.reload();
        //         }, 0);
        //     } else {
        //         clearPath();
        //         let fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
        //         if (fingerprintPwd_flag == '1') {
        //             if(chooseVerson == '2' && !snowballMarketShow && !snowballMarketShow.length) return fingerprint.showLoginDig(_pageId, _page_code,'1');
        //             //初始化指纹弹窗
        //             fingerprint.showLoginDig(_pageId, _page_code);
        //         } else {
        //             // 当前场景营销页点击登录
        //             if(chooseVerson == '2' && !snowballMarketShow && !snowballMarketShow.length) return common.gestureLogin('','1');
        //             common.gestureLogin();
        //         }
        //     }
        // });
        //退出登录
        appUtils.bindEvent($(_pageId + " #loginOut"), function () {
            if (ut.getUserInf()) {
                //埋点ID
                let operationId = 'loginOut';
                layerUtils.iConfirm("确定退出吗？", function () {
                    service.reqFun1100004({}, function (data) {
                        if (data.error_no == "0") {
                            //退出登录埋点
                            // tools.recordEventData('1','loginOut','退出登录');
                            tools.loginOutQy();
                            appUtils.clearSStorage(true);
                            let arr = ["login/userIndexs"];
                            $(_pageId + " #yuanhui").hide();
                            appUtils.setSStorageInfo("routerList", arr)
                            $(_pageId + " .tip").hide();
                            $(_pageId + " .mail").hide();
                            $(_pageId + " #loginOut").text("登录");
                            if(chooseVerson == '1' || chooseVerson == '3') setData(0, 0);
                            if(chooseVerson == '3') $(_pageId + " .high_index_top .info").hide();
                        } else {
                            if(chooseVerson == '1' || chooseVerson == '3') setData(0, 0);
                            layerUtils.iMsg(-1, data.error_info);
                        }
                    });
                    tools.guanggao({ _pageId: _pageId, group_id: chooseVerson == '1' ? '33' : snowballInit.group_id_loginBefore });
                    $(_pageId + " #yuanhui").hide();
                    $(_pageId + " #inviteFriends").show();
                }, function () {
                },'确定','取消',operationId);
            } else {
                clearPath();
                let fingerprintPwd_flag = common.getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
                if (fingerprintPwd_flag == '1') {
                    if(chooseVerson == '2' && !snowballMarketShow && !snowballMarketShow.length) return fingerprint.showLoginDig(_pageId, _page_code,'1');
                    //初始化指纹弹窗
                    fingerprint.showLoginDig(_pageId, _page_code);
                    //tools.showLoginDig()
                } else {
                    // 当前场景营销页点击登录
                    if(chooseVerson == '2' && !snowballMarketShow && !snowballMarketShow.length) return common.gestureLogin('','1');
                    common.gestureLogin();
                }
            }
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_page_code)
        });
        //bannear 点击链接
        appUtils.preBindEvent($(_pageId + " #scroller_index2"), ".banner_list", function (e) {
            e.stopPropagation();
            e.preventDefault();
            // if (!common.loginInter()) return;
            var file_type = $(this).attr("file_type"); // 链接类型 0 内链 1 外链 2 授权登录
            var url = $(this).attr("url");
            var file_state = $(this).attr("file_state"); //是否有效 0 无效 1 有效
            var name = $(this).attr("name");
            var description = $(this).attr("description");
            var picture = $(this).attr("picture");
            var group_id = $(this).attr("group_id");
            var banner_id = $(this).attr("banner_id");
            var qualified_investor_visible = $(this).attr("qualified_investor_visible");
            /**
             * qualified_investor_visible 合格投资人确认弹窗
             * userAuthenticationStatus 是否认证过
             */
            if(qualified_investor_visible && qualified_investor_visible == '1' && userAuthenticationStatus && userAuthenticationStatus == '0'){
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                is_banner_pageTo = true;
                bannerPageIntoData = {file_type,url,file_state,name,description,picture,group_id,banner_id,qualified_investor_visible}
                return $(_pageId + ".qualifiedInvestor").show();
            }
            if (file_type == "0" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", url, {
                    "group_id": group_id,
                    "banner_id": banner_id,
                });
                return;
            }
            // 是否是有效内外链接
            if (file_type == "1" && file_state == "1" && url) {
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "group_id": group_id,
                    "banner_id": banner_id,
                    "name": name,
                    "description": description,
                });
                return;
            }
            // 登录授权
            if (file_type == "2" && file_state == "1") {
                if (!common.loginInter()) return;
                if (!/^(http|https):/.test(url)) {
                    if (url.indexOf("?") > -1) {
                        var skip_url = url.split("?")[0];
                        var parameter = url.split("?")[1];
                        var parameter_arr = parameter.split("&"); //各个参数放到数组里
                        var urlInfo = {};//url的参数信息
                        for (var i = 0; i < parameter_arr.length; i++) {
                            num = parameter_arr[i].indexOf("=");
                            if (num > 0) {
                                name = parameter_arr[i].substring(0, num);
                                value = parameter_arr[i].substr(num + 1);
                                urlInfo[name] = value;
                                urlInfo["group_id"] = group_id;
                                urlInfo["banner_id"] = banner_id;
                            }
                        }
                        if (url.indexOf("activity/fundLuckdrawnew") > -1) { urlInfo['channel'] = "jjcf_app"; urlInfo['type'] = "luckDraw" }
                        appUtils.pageInit("login/userIndexs", skip_url, urlInfo);

                    } else {
                        appUtils.pageInit("login/userIndexs", url);
                    }
                    return;
                }
                if (url.indexOf("activity/fundLuckdraw") > -1) {
                    common.setLocalStorage("activityInfo", {
                        activity_id: "2505",
                        group_id: group_id,
                        banner_id: banner_id,
                        cust_no: ut.getUserInf().custNo,
                        channel: "jjcf_app",
                        type: "luckDraw",
                        mobile: ut.getUserInf().mobileWhole,
                    })
                    var data = external.callMessage({
                        "funcNo": "50043",
                        "moduleName": "mall",
                        "key": "activityInfo",
                    })
                }
                appUtils.pageInit("login/userIndexs", "guide/advertisement", {
                    "url": url,
                    "name": name,
                    "description": description,
                    "group_id": group_id,
                    "banner_id": banner_id
                });
            }
            // 小程序跳转
            if (file_type == "3" && url) {
                tools.jump_applet(url);
                return;
            }
        }, 'click');

        //关闭更新
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + ' .update_prompt').hide();
        });

        //关闭特定活动弹窗
        appUtils.bindEvent($(_pageId + " #speciaDialog .index-popup__btn-close"), function () {
            //调用接口，使弹框值出现一次，messageType:
            var id = $(_pageId + " #speciaDialog").attr("activityId");
            service.reqFun101029({ "id": id }, function () {
                $(_pageId + " #updateLevel").hide();
                $(_pageId + " .updateLevel_layer").hide();
                var user = ut.getUserInf();
                user['messageInfo'] = "";
                ut.saveUserInf(user);
                $(_pageId + " #speciaDialog").hide();
                // $(_pageId + " .pop_layer").hide();
            });
        });

        //定向弹窗跳转
        appUtils.bindEvent($(_pageId + " #speciaDialog .popup-description"), function () {
            var url = $(this).parents("#speciaDialog").attr("urls");
            var file_type = $(this).parents("#speciaDialog").attr("urltype");
            //调用接口，使弹框值出现一次，messageType:1 晋金宝升级 2 定向弹窗
            var id = $(_pageId + " #speciaDialog").attr("activityId");
            var pop_id = $(_pageId + " #speciaDialog").attr("pop_id");
            var name = $(_pageId + " #speciaDialog").attr("name");
            service.reqFun101029({ "id": id }, function () {
                $(_pageId + " #updateLevel").hide();
                $(_pageId + " .updateLevel_layer").hide();
                $(_pageId + " #speciaDialog").hide();
                // $(_pageId + " .pop_layer").hide();
                service.reqFun101083({ msg_id: pop_id }, (data) => {
                    if (data.error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                });
                // 是否是有效内连接
                if (file_type == "0" && url != "" && url != undefined && url != null) {
                    if (url.indexOf("activity/marketing") > -1) {
                        if (url.indexOf("?") > -1) {
                            var skip_url = url.split("?")[0];
                            var parameter = url.split("?")[1];
                            var parameter_arr = parameter.split("&"); //各个参数放到数组里
                            var urlInfo = {};//url的参数信息
                            for (var i = 0; i < parameter_arr.length; i++) {
                                num = parameter_arr[i].indexOf("=");
                                if (num > 0) {
                                    name = parameter_arr[i].substring(0, num);
                                    value = parameter_arr[i].substr(num + 1);
                                    urlInfo[name] = value;
                                }
                            }
                            appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                        }
                        return;
                    }
                    if (url.indexOf("activity/fundLuckdrawnew") > -1) {
                        if (url.indexOf("?") > -1) {
                            var skip_url = url.split("?")[0];
                            var parameter = url.split("?")[1];
                            var parameter_arr = parameter.split("&"); //各个参数放到数组里
                            var urlInfo = {};//url的参数信息
                            for (var i = 0; i < parameter_arr.length; i++) {
                                num = parameter_arr[i].indexOf("=");
                                if (num > 0) {
                                    name = parameter_arr[i].substring(0, num);
                                    value = parameter_arr[i].substr(num + 1);
                                    urlInfo[name] = value;
                                }
                            }
                            if (url.indexOf("activity/fundLuckdrawnew") > -1) {
                                urlInfo['channel'] = "jjcf_app";
                                urlInfo['type'] = "luckDraw";
                            }
                            appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
                        } else {
                            appUtils.pageInit("login/userIndexs", url);
                        }
                        return;
                    }
                    appUtils.pageInit(_page_code, url, {});
                }
                // 是否是有效外链
                if (file_type == "1" && url != "" && url != undefined && url != null) {
                    if (url.indexOf("activity/fundLuckdraw") > -1) {
                        common.setLocalStorage("activityInfo", {
                            activity_id: "2505",
                            cust_no: ut.getUserInf().custNo,
                            channel: "jjcf_app",
                            type: "luckDraw",
                            mobile: ut.getUserInf().mobileWhole,
                        })
                        var data = external.callMessage({
                            "funcNo": "50043",
                            "moduleName": "mall",
                            "key": "activityInfo",
                        })
                    }
                    // appUtils.pageInit(_page_code, "guide/advertisement", {
                    //     "url": url,
                    //     "prePage_code": _page_code
                    // });
                    tools.livePageTo(null, null, url, null, _page_code, '晋金财富');
                }

            });
        });

        //关闭活动弹窗
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog .index-popup__btn-close", function () {
            var date = new Date();
            var activityId = $(this).parents(".activityDialog").attr("activityId");
            if ($(this).parents(".activityDialog").attr("activetype") !== "3") {
                
                var activetype = $(this).parents(".activityDialog").attr("activetype");
                if (sessionStorage.old_pop_up == 1) {
                    appUtils.setLStorageInfo("app_activity", {
                        "activeId": activityId,
                        "activetype": activetype
                    });
                    appUtils.setLStorageInfo("app_activedate", {
                        "year": date.getFullYear(),
                        "month": date.getMonth() + 1,
                        "day": date.getDate()
                    });
                    sessionStorage.old_pop_up = null;
                }

            }
            $(this).parents(".activityDialog").remove();
        }, 'click');
        //分享到小程序
        appUtils.bindEvent($(_pageId + " #shareXCX"), function () {
            let userInfo = ut.getUserInf();
            //获取用户性别
            // let gender = userInfo.gender == "1" ? "先生" : "女士";
            //获取用户姓
            // let inviterName = userInfo.name ?  userInfo.name.split('')[0] : '';
            //获取用户手机号
            let inviterMobile;
            // inviterMobile = common.desEncrypt("inviterMobile", '15635171353');
            //直接跳转小程序
            // if(activityInfo && activityInfo.shareTemplate && !ewmInfo.share_url.includes('#')){
                
            // }else{
            //     $(_pageId + " #pop_layer").show();
            // }
            let param = {
                    funcNo:'50231',
                    shareType:'22', 
                    title:'晋金财富',
                    link:'https://www.baidu.com',
                    webpageUrl:'https://www.baidu.com',
                    content:'分享给朋友',
                    userName:global.appletAuthentication.appId,
                    description:"晋金财富",
                    type:global.appletAuthentication.miniprogramType,
                    shareContentType:'10',
                    path:`pages/login/userRegistered/userRegistered?mobile=${'15635171353'}`,
                    imgUrl:'https://m.xintongfund.com/m/mall/images/120.png',
                }
                external.callMessage(param);
        });
        //活动弹窗跳转
        appUtils.preBindEvent($(_pageId + " #activityDialog"), ".activityDialog .popup-description", async function () {
            var date = new Date();
            if ($(this).parents(".activityDialog").attr("activetype") !== "3") {
                var activityId = $(this).parents(".activityDialog").attr("activityId");
                var activetype = $(this).parents(".activityDialog").attr("activetype");
                if (sessionStorage.old_pop_up == 1) {
                    appUtils.setLStorageInfo("app_activity", {
                        "activeId": activityId,
                        "activetype": activetype
                    });
                    appUtils.setLStorageInfo("app_activedate", {
                        "year": date.getFullYear(),
                        "month": date.getMonth() + 1,
                        "day": date.getDate()
                    });
                    sessionStorage.old_pop_up = null;
                }
            }
            var url = $(this).parents(".activityDialog").attr("urls");
            var file_type = $(this).parents(".activityDialog").attr("urltype");
            var activityId = $(this).parents(".activityDialog").attr("activityId");
            var name = $(this).parents(".activityDialog").attr("name");
            var jump_page_type = $(this).parents(".activityDialog").attr("jump_page_type");
            $(this).parents(".activityDialog").remove();
            service.reqFun101083({ msg_id: activityId }, (data) => {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
            });
            //仅图片特殊处理
            if (file_type == "0" && !url) return tools.jump_page(_page_code, newPopupInfo, userAuthenticationStatus, _pageId);
            //自定义链接跳转
            if (file_type == "0" && url && jump_page_type == '5'){
                if(url == 'senceId=2'){
                    //弹窗跳转新页面
                    return pageInitIndex();
                }
                var skip_url = url.split("?")[0];
                var parameter = url.split("?")[1];
                var urlInfo = {};//url的参数信息
                if(parameter){
                    var parameter_arr = parameter.split("&"); //各个参数放到数组里
                    for (var i = 0; i < parameter_arr.length; i++) {
                        num = parameter_arr[i].indexOf("=");
                        if (num > 0) {
                            name = parameter_arr[i].substring(0, num);
                            value = parameter_arr[i].substr(num + 1);
                            urlInfo[name] = value;
                        }
                    }
                }
                //pageType 判断页面类型特殊处理 1 财富顾问页面
                if(urlInfo.pageType == '1') return advisorPage(skip_url)
                return appUtils.pageInit("login/userIndexs", skip_url, urlInfo);
            }
            // 是否是有效内连接
            if (file_type == "0" && url) {
                tools.jump_page(_page_code, newPopupInfo, userAuthenticationStatus, _pageId);
            }
            // 是否是有效外链
            if (file_type == "1" && url) {
                if (url.indexOf("activity/fundLuckdraw") > -1) {
                    common.setLocalStorage("activityInfo", {
                        activity_id: "2505",
                        cust_no: ut.getUserInf().custNo,
                        channel: "jjcf_app",
                        type: "luckDraw",
                        mobile: ut.getUserInf().mobileWhole,
                    })
                    var data = external.callMessage({
                        "funcNo": "50043",
                        "moduleName": "mall",
                        "key": "activityInfo",
                    })
                }
                tools.livePageTo(null, null, url, null, _page_code, '晋金财富');
            }
            // 小程序跳转
            if (file_type == "3" && url) {
                tools.jump_applet(url);
                return;
            }
        }, 'click');

        //充值
        appUtils.bindEvent($(_pageId + " #recharge"), function () {
            // tools.clickPoint(_pageId, _page_code, 'recharge');
            tools.setPageToUrl('thfund/inputRechargePwd', '1')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if (perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    let operationId = 'riskAssessment'
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                appUtils.pageInit(_page_code, "thfund/inputRechargePwd", {});
            });
        });

        //取现
        appUtils.bindEvent($(_pageId + " #enchashment"), function () {
            // tools.clickPoint(_pageId, _page_code, 'enchashment')
            tools.setPageToUrl('thfund/enchashment', '3')
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            if (perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    // tools.recordEventData('1','updateIDCard','更新身份照片');
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            //查询是否已开银行账户
            service.reqFun151110({ bank_channel_code: "" }, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var results = data.results[0];
                if (results.bank_flag == "0") { //未开银行账户
                    common.changeCardInter(function () {
                        appUtils.pageInit(_page_code, "thfund/enchashment");
                    })
                    return;
                }
                service.reqFun151107({}, function (data) { //查询银行资产
                    if (data.error_no != "0") {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var results = data.results[0];
                    if (results && results.total_yue > 0) { //银行余额大于0，进入取现主页
                        layerUtils.iLoading(false);
                        appUtils.pageInit(_page_code, "thfund/enchashmentHome");
                    } else {
                        common.changeCardInter(function () {
                            appUtils.pageInit(_page_code, "thfund/enchashment");
                        })
                    }
                }, { isLastReq: false })
            }, { isLastReq: false })

        });

        //基金超市
        appUtils.bindEvent($(_pageId + " #institFinancial"), function () {
            // tools.clickPoint(_pageId, _page_code, 'institFinancial');
            appUtils.setSStorageInfo("fund_market_type", '');   //存储分类一级内容
            appUtils.setSStorageInfo("fund_market_zone_prod_type", '');   //存储分类一级内容
            appUtils.pageInit(_page_code, "fundSupermarket/index", {
                // pageInfo: '02'
            });
        });

        //同行好友
        appUtils.bindEvent($(_pageId + " #inviteFriends"), function () {
            // tools.clickPoint(_pageId, _page_code, 'inviteFriends')
            tools.setPageToUrl('vipBenefits/index', '2');
            if (!common.loginInter()) return;
            appUtils.pageInit(_page_code, "vipBenefits/index", {});
        });

        //跳转固收
        appUtils.bindEvent($(_pageId + " #fixedIncome"), function () {
            // tools.clickPoint(_pageId, _page_code, 'fixedIncome')
            appUtils.setSStorageInfo("productType", '02') //存储类型
            appUtils.pageInit(_page_code, "login/listMorePage", {});
        });

        //跳转稳健理财
        appUtils.bindEvent($(_pageId + " #stableMoney"), function () {
            // tools.clickPoint(_pageId, _page_code, 'stableMoney')
            appUtils.setSStorageInfo("productType", '03') //存储类型
            appUtils.pageInit(_page_code, "login/listMorePage", {});
        });

        //跳转权益投资
        appUtils.bindEvent($(_pageId + " #longTermMoney"), function () {
            // tools.clickPoint(_pageId, _page_code, 'longTermMoney')
            appUtils.setSStorageInfo("productType", '04') //存储类型
            appUtils.pageInit(_page_code, "login/listMorePage", {});
        });

        //转让专区
        appUtils.bindEvent($(_pageId + " #transferPro"), function () {
            // tools.clickPoint(_pageId, _page_code, 'transferPro')
            tools.setPageToUrl('login/listMorePage', '2');
            appUtils.setSStorageInfo("productType", '06') //存储类型
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                let operationId = 'riskAssessment'
                layerUtils.iConfirm("您还未进行风险测评", function () {
                    // tools.recordEventData('1','riskQuestion','风险测评');
                    appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                }, function () {
                }, "去测评", "取消",operationId);
                return;
            } else if (invalidFlag == '1') {
                pageTo_evaluation()
                return
            }
            // if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "login/listMorePage", {});
        });
        //分类列表或者图片跳转页面
        appUtils.preBindEvent($(_pageId + " .homePageIndex_classificationList"), ".classificationList_card .classificationList_card_top", function () {

            var productInfo = JSON.parse($(this).find("em").text()); //存储数据格式
            var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
            if (productInfo.financial_prod_type == '06') { //跳转基金超市
                appUtils.setSStorageInfo("fund_market_zone_prod_type", '');   //存储分类一级内容
                appUtils.setSStorageInfo("fund_market_type", '');   //存储分类一级内容
                return appUtils.pageInit(_page_code, "fundSupermarket/index", {
                    // pageInfo: '02'
                });
            }
            if (productInfo.financial_prod_type == '01') {
                var url = global.oss_url + productInfo.img_url
                if (!productInfo.img_url) return
                var zone_name = productInfo.classify_name
                appUtils.pageInit(_page_code, "login/showImgMarket", {
                    imgUrl: url, zoneName: zone_name
                });
                //投顾专区
            } else if (productInfo.financial_prod_type == '07') {
                appUtils.pageInit(_page_code, "combProduct/combProdList", {});
            } else if (productInfo.financial_prod_type != '01' && productInfo.financial_prod_type) {
                appUtils.setSStorageInfo("productType", productInfo.financial_prod_type) //存储类型
                appUtils.pageInit(_page_code, "login/listMorePage", {});
            }
        }, 'click');
        //点击问号（专享产品特定按钮）
        appUtils.preBindEvent($(_pageId + " .homePageIndex_classificationList"), ".classificationList_card .thirty_tips", function (e) {
            e.preventDefault();
            e.stopPropagation();
            layerUtils.iAlert('综合收益率 = 基础收益 + 积分奖励');
        }, 'click');
        //跳转产品详情页面
        appUtils.preBindEvent($(_pageId + " .homePageIndex_classificationList"), ".classificationList_card .classificationList_card_main", function () {
            var productInfo = JSON.parse($(this).find("em").text()); //存储数据格式
            let parentsHtml = $(this).parent().parent().children('.classificationList_card_top')[0]
            let parentsHtmlData = JSON.parse($(parentsHtml).find("em").text());
            if (productInfo.prod_source == '3') { //系列产品
                appUtils.setSStorageInfo("series_info", productInfo);   //存储分类一级内容
                return pageTo_fundsBuy(productInfo);
            }
            if (parentsHtmlData.financial_prod_type == "07" && parentsHtmlData.is_link_zone == '1') {
                return appUtils.pageInit(_page_code, "combProduct/combProdList", {});
            } else if (parentsHtmlData.is_link_zone == '1') {
                appUtils.setSStorageInfo("fund_market_zone_prod_type", '');   //存储分类一级内容
                appUtils.setSStorageInfo("fund_market_type", '');   //存储分类一级内容
                return appUtils.pageInit(_page_code, "fundSupermarket/index", {
                    pageInfo: productInfo.fund_market_type
                });
            }

            var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
            appUtils.setSStorageInfo("productInfo", productInfo);   //存储分类一级内容
            appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
            appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
            appUtils.setSStorageInfo("financial_prod_type", productInfo.financial_prod_type);
            if (productInfo.prod_source == '2') { // 投顾 
                appUtils.setSStorageInfo("combProductInfo", productInfo);   //存储分类一级内容
                if (productInfo.prod_propagate_temp) {
                    tools.setPageToUrl('combProduct/combProdMarketing', '1')
                    
                } else {
                    tools.setPageToUrl('combProduct/combProdDetail', '1')
                }
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                // var invalidFlag = appUtils.getSStorageInfo("user") ? appUtils.getSStorageInfo("user").invalidFlag : null;
                common.changeCardInter(function () {
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        let operationId = 'riskAssessment'
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            // tools.recordEventData('1','riskQuestion','风险测评');
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    } else if (invalidFlag == '1') {
                        pageTo_evaluation()
                        return
                    }
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        let operationId = 'replaceIdCard'
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            // tools.recordEventData('1','updateIDCard','更新身份照片');
                            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                        }, "取消", "更换",operationId);
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.comb_code);

                    if (productInfo.prod_propagate_temp) {
                        appUtils.pageInit(_page_code, "combProduct/combProdMarketing");
                    } else {
                        //缓存当前是否为系列投顾从产品
                        appUtils.setSStorageInfo("isSeriesComb", '0');
                        appUtils.pageInit(_page_code, "combProduct/combProdDetail");
                    }
                });
                return;
            } else if (productInfo.fund_code == '000709') {
                //跳转晋金宝
                tools.setPageToUrl('thfund/myProfit', '1')
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
                common.changeCardInter(function () {
                    //到期3个月后提示
                    if (perfect_info == 4) {
                        let operationId = 'replaceIdCard'
                        return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                            // tools.recordEventData('1','updateIDCard','更新身份照片');
                            appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                        }, "取消", "更换",operationId);
                    }
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        let operationId = 'riskAssessment'
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            // tools.recordEventData('1','riskQuestion','风险测评');
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    } else if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) {
                        pageTo_evaluation()
                        return
                    }
                    
                    // appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                    // appUtils.setSStorageInfo("prod_sub_type", productInfo.prod_sub_type);
                    appUtils.pageInit(_page_code, 'thfund/myProfit', {});
                });
                return;
            } else if (productInfo.exclusive_product_type == '03' || productInfo.exclusive_product_type == '05') {
                clearPath();
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                if ((productInfo.buy_state == '1' || productInfo.buy_state == '2') && (invalidFlag == '1')) {
                    pageTo_evaluation()
                    return
                }
                if (productInfo.buy_state == "0" && productInfo.exclusive_product_type != "03") { // 不包括货基
                    layerUtils.iAlert("敬请期待");
                    return;
                }
                if (productInfo.buy_state == "2" && productInfo.exclusive_product_type != "03") { // 不包括货币基金
                    layerUtils.iAlert("产品已售罄");
                    return;
                }
                productInfo.fund_code = productInfo.prod_id;//产品编码
                common.changeCardInter(function () {
                    let operationId = 'riskAssessment'
                    if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                        layerUtils.iConfirm("您还未进行风险测评", function () {
                            // tools.recordEventData('1','riskQuestion','风险测评');
                            appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                        }, function () {
                        }, "去测评", "取消",operationId);
                        return;
                    }
                    appUtils.setSStorageInfo("fund_code", productInfo.prod_id);
                    productInfo["prod_sub_type"] = "10";
                    appUtils.setSStorageInfo("productInfo", productInfo);
                    appUtils.pageInit(_page_code, "inclusive/moneytaryPurchase");
                });
                return;
            } else if (productInfo.prod_sub_type2 == '200') {   //公募
                if (productInfo.prod_propagate_temp) {
                    tools.jumpMarketingPage(_page_code, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpDetailPage(_page_code, productInfo.prod_sub_type, productInfo.prod_sub_type2)
                }
            } else if (productInfo.prod_sub_type2 == '100') {   //私募
                clearPath();
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                if (userAuthenticationStatus == '0') {
                    is_banner_pageTo = false;
                    return $(_pageId + ".qualifiedInvestor").show();
                }
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                if (productInfo.prod_propagate_temp) {
                    tools.jumpMarketingPage(_page_code, productInfo.prod_sub_type2,);
                } else {
                    tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
                }
            } else {  //私募其他
                if (userAuthenticationStatus == '0') {
                    is_banner_pageTo = false;
                    return $(_pageId + ".qualifiedInvestor").show();
                }
                clearPath();
                if (!common.loginInter()) return;
                if (!ut.hasBindCard(_page_code)) return;
                //校验用户是否上传过身份证照片
                if(!ut.getUploadStatus()){
                    let operationId = 'noUploadIdCard'
                    appUtils.setSStorageInfo("noUploadIdCard", '1');
                    return layerUtils.iConfirm("您还未上传身份证照片", function () {
                        appUtils.pageInit(_page_code, "account/uploadIDCard",{});
                    }, function () {
                    }, "去上传", "取消",operationId);
                }
                tools.jumpPriDetailPage(_page_code, productInfo.prod_sub_type2);
            }
            // appUtils.pageInit(_page_code, "login/listMorePage", {});
        }, 'click');
        //点击我的
        appUtils.bindEvent($(_pageId + " #wode"), function () {
            tools.setPageToUrl('thfund/inputRechargePwd', '4');
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            if (ut.getUserInf().bankAcct) {
                appUtils.pageInit(_page_code, "account/myAccount", {});
            } else {
                appUtils.pageInit(_page_code, "account/myAccountNoBind", {});
            }
        });
        //直接跳转消息详情 
        appUtils.preBindEvent($(_pageId + " .messgae"), ".main_message", function (e) {
            e.preventDefault();
            e.stopPropagation();
            tools.pageTo_message_datail($(this),_page_code)
        }, 'click');
        appUtils.preBindEvent($(_pageId + " .high"), ".info_msg .msg_details", function (e) {
            e.preventDefault();
            e.stopPropagation();
            tools.pageTo_message_datail($(this),_page_code)
        }, 'click');
        //跳转到学投资页面
        // appUtils.bindEvent($(_pageId + " #Learn"), function () {
        //     tools.setPageToUrl('thfund/inputRechargePwd', '5')
        //     if (!common.loginInter()) return;
        //     //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
        //     if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
        //     //页面跳转前存储当前时间戳
        //     localStorage.toPageTime = new Date().getTime();
        //     appUtils.pageInit(_page_code, "liveBroadcast/index", {});
        // });
        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_page_code, "moreDetails/more", {});
        });
        // 用户合格投资人弹窗确认
        appUtils.bindEvent($(_pageId + " .verify_btn"), function () {
            service.reqFun101038({}, function (datas) {
                if (datas.error_no == 0) {
                    if (sessionStorage.digUserAuthenticationStatus == '1') {
                        appUtils.pageInit("login/userIndexs", sessionStorage.digUserAuthenticationUrl);
                        sessionStorage.digUserAuthenticationStatus = '';
                        sessionStorage.digUserAuthenticationUrl = '';
                        return;
                    }
                    if(is_banner_pageTo){
                        //通过banner点击的合格投资者确认弹窗跳转逻辑
                        return banner_pageTo();
                    }
                    $(_pageId + ".qualifiedInvestor").hide();
                    userAuthenticationStatus = '1'
                    appUtils.setSStorageInfo("isAuthentication", 1)
                    setData(1, 1)
                } else {
                    layerUtils.iAlert(datas.error_info);
                }
            });
        });
        //源晖专享
        appUtils.bindEvent($(_pageId + " #yuanhui"), function () {
            // tools.clickPoint(_pageId, _page_code, 'yuanhui');
            tools.setPageToUrl('yuanhui/fundList', '1');
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            appUtils.pageInit(_page_code, "yuanhui/fundList", {});
        });
        // 用户合格投资人弹窗取消
        appUtils.bindEvent($(_pageId + " .cancel_btn"), function () {
            appUtils.clearSStorage("fund_code");
            appUtils.clearSStorage("productInfo");
            $(_pageId + ".qualifiedInvestor").hide();
        });
        // 系列产品
        appUtils.preBindEvent($(_pageId + " .homePageIndex_classificationList"), ".userIndexBtn", function (e) {
            pageTo_fundsBuy($(this).attr("financial_prod_type"));
        });
        // 场景产品详情
        // appUtils.preBindEvent($(_pageId + " .sceneList"), ".card", function (e) {
        //     let pageInfo = JSON.parse($(this).find("em").text()); //存储数据格式
        //     appUtils.setSStorageInfo("isSeriesComb", '1');
        //     let productInfo = {
        //         fund_code:pageInfo.income_prod
        //     }
        //     tools.recordEventData('1','sceneDetail_' + pageInfo.income_prod,'宣传页产品详情');
        //     appUtils.setSStorageInfo("productInfo",productInfo);
        //     //标记用户首次进入 场景产品详情
        //     appUtils.setSStorageInfo("firstSceneCombProdDetail",'1');
        //     appUtils.pageInit(_page_code, "scene/combProdDetail",{});
        // });
        //跳转开启定投页面
        // appUtils.preBindEvent($(_pageId + " .wallet_product"), ".walletCard_product",  function(e) {
        //     let that_ = $(this);
        //     if (!common.loginInter()) return;
        //     if (!ut.hasBindCard(_page_code)) return;
        //     //校验用户是否上传过身份证照片
        //     if(!ut.getUploadStatus()){
        //         let operationId = 'noUploadIdCard'
        //         appUtils.setSStorageInfo("noUploadIdCard", '1');
        //         return layerUtils.iConfirm("您还未上传身份证照片", function () {
        //             appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        //         }, function () {
        //         }, "去上传", "取消",operationId);
        //     }
        //     var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
        //     var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            
        //     //到期3个月后提示
        //     if (perfect_info == 4) {
        //         let operationId = 'replaceIdCard'
        //         return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
        //             // tools.recordEventData('1','updateIDCard','更新身份照片');
        //             appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        //         }, "取消", "更换",operationId);
        //     }
        //     common.changeCardInter( function() {
        //         if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
        //             let operationId = 'riskAssessment'
        //             layerUtils.iConfirm("您还未进行风险测评", function () {
        //                 // tools.recordEventData('1','riskQuestion','风险测评');
        //                 appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        //             }, function () {
        //             }, "去测评", "取消",operationId);
        //             return;
        //         } else if (invalidFlag == '1') {
        //             pageTo_evaluation()
        //             return
        //         }
        //         //重置开启计划持仓
        //         appUtils.setSStorageInfo("pageInputCasting", {});
        //         let sceneInfo = {
        //             plan_type:that_.attr("plan_type"),
        //             plan_id:that_.attr("plan_id"),
        //             hold_id:that_.attr("hold_id"),
        //             pageTitle:that_.attr("pageTitle"),
        //             bgColor:that_.attr("bgColor"),
        //             select_id:that_.attr("select_id"),
        //         }
        //         appUtils.setSStorageInfo("sceneInfo",sceneInfo);
        //         service.reqFun102212({plan_type: sceneInfo.plan_type}, async (data) => {
        //             if (data.error_no == '0') {
        //                 let list = data.results;
        //                 if(list && list.length){
        //                     //跳转持仓
        //                     tools.recordEventData('1','snowball_' + that_.attr("plan_type"),'攒钱计划持仓');
        //                     appUtils.pageInit(_page_code, "scene/sceneHold");
        //                 }else{
        //                     //跳转开启
        //                     tools.recordEventData('1','snowball_' + that_.attr("plan_type"),'开启攒钱计划');
        //                     appUtils.setSStorageInfo("userChooseAnswer",'');
        //                     appUtils.pageInit(_page_code, "scene/selectProduct");
        //                 }
        //             } else {
        //                 layerUtils.iAlert(data.error_info);
        //             }
        //         })
        //     });
        // });

        //跳转晋金宝页面
        // appUtils.preBindEvent($(_pageId + " .wallet"), ".walletCard", function (e) {   
        //     tools.recordEventData('1','myProfit','充值取现');         
        //     if (!common.loginInter()) return;
        //     if (!ut.hasBindCard(_page_code)) return;
        //     //校验用户是否上传过身份证照片
        //     if(!ut.getUploadStatus()){
        //         let operationId = 'noUploadIdCard'
        //         appUtils.setSStorageInfo("noUploadIdCard", '1');
        //         return layerUtils.iConfirm("您还未上传身份证照片", function () {
        //             appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        //         }, function () {
        //         }, "去上传", "取消",operationId);
        //     }
        //     var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
        //     var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
        //     //到期3个月后提示
        //     if (perfect_info == 4) {
        //         let operationId = 'replaceIdCard'
        //         return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
        //             // tools.recordEventData('1','updateIDCard','更新身份照片');
        //             appUtils.pageInit(_page_code, "account/uploadIDCard", {});
        //         }, "取消", "更换",operationId);
        //     }
        //     common.changeCardInter(function () {
        //         if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
        //             let operationId = 'riskAssessment'
        //             layerUtils.iConfirm("您还未进行风险测评", function () {
        //                 // tools.recordEventData('1','riskQuestion','风险测评');
        //                 appUtils.pageInit(_page_code, "safety/riskQuestion", {});
        //             }, function () {
        //             }, "去测评", "取消",operationId);
        //             return;
        //         } else if (invalidFlag == '1') {
        //             pageTo_evaluation()
        //             return
        //         }
        //         appUtils.pageInit(_page_code, "thfund/myProfit");
        //     });
        // });


        //跳转攒钱策略
        // appUtils.bindEvent($(_pageId + " #strategy"), function () {
        //     tools.recordEventData('1','strategy','攒钱策略');
        //     if (!common.loginInter()) return;
        //     appUtils.setSStorageInfo("productType", '07') //存储类型
        //     appUtils.pageInit(_page_code, "scene/listMorePage", {});
        // });
          //跳转陪伴服务
        // appUtils.bindEvent($(_pageId + " #accompany"), function () {
        //     tools.recordEventData('1','accompany','陪伴服务');
        //     if (!common.loginInter()) return;
        //     let code = "6";
        //     let name = "陪伴服务";
        //     let data = {code,name};
        //     sessionStorage.newsData = JSON.stringify(data);
        //     appUtils.pageInit(_page_code, "liveBroadcast/newsList");
        // });

        //跳转积分奖励
        // appUtils.bindEvent($(_pageId + " #reward"), function () {
        //     tools.recordEventData('1','reward','积分奖励');
        //     if (!common.loginInter()) return;
        //     appUtils.pageInit(_page_code, "vipBenefits/index");
        // });

        //跳转邀请好友
        appUtils.bindEvent($(_pageId + " #invitation"), function () {
            // tools.recordEventData('1','invitation','邀请好友');
            if (!common.loginInter()) return;
            if (!ut.getUserInf().bankAcct || ut.getUserInf().bankAcct == '') return layerUtils.iAlert("仅注册客户暂未开通");
            appUtils.pageInit(_page_code, "vipBenefits/friendInvitation",{ source: source });
        });
        //跳转邀请好友
        appUtils.bindEvent($(_pageId + " #invitationRewards"), function () {
            tools.recordEventData('1','invitationRewards','邀请好友');
            if (!common.loginInter()) return;
            if (!ut.getUserInf().bankAcct || ut.getUserInf().bankAcct == '') return layerUtils.iAlert("仅注册客户暂未开通");
            appUtils.pageInit(_page_code, "vipBenefits/friendInvitation",{ source: source });
        });
        //算算我的财富
        // appUtils.bindEvent($(_pageId + " .snowStory"), function () {
        //     tools.recordEventData('1','snowStory','算算我的财富');
        //     appUtils.pageInit(_page_code, "scene/snowStory");
        // });
        // //跳转钱包账户页面
        // appUtils.bindEvent($(_pageId + " .walletTitle"), function () {
        //     tools.recordEventData('1','walletTitle','钱包账户');
        //     appUtils.pageInit(_page_code, "login/showImgMarket", {
        //         imgUrl: global.oss_url + "fund_filesystem/integral/*************.png", zoneName: "钱包账户"
        //     });
        // });
        //点击播放
        // appUtils.bindEvent($(_pageId + " .videoShow p"), function () {
        //     tools.recordEventData('1','videoShow','点击播放按钮');
        //     let html = `<video id="userIndex_new_example_video" style="width:100%;height:100%" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
        //     webkit-playsinline="true" playsinline="true" height="100%" controls preload="auto" poster="./images/index_poster.png"
        //     data-setup="{}">
        //     </video>`
        //     $(_pageId + " #new_example_div").html(html);
        //     //初始化视频
        //     index_player = videojs('userIndex_new_example_video', {
        //     }, function onPlayerReady() {
        //         //结束和暂时时清除定时器，并向后台发送数据
        //         this.on('ended', function () {
        //             // window.clearInterval(time1);
        //         });
        //         this.on('pause', function () {
        //             // window.clearInterval(time1);
        //         });
        //         this.on('waiting', function () {
        //             // window.clearInterval(time1);
        //         })
        //     });
        //     index_player.reset();
        //     index_player.src({ src: 'https://market.xintongfund.com/fund_filesystem/video/activityVideoCover/202502271500000999.mp4' })
        //     index_player.load('https://market.xintongfund.com/fund_filesystem/video/activityVideoCover/202502271500000999.mp4')
        //     // $(_pageId + " video").attr("poster", 'https://jjdxcs.xintongfund.com/oss/fund_filesystem/video/productVideoCover/20220722175132.png')
        //     $(_pageId + " #showVideo").show()
        // });
        // appUtils.bindEvent($(_pageId + " #getBack"), function () {
        //     tools.recordEventData('1','close_video','关闭视频弹窗');
        //     index_player.pause();
        //     setTimeout(function() {
        //         index_player.dispose();
        //         index_player = '';
        //     }, 0);
        //     $(_pageId + " #showVideo").hide();
        // });
        /**
         * 高端版本点击事件
        */
        
        //跳转高端合格投资者认证首页
        appUtils.bindEvent($(_pageId + " .authentication"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "highVersion/authentication");
        });
        //专属财顾
        appUtils.bindEvent($(_pageId + " .exclusive"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.pageInit(_page_code, "highVersion/adviser");
        });
        //投资必看
        appUtils.bindEvent($(_pageId + " .investmentMustSee"), function () {
            appUtils.pageInit(_page_code, "highVersion/investmentMustSee");
        });
        appUtils.bindEvent($(_pageId + " .exclusiveEnjoyment"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            appUtils.setSStorageInfo("service_type",'2');
            appUtils.pageInit(_page_code, "highVersion/exclusiveEnjoyment");
        });
        //标准版投资必看
        appUtils.bindEvent($(_pageId + " #mustSee"), function () {
            appUtils.pageInit(_page_code, "template/investmentMustSee");
        });
        //标准版攒钱计划
        appUtils.bindEvent($(_pageId + " #savingsPlan"), function () {
            appUtils.pageInit(_page_code, "scene/index");
        });
        //标准版更多
        appUtils.bindEvent($(_pageId + " #listMore"), function () {
            const $popup = $(_pageId + " .standardPopList");
            const $container = $(_pageId + " .moreList");
            $(_pageId).addClass('pageActive');
            // 先显示容器再触发动画
            $container.show().css('display', 'flex'); // 确保容器可见
            $popup.show();
            // 重置动画状态
            $popup[0].offsetHeight; // 强制触发重绘:ml-citation{ref="1,3" data="citationList"}
            $popup.addClass("active");
        });
        //高端版更多
        appUtils.bindEvent($(_pageId + " #highMore"), function () {
            const $popup = $(_pageId + " .highPopList");
            const $container = $(_pageId + " .high_moreList");
            $(_pageId).addClass('pageActive');
            // 先显示容器再触发动画
            $container.show().css('display', 'flex'); // 确保容器可见
            $popup.show();
            // 重置动画状态
            $popup[0].offsetHeight; // 强制触发重绘:ml-citation{ref="1,3" data="citationList"}
            $popup.addClass("active");
        });
        appUtils.bindEvent($(_pageId + " .index_close"), function () {
            const $popup = $(_pageId + " .popList");
            // const $container = $(_pageId + " .high_moreList");
            $(_pageId).removeClass('pageActive');
            // 先完成动画再隐藏容器
            $popup.removeClass("active");
            setTimeout(() => {
                $(_pageId + " .high_moreList").hide();
                $(_pageId + " .moreList").hide();
                $popup.hide();
            }, 300); // 等待动画执行完毕:ml-citation{ref="3,4" data="citationList"}
        });
        appUtils.bindEvent($(_pageId + " .high_moreList"), function (e) {
            const $popup = $(_pageId + " .popList");
            const $container = $(_pageId + " .high_moreList");
            $(_pageId).removeClass('pageActive');
            // 先完成动画再隐藏容器
            $popup.removeClass("active");
            setTimeout(() => {
                $container.hide();
                $popup.hide();
            }, 300); // 等待动画执行完毕:ml-citation{ref="3,4" data="citationList"}
        });
        appUtils.bindEvent($(_pageId + " .moreList"), function (e) {
            const $popup = $(_pageId + " .popList");
            const $container = $(_pageId + " .moreList");
            $(_pageId).removeClass('pageActive');
            // 先完成动画再隐藏容器
            $popup.removeClass("active");
            setTimeout(() => {
                $container.hide();
                $popup.hide();
            }, 300); // 等待动画执行完毕:ml-citation{ref="3,4" data="citationList"}
        });
        appUtils.bindEvent($(_pageId + " .popList"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
        });
        // appUtils.bindEvent($(_pageId + " .moreList .popList"), function (e) {
        //     e.preventDefault();
        //     e.stopPropagation();
        // });
        
        
    }
    function destroyVideo(){
        if(index_player){
            index_player.pause();
            setTimeout(function() {
                index_player.dispose();
                index_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        }
    }
    //返回事件
    function pageBack() {
        //原生返回判断是否有banner跳转
        tools.recordEventData('1','back','返回');
        if(start_player){
            start_player.pause();
            setTimeout(function() {
                start_player.dispose();
                start_player = '';
            }, 0);
        }
        $(_pageId + " #showVideo").hide();
        $(_pageId + " .loginDig").hide();
    };
    //页面销毁事件
    function destroy() {
        $(_pageId).removeClass('pageActive');
        $(_pageId + " .standardPopList").hide();
        $(_pageId + " .highPopList").hide();
        $(_pageId + " .moreList").hide();
        $(_pageId + " .high_moreList").hide();
        $(_pageId + " .messgae").hide();
        $(_pageId + " .preTransMsg").hide();
        $(_pageId + " .switchVer").hide();
        $(_pageId + " #showVideo").hide();
        $(_pageId + ' .switchVerName').text('');
        $(_pageId + " #standardFooter").hide();
        $(_pageId + " #templatePage").html('');
        // $(_pageId + " #templatePage").hide();
        // $(_pageId + " #userIndex").hide();
        $(_pageId+ " #Learn").hide();
        is_banner_pageTo = false;
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId+ " .main_message").hide();
        $(_pageId + " .high_msg_num").hide();
        $(_pageId + " .loginDig_phone").text('')
        $(_pageId + " .loginDig").hide()
        $(_pageId + " .tip").hide();
        $(_pageId + " #loginOut").text('');
        if (hIscroll) {
            hIscroll.destroy();
            hIscroll = null;
        }
        $(_pageId + " .footerAll").hide();
        $(_pageId).hide();
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .pop_gold").hide();
        $(_pageId + " .gold_inner #tyj_money").html("");
        $(_pageId + " .inclusive_area").hide();
        $(_pageId + " .thfund_area").hide();
        $(_pageId + " .bank_area").hide();
        $(_pageId + ' .highEnd_area .more').hide();
        $(_pageId + " .highEnd_area").hide();
        $(_pageId + ' .highEnd_list').html('')
        $(_pageId + " .exclusive").hide();
        $(_pageId + " #yuanhui").hide();
        $(_pageId + ".qualifiedInvestor").hide();
    }
    var userIndex = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "destroyVideo":destroyVideo
    };
    // 暴露对外的接口
    module.exports = userIndex;
});