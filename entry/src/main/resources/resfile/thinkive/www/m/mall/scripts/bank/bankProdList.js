// 指定银行列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        common = require("common"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "bank/bankProdList",
        _pageId = "#bank_bankProdList ";
    var current_page;
    var total_pages;
    var page = 1;
    var pay_int_hz;
    var ut = require("../common/userUtil");
    var bank_channel_code;


    function init() {
        bank_channel_code = appUtils.getPageParam("bank_channel_code");
        //banner轮播
        pay_int_hz = "0";//默认查全部
        chanping(1, false, pay_int_hz);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //产品详情
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".pro_detail", function () {
            var productInfo = JSON.parse($(this).find(".productInfo").text());
            productInfo.bank_channel_code = productInfo.bank_channel_code;
            appUtils.setSStorageInfo("productInfo", productInfo);
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_page_code)) return;
            if (productInfo.prod_status != "2") {
                layerUtils.iAlert("产品已售罄");
                return;
            }
            service.reqFun151110({bank_channel_code: productInfo.bank_channel_code}, function (data) {
                if (data.error_no != 0) {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.results[0].bank_flag == 0) { //未开户
                    appUtils.pageInit(_page_code, "bank/faceRecognition");
                    return;
                }
                appUtils.pageInit(_page_code, "bank/purchase");
            })
        }, 'click');
    }


    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    page = 1;
                    chanping(page, false, pay_int_hz);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (current_page < total_pages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        page += 1;
                        chanping(page, true, pay_int_hz);
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (current_page == total_pages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }


    function chanping(list_page, isAppendFlag, pay_int_hz) {
        var param = {
            page: list_page,
            num_per_page: "5",
            pay_int_hz: pay_int_hz, //天数 0 不限   1 一个月内   2 1-6个月  3  6个月以上
        };
        if (bank_channel_code) {
            param.bank_channel_code = bank_channel_code;
        }
        service.reqFun151101(param, function (datas) {
            if (datas.error_no == 0) {
                if (datas.results.length == 0) return;
                current_page = datas.results[0].current_page;//当前页数
                total_pages = datas.results[0].total_pages;//总页数
                var prod_list = datas.results[0].prod_list;
                var str = "", btnClass = "", btnText = "", dep_day_typeName;
                var dateObj = {
                    "D": "天",
                    "M": "月",
                    "Y": "年",
                }
                if (parseFloat(current_page) <= parseFloat(total_pages)) {
                    for (var i = 0; i < prod_list.length; i++) {
                        var prod_code = prod_list[i].prod_code; //产品代码
                        var prod_name = prod_list[i].prod_name; //产品名称
                        var surv_amt = prod_list[i].surv_amt; //起存金额（元）
                        var pay_int_type = prod_list[i].pay_int_type;  //付息单位 D-天 M-月 Y-年
                        var pay_int_hz = prod_list[i].pay_int_hz; //付息周期（天数）
                        var dep_day_type = prod_list[i].dep_day_type;  //周期单位 D-天 M-月 Y-年
                        var prod_dep_day = prod_list[i].prod_dep_day; //周期（天数）
                        var bas_int_rate = prod_list[i].bas_int_rate; //基础利率
                        var prod_status = prod_list[i].prod_status; //产品状态 2-发售 4-售罄 6-停售
                        var brnd_sris = prod_list[i].brnd_sris; //产品系列   SD001 众力存  SD002 中惠存
                        if(brnd_sris == "SD002") { //
                            dep_day_typeName = dateObj[pay_int_type];
                            var dateStr = "<span>每" + pay_int_hz + dep_day_typeName + "付息</span>";
                        } else if(brnd_sris == "SD001"){
                            dep_day_typeName = dateObj[dep_day_type];
                            pay_int_hz = prod_dep_day;
                            var dateStr = "<span>期限：" + pay_int_hz + dep_day_typeName + "</span>";
                        }
                        if (prod_status == "2") {
                            btnClass = "";
                            btnText = "购买";
                        } else if (prod_status == "4") {
                            btnClass = "sold_out";
                            btnText = "售罄";
                        } else if (prod_status == "6") {
                            btnClass = "sold_out";
                            btnText = "停售";
                        } else {
                            btnClass = "";
                            btnText = "购买";
                        }
                        str += "<div class='pro_detail' prod_code='" + prod_code + "'><div class='box'>" +
                            "<span style='display: none' class='productInfo'>" + JSON.stringify(prod_list[i]) + "</span>" +
                            "<h4>" + prod_name + "</h4>" +
                            "<p>存款利率：<span class='redcol'>" + tools.fmoney(bas_int_rate) + "</span>%</p>" +
                            "<p><span style='width: 1.2rem;display: inline-block'>起购：" + tools.fmoney(surv_amt) + "元</span>" + dateStr + "</p>" +
                            "<em><i class='turn in'></i></em></p><a  href='javascript:void(0)' class='buy_btn pop " + btnClass + " in'>" + btnText + "</a></div></div>";
                    }
                }
                if (current_page == 1 && prod_list.length == 0) {
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                    if ($(_pageId + " .nodata").length == 0) {
                        str = "<div class='nodata'>暂无数据</div>";
                    }
                }
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                pageScrollInit();
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }

    function destroy() {
        page = 1;
        $(_pageId + " .finance_pro").html("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var bankList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankList;
});
