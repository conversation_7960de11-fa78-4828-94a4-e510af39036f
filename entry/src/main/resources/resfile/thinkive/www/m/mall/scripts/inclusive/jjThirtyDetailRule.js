define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageId = "#inclusive_jjThirtyDetailRule";
    var _pageCode = "inclusive/jjThirtyDetailRule";
    var _fund_code = "";
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo_jjb") || appUtils.getSStorageInfo("productInfo");
        tools.initFundBtn(productInfo, _pageId);
        _fund_code = appUtils.getSStorageInfo("jjbFundCode") || appUtils.getSStorageInfo("fund_code");
        getRateInfo();
        //获取交易时间
        reqFun102008();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.preBindEvent($(_pageId + " #operateRate"),".rule_tip", function () {
            layerUtils.iAlert("客户维护费指基金管理人与基金销售机构通过基金销售协议约定，依据销售机构销售基金的保有量，从基金管理费中列支一定比例，用以向基金销售机构支付客户服务及销售活动中产生的相关费用。");
        });
        //点击全部
        appUtils.bindEvent($(_pageId + " #payRule"), function () {
            $(_pageId + " #payRule").addClass("active");
            $(_pageId + " #enchashmentRule").removeClass("active");

            $(_pageId + " #payRuleBox").show();
            $(_pageId + " #enchashmentRuleBox").hide();
        });
        //点击更多分类
        appUtils.bindEvent($(_pageId + " #enchashmentRule"), function () {
            $(_pageId + " #payRule").removeClass("active");
            $(_pageId + " #enchashmentRule").addClass("active");

            $(_pageId + " #enchashmentRuleBox").show();
            $(_pageId + " #payRuleBox").hide();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

    }

    //获取交易时间
    function reqFun102008() {
        var param = {
            type: "6"
        }
        service.reqFun102008(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (!results || results.length == 0) {
                    return;
                }
                var qrDate = tools.FormatDateText(results.qrDate.substr(4));
                var syDate = tools.FormatDateText(results.syDate.substr(4));

                $(_pageId + " #qrDate").html(qrDate);
                $(_pageId + " #syDate").html(syDate);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 产品详情查询--费率查询
    function getRateInfo() {
        // 产品详情查询--费率查询
        service.reqFun102003({fund_code: productInfo.fund_code}, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                var purchaseRateStr = "";
                var operateRateStr = "";
                var redeemRateStr = "";
                if (result.purchaseRate && result.purchaseRate.length > 0) {
                    purchaseRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.purchaseRate[0].chgrate_type_desc + '</h1><div><p>适用金额</p>' +
                        '<p>申购费率</p></div>';
                    for (var i = 0; i < result.purchaseRate.length; i++) {
                        var fcitem_tval = result.purchaseRate[i].fcitem_tval; //最大
                        var fcitem_lval = result.purchaseRate[i].fcitem_lval; //最小
                        var discount_ratevalue = result.purchaseRate[i].discount_ratevalue; //最小
                        var purchaseStr = "";
                        if (!fcitem_tval && !fcitem_lval) {
                            purchaseRateStr += '<div>' +
                                '<p>--</p>' +
                                '<p>免申购费</p>' +
                                '</div>';
                            continue;
                        }
                        if (fcitem_lval == 0) { //最小为0
                            purchaseStr += fcitem_tval / 10000 + '万元以下';
                        } else if (fcitem_tval == "-1") { //最大
                            purchaseStr += fcitem_lval / 10000 + '万元以上(包含)';
                        } else {
                            purchaseStr += fcitem_lval / 10000 + '万元(包含) - ' + fcitem_tval / 10000 + '万元';
                        }
                        var rateStr = "";
                        if(discount_ratevalue) {
                            rateStr = "<span style='text-decoration: line-through;margin-right: 0.1rem'>" + result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit + "</span>" + discount_ratevalue + result.purchaseRate[i].chgrate_unit;
                        } else {
                            rateStr = result.purchaseRate[i].chgrate_tval + result.purchaseRate[i].chgrate_unit;
                        }
                        purchaseRateStr += '<div>' +
                            '<p>' + purchaseStr + '</p>' +
                            '<p>' + rateStr + '</p>' +
                            '</div>';
                    }
                    purchaseRateStr += "</div>"
                }
                if (result.operateRate && result.operateRate.length > 0) {
                    operateRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.operateRate[0].chgrate_type_desc + '</h1><div>';
                    for (var i = 0; i < result.operateRate.length; i++) {
                        if(result.operateRate[i].chgrate_type == "11"){
                            if(result.operateRate[i].commission_per_p && result.operateRate[i].commission_per_p > 0){
                                var str = '<p style="height:0.6rem;line-height: 0.6rem;">' + result.operateRate[i].chgrate_item_desc + '</p>' +
                                    '<p style="height: auto;line-height: 0.3rem;"><em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                    '<em class="proportion" >·客户维护费占' + (+result.operateRate[i].commission_per_p).toFixed(2)/1 + '%<img src="../mall/images/rule_tip.png" class="rule_tip"></em></p></div><div>';
                            }else{
                                var str = '<p style="">' + result.operateRate[i].chgrate_item_desc + '</p>' +
                                    '<p style=""><em class="manage_cost">' + (+result.operateRate[i].chgrate_tval).toFixed(4) + result.operateRate[i].chgrate_unit + '</em>' +
                                    '</p></div><div>';
                            }

                            result.operateRate.splice(i,1);
                        }
                        var chgrate_tval = (+result.operateRate[i].chgrate_tval).toFixed(4);
                        operateRateStr += '<p>' + result.operateRate[i].chgrate_item_desc + '</p>' +
                            '<p>' + chgrate_tval + result.operateRate[i].chgrate_unit + '</p></div><div>';

                    }
                    operateRateStr += str + "</div>"
                }
                if (result.redeemRate && result.redeemRate.length > 0) {
                    redeemRateStr += '<div class="highFinancialRateInfo">' +
                        '<h1>' + result.redeemRate[0].chgrate_type_desc + '</h1><div><p>适用期限</p>' +
                        '<p>赎回费率</p></div>';
                    for (var i = 0; i < result.redeemRate.length; i++) {
                        var datestr = "";
                        var fcitem_lval = result.redeemRate[i].fcitem_lval; //最小
                        var fcitem_lvunit = result.redeemRate[i].fcitem_lvunit;//最小单位
                        var fcitem_tval = result.redeemRate[i].fcitem_tval;//最大
                        var fcitem_tvunit = result.redeemRate[i].fcitem_tvunit;//最大单位
                        if (!fcitem_tval && !fcitem_lval) {
                            redeemRateStr += '<div>' +
                                '<p>--</p>' +
                                '<p>免赎回费</p>' +
                                '</div>';
                            continue;
                        }
                        if (fcitem_tval == "-1") { //最大
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限";
                        } else if (fcitem_lval == "0") { //最小
                            datestr += "持有期限<" + fcitem_tval + fcitem_tvunit;
                        } else {
                            datestr += fcitem_lval + fcitem_lvunit + "≤持有期限<" + fcitem_tval + fcitem_tvunit;
                        }
                        redeemRateStr += '<div>' +
                            '<p>' + datestr + '</p>' +
                            '<p>' + result.redeemRate[i].chgrate_tval + result.redeemRate[i].chgrate_unit + '</p>' +
                            '</div>';
                    }

                    redeemRateStr += "</div>";
                }
                $(_pageId + " #purchaseRate").html(purchaseRateStr);
                $(_pageId + " #redeemRate").html(redeemRateStr);
                $(_pageId + " #operateRate").html(operateRateStr);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    function destroy() {
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
