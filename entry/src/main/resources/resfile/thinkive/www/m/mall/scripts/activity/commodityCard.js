//七鱼客服web页面
define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var tools = require("../common/tools"); 
    var service = require("mobileService");
    var _pageCode = "activity/commodityCard", _pageId = "#activity_commodityCard";
    function init() {
        //初始化页面
        setData()
    }
    async function setData(){
        let arr = await getFirstList();
        let newArr = [...arr[1].data['02'],...arr[1].data['03'],...arr[1].data['04']]
        let html = ''
        for(let i = 0; i < newArr.length ; i++){
            let productInfoSon = JSON.stringify(newArr[i]) //二级列表接口传递数据
            let item = newArr[i]
            if(item.prod_sub_type2 == '200'){   //公募
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                var income_period_list = item.income_period_list == '1' ? '' : 'display_none' //是否展示近X年化
                var per_yield_list = item.per_yield_list == '1' ? '' : 'display_none' //是否展示上一期封闭年化收益率
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                //
                var preincomerate = item.preincomerate?tools.fmoney(item.preincomerate):'--' //年化标准
                var inrest_term = item.inrest_term   //封闭期/锁定期
                var income_period_type_desc = item.income_period_type_desc?item.income_period_type_desc:'--' //近多少年化
                var nav = item.nav?tools.fmoney(item.nav, 4):'--'
                var per_yield = item.per_yield?tools.fmoney(item.per_yield):'--'
                var dk_income_rate = item.dk_income_rate?tools.fmoney(item.dk_income_rate):'--'
                html += `<div class="card" style="overflow: hidden">
                        <h5>${item.prod_sname}</h5> 
                        <ul>
                            <div>
                                <li class="${compare_benchmark_list}">
                                    <span style="color: red">${ preincomerate ? preincomerate + '%' : '--' }</span>
                                    <span>业绩计提基准(年化)</span>
                                </li>
                                <li class="${nav_list}">
                                    <span style="color: red">${ nav }</span>
                                    <span>单位净值</span>
                                </li>
                                <li class="${income_period_list}">
                                    <span style="color: red">${ dk_income_rate ? dk_income_rate + '%' : '--' }</span>
                                    <span>${income_period_type_desc}年化</span>
                                </li>
                                <li class="${per_yield_list}">
                                    <span style="color: red">${ per_yield ? per_yield + '%' : per_yield }</span>
                                    <span>上一封闭期年化收益率</span>
                                </li>
                            </div>
                            <div>
                                <li class="${lock_period_list}">
                                    <span>${inrest_term ? inrest_term : '--'}</span>
                                    <span>锁定期</span>
                                </li>
                                <li class="${closed_period_list}">
                                    <span>${inrest_term ? inrest_term : '--'}</span>
                                    <span>期限</span>
                                </li>
                            </div>
                            <div>
                                <li>
                                    <span>${item.threshold_amount}元</span>
                                    <span>起购金额</span>
                                </li>
                            </div>
                        </ul>
                        <div class="label">
                            <span>${item.risklevel_name}</span>
                            <span class="btn">发送<em style='display: none' class='productInfo'>${productInfoSon}</em></span>
                        </div>
                    </div>`
            }else if(item.prod_sub_type2 == '100'){
                //私募列表展示
                var found_rate = item.found_rate ? item.found_rate : '--' //成立以来收益
                var preincomerate = tools.fmoney(item.preincomerate) //年化标准
                var threshold_amount = item.threshold_amount / 10000 //起购金额
                var inrest_term = item.inrest_term   //封闭期/锁定期
                var nav = tools.fmoney(item.nav, 4)
                //产品整合 是否展示
                var compare_benchmark_list = item.compare_benchmark_list == '1' ? '' : 'display_none' //是否展示年化标准
                var fund_rate_list = item.fund_rate_list == '1' ? '' : 'display_none' //是否展示成立以来收益
                var closed_period_list = item.closed_period_list == '1' ? '' : 'display_none' //是否展示封闭期
                var lock_period_list = item.lock_period_list == '1' ? '' : 'display_none' //是否展示锁定期
                var this_year_rate = tools.fmoney(item.this_year_rate ? item.this_year_rate : item.annu_yield)
                var this_year_rate_list = item.this_year_rate_list == '1' ? '' : 'display_none' //是否展示今年以来收益
                var nav_list = item.nav_list == '1' ? '' : 'display_none' //是否展示净值
                html += `<div class="card" style="overflow: hidden">
                        <h5>${item.prod_sname}</h5> 
                        <ul>
                            <div>
                                <li class="${compare_benchmark_list}">
                                    <span style="color: red">${ preincomerate ? preincomerate + '%' : '--' }</span>
                                    <span>业绩计提基准(年化)</span>
                                </li>
                                <li class="${fund_rate_list}">
                                    <span style="color: red">${ tools.fmoney(found_rate) }</span>
                                    <span>成立以来收益</span>
                                </li>
                            </div>
                            <div>
                                <li class="${lock_period_list}">
                                    <span>${inrest_term ? inrest_term : '--'}</span>
                                    <span>锁定期</span>
                                </li>
                                <li class="${closed_period_list}">
                                    <span>${inrest_term ? inrest_term : '--'}</span>
                                    <span>期限</span>
                                </li>
                                <li class="${this_year_rate_list}">
                                    <span>${this_year_rate ? tools.fmoney(this_year_rate) : "--"}</span>
                                    <span>今年以来收益</span>
                                </li>
                                <li class="${nav_list}">
                                    <span style="color: red">${ nav }</span>
                                    <span>最新净值</span>
                                </li>
                            </div>
                            <div>
                                <li>
                                    <span>${item.threshold_amount}元</span>
                                    <span>起购金额</span>
                                </li>
                            </div>
                        </ul>
                        <div class="label">
                            <span>${item.risklevel_name}</span>
                            <span class="btn">发送<em style='display: none' class='productInfo'>${productInfoSon}</em></span>
                        </div>
                    </div>`
            }
            
        }
        $(_pageId + " .list").html(html)
    }
    //绑定事件
    function bindPageEvent(){
        appUtils.preBindEvent($(_pageId + " .list"), ".card .btn", function () {
            var productInfo = JSON.parse($(this).find("em").text()); //存储数据格式
            var data = {
                // 要发送到七鱼的商品或者订单的数据对象
                picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
                // url:'template/publicOfferingDetail?fund_code=' + productInfo.fund_code + '&prod_sub_type2=' + productInfo.prod_sub_type2,
                showCustomMsg:'1',
                title:productInfo.prod_sname,
                desc:'点击查看产品详情',
            }
            if(productInfo.prod_sub_type2 == '100'){
                // data.desc = "点击查看产品详情"
                data.url = 'template/heighEndProduct?fund_code=' + productInfo.fund_code + '&prod_sub_type2=' + productInfo.prod_sub_type2 + '&financial_prod_type=' + productInfo.financial_prod_type;
            }else{
                sessionStorage.vip_buttonShow = true;
                data.url = 'template/publicOfferingDetail?fund_code=' + productInfo.fund_code + '&prod_sub_type2=' + productInfo.prod_sub_type2 + '&financial_prod_type=' + productInfo.financial_prod_type;;
                // data.desc = "成立以来年化:" + (productInfo.preincomerate?tools.fmoney(productInfo.preincomerate) + '%' : "--") + " 锁定期:" + (productInfo.inrest_term?productInfo.inrest_term:"") + " 起购金额:" + (productInfo.threshold_amount?productInfo.threshold_amount+'元':"")
            }
            window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
        }, 'click');
    }
    function destroy(){

    }
    function pageBack(){
        appUtils.pageBack();
    }
    //获取一级列表分类
    async function getFirstList(){
        return new Promise(async(resolve, reject) => {
            service.reqFun102117({}, async(data) =>{
                if(data.error_no == '0'){
                    var res = data.results
                    resolve(res)
                }else{
                    reject('异常抛出')
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
        
    }
    var commodityCard = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = commodityCard;
});
// /**
//  * 模块名：新理念抢先猜
//  */
// define(function (require, exports, module) {
//     /* 引用模块 */
//     var appUtils = require("appUtils"), layerUtils = require("layerUtils"), SHIscroll = require("shIscroll"),
//         service = require("mobileService"), gconfig = require("gconfig"), common = require("common"), validatorUtil = require("validatorUtil");
//     var external = require("external");
//     var ut = require("../common/userUtil");
//     var swiper = require("../common/swiper");
//     /* 常量 */
//     var _pageCode = "activity/commodityCard", _pageId = "#activity_commodityCard";
//     /**
//      * 初始化
//      */
//     function init() {

//     }

//     /**
//      * 事件绑定
//      */
//     function bindPageEvent() {

//     }
//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn0"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             template:'pictureLink',
//             picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });
//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn1"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//             title:'晋金宝90天',
//             desc:'晋金宝90天简介',
//             activity:'活动展示标题',
//             activityHref:'活动跳转链接',
//             price:'1.00'
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });

//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn2"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             picture:'https://jjdx.sxjjd.com/m/mall/images/icon_app.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//             title:'晋金宝90天',
//             desc:'晋金宝90天简介',
//             activity:'活动展示标题',
//             activityHref:'活动跳转链接',
//             payMoney:'1.00',
//             orderId:"订单id",
//             orderTime:"2021-10-11",
//             orderSku:"订单描述",
//             orderCount:"1",
//             orderStatus:"成功",
//             tags:'{"label": "打开七鱼网址","url": "https://qi.163.com"}',
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });

//     //发送
//     appUtils.bindEvent($(_pageId + " .label .btn0"), function () {
//         var data = {
//             // 要发送到七鱼的商品或者订单的数据对象
//             template:'pictureLink',
//             picture:'../mall/images/activity/phone.png',
//             url:'template/publicOfferingDetail',
//             showCustomMsg:'1',
//         }
//         window.parent.postMessage(data, '*'); // 将数据用postMessage的方式发送到七鱼
//         console.log('11')
//     });

//     /**
//      * 销毁
//      */
//     function destroy() {

//     };
//     /*
//      * 返回
//      */
//     function pageBack() {
//         appUtils.pageBack();
//     }

//     var index = {
//         "init": init,
//         "bindPageEvent": bindPageEvent,
//         "destroy": destroy,
//         "pageBack": pageBack
//     };
//     module.exports = index;


// });
