//晋金高端交易结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageUrl = "highEnd/purchaseResult",
        tools = require("../common/tools"),
        _pageId = "#highEnd_purchaseResult ";
    var outputInfo;
    var num;
    var t;
    var ut = require("../common/userUtil");
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        var trans_serno = appUtils.getPageParam("trans_serno");
        var app_amt = appUtils.getPageParam("app_amt");
        var productInfo = appUtils.getSStorageInfo("productInfo");
        tools.getActivityInfo(_pageId,_pageUrl)
        $(_pageId + " .app_amt").text(tools.fmoney(app_amt));
        $(_pageId + " .prod_sname").text(productInfo.prod_sname ? productInfo.prod_sname : productInfo.fund_sname);
        if(!trans_serno) { //预约为同步，直接展示预约成功
            $(_pageId + " .success").show();//购买成功
            return;
        }
        $(_pageId + ' .load').show();
        num = 5;
        outputInfo = appUtils.getPageParam();
        countDown();
    }

    //绑定事件
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + ".done_btn"), function () {
        	if(ut.getUserInf().custLabelCnlCode == "yh"){
        		appUtils.setSStorageInfo("routerList", ["yuanhui/userIndexs", "yuanhui/myAccount"]);
                appUtils.pageInit(_pageUrl, "yuanhui/hold");
        		return;
        	}else if(ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) ||ut.getUserInf().custLabelCnlCode == "jjdx"){
                appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount"]);
                appUtils.pageInit(_pageUrl, "template/positionList");
                return;
            } else{
                appUtils.setSStorageInfo("routerList", ["hengjipy/userIndexs", "yuanhui/myAccount"]);
                appUtils.pageInit(_pageUrl, "highEnd/hold");
                return;
            }

        })
        //查看交易记录
        appUtils.bindEvent($(_pageId + " .jyjl"), function () {
            if(ut.getUserInf().custLabelCnlCode == "yh_jjdx" || !(ut.getUserInf().custLabelCnlCode) ||ut.getUserInf().custLabelCnlCode == "jjdx"){
                appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount", "template/positionList"]);
                appUtils.setSStorageInfo("series_id",'');
                appUtils.pageInit(_pageUrl, "template/transaction");
                return;
            }
            if(ut.getUserInf().custLabelCnlCode == "yh"){
                appUtils.setSStorageInfo("routerList", ["yuanhui/userIndexs", "yuanhui/myAccount", "yuanhui/hold"]);
                appUtils.pageInit(_pageUrl, "highEnd/transaction");
            } else if(ut.getUserInf().custLabelCnlCode == "hjpy"){
                appUtils.setSStorageInfo("routerList", ["hengjipy/userIndexs", "yuanhui/myAccount", "highEnd/hold"]);
                appUtils.pageInit(_pageUrl, "highEnd/transaction");
            }else {
                appUtils.pageInit(_pageUrl, "highEnd/transaction");
            }

        })

    }
    //倒计时
    function countDown() {
        $(_pageId + " #djs").html(num);
        t = setInterval(function () {
            if (num > 0) {
                num--;
                $(_pageId + " #djs").html(num)
            } else {
                clearInterval(t);
                $(_pageId + " #djs").html(num);
                service.reqFun102030(outputInfo, function (data) {
                    if (data.error_no == "0") {
                        $(_pageId + ".load").hide();
                        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败 8 确认成功 9 确认失败
                        if (data.results[0].trans_status == "8") {
                            $(_pageId + " .success").show();//购买成功
                        } else if (data.results[0].trans_status == "9" || data.results[0].trans_status == "4" || data.results[0].trans_status == "1") {
                            if (data.results[0].host_desc) {
                                $(_pageId + ' #failinf').text('失败原因：' + data.results[0].host_desc);
                            } else {
                                $(_pageId + ' #failinf').text('很抱歉，购买失败');
                            }
                            $(_pageId + " .fail").show();//购买失败
                        } else {
                            $(_pageId + " .wait").show();//购买无结果
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(data.error_info);
                    }
                })
            }

        }, 1000)
    }


    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        clearInterval(t);
        $(_pageId + ' .fail').hide();
        $(_pageId + ' .success').hide();
        $(_pageId + ' .wait').hide();
        $(_pageId + ' .load').hide();
    }


    var result = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = result;
});
