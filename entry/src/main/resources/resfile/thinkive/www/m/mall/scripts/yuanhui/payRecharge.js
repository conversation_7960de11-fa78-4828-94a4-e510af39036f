// 线下入金
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageId = "#yuanhui_payRecharge ",
    	_pageUrl = "yuanhui/payRecharge ";
    var ut = require("../common/userUtil");
    require("../common/clipboard.min.js");
    var user;

    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        user = ut.getUserInf();
        var bankName = user.bankName;
        var bankCard = user.bankAcct;
        $(_pageId + " #bankName").html(bankName);
        $(_pageId + " #bankCard").html(bankCard.substring(bankCard.length - 4, bankCard.length));
        copyContent("hmCopy");
        copyContent("zhCopy");
        copyContent("khhCopy");
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " #sure"), function () {
            $(_pageId + " #tit_hkcz").hide();
            $(_pageId + " #mask_hkcz").hide();
        })
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
        	pageBack();
        });

        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum("yuanhui/payRecharge")
        });
        //打开手机银行
        appUtils.bindEvent($(_pageId + " .iphone_bank"), function () {
            tools.openBankApp(user.bankCode);
        });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .web_bank"), function () {
            layerUtils.iAlert("请登录" + user.bankName + "网站进行转账汇款");
        });
        //跳转汇款充值操作指南
        appUtils.bindEvent($(_pageId + " .counter_bank"), function () {
            layerUtils.iAlert("请您到" + user.bankName + "网点进行转账汇款");
        });

    }


    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);

        clipboard.on('success', function (e) {
            if(!e.text.trim()) {
                layerUtils.iAlert("复制失败，请重试", -1, function () {
                });
                return;
            }
            layerUtils.iAlert("复制成功，可粘贴");
        });

        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }
    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " #bankName").html("");
        $(_pageId + " #bankCard").html("");
        $(_pageId + " #hmCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #zhCopy").attr("data-clipboard-text", " ");
        $(_pageId + " #khhCopy").attr("data-clipboard-text", " ");
    }


    var payRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = payRecharge;
});
