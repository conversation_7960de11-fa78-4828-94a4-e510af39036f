// 账户-交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageUrl = "bank/transaction",
        _pageId = "#bank_transaction ";
    var ut = require("../common/userUtil");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var isAppendFlag;
    var isEnd = false;
    var startTime = "";
    var endTime = "";
    var to_index;
    var tools = require("../common/tools");
    var productInfo;
    var curr_page; // 当前页数
    function init() {
        curr_page = 1;
        productInfo = appUtils.getSStorageInfo("productInfo");
        isAppendFlag = false;
        $(_pageId + ".olay").remove();
        selectDate(_pageId + '#startTime', 0, _pageId);
        selectDate(_pageId + '#endTime', 1, _pageId);
        //用于接收详情页带回的参数，找回跳转前的筛选结果
        if (appUtils.getSStorageInfo("qry_condition")) {
            var qry_condition = appUtils.getSStorageInfo("qry_condition");
            appUtils.clearSStorage("qry_condition");
            $(_pageId + " #query_type li a").removeClass("active");
            if (qry_condition.busiCode) {
                $(_pageId + "#query_type [busiCode=" + qry_condition.busiCode + "]").addClass("active");
            } else {
                $(_pageId + " #query_type li a").eq(0).addClass("active");
            }
            $('#startTime').attr('time', qry_condition.startTime).val(qry_condition.startTime);
            $('#endTime').attr('time', qry_condition.endTime).val(qry_condition.endTime);
            $(_pageId + " #query_date li a").removeClass("active");
            if (qry_condition.date_value) {
                $(_pageId + "#query_date [data-value=" + qry_condition.date_value + "]").addClass("active");
            } else {
                $(_pageId + " #query_date li a").eq(0).addClass("active");
            }
        } else {
            resetInputDate();
        }
        getUsertransaction(false);
    }

    function bindPageEvent() {
        //筛选日期
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " #jymx_filter").show();
            $(_pageId + " #filter_layer").show();
        });
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId + " #query_date li"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).find("a").addClass("active");
            var data_value = $(this).find("a").attr("data-value");
            setInputDate("endTime", new Date());
            var start_date = new Date();//开始日期

            if (data_value == "90") {
                start_date.setMonth(start_date.getMonth() - 3);
            } else if (data_value == "360") {
                start_date.setFullYear(start_date.getFullYear() - 1);
            } else {
                start_date = "";
                endTime = "";
                setInputDate("endTime", "");
            }
            setInputDate("startTime", start_date);
        });
        //快捷键选择时间范围
        appUtils.bindEvent($(_pageId + " #query_type li"), function () {
            $(_pageId + " #query_type li a").removeClass("active");
            $(this).find("a").addClass("active");
        });
        //重置
        appUtils.bindEvent($(_pageId + " #reset"), function () {
            resetInputDate();
        });
        //确定
        appUtils.bindEvent($(_pageId + " #confirm"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
            isAppendFlag = false;
            endTime = "";
            curr_page = 1;
            getUsertransaction(false);
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " #filter_layer"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
        });
        //点击取消时间控件
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");
        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function getUsertransaction(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        endTime = $(_pageId + '#endTime').attr('time').replace(/-/g, "");
        startTime = $(_pageId + '#startTime').attr('time').replace(/-/g, "");
        var busiCode = $(_pageId + " #query_type .active").attr("busiCode");
        var busiCodeName = {
            "510": "充值",
            "511": "取现",
            "512": "购买",
            "513": "支取",
            "514": "付息",
            "515": "汇款充值",
            "516": "系统结息",
            "517": "挂单",
            "518": "撤单",
            "519": "转让",
            "520": "购买转让",
        }
        // 0 受理成功 1 受理失败 3 交易成功 4 交易失败
        var transStatusName = {
            "0": "受理成功",
            "1": "受理失败",
            "3": "交易成功",
            "4": "交易失败",
            "6": "撤单成功",
            "7": "撤单失败",
            "8": "确认成功",
            "9": "确认失败",
        }
        var param = {
            bank_channel_code: productInfo.bank_channel_code,
            str_date: startTime,
            end_date: endTime,
            busiCode: busiCode,
            curr_page: curr_page,
            pgrec_num: "10"
        };

        var gettransActionCallBack = function (data) {
            if (data.error_no == "0") {
                var results = data.results[0].UserAsstPostList;
                if (data.tol_num == "0") {
                    $(_pageId + " .trade_history").html('<div class="nodata">暂无数据</div>');
                    return;
                }
                if (results && results.length > 0) {
                    var str = "";
                    for (var i = 0; i < results.length; i++) {
                        //购买日期
                        var busiCode = results[i].busiCode;
                        var crtDate = results[i].crtDate;
                        var crtTime = results[i].crtTime;
                        var totAmt = results[i].totAmt; //总金额
                        var transAmt = results[i].transAmt;//本金
                        var interest = results[i].interest; //利息
                        var fundName = results[i].fundName;
                        var transStatus = results[i].transStatus;
                        var remark = results[i].remark;
                        var money = "";
                        if(busiCode == "517"){
                        	money = tools.fmoney(transAmt);
                        }else{
                        	money = tools.fmoney(totAmt);
                        }
                        /*var remark = '';
                        if (busiCode == "512" || busiCode == "513" || busiCode == "517" || busiCode == "518" || busiCode == "519" || busiCode == "520") {
                            remark = '<div style="width:100%;font-size: 12px;margin-top: 0.02rem;color: #999;">备注：' + fundName;
                        }
                        if (busiCode == "513" || busiCode == "517" || busiCode == "519") {
                            remark += '，本金：' + tools.fmoney(transAmt) + '元，利息：' + tools.fmoney(interest) + '元'
                        }
                        if (remark) {
                            remark += '</div>';
                        }*/
                        str += '<div class="trade_box">' +
                            // '<div class="icon"><img src="./images/bank_' + dc_flag + '_icon.png" alt=""></div>' +
                            '<div class="fundInfo">'+ '<p style="display:none" class="info">' + JSON.stringify(results[i]) + 
                            '</p><p>' + busiCodeName[busiCode] + '</p>' +
                            '<p class="date">' + tools.ftime(crtDate) + " " + tools.ftime(crtTime) + '</p>' +
                            '</div>' +
                            '<div class="result" style="padding: 0;margin-right:4%;">' +
                            '<p style="padding-top: 0">' + money + '元</p>' +
                            '<p>' + transStatusName[transStatus] + '</p>' +
                            '</div>' +
                            '<div class="jjsss" style="position: absolute;right: 2%;top: 0.4rem;"></div>' +
                            /*remark +*/
                            '</div>';
                    }
                }
                if ((results && results.length == "0" || !results) && $(_pageId + " .nodata").length == 0) {
                    isEnd = true;
                    str = '<div class="nodata">没有更多数据</div>'
                }

                $(_pageId + " .my_finance").hide();
                $(_pageId + " #v_container_productList").show();
                if (isAppendFlag) {
                    $(_pageId + " .trade_history").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                } else {
                    $(_pageId + " .trade_history").html(str);
                }
                pageScrollInit();
                $(_pageId + " .trade_history").show();
                
                //详情
                appUtils.bindEvent($(_pageId + " .trade_box"), function () {    
                	var info = JSON.parse($(this).find(".info").text());
                    var param = {
                        date_value: $(_pageId + " #query_date li a.active").attr("data-value"),
                        startTime: $(_pageId + " #startTime").val(),
                        endTime: $(_pageId + " #endTime").val(),
                        busiCode: $(_pageId + " #query_type li a.active").attr("busiCode")
                    }
                    appUtils.setSStorageInfo("qry_condition", param); //保留此次筛选条件
                    appUtils.setSStorageInfo("getTransation", info);
                    appUtils.pageInit(_pageUrl, "bank/transactiondetail");
                }, "click");

            } else {
                layerUtils.iAlert(data.error_info);
            }
        };
        service.reqFun151105(param, gettransActionCallBack);
    }


    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {

        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    curr_page = 1;
                    endTime = "";
                    isAppendFlag = false;
                    getUsertransaction(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        curr_page += 1;
                        $(_pageId + " .new_none").hide();
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getUsertransaction(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
//			if(currentPage==totalPages){
//				$(_pageId+" .visc_pullUp").hide();
//			}else{
//				$(_pageId+" .visc_pullUp").show();
//			}
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }

    }

    function pageBack() {
        appUtils.pageBack();
        // appUtils.pageInit("bank/transaction", "login/userIndexs");
    }


    function destroy() {
        curr_page = 1;
        $(_pageId + " #start_date").val("查询日期");
        $(_pageId + " .my_finance").hide();
        $(_pageId + " .trade_history").hide();
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        endTime = "";
        resetInputDate();
    }

    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", date);
            $(_pageId + " #" + id).val(date);
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        //快捷筛选选中
        $(_pageId + " #jymx_filter ul li a").removeClass("active");
        // $(_pageId + " #init_li").addClass("active");


        $(_pageId + " #query_type li").eq(0).find("a").addClass("active");
        $(_pageId + " #query_date li").eq(0).find("a").addClass("active");

        //开始时间框设置
        $(_pageId + '#startTime').attr("time", "");
        $(_pageId + '#startTime').val("");
        $(_pageId + '#endTime').attr("time", "");
        $(_pageId + '#endTime').val("");
//         var date = new Date();
//         setInputDate("endTime", date);
// //		//结束时间框设置
//         date.setMonth(date.getMonth() - 3);
//         setInputDate("startTime", date);
    }

    var transaction = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = transaction;
});
