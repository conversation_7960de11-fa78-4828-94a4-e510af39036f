// 产品详情模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _page_code = "template/heighEndProduct",
        _pageId = "#template_heighEndProduct";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js')
    // let productInfo;
    var ut = require("../common/userUtil");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    let heighEndProduct //new 一个 vue 实例
    let createdData
    let activeClass;
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];
    var time1;  //观看秒数定时器获取
    var t1 = 0;
    var player = ''
    var isShow = false;
    var vipBenefitsTaskData, startTime, timer = null;
    //获取产品详情
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '0',
                fund_code: createdData.fund_code
            }
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res.template_content)
            })
        })
    }
    async function init() {
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        createdData = appUtils.getSStorageInfo("productInfo"); //产品信息
        //页面埋点初始化
        tools.initPagePointData({fundCode:createdData.fund_code});
        vipBenefitsTaskData = appUtils.getPageParam();
        if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
            startTime = Date.now();
            var readingTime = vipBenefitsTaskData.duration && parseFloat(vipBenefitsTaskData.duration) * 1000;
            if (vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1')) {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                    }, readingTime)
                }
            }
        }
        let html = await setTemplate() //拿到模板数据
        $(".main_heighEndProduct").html(html)   //渲染模板
        activeClass = $(_pageId + " .chartContent").attr("activeClass") ? $(_pageId + " .chartContent").attr("activeClass") : 0;
        heighEndProduct = new Vue({
            el: '#main_heighEndProduct',
            data() {
                return {
                    oss_url: global.oss_url,
                    // productInfo:{},//用户信息
                    detailsInfo: {},//详情信息
                    timeOptions: '',//绘制折线图配置
                    activeClass: activeClass,  //高亮时间（7天）
                    activeClassSecond: '1', //高亮业绩表现，历史净值
                    initShowChat: "0", // 0 业绩走势 1 净值走势
                    moreName: "更多",
                    timeList: [  //区间
                        {
                            name: '近1个月'
                        },
                        {
                            name: '近3个月'
                        },
                        {
                            name: '近6个月'
                        },
                        {
                            name: '近一年'
                        },
                        {
                            name: '近三年'
                        },
                    ],
                    timeListNew: [  //区间
                        {
                            name: '近1个月',
                            section: "1"
                        },
                        {
                            name: '近3个月',
                            section: "3"
                        },
                        {
                            name: '近6个月',
                            section: "6"
                        },
                        {
                            name: '近一年',
                            section: "12"
                        },
                    ],
                    timeListMore: [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        {
                            name: '近五年',
                            section: "60",
                            index: "5"
                        },
                        {
                            name: '成立来',
                            section: "",
                            index: "6"
                        },
                    ]
                }
            },
            //视图 渲染前
            created() {
                this.detailsInfo = createdData
                this.smDetails()    //获取详情
            },
            //渲染完成后
            mounted() {
                var appletEnterImg = global.oss_url + $(_pageId + " #applet_enter").html();
                $(_pageId + " #applet_enter_img").attr("src", appletEnterImg);
                this.initShowChat = "0";
                let prodType = $(_pageId + " .product_desc").attr("prodType");
                appUtils.setSStorageInfo("prodType", prodType);
                if (prodType && prodType == "nav") {
                    this.getHistory();
                    this.getPerformance();
                    this.initTrajectoryData(); // 默认显示一年
                }
            },
            //计算属性
            computed: {
                setNum1: () => {
                    return (str, len) => {
                        if (!str) return '--'
                        return (+str).toFixed(len)
                    }
                },
                //日期处理
                timeResult: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        if (num1) return tools.ftime(time.substr(num, num1), "-")
                        return tools.ftime(time.substr(0, num), "-")
                    }
                },
                //金额 利率处理
                toolsMoney: () => {
                    return (time, num) => {
                        if (!time || time == '--') return '--'
                        return tools.fmoney(time, num)
                    }
                },
                threshold_amount_Result: () => {
                    return (threshold_amount) => {
                        if (!threshold_amount) return '--元起购'
                        threshold_amount = threshold_amount > 10000 ? Number(threshold_amount / 10000) + "万元起购" : tools.fmoney(threshold_amount) + '元起购';
                        return threshold_amount
                    }
                },
                savePoint: () => {
                    return (value, num) => {
                        if (!value || value == '--') return '--'
                        return (+value).toFixed(num)
                    }
                }
            },
            //绑定事件
            methods: {
                //点击业绩计提基准说明
                annual_details() {
                    tools.recordEventData('1','annual_details','击业绩计提基准说明');
                    layerUtils.iAlert("<span style='text-align:left;display:block'>本产品业绩计提基准为" + tools.fmoney(this.detailsInfo.p_expected_yield, 2) + "%（不构成最低收益保证）</span><span style='text-align:left;display:block'>" + this.detailsInfo.administrator_accrual + "</span>")
                },
                // 可转让说明
                transferable_desc() {
                    tools.recordEventData('1','transferable_desc','可转让说明');
                    let rete_list = this.detailsInfo.interest_rate_list;
                    let exit_date_arr = [];
                    rete_list.forEach((item, i) => {
                        if (i < rete_list.length - 1) {
                            exit_date_arr.push(this.timeResult(item.exit_date));
                        }

                    })

                    layerUtils.iAlert(`<span style='text-align:left;display:block'>该产品${exit_date_arr.join("、")}可申请退出，持有满${this.detailsInfo.transfer_start_close_period ? this.detailsInfo.transfer_start_close_period : "--"}天可转让</span>`)
                },
                //颜色
                text_color(risk_level) {
                    if (!risk_level) return ''
                    if (risk_level.substr(1) >= 4) {
                        return 'm_text_red'
                    } else {
                        return 'm_text_green'
                    }
                },
                //跳转产品详情
                page_details() {
                    tools.recordEventData('1','page_details','产品详情');
                    this.detailsInfo.prod_sub_type2 = "100"
                    // appUtils.setSStorageInfo("productInfo", this.detailsInfo);
                    appUtils.pageInit(_page_code, "highEnd/archives");
                },
                //私募产品详情查询（102043）
                smDetails() {
                    service.reqFun102108(this.detailsInfo, (datas) => {
                        if (datas.error_no == 0) {
                            let newData = datas.results[0]
                            this.detailsInfo = { ...this.detailsInfo, ...newData };
                            appUtils.setSStorageInfo("productInfo", this.detailsInfo); //产品信息
                            appUtils.setSStorageInfo("fund_code", this.detailsInfo.fund_code);
                            appUtils.setSStorageInfo("prod_sub_type", this.detailsInfo.prod_sub_type);
                            appUtils.setSStorageInfo("financial_prod_type", this.detailsInfo.financial_prod_type);
                            this.detailsInfo.prod_sub_type2 = "100"
                            this.detailsInfo.transferShowDay = this.detailsInfo.transferable == '0' ? '否' : '满' + (this.detailsInfo.transfer_start_close_period ? this.detailsInfo.transfer_start_close_period : '--') + '天可转让'
                            this.detailsInfo.surplus_amount = this.detailsInfo.surplus_amount + '万元'
                            this.detailsInfo.p_expected_yield = tools.changeTwoDecimal_f(this.detailsInfo.p_expected_yield);    //业绩计提
                            this.detailsInfo.nav_date = datas.results[0].nav_date

                            if (this.detailsInfo.video_info) {
                                this.detailsInfo.cover_path = global.oss_url + this.detailsInfo.video_info.cover_path;
                                this.detailsInfo.video_info.cover_path = global.oss_url + this.detailsInfo.video_info.cover_path;
                                this.detailsInfo.video_info.video_path = global.oss_url + this.detailsInfo.video_info.video_path;
                            }
                            tools.setPageTop("#template_jjThirtyDetail");
                            tools.initPriFundBtn(this.detailsInfo, _pageId);

                        } else {
                            layerUtils.iAlert(datas.error_info);
                        }
                    });
                },

                chooseTrajectoryData(item) {
                    if (item.section == this.activeClass) {
                        return;
                    }
                    this.activeClass = item.section
                    this.moreName = "更多"
                    if (this.initShowChat == 1) {
                        tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                        this.earningsChart1(); // 获取净值走势
                    } else if (this.initShowChat == 0) {
                        tools.recordEventData('1','initTrajectoryData_'+item.section,item.name+'-业绩走势');
                        this.initTrajectoryData(); // 获取业绩走势
                    }
                },
                chooseMoreList(item) {
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    this.moreName = item.name;
                    this.activeClass = item.section;
                    if (this.initShowChat == 1) { // 净值走势
                        tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                        this.earningsChart1();
                    } else { // 业绩走势
                        tools.recordEventData('1','initTrajectoryData_'+item.section,item.name+'-业绩走势');
                        this.initTrajectoryData(); // 获取业绩走势
                    }
                },

                // 点击获取单位净值走势
                initializationData7() {
                    tools.recordEventData('1','navTrend','单位净值走势');
                    $(_pageId + " #navTrend").show();
                    $(_pageId + " #trajectory").hide();
                    this.initShowChat = 1;
                    this.earningsChart1()
                },

                //获取历史净值
                getHistory() {
                    var params = {
                        fund_code: this.detailsInfo.fund_code,
                        cur_page: "1",
                        num_per_page: "5"
                    }
                    service.reqFun102006(params, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results;
                            if (!results || results.length == 0) {
                                return;
                            }
                            var html = "";
                            results.forEach(item => {
                                var end_date = item.end_date;
                                if (end_date && end_date != "--" && end_date.length == 8) {
                                    end_date = tools.ftime(end_date.substring(0, 8));
                                } else {
                                    end_date = '20210908'
                                    end_date = tools.ftime(end_date.substring(0, 8));
                                }
                                //单位净值
                                var nav = item.nav;
                                if (nav != "--") {
                                    nav = (+nav).toFixed(4);
                                }

                                //累计净值
                                var accunav = item.accunav;
                                if (accunav != "--") {
                                    accunav = (+accunav).toFixed(4);
                                }

                                //日涨跌幅
                                var rateClass = "add";
                                var daily_return = item.daily_return;
                                if (daily_return != "--") {
                                    rateClass = this.addMinusClass(daily_return);
                                    daily_return = (+daily_return).toFixed(2) + "%";
                                }

                                html += '<div class="item">' +
                                    '<span>' + end_date + '</span>' +
                                    '<span class="">' + nav + '</span>' +
                                    '<span class="">' + accunav + '</span>' +
                                    '<span class=' + rateClass + '>' + daily_return + '</span>' +
                                    '</div>';
                            })
                            $(_pageId + " #historyContent .list_content").html(html);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //获取业绩表现
                getPerformance() {
                    service.reqFun102007({ fund_code: this.detailsInfo.fund_code }, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            var dataArr = ["近一月", "近三月", "近半年", "近一年"];
                            var numData = [];
                            //近一周
                            // numData.push(results.week);
                            //近一月
                            numData.push(results.month);
                            //近三月
                            numData.push(results.season);
                            //近半年
                            numData.push(results.six_month);
                            //近一年
                            numData.push(results.year);
                            var html = "";
                            var isShowAnnualized = $(_pageId + " #performanceContent").attr("data-show-annualized") == 1 ? true : false;
                            for (var i = 0; i < numData.length; i++) {
                                //空数据处理
                                numData[i] = tools.FormatNull(numData[i]);
                                //基金年化收益率
                                var rateClass = "text_red";
                                var rate = numData[i].rate;
                                if (rate != "--") {
                                    rateClass = this.addMinusClass(numData[i].rate);
                                    rate = (+rate).toFixed(2);
                                    rate = rate + "%";
                                }
                                var simiRateClass = "text_red";
                                var simiRate = numData[i].simi_rate;
                                if (simiRate != "--") {
                                    simiRateClass = this.addMinusClass(numData[i].simi_rate);
                                    simiRate = (+simiRate).toFixed(2);
                                    simiRate = simiRate + "%";
                                }

                                html += '<div class="item">' +
                                    '<span class="m_width_50">' + dataArr[i] + '</span>' +
                                    '<span id="m_width_50" class=' + rateClass + '>' + rate + '</span>' +
                                    `${isShowAnnualized ? `<span id="m_width_50" class='${simiRateClass}'>${simiRate}</span>` : ''}` +
                                    '</div>';
                            }
                            $(_pageId + " #performanceContent .list_content").html(html);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },

                // 获取业绩走势折线图
                initTrajectoryData(section) {
                    section = this.activeClass;
                    $(_pageId + " #navTrend").hide();
                    $(_pageId + " #trajectory").show();
                    this.initShowChat = 0;
                    service.reqFun102146({ fund_code: this.detailsInfo.fund_code, section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer2").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer2").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: (params) => {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">${t.seriesName}：</span><span style="color:${t.color}"><b>${(t.value * 100).toFixed(2)}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                hideDelay: 10
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                // axisLabel: {
                                //     formatter: '{value} %'
                                // },
                                splitNumber: 3,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: (value, index) => {
                                        return (value * 100).toFixed(2) + "%"
                                    }
                                },

                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '14%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                                // data: ['实际巡检', '计划巡检', '漏检次数'],
                            },
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            if (item.achievement) {
                                item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                                item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            }

                            series.push({
                                type: 'line',
                                name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series;
                        if (results[0].date) {
                            config.xAxis.axisLabel.interval = results[0].date.length - 2
                        }

                        let dom = document.getElementById(`chartContainer2`);
                        // let dom = $(_pageId + " #chartContainer1");
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                // 净值走势 新
                earningsChart1() {
                    section = this.activeClass;
                    service.reqFun102147({ fund_code: this.detailsInfo.fund_code, section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer3").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        // results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer3").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                z: 0,
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: function (params) {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    return `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;">${year}-${month}-${day}</div><div class="chart_tooltip_item" style="margin-top:5px;height:20px;">单位净值：${parseFloat(params[0].value).toFixed(4)}</div>`
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                enterable: true,
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    /* formatter: function (e) {
                                         return tools.FormatDateText(e, 1);

                                     },*/

                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                splitNumber: 5,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: function (value, index) {
                                        return value.toFixed(4)
                                    }
                                }
                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '5%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                            },
                            series: [],
                        };
                        let series = [];
                        //处理分红标点
                        let markPoint = {data:[]}
                        if(results[0].dividsplit && results[0].dividsplit.length > 0){
                            let dividsplit = JSON.parse(results[0].dividsplit.substring(1, results[0].dividsplit.length - 1));
                            dividsplit.forEach((item,i) =>{
                                // console.log(i,1111)
                                markPoint.data.push(
                                    {
                                        name: 'redPoint',
                                        coord: [item.xIndex,item.yValue],
                                        value: '',
                                        symbol: 'circle', // 将点的形状设置为圆
                                        symbolSize: 5,   // 设置点的大小
                                        itemStyle: {
                                            color: '#e5443c' // 设置点的颜色为红色
                                        },
                                        // 添加标签
                                        label: i == dividsplit.length - 1 ?label : {},
                                        emphasis: {
                                            label: {
                                                show: true,
                                                fontSize: 16,
                                                fontWeight: 'bold'
                                            }
                                        }
                                    }
                                )
                            })
                        }
                        results.forEach((item, i) => {
                            item.nav = JSON.parse(item.nav.substring(1, item.nav.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                // name: item.indexName,
                                data: item.nav,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                markPoint:markPoint,
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series
                        // 显示第一个和最后一个日期
                        // config.xAxis.axisLabel.interval = 244
                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = document.getElementById(`chartContainer3`);
                        // let dom = $(_pageId + " #chartContainer")
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                            myChart.off('click');
                            myChart.on('click', {seriesType: 'line'},  function(params) {
                                params.event.event.stopPropagation();
                                setTimeout(function() {
                                    // 你的 click 事件处理逻辑
                                    if(params.name == 'redPoint'){
                                        // 获取 markPoint 的数据
                                        let xIndex = params.data.coord[0];
                                        //获取点击红点的日期
                                        let chooseDate = tools.ftime(results[0].date[xIndex]).split('-');
                                        // 在这里可以处理点击 markPoint 的逻辑，例如显示提示信息等
                                        layerUtils.iAlert(`${chooseDate[1]}月${chooseDate[2]}日分红，分红金额已红利再投或现金分红`);
                                    }
                                }, 100);
                            });
                        }
                        // 监听图表的点击事件
                        // myChart.on('click',  (params) => {
                        //     // 如果点击的是 markPoint
                        //     console.log(params)
                        //     if (params.componentType === 'markPoint') {
                        //         // 获取 markPoint 的数据
                        //         // 获取点击分红的index
                        //         let xIndex = params.data.coord[0];
                        //         //获取点击红点的日期
                        //         let chooseDate = tools.ftime(results[0].date[xIndex]).split('-');
                        //         // 在这里可以处理点击 markPoint 的逻辑，例如显示提示信息等
                        //         layerUtils.iAlert(`${chooseDate[1]}月${chooseDate[2]}日分红，分红金额已红利再投或现金分红`);
                        //     }
                        // });
                    })
                },
                //选择7天 业绩表现，历史净值
                performance7(index) {
                    tools.recordEventData('1','performanceContent','业绩表现');
                    $(_pageId + " #performanceContent").show();
                    $(_pageId + " #historyContent").hide();
                    this.activeClassSecond = index
                },
                history7(index) {
                    tools.recordEventData('1','historyContent','历史净值');
                    $(_pageId + " #performanceContent").hide();
                    $(_pageId + " #historyContent").show();
                    this.activeClassSecond = index
                },
                // 显示更多区间
                showMoreSpliceDate() {
                    tools.recordEventData('1','moreSpliceDate','显示更多区间');
                    //$(_pageId + " #tooltip").parent().css({ "z-index": "999" })
                    $(_pageId + " .thfundBtn").hide();
                    $(_pageId + " #moreSpliceDate").show();
                },
                cancelMoreSpliceDate() {
                    tools.recordEventData('1','thfundBtn','隐藏更多区间');
                    $(_pageId + " .thfundBtn").show();
                    $(_pageId + " #moreSpliceDate").hide();
                },
                onClickPlayVideo() {
                    tools.recordEventData('1','onClickPlayVideo','点击视频');
                    isShow = true;
                    let param = this.detailsInfo.video_info //存储数据格式
                    player = videojs('heigh_video', {
                    }, function onPlayerReady() {
                        //结束和暂时时清除定时器，并向后台发送数据
                        this.on('ended', function () {
                            window.clearInterval(time1);
                        });
                        this.on('pause', function () {
                            window.clearInterval(time1);
                        });
                        this.on('waiting', function () {
                            window.clearInterval(time1);
                        })
                        //开始播放视频时，设置一个定时器，每100毫秒调用一次aa(),观看时长加1秒
                        this.on('playing', function () {
                            window.clearInterval(time1);
                            time1 = setInterval(function () {
                                t1 += 1;
                                if (t1 >= 20) {
                                    //调用接口 清空时间 初始化时间0
                                    window.clearInterval(time1);
                                    t1 = 0;
                                    // seeVideo();
                                }
                            }, 1000);
                        });
                    });
                    t1 = 0;
                    window.clearInterval(time1);
                    // playVideoData = param;  //选中当前选择的播放视频
                    player.reset();
                    player.src({ src: param.video_path })
                    player.load(param.video_path)
                    $(_pageId + " video").attr("poster", param.cover_path)
                    $(_pageId + " #showVideo").show()
                },
                getBack() {
                    tools.recordEventData('1','closeVideo','暂停视频');
                    window.clearInterval(time1);
                    player.pause();
                    $(_pageId + " #showVideo").hide()
                },
                addMinusClass(str) {
                    var numClass = "text_red";

                    if (str < 0) {
                        numClass = "text_green";
                    } else if (str > 0) {
                        numClass = "text_red";
                    } else {
                        numClass = "text_gray"
                    }
                    return numClass;
                },
                //设置时间为highChart所需时间格式
                datearr(data) {
                    for (var i = 0; i < data.length; i++) {
                        var x = data[i].x.toString();
                        Date.UTC()
                        data[i].x = Date.UTC(x.substring(0, 4), x.substring(4, 6) - 1, x.substring(6, 8));
                    }
                    return data;
                },
                //更多业绩表现
                page_performance() {
                    tools.recordEventData('1','performanceList','更多业绩表现');
                    var isShowRank = $(_pageId + " #performanceContent").attr("data-show-rank") == 1 ? true : false;
                    var isShowAnnualized = $(_pageId + " #performanceContent").attr("data-show-annualized") == 1 ? true : false;
                    appUtils.setSStorageInfo("_isShowBank", isShowRank)
                    appUtils.setSStorageInfo("_isShowAnnualized", isShowAnnualized)
                    appUtils.pageInit(_page_code, "inclusive/performanceList");
                },
                //更多历史净值
                page_history() {
                    tools.recordEventData('1','historyValueList','更多历史净值');
                    appUtils.pageInit(_page_code, "highEnd/historyValueList");
                },
                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                }
            },
        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .close"), function () {
            tools.recordEventData('1','closeVideo','关闭视频');
            window.clearInterval(time1);
            player.pause();
            $(_pageId + " #showVideo").hide()
        });
        // //点击计提问号
        // appUtils.bindEvent($(_pageId + " .annual_details"), function () {
        //     layerUtils.iAlert("<span style='text-align:left;display:block'>本产品业绩计提基准为" + $(_pageId + " .annual").text() + "（不构成最低收益保证）</span><span style='text-align:left;display:block'>" + productInfo.administrator_accrual + "</span>")
        // });
        // //查看更多
        // appUtils.bindEvent($(_pageId + " .more"), function () {
        //     if ($(_pageId + " .detailImgBox").height() == 200) {
        //         $(_pageId + " .detailImgBox").css({ height: 'auto' });
        //         $(_pageId + " .more").text("收起");
        //     } else {
        //         $(_pageId + " .detailImgBox").css({ height: 200 });
        //         $(_pageId + " .more").text("查看更多");
        //     }
        // });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_page_code)
        });
    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        // $(_pageId + " #kefu").hide();
        $(_pageId + " #showVideo").hide();
        $(_pageId + " .thfundBtn").hide();
        window.clearInterval(time1);
        if (player) {
            player.pause();
        }
        isShow = false;
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        vipBenefitsTaskData = ""; startTime = "";
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        if (player) {
            player.pause();
        }
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && vipBenefitsTaskData.task_type == '1' && !tools.getStayTime(startTime, vipBenefitsTaskData.duration)) {
            var remainTime = tools.getRemainTime(startTime, vipBenefitsTaskData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_page_code, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }
    let thproductDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thproductDetail;
});
