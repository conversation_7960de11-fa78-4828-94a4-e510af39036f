// 普通换卡--验证原银行卡信息
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        _pageId = "#safety_oldcardinfo ";
    var phoneNum = "";//手机号码
    var timer = null;//计时器
    var i = 120;//倒计时长
    var yzm = "";
    var ut = require("../common/userUtil");
    var bank_card = "";
    var mobile_phone = "";
    var backPage = "";
    var Millisecond;
    var userInfo;

    function init() {
        userInfo = ut.getUserInf(); //拿到缓存的用户信息
        $(_pageId + " #bankCard").val(userInfo.bankAcct);
        $(_pageId + " #yhmPhone").val(userInfo.bankReservedMobile);
        initYanZma();
        backPage = appUtils.getSStorageInfo("_prePageCode");
        var hkCardInfo = appUtils.getSStorageInfo("hkCardInfo");
        if (hkCardInfo != null) {
            $(_pageId + " #bankCard").val(hkCardInfo.bank_card);
            $(_pageId + " #yhmPhone").val(hkCardInfo.mobile_phone);
        }
        window.clearInterval(timer);
        $(_pageId + " #getYzm").attr("data-state", "true");
        bank_card = appUtils.getSStorageInfo("cardNo");
        mobile_phone = appUtils.getSStorageInfo("bankLeavePhone");
//		$(_pageId + " #bankCard").val(bank_card);
//		$(_pageId + " #yhmPhone").val(mobile_phone);
    }
    // 点击滑块的时候设置进度
    function positioning(val) {
        if (typeof val === 'number') {
        light.style.width = val + 'px'
        tip.style.left = val + 'px'
        } else {
        light.style.width = val.offsetX + 'px'
        tip.style.left = val.offsetX + 'px'
        }
    }
    /**
     * 显示读秒
     * */
    function shows() {
        var $code = $(_pageId + " #getYzm");
        $code.attr("data-state", "false");//点击不能发送
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.css("background-color", "#aaaaaa");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.css("background-color", " #E5433B");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").hide();
            $(_pageId + " #weihao").hide();
        }
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 手机号码输入事件
        appUtils.bindEvent($(_pageId + " #yhmPhone"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //验证码输入控制
        appUtils.bindEvent($(_pageId + " #verificationCode"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        //发送验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var bankCard = $(_pageId + " #bankCard").val();
            var mobile = $(_pageId + " #yhmPhone").val();
            // mobile = ut.getUserInf().mobileWhole;
            service.reqFun11000010({"value": bankCard, "type": "card_no"}, function (data) {
                if (data.error_no == "0") {
                    service.reqFun11000011({"value": mobile, "type": "bank_leave_phone"}, function (data) {
                        if (data.error_no == "0") {
                            var $code = $(_pageId + " #getYzm");
                            // if (!validatorUtil.isMobile(mobile)) {
                            //     layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                            //     return;
                            // }
                            if ($code.attr("data-state") == "false") {
                                return;
                            }
                            if (mobile != null && mobile != "") {
                                // if (!validatorUtil.isMobile(mobile)) {
                                //     //判断输入的是否是手机号
                                //     layerUtils.iMsg(-1, "请确定你输入的号码是否正确");
                                //     return;
                                // } else {
                                    
                                // }
                                window.clearInterval(timer);
                                //获取验证码
                                sendPhoneCode(mobile); //发送短信,获取验证码
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iMsg(-1, "请输入原银行卡手机预留号码");
                        }
                    }, {isLastReq: false});
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iMsg(-1, "请输入原卡号信息");
                }
            }, {isLastReq: false});
        });

        // 调用ocr拍照识别
        appUtils.bindEvent($(_pageId + " .icon_photo"), function () {
            var paramExt = {
                "isSport": "No",
            };
            var external = require("external");
            var Param = {
                "funcNo": "60304",
                "moduleName": "mall",
                "paramExt": paramExt
            };
            external.callMessage(Param);
        });

        //关闭换卡必读
        appUtils.bindEvent($(_pageId + " .grid_02 a"), function () {
            $(_pageId + " .card_rules").hide();
            $(_pageId + " .pop_layer4").hide();
        });
        //换卡必读
        appUtils.bindEvent($(_pageId + " .right_btn"), function () {
            $(_pageId + " .card_rules").show();
            $(_pageId + " .pop_layer4").show();
        });
        //变更手机号
        appUtils.bindEvent($(_pageId + " .changeIphone"), function () {
            appUtils.pageInit("safety/oldcardinfo", "safety/changeBankphone");
        });
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            // 控制全数字输入
            /*var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if(!/\d/.test(curInputVal))
            {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }*/

            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);


            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");


        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");

        //银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            $(_pageId + " #pop_view").css("visibility", "hidden");
            $(_pageId + " #big_show_bank").html("");
        }, "blur");
        //下一步
        appUtils.bindEvent($(_pageId + " #bk"), function () {
            var bankCard = $(_pageId + " #bankCard").val();
            var mobile = $(_pageId + " #yhmPhone").val();
            // mobile = ut.getUserInf().mobileWhole;
            service.reqFun11000010({"value": bankCard, "type": "card_no"}, function (data) {
                if (data.error_no == "0") {
                    service.reqFun11000011({"value": mobile, "type": "bank_leave_phone"}, function (data) {
                        if (data.error_no == "0") {
                            if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                                layerUtils.iMsg(-1, "请先获取验证码");
                                return;
                            }
                            if ($(_pageId + " #verificationCode").val() == "") {
                                if ($(_pageId + " #getYzm").attr("data-state") == "true") {
                                    layerUtils.iMsg(-1, "请先获取验证码");
                                } else {
                                    layerUtils.iMsg(-1, "请输入验证码");
                                }
                            } else {
                                secondCard();
                            }
                        } else {
                            layerUtils.iLoading(false);
                            layerUtils.iMsg(-1, "请输入原银行卡手机预留号码");
                        }
                    }, {isLastReq: false});
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iMsg(-1, "请输入原卡号信息");
                }
            }, {isLastReq: false});
        });

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

    }

    //获取语音验证码
    function getCodeOFTalk() {
        var mobile = $(_pageId + " #yhmPhone").val();
        if (mobile) {
            var param = {
                "mobile_phone": mobile,
                "type": common.sms_type.changeCard,
                "send_type": "1",
                "mobile_type":"2"
            }
            service.reqFun11000011({"value": mobile, "type": "bank_leave_phone"}, function (data) {
                if (data.error_no == "0") {
                    service.reqFun199001(param, function (data) {
                        if (data.error_no == "0") {
                            var result = data.results;
                            var talk_mobile = result[0].orgphone;
                            var $dd = "晋金财富将致电您的手机语音告知验证码";
                            $(_pageId + " #talkCode").html($dd);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    });
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iMsg(-1, "原银行卡手机预留号码错误");
                }
            }, {isLastReq: false});
        } else {
            layerUtils.iAlert("请输入手机号");
        }
    }

    //初始化语音验证码
    function initYanZma() {
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
    }

    /**
     * 发送手机验码
     * */
    function sendPhoneCode(phoneNum) {
        var param = {
            mobile_phone: phoneNum,
            send_type: "10",
            type: common.sms_type.changeCard,
            mobile_type:'2'
        };
        service.reqFun199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + " #talkCode").show();
                $(_pageId + ' #weihao').show().text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                timer = setInterval(function () {
                    shows();
                }, 1000);
            } else {

                var $code = $(_pageId + " #getYzm");
                window.clearInterval(timer);
                i = 120;
                $code.css("background-color", " #E5433B");
                $code.attr("data-state", "true");
                $code.html("重新获取验证码");
                initYanZma();
                $(_pageId + ' #weihao').hide();
                //$(_pageId + " #talkCode").show();
                layerUtils.iAlert(error_info);
            }

        });
    }

    function secondCard() {
        var sms_code = $(_pageId + " #verificationCode").val();
        var phone = $(_pageId + " #yhmPhone").val();
        var param = {
            "sms_mobile": phone,
            "sms_code": sms_code,
            "mobile_type": '2',
        }
        var callback = function (resultsVo) {
            //验证码重置
            window.clearInterval(timer);
            var $code = $(_pageId + " #getYzm");
            $(_pageId + " .tips_box").hide();
            i = 120;
            $code.css("background-color", " #E5433B");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").hide();
            $(_pageId + " #weihao").hide();
            //验证码重置
            if (resultsVo.error_no == 0) {
                var hkCardInfo = {
                    "bank_card": $(_pageId + " #bankCard").val(),
                    "mobile_phone": $(_pageId + " #yhmPhone").val()
                }
                appUtils.setSStorageInfo("hkCardInfo", hkCardInfo);
                appUtils.pageInit("safety/oldcardinfo", "safety/uploadphoto");
                clearPage();
            } else {
                layerUtils.iAlert(resultsVo.error_info);
            }
        }
        service.reqFun1100003(param, callback);
    }

    //页面清理
    function clearPage() {
        $(_pageId + " input").attr("value", "");
    }

    function destroy() {
        $(_pageId + " #bankCard").val('');
        $(_pageId + " #yhmPhone").val('');
        userInfo = {};
        var $code = $(_pageId + " #verificationCode");
        $code.val("");
        window.clearInterval(timer);
        var $yzm = $(_pageId + " #getYzm");
        $yzm.attr("data-state", "true");
        $yzm.css("background-color", "#E5433B");
        $yzm.html("获取验证码");
        i = 120;
        $code.html("获取验证码");
        service.destroy();
        $(_pageId + " #getYzm").removeAttr("style");
        i = 120;
        $(_pageId + " input").attr("value", "");
        $(_pageId + ' #weihao').hide();
        $(_pageId + " #talkCode").hide();
    }

    function pageBack() {
        clearPage();
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
