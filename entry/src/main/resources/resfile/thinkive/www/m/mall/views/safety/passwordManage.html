<div class="page" id="safety_passwordManage" data-pageTitle="安全中心" data-refresh="true">
    <div class="pop_layer4 pop_layer" style="display: none"></div>
    <div class="card_rules card_rules2" style="display:none">
        <div class="rules_box slideup in">
            <h5>快捷换卡规则</h5>
            <div class="rules_list">
                <strong>1. 客户总资产为零，且无未确认的交易（在途资金、单边账、代发工资等）的情况下才可进行快捷换卡。</strong>
                <strong>2. 客户需输入平台交易密码和手机验证码，填写新卡卡号和新卡银行预留手机号，系统验签通过后，即完成快捷换卡。</strong>
            </div>
            <p class="risk_tips">温馨提示：换卡成功后，您的交易将通过变更后的新签约银行卡办理，请知晓。</p>
            <div class="grid_02">
                <a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
            </div>
        </div>
    </div>
    <div class="card_rules card_rules1" style="display:none">
        <div class="rules_box slideup in">
            <h5>换卡规则</h5>
            <div class="rules_list">
                <!--				<strong>1.无未确认的交易（在途资金、单边账等）的情况下可进行换卡</strong>-->
                <strong>1.客户需上传电子资料：</strong>
                <p>a.身份证正反面照片或扫描件。</p>
                <p>b.新银行卡正反面照片或扫描件。</p>
                <p>c.一手持新银行卡正面，一手持身份证正面照片（含本人头像，要求身份证信息、银行卡信息清晰）。</p>
                <strong>2.提交换卡申请后，我公司将在两个工作日（节假日顺延）内进行审核。</strong>
                <!--                <strong id="str2">3.提交换卡审核成功后，需在页面上进行短信验证码确认，在确认前不能交易。</strong>-->
            </div>
            <p class="risk_tips">风险提示：换卡存在一定的风险，换卡成功后，取现资金到账银行卡为您变更后新的银行卡，请知晓。</p>
            <div class="grid_02">
                <a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
            </div>
        </div>
    </div>
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" id="getBack" class="icon_back icon_gray" operationType="1" operationId="getBack" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center">安全中心</h1>
            </div>
        </header>
        <article class="bg_gray">
            <!-- PSW_MANAGE START -->
            <div class="psw_manage mt20">
                <p id="cardauditsta" style="display:none;">银行卡
                    <!--<a href="javascript:void(0);" id="kjchangeCard" class="btn_modify">快捷换卡</a>-->
                    <a href="javascript:void(0);" id="changeCard" class="btn_reset" operationType="1" operationId="changeCard" operationName="换卡">换卡</a>
                </p>

                <p id="cardauditsta_z" style="display:none;">银行卡
                    <a href='javascript:void(0);' class='btn_reset' style='color:#3f4e59;'> 审核中</a>
                </p>
                <p id="cardauditsta_makesure" style="display:none;">银行卡
                    <a href='javascript:void(0);' class='btn_reset' style='color:red;'> 待确认</a>
                </p>
                <p id="cardauditsta_no_bankAcct" style="display:none;">银行卡
                    <a href='javascript:void(0);' class='btn_reset' style='color:#404257;'> -- </a>
                </p>
                <p>登录密码
                    <span style="float:right;margin-right:0.9rem;"></span>
                    <a href="javascript:void(0);" id="modifyLogin" class="btn_reset" style="display:none" operationType="1" operationId="modifyLogin" operationName="修改登录密码">修改</a>
                    <a href="javascript:void(0);" id="setLoginPassword" class="btn_reset" style="display:none" operationType="1" operationId="modifyLogin" operationName="设置登录密码">设置</a>
                </p>
                <p>交易密码
                    <a href="javascript:void(0);" id="modifyJiaoYi" class="btn_modify" operationType="1" operationId="modifyJiaoYi" operationName="修改交易密码">修改</a>
                    <a href="javascript:void(0);" id="resetJiaoYi" class="btn_reset" operationType="1" operationId="resetJiaoYi" operationName="重置交易密码">重置</a>
                    <a href="javascript:void(0);" id="jiaoYi_no_bank" style='color:#404257;display:none;' class="btn_reset">--</a>
                </p>
                <p id="gestureManagement"></p><!-- 用于绑定手势密码 -->
                <p id="fingerprint_parent" style="display: none;">指纹密码
                    <span style="float:right;margin-right:0.9rem;"></span>
                    <a href="javascript:void(0);" id="fingerprint" class="btn_reset" operationType="1" operationId="fingerprint" operationName="设置指纹密码">设置</a>
                </p>
                <p>风险测评
                    <span style="float:right;margin-right:0.9rem;"></span>
                    <a href="javascript:void(0);" id="pRrisk" class="btn_reset" operationType="1" operationId="pRrisk" operationName="重新测评">重新测评</a>
                    <a href="javascript:void(0);" id="pRrisk_no_bank" style='color:#404257;display:none;' class="btn_reset">--</a>
                </p>
                <p>合格投资人认证
                    <span style="float:right;margin-right:0.9rem;"></span>
                    <a href="javascript:void(0);" id="investment_auth" class="btn_reset" operationType="1" operationId="investment_auth" operationName="认证">认证</a>
                    <a href="javascript:void(0);" id="investment_auth_no_bank" class="btn_reset" style='color:#404257;display:none;'>--</a>
                </p>
                <p>注册手机号
                    <a href="javascript:void(0);" class="btn_reset" id="change" operationType="1" operationId="change" operationName="变更注册手机号">变更</a>
                    <span id="phoneNum" style="float:right;margin-right:0.9rem;"></span>
                </p>
                <p id='bankphonechange_p'>银行预留手机号
                    <a href="javascript:void(0);" class="btn_reset" id="bankphonechange" operationType="1" operationId="bankphonechange" operationName="变更银行预留手机号">变更</a>
                    <a href="javascript:void(0);" class="btn_reset" id="bankphonechange_no_bank" style='color:#404257;display:none;'>--</a>
                    <span id="bankphoneNum" style="float:right;margin-right:0.9rem;"></span>
                </p>
                <p>别名设置
                    <a style="display:none;" href="javascript:void(0);" id="setOtherName" class="btn_reset" operationType="1" operationId="setOtherName" operationName="设置别名">设置</a>
                    <a style="display:none;" href="javascript:void(0);" id="updateOtherName" class="btn_reset" operationType="1" operationId="updateOtherName" operationName="修改别名">修改</a>
                    <span id="otherName_span" style="position: absolute;right: 1rem"></span>
                </p>
                <p style="clear: both;">电子邮箱
                    <a style="display:none;" href="javascript:void(0);" id="setEmial" class="btn_reset" operationType="1" operationId="setEmial" operationName="设置电子邮箱">设置</a>
                    <a style="display:none;" href="javascript:void(0);" id="updateEmial" class="btn_reset" operationType="1" operationId="updateEmial" operationName="修改电子邮箱">修改</a>
                    <a href="javascript:void(0);" class="btn_reset" id="email_no_bank" style='color:#404257;display:none;'>--</a>
                    <span id="emial_span" style="float:right;margin-right:0.9rem;"></span>
                </p>
                <p style="clear: both;">通知管理
                    <a href="javascript:void(0);" id="setInformManager" class="btn_reset" operationType="1" operationId="setInformManager" operationName="设置通知管理">设置</a>
                </p>
            </div>
            <!-- PSW_MANAGE END -->


        </article>
    </section>
</div>