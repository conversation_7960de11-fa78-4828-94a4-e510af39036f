define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#bank_transferBuy ";
    var ut = require("../common/userUtil");
    var _pageCode = "bank/transferBuy";
    var tools = require("../common/tools");
    var userInfo;
    var sms_mobile = require("../common/sms_mobile");
    var avail_amt; // 电子账户可用余额
    var productInfo;// 产品信息
    var bankElectornReservedMobile; //电子银行预留手机号
    var total_amt; //用户持有该银行总资产
    var calculator = require("../common/calculator");
    var bank_max_recharge;

    function init() {
        userInfo = ut.getUserInf();
        sms_mobile.init(_pageId, {
            "backgroundColor": "#ffffff",
            "btnText": "120s",
            "activeBtnText": "s",
            "activeEndBtnText": "重新获取",
            "disBackgroundColor": "#ffffff",
            "talkCodeText": "如收不到短信 &nbsp;&nbsp;<span id='getTalk'   style='color:blue;font-size:0.14rem;'>语音获取</span>"
        });
        productInfo = appUtils.getSStorageInfo("productInfo");
        tools.getBankPdf("3", productInfo.bank_channel_code, productInfo.prod_code);
        getAccountInfo();
        reqFun199015();//获取银行最大限额
        $(_pageId + " #verificationCode").focus();
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "bank/bankDetail");
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭验证码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });

        // 跳转充值页面
        appUtils.bindEvent($(_pageId + " #recharge "), function () {
            appUtils.pageInit(_pageCode, "bank/recharge");
        });
        // 点击发送验证码事件
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            // 获取验证码
            var param = {
                "mobile_phone": bankElectornReservedMobile,
                "type": common.sms_type.bankTransferBuy,
                "send_type": "0",
                "mobile_type": "2",
                "bank_abbr": productInfo.prod_name
            };
            sms_mobile.sendPhoneCode(param)

        });
        //显示验证码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            var money = productInfo.hang_list_amt;
            $(_pageId + " #input_money").text(tools.fmoney(money) + "元");
            // 获取验证码
            var param = {
                "mobile_phone": bankElectornReservedMobile,
                "type": common.sms_type.bankPurchase,
                "send_type": "0",
                "mobile_type": "2",
                "bank_abbr": productInfo.prod_name
            };
            var $code = $(_pageId + " #getYzm");
            if ($code.attr("data-state") == "false") {
                return;
            }
            sms_mobile.sendPhoneCode(param, function (data) {
                $(_pageId + " #verificationCode").focus();
            });
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {

            document.activeElement.blur();
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (verificationCode.length != 6) {
                layerUtils.iMsg(-1, "请输入完整的验证码");
                return;
            }
            //进行充值
            var trans_amt = productInfo.hang_list_amt;
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                trans_amt: productInfo.hang_list_amt, //交易金额
                bank_channel_code: productInfo.bank_channel_code,
                prod_code: productInfo.prod_code,
                hang_list_grp_srlno: productInfo.hang_list_grp_srlno,
                brnd_sris: productInfo.brnd_sris,
                hang_list_no: productInfo.hang_list_no,
                sms_code: verificationCode,
                sms_mobile: bankElectornReservedMobile,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            trade(param);
        });
        //调用交易接口
        appUtils.bindEvent($(_pageId + " #next"), function () {
            $(_pageId + " .pop_layer").hide();
            var verificationCode = $(_pageId + " #verificationCode").val();   // 判断是否填写验证码
            var isSend = $(_pageId + " #getYzm").attr("data-state");  // 判断是否获取验证码
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }

            if (total_amt && bank_max_recharge && calculator.plus(productInfo.hang_list_amt, total_amt) > parseFloat(bank_max_recharge)) { //当前产品转让份额 + 已持有该银行总份额 > 500000
                layerUtils.iAlert("当前银行限购" + (bank_max_recharge / 10000).toFixed(0) + "万元");
                return;
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " .action_sheet_wrapper").show();
            //是否可以购买
            isCanBuy();
        });
    }

    function trade(param) {
        service.reqFun151014(param, function (data) {
            sms_mobile.clear(_pageId);
            if (data.error_no != 0) {
                $(_pageId + " #verificationCode").val("");
                sms_mobile.clear();
                layerUtils.iAlert(data.error_info);
                return;
            }
            $(_pageId + " #verificationCode").val("");
            var trans_serno = data.results[0].trans_serno;
            appUtils.pageInit(_pageCode, "bank/purchaseResult", {
                trans_serno: trans_serno,
                is_transfer: "1",
            });
        })
    }

    function getAccountInfo() {
        service.reqFun151103({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                if (results.acct_status == "C") {
                    layerUtils.iLoading(false);
                    service.reqFun151123({bank_channel_code: productInfo.bank_channel_code}, function () {
                    })
                    var routerList = appUtils.getSStorageInfo("routerList");
                    routerList.splice(-1);
                    appUtils.setSStorageInfo("routerList", routerList);
                    appUtils.pageInit(_pageCode, "bank/faceRecognition");
                    return;
                }
                $(_pageId + " .bankIcon img").attr("src", tools.judgeBankImg(productInfo.bank_channel_code).icon);
                // acct_no 电子银行    bank_acct_no 绑定银行
                $(_pageId + " .bank_electron_info").text(results.bank_channel_name + "(尾号" + results.acct_no.substr(-4) + ")");
                $(_pageId + " .bank_electron_name").text(results.bank_channel_name);
                avail_amt = results.avail_amt;
                bankElectornReservedMobile = results.mobile_phone;
                $(_pageId + " .avail_amt").text(tools.fmoney(results.avail_amt) + "元");
                initProductInfo();
                getMyAsset();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //查询用户持有该银行总资产
    function getMyAsset() {
        service.reqFun151106({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                total_amt = results.total_amt;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function initProductInfo() {
        $(_pageId + " .prod_sname").text(productInfo.prod_name);
        $(_pageId + " .rate").text(tools.fmoney(productInfo.bas_int_rate) + "%");
        $(_pageId + " #inputspanid span").html(tools.fmoney(productInfo.hang_list_amt));
    }

    //是否可以购买
    function isCanBuy() {
        var trans_amt = productInfo.hang_list_amt;
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        avail_amt = (+avail_amt);
        if (trans_amt <= avail_amt) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }

    function reqFun199015() {
        service.reqFun199015({bank_channel_code: productInfo.bank_channel_code}, function (data) {
            if (data.error_no == "0") {
                var results = data.results[0];
                bank_max_recharge = results.val;
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        $(_pageId + " .prod_sname").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " #inputspanid span").text("");
        $(_pageId + " .bank_electron_info").text("--");
        $(_pageId + " .rate").text("--");
        $(_pageId + " #verificationCode").val("");
        $(_pageId + " .avail_amt").text("--");
        $(_pageId + " .agreement2 i").removeClass("active");
        $(_pageId + " .bankIcon img").attr("src", "");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " .password_box").hide();
        sms_mobile.destroy();
        bank_max_recharge = "";
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var bankRecharge = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bankRecharge;
});
