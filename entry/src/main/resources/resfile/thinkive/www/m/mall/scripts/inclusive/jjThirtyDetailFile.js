//基金文件
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageId = "#inclusive_jjThirtyDetailFile",
        _pageCode = "inclusive/jjThirtyDetailFile";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        $(_pageId + " #filter").hide();
        $(_pageId + " .bottom .no").hide();
        tools.initFundBtn(productInfo, _pageId);
        //查询基金文件
        reqFun102083();

    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });

        //点击基金文件
        appUtils.preBindEvent($(_pageId + " #fundFile"), " .text", function (e) {
            var url = $(this).attr("url");
            e.preventDefault();
            e.stopPropagation();
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        }, 'click');

    }

    //查询基金文件
    function reqFun102083() {
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102083(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    $(_pageId + " .thfundCopyright").css({position: "fixed", bottom: "1rem"}).show();
                    html = '<div style="text-align: center;padding-top: 0.1rem">暂无数据</div>';
                    $(_pageId + " #fundFile").html(html);
                    return;
                }
                $(_pageId + " .thfundCopyright").css({position: "static"}).show();
                var html = '';
                for (var i = 0; i < results.length; i++) {
                    html += '<div class="item">' +
                        '<span class="time"> ' + tools.ftime(results[i].info_pubdate) + '</span>' +
                        '<span class="text" operationType="1" operationId="item_'+ results[i].id +'" operationName="基金文件" url="' + global.oss_url + results[i].file_url + '">' + results[i].file_title + '</span>' +
                        '</div>'

                }
                $(_pageId + " #fundFile").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #buy_rate_box").html("免申购费");
        $(_pageId + " #fundFile").html("");
        $(_pageId + " .thfundCopyright").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailNotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailNotice;
});
