//liveBroadcast_broadcastPage
// 直播外链处理页面
//@版本: 2.0
// /**
//  * 监听iframe子页面消息
//  */
// $(function() {
//     $(".home-share__dialog-poster").on({
//         touchstart: function(e) {
//             timeOutEvent = setTimeout(longPress(), 500);
//             e.preventDefault();
//         },
//         touchmove: function() {
//             clearTimeout(timeOutEvent);
//             timeOutEvent = 0;
//         },
//         touchend: function() {
//             clearTimeout(timeOutEvent);
//             if (timeOutEvent != 0) {
//                 console.log("这是点击，不是长按");
//             }
//             return false;
//         }
//     })
// });
document.getElementById('liveIframe').onload = function(){ //通过inload事件监测iframe的加载
    $(".home-share_btn").hide()
}
define(function (require, exports, module) {
    var external = require("external");
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        service = require("mobileService"),
        serviceConstants = require("constants"),
        ut = require("../common/userUtil");
    var _pageId = "#liveBroadcast_broadcastPage ";
    var _pageCode = "liveBroadcast/broadcastPage";
    var gconfig = require("gconfig");
	var global = gconfig.global;
	var platform = gconfig.platform;
    var h5Url,room_id,share_data;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        h5Url = appUtils.getPageParam("h5Url");
        room_id = appUtils.getPageParam("room_id");
        // console.log(room_id)
        h5Url = setUrlParam("r", Math.random(), h5Url);
        $(_pageId + " #liveIframe").attr("src", h5Url); //生成随机地址，防止页面不刷新

        //查询当前页面是否可分享
        isShare(room_id);
    }
    
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function (e) {
            // alert(222)
            // console.log(window.history,111)
            // window.history.go(-1);
            // window.location.go(-1);
            // return false;
            // history.back(-1);
            history.go(-1);
        });
        //点击分享
        appUtils.bindEvent($(_pageId + " #share"), function (e) {
            // console.log(222)
            $(_pageId + " #pop_layer_pageShare").show()
        });
        //取消分享
        appUtils.bindEvent($(_pageId + " .btn_cancel"), function (e) {
            // console.log(222)
            $(_pageId + " #pop_layer_pageShare").hide()
        });
        //微信
        appUtils.bindEvent($(_pageId + " #share_WeChat"), function (e) {
            // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
            layerUtils.iMsg(-1, "启动分享中...请稍后！");
            $(".pop_layer").hide();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50231";
            param["shareType"] = '22';//平台字典
            param["title"] = share_data.title;
            param["link"] = h5Url;
            param["content"] = share_data.content; //分享文本
            param["imgUrl"] = share_data.img_url
            external.callMessage(param);
        });
        //朋友圈
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function (e) {
            // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
            layerUtils.iMsg(-1, "启动分享中...请稍后！");
            $(".pop_layer").hide();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50231";
            param["shareType"] = '23';//平台字典
            param["title"] = share_data.title;
            param["link"] = h5Url;
            param["content"] = share_data.content; //分享文本
            param["imgUrl"] = share_data.img_url;
            external.callMessage(param);
        });
        
        
    }
    function isShare(room_id){
        let data = {
            busi_id:room_id,
            page_type:"5"
        }
        service.reqFun102129(data, (datas) => {
            if (datas.error_no == 0) {
                let results = datas.results[0];
                if (results && results.is_share == '1' && results.state == '1') $(_pageId + " #share").show();
                share_data = results;
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        })
    }
    //生产页面随机数
    function setUrlParam(paramName, value, url) {

        if (url.indexOf("?") != -1) {
            url = url + "&" + paramName + "=" + value;
        } else {
            url = url + "?" + paramName + "=" + value;
        }
        return url;
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #pop_layer_pageShare").hide()
        $(_pageId + " #guangaolianjie").attr("src", "");
        $(_pageId + " #share").hide();
    }
    /*
     * 返回
     */
    function pageBack() {
        $(_pageId + " #pop_layer_pageShare").hide()
        window.history.go(-1);
    }
    
    var liveBroadcast_broadcastPage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = liveBroadcast_broadcastPage;
});
