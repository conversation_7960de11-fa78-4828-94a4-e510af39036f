// 资质荣誉
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#aboutUs_zzry ";

    function init() {
    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var aboutJJS = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = aboutJJS;
});