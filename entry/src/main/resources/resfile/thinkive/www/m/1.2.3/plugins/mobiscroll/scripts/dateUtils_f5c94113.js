/*创建时间hSea 2015-12-31 15:36:59 PM */
define(function(require,exports,module){function a(a,b){b=b?b:{};var c=b.preset,d=b.dateFormat,e=b.mode,f=b.display,g=b.theme,h={preset:c||"date",dateFormat:d||"yyyy-mm-dd",mode:e||"mixed",display:f||"modal",dateOrder:"yymmdd",theme:g?g:iBrowser.ios?"ios":"android-ics light",setText:"确定",cancelText:"取消",dayText:"日",monthText:"月",yearText:"年",endYear:2099,hourText:"时",minuteText:"分",secText:"秒",lang:"zh"};$("#"+a+" input[data-dateplugin='mobiScroll']").mobiscroll(h),$("#"+a+" input[data-dateplugin='mobiScroll']").each(function(){var a=$(this).val();a&&/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/.test(a)&&$(this).mobiscroll("setDate",new Date(a.replaceAll("-","/")),!0,0)})}require("mobiscroll");var b={initDateUI:a};module.exports=b});
/*创建时间 2015-12-31 15:36:59 PM */
