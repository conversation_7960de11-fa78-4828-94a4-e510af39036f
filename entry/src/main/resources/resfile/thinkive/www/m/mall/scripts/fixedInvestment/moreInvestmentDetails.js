//多基金定投详情列表，渲染单基金
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false}, //初始化渲染分页组件
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        _pageId = "#fixedInvestment_moreInvestmentDetails";
        _pageCode = "fixedInvestment/moreInvestmentDetails";
        require('../../js/zepto.min.js');
        require('../../js/iscroll.js');
        require('../../js/iosSelect.js');
    var ut = require("../common/userUtil");
    var productInfo;
    var monkeywords = require("../common/moneykeywords");
    var userInfo;
    var cur_page = 1, num_per_page = 10;
    let querystatus = '2';
    var jymm;
    var isEnd = false;
    var chooseData,workdate,moreData;
    var dataList = ["","周一",'周二','周三','周四','周五']
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        productInfo = appUtils.getSStorageInfo("productInfo");  //缓存的列表详情
        userInfo = ut.getUserInf()
        moreData = appUtils.getPageParam() ? appUtils.getPageParam() : {planid:sessionStorage.more_planid} //获取多基金定投数据
        // console.log(moreData)
        getList('2',false)
        appUtils.setSStorageInfo("querystatus", null);
        //获取下个交易日
        getTransaction()
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    //获取下一工作日
    function getTransaction(){
        service.reqFun106050({}, (datas) => {
            if (datas.error_no != '0') {
                layerUtils.iAlert(datas.error_info);
                return;
            }
            let result = datas.results[0];
            workdate = result.workdate
        })
    }
    function getList(type,isAppendFlag){
        isEnd = false;
        $(_pageId + " .new_none").hide();
        //0进行中 1已终止
        //进行中
        let data = {
            custno:userInfo.custNo,
            fundcode:(productInfo && productInfo.fund_code) ? productInfo.fund_code : '',
            virfundcode:(productInfo && productInfo.vir_fundcode) ? productInfo.vir_fundcode : '',
            querystatus:type,
            cur_page:cur_page,
            investtype:'2',
            investdetaile:moreData.planid
        }
        service.reqFun106044(data, (datas) => {
            let bankAcct = userInfo.bankAcct.substr(userInfo.bankAcct.length - 4, 4);
            let bankNameRemark = userInfo.bankName + '(尾号' + bankAcct + ')';
            let result = datas.results[0].data
            let list = result.data
            if(list.length == 0){
                isEnd = true;
                $(_pageId + " .new_none").show();
                $(_pageId + " .new_none").html('暂无数据')
                hidePullUp();
                return;
            }
            if (list.length < num_per_page) {
                isEnd = true;
                $(_pageId + " .new_none").show();
                $(_pageId + " .new_none").html('没有更多数据')
            }
            cur_page = (+cur_page);
            cur_page += 1;
            let html = ''
            for(let i = 0; i< list.length; i++){
                let nextdate = (tools.FormatDateText(list[i].nextdate)).split('年')[1]
                var productInfoSon = JSON.stringify(list[i])
                let paymethod = list[i].paymethod;
                html += `<ul class="card main_flxe vertical_line border_top  ${list[i].isend == 0 ? '' : 'color_ccc'}">
                            <em style='display: none' class='productInfo'>${productInfoSon}</em>
                            <li class="flex ">
                                <div>
                                    <span class="m_bold">${list[i].fundname} </span>
                                </div>
                                ${list[i].isend == 0 ? `<span class="m_agreement_color termination operationType="1" operationId="termination_${list[i].planid}" operationName="终止定投" m_font_size12">终止定投</span>` : `<div><span class="m_font_size12 terminated">已终止</span></div>`}
                            </li>
                            <li class="main_flxe m_paddingTop_10 ${list[i].isend == 0 ? 'm_color_777' : 'color_ccc'}">
                                <span class="m_width_25 m_center  m_font_size12">扣款周期</span>
                                <span class="m_width_25 m_center  m_font_size12">每期定投(元)</span>
                                <span class="m_width_25 m_center  m_font_size12">已定投</span>
                                <span class="m_width_25 m_center  m_font_size12">累计定投(元)</span>
                            </li>
                            <li class="main_flxe m_paddingTop_10">
                                <span class="m_width_25 m_center m_font_size12">${setTime(list[i].investcycle,list[i].investdate)}</span>
                                <span class="m_width_25 m_center m_font_size12">${tools.fmoney(list[i].investmoney)}</span>
                                <span class="m_width_25 m_center m_font_size12">${list[i].investsuccess}期</span>
                                <span class="m_width_25 m_center m_font_size12">${tools.fmoney(list[i].investtotal)}</span>
                            </li>
                            <li class="flex m_font_size12 m_paddingTop_10 m_marginTop_10 border_top">
                            ${list[i].isend == 0 ? `<span class="m_text_gray">下一扣款日：<strong class="m_text_red">${nextdate}</strong>  <strong class="m_text_darkgray">${paymethod == '0' ? '晋金宝' : bankNameRemark}</strong></span>`:`下一扣款日：--`}
                                    <span class="record operationType="1" operationId="record_${list[i].planid}" operationName="定投记录" m_agreement_color">定投记录</span>
                            </li>
                            
                        </ul>`
            }
            if (isAppendFlag) {
                $(_pageId + ' .list').append(html)
            } else {
                $(_pageId + ' .list').html(html)
            }
            hidePullUp();
        })
    }
    function hidePullUp() {
   	    $(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }
    /**上下滑动刷新事件**/
    function pageScrollInit() {
    var height = $(_pageId + " #v_container_productList").offset().top;
    var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    cur_page = 1;
                    // getHoldProd(false);
                    getList(querystatus,false)
                },
                "upHandle": function () {
                    if (!isEnd) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        $(_pageId + " .new_none").hide();
                        // getHoldProd(true);
                        getList(querystatus,true)
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                    hidePullUp();
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
    }}
    function setTime(investcycle,investdate){
        let str
        if(investcycle == '2'){
            str = '每月 ' + investdate + '日'
        }else if(investcycle == '0'){
            str = '每周 ' + dataList[investdate]
            
        }else{
            str = '每两周 ' + dataList[investdate]
        }
        return str;
    }
    //关闭数字键盘
    appUtils.bindEvent($(_pageId), function () {
        monkeywords.close();
    });
    //密码输入框初始化
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {}, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    function bindPageEvent() {
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        appUtils.preBindEvent($(_pageId + " .list"), ".card .record", function () {
            chooseData = JSON.parse($(this).parents('ul').find("em").text())
            // console.log(chooseData)
            let param = {
                fundcode:chooseData.fundcode,
                planid:chooseData.planid,
                custno:userInfo.custNo
            }
            appUtils.setSStorageInfo("listInfo", param);
            appUtils.pageInit(_pageCode, "fixedInvestment/investmentIndex");
        },'click');
        //点击终止定投按钮
        appUtils.preBindEvent($(_pageId + " .list"), ".card .termination", function () {
            chooseData = JSON.parse($(this).parents('ul').find("em").text())
            $(_pageId + " #rechargeInfo em").text(chooseData.fundname)
            let operationId = 'stopInvestment';
            layerUtils.iConfirm("定投计划将于"+ '<span class="m_text_red">'+ tools.FormatDateText(workdate) +'</span>' + "终止，终止日当天如有扣款仍将执行。<span style='display:block;padding-top:0.1rem;'>一键分散定投计划中的其他产品不受影响</span>", function () {
            }, function () {
                //吊起密码输入框
                $(_pageId + " .pop_layer").show();
                $(_pageId + " .password_box").show();
                monkeywords.flag = 0;
                passboardEvent();
                //键盘事件
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50210";
                param["pageId"] = "fixedInvestment_moreInvestmentDetails";
                param["eleId"] = "jymm";
                param["doneLable"] = "确定";
                param["keyboardType"] = "4";
                require("external").callMessage(param);
                }, "取消", "确定终止",operationId);
        }, 'click');
        //确定终止
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            // jymm1 = '123123'
            guanbi();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            let param = {
                planid:chooseData.planid + '',
                custno:userInfo.custNo,
                fundcode:chooseData.fundcode,
                transpwd:jymm1,
                operationcode:'0'
            }
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.transpwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.transpwd);
                service.reqFun106043(param, (datas) => {
                    if (datas.error_no != '0') {
                        layerUtils.iAlert(datas.error_info);
                        return;
                    }
                    chooseData.exedate = datas.results[0].exedate
                    chooseData.enddate = datas.results[0].enddate
                    $(_pageId + ' .list').html('')
                    cur_page = 1;
                    appUtils.pageInit(_pageCode, "fixedInvestment/stopResults",chooseData);
                    // getList(querystatus)
                })
            }, {isLastReq: false});
        });
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //新增定投计划
        appUtils.bindEvent($(_pageId + " .thfundBtn"), function () {
            appUtils.setSStorageInfo("isAdvisoryInvestment", '');
            appUtils.pageInit(_pageCode, "fixedInvestment/startInvestment");
        });
        
        //点击进行或终止按钮
        appUtils.bindEvent($(_pageId + " .tab_box a"), function () {
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            if(querystatus != $(this).attr("querystatus")){
                $(_pageId + ' .list').html('')
                querystatus = $(this).attr("querystatus");
                cur_page = 1;
                getList(querystatus)
            }
        }, "click");
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .thfundBtn").hide();
        monkeywords.destroy();
        querystatus = '2';
        $(_pageId + " .new_none").html('没有更多数据');
        // isEnd = true;
        isEnd = false;
        cur_page = 1;
        $(_pageId + " .new_none").hide();
        jymm = "";
        $(_pageId + " #jymm").val("");
        $(_pageId + " .list").html("");
        $(_pageId + " .tab_box a").removeClass("current");
        $(_pageId + " .tab_box a").first().addClass('current');
    }
    function pageBack() {
        appUtils.pageBack();
    }

    var investmentList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = investmentList;
});
