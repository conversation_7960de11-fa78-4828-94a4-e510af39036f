// 修改支付密码
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_resetTradersPassword";
    var ut = require("../common/userUtil");
    //弱密码库
    var weakpwdArr = ['111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999'];
    var i = 120;
    var timer = "";
    var yzmValue = "";
    var bank_code = "";
    var Millisecond;
    var userInfo;
    var tools = require("../common/tools");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        initYanZma();
        //初始化键盘
        jpInit();
        /*	service.getAddBank({},setBank);*/
        common.systemKeybord(); // 解禁系统键盘
        window.clearInterval(timer);
        $(_pageId + " #getYzm").attr("data-state", "true");
        $(_pageId + " #getYzm").removeAttr("class");
        $(_pageId + " #getYzm").html("获取验证码");
        // $(_pageId + " #userInfo").html("您好！您正在为账户 " + userInfo.mobile.substr(0, 3) + "****" + userInfo.mobile.substr(7, 4) + "重置交易密码。");

    }

    //绑定事件
    function bindPageEvent() {

        // 银行预留手机号码输入控制
        appUtils.bindEvent($(_pageId + " #phoneNum"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");
        // 验证码输入控制
        appUtils.bindEvent($(_pageId + " #yzm"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
        }, "input");


        //点击返回按钮
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });

        //银行卡显示特效
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var curVal = this.value;
            var curPosi = putils.getInputCursorPosition(this);
            var curInputVal = curVal.substring(curPosi - 1, curPosi); // 当前输入的值
            if (!/\d/.test(curInputVal)) {
                this.value = curVal.substring(0, curPosi - 1) + curVal.substring(curPosi);
                putils.setInputCursorPosition(this, curPosi - 1);
            }
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);
            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "input");


        //点击获取验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var getYzm = $(this).attr("data-state");
            var phoneNum = $(_pageId + " #phoneNum").val();
            if (!validatorUtil.isMobile(phoneNum)) {
                layerUtils.iMsg(-1, "请确定您的银行预留手机号是否正确");
                return;
            }
            if (getYzm == "true") {
                $(this).attr("data-state", "false");
                //获取验证码
                var param = {
                    "mobile_phone": phoneNum,
                    "type": common.sms_type.restTradePwd,
                    "send_type": "0",
                };
                sendPhoneCode(param);
            }
        });

        //银行卡显示特效
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            var zhanghao_b = this.value;
            if (zhanghao_b.length > 0) {
                $(_pageId + " #pop_view").css("visibility", "visible");
                var yihangka = "";
                for (var i = 0; i < zhanghao_b.length; i++) {
                    if ((i + 1) % 4 == 0) {
                        yihangka += zhanghao_b[i] + " ";
                    } else {
                        yihangka += zhanghao_b[i];
                    }
                }
                $(_pageId + " #big_show_bank").html(yihangka);


            } else {
                $(_pageId + " #big_show_bank").html("");
                $(_pageId + " #pop_view").css("visibility", "hidden");
            }
        }, "focus");

        //银行卡显示特效
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            $(_pageId + " #pop_view").css("visibility", "hidden");
            $(_pageId + " #big_show_bank").html("");
        }, "blur");

        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            guanbi();
            /*var chooseBank = $(_pageId+" #chooseBank").val();*/
            var bankCard = $(_pageId + " #bankCard").val();
            var realName = $(_pageId + " #realName").val();
            var idCard = $(_pageId + " #card").val();
            var phoneNum = $(_pageId + " #phoneNum").val();
            var verificationCode = $(_pageId + " #yzm").val();
            var pwd = $(_pageId + " #pwd").val();
            var pwd2 = $(_pageId + " #pwd2").val();
            var isSend = $(_pageId + " #getYzm").attr("data-state");
            if (isSend == "true") {
                layerUtils.iMsg(-1, "您还未获取验证码");
                return;
            }
            if (bankCard == null || realName == null || verificationCode == null || bankCard == "" || realName == "" || verificationCode == "") {
                layerUtils.iMsg(-1, "您还有必填项未填完");
                return;
            }
            if (!validatorUtil.isBankCode(bankCard)) {
                layerUtils.iMsg(-1, "请输入正确的银行卡号");
                return;
            }
            if (!checkName($.trim(realName))) {
                layerUtils.iMsg(-1, "请输入真实姓名");
                return;
            }
            if (!validatorUtil.isCardID(idCard)) {
                layerUtils.iMsg(-1, "请输入正确的身份证号");
                return;
            }
            if (!validatorUtil.isMobile(phoneNum)) {
                layerUtils.iMsg(-1, "请输入正确的手机号");
                return;
            }
            if (!checkInput(pwd, pwd2)) {
                return;
            }
            if (!checkTradePwd(pwd, "")) {
                return;
            }
            //进行验证码的校验
            var param = {
                "cust_name": realName,
                "bank_acct": bankCard,
                "cert_no": idCard,
                "bank_reserved_mobile": phoneNum,
                "bank_code": userInfo.bankCode,
                "trans_pwd": pwd,
                "sms_mobile": phoneNum,
                "sms_code": verificationCode
            };
            getbankCodeBucardNum(param);
        });

        //交易密码获得焦点
        appUtils.bindEvent($(_pageId + " #pwd11"), function () {
            if ($(_pageId + " #pwd").val() == "") {
                $(_pageId + " #pwd1").removeClass("unable");
            } else {
                $(_pageId + " #pwd1").removeClass("unable").addClass("active");
            }
            $(_pageId + " #pwd21").removeClass("active").addClass("unable");
            kaiqi("pwd");
        }, "click");


        //确定密码获得焦点
        appUtils.bindEvent($(_pageId + " #pwd211"), function () {
            if ($(_pageId + " #pwd2").val() == "") {
                $(_pageId + " #pwd21").removeClass("unable");
            } else {
                $(_pageId + " #pwd21").removeClass("unable").addClass("active");
            }
            $(_pageId + " #pwd1").removeClass("active").addClass("unable");
            kaiqi("pwd2");
        }, "click");

        appUtils.bindEvent($(_pageId + " input"), function () {
            $(_pageId + " #pwd21").removeClass("active").addClass("unable");
            $(_pageId + " #pwd1").removeClass("active").addClass("unable");
            guanbi();
        }, "click");

        //银行卡号输入框失去焦点事件
        appUtils.bindEvent($(_pageId + " #bankCard"), function () {
            $(_pageId + " #pop_view").css("visibility", "hidden");
            $(_pageId + " #big_show_bank").html("");
            var bankCard = $(_pageId + " #bankCard").val();
            if (!validatorUtil.isBankCode(bankCard)) {
                /*	layerUtils.iMsg(-1,"请输入正确的银行卡号");*/
                return;
            }
            var bin_id = $(this).val();
        }, "blur");

        //点击获取语音验证码
        appUtils.preBindEvent(_pageId + " #talkCode", "#getTalk", function () {
            getCodeOFTalk();
            window.clearInterval(timer);
            i = 120;
            timer = setInterval(function () {
                shows();
            }, 1000);
        });

    }


    //获取语音验证码
    function getCodeOFTalk() {
        var mobile = $(_pageId + " #phoneNum").val();
        if (mobile) {
            var param = {
                "mobile_phone": mobile,
                "type": common.sms_type.restTradePwd,
                "send_type": "1",
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(_pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        } else {
            layerUtils.iAlert("请输入手机号");
        }
    }

    //初始化语音验证码
    function initYanZma() {
        var $dd = "如收不到短信 &nbsp;&nbsp;<span id='getTalk' style='color:blue;font-size:0.14rem;'>语音获取</span>";
        $(_pageId + " #talkCode").html($dd);
        $(_pageId + " #talkCode").hide();
    }


    function pwdJiaMi(param) {
        //密码加密
        service.getRSAKey({}, function (data) {
            if (data.error_no == "0") {
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                //重置交易密码
                resetPwd(param);
            } else {
                layerUtils.iLoading(false);
                layerUtils.iAlert("网络繁忙请稍后再试");
            }
        }, {isLastReq: false});
    }

    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iMsg(-1, "密码不能为空");
            return false;
        }
        if (!validatorUtil.isAlphaNumeric(pwd1) || pwd1.length != 6) {
            layerUtils.iMsg(-1, "交易密码应为6位数字");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iMsg(-1, "确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iMsg(-1, "两次密码不相同");
            return false;
        }
        return true;
    }

    function resetPwd(param) {
        service.reqFun101007(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            //验证码重置
            window.clearInterval(timer);
            var $code = $(_pageId + " #getYzm");
            $(_pageId + " .tips_box").hide();
            i = 120;
            $code.css("background-color", " #C1E3B6");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();

            if (error_no == "0") {
                $(_pageId + " #submit").html("确定");
                //禁用标签的事件
                $(_pageId + " #submit").css("pointer-events", "auto");
                layerUtils.iLoading(false);
                layerUtils.iAlert("交易密码重置成功", 1, function () {
                    appUtils.pageBack();
                });
            } else {
                $(_pageId + " #submit").html("确定");
                //禁用标签的事件
                $(_pageId + " #submit").css("pointer-events", "auto");
                layerUtils.iAlert(error_info);
            }
        });
    }

    //获取验证码
    function sendPhoneCode(param) {
        service.reqFun199001(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                timer = setInterval(function () {
                    shows();
                }, 1000);
            } else {
                layerUtils.iAlert(error_info);
            }
            $(_pageId + " #talkCode").show();
        });
    }

    /**
     * 每过一秒执行一次
     * */
    function shows() {
        var $code = $(_pageId + " #getYzm");
        $code.attr("data-state", "false");
        var myDate = new Date();
        var TimeDifference = myDate.getTime();

        if (i == 120) {
            Millisecond = TimeDifference + 120000;
        }
        i = (Millisecond - (Millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
        if (i > 1) {
            $code.html("" + i + "秒后重新获取");
            $code.addClass("sending");
            i--;
        } else {
            window.clearInterval(timer);
            i = 120;
            $code.removeAttr("class");
            $code.attr("data-state", "true");
            $code.html("重新获取验证码");
            initYanZma();
            $(_pageId + " #talkCode").show();
        }
    }


    /**
     * 验证密码格式
     * pwd:面码
     * pwd_name:密码名 如支付密码
     * */
    function checkTradePwd(pwd, pwdName) {
        var pwd_Name = "";
        //如果pwdName为空
        if (!pwdName) {
            pwd_Name = "密码";
        }
        if (validatorUtil.isEmpty(pwd)) {
            layerUtils.iMsg(-1, pwd_Name + "不能为空");
            return false;
        }
        if (!/^\d{6}$/.test(pwd)) {
            layerUtils.iMsg(-1, pwd_Name + "为6位数字");
            return false;
        }
        var temp_pwd = 0;
        for (var i = 0; i < pwd.length - 1; i++) {
            if (parseInt(pwd[i]) + 1 == parseInt(pwd[i + 1])) {
                temp_pwd++;
            }
        }
        if (temp_pwd == 5) {
            layerUtils.iMsg(-1, pwd_Name + "不能为顺增的6位数字");
            return false;
        }
        for (var i = 0; i < weakpwdArr.length; i++) {
            var weakpwd = weakpwdArr[i];
            if (weakpwd.indexOf(pwd) > -1) {
                layerUtils.iMsg(-1, pwd_Name + "不能为连续数字");
                return false;
            }
        }
        layerUtils.iLayerClose();
        return true;
    }

    //开启键盘
    function kaiqi(eleId) {
        //调用键盘
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_resetTradersPassword";
        param["eleId"] = eleId;
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    }

    function jpInit() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #pwd").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #pwd1").addClass("active");
                    }
                } else {
                    passflag = "请输入6位数字";
                    $(_pageId + " #pwd1").removeClass("active");
                }
                $(_pageId + " #pwd1").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #pwd").val(shuru);
                    $(_pageId + " #pwd1").text(passflag);
                }
                var shuru_2 = $(_pageId + " #pwd2").val();
                var passflag_2 = "";
                if (shuru_2) {
                    for (var i = 0; i < shuru_2.length; i++) {
                        passflag_2 += "*";
                    }
                    if (shuru_2.length == 1) {
                        $(_pageId + " #pwd21").addClass("active");
                    }
                } else {
                    passflag_2 = "请输入6位数字";
                    $(_pageId + " #pwd21").removeClass("active");
                }
                $(_pageId + " #pwd21").text(passflag_2);
                var len = shuru_2.length;
                if (len >= 6) {
                    shuru_2 = shuru_2.substring(0, 6);
                    $(_pageId + " #pwd2").val(shuru_2);
                    $(_pageId + " #pwd21").text(passflag_2);
                }

            } // 键盘的输入事件
        };
    }

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //校验姓名
    var checkName = function (name) {
        var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/;
        var reg1 = /^[\u4E00-\u9FA5\uf900-\ufa2d.s]{2,20}$/;
        if (reg.test(name) || reg1.test(name)) return true;
        return false;
    }

    function getbankCodeBucardNum(param) {
        $(_pageId + " #submit").html("修改中...");
        //禁用标签的事件
        $(_pageId + " #submit").css("pointer-events", "none");
        pwdJiaMi(param);
    }

    function destroy() {
        guanbi();
        $(_pageId + " #submit").html("确定");
        $(_pageId + " #submit").css("pointer-events", "auto");
        $(_pageId + " .grid_03 strong span").remove();
        window.clearInterval(timer);
        $(_pageId + " #chooseBank").val();
        $(_pageId + " #bankCard").val("");
        $(_pageId + " #realName").val("");
        $(_pageId + " #card").val("");
        $(_pageId + " #phoneNum").val("");
        $(_pageId + " #yzm").val("");
        $(_pageId + " #pwd").val("");
        $(_pageId + " #pwd2").val("");
        $(_pageId + " #pwd21").text("请输入6位数字");
        $(_pageId + " #pwd1").text("请输入6位数字");
        $(_pageId + ' #weihao').hide();
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var changePassword = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = changePassword;
});
