 // 手机修改登录密码
 //@版本: 2.0
define(function(require, exports, module){
	var appUtils = require("appUtils"),
	    dateUtils = require("dateUtils"),
		layerUtils = require("layerUtils"),
		validatorUtil = require("validatorUtil"),
		service = require("mobileService"),
		common = require("common"),
		_pageId = "#safety_userPasswordSuccess";

	function init(){
		 common.systemKeybord(); // 解禁系统键盘

	}

	//绑定事件
	function bindPageEvent(){
		//点击返回
		appUtils.bindEvent(_pageId+" #getBack",function(){
			pageBack();
		});

		//点击完成
		appUtils.bindEvent(_pageId+" #finish",function(){

		});
	}
	function pageBack() {
        appUtils.pageInit("safety/userPasswordSuccess","login/userLogin",{});
    }
	function changePwd(param){
		service.reqFun901403(param,function(data){
			var error_no = data.error_no,
				error_info = data.error_info;
			if(error_no == "0"){
				var result = data.result;
				layerUtils.iAlert("密码修改成功页面即将跳转到登录页面!",0,function(){
					layerUtils.iLoading(true);
					setTimeout(function(){
						layerUtils.iLoading(false);
						appUtils.pageInit("safety/userPasswordSuccess","login/userLogin",{});
					   },2000);
				});
			} else {
				layerUtils.iLoading(true);
				layerUtils.iMsg(-1,error_info);
			}
		});
	}
	/**
	 * 检验输入密码是否符合规范
	 * 长度，格式等
	 */
	function checkInput(pwd1 ,pwd2){
		if (validatorUtil.isEmpty(pwd1)){
			layerUtils.iMsg(-1,"登录密码不能为空");
			return false;
		}
		if (validatorUtil.isEmpty(pwd2)){
			layerUtils.iMsg(-1,"确认密码不能为空");
			return false;
		}

		if(pwd1 !== pwd2){
			layerUtils.iMsg(-1,"两次密码不相同");
			return false;
		}

		var flag = 0;
		for(var i = 0 ; i < pwd1.length;i++){
			if(pwd1[i].match(/^[a-zA-z]+$/)){
				flag=flag+1;
				break;
			}
		}
		for(var i = 0 ; i < pwd1.length;i++){
			if(pwd1[i].match(/^\d+$/)){
				flag=flag+1;
				break;
			}
		}
        if(flag!=2){
        	layerUtils.iAlert("请输入6-16位字母和数字组合");
			return false;
		}
		return true;
	}

	function destroy(){
		 $(_pageId+" #newPwd").val("");
		 $(_pageId+" #newPwd2").val("");
	}

	var userPasswordSuccess = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = userPasswordSuccess;
});
