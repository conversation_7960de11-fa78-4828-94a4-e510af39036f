// 持有页面
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#template_positionList ";
    var _pageCode = "template/positionList";
    var tools = require("../common/tools"); //是否有新消息
    var cur_page = 1, num_per_page = 10, isEnd = false
    var cur_page_done = 1, num_per_page_done = 10, isEndDone = false;
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var VIscroll = require("vIscroll");
    var vIscroll = { "scroll": null, "_init": false };
    let positionListData, positionTypeTitle, financial_prod_type
    var _cust_fund_type = "0"
    function init() {
        
        positionTypeTitle = {
            '02': '类固收',
            '03': '固收增强',
            '04': '权益投资',
            '06': '基金超市',
            '07': '攒钱计划',
        }
        
        financial_prod_type = appUtils.getSStorageInfo("financial_prod_type");
        let positionListTitle = positionTypeTitle[financial_prod_type] ? positionTypeTitle[financial_prod_type] : "--";
        tools.initPagePointData({pageName:positionListTitle});
        $(_pageId + ' .positionListTitle').html(positionListTitle); //展示具体资产列表名称
        $(_pageId + ' .g_marginTop_30').html(positionTypeTitle[financial_prod_type] ? positionTypeTitle[financial_prod_type] + '(元)' : "--"); //展示具体资产列表名称
        $(_pageId + " .tab_box a").removeClass("current");
        $(_pageId + " .tab_box a[content='cyList']").addClass("current");
        //获取客户公募资产
        getHoldProd(false);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }

    function getHoldProd(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();

        var param = {
            cur_page: cur_page,
            num_per_page: num_per_page,
            financial_prod_type: financial_prod_type
        };
        // console.log(param);
        service.reqFun101933(param, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                //空数据处理
                results = tools.FormatNull(results);
                // 初始化tab
                if (results.havecompleted == "true") { // TODO：返回的是字符串类型
                    $(_pageId + " .tab_box").show();
                } else {
                    $(_pageId + " .tab_box").hide();
                }
                //顶部总数据展示
                showTotalData(results);
                var detailParams = results.data;
                if (!detailParams) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }
                var fundData = detailParams.data;
                if (!fundData || fundData.length == 0) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }
                if (fundData.length < num_per_page) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                //产品列表展示
                var html = showHoldFunds(fundData);
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro .holdFunds").append(html);
                } else {
                    $(_pageId + " .finance_pro .holdFunds").html(html);
                }
            } else {
                layerUtils.iAlert(datas.error_info);
            }
            hidePullUp();
        });
    }

    function getHoldCompletedProd(isAppendFlag) {
        isEndDone = false;
        $(_pageId + " .new_none").hide();
        var param = {
            cur_page: cur_page_done,
            num_per_page: num_per_page_done,
            financial_prod_type: financial_prod_type
        };
        // console.log(param);
        service.reqFun102125(param, function (datas) {
            if (datas.error_no == 0) {
                var results = datas.results[0];
                //空数据处理
                results = tools.FormatNull(results);
                var detailParams = results.data;
                if (!detailParams) {
                    isEndDone = true;
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }
                var fundData = detailParams.data;
                if (!fundData || fundData.length == 0) {
                    isEndDone = true;
                    $(_pageId + " .new_none").show();
                    hidePullUp();
                    return;
                }
                if (fundData.length < num_per_page) {
                    isEndDone = true;
                    $(_pageId + " .new_none").show();
                }
                //产品列表展示
                var html = showHoldCompletedData(fundData);
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro .holdFunds").append(html);
                } else {
                    $(_pageId + " .finance_pro .holdFunds").html(html);
                }
            } else {
                layerUtils.iAlert(datas.error_info);
            }
            hidePullUp();
        });
    }

    //顶部总数据展示
    function showTotalData(results) {
        $(_pageId + " #totalFundvol").html(tools.fmoney(results.all_total_fund_vol));

        //买入在途
        var int_way_vol = (+results.int_way_vol);
        if (int_way_vol > 0) {
            int_way_vol = tools.fmoney(int_way_vol);
            $(_pageId + " #int_way_vol_box").show();
            $(_pageId + " #int_way_vol").html(int_way_vol);
        } else {
            $(_pageId + " #int_way_vol_box").hide();
        }

        //卖出在途
        var out_way_vol = (+results.out_way_vol);
        if (out_way_vol > 0) {
            out_way_vol = tools.fmoney(out_way_vol);
            $(_pageId + " #out_way_vol_box").show();
            $(_pageId + " #out_way_vol").html(out_way_vol);
        } else {
            $(_pageId + " #out_way_vol_box").hide();
        }

        // 调仓在途
        var change_way_vol = (+results.change_way_vol);
        if (change_way_vol > 0) {
            change_way_vol = tools.fmoney(change_way_vol);
            $(_pageId + " #change_way_vol_box").show();
            $(_pageId + " #change_way_vol").html(change_way_vol);
        } else {
            $(_pageId + " #change_way_vol_box").hide();
        }

        // 如果是基金超市
        if (financial_prod_type == '06'  || financial_prod_type == '08') {
            $(_pageId + " .returns_list").show();
            $(_pageId + " .returns_list .all_hold_income_vol").html(results.all_hold_income_vol ? results.all_hold_income_vol : '--');//总持仓收益
            $(_pageId + " .returns_list .all_yest_income_vol").html(results.all_yest_income_vol ? results.all_yest_income_vol : '--');//总昨日收益
            $(_pageId + " .returns_list .all_total_income_vol").html(results.all_total_income_vol ? results.all_total_income_vol : '--');//总累计收益
        } else {
            $(_pageId + " .returns_list").hide();
        }
        //未确认交易笔数
        // var unknow_trans_count = (+results.unknow_trans_count);
        // if(unknow_trans_count>0){
        // 	$(_pageId + " #unknow_trans_count_box").show();
        // 	$(_pageId + " #unknow_trans_count").html(unknow_trans_count);
        // }else{
        // 	$(_pageId + " #unknow_trans_count_box").hide();
        // }
    };

    //持有产品列表展示
    function showHoldFunds(fundData) {
        // console.log(fundData);
        cur_page = (+cur_page);
        cur_page += 1;

        //基金信息
        var html = "";
        for (var i = 0; i < fundData.length; i++) {
            /**
             * 公募相关
             */
            //产品名称
            var fundname = fundData[i].fund_sname;
            //产品代码
            var fundcode = fundData[i].fund_code;
            fundData[i].comb_code = fundData[i].fund_code;
            //基金类型  股票型 债券型 货币型.....
            var prod_type2 = fundData[i].prod_type2;
            //资产
            var fundVol = tools.fmoney(fundData[i].fund_vol);
            //总资产
            var totalFundVol = tools.fmoney(fundData[i].total_fund_vol);
            //本金
            var cost_money = tools.fmoney(fundData[i].cost_money);
            //卖出在途
            var fund_out_way_vol = fundData[i].fund_out_way_vol * 1;
            // fund_out_way_vol = tools.fmoney(fund_out_way_vol);
            //买入在途
            // var fund_way_vol = fundData[i].fund_way_vol;
            // fund_way_vol = tools.fmoney(fund_way_vol);
            //产品类型
            var prod_sub_type = fundData[i].prod_sub_type;
            //产品子类型
            var prod_sub_type2 = fundData[i].prod_sub_type2;

            //专享类型
            var exclusive_type = fundData[i].exclusive_type;

            //业绩计提基准（年化）
            var accrualBasis = fundData[i].accrual_basis ? tools.fmoney(fundData[i].accrual_basis) : "--";
            //期限
            var closedLength = fundData[i].closed_length;
            //成立日
            var interestStartDate = fundData[i].interest_start_date;
            //到期日
            var dueDate = fundData[i].due_date;
            //预计下一开放日
            var respect_next_open_date = fundData[i].respect_next_open_date;
            //持有天数
            var betweenDays = fundData[i].betweenDays > 0 ? fundData[i].betweenDays : "--";
            //收益
            //持有期94业绩年化
            var min_interest_rate = tools.fmoney(fundData[i].min_interest_rate);
            var max_interest_rate = tools.fmoney(fundData[i].max_interest_rate);
            var transferable = fundData[i].transferable;//是否可转让角标
            var str = "";
            if (transferable == "1") {
                str = "<img src='" + global.oss_url + fundData[i].url + "' style='width: 14%;margin-left: 0.12rem;margin-top: -0.04rem;'>"
            }
            //一创小集合金额
            var principal = tools.fmoney(fundData[i].principal);
            //定开私募温馨提示
            var hold_tips = fundData[i].hold_tips;
            var tip_str = hold_tips ? "<dl><dd>温馨提示：" + hold_tips + "</dd></dl>" : ""

            var judgeClass = "";
            let hold_vol = fundData[i].hold_vol * 1; //持仓
            let hold_volList = hold_vol > 0 ? 'm_right_icon' : ''
            let o_hold_volList = hold_vol > 0 ? 'right_icon' : ''
            let fund_in_way_vol = fundData[i].fund_in_way_vol * 1 //买入在涂
            let fund_change_way_vol = fundData[i].fund_change_way_vol * 1 //调仓在途
            let fund_in_way_vol_show = (fund_in_way_vol && fund_in_way_vol > 0) ? '' : 'new_display_none'
            let fund_out_way_vol_show = (fund_out_way_vol && fund_out_way_vol > 0) ? '' : 'new_display_none'
            let fund_change_way_vol_show = (fund_change_way_vol && fund_change_way_vol > 0) ? '' : 'new_display_none'
            if (prod_sub_type2 == '100') {
                //参数
                // let respect_ack_date = fundData[i].respect_ack_date ? fundData[i].respect_ack_date : '--'
                let due_date = dueDate ? tools.ftime(dueDate.substr(0, 8)) : '--'
                let hold_incomeColor = (fundData[i].hold_income * 1) > 0 ? 'red' : (fundData[i].hold_income * 1) < 0 ? 'green' : ''
                let hold_income = fundData[i].hold_income ? fundData[i].hold_income : '--'
                let day_name = fundData[i].lock_period_unit == '0' ? '年' : fundData[i].lock_period_unit == '1' ? '月' : '天'
                var interest_rate = tools.fmoney(fundData[i].interest_rate_min) + "%" + "-" + tools.fmoney(fundData[i].interest_rate_max) + "%";
                //是否展示
                let assets_is_show = fundData[i].assets_hold_show == '1' ? '' : 'display_none' //是否展示资产
                let term_is_show = fundData[i].term_hold_show == '1' ? '' : 'display_none'    //是否展示封闭期
                let compare_benchmark_is_show = fundData[i].compare_benchmark_hold_show == '1' ? '' : 'display_none' //是否展示年化基准
                let redemption_date_is_show = fundData[i].redemption_date_hold_show == '1' ? '' : 'display_none' //是否展示预计可赎回日
                let confirmation_date_is_show = fundData[i].confirmation_date_hold_show == '1' ? '' : 'display_none' //是否展示确认日
                let tips_is_show = fundData[i].tips_hold_show == '1' ? '' : 'display_none'
                let profit_is_show = fundData[i].profit_hold_show == '1' ? '' : 'display_none'
                let performance_benchmarks_hold_show = fundData[i].performance_benchmarks_hold_show == '1' ? '' : 'display_none';
                var stage_yield_hold_show = fundData[i].stage_yield_hold_show == '1' ? '' : 'display_none' // 是否展示阶段收益率
                
                let due_date_hold_show = fundData[i].due_date_hold_show == '1' ? '' : 'display_none' //是否展示到期日
                let expect_flag_show = (fundData[i].expect_flag == '1' && fundData[i].expect_status == '1') ? '' : 'display_none';
                if((hold_vol - fund_out_way_vol <= 0) && fund_in_way_vol <= 0 ){//纯卖出在途的时候不展示到期日和业绩计提
                    due_date_hold_show = 'display_none';
                    redemption_date_is_show  = 'display_none';
                    compare_benchmark_is_show = 'display_none';
                }
                //持有列表，是否展示
                html += "<div class='pro_box item template_box flex'>" +
                    "<div style='display: none' class='fundInfo'>" + JSON.stringify(fundData[i]) + "</div>" +
                    "<ul class='template_left vertical_line'>" +
                    "<li class='m_border_bottom_D2E3E8  m_font_size16 m_bold " + hold_volList + "'>" + fundname + str + "</li>" +
                    "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                    "<span class='m_font_size12 " + assets_is_show + "'>资产：<span>" + totalFundVol + "元</span></span>" +
                    "<span class='m_font_size12 " + profit_is_show + "'>收益：<span class='" + hold_incomeColor + "'>" + hold_income + "元</span></span>" +
                    "<span class='m_font_size12 " + compare_benchmark_is_show + "'>业绩计提基准(年化)：<span>" + accrualBasis + "</span>%</span>" +
                    "<span class='m_font_size12 " + performance_benchmarks_hold_show + "'>业绩比较基准(年化)：<span>" + accrualBasis + "</span>%</span>" +
                    "<span class='m_font_size12 " + stage_yield_hold_show + "'>业绩计提基准(年化)：<span>" + interest_rate + "</span></span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                    "<span class='m_font_size12 " + redemption_date_is_show + "'>预计可赎回日：<span>" + due_date + "</span></span>" +
                    "<span class='m_font_size12 " + due_date_hold_show + "'>到期日：<span>" + due_date + "</span></span>" +
                    "<span class='m_font_size12 " + term_is_show + "'>期限：<span>" + closedLength + day_name + "</span></span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                    "<span class='" + fund_in_way_vol_show + "'>买入在途：" + tools.fmoney(fund_in_way_vol) + "元</span>" +
                    "<span class='" + fund_out_way_vol_show + "'>卖出在途：" + tools.fmoney(fund_out_way_vol) + "元</span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 m_paddingTop_10 " + tips_is_show + "'>" + hold_tips + "</li>" +
                    "<li style='white-space: nowrap;color:#319ef2' class='m_text_right contract m_font_size12 m_paddingTop_10  " + '' + "'>产品合同</li>" +
                    "</ul>" +
                    "</div>"
                //"<dd class='contract "+ fund_in_way_vol_show +"' style='white-space: nowrap;'>买入在途：" + tools.fmoney(fund_in_way_vol)+
                //"<dd class='contract "+ fund_out_way_vol_show +"' style='white-space: nowrap;'>卖出在途" + tools.fmoney(fund_out_way_vol)+
            } else if (fundData[i].series_id) {   //系列产品
                let expect_flag_show = (fundData[i].expect_flag == '1' && fundData[i].expect_status == '1') ? '' : 'display_none';
                //收益
                let hold_income = fundData[i].hold_income;
                //累计收益
                let total_income = fundData[i].accumulated_income;
                //最新收益
                let last_income = fundData[i].yest_income;
                //当期预计收益
                let respect_income = fundData[i].respect_income;
                //是否展示资产
                let assets_hold_show = fundData[i].assets_hold_show == '1' ? '' : 'display_none' //是否展示资产
                let profit_hold_show = fundData[i].profit_hold_show == '1' ? '' : 'display_none' //是否展示收益（持仓收益）
                let compare_benchmark_hold_show = fundData[i].compare_benchmark_hold_show == '1' ? '' : 'display_none'    //业绩计提
                let performance_benchmarks_hold_show = fundData[i].performance_benchmarks_hold_show == '1' ? '' : 'display_none'    //业绩比较
                let principal_hold_show = fundData[i].principal_hold_show == '1' ? '' : 'display_none' //是否展示本金
                let last_income_hold_show = fundData[i].last_income_hold_show == '1' ? '' : 'display_none'    //是否展示最新收益
                let total_income_hold_show = fundData[i].total_income_hold_show == '1' ? '' : 'display_none';    //是否展示累计收益
                let estimated_income_hold_show = fundData[i].estimated_income_hold_show == '1' ? '' : 'display_none';
                let last_income_hold_show_bottom;
                let last_income_hold_show_top = 'display_none';
                if (fundData[i].profit_hold_show === '1' && fundData[i].total_income_hold_show === '1' && fundData[i].last_income_hold_show === '1') {
                    last_income_hold_show_top = '';
                    last_income_hold_show_bottom = 'display_none';
                } else if (fundData[i].last_income_hold_show === '1') {
                    last_income_hold_show_bottom = '';
                } else {
                    last_income_hold_show_bottom = 'display_none';
                }
                html += "<div class='pro_box item template_box flex'>" +
                    "<div style='display: none' class='fundInfo'>" + JSON.stringify(fundData[i]) + "</div>" +
                    "<ul class='template_left vertical_line'>" +
                    "<li class='m_border_bottom_D2E3E8 main_flxe new_right_icon  m_font_size16 m_bold'><p class='main_flxe m_width_61'><span>" + fundData[i].series_name + "</span></p><p class='main_flxe vertical_center flex_1 m_right_icon'></p></li>" +
                    // "<li class='m_border_bottom_D2E3E8  m_font_size16 m_bold " + hold_volList + "'><span>" + fundname + "</span><span class='revenueUpdates "+ expect_flag_show +"'>今日收益已更新</span></li>" +
                    "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                    "<span class='m_font_size12 " + assets_hold_show + "'>资产：<span>" + totalFundVol + "元</span></span>" +
                    "<span class='m_font_size12 " + last_income_hold_show_top + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                    "<span class='m_font_size12 " + principal_hold_show + "'>本金：<span>" + cost_money + "元</span></span>" +
                    "<span class='m_font_size12 " + last_income_hold_show_bottom + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                    "<span class='m_font_size12 " + profit_hold_show + "'>持仓收益：<span class='text_red'>" + hold_income + "元</span></span>" +
                    "<span class='m_font_size12 " + compare_benchmark_hold_show + "'>年化业绩基准：<span>" + accrualBasis + "%</span></span>" +
                    "<span class='m_font_size12 " + total_income_hold_show + "'>累计收益：<span class='text_red'>" + total_income + "元</span></span>" +
                    "<span class='m_font_size12 " + estimated_income_hold_show + "'>当期预计收益：<span>" + respect_income + "元</span></span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                    "<span class='" + fund_in_way_vol_show + "'>买入在途：" + tools.fmoney(fund_in_way_vol) + "元</span>" +
                    "<span class='" + fund_out_way_vol_show + "'>卖出在途：" + tools.fmoney(fund_out_way_vol) + "元</span>" +
                    "</li>" +
                    "</ul>" +
                    "</div>"

            } else if (prod_sub_type2 == "90") { //普通私募
                html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + JSON.stringify(fundData[i]) + "</div>" +
                    "<h4 class='" + o_hold_volList + "'>" + fundname + str + "<span class='icon'></span></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + cost_money +
                    "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准(年化)：<span>" + accrualBasis +
                    "%</span></dd></dl><dl><dt></dt><dd>期限： <span>" + closedLength +
                    "</span></dd><dd class='text-right' style='white-space: nowrap;'>确认日：<span>" + tools.ftime(interestStartDate) +
                    "</span></dd></dl><dl><dt></dt><dd>到期日： <span>" + tools.ftime(dueDate) + "</span></dd>" +
                    "</dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" + "</dd></dl>" +
                    "<dl><dd class='contract " + fund_in_way_vol_show + "' style='white-space: nowrap;'>买入在途：" + tools.fmoney(fund_in_way_vol) +
                    "元</dd><dd class='contract " + fund_out_way_vol_show + "' style='white-space: nowrap;'>卖出在途" + tools.fmoney(fund_out_way_vol) +
                    "元</dl></div></div></div>";

            } else if (prod_sub_type2 == "93") {
                html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + JSON.stringify(fundData[i]) + "</div>" +
                    "<h4 class='" + o_hold_volList + "'>" + fundname + str + "<span class='icon'></span></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>资产：<span>" + tools.fmoney(fundData[i].fund_vol) +
                    "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>昨日收益：<span>" + tools.fmoney(fundData[i].yest_income) +
                    "元</span></dd></dl><dl><dt></dt><dd>持仓收益： <span>" + tools.fmoney(fundData[i].hold_income) + "元</span></dd>" +
                    "</dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" + "</dd></dl>" +
                    "<dl><dd class='contract " + fund_in_way_vol_show +  "' style='white-space: nowrap;'>买入在途：" + tools.fmoney(fund_in_way_vol) +
                    "元</dd><dd class='contract " + fund_out_way_vol_show + "' style='white-space: nowrap;'>卖出在途：" + tools.fmoney(fund_out_way_vol) +
                    "元</dl></div></div></div>";
            } else if (prod_sub_type2 == "94") {
                html += "<div class='pro_box shadow_box'>" +
                    "<div class='fundInfo' style='display: none'>" + JSON.stringify(fundData[i]) + "</div>" +
                    "<h4 class='" + o_hold_volList + "'>" + fundname + str + "<span class='icon'></span></h4>" +
                    "<div class='pro-info-content'><div class='pro-info-body'><dl><dd>本金：<span>" + tools.fmoney(fundData[i].cost_money) +
                    "</span>元</dd><dd class='text-right' style='white-space: nowrap;'>业绩计提基准：<span>" + tools.fmoney(min_interest_rate) + '%-' + tools.fmoney(max_interest_rate) +
                    "%</span></dd></dl><dl><dt></dt><dd>锁定期： <span>" + fundData[i].lock_day + "</span></dd>" +
                    "</dd><dd class='text-right contract' style='white-space: nowrap;color:#319ef2;'>产品合同" + "</dd></dl>" +
                    "<dl><dd class='contract " + fund_in_way_vol_show + "' style='white-space: nowrap;'>买入在途：" + tools.fmoney(fund_in_way_vol) +
                    "元</dd><dd class='contract " + fund_out_way_vol_show + "' style='white-space: nowrap;'>卖出在途：" + tools.fmoney(fund_out_way_vol) +
                    "元</dl></div></div></div>";
            } else if (prod_sub_type2 == "200") { //公募整合
                // console.log(fundData[i].prod_sub_type2,111)
                //专享产品
                if (fundData[i].exclusive_type) {
                    let newShldName = fundData[i].exclusive_type == '8' ? '收益' : '基础收益'
                    fundname = fundData[i].exclusive_type ? fundData[i].prod_exclusive_name : fundData[i].fund_sname;
                    var imgStr = "";
                    if (fundData[i].url) { //专享图标
                        imgStr = '<img src="' + require("gconfig").global.oss_url + fundData[i].url + '"/>';
                    }

                    html += "<div class='pro_box item template_box flex'>" +
                        "<div style='display: none' class='fundInfo'>" + JSON.stringify(fundData[i]) + "</div>" +
                        "<ul class='template_left vertical_line'>" +
                        "<li class='m_border_bottom_D2E3E8 m_font_size16 m_bold " + hold_volList + "'>" + fundname + imgStr + "</li>" +
                        "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                        "<span class='m_font_size12 '>资产：<span>" + tools.fmoney(fundData[i].total_fund_vol) + "元</span></span>" +
                        "</li>" +
                        "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                        "<span class='m_font_size12'>本金：<span>" + tools.fmoney(fundData[i].cost_money) + "元</span></span>" +
                        "<span class='m_font_size12'>" + newShldName + "：<span class='" + tools.addMinusClass(fundData[i].hold_income) + "'>" + tools.fmoney(fundData[i].hold_income) + "元</span></span>" +
                        "</li>" +
                        "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                        "<span class='" + fund_in_way_vol_show + "'>买入在途：" + tools.fmoney(fund_in_way_vol) + "元</span>" +
                        "<span class='" + fund_out_way_vol_show + "'>卖出在途：" + tools.fmoney(fund_out_way_vol) + "元</span>" +
                        "</li>" +
                        "</ul>" +
                        "</div>"
                } else {
                    // 普通公募
                    //收益
                    let hold_income = fundData[i].hold_income;
                    //累计收益
                    let total_income = fundData[i].accumulated_income;
                    //昨日收益
                    let last_income = fundData[i].yest_income;
                    //当期预计收益
                    let respect_income = fundData[i].respect_income;
                    //是否展示资产
                    let assets_hold_show = fundData[i].assets_hold_show == '1' ? '' : 'display_none' //是否展示资产
                    let profit_hold_show = fundData[i].profit_hold_show == '1' ? '' : 'display_none' //是否展示收益（持仓收益）
                    let compare_benchmark_hold_show = fundData[i].compare_benchmark_hold_show == '1' ? '' : 'display_none'    //业绩计提
                    let performance_benchmarks_hold_show = fundData[i].performance_benchmarks_hold_show == '1' ? '' : 'display_none'    //业绩比较
                    let principal_hold_show = fundData[i].principal_hold_show == '1' ? '' : 'display_none' //是否展示本金
                    let last_income_hold_show = fundData[i].last_income_hold_show == '1' ? '' : 'display_none'    //是否展示昨日收益
                    let total_income_hold_show = fundData[i].total_income_hold_show == '1' ? '' : 'display_none'    //是否展示累计收益
                    let estimated_income_hold_show = fundData[i].estimated_income_hold_show == '1' ? '' : 'display_none'    //是否展示当期预计收益
                    let last_income_hold_show_bottom;
                    let last_income_hold_show_top = 'display_none';
                    if (fundData[i].profit_hold_show === '1' && fundData[i].total_income_hold_show === '1' && fundData[i].last_income_hold_show === '1') {
                        last_income_hold_show_top = '';
                        last_income_hold_show_bottom = 'display_none';
                    } else if (fundData[i].last_income_hold_show === '1') {
                        last_income_hold_show_bottom = '';
                    } else {
                        last_income_hold_show_bottom = 'display_none';
                    }
                    if (financial_prod_type == '06') {
                        //如果是基金超市
                        html += "<div class='pro_box item template_box flex'>" +
                            "<div style='display: none' class='fundInfo'>" + JSON.stringify(fundData[i]) + "</div>" +
                            "<ul class='template_left vertical_line'>" +
                            "<li class='m_border_bottom_D2E3E8  m_font_size16 m_bold " + hold_volList + "'>" + fundname + "</li>" +
                            "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                            "<span class='m_font_size12 " + assets_hold_show + "'>资产：<span>" + totalFundVol + "元</span></span>" +
                            "<span class='m_font_size12 " + last_income_hold_show_top + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                            "</li>" +
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                            "<span class='m_font_size12 " + principal_hold_show + "'>本金：<span>" + cost_money + "元</span></span>" +
                            "<span class='m_font_size12 " + last_income_hold_show_bottom + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                            "<span class='m_font_size12 " + profit_hold_show + "'>持仓收益：<span class='text_red'>" + hold_income + "元</span></span>" +
                            "<span class='m_font_size12 " + compare_benchmark_hold_show + "'>年化业绩基准：<span>" + accrualBasis + "%</span></span>" +
                            "<span class='m_font_size12 " + performance_benchmarks_hold_show + "'>业绩比较基准：<span>" + accrualBasis + "%</span></span>" +
                            "<span class='m_font_size12 " + total_income_hold_show + "'>累计收益：<span class='text_red'>" + total_income + "元</span></span>" +
                            "<span class='m_font_size12 " + estimated_income_hold_show + "'>当期预计收益：<span>" + respect_income + "元</span></span>" +
                            "</li>" +
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                            "<span class='" + fund_in_way_vol_show + "'>买入在途：" + tools.fmoney(fund_in_way_vol) + "元</span>" +
                            "<span class='" + fund_out_way_vol_show + "'>卖出在途：" + tools.fmoney(fund_out_way_vol) + "元</span>" +
                            "</li>" +
                            "</ul>" +
                            "</div>"
                    } else {

                        html += "<div class='pro_box item template_box flex'>" +
                            "<div style='display: none' class='fundInfo'>" + JSON.stringify(fundData[i]) + "</div>" +
                            "<ul class='template_left vertical_line'>" +
                            "<li class='m_border_bottom_D2E3E8  m_font_size16 m_bold " + hold_volList + "'>" + fundname + "</li>" +
                            "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                            "<span class='m_font_size12 " + assets_hold_show + "'>资产：<span>" + totalFundVol + "元</span></span>" +
                            "<span class='m_font_size12 " + last_income_hold_show_top + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                            "<span class='m_font_size12 " + principal_hold_show + "'>本金：<span>" + cost_money + "元</span></span>" +
                            "<span class='m_font_size12 " + last_income_hold_show_bottom + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                            "<span class='m_font_size12 " + profit_hold_show + "'>持仓收益：<span class='text_red'>" + hold_income + "元</span></span>" +
                            "<span class='m_font_size12 " + compare_benchmark_hold_show + "'>年化业绩基准：<span>" + accrualBasis + "%</span></span>" +
                            "<span class='m_font_size12 " + total_income_hold_show + "'>累计收益：<span class='text_red'>" + total_income + "元</span></span>" +
                            "<span class='m_font_size12 " + estimated_income_hold_show + "'>当期预计收益：<span>" + respect_income + "元</span></span>" +
                            "</li>" +
                            "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                            "<span class='" + fund_in_way_vol_show + "'>买入在途：" + tools.fmoney(fund_in_way_vol) + "元</span>" +
                            "<span class='" + fund_out_way_vol_show + "'>卖出在途：" + tools.fmoney(fund_out_way_vol) + "元</span>" +
                            "</li>" +
                            "</ul>" +
                            "</div>"
                    }
                }
            } else if (fundData[i].prod_source == '2') { // 投顾
                //收益
                let hold_income = fundData[i].hold_income;
                //累计收益
                let total_income = fundData[i].accumulated_income;
                //昨日收益
                let last_income = fundData[i].yest_income;
                //当期预计收益
                let respect_income = fundData[i].respect_income;
                //是否展示资产
                // fundData[i].assets_hold_show = '1'
                // fundData[i].profit_hold_show = '1'
                // fundData[i].last_income_hold_show = '1'
                // fundData[i].total_income_hold_show = '1'
                let assets_hold_show = fundData[i].assets_hold_show == '1' ? '' : 'display_none' //是否展示资产
                let profit_hold_show = fundData[i].profit_hold_show == '1' ? '' : 'display_none' //是否展示收益（持仓收益）
                let last_income_hold_show = fundData[i].last_income_hold_show == '1' ? '' : 'display_none'    //是否展示昨日收益
                // console.log(fundData[i].total_income_hold_show)
                let total_income_hold_show = fundData[i].total_income_hold_show == '1' ? '' : 'display_none'    //是否展示累计收益
                let operatehold = fundData[i].operatehold == '1' ? '' : 'display_none'    //是否展示运作天数
                let operatedays = fundData[i].operatedays  //运作天数
                html += "<div class='pro_box item template_box flex'>" +
                    "<div style='display: none' class='fundInfo'>" + JSON.stringify(fundData[i]) + "</div>" +
                    "<ul class='template_left vertical_line'>" +
                    "<li class='m_border_bottom_D2E3E8 m_right_icon  m_font_size16 m_bold " + hold_volList + "'>" + fundname + "</li>" +
                    "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                    "<span class='m_font_size12 " + assets_hold_show + "'>资产：<span>" + tools.fmoney(fundData[i].fund_vol) + "元</span></span>" +
                    "<span class='m_font_size12 " + last_income_hold_show + "'>昨日收益：<span class='text_red'>" + last_income + "元</span></span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                    "<span class='m_font_size12 " + profit_hold_show + "'>持仓收益：<span class='text_red'>" + hold_income + "元</span></span>" +
                    "<span class='m_font_size12 " + total_income_hold_show + "'>累计收益：<span class='text_red'>" + total_income + "元</span></span>" +
                    "<span class='m_font_size12 " + operatehold + "'>运作天数：<span class='text_red'>" + operatedays + "天</span></span>" +
                    "</li>" +
                    "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                    "<span class='" + fund_in_way_vol_show + "'>买入在途：" + tools.fmoney(fund_in_way_vol) + "元</span>" +
                    "<span class='" + fund_out_way_vol_show + "'>卖出在途：" + tools.fmoney(fund_out_way_vol) + "元</span>" +
                    "<span class='" + fund_change_way_vol_show + "'>调仓在途：" + tools.fmoney(fund_change_way_vol) + "元</span>" +
                    "</li>" +
                    "</ul>" +
                    "</div>"
            } else {
                html += ''
            }
        }
        return html;
    }

    function showHoldCompletedData(data) {
        cur_page_done = (+cur_page_done);
        cur_page_done += 1;
        var html = "";
        for (var i = 0; i < data.length; i++) {
            var fundname = data[i].fund_names;
            //产品代码
            var fundcode = data[i].fund_code;
            //业绩计提基准（年化）
            var performanceBasis = data[i].performance_basis ? tools.fmoney(data[i].performance_basis) : "--";
            //实际年化收益率
            var annualizedRate = data[i].annualized_rate ? tools.fmoney(data[i].annualized_rate) : "--";

            //到期日 due_date end_date
            var dueDate
            if (data[i].due_date > data[i].end_date) {
                dueDate = data[i].end_date;
            } else {
                dueDate = data[i].due_date
            }
            //起息日
            var valueDate = data[i].value_date;

            html += "<div class='pro_box item template_box flex'>" +
                "<div style='display: none' class='fundInfo'>" + JSON.stringify(data[i]) + "</div>" +
                "<ul class='template_left vertical_line'>" +
                "<li class='m_border_bottom_D2E3E8 m_font_size16 m_bold'>" + fundname + "<span class='m_font_size12 transaction' style='float:right;white-space: nowrap;color: #319ef2;'>交易详情</span></li>" +

                "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                "<span class='m_font_size12'>本金：<span>" + tools.fmoney(data[i].principal) + "元</span></span>" +
                "<span class='m_font_size12'>收益：<span>" + tools.fmoney(data[i].total_revenue) + "元</span></span>" +
                "</li>" +
                "<li class='m_text_color_778590 flex m_paddingTop_10'>" +
                "<span class='m_font_size12 '>起息日：<span>" + tools.ftime(valueDate) + "</span></span>" +
                "<span class='m_font_size12 '>到期日：<span>" + tools.ftime(dueDate) + "</span></span>" +
                "</li>" +
                "<li class='m_text_color_778590 m_font_size12 flex wrap m_paddingTop_10'>" +
                "<span class='m_font_size12 '>实际年化收益率：<span>" + annualizedRate + "%</span></span>" +
                "<span class='m_font_size12 '>业绩计提基准：<span>" + performanceBasis + "%</span></span>" +
                "</li>" +
                "</ul>" +
                "</div>"
        }
        return html;
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //交易记录
        appUtils.bindEvent($(_pageId + " .tradeRecord"), function () {
            appUtils.clearSStorage("trsFundCode");
            appUtils.clearSStorage("trsAckDate");
            appUtils.setSStorageInfo("financial_prod_type", financial_prod_type);
            // if (financial_prod_type == '07' || financial_prod_type == '08') {
            //     appUtils.setSStorageInfo("is_transit", '');
            //     appUtils.setSStorageInfo("busi_code", '');
            //     appUtils.pageInit(_pageCode, "combProduct/combTransaction");
            // } else {

            // }
            appUtils.setSStorageInfo("series_id", '');
            appUtils.pageInit(_pageCode, "template/transaction");

        }, "click");
        //私募产品合同
        appUtils.preBindEvent($(_pageId + " .holdFunds"), ".contract", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            //查看可转让合同
            if (detail.transferable == '1') sessionStorage.transferable = 1
            appUtils.setSStorageInfo("productInfo", detail);
            tools.recordEventData('1','contract','查看合同');
            appUtils.pageInit(_pageCode, "highEnd/contract", { isSign: true });
        }, 'click');
        //产品详情
        appUtils.preBindEvent($(_pageId + " .holdFunds"), ".pro_box", function () {
            var productInfo = JSON.parse($(this).find(".fundInfo").text());
            // console.log(productInfo)
            let fundCode = productInfo.fund_code ? productInfo.fund_code : productInfo.series_id ? productInfo.series_id : ''
            tools.recordEventData('1','pro_box_' + fundCode,'进入持仓详情',{fundCode:fundCode});
            sessionStorage.due_date = productInfo.due_date
            let hold_vol = productInfo.hold_vol * 1
            // if(hold_vol <= 0 && (!productInfo.series_id || productInfo.series_id == '')){
            //     //在途 无效点击
            //     return;
            // }
            var prod_sub_type2 = productInfo.prod_sub_type2;
            var page = {
                "81": "highEnd/holdForgeDetail", //一创小集合
                "90": "highEnd/holdDetail",
                "91": "highEnd/smallholdDetail",
                "92": "highEnd/fixHoldDetail",
                "93": "highEnd/yh_holdDetail",
                "94": "highEnd/holdLockDetail",
                "95": "highEnd/holdPolicyDetail",
                "96": "highEnd/holdSolidDetail",
                "97": "highEnd/holdFixDetail",
                "100": "template/holdHeighDetail",
                "200": "template/publicHoldHeightDetail"
            }
            sessionStorage.vip_buttonShow = true;
            var exclusive_type = productInfo.exclusive_type;
            if (productInfo.series_id) {
                //系列产品特殊处理
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.setSStorageInfo("seriesInfo", productInfo);
                appUtils.setSStorageInfo("list_productInfo", productInfo);
                appUtils.pageInit(_pageCode, "template/seriesList");
                return;
            }
            if (page[prod_sub_type2]) {
                if (exclusive_type && (exclusive_type == "8" || exclusive_type == '1' || exclusive_type == '2')) {
                    let newType = exclusive_type == "8" ? 'old' : 'new'
                    appUtils.setSStorageInfo("newType", newType);
                    appUtils.setSStorageInfo("holdObj", productInfo);
                    appUtils.pageInit(_pageCode, "inclusive/moneytaryHousingMochikura", { newType: newType });
                    return;
                }
                appUtils.setSStorageInfo("list_productInfo", productInfo);
                appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.pageInit(_pageCode, page[productInfo.prod_sub_type2]);
            } else if (productInfo.prod_source == '2') {
                appUtils.setSStorageInfo("list_productInfo", productInfo);
                appUtils.setSStorageInfo("fund_code", productInfo.fund_code);
                appUtils.setSStorageInfo("productInfo", productInfo);
                appUtils.pageInit(_pageCode, "combProduct/combHoldHeightDetail");
            }
        }, 'click');

        // //点击持有或在途或已完成按钮
        // appUtils.bindEvent($(_pageId + " .tab_box a"), function () {
        //     $(_pageId + " .tab_box a").removeClass("current");
        //     $(this).addClass("current");
        //     _cust_fund_type = $(this).attr("cust_fund_type");
        //     $(_pageId + " .finance_pro .holdFunds").html(" ");
        //     // 已完成
        //     if (_cust_fund_type == "1") {
        //         cur_page_done = 1, num_per_page_done = 10; isEndDone = false;
        //         getHoldCompletedProd(false);
        //     } else if (_cust_fund_type == "0") {
        //         cur_page = 1, num_per_page = 10; isEnd = false
        //         getHoldProd(false);
        //     }
        // }, "click");

        // 持有
        appUtils.bindEvent($(_pageId + " .tab_box #cyList"), function () {
            $(_pageId + " .finance_pro .holdFunds").html("");
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            _cust_fund_type = $(this).attr("cust_fund_type");
            cur_page = 1, num_per_page = 10; isEnd = false
            getHoldProd(false);
        }, "click")

        // 已完成
        appUtils.bindEvent($(_pageId + " .tab_box #ztList"), function () {
            $(_pageId + " .finance_pro .holdFunds").html("");
            $(_pageId + " .tab_box a").removeClass("current");
            $(this).addClass("current");
            _cust_fund_type = $(this).attr("cust_fund_type");
            cur_page_done = 1, num_per_page_done = 10; isEndDone = false;
            getHoldCompletedProd(false);
        }, "click")

        //已完成交易记录
        appUtils.preBindEvent($(_pageId + " .holdFunds"), ".transaction", function (e) {
            e.stopPropagation();    //  阻止事件冒泡
            e.preventDefault();
            var detail = JSON.parse($(this).parents(".pro_box").find(".fundInfo").text());
            appUtils.setSStorageInfo("productInfo", detail);
            tools.recordEventData('1','transaction','交易记录');
            appUtils.pageInit(_pageCode, "template/cpdTransaction");
        }, 'click');
    }

    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    if (_cust_fund_type == "1") {
                        cur_page_done = 1;
                        getHoldCompletedProd(false);
                    } else if (_cust_fund_type == "0") {
                        cur_page = 1;
                        getHoldProd(false);
                    }
                },
                "upHandle": function () {
            
                    if (_cust_fund_type == "1") {
                        if (!isEndDone) {
                            $(_pageId + " .visc_pullUpIcon").show();
                            $(_pageId + " .visc_pullUpDiv").show();
                            getHoldCompletedProd(true);
                        } else {
                            $(_pageId + " .new_none").show();
                        }
                    } else if (_cust_fund_type == "0") {
                        if (!isEnd) {
                            $(_pageId + " .visc_pullUpIcon").show();
                            $(_pageId + " .visc_pullUpDiv").show();
                            getHoldProd(true);
                        } else {
                            $(_pageId + " .new_none").show();
                        }
                    }

                    hidePullUp();
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        //$(_pageId + " .visc_pullUp").show();
    }

    function hidePullUp() {
        //$(_pageId + " .visc_pullUp").hide();
        $(_pageId + " .visc_pullUpIcon").hide();
        $(_pageId + " .visc_pullUpDiv").hide();
        pageScrollInit();
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #totalFundvol").html("--");
        $(_pageId + " #int_way_vol").html("--");
        $(_pageId + " #out_way_vol").html("--");
        $(_pageId + " #unknow_trans_count").html("--");
        $(_pageId + " .finance_pro .holdFunds").html(" ");
        $(_pageId + " .new_none").hide();
        $(_pageId + " .update_em").hide();
        $(_pageId + " .tab_box").hide();
        isEnd = false;
        isEndDone = false;
        cur_page = 1;
        cur_page_done = 1;
        _cust_fund_type = "0";
    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});