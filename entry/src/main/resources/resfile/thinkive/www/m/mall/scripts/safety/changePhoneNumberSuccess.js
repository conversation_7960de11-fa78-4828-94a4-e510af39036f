//@版本: 2.0
define(function (require, exports, module) {
    var tools = require("../common/tools");
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        isUserBankCard,
        _pageId = "#safety_changePhoneNumberSuccess";
    var external = require("external");
    var phoneNum = "";//注册手机号码
    var userInfo;
    var ut = require("../common/userUtil");
    var sms_mobile = require("../common/sms_mobile");

    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        isUserBankCard = appUtils.getSStorageInfo("isUserBankCard");
        if(isUserBankCard == false){    //未绑卡进入
            $(_pageId + " #pwd_111").hide();
        }else{  //已绑卡进入
            $(_pageId + " #pwd_111").show();
        }
        sms_mobile.init(_pageId);
        userInfo = ut.getUserInf();
        $(_pageId + " #phoneShow").hide();
        common.systemKeybord(); // 解禁系统键盘
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                guanbi();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var shuru = $(_pageId + " #pwd_1").val();
                var passflag = "";
                if (shuru) {
                    for (var i = 0; i < shuru.length; i++) {
                        passflag += "*";
                    }
                    if (shuru.length == 1) {
                        $(_pageId + " #pwd_11").addClass("active");
                    }
                } else {
                    passflag = "请输入交易密码";
                    $(_pageId + " #pwd_11").removeClass("active");
                }
                $(_pageId + " #pwd_11").text(passflag);
                var len = shuru.length;
                if (len >= 6) {
                    shuru = shuru.substring(0, 6);
                    $(_pageId + " #pwd_1").val(shuru);
                    $(_pageId + " #pwd_11").text(passflag);
                }
            } // 键盘的输入事件
        };
    }
    function noBindCardSumit(phone,code,identifyingcode){
        let registered_mobile = phone;  //新手机
        var changePhoneInfo = appUtils.getSStorageInfo("changePhoneInfo");
        let old_registered_mobile = changePhoneInfo.changephone; //旧手机
        let sms_code = identifyingcode;
        let param = {registered_mobile,old_registered_mobile,sms_code};
        param.sms_mobile = registered_mobile;
        service.reqFun101086(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            //验证码重置
            sms_mobile.clear();
            $(_pageId + " .tips_box").hide();
            if (error_no == "0") {
                // 清除手势密码账号和参数
                var paramlist = {
                    funcNo: "50042",
                    key: "account_password",
                    isEncrypt: "1",
                    value: ""
                };
                tools.loginOutQy();
                common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
                common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
                common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
                external.callMessage(paramlist);
                layerUtils.iMsg(-1, "更换成功 请重新登录");
                // appUtils.clearSStorage("changePhoneInfo");
                common.clearLocalStorage("mobileWhole");
                appUtils.clearSStorage(true);
                // common.setLocalStorage("mobileWhole", newPhoneNumber);
                appUtils.pageInit("safety/changePhoneNumberSuccess", "login/userLogin", {});
            } else {
                layerUtils.iAlert(error_info);
            }
        });
        // console.log(param)
    }
    //绑定事件
    function bindPageEvent() {
        //点击获取验证码
        appUtils.bindEvent($(_pageId + " #getYzm"), function () {
            var newPhoneNumber = $(_pageId + " #newPhoneNumber").val();

            var code = $(_pageId + " #getYzm").attr("data-state");
            if (code == "false") {
                return;
            }
            if (validatorUtil.isEmpty(newPhoneNumber)) {
                layerUtils.iMsg(-1, "新手机号码不能为空");
                return false;
            }
            if (!validatorUtil.isMobile(newPhoneNumber)) {
                //判断输入的是否是手机号
                layerUtils.iMsg(-1, "新手机号码有误");
                return;
            } else {
                checkMobileIsExists(newPhoneNumber);
            }
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            guanbi();
        });
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #identifyingcode"), function () {
            $(_pageId + " #pwd_11").removeClass("active").addClass("unable");
            guanbi();
        }, 'focus');

        appUtils.bindEvent($(_pageId + " #newPhoneNumber"), function () {
            $(_pageId + " #pwd_11").removeClass("active").addClass("unable");
            guanbi();
        }, 'focus');

        appUtils.bindEvent($(_pageId + " #newPhoneNumber"), function () {
            var curVal = this.value;
            if (curVal.length == 11) {
                var param = {
                    mobile: curVal,
                };
                service.reqFun102005(param, function (data) {
                    var error_no = data.error_no,
                        error_info = data.error_info;
                    if (error_no == "-10200502") {//10200501 账号已注册
                        $(_pageId + " #phoneShow").html("该手机号已注册");
                        $(_pageId + " #phoneShow").show();
                        $(_pageId + " #phoneShow").html(error_info).show();
                    } else if (error_no == "-10200501") {//黑名单
                        $(_pageId + " input").blur();
                        layerUtils.iAlert("网络繁忙,请稍后重试!");
                    } else if (error_no == "-10200504") {//晋金所已开户
                    } else if (error_no != "0") {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
            if (!validatorUtil.isMobile(curVal)) {
                $(_pageId + " #phoneShow").hide();
            }
        }, 'input');


        //点击完成
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            var code = $(_pageId + " #getYzm").attr("data-state");
            var newPhoneNumber = $(_pageId + " #newPhoneNumber").val();
            var identifyingcode = $(_pageId + " #identifyingcode").val();
            var pwd_1 = $(_pageId + " #pwd_1").val();

            if (!validatorUtil.isMobile(newPhoneNumber)) {
                layerUtils.iMsg(-1, "新手机号码有误");
                return;
            }
            if (identifyingcode.length < 6) {
                layerUtils.iMsg(-1, "验证码最少6位数");
                return;
            }
            if (code == "true") {
                layerUtils.iMsg(-1, "请先获取验证码");
                return;
            }
            if(isUserBankCard == false) return noBindCardSumit(newPhoneNumber,code,identifyingcode)
            if (validatorUtil.isEmpty(pwd_1)) {
                layerUtils.iMsg(-1, "请输入交易密码");
                return;
            }
            if (pwd_1.length < 6) {
                layerUtils.iMsg(-1, "交易密码最少6位数");
                return;
            }
            
            service.getRSAKey({}, function (data) {
                if (data.error_no == "0") {
                    var modulus = data.results[0].modulus;
                    var publicExponent = data.results[0].publicExponent;
                    var endecryptUtils = require("endecryptUtils");
                    var trade_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, pwd_1);
                    var changePhoneInfo = appUtils.getSStorageInfo("changePhoneInfo");
                    var param101010 = {
                        "cert_type": "0",
                        "cert_no": userInfo.identityNum,
                        "sms_mobile": newPhoneNumber,
                        "sms_code": identifyingcode,
                        "registered_mobile": newPhoneNumber,
                        "old_registered_mobile": changePhoneInfo.changephone,
                        "trans_pwd": trade_pwd,
                    };
                    service.reqFun101010(param101010, function (data) {
                        var error_no = data.error_no,
                            error_info = data.error_info;
                        //验证码重置
                        sms_mobile.clear();
                        $(_pageId + " .tips_box").hide();
                        if (error_no == "0") {
                            // 清除手势密码账号和参数
                            var paramlist = {
                                funcNo: "50042",
                                key: "account_password",
                                isEncrypt: "1",
                                value: ""
                            };
                            tools.loginOutQy();
                            common.setLocalStorage("fingerprintPwd_flag",'0');  //置空指纹登录信息
                            common.setLocalStorage("setFingerprintNum",''); //去掉首页弹窗次数
                            common.setLocalStorage("setFingerprintInfo",'');//充值设置指纹次数
                            external.callMessage(paramlist);
                            layerUtils.iMsg(-1, "更换成功 请重新登录");
                            // appUtils.clearSStorage("changePhoneInfo");
                            common.clearLocalStorage("mobileWhole");
                            appUtils.clearSStorage(true);
                            // common.setLocalStorage("mobileWhole", newPhoneNumber);
                            appUtils.pageInit("safety/changePhoneNumberSuccess", "login/userLogin", {});
                        } else {
                            layerUtils.iAlert(error_info);
                        }
                    });
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(data.error_info);
                }
            }, {"isLastReq": false});
        });
    }


    //判断新的手机号是否注册过
    function checkMobileIsExists(newPhoneNumber) {
        var param = {mobile: newPhoneNumber};
        service.reqFun102005(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "-10200501") { //账号已注册
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, "该手机号码已在平台注册");
            } else if (error_no == "-10200502") { //黑名单用户
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, "系统繁忙，请稍后重试");
            } else if (error_no == "-10200504") { //晋金所已开户
                var param = {
                    "mobile_phone": newPhoneNumber,
                    "type": common.sms_type.changeMobile,
                    "send_type": "0",
                };
                sms_mobile.sendPhoneCode(param); //发送短信,获取验证码
            } else if (error_no == "0") {
                var param = {
                    "mobile_phone": newPhoneNumber,
                    "type": common.sms_type.changeMobile,
                    "send_type": "0",
                };
                sms_mobile.sendPhoneCode(param); //发送短信,获取验证码
            } else {
                layerUtils.iLoading(false);
                layerUtils.iMsg(-1, error_info);
            }
        }, {isLastReq: false});
    }

    //点击开启键盘
    appUtils.bindEvent($(_pageId + " #pwd_111"), function (e) {
        e.stopPropagation();
        $(_pageId + " #newPhoneNumber").blur();
        $(_pageId + " #identifyingcode").blur();
        if ($(_pageId + " #pwd_1").val() == "") {
            $(_pageId + " #pwd_11").removeClass("unable");
        } else {
            $(_pageId + " #pwd_11").removeClass("unable").addClass("active");
        }
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "safety_changePhoneNumberSuccess";
        param["eleId"] = "pwd_1";
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    });

    //关闭键盘
    function guanbi() {
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    function destroy() {
        $(_pageId + " #newPhoneNumber").blur();
        $(_pageId + " #identifyingcode").blur();
        $(_pageId + " #pwd_11").removeClass("active").addClass("unable");
        $(_pageId + " #pwd_1").val("");
        guanbi();
        sms_mobile.destroy();
        $(_pageId + " #identifyingcode").val("");
        $(_pageId + " #newPhoneNumber").val("");
        $(_pageId + " .tips_box").hide();
        $(_pageId + " #pwd_11").text("请输入交易密码");
        $(_pageId + ' #weihao').hide();
        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var changePhoneNumberSuccess = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = changePhoneNumberSuccess;
});
