/**
 * 创建于2017.12.29
 * 发送短信验证码
 *
 */

define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var service = require("mobileService"); //业务层接口，请求数据
    var layerUtils = require("layerUtils");
    var i;
    var sms_mobile = {
        _pageId: "",
        _send_sms_time: "", //短信验证码持续时间
        _timer: "", // 计时器
        _style: {},
        _i : "", //倒计时长
        _millisecond: "", //
        // 初始化短信验证码
        init: function (_pageId, style) {
            sms_mobile._pageId = _pageId;
            sms_mobile._style = style || {
                "backgroundColor": "#C1E3B6",
                "btnText": "获取验证码",
                "activeBtnText": "秒后重新获取",
                "activeEndBtnText": "重新获取验证码",
                "disBackgroundColor": "#D1D4D5",
                "talkCodeText": "如收不到短信 &nbsp;&nbsp;<span id='getTalk'   style='color:blue;font-size:0.14rem;'>语音获取</span>"
            };
            if (sms_mobile._timer) {
                window.clearInterval(sms_mobile._timer);
                sms_mobile._timer = null; // 将_timer设为null，表示没有活动的定时器
            }
            var $yzm = $(sms_mobile._pageId + " #getYzm");
            $yzm.attr("data-state", "true");
            $yzm.css("background-color", sms_mobile._style.backgroundColor);
            $yzm.html(sms_mobile._style.btnText);
            sms_mobile._send_sms_time = '';
            $(sms_mobile._pageId + " #talkCode").html(sms_mobile._style.talkCodeText);
            $(sms_mobile._pageId + " #talkCode").hide();
            $(sms_mobile._pageId + ' #weihao').hide();

        },
        shows: function (time,pageId) {
            sms_mobile._send_sms_time = time || 120;
            var $code;
            if( pageId && pageId !="" && pageId!=null){
                $code = $(sms_mobile._pageId + " #zfqygetYzm");
            }else{
                $code = $(sms_mobile._pageId + " #getYzm");
            }
            $code.attr("data-state", "false"); //点击不能发送
            $code.css({backgroundColor: sms_mobile._style.disBackgroundColor});
            var myDate = new Date();
            var TimeDifference = myDate.getTime();
            if (sms_mobile._i == sms_mobile._send_sms_time) {
                sms_mobile._millisecond = TimeDifference + sms_mobile._send_sms_time * 1000;
            }
            sms_mobile._i = (sms_mobile._millisecond - (sms_mobile._millisecond - TimeDifference) % 1000 - TimeDifference) / 1000;
            if (sms_mobile._i > 1) {
                
                $code.html("" + sms_mobile._i + sms_mobile._style.activeBtnText);
                $code.addClass("sending");
                sms_mobile._i--;
            } else {
                if (sms_mobile._timer) {
                    clearInterval(sms_mobile._timer);
                    sms_mobile._timer = null;
                }
                $(sms_mobile._pageId + " #inputspanid span").css({ color: "#000" });//输入金额正常
                appUtils.setSStorageInfo("icbc_isClick", "1");//可以点击
                sms_mobile._i = sms_mobile._send_sms_time;
                $code.attr("data-state", "true"); //点击能发送
                $code.addClass("sending");
                $code.css({backgroundColor: sms_mobile._style.backgroundColor});
                $code.removeAttr("class");
                $code.html(sms_mobile._style.activeEndBtnText);
                $(sms_mobile._pageId + " #talkCode").html(sms_mobile._style.talkCodeText);
                $(sms_mobile._pageId + " #talkCode").hide();
            }
        },
        //发送普通短信验证码
        sendPhoneCode: function (param,callback) {
            if (sms_mobile._timer) {
                clearInterval(sms_mobile._timer);
                sms_mobile._timer = null;
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    $(sms_mobile._pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                    sms_mobile._i = 120;
                    $(sms_mobile._pageId + " #getYzm").attr("data-state", "false"); //点击不能发送
                    sms_mobile._timer = setInterval(function () {
                        sms_mobile.shows();
                    }, 1000);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
                if(callback) callback(data);
                $(sms_mobile._pageId + " #talkCode").show();
                $(sms_mobile._pageId + " #talkCode").html(sms_mobile._style.talkCodeText);
            });
            //点击获取语音验证码
            appUtils.preBindEvent(sms_mobile._pageId + " #talkCode", "#getTalk", function () {
                param.send_type = "1";
                sms_mobile.sendTalkCode(param);
                if (sms_mobile._timer) {
                    clearInterval(sms_mobile._timer);
                    sms_mobile._timer = null;
                }
                sms_mobile._i = 120;
                sms_mobile._timer = setInterval(function () {
                    sms_mobile.shows();
                }, 1000);
            });
        },
        //银行协议支付验证码
        sendPhoneCodeBankProto: function (param, returnVo) {
            if (sms_mobile._timer) {
                clearInterval(sms_mobile._timer);
                sms_mobile._timer = null;
            }
            service.reqFun151009(param, function (data) {
                if (returnVo) returnVo(data);
                if (data.error_no == "0") {
                    sms_mobile._i = 120;
                    $(sms_mobile._pageId + " #getYzm").attr("data-state", "false"); //点击不能发送
                    $(sms_mobile._pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                    sms_mobile._timer = setInterval(function () {
                        sms_mobile.shows();
                    }, 1000);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        },

        //晋金所平台签约验证码
        sendPhoneCodeFundProto: function (param, returnVo) {
            // console.log(sms_mobile._pageId)
            if (sms_mobile._timer) {
                clearInterval(sms_mobile._timer);
                sms_mobile._timer = null;
            }
            service.reqFun101031(param, function (data) {
                if (returnVo) returnVo(data);
                if (data.error_no == "0") {
                	if(param.sms_time && param.sms_time != "" && param.sms_time != null){
                		sms_mobile._i = param.sms_time
                	}else{
                    	sms_mobile._i = 30;
                    }
                    if(param.page_id && param.page_id != "" && param.page_id != null){
                        $(sms_mobile._pageId + " #zfqygetYzm").attr("data-state", "false"); //点击不能发送
                        $(sms_mobile._pageId + ' #sjweihao').text('已向尾号' + param.bank_reserved_mobile.substring(7, 11) + '的手机号发送短信验证码').show();
                    }else{
                        $(sms_mobile._pageId + " #getYzm").attr("data-state", "false"); //点击不能发送
                        $(sms_mobile._pageId + ' #weihao').text('已向尾号' + param.bank_reserved_mobile.substring(7, 11) + '的手机号发送短信验证码').show();
                      
                    }
                    if(param.payorg_id == '014' && sms_mobile._pageId == '#thfund_inputRechargePwd '){
                        //如果是工行 输入的金额置灰 设置无法点击
                        if(param.isDisabled != '0'){
                            $(sms_mobile._pageId + " #inputspanid span").css({ color: "#666666" });//输入金额置灰
                            appUtils.setSStorageInfo("icbc_isClick", "0");//不可点击
                        }
                        
                    }
                    sms_mobile._timer = setInterval(function () {
                        sms_mobile.shows(sms_mobile._i,param.page_id);
                    }, 1000);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        },

        //晋金所平台签约验证码
        sendPhoneCodeModeFundProto: function (param, returnVo) {
            if (sms_mobile._timer) {
                clearInterval(sms_mobile._timer);
                sms_mobile._timer = null;
            }
            service.reqFun106080(param, function (data) {
                if (returnVo) returnVo(data);
                if (data.error_no == "0") {
                	if(param.sms_time && param.sms_time !="" && param.sms_time!=null){
                		sms_mobile._i = param.sms_time
                	}else{
                    	sms_mobile._i = 30;
                    }
                    if(param.page_id&& param.page_id !="" && param.page_id!=null){
                        $(sms_mobile._pageId + " #zfqygetYzm").attr("data-state", "false"); //点击不能发送
                        $(sms_mobile._pageId + ' #sjweihao').text('已向尾号' + param.bank_reserved_mobile.substring(7, 11) + '的手机号发送短信验证码').show();
                    }else{
                        $(sms_mobile._pageId + " #getYzm").attr("data-state", "false"); //点击不能发送
                        $(sms_mobile._pageId + ' #weihao').text('已向尾号' + param.bank_reserved_mobile.substring(7, 11) + '的手机号发送短信验证码').show();
                    }
                    $(sms_mobile._pageId + " #inputspanid span").css({ color: "#666666" });//输入金额置灰
                    appUtils.setSStorageInfo("icbc_isClick", "0");//不可点击
                    sms_mobile._timer = setInterval(function () {
                        sms_mobile.shows(sms_mobile._i,param.page_id);
                    }, 1000);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        },


        //晋金所授权发送验证码
        sendDrainPhoneCode: function (param, returnVo) {
            if (sms_mobile._timer) {
                clearInterval(sms_mobile._timer);
                sms_mobile._timer = null;
            }
            service.reqFun199003(param, function (data) {
                if (returnVo) returnVo(data);
                if (data.error_no == "0") {
                    sms_mobile._i = 120;
                    $(sms_mobile._pageId + " #getYzm").attr("data-state", "false"); //点击不能发送
                    $(sms_mobile._pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                    sms_mobile._timer = setInterval(function () {
                        sms_mobile.shows(120);
                    }, 1000);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        },

        //发送注册短信验证码  图形验证码校验
        sendRegisterPhoneCode: function (param,callback) {
            if (sms_mobile._timer) {
                clearInterval(sms_mobile._timer);
                sms_mobile._timer = null;
            }
            service.reqFun9199001(param, function (data) {
                if (data.error_no == "0") {
                    $(sms_mobile._pageId + ' #weihao').text('已向尾号' + param.mobile_phone.substring(7, 11) + '的手机号发送短信验证码').show();
                    sms_mobile._i = 120;
                    $(sms_mobile._pageId + " #getYzm").attr("data-state", "false"); //点击不能发送
                    sms_mobile._timer = setInterval(function () {
                        sms_mobile.shows();
                    }, 1000);
                } else {
                    if(callback) callback(data);
                    layerUtils.iAlert(data.error_info);
                }
                $(sms_mobile._pageId + " #talkCode").show();
                $(sms_mobile._pageId + " #talkCode").html(sms_mobile._style.talkCodeText);
            });
            //点击获取语音验证码
            appUtils.preBindEvent(sms_mobile._pageId + " #talkCode", "#getTalk", function () {
                param.send_type = "1";
                sms_mobile.sendTalkCode(param);
                if (sms_mobile._timer) {
                    window.clearInterval(sms_mobile._timer);
                    sms_mobile._timer = null; // 将_timer设为null，表示没有活动的定时器
                }
                sms_mobile._i = 120;
                sms_mobile._timer = setInterval(function () {
                    sms_mobile.f();
                }, 1000);
            });
        },

        //获取语音验证码
        sendTalkCode: function (param) {
            if (sms_mobile._timer) {
                window.clearInterval(sms_mobile._timer);
                sms_mobile._timer = null; // 将_timer设为null，表示没有活动的定时器
            }
            service.reqFun199001(param, function (data) {
                if (data.error_no == "0") {
                    var result = data.results;
                    var talk_mobile = result[0].orgphone;
                    var $dd = "晋金财富将致电您的手机语音告知验证码";
                    $(sms_mobile._pageId + " #talkCode").html($dd);
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        },
        //销毁短信验证码
        destroy: function () {
            if (sms_mobile._timer) {
                window.clearInterval(sms_mobile._timer);
                sms_mobile._timer = null; // 将_timer设为null，表示没有活动的定时器
            }
            var $yzm = $(sms_mobile._pageId + " #getYzm");
            $yzm.attr("data-state", "true");
            $yzm.css("background-color", sms_mobile._style.backgroundColor);
            $yzm.html(sms_mobile._style.btnText);
            sms_mobile._i = sms_mobile._send_sms_time;
            $(sms_mobile._pageId + " #talkCode").html("");
            $(sms_mobile._pageId + " #talkCode").hide();
            $(sms_mobile._pageId + ' #weihao').text("").hide();
            sms_mobile._pageId = "";
            sms_mobile._millisecond = "";
        },
        //清除倒计时，还原按钮状态
        clear: function (is_zfqy) {
            // 确保_timer存在再清除
            if (sms_mobile._timer) {
                window.clearInterval(sms_mobile._timer);
                sms_mobile._timer = null; // 将_timer设为null，表示没有活动的定时器
            }
            var $yzm = $(sms_mobile._pageId + " #getYzm");
            if(is_zfqy) $yzm = $(sms_mobile._pageId + ` ${is_zfqy}`);
            $yzm.attr("data-state", "true");
            $yzm.css("background-color", sms_mobile._style.backgroundColor);
            $yzm.html(sms_mobile._style.activeEndBtnText);
            sms_mobile._i = sms_mobile._send_sms_time;
            sms_mobile._millisecond = "";
        }
    }

    //暴露对外的接口
    module.exports = sms_mobile;
});
