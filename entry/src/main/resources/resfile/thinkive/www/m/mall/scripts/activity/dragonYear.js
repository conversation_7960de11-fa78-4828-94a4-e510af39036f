//活动 - 理财账单
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools.js"),
        validatorUtil = require("validatorUtil");
    var _pageId = "#activity_dragonYear ";
    var _pageCode = "activity/dragonYear"
    var layerUtils = require("layerUtils");
    var userInfo, activityInfo;
    var external = require("external");
    var global = gconfig.global;
    var ut = require("../common/userUtil.js");
    require("../../js/draw.js");
    require("../common/html2canvas.min.js");
    var pageTouchTimer = null;
    /* 变量  活动信息*/
    var activityInfo = "", reward_num = 1, state, shareflag, share_template, activity_id_url, group_id, banner_id;
    var activityAiInfo = "";
    var stopIndex;
    // 初始化转盘
    var luck = {
        index: 0, // 当前转动到哪个位置，起点位置
        count: 0, // 总共有多少个位置
        timer: 0, // setTimeout的ID，用clearTimeout清除
        speed: 20, // 初始转动速度
        times: 0, // 转动次数
        cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
        prize: -1, // 中奖位置
        init: function (id) {
            if ($("#" + id).find(".luck-unit").length > 0) {
                var $luck = $("#" + id);
                var $units = $luck.find(".luck-unit");
                this.obj = $luck;
                this.count = $units.length;
                $luck.find(".luck-unit-" + this.index).addClass("active");
            }
        },
        roll: function () {
            var index = this.index;
            var count = this.count;
            var luck = this.obj;
            $(luck).find(".luck-unit-" + index).removeClass("active");
            index += 1;
            if (index > count - 1) {
                index = 0;
            }
            $(luck).find(".luck-unit-" + index).addClass("active");
            this.index = index;
            return false;
        },
        stop: function (index) {
            this.prize = index;
            return false;
        }
    };
    var moneyObj = {
        // 顺时针reward_type-0-7
        "5000": {
            list_text: "5000积分", //领取记录文字
            reward_text: "5000积分", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [0],
            reward_type: "0", // 类型-1:随机积分 0:固定积分 2:实物
            btn_txt: "我知道了",
            prompt: "",
        },
        "physical3": {
            list_text: "华为手机", //领取记录文字
            reward_text: "华为手机", //中奖
            src: "./images/activity/p_ok.png",
            stopIndex: [1],
            reward_type: "2",// 实物
            btn_txt: "我知道了",
            prompt: "工作人员将在两个工作日内与您联系",
        },
        "auto": {
            list_text: "随机积分", //领取记录文字
            reward_text: "", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [2],
            reward_type: "1",// 随机积分
            btn_txt: "我知道了",
            prompt: "",
        },
        "physical8": {
            list_text: "小米随手杯",
            reward_text: "小米随手杯",
            src: "./images/activity/p_ok.png",
            stopIndex: [7],
            reward_type: "2",
            btn_txt: "我知道了",
            prompt: "工作人员将在两个工作日内与您联系",
        },
        "physical8": {
            list_text: "小米随手杯",
            reward_text: "小米随手杯",
            src: "./images/activity/p_ok.png",
            stopIndex: [3],
            reward_type: "2",//实物
            btn_txt: "我知道了",
            prompt: "工作人员将在两个工作日内与您联系",
        },
        "auto": {
            list_text: "随机积分", //领取记录文字
            reward_text: "", //中奖提示
            src: "./images/activity/p_ok.png",
            stopIndex: [6],
            reward_type: "1",// 随机积分
            btn_txt: "我知道了",
            prompt: "",
        },
        "physical9": {
            list_text: "小爱音箱",
            reward_text: "小爱音箱",
            src: "./images/activity/p_ok.png",
            stopIndex: [3],
            reward_type: "2",//实物
            btn_txt: "我知道了",
            prompt: "工作人员将在两个工作日内与您联系",
        },
        "10000": {
            list_text: "10000积分",
            reward_text: "10000积分",
            src: "./images/activity/p_ok.png",
            stopIndex: [4],
            reward_type: "0",
            btn_txt: "我知道了",
            prompt: "",
        }
    }
    var platform = gconfig.platform;
    platform = '1'
    /**
     * 初始化
     */
    function init() {
        activityInfo = appUtils.getPageParam() ? appUtils.getPageParam() : "";
        if (activityInfo && validatorUtil.isEmpty(activityInfo.activity_id)) {
            activityInfo = ''
            layerUtils.iAlert('活动ID未配置,请联系管理人员', -1, function () {
                pageBack();
            });
        }
        luck.init('luck');
        $(_pageId + " .activity_pop_layer").hide();
        if (activityInfo) {
            getActivityInfo();
        }
        getActivityAIInfo();
    };

    /**
     * 获取活动信息
     * */
    function getActivityInfo() {
        service.reqFun108015({
            cust_no: activityInfo.cust_no,
            activity_id: activityInfo.activity_id
        }, function (data) {
            var error_no = data.error_no;
            if (error_no == 0) {
                var result = data.results[0];
                state = result.activity_status;//1:进行中 2:未开始 3:已结束
                shareflag = result.shareflag; //0未分享 1:分享过
                reward_num = result.times;//抽奖次数
                share_template = result.share_template;//分享模板ID
                $(_pageId + " .b_con").html(result.introduce)
                $(_pageId + " .activity_tips").html(result.activity_tips)
                if (state == "2") {
                    $(_pageId + " #start .start_btn_name").html("即将开始");
                }
                if (state == "3") {
                    $(_pageId + " #start .start_btn_name").html("已结束");
                    $(_pageId + " #start .start_btn_name").css({ "width": '0.7rem' })
                    $(_pageId + " #start").css({ "background": '#808080' })
                }
                if (state == "1" && reward_num <= 0) {
                    $(_pageId + " #start .start_btn_name").html("再抽一次");
                    $(_pageId + " #start").removeClass("notclick");
                }
                // 获取分享模板
                let query_params = {
                    registered_mobile: ut.getUserInf().mobileWhole,
                    share_template: share_template
                }
                service.reqFun102012(query_params, async function (data) {
                    if (data.error_no == '0') {
                        var result = data.results[0];
                        if (data.results[0] && data.results[0].share_form == 2) {  // 若分享的是卡片，先渲染卡片
                            setShareImg(result);
                        }
                    } else {
                        layerUtils.iAlert(data.error_info);
                    }
                });
            }
        });
    }
    /**获取2024春节活动信息 */
    function getActivityAIInfo() {
        service.reqFun108049({}, function (data) {
            if (data.error_no == '0') {
                if (data.results && data.results.length) {
                    activityAiInfo = data.results[0];
                    $(_pageId + " .dialogue").html(activityAiInfo.activityTips);
                }
            } else if (data.error_no == "-2") { // 不启用或者不展示
                $(_pageId + " .dragon_years_AI").css("display", "none");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //渲染分享卡片图片
    function setShareImg(chooseData) {
        let bgImg = gconfig.global.oss_url + chooseData.img_url;
        service.reqFun102119({ img_url: bgImg }, function (data) {
            if (data.error_no == "0") {
                let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                $(_pageId + " .bgImg").attr("src", base64Str);
                if (chooseData.qr_code_type && chooseData.qr_code_type == '2') {
                    $(_pageId + " #qr_img").show();
                    $(_pageId + " #code").hide();
                    $(_pageId + "#qr_img").empty();
                    let qr_code_img = global.oss_url + chooseData.qr_code_img_url;
                    service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                        if (qrData.error_no == "0") {
                            var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                            $(_pageId + " #qr_img").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                        } else {
                            layerUtils.iAlert(d.error_info);
                        }
                    }, { "isShowWait": false })
                } else {
                    $(_pageId + " #qr_img").hide();
                    $(_pageId + " #code").show();
                    qrcode(chooseData);
                }

            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false })
    }
    //渲染短链二维码
    function qrcode(chooseData) {
        let mobile = ut.getUserInf().mobileWhole;
        mobile = common.desEncrypt("mobile", mobile);//加密
        // let long_url = "https://xhxts.sxfae.com/m/mall/index.html#!/drainage/userInvitationWx.html?mobile=" + mobile;
        let long_url = chooseData.qr_code_img_url + '?mobile=' + mobile;
        console.log(long_url);
        $(_pageId + "#code").empty();
        service.reqFun101073({ long_url: long_url }, function (res) {
            if (res.error_no == "0") {
                if (res.results != undefined && res.results.length > 0) {
                    var short_url = res.results[0].shortUrl;
                    require("../common/jquery.qrcode.min.js");
                    $(_pageId + " #code").qrcode({
                        render: "canvas", //设置渲染方式，有table和canvas
                        text: short_url, //扫描二维码后自动跳向该链接
                        width: 70, //二维码的宽度
                        height: 70, //二维码的高度
                        imgWidth: 20,
                        imgHeight: 20,
                        src: '../mall/images/icon_app.png'
                    });
                }
            } else {
                layerUtils.iAlert(res.error_info);
            }
        })
    }

    /*分享卡片*/
    function shareCard() {
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        $(_pageId + " .pop_layer").hide();
        var father = document.querySelector("#content");
        var _fatherHTML = document.querySelectorAll("#content .page");
        var cur = document.querySelector("#activity_dragonYear");
        father.innerHTML = "";
        father.appendChild(cur);
        let dom = document.querySelector(_pageId + " .shareImg");
        html2canvas(dom, {
            scale: 4
        }).then(canvas => {
            var base64 = canvas.toDataURL("image/png");
            var _base64 = base64.split(",")[1];
            father.innerHTML = "";
            for (let i = 0; i < _fatherHTML.length; i++) {
                father.appendChild(_fatherHTML[i]);
            }
            param = {
                "funcNo": "50231",
                "imgUrl": _base64,
                "shareType": "23",
                "imageShare": "1",
                "imageType":"base64"
            }
            require("external").callMessage(param);
        })
    }

    /**
     * 事件绑定
     */
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        //活动规则
        appUtils.bindEvent($(_pageId + " #newActivityRules"), function () {
            $(_pageId + " #activityRules").show();
        });
        //隐藏活动规则
        appUtils.bindEvent($(_pageId + " .ruleSure"), function (e) {
            $(_pageId + " #activityRules").hide();
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #pop_layer #share_WeChatFriend"), function () {
            tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', activityInfo.activity_id);
            appUtils.setSStorageInfo("activityInfo", activityInfo);
            if (!activityInfo || !share_template) {
                common.share("23", "1");
                return;
            }
            let results = {
                banner_id: activityInfo.banner_id,
                group_id: activityInfo.group_id,
                activity_id: activityInfo.activity_id,
            }
            let query_params = {
                registered_mobile: ut.getUserInf().mobileWhole,
                share_template: share_template
            }
            service.reqFun102012(query_params, async function (data) {
                if (data.error_no == '0') {
                    var result = data.results[0];
                    if (data.results[0] && data.results[0].share_form == 1) {  // 链接
                        results.title = result.title;
                        results.content = result.content;
                        results.img_url = result.img_url;
                        let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                        return tools.pageShare(results, '23', _pageId, shareUrlLast, null, activityInfo.activity_id);//活动页面分享
                    } else if (data.results[0] && data.results[0].share_form == 2) { // 图片 3--页面
                        shareCard();
                    }
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
            // common.share("23", share_template);
            pageTouchTimer = setTimeout(() => {
                // 分享加次数
                service.reqFun108016({ activity_id: activityInfo.activity_id }, function (data) {
                    if (data.error_no != "0") {//失败
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    layerUtils.iMsg(-1, "分享成功");
                    getActivityInfo();
                })
            }, 6000);
        });
        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #pop_layer #cancelShare"), function () {
            // $(_pageId + " #start").removeClass("notclick");
            $(_pageId + " #pop_layer").hide();
        });
        //抽奖记录
        appUtils.bindEvent($(_pageId + " #myRewards"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            $(_pageId + " .content_list").html("");
            var QueryParam = {
                "activity_id": activityInfo.activity_id.toString() + "," + activityAiInfo.activityId,
                "cust_no": activityInfo.cust_no,
            }
            service.reqFun108018(QueryParam, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var results = data.results[0].data;
                if (validatorUtil.isNotEmpty(data.results[0].data) && results.length > 0) {
                    var html = "";
                    for (var i = 0; i < results.length; i++) {
                        var reward_name = results[i].reward_name;//奖励内容
                        var crt_time = results[i].crt_time;//奖励时间
                        html += '<div class="item">\n' +
                            '       <p style="padding-right:0.05rem;width: 8%;text-align: right">' + (i + 1) + '.</p>' +
                            '       <p style="padding-right:0.05rem;width: 51%">' + crt_time.substr(0, 4) + '年' + crt_time.substr(4, 2) + '月' + crt_time.substr(6, 2) + '日' + '</p>\n' +
                            '       <p style="width: 41%">' + reward_name + '</p>' +
                            '    </div>'
                    }
                    $(_pageId + " .content_list").html(html);
                } else {
                    $(_pageId + " .content_list").html("<div style='text-align: center;line-height: 4;font-size: 0.14rem'>暂无领取记录</div>");
                }
                $(_pageId + " #rewardRecord").show();
            }, { "isShowWait": false });
        });
        //隐藏抽奖记录
        appUtils.bindEvent($(_pageId + " .recordSure"), function (e) {
            $(_pageId + " #rewardRecord").hide();
        });
        //抽奖
        appUtils.bindEvent($(_pageId + " #start"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if (state == "2") {
                reward_num = 0;
                layerUtils.iMsg(-1, "活动未开始！", 2);
                return;
            }
            if (state == "3") {
                reward_num = 0;
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            if (shareflag === "0" && reward_num <= 0) {
                layerUtils.iConfirm("分享活动给好友可获得一次抽奖机会", function () {
                    $(_pageId + " #pop_layer").show();
                }, function () {

                }, "去分享", "取消");
                return;
            }
            if (reward_num <= 0) {
                layerUtils.iMsg(-1, "今日抽奖次数已用完，请下次再来", 2);
                return;
            }
            if ($(_pageId + " #start").hasClass("notclick")) {
                return;
            }

            // 按钮禁用
            $(_pageId + " #start").addClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0_end.png'>");
            luck.speed = 100;
            var param = {
                "activity_id": activityInfo.activity_id,
                "cust_no": activityInfo.cust_no
            };
            service.reqFun108052(param, function (data) {
                // -2 未绑卡
                // -3 活动尚未开始或已结束
                // -4 次数已用完
                // -6 当前不符合参与条件
                // -999011 请求过于频繁，请稍候再试
                //奖池为空时默认谢谢参与
                if (data.error_no == "-2" || data.error_no == "-3" || data.error_no == "-4" || data.error_no == "-5" || data.error_no == "-6" || data.error_no == "-7" || data.error_no == "-999011") {
                    stopIndex = 0;
                    $(_pageId + " #start").removeClass("notclick");
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                if (data.error_no != "0") {
                    stopIndex = 5;
                    $(_pageId + " .index_info").html(moneyObj["0"].reward_text);
                    $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[0].src);
                    $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[0].btn_txt);
                    $(_pageId + " .index_info_tishi").html('');
                    roll();
                    reward_num--;
                    if (state == "1" && reward_num <= 0) {
                        $(_pageId + " #start .start_btn_name").html("再抽一次");
                        $(_pageId + " #start").removeClass("notclick");
                    }
                    return;
                }
                var results = data.results[0];
                var money = Number(results.reward_vol) + "";
                var moneyKey;
                //奖励类型reward_type 1:随机积分 0:固定积分 2:实物
                if (results.reward_type === "2") { //实物
                    // 实物类型 7-绒布对联、8-小米随手杯、9-小爱音箱
                    moneyKey = "physical" + results.reward_vol;
                } else if (results.reward_type === "0") { //固定积分
                    moneyKey = money;
                } else if (results.reward_type === "1") { //随机积分
                    moneyKey = "auto";
                } else if (results.reward_type === "6") { // 候补奖励 发的也是随机积分
                    moneyKey = "auto";
                }
                if (validatorUtil.isEmpty(moneyObj[moneyKey])) {
                    moneyKey = "auto"
                }
                $(_pageId + " .index_info").html(moneyObj[moneyKey].reward_text ? moneyObj[moneyKey].reward_text : money + "积分");
                $(_pageId + " .index_info_tishi").html(moneyObj[moneyKey].prompt ? moneyObj[moneyKey].prompt : '');
                stopIndex = moneyObj[moneyKey].stopIndex[Math.floor(Math.random() * moneyObj[moneyKey].stopIndex.length)]
                $(_pageId + " #rewardResult .activityRulesBonce .box").attr("src", moneyObj[moneyKey].src);
                $(_pageId + " #rewardResult .sureBtn span").html(moneyObj[moneyKey].btn_txt);
                roll();
                reward_num--;
                if (state == "1" && reward_num <= 0) {
                    $(_pageId + " #start .start_btn_name").html("再抽一次");
                    $(_pageId + " #start").removeClass("notclick");
                }
            }, { "isShowWait": false });
        });
        //隐藏中奖弹框
        appUtils.bindEvent($(_pageId + " .sureBtn"), function (e) {
            $(_pageId + " #rewardResult").hide();
        });
        //积分兑换
        appUtils.bindEvent($(_pageId + " #pointsFor"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            appUtils.setSStorageInfo("front_pageUrl", `${_pageCode}?activity_id=${activityInfo.activity_id}`);
            appUtils.pageInit("guide/advertisement", "vipBenefits/index", { 'luckflag': 'dargonYearDraw' })
        });
        // 邀好友抽奖
        appUtils.bindEvent($(_pageId + " #inviteBtn"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            $(_pageId + " #pop_layer").show();
        });
        // 点击进入AI页面
        appUtils.preBindEvent($(_pageId + " .ai_Select"), " li", function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if (!activityAiInfo || (activityAiInfo && activityAiInfo.state == "2")) {
                layerUtils.iMsg(-1, "活动未开始！", 2);
                return;
            }
            if (!activityAiInfo || (activityAiInfo && activityAiInfo.state == "3")) {
                layerUtils.iMsg(-1, "活动已结束！", 2);
                return;
            }
            var aiType = $(this).attr("data-type");
            appUtils.pageInit(_pageCode, "activity/dragonYearAIChat", { 'aiType': aiType, 'activityAiInfo': activityAiInfo, "activity_id": activityInfo.activity_id })
        })
    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + " .activity_pop_layer").hide();
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #pointsFor").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #rewardResult .sureBtn span").html("");
        $(_pageId + " #qr_img").hide();
        $(_pageId + " #code").show();
        clearTimeout(pageTouchTimer);
        appUtils.clearSStorage("front_pageUrl");
        service.destroy();
    };

    function roll() {
        luck.times += 1;
        luck.roll();
        if (luck.times > luck.cycle + 10 && luck.prize == luck.index) {
            clearTimeout(luck.timer);
            luck.prize = -1;
            luck.times = 0;
            if (stopIndex !== 4) {
                setTimeout(function () {
                    $(_pageId + " #rewardResult").show();
                }, 600);
            } else {
                setTimeout(function () {
                    $(_pageId + " #rewardResult").show();
                }, 600);
            }
            $(_pageId + " #start").removeClass("notclick");
            // $(_pageId + " #start").html("<img src='images/activity/p_0.png'>");
        } else {
            if (luck.times < luck.cycle) {
                luck.speed -= 10;
            } else if (luck.times == luck.cycle) {
                luck.prize = stopIndex;
            } else {
                if (luck.times > luck.cycle + 10 && ((luck.prize == 0 && luck.index == 7) || luck.prize == luck.index + 1)) {
                    luck.speed += 110;
                } else {
                    luck.speed += 20;
                }
            }
            if (luck.speed < 40) {
                luck.speed = 40;
            }
            luck.timer = setTimeout(roll, luck.speed);
        }
        return false;
    }


    var dargonYearModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = dargonYearModule;
});
