// 手机注册
//@版本: 2.0
define(['vIscroll'], function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#redPack_redPackage ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var external = require("external");
    var currentPage;
    var totalPages;
    var order_no;
    var page = 1;
    var _page_code = "redPack/redPackage";
    var ut = require("../common/userUtil");
    var serviceConstants = require("constants");
    var activitiesType;

    function init() {
        activitiesType = appUtils.getPageParam("activitiesType") || "";
        // queryNum(1, false);
    }

    //绑定事件
    function bindPageEvent() {

        //红包 体验金
        appUtils.bindEvent($(_pageId + "  .red_tab a"), function () {
            $(_pageId + "  .red_tab a").removeClass("active");
            $(this).addClass("active");
            var val = $(this).attr("id");
            if (val == 0) {
                queryNum(1, false);
            } else {
                queryUnNum(1, false);
            }

        });

        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });

        appUtils.preBindEvent($(_pageId + "  .red_list"), ".btn_get", function (e) {
            e.stopPropagation();
            e.preventDefault();
            var user_info = ut.getUserInf();
            var risk_count = user_info.risk_count;
            var risk_name = user_info.riskName;
            var cardno = user_info.bankAcct;
            if (!ut.hasBindCard(_page_code)) return;
            if (parseFloat(risk_count) == 0 || risk_name == "" || risk_name == null) {
                layerUtils.iConfirm("您还未做风险测评？",
                    function () {
                        appUtils.pageInit(_page_code, "account/qualifiedInvestor", {});
                        return;
                    },
                    function () {
                        return;
                    },
                    "进行测评", "取消");
                return;
            }
            //获取生效时间，同当前时间比较，不相同弹窗提示
            var effect_date = $(this).attr("effect_date");
            if (effect_date != null && effect_date != undefined && effect_date != "") {
                var effect_date_time = effect_date.replace(/\-/g, "\/");
                var current_date = getNowFormatDate().replace(/\-/g, "\/");
                if (effect_date_time > current_date) {
                    layerUtils.iAlert(effect_date + "起方可领取");
                    return;
                }
            }
            var that = this;
            //end
            order_no = $(that).attr("id");
            common.changeCardInter(function () {
                if (order_no) {
                    var params = {
                        "card_no": ut.getUserInf().bankAcct,
                        "order_no": order_no
                    };
                    service.getRedPackage(params, function (resultVo) {
                        if (resultVo.error_no == "0") {
                            layerUtils.iAlert("领取成功");
                            queryNum(1, false);
                        } else {
                            layerUtils.iAlert(resultVo.error_info);
                        }
                    });
                }
            });
        }, 'click');


    }

    //查询未使用，已失效查询 历史表
    function queryUnNum(list_page, isAppendFlag) {
        var state = $(_pageId + "  .red_tab .active").attr("id");


        var param = {
            "state": state, "is_page": "Y", page: list_page,
            numPerPage: "5", "is_order": "2", "reward_code": "0"
        };
        service.queryUnRedPackage(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;

            var str = "";

            if (error_no == "0") {
                if (data.results[0].data != undefined && data.results[0].data.length > 0) {
                    currentPage = data.results[0].currentPage;//当前页数
                    totalPages = data.results[0].totalPages;//总页数
                    if (currentPage <= totalPages) {
                        for (var i = 0; i < data.results[0].data.length; i++) {
                            if (data.results[0].data[i].state == "0") {
                                str += '<div class="envelop_inner"><div class="envelop_detail">';
                            } else if (data.results[0].data[i].state == "1") {
                                str += '<div class="envelop_inner already"><div class="envelop_detail">';
                            } else if (data.results[0].data[i].state == "2") {
                                str += '<div class="envelop_inner invalid"><div class="envelop_detail">';
                            }

                            str += '<h5>红包</h5>';
                            var money = Number(data.results[0].data[i].money);
                            if (money % 1 != 0) {
                                money = money.toFixed(2);
                            }
                            str += '<strong>' + money + '元</strong>';
//								if(data.results[0].data[i].reward_type=="1"){
//									str+='<strong>'+(data.results[0].data[i].money).toFixed(2)+'元</strong>';
//								}
//								else{
//									str+='<strong>'+data.results[0].data[i].money+'元</strong>';
//								}

                            if (data.results[0].data[i].expire_date && data.results[0].data[i].expire_date != "") {
                                str += '<p>有效期至<span>' + data.results[0].data[i].expire_date + '</span></p>';
                            }

                            str += '</div>';

                            if (data.results[0].data[i].state == "0") {
                                str += '<a href="javascript:void(0);" class="btn_get" id="' + data.results[0].data[i].order_no + '" effect_date="' + data.results[0].data[i].effect_date + '" >';
                                str += '领取';
                            } else if (data.results[0].data[i].state == "1") {
                                str += '<a href="javascript:void(0);" class="btn_get">';
                                str += '已领取';
                            } else if (data.results[0].data[i].state == "2") {
                                str += '<a href="javascript:void(0);" class="btn_get">';
                                str += '已失效';
                            }
                            str += '</a>';
                            str += '</div>';

                        }
                    } else {
                        str += "<div class='my_finance'>暂无数据...</div>";
                    }
                    if (isAppendFlag) {
                        $(_pageId + " .red_list").append(str);
                        $(_pageId + " .visc_pullUpIcon").hide();
                        $(_pageId + " .visc_pullUpDiv").hide();
                    } else {
                        $(_pageId + " .red_list").html(str);
                    }
                    pageScrollInit();
                } else {
                    $(_pageId + " .red_list").html(str);
                }

            } else if (data.error_no == "-1") {
                layerUtils.iAlert("网络繁忙，请稍后重试");
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });


    }

    function queryNum(list_page, isAppendFlag) {
        var state = $(_pageId + "  .red_tab .active").attr("id");


        var param = {
            "state": state, "is_page": "Y", page: list_page,
            numPerPage: "5", "is_order": "2", "reward_code": "0"
        };
        service.queryRedPackage(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;

            var str = "";

            if (error_no == "0") {
                if (data.results[0].data != undefined && data.results[0].data.length > 0) {
                    currentPage = data.results[0].currentPage;//当前页数
                    totalPages = data.results[0].totalPages;//总页数
                    if (currentPage <= totalPages) {
                        for (var i = 0; i < data.results[0].data.length; i++) {
                            if (data.results[0].data[i].state == "0") {
                                str += '<div class="envelop_inner"><div class="envelop_detail">';
                            } else if (data.results[0].data[i].state == "1") {
                                str += '<div class="envelop_inner already"><div class="envelop_detail">';
                            } else if (data.results[0].data[i].state == "2") {
                                str += '<div class="envelop_inner invalid"><div class="envelop_detail">';
                            }

                            str += '<h5>红包</h5>';
                            var money = Number(data.results[0].data[i].money);
                            if (money % 1 != 0) {
                                money = money.toFixed(2);
                            }
                            str += '<strong>' + money + '元</strong>';
//								if(data.results[0].data[i].reward_type=="1"){
//									str+='<strong>'+(data.results[0].data[i].money).toFixed(2)+'元</strong>';
//								}
//								else{
//									str+='<strong>'+data.results[0].data[i].money+'元</strong>';
//								}

                            if (data.results[0].data[i].expire_date && data.results[0].data[i].expire_date != "") {
                                str += '<p>有效期至<span>' + data.results[0].data[i].expire_date + '</span></p>';
                            }

                            str += '</div>';

                            if (data.results[0].data[i].state == "0") {
                                str += '<a href="javascript:void(0);" class="btn_get" id="' + data.results[0].data[i].order_no + '" effect_date="' + data.results[0].data[i].effect_date + '" >';
                                str += '领取';
                            } else if (data.results[0].data[i].state == "1") {
                                str += '<a href="javascript:void(0);" class="btn_get">';
                                str += '已领取';
                            } else if (data.results[0].data[i].state == "2") {
                                str += '<a href="javascript:void(0);" class="btn_get">';
                                str += '已失效';
                            }
                            str += '</a>';
                            str += '</div>';

                        }
                    } else {
                        str += "<div class='my_finance'>暂无数据...</div>";
                    }
                    if (isAppendFlag) {
                        $(_pageId + " .red_list").append(str);
                        $(_pageId + " .visc_pullUpIcon").hide();
                        $(_pageId + " .visc_pullUpDiv").hide();
                    } else {
                        $(_pageId + " .red_list").html(str);
                    }
                    pageScrollInit();
                } else {
                    $(_pageId + " .red_list").html(str);
                }

            } else if (data.error_no == "-1") {
                layerUtils.iAlert("网络繁忙，请稍后重试");
            } else {
                layerUtils.iAlert(data.error_info);
            }

        });
    }

    /**
     * 获取当前日期
     */
    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }


    /**上下滑动刷新事件**/
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    page = 1;
                    var val = $(_pageId + " .active").attr("id");
                    if (val == 0) {
                        queryNum(page, false);
                    } else {
                        queryUnNum(page, false);
                    }

                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        page += 1;
                        var val = $(_pageId + " .active").attr("id");
                        if (val == 0) {
                            queryNum(page, false);
                        } else {
                            queryUnNum(page, false);
                        }

                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }

    }

    function pageBack() {
        appUtils.pageBack();
    }

    function destroy() {
        $(_pageId + "  .red_tab a").removeClass("active");
        $(_pageId + "  .red_tab a").eq(0).addClass("active");
    }

    //查询是否是合格投资人
    function qualifiedInvestor(callback) {
        var custNo = ut.getUserInf().bankAcct;
        var param = {"cust_no": custNo};
        var funCallBack = function (resultVo) {
            var data = resultVo;
            if (resultVo.error_no != 0) {
                if (callback) callback();
            } else {
                changecard(function () {
                    if (order_no) {
                        var params = {
                            "cust_no": appUtils.getSStorageInfo("custNo"),
                            "card_no": appUtils.getSStorageInfo("cardNo"),
                            "order_no": order_no
                        };
                        service.getRedPackage(params, function (resultVo) {
                            if (resultVo.error_no == "0") {
                                layerUtils.iAlert("领取成功");
                                queryNum(1, false);
                            } else {
                                layerUtils.iAlert(resultVo.error_info);
                            }
                        });
                    }
                });
            }
        };
        service.reqFun901411(param, funCallBack);
    }


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
