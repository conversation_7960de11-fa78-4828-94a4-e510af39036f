<div class="page" id="bank_openAccount" data-pageTitle="电子账户开户" data-refresh="true" data-pageLevel="0">

    <section class="main fixed" data-page="home" id="bankApen">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">电子账户开户</h1>
            </div>
        </header>
        <article class="bg_blue" style="padding-bottom: 0">
            <!--  <div class="bank_box">
                <div class="bank_inner flow in">
                    <h4>
                        <em><img src="" width="100" alt="" class="bank_electron_icon"></em>
                        <span class="bank_electron_name"></span>
                    </h4>
                    <p>应银行要求，需先开通银行电子账户，通过电子账户与银行直接进行交易，资金安全有保障</p>
                </div>
            </div>  -->
            <div class="bank_form">
                <div class="input_box">
                    <div class="ui field text">
                        <label class="ui label">姓名</label>
                        <input id="cardPerson" type="text" class="ui input" disabled style="color: #666666;"/>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">身份证号</label><input id="idCard" maxlength="19" type="tel"
                                                                   class="ui input" disabled style="color: #666666;"/>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">证件照片</label>
                        <div class="ui dropdown" style="margin-left:0.1rem;">已上传</div>
                        <div class="retryUpload ft blue" style="margin-right: 0.2rem;line-height: 0.44rem;">重新上传</div>
                    </div>
                    <div class="ui field text">
                        <div class="pop_view" id="pop_view" style="visibility:hidden;">
                            <p id="big_show_bank"></p>
                        </div>
                        <label class="ui label">银行卡号</label><input id="bankCard" maxlength="19" type="tel"
                                                                   class="ui input" style="color: #666666;" disabled/>
                        <div class="changeBankCard">修改</div>
                    </div>
                    <div class="ui field text">
                        <label class="ui label">银行名称</label>
                        <div class="ui dropdown" id="bankname" style="margin-left:0.1rem;color: #666666;"></div>
                    </div>
                    <div class="place" style="padding-left: 1rem;height: auto">
                        <span class="limit">银行卡单笔限额：
                            <span id="oneMoney" style="color: #000;font-weight: bold;"></span> 单日限额：
                            <span id="drxe" style="color: #000;font-weight: bold;"></span>
                        </span>
                        <span class="bankInfo" style="color: #319ef2">支持的银行卡</span>
                    </div>
                </div>

                <div class="input_box">
                    <div class="ui field text">
                        <label class="ui label">预留手机号</label><input id="yhmPhone" maxlength="11" type="tel"
                                                                    placeholder="" class="ui input" disabled
                                                                    style="color: #666666;padding-left: 0.1rem"/>
                        <div class="changeBankMobile ft blue">修改</div>
                    </div>
                    <!-- <div class="ui field text">
                        <label class="ui label">人脸识别</label>
                        <div class="ui dropdown faceRecognitionResult" style="margin-left:0.1rem;float: left;display: none">已识别</div>

                        <div class="ui dropdown blue goFaceRecognition text-right" style="margin-left:0.1rem;float: right;padding-right: 0.2rem">去识别</div>
                    </div> -->
                    <div class="grid_03 grid_02 grid">
                        <div class="ui field text rounded input_box2" id="yzmBox">
                            <label class="short_label2 text-right" style="width:0.9rem;padding-right:0.1rem;">验证码</label>
                            <input custom_keybord="0" id="verificationCode" type="tel" maxlength="6" class="ui input code_input"
                                   placeholder=""/>
                            <a id="getYzm" data-state="true">获取验证码</a>
                        </div>
                    </div>
                    <div class="finance_det recharge_det">
                        <dl class="bank_limit">
                            <dt></dt>
                            <dd id="weihao" style="display:none"></dd>
                            <dd>
                            </dd>
                        </dl>
                    </div>
                    <!-- 语音验证码 -->
                    <div class="finance_det recharge_det">
                        <dl class="bank_limit">
                            <dt></dt>
                            <dd id="talkCode" style="display: block;">晋金财富将致电您的手机语音告知验证码
                            </dd>
                            <dd>
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="input_box">
                    <div class="ui field text right_icon">
                        <label class="ui label">职业</label><input id="occp" placeholder="" class="ui input" readonly
                                                                 style="color: #666666;padding-right: 0.3rem"/>
                    </div>
                </div>
                <div class="rule_check" style="padding: 0 0.1rem">
                    <span id="xuanzeqi" style="display: inline-block"><i></i>我已仔细阅读并同意签署</span><span class="deal_box"></span>，并承诺本人是中国税收居民
                    <!-- <a href="javascript:void(0);">《业务说明书》</a><a href="javascript:void(0);">《定向委托管理协议》</a><a href="javascript:void(0);">《资金结算协议》</a> -->
                </div>
                <div class="btn">
                    <a href="javascript:void(0);" class="ui button block rounded" id="next"
                       style="background: rgb(209, 212, 213);">立即开通电子账号</a>
                </div>
                <!-- 协议相关弹窗 -->
                <div class="agreement_layer display_none">
                    <div class="agreement_popup in">
                        <div class="agreement_popup_header">
                            <div class="new_close_btn"></div>
                            <h3>相关协议</h3>
                        </div>
                        <ul class="agreement_list flex vertical_line"></ul>
                    </div>
                </div>
            </div>
        </article>
    </section>


</div>
