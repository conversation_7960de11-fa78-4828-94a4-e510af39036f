// 晋金配置
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#inclusive_jjThirtyDetailConfiguration",
        _pageCode = "inclusive/jjThirtyDetailConfiguration";
    var tools = require("../common/tools");
    var productInfo;
    var ut = require("../common/userUtil");
    require('../common/echarts.min');
    require('../common/echartsData.js')
    var common = require("common");
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        //获取 资产配置
        reqFun102009();
        //获取 重仓持债
        reqFun102010();
        reqFun102134();
        tools.initFundBtn(productInfo, _pageId);
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //获取 资产配置
    function reqFun102009() {
        var param = {
            fund_code: productInfo.fund_code,
            type: "1",
        }
        service.reqFun102009(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                //设置资产配置html
                if (results && results.length) {
                    //截止日期
                    var deadline = tools.ftime(results[0].end_time);
                    $(_pageId + " #configuration .deadline").html(deadline);
                    let arr = [];
                    let total_holdingv_val = (results[0].net_asset / 100000000).toFixed(2);
                    results.sort(function (a, b) { return parseFloat(b.pctof_total_asset) - parseFloat(a.pctof_total_asset) })
                    results.forEach(item => {
                        arr.push({
                            value: item.pctof_total_asset ? item.pctof_total_asset : 0,
                            name: item.secu_category_name,
                        })
                    })
                    let option = prodDetailAssetAllocationOption;
                    option.series[0].data = arr;
                    option.title.text = total_holdingv_val + "亿元"
                    option.legend.formatter = (name) => {
                        var target;
                        for (var i = 0, l = arr.length; i < l; i++) {
                            if (arr[i].name == name) {
                                target = arr[i].value;
                            }
                        }
                        return name + '：' + target + '%';
                    }

                    let dom = document.getElementById(`chart-container-config`);
                    let myChart = echarts.init(dom, null, {
                        renderer: 'canvas',
                        useDirtyRect: false
                    });
                    if (option && typeof option === 'object') {
                        myChart.setOption(option);
                    }
                }
                // setConfigurationHtml(results)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 获取行业分布
    function reqFun102134() {
        var param = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102134(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                setConfigurationHtml(results)
                
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //设置资产配置html
    function setConfigurationHtml(data) {
        if (data.length == 0) {
            $(_pageId + " #indus .list").html('<div class="nodata">暂无数据</div>');
            return;
        }
        var html = "";
        for (var i = 0; i < data.length; i++) {
            //空数据处理
            data[i] = tools.FormatNull(data[i]);
            var indus_value = data[i].indus_value;
            if (indus_value != "--") {
                indus_value = (indus_value / 10000).toFixed(2);
            }

            var indus_tonav = data[i].indus_tonav;
            if (indus_tonav != "--") {
                indus_tonav = indus_tonav + "%";
            }
            html += '<li class="item">' +
                '<span>' + data[i].indus_name + '</span>' +
                '<span class="text-center">' + indus_value + '</span>' +
                '<span class="text-right">' + indus_tonav + '</span>' +
                '</li>';
        }
        if (data.length == 0) {
            $(_pageId + " #indus .list").html('<div class="nodata">暂无数据</div>');
        } else {
            $(_pageId + " #indus .list").html(html);
        }
    }

    //获取 重仓持债
    function reqFun102010() {
        var param = {
            fund_code: productInfo.fund_code,
            type: "3",
        }
        service.reqFun102010(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                // console.log(results[0]);
                //设置 重仓持债html
                setBondholdersHTML(results[0].combineList);
                setHeavyListHTML(results[0].heavyList);
                setFundListHTML(results[0].fundList);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //设置 重仓持股
    function setHeavyListHTML(data) {
        if (data.length == 0) {
            $(_pageId + " #heavyList").css({"display": "none"});
            return;
        }
        var html = "";
        for (var i = 0; i < data.length; i++) {
            if (!data[i].stock_sname) {
                data[i].stock_sname = "--";
            }
            if (parseFloat(data[i].alteration) > 0) {
                data[i].alteration = "↑" + Math.abs(data[i].alteration) + "%"
            } else if (parseFloat(data[i].alteration) < 0) {
                data[i].alteration = "↓" + Math.abs(data[i].alteration) + "%"
            } 
            if (!data[i].mkv_ratio_nav) {
                data[i].mkv_ratio_nav = "--";
            } else {
                data[i].mkv_ratio_nav = data[i].mkv_ratio_nav + "%";
            }
            html += '<li class="item">' +
                '<span>' + data[i].stock_sname + '</span>' +
                '<span>' + data[i].mkv_ratio_nav + '</span>' +
                '<span>' + data[i].alteration + '</span>' +
                '</li>';
        }
        if (data.length == 0) {
            $(_pageId + " #heavyList .list").html('<div class="nodata">暂无数据</div>');
        } else {
            $(_pageId + " #heavyList .list").html(html);
        }
    }

    //设置 重仓持债html
    function setBondholdersHTML(data) {
        if (data.length == 0) {
            $(_pageId + " #bondholders").css({"display": "none"});
            return;
        }
        var deadline = "";
        if (data[0].report_date) {
            deadline = tools.ftime(data[0].report_date);
        } else {
            deadline = "--";
        }
        //截止日期
        // $(_pageId + " #bondholders .deadline").html(deadline);
        var html = "";
        for (var i = 0; i < data.length; i++) {
            if (!data[i].secushortnm) {
                data[i].secushortnm = "--";
            }
            if (!data[i].market_value) {
                data[i].market_value = "--";
            }
            if (!data[i].mkv_ratio_nav) {
                data[i].mkv_ratio_nav = "--";
            } else {
                data[i].mkv_ratio_nav = data[i].mkv_ratio_nav + "%";
            }
            html += '<li class="item">' +
                '<span class="m_width_50">' + data[i].secushortnm + '</span>' +
                '<span class="m_width_50 m_text_right">' + data[i].mkv_ratio_nav + '</span>' +
                '</li>';
        }
        $(_pageId + " #bondholders .list").html(html);
        // if (data.length == 0) {
        //     $(_pageId + " #bondholders .list").html('<div class="nodata">暂无数据</div>');
        // } else {
        //     $(_pageId + " #bondholders .list").html(html);
        // }
    }

    //设置 重仓持债html
    function setFundListHTML(data) {
        if (data.length == 0) {
            $(_pageId + " #fundList").css({"display": "none"});
            return;
        }
        var deadline = "";
        if (data[0].report_date) {
            deadline = tools.ftime(data[0].report_date);
        } else {
            deadline = "--";
        }
        //截止日期
        $(_pageId + " #fundList .deadline").html(deadline);
        var html = "";
        for (var i = 0; i < data.length; i++) {
            if (!data[i].fund_sname) {
                data[i].fund_sname = "--";
            }
            if (!data[i].market_value) {
                data[i].market_value = "--";
            }
            if (!data[i].mkv_ratio_nav) {
                data[i].mkv_ratio_nav = "--";
            } else {
                data[i].mkv_ratio_nav = data[i].mkv_ratio_nav + "%";
            }
            html += '<li class="item">' +
                '<span class="m_width_50">' + data[i].fund_sname + '</span>' +
                '<span class="m_width_50 m_text_right">' + data[i].mkv_ratio_nav + '</span>' +
                '</li>';
        }
        if (data.length == 0) {
            $(_pageId + " #fundList .list").html('<div class="nodata">暂无数据</div>');
        } else {
            $(_pageId + " #fundList .list").html(html);
        }
    }

    function dealHtml(data) {
        var html = "";
        data.forEach(item => {
            if (!item.secushortnm) {
                item.secushortnm = "--";
            }
            if (!item.market_value) {
                item.market_value = "--";
            }
            if (!item.mkv_ratio_nav) {
                item.mkv_ratio_nav = "--";
            } else {
                item.mkv_ratio_nav = item.mkv_ratio_nav + "%";
            }
            html += '<li class="item">' +
                '<span>' + item.secushortnm + '</span>' +
                '<span>' + item.market_value + '</span>' +
                '<span>' + item.mkv_ratio_nav + '</span>' +
                '</li>';
        })
        return html;

    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #indus .list").html('');
        $(_pageId + " #bondholders .list").html('');
        $(_pageId + " #heavyList .list").html('');
        $(_pageId + " #threshold_amount").html("--");
        $(_pageId + " #fundList .list").html("");
        $(_pageId + " #buy_rate_box").html("免申购费");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetailConfiguration = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetailConfiguration;
});
