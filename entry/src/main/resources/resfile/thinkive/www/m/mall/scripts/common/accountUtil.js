/**
 * 模块名：账户类相关
 * 作者： 蒋松
 * 时间：2015年1月21日15:32:40
 * 简述：获取微信用户相关信息及账户绑定检测操作
 */
define(function(require,exports,module){
	var appUtils = require("appUtils"),
		layerUtils = require("layerUtils"),
		service = require("service_account"),
		service2 = require("mobileService"),
		enDecryptUtils= require("endecryptUtils"),
		weixinpk  = "",
		openid = "";
	/**
	 * 获取微信用户信息
	 * @param accountNo	String	资金账号节点
	 * @param weixin_no	String	微信号节点
	 * @param weixin_name	String	微信昵称节点
	 */
	var getWXUserInfo = function(weixin_noObj,usernameObj,accountNoObj){
		var param = {
				"weixinpk":appUtils.getSStorageInfo("weixinpk"),
				"openid":appUtils.getSStorageInfo("openid")
		};
		var callBack = function(resultVo){
			if(resultVo.error_no == 0){
				var results = resultVo["results"];
				if(weixin_noObj != null){
					weixin_noObj.html(results[0]["weixin_no"]);
				}
				if(usernameObj != null){
					usernameObj.html(results[0]["weixin_name"]);
				}
				if(accountNoObj != null){
					accountNoObj.html(results[0]["accountNo"]);
				}
			}else{
				layerUtils.iMsg(-1,resultVo.error_info);
				return false;
			}
		};
		//根据微信号同步资金账号以及微信号相关信息(1002011)
		service.WXUserInfo(param,callBack);
	};
	/**
	 * 绑定检测
	 * @param show 是否显示引导跳转弹出层
	 * @param bindCallBack  确认已绑定后回调
	 */
	function hasBindAccount(bindCallBack, show){
		var utils = require("utils");
		if(appUtils.getSStorageInfo("bindFlag")){
			bindCallBack();
			return true;
		} else {
			checkBind(bindCallBack, show);
		}
	};
	/**
	 * 绑定检测调服务层
	 * 具体视项目而定
 	 * @param show 是否显示引导跳转弹出层
	 * @param bindCallBack  确认已绑定后回调
	 */
	function checkBind(bindCallBack, show){
		var param = {
				"weixinpk":appUtils.getSStorageInfo("weixinpk"),
				"openid":appUtils.getSStorageInfo("openid"),
				"operation_type":"1"
		};
		var callBack = function(resultVo){
			if(resultVo.error_no == 0){
				
				var results = resultVo["results"];
				appUtils.setSStorageInfo("nickname",results[0]["nickname"]);
				if(results[0]["bind_status"] == "1"){
					//已绑定 如果页面有解除绑定的功能请注意清掉这个值
					appUtils.setSStorageInfo("bindFlag",true);
					appUtils.setSStorageInfo("fund_account",results[0]["zj_account"]);
				}else{
					if(show){
//						utils.myConfirm("浏览该页需绑定资金账户，是否绑定？","马上去绑定","我要离开",function(){
//							appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"),"account/accountBind");
//						},function(){WeixinJSBridge.call("closeWindow");},5);
					}
				}
			}else{
				layerUtils.iMsg(-1,resultVo.error_info);
				return false;
			}
		};
		//账户绑定检测(1002204)
		service.checkAndRebindAcc(param,callBack);
	}
	
	
	function checkIfBind(callBack1,weixinpk,openid)
	{
		var param = {
				"weixinpk":weixinpk,
				"openid":openid,
				"operation_type":"1"
		};
		var callBack=function(resultVo){
			if(resultVo.error_no==0)
			{
				var results = resultVo["results"];
				appUtils.setSStorageInfo("nickname",results[0]["nickname"]);
				if(results[0]["bind_status"]=="1")//已经绑定
				{
					appUtils.setSStorageInfo("bindFlag",true);
					appUtils.setSStorageInfo("fund_account",results[0]["zj_account"]);
				//	return true;
				}
				else
				{
					appUtils.setSStorageInfo("bindFlag",false);
				//	return true;
				}
				callBack1();
			}
			else
			{
				layerUtils.iMsg(-1,resultVo.error_info);
				return false;
			}
		};
		service.checkAndRebindAcc(param,callBack);
	}
	/**
	 * 取加密串
	 * @param password 密碼
	 * @param func 回調
	 */
	function getKey(password, func){
		/*回调函数*/
		var callBack = function(resultVo){
			if(resultVo.error_no == 0){
				var results = resultVo["results"];
				if(results.length>0&&results!=null)
				{
					var result = results[0];
					var modulus = result.modulus;
					var publicExponent = result.publicExponent;
					var pass=enDecryptUtils.rsaEncrypt(modulus,publicExponent,password);
					func(pass);
				}
			}else{
				layerUtils.iMsg(-1,resultVo.error_info);
				return false;
			}
		};
		// 获取密码加密串(1000000)
		service2.getRSAKey({},callBack);
	}
	
	var account = {
			"getWXUserInfo":getWXUserInfo,
			"hasBindAccount":hasBindAccount,
			"checkIfBind":checkIfBind,
			"getKey": getKey
	};
	module.exports = account;
});