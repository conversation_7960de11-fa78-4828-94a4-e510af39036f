define(function (require, exports, module) {

    var appUtils = require("appUtils");
    var gconfig = require("gconfig");
    var layerUtils = require("layerUtils");
    var service = require("mobileService"); //业务层接口，请求数据
    var external = require("external");
    var des = require("des");//js加密声明
    var endecryptUtils = require("endecryptUtils");
    var platform = require("gconfig").platform;
    var ssoUtils = require("ssoUtils");
    var serviceConstants = require("constants"),
        ut = require("./userUtil"),
        fingerprint = require('./fingerprint.js'),
        // tools = require('./tools.js')
        validatorUtil = require("validatorUtil"),
        global = gconfig.global;
    var modulus = "";
    var publicExponent = "";
    // var
    // var homePageIndex = require("../scripts/login/userIndexs"); //引入首页js，触发init事件
    /**
     * 将科学计数法的数字转为字符串
     * 说明：运算精度丢失方法中处理数字的时候，如果出现科学计数法，就会导致结果出错  
     * 4.496794759834739e-9  ==> 0.000000004496794759834739
     * 4.496794759834739e+9  ==> 4496794759.834739
     * @param  num
     */
    var toNonExponential = (num)=> {
        if(num == null) {
            return num;
        }
        if(typeof num == "number") {
            var m = num.toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/);
            return num.toFixed(Math.max(0, (m[1] || '').length - m[2]));
        }else {
            return num;
        }
    }

    /**
     * 乘法 - js运算精度丢失问题
     * @param arg1  数1
     * @param arg2  数2
     * 0.0023 * 100 ==> 0.22999999999999998
     * {{ 0.0023 | multiply(100) }} ==> 0.23
     */
    var floatMultiply = (arg1, arg2) => {
        arg1 = Number(arg1);
        arg2 = Number(arg2);
        if ((!arg1 && arg1!==0) || (!arg2 && arg2!==0)) {
            return null;
        }
        arg1 = toNonExponential(arg1);
        arg2 = toNonExponential(arg2);
        var n1, n2;
        var r1, r2; // 小数位数
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        n1 = Number(arg1.toString().replace(".", ""));
        n2 = Number(arg2.toString().replace(".", ""));
        return n1 * n2 / Math.pow(10, r1 + r2);
    }

    /**
     * 除法 - js运算精度丢失问题
     * @param arg1  数1
     * @param arg2  数2
     * 0.0023 / 0.00001 ==> 229.99999999999997
     * {{ 0.0023 | divide(0.00001) }} ==> 230
     */
    var floatDivide = (arg1, arg2) => {
        arg1 = Number(arg1);
        arg2 = Number(arg2);
        if (!arg2) {
            return null;
        }
        if (!arg1 && arg1!==0) {
            return null;
        }else if(arg1===0) {
            return 0;
        }
        arg1 = toNonExponential(arg1);
        arg2 = toNonExponential(arg2);
        var n1, n2;
        var r1, r2; // 小数位数
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        n1 = Number(arg1.toString().replace(".", ""));
        n2 = Number(arg2.toString().replace(".", ""));
        return floatMultiply((n1 / n2), Math.pow(10, r2 - r1));
        // return (n1 / n2) * Math.pow(10, r2 - r1);   // 直接乘法还是会出现精度问题
    }

    /**
     * 加法 - js运算精度丢失问题
     * @param arg1  数1
     * @param arg2  数2
     * 0.0023 + 0.00000000000001 ==> 0.0023000000000099998
     * {{ 0.0023 | plus(0.00000000000001) }} ==> 0.00230000000001
     */
    var floatAdd = (arg1, arg2) => {
        arg1 = Number(arg1) || 0;
        arg2 = Number(arg2) || 0;
        arg1 = toNonExponential(arg1);
        arg2 = toNonExponential(arg2);
        var r1, r2, m;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2));
        return (floatMultiply(arg1, m) + floatMultiply(arg2, m)) / m;
    }
    
    /**
     * 减法 - js运算精度丢失问题
     * @param arg1  数1
     * @param arg2  数2
     * 0.0023 - 0.00000011  ==>  0.0022998899999999997
     * {{ 0.0023 | minus( 0.00000011 ) }}  ==>  0.00229989
     */
    var floatSub = (arg1, arg2) => {
        arg1 = Number(arg1) || 0;
        arg2 = Number(arg2) || 0;
        arg1 = toNonExponential(arg1);
        arg2 = toNonExponential(arg2);
        var r1, r2, m, n;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2));
        // 动态控制精度长度
        n = (r1 >= r2) ? r1 : r2;
        return ((floatMultiply(arg1, m) - floatMultiply(arg2, m)) / m).toFixed(n);
    }


    /**
     * 取余 - js运算精度丢失问题
     * @param arg1  数1
     * @param arg2  数2
     * 12.24 % 12  ==> 0.2400000000000002
     * {{ 12.24 | mod( -12 ) }}  ==>  0.24
     */
    var floatMod = (arg1, arg2) => {
        arg1 = Number(arg1);
        arg2 = Number(arg2);
        if (!arg2) {
            return null;
        }
        if (!arg1 && arg1!==0) {
            return null;
        }else if(arg1===0) {
            return 0;
        }
        let intNum = arg1 / arg2;
        intNum = intNum < 0 ? Math.ceil( arg1 / arg2 ) : Math.floor( arg1 / arg2 );  // -1.02 取整为 -1; 1.02取整为1
        let intVal = floatMultiply(intNum, arg2);
        return floatSub( arg1,intVal );  12.4
    }
    


    /*
    邮箱后缀
    * */
    var emailType = [
        {id: "0", value: "@qq.com"},
        {id: "1", value: "@163.com"},
        {id: "2", value: "@139.com"},
        {id: "3", value: "@126.com"},
        {id: "4", value: "@sina.com"},
        {id: "5", value: "@sohu.com"},
        {id: "6", value: "其他"}
    ]
    /**
     * 推荐指数转换 "1"-->"★"、"2"-->"★★"..."5"-->"★★★★★"、""-->"--"
     * @param recommendLevel 推荐指数
     * @returns {String} 几颗”★“
     */
    function convRecommendLevel(recommendLevel) {
        var starStr = "";
        if (recommendLevel) {
            starStr = "<span class=\"star\"><em class=\"level level" + recommendLevel + "\"></em></span>";
        } else {
            starStr = "--";
        }
        return starStr;
    }


    //格式化日期20180817
    function dateStyle(date) {
        var length = date.length;
        var year = date.substring(0, 4);
        var month = Number(date.substring(4, 6));
        var day = Number(date.substring(6, 8));
        var resultDate = year + "年" + month + "月" + day + "日";
        if (length > 8 && length <= 14) {
            var hour = date.substring(8, 10);
            var minute = date.substring(10, 12);
            var seconds = date.substring(12, 14);
            resultDate = resultDate + hour + ":" + minute;
        }
        return resultDate;
    }

    function dateStyle2(date) {
        var length = date.length;
        var year = date.substring(0, 4);
        var month = Number(date.substring(4, 6));
        var day = Number(date.substring(6, 8));
        var resultDate = month + "月" + day + "日";
        if (length > 8 && length <= 14) {
            var hour = date.substring(8, 10);
            var minute = date.substring(10, 12);
            var seconds = date.substring(12, 14);
            resultDate = resultDate + hour + ":" + minute;
        }
        return resultDate;
    }

    /**
     * 学历字典转换
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function convEducation(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "1":
                dictName = "博士";
                break;
            case "2":
                dictName = "硕士";
                break;
            case "3":
                dictName = "学士";
                break;
            case "4":
                dictName = "大专";
                break;
            case "5":
                dictName = "高中及以下";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 职业字典转换
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function convOccupation(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "0":
                dictName = "党政机关";
                break;
            case "1":
                dictName = "企事业单位";
                break;
            case "2":
                dictName = "农民";
                break;
            case "3":
                dictName = "个体工商户";
                break;
            case "4":
                dictName = "学生";
                break;
            case "5":
                dictName = "证券从业人员";
                break;
            case "6":
                dictName = "军人";
                break;
            case "7":
                dictName = "无业";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 风险等级字典转换
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function convRiskLevel(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "0":
                dictName = "安逸型";
                break;
            case "1":
                dictName = "保守型";
                break;
            case "2":
                dictName = "谨慎型";
                break;
            case "3":
                dictName = "稳健型";
                break;
            case "4":
                dictName = "积极型";
                break;
            case "5":
                dictName = "进取型";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 产品状态字典转换
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function convProStatus(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "1":
                dictName = "认购";
                break;
            case "2":
                dictName = "申购";
                break;
            case "3":
                dictName = "停止交易";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 持续类型字典转换
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function convDurationType(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "1":
                dictName = "天";
                break;
            case "2":
                dictName = "月";
                break;
            case "3":
                dictName = "年";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 订单类型字典转换
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function convServiceType(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "1":
                dictName = "邮件";
                break;
            case "2":
                dictName = "短信";
                break;
            case "3":
                dictName = "邮件+短信";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }


    /**
     * 微信起始过渡页特殊的pageInit方法
     * 这里没有prePageCode，而是负载元素pageCode
     */
    function wxTransPageInit(parentPageCode, pageCode, param) {
        $("#stepLoadingDiv").hide();
        //增加过渡页面的标志位，兼容普通见面跳转过来
        if (!param) {
            param = {};
        }
        param._isWxTransFlag = true;

        var pageUrl = gconfig.viewsPath + pageCode + ".html",
            pageId = pageCode.replaceAll("/", "_"),
            parentPageId = parentPageCode.replaceAll("/", "_"); //负载父元素ID

        try {
            if ($("#" + pageId).length < 1) {
                require("ajax").loadHtml(pageUrl, function (htmlContent) {
                    document.title = $(htmlContent).attr("data-pageTitle");
                    $("#" + parentPageId).append(htmlContent); //保存这个page到parentPageCode
                    $("#" + pageId + " img").each(function () { //设置图片scr地址，防止出错
                        var src = $(this).attr("src");
                        if (src && $(this).attr("data-serverImg") != "true") {
                            $(this).attr("src", gconfig.imagesPath + src.substring(src.indexOf("images") + 7));
                        }
                    });
                    require.async(gconfig.scriptsPath + pageCode, function (page) {
                        appUtils.setSStorageInfo("wxTransPage_pageParam", JSON.stringify(param));
                        page.init(); //页面初始化
                        page.bindPageEvent(); //事件绑定
                    });
                }, true, false, false);
            } else {
                require.async(gconfig.scriptsPath + pageCode, function (page) {
                    appUtils.setSStorageInfo("wxTransPage_pageParam", JSON.stringify(param));
                    page.init(); //页面初始化
                    page.bindPageEvent(); //事件绑定
                });
            }
        } catch (e) {
            console.printStackTrace(e);
        }
    }

    /**
     * 微信起始过渡页特殊的getPageParam方法
     */
    function wxGetPageParam(paramName) {
        //根据_isWxTransFlag判断，兼容普通见面跳转过来
        var jsonParam = appUtils.getSStorageInfo("wxTransPage_pageParam");
        jsonParam = JSON.parse(jsonParam);
        if (jsonParam && jsonParam._isWxTransFlag) {
            if (paramName) {
                return jsonParam[paramName];
            } else {
                return jsonParam;
            }
        } else {
            return appUtils.getPageParam(paramName);
        }
    }
    //app硬件存储
    function setAppData(data){
        localStorage.burialPointList = JSON.stringify(data);
    }
    //app取值
    function getAppdata(){
        let data = localStorage.burialPointList;
        let burialPointList = (data && data.length) ? JSON.parse(data) : [];
        return burialPointList;
    }
    //生成随机UUID
    function generateUUID() {
        let dt = new Date().getTime();
        const uuid = 'xxxxxxxx-xxxx-xxxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c)=> {
            const r = (dt + Math.random()*16)%16 | 0;
            dt = Math.floor(dt/16);
            return (c=='x' ? r :(r&0x3|0x8)).toString(16);
        });
        return uuid;
    }
    //获取设备号
    function getEquipmentId(){
        let deviceTokenresult = external.callMessage({
            funcNo: "50022"
        });
        let deviceToken = deviceTokenresult.results ? deviceTokenresult.results[0].deviceToken : "";
        return deviceToken;
    }
    /**
     * @param {string} operationType 事件类型 1点击 2滑动 3后台切前台 4 页面销毁
     * @param {string} operationId 事件ID
     * @param {string} operationName 事件名称
     * @param {string} contentType 内容类型 1 产品 2 banner 3 活动ID 4 mailId 消息ID 5 popId 弹窗ID 6 fundMsgId 产品资讯ID 7 fundNoticeId 产品公告ID 8 fundFileId 产品文件ID 9 fundAgreementId 产品协议ID 10 noticeId 平台资讯ID 11 newsId 平台公告ID 12 articlId 学投资文章ID 13agreement_id协议ID
     */
    function recordEventData(operationType,operationId,operationName,contentType,contentData){
        if(platform == '0') return;
        let userInfo = ut.getUserInf() ? ut.getUserInf() : {};//拿到用户信息
        let allDataList = getAppdata();//取出所有参数
        let clickData = {
            eventId:generateUUID(),
            pageId:$("body .page[data-display='block']").attr("id"), //页面ID
            operationId:operationId,// 当前事件ID
            operationType:operationType ,// 当前事件类型 
            operationName:operationName,// 事件名称
            operationTime:new Date().getTime() + '',//点击时间
            custNo:userInfo.custNo ? userInfo.custNo : '',//客户号
            equipmentId:getEquipmentId(),// 设备号
            channelId:userInfo.custLabelCnlCode ? userInfo.custLabelCnlCode : '',// 渠道ID
            // ip:getNetworkStatus()[1],//IP
            platform:platform,// 设备类型
            // netWorkStatus:getNetworkStatus()[0],// 网络状态
            pageUuid:"",
        }
        if(contentType){
            //处理特殊参数
            if(contentType == '1') clickData.fundCode = contentData.fund_code;
            if(contentType == '2') clickData.bannerId = contentData.ad_id;
            if(contentType == '3') clickData.activityId = contentData.activity_id ? contentData.activity_id : contentData.id;
            if(contentType == '4') clickData.mailId = contentData.hash_key;
            if(contentType == '5') clickData.popId = contentData.pop_id;
            if(contentType == '6') clickData.fundMsgId = contentData.id;
            if(contentType == '9') clickData.fundAgreementId = contentData.agreement_id;
            if(contentType == '12') clickData.articlId = contentData.id + '';
            if(contentType == '14') clickData.essayId = contentData.essay_id;
            if(contentType == '15') clickData.catalogId = contentData.catalog_id;
            
        }
        let pageData = allDataList[allDataList.length - 1];//取出最后一条页面参数
        if(operationType == '4'){    //离开页面记录离开时间
            pageData.leaveTime = new Date().getTime() + '';
        }
        clickData.pageUuid = pageData.pageUuid //记录父级页面埋点ID 关联页面ID
        pageData.eventList.push(clickData);//生成事件埋点
        allDataList[allDataList.length - 1] = pageData;//重新赋值
        setAppData(allDataList);//存入缓存
    }
    //获取设备网络状,IP
    function getNetworkStatus(){
        let devresult = external.callMessage({
            funcNo: "50001"
        });
        let deviceNetwork = devresult.results ? devresult.results[0].deviceNetwork: "";
        let ip = devresult.results ? devresult.results[0].deviceLIP : "";
        return [deviceNetwork,ip];
    }
    function firstLoadFunc() {
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50100";
        require("external").callMessage(param);
        //是否刷新用户是否去过营销页
        if(getLocalStorage("sceneRefresh") == '0' ){
            //不刷 snowballMarketShow
            setLocalStorage("sceneRefresh",'1')
        }else{
            setLocalStorage("snowballMarketShow",'')
        }
        //是否重置用户选择版本
        if(getLocalStorage("userChooseRefresh") == '0' ){
            //不重置用户选择版本
            setLocalStorage("userChooseRefresh",'1')
        }else{
            //重置用户版本
            setLocalStorage("userChooseVerson",'')
        }
        //初始化是加载version_code
        var oVersion = require("external").callMessage({funcNo: "50010"});
        oVersion = oVersion.results ? oVersion.results[0] : {versionSn: global.version_code};
        var version = oVersion.versionSn;
        global.version_code = version;

        //checkCookieValidity方法参数暂时无用
        ssoUtils.checkCookieValidity(gconfig.global.ssoSignKey);
    }
    //重写app内的点击事件 bindEvent/preBindEvent
    let bindEvent = appUtils.bindEvent;
    appUtils.bindEvent = function(el, fn, type){
        let myCallback = function () {
            //进行埋点统一处理 bindEvent点击事件
            let operationType = $(this).attr("operationType");//获取事件类型
            let operationId = $(this).attr("operationId");//获取事件ID
            let operationName = $(this).attr("operationName");//获取事件名称
            let contentType = $(this).attr("contentType");//获取内容类型
            let contentData;
            if(contentType){
                contentData = $(this).find("em").text() ? JSON.parse($(this).find("em").text()) : $(this).siblings("em").text() ? JSON.parse($(this).siblings("em").text()) : {};
            }
            if(operationId && operationName) recordEventData(operationType,operationId,operationName,contentType,contentData);
            fn.apply(this, arguments);
        }
        bindEvent(el, myCallback, type);
    }
    let preBindEvent = appUtils.preBindEvent;
    appUtils.preBindEvent = function(parentEle, eleSelc, eMethod, eType){
        let myMethod = function(){
            //进行埋点统一处理 preBindEvent点击事件
            let operationType = $(this).attr("operationType");//获取事件类型
            let operationId = $(this).attr("operationId");//获取事件ID
            let operationName = $(this).attr("operationName");//获取事件名称
            let contentType = $(this).attr("contentType");//获取内容类型
            // console.log($(this).find("em").text(),111);
            let contentData = contentType ? JSON.parse($(this).find("em").text() ? $(this).find("em").text() : $(this).siblings("em").text()) : {};
            // recordEventData(operationType,operationId,operationName);
            if(operationId && operationName) recordEventData(operationType,operationId,operationName,contentType,contentData);
            eMethod.apply(this, arguments);
        };
        preBindEvent(parentEle, eleSelc, myMethod, eType);
    }
    function filterLoginOut(data) {
        var error_no = data.error_no;
        var error_info = data.error_info;
        if (error_no == "-999") {
            appUtils.clearSStorage("_isLoginIn");
            appUtils.clearSStorage("_loginInPageCode");
            appUtils.clearSStorage("_loginInPageParam");
            //其他清理工作...
            appUtils.pageInit(appUtils.getSStorageInfo("_curPageCode"), gconfig.loginPage.pageCode, gconfig.loginPage.jsonParam);
        }
    }

    /**
     * 权限校验
     * @param checkInParam 校验判断依据
     * "checkInParam": {
     * 		"prePageCode": prePageCode,
     * 		"pageCode": pageCode,
     * 		"param": param,
     * 		"isLastReq": isLastReq,
     * 		"isShowWait": isShowWait,
     * 		"isShowOverLay": isShowOverLay
     *	}
     */
    function checkPermission(checkInParam) {
        return true;
    }

    /**
     * 查询个人信息
     * @param {Object} bank_no
     * @return {TypeName}
     */
    function queryUser(user_id, backCall) {
        var param = {
            "user_id": user_id
        };
        service.queryUserInfo(param, function (data) {
            var error_info = data.error_info;
            var error_no = data.error_no;
            if (error_no != "0") {
                layerUtils.iAlert(error_info);
                layerUtils.iLoading(false);
                return false;
            }
            var result = data.results;
            if (result[0].bank_state) {
                appUtils.setSStorageInfo("bank_state", result[0].bank_state);
            }
            if (result[0].fund_pwd_state) {
                appUtils.setSStorageInfo("fund_pwd_state", result[0].fund_pwd_state);
            }
            if (result[0].info_state) {
                appUtils.setSStorageInfo("info_state", result[0].info_state);
            }
            if (result[0].kh_state) {
                appUtils.setSStorageInfo("kh_state", result[0].kh_state);
            }
            if (result[0].risk_state) {
                appUtils.setSStorageInfo("risk_state", result[0].risk_state);
            }
            if (result[0].trade_pwd_state) {
                appUtils.setSStorageInfo("trade_pwd_state", result[0].trade_pwd_state);
            }
            if (backCall) {
                backCall(data);
            }
        });
    }

    //金额 用逗号 隔开,可以控制小数位数，自动四舍五入
    //排除数字、.-之外字符，保留给定小数位，默认两位
    //s -金额   n -保留小数位数
    function fmoney(s, n) {
        if (s == 0) {
            s = s + "";
        }
        if (!s) return "--"; // 避免传参为空方法调用失败
        if (s == "--") return "--";
        s = s + "";
        n = n > 0 && n <= 20 ? n : 2;
        var startStr = "";
        if (s.substr(0, 1) == "-" || s.substr(0, 1) == "+") { //兼容负数、证书处理,截取 “-” “+”，数据处理完成再添加
            startStr = s.substr(0, 1);
            s = s.substr(1, s.length - 1);
        }
        s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";//更改这里n数也可确定要保留的小数位
        var l = s.split(".")[0].split("").reverse(),
            r = s.split(".")[1];
        var t = "";
        for (var i = 0; i < l.length; i++) {
            t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
        }
        return startStr + t.split("").reverse().join("") + "." + r.substring(0, n);//保留2位小数  如果要改动 把substring 最后一位数改动就可
    }

    //金额，还原函数
    function rmoney(s) {
        return parseFloat(s.replace(/[^\d\.-]/g, ""));
    }

    //保留小数点后的两位
    function keepTwoNumber(n) {
        if (/^(0|[1-9]\d*)(\.\d+)?$/.test(n)) {
            var slength = 0,
                p = n.indexOf('.');
            if (p >= 0) {
                var s_ = n.split("."),
                    slength_ = s_[1].length;
                if (slength_ > 2) {
                    slength = 2;
                } else {
                    slength = slength_;
                }
                n = n.substring(0, p + slength + 1);
            }
        }
        return n;
    }

    /**
     * 根据银行代码，返回银行名字,图标信息
     * @param {Object} bank_no
     */
    function getBankInfo(bank_no) {
        var bankKey = "bank_no_" + bank_no;
        var bankInfo = {
            bank_no_5100: {bank_name: "农业银行", bank_icon_s: "jh.png", bank_icon_b: "icon1.png"},
            bank_no_5: {bank_name: "招商银行", bank_icon_s: "zsyh.png", bank_icon_b: "zs.png"},
            bank_no_6: {bank_name: "中国银行", bank_icon_s: "zgyh.png", bank_icon_b: "NUHUFRrHx.png"},
            bank_no_9: {bank_name: "兴业银行", bank_icon_s: "xyyh.png", bank_icon_b: "icon7.png"},
            bank_no_b: {bank_name: "华夏银行", bank_icon_s: "hxyh.png", bank_icon_b: "huaxia.png"},
            bank_no_c: {bank_name: "光大银行", bank_icon_s: "gdyh.png", bank_icon_b: "guangda.jpg"},
            bank_no_d: {bank_name: "中信银行", bank_icon_s: "zxyh.png", bank_icon_b: "icon8.png"},
            bank_no_h: {bank_name: "浦发银行", bank_icon_s: "pfyh.png", bank_icon_b: "icon5.png"},
            bank_no_5000: {bank_name: "建设银行", bank_icon_s: "72jsyh.png", bank_icon_b: "jsyh.png"},
            bank_no_f: {bank_name: "广发银行", bank_icon_s: "72gfyh.png", bank_icon_b: "gfyh.png"},
            bank_no_8: {bank_name: "工商银行", bank_icon_s: "72gsyh.png", bank_icon_b: "gsyh.png"},
            bank_no_a: {bank_name: "交通银行", bank_icon_s: "72jtyh.png", bank_icon_b: "jtyh.png"},
            bank_no_e: {bank_name: "民生银行", bank_icon_s: "72msyh.png", bank_icon_b: "msyh.png"},
            bank_no_g: {bank_name: "深发展银行", bank_icon_s: "72payh.png", bank_icon_b: "payh.png"},
            bank_no_i: {bank_name: "北京银行", bank_icon_s: "72BJYH.png", bank_icon_b: "BJYH.png"},
            bank_no_q: {bank_name: "上海银行", bank_icon_s: "72SHYH.png", bank_icon_b: "SHYH.png"}
        };
        return bankInfo[bankKey];
    }

    /**
     * 根据订单状态码，返回状态描述
     * @param {Object} order_state
     */
    function getOrderStateName(order_state) {
        var stateKey = "order_state_" + order_state;
        var orderState = {
            "order_state_0": "新建",
            "order_state_1": "待提交",
            "order_state_2": "提交成功",
            "order_state_3": "失败",
            "order_state_4": "已成交",
            "order_state_5": "取消",
            "order_state_6": "已撤单",
            "order_state_7": "已退款"
        };
        return orderState[stateKey];
    }

    /**
     * 根据订单状态码，返回状态描述
     * @param {Object} order_state
     */
    function getBusinessTypeName(business_type) {
        var stateKey = "business_type_" + business_type;
        var orderState = {
            "business_type_0": "认购",
            "business_type_1": "申购",
            "business_type_2": "赎回",
            "business_type_3": "买入",
            "business_type_4": "卖出"
        };
        return orderState[stateKey];
    }

    /**
     * ios 解禁系统键盘
     */
    function systemKeybord() {
        var curPageCode = appUtils.getSStorageInfo("_curPageCode").replaceAll("/", "_");
        $(function () {    //遍历
            $("#" + curPageCode + " input").each(function () {
                if ($(this).attr("custom_keybord") == "0") {

                    appUtils.bindEvent($(this), function (e) {

                        external.callMessage({
                            "moduleName": "common",
                            "source": "mall",
                            "funcNo": "50210",
                            "pageId": curPageCode,
                            "eleId": "",
                            "doneLable": "done",
                            "keyboardType": "9"
                        });
                        e.stopPropagation();
                    }, "focus");
                }
            });
        });
    }

    /**
     * 登录时手势密码
     * params : 进入转让页面时必须传入的Json串
     */
    function gestureLogin(skipURL,snowballMarketShow) {
        if (!(skipURL)) {
            skipURL = "";
        } else {
            appUtils.setSStorageInfo("skipURL", skipURL);
        }
        //缓存当前从场景营销页去登录
        if(snowballMarketShow) appUtils.setSStorageInfo("isMarketLogin", snowballMarketShow);
        // 得到当前页
        var pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = pageId.replace("_", "/");

        // 获取用户信息
        if (platform != "0") {
            // 存储之前的路径 和必须要的参数
            var paramurl = {
                funcNo: "50042",
                key: "skipURL",
                isEncrypt: 1,//加密
                value: skipURL
            };
            external.callMessage(paramurl);

            // 获取用户头像信息
            var userImage = "";
            var params = {
                funcNo: "50043" ,
                key: "photo_url"
            };
            var datas = external.callMessage(params);
            if (datas.results[0].value) {
                userImage = datas.results[0].value;
            }
            // 获取用户账户信息
            var param50043 = {
                funcNo: "50043",
                key: "account_password"
            };
            var firstInstall = external.callMessage(param50043);
            let fingerprintPwd_flag = getLocalStorage("fingerprintPwd_flag");    //判断是否开启指纹登录
            if(fingerprintPwd_flag == '1') return fingerprint.showLoginDig(pageId,pageCode)
            // 得到用户手机号码进行查询是否设置过手势密码
            if (firstInstall.results[0].value) {
                firstInstall = firstInstall.results[0].value;
                if (firstInstall) {
                    var account = firstInstall.substring(0, firstInstall.indexOf("_"));
                    var param = {
                        "funcNo": "50263",
                        "account": account
                    };
                    var data = external.callMessage(param);
                    var flag = data.results[0].flag;
                    if (flag == "1") { //flag	String	状态（0：未设置，1：已设置）
                        var setParam = {
                            "funcNo": "50261",
                            "moduleName": "mall",
                            "style": "1",//style	String	手势密码的样式类型(0：不显示中心小圆，1：显示）
                            "account": account,
                            "errorNum": "5",
                            "isCanBack": "1",
                            "lockSenconds": "60",
                            "userImage": userImage
                        };
                        external.callMessage(setParam);
                    } else {
                        appUtils.pageInit(pageCode, "login/userLogin", {
                            "skipURL": skipURL,
                            "snowballMarketShow":snowballMarketShow
                        });
                    }
                }
            } else {
                appUtils.pageInit(pageCode, "login/userLogin", {"skipURL": skipURL,"snowballMarketShow":snowballMarketShow});
            }
        } else {
            appUtils.pageInit(pageCode, "login/userLogin", {"skipURL": skipURL,"snowballMarketShow":snowballMarketShow});
        }
    }

    /**
     * 功能：rsa加密
     * 参数：    password:需要加密的密码串
     *        backFunc:回调函数
     */
    function rsaEncrypt(password, backFunc) {
        if (validatorUtil.isNotEmpty(modulus) && validatorUtil.isNotEmpty(publicExponent)) {
            password = endecryptUtils.rsaEncrypt(modulus, publicExponent, password)
            backFunc(password);
        } else {
            var back = function (resultVo) {
                if (resultVo.error_no == "0") {
                    var dataList = resultVo.results[0];
                    modulus = dataList.modulus;
                    publicExponent = dataList.publicExponent;
                    password = endecryptUtils.rsaEncrypt(modulus, publicExponent, password);
                    backFunc(password);
                } else {
                    layerUtils.iLoading(false);
                    layerUtils.iAlert(resultVo.error_info);
                }

            };
            service.getRSAKey({}, back, {"isLastReq": false});
        }
    }


    /*
     * 名称： 换卡拦截
     * @param callback 回调函数
     * @param isShowTips <换卡期间不允许交易> 是否展示
     * @param changeCardTip 换卡提示语
     * @param isPage 换卡提示语
     * */
    function changeCardInter(callback, isShowTips, changeCardTip, ctrlParam,isPage,homePageIndex) {
        if (!ut.getUserInf().bankAcct) return bindPageTo();
        var changeCardTip = changeCardTip || "换卡期间不允许交易！";
        // 得到当前页
        var pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = pageId.replace("_", "/");
        if (isShowTips == undefined || isShowTips == null || isShowTips === "") {
            isShowTips = true;
        }
        service.reqFun101024({}, function (resultVo) {
            // replace_status 0:待审核 1:已审核待确认 2:换卡成功 3:换卡失败 4: 非换卡期间
            if (resultVo.error_no == "0") {
                var dataList = resultVo.results[0];
                if (dataList.replace_status == "0" && isShowTips) { //代审核
                    if(isPage){ //跳转
                        return bindPageTo(homePageIndex)
                    }else{
                        layerUtils.iAlert(changeCardTip);
                        return;
                    }
                };
                if(dataList.replace_status == "2" && isPage){   //换卡成功
                    return trueUrl()
                }
                if(dataList.replace_status == "3" && isPage){   //换卡失败
                    return bindPageTo(homePageIndex)
                }
                if(dataList.replace_status == "4" && isPage){   //换卡失败
                    return trueUrl()
                }
                // else if (dataList.replace_status == "1") {
                //     layerUtils.iConfirm("您的换卡已通过审核,请确认", function () {
                //         appUtils.pageInit(pageCode, "safety/changecardConfirm");
                //     })
                //     return;
                // }
                if (callback) callback();
            } else {
                layerUtils.iAlert(resultVo.error_info);
                layerUtils.iLoading(false);
                if(isPage){ //跳转
                    return bindPageTo(homePageIndex)
                }
            }
        }, ctrlParam)
    }
    function trueUrl(){
        let pageTopUrlInfo = appUtils.getSStorageInfo("pageTopUrlInfo");
        if(!pageTopUrlInfo || !pageTopUrlInfo.length) return bindPageTo(homePageIndex);
        let url = pageTopUrlInfo.split('_')[0];
        let previous = pageTopUrlInfo.split('_')[2] ? pageTopUrlInfo.split('_')[2] : null;
        appUtils.setSStorageInfo("pageTopUrlInfo","");
        if(previous) appUtils.setSStorageInfo("routerList", ["login/userIndexs",previous]);
        if(!previous) appUtils.setSStorageInfo("routerList", ["login/userIndexs"]);
        let pageId = $("body .page[data-display='block']").attr("id");
        if(pageId.trim().substr(0, 1) != '#'){
            pageId = '#' + pageId
        }
        $(pageId + ' .loginDig').hide()
        appUtils.pageInit('login/userIndexs', url, {});
    }
    function bindPageTo(homePageIndex){
        let userInfo = ut.getUserInf();
        var pageId = $("body .page[data-display='block']").attr("id");
        var pageCode = pageId.replace("_", "/");
        if (pageCode == "yuanhui/userIndexs" || pageCode == "hengjipy/userIndexs" || pageCode == "login/userIndexs") {
            return homePageIndex.init();
        }
        if(userInfo.custLabelCnlCode == "yh"){
            appUtils.pageInit("login/userLogin", "yuanhui/userIndexs", {});
        }else if(userInfo.custLabelCnlCode == "yh_jjdx" || !(userInfo.custLabelCnlCode) ||userInfo.custLabelCnlCode == "jjdx"){
            appUtils.pageInit("login/userLogin", "login/userIndexs", {});
        }else{
            appUtils.pageInit("login/userLogin", "hengjipy/userIndexs", {});
        }
    }

    /*
     * 名称：登录拦截
     * 作用：增加记忆功能
     * @param skpurl 记忆地址
     *
     * */
    function loginInter(skpUrl) {
        if (!ut.getUserInf()) {
            gestureLogin(skpUrl);
            // appUtils.pageInit("login/userIndexs", "login/userLogin", {skpUrl: skpUrl});
            return false;
        } else {
            return true;
        }
    }

    /**
     * 名称：上传身份证拦截
     * 作用：校验上传次数，未上传拦截
     * @param _pageCode 当前页面code
     * @param callback 回调函数
     * */
    function uploadIDInter(_pageCode, callback) {
        if (ut.getUploadStatus()) {
            callback();
            return;
        }
        appUtils.pageInit(_pageCode, "account/uploadIDCard");
    }

    /*
    * 名称：加密
    * */
    function desEncrypt(key, value) {
        var keyHex = des.enc.Utf8.parse(key);
        var valueHex = des.enc.Utf8.parse(value);
        var encrypted = des.DES.encrypt(valueHex, keyHex, {
            mode: des.mode.ECB,
            padding: des.pad.Pkcs7
        });
        return encrypted.toString();
    }
    /**
     * url转base64
     * @param {*} url 图片地址
     */
     async function url_base64(url){
        let img = url
        let image = new Image();
        image.src = img;
        // image.setAttribute('crossOrigin', 'anonymous');
        return new Promise((resolve, reject) => (
            image.onload = async ()=>{
                let base64 = await getBase64Image(image);
                resolve(base64) 
            }
        ))
    }
    async function getBase64Image(img){
        let canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        let ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, img.width, img.height);
        let ext = img.src.substring(img.src.lastIndexOf(".")+1).toLowerCase();
        let dataURL = canvas.toDataURL("image/"+ext);
        return dataURL;
    }
    /**
     * 名称：分享
     * @param shareTypeList 分享渠道
     * @param share_type 分享类型
     * @param isShowImg 是否仅展示图片
     * @param source 页面来源
     * @param activity_id 活动ID
     * */
    async function share(shareTypeList, share_type , activity_id,isShowImg, img_base64,is_open, is_activity , source,is_help,help_img_url) {
        // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
        activity_id = activity_id ? activity_id : ''
        // 简化分享链接
        var mobile = ut.getUserInf().mobileWhole;
        // 得到当前页
        var pageId = $("body .page[data-display='block']").attr("id");
        var query_params = {};
        query_params["registered_mobile"] = mobile;
        if (is_activity) {
            query_params["share_template"] = share_type;
        } else {
            query_params["tem_type"] = share_type;
        }
     
        // query_params["id"] = share_template;
        service.reqFun102012(query_params, async function (data) {
            if (data.error_no != 0) {
                return;
            }
            let randomNum = ('000000' + Math.floor(Math.random() * 999999)).slice(-6)
            if (data.results != undefined && data.results.length > 0) {
                mobile = desEncrypt("mobile", mobile);
                var result = data.results[0];
                var share_url = result.share_url;
                if (validatorUtil.isEmpty(share_url)) {
                    share_url = global.link;
                } 
                if (share_url.indexOf("?") != -1) {
                    // let lastUrl = share_url.split('!/')[1]
                    share_url = share_url + "&mobile=" + mobile + "&time=" + new Date().getTime() + randomNum + "&activity_id=" + activity_id + "&is_open=" + is_open + "&sysdate=" + result.sysdate + "&is_help=" + is_help + "&help_img_url=" + help_img_url  + "&source=" + source; 
                } else {
                    let lastUrl = share_url.split('!/')[1]; // 获取最后一级url
                    // console.log(lastUrl)
                    share_url = global.serverUrl + "/m/mall/index.html?mobile=" + mobile + "&time=" + new Date().getTime() + randomNum + "&activity_id=" + activity_id + "&is_open=" + is_open + "&sysdate=" + result.sysdate + "&is_help=" + is_help + "&help_img_url=" + help_img_url + "&source=" + source  + '#!/' + lastUrl;
                }
                var img_url = result.img_url;
                if (validatorUtil.isEmpty(img_url)) {
                    img_url = global.imgUrl;
                }
                var content = result.content;
                var title = result.title;
                var params = {};
                params["url"] = share_url;
                layerUtils.iMsg(-1, "启动分享中...请稍后！");
                $( ".pop_layer").hide();
                var param = {};
                param["moduleName"] = "mall";
                param["funcNo"] = "50231";
                param["shareType"] = shareTypeList;//平台字典
                param["title"] = title;
                param["link"] = share_url;
                param["content"] = content; //分享文本
                param["imgUrl"] = img_url
                if(isShowImg){  //单纯分享图片
                    param = {
                        "funcNo":"50231",
                        "imgUrl":img_base64,
                        "shareType":shareTypeList,
                        "imageShare":"1",
                        "imageType":"base64"
                    }
                }
                console.log(param)
                require("external").callMessage(param);
                return;
            }
        });
    }
    //保存信息至apk
    function setLocalStorage(key,value) {
            var paramlist = {
                funcNo: "50042",
                key: key,
                isEncrypt: "1",
                value: value
            };
            external.callMessage(paramlist);
    }
    // 清楚apk信息
    function clearLocalStorage(key) {
        var paramlist = {
            funcNo: "50042",
            key: key,
            isEncrypt: "1",
            value: ""
        };
        external.callMessage(paramlist);
    }

    //从apk获取信息
    function getLocalStorage(key) {
        var data = external.callMessage({
            funcNo: "50043",
            key: key,
        })
        if(data && data.results &&data.results.length > 0) {
            return  data.results[0].value
        }
        return "";
    }
    var sms_type = {
        changeBankMobile: "1", //变更银行预留手机号
        register: "2", //注册
        bindCard: "3", //绑卡
        fundInput: "4", //晋金宝充值
        changeMobile: "5", //变更注册手机号
        fundRealTimeEchash: "6", //晋金宝实时取现
        resetLoginPwd: "7", //重置登录密码
        restTradePwd: "8", //重置交易密码
        changeCard: "9", //换卡
        kjChangeCard: "10", //快捷换卡
        fundNormalEchash: "11", //晋金宝普通取现
        gatherModifyDividendsWay: "19", //大集合修改分红方式
        gatherModifyExpireWay: "20", //大集合修改到期赎回方式
        bankOpen: "50", //银行产品开户
        bankRecharge: "52", //银行产品充值
        bankEchash: "53", //银行产品取现
        bankPurchase: "54", //银行产品购买
        bankDraw: "55", //银行产品赎回
        bankChangeCard: "64", //银行电子账户更换银行卡
        bankTransferSell: "69", //银行产品转让-挂单
        bankTransferBuy: "70", //银行产品买转让
        bankCancelOrder: "71", //银行产品撤单
        bankEarlyDraw: "72", //银行产品撤单提前支取
        loginOff: "73", //销户
        loginDrain: "77", //引流登录
        yuanhuiRegister :"100", // 源晖注册
        jjsInput :"104", // 晋金所晋金宝转入
        bankInvestment:"151",//银行卡定投
    }

    //升级
    function updateVers(_pageId) {
        var chancel = null;
        var soft_no = global.soft_no;
        var oVersion = external.callMessage({funcNo: "50010"});
        oVersion = oVersion.results ? oVersion.results[0] : {versionSn: global.version_code};
        var version = oVersion.versionSn;
        global.version_code = version;
        // if (platform == "1") {
        //     chancel = "1";//手机
        // } else if (platform == "0") {
        //     chancel = "0";//PC
        // } else if (platform == "2") {
        //     chancel = "2";//IOS
        // } else {
            
        // }
        chancel = platform;
        var queryParam = {
            "versionsn": version,
            "channel": chancel,
            "soft_no": soft_no
        };
        service.reqFun102070(queryParam, function (data) {
            if (data.error_no == 0) {
                if (data.results.length != 0) {
                    var version_name = data.results[0].versionName;  //版本应用名称
                    var description = data.results[0].description;  //更新说明
                    var version_size = data.results[0].versionSize; //版本更新大小
                    var app_version = data.results[0].versionCode; //
                    var typeFlag = data.results[0].typeFlag;  //下载类型(0：原生，1：H5）
                    var isSilenceUpdate = data.results[0].isSilenceUpdate;  //是否是静默升级
                    var url = data.results[0].downloadUrl;
                    var versionStoreUpdate = data.results[0].versionStoreUpdate
                    $(_pageId + ' #edition').html(version_name);
                    $(_pageId + ' #description').html(description);
                    $(_pageId + ' #Size').html(version_size);
                    var invokeParam = {
                        funcNo: "50201", // String	功能号	Y
                        url: url, // 更新地址
                        type: typeFlag, //下载类型(0：原生，1：H5）
                        version: version_name,  //app版本
                        isShowUpdateTip: isSilenceUpdate,
                        versionSn: app_version //app版本序列号
                    };
                    if (isSilenceUpdate == "0" && typeFlag == "1") {//静默升级  (静默升级任何情况不弹框)
                        if (parseFloat(version) < parseFloat(app_version)) {
                            //缓存用户选中版本
                            setLocalStorage("sceneRefresh",'0');
                            setLocalStorage("userChooseRefresh",'0');
                            $(_pageId + " #activityDialog").hide();
                            external.callMessage(invokeParam);
                        }
                        return;
                    }
                    if (data.results[0].updateFlag == "0") {// 0 普通更新
                        if (parseFloat(version) < parseFloat(app_version)) {
                            $(_pageId + " #activityDialog").hide();
                            // $(_pageId + ' .pop_layer').show();
                            $(_pageId + ' .update_prompt').show();
                            $(_pageId + ' #close').show();
                            $(_pageId + ' #immediateUpdates').css('width', '50%');
                            // 点击更新事件
                            appUtils.bindEvent($(_pageId + " #immediateUpdates"), function () {
                                // $(_pageId + ' .pop_layer').hide();
                                // holdUpdateVer();
                                //缓存用户选中版本
                                setLocalStorage("sceneRefresh",'0');
                                setLocalStorage("userChooseRefresh",'0');
                                $(_pageId + ' .update_prompt').hide();
                                external.callMessage(invokeParam);
                            });
                        }
                    } else if (data.results[0].updateFlag == "1") { //1 强制更新
                        if (parseFloat(version) < parseFloat(app_version)) {
                            $(_pageId + " #activityDialog").hide();
                            // $(_pageId + ' .pop_layer').show();
                            $(_pageId + ' .update_prompt').show();
                            $(_pageId + ' #close').hide();
                            $(_pageId + ' #immediateUpdates').css('width', '100%');
                            // 点击更新事件
                            appUtils.bindEvent($(_pageId + " #immediateUpdates"), function () {
                                //缓存用户选中版本
                                setLocalStorage("sceneRefresh",'0');
                                setLocalStorage("userChooseRefresh",'0');
                                // $(_pageId + ' .pop_layer').hide();
                                $(_pageId + ' .update_prompt').hide();
                                if (typeFlag == "0" && platform == "1") { //安卓原生升级
                                    // holdUpdateVer();
                                    if(versionStoreUpdate == 1){
                                        invokeParam = {
                                            funcNo: "80318", // String	功能号	Y
                                            url: url, //下载地址）
                                        };
                                    }else{
                                        invokeParam = {
                                            funcNo: "80050", // String	功能号	Y
                                            downloadLink: url, //下载地址）
                                        };
                                    }
                                }
                                external.callMessage(invokeParam);
                            });
                        }
                    }
                }
            }
        });
    }


    /**
     *基金超市指数基金估值提示
     * @param pe_evaluation
     * @param zone_name
     * @param order_per
     * @returns {string}
     */
    function indexTips(pe_evaluation,zone_name,order_per){
        let ratio = (100-order_per).toFixed(0);
        var dictName = zone_name+"当前PE估值比"+ratio+"%时间低,";
        switch (pe_evaluation) {
            case "01":
                dictName += "值得持有";
                break;
            case "02":
                dictName += "持续关注";
                break;
            case "03":
                dictName += "谨慎持有";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }
    //更新后保留用户选中版本状态
    function holdUpdateVer(){
        setLocalStorage("sceneRefresh",'0');
        setLocalStorage("userChooseRefresh",'0');
    }
    var convDict = {
        "holdUpdateVer":holdUpdateVer,
        "recordEventData":recordEventData,
        "filterLoginOut": filterLoginOut,
        "convRecommendLevel": convRecommendLevel,
        "convRiskLevel": convRiskLevel,
        "convProStatus": convProStatus,
        "convDurationType": convDurationType,
        "convServiceType": convServiceType,
        "firstLoadFunc": firstLoadFunc,
        // "bindEvent":bindEvent,
        // "preBindEvent":preBindEvent,
        "checkPermission": checkPermission,
        "wxTransPageInit": wxTransPageInit,
        "wxGetPageParam": wxGetPageParam,
        "queryUser": queryUser,
        "getBankInfo": getBankInfo,
        "keepTwoNumber": keepTwoNumber,
        "fmoney": fmoney,
        "systemKeybord": systemKeybord,
        "gestureLogin": gestureLogin,
        "dateStyle": dateStyle,
        "dateStyle2": dateStyle2,
        "rsaEncrypt": rsaEncrypt,
        "changeCardInter": changeCardInter,
        "uploadIDInter": uploadIDInter,
        "loginInter": loginInter,
        "share": share,
        "sms_type": sms_type,
        "getLocalStorage": getLocalStorage,
        "setLocalStorage": setLocalStorage,
        "clearLocalStorage": clearLocalStorage,
        "updateVers": updateVers,
        "desEncrypt": desEncrypt,
        "emailType":emailType,
        "floatMultiply":floatMultiply,
        "floatDivide":floatDivide,
        "floatAdd":floatAdd,
        "floatSub":floatSub,
        "floatMod":floatMod,
        "indexTips":indexTips
    };
    //暴露对外的接口
    module.exports = convDict;
});
