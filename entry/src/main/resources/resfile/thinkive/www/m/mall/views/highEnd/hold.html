<div class="page" id="highEnd_hold" data-pageTitle="高端理财" data-pageLevel="0" data-refresh="true"
    style="-webkit-overflow-scrolling : touch;">
    <section id="product" class="main fixed" data-page="home" style="padding-bottom: 0">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" class="icon_back icon_gray" style="z-index: 99"><span>返回</span></a>
                <h1 class="text_gray text-center">高端理财</h1> 
                <a id="kefu" href="javascript:void(0)" class="coustomer-service-icon">
                    <img src="./images/customerService.png">
                </a>
            </div>
        </header>
        <article style="padding-top: 0">
             <div id="v_container_productList">
                <div class="visc_wrapper" id="v_wrapper_productList" data-iscrollPageId="Products_productList">
                    <div class="visc_scroller">
                        <div class="visc_pullDown" style="display:none;">
                            <span class="visc_pullDownIcon"></span>
                            <div class="visc_pullDownDiv">
                                <span class="visc_pullDownLabel">下拉加载上一页</span><br />
                                时间更新于：<span class="visc_pullDownTime"></span>
                            </div>
                        </div>
                        <!-- 页面展示 -->
                        <div class="finance_pro" style="padding: 0">
                            <div class="asset_top ">
                                <p class="g_marginTop_30 text_center text_gray g_fontSize14">高端理财资产(元)</p>
                                <p class="text_center text_darkgray g_fontSize30 prifundAssets">--</p>
                                <p class="tradeRecord text-gray displayFlex right_icon" style="padding-left: 0.1rem"><img
                                    src="../../images/history_icon.png" alt=""
                                    style="width: 0.15rem;height: 0.15rem;vertical-align: sub;">交易记录</p>
                            </div>
                            <div class="">
                                <div class="pro_status" style="height: 100%;">
                                    <div class="tab_box">
                                        <a href="javascript:void(0);" class="current" content="cyList" cust_fund_type="0">持有</a>
                                        <a href="javascript:void(0);" class=""  content="ztList" cust_fund_type="1">在途</a>
                                        <a href="javascript:void(0);" class="tobepaid"  content="doneList" cust_fund_type="2" style="display:none">待支付</a>
                                    </div>
                                </div>
                                <div class="my_finance">
                                    <!-- 持有，在途买入 -->
                                    <div class="purchase_list">
                                        
                                    </div>
                                    <div class="sale_list"></div>
                                </div>
                                <div class="new_none" style="display:none;">
                                    <div
                                            style="display:block;font-size:0.14rem;text-align:center;color:#778590;padding:0.15rem;">
                                        没有更多数据
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 页面展示数据 end-->
                        <div class="visc_pullUp" style="display:none;">
                            <span class="visc_pullUpIcon" style="display:none;"></span>
                            <div class="visc_pullUpDiv" style="display:none;">
                                <span class="visc_pullUpLabel">上拉加载下一页</span><br />
                                时间更新于：<span class="visc_pullUpTime"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </article>
    </section>
</div>
