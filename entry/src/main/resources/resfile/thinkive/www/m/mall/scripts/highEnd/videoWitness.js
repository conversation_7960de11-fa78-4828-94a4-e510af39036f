// 视频见证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        _pageCode = "highEnd/videoWitness",
        _pageId = "#highEnd_videoWitness ";

    var gconfig = require("gconfig");
    var global = gconfig.global;
    var ut = require("../common/userUtil");
    var userInfo;
    var productInfo;
    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        userInfo = ut.getUserInf();
        productInfo = appUtils.getSStorageInfo("productInfo");
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var biz_type = global.videoAuthentication.useTysp == '1' ? '1002' : '1';
            var userId = userInfo.custNo + "|" + productInfo.fund_code;
            var param = {
                "userId": userId,   // 用户编号
                "userName": userId,    // 用户名称
                "orgId": "1500",  // 营业部编号
                "netWorkStatus": 'WIFI',  // 网络状态 (可不填
                "url": global.videoPath + '?', // 连接排队BUS的服务器地址 (ios必须要在url加上?不然无法识别
                "moduleName": 'home', // 必须为open
                "funcNo": '60005', // 双向视频见证
                "isRejectToH5": '1',//原生是否处理透明通道信息 默认为0
                "userType": '1',
                //兼容原生和统一视频版本
                "user_id": userId,   // 用户编号
                "user_name": userId,    // 用户名称
                "branch_id": "1500",
                "origin": gconfig.platform,
                "biz_type": biz_type,
                "bizType": biz_type,
                "business_type": biz_type,
                "business_code": biz_type,
                "business_id": "fxckhWitness",
                "client_id": userId,
                "business_name": '非现场开户',
                "client_name": userInfo.name,
                "videoType": global.videoAuthentication.videoType || '0',
                "useTsyp": global.videoAuthentication.useTysp || '',
                "isNewView": "0",
                "videoInfo":"5163",
                "version": "4.0"
            }
            require("external").callMessage(param);
        })
    }

    /**
     * 多驳回
     */
    function videoWitnessCallback(param) {
        if (!param) {
            $.alert("透明通道返回信息为空");
            return;
        }
        var message;
        if(param.videoFlag) {
            message = JSON.parse(param.videoFlag);
        } else if(param.message) {
            message = JSON.parse(param.message);
        } else {
            layerUtils.iAlert("视频认证异常，请重试");
            return;
        }
        //"{"msgType":1,"msgNo":0}" //成功
        //"{"msgType":1,"msgNo":1}" //驳回
        //"{"msgType":1,"msgNo":2}" //失败
        if (message.msgNo == "0") {
            layerUtils.iAlert("视频认证成功", "", function () {
                appUtils.pageBack();
            });
            return;
        }
        if (message.msgNo == "1") {
            var str = "";
            for(var i = 0; i< message.msgInfo.length; i++) {
                str += (i+1) + "：" + message.msgInfo[i].rejectContent + "；";
            }
            str.substr(0, str.length - 1);
            layerUtils.iAlert("视频认证驳回，驳回原因：" + str);
            return;
        }
        if (message.msgNo == "2") {
            layerUtils.iAlert("视频认证失败，失败原因：" + msgInfo.rejectContent);
            return;
        }
    }

    function destroy() {
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var videoWitness = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack,
        "videoWitnessCallback": videoWitnessCallback,
    };
    // 暴露对外的接口
    module.exports = videoWitness;
});

