<div class="page" id="combProduct_combHoldDistribution" data-pageTitle="详细持仓" data-pageLevel="0" data-isSaveDom="false"
    data-refresh="true" style="-webkit-overflow-scrolling : touch;">

    <section id="m_product " class="main fixed" data-page="home" id="product">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" id="getBack" class="icon_back icon_gray" operationType="1" operationId="icon_back" operationName="返回"><span>返回</span></a>
                <h1 class="text_gray text-center">详细持仓</h1>
                <a id="kefu" operationType="1" operationId="kefu" operationName="客服" href="javascript:void(0)" class="coustomer-service-icon">
                    <img src="./images/customerService.png">
                </a>
            </div>
        </header>
        <article style="padding-bottom: 0.64rem;">
            <div class="bg-white">
                <div class="remarkTitle7 flex">
                    <ul class="m_font_size16 m_bold">持仓分布</ul>
                    <ul class="flex vertical_center">
                        <li class="m_font_size14 m_text_999">截止日期：<span id="crt_date"></span></li>
                    </ul>
                </div>
                <div id="chart-container1" style="height: 200px;width: 100%;"></div>
            </div>
            <div class="hold_detail bg-white mt10">
                <div class="hold_detail_th m_font_size16 m_bold">
                    <span>明细</span>
                    <span class="m_title_right">占比</span>
                </div>
                <div id="bondList">
                </div>
                <div id="mixedList">
                </div>
                <div id="currencyList">
                </div>
                <div id="stockList">
                </div>
                <div id="otherList">
                </div>

            </div>
            <div class="thfundBtn" style="display:none"></div>

        </article>
        <!-- <div class="thfundBtn" style="height: 0.86rem">
            <div class="purchase">
                <span id="threshold_amount">--</span>
                <span>元起购，</span>
                <span id="buy_rate_box">免申购费</span>
            </div>
            <div class="buy">
                <span class="f18" id="buy_state">--</span>
            </div>
        </div> -->
    </section>
</div>