/**
 * 模块名：晋金财富新年活动页面
 */
define(function (require, exports, module) {
    // require('../../js/video.min.js');
    /* 引用模块 */
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        common = require("common"),
        validatorUtil = require("validatorUtil"),
        tools = require("../common/tools");
    var external = require("external");
    var ut = require("../common/userUtil");
    require("../common/html2canvas.min");
    var global = gconfig.global;
    var _base64; //保存图片时，图片的base64编码
    var bigGiftInfo,messageInfo,children_activity_data,bannerInfo,main_activity_id,share_message_info;  //子活动对象集合
    var activity_id_string = [];//获取ID集合
    var choose_message_data = {}; //选中的模板
    var used_times,unused_times = '';    //看了的视频 未看的视频
    var self_send_word_content = '';    //接口返回已发布的寄语
    var main_activity_info;//主活动内容
    var send_word_template_list,send_word_board_list;//选择模板 选中的寄语
    var choose_message_text = '';   //选中的寄语
    var playVideoData = {}; //播放的视频对象集合
    var main_video_activity_data = {}; //视频子活动所有数据
    var play_video_id = [];
    var isShow = false;
    /* 常量 */
    var _pageCode = "activity/tenBillion", _pageId = "#activity_tenBillion";
    /* 变量  活动信息*/
    var userInfo, cust_no,video_list;
    var time1;  //观看秒数定时器获取
    var t1 = 0;
    var player = ''
    /**
     * 初始化
     */
    function init() {
        let html = `<video id="example_video" class="video-js vjs-default-skin vjs-big-play-centered" width="100%" webkit-playsinline="true" playsinline="true" height="100%" controls preload="auto" data-setup="{}"></video>`
        $(_pageId + ' #showVideo').append(html)
        userInfo = ut.getUserInf();
        cust_no = userInfo.custNo
        //获取页面信息
        initData()
        let mobile = ut.getUserInf().mobileWhole;
        bannerInfo = appUtils.getPageParam();
        if(!bannerInfo || bannerInfo == 'undefined'){
            bannerInfo = JSON.parse(sessionStorage.bannerInfo)
        }
        //appUtils.getSStorageInfo("routerList");
    }
    function notQualified(){
        return layerUtils.iAlert('当前参与人数过多，请稍后再试');
    }
    //获取页面信息
    function initData(){
        if (!common.loginInter()) return;   //未登录跳转登录页
        if (!ut.hasBindCard(_pageCode));
        service.reqFun108039({}, function (data) {
            if (data.error_no == "0") {
                let result = data.results[0];
                main_activity_info = result;//存主活动所有信息
                main_activity_id = result.id;
                used_times = result.used_times
                unused_times = result.unused_times
                $(_pageId + " .tenBillion_button .tips").text(result.activityText)
                if(main_activity_info.join_flag != '1'){
                    //渲染当前拼图
                    used_times = 0;
                    unused_times = 0;
                }
                $(_pageId + " .jigsawPuzzle").attr('src','./images/activity/pintu'+ used_times +'.png');
                //渲染拼图奖励文案
                
                //渲染按钮
                if(used_times == '6'){
                    if(main_activity_info.status == '3'){
                        $(_pageId + " .button2 span").html('已完成')
                        $(_pageId + " .button2 img").attr('src','./images/activity/tenBillion_button2.png')
                    }else{
                        $(_pageId + " .button2 span").html('领66元奖励')
                        $(_pageId + " .button2 img").attr('src','./images/activity/tenBillion_button3.png')
                    }
                }else{
                    $(_pageId + " .button2 img").attr('src','./images/activity/tenBillion_button2.png')
                    $(_pageId + " .button2 span").html(`<span>拼福榜*<em></em>次</span>`)
                    $(_pageId + " .button2 em").text(unused_times)
                }
                //渲染活动规则
                $(_pageId + " .tenBillionRule .rules").html(result.introduce)
                //渲染拼图完成列表
                setPuzzleList(result.prepose_list);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染视频 

    function setImgList(){
        let html = ''
        video_list.map(item=>{
            if(item.isWatched == '1'){
                play_video_id.push(item.video_id)
            }
            let video_original_name = item.video_name?item.video_name:'--'    //视频名称
            let cover_path = global.oss_url + item.cover_path;   //封面地址
            html += `<ul>
                        <li class="videoBg">
                            <em style="display:none">${JSON.stringify(item)}</em>
                            <p class="main_flxe flex_center" style="background: url(${cover_path}) no-repeat center center;">
                                <i></i>
                            </p>
                        </li>
                        <li class="m_center">
                            ${video_original_name}
                        </li>
                    </ul>`
        })
        $(_pageId + " .videoList").html(html)
    }
    //获取活动记录
    function getActivityList(){
        let requestData = {
            activity_id:activity_id_string.toString() + ','+ main_activity_id,
        }
        service.reqFun108018(requestData, async(data) =>{
            if(data.error_no == '0'){
                var res = data.results
                if(!res[0] || !res[0].data){
                    return $(_pageId + " .tenBillionRule .haveList").html('<p class="m_center">暂无数据</p>')
                }
                let list = res[0].data
                let html = ''
                if(!list.length) return
                for(var i=0;i< list.length;i++){
                    html += `<li class="flex">
                                <span class="haveList_left">${list[i].crt_time.substr(4, 2) + '-' + list[i].crt_time.substr(6, 2)}</span>
                                <span class="haveList_center">${list[i].activity_name}</span>
                                <span class="haveList_right">${list[i].reward_name}</span>
                            </li>`
                }
                $(_pageId + " .tenBillionRule .haveList").html(html)
            }else{
                $(_pageId + " #tenBillionRule .haveList").html('<span class="m_center">暂无数据</span>')                
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染拼图完成列表
    function setPuzzleList(list){
        let html = ''
        if(!list || list.length == 0){
            return 
        }
        list.map(item=>{
            activity_id_string.push(item.activity_id)
            if(item.activity_type == '6'){  //终极大礼
                bigGiftInfo = item;
                $(_pageId + " .bigRemark").text(bigGiftInfo.activity_text);
                if(bigGiftInfo.state != '1' || main_activity_info.join_flag != '1') $(_pageId + " .luckDraw").text(bigGiftInfo.btn_name);
                if(main_activity_info.state == '1' && bigGiftInfo.state == '1' && main_activity_info.join_flag ==  '1'){
                    $(_pageId + " .luckDraw").text('立 即 抽 奖');
                    if(main_activity_info.status == '1'){  //未完成
                        $(_pageId + " #noLuckDraw span").text(6 - (main_activity_info.used_times*1))
                        $(_pageId + " #noLuckDraw").show();
                    }else if(main_activity_info.status == '2' && bigGiftInfo.times*1 > 0){    //可以领取
                        $(_pageId + " #canLuckDraw").show();
                        // $(_pageId + " .luckDraw").text(bigGiftInfo.btn_name);
                    }else if(main_activity_info.status == '3' && bigGiftInfo.times*1 > 0){    //可以领取
                        $(_pageId + " #canLuckDraw").show()
                        // $(_pageId + " .luckDraw").text(bigGiftInfo.btn_name);
                    }
                }
            }else if(item.activity_type == '26'){ //寄语模块
                share_message_info = item
            }else{
                let btn_name = (item.status == '1' ||  main_activity_info.join_flag != '1') ? item.btn_name : item.status == '3' ? '已完成' : ''  //列表按钮样式
                if(item.activity_type == '24' && main_activity_info.join_flag != '1'){
                    item.schedule = 0;
                }
                html += `<li class="main_flxe" style="" state="${item.state}" status="${item.status}" share_template="${item.share_template}" activity_id="${item.activity_id}">
                            <em style="display:none">${JSON.stringify(item)}</em>
                            <div class="doorshow main_flxe flex_center">
                                <img class="door" src="./images/activity/tenBillion_pian.png" alt="">
                                <span>*1</span>
                            </div>
                            <h5 class="share_template ${item.activity_type == "24"?"video_title":""}">${item.activity_type == "24"? item.activity_tips + '(' + item.schedule + '/' + item.total_schedule + ')' : item.activity_tips}</h5>
                            <h6 class="btn_name main_flxe vertical_center ${item.activity_type == "24"?"video_btn_name":item.activity_type == "25"?"message_btn_name":""}">${btn_name}></h6>
                        </li>`
                if(item.activity_type == "24"){ //渲染视频
                    if(!item || !item.video_list) return
                    video_list = JSON.parse(item.video_list);
                    main_video_activity_data = item;
                    $(_pageId + " .video_remark").text(main_video_activity_data.activity_text)
                    setImgList(video_list);
                }
                if(item.activity_type == '25'){ //渲染模板
                    messageInfo = item
                    self_send_word_content = item.self_send_word_content;
                    $(_pageId + " .message_remark").text(messageInfo.activity_text)
                    send_word_board_list = JSON.parse(item.send_word_board_list)
                    if(item.send_word_template_list && item.send_word_template_list.length) set_send_word_template_list(JSON.parse(item.send_word_template_list));
                    if(item.send_word_board_list && item.send_word_board_list.length) set_send_word_board_list(JSON.parse(item.send_word_board_list));
                    if(self_send_word_content != '') $(_pageId + " .release_left").text(self_send_word_content)
                }
            }
        })
        let tips = main_activity_info.activityTips ? main_activity_info.activityTips : '';
        html += ` <div class="activityTips">${tips}</div> `
        $(_pageId + " .subactivity_li").html(html)
        //获取活动记录
    }
    /**
     * 事件绑定
     */
    //渲染上墙寄语
    function set_send_word_board_list(list){
        let html = ''
        list.map(item=>{
            html += `<p>
                        <span>${item.mobile}:</span>
                        <span>${item.send_word_content}</span>
                    </p>`
        })
        $(_pageId + ' .show_messageVersion').html(html);
        // let height = $(_pageId + ' .show_messageVersion_father').height()
        // $(_pageId + ' .show_messageVersion_father').css('max-height',height+'px')
    }
    //渲染寄语模板
    function set_send_word_template_list(list){
        let html = ''
        
        list.map(item=>{
            let chooseClass
            if(self_send_word_content == item.send_word_content){
                chooseClass = 'active'
            }
            html += `<p class="${chooseClass}" send_word_id="${item.send_word_id}">${item.send_word_content}</p>`
        })
        $(_pageId + ' .choose_messageVersion ul').html(html)
    }
    //观看够30秒视频
    function seeVideo(){
        if(main_activity_info.join_flag != '1') return;
        let chooseId = playVideoData.video_id;
        let str = play_video_id.join(',');
        if(str.indexOf(chooseId) != -1){
            return;
        }
        let requestData = {
            video_id:playVideoData.video_id + '',
            activity_id:playVideoData.activity_id + '',
            main_activity_id:main_activity_id
        }
        service.reqFun108042(requestData, function (data) {
            if(data.error_no == "0"){
                if(data.results && data.results.length){
                    let result = data.results[0]
                    if((result.schedule*1) < video_list.length){    //部分播放
                        play_video_id.push(playVideoData.video_id)
                        $(_pageId + " .video_title").text(main_video_activity_data.activity_tips + '(' + (result.schedule*1) + '/' + main_video_activity_data.total_schedule + ')')
                    }else{
                        unused_times = unused_times*1 + 1
                        main_video_activity_data.status = '3';
                        play_video_id.push(playVideoData.video_id)
                        $(_pageId + ' .video_btn_name').text('已完成>');
                        $(_pageId + " .button2 em").text(unused_times*1)
                        $(_pageId + " .video_title").text(main_video_activity_data.activity_tips + '(' + main_video_activity_data.total_schedule + '/' + main_video_activity_data.total_schedule + ')')
                    }
                    //schedule
                }else{
                    //不做处理
                }
            }
        }, {"isShowWait": false})
    }
    function bindPageEvent() {
        //返回首页
        appUtils.bindEvent($(_pageId + " .icon_back"), () => {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .next"), () => {
            autoScroll(".choose_messageVersion")
        });
        //查看领取记录
        appUtils.bindEvent($(_pageId + " .button1"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if(main_activity_info.join_flag != '1') return notQualified()
            getActivityList()
            $(_pageId + " #rewardRecord").show();
        });
        //关闭领取记录
        appUtils.bindEvent($(_pageId + " #rewardRecord .tenBillionRule_btn"), function () {
            $(_pageId + " #rewardRecord").hide();
        });
        //查看活动规则
        appUtils.bindEvent($(_pageId + " .button3"), function () {
            $(_pageId + " #activityRules").show();
        });
        //关闭活动规则
        appUtils.bindEvent($(_pageId + " #activityRules .tenBillionRule_btn"), function () {
            $(_pageId + " #activityRules").hide();
        });
        //关闭第六个拼图弹窗
        appUtils.bindEvent($(_pageId + " #over_onePuzzleSix .onePuzzle_btn"), function () {
            $(_pageId + " #over_onePuzzleSix").hide();
        });
        //关闭第一个拼图弹窗
        appUtils.bindEvent($(_pageId + " #onePuzzle .onePuzzle_btn"), function () {
            $(_pageId + " #onePuzzle").hide();
        });
        //关闭寄语弹出层
        appUtils.bindEvent($(_pageId + " #over_shareMessage .onePuzzle_btn"), function () {
            $(_pageId + " #over_shareMessage").hide();
        });
        //完成所有拼图
        appUtils.bindEvent($(_pageId + " #over_onePuzzle .onePuzzle_btn"), function () {
            $(_pageId + " #over_onePuzzle").hide();
        });
        //可以抽奖 
        appUtils.bindEvent($(_pageId + " #canLuckDraw .onePuzzle_btn"), function () {
            // $(_pageId + " #canLuckDraw").hide();
            if(main_activity_info.join_flag != '1') return notQualified()
            appUtils.pageInit(_pageCode,"activity/tenBillionLuckDraw", {
                "activity_id":bigGiftInfo.activity_id,
                "main_activity_id":main_activity_id,//主活动id

            });
        });
        //不可以抽奖弹窗 
        appUtils.bindEvent($(_pageId + " #noLuckDraw .onePuzzle_btn"), function () {
            $(_pageId + " #noLuckDraw").hide();
        });
        //点击立即抽奖 
        appUtils.bindEvent($(_pageId + " .luckDraw"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if(main_activity_info.join_flag != '1') return notQualified()
            if(main_activity_info.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(main_activity_info.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(bigGiftInfo.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(bigGiftInfo.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(bigGiftInfo.times*1 <= 0){
                return layerUtils.iAlert('今日抽奖次数已用完');
            }
            if(used_times != '6' ){
                $(_pageId + " #noLuckDraw span").text(6 - (used_times*1))
                $(_pageId + " #noLuckDraw").show();
            }else{
                appUtils.pageInit(_pageCode,"activity/tenBillionLuckDraw", {
                    "activity_id":bigGiftInfo.activity_id,
                    "main_activity_id":main_activity_id,//主活动id
                });
            }
        });
        //点击视频
        appUtils.preBindEvent($(_pageId + " .videoList"), "ul .videoBg", function () {
            if (!ut.hasBindCard(_pageCode)) return;
            isShow = true;
            let param = JSON.parse($(this).find("em").text()); //存储数据格式
            param.video_path = global.video_oss_url + param.video_path;
            param.cover_path = global.oss_url + param.cover_path;
            player = videojs('example_video', {
            }, function onPlayerReady() {
                //结束和暂时时清除定时器，并向后台发送数据
                this.on('ended', function () {
                    window.clearInterval(time1);
                });
                this.on('pause', function () {
                    window.clearInterval(time1);
                });
                this.on('waiting', function () {
                    window.clearInterval(time1);
                })
                //开始播放视频时，设置一个定时器，每100毫秒调用一次aa(),观看时长加1秒
                this.on('playing', function () {
                    window.clearInterval(time1);
                    time1 = setInterval(function () {
                        t1 += 1;
                        if(t1 >= 20){
                            //调用接口 清空时间 初始化时间0
                            window.clearInterval(time1);
                            t1 = 0;
                            seeVideo();
                        }
                    }, 1000);     
                });     
            });
            //判断当前选中的和之前播放的是否一致
            if(playVideoData.video_id == param.video_id){   //视频一致
                $(_pageId + " #showVideo").show()
                // player.play()
            }else{
                t1 = 0;
                window.clearInterval(time1);
                playVideoData = param;  //选中当前选择的播放视频
                player.reset();
                player.src({src:param.video_path})
                player.load(param.video_path)
                $(_pageId + " video").attr("poster",param.cover_path)
                $(_pageId + " #showVideo").show()
                // player.play()
                
            }
        },'click');
        //点击寄语模板
        appUtils.preBindEvent($(_pageId + " .choose_messageVersion ul"), "p", function () {
            if(self_send_word_content != '') return layerUtils.iAlert('您已经发布过寄语');
            choose_message_data = {
                send_word_content:$(this).text(),
                send_word_id:$(this).attr('send_word_id'),
                activity_id:messageInfo.activity_id,
                main_activity_id:main_activity_id
            }
            if ($(this).hasClass("active")) {
                $(this).removeClass("active");
                choose_message_text = '';
                choose_message_data = {}
            } else {
                $(_pageId + " .choose_messageVersion ul p").removeClass('active')
                $(this).addClass('active')
                choose_message_text = $(this).text()
                $(_pageId + ' .release_left').text($(this).text())
            }
            
        },'click');
        //点击拼图列表
        appUtils.preBindEvent($(_pageId + " .subactivity_li"), "li", function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if(main_activity_info.join_flag != '1') return notQualified()
            let param = JSON.parse($(this).find("em").text()); //存储数据格式
            if(main_activity_info.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(main_activity_info.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(param.state == '2'){ //活动未开始
                return layerUtils.iAlert('活动还未开始。');
            }
            if(param.state == '3'){ //活动未开始
                return layerUtils.iAlert('活动已结束。');
            }
            if(param.status != '1'){    //您已参加此活动
                return layerUtils.iAlert('您已参加此活动。');
            }
            
            if(param.activity_type == '27'){    //设置定投
                if (!common.loginInter()) return;   //未登录跳转登录页
                if (!ut.hasBindCard(_pageCode)) return;
                // appUtils.pageInit(_pageCode, "login/userIndexs");
                appUtils.setSStorageInfo("fund_market_zone_prod_type", '');   //存储分类一级内容
                appUtils.pageInit(_pageCode, "fundSupermarket/index", {
                    // pageInfo: '02'
                });
            }
            if(param.activity_type == '17' || param.activity_type == '23'){
                if (!common.loginInter()) return;   //未登录跳转登录页
                if (!ut.hasBindCard(_pageCode)) return;
                //获取子活动参数
                children_activity_data = param
                $(_pageId + " #pop_layer").show()
            }
            if(param.activity_type == '25'){    //页面滚动到寄语
                if(messageInfo.status !='1') return layerUtils.iAlert('您已参加此活动。');
                $(_pageId + " .boost").animate({scrollTop:($("#message_div").offset().top + $(".boost").scrollTop() - 80)},1000)
            }
            if(param.activity_type == '24'){
                if(main_video_activity_data.status != '1') return layerUtils.iAlert('您已参加此活动。');
                $(_pageId + " .boost").animate({scrollTop:($("#video_div").offset().top + $(".boost").scrollTop() - 80)},1000)
            }
            // $(".boost").scrollTop(400)
        }, 'click');
        //分享到朋友圈 
        appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), function () {
            if(main_activity_info.join_flag != '1') return notQualified()
            let results = {
                banner_id:bannerInfo.banner_id,
                group_id:bannerInfo.group_id,
                activity_id:children_activity_data.activity_id,
            }
            if(children_activity_data.activity_type == '17') return common.share("23", children_activity_data.share_template);   //普通分享，邀请投资
            if(children_activity_data.activity_type == '23')  {
                let mobile = ut.getUserInf().mobileWhole;
                let query_params = {
                    registered_mobile:mobile,
                    tem_type:children_activity_data.share_template
                }
                service.reqFun102012(query_params, async function (data) {
                    if (data.error_no == '0') {
                        // if (data.results == undefined || data.results.length <= 0) ret
                        var result = data.results[0];
                        results.title = result.title;
                        results.content = result.content;
                        results.img_url = result.img_url;
                        let shareUrlLast = result.share_url.split('#!/')[1].split('.')[0]
                        return tools.pageShare(results, '23', _pageId, shareUrlLast, null, children_activity_data.activity_id);//活动页面分享
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                });
                return
            }
            if(children_activity_data.activity_type == '26'){   //分享寄语
                if (!ut.hasBindCard(_pageCode)) return;
                if(self_send_word_content == ''){
                    return layerUtils.iAlert('请先发布寄语');
                }
                // if(share_message_info.status == 3){
                //     return layerUtils.iAlert('您已经参加过活动');
                // }
                //分享图片
                $(_pageId + ' .remark').html(self_send_word_content)
                var father = document.querySelector("#content");
                var _fatherHTML = document.querySelectorAll("#content .page");
                var cur = document.querySelector("#activity_tenBillion");
                father.innerHTML = "";
                father.appendChild(cur);
                let dom = document.querySelector(_pageId + " .imvitaion-img")
                html2canvas(dom, {
                    scale: 4
                }).then(canvas => {
                    var base64 = canvas.toDataURL("image/png");
                    var _base64 = base64.split(",")[1];
                    father.innerHTML = "";
                    for (let i = 0; i < _fatherHTML.length; i++) {
                        father.appendChild(_fatherHTML[i]);
                    }
                    common.share("23", share_message_info.share_template, "", true, _base64);
                })
                //调用寄语参与接口
                // play_message_activity()
            }
            
        });
        //发送寄语
        appUtils.bindEvent($(_pageId + " .tenBillion_release"), function () {
            if (!ut.hasBindCard(_pageCode)) return;
            if(main_activity_info.join_flag != '1') return notQualified()
            if(main_activity_info.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(main_activity_info.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(self_send_word_content != ''){
                return layerUtils.iAlert('您已经发布过寄语');
            }
            if(!choose_message_data.send_word_id || choose_message_data.send_word_id == 'undefined'){
                return layerUtils.iAlert('请选择要发布的寄语');
            }
            service.reqFun108043(choose_message_data, function (data) {
                if(data.error_no == "0" && data.results && data.results[0]){
                    self_send_word_content = choose_message_data.send_word_content
                    //渲染按钮
                    let mobile = ut.getUserInf().mobileWhole;
                    mobile = mobile.substr(0, 3) + "****" + mobile.substr(-4)
                    let html = `<p>
                                    <span>${mobile}:</span>
                                    <span>${choose_message_text}</span>
                                </p>`
                    $(_pageId + " .button2 em").text(unused_times*1 + 1);
                    
                    $(_pageId + ' .message_btn_name').text('已完成>');
                    messageInfo.status = '3';
                    unused_times = unused_times*1 + 1;
                    if(send_word_board_list && send_word_board_list.length < 3){
                        $(_pageId + ' .show_messageVersion').append(html)
                        let height = $(_pageId + ' .show_messageVersion').height()
                        $(_pageId + ' .show_messageVersion_father').css('max-height',(height + 20) +'px')
                    }else if(send_word_board_list && send_word_board_list.length == 3){
                        $(_pageId + ' .show_messageVersion').append(html)
                        let height = $(_pageId + " .show_messageVersion p:last-child").height()
                        let new_height = $(_pageId + ' .show_messageVersion').height()
                        $(_pageId + ' .show_messageVersion_father').css('max-height',(new_height + 20) +'px')
                        autoScroll('.show_messageVersion_father',height)
                    }
                }else{
                    layerUtils.iAlert(data.error_info?data.error_info:'您已发布过寄语');
                }
            })
        });
        appUtils.bindEvent($(_pageId + " .ceshi"), function () {

        })
        //拼福榜
        appUtils.bindEvent($(_pageId + " .button2"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if(main_activity_info.join_flag != '1') return notQualified()
            if(main_activity_info.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(main_activity_info.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(main_activity_info.status == '3'){
                return layerUtils.iAlert('活动已完成');
            }
            if(unused_times == '0' && used_times != '6'){
                return layerUtils.iAlert('参与下方活动可获得拼图机会');
            }
            let main_used_times = used_times;
            service.reqFun108032({activity_id: main_activity_id}, function (data) {
                if(data.error_no == "0" && data.results && data.results[0]){
                    let result = data.results[0]
                    if(main_used_times == '6'){
                        main_activity_info.status = '3'
                        $(_pageId + " .button2 span").html('已完成')
                        $(_pageId + " .button2 img").attr('src','./images/activity/tenBillion_button2.png')
                        return $(_pageId + " #over_onePuzzle").show();
                    }
                    if(result.used_times != '6'){
                        unused_times = result.unused_times
                        used_times = result.used_times
                        $(_pageId + " .jigsawPuzzle").attr('src','./images/activity/pintu'+ result.used_times  +'.png')
                        $(_pageId + " .button2 em").text(result.unused_times)
                        $(_pageId + " #onePuzzle .gaveIntegralNum").text(result.points)
                        $(_pageId + " #onePuzzle .onePuzzleNum").text(6 - (result.used_times*1))
                        $(_pageId + " #onePuzzle").show();
                    }else{
                        unused_times = result.unused_times;
                        used_times = result.used_times;
                        main_activity_info.status = '2';
                        bigGiftInfo.times = 1;
                        if(bigGiftInfo.state != '1') $(_pageId + " .luckDraw").text(bigGiftInfo.btn_name);
                        if(bigGiftInfo.state == '1') $(_pageId + " .luckDraw").text('立 即 抽 奖');
                        $(_pageId + " .jigsawPuzzle").attr('src','./images/activity/pintu'+ result.used_times  +'.png');
                        $(_pageId + " .button2 span").html('领66元奖励');
                        $(_pageId + " .button2 img").attr('src','./images/activity/tenBillion_button3.png');
                        $(_pageId + " #over_onePuzzleSix .gaveIntegralSix").text(result.points)
                        $(_pageId + " #over_onePuzzleSix").show();
                    }
                }else{
                    if(data.error_no == '-7'){
                        return layerUtils.iAlert('参与下方活动可获得拼图机会');
                    }
                    layerUtils.iAlert(data.error_info);
                }
            })
        });
        //取消分享到朋友圈 
        appUtils.bindEvent($(_pageId + " #cancelShare"), function () {
            $(_pageId + " #pop_layer").hide()
        });
        //取消分享到朋友圈 
        appUtils.bindEvent($(_pageId + " .close"), function () {
            window.clearInterval(time1);
            player.pause();
            $(_pageId + " #showVideo").hide()
        });
        //分享寄语
        appUtils.bindEvent($(_pageId + " .share_messageVersion"), function () {
            if (!common.loginInter()) return;   //未登录跳转登录页
            if (!ut.hasBindCard(_pageCode)) return;
            if(main_activity_info.join_flag != '1') return notQualified()
            if(!share_message_info) return layerUtils.iAlert('活动不存在');
            if(main_activity_info.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(main_activity_info.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(share_message_info && share_message_info.state == '2'){
                return layerUtils.iAlert('活动未开始');
            }
            if(share_message_info && share_message_info.state == '3'){
                return layerUtils.iAlert('活动已结束');
            }
            if(share_message_info && share_message_info.status == '3'){
                return layerUtils.iAlert('您已参加此活动'); 
            }
            if(self_send_word_content == '' || !self_send_word_content){
                return layerUtils.iAlert('请先发布寄语');
            }
            share_message_info.self_send_word_content = self_send_word_content;
            sessionStorage.bannerInfo = JSON.stringify(bannerInfo)
            appUtils.pageInit(_pageCode, "activity/tenBillionImg", share_message_info);
        });
        
    }
    //寄语板操作
    function autoScroll(obj,height){ 
        $(obj).find("ul").animate({marginTop : height?('-' + (height+20) + "px"):"-0.43rem"},800,function(){ 
            $(this).css({marginTop : "0px"}).find("p:first").appendTo(this); 
            if(height){
                //删除最后一个li
                $(_pageId + " .show_messageVersion p:last").remove()
            }
        }) 
     } 
    /**
     * 销毁
     */
    function destroy() {
        $(_pageId + " .tenBillionRule .haveList").html('');
        $(_pageId + " #rewardRecord").hide();
        $(_pageId + " #noLuckDraw").hide();
        $(_pageId + " #canLuckDraw").hide();
        $(_pageId + " #showVideo").hide();
        $(_pageId + ' .release_left').text('选择以下你想说的话参与活动。')
        used_times = '';
        unused_times = '';
        main_activity_info = {};
        self_send_word_content = '';
        choose_message_text = '';
        choose_message_data = {};
        playVideoData = {};
        main_video_activity_data = {};
        window.clearInterval(time1);
        if(player && isShow){
            player.dispose();
        }
        $(_pageId + ' #example_video').remove();
        play_video_id = [];
        isShow = false;
    };
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }

    var index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    module.exports = index;


});
