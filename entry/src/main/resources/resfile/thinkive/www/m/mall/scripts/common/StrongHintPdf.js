/**
 * H5 PDF 文件预览
 */
 define((require, exports, module)=> {
    require('./pdf.worker.js')
    var appUtils = require("appUtils")
    var gconfig = require("gconfig");
    let layerUtils = require("layerUtils")
    let service = require("mobileService")
    let pdfh5 = require('./pdfh5.js')
    let global = gconfig.global;
    // let new_configuration = require("configuration")  
    let requestData
    let new_setInterval
    let _pageId
    
	var get_pdf_file ={
        // get_pdf_file.clearTime()
        //获取需要预览的文件信息
        get_file:(fund_code,_pageId, type)=>{
            get_pdf_file.clearTime()
            _pageId = _pageId
            appUtils.bindEvent($(_pageId+ " .iframe_pdf_bottom .vant_button"),  (e)=> {
                e.stopPropagation();
                let setDom = $(_pageId+ " .iframe_pdf_bottom .vant_button") //操作按钮的dom元素
                if(setDom.attr('getServer') == 1) return
                //点击确定调用接口
                service.reqFun102107(requestData, (data)=>{
                    if(data.error_no == '0'){
                        get_pdf_file.clearTime()
                        // if(new_setInterval)clearInterval(new_setInterval)
                        $(_pageId+ " .van-overlay").hide();
                    }else{
                        layerUtils.iAlert(data.error_info);
                    }
                })
            })
            //取消
            appUtils.bindEvent($(_pageId+ " #paf_new_close"),(e)=> {
                // if(new_setInterval)clearInterval(new_setInterval)
                get_pdf_file.clearTime();
                appUtils.setSStorageInfo("userChooseAnswer",'');
                $(" .van-overlay").hide();
                appUtils.pageBack();
            })
            // console.log(_pageId)
            service.reqFun102106({fund_code: fund_code, type: type}, (data)=>{
                if(data.error_no == '0'){
                    let info = data.results[0]
                    if(info.state == '0' || info.state == '2') return
                    requestData = {fund_code:fund_code,version:info.version, type: info.file_type}
                    get_pdf_file.showStrongHint(requestData,info,_pageId)
                }else{
                    layerUtils.iAlert(data.error_info);
                }
            })
        },
        
        //判断是否展示PDF
        showStrongHint:(requestData,info,_pageId)=>{
            let new_info = info
            new_info.file_url = global.oss_url + new_info.file_url
            get_pdf_file.ExhibitionPdf(new_info,_pageId)
        },
        //清除定时器
        clearTime:()=>{
            if(new_setInterval)clearInterval(new_setInterval)
        },
        //进行渲染PDF
        ExhibitionPdf:(data,_pageId)=>{
            //阅读并同意（4）秒
            let new_time = data.pop_time?data.pop_time:5
            let setDom = $(_pageId+" .iframe_pdf_bottom .vant_button") //操作按钮的dom元素
            $(_pageId+ ' .iframe_pdf_header span').text(data.file_title)
            $(_pageId+ ' .van-overlay').show()
            setDom.addClass("disable");
            setDom.attr('getServer',1);
            let tip = '阅读并同意（'+ new_time +'）秒'
            setDom.text(tip)
            new_setInterval = setInterval(() => {
                // console.log(new_time)
                if(new_time > 0){
                    new_time--
                    let text = '阅读并同意（'+ new_time +'）秒'
                    setDom.text(text)
                }else{
                    get_pdf_file.clearTime()
                    setDom.attr('getServer',2);
                    setDom.removeClass("disable");
                    setDom.text('我已阅读并同意')
                    new_time = data.pop_time
                    return 
                }
            }, 1000);
            pdfh5 = new Pdfh5(_pageId+' #iframe_pdf', {
                pdfurl:data.file_url,
                loadingBar:true
            });
            pdfh5.on("complete",  (status, msg, time)=> {
                // console.log('渲染完成')
            })
            pdfh5.on("error", function (time) {
                get_pdf_file.clearTime()
                layerUtils.iAlert('获取PDF文件失败，请检查文件');
                // clearInterval(new_setInterval)
            })
        }
    };
    
    // 暴露对外的接口
    module.exports = get_pdf_file;
})
