//开始定投结果页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#combProduct_castingResult ";
        _pageUrl = "combProduct/castingResult";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var info;
    function init() {
        $(_pageId + " #remark").hide();
        //页面埋点初始化
        tools.initPagePointData();
        let userInfo = ut.getUserInf();
        info = appUtils.getPageParam();
        $(_pageId + " .prod_sname").text(info.prod_sname);
        $(_pageId + " .czje_Amt").text(info.czje_Amt);
        $(_pageId + " .pageTitle").text(info.pageTitle ? info.pageTitle : '我的财富成长计划');
        $(_pageId + " .czje_Amt_Name").text(info.plan_type == '4' ? '存入金额' : '首次投入');
        $(_pageId + " .prod_sname_title").html(info.plan_type ? '名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;称' : '产品投向');
        $(_pageId + " .header .text_gray").html(info.plan_type ? '攒钱结果' : '开启结果');
        $(_pageId + " .subtitle").html(info.plan_type ? '攒钱成功' : '计划开启成功');
        if(info.remark){
            $(_pageId + " .remark").text(info.remark);
            $(_pageId + " #remark").show();
        }
        info.czje_InvestAmt = info.czje_InvestAmt;
        if(!info.czje_InvestAmt || info.czje_InvestAmt == '' || info.czje_InvestAmt == '--' || info.czje_InvestAmt == '0.00' || info.czje_InvestAmt == '0'){
            return $(_pageId + " .czje_InvestAmt_show").hide();
        }
        $(_pageId + " .czje_InvestAmt").text(info.czje_InvestAmt);
        $(_pageId + " .nextdate").text(info.nextdate);
        $(_pageId + " .payMethodName").text(info.payMethodName);
        $(_pageId + " .czje_InvestAmt_show").show();
    }

    function bindPageEvent() {
        //跳转持仓
        appUtils.bindEvent($(_pageId + " #goHome"), function () {
            appUtils.setSStorageInfo("routerList", ["login/userIndexs", "account/myAccount", "template/positionList"]);
            appUtils.pageInit(_pageUrl, "template/positionList");
        });
    }
    function pageBack() {
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
    	$(_pageId + " .czje_InvestAmt_show").hide();
        $(_pageId + " #remark").hide();
    }
    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
