// 源晖 基金信息
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        _pageId = "#yuanhui_fundInfo ";
    var productInfo;
    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " .prodName").html(productInfo.prod_name);
        $(_pageId + " .fund_code").html(productInfo.fund_code);
        $(_pageId + " .establishDate").html(tools.ftime(productInfo.establish_date));//成立日期
        $(_pageId + " .riskDesc").html(productInfo.risk_level_desc);
        $(_pageId + " .mgrcompName").html(productInfo.mgrcomp_name);//管理人
        $(_pageId + " .mgrcompSname").html(productInfo.mgrcomp_sname);
        $(_pageId + " .trusteeName").html(productInfo.trustee_name);//托管人
        $(_pageId + " .fundManagers").html(productInfo.fund_managers);
        $(_pageId + " .fundTypeName").html(productInfo.fund_type_name);//基金类型
        $(_pageId + " .yhProdTermDesc").html(productInfo.yh_prod_term_desc);//产品期限说明
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }


    function destroy() {
        $(_pageId + " .prodName").text("");
        $(_pageId + " .fund_code").text("");
        $(_pageId + " .establishDate").text("");
        $(_pageId + " .riskDesc").text("");
        $(_pageId + " .issuingScale").text("");
        $(_pageId + " .mgrcompName").text("");
        $(_pageId + " .mgrcompSname").text("");
        $(_pageId + " .trusteeName").text("");
        $(_pageId + " .fundManagers").text("");
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thsurvey = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thsurvey;
});
