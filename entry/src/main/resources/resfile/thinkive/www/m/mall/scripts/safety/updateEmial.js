// 邮箱更新
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#safety_updateEmial";
    var tools = require("../common/tools");
    var param = {};
    var ut = require("../common/userUtil");
    var userInfo;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        userInfo = ut.getUserInf();
        initData();
    }

    function initData() {
        $(_pageId + " #old_emial").val("");
        $(_pageId + " #new_emial").val("");
        if (userInfo.email) {
            $(_pageId + " #old_emial").val(userInfo.email);
        }
        $(_pageId + " #userInfo").html("您好！您正在为账户 " + userInfo.mobile + " 修改电子邮箱。");
    }

    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        //点击确认
        appUtils.bindEvent($(_pageId + " #submit"), function () {
            var oldEmial = $(_pageId + " #old_emial").val();
            var newEmial = $(_pageId + " #new_emial").val();
            if (newEmial == "") {
                layerUtils.iMsg(-1, "新邮箱不能为空");
                return;
            }
            if (!validatorUtil.isEmail(newEmial)) {
                layerUtils.iMsg(-1, "新邮箱格式输入不正确");
                return;
            }
            if (newEmial == oldEmial) {
                layerUtils.iMsg(-1, "新邮箱不能与原邮箱一致");
                return;
            }
            updateEmials(newEmial);
        });
    }

    function updateEmials(email) {
        param.email = email;
        service.reqFun101030(param, function (data) {
            if (data.error_no == "0") {
                layerUtils.iMsg(-1, "邮箱修改成功");
                userInfo.email = email;
                ut.saveUserInf(userInfo);
                appUtils.pageBack();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        });
    }

    function destroy() {

        tools.recordEventData('4','destroy','页面销毁');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var updateEmial = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = updateEmial;
});
