<div class="page" id="safety_changecardConfirm" data-pageTitle="换卡确认" data-refresh="true">
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">换卡确认</h1>
			</div>
		</header>
		<article class="bg_blue">
			<div class="bank_form">
				<div class="input_box">
					<div class="ui field text">
						<label class="ui label">原银行卡号</label>
						<input id="bankCard" maxlength="19" type="tel" readonly="readonly" class="ui input" />
					</div>
					<div class="ui field text">
					<div class="pop_view" id="pop_view"  style="visibility:hidden;">
						<p id="big_show_bank"></p>
					</div>
						<label class="ui label">新银行卡号</label><input id="newbankCard" readonly="readonly" maxlength="19" type="tel" class="ui input" />
					</div>
					<!-- <div class="ui field text">
						<label class="ui label">银行名称</label>
						<div class="ui dropdown" id="bankname" style="margin-left:0.1rem;">
							<strong id="chooseBank"></strong>
						</div>
						<input style="background:white;" id="bankname"  readonly="readonly"  unselectable="on"  type="text" class="ui input" />
					</div> -->
				</div>
				<div class="place" style="display:none">
					<p>银行卡单笔限额：<span id="oneMoney" style="color: #000;font-weight: bold;"></span>  单日限额：<span id="drxe" style="color: #000;font-weight: bold;"></span></p>
				</div>
				<div class="input_box">
					<div class="ui field text">
						<label class="ui label">预留手机号</label><input id="yhmPhone" maxlength="11" type="tel" readonly="readonly"   placeholder="" class="ui input" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2" id="yzmBox">
						<label class="short_label2" style="width:0.9rem;padding-right:0.1rem;">验证码</label>
						<input custom_keybord="0" id="verificationCode" type="tel" maxlength="6" class="ui input code_input"
							   placeholder=""/>
						<a id="getYzm" data-state="true">获取验证码</a>
					</div>
				</div>
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="weihao" style="display:none"></dd>
						<dd>
						</dd>
					</dl>
				</div>
				<!-- 语音验证码 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
					</dl>
				</div>
				<!-- 语音验证码 -->
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="next">下一步</a>
				</div>
			</div>
		</article>
	</section>
</div>
