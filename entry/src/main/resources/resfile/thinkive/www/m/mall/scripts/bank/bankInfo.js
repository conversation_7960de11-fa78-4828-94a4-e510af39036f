// 银行限额列表
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#bank_bankInfo ";

    function init() {
       
        var productInfo = appUtils.getSStorageInfo("productInfo");
        var bank_channel_code = productInfo.bank_channel_code;
        BankCardInformation(bank_channel_code);
    }

    //查出支持的银行卡
    function BankCardInformation(bank_channel_code) {
        service.reqFun151108({bank_channel_code: bank_channel_code}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            var str = "";
            if (error_no == "0") {
                for (var i = 0; i < data.results.length; i++) {
                    var bank_name = data.results[i].bank_name;
                    var day_limit = data.results[i].day_limit;
                    var single_limit = data.results[i].single_limit;
                    var remark = data.results[i].remark;
                    if (day_limit < 0) {
                        day_limit = "不限";
                    }
                    if (single_limit < 0) {
                        single_limit = "不限";
                    }
                    if (day_limit >= 10000) {
                        day_limit = day_limit / 10000 + "万";
                    }
                    if (single_limit >= 10000) {
                        single_limit = single_limit / 10000 + "万";
                    }

                    str += "<tr><td>" + bank_name + "</td><td>" + single_limit + "</td><td>" + day_limit + "</td><td class='add'>" + remark + "</td></tr>";

                }
                $(_pageId + " #mainInfo").html(str);
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    //绑定事件
    function bindPageEvent() {
        //后退
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }


    function destroy() {
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
