<div class="page" id="safety_kjyanzheng" data-pageTitle="快捷换卡" data-refresh="true">
	<div class="pop_layer4 pop_layer"></div>
	<div class="card_rules"  >
		<div class="rules_box slideup in">
			<h5>快捷换卡规则</h5>
			<div class="rules_list">
				<strong>1. 客户总资产为零，且无未确认的交易（在途资金、单边账等）的情况下才可进行快捷换卡。</strong>
				<strong>2. 客户需输入平台交易密码和手机验证码，填写新卡卡号和新卡银行预留手机号，系统验签通过后，即完成快捷换卡。</strong>
			</div>
			<p class="risk_tips">温馨提示：换卡成功后，您的交易将通过变更后的新签约银行卡办理，请知晓。</p>
			<div class="grid_02">
				<a href="javascript:void(0)" class="ui button block rounded btn_01">我知道了</a>
			</div>
		</div>
	</div>
	<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a herf="javascript:void(0)" class="icon_back icon_gray"><span>返回</span></a>
				<h1 class="text_gray text-center">快捷换卡</h1>
				<a href="javascript:void(0)" class="right_btn redcol">换卡必读</a>
			</div>
		</header>
	 	<article class="bg_blue">
			<div class="bank_form">
				<h3>身份验证</h3>
				<div class="input_box">
					<div class="ui field text" id="tradeNum11">
						 <label class="ui label">交易密码</label>
						 <input type="password" id="tradeNum" maxlength="6" readonly="readonly"  placeholder=" " class="ui input" style="display:none"/>
						 <div class="simulate_input no_border">
							<span class="unable" id="tradeNum1" style="line-height:44px;">请输入交易密碼</span>
						 </div>
					</div>
					<div class="ui field text" id="yzmBox">
						<label class="ui label">验证码</label><input  type="tel"  maxlength="6" id="verificationCode" placeholder=" "  class="ui input" />
						<a href="javascript:void(0);" class="get_code" id="getYzm">获取验证码</a>
					</div>
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="weihao"   style="display:none" ><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->
					<div class="finance_det recharge_det">
						<dl class="bank_limit">
							<dt></dt>
							<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
						</dl>
					</div>
					<!-- 语音验证码 -->

				</div>
				<!-- <div class="rule_check">
					<span id="xuanzeqi"><i class="active"></i>我已阅读并同意签署</span> <span id="ckagreement"></span><a href="javascript:void(0);">《业务说明书》</a><a href="javascript:void(0);">《定向委托管理协议》</a><a href="javascript:void(0);">《资金结算协议》</a>
				</div> -->
				<div class="btn">
					<a href="javascript:void(0);" class="ui button block rounded" id="next">下一步</a>
				</div>
			</div>
		</article>
	</section>
</div>
