/*
    模拟光标
*/
.cursor-bink {
	display: inline-block;
	width: 1px;
	position: absolute;
	left: 0.5rem;
	top: 0.14rem;
	height: 0.16rem;
	animation: blink 1s infinite steps(1, start);
}
#safety_updateUserPassword .cursor-bink {
	left: 1rem;
}
#safety_userPassword .cursor-bink {
	left: 1rem;
}
#login_setPassword .cursor-bink {
	left: 1rem;
}
#login_userRegistered .cursor-bink {
	left: 1rem;
}
@keyframes blink {
	0%,
	100% {
		background-color: #000;
		color: #aaa;
	}
	50% {
		background-color: #fff;
		color: #000;
	}
}
.placeholderPsd {
	display: flex;
	align-items: center;
	padding-left: 0.1rem;
	color: #aaa;
	font-size: 14px;
	float: left;
	font-family: ST<PERSON>eiti STXihei, Microsoft JhengHei, Microsoft YaHei, Arial !important;
}
.placeholderYzm {
	display: flex;
	align-items: center;
	padding-left: 0.1rem;
	color: #aaa;
	font-size: 14px;
	float: left;
}
.font_normal{
	font-style: normal
}
.inline_block {
	display: inline-block;
}
.overflow_hidden {
	overflow: hidden !important;
}
.position_relative {
	position: relative;
}
.z_index_1000 {
	z-index: 1000;
}
.line_hight28{
	line-height:0.28rem;
}
.bg_fff {
	background: #fff;
}
.bg_f2f {
	background: #f2f6f8;
}
.bg_ccc{
	background: #ccc !important;
	border:none !important;
}
.color_70 {
	color: rgb(70, 123, 178);
}
.color_0770ff {
	color: #0770ff;
}
.high_color{
	color:#DE9C6C;
}
.color_51 {
	color: #515151;
}
.color_ccc {
	color: #ccc !important;
}
.color_aaa {
	color: #aaa !important;
}
.color_fff {
	color: #fff;
}
.color_777{
	color: #777;
}
.border_top {
	border-top: 1px solid #ddd;
}
.text-indent {
	text-indent: 2em;
}
.color_000 {
	color: #000 !important;
}
.m_margin_left {
	margin-left: 0;
}
.m_margin_left01 {
	margin-left: 0.1rem;
}
.m_margin_left02 {
	margin-left: 0.2rem;
}
.m_padding_leftRight_01 {
	padding: 0 0.1rem;
}
.m_padding_left {
	padding-left: 0 !important;
}
.m_padding_left_0_6 {
	padding-left: 0.06rem;
}
.m_padding_left_4 {
	padding-left: 0.4rem;
}
.m_padding_right_2{
	padding-right: 0.2rem;
}
.m_padding_top_15 {
	padding-top: 0.15rem;
}
.m_font_size30 {
	font-size: 30px;
}
.m_font_size24 {
	font-size: 24px;
}
.m_font_size22 {
	font-size: 22px;
}
.m_font_size20 {
	font-size: 20px;
}
.m_font_size18 {
	font-size: 18px;
}
.m_font_size16 {
	font-size: 16px !important;
}
.m_font_size13 {
	font-size: 0.13rem;
}
.m_font_size14 {
	font-size: 14px;
}
.m_font_size12 {
	font-size: 12px;
}
.m_font_size10 {
	font-size: 0.1rem;
}
.rem_width_15 {
	width: 0.15rem;
}
.rem_height_15 {
	height: 0.15rem;
}
.rem_width_100 {
	width: 1rem;
}
.m_height_100 {
	height: 100%;
}
.m_width_100 {
	width: 100% !important;
}
.m_width_60 {
	width: 60% !important;
}
.m_width_61 {
	width: 61% !important;
}

.m_width_70 {
	width: 70% !important;
}
.m_width_80 {
	width: 80% !important;
}
.m_width_50 {
	width: 50% !important;
}
#m_width_50 {
	width: 50% !important;
}
.m_width_45 {
	width: 45% !important;
}
.m_width_40 {
	width: 40% !important;
}
.m_width_30 {
	width: 30% !important;
}
.m_width_33 {
	width: 33.3% !important;
}
.m_width_25 {
	width: 25% !important;
}
.m_width_20 {
	width: 20% !important;
}
.m_width_35 {
	width: 35% !important;
}
.m_width_20rem {
	width: 2rem !important;
}
.m_text_393939{
	color: #393939;
}
.m_text_darkgray {
	color: #333 !important;
}
.m_text_darkgray666 {
	color: #666 !important;
}
.m_text_999 {
	color: #999;
}
.m_bg_aaa {
	background: #aaa !important;
}
.bg-white {
	background-color: #fff;
}
.m_golden {
	color: rgb(162, 133, 65);
}
.indexRemarkBg{
	background: linear-gradient(180deg,#ffffff 50%,#feeeee 50%);
	color:#333;
	letter-spacing: 0.5px;
}
#login_userIndexs .high .indexRemarkBg{
	/* background: #eee;
	border-radius: 4px;
	font-weight: 400;
	font-size: 12px;
	color: #666666;
	height:26px;
	line-height: 26px;
	width:100%;
	padding-left: 0.1rem;
	margin-top: 0.1rem;
	padding:0.05rem; */
}
.m_text_red {
	color: #e5443c !important;
	border-color: #e5443c !important;
}
.bg_red {
	background: linear-gradient(
		to right,
		#faa96a,
		#eb7253,
		#ea5a51,
		#e94951,
		#ea4354
	);
}
.bg_green {
	background: linear-gradient(
		to right,
		#76d473,
		#76d474,
		#77d058,
		#78d046,
		#7bce40
	);
}
.m_text_color_778590 {
	color: #778590;
}
.m_text_green {
	color: #00c35e !important;
	border-color: #00c35e !important;
}
.m_paddingLeft_30 {
	padding-left: 0.3rem !important;
}
.m_paddingLeft_10 {
	padding-left: 0.1rem !important;
}
.m_center {
	text-align: center;
}
.m_radius {
	border-radius: 0.1rem;
}
/*
    协议
*/
.m_agreement_color {
	color: #319ef2 !important;
	/* font-family: STHeiti STXihei, Microsoft JhengHei, Microsoft YaHei, Arial; */
	vertical-align: middle;
}
.m_color_777 {
	color: #777 !important;
}
.m_text_gray {
	color: #999 !important;
}
.m_marginTop_10 {
	margin-top: 0.1rem !important;
}
.m_marginTop_15{
	margin-top: 0.15rem !important;
}
.m_marginTop_20 {
	margin-top: 0.2rem !important;
}
.m_marginTop_30 {
	margin-top: 0.3rem !important;
}
.m_marginTop_02 {
	margin-top: 0.02rem !important;
}
.m_paddingTop_0 {
	padding-top: 0 !important;
}
.m_paddingTop_10 {
	padding-top: 0.1rem !important;
}
.m_paddingTop_30 {
	padding-top: 0.3rem !important;
}
.m_paddingTop_20 {
	padding-top: 0.2rem !important;
}
.m_paddingTop_300 {
	padding-top: 0.03rem !important;
}
.m_marginBottom_10 {
	margin-bottom: 0.06rem;
}
.m_marginBottom_20 {
	margin-bottom: 0.2rem;
}
.m_marginBottom_30 {
	margin-bottom: 0.3rem;
}
.m_paddingRight_10 {
	padding-right: 0.2rem;
}
.m_paddingRight_16 {
	padding-right: 0.16rem;
}
.m_padding_0 {
	padding: 0;
}
.m_padding_10_10 {
	padding: 0.1rem;
}
.m_padding_10_05 {
	padding: 0.05rem;
}
.m_padding_20_20 {
	padding: 0.2rem;
}
.m_padding_10 {
	padding: 0.1rem 0.2rem;
}
.m_topMargin_01 {
	margin-top: -0.05rem;
}
.m_padding_3 {
	padding: 0.03rem 0.2rem;
}
.m_paddingBottom_10 {
	padding-bottom: 0.1rem;
}
.m_paddingBottom_30 {
	padding-bottom: 0.3rem;
}
.m_border_bottom_gray {
	border-bottom: 1px solid #e6e6e6;
}
.m_border_bottom_aaa {
	border-bottom: 1px solid #aaa;
}
.m_border_bottom_D2E3E8 {
	border-bottom: 1px solid #d2e3e8;
}
.m_border_top_gray {
	border-top: 1px solid #e6e6e6;
}
.m_text_left {
	text-align: left;
}
.ellipsis {
	/* width:100%; */
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.m_title_right {
	float: right;
}
.m_inline_block {
	display: inline-block;
	width: 100%;
}
.m_text_right {
	text-align: right !important;
}
.m_text_center {
	text-align: center !important;
}
.m_relative {
	position: relative;
}
.m_absolute {
	position: absolute;
}

.m_bold {
	font-weight: bold;
}
.m_bold_500 {
	font-weight: 500;
}
.m_italic {
	font-style: italic;
}
.m_right_icon:after {
	content: "";
	width: 0.08rem;
	height: 0.08rem;
	display: inline-block;
	border-left: 2px solid #3f4e59;
	border-bottom: 2px solid #3f4e59;
	transform: rotate(225deg);
	-webkit-transform: rotate(225deg);
	float: right;
	margin-top: 0.06rem;
}
#template_positionList .new_right_icon .m_right_icon:after {
	margin-top: 0;
	float: none;
	position: absolute;
	right: 0.1rem;
}
/* 提示，小问号 */
.m_tips {
	width: 0.2rem;
}
/*高端产品详情*/
#template_heighEndProduct article,
#template_holdHeighDetail article,
#template_positionList article {
	padding-top: 0;
}
#m_product .red {
	color: #e5443c;
}

#m_product .green {
	color: #008000;
}
#m_product {
	padding-top: 0.44rem;
	padding-bottom: 0.86rem;
}
#m_product .product_desc {
	padding: 0.2rem 0.1rem 0.1rem;
	background: #ffffff;
}
#m_product .product_head .product_name {
	font-size: 16px;
	color: #333;
}
#m_product .product_head .product_code {
	font-size: 12px;
	color: #999;
}

#m_product .annual_details {
	position: relative;
	width: 0.24rem;
}
#m_product .transferable span {
	font-size: 0.12rem;
	color: #e5443c;
	border: 1px solid #e5443c;
	border-radius: 0.15rem;
	padding: 0 0.04rem;
	margin: 0 0.04rem;
}
.border_radius {
	border-radius: 0.1rem;
}
#m_product .product_top {
	padding: 0.12rem 0 0.12rem;
	overflow: hidden;
	border-bottom: 1px solid #e6e6e6;
}

#m_product .product_top > div:first-child {
	padding: 0;
}
#m_product .product_top > div {
	width: 33.333333%;
	float: left;
	color: #999;
	font-size: 12px;
	position: relative;
	padding-left: 0.1rem;
	/* border-right: 1px solid #e6e6e6; */
	line-height: 1.2;
}
#m_product .middle {
	border-left: 1px solid #e6e6e6;
	border-right: 1px solid #e6e6e6;
}
#m_product .product_top > div:last-child {
	border: none;
}
#m_product .product_tip {
	padding: 0.04rem 0.2rem 0.1rem 0.2rem;
	background-color: #fff;
	overflow: hidden;
	margin-bottom: 0.1rem;
}
#m_product .product_tip > div {
	float: left;
	margin-right: 0.1rem;
	border: 1px solid;
	padding: 0.02rem 0.04rem;
	line-height: 1;
}
#m_product .product_tip .fund_type_name {
	border-color: #999999;
	color: #999999;
	font-size: 0.12rem;
}
#m_product .title {
	background: #ffffff;
	padding: 0.08rem 0.2rem;
	border-bottom: 1px solid #e6e6e6;
	color: #333;
	font-size: 14px;
}

#m_product .arrows_right {
	background-image: url(../images/arrows_right.png);
	background-size: 100% 100%;
	float: right;
	width: 0.16rem;
	height: 0.16rem;
	margin-top: 0.05rem;
	margin-left: 0.05rem;
}
#m_product .warm-tip {
	text-align: center;
	color: #999;
	font-size: 12px;
	padding: 0.1rem 0.2rem 0;
}
#m_product .thirty_tips {
	width: 0.25rem;
}

/*高端持仓详情页*/

#m_product .asset_top {
	background: #fff;
	overflow: hidden;
	padding-left: 0.2rem;
	padding-right: 0.2rem;
	padding-bottom: 0.1rem;
	position: relative;
}
#m_product .asset_bonus {
	display: flex;
}
#m_product .asset_bonus > div {
	-webkit-box-flex: 1;
	-moz-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
}
#m_product .product_footer {
	position: fixed;
	width: 100%;
	height: 0.5rem;
	line-height: 0.5rem;
	bottom: 0;
	z-index: 999;
}

#m_product .right_icon:after {
	content: "";
	width: 0.08rem;
	height: 0.08rem;
	border-left: 2px solid #999;
	border-bottom: 2px solid #999;
	transform: rotate(225deg);
	-webkit-transform: rotate(225deg);
	-moz-transform: rotate(225deg);
	position: absolute;
	right: 0;
	top: 50%;
	margin-top: -0.04rem;
}
#m_product .product_footer .sell.no_active {
	color: #e2e2e2;
}
#m_product .product_footer .fixedInvestment {
	background: #fff;
	color: #e5443c;
	border-left: 1px solid #eee;
}
#m_product .product_footer .sell {
	float: left;
	width: 50%;
	background: #fff;
	color: #e5443c;
}
#m_product .product_footer .hangingOrder.no_active {
	color: #e2e2e2;
}

#m_product .product_footer .hangingOrder {
	float: left;
	width: 50%;
	background: #fff;
	color: #e5443c;
}
.m_list_color {
	color: #404257 !important;
}
.m_main_color {
	color: #546374;
}
#m_product .product_footer .redeem.no_active {
	background: #e2e2e2;
	color: #fff;
}
#m_product .product_footer .redeem {
	float: right;
	width: 50%;
	background: #e5443c;
	color: #fff;
}
#m_product .fund_tips {
	font-size: 10px;
	position: absolute;
	bottom: 0.02rem;
}
#m_product .image_tips {
	position: absolute;
	width: 0.2rem;
	margin-left: -0.04rem;
	bottom: 0.01rem;
}
#template_holdHeighDetail .public_top_middle {
	background: #fff;
	padding: 0.1rem 0.2rem;
	border-bottom: none;
}
/* 持仓列表 */

.new_display_none {
	display: none !important;
}
/* 盒子布局公共样式 */
.main_flxe {
	display: flex;
}
.flex {
	display: flex;
	justify-content: space-between;
}
/* 绝对居中 */
.flex_center {
	justify-content: center;
	align-items: center;
}
/* 垂直居中 */
.vertical_center {
	align-items: center;
}
/* 水平居中 */
.level_center {
	justify-content: center;
}
/*
    垂直排列
*/
.vertical_line {
	flex-direction: column;
}
.flex_end {
	align-items: flex-end;
}
/*
    超出后自动换行
*/
.flex_wrap {
	flex-wrap: wrap;
}
/*
    平均占位
*/
.flex_1 {
	flex: 1;
}
.wrap {
	flex-wrap: wrap;
}
.display_none {
	display: none;
}
/*
    虚线
*/
.dottedLine {
	border-bottom: 1px dashed #ddd;
}
.solid {
	border-top: 1px solid #ddd;
}
.solid_bottom {
	border-bottom: 1px solid #ddd;
}
/*
    列表相关
*/
.template_item {
}
.template_box {
	padding: 0.1rem 0.1rem;
	border: 0.01rem solid #ccc;
	background: #fff;
	border-radius: 0.05rem;
	margin-bottom: 0.1rem;
}

.template_box .template_right {
	width: 0.6rem;
	display: flex;
}
.template_box .template_left {
	display: flex;
	flex: 1;
}
.template_box .template_left li {
	padding-right: 0.05rem;
}
.template_box .template_right li {
	/* border: 0.01rem solid #e5443c;
    color: #e5443c;
    border-radius: 50%;
    height: 0.60rem;
    width: 0.60rem;
    box-shadow: 0 0 0.1rem #d2e3e8;
    -webkit-animation-duration: 0ms;
    animation-duration: 0ms; */
	border: 1px solid #e5443c;
	border-radius: 50%;
	width: 0.6rem;
	height: 0.6rem;
	color: #e5443c;
}

/*持仓详情的持仓明细*/
.hold_card:last-child {
	border-bottom: none;
}
/* #myTransfer_hangingOrder #product{
    padding-top:0;
    padding-bottom:0;
} */
.invertedTriangle {
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 10px solid #e5443c;
	display: inline-flex;
}
.regularTriangle {
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-bottom: 10px solid #e5443c;
	display: inline-flex;
}

/* 公募整合 7天 */
.public_top {
	padding: 0.2rem 0.1rem 0.1rem;
	background: #fff;
}
.public_top_middle {
	padding: 0.1rem 0;
	border-bottom: 1px solid #e6e6e6;
}
.public_bottom {
	padding-left: 0.16rem;
}
.public_bottom .risk_level_name {
	border-color: #008000;
	color: #008000;
}
.public_bottom .fund_type_name {
	border-color: #999999;
	color: #999999;
}
.public_bottom .public_bottom_first span {
	font-size: 0.12rem;
	margin-right: 0.1rem;
	border: 1px solid;
	padding: 0.02rem 0.04rem;
}
#template_publicOfferingDetail .chart,
#template_heighEndProduct .chart {
	background: #fff;
	display: flex;
	justify-content: space-between;
	line-height: 0.4rem;
	border-bottom: 0.01rem solid #e6e6e6;
	padding-left: 0.2rem;
	margin-top: 0.1rem;
	font-size: 0.16rem;
	color: #333;
}
.remarkTitle7 {
	background: #ffffff;
	padding: 0.1rem 0.2rem;
	border-bottom: 1px solid #e6e6e6;
	color: #333;
}
.right_icon7 {
	width: 0.15rem;
	height: 0.15rem;
}
#template_publicOfferingDetail .product_archives {
	background: #ffffff;
	padding: 0.1rem 0.2rem;
	border-bottom: 1px solid #e6e6e6;
	color: #333;
	font-size: 16px;
}
#template_publicOfferingDetail .item_box .title,
#template_publicProdFiles .item_box .title {
	background: #ffffff;
	padding: 0.08rem 0.2rem;
	border-bottom: 1px solid #e6e6e6;
	color: #333;
	font-size: 14px;
}
#template_publicOfferingDetail .item_box .title .title_right,
#combProduct_combProdDetail .item_box .title .title_right,
#scene_combProdDetail .item_box .title .title_right,
#template_publicProdFiles .item_box .title .title_right {
	font-size: 14px;
	color: #999;
	float: right;
}
#template_publicOfferingDetail .warm-tip,
#combProduct_combProdDetail .warm-tip,
#scene_combProdDetail .warm-tip,
#template_decentralizedPurchasing .warm-tip {
	color: #999;
	font-size: 12px;
	padding: 0.1rem 0.2rem 0;
}
#combProduct_combProdDetail .trend-chart,
#scene_combProdDetail .trend-chart,
#template_publicOfferingDetail .trend-chart,
#template_heighEndProduct .trend-chart,
#template_decentralizedPurchasing .trend-chart {
	background: #fff;
	border-bottom: 0.01rem solid #e6e6e6;
	padding-left: 0.2rem;
	padding: 0.06rem;
	margin-top: 0.1rem;
	font-size: 0.16rem;
	color: #333;
}
#combProduct_combProdDetail .trend-chart span:nth-child(1),
#scene_combProdDetail .trend-chart span:nth-child(1),
#template_publicOfferingDetail .trend-chart span:nth-child(1),
#template_heighEndProduct .trend-chart span:nth-child(1),
#template_decentralizedPurchasing .trend-chart span:nth-child(1) {
	padding: 0.04rem 0.08rem;
	border-right: 2px solid #e6e6e6;
}
#combProduct_combProdDetail .trend-chart span,
#scene_combProdDetail .trend-chart span,
#template_publicOfferingDetail .trend-chart span,
#template_heighEndProduct .trend-chart span,
#template_decentralizedPurchasing .trend-chart span {
	padding: 0.04rem 0.08rem;
}
/*******产品营销页详情********/
#m_product .marketing_desc {
	padding: 0.12rem;
}
#m_product .marketing_content {
	margin-top: 0.2rem;
	margin-bottom: 0.2rem;
	padding: 0.2rem;
	/* border: 2px solid #F8F8F8; */
	border-radius: 15px;
	box-shadow: 0 0 10px #e0e0e0;
}
#m_product .product_bottom_img {
	background-size: 100%;
	border-radius: 15px;
	height: 2rem;
	box-shadow: 0 0 10px #e0e0e0;
	width: 100%;
	background-repeat: no-repeat;
}
#m_product .product_head_img {
	background: #fd852f;
	border-radius: 15px;
}
#m_product .product_head_img img {
	width: 100%;
}
#m_product .prod_name {
	margin-bottom: 0.1rem;
}
#m_product .prod_name span {
	font-size: 18px;
	font-weight: bold;
}
#m_product .marketing_content .prod_info {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
#m_product .marketing_content .prod_info_item {
	text-align: center;
}
#m_product .marketing_content .prod_info_item span {
	font-weight: bold;
	font-size: 18px;
}
#m_product .marketing_content .prod_stop_buy {
	border-top: 1px dashed #d9d9d9;
	margin-top: 0.15rem;
	height: 40px;
	line-height: 55px;
	text-align: center;
}
#m_product .marketing_content .prod_stop_buy-info {
	height: 40px;
	line-height: 60px;
	text-align: center;
}
#m_product .marketing_content .prod_tip {
	background: #e33930;
	font-size: 12px;
	color: #fff;
	padding: rem;
	border-radius: 0.06rem;
	padding: 0.02rem 0.04rem;
	margin-left: 0.06rem;
	line-height: 16px;
	display: inline-block;
	position: relative;
	top: -2px;
}
#m_product .chartContent {
	/* padding: 0 0.15rem 0.1rem; */
	background: #fff;
}
#m_product #chartContainer {
	height: 2.1rem;
}
#m_product .spliceDate li {
	width: 20%;
}
#template_publicOfferingDetail .arrows_right,
#combProduct_combProdDetail .arrows_right,
#scene_combProdDetail .arrows_right,
#template_publicProdFiles .arrows_right {
	background-image: url(../images/arrows_right.png);
	background-size: 100% 100%;
	float: right;
	width: 0.16rem;
	height: 0.16rem;
	margin-top: 0.05rem;
	margin-left: 0.05rem;
}

/*大集合模板*/
#template_publicOfferingDetail .deadline_box {
	background: #fff;
	margin: 0.1rem 0;
	padding: 0.2rem;
}
#template_publicOfferingDetail .deadline_box .item {
	display: flex;
	justify-content: space-between;
}
#template_publicOfferingDetail .deadline_box .item .item_title {
	color: #333;
	font-size: 14px;
	width: 35%;
}
#template_publicOfferingDetail .deadline_box .item .item_content {
	text-align: left;
	width: 65%;
	padding-left: 0.1rem;
	color: #999;
}
#template_publicOfferingDetail .deadline_box .item .vertical_layout span {
	display: block;
	width: 100%;
}
#template_publicOfferingDetail .infro_box {
	padding: 0 0 0.2rem;
	margin-bottom: 0.1rem;
	background: #fff;
}
#template_publicOfferingDetail .infro_box .item {
	display: flex;
	justify-content: space-between;
	padding: 0.1rem 0.2rem 0;
	background: #fff;
}
#template_publicOfferingDetail .infro_box .item .item_left {
	color: #999;
	width: 35%;
}
#template_publicOfferingDetail .infro_box .item .item_right {
	color: #333;
	width: 65%;
	text-align: left;
	padding-left: 0.1rem;
}
#template_publicOfferingDetail .rule_box {
	position: relative;
	padding: 0;
}
#template_publicOfferingDetail .public_top_middle > li {
	float: left;
	color: #999;
	font-size: 12px;
	position: relative;
	border-right: 1px solid #e6e6e6;
}
#template_publicOfferingDetail .public_top_middle > li:last-child {
	border-right: none;
}

#template_publicOfferingDetail .thirty_tips {
	position: absolute;
	width: 0.24rem;
	margin-left: 0.05rem;
	z-index: 1;
}
#template_publicOfferingDetail .handling_box {
	padding: 0 0 0.2rem;
	margin-bottom: 0.1rem;
	background: #fff;
}
#template_publicOfferingDetail .handling_box .item {
	display: flex;
	justify-content: space-between;
	padding: 0.1rem 0.2rem 0;
	background: #fff;
}

/*  公募持仓详情  */

/* 首页整合CSS */
#login_userIndexs .header_logo,
#yuanhui_userIndexs .header_logo {
	height: 100%;
	background: url(../images/icon_logo.png) no-repeat center center;
	background-size: 100% 80%;
	width: 1.8rem;
}
#login_userIndexs .mail {
	padding: 0rem 0.1rem 0.2rem 0.1rem;
}
#login_userIndexs .mail ul {
	line-height: 0.2rem;
	justify-content: space-between;
	background: rgb(236, 246, 253);
	width: 100%;
	padding: 0.1rem;
	border-radius: 0.1rem;
}
#login_userIndexs .main_img {
	width: 100%;
}
#login_userIndexs .main_message{
	width: 100%;
    background: #fff;
    margin-top: 0.1rem;
    border-radius: 0.1rem;
    padding: 0.1rem;
    font-weight: 600;
    font-size: 16px;
    color: #000;
	display: flex;
    align-items: center;
}
#login_userIndexs .main_message i{
	display: inline-block;
    width: 0.1rem;
    height: 0.1rem;
    background: red;
    border-radius: 50%;
	margin-right: 0.1rem;
}
#login_userIndexs .main_img img {
	width: 0.2rem;
	display: inline-block;
	height: 0.2rem;
}
#login_userIndexs .mail .message_tip {
	flex: 1;
	padding-left: 0.1rem;
}
#login_userIndexs .swiper-pagination {
	/* display: none; */
}
#login_userIndexs .banner_box {
	padding: 0.1rem;
	border-radius: 0.1rem;
	height: 1rem;
}
#login_userIndexs #wrapper_index2 {
	height: 1rem;
	border-radius: 0.1rem;
}
#login_userIndexs #wrapper_index2 img {
	width: 100%;
	height: 1rem;
}
.homePageIndex_list {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	padding: 0.1rem;
}
.homePageIndex_list .menu_item {
	width: 25%;
	display: flex;
	justify-content: center;
}
.homePageIndex_list .menu_item li {
	/*box-shadow:5px 5px  5px 5px #eee;*/
	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 90%;
	/* background: #fff; */
	text-align: center;
	border-radius: 0.05rem;
	padding: 0;
	height: 0.8rem;
	border: 1px solid #dcdcdc;
}
.homePageIndex_list .menu_item li img {
	width: 0.3rem;
	margin: auto;
}
.homePageIndex_list .menu_item li span {
	/* padding-top:0.05rem; */
}
#login_userIndexs {
	background: #fff;
}
.homePageIndex_classificationList {
	padding: 0rem 0.1rem 0.1rem 0.1rem;
}
.homePageIndex_classificationList .classificationList_card {
	background: rgb(247, 239, 229);
	padding: 0.1rem 0.05rem;
	border-radius: 0.05rem;
	margin-bottom: 0.1rem;
}
.classificationList_card .classificationList_card_top {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.classificationList_card_bottoom {
	border-bottom: 1px solid #ccc;
	background: #fff;
	width: 80%;
}
.classificationList_card .classificationList_card_main {
	/* background: #fff; */
	/* padding:0.1rem; */
	/* border-radius: 0.05rem; */
	padding: 0.1rem 0;
	border-bottom: 1px solid #ccc;
}
.classificationList_card .classificationList_card_main:last-child {
	border-bottom: none;
}
.classificationList_card .classificationList_card_main_btn span {
	/*border:1px solid rgb(162,133,65);*/
	border-radius: 50%;
	width: 0.6rem;
	height: 0.6rem;
	/*color:rgb(162,133,65);*/
	border: 1px solid #e5443c;
	color: #e5443c;
}
.indexPageRightIcon {
	color: #546374;
	font-size: 0.12rem;
}
.indexPageRightIcon i {
	display: inline-block;
	width: 0.3rem;
	height: 0.3rem;
	background: url(../images/newIndexPage.png) no-repeat center top;
	background-size: 100% auto;
}
.homePageIndex_mechanism {
	padding: 0rem 0.15rem;
}
.homePageIndex_remark {
	padding: 0.2rem 0.15rem;
	text-align: center;
	font-size: 10px;
}
.homePageIndex_mechanism img {
	width: 1rem;
}
.homePageIndex_mechanism li {
	text-align: center;
}
#login_userIndexs .tip {
	text-align: center;
	padding-top: 0.1rem;
	background: #f5f8fa;
	padding: 0.2rem 0 0.1rem 0;
}
#login_userIndexs .tip .uploadIDCard,
#login_userIndexs .tip .decertification {
	color: blue;
	margin-left: -0.04rem;
}
.homePageIndex_classificationList 
/* 持仓列表样式 */
#template_positionList #product .holdFunds {
	padding: 0.1rem;
}
#template_positionList .finance_pro {
	padding: 0;
}
#template_positionList #product .asset_bottom {
	background: #f2f6f8;
	padding: 0 0.1rem 0.1rem 0.1rem;
}
#template_positionList .holdFunds img {
	height: 0.2rem;
	vertical-align: text-bottom;
	margin-left: 0.05rem;
}
.classificationList_card
	.classificationList_card_main
	li:first-child
	.m_text_999
	span:first-child {
	padding-right: 0.1rem;
}
.sub-classify li .purchase span:first-child {
	padding-right: 0.1rem;
}
.classificationList_card .classificationList_card_main li:first-child img {
	width: 0.4rem;
	height: 0.16rem;
	margin-top: 0.05rem;
}
.classificationList_card .classificationList_card_main .thirty_tips {
	width: 0.4rem;
	height: auto !important;
	margin-top: -0.05rem !important;
}
.classificationList_card .vipTitleImg {
	float: right;
}
.tem_null_data {
	padding: 0.1rem;
	background: #fff;
	height: 0.8rem;
	border-radius: 0.05rem;
	display: flex;
	color: #5463747d;
	font-size: 0.16rem;
}
.footerAll .logoText {
	padding-top: 0.05rem;
	font-size: 0.1rem;
	color: #999;
}
.classificationList_card .main_list {
	border-radius: 0.05rem;
	display: block;
	background: #fff;
	overflow: hidden;
	padding: 0 0.1rem;
	/* margin-top:0.1rem; */
}
/*产品整合交易记录数据*/
#template_transaction .trade_box .result,
#template_cpdTransaction .trade_box .result,
#template_returnsDetailed .trade_box .result,
#combProduct_combTransaction .trade_box .result,
#combProduct_revenueDetails .trade_box .result {
	right: 0;
	float: right;
	padding-right: 0.15rem;
	top: 0;
	bottom: 0;
	margin: auto;
	height: 0.4rem;
	text-align: right;
}
#template_transaction .trade_box .result p:nth-of-type(1),
#template_cpdTransaction .trade_box .result p:nth-of-type(1),
#template_returnsDetailed .trade_box .result p:nth-of-type(1),
#combProduct_combTransaction .trade_box .result p:nth-of-type(1),
#combProduct_revenueDetails .trade_box .result p:nth-of-type(1) {
	padding: 0.1rem 0 0;
	font-size: 16px;
}
#template_transaction .trade_box .result p:nth-of-type(2),
#template_cpdTransaction .trade_box .result p:nth-of-type(2),
#template_returnsDetailed .trade_box .result p:nth-of-type(2),
#combProduct_combTransaction .trade_box .result p:nth-of-type(2),
#combProduct_revenueDetails .trade_box .result p:nth-of-type(2) {
	color: #999;
}
/* 产品营销页 */
#template_publicMarketing #bottom_img {
	width: 100%;
	box-shadow: 0 0 10px #e0e0e0;
	border-radius: 15px;
}
#template_marketing #bottom_img {
	width: 100%;
	box-shadow: 0 0 10px #e0e0e0;
	border-radius: 15px;
}
#template_marketing #bottom_img {
	width: 100%;
	box-shadow: 0 0 10px #e0e0e0;
	border-radius: 15px;
}
/* 消息列表 */
.moreDetails_msg_list {
	width: 100%;
}
.moreDetails_msg_list ul {
	background: #fff;
	padding: 0.1rem;
	margin-top: 0.1rem;
}
.moreDetails_msg_list ul .pic {
	position: relative;
}
.moreDetails_msg_list .tips {
	position: absolute;
	top: 0rem;
	right: -0.1rem;
	background: #e5443c;
	color: #fff;
	font-size: 0.12rem;
	width: 0.2rem;
	height: 0.2rem;
	border-radius: 0.1rem;
	text-align: center;
	line-height: 0.2rem;
}
.moreDetails_msg_list ul .pic img {
	width: 0.4rem;
	height: 0.4rem;
	background-size: 100% auto;
	display: inline-block;
}
.moreDetails_msg_list ul .right_remark {
	flex: 1;
	padding-left: 0.2rem;
}
/* 消息 */
#moreDetails_message .message_none {
	padding: 0.1rem;
	text-align: center;
	font-size: 0.16rem;
}
#moreDetails_message .main {
	/* background:#fff; */
}
#moreDetails_message .message_list .message_card {
	padding: 0.08rem 0.1rem;
	border-bottom: 1px solid #d2e3e8;
	display: flex;
	background: #fff;
}

#moreDetails_message .message_list .message_card i {
	width: 0.1rem;
	height: 0.1rem;
	display: inline-block;
	border-radius: 50%;
}
#moreDetails_message .message_list .red_count {
	background: rgb(250, 12, 30);
}
#moreDetails_message .message_list .message_time {
	display: flex;
	width: 100%;
}
#moreDetails_message .message_list .message_right {
	display: flex;
	width: 100%;
	padding-left: 0.1rem;
	/* padding:0 0.2rem; */
}
#moreDetails_message .message_list .message_icon {
	display: flex;
	width: 100%;
	width: 0.1rem;
	padding-left: 0.1rem;
	position: relative;
	/* padding:0 0.2rem; */
}
#moreDetails_message .message_list .message_title .right_icon {
	/* background-image: url(../images/arrows_right.png);
    background-size: 100% 100%; */
}

#moreDetails_message .message_list .message_title {
	font-size: 0.16rem;
	/* display:flex; */
	/* justify-content: space-between; */
	width: 100%;
	/* font-weight:600; */
}
#moreDetails_messageDetails {
	padding: 0.1rem;
}
#moreDetails_messageDetails .img_details {
	width: 100%;
}
#moreDetails_messageDetails .img_details img {
	width: 100%;
	display: block;
}
#moreDetails_messageDetails .moreDetails_messageDetails .title {
	color: #404257;
	font-size: 0.16rem;
}
#moreDetails_messageDetails .moreDetails_messageDetails .time {
	font-size: 0.12rem;
	color: #47525d;
	margin-bottom: 0.1rem;
}
#moreDetails_messageDetails .chain, #moreDetails_messageDetails .applet, #moreDetails_noticeDetails .chain, #moreDetails_noticeDetails .applet {
	color: #2440b3 !important;
}
.announcement_detail .chain, .announcement_detail .applet {
	color: #2440b3 !important;
}
#login_userRegistered .box {
}
/* 定投 */
#fixedInvestment_startInvestment .chooseCycle,
#combProduct_startCasting .chooseCycle {
	display: flex;
	height: 2.6rem;
	justify-content: space-between;
	background: #fff;
}
#fixedInvestment_startInvestment .chooseCycle .listLeft {
	width: 50%;
	background: #f7f7f7;
}
#fixedInvestment_startInvestment .chooseCycle .listLeft li {
	text-align: center;
	padding: 0.1rem 0;
}
#fixedInvestment_startInvestment .chooseCycle .listLeft li.active {
	background: #fff;
}
#combProduct_startCasting .chooseCycle .listLeft {
	width: 50%;
	background: #f7f7f7;
}
#combProduct_startCasting .chooseCycle .listLeft li {
	text-align: center;
	padding: 0.1rem 0;
}
#combProduct_startCasting .chooseCycle .listLeft li.active {
	background: #fff;
}

#fixedInvestment_startInvestment .chooseCycle .listRight {
	width: 50%;
	overflow-y: scroll;
}
#fixedInvestment_startInvestment .chooseCycle .listRight li,
#combProduct_startCasting .chooseCycle .listRight li {
	text-align: center;
	padding: 0.1rem 0;
}
#fixedInvestment_startInvestment .chooseCycle .listRight li.active,
#combProduct_startCasting .chooseCycle .listRight li.active {
	color: #e5443c;
}
#combProduct_startCasting .chooseCycle .listRight {
	width: 50%;
	overflow-y: scroll;
}
#fixedInvestment_startInvestment .determine,
#fixedInvestment_calculator .determine,
#combProduct_startCasting .determine {
	position: absolute;
	right: 0;
	top: 0;
	padding-right: 0.1rem;
	color: #319ef2;
}
#fixedInvestment_startInvestment .payList .img {
	background: url(../images/old_no_choose.png) no-repeat;
	background-size: 100% 100%;
	width: 0.24rem;
	height: 0.24rem;
	margin-top: 0.1rem;
	display: inline-block;
	margin-right: 0.1rem;
}
#fixedInvestment_startInvestment .payList .active {
	background: url(../images/choose.png) no-repeat;
	background-size: 100% 100%;
}
#fixedInvestment_startInvestment .pay_mode {
	padding: 0.1rem 0;
}
#fixedInvestment_startInvestment .borderActive {
	border-bottom: 1px solid #e6e6e6;
	padding: 0.1rem 0;
}
#fundSupermarket_purchaseDetail .payList .img {
	background: url(../images/old_no_choose.png) no-repeat;
	background-size: 100% 100%;
	width: 0.24rem;
	height: 0.24rem;
	margin-top: 0.1rem;
	display: inline-block;
	margin-right: 0.1rem;
}
#fundSupermarket_purchaseDetail .payList .active {
	background: url(../images/choose.png) no-repeat;
	background-size: 100% 100%;
}
#fundSupermarket_purchaseDetail .payList .pay_mode {
	padding: 0.1rem 0;
}
#fundSupermarket_purchaseDetail .payList .borderActive {
	border-bottom: 1px solid #e6e6e6;
	padding: 0.1rem 0;
}
/* 定投列表 */
#fixedInvestment_investmentList .list {
	text-align: center;
}
#fixedInvestment_investmentList .list .card {
	background: #fff;
	padding: 0.1rem;
	margin-top: 0.05rem;
}
#fixedInvestment_investmentIndex .trade_box .result {
	right: 0;
	float: right;
	padding-right: 0.15rem;
	top: 0;
	bottom: 0;
	margin: auto;
	height: 0.4rem;
	text-align: right;
}

#fixedInvestment_investmentIndex .trade_box .result p:nth-of-type(1) {
	padding: 0.1rem 0 0;
	font-size: 16px;
}
#fixedInvestment_investmentIndex .trade_box .result p:nth-of-type(2) {
	color: #999;
}
.flex_0 {
	flex: none !important;
	width: 0.8rem !important;
}
/* 理财账单 */
#activity_financialBill #product {
	/* background-color: #fff !important; */
}
#activity_financialBill .topCard {
	flex-direction: row;
	padding: 0.1rem 0.2rem;
	position: relative;
	height: 0.8rem;
}
#activity_financialBill .annualBill_detailed {
	height: 0.46rem;
}
#activity_financialBill .topCard ul {
	width: 50%;
}
#activity_financialBill .m_right_icon:after {
	margin-top: 0.07rem;
}
#activity_financialBill .annualBill_center {
	text-align: center;
	/* height:calc(100vh - 2.56rem); */
	overflow-y: scroll;
}
#activity_financialBill .annualBill_center ul {
	padding: 0.02rem 0;
}
#activity_financialBill #chooseYear {
	padding-right: 0.14rem;
}
#activity_financialBill #chooseYear:after {
	content: "";
	width: 0.06rem;
	height: 0.06rem;
	border-left: 2px solid #999;
	border-bottom: 2px solid #999;
	transform: rotate(315deg);
	-webkit-transform: rotate(315deg);
	-moz-transform: rotate(315deg);

	position: absolute;
	right: 0;
	top: 50%;
	margin-top: -0.06rem;
}
#activity_financialBill .annualBill_center {
	padding: 0.05rem 0.2rem;
}
#activity_financialBill .visc_pullUp {
	height: 0;
}

/*晋金宝90天宣传页*/
@media screen and (min-width: 320px) and (max-width: 360px) {
	.perChartName {
		font-size: 0.12rem;
	}
	.perChartName_width {
		width: 80% !important;
	}
	/* .perChartPad {
		padding: 0.06rem !important;
	} */
	.perMar {
		margin-left: 0.1rem !important;
	}
}
.vjs-big-play-centered .vjs—big-play-button {
	top: 50%;
	left: 50%;
	margin-top: -0.81666em;
	margin-left: -1.5em;
}
/*一键购买*/
.pc-select .placeholder {
	font: 400 13.3333px Arial;
	border-radius: 0px;
	border-width: 1px;
	border-style: solid;
	padding: 0.02rem;
	color: #319ef2;
	border: 1px solid #d3d3d3;
}
.pc-select ul {
	border-radius: 3px;
	border-width: 1px;
	border-color: #d3d3d3;
	border-style: solid;
	margin-top: -1px;
	padding: 0.01rem;
	position: absolute;
	z-index: 10001;
	right: 0.15rem;
	box-shadow: 1px 1px 1px #d3d3d3;
	min-width: 1rem;
	background-color: #fff;
}
.pc-select ul li {
	display: block;
	text-align: left;
	color: #999;
	cursor: pointer;
	padding: 0 0.1rem;
}
.pc-select ul li:hover {
	background: #319ef2;
	color: #fff;
}
.userIndexBtn {
	border: 1px solid #e5443c;
	color: #e5443c;
	text-align: center;
	padding: 0.04rem;
	border-radius: 0.05rem;
	margin: 0.15rem 0 0.05rem 0;
}
.videoShow {
	height: 2rem;
	width: 100%;
	position: relative;
}
.videoShow p {
	width: 100%;
	height: 100%;
	background: url(https://jjdx.sxjjd.com/oss/fund_filesystem/video/activityVideoCover/20220623170620.png)
		no-repeat center center;
	background-size: 100% 100% !important;
}
.videoShow p .videoBtnPlay {
	background: url(../images/play_bg.png) no-repeat center center;
	background-size: 100% 100%;
	width: 0.4rem;
	height: 0.4rem;
	display: inline-block;
    position: absolute;
    right: 0.2rem;
	bottom: 0.2rem;
}
#showVideo .header_inner {
	position: initial;
	line-height: 0.22rem;
}
#showVideo .close {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
	padding: 0.1rem;
	color: #fff;
}
#showVideo .header_inner .icon_back.icon_gray span:before {
	border-left: 2px solid #fff;
	border-bottom: 2px solid #fff;
}
#showVideo #new_example_video,
#showVideo #heigh_video,
#showVideo #startCasting_new_example_video,
#showVideo #userIndex_new_example_video {
	width: 100%;
	height: 100%;
	position: absolute;
}
#template_publicOfferingDetail .vjs-control-bar,
#template_heighEndProduct .vjs-control-bar {
	width: 85% !important;
	margin: 0 auto !important;
	margin-bottom: 1rem !important;
}
#template_publicOfferingDetail .vjs-fullscreen-control,
#template_heighEndProduct .vjs-fullscreen-control,
#login_userIndexs .vjs-fullscreen-control {
	display: none;
}
/* 一键定投 */
.terminated {
	color: #fff;
	background: #999;
	padding: 0 0.05rem;
}
#fixedInvestment_investmentList .singlePlanMore {
	font-size: 10px;
	background: #e5443c;
	text-align: center;
	color: #fff;
	width: 0.8rem;
	height: 0.16rem;
	line-height: 0.16rem;
	margin-top: -0.1rem;
	margin-left: -0.1rem;
	margin-bottom: 0.1rem;
	/* height: 0.18rem; */
	/* line-height: 0.18rem; */
}
#fixedInvestment_investmentList .details {
	padding: 0 0.03rem !important;
	color: #319ef2;
	font-size: 12px;
	display: flex;
	margin-top: 0.02rem;
}
.loginDig {
	width: 100%;
	height: 100%;
	position: absolute;
	background: #fff;
	z-index: 999;
	top: 0;
}
.img_goods {
	background: url(../images/img_good.png) no-repeat 0 0;
	width: 0.48rem;
	height: 0.48rem;
	background-size: cover;
}

/*
多基金买入营销页
*/
#main_fundsMarketing .fundsMarketing_desc {
	padding: 0.12rem 0.2rem;
	background-size: cover !important;
	background-position: center !important;
}
#main_fundsMarketing .fundsMarketing_desc .product_head {
	text-shadow: 0px 1px 1px #900808;
	font-size: 32px;
}
#main_fundsMarketing .fundsMarketing_content {
	background: linear-gradient(#e0211e, #f76a68, #f6e586);
	margin-top: 0.1rem;
	margin-bottom: 0.2rem;
	padding: 0.1rem;
}
#main_fundsMarketing .content {
	padding: 0.1rem 0.2rem;
	margin-top: 0.06rem;
	overflow: overlay;
}
@media screen and (min-width: 320px) and (max-width: 360px) {
	#main_fundsMarketing .content {
		padding: 0.1rem;
	}
}
#main_fundsMarketing .content table {
	font-size: 0.12rem;
}
#main_fundsMarketing .content table th,
#main_fundsMarketing .content table td {
	border: 1px solid #ccc;
	text-align: center;
	text-align: -webkit-center;
	padding: 0.04rem;
	line-height: 0.16rem;
	vertical-align: middle;
}
.color-red {
	color: #e5443c;
}
.color-green {
	color: #00c35e;
}
/* 汇款充值页面样式特殊处理 */
#thfund_inputRechargePwd .supportedBankCards {
	padding: 0.05rem 0.4rem;
}
#account_payRecharge .pay {
	width: 50%;
	text-align: center;
	color: #000;
	font-size: 0.16rem;
}
#account_payRecharge .rules_list strong {
	font-size: 0.18rem;
	text-align: center;
	line-height: 0.3rem;
	padding-bottom: 0.1rem;
}
#account_payRecharge .card_rules .rules_box h5 {
	border-bottom: 0;
	padding-top: 0.3rem;
	padding-bottom: 0.1rem;
	line-height: 0;
}
#account_payRecharge .ui.buttons {
	margin: 0;
	padding: 0;
}
#safety_oldcardinfo .changeIphone {
	display: inline-block;
	height: 0.44rem;
	line-height: 0.44rem;
	width: 0.5rem;
	text-align: center;
	color: #319ef2;
}
#safety_setInformManager .box {
	padding: 4px 0;
	width: 200px;
	height: 4px;
	position: relative;
	margin: 100px auto;
	cursor: pointer;
}

#safety_setInformManager .box .length {
	width: 100%;
	height: 4px;
	background: gray;
	border-radius: 2px;
	cursor: pointer;
}

#safety_setInformManager .box .tip {
	position: absolute;
	width: 10px;
	height: 10px;
	background: green;
	top: 50%;
	transform: translateY(-50%);
	border-radius: 50%;
	left: 45px;
	z-index: 3;
	cursor: pointer;
}

#safety_setInformManager .box .light {
	position: absolute;
	width: 45px;
	height: 4px;
	top: 50%;
	transform: translateY(-50%);
	background-color: aqua;
	cursor: pointer;
}
/*弹框详情模板*/
#activity_marketing .marketing_bac {
	background-size: 100% 100% !important;
	padding-top: 64%;
	padding-bottom: 0.2rem;
}
#activity_marketing .marketing_content {
	background-color: #fdf2d1;
	margin: 0.2rem;
	border-radius: 0.08rem;
	padding: 0.1rem;
	color: #a57a52;
	line-height: normal;
}
#activity_marketing .marketing_title {
	font-weight: 600;
	border-bottom: 0.04rem solid #f7bf4c;
	margin-right: 0.8rem;
	margin-bottom: 0.1rem;
}
#activity_marketing .marketing_em {
	width: 0.14rem;
	height: 0.14rem;
	background: #f7bf4b;
	display: inline-block;
	border-radius: 50%;
	margin-right: 0.04rem;
	margin-top: 0.02rem;
	float: left;
}
#activity_marketing .marketing_p {
	margin-left: 0.2rem;
}
#activity_marketing .m_button {
	width: 1.6rem;
	background: #f7bf4b;
	border-radius: 0.06rem;
	line-height: 0.4rem;
	font-size: 0.16rem;
	margin: 0.1rem auto;
	text-align: center;
	border: 0.02rem solid #cb7f08;
	color: #fff;
	box-shadow: 0.01rem 0.01rem 0.01rem #ccc;
}
#activity_marketing .m_button_pro {
	position: absolute;
	background: #e5443c;
	text-align: center;
	width: 72%;
	border-radius: 0.05rem;
	margin: 0 auto;
	height: 0.42rem;
	line-height: 0.4rem;
	color: #ffffff;
	font-weight: bold;
	left: 0;
	right: 0;
	top: 24%;
}
#activity_marketing .mark_info {
	position: absolute;
	margin: 0 16%;
	height: 0.42rem;
	line-height: 0.4rem;
	top: 20%;
}
@media screen and (min-width: 320px) and (max-width: 350px) {
	#activity_marketing .mark_info {
		margin: 0 12%;
	}
}
@media screen and (min-width: 400px) {
	#activity_marketing .mark_info {
		margin: 0 20%;
	}
}
#activity_marketing .mark_income {
	font-size: 0.4rem;
	float: left;
	font-weight: bold;
}
#activity_marketing .mark_date {
	float: right;
	margin-left: 0.1rem;
	font-size: 0.12rem;
	margin-top: -0.06rem;
}

/*
基金超市指数基金提示简介
*/
.marketing_tip {
	background-image: url(../images/question.png);
	background-size: cover;
	position: relative;
	width: 0.2rem;
	height: 0.2rem;
	float: right;
	margin-left: -0.2rem;
	right: 0.2rem;
	margin-top: 0.02rem;
}
/* 定投css */
#fixedInvestment_startInvestment .jjs_yue {
	padding: 0 0 0 0.34rem;
}
#fundSupermarket_purchaseDetail .jjs_yue {
	padding: 0 0.2rem 0 0.34rem;
}
#fixedInvestment_startInvestment .choosePay {
	padding: 0.1rem 0.2rem 0 0.2rem;
}
#fundSupermarket_purchaseDetail .payList {
	margin-top: 0.1rem;
	padding: 0.1rem 0.1rem 0 0.1rem;
}
/* 营销页图 */
#echartsOne {
	width: 100%;
	height: 4rem;
}
/* 指纹登录相关 */
#safety_fingerprintPwd .switch,
#safety_setInformManager .switch {
	width: 40px;
	height: 24px;
	position: relative;
	border: 1px solid #ccd9e0;
	background-color: #ccd9e0;
	box-shadow: #ccd9e0 0 0 0 0 inset;
	border-radius: 20px;
	background-clip: content-box;
	display: inline-block;
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	user-select: none;
	outline: none;
	float: right;
	margin-top: 0.1rem;
	margin-right: 0.1rem;
}
#safety_fingerprintPwd .switch::before,
#safety_setInformManager .switch::before {
	content: "";
	position: absolute;
	width: 22px;
	height: 22px;
	background-color: #ffffff;
	border-radius: 50%;
	top: 0;
	bottom: 0;
	margin: auto;
}
#safety_fingerprintPwd .switch_uncheck::before,
#safety_setInformManager .switch_uncheck::before {
	/* left: 2px; */
	transition: 0.3s;
}
#safety_fingerprintPwd .switch_check,
#safety_setInformManager .switch_check {
	background-color: #2fa350;
	transition: 0.6s;
}
#safety_fingerprintPwd .switch_check::before,
#safety_setInformManager .switch_check::before {
	left: 16px;
	transition: 0.8s;
}

#template_marketing .perfor_table {
	padding: 0.04rem;
}

#template_marketing .perfor_table th,
#template_marketing .perfor_table td {
	text-align: center;
	border: 1px solid #000;
	border-collapse: collapse;
}
#myTransfer_hangingOrder .transaction_details, .transactionDetails .transaction_details{
	background: #fff;
	margin: 0.1rem 0;
	height: 0.4rem;
	line-height: 0.4rem;
}
/* 投顾营销页样式修改 */

#combProduct_combProdMarketing .content-item .remark,
#template_seriesChildrenMarketing .content-item .remark{
	color: #7b430a;
    /* font-weight: bold; */
    background: linear-gradient(to bottom,rgb(249,238,222),#fff);
    border-radius: 0.1rem;
    display: block;
    padding: 0.1rem;
}
#combProduct_combProdMarketing .cardList,
#template_seriesChildrenMarketing .cardList{
	padding:0.1rem 0.1rem 0 0.1rem;
}
#combProduct_combProdMarketing .cardList .card,
#template_seriesChildrenMarketing .cardList .card{
	width:45%;
	text-align: center;
	background-image: url(../images/card_bg.png); /* 替换为你的图片路径 */
	background-size: cover; /* 让图片覆盖整个容器 */
	background-position: center; /* 将图片放置在容器的中心 */
	background-repeat: no-repeat; /* 防止图片重复显示 */ 
	border-radius: 0.05rem;
	padding:0.1rem 0;
	
}
#combProduct_combProdMarketing .cardList .card_title,
#template_seriesChildrenMarketing .cardList .card_title{
	background: linear-gradient(to bottom, #f8681e, #f89766);
    -webkit-background-clip: text;
    color: transparent;
	padding-bottom:0.06rem;
}



/*滚入本期前投资情况*/
  #template_rollingSituation .fixed-table-container {
    width: 100%;
    display: flex;
    overflow-x: scroll;
	background-color: #eeeeee;
	-webkit-overflow-scrolling: touch; /* 启用平滑滚动 */
	touch-action: pan-x;	
  }
/* 尝试强制滚动条显示 */
#template_rollingSituation .fixed-table-container::-webkit-scrollbar {
    -webkit-appearance: none; /* 隐藏默认样式 */
    height: 5px; /* 设置滚动条的高度 */
}

#template_rollingSituation .fixed-table-container::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: #999; /* 滑块颜色 */
}

  #template_rollingSituation  table {
	border-collapse: collapse;
	min-width: 100%;
	white-space: nowrap;
  }
  #template_rollingSituation th,  #template_rollingSituation td {
    border: 0.01rem solid #ddd;
    padding: 0.05rem;
    text-align: center;
    white-space: nowrap;
	vertical-align: middle;
  }

  /*#template_rollingSituation th:first-child,  #template_rollingSituation td:first-child {
    position: sticky;
    left: 0;
    z-index: 3;
	background-color: #fff;
  }

  #template_rollingSituation th:nth-child(2), #template_rollingSituation td:nth-child(2) {
	position: sticky;
	left:0.4rem;
    z-index: 2;
	background-color: #fff;
  } */

#template_rollingSituation .fixed-table-container tbody tr:nth-child(odd){
	background-color: #fff;
}

@media screen and (max-width: 600px) {
	#template_rollingSituation .responsive-table {
      display: block;
      overflow-x: auto;
    }
  }


  #template_rollingSituation  .problem_prompt{
	text-align: center;
	margin-top: 0.5rem;
  }
  #template_rollingSituation  .problem_prompt a{
	color:#319ef2
  }

  /* #template_rollingSituation * { touch-action: pan-x; }; */
  /* #fundSupermarket_index * { 
	touch-action: pan-x pan-y
  }; */
   
/* 高端版本 列表固定宽度 */
.high_fixed_width{
	width: 1.04rem;	
}
/* 攒钱计划特殊优化 */
#scene_sceneHold .startPlanStyle{
	height:0.5rem;
	line-height: 0.5rem;
}
#scene_sceneHold .startPlanStyle_li{
	margin-top: -0.22rem;
}