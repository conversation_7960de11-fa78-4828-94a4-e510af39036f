define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var tools = require("./tools");
    var monkeywords = {};
    monkeywords.flag = 0; //用来判断是否是弹出状态
    monkeywords._pageId; //当前页面得_pageId
    monkeywords.endcallback;
    monkeywords.domid;
    monkeywords.isShowUpMoney;
    monkeywords.open = function (parm) {
        /*
         parm={
             _pageId:当前页面得_pageId
             domid:数字键盘的input
             inputcallback:键盘输入
            endcallback：键盘完成
            keyBoardHide: 键盘隐藏
            isShowUpMoney: 是否显示 默认不显示
         }
         * */
        monkeywords.flag = 1;
        monkeywords._pageId = parm._pageId;
        monkeywords.endcallback = parm.endcallback;
        monkeywords.domid = parm.domid;
        monkeywords.isShowUpMoney = (parm.isShowUpMoney == undefined || parm.isShowUpMoney == false) ? false : true;
        if (!parm.idnum) {
            monkeywords.idnum = '';
        } else {
            monkeywords.idnum = parm.idnum;
        }
        $(parm._pageId + ' #inputspanid' + monkeywords.idnum + ' span').text('').removeClass("unable").css('color', '#000000');
        $(parm._pageId + ' #inputspanid' + monkeywords.idnum + ' span').attr("data-content-before", "");
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                var spanElement = $(parm._pageId + ' #inputspanid' + monkeywords.idnum + ' span');
                var spanValue = monkeywords.domid.val();
                if (spanValue == ".") {
                    monkeywords.domid.val("0");
                }
                if (parm.otherend) parm.otherend();
                monkeywords.flag = 0;
                if (spanElement.text() == '') {
                    spanElement.text(spanElement.attr('text'))
                }
                spanElement.removeClass('inputspan');
                if (parm.endcallback) parm.endcallback();
                spanElement.text(parm.domid.val());
                if (spanValue != '') {
                    spanElement.text(tools.fmoney(spanValue.replace(/,/g, "")));
                    spanElement.addClass("active").addClass("unable");
                    var left = (spanElement.width() / 100 + 0.07) + "rem";
                    monkeywords.isShowUpMoney && document.styleSheets[0].addRule(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span:before', 'left: ' + left);
                    monkeywords.isShowUpMoney && spanElement.attr("data-content-before", tools.numtoCN(spanValue))
                }
                if (spanElement.text() == '') {
                    spanElement.text(spanElement.attr('text')).css('color', '#999999');
                    spanElement.removeClass("active").addClass("unable");
                }
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                if (parm.inputcallback) parm.inputcallback();
                var spanElement = $(parm._pageId + ' #inputspanid' + monkeywords.idnum + ' span');
                var spanValue = monkeywords.domid.val();
                spanElement.text(parm.domid.val());
                if (spanValue != '') {
                    spanElement.addClass("active");
                    var num = spanValue.replace(/,/g, "");
                    var left = (spanElement.width() / 100 + 0.07) + "rem";
                    monkeywords.isShowUpMoney && document.styleSheets[0].addRule(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span:before', 'left: ' + left);
                    monkeywords.isShowUpMoney && spanElement.attr("data-content-before", tools.numtoCN(num))
                }
                if (spanElement.text() == '') {
                    spanElement.removeClass("active");
                    spanElement.attr("data-content-before", "")
                }
            }, // 键盘的输入事件
            keyBoardHideFunction: function () {
                var spanElement = $(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span');
                var spanValue = monkeywords.domid.val();
                if (parm.keyBoardHide) parm.keyBoardHide();
                if (spanValue != '') {
                    spanElement.addClass("active");
                    var left = (spanElement.width() / 100 + 0.07) + "rem";
                    if(parseFloat(spanValue))
                    monkeywords.isShowUpMoney && document.styleSheets[0].addRule(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span:before', 'left: ' + left);
                    monkeywords.isShowUpMoney && spanElement.attr("data-content-before", tools.numtoCN(spanValue))
                }
                if (spanElement.text() == '') {
                    spanElement.removeClass("active");
                    spanElement.attr("data-content-before", "")
                }
            }//键盘隐藏事件
        };
    };
    monkeywords.close = function () {
        if (monkeywords.flag == 1) {
            var spanElement = $(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span');
            var spanValue = monkeywords.domid.val();
            monkeywords.flag = 0;
            if (monkeywords.endcallback) monkeywords.endcallback();
            spanElement.addClass("unable");
            if (spanValue != '') {
                spanElement.text(tools.fmoney(spanValue.replace(/,/g, "")));
                spanElement.addClass("active");
                var left = (spanElement.width() / 100 + 0.07) + "rem";
                monkeywords.isShowUpMoney && document.styleSheets[0].addRule(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span:before', 'left: ' + left);
                monkeywords.isShowUpMoney && spanElement.attr("data-content-before", tools.numtoCN(spanValue))
            }
            if (spanElement.text() == '') {
                spanElement.removeClass("active");
                spanElement.text(spanElement.attr('text')).css('color', '#999999');
            }
            var param = {};
            param["funcNo"] = "50211";
            require("external").callMessage(param);
        }
    }
    monkeywords.empty = function () {
        var spanElement = $(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span');
        monkeywords.domid && monkeywords.domid.val("");
        spanElement && spanElement.text("");
        monkeywords.isShowUpMoney && document.styleSheets[0].addRule(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span:before', 'left: 0');
        monkeywords.isShowUpMoney && spanElement.attr("data-content-before", "")
    }
    monkeywords.destroy = function () {
        $(monkeywords._pageId + ' #inputspanid' + monkeywords.idnum + ' span').removeClass("active");
        monkeywords.empty()
    }
    // 暴露对外的接口
    module.exports = monkeywords;
});
