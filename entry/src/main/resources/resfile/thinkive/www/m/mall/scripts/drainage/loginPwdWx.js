// 引流注册-设置登录密码
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#drainage_loginPwdWx";
    var _pageCode = "drainage/loginPwdWx";
    var mobile;
    var invitationMobile;
    var labelId,activity_id;//渠道ID
    function init() {
        mobile = appUtils.getSStorageInfo("mobile");
        labelId = appUtils.getSStorageInfo("labelId");// 获取渠道ID
        activity_id = appUtils.getSStorageInfo("activity_id");
        if (!mobile) {
            appUtils.pageInit(_pageCode, "drainage/userInvitationWx");
            return;
        }
        //获取邀请人手机号
        invitationMobile= appUtils.getSStorageInfo("invitationMobile");
    }

    //绑定事件
    function bindPageEvent() {

        //点击注册按钮
        appUtils.bindEvent($(_pageId + " #nextStep"), function () {
            //检查登录密码
            var pwd1 = $(_pageId + " #pwd1").val();
            var pwd2 = $(_pageId + " #pwd2").val();
            if (!checkInput(pwd1, pwd2)) {
                return;
            }
            //注册
            var param = {
                registered_mobile: mobile,
                login_pwd: pwd1,
                invitationMobile: invitationMobile,
                labelId:labelId,
                source:"web",
                activity_id:activity_id
            };
            regAndlogin(param);
        });
    }

    /**
     * 检验输入密码是否符合规范
     * 长度，格式等
     */
    function checkInput(pwd1, pwd2) {
        if (validatorUtil.isEmpty(pwd1)) {
            layerUtils.iAlert("登录密码不能为空");
            return false;
        }
        if (pwd1.length <6) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }
        if (validatorUtil.isEmpty(pwd2)) {
            layerUtils.iAlert("确认密码不能为空");
            return false;
        }
        if (pwd1 !== pwd2) {
            layerUtils.iAlert("两次密码不相同");
            return false;
        }
        var flag = 0;
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^[a-zA-z]+$/)) {
                flag = flag + 1;
                break;
            }
        }
        for (var i = 0; i < pwd1.length; i++) {
            if (pwd1[i].match(/^\d+$/)) {
                flag = flag + 1;
                break;
            }
        }
        if (flag != 2) {
            layerUtils.iAlert("登录密码请输入6-16位字母和数字组合");
            return false;
        }

        return true;
    }


    /**
     * 设置登录密码
     */
    function regAndlogin(paramObj) {
        //对登录密码进行加密*/
        var pwdCallBack = function (pwdRSAStr) {
            paramObj.login_pwd = pwdRSAStr;
            paramObj.registered_mobile = paramObj.registered_mobile;
            paramObj.recommend = paramObj.invitationMobile;
             service.reqFun101049(paramObj, function(data) {
                if(data.error_no == "0") {
                    tools.clickPoint(_pageId,_pageCode,'nextStep',activity_id,'web',labelId)    //注册
                    layerUtils.iAlert("恭喜您，注册成功！", "", function () {
                        tools.clickPoint(_pageId,_pageCode,'download',activity_id,'web',labelId)
                        appUtils.pageInit(_pageCode, "appdown/index");
                    },"立即下载");
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            });
        };
        common.rsaEncrypt(paramObj.login_pwd, pwdCallBack);
    }


    function destroy() {
        service.destroy();
        $(_pageId + " input").val("");
    }

    function pageBack() {
        appUtils.pageInit(_pageCode, "drainage/userLoginWx");
    }

    /*****************************************************************************************************************************/


    /*****************************************************************************************************************************/
    var drainageLoginPwd = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = drainageLoginPwd;
});
