define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var platform = require("gconfig").platform;
    var layerUtils = require("layerUtils");
    // var tools = require("./tools");
    var monkeywords = {};
    monkeywords.flag = 0; //用来判断是否是弹出状态
    monkeywords._pageId; //当前页面得_pageId
    monkeywords.endcallback;
    monkeywords.domid;
    monkeywords.isShowUpMoney;
    monkeywords.open = function (parm) {
        //秘密输入框弹出后，安卓禁用录屏/截屏 IOS提示 60094
        let param60094 = { //安卓
            funcNo:"60094",
            isInterceptScreenshot:'1',
            screenCaptureTip:'正在截屏，请注意个人信息安全',
            screenRecordingTip:'正在录屏，请注意个人信息安全'
        }
        let param80323 = {  //IOS
            funcNo:"80323",
        }
        if(platform == '1' || platform == '5') require("external").callMessage(param60094);
        if(platform == '2'){
            let iosData = require("external").callMessage(param80323);
            if(iosData && iosData.error_no == '0' && iosData.results[0].isRecord == '1'){   //客户处于录屏中
                layerUtils.iAlert('正在录屏，请注意个人信息安全');
            }
        }
        /*
        parm={
            _pageId:当前页面得_pageId
            domid:数字键盘的input
            inputcallback:键盘输入
        endcallback：键盘完成
        keyBoardHide: 键盘隐藏
        isShowUpMoney: 是否显示 默认不显示
        }
         * */
        monkeywords.flag = 1;
        monkeywords._pageId = parm._pageId;
        monkeywords.endcallback = parm.endcallback;
        monkeywords.domid = parm.domid;
        monkeywords.isShowUpMoney = (parm.isShowUpMoney == undefined || parm.isShowUpMoney == false) ? false : true;
        if (!parm.idnum) {
            monkeywords.idnum = '';
        } else {
            monkeywords.idnum = parm.idnum;
        }
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
                var spanValue = monkeywords.domid.val();
                if (spanValue == ".") {
                    monkeywords.domid.val("0");
                }
                monkeywords.flag = 0;
                if (parm.endcallback) return  parm.endcallback();
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                if (parm.inputcallback) return parm.inputcallback();
            }, // 键盘的输入事件
            keyBoardHideFunction: function () {
                var spanValue = monkeywords.domid.val();
                if (parm.keyBoardHide) return parm.keyBoardHide();
            }//键盘隐藏事件
        };
    };
    monkeywords.close = function () {
        if (monkeywords.flag == 1) {
            var spanValue = monkeywords.domid.val();
            monkeywords.flag = 0;
            if (monkeywords.endcallback)  monkeywords.endcallback();
            var param = {};
            param["funcNo"] = "50211";
            require("external").callMessage(param);
            //秘密输入框弹出后，安卓禁用录屏/截屏 IOS提示 60094
            let param60094 = {
                funcNo:"60094",
                isInterceptScreenshot:'0'
            }
            if(platform == '1' || platform == '5') require("external").callMessage(param60094);
        }
    }
    monkeywords.empty = function () {
        monkeywords.domid && monkeywords.domid.val("");
    }
    monkeywords.destroy = function () {
        monkeywords.empty()
    }
    // 暴露对外的接口
    module.exports = monkeywords;
});
