// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#highEnd_notice ",
        gconfig = require("gconfig"),
        global = gconfig.global;
    var productInfo;
    var tools = require("../common/tools");

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        queryGg()

    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点击公告
        appUtils.preBindEvent($(_pageId + " #list_box"), " .item", function (e) {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["statusColor"] = statusColor
            param["titleColor"] = titleColor
            if(url.indexOf("http") > -1) {
                param["url"] = url;
            } else {
                param["url"] = global.oss_url + url;
            }
            require("external").callMessage(param);
        }, 'click');
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #list_box").html("");
    }

    //查询公告
    function queryGg() {
        var param = {
            fund_code: productInfo.fund_code
        };
        service.reqFun102033(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results;
                if (!results || results.length == 0) {
                    return;
                }
                var html = "";
                var title;
                for (var i = 0; i < results.length; i++) {
                    var title_new = results[i].info_pubdate.substring(0, 4);
                    //年份与之前不同,新增一个年份框,并记录最新年份
                    if (title != title_new) {
                        title = title_new
                        html += '<div class="list" data-date=' + title_new + '>' +
                            '<div class="title">' + title_new + '年</div>';
                    }
                    //增加本条数据
                    var time = tools.ftime(results[i].info_pubdate).substring(5);
                    html += '<div class="item" operationType="1" operationId="item_' + results[i].id + '" operationName="点击公告" url="' + results[i].an_url + '"data-id=' + results[i].id + '>' +
                        '<span class="time">' + time + '</span>' +
                        '<span class="text">' + results[i].an_title + '</span>' +
                        '</div>';

                }
                $(_pageId + " #list_box").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thnotice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thnotice;
});
