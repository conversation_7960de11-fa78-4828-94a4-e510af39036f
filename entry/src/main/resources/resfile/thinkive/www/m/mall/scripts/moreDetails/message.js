// 消息列表
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        gconfig = require("gconfig"),
        _pageId = "#moreDetails_message ";
        var tools = require("../common/tools");
        var page = 1;
    function init() {
        //页面埋点初始化
        tools.initPagePointData();
        // 程序所有的公告
        // getMessages();
        // 程序所有的公告
        getMessages(1, false);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");
    }
    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 点击进入消息详情页面
        appUtils.preBindEvent($(_pageId + " .message_list"), ".message_card", function () {
            tools.pageTo_message_datail($(this),"moreDetails/message")
        }, 'click');

    }
    //获取公告
    function getMessages(list_page, isAppendFlag) {
        let resquestData = {
            page_num:list_page + '',
            page_size:'10'
        }
        service.reqFun105003(resquestData, function (data) {
            let error_no = data.error_no
            let error_info = data.error_info
            var str = "";
            if (error_no == "0") {
                currentPage = data.results[0].currentPage;// 当前页数
                totalPages = data.results[0].totalPages;// 总页数
                var result = (data.results)[0];
                var rstData = result.data;
                // console.log(rstData)
                if(rstData && rstData.length){
                    for(let i = 0; i < rstData.length; i ++){
                        let cardData = rstData[i]
                        let title = cardData.mail_title
                        let create_date = cardData.create_date.substring(0,8)
                        // sessionStorage.detailsText = cardData.mail_content ? cardData.mail_content : ''
                        let productInfoSon = JSON.stringify(cardData) //二级列表接口传递数据
                        let cardHtml = 
                                    `<ul class="message_card" operationType="1" operationId="message_card" contentType="4" operationName="消息详情">
                                        <em style="display:none">${productInfoSon}</em>
                                        <li class="flex vertical_center">
                                            ${cardData.is_read == '0' ? '<i class="red_count"></i>' : '<i></i>'}
                                        </li>
                                        <li class="main_flex message_right vertical_line">
                                            <strong  class="message_title  ${cardData.is_read == '0' ? 'color_000' : 'm_text_999'}">
                                                ${title}
                                            </strong>
                                            <p class="message_time m_text_999">${tools.ftime(create_date)}</p>
                                        </li>
                                        <li class="flex vertical_center message_icon">
                                            <span class="right_icon"></span>
                                        </li>
                                    </ul>`
                        str = str += cardHtml
                    }
                    if (isAppendFlag) {
                        $(_pageId + " .message_list").append(str);
                        $(_pageId + " .visc_pullUpIcon").hide();
                        $(_pageId + " .visc_pullUpDiv").hide();
    
                    } else {
                        $(_pageId + " .message_list").html(str);
                    }
                    pageScrollInit();
                }else{
                    let str = `<div  class="message_none">
                                    暂无消息
                                </div>`
                    $(_pageId + " .message_list").html(str);
                    pageScrollInit();
                }
            } else {
                layerUtils.iAlert(error_info);

            }
        });
    }
    /** 上下滑动刷新事件* */
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, // 这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    page = 1;
                    getMessages(page, false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        page += 1;
                        getMessages(page, true);
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); // 初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }

    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + ' .message_none').hide()
        page = 1;
        $(_pageId + " .message_list").html('');
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var notice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = notice;
});
