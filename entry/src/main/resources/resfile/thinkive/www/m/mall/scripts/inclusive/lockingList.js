//债基 持有期： 锁定份额
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        _pageId = "#inclusive_lockingList";
    var _pageCode = "inclusive/lockingList";
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("holdObj");
        $(_pageId + " .lock_date").html(productInfo.closed_length);

        if(productInfo.prod_sub_type2 == "200"){
            getNewLockList();
        }else{
            //获取锁定份额
            getlockingList();
        }

    }

    function bindPageEvent() {
    	appUtils.bindEvent($(_pageId + " #getBack"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    //获取锁定份额
    function getlockingList() {
        var prams = {
            fund_code: productInfo.fund_code,
        }
        service.reqFun102087(prams, function (data) {
            if (data.error_no == 0) {
                if (data.results.length == 0) {
                    $(_pageId + " #performanceContent .list_content").html("<div class='nodata'>暂无数据</div>");
                    $(_pageId + " .warm-tip").hide();
                    return;
                }
                var list_lock_vol = data.results[0].list_lock_vol;

                var html = "";
                for (var i = 0; i < list_lock_vol.length; i++) {
                    var lock_vol = list_lock_vol[i].lock_vol;
                    var due_date = list_lock_vol[i].due_date;

                    html += '<div class="item">' +
                        '<span style="width: 50%">' + lock_vol + '份</span>' +
                        '<span class="">' + tools.ftime(due_date.substr(0, 8)) + '</span>' +
                        '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
                $(_pageId + " .warm-tip").show();
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function getNewLockList() {
        var list_lock_vol = productInfo.list_lock_vol;
        var html = "";
        if(list_lock_vol.length){
            for (var i = 0; i < list_lock_vol.length; i++) {
                var lock_vol = list_lock_vol[i].vol;
                var due_date = list_lock_vol[i].due_date;
    
                html += '<div class="item">' +
                    '<span style="width: 50%">' + lock_vol + '份</span>' +
                    '<span class="">' + tools.ftime(due_date.substr(0, 8)) + '</span>' +
                    '</div>';
            }
            $(_pageId + " #performanceContent .list_content").html(html);
            $(_pageId + " .warm-tip").show();
        }
    }

    function destroy() {
        $(_pageId + " .header_inner h1").html("--");
        $(_pageId + " #performanceContent .list_content").html("");
        $(_pageId + " .warm-tip").hide();
        $(_pageId + " .lock_date").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
