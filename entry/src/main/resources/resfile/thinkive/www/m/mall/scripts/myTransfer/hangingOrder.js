// 挂卖单
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        serviceConstants = require("constants"),
        monkeywords = require("mall/scripts/common/moneykeywords"),
        ut = require("../common/userUtil");
    var _pageId = "#myTransfer_hangingOrder";
    var _pageCode = "myTransfer/hangingOrder";
    let jymm,cancelOrderData,info; //支付密码
    let transfer_type; //转让产品类型
    let isSingle = '0';//是否为单笔 默认为单笔
    let agreement_sub_type;
    function init() {
        cancelOrderData = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        let transferData = appUtils.getSStorageInfo("transferData");
        if(transferData && transferData.same_prod_transfer_all == '1'){
            transfer_type = '1'; //新版产品
            // $(_pageId + " #fundMoneyBox").hide();
            $(_pageId + " .intoAccountDate").hide();
            $(_pageId + " .intoAccountDate_old").show();
            $(_pageId + " .banKCard").show();
            getTransferMoney(0,cancelOrderData); //初始化让出收益
            agreement_sub_type = '6'
        }else{
            transfer_type = '0'; //旧版产品
            $(_pageId + " .banKCard").show();
            $(_pageId + " #fundMoneyBox").show();
            $(_pageId + " .intoAccountDate").show();
            $(_pageId + " .intoAccountDate_old").hide();
            agreement_sub_type = '1,6'
        }
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        tools.getPdf({
            agreement_type: "prod",
            fund_code: cancelOrderData.fund_code,
            bookTitleMark: "0",
            agreement_sub_type: agreement_sub_type
        }); //获取协议
        if(transfer_type == '0') getCancelInfo(cancelOrderData);
        // getTransferMoney("3000",cancelOrderData)
        // if(transfer_type == '1') getTransferMoney(0,cancelOrderData);
        
    }
    //获取挂单信息
    function getCancelInfo(data){
        let param = {
            fund_code:data.fund_code,
            vir_fundcode:cancelOrderData.vir_fundcode
        }
        service.reqFun107001(param, function (datas) {
            if (datas.error_no == 0) {
                info = datas.results[0]
                let previousInfo = cancelOrderData
                let fund_code = "(" + previousInfo.fund_code + ")"  
                let fund_vol = tools.fmoney(previousInfo.fund_vol ? previousInfo.fund_vol : previousInfo.cost_money) + '元';
                // if(transfer_type == '1') fund_vol = tools.fmoney(previousInfo.transfer_amt);
                let resignProfits = tools.fmoney(info.resignProfits)
                $(_pageId + " #fund_sname").html(previousInfo.fund_sname)
                $(_pageId + " #fund_code").html(fund_code)
                if(transfer_type == '0') $(_pageId + " #fund_vol").html(fund_vol);
                $(_pageId + " #resignProfits").html(resignProfits)
                $(_pageId + " #intoAccountDate").html(info.intoAccountDate)
                
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //渲染卡片列表信息
    function setListData(list){
        let html = '<ul class="flex"><li>本金</li><li>到期日</li><li>剩余期限</li><li>业绩计提基准</li></ul>';
        list.map(item=>{
            html += `
            <ul class="flex">
                    <li>${tools.fmoney(item.costmoney)}元</li>
                    <li>${tools.ftime(item.due_date)}</li>
                    <li>${item.surpterm}天</li>
                    <li class="m_text_red">${tools.fmoney(item.rate)}%</li>
            </ul>
            `
        })
        $(_pageId + " .card").html(html);
        $(_pageId + " .card").show();
    }
    //获取实际交易金额
    function getTransferMoney(money,cancelOrderData){
        let param = {
            give_profit:money + '',
            fund_code:cancelOrderData.fund_code,
            vir_fundcode:cancelOrderData.vir_fundcode
        }
        service.reqFun107008(param, function (data) {
            if (data.error_no == 0) {
                if(transfer_type == '0'){
                    //旧版
                    let val = data.results[0].transfer_amt;
                    $(_pageId + ' #transfer_amt').html(val)
                    return;
                }   
                //初始化数据
                info = data.results[0]
                if(cancelOrderData){
                    let previousInfo = cancelOrderData
                    let fund_code = "(" + previousInfo.fund_code + ")"  
                    let fund_vol = tools.fmoney(previousInfo.fund_vol ? previousInfo.fund_vol : previousInfo.cost_money) + '元';
                    // if(transfer_type == '1') fund_vol = tools.fmoney(previousInfo.transfer_amt);
                    let resignProfits = tools.fmoney(info.resignProfits)
                    $(_pageId + " #fund_sname").html(previousInfo.fund_sname)
                    $(_pageId + " #fund_code").html(fund_code)
                    $(_pageId + " #resignProfits").html(resignProfits);
                    $(_pageId + " #intoAccountDate").html(info.intoAccountDate);
                }
                if(transfer_type == '1'){
                    $(_pageId + ' #transfer_amt').html(tools.fmoney(data.results[0].transfer_amt) + '元');
                    $(_pageId + " #fund_vol").html(tools.fmoney(data.results[0].costmoney_sum) + '元');
                }else{
                    $(_pageId + ' #transfer_amt').html(data.results[0].transfer_amt);
                }
                let listData = data.results[0].details;
                if(listData && listData.length > 1){
                    //存在多期
                    isSingle = '1';
                    $(_pageId + " .see_details").show();
                    $(_pageId + " #fundMoneyBox").hide();
                    setListData(listData)
                }else{
                    //只有一期
                    isSingle = '0'
                    $(_pageId + " .see_details").hide();
                    $(_pageId + " #fundMoneyBox").show();
                }
                // setProList
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    function bindPageEvent() {
        //弹出明细列表
        appUtils.bindEvent($(_pageId + " .see_details"),function () {
            //判断卡片是否展示
            let showCard = $(_pageId + " .card").is((':visible'))
            if(!showCard){
                $(_pageId + " .see_details .bottom").hide();
                $(_pageId + " .see_details .top").show();
                $(_pageId + " .card").show();
            }else{
                $(_pageId + " .see_details .top").hide();
                $(_pageId + " .see_details .bottom").show();
                $(_pageId + " .card").hide();
            }
        })
        //弹出转让规则
        appUtils.bindEvent($(_pageId + " #transferRule"),function () {
            $(_pageId + " .rule_dio").show();
            $(_pageId + " .card1").show();
        })
        //关闭转让规则弹窗
        appUtils.bindEvent($(_pageId + " .card_footer"),function () {
            $(_pageId + " .rule_dio").hide();
            $(_pageId + " .card1").hide();
        })
        //跳转产品详情页面
        appUtils.bindEvent($(_pageId + " .myTransferFund_entry"), function () {
            
            appUtils.pageInit(_pageCode, "template/heighEndProduct");
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //呼出交易密码
        appUtils.bindEvent($(_pageId + " .myTransfer_hangingOrder"), function () {
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_purchase";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //点击挂卖单按钮
        appUtils.bindEvent($(_pageId + " .myTransferFooter"),function(){
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            let curVal = $(_pageId + " #czje").val();
            let moneys = curVal.replace(/,/g, "");
            if(moneys == '' && isSingle == '0') return layerUtils.iAlert("请输入让出收益");
            if(!moneys && (moneys != 0) && isSingle == '0') return layerUtils.iAlert("请输入让出收益");
            if(isSingle == '0' && (parseFloat(moneys) > parseFloat(info.resignProfits)) && (parseFloat(info.resignProfits) != 0)) return layerUtils.iAlert("让出收益超出最大范围，请重新输入");
            let showMoney = tools.fmoney(moneys)
            if(isSingle == '1') return ejectPassword()
            layerUtils.iConfirm("您确定要让出收益" + showMoney + "元吗",
            function () {
            }, function () {
                //输入密码
                ejectPassword()
            }, "取消", "确定");
        })
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        //点击输入密码框的关闭按钮
        appUtils.bindEvent($(_pageId + " #close"), function () {
            $(_pageId + " .passwordLayer").css("display", "none");
            $(_pageId + " #rechargeInfo").empty();
            $(_pageId + " #jymm").val("");
            guanbi()
        });
        //点击确定密码
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            determinePassword()
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            $(_pageId + " .pop_text").show();
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "myTransfer_hangingOrder";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        
        //金额键盘事件
        function moneyboardEvent() {
            monkeywords.open({
                _pageId: _pageId,
                domid: $(_pageId + " #czje"),
                endcallback: function () {
                    var curVal = $(_pageId + " #czje").val();
                    var moneys = curVal.replace(/,/g, "");
                    if (moneys) {
                        getTransferMoney(moneys,cancelOrderData)
                        moneys = tools.fmoney(moneys);
                    }
                    $(_pageId + " #czje").val(moneys);
                    
                },
                inputcallback: function () {
                    var curVal = $(_pageId + " #czje").val();
                    if(!curVal) return
                    curVal = curVal.replace(/,/g, "");
                    if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                        $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                    }
                },
                keyBoardHide: function () {
                    var curVal = $(_pageId + " #czje").val();
                    if(!curVal) return
                    var moneys = curVal.replace(/,/g, "");
                    if (moneys) {
                        getTransferMoney(moneys,cancelOrderData)
                        $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                        $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                    }
                }
            })
        }
    }
    //确定密码
    function determinePassword(){
        var jymm1 = $(_pageId + " #jymm").val();
        var curVal = $(_pageId + " #czje").val();
        var moneys = curVal.replace(/,/g, "");
        // jymm1 = '123123'
        if (jymm1.length != 6) {
            layerUtils.iAlert("请确定您的交易密码格式正确");
            return;
        }
        if (transfer_type != '1' && (moneys < 0 || !moneys) && (parseFloat(moneys) != 0)) {
            layerUtils.iAlert("请输入让出收益");
            return;
        }
        if(transfer_type != '1' && (parseFloat(moneys) > parseFloat(info.resignProfits)) && (parseFloat(info.resignProfits) != 0)){
            layerUtils.iAlert("超出可让出收益范围，请重新输入");
            return;
        }
        $(_pageId + " .passwordLayer").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " #rechargeInfo").empty();
        guanbi();
        let cancelOrderData = appUtils.getSStorageInfo("productInfo");
        var param = {
            fund_code: cancelOrderData.fund_code,
            vir_fundcode:cancelOrderData.vir_fundcode,
            trans_pwd: jymm1, //交易密码
            give_profit:moneys,
            costmoney:cancelOrderData.fund_vol,
        };
        if(transfer_type == '1'){ //新版挂单
            service.reqFun107014(param, function (data) {
                if (data.error_no == 0) {
                    //挂单成功
                    appUtils.pageInit(_pageCode, "bank/transferResult", {});
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }else{
            service.reqFun107002(param, function (data) {
                if (data.error_no == 0) {
                    //挂单成功
                    appUtils.pageInit(_pageCode, "bank/transferResult", {});
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }
    }
    //关闭事件
    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }
    
    //密码输入框+描述
    function ejectPassword(){
        if ($(this).hasClass("noactive")) {
            return;
        }
        $(_pageId + " .passwordLayer").show();
        $(_pageId + " .password_box").show();
        //输入交易密码时的提示
        setRechargeInfo();
        passboardEvent();
        monkeywords.flag = 0;
        //键盘事件
        var param = {};
        param["moduleName"] = "mall";
        param["funcNo"] = "50210";
        param["pageId"] = "myTransfer_hangingOrder";
        param["eleId"] = "jymm";
        param["doneLable"] = "确定";
        param["keyboardType"] = "4";
        require("external").callMessage(param);
    }
    function setRechargeInfo(){
        let str = "正在转让"+cancelOrderData.fund_sname + " 转让价格<em>" + $(_pageId + " #transfer_amt").html() + "</em>"
        $(_pageId + " #rechargeInfo").html(str);
    }
    //交易密码
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .rule_dio").hide();
        $(_pageId + " .card1").hide();
        $(_pageId + " .agreement1").hide();
        $(_pageId + " .agreement").attr("isVerify", "false");
        $(_pageId + " .passwordLayer").hide();
        $(_pageId + " .password_box").hide();
        $(_pageId + " #rechargeInfo").empty();
        $(_pageId + ' #transfer_amt').html('')
        $(_pageId + " #jymm").val("");
        $(_pageId + " .agreement1 i").removeClass("active");
        $(_pageId + " .see_details .bottom").show();
        $(_pageId + " .card").html('');
        $(_pageId + " .card").hide();
        $(_pageId + " .see_details .top").hide();
        $(_pageId + " .see_details").hide();
        jymm = ''
        $(_pageId + " #fundMoneyBox").hide();
        guanbi()
        monkeywords.flag = 0;
        monkeywords.destroy();
        $(_pageId + " #inputspanid span").text("").css({color: "rgb(153, 153, 153)"});
        isSingle = '0';
    }
    /*
     * 返回
     */
    function pageBack() {
        appUtils.pageBack();
    }
    var benefitsList = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = benefitsList;
});
