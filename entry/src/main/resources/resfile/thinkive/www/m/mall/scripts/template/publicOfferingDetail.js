// 产品详情模板
define(function (require, exports, module) {
    require('../common/vue.min')
    let appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "template/publicOfferingDetail",
        _pageId = "#template_publicOfferingDetail";
    require("chartsUtils");
    require('../common/echarts.min');
    require('../common/echartsData.js');
    var ut = require("../common/userUtil");
    var gconfig = require("gconfig");
    var global = gconfig.global;
    let publicOfferingDetail //new 一个 vue 实例f
    let productInfo;
    let activeClass;
    let activeClassPer;
    var colorList = ["#e5443c", "#767171", "#2F5597", "#E27F2C", "#FF7A11", "#036", "#042cf7", "#da01ff", "#00ffa3"];
    // var time1;  //观看秒数定时器获取
    var t1 = 0;
    var public_player = ''
    var isShow = false;
    var startTime, timer = null;
    // let shareData;
    //获取模板详情
    async function setTemplate() {
        return new Promise(async (resolve, reject) => {
            let data = {
                type: '0',
                fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code
            }
            // if(appUtils.getPageParam("busi_id")) productInfo.fund_code = appUtils.getPageParam("busi_id")
            service.reqFun102109(data, (datas) => {
                if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
                let res = datas.results[0]
                resolve(res.template_content)
            })
        })
    }
    async function init() {
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        productInfo = appUtils.getSStorageInfo("productInfo");
        let pointCode = appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code
        //页面埋点初始化
        tools.initPagePointData({fundCode:pointCode});
        pageInfo = appUtils.getPageParam();
        if (pageInfo && pageInfo.activity_id) {
            startTime = Date.now();
            console.log(pageInfo,222)
            var readingTime = pageInfo.duration && parseFloat(pageInfo.duration) * 1000;
            console.log(readingTime,333)
            if (pageInfo.task_id && (pageInfo.task_type == '1' || pageInfo.task_type == '3')) {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (pageInfo.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: pageInfo.activity_id, task_id: pageInfo.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && pageInfo.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: pageInfo.activity_id, task_id: pageInfo.task_id });
                            }
                        }
                    }, readingTime)
                }
            }
        }
        // console.log(pageInfo,222)
        let html = await setTemplate() //拿到模板数据
        $(".main_publicOfferingDetail").html(html)   //渲染模板
        activeClass = $(_pageId + " .chartContent").attr("activeClass") ? $(_pageId + " .chartContent").attr("activeClass") : 0;
        activeClassPer = $(_pageId + " .perChart").attr("activeClassPer") ? $(_pageId + " .perChart").attr("activeClassPer") : 0;
        publicOfferingDetail = new Vue({
            el: '#main_publicOfferingDetail',
            data() {
                return {
                    oss_url:global.oss_url,
                    publicDetatilData: {},//产品整合详情
                    characteristicData: {}, // 基金经理、特色数据、资产配置信息
                    fndmgrList: [],
                    spliceDate: -1100,//默认展示7天数据 // TODO:不知道干啥用
                    tips: '', //默认提示为累计收益走势
                    timeOptions: '',//绘制折线图配置
                    activeClass: activeClass,  //高亮时间（7天）
                    activeClassPer: activeClassPer, // 默认3个月
                    activeClassSecond: '1', //高亮业绩表现，历史净值
                    initShowChat: "0", // 0 业绩走势 1 净值走势
                    moreName: "更多",

                    timeList: [  //区间
                        {
                            name: '近1个月'
                        },
                        {
                            name: '近3个月'
                        },
                        {
                            name: '近6个月'
                        },
                        {
                            name: '近一年'
                        },
                        {
                            name: '近三年'
                        },
                    ],
                    timeListNew: [  //区间
                        {
                            name: '近1个月',
                            section: "1"
                        },
                        {
                            name: '近3个月',
                            section: "3"
                        },
                        {
                            name: '近6个月',
                            section: "6"
                        },
                        {
                            name: '近一年',
                            section: "12"
                        },
                    ],
                    timeListMore: [
                        {
                            name: '近三年',
                            section: "36",
                            index: "4"
                        },
                        {
                            name: '近五年',
                            section: "60",
                            index: "5"
                        },
                        {
                            name: '成立来',
                            section: "",
                            index: "6"
                        },
                    ]
                }
            },
            //视图 渲染前
            created() {
				//获取产品详情
				this.getPublicDetatils(); 

            },
            //渲染完成后
            mounted() {
                var appletEnterImg = global.oss_url + $("#main_publicOfferingDetail #applet_enter").html();
                $(_pageId + " #main_publicOfferingDetail #applet_enter_img").attr("src", appletEnterImg);
                let prodType = $(_pageId + " .public_top").attr("prodType");
                appUtils.setSStorageInfo("prodType", prodType);
                if ($(_pageId + " .public_top").attr("reqFun102135") != 'none') {
                    this.reqFun102135(); // 获取特色数据
                }
                if ($(_pageId + " .public_top").attr("getYield") != 'none') {
                    this.getYield(true);
                }
                this.initShowChat = "0";
                //获取折线图数据
                this.getHistory();
                //获取业绩表现
                this.getPerformance();
                this.initTrajectoryData(); // 默认显示一年             
                this.getInvestmentIncome();
            },
            //计算属性
            computed: {
                //日期处理
                setTimeData: () => {
                    return (time, num, num1) => {
                        if (!time) return '--'
                        return time.substr(num, num1)
                    }
                },
                /**
                 * 格式化时间
                 */
                setTime: () => {
                    return (date, symbol) => {
                        if (!date) return '--'
                        return tools.ftime(date, symbol)
                    }
                },
                setDateText: () => {
                    return (str) => {
                        if (!str) return '--'
                        return tools.FormatDateText(str)
                    }
                },
                setNum1: () => {
                    return (str, len) => {
                        if (!str) return '--'
                        return (+str).toFixed(len)
                    }
                }


            },
            //绑定事件
            methods: {
                //跳转定投计算器
                pageCalculator() {
                    tools.recordEventData('1','calculator','定投计算器');
                    if (!common.loginInter(_pageCode)) return;
                    appUtils.pageInit(_pageCode, "fixedInvestment/calculator");
                },
                //获取定投收益
                getInvestmentIncome() {
                    let fundCode = appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code;
                    service.reqFun102158({ fundCode: fundCode }, (data) => {
                        if (data.error_no == '0') {
                            let result = data.results[0];
                            let numData = [
                                {
                                    name: '近一年',
                                    rate: result.y1
                                },
                                {
                                    name: '近两年',
                                    rate: result.y2
                                },
                                {
                                    name: '近三年',
                                    rate: result.y3
                                },
                                {
                                    name: '近五年',
                                    rate: result.y5
                                },
                                // {
                                //     name:'成立以来',
                                //     rate:result.e
                                // },
                            ]
                            var html = "";
                            for (var i = 0; i < numData.length; i++) {
                                //空数据处理
                                numData[i] = tools.FormatNull(numData[i]);
                                //基金年化收益率
                                var rateClass = "add";
                                var rate = numData[i].rate;
                                if (rate != "--") {
                                    rateClass = this.addMinusClass(numData[i].rate);
                                    rate = (+rate).toFixed(2);
                                    rate = rate + "%";
                                }

                                html += '<div class="item">' +
                                    '<span class="m_width_50">' + numData[i].name + '</span>' +
                                    '<span id="m_width_50" class=' + rateClass + '>' + rate + '</span>' +
                                    '</div>';
                            }
                            // let str = `<div>
                            //     <li class="m_text_999 m_paddingLeft_10 m_font_size12">注：按照每周四定投计算历史业绩</li>
                            //     <li class="calculator m_agreement_color m_center m_paddingTop_10">定投计算器</li>
                            // </div>`
                            $(_pageId + " #investmentYield .list_content").html(html);
                            // let html = '<div class="item">' +
                            //                 '<span class="m_width_50">近一年</span>' +
                            //                 '<span id="m_width_50" class=' + rateClass + '>' + rate + '</span>' +
                            //             '</div>';
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                // 点击定投收益
                investmentIncome(index) {
                    $(_pageId + " #performanceContent").hide();
                    $(_pageId + " #historyContent").hide();
                    $(_pageId + " #investmentYield").show();
                    tools.recordEventData('1','investmentYield','定投收益');
                    this.activeClassSecond = index;
                },
                //获取分享状态
                async getPageShareStatus(is_share) {
                    let data = {
                        busi_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code,
                        page_type: '1',
                        pageId: _pageId,
                        pageCode: _pageCode
                    }
                    if(pageInfo && pageInfo.activity_id) data.activity_id = pageInfo.activity_id;
                    tools.isShowShare(data, is_share, pageInfo, startTime)
                    // let res = await tools.isShowShare(data);
                    // if(res.is_share == 1){
                    //     $(_pageId + " #share").show()
                    //     shareData = res
                    // }
                },
                //获取交易时间
                getTransactionTime(fund_code, type) {
                    var param = {
                        //90天
                        // fund_code: "708059",
                        // type: "10"
                        //7天
                        fund_code: fund_code,
                        type: type

                    }
                    service.reqFun102008(param, function (data) {
                        if (data.error_no == 0) {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            //空数据处理
                            results = tools.FormatNull(results);
                            var qrDate1 = tools.FormatDateText(results.qrDate.substr(4));
                            var qrDate = qrDate1 + "(" + results.qrDayOfWeek + ")";

                            var syDate1 = tools.FormatDateText(results.syDate.substr(4));
                            var syDate = syDate1 + "(" + results.syDayOfWeek + ")";

                            if (results.kqDate) {
                                var kqDate = tools.FormatDateText(results.kqDate.substr(4));
                            }

                            //确认日期
                            $(_pageId + " #qrDate").html(qrDate);
                            //收益日期
                            $(_pageId + " #syDate").html(syDate);

                            //确认日期
                            $(_pageId + " #qrDate1").html(qrDate1);
                            //收益日期
                            $(_pageId + " #syDate1").html(syDate1);
                            $(_pageId + " #kqDate").html(kqDate);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },

                //获取业绩表现
                getPerformance() {
                    service.reqFun102007({ fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code }, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results[0];
                            if (!results || results.length == 0) {
                                return;
                            }
                            var dataArr = ["近一月", "近三月", "近半年", "近一年"];
                            var numData = [];
                            //近一周
                            // numData.push(results.week);
                            //近一月
                            numData.push(results.month);
                            //近三月
                            numData.push(results.season);
                            //近半年
                            numData.push(results.six_month);
                            //近一年
                            numData.push(results.year);
                            var html = "";
                            var isShowRank = $(_pageId + " #performanceContent").attr("data-show-rank") == 1 ? true : false;
                            var isShowAnnualized = $(_pageId + " #performanceContent").attr("data-show-annualized") == 1 ? true : false;
                            for (var i = 0; i < numData.length; i++) {
                                //空数据处理
                                numData[i] = tools.FormatNull(numData[i]);
                                //基金年化收益率
                                var rateClass = "add";
                                var rate = numData[i].rate;
                                if (rate != "--") {
                                    rateClass = this.addMinusClass(numData[i].rate);
                                    rate = (+rate).toFixed(2);
                                    rate = rate + "%";
                                }
                                var simiRateClass = "add";
                                var simiRate = numData[i].simi_rate;
                                if (simiRate != "--") {
                                    simiRateClass = this.addMinusClass(numData[i].simi_rate);
                                    simiRate = (+simiRate).toFixed(2);
                                    simiRate = simiRate + "%";
                                }

                                html += '<div class="item">' +
                                    '<span class="m_width_50">' + dataArr[i] + '</span>' +
                                    '<span id="m_width_50" class=' + rateClass + '>' + rate + '</span>' +
                                    `${isShowAnnualized ? `<span id="m_width_50" class='${simiRateClass}'>${simiRate}</span>` : ''}` +
                                    `${isShowRank ? `<span class="m_width_50 m_paddingRight_10"><div class="m_text_right">${numData[i].simi_ranking}</div><div class="m_text_right">${numData[i].rank_desc}</div></span>` : ''}` +
                                    '</div>';
                            }
                            $(_pageId + " #performanceContent .list_content").html(html);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },
                //选择7天 业绩表现，历史净值
                performance7(index) {
                    $(_pageId + " #historyContent").hide();
                    $(_pageId + " #investmentYield").hide();
                    $(_pageId + " #performanceContent").show();
                    tools.recordEventData('1','performanceContent','业绩表现');
                    this.activeClassSecond = index
                },
                history7(index) {
                    $(_pageId + " #performanceContent").hide();
                    $(_pageId + " #investmentYield").hide();
                    $(_pageId + " #historyContent").show();
                    tools.recordEventData('1','historyContent','历史净值');
                    this.activeClassSecond = index
                },
                //获取产品详情
                async getPublicDetatils() {
                    let res = await this.getDetails()
                    let info = { ...res, ...productInfo }
                    // let info = res;
                    console.log(info,333)
                    appUtils.setSStorageInfo("productInfo", info);
                    appUtils.setSStorageInfo("fund_code", res.fund_code);
                    appUtils.setSStorageInfo("financial_prod_type", res.financial_prod_type);
                    appUtils.setSStorageInfo("prod_sub_type", res.prod_sub_type);
                    //建议持有
                    $(_pageId + " #tip_max_day").html(res.tip_max_day);
                    //若持有不满
                    $(_pageId + " #tip_min_day").html(res.tip_min_day);
                    //赎回费率不低于
                    $(_pageId + " #tip_max_rate").html(res.tip_max_rate);
                    res.p_expected_yield = (+res.p_expected_yield).toFixed(2) + '%';
                    res.qrDate = tools.FormatDateText(res.qrDate.substr(4));//确认日
                    res.syDate = tools.FormatDateText(res.syDate.substr(4));//收益日
                    res.kqDate = tools.FormatDateText(res.kqDate);//可赎回日

                    if (res.nav_date && res.nav_date != '--') res.nav_date = tools.ftime(res.nav_date).substring(5);
                    if (res.nav != '--') res.nav = (+res.nav).toFixed(4) + "元";//单位净值
                    if (res.scale_fe != '--') res.scale_fe = (res.scale_fe / 100000000).toFixed(2) + '亿元';
                    if (res.scale_fund != '--') res.scale_fund = (res.scale_fund / 100000000).toFixed(2) + '亿元';
                    res.scale_fe_date = tools.ftime(res.scale_fe_date.substr(4, 4))
                    tools.initFundBtn(res, _pageId);
                    this.publicDetatilData = res;
                    this.publicDetatilData.updateVesion = true;
                    if (res.buy_state == "1") { // 购买
                        $(_pageId + " .purchase_box").show();
                    } else if (res.buy_state == "2") { // 预约
                        $(_pageId + " .apponit_box").show();
                    } else if (res.buy_state == "3") { // 敬请期待
                        $(_pageId + " .comingsoon_box").show();
                    } else if (res.buy_state == "4") { //封闭中
                        $(_pageId + " .deadline_box").hide();
                        $(_pageId + " .soldout_box").show();
                    } else if (res.buy_state == "5") { // 售罄
                        $(_pageId + " .soldout_box").show();
                    }
                    let is_share = $(_pageId + " .pro_share").attr("is_share")
                    if (is_share == "1") {
                        this.getPageShareStatus(is_share)
                    }
                    if (res.video_info) {
                        res.cover_path = global.oss_url + res.video_info.cover_path;
                        res.video_info.cover_path = global.oss_url + res.video_info.cover_path;
                        res.video_info.video_path = global.video_oss_url + res.video_info.video_path;
                    }
                    tools.setPageTop("#inclusive_jjThirtyDetail");
                },
                //获取产品详情
                async getDetails() {
                    return new Promise(async (resolve) => {
                        let data = {
                            fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code
                        }
                        service.reqFun102113(data, async (data) => {
                            if (data.error_no == '0') {
                                resolve(data.results[0])
                            } else {
                                layerUtils.iAlert(data.error_info);
                            }
                        })
                    })
                },
                chooseTrajectoryData(item) {
                    if (item.section == this.activeClass) {
                        return;
                    }
                    this.activeClass = item.section
                    this.moreName = "更多"
                    if (this.initShowChat == 1) {
                        tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                        this.earningsChart1(); // 获取净值走势
                    } else if (this.initShowChat == 0) {
                        tools.recordEventData('1','initTrajectoryData_'+item.section,item.name+'-业绩走势');
                        this.initTrajectoryData(); // 获取业绩走势
                    }
                },
                chooseMoreList(item) {
                    $(_pageId + " #moreSpliceDate").hide();
                    $(_pageId + " .thfundBtn").show();
                    this.moreName = item.name;
                    this.activeClass = item.section;
                    if (this.initShowChat == 1) { // 净值走势
                        tools.recordEventData('1','earningsChart_'+item.section,item.name+'-净值走势');
                        this.earningsChart1();
                    } else { // 业绩走势
                        tools.recordEventData('1','initTrajectoryData_'+item.section,item.name+'-业绩走势');
                        this.initTrajectoryData(); // 获取业绩走势
                    }
                },
                choosePerList(index) {
                    if (index == this.activeClassPer) {
                        return;
                    }
                    this.activeClassPer = index
                    tools.recordEventData('1','choosePerList_'+index,'历史盈利概率-' + index+'个月');
                    this.getYield(false);
                },

                initializationData() {
                    this.activeClass = 0
                    this.fixEarningsChart()
                },
                // 点击获取单位净值走势
                initializationData7() {
                    $(_pageId + " #navTrend").show();
                    $(_pageId + " #trajectory").hide();
                    this.initShowChat = 1;
                    tools.recordEventData('1','earningsChart','净值走势');
                    this.earningsChart1()
                },
                // 获取业绩走势折线图
                initTrajectoryData(section) {
                    section = this.activeClass;
                    $(_pageId + " #navTrend").hide();
                    $(_pageId + " #trajectory").show();
                    this.initShowChat = 0;
                    service.reqFun102146({ fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code, section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer1").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer1").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: (params) => {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    var s = `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;margin-left: 12px">${year}-${month}-${day}</div>`;
                                    params.forEach(function (t, i) {
                                        s += `<div class="chart_tooltip_item" style="margin-top:5px;height:20px;"><i style="background:${t.color};
                                        display: inline-block;width: 0.1rem; height: 0.1rem;border-radius: 50%; margin-right: 0.05rem;"></i><span style="font-size:12px">${t.seriesName}：</span><span style="color:${t.color}"><b>${(t.value * 100).toFixed(2)}%</b></span></div>`
                                    })
                                    return s;
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc",
                                hideDelay: 10
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                // axisLabel: {
                                //     formatter: '{value} %'
                                // },
                                splitNumber: 3,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: (value, index) => {
                                        return (value * 100).toFixed(2) + "%"
                                    }
                                },

                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '14%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                                // data: ['实际巡检', '计划巡检', '漏检次数'],
                            },
                            series: [],
                        };
                        let series = [];
                        results.forEach((item, i) => {
                            item.achievement = JSON.parse(item.achievement.substring(1, item.achievement.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                name: item.indexName,
                                data: item.achievement,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series;
                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = document.getElementById("chartContainer1");
                        // let dom = $(_pageId + " #chartContainer1");
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                        }
                    })
                },
                // 净值走势 新
                earningsChart1() {
                    section = this.activeClass;
                    service.reqFun102147({ fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code, section: section }, function (datas) {
                        if (datas.error_no != "0") {
                            $(_pageId + " #chartContainer").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            layerUtils.iAlert(datas.error_info);
                            return;
                        }
                        var results = datas.results;
                        // results = results.reverse();
                        if (datas.results.length == 0) {
                            $(_pageId + " #chartContainer").html("暂无数据").css({
                                "line-height": "2rem",
                                "text-align": "center",
                                "padding-top": "0.1rem"
                            });
                            $(_pageId + " #spliceDate").hide();
                            return;
                        }
                        $(_pageId + " #spliceDate").show();
                        var config = {
                            tooltip: {
                                trigger: 'axis',
                                extraCssText: "z-index:9",
                                formatter: function (params) {
                                    var time = params[0].axisValue;
                                    if (!time) return ""
                                    var year = time.substring(0, 4);
                                    var month = Number(time.substring(4, 6));
                                    var day = Number(time.substring(6, 8));
                                    return `<div id="tooltip" style="height:18px;font-size: 12px;color: #353535;">${year}-${month}-${day}</div><div class="chart_tooltip_item" style="margin-top:5px;height:20px;">单位净值：${parseFloat(params[0].value).toFixed(4)}</div>`
                                },
                                backgroundColor: "rgba(255,255,255,0.85)",
                                textStyle: {
                                    opacity: 1,
                                    lineHeight: "24px",
                                },
                                borderColor: "#cccccc"
                            },
                            xAxis: {
                                type: 'category',
                                data: [],
                                axisLabel: {
                                    /* formatter: function (e) {
                                         return tools.FormatDateText(e, 1);
                                         
                                     },*/

                                    formatter: function (value, index) {
                                        if (index == 0) {
                                            return '          ' + tools.FormatDateText(value, 1);
                                        }
                                        if (index == results[0].date.length - 1) {
                                            return tools.FormatDateText(value, 1) + '          ';
                                        }
                                        return tools.FormatDateText(value, 1);
                                    }
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                splitNumber: 5,
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed'//虚线
                                    },
                                    show: true //隐藏
                                },
                                axisLabel: {
                                    formatter: function (value, index) {
                                        return value.toFixed(4)
                                    }
                                }
                            },
                            grid: {
                                left: '1%',
                                right: '4%',
                                bottom: '5%',
                                top: '10%',
                                containLabel: true,
                            },
                            legend: {
                                padding: 10,
                                tooltip: {
                                    show: true,
                                },
                                y: 'bottom',
                            },
                            series: [],
                        };
                        let series = [];
                        //处理分红标点
                        let markPoint = {data:[]}
                        if(results[0].dividsplit && results[0].dividsplit.length > 0){
                            let dividsplit = JSON.parse(results[0].dividsplit.substring(1, results[0].dividsplit.length - 1));
                            dividsplit.forEach((item,i) =>{
                                // console.log(i,1111)
                                markPoint.data.push(
                                    {
                                        // 在第三个点添加高亮
                                        name: 'redPoint',
                                        coord: [item.xIndex,item.yValue],
                                        value: '',
                                        symbol: 'circle', // 将点的形状设置为圆
                                        symbolSize: 5,   // 设置点的大小
                                        itemStyle: {
                                            color: '#e5443c' // 设置点的颜色为红色
                                        },
                                        // 添加标签
                                        label: i == dividsplit.length - 1 ?label : {}
                                    }
                                )
                            })
                        }
                        results.forEach((item, i) => {
                            item.nav = JSON.parse(item.nav.substring(1, item.nav.length - 1))
                            item.date = JSON.parse(item.date.substring(1, item.date.length - 1))
                            series.push({
                                type: 'line',
                                // name: item.indexName,
                                data: item.nav,
                                symbol: 'circle',
                                showSymbol: false,
                                areaStyle: {
                                    color: "rgba(0, 0, 0, 0)"
                                },
                                markPoint:markPoint,
                                itemStyle: { normal: { color: colorList[i], lineStyle: { color: colorList[i], } }, },
                            })
                        })
                        config.xAxis.data = results[0].date;
                        config.series = series
                        // 显示第一个和最后一个日期
                        // config.xAxis.axisLabel.interval = 244
                        config.xAxis.axisLabel.interval = results[0].date.length - 2
                        let dom = document.getElementById(`chartContainer`);
                        let myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        if (config && typeof config === 'object') {
                            myChart.setOption(config);
                            myChart.off('click');
                            myChart.on('click', {seriesType: 'line'},  function(params) {
                                params.event.event.stopPropagation();
                                setTimeout(function() {
                                    // 你的 click 事件处理逻辑
                                    if(params.name == 'redPoint'){
                                        // 获取 markPoint 的数据
                                        let xIndex = params.data.coord[0];
                                        //获取点击红点的日期
                                        let chooseDate = tools.ftime(results[0].date[xIndex]).split('-');
                                        // 在这里可以处理点击 markPoint 的逻辑，例如显示提示信息等
                                        layerUtils.iAlert(`${chooseDate[1]}月${chooseDate[2]}日分红，分红金额已红利再投或现金分红`);
                                    }
                                }, 100);
                            });
                        }
                    })
                },
                //单位净值走势 折线图
                earningsChart() {
                    tips = "单位净值走势";
                    service.reqFun102006({ fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code }, (result) => {
                        if (result.error_no == 0) {
                            var results = result.results;
                            var dataArr = [];
                            for (var i = 0; i < results.length; i++) {
                                var obj = {
                                    x: results[i].end_date.substring(0, 8),
                                    y: parseFloat(results[i].nav)
                                }
                                dataArr.push(obj);
                            }
                            dataArr = dataArr.reverse();
                            dataArr = dataArr.slice();
                            dataArr = this.datearr(dataArr);
                            var data = [
                                {
                                    name: this.tips,
                                    color: "rgba(632,64,63,1)",
                                    borderWidth: 2,
                                    data: dataArr
                                },
                            ];

                            this.showChart(data, this.tips, function (c) {
                                chat = c;
                                chat.rangeSelector.clickButton(activeClass);
                            })
                        } else {
                            layerUtils.iAlert(result.error_info);
                        }
                    });
                },
                // //累计净值
                // fixEarningsChart() {
                //     tips = "累计净值走势";
                //     service.reqFun102006({ fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code }, function (result) {
                //         if (result.error_no == 0) {
                //             var results = result.results;
                //             var dataArr = [];

                //             if (!results || results.length == 0) {
                //                 return;
                //             }
                //             var html = "";
                //             for (var i = 0; i < results.length; i++) {
                //                 var obj = {
                //                     x: results[i].end_date.substring(0, 8),
                //                     y: Number(results[i].accunav)
                //                 }
                //                 dataArr.push(obj);
                //                 if (i < 5) {
                //                     html += '<div class="item">' +
                //                         '<span>' + tools.ftime(results[i].end_date.substring(0, 8)) + '</span>' +
                //                         '<span>' + tools.fmoney(results[i].nav, 4) + '</span>' +
                //                         '<span>' + tools.fmoney(results[i].accunav, 4) + '</span>' +
                //                         '<span class=' + tools.addMinusClass(results[i].daily_return) + '>' + tools.fmoney(results[i].daily_return) + '%</span>' +
                //                         '</div>';
                //                 }
                //             }
                //             $(_pageId + " #historyContent .list_content").html(html);

                //             dataArr = dataArr.reverse();
                //             dataArr = dataArr.slice(spliceDate);
                //             dataArr = datearr(dataArr);
                //             var data = [
                //                 {
                //                     name: tips,
                //                     color: "rgba(632,64,63,1)",
                //                     borderWidth: 2,
                //                     data: dataArr
                //                 },
                //             ];

                //             showChart(data, tips, function (c) {
                //                 chat = c;
                //                 chat.rangeSelector.clickButton(activeClass);
                //             })
                //         } else {
                //             layerUtils.iAlert(result.error_info);
                //         }
                //     });
                // },

                addMinusClass(str) {
                    var numClass = "text_red";

                    if (str < 0) {
                        numClass = "text_green";
                    } else if (str > 0) {
                        numClass = "text_red";
                    } else {
                        numClass = "text_gray"
                    }
                    return numClass;
                },

                //设置时间为highChart所需时间格式
                datearr(data) {
                    for (var i = 0; i < data.length; i++) {
                        var x = data[i].x.toString();
                        Date.UTC()
                        data[i].x = Date.UTC(x.substring(0, 4), x.substring(4, 6) - 1, x.substring(6, 8));
                    }
                    return data;
                },

                //获取历史净值数据
                getHistory() {
                    var params = {
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code,
                        cur_page: "1",
                        num_per_page: "5"
                    }
                    service.reqFun102006(params, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results;
                            if (!results || results.length == 0) {
                                return;
                            }
                            var html = "";
                            results.forEach(item => {
                                var end_date = item.end_date;
                                if (end_date && end_date != "--" && end_date.length == 8) {
                                    end_date = tools.ftime(end_date.substring(0, 8));
                                } else {
                                    end_date = '20210908'
                                    end_date = tools.ftime(end_date.substring(0, 8));
                                }
                                //单位净值 
                                var nav = item.nav;
                                if (nav != "--") {
                                    nav = (+nav).toFixed(4);
                                }

                                //累计净值
                                var accunav = item.accunav;
                                if (accunav != "--") {
                                    accunav = (+accunav).toFixed(4);
                                }

                                //日涨跌幅
                                var rateClass = "add";
                                var daily_return = item.daily_return;
                                if (daily_return != "--") {
                                    rateClass = this.addMinusClass(daily_return);
                                    daily_return = (+daily_return).toFixed(2) + "%";
                                }

                                html += '<div class="item">' +
                                    '<span>' + end_date + '</span>' +
                                    '<span class="">' + nav + '</span>' +
                                    '<span class="">' + accunav + '</span>' +
                                    '<span class=' + rateClass + '>' + daily_return + '</span>' +
                                    '</div>';
                            })
                            $(_pageId + " #historyContent .list_content").html(html);
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })
                },

                //显示晋金宝折线图
                showChart(data, tips, callback) {
                    tips = this.tips || "单位净值";
                    this.timeOptions = {
                        lang: {
                            rangeSelectorZoom: null // 不显示 'zoom' 文字
                        },
                        rangeSelector: {
                            inputEnabled: false,
                            buttonPosition: '11',
                            buttons: [{
                                type: 'month',
                                count: 1,
                                text: '1月'
                            }, {
                                type: 'month',
                                count: 3,
                                text: '3月'
                            }, {
                                type: 'month',
                                count: 6,
                                text: '6月'
                            }, {
                                type: 'year',
                                count: 1,
                                text: '近1年'
                            }, {
                                type: 'year',
                                count: 3,
                                text: '近3年'
                            }, {
                                type: 'year',
                                count: 5,
                                text: '近5年'
                            }, {
                                type: 'all',
                                text: '成立以来'
                            }],
                            buttonTheme: {
                                display: "none"
                            }
                        },
                        scrollbar: {
                            enabled: false
                        },
                        navigator: {
                            enabled: false
                        },
                        chart: {
                            type: 'line',
                            panning: false, //禁用放大
                            pinchType: ''//禁用手势操作
                        },
                        title: {
                            text: null
                        },
                        xAxis: {
                            title: {
                                text: null
                            },
                            tickPixelInterval: 60,
                            gridLineWidth: 1,
                            // type: 'datetime',
                            // labels: {
                            //     format: '{value:%y/%m/%d}'
                            // },
                            dateTimeLabelFormats: {
                                millisecond: '%H:%M:%S.%L',
                                second: '%H:%M:%S',
                                minute: '%H:%M',
                                hour: '%H:%M',
                                day: '%m/%d',
                                week: '%m/%d',
                                month: '%y/%m',
                                year: '%Y'
                            }
                        },
                        yAxis: {
                            title: {
                                text: ''
                            },
                            gridLineWidth: 1,

                            options: {
                                startOnTick: false,
                                endOnTick: false,
                            },
                            labels: {
                                format: '{value}%'
                            },
                            tickPixelInterval: 20,
                        },
                        legend: {
                            enabled: false
                        },
                        plotOptions: {
                            series: {
                                turboThreshold: 0 //性能阈值检查,默认值为1000，当数据量超过这个值就会报错；如果需要关掉性能阈值检查，可以将此参数设置为 0
                            },
                        },
                        tooltip: {
                            backgroundColor: '#1E4DA9',   // 背景颜色
                            borderColor: '',         // 边框颜色
                            borderRadius: 0,             // 边框圆角
                            borderWidth: 0,               // 边框宽度
                            shadow: false,                 // 是否显示阴影
                            animation: true,               // 是否启用动画效果
                            crosshairs: "Mixed",
                            followTouchMove: true,
                            style: {                      // 文字内容相关样式
                                color: "#ffffff",
                                fontSize: "12px",
                                fontWeight: "normal",
                                fontFamily: "Courir new"
                            },
                            type: "datetime",
                            formatter: function (item) {
                                var time = new Date(this.x)
                                return "<div>" + time.getFullYear() + '/' + (time.getMonth() + 1) + '/' + time.getDate() + "</div>" +
                                    "<div>" + tips + ":" + this.y.toFixed(4) + "</div>"
                            },
                            useHTML: true,
                            headerFormat: '<table><small>{point.key}</small><table>',
                            pointFormat: '<tr><td >{series.name} : </td>' +
                                '<td style="text-align: right">{point.y}%</td></tr>',
                            footerFormat: '</table>',

                        },
                        series: [
                            //               {
                            //                name: "<span style='color:#ffffff'>" + tips + "</span>",
                            //                color:"rgba(632,64,63,1)",
                            //                borderWidth: 2,
                            //                data: data
                            //               }
                        ]
                    };
                    this.timeOptions.series = data;
                    if (this.tips == "七日年化") {
                        this.timeOptions.yAxis["labels"]["format"] = '{value}%';
                        this.timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                            '<td style="text-align: right">{point.y}%</td></tr>'
                    } else {
                        this.timeOptions.yAxis["labels"]["format"] = '{value}';
                        this.timeOptions.tooltip.pointFormat = '<tr><td >{series.name} : </td>' +
                            '<td style="text-align: right">{point.y}</td></tr>'
                    }
                    $(_pageId + ' #chartContainer').highcharts('StockChart', this.timeOptions, callback);
                    Highcharts.setOptions({
                        lang: {
                            rangeSelectorZoom: '' // 不显示 'zoom' 文字
                        }
                    });
                },
                //更多业绩表现
                page_performance() {
                    var isShowRank = $(_pageId + " #performanceContent").attr("data-show-rank") == 1 ? true : false;
                    var isShowAnnualized = $(_pageId + " #performanceContent").attr("data-show-annualized") == 1 ? true : false;
                    appUtils.setSStorageInfo("_isShowBank", isShowRank)
                    appUtils.setSStorageInfo("_isShowAnnualized", isShowAnnualized)
                    tools.recordEventData('1','performanceList','更多业绩表现');
                    appUtils.pageInit(_pageCode, "inclusive/performanceList");
                },
                //更多历史净值
                page_history() {
                    tools.recordEventData('1','historyValueList','更多历史净值');
                    appUtils.pageInit(_pageCode, "inclusive/historyValueList");
                },
                //交易规则
                page_rule() {
                    tools.recordEventData('1','BuyingSellingRule','交易规则');
                    appUtils.pageInit(_pageCode, "inclusive/BuyingSellingRule")
                },
                //大集合交易规则
                page_gatherRule() {
                    tools.recordEventData('1','gatherDetailRule','大集合交易规则');
                    appUtils.pageInit(_pageCode, "thfund/gatherDetailRule")
                },
                //大集合产品概况
                page_gatherRecord() {
                    tools.recordEventData('1','gatherDetailCompany','大集合产品概况');
                    appUtils.pageInit(_pageCode, "thfund/gatherDetailCompany")
                },
                //产品档案
                page_prodFiles() {
                    tools.recordEventData('1','publicProdFiles','产品档案');
                    appUtils.setSStorageInfo("mgrcomp_sname", this.publicDetatilData.mgrcomp_sname);
                    appUtils.pageInit(_pageCode, "template/publicProdFiles")
                },
                //基金公告
                page_notice() {
                    tools.recordEventData('1','jjThirtyDetailNotice','基金公告');
                    appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailNotice")
                },
                //基金文件
                page_file() {
                    tools.recordEventData('1','jjThirtyDetailFile','基金文件');
                    appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailFile")
                },
                // 基金档案
                page_record() {
                    tools.recordEventData('1','jjThirtyDetailRecord','基金档案');
                    appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailRecord")
                },
                // 基金经理
                page_manager() {
                    tools.recordEventData('1','jjThirtyDetailManager','基金经理');
                    appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailManager")
                },
                // 资产配置
                page_configuration() {
                    tools.recordEventData('1','jjThirtyDetailConfiguration','资产配置');
                    appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailConfiguration")
                },
                //基金公司
                page_company() {
                    tools.recordEventData('1','jjThirtyDetailCompany','基金公司');
                    appUtils.setSStorageInfo("mgrcomp_sname", this.publicDetatilData.mgrcomp_sname);
                    appUtils.pageInit(_pageCode, "inclusive/jjThirtyDetailCompany")
                },
                //定开产品上一封闭期问号
                thirty_tips1() {
                    tools.recordEventData('1','bondFixCloseList','定开产品上一封闭期问号');
                    appUtils.pageInit(_pageCode, "inclusive/bondFixCloseList");
                },
                //定开产品预计下一开放时间问号
                thirty_tips2() {
                    tools.recordEventData('1','thirty_tips2','定开产品预计下一开放时间问号');
                    layerUtils.iAlert("预计开放时间是平台根据基金合同估算，实际时间以基金公司公告为准");
                },
                //大集合转净值产品预计业绩计提问号
                thirty_tips3() {
                    tools.recordEventData('1','thirty_tips3','大集合转净值产品预计业绩计提问号');
                    layerUtils.iAlert("本产品业绩计提基准为" + this.publicDetatilData.p_expected_yield + "(不构成最低收益保证）");
                },
                pop_prod_income() {
                    $(_pageId + " #prod_income").show();
                },
                pop_prod_sharp() {
                    $(_pageId + " #prod_sharp").show();
                },
                pop_prod_retreat() {
                    $(_pageId + " #prod_retreat").show();
                },
                close_pop() {
                    $(_pageId + " .special_data_pop").hide();
                },
                //获取特色数据
                reqFun102135() {
                    var param = {
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code,
                    }
                    service.reqFun102135(param, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results[0];
                            this.characteristicData = results.characteristicData;
                            if(!this.characteristicData){
                                //隐藏特色数据
                                $(_pageId + " .special_data").hide();
                            }else{
                                $(_pageId + " .special_data").show();
                            }
                            if (results.assetAllocation && results.assetAllocation.length) {
                                $(_pageId + " .assetAllocation").show();
                                results.assetAllocation && results.assetAllocation.sort(function (a, b) { return parseFloat(b.pctof_total_asset) - parseFloat(a.pctof_total_asset) })
                                let arr = [];
                                let total_holdingv_val = (results.assetAllocation[0].net_asset / 100000000).toFixed(2);
                                results.assetAllocation.forEach(item => {
                                    arr.push({
                                        value: item.pctof_total_asset ? item.pctof_total_asset : 0,
                                        name: item.secu_category_name,
                                    })
                                })
                                let option = prodDetailAssetAllocationOption;
                                option.series[0].data = arr;
                                option.title.text = total_holdingv_val + "亿元"
                                option.legend.formatter = (name) => {
                                    var target;
                                    for (var i = 0, l = arr.length; i < l; i++) {
                                        if (arr[i].name == name) {
                                            target = arr[i].value;
                                        }
                                    }
                                    return name + '：' + target + '%';
                                }

                                let dom = document.getElementById(`chart-container`);
                                let myChart = echarts.init(dom, null, {
                                    renderer: 'canvas',
                                    useDirtyRect: false
                                });
                                if (option && typeof option === 'object') {
                                    myChart.setOption(option);
                                }
                            }else{
                                //隐藏资产配置
                                $(_pageId + " .assetAllocation").hide();
                            }
                            this.fndmgrList = results.fndmgrList;
                            if (this.fndmgrList && this.fndmgrList.length) {
                                this.fndmgrList.forEach(item => {
                                    item.serving_return = (+item.serving_return).toFixed(2) + '%';
                                    item.serving_date = tools.ftime(item.serving_date.substr(0, 8));
                                    item.manager_img = global.oss_url + item.manager_img;
                                })
                            }
                        } else {
                            layerUtils.iAlert(data.error_info);
                        }
                    })

                },
                // 显示更多区间
                showMoreSpliceDate() {
                    //$(_pageId + " #tooltip").parent().css({ "z-index": "999" })
                    tools.recordEventData('1','moreSpliceDate','显示更多区间');
                    $(_pageId + " .thfundBtn").hide();
                    $(_pageId + " #moreSpliceDate").show();
                },
                cancelMoreSpliceDate() {
                    tools.recordEventData('1','thfundBtn','隐藏更多区间');
                    $(_pageId + " .thfundBtn").show();
                    $(_pageId + " #moreSpliceDate").hide();
                },
                onClickPlayVideo() {
                    tools.recordEventData('1','onClickPlayVideo','点击视频');
                    isShow = true;
                    let html = `<video id="public_new_example_video" style="width:100%;height:100%" class="video-js vjs-default-skin vjs-big-play-centered" width="100%"
                        webkit-playsinline="true" playsinline="true" height="100%" controls preload="auto" poster=""
                        data-setup="{}">
                    </video>`
                    $(_pageId + " #new_example_div").html(html);
                    let param = this.publicDetatilData.video_info //存储数据格式
                    public_player = videojs('public_new_example_video', {
                    }, function onPlayerReady() {
                        //结束和暂时时清除定时器，并向后台发送数据
                        this.on('ended', function () {
                            // window.clearInterval(time1);
                        });
                        this.on('pause', function () {
                            // window.clearInterval(time1);
                        });
                        this.on('waiting', function () {
                            // window.clearInterval(time1);
                        })
                        //开始播放视频时，设置一个定时器，每100毫秒调用一次aa(),观看时长加1秒
                        this.on('playing', function () {
                            // window.clearInterval(time1);
                            // time1 = setInterval(function () {
                            //     t1 += 1;
                            //     if (t1 >= 20) {
                            //         //调用接口 清空时间 初始化时间0
                            //         window.clearInterval(time1);
                            //         t1 = 0;
                            //         // seeVideo();
                            //     }
                            // }, 1000);
                        });
                    });
                    t1 = 0;
                    // window.clearInterval(time1);
                    // playVideoData = param;  //选中当前选择的播放视频
                    public_player.reset();
                    public_player.src({ src: param.video_path })
                    public_player.load(param.video_path)
                    $(_pageId + " video").attr("poster", param.cover_path)
                    $(_pageId + " #showVideo").show()
                },
                getBack() {
                    tools.recordEventData('1','closeVideo','关闭视频');
                    public_player.pause();
                    $(_pageId + " #showVideo").hide()
                },
                getYield(isInit) {
                    section = this.activeClassPer;
                    var params = {
                        fund_code: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code,
                        section: section ? section : "12"
                    }
                    service.reqFun102128(params, (data) => {
                        if (data.error_no == 0) {
                            var results = data.results;
                            if (!results || results.length == 0) {
                                return;
                            }
                            this.showPer(results, isInit)
                        }
                    });
                },
                showPer(results, isInit) {
                    var yAxis = [];
                    var data = [];
                    var maxValue = parseFloat(results[0].income_per);
                    var max = results[0].income_between;
                    var probabilityNum = 0;
                    var orderNm = $(_pageId + " .perMar").attr("data-order");
                    var sectionProbability = 0;
                    for (let i = 0; i < results.length; i++) {
                        if (results[i].order_nm.substr(0, 1) == "1") {
                            probabilityNum = common.floatAdd(probabilityNum, results[i].income_per);
                        }
                        yAxis.push(results[i].income_between);
                        data.push(parseFloat(results[i].income_per));
                        if (maxValue < results[i].income_per) {
                            max = results[i].income_between;
                            maxValue = parseFloat(results[i].income_per)
                        }
                        if (orderNm > 0 && results[i].order_nm <= orderNm) {
                            sectionProbability = common.floatAdd(sectionProbability, results[i].income_per);
                        }

                    }

                    if (isInit) {
                        $(_pageId + " .perProbability").html(probabilityNum + "%");
                        $(_pageId + " .perNum").html(max);
                        $(_pageId + " .sectionProbability").html(sectionProbability + "%");
                    }
                    $(_pageId + " .perDate").html(tools.ftime(results[0].trans_date));

                    var myChart = echarts.init(document.getElementById('earningChartContainer'));
                    // 指定图表的配置项和数据
                    var option = {
                        tooltip: {
                            trigger: false
                        },
                        legend: {

                        },
                        grid: {
                            left: '2%',
                            right: '32%',
                            top: '0.2%',
                            bottom: '0',
                            containLabel: true //设置自适应画布大小状态为开，也可通过设置left左移实现相同效果。
                        },
                        xAxis: {
                            type: 'value',
                            show: false
                        },
                        yAxis: {
                            type: 'category',
                            data: yAxis,
                            axisLabel: {
                                interval: 0,
                                textStyle: {
                                    color: function (param) {
                                        if (param.substr(0, 1) == "-") {
                                            return '#00c35e'
                                        } else {
                                            return '#F7575E'
                                        }
                                    },
                                    fontWeight: 600,
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#F7575E',
                                    fontWeight: 600,
                                    width: 2
                                }
                            },
                            axisTick: {
                                show: false,
                                length: 1,
                            },
                        },
                        series: [{
                            type: 'bar',
                            data: data,
                            barMinHeight: 2,
                            barMaxHeight: 70,
                            barWidth: 20,
                            itemStyle: {
                                color: function (param) {
                                    if (param.name.substr(0, 1) == "-") {
                                        return '#C8CACF'
                                    } else {
                                        return '#E17A74'
                                    }
                                }
                            },
                            label: {
                                show: true,
                                position: 'right',//控制数据显示位置，‘’right‘’为显示在柱状图右侧
                                rich: {
                                    a: {
                                        backgroundColor: "#E17A74",
                                        color: "#fff",
                                        borderRadius: 8,
                                        padding: [2, 3],
                                        fontSize: 10,
                                        align: 'center',

                                    }
                                },
                                formatter: function (params) {
                                    if (params.data == maxValue) {
                                        return params.data + '%' + ' {a| 大概率}'
                                    } else {
                                        return params.data + '%'
                                    }
                                }
                            }
                        }]
                    };
                    myChart.setOption(option);
                    window.onresize = function (ec) { // 监听窗口大小变化
                        myChart.resize()       // 自适应大小变化
                    }
                },

                appletEnter(url) {
                    tools.recordEventData('1','applet','小程序');
                    tools.jump_applet(url);
                    return;
                }
            },

        })
    }
    //绑定事件
    function bindPageEvent() {
        //返回上个页面
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            tools.recordEventData('1','icon_back','返回');
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " .close"), function () {
            // window.clearInterval(time1);
            // public_player.pause();
            // $(_pageId + " #showVideo").hide()
            tools.recordEventData('1','closeVideo','关闭视频');
            public_player.pause();
            setTimeout(function () {
                public_player.dispose();
                public_player = '';
            }, 0);
            $(_pageId + " #showVideo").hide();
        });
        //客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.recordEventData('1','kefu','客服');
            tools.saveAlbum(_pageCode)
        });
        // appUtils.bindEvent($(_pageId + " #cancelShare"), async function () {
        //     $(_pageId + " #pop_layer").hide()
        // });
        // //分享到微信
        // appUtils.bindEvent($(_pageId + " #share_WeChat"), async function () {
        //     // let data = {
        //     //     shareTypeList:'22'
        //     // }
        //     tools.pageShare(shareData,'22',_pageId,_pageCode)
        // });
        // //分享到朋友圈
        // appUtils.bindEvent($(_pageId + " #share_WeChatFriend"), async function () {
        //     // let data = {
        //     //     shareTypeList:'23'
        //     // }
        //     tools.pageShare(shareData,'23',_pageId,_pageCode)
        // });
        // 点击风险提示，我知道了
        appUtils.bindEvent($(_pageId + " .risk_alert .know"), function () {
            tools.recordEventData('1','risk_alert','风险测评');
            hideRiskAlert()
        });
        //立即打开
        appUtils.bindEvent($("#open_tips .immediately-open"), function () {
            tools.recordEventData('1','immediately-open','立即打开');
            tools.jumpApp(pageParam);
        });


    }
    //页面销毁
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " .assetAllocation").hide();
        $(_pageId + " .special_data").hide();
        $(_pageId + " #share").hide();
        $(_pageId + " #kefu").hide();
        $(_pageId + " #pop_layer_pageShare").hide();
        $(_pageId + " #showVideo").hide();
        $(_pageId + " .thfundBtn").hide();
        $(_pageId + " #moreSpliceDate").hide();
        // if(public_player){
        //     public_player.pause();
        //     setTimeout(function () {
        //         public_player.dispose();
        //         public_player = '';
        //     }, 0);
        // }
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        pageInfo = ""; startTime = "";
        $(_pageId + " #share").removeAttr("has-share");
        isShow = false;
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        //原生返回判断视频弹窗是否打开,如果打开关闭弹窗
        if($(_pageId + " #showVideo").is(":visible")){
            public_player.pause();
            setTimeout(function() {
                public_player.dispose();
                public_player = '';
            }, 0);
            return $(_pageId + " #showVideo").hide();
        }
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        if (pageInfo && pageInfo.task_id && (pageInfo.task_type == '1' || pageInfo.task_type == '3') && !tools.getStayTime(startTime, pageInfo.duration)) {
            var remainTime = tools.getRemainTime(startTime, pageInfo.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                startTime = Date.now();
                pageInfo.duration = remainTime / 1000;
                let is_share = $(_pageId + " .pro_share").attr("is_share")
                if (is_share == "1") {
                    let data = {
                        busi_id: appUtils.getPageParam("busi_id") ? appUtils.getPageParam("busi_id") : productInfo.fund_code,
                        page_type: '1',
                        pageId: _pageId,
                        pageCode: _pageCode
                    }
                    if(pageInfo && pageInfo.activity_id) data.activity_id = pageInfo.activity_id;
                    tools.isShowShare(data, is_share, pageInfo, startTime)
                }
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (pageInfo.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: pageInfo.activity_id, task_id: pageInfo.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && pageInfo.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: pageInfo.activity_id, task_id: pageInfo.task_id });
                            }
                        }
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_pageCode, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }
    let publicOfferingDetailModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = publicOfferingDetailModule;
});
