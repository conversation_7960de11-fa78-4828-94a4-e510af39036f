// 合格投资者验证
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        ut = require("../common/userUtil"),
        service = require("mobileService"),
        publicUtil = require('./publicUtil'),
        validatorUtil = require("validatorUtil"),
        _page_code = "highEnd/qualifiedInvestor1",
        _pageId = "#highEnd_qualifiedInvestor1 ";
    var qualifiedInvestorStartAmount = require("gconfig").global.qualifiedInvestorStartAmount;
    var surplusAmount; //合格投资人还需金额
    var familySurplusIncome; // 合格投资人家庭所需资产
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        //页面埋点初始化
        tools.initPagePointData();
        // console.log(qualifiedInvestorStartAmount,1111)
        // $(_pageId + " #arrticle").show();
        $(_pageId + " .qualifiedInvestorStartAmount").html(qualifiedInvestorStartAmount / 10000 + "万");
        if(productInfo.prod_sub_type2 == '100'){
            $(_pageId + " .averageIncome").html(publicUtil.assetInfo[productInfo.prod_sub_type2][productInfo.prod_sub_type].averageIncome);
            $(_pageId + " .normal").addClass(publicUtil.assetInfo[productInfo.prod_sub_type2][productInfo.prod_sub_type].normal);
            $(_pageId + " .small").addClass(publicUtil.assetInfo[productInfo.prod_sub_type2][productInfo.prod_sub_type].small);
            $(_pageId + " .asset_proof").html(publicUtil.assetInfo[productInfo.prod_sub_type2][productInfo.prod_sub_type].asset_proof);
        }else{
            $(_pageId + " .averageIncome").html(publicUtil.assetInfo[productInfo.prod_sub_type2].averageIncome);
            $(_pageId + " .normal").addClass(publicUtil.assetInfo[productInfo.prod_sub_type2].normal);
            $(_pageId + " .small").addClass(publicUtil.assetInfo[productInfo.prod_sub_type2].small);
            $(_pageId + " .asset_proof").html(publicUtil.assetInfo[productInfo.prod_sub_type2].asset_proof);
        }
        getAssetInfo();
    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 充值入口
        appUtils.bindEvent($(_pageId + " .input"), function () {
            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                let operationId = "riskAssessment"
                layerUtils.iConfirm("您还未进行风险测评", function () {
                    appUtils.pageInit(_page_code, "safety/riskQuestion", {});
                }, function () {
                }, "去测评", "取消",operationId);
                return;
            }
            common.changeCardInter(function () {
                appUtils.pageInit(_page_code, "thfund/inputRechargePwd");
            })
        });
        //上传资产证明
        appUtils.bindEvent($(_pageId + " .upload1"), function () {
            appUtils.setSStorageInfo("assetInfo",
                {
                    surplusAmount: surplusAmount,
                    familySurplusIncome: familySurplusIncome,
                }
            )
            appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor2");
        });
        //上传收入证明
        appUtils.bindEvent($(_pageId + " .upload2"), function () {
            appUtils.setSStorageInfo("surplusAmount", surplusAmount.replace(/,/g, ""));
            appUtils.pageInit(_page_code, "highEnd/qualifiedInvestor3");
        });
        //完成
        appUtils.bindEvent($(_pageId + " #next"), function () {
            appUtils.pageBack();
        });

    }

    function getAssetInfo() {
        service.reqFun101932({}, function (data) {
            if (data.error_no == "0") {
                var result = data.results[0];
                var totalAssets = result.total_assets ? common.fmoney(result.total_assets, 2) : "--";//总资产
                $(_pageId + " .totalAssets").html(totalAssets);
                if (parseFloat(result.total_assets) >= parseFloat(qualifiedInvestorStartAmount)) { //持有资产大于 300万
                    service.reqFun101039({}, function (data) {
                        if (data.error_no != "0") {
                            layerUtils.iAlert(data.error_info, -1, function () {
                                appUtils.pageBack();
                            });
                        }
                        var user = appUtils.getSStorageInfo("user");
                        user.hgSoonInvalidState = "5";
                        appUtils.setSStorageInfo("user", user);
                        $(_pageId + " #qualifiedInvestor").show();
                        $(_pageId + " #arrticle").hide();
                    })
                    return;
                }
                $(_pageId + " #arrticle").show();
                $(_pageId + " #qualifiedInvestor").hide();
                layerUtils.iLoading(false);


                surplusAmount = common.fmoney((parseFloat(qualifiedInvestorStartAmount) - parseFloat(result.total_assets)).toString());
                // familySurplusIncome = common.fmoney((parseFloat(publicUtil.assetInfo[productInfo.prod_sub_type2].familyIncome * 10000) - parseFloat(result.totalAssets)).toString());
                if(productInfo.prod_sub_type2 == '100'){
                    familySurplusIncome = common.fmoney((parseFloat(publicUtil.assetInfo[productInfo.prod_sub_type2][productInfo.prod_sub_type].familyIncome * 10000) - parseFloat(result.total_assets)).toString());
                }else{
                    familySurplusIncome = common.fmoney((parseFloat(publicUtil.assetInfo[productInfo.prod_sub_type2].familyIncome * 10000) - parseFloat(result.total_assets)).toString());
                }
                $(_pageId + " .surplusAmount").html(surplusAmount);
                $(_pageId + " .familySurplusIncome").html(familySurplusIncome);
            } else {
                layerUtils.iLoading(false);
                $(_pageId + " .totalAssets").html("--");
                $(_pageId + " .surplusAmount").html("--");
                layerUtils.iAlert(data.error_info);
            }
        }, {isLastReq: false})
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #arrticle").hide();
        $(_pageId + " #qualifiedInvestor").hide();
        $(_pageId + " .normal").hide();
        $(_pageId + " .smallGather").hide();
        $(_pageId + " .normal").removeClass("hidden").removeClass("block");
        $(_pageId + " .small").removeClass("hidden").removeClass("block");
        surplusAmount = "";
        familySurplusIncome = "";
    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    // 暴露对外的接口
    module.exports = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    }
});
