//公司咨询
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _pageId = "#moreDetails_consultation ";
    var page = 1;
	var tools = require("../common/tools");
	var currentPage;
	var totalPages;
    function init() {
        // 获取所有咨询
        getAllConsultation(1, false);
        var pageTouchTimer = null;
        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
            pageTouchTimer = setTimeout(function () {
                vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
            }, 500);
        }, "touchmove");

        appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
            pageTouchTimer && clearTimeout(pageTouchTimer);
        }, "touchend");

    }

    //绑定事件
    function bindPageEvent() {
        //点击返回按钮
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        // 点击进入公告详情页
        appUtils.preBindEvent($(_pageId + " .announcement_box"), ".announcement_inner", function () {
            var id = $(this).attr("id");
            var param = {
                "id": id
            };
            appUtils.pageInit("moreDetails/consultation", "moreDetails/consultationDetails", param);
        }, 'click');

    }

    //获取公告
    function getAllConsultation(list_page, isAppendFlag) {
        var param = {
            cur_page: list_page,
            num_per_page: "10",
        };
        service.reqFun102055(param, function (data) {
            var error_no = data.error_no;
            var error_info = data.error_info;
            var str = "";
            if (error_no == "0") {
                currentPage = data.results[0].currentPage;// 当前页数
                totalPages = data.results[0].totalPages;// 总页数
                var result = (data.results)[0];
                var rstData = result.data;
				for (var i = 0; i < rstData.length; i++) {
					var title = rstData[i].title; //公告标题
					var createDate = rstData[i].createDate.substr(0,8); //发布时间
					var id = rstData[i].id; //公告id
					str += "<div class='announcement_inner' class='announcement_inner' id=" + id + ">" +
						"<strong>" + title + "</strong>" +
						"<p>" + tools.ftime(createDate) + "</p>" +
						"</div>";
				}
                if (isAppendFlag) {
                    $(_pageId + " .announcement_box").append(str);
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();

                } else {
                    $(_pageId + " .announcement_box").html(str);
                }
                pageScrollInit();
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    /** 上下滑动刷新事件* */
    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, // 这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    page = 1;
                    getAllConsultation(page, false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (currentPage < totalPages) {
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        page += 1;
                        getAllConsultation(page, true);
                    }
                },
                "wrapperObj": null
            };
            vIscroll.scroll = new VIscroll(config); // 初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }

        if (currentPage == totalPages) {
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }

    }

    function destroy() {
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var notice = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = notice;
});
