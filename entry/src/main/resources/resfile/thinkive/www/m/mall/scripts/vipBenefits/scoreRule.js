// 积分规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageCode = "vipBenefits/scoreRule",
        validatorUtil = require("validatorUtil"),
        _pageId = "#vipBenefits_scoreRule ";
    var service = require("mobileService");
    var tools = require("../common/tools");

    function init() {
        reqFun108024();//活动规则查询
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //活动规则查询(108024)
    function reqFun108024() {
        service.reqFun108024({}, (data)=> {
            if (data.error_no == 0) {
                var results = data.results[0];
                if(validatorUtil.isEmpty(results) || validatorUtil.isEmpty(results.introduce)){
                    $(_pageId + " .reward_rule").show();
                    $(_pageId + " .invite_box .rule_inner .rule").html('<h4 style="height: 1.2rem;width: 100%;text-align: center;line-height: 1rem">暂无数据</h4>');
                    return;
                }
                $(_pageId + " .reward_rule").show();
                $(_pageId + " .invite_box .rule_inner .rule").html(results.introduce).show();
            } else {
                $(_pageId + " .reward_rule").show();
                $(_pageId + " .invite_box .rule_inner .rule").html('<h4 style="height: 1.2rem;width: 100%;text-align: center;line-height: 1rem">暂无数据</h4>');
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
