define(function (require, exports, module) {
    var appUtils = require("appUtils"),
            layerUtils = require("layerUtils"),
            service = require("mobileService"),
            common = require("common"),
            _pageId = "#highEnd_smallgatherBuy ";
    var gconfig = require("gconfig");
    var ut = require("../common/userUtil");
    var _pageCode = "highEnd/smallgatherBuy";
    var calculator = require("../common/calculator");
    var tools = require("../common/tools");
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var threshold_amount;
    var jymm;
    var addition_amt;//追加金额
    var step_amt;//递增金额
    var _available_vol;
    var buy_state; //1 购买、2 预约
    var isFirstPurchase;// 是否首次购买产品
    var _first_max_amt;  //单笔最高限额
    var purchaseRateList = [];//申购费率、认购费率表
    var purchaseRateStr; // 认购、申购字段
    var step_amt_str;
    var buyflag;  //风险等级是否匹配   1 不匹配
    var productInfo;
 
    function init() {
        userInfo = ut.getUserInf();
        appUtils.clearSStorage("productInfo_jjb"); //删除华安汇财通本地信息，防止本地信息混淆
        productInfo = appUtils.getSStorageInfo("productInfo")
        //页面埋点初始化
        tools.initPagePointData({fundCode:productInfo.fund_code});
        //可用份额
        reqFun101901();
        //获取时间
        //reqFun102008();
        //查询该产品是否首次购买
//        reqFun102072();
        reqFun102103();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
    }

    function bindPageEvent() {
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            tools.intercommunication(_pageCode);
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            tools.jumpPriDetailPage(_pageCode, productInfo.prod_sub_type2);
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_smallgatherBuy";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //打开协议弹框
        appUtils.bindEvent($(_pageId + " #agreement_btn"), function () {
            guanbi();
            $(_pageId + " #agreementList").show();
            $(_pageId + " .pop_layer").show();
        });
        //关闭协议弹框
        appUtils.bindEvent($(_pageId + " #agree_closeBtn"), function () {
            $(_pageId + " #agreementList").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //点击交易规则
        appUtils.bindEvent($(_pageId + " .trade_rule .rule"), function () {
            appUtils.pageInit(_pageCode, "highEnd/tradeRules");
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
            setRate();
        });
        // 打开pdf协议
        appUtils.bindEvent($(_pageId + " .xy"), function () {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        });
        //风险承受能力确认
        appUtils.bindEvent($(_pageId + " .agreement1"), function () {
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active")
            } else {
                $(this).find("i").addClass("active")
            }
        });
        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });

        //显示购买弹框
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            if((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            var moneys = $(_pageId + " #czje").val().replace(/,/g, "");
            var isverify = $(_pageId + " .agreement").attr("isverify");
            // if (!$(_pageId + " .agreement1 i").hasClass("active") && isverify == "true") {
            //     layerUtils.iAlert("请勾选风险说明");
            //     return;
            // }
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (moneys <= 0 || !moneys) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (threshold_amount && parseFloat(moneys) < parseFloat(threshold_amount) && isFirstPurchase) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            if (addition_amt && parseFloat(moneys) < parseFloat(addition_amt) && !isFirstPurchase) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            if (isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.threshold_amount, productInfo.step_amt)) return
            if (!isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.addition_amt, productInfo.step_amt)) return
            if (_first_max_amt && moneys > parseFloat(_first_max_amt)) {
                layerUtils.iAlert("超过单笔最高限额");
                return;
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #payMethod").show();
            //是否可以购买
            isCanBuy();
        });

        //切换购买状态
//        appUtils.bindEvent($(_pageId + " .pay_method"), function () {
//            var buy_money = $(_pageId + " #czje").val().replace(/,/g, "");
//            if (totalMoney < buy_money) {
//                $(_pageId + " .pay_method").removeClass("active").filter(".bank_pay").addClass("active");
//                return;
//            }
//            $(_pageId + " .pay_method").removeClass("active").filter(this).addClass("active");
//        });

        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "highEnd_smallgatherBuy";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            //进行充值
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                app_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                fund_code: productInfo.fund_code,
                buyflag: buyflag,
                period: productInfo.period,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no")//协议签署流水号
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                service.reqFun106014(param, function (resultVo) {
                    if (resultVo.error_no == "0") {
                        if (buy_state == "1") { //购买
                            purchase(param);
                        } else if (buy_state == "2") { // 预约
                            apponit(param);
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(resultVo.error_info);
                    }
                }, {isLastReq: false});
            }, {isLastReq: false});
        });
    }
    // 100 产品详情查询
    // function new_getFundInfo(){
    //     service.reqFun102108(productInfo, function (data) {
    //         if (data.error_no == 0) {
    //             var result = data.results[0];
    //             productInfo = result;
    //             $(_pageId + " .prod_sname").html(result.prod_sname);//产品简称
    //             if (result.prod_sub_type2 == "95" || result.prod_sub_type2 == "96" || result.prod_sub_type2 == "81") { // 政金债、申港小集合、一创小集合
    //                 $(_pageId + " .trade_rule_tip2").html("");
    //             } else {
    //                 $(_pageId + " .trade_rule_tip2").html("温馨提示：预计下一开放期：" + result.estimated_opening_start.substr(2, 2) + "." + result.estimated_opening_start.substr(4, 2) + "." + result.estimated_opening_start.substr(6, 2) + "-" + result.estimated_opening_end.substr(2, 2) + "." + result.estimated_opening_end.substr(4, 2) + "." + result.estimated_opening_end.substr(6, 2));
    //             }

    //             threshold_amount = result.threshold_amount;//起购金额
    //             addition_amt = result.addition_amt;//追加金额
    //             step_amt = result.step_amt;//递增金额
    //             var str = "";
    //             if (threshold_amount && isFirstPurchase) {
    //                 str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
    //             } else if (addition_amt && !isFirstPurchase) {
    //                 str += (addition_amt >= 10000 ? (addition_amt / 10000 + "万") : tools.fmoney(addition_amt)) + '元起购';
    //             }

    //             if (step_amt && step_amt > 0) {
    //                 str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
    //                         tools.fmoney(step_amt + ''))
    //                         + "元递增"
    //             }
    //             if (str) {
    //                 $(_pageId + " #inputspanid span").text(str);
    //                 $(_pageId + " #inputspanid span").attr("text", str);
    //             } else {
    //                 $(_pageId + " #inputspanid span").text("请输入购买金额");
    //                 $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
    //             }
    //             //单笔最高限额
    //             _first_max_amt = result.first_max_amt;
    //             buy_state = result.buy_state
    //             tools.getPdf("prod", productInfo.fund_code, buy_state); //获取协议
    //             appUtils.setSStorageInfo("productInfo", result);
    //             initBuyFlag(result);
    //             queryFl(); // 费率计算
    //         } else {
    //             layerUtils.iAlert(data.error_info);
    //         }
    //     })
    // }
    // 产品详情查询
    function getFundInfo() {
        let result
        if(productInfo.prod_sub_type2 == "100"){
            //新详情接口
            service.reqFun102108(productInfo, function (data) {
                if (data.error_no == 0) {
                    result = data.results[0];
                    result.prod_sub_type2 = "100"
                    handleData(result)
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }else{
            //老详情接口
            service.reqFun102043(productInfo, function (data) {
                if (data.error_no == 0) {
                    result = data.results[0];
                    handleData(result)
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        }
    }
    function handleData(result){
        productInfo = Object.assign(productInfo,result);
        $(_pageId + " .prod_sname").html(result.prod_sname);//产品简称
        if (result.prod_sub_type2 == "95" || result.prod_sub_type2 == "100" || result.prod_sub_type2 == "96" || result.prod_sub_type2 == "81") { // 政金债、申港小集合、一创小集合
            $(_pageId + " .trade_rule_tip2").html("");
        } else {
            $(_pageId + " .trade_rule_tip2").html("温馨提示：预计下一开放期：" + result.estimated_opening_start.substr(2, 2) + "." + result.estimated_opening_start.substr(4, 2) + "." + result.estimated_opening_start.substr(6, 2) + "-" + result.estimated_opening_end.substr(2, 2) + "." + result.estimated_opening_end.substr(4, 2) + "." + result.estimated_opening_end.substr(6, 2));
        }

        threshold_amount = result.threshold_amount;//起购金额
        addition_amt = result.addition_amt;//追加金额
        step_amt = result.step_amt;//递增金额
        var str = "";
        if (threshold_amount && isFirstPurchase) {
            str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
        } else if (addition_amt && !isFirstPurchase) {
            str += (addition_amt >= 10000 ? (addition_amt / 10000 + "万") : tools.fmoney(addition_amt)) + '元起购';
        }

        if (step_amt && step_amt > 0) {
            str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                    tools.fmoney(step_amt + ''))
                    + "元递增"
        }
        if (str) {
            $(_pageId + " #inputspanid span").text(str);
            $(_pageId + " #inputspanid span").attr("text", str);
        } else {
            $(_pageId + " #inputspanid span").text("请输入购买金额");
            $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
        }
        //单笔最高限额
        _first_max_amt = result.first_max_amt;
        buy_state = result.buy_state
        tools.getPdf("prod", productInfo.fund_code, buy_state); //获取协议
        // appUtils.setSStorageInfo("productInfo", result);
        initBuyFlag(result);
        queryFl(); // 费率计算
    }
    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];

            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额：<em class="money">' + tools.fmoney(_available_vol + "") + '</em>元';
            $(_pageId + " .pay_bank").html(html);
        })
    }

    //买入费率
    function queryFl() {
        service.reqFun102003(productInfo, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                if (productInfo.businesscode == "20") {  // 22申购 20认购
                    purchaseRateList = result.subscription;
                    purchaseRateStr = "认购费";
                } else if (productInfo.businesscode == "22") {
                    purchaseRateList = result.purchaseRate;
                    purchaseRateStr = "申购费";
                }
                $(_pageId + " .mrfl").text(purchaseRateList[0] ? purchaseRateStr + "率" + purchaseRateList[0].chgrate_tval + purchaseRateList[0].chgrate_unit : "免" + purchaseRateStr)
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function setRate() {
        var money = $(_pageId + " #czje").val();
        money = money ? money.replace(/,/g, "") : 0;
        if (purchaseRateList.length == 0) {
            $(_pageId + " .mrfl").text("免" + purchaseRateStr);
        } else {
            purchaseRateList.forEach(function (item, index) {
                if (item.fcitem_tval == -1 && parseFloat(item.fcitem_lval) <= parseFloat(money) || parseFloat(item.fcitem_lval) <= parseFloat(money) && parseFloat(money) < parseFloat(item.fcitem_tval)) {
                    $(_pageId + " .mrfl").text(purchaseRateStr + "率" + item.chgrate_tval + item.chgrate_unit)
                }
            })
        }
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //现金宝认购、申购产品
    function purchase(param) {
        service.reqFun106003(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "highEnd/purchaseResult", data.results[0]);
        })
    }

    // 现金宝预约产品
    function apponit(param) {
        service.reqFun106008(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "highEnd/purchaseResult", data.results[0]);
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            isShowUpMoney: true,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                if (isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.threshold_amount, productInfo.step_amt)) return
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.addition_amt, productInfo.step_amt)) return
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #czje").val(moneys);
                setRate();

            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if(!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
                setRate();
                if (isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.threshold_amount, productInfo.step_amt)) return
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, productInfo.addition_amt, productInfo.step_amt)) return
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }

    //查询产品是否首次购买
    function reqFun102103() {
        service.reqFun102103(productInfo, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (results.is_can_add == 0) {
                    isFirstPurchase = false;
                } else {
                    isFirstPurchase = true;
                }
            } else {
                isFirstPurchase = true;
            }
            //获取产品详情
            getFundInfo()
            
        }, {isLastReq: false})
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html($(_pageId + " #czje").val());
    }

    function initBuyFlag(productInfo) {
        if ((parseFloat(userInfo.riskLevel.substr(1)) >= parseFloat(productInfo.risk_level.substr(1))) || parseFloat(productInfo.risk_level.substr(1))== 1) {
            buyflag = "";
        } else {
            buyflag = "1";
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        guanbi();
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");//产品编码
        $(_pageId + " .fund_type_name").html("--");//产品编码
        $(_pageId + " #czje").val("");
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " #inputspanid span").text("").css({color: "rgb(153, 153, 153)"});
        $(_pageId + " .password_box").hide();
        buy_state = "";
        _first_max_amt;
        monkeywords.destroy();
        step_amt_str = "";
        purchaseRateList = [];
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " #payMethod").hide();
        $(_pageId + " .trade_rule_tip2").html("");
        $(_pageId + " .jjs_yue").hide();

    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
