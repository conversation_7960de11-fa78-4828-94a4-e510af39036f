/*创建时间hSea 2015-12-31 15:36:59 PM */
define(function(require,exports,module){function a(a){a=a?a:{};var e=a.container,f=a.pagingObj;e.find(".current_page").html(a.pagingObj.curPage),e.find(".total_page").html(a.pagingObj.totalPage),c.bindEvent(e.find("ul .active"),function(){b(a)}),c.bindEvent(e.find("ul .home"),function(){1!=f.curPage&&(a.gotoPage(1),e.find(".page_list").hide())}),c.bindEvent(e.find("ul .last"),function(){f.curPage!=f.totalPage&&(a.gotoPage(f.totalPage),e.find(".page_list").hide())}),c.bindEvent(e.find("ul .pre"),function(){f.curPage>1&&(a.gotoPage(f.curPage-1),e.find(".page_list").hide())}),c.bindEvent(e.find("ul .next"),function(){f.curPage<f.totalPage&&(a.gotoPage(f.curPage+1),e.find(".page_list").hide())}),c.bindEvent(e.find(".page_list .page_input_page .btn"),function(){var b=a.container,c=a.pagingObj,e=c.totalPage,f=c.curPage,g=Number(b.find(".page_list .page_input_page .t1").val());g>0&&e>=g&&g!=f?a.gotoPage&&(a.gotoPage(g),b.find(".page_list").hide()):d.iMsg(-1,"请输入有效跳转的页数！"),b.find(".page_list .page_input_page .t1").val("")})}function b(a){var b=a.container,d=a.wrapper,f=a.scroller,g=a.pagingObj;if(b.find(".current_page").html(a.pagingObj.curPage),b.find(".total_page").html(a.pagingObj.totalPage),g.totalPage>1)if(b.find(".page_list").is(":visible"))b.find(".page_list").hide();else{if(!g._init){for(var h=g.totalPage,i=h%15==0?parseInt(h/15):parseInt(h/15)+1,j="",k=1;i>=k;k++){for(var l='<div class="page_list_box">',m=k==i&&h%15!=0?h%15:15,n=1;m>=n;n++){var o=15*(k-1)+n;l+='<a id="pageNo'+o+'" href="javascript:void(0);" data-idx="'+o+'"><span>'+o+"</span></a>"}l+="</div>",j+=l}f.html(j),c.bindEvent(f.find("a"),function(){var c=$(this).attr("data-idx"),d=g.curPage;c!=d&&(a.gotoPage(c),b.find(".page_list").hide())}),g._init=!0}var p=g.curPage,q=f.find("a").eq(p-1);if(q.addClass("active").siblings().removeClass("active"),b.find(".page_list").show(),g.iscrollPage)g.iscrollPage.setCssHW();else{var r={wrapper:d,scroller:f,perCount:1,showTab:!1,auto:!1,onScrollEnd:function(){var a=g.totalPage,c=a%15==0?parseInt(a/15):parseInt(a/15)+1,d=g.iscrollPage.getActivePage()+1;b.find(".page_list h4 span").attr("class",""),d==c&&b.find(".page_list h4 span").addClass("no_next"),1==d&&b.find(".page_list h4 span").addClass("no_pre")}};g.iscrollPage=new e(r)}var s=g.curPage,t=s%15==0?parseInt(s/15):parseInt(s/15)+1;g.iscrollPage.scrollToPage(t-1,0)}}var c=require("appUtils"),d=require("layerUtils"),e=require("hIscroll");require("../css/paging.css");var f={init:a};module.exports=f});
/*创建时间 2015-12-31 15:36:59 PM */