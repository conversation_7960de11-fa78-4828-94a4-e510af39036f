// /**
//  * 页面埋点初始化数据
//  */

// const burialPointList = {
//     pageUuid: "",//数据唯一标识
//     pageId: "", //页面ID
//     enterTime: "",//进入时间
//     eventList: [    //点击事件埋点集合
        
//     ],
//     leaveTime: "",//离开时间
//     channelId: "",//渠道ID
//     equipmentId: "",//设备ID
//     netWorkStatus: "",//网络状态 wife/数据流量
//     platform: "",//1：android手机壳子嵌phonegap、2：ios手机壳子嵌phonegap
//     custNo: "",//客户号 空为未登录状态
//     fundCode: "",//产品ID
//     bannerId: "",//bannerID
//     adId: "",//活动ID
//     mailId: "",//消息ID（站内信）
//     popId: "",//弹框ID
//     fundMsgId: "",//产品资讯id
//     fundNoticeId: "",//产品公告id
//     fundFileId: "",//产品文件id
//     fundAgreementId: "",//产品协议id
//     noticeId: "",//平台资讯id
//     newsId: "",//平台公告id
//     articlId: "",//学投资文章id
//     remark: "",//备注
// }
// /**
//  * 点击事件埋点初始化数据
//  */
// let clickPointData = {
//     operationTime: "",//操作时间
//     operationId: "",//操作ID
//     operationType: "",//操作类型
//     operationName: "",//操作名称
//     netWorkStatus: "",//网络状态 wife/数据流量
//     fundCode: "",//产品ID
//     bannerId: "",//bannerID
//     adId: "",//活动ID
//     mailId: "",//消息ID（站内信）
//     popId: "",//弹框ID
//     msgId: "",//产品资讯id
//     fundNoticeId: "",//产品公告id
//     fundFileId: "",//产品文件id
//     fundAgreementId: "",//产品协议id
//     noticeId: "",//平台资讯id
//     newsId: "",//平台公告id
//     articlId: "",//学投资文章id
//     remark: "",//备注
// }
/**
 * 埋点集合
 */
define((require, exports, module)=> {
    require('./pdf.worker.js')
    var appUtils = require("appUtils")
    var gconfig = require("gconfig");
    let layerUtils = require("layerUtils");
    let service = require("mobileService");
    let global = gconfig.global;
    // 页面埋点参数
    let pageData = {
        pageUuid: tools.generateUUID(),//数据唯一标识
        pageId: "", //页面ID
        enterTime: new Date().getTime(),//进入时间
        eventList: [],//事件埋点集合
        leaveTime: "",//离开时间
        channelId: "",//渠道ID
        equipmentId: "",//设备ID
        netWorkStatus: "",//网络状态 wife/数据流量
        platform: "",//1：android手机壳子嵌phonegap、2：ios手机壳子嵌phonegap
        custNo: "",//客户号 空为未登录状态
        fundCode: "",//产品ID
        bannerId: "",//bannerID
        adId: "",//活动ID
        mailId: "",//消息ID（站内信）
        popId: "",//弹框ID
        fundMsgId: "",//产品资讯id
        fundNoticeId: "",//产品公告id
        fundFileId: "",//产品文件id
        fundAgreementId: "",//产品协议id
        noticeId: "",//平台资讯id
        newsId: "",//平台公告id
        articlId: "",//学投资文章id
        remark: "",//备注
        essayId: "",//文章id
        catalogId: "",//栏目id
    }
    //事件埋点参数
    let eventData = {

    }
	var get_pdf_file ={
        //页面进出方法
        showStrongHint:()=>{
            pageData.enterTime = new Date().getTime();
            pageData.pageUuid = tools.generateUUID();
            pageData.pageId = pageId;
        },
        //事件埋点
        clearTime:()=>{
            
        },

    };
    
    // 暴露对外的接口
    module.exports = get_pdf_file;
})
