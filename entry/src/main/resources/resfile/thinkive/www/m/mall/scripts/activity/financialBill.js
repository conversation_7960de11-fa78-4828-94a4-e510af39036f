//活动 - 理财账单
define(function (require, exports, module) {
    var appUtils = require("appUtils");
    var service = require("mobileService");
    var common = require("common");
    var tools = require("../common/tools");
    var _pageId = "#activity_financialBill ";
    var _pageUrl = "activity/financialBill"
    var layerUtils = require("layerUtils");
    var bill_type = "1";
    var isEnd = false;
    var timeData,mobileSelectTime,chooseData;
    var cur_page = 1;
    var num_per_page = 15;
    var totalPages = 1;
    var hisdate;//拥有理财账单最小年份
    var fullYear,fullMonth;//当前选中年份,月份
    var VIscroll = require("vIscroll");
    var showType = '0'; //用户选择类型 0月份 1年份
    // require('../common/timeList.js');
    // require('../../js/zepto.min.js');
    // require('../../js/iscroll.js');
    // require('../../js/iosSelect.js');
    function init() {
        showType = '0';//初始化默认展示月份
        //获取当前渲染数据的时间，初始化数据
        getAdjustedYearMonth();
        //获取账单列表
        // getFinancialBill();
        //获取账单详情
        
        // vIscroll = { "scroll": null, "_init": false };
        // //初始化数据
        // creatData()
        // $(".btnBar").append('<div class="choose main_flxe flex_center"><ul class="active" bill_type="1">按月</ul><ul bill_type="0">按年</ul></div>');
        // $(".panel").css("margin-top","45px");
        // setBindClick();
        // var pageTouchTimer = null;
        // appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
        //     pageTouchTimer && clearTimeout(pageTouchTimer);
        //     pageTouchTimer = setTimeout(function () {
        //         vIscroll.scroll && vIscroll.scroll.getWrapperObj() && vIscroll.scroll.getWrapperObj().scrollTo(0, 0, 200);
        //     }, 500);
        // }, "touchmove");

        // appUtils.bindEvent($(_pageId + " #v_container_productList"), function () {
        //     pageTouchTimer && clearTimeout(pageTouchTimer);
        // }, "touchend");
        
        
        
    };
    //初始化当前选中
    function getAdjustedYearMonth() {
        let currentDate = new Date();
        let year = currentDate.getFullYear(); // 获取当前年份‌:ml-citation{ref="5,6" data="citationList"}
        let month = currentDate.getMonth(); // 月份范围0-11（对应1-12月）‌:ml-citation{ref="1,5" data="citationList"}
        let day = currentDate.getDate(); // 获取当前日期‌:ml-citation{ref="5,7" data="citationList"}
    
        // 根据日期决定减去的月份数
        let subtractMonths = day > 16 ? 1 : 2; // 逻辑判断‌:ml-citation{ref="1,2" data="citationList"}
    
        // 计算总月份数并转换为新的年和月
        let totalMonths = year * 12 + month - subtractMonths; // 总月份计算‌:ml-citation{ref="1,8" data="citationList"}
        let newYear = Math.floor(totalMonths / 12);
        let newMonth = (totalMonths % 12 + 12) % 12; // 处理负数情况‌:ml-citation{ref="1,8" data="citationList"}
        // 格式化月份为两位数字（如1→"01"）
        let formattedMonth = String(newMonth + 1); // 格式处理‌:ml-citation{ref="5,7" data="citationList"}
        fullYear = newYear;
        fullMonth = formattedMonth;
        $(_pageId + " .fullYear").text(fullYear + '年')
        getFinancialBill(showType,newYear + '',null,true);
        
        // getFinancialBillDetails(showType,showType == '0' ? newYear:(newYear - 1),formattedMonth.padStart(2, '0'))
        
    }
    //获取账单详情
    function getFinancialBillDetails(bill_type,newYear,formattedMonth){
        querydate = showType == '0' ? newYear + formattedMonth : newYear;
        let data = {
            bill_type:bill_type,
            querydate:querydate
        }
        service.reqFun102221(data, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let res = datas.results[0] 
            // if(!res || !res.data || !res.data.length) return $(_pageId + " .noList").show();
            //渲染选中的收益详情
            let chooseDetailsTxt = showType == '0' ? `${newYear}年${formattedMonth}月收益（元）` : `${newYear}年收益（元）`;
            $(_pageId + " .chooseDetails").text(chooseDetailsTxt);
            $(_pageId + " .chooseDetailsMoney").removeClass("red green")
            $(_pageId + " .chooseDetailsMoney").addClass((res.total_revenue*1)<0?'green':'red')
            $(_pageId + " .chooseDetailsMoney").text(res.total_revenue > 0 ? ('+' + res.total_revenue) : res.total_revenue)
            //渲染列表
            setList(res.data)
            $(_pageId + " .haveList").show();
        })
    }
    //渲染列表
    function setList(list){
        if(!list || !list.length) {
            $(_pageId + " .incomeComposition-list").html(``);
            $(_pageId + " .incomeComposition").hide();
            return;
        } 
        let html = ``;
        list.map(item=>{
            let listChild = JSON.parse(item.class_data)
            let htmlChild = ``;
            listChild.map(its=>{
                htmlChild += `
                                <li class="flex">
                                    <p class="m_bold">${its.prod_sname}<span class="sale_all ${its.sale_sign == '1' ? '' : 'new_display_none'}">${its.sale_sign == '1' ? '清仓' : ''}</span></p>
                                    <p class="${(its.revenue*1)<0 ? 'green' : 'red'}"">${its.revenue > 0 ? ('+' + its.revenue) : its.revenue}</p>
                                </li>
                            `
            })
            html += `<ul class="flex list-title vertical_center">
                        <li class="m_bold color_000 m_font_size16">
                            <span>${item.classify_info}</span>
                            <img class="icon_arrow" src="./images/expand.png" alt="">
                        </li>
                        <li class="m_bold ${(item.class_revenue*1)<0 ? 'green' : 'red'}">${item.class_revenue > 0 ? ('+' + item.class_revenue) : item.class_revenue}</li>
                    </ul>
                    <ul class="productList" style="display:none">
                        ${htmlChild}
                    </ul>`
        })
        $(_pageId + " .incomeComposition-list").html(html);
        $(_pageId + " .incomeComposition").show();
    }
    /**
     * 
     * @param {*} bill_type 类型 月/年
     * @param {*} year 查询年
     * @param {*} chooseFlag 是否重置选中状态
     * @param {chooseYear} //是否在按月查询时选则 年度
     */
    function getFinancialBill(bill_type,year,chooseFlag,chooseYearFlag){
        let data = {
            bill_type:bill_type,
            year:year
        }
        service.reqFun102220(data, (datas) => {
            if(!datas || !datas.results || !datas.results[0] || !datas.results[0].data || !datas.results[0].data.length) return $(_pageId + " .noList").show();
            let res = datas.results[0]
            hisdate = res.hisdate;
            let list = res.data;
            let html = ``;
            list.map((item,index)=>{
                let chooseClass = item.revenue*1 == '0' ? 'zeroIncome' : item.revenue*1 < 0 ? 'negativeIncome' : 'positiveIncome'
                let chooseMonthClass = '';
                if(chooseYearFlag){
                    if(!chooseFlag) chooseMonthClass = ((list.length) - 1 == index && (item.revenue*1 > 0 || item.revenue*1 == 0)) ? 'userChooose_positiveIncome' : ((list.length - 1) == index && item.revenue*1 < 0) ? 'userChooose_negativeIncome' : ''
                }else{
                    if(!chooseFlag && showType == '0') chooseMonthClass = (fullMonth == item.querydate.split('月')[0] && (item.revenue*1 > 0 || item.revenue*1 == 0)) ? 'userChooose_positiveIncome' : (fullMonth == item.querydate.split('月')[0] && item.revenue*1 < 0) ? 'userChooose_negativeIncome' : '';
                    if(!chooseFlag && showType == '1') chooseMonthClass = ((list.length) - 1 == index && (item.revenue*1 > 0 || item.revenue*1 == 0)) ? 'userChooose_positiveIncome' : ((list.length - 1) == index && item.revenue*1 < 0) ? 'userChooose_negativeIncome' : ''
                }
                // console.log(item.revenue);
                item.revenue = item.revenue*1
                //判断 item.revenue >10000转成万，保留两位小数
                if(item.revenue > 10000 || item.revenue < -10000){
                    item.revenue = (item.revenue > 10000 ? '+' : '') + (item.revenue/10000).toFixed(2) + '万'
                }
                html += 
                    `<li class="vertical_line ${chooseClass} ${chooseMonthClass}" month="${item.querydate.split('月')[0]}" year="${item.querydate.split('年')[0]}" revenue="${item.revenue}">
                        <p class="m_bold item-title">${item.querydate}</p>
                        <p class="m_font_size12 item-val">${item.revenue > 0 ? ('+' + item.revenue) : item.revenue}</p>
                    </li>`
            })
            $(_pageId + " .revenueCalendar .item-list").html(html);
            if(chooseYearFlag) getFinancialBillDetails(bill_type,(showType == '0') ? year : (year - 1),list[list.length - 1].querydate.split('月')[0].padStart(2, '0'));
            //${item.revenue > 0 ? ('+' + item.revenue) : item.revenue}
        })
    }   
    //绑定事件
    function bindPageEvent() {
        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //点月份
        appUtils.bindEvent($(_pageId + " .clickMonth"), function () {
            $(_pageId + " .clickYear").removeClass("active")
            $(this).addClass("active")
            $(_pageId + " .chooseYear .forward").show();
            showType = '0'
            getAdjustedYearMonth();
        });
        //点财富顾问
        appUtils.bindEvent($(_pageId + " .remark .consultant"), function () {
            let scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
            scene_code = scene_code ? scene_code : '';
            if(scene_code == '3') return appUtils.pageInit(_pageUrl, "highVersion/adviser", {});
            appUtils.pageInit(_pageUrl, "account/wealthAdvisor", {});
        });
        appUtils.bindEvent($(_pageId + " .remark .call"), function () {
            // if(!adviser || !adviser.phone) return;
            // let mobile = adviser.phone;
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = '**********';
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //点年份
        appUtils.bindEvent($(_pageId + " .clickYear"), function () {
            $(_pageId + " .clickMonth").removeClass("active")
            $(this).addClass("active");
            $(_pageId + " .chooseYear .forward").hide();
            showType = '1'
            getAdjustedYearMonth();
        });
        //上一年
        appUtils.bindEvent($(_pageId + " .previousYear"), function () {
            let smallYear = hisdate; //获取用户最小年份
            //取前四位
            smallYear = smallYear.substring(0,4)*1;
            let nowYear = $(_pageId + " .fullYear").text().split('年')[0]*1;//当前选中年份
            if(nowYear > smallYear){
                $(_pageId + " .fullYear").text(`${nowYear - 1}年`);
                fullYear = nowYear-1;
                getFinancialBill(showType,nowYear - 1 + '',false,true);
            }
        });
        //下一年
        appUtils.bindEvent($(_pageId + " .nextYear"), function () {
            //获取当前年份
            let maxYear = new Date().getFullYear();
            let nowYear = $(_pageId + " .fullYear").text().split('年')[0]*1;//当前选中年份
            if(nowYear < maxYear){
                $(_pageId + " .fullYear").text(`${nowYear + 1}年`);
                fullYear = nowYear+1;
                getFinancialBill(showType,nowYear + 1 + '',false,true);
            }
        });
        //点击列表 月份/年份
        appUtils.preBindEvent($(_pageId + " .revenueCalendar"), ".item-list li", function (e) {
            fullMonth = $(this).attr('month');
            if(showType == '1')fullYear = $(this).attr('year');
            let revenue = $(this).attr('revenue');
            $(_pageId + " .revenueCalendar .item-list li").removeClass('userChooose_positiveIncome userChooose_negativeIncome');
            // let chooseMonthClass = ((revenue*1 > 0 || revenue*1 == 0)) ? 'userChooose_positiveIncome' : (revenue*1 < 0) ? 'userChooose_negativeIncome' : '';
            if(revenue.includes('+')){
                chooseMonthClass = 'userChooose_positiveIncome';
            }else if(revenue.includes('-')) {
                chooseMonthClass = 'userChooose_negativeIncome';
            }else{
                chooseMonthClass = 'userChooose_positiveIncome';
            }
            $(this).addClass(chooseMonthClass);
            getFinancialBillDetails(showType,fullYear,fullMonth.padStart(2, '0'));
        }, 'click');
        //点击账单详情列表
        appUtils.preBindEvent($(_pageId + " .incomeComposition"), ".incomeComposition-list .list-title", function (e) {
            //获取相邻类名为productList的ul
            let nextUl = $(this).next('.productList');
            if (nextUl.css('display') == 'none') {
                nextUl.css('display', 'block');
                $(this).find('.icon_arrow').attr('src', './images/withdraw.png');
            } else {
                nextUl.css('display', 'none');
                $(this).find('.icon_arrow').attr('src', './images/expand.png');
            }

        }, 'click');
    }
    //选择年度
    function destroy() {
        $(_pageId + " .incomeComposition").hide();
        $(_pageId + " .haveList").hide();
        $(_pageId + " .noList").hide();
        // $(".btnBar").removeClass('choose');
        // $(".panel").css("margin-top","0px");
        // $(".mobileSelect").remove();
        // $(".picker").remove();
        // $(_pageId + " .annualBill_center").html('')
        // totalPages = 1;
        // cur_page = 1;
        // // $(_pageId + " .annualBill_center").html('暂无数据');
        // $(_pageId + " .total_assets").text("--");
        // $(_pageId + " .total_revenue").text("--");
        // $(_pageId + " .total_assets_name").text("月末总资产(元)");
        // $(_pageId + " .total_revenue_name").text("月收益(元)");
        // $(_pageId + " .totalTime").text("--");
        // bill_type = "1";
    }
    function pageBack() {
        appUtils.pageBack();
    }
    var caitongimage = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = caitongimage;
});
