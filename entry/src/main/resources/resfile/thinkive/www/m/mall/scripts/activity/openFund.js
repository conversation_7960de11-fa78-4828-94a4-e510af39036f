//活动 - 介绍页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageCode = "activity/openFund",
        _pageId = "#activity_openFund ";
    var external = require("external");
    var tools = require("../common/tools");

    function init() {
        var param = {
            page: 1,
            num_per_page: "5",
            pay_int_hz: 0, //天数 0 不限   1 一个月内   2 1-6个月  3  6个月以上
            recommend_type: "5", //天数 0 不限   1 一个月内   2 1-6个月  3  6个月以上
        };
        service.reqFun151101(param, function (datas) {
            if (datas.error_no == 0) {
                if (datas.results.length == 0) return;
                var productList = datas.results[0].prod_list;
                var str = "", btnClass = "", btnText = "", depDayTypeName;
                var dateObj = {
                    "D": "天",
                    "M": "月",
                    "Y": "年",
                }
                for (var i = 0; i < productList.length; i++) {
                    var prod_name = productList[i].prod_name; //产品名称
                    var surv_amt = productList[i].surv_amt; //起存金额（元）
                    var pay_int_type = productList[i].pay_int_type;  //付息单位 D-天 M-月 Y-年
                    var pay_int_hz = productList[i].pay_int_hz; //付息周期（天数）
                    var dep_day_type = productList[i].dep_day_type;  //周期单位 D-天 M-月 Y-年
                    var prod_dep_day = productList[i].prod_dep_day; //周期（天数）
                    var bas_int_rate = productList[i].bas_int_rate; //基础利率
                    var brnd_sris = productList[i].brnd_sris; //产品系列   SD001 众力存  SD002 中惠存
                    var surv_amtStr = "";
                    if (surv_amt <= 0 && surv_amt < 100) {
                        surv_amtStr = "0元起投"
                    } else if (100 <= surv_amt && surv_amt < 1000) {
                        surv_amtStr = "百元起投"
                    } else if (1000 <= surv_amt && surv_amt < 10000) {
                        surv_amtStr = "千元起投"
                    } else if (10000 <= surv_amt) {
                        surv_amtStr = "万元起投"
                    }
                    if (brnd_sris == "SD002") {
                        depDayTypeName = dateObj[pay_int_type];
                        var sub_info_str = '<span>' + surv_amtStr + '</span>\n' +
                            '               <span>随存随取</span>\n' +
                            '               <span>本息保障</span>\n' +
                            '               <span>每' + pay_int_hz + depDayTypeName + '付息</span>';

                    } else if (brnd_sris == "SD001") {
                        depDayTypeName = dateObj[dep_day_type];
                        pay_int_hz = prod_dep_day;
                        var sub_info_str = '<span style="padding: 0.02rem 0.18rem">' + surv_amtStr + '</span>\n' +
                            '               <span style="padding: 0.02rem 0.18rem">本息保障</span>\n' +
                            '               <span style="padding: 0.02rem 0.18rem">支持转让</span>';
                    }

                    str += '<div class="item">\n' +
                        '                        <div class="clearfix prod_name">\n' +
                        '                            <span >' + prod_name + '</span>\n' +
                        '                            <span>固定收益</span>\n' +
                        '                        </div>\n' +
                        '                        <div class="rate">\n' +
                        '                            <span>' + tools.fmoney(bas_int_rate) + '%</span>\n' +
                        '                            <span>存款利率</span>\n' +
                        '                        </div>\n' +
                        '                        <div class="sub_info">\n' + sub_info_str +
                        '                        </div>\n' +
                        '                        <div class="line2"></div>\n' +
                        '                    </div>'
                }
                $(_pageId + " .bankList").html(str);
            } else {
                layerUtils.iAlert(datas.error_info);
            }
        });
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .download"), function () {
            var setParam = {
                "funcNo": "80002",
                "moduleName": "mall",
                "biz_code": "open_thfund",
            };
            external.callMessage(setParam);
        })


    }

    function destroy() {
        $(_pageId + " .bankList").html("");

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var advertisement = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = advertisement;
});
