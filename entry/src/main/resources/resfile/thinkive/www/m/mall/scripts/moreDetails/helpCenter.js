/**
 * @Description 帮助中心一级菜单
 */
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        gconfig = require("gconfig"),
        _pageUrl = "moreDetails/helpCenter",
        _pageId = "#moreDetails_helpCenter ";
    var windowHeight;
    var tools = require("../common/tools");
    function init() {
        $(_pageId + " .custServiceTel").html(require("gconfig").global.custServiceTel);
        showDirectory();//显示目录
        windowHeight = $(window).height();
    }


    function showDirectory() {
        service.reqFun102063({}, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var array = data.results;
            for (var i = 0; i < array.length; i++) {
                var html = "";
                var id = array[i].id;
                var title = array[i].title;
                html += "<p id='" + id + "'><a href='javascript:void(0)'>" + title + "</a></p>";
                $(_pageId + "#question_list").append(html);
            }
        })


    }

    //绑定事件
    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //客服电话
        appUtils.bindEvent($(_pageId + " #phone"), function () {
            $(_pageId + "#phone_div").show();
            $(_pageId + ".pop_layer").show();

        });
        //取消拨打电话
        appUtils.bindEvent($(_pageId + " #call_off"), function () {
            $(_pageId + "#phone_div").hide();
            $(_pageId + ".pop_layer").hide();
        });
        //拨打电话
        appUtils.bindEvent($(_pageId + " #call_up"), function () {
            var param = {};
            param["funcNo"] = "50220";
            param["telNo"] = gconfig.global.custServiceTel;
            param["callType"] = "0";
            require("external").callMessage(param);
        });
        //在线客服
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageUrl)
        });
        //目录点击事件
        appUtils.preBindEvent($(_pageId + " #question_list"), $(_pageId + " #question_list p"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target).closest("p")[0];
            appUtils.setSStorageInfo("helpCenterInfo", {id: node.id, name: $(node).text()});
            service.reqFun102063({parentId: node.id}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                }
                var array = data.results;
                if (array.length > 0) { //存在子目录，跳转二级菜单
                    appUtils.pageInit(_pageUrl, "moreDetails/helpCenterSubpage");
                } else { //不存在子目录，进入内容页
                    //跳转到内容页
                    var param = {
                        "id": node.id,
                        "name": $(node).text(),
                    };
                    appUtils.setSStorageInfo("helpCenterContentInfo", param);
                    appUtils.pageInit(_pageUrl, "moreDetails/helpCenterContentpage");

                }
            })
        });
        //搜索按钮
        appUtils.bindEvent($(_pageId + " #searchButton"), function () {
            $(_pageId + "#searchText").blur();
            var key = $.trim($(_pageId + " #searchText").val());
            if (!key) {
                layerUtils.iAlert("请输入问题关键字");
                return;
            }
            var param = {
                "searchText": key,
                "windowHeight": windowHeight,
            };
            appUtils.setSStorageInfo("helpSearch", param);
            appUtils.pageInit(_pageUrl, "moreDetails/helpCenterSearch");
        });
    }

    function destroy() {
        $(_pageId + "#phone_div").hide();
        $(_pageId + ".pop_layer").hide();
        $(_pageId + "#question_list").html("");
        $(_pageId + "#searchText").val("");
    }

    function pageBack() {
        appUtils.clearSStorage("helpCenterContentInfo");
        appUtils.clearSStorage("helpCenterInfo");
        appUtils.clearSStorage("helpSearch");
        appUtils.pageBack();
    }

    var helpCenter = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = helpCenter;
});
