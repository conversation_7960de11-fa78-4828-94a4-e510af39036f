<div class="page" id="thfund_inputRechargePwd" data-pageTitle="充值" data-isSaveDom="false" data-refresh="true"
    style="-webkit-overflow-scrolling : touch;">
    <div class="excess_layer" style="display:none;position: absolute;">
        <div class="excess_box">
            <div class="excess_inner slidedown in" style="    border-radius: 10px;
            padding-top: 0;">
                <div style="text-align: left;
                border-bottom: 1px solid #ccc;
                padding: 20px 30px 10px 30px;">充值金额超限，即将跳转，请确认是否安装<span class="mobile_bank_name"></span>手机银行APP。</div>
                <div style="    text-align: left;
                padding: 10px 30px 10px 30px;">
                    <div>温馨提示</div>
                    <p>1、如已安装，请选择“手机银行充值”</p>
                    <p>2、如未安装，请选择“汇款充值”</p>
                    <p>3、请务必使用绑定卡充值</p>
                </div>
                <div class="excess_btn" style="    ">
                    <a href="javascript:void(0);" id="mobileRecharge" operationType="1" operationId="mobileRecharge" operationName="手机银行充值" class="ui button rounded btn_next pop in"
                        style="margin-right: 15px;line-height: 0.38rem;">手机银行充值</a>
                    <a href="javascript:void(0);" id="hkcz"  class="ui button rounded btn_next pop in"
                        style="background: rgb(128, 128, 128);color: #FFFFFF;line-height: 0.38rem;border: 1px solid rgb(128, 128, 128);margin-left: 15px;">汇款充值</a>
                </div>

            </div>
        </div>
    </div>
    <div class="pop_layer" style="display:none;position: absolute;">
        <div class="password_box">
            <div class="password_inner slidedown in">
                <a href="javascript:void(0);" operationType="1" operationId="closePassword" operationName="关闭密码输入框" id="close" class="close_btn"></a>
                <h4>请输入<span>6</span>位交易密码</h4>
                <h6 id="rechargeInfo">
                    <!-- 使用<em>尾号7782光大银行卡，</em>充值现金宝<em>2.00</em>元 -->
                </h6>
                <div class="password_input">
                    <span id="span00"></span>
                    <span id="span01"></span>
                    <span id="span02"></span>
                    <span id="span03"></span>
                    <span id="span04"></span>
                    <span id="span05"></span>
                    <input type="text" id="jymm" maxlength="6" style="display:none;">
                </div>
                <a href="javascript:void(0);" operationType="1" operationId="queDing" operationName="确定密码" id="queDing" class="sure_btn text-center">确定</a>
            </div>
        </div>
    </div>
    <div class="confirm_layer" style="display:none;position: absolute;">
        <div class="excess_box">
            <div class="excess_inner slidedown in" style="    border-radius: 10px;
            padding-top: 0;">
                <!-- <a href="javascript:void(0);" id="confirmClose" class="close_btn"></a> -->
                <div style="    text-align: left;
                padding: 10px 30px 10px 30px;">
                    <div>温馨提示</div>
                    <p>1.如您未完成支付，请点击"继续支付"</p>
                    <p>2.如您已完成支付，请勿重复支付，请点击“完成”</p>
                    <!-- <p>3、请务必使用绑定卡充值</p> -->
                </div>
                <div class="excess_btn">
                    <a href="javascript:void(0);" id="confinuePay" operationType="1" operationId="confinuePay" operationName="继续支付" class="ui button rounded btn_next pop in"
                        style="margin-right: 15px;line-height: 0.38rem;">继续支付</a>
                    <a href="javascript:void(0);" id="complete" operationType="1" operationId="complete" operationName="完成" class="ui button rounded btn_next pop in"
                        style="background: rgb(128, 128, 128);color: #FFFFFF;line-height: 0.38rem;border: 1px solid rgb(128, 128, 128);margin-left: 15px;">完成</a>
                </div>

            </div>
        </div>
    </div>

    <section class="main fixed" data-page="home" id="product" style="padding-bottom: 0;padding-top: 0">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0)" operationType="1" operationId="getBack" operationName="返回" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">充值</h1>
                <!-- <a id="kefu" href="javascript:void(0)" class="coustomer-service-icon">
                    <img src = "./images/customerService.png" >
                </a> -->
            </div>
        </header>
        <article>
            <div class="fund_entry  bgWhite right_icon mb10" operationType="1" operationId="fund_entry" operationName="晋金宝">
                <p class="g_fontSize16 text_darkgray prod_sname">华安汇财通货币</p>
                <p class="g_fontSize14 text_gray ">(<span class="fund_code">000709</span>)货币型</p>
                <p class="prod_sub_type" style="display: none">(000709)货币型</p>
            </div>
            <div class="bankInfoBox mb10" style="padding: 0.1rem">
                <div>
                    <div class="bankIcon">
                        <img src="" alt="" style="width: 90%;margin-top: 0.05rem">
                    </div>
                    <div class="bankInfo">
                        <p style="    color: #000000;font-weight: bold;font-size: 16px;margin-bottom: 0">--</p>
                        <p>单笔限额<span class="single_limit" style="margin-right: 0.2rem">--</span>单日限额<span
                                class="day_limit">--</span></p>
                    </div>
                </div>
                <div class="supportedBankCards m_agreement_color" operationType="1" operationId="supportedBankCards" operationName="支持的银行卡">*支持的银行卡</div>
            </div>

            <div class="product_buy" id="fundMoneyBox" style="padding-right: 0.1rem;">
                <p>充值金额(元)</p>
                <div style="height:0.4rem;position: relative">
                    <!--                    <div class="pop_text" style="display: none">-->
                    <!--                        <span>充值资金不能购买"银行存款"</span>-->
                    <!--                    </div>-->
                    <i class="symbol">￥</i>
                    <span class="buy_money" id="inputspanid"><span text="请输入充值金额" class="unable"
                            style="color: rgb(153, 153, 153);">请输入充值金额</span></span>
                    <input id="czje" style="display: none;" readonly="readonly">
                </div>
                <div style="padding-top: 0.1rem;width: 100%;font-size: 12px;color: #999999;">15:00前充值<span id="yqsj"
                        style=""></span>开始计算收益，15:00后充值<span id="afterSy" style=""></span>开始计算收益</div>
            </div>
            <div class="grid_03 grid_02 grid">
                <div class="ui field text rounded input_box2" id="yzmBox">
                    <label class="short_label2">验证码</label>
                    <input custom_keybord="0" id="yzm" type="tel" maxlength="6" class="ui input code_input"
                        placeholder="" />
                    <a id="getYzm" operationType="1" operationId="getYzm" operationName="获取验证码" data-state="true">获取验证码</a>
                </div>
            </div>
            <div class="finance_det recharge_det">
                <dl class="bank_limit">
                    <dt></dt>
                    <dd id="weihao" style="display:none"></dd>
                    <dd></dd>
                </dl>
            </div>
            <!-- 语音验证码 -->
            <div class="finance_det recharge_det">
                <dl class="bank_limit">
                    <dt></dt>
                    <dd id="talkCode" style="display: block;">晋金财富将致电您的手机语音告知验证码</dd>
                    <dd></dd>
                </dl>
            </div>
            <div class="recharge">
                <p id="xianErXianShi" class="limit_tips text-center"></p>
                <p class="newtips_topup"><span>温馨提示</span>:&nbsp;充值超过限额时,请<a href="javascript:void(0);"
                        id="hkcz" operationType="1" operationId="hkcz" operationName="汇款充值">汇款充值</a>
                <div class="agreement">
                    <div class="radio_box mt20">
                        <div class="ui radio">
                            <input type="radio" id="input_radio2" checked="checked">
                            <label id="isChecked"></label>
                        </div>我已阅读并同意签署<span class="deal_box"></span>
                        <span>，承诺购买基金行为出于本人真实意愿且资金来源合法，已充分了解产品风险和服务内容</span>
                    </div>
                </div>

                <div class="grid_02" id="nextBtn">
                    <a href="javascript:void(0);" id="nextStep" operationType="1" operationId="nextStep" operationName="下一步" class="ui button block rounded btn_next pop in">下一步</a>
                </div>
                <div class="grid_02" id="nextBtn_one" style="display:none;">
                    <a href="javascript:void(0);" class="ui button block rounded btn_next pop in"
                        style="background: rgb(128, 128, 128);color: #FFFFFF;border: 1px solid rgb(128, 128, 128);">下一步</a>
                </div>
            </div>

            <div class="cash_rules">
                <p>*充值规则</p>
                <table width="100%" class="ui table rounded" cellspacing="0" cellpadding="0">
                    <tr>
                        <th colspan="3">投资申请日与收益起算日对应关系</th>
                    </tr>
                    <tr>
                        <td width="44%">申请日</td>
                        <td width="28%">有效申请日</td>
                        <td width="28%">收益起算日</td>
                    </tr>
                    <tr>
                        <td>周一0时-15时</td>
                        <td>周一</td>
                        <td>周二</td>
                    </tr>
                    <tr>
                        <td>周一15时-周二15时</td>
                        <td>周二</td>
                        <td>周三</td>
                    </tr>
                    <tr>
                        <td>周二15时-周三15时</td>
                        <td>周三</td>
                        <td>周四</td>
                    </tr>
                    <tr>
                        <td>周三15时-周四15时</td>
                        <td>周四</td>
                        <td>周五</td>
                    </tr>
                    <tr>
                        <td>周四15时-周五15时</td>
                        <td>周五</td>
                        <td>下周一</td>
                    </tr>
                    <tr>
                        <td>周五15时-周日24时</td>
                        <td>下周一</td>
                        <td>下周二</td>
                    </tr>

                    <tr>
                        <th colspan="3" style="text-align:left;">
                            注：工作日为上海证券交易所、深圳证券交易所的正常交易日。如周一至周五遇非工作日，则自动顺延至下一工作日为有效申请日，相应有效申请日下一工作日为收益起算日。
                        </th>
                    </tr>
                </table>
            </div>
            
        </article>
    </section>
    <!-- 协议相关弹窗 -->
    <div class="agreement_layer display_none">
        <div class="agreement_popup in">
            <div class="agreement_popup_header">
                <div class="new_close_btn" operationType="1" operationId="new_close_btn" operationName="关闭协议弹窗"></div>
                <h3>相关协议</h3>
            </div>
            <ul class="agreement_list flex vertical_line"></ul>
        </div>
    </div>
    <div class="van-overlay" style="display:none">
        <div class="tip_rules" style="padding:0 0.4rem;">
            <div class="rules_box slideup in pdfCenter_dio" style="width: 100%;overflow-y: auto;max-height:70%;">
                <h5>基金产品资料概要 <a href="javascript:void(0);" id="paf_new_close" class="close_btn"></a></h5>
                <div class="rules_list" style="padding: 0.15rem;height: 3rem;overflow-y: scroll">
                    <div id="iframe_pdf"></div>
                </div>
                <div class="grid_02 iframe_pdf_bottom">
                    <a href="javascript:void(0)" class="vant_button ui button block rounded btn_01 disable"
                        id="sure">我知道了</a>
                </div>
            </div>
        </div>
    </div>
	 <div class="pop_layer1" id="zfqy" style="display:none;position: absolute;">
        <div class="card_rules">
            <div class="rules_box slideup in agreement_popup_header" style="border-radius: 10px; padding-top: 0;">
            	<a href="javascript:void(0);" operationtype="1" operationid="closePassword" operationName="关闭" class="" id="zfqy_close"></a>
                <div class="g_fontSize16" style="padding: 20px 30px 10px 30px;text-align: center;font-weight: 700;">支付服务升级</div>
                <div class="grid_03 grid_02 grid " style="text-align: left; padding: 10px 30px 10px 30px;">
                    <div style="margin-bottom: 10px;font-weight: 300;"></div>
                    <p style="font-weight: 700;">银行预留手机号</p>
                    <p style="border-bottom: 1px solid #ccd9e0;font-weight: 700;padding: 0.1rem 0;;"><span id="bankcardphone"></span></p>
                    <p for="sms-code" style="font-weight: 700;margin-top: 0.1rem;">短信验证码</p>
                    <div class="ui field text input_box2">
                    	<input custom_keybord="0" id="zfqyyzm" type="tel" maxlength="6" class="ui input code_input"
                        placeholder="请输入短信验证码" style="padding-left: 0;"/>
                    	<a id="zfqygetYzm" operationType="1" operationId="getYzm" operationName="获取验证码" data-state="true">获取验证码</a>
                    </div>
                </div>
                <div class="finance_det recharge_det">
                    <dl class="bank_limit">
                        <dt></dt>
                        <dd id="sjweihao" style="display:none"></dd>
                        <dd></dd>
                    </dl>
                </div>
                <div>
                    <a href="javascript:void(0);" id="zfqy_ok"  class="ui button block rounded btn_next pop in" style="   margin: 0.1rem 0.2rem;">完成</a>
                </div>

                <div class="agreement">
				<div class="radio_box mt20">
					<div class="ui radio">
						<input type="radio" id="input_radio2" checked="checked">
						<label id="isChecked"></label>
					</div>我已阅读并同意签署<span class="deal_box"></span>
				</div>
			</div>
            </div>
        </div>
</div>