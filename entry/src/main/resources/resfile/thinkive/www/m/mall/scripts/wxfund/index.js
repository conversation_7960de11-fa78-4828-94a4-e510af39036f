// 晋金财富微信版
define(function (require, exports, module) {
    var _pageId = "#wxfund_index ",
        _pageCode = "wxfund/index",
        gconfig = require("gconfig"),
        global = gconfig.global,
        service = require("mobileService"),
        appUtils = require("appUtils");

    function init() {
        var token = appUtils.getPageParam("token");
        //设置页面高度
        setHeight(token);
    }

    //设置页面高度
    function setHeight(token) {
        var wxfund_url = global.wxfund_url;
        $(_pageId + " iframe").css("overflow-y", "auto").attr("src", wxfund_url + "?token=" + token);
        $(_pageId + " iframe").css("height",
            window.innerHeight -
            $(_pageId + " header").height() - $(_pageId + " footer").height()
        );
    }

    //绑定事件
    function bindPageEvent() {

        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //首页
        appUtils.bindEvent($(_pageId + " #shouye"), function () {
            appUtils.pageInit(_pageCode, "login/userIndexs", {});
        });
        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            appUtils.pageInit(_pageCode, "moreDetails/more", {});
        });
    }

    function destroy() {
        $(_pageId + " iframe").attr("src", "");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var wxfund_index = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = wxfund_index;
});
