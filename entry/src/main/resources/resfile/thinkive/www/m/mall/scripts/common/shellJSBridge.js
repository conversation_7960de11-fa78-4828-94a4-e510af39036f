/**
 * ios 原生交互 js
 */
define(function(require, exports, module){
	var messagingIframe;
	var sendMessageQueue = [];
	var receiveMessageQueue = [];
	var messageHandlers = {};

	var CUSTOM_PROTOCOL_SCHEME = 'wvjbscheme';
	var QUEUE_HAS_MESSAGE = '__WVJB_QUEUE_MESSAGE__';

	var responseCallbacks = {};
	var uniqueId = 1;

	/**
	 * shellJSBridge.js主要完成了如下工作:
	 * (1) 创建了一个用于发送消息的iFrame(通过创建一个隐藏的ifrmae，并设置它的URL 来发出一个请求，从而触发UIWebView的回调协议)
	 * (2) 创建了一个核心对象external，并给它定义了几个方法，这些方法大部分是公开的API方法
	 * (3) 创建了一个事件：externalReady，并dispatch(广播)了它。
	 */
	
	
	/******************************************************对于(1)，相应的代码如下：********************************************************************/
	/**
	 * 创建一个iFrame，设置隐藏并加入到DOM中
	 */
	function _createQueueReadyIframe(doc) {
	    messagingIframe = doc.createElement('iframe');
	    messagingIframe.style.display = 'none';
	    messagingIframe.src = CUSTOM_PROTOCOL_SCHEME + '://' + QUEUE_HAS_MESSAGE;
	    doc.documentElement.appendChild(messagingIframe);
	}
	
	
	/*****************************************************对于(2)中的external，其对象拥有如下方法：*****************************************************/
	module.exports = window.external = {
		init: init,
		send: send,
		registerHandler: registerHandler,
		callHandler: callHandler,
		_fetchQueue: _fetchQueue,
		_handleMessageFromObjC: _handleMessageFromObjC
	};
	
	/*****************************************************方法的实现：*********************************************************************************/
	/**
	 * 初始化方法，注入默认的消息处理器 
     * 默认的消息处理器用于在处理来自objc的消息时，如果该消息没有设置处理器，则采用默认处理器处理
	 */
	function init(messageHandler) {
	    if (external._messageHandler) {
	        throw new Error('external.init called twice');
	    }
	    external._messageHandler = messageHandler;
	    var receivedMessages = receiveMessageQueue;
	    receiveMessageQueue = null;
	    //如果接收队列有消息，则处理
	    for (var i = 0; i < receivedMessages.length; i++) {
	        _dispatchMessageFromObjC(receivedMessages[i]);
	    }
	}
	
	/**
	 * 发送消息并设置回调 
	 */
	function send(moduleName, childModuleName, msgId, data, responseCallback) {
		var reqParam = {
			moduleName: moduleName,
			childModuleName: childModuleName,
			msgId: msgId,
			data: data
		};
		if(!responseCallback)
		{
			responseCallback = function(){};
		}
	    _doSend(reqParam, responseCallback);
	}

	/**
	 * 注册消息处理器
	 */
	function registerHandler(handlerName, handler) {
	    messageHandlers[handlerName] = handler;
	}

	/**
	 * 调用处理器并设置回调
	 */
	function callHandler(handlerName, moduleName, childModuleName, msgId, data, responseCallback) {
		var reqParam = {
			handlerName: handlerName,
			moduleName: moduleName,
			childModuleName: childModuleName,
			msgId: msgId,
			data: data
		};
	    _doSend(reqParam, responseCallback);
	}

	/******************************************************涉及到的两个内部方法：************************************************************************/
	/**
	 * 内部方法:消息的发送 
	 */
	function _doSend(message, responseCallback) {
		//如果定义了回调
	    if (responseCallback) {
	    	//为回调对象产生唯一标识
	        var callbackId = 'function' + message.msgId;
	        //并存储到一个集合对象里
	        responseCallbacks[callbackId] = responseCallback;
	        //新增一个key-value对- 'callbackId':callbackId
	        message['callbackId'] = callbackId;
	    }
	    sendMessageQueue.push(message);
	    messagingIframe.src = CUSTOM_PROTOCOL_SCHEME + '://' + QUEUE_HAS_MESSAGE;
	}
	
	/**
	 * 内部方法:处理来自objc的消息
	 */
	function _dispatchMessageFromObjC(messageJSON) {
	    setTimeout(function _timeoutDispatchMessageFromObjC() {
	        var message = JSON.parse(messageJSON);
	        var messageHandler;

	        if (message.responseId) {
	        	//取出回调函数对象并执行
	            var responseCallback = responseCallbacks[message.responseId];
	            if (!responseCallback) {
	                return;
	            }
	            responseCallback(message.responseData);
	            delete responseCallbacks[message.responseId];
	        } else {
	            var responseCallback;
	            if (message.callbackId) {
	                var callbackResponseId = message.callbackId;
	                responseCallback = function(responseData) {
	                    _doSend({
	                        responseId: callbackResponseId,
	                        responseData: responseData
	                    });
	                };
	            }

	            var handler = external._messageHandler;
	            //如果消息中已包含消息处理器，则使用该处理器；否则使用默认处理器
	            if (message.handlerName) {
	                handler = messageHandlers[message.handlerName];
	            }

	            try {
	                handler(message.data, responseCallback);
	            } catch(exception) {
	                if (typeof console != 'undefined') {
	                    // console.log("external: WARNING: javascript handler threw.", message, exception);
	                }
	            }
	        }
	    });
	}

	/*******************还有两个js方法_fetchQueue、_dispatchMessageFromObjC是供native端直接调用的方法(它们本身也是为native端服务的)：*********************/
	/**
	 * 获得队列，将队列JSON对象转成一个字符串【native端调用】
	 */
	function _fetchQueue() {
	    var messageQueueString = JSON.stringify(sendMessageQueue);
	    sendMessageQueue = [];
	    return messageQueueString;
	}

	/**
	 * 处理来自ObjC的消息【native端调用】
	 */
	function _handleMessageFromObjC(messageJSON) {
		//如果接收队列对象存在则入队该消息，否则直接处理
	    if (receiveMessageQueue) {
	        receiveMessageQueue.push(messageJSON);
	    } else {
	        _dispatchMessageFromObjC(messageJSON);
	    }
	}
	
	
	/*********************************对于(3)，定义一个事件并触发，同时设置设置external对象为事件的一个bridge属性：*****************************************/
	var doc = document;
	_createQueueReadyIframe(doc);
	//创建并实例化一个事件对象
	var readyEvent = doc.createEvent('Events');
	readyEvent.initEvent('externalReady', true, true);
	readyEvent.bridge = external;
	//广播事件
	doc.dispatchEvent(readyEvent);
});