// 引流注册-开户成功
define(function(require, exports, module){
    var appUtils = require("appUtils"),
        ut = require("../common/userUtil"),
        _pageId = "#drainage_openAccountSuccess",
        _pageUrl = "drainage/openAccountSuccess";


    function init(){
        appUtils.clearSStorage("bankAccInfo");
        let errorMessage = appUtils.getSStorageInfo("errorMessage")
        if(errorMessage == "1"){
            $(_pageId + " .success_message").text("您已经提交资料，待审核通过后，您可进行充值购买")
        }else{
            $(_pageId + " .success_message").text("绑卡受理成功！")
        }
    }

    //绑定事件
    function bindPageEvent(){
        //完成
        appUtils.bindEvent($(_pageId+" #wancheng"),function(){
            appUtils.setSStorageInfo("errorMessage","0")
            appUtils.pageInit(_pageUrl, "login/userIndexs", {bindCard: true});
        });
    }

    function destroy() {
    }
    var setBankCardSuccess = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = setBankCardSuccess;
});
