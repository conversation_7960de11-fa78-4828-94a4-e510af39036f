//活动 - 理财账单
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        gconfig = require("gconfig"),
        common = require("common"),
        service = require("mobileService"),
        tools = require("../common/tools");
    var _pageId = "#activity_dragonYearAIChat ";
    var _pageCode = "activity/dragonYearAIChat"
    var layerUtils = require("layerUtils");
    var userInfo;
    var pageTouchTimer = null;
    var global = gconfig.global;
    var ut = require("../common/userUtil.js");
    require("../common/html2canvas.min.js");
    var activity_id, activityAiInfo, aiType, shareTemplateInfo; // activity_id是大转盘活动id，返回页面时需要带
    var isUnderWay = false, isGenerateImg = false;// 是否在生成中，默认是false
    require("../common/clipboard.min.js");
    var platform = gconfig.platform;
    /**
     * 初始化
     */
    function init() {
        aiType = appUtils.getPageParam("aiType");
        activityAiInfo = appUtils.getPageParam("activityAiInfo")
        activity_id = appUtils.getPageParam("activity_id"); // 保留，返回有用
        if (activityAiInfo.dictMap && activityAiInfo.dictMap.chunlian_2024 && activityAiInfo.dictMap.chunlian_2024.length) {
            let html = ''
            activityAiInfo.dictMap.chunlian_2024.forEach(item => {
                html += `<li data-name="${item.name}" data-code="${item.code}">${item.name}</li>`
            })
            $(_pageId + " #couplet .couplet_theme").html(html);
        }
        if (activityAiInfo.dictMap && activityAiInfo.dictMap.duanxin_2024 && activityAiInfo.dictMap.duanxin_2024.length) {
            let html = ''
            activityAiInfo.dictMap.duanxin_2024.forEach(item => {
                html += `<li data-name="${item.name}" data-code="${item.code}">${item.name}</li>`
            })
            $(_pageId + " #msg .msg_select").html(html);
        }
        if (activityAiInfo.dictMap && activityAiInfo.dictMap.nianyefan_2024 && activityAiInfo.dictMap.nianyefan_2024.length) {
            let html = ''
            activityAiInfo.dictMap.nianyefan_2024.forEach(item => {
                html += `<li data-name="${item.name}" data-code="${item.code}">${item.name}</li>`
            })
            $(_pageId + " #recipe .recipe_select").html(html);
        }
        var contentH = $(_pageId + " .boost").height() - 60 - 78;
        //获取活动详情
        if (aiType == "0") {
            $(_pageId + " #couplet").show();
            $(_pageId + " #recipe").hide();
            $(_pageId + " #msg").hide();
            getShareTemplate();
            $(_pageId + " #couplet .activity_content").css({ height: contentH })
        } else if (aiType == "2") {
            $(_pageId + " #recipe").show();
            $(_pageId + " #msg").hide();
            $(_pageId + " #couplet").hide();
            $(_pageId + " #recipe .activity_content").css({ height: contentH })
        } else if (aiType == "1") {
            $(_pageId + " #msg").show();
            $(_pageId + " #recipe").hide();
            $(_pageId + " #couplet").hide();
            $(_pageId + " #msg .activity_content").css({ height: contentH })
        }
    };

    function getShareTemplate() {
        // return new Promise((resolve, reject) => {
        // 获取分享模板,先渲染二维码图片
        let query_params = {
            registered_mobile: ut.getUserInf().mobileWhole,
            share_template: activityAiInfo.shareTemplate
        }
        service.reqFun102012(query_params, async function (data) {
            if (data.error_no == '0') {
                var result = data.results[0];
                if (data.results[0] && data.results[0].share_form == 2) {  // 若分享的是卡片，先渲染卡片
                    var chooseData = result;
                    shareTemplateInfo = chooseData;
                    let bgImg = gconfig.global.oss_url + chooseData.img_url;
                    service.reqFun102119({ img_url: bgImg }, function (data) {
                        if (data.error_no == "0") {
                            let base64Str = 'data:image/jpeg;base64,' + data.results[0].base64Str
                            $(_pageId + " .shareImg .bgImg").attr("src", base64Str);
                            if (chooseData.qr_code_type && chooseData.qr_code_type == '2') { // 1 个人邀请码 2 上传的二维码
                                $(_pageId + " .shareImg #qrImg").empty();
                                $(_pageId + " .shareImg #qrImg").show();
                                $(_pageId + " .shareImg #qrCode").hide();
                                let qr_code_img = global.oss_url + chooseData.qr_code_img_url;
                                service.reqFun102119({ img_url: qr_code_img }, function (qrData) {
                                    if (qrData.error_no == "0") {
                                        var qrBase64Str = 'data:image/jpeg;base64,' + qrData.results[0].base64Str;
                                        $(_pageId + " #qrImg").html(`<img style="width: 100%;height: 100%;" src="${qrBase64Str}" alt="">`);
                                        // isGenerateImg = true;
                                    } else {
                                        layerUtils.iAlert(d.error_info);
                                    }
                                }, { "isShowWait": false })
                            } else {
                                $(_pageId + " .shareImg #qrImg").hide();
                                $(_pageId + " .shareImg #qrCode").show();
                                qrcode(chooseData);
                                // isGenerateImg = true;
                            }

                        } else {
                            // isGenerateImg = false;
                            layerUtils.iAlert(data.error_info);
                        }
                    }, { "isShowWait": false })
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        }, { "isShowWait": false });
        // })

    }

    // 处理食谱渲染
    // async 表示函数里有异步操作
    async function processText(textArr, textOld) {
        for (let i = 0; i <= textArr.length; i++) {
            // 处理该行文本  
            let newDiv = document.createElement("div");
            var liLength = $(_pageId + " #chatBoxRecipe>li.auto").length;
            newDiv.id = "text" + liLength + i; // 设置 id 为 myDiv  
            $(_pageId + " #chatBoxRecipe>li.auto:last-child").append(newDiv);
            if (i == textArr.length) {
                var index = $(_pageId + " #chatBoxRecipe>li.auto").length - 1;
                let newTextOld = textOld.replace(/<br>/g, "");
                var copyHtml = `<div class='copy' id='copy${index}' data-clipboard-action='copy' data-clipboard-text="${newTextOld}">复制</div>`
                $(_pageId + " #chatBoxRecipe>li.auto:last-child").append(copyHtml);
                isUnderWay = false;
                return;
            }
            // await 表示紧跟在后面的表达式需要等待结果
            await renderText("text" + liLength + i, textArr[i]); // 使用 await 来等待 renderText 方法执行完成  
        }
    }

    // JavaScript部分  
    function renderText(elementId, text) {
        return new Promise((resolve, reject) => {
            var containerElement = document.getElementById(elementId);
            var index = 0;
            function showNextChar() {
                if (index < text.length && containerElement) {
                    containerElement.innerHTML += text[index];
                    index++;
                    // 设置加载完自动滚动到底部
                    var scrollHeight = $(_pageId + " #recipe .activity_content")[0].scrollHeight
                    var height = $(_pageId + " #recipe .activity_content").height();
                    var scrollPosition = scrollHeight - height;
                    // 将滚动条设置到底部
                    $(_pageId + " #recipe .activity_content").scrollTop(scrollPosition);
                    setTimeout(showNextChar, 100); // 每隔100ms显示下一个字符  
                } else {
                    resolve(); // 当所有字符显示完毕后，resolve这个Promise  

                }
            }
            showNextChar();
        });
    }

    // 复制
    function copyContent(id) {
        var btn = document.getElementById(id);
        var clipboard = new ClipboardJS(btn);
        clipboard.on('success', function (e) {
            layerUtils.iAlert("复制成功，可粘贴");
        });
        clipboard.on('error', function (e) {
            layerUtils.iAlert("复制失败，请重试");
        });
    }

    function getAiAnswer(type, theme, id) {
        var question_content = "";
        var iid = "";
        // type 0 对联 1 短信祝福语 2 年夜饭-食谱
        if (type == "0") {
            question_content = `写一副主题是${theme}的龙年春联，包含上联、下联、横批,上下联字数均不超过7字,横批不超过4个字。`
            iid = "couplet"
        } else if (type == "1") {
            question_content = `写一段主题包含${theme}的龙年拜年短信,限制在30个字以内,跟上次不一样`
            iid = "msg"
        } else if (type == "2") {
            question_content = `写一篇关于${theme}年夜饭的步骤，200字以内`
            iid = "recipe"
        }
        // auto 代表是新生成的，销毁时需要删除
        var html = `<li class="left frame auto"><div class="loading">内容生成约30秒，请不要离开<span class="dotting"></span></div></li>`;
        $(_pageId + ` #${id}`).append(html);
        scrollBottom(iid);
        service.reqFun161001({
            question_content: question_content,
            question_type: type
        }, async function (data) {
            if (data.error_no == '0') {
                if (data.results.length) {
                    var results = data.results[0];
                    if (results.ret_code == "0000") { // 成功
                        // 处理页面渲染
                        if (results.question_type == "2") {
                            var recipeText = results.answer_content;
                            let recipeTextMatch = recipeText.replace(/\n\n/g, "<br>").replace(/\n/g, "<br>").replace(/[*]/g, "");
                            let recipeTextArr = recipeTextMatch.split("<br>")
                            $(_pageId + ` #${id}>li.auto:last-child`).html("");
                            processText(recipeTextArr, recipeTextMatch);
                        } else if (results.question_type == "1") {
                            var index = $(_pageId + " #chatBoxMsg>li.auto").length - 1;
                            $(_pageId + " #chatBoxMsg>li.auto:last-child").html(results.answer_content + `<div class='copy' id='copy${index}' data-clipboard-action='copy' data-clipboard-text='${results.answer_content}'>复制</div>`);
                            scrollBottom("msg");
                            isUnderWay = false;
                        } else if (results.question_type == "0") { // 对联
                            // ${gconfig.global.oss_url + shareTemplateInfo.img_url}
                            results.horizontal = results.horizontal.substring(0, 4); // 横批截取四个字
                            let html = `  <div class="couplet_share" style="position: relative;" data-horizontal='${results.horizontal}'>
                              <img style="width: 100%;" src="./images/activity/dargonYear/recipe_bg.png">
                                ${shareTemplateInfo.qr_code_type == '2' ? `  <div class="text-center qr_img">
                                    <img style="width: 100%;height: 100%;" src="${gconfig.global.oss_url + shareTemplateInfo.qr_code_img_url}" alt="">
                                    </div>` : ` <div class="text-center qr_code">
                                        <span></span>
                                    </div>`}
                                  <div class="couplet_top">${results.horizontal}</div>
                                  <div class="couplet_left">${results.first_line}</div>
                                  <div class="couplet_right">${results.second_line}</div>
                            </div>
                            <div class="shareBtn">分享海报得随机积分</div>`
                            $(_pageId + " #chatBoxCouplet>li.auto:last-child").html(html);
                            scrollBottom("couplet");
                            isUnderWay = false;
                        }
                    } else if (results.ret_code == "1111" || results.ret_code == "4444") { //存在敏感词
                        $(_pageId + ` #${id}>li.auto:last-child`).html("请换个主题，重新试试吧");
                        isUnderWay = false;
                        return;
                    } else if (results.ret_code == "9999") {
                        $(_pageId + ` #${id}>li.auto:last-child`).html("生成失败，请重新生成");
                        isUnderWay = false;
                        return;
                    } else {
                        layerUtils.iAlert(results.ret_msg);
                        $(_pageId + ` #${id}>li.auto:last-child`).remove();
                        isUnderWay = false;
                        return;
                    }
                }
            } else {
                $(_pageId + ` #${id}>li.auto:last-child`).html("生成失败，请重新生成");
                layerUtils.iAlert(data.error_info);
                isUnderWay = false;
            }
        }, { "isShowWait": false })
    }

    // 设置加载完自动滚动到底部
    function scrollBottom(id) {
        var scrollHeight = $(_pageId + ` #${id} .activity_content`)[0].scrollHeight
        var height = $(_pageId + ` #${id} .activity_content`).height();
        var scrollPosition = scrollHeight - height;
        // 将滚动条设置到底部
        $(_pageId + ` #${id} .activity_content`).scrollTop(scrollPosition);
    }

    //渲染短链二维码
    function qrcode(chooseData) {
        let mobile = ut.getUserInf().mobileWhole;
        mobile = common.desEncrypt("mobile", mobile);//加密
        let long_url = chooseData.qr_code_img_url + '?mobile=' + mobile;
        console.log(long_url);
        $(_pageId + " #qrCode").empty();
        service.reqFun101073({ long_url: long_url }, function (res) {
            if (res.error_no == "0") {
                if (res.results != undefined && res.results.length > 0) {
                    var short_url = res.results[0].shortUrl;
                    require("../common/jquery.qrcode.min.js");
                    $(_pageId + " #qrCode").qrcode({
                        render: "canvas", //设置渲染方式，有table和canvas
                        text: short_url, //扫描二维码后自动跳向该链接
                        width: 70, //二维码的宽度
                        height: 70, //二维码的高度
                        imgWidth: 20,
                        imgHeight: 20,
                        src: '../mall/images/icon_app.png'
                    });
                }
            } else {
                layerUtils.iAlert(res.error_info);
            }
        }, { "isShowWait": false })
    }

    function shareGetReward() {
        pageTouchTimer = setTimeout(() => {
            // 分享得积分 如果有奖励会返回奖励积分的数量，没有返回积分数量代表不满足参与条件或者超过5次无法获得积分了
            service.reqFun108048({ activity_id: activityAiInfo.activityId }, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iAlert(data.error_info);
                    return;
                } else {
                    if (data.results && data.results[0]) {
                        if (data.results[0].pointsAmount && data.results[0].pointsAmount > 0) {
                            $(_pageId + " #rewardResult .index_info").html(`恭喜您获得${data.results[0].pointsAmount}积分`);
                            $(_pageId + " #rewardResult").show();
                        } else {
                            layerUtils.iMsg(-1, "分享成功");
                        }
                    } else {
                        layerUtils.iMsg(-1, "分享成功");
                    }
                }
            })
        }, 6000);

    }
    //绑定事件
    function bindPageEvent() {
        //点返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        // 生成AI
        appUtils.bindEvent($(_pageId + " .chat_btn"), function () {
            let themeName = $(this).parent().find("input").val();
            let type = $(this).attr("data-type");
            if (isUnderWay) {
                layerUtils.iAlert("生成中，请不要重复点击");
                return;
            }
            if (!themeName) {
                layerUtils.iAlert("请先输入你想生成的主题");
                return
            }
            var html = `<li class="right frame">${themeName}</li>`;
            isUnderWay = true;
            $(this).parent().find("input").val("");
            if (type == "0") {
                $(_pageId + ` #chatBoxCouplet`).append(html);
                scrollBottom("couplet")
                setTimeout(function () {
                    getAiAnswer("0", themeName, "chatBoxCouplet")
                }, 300)
            } else if (type == "1") {
                $(_pageId + ` #chatBoxMsg`).append(html);
                scrollBottom("msg")
                setTimeout(function () {
                    getAiAnswer("1", themeName, "chatBoxMsg")
                }, 300)
            } else if (type == "2") {
                $(_pageId + ` #chatBoxRecipe`).append(html);
                scrollBottom("recipe")
                setTimeout(function () {
                    getAiAnswer("2", themeName, "chatBoxRecipe")
                }, 300)
            }

        });
        // 年夜饭菜单选项点击事件
        appUtils.preBindEvent($(_pageId + " #recipe .recipe_select"), " li", function () {
            if (isUnderWay) {
                layerUtils.iAlert("生成中，请不要重复点击");
                return;
            }
            var themeName = $(this).attr("data-name");
            var html = `<li class="right frame">${themeName}</li>`;
            $(_pageId + ` #chatBoxRecipe`).append(html);
            isUnderWay = true;
            scrollBottom("recipe")
            setTimeout(function () {
                getAiAnswer("2", themeName, "chatBoxRecipe")
            }, 300)
        })
        // 对联主题选项点击事件
        appUtils.preBindEvent($(_pageId + " #couplet .couplet_theme"), " li", function () {
            if (isUnderWay) {
                layerUtils.iAlert("生成中，请不要重复点击");
                return;
            }
            var themeName = $(this).attr("data-name");
            var html = `<li class="right frame">${themeName}</li>`;
            $(_pageId + ` #chatBoxCouplet`).append(html);
            isUnderWay = true;
            scrollBottom("couplet")
            setTimeout(function () {
                getAiAnswer("0", themeName, "chatBoxCouplet")
            }, 300)
        })
        // 短信主题选项点击事件
        appUtils.preBindEvent($(_pageId + " #msg .msg_select"), " li", function () {
            if (isUnderWay) {
                layerUtils.iAlert("生成中，请不要重复点击");
                return;
            }
            var themeName = $(this).attr("data-name");
            var html = `<li class="right frame">${themeName}</li>`;
            $(_pageId + ` #chatBoxMsg`).append(html);
            isUnderWay = true;
            scrollBottom("msg")
            setTimeout(function () {
                getAiAnswer("1", themeName, "chatBoxMsg")
            }, 300)
        })
        // 分享海报
        appUtils.preBindEvent($(_pageId + " #couplet"), " .left .shareBtn", function () {
            let parent = $(this).parent();
            $(_pageId + " #couplet .auto").removeClass("selected")
            parent.addClass("selected")
            $(_pageId + " #pop_layer").show();
        })

        // 复制
        appUtils.preBindEvent($(_pageId + " .activity_content"), " .copy", function () {
            var id = $(this).attr("id");
            copyContent(id);
        })

        //取消邀请好友
        appUtils.bindEvent($(_pageId + " #pop_layer #cancelShare"), function () {
            $(_pageId + " #couplet .auto").removeClass("selected")
            $(_pageId + " #pop_layer").hide();
        });
        //微信朋友圈
        appUtils.bindEvent($(_pageId + " #pop_layer #share_WeChatFriend"), async function () {
            // if(platform == '5') return layerUtils.iAlert(global.noHmFuncMsg);
            tools.clickPoint(_pageId, _pageCode, 'share_WeChatFriend', activityAiInfo.activityId);
            $(_pageId + " .shareImg .couplet_top").remove();
            $(_pageId + " .shareImg .couplet_left").remove();
            $(_pageId + " .shareImg .couplet_right").remove();
            var h = $(_pageId + " #couplet li.selected .couplet_share .couplet_top").html();
            var l = $(_pageId + " #couplet li.selected .couplet_share .couplet_left").html();
            var r = $(_pageId + " #couplet li.selected .couplet_share .couplet_right").html();
            var lArr = l.split("");
            var rArr = r.split("")
            var lHtml = ''
            for (let i = 0; i < lArr.length; i++) {
                lHtml += `<li>${lArr[i]}</li>`
            }
            var rHtml = ''
            for (let i = 0; i < rArr.length; i++) {
                rHtml += `<li>${rArr[i]}</li>`
            }
            let html = `
                <div class="couplet_top">${h}</div>
                <ul class="couplet_left">${lHtml}</ul>
                <ul class="couplet_right">${rHtml}</ul>
            `
            $(_pageId + " .shareImg").append(html);
            $(_pageId + " #pop_layer").hide();

            var father = document.querySelector("#content");
            var _fatherHTML = document.querySelectorAll("#content .page");
            var cur = document.querySelector("#activity_dragonYearAIChat");
            father.innerHTML = "";
            father.appendChild(cur);
            let dom = document.querySelector(_pageId + " .shareImg")
            html2canvas(dom, {
                scale: 3
            }).then(canvas => {
                var base64 = canvas.toDataURL("image/png");
                var _base64 = base64.split(",")[1];
                father.innerHTML = "";
                for (let i = 0; i < _fatherHTML.length; i++) {
                    father.appendChild(_fatherHTML[i]);
                }
                param = {
                    "funcNo": "50231",
                    "imgUrl": _base64,
                    "shareType": "23",
                    "imageShare": "1",
                    "imageType":"base64"
                }
                require("external").callMessage(param);
            })
            shareGetReward();
        });
        //隐藏中奖弹框
        appUtils.bindEvent($(_pageId + " .sureBtn"), function (e) {
            $(_pageId + " #rewardResult").hide();
        });
        // 添加事件监听器
        window.addEventListener('resize', handleResize);
    }

    // 定义一个处理函数
    function handleResize() {
        // 获取新的页面高度
        var newHeight = $(_pageId + " .boost").height()
        var curAiTypeId = aiType == "0" ? 'couplet' : (aiType == "1" ? "msg" : (aiType == "2" ? "recipe" : ""))
        $(_pageId + ` #${curAiTypeId} .activity_content`).css({ height: newHeight - 60 - 78 })
        scrollBottom(curAiTypeId)
    }

    function pageBack() {
        appUtils.pageInit(_pageCode, "activity/dragonYear", { "activity_id": activity_id })
    }

    function destroy() {
        $(_pageId + " #couplet").hide();
        $(_pageId + " #recipe").hide();
        $(_pageId + " #msg").hide();
        $(_pageId + " #couplet .couplet_theme").html("");
        $(_pageId + " #msg .msg_select").html("");
        $(_pageId + " #recipe .recipe_select").html("");
        $(_pageId + " #pop_layer").hide();
        $(_pageId + " #rewardResult").hide();
        $(_pageId + " .dragon_year_main").find(".auto").remove();
        $(_pageId + " .dragon_year_main").find(".right").remove();
        $(_pageId + " #qrImg").empty();
        $(_pageId + " #qrCode").empty();
        $(_pageId + " #qrImg").hide();
        $(_pageId + " #qrCode").show();
        $(_pageId + " .shareImg .couplet_top").remove();
        $(_pageId + " .shareImg .couplet_left").remove();
        $(_pageId + " .shareImg .couplet_right").remove();
        isUnderWay = false;
        shareTemplateInfo = "";
        activity_id = "";
        activityAiInfo = "";
        aiType = "";
        clearTimeout(pageTouchTimer);

    };

    var dragonYearAIChatModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = dragonYearAIChatModule;
});
