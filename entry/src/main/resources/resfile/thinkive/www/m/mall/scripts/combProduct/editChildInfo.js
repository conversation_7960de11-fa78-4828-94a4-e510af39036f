// 修改子女信息
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        putils = require("putils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageUrl = "combProduct/editChildInfo",
        
        _pageId = "#combProduct_editChildInfo ";
        require('../../js/zepto.min.js');
        require('../../js/iscroll.js');
        require('../../js/iosSelect.js');
        var selectDate = require('../../js/selectDate.js');
        require('../../css/iosSelect.css');
    var tools = require("../common/tools");
    var info;//缓存子女信息
    var uphobby;
    var nick_comb_name_suffix;
    var supplementaryContent;
    function init() {
        selectDate(_pageId + '#birthday', 0, _pageId);
        //页面埋点初始化
        tools.initPagePointData();
        info = appUtils.getPageParam() ? appUtils.getPageParam() : {};
        //获取nick_comb_name后缀
        nick_comb_name_suffix = info.nick_comb_name.split(info.nick_name)[1] ? info.nick_comb_name.split(info.nick_name)[1] : '';
        $(_pageId + " #nick_name").val(info.nick_name ? info.nick_name : '');
        supplementaryContent = info.plan_type == '4' ? '存钱罐' : '攒钱计划';
        if(info.plan_type=="1"||info.plan_type=="4"){            
            $(_pageId + " .baby").show();
            $(_pageId + " .target").hide();
            $(_pageId + " #birthday").val(info.birthday ? tools.ftime(info.birthday) : '');
            $(_pageId + " #hobby").val(info.hobby ? info.hobby : '');
        }else{
            $(_pageId + " .target").show();
            $(_pageId + " .baby").hide();
            $(_pageId + " #target").val(info.hobby ? info.hobby : '');
        }

       
        $(_pageId + " .header img").attr("src", info.avatar_url ? info.avatar_url : "../mall/images/icon_app.png")
        
    }


    function bindPageEvent() {
        //更换头像
        appUtils.bindEvent($(_pageId + " .show_dio"), function () {
            var param = {};
            param["funcNo"] = "50273";
            param["moduleName"] = "mall";
            param["fileName"] = "headerImg";
            if (require("gconfig").platform == "2") {
                param["titleColor"] = "#111111";
            }
            param.paramExt = {
                multi: false,
                type: "children_header"
            }
            param["cutFlag"] = "0";
            param["compress"] = "0.8";
            param["width"] = "500";
            param["height"] = "500";
            tools.fileImg(_pageId, param);
        });
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " footer"), async function () {
            var regexHobby = /^[\u4e00-\u9fa5a-zA-Z0-9\s.,!?，。！？、；：“”‘’（）【】￥$%^&*+=_~`'"\-\\|]+$/; //爱好正则表达式
            let regexName = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
            let birthday = $(_pageId + " #birthday").val() ? $(_pageId + " #birthday").val().replace(/-/g, "") : '';
            let avatar_url = $(_pageId + " .header img").attr("src") ? $(_pageId + " .header img").attr("src") : '';
            let nick_name = $(_pageId + " #nick_name").val() ? $(_pageId + " #nick_name").val().trim() : '';
            let hobby = $(_pageId + " #hobby").val() ? $(_pageId + " #hobby").val().trim() : '';
            let target = $(_pageId + " #target").val() ? $(_pageId + " #target").val().trim() : '';
            if(!nick_name) return layerUtils.iAlert('昵称不能为空');
            // let res = await getUserIsNickName() //判断昵称是否重复
            // if(res.existNick > 0) return layerUtils.iAlert(`${info.plan_type == 4 ? '存钱罐名称' : '计划名称'}已存在，请重新输入`);
            if(hobby && hobby!='' && hobby.length > 10) return layerUtils.iAlert("爱好不能超过十个字符，请修改");
            if(hobby && !regexHobby.test(hobby)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
            if(target && target!='' && target.length > 10) return layerUtils.iAlert("目标不能超过十个字符，请修改");
            if(target && !regexHobby.test(target)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
            if(!regexName.test(nick_name)) return layerUtils.iAlert("您可能输入表情等特殊字符，请修改");
            if(!tools.validateInputName(nick_name)) return layerUtils.iAlert("昵称字数不能超过5个字符，请修改");
            if(avatar_url == '../mall/images/icon_app.png') avatar_url = ''; //默认头像为空
            if(info.plan_type=="1"||info.plan_type=="4"){
                uphobby = hobby
            }else{
                uphobby = target
            }
            let play_type_remark = {
                '1':'的攒钱计划',
                '2':'的攒钱计划',
                '3':'的攒钱计划',
                '4':'的存钱罐'
            }
            let param = {
                nick_name:nick_name,
                birthday:birthday,
                hobby:uphobby,
                avatar_url:avatar_url,
                comb_code:info.comb_code,
                nick_comb_name:nick_name + nick_comb_name_suffix
            }
            // console.log(param)
            service.reqFun102199(param, (res) => {
                if (res.error_no == '0') {
                    //上传成功 返回上个页面
                    pageBack();
                } else {
                    layerUtils.iAlert(res.error_info);
                }
            })
        });
    }
    //判断昵称是否重复
    async function getUserIsNickName(){
        return new Promise(async (resolve) => {
            let data = {
                // series_id:productInfo.prod_id ? productInfo.prod_id : '',
                nick_name:$(_pageId + " .nick_name").val(),
                nick_comb_name:$(_pageId + " .nick_name").val().trim() +`的${supplementaryContent}`
            }
            service.reqFun102195(data, async (data) => {
                if (data.error_no == '0') {
                    resolve(data.results[0])
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        })
    }
    function pageBack(){
        appUtils.pageBack();
    }
    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #nick_name").val("");
        $(_pageId + " #birthday").val("");
        $(_pageId + " #hobby").val("");
        $(_pageId + " .header img").attr("src", "../mall/images/icon_app.png")
        $(_pageId + " .target").hide();
        $(_pageId + " .baby").hide();
        $(_pageId + " #target").val("");
    }
    //重置时间框
    function resetInputDate() {
        
    }

    //返回页面
    function pageBack() {
        appUtils.pageBack();
    }

    var combProduct_returnsDetailed = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = combProduct_returnsDetailed;
});
