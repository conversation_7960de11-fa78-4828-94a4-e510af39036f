// 投顾交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageId = "#combProduct_combTransaction ",
        _pageCode = "combProduct/combTransaction",
        tools = require("../common/tools");
    require('../../js/zepto.min.js');
    require('../../js/iscroll.js');
    require('../../js/iosSelect.js');
    var selectDate = require('../../js/selectDate.js');
    require('../../css/iosSelect.css');
    var isEnd = false;
    var startTime = "";
    var financial_prod_type = '';
    var endTime = "";
    var _cur_page = 1;
    var _num_per_page = 10;
    var is_transit, busi_code;
    function init() {
        _cur_page = 1;
        is_transit = appUtils.getSStorageInfo("is_transit");
        busi_code = appUtils.getSStorageInfo("busi_code");
        if (is_transit == '1') {
            $(_pageId + " .btn_filter").hide();
        } else {
            $(_pageId + " .btn_filter").show();
        }
        //页面埋点初始化
        tools.initPagePointData();
        financial_prod_type = appUtils.getSStorageInfo("financial_prod_type");
        $(_pageId + ".olay").remove();
        selectDate(_pageId + '#startTime', 0, _pageId);
        selectDate(_pageId + '#endTime', 1, _pageId);
        //用于接收详情页带回的参数，找回跳转前的筛选结果
        if (appUtils.getSStorageInfo("qry_condition")) {
            var qry_condition = appUtils.getSStorageInfo("qry_condition");
            appUtils.clearSStorage("qry_condition");
            $(_pageId + " #query_type li a").removeClass("active");
            if (qry_condition.busi_type) {
                $(_pageId + "#query_type [data-value=" + qry_condition.busi_type + "]").addClass("active");
            } else {
                $(_pageId + " #query_type li a").eq(0).addClass("active");
            }
            $(_pageId + ' #startTime').attr('time', qry_condition.startTime).val(qry_condition.startTime);
            $(_pageId + ' #endTime').attr('time', qry_condition.endTime).val(qry_condition.endTime);
            $(_pageId + " #query_date li a").removeClass("active");
            if (qry_condition.date_value) {
                $(_pageId + "#query_date [data-value=" + qry_condition.date_value + "]").addClass("active");
            } else {
                $(_pageId + " #query_date li a").eq(0).addClass("active");
            }
        } else {
            resetInputDate();
            initBusiType();
        }
        getUsertransaction(false);

    }

    function bindPageEvent() {
        //筛选日期
        appUtils.bindEvent($(_pageId + " .btn_filter"), function () {
            $(_pageId + " #jymx_filter").show();
            $(_pageId + " #filter_layer").show();
        });
        //快捷键选择时间
        appUtils.bindEvent($(_pageId + " #query_date li a"), function () {
            $(_pageId + " #query_date li a").removeClass("active");
            $(this).addClass("active");
            var data_value = $(this).attr("data-value");
            var endTime = new Date();
            var startTime = new Date();
            if (data_value == "30") {
                startTime.setMonth(endTime.getMonth() - 1);
            } else if (data_value == "90") {
                startTime.setMonth(endTime.getMonth() - 3);
            } else if (data_value == "180") {
                startTime.setMonth(endTime.getMonth() - 6);
            } else {
                startTime = "";
                endTime = "";
            }
            setInputDate("endTime", endTime);
            setInputDate("startTime", startTime);
        });

        //确定/重置按钮绑定事件
        appUtils.bindEvent($(_pageId + " .record_filter #query-button a"), function () {
            _cur_page = 1;
            var dataType = $(this).attr("data-value");
            if (dataType == "reset") {
                //重置按钮
                resetInputDate();
            } else if (dataType == "confirm") {
                startTime = $(_pageId + " #startTime").attr("time");
                endTime = $(_pageId + " #endTime").attr("time");
                getUsertransaction(false);
                $(_pageId + " .record_filter").hide();
                $(_pageId + " .pop_layer").hide();
            }
        });
        //关闭筛选层
        appUtils.bindEvent($(_pageId + " #filter_layer"), function () {
            $(_pageId + " #jymx_filter").hide();
            $(_pageId + " #filter_layer").hide();
        });
        //点击取消时间控件
        appUtils.preBindEvent($(_pageId), $(_pageId + " .olay"), function (e) {
            //取消冒泡
            if (e || e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.CancelBubble = true;
            }
            //获取事件源
            var node = $(e.srcElement || e.target);
            if (node.hasClass("olay")) {
                $(_pageId + " .olay").remove();
            }
        }, "click");

        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转详情
        appUtils.preBindEvent($(_pageId + " .finance_pro"), ".trade_box", function (e) {
            var info = JSON.parse($(this).find(".info").text());
            info['financial_prod_type'] = financial_prod_type;
            //跳转详情
            judge_busi_code_e(info);
        }, "click");

    }

    function getUsertransaction(isAppendFlag) {
        isEnd = false;
        endTime = $(_pageId + '#endTime').attr('time');
        if (!validatorUtil.isEmpty(endTime)) {
            endTime = endTime.replace(/-/g, "");
        }
        startTime = $(_pageId + '#startTime').attr('time');
        if (!validatorUtil.isEmpty(startTime)) {
            startTime = startTime.replace(/-/g, "");
        }
        var param = {
            "start_date": startTime,
            "end_date": endTime,
            "cur_page": _cur_page,
            "num_per_page": _num_per_page,
            "fund_code": "",
            "sub_busi_code": $(_pageId + " #query_type li .active").attr("data-code"),
            "is_transit": is_transit ? is_transit : '',
            "busi_code": busi_code ? busi_code : '',
            "financial_prod_type": financial_prod_type
        };
        var trsFundCode = appUtils.getSStorageInfo("trsFundCode");
        if (trsFundCode) {
            param.fund_code = trsFundCode;
        }
        var gettransActionCallBack = function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var totalPages = data.results[0].data.totalPages; //总页数
                var results = data.results[0].data.data;
                var str = "";
                for (var i = 0; i < results.length; i++) {
                    str += tradeList(results[i]);
                }
                if (totalPages == _cur_page) {
                    isEnd = true;
                    str += '<div class="nodata">没有更多数据</div>'
                }
                if (totalPages == 0 && results.length == 0) {
                    isEnd = true;
                    str = '<div class="nodata">暂无数据</div>'
                }
                $(_pageId + " #v_container_productList").show();
                if (isAppendFlag) {
                    $(_pageId + " .finance_pro").append(str);
                } else {
                    $(_pageId + " .finance_pro").html(str);
                }
                $(_pageId + " .visc_pullUp").hide();
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
                pageScrollInit();
            } else {
                layerUtils.iAlert(error_info);
            }
        };
        service.reqFun102162(param, gettransActionCallBack);
    }

    function initBusiType() {
        //交易类型（1.现金宝；2.公募；3.私募）产品整合后4：整合后交易类型 5.投顾
        var param = {
            "trans_type": "5",
        }
        service.reqFun102059(param, function (resultVo) {
            var str = "<li><a class='active' data-value=''>所有</a></li>";
            if (resultVo.error_no == "0") {
                var dataList = resultVo.results;
                for (var i = 0; i < dataList.length; i++) {
                    str += `<li><a data-code="${(dataList[i].subBusiType)}" data-value=${dataList[i].busi_type}>${dataList[i].busiName}</a></li>`
                }
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }
            $(_pageId + " #query_type").html(str);
            //快捷键选择类型
            appUtils.bindEvent($(_pageId + " #query_type li a"), function () {
                $(_pageId + " #query_type li a").removeClass("active");
                $(this).addClass("active");
            });
        })
    }

    /**
     * 上下滑动刷新事件
     * */

    function pageScrollInit() {
        var height = $(_pageId + " #v_container_productList").offset().top;
        var height2 = $(window).height() - height - 50 + 44;
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    _cur_page = 1;
                    endTime = "";
                    getUsertransaction(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        _cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getUsertransaction(true);
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
            if (isEnd) {//可能有当前页为1总页数为0的情况
                $(_pageId + " .visc_pullUp").hide();
            } else {
                $(_pageId + " .visc_pullUp").show();
            }
        }
    }

    //交易记录列表
    function tradeList(data) {
        var amt = "";
        // 投顾定投申购 12221  投顾银行卡定投  12222
        if (data.sub_busi_code_e == "12218" || data.sub_busi_code_e == "12221" || data.sub_busi_code_e == "12222" || data.sub_busi_code_e == '12303' ) {
            amt = tools.fmoney(data.app_amt) + "元";
        } else if (data.sub_busi_code_e == "12419" || data.sub_busi_code_e == "12423" || data.sub_busi_code_e == "12425") {
            amt = common.floatMultiply(data.ack_vol, 100) + "%";
        } else if (data.sub_busi_code_e == "14301") {
            amt = tools.fmoney(data.ack_vol) + "份";
        } else if (data.sub_busi_code_e == "12420" || data.sub_busi_code_e == '12421') {
            amt = tools.fmoney(data.ack_vol) + "元";
        } else if (data.sub_busi_code_e == "14302" || data.sub_busi_code_e == "14303") {
            amt = tools.fmoney(data.ack_amt) + "元";
        } else if (data.sub_busi_code_e == "14201" || data.sub_busi_code_e == "14202") {  // 强赎到宝 强赎到卡
            amt = tools.fmoney(data.ack_vol) + "份";
        }
        var remark1 = data.remark1,
            remarkStr = "";
        if (data.sub_busi_code_e != "12208" && data.sub_busi_code_e != "12413") {
            if (remark1) {
                remarkStr = '<p class="remark1" style="display: inline-block;width: 100%;">备注：' + remark1 + '</p>'
            }
        }
        var status = "";
        if (data.sub_busi_code_e == "12419" || data.sub_busi_code_e == "12423" || data.sub_busi_code_e == "12425") { // 赎回
            status = tools.fundDataDict(data.trans_status, "pri_redeem_status_name")
        } else if (data.sub_busi_code_e == "14301" || data.sub_busi_code_e == "14302" || data.sub_busi_code_e == "14303") { // 分红
            status = tools.fundDataDict(data.trans_status, "pri_bonus_trans_status_name")
        } else if (data.sub_busi_code_e == "30101") { // 调仓
            status = tools.fundDataDict(data.trans_status, "tc_trans_status_name")
        } else if (data.sub_busi_code_e == "14201" || data.sub_busi_code_e == "14202") {  // 强赎到宝 强赎到卡
            status = tools.fundDataDict(data.trans_status, "comb_qs_trans_status_name")
        } else {
            status = tools.fundDataDict(data.trans_status, "pri_trans_status_name")
        }
        var html = '<div class="trade_box" operationType="1" operationId="trade_box" operationName="详情">' +
            '<div class="icon">' +
            '</div>' +
            '<div class="fundInfo">' +
            '<p class="info" style="display: none">' + JSON.stringify(data) + '</p>' +
            '<p>' + data.sub_busi_code + '</p>' +
            '<p style="width:1.6rem">' + data.comb_name + '</p>' +
            '<p>' + tools.ftime(data.crt_date + data.crt_time) + '</p>' +
            '</div>' +
            `<div class="result ${data.sub_busi_code_e == "12420" || data.sub_busi_code_e == '12421' ? "" : "right_icon"} " style="height: 100%">` +
            '<p>' + amt + '</p>' +
            '<p>' + status + '</p>' +
            '</div>' +
            remarkStr +
            '</div>'
        return html;
    }

    //设置时间控件
    function setInputDate(id, date) {
        if (!date) {
            $(_pageId + " #" + id).attr("time", date);
            $(_pageId + " #" + id).val(date);
            return;
        }
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var date = date.getDate();
        $(_pageId + " #" + id).attr("data-year", year);
        $(_pageId + " #" + id).attr("data-month", month);
        $(_pageId + " #" + id).attr("data-date", date);
        var datastr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (date < 10 ? "0" + date : date);
        $(_pageId + " #" + id).attr("time", datastr);
        $(_pageId + " #" + id).val(datastr);
    }

    //重置时间框
    function resetInputDate() {
        $(_pageId + " #query_type li a").removeClass("active").eq(0).addClass("active");
        //快捷时间筛选
        $(_pageId + " #query_date li a").removeClass("active").eq(0).addClass("active");
        $(_pageId + '#startTime').attr("time", "").val("");
        $(_pageId + '#endTime').attr("time", "").val("");
    }

    //跳转详情
    function judge_busi_code_e(info) {
        var sub_busi_code_e = info.sub_busi_code_e;
        var obj = {
            "12218": "combProduct/combTrsDetailsBuy",
            "12419": "combProduct/combTrsDetailsSell",
            "12423": "combProduct/combTrsDetailsSell",
            "12425": "combProduct/combTrsDetailsSell",
            "14301": "combProduct/combTrsDetailsOther",
            "30101": "combProduct/combTrsDetailsOther", // 组合调仓
            "14302": "combProduct/combTrsDetailsFh",
            "14303": "combProduct/combTrsDetailsFh",
            "14201": "combProduct/combTrsDetailsFore",
            "14202": "combProduct/combTrsDetailsFore",
            "12221": "combProduct/combTrsDetailsBuy",
            "12222": "combProduct/combTrsDetailsBuy",
            "12303": "combProduct/combTrsDetailsBuy", //投顾预约申购
        }
        var param = {
            date_value: $(_pageId + " #query_date li a.active").attr("data-value"),
            startTime: $(_pageId + " #startTime").val(),
            endTime: $(_pageId + " #endTime").val(),
            busi_type: $(_pageId + " #query_type li a.active").attr("data-value")
        }
        appUtils.setSStorageInfo("qry_condition", param); //保留此次筛选条件
        if (obj[sub_busi_code_e]) {
            //从投顾交易记录进入
            appUtils.setSStorageInfo("pageSourceComb",'2');
            appUtils.pageInit(_pageCode, obj[sub_busi_code_e], info);
        }
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        _cur_page = 1;
        $(_pageId + " #start_date").val("查询日期");
        $(_pageId + " #jymx_filter").hide();
        $(_pageId + " #filter_layer").hide();
        $(_pageId + ".olay").hide();
        isEnd = false;
        startTime = "";
        endTime = "";
        $(_pageId + " .finance_pro").html("");
    }

    function pageBack() {
        appUtils.setSStorageInfo("trsFundCode", '');
        appUtils.pageBack();
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
