<div class="page" id="safety_userPassword" data-pageTitle="忘记密码" data-refresh="true">
<section class="main fixed" data-page="home">
		<header class="header">
			<div class="header_inner bg_header">
				<a href="javascript:void(0);" class="icon_back icon_gray"><span>返回<!-- 密码管理 --></span></a>
				<h1 class="text_gray text-center">忘记密码</h1>
			</div>
		</header>
		<article>
			<!-- FORGET_PSD START -->
			<div class="forget_psd">
				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label class="short_label" >手机号</label>
						<input custom_keybord="0" type="tel" id="phoneNum"  class="ui input" maxlength="11" placeholder="请输入您注册时的手机号码" />
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2">
						<label class="short_label" >图形验证码</label>
						<input custom_keybord="0" id="tuxingCode"  maxlength="4" class="ui input code_input" placeholder="请输入验证码"/>
						<a  class="pic_code" id="getCode" data-state="true"  style="padding:0 0;width:82px;"><img src="" alt=""></a>
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2" id="yzmBox">
						<label class="short_label" >验证码</label>
						<input custom_keybord="0" type="text" maxlength="6" id="verificationCode" class="ui input code_input" placeholder="请输入验证码" />
						<a href="javascript:void(0)" data-state="true" id="getYzm">获取验证码</a>
					</div>
				</div>

				<!-- 语音验证码 -->
				<div class="finance_det recharge_det">
					<dl class="bank_limit">
						<dt></dt>
						<dd id="talkCode"   style="display:none">如收不到短信 &nbsp;&nbsp;<span id="getTalk" style="color:blue">语音获取</span><dd>
					</dl>
				</div>
				<!-- 语音验证码 -->

				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2" id="new_password">
						<label class="short_label" >新密码</label>
						<input custom_keybord="0" style="display:none" type="password" maxlength="16" disabled id="newPwd" class="ui input" placeholder="请输入新密码" />
						<div class="placeholderPsd">请输入密码</div>
						<span style="display:none" class="cursor-bink">&nbsp;</span>
					</div>
				</div>
				<div class="grid_03 grid_02 grid">
					<div class="ui field text rounded input_box2" id="new_password_repeat">
						<label class="short_label" >确认密码</label>
						<input custom_keybord="0" style="display:none" type="password" maxlength="16" disabled id="newPwd2" class="ui input" placeholder="请再次输入新密码" />
						<div class="placeholderPsd">请输入密码</div>
						<span style="display:none" class="cursor-bink">&nbsp;</span>
					</div>
				</div>
			</div>
		    <div class="grid_02 mt20">
				<a href="javascript:void(0)" id="nextStep" class="ui button block rounded btn_register">确定</a>
			</div>
			<!-- FORGET_PSD END -->
		</article>
	</section>
</div>
