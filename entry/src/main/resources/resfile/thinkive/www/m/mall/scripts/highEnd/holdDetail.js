// 晋金高端持仓详情页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        _pageCode = "highEnd/holdDetail",
        _pageId = "#highEnd_holdDetail ";
    var highEndHoldDetail;

    function init() {
        highEndHoldDetail = appUtils.getSStorageInfo("list_productInfo");
        //空数据处理
        highEndHoldDetail = tools.FormatNull(highEndHoldDetail);
        // if(highEndHoldDetail.cust_fund_type == "0") {  //持有
        //     $(_pageId + " .asset_bonus").show();
        // } else if(highEndHoldDetail.cust_fund_type == "1") {  //在途
        //     $(_pageId + " .asset_bonus").hide();
        // }

        $(_pageId + " .fund_name").text(highEndHoldDetail.fund_sname);
        $(_pageId + " .fund_code").text(highEndHoldDetail.fund_code);
        $(_pageId + " .prod_type2").text(etf(highEndHoldDetail.prod_type2));
        var accrual_basis = highEndHoldDetail.accrual_basis ? tools.fmoney(highEndHoldDetail.accrual_basis) + "%" : "--";
        $(_pageId + " .fund_vol").text(tools.fmoney(highEndHoldDetail.cost_money));
        $(_pageId + " .accrual_basis").text(accrual_basis);
        $(_pageId + " .closed_length").text(highEndHoldDetail.closed_length);
        var interest_start_date = highEndHoldDetail.interest_start_date;
        var due_date = highEndHoldDetail.due_date;
        $(_pageId + " .interest_start_date").text(tools.ftime(interest_start_date));
        $(_pageId + " .due_date").text(tools.ftime(due_date));

//        $(_pageId + " .dividend_method").text(fhWay(highEndHoldDetail.defdividend_method));
        $(_pageId + " .endflag_method").text(dfWay(highEndHoldDetail.endflag_method));

        var defdividend_method = highEndHoldDetail.defdividend_method;
        var dividend_status = highEndHoldDetail.dividend_status;
        if (dividend_status == "01") { //受理成功
        	$(_pageId + " .dividend_method").text(fhWay(defdividend_method)+ "(修改中)");
        } else {
        	$(_pageId + " .dividend_method").text(fhWay(defdividend_method));
        }
        
        $(_pageId + " #fh").attr('fund_code', highEndHoldDetail.fund_code);
        $(_pageId + " #fh").attr('fund_name', highEndHoldDetail.fund_name);
    }

    /**
     * 基金类型
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function etf(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "00":
                dictName = "综合性";
                break;
            case "10":
                dictName = "股票型";
                break;
            case "20":
                dictName = "混合型";
                break;
            case "30":
                dictName = "债券型";
                break;
            case "40":
                dictName = "货币型";
                break;
            case "50":
                dictName = "商品型";
                break;
            case "60":
                dictName = "FOF 型";
                break;
            case "70":
                dictName = "QDII 型";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 分红方式
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function fhWay(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "1":
                dictName = "红利再投";
                break;
            case "2":
                dictName = "分红到晋金宝";
                break;
            case "3":
                dictName = "分红到晋金宝";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }

    /**
     * 到期兑付方式
     * @param dictVal 字典值
     * @returns {String} 字典项名称
     */
    function dfWay(dictVal) {
        dictVal += "";
        var dictName = "";
        switch (dictVal) {
            case "0":
                dictName = "买入下一期";
                break;
            case "1":
                dictName = "自动赎回到银行卡";
                break;
            case "2":
                dictName = "自动赎回到晋金宝";
                break;
            default:
                dictName = "--";
                break;
        }
        return dictName;
    }


    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        //跳转客服页面
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //修改分红方式
        appUtils.bindEvent($(_pageId + " #fh"), function () {
            var bonus_if_alter = highEndHoldDetail.bonus_if_alter;
            var dividend_status = highEndHoldDetail.dividend_status;
            //0 不可更改  1可能改
            if (bonus_if_alter == "0") {
                layerUtils.iAlert("该产品不能修改分红方式");
                return;
            }
            if (dividend_status == "01") {
                layerUtils.iAlert("分红方式确认前将不能再次修改");
                return;
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/modifyDividendsWay");
            });
        });
        //修改到期方式
        appUtils.bindEvent($(_pageId + " #dq"), function () {
            var back_way_alter = highEndHoldDetail.back_way_alter;
            //0 不可更改  1可能改
            if (back_way_alter == "0") {
                layerUtils.iAlert("该产品不能修改到期方式");
                return;
            }
            let invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            if(invalidFlag == 1) return tools.pageTo_evaluation(_pageCode)
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            common.changeCardInter(function () {
                appUtils.pageInit(_pageCode, "highEnd/modifyExpireWay");
            });
        });


        //产品详情
        appUtils.bindEvent($(_pageId + " .fund_entry"), function () {
            appUtils.setSStorageInfo("productInfo", highEndHoldDetail);
            appUtils.pageInit(_pageCode, "highEnd/productDetail");
        });

    }

    function destroy() {
        // $(_pageId + " .asset_bonus").hide();
        $(_pageId + " .fund_name").text("");
        $(_pageId + " .fund_code").text("");
        $(_pageId + " .prod_type2").text("");
        $(_pageId + " .fund_vol").text("");
        $(_pageId + " .accrual_basis").text("");
        $(_pageId + " .closed_length").text("");
        $(_pageId + " .establish_date").text("--");
        $(_pageId + " .due_date").text("--");
        $(_pageId + " .dividend_method").text("");
        $(_pageId + " .endflag_method").text("");

    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.clearSStorage("highEndHoldDetail");
        appUtils.pageBack();
    }

    var highEndDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highEndDetail;
});
