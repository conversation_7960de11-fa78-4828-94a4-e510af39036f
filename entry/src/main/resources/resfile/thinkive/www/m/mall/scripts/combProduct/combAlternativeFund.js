// 买入卖出规则
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        tools = require("../common/tools"),
        common = require("common"),
        _pageId = "#combProduct_combAlternativeFund",
        _pageCode = "combProduct/combAlternativeFund";
    var ut = require("../common/userUtil");
    var productInfo;

    function init() {
        productInfo = appUtils.getSStorageInfo("productInfo");
        reqFun102183();
        //页面埋点初始化
        tools.initPagePointData();
    }

    function reqFun102183() {
        service.reqFun102183({
            comb_code: productInfo.comb_code
        }, async (data) => {
            if (data.error_no == '0') {
                if (data.results && data.results.length) {
                    let html = `
                        <div class="fund_box" style="font-size: 16px;font-weight: bold;">
                            <div>基金名称</div>
                            <div>基金类型</div>
                        </div>
                    `;
                    data.results.forEach(item => {
                        html += `
                        <div class="fund_box" >
                            <div class="fund_info">
                                <div>${item.prod_sname}</div>
                                <div style="height: 25px;line-height: 25px;">${item.prod_id}${item.is_related == '1' ? `<span class="related_icon">关联产品</span>` : ''}</div>
                            </div>
                            <div class="fund_type">
                                <div style="width: 0.5rem">${item.investment_name}</div>
                            </div>
                        </div>
                        `
                    })
                    $(_pageId + " #fundList").html(html);
                }
                else {
                    $(_pageId + " .no_date").show();
                    $(_pageId + " #fundList").css({"display": "none"});
                }
                // resolve(data.results[0])
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })

    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .no_date").hide();
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var combAlternativeFundModule = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = combAlternativeFundModule;
});
