<div class="page" id="safety_setEmial" data-pageTitle="设置邮箱">
    <section class="main fixed" data-page="home">
        <header class="header">
            <div class="header_inner bg_header">
                <a href="javascript:void(0);" id="getBack" class="icon_back icon_gray"><span>返回</span></a>
                <h1 class="text_gray text-center">设置邮箱</h1>
            </div>
        </header>
        <article>
            <!-- FORGET_PSD START -->
            <div class="forget_psd">
                <div class="check_tips slidedown in">
                    <p id="userInfo">您好！您正在为账户 设置电子邮箱。</p>
                </div>
                <div class="grid_03 grid_02 grid">
                    <div class="ui field text rounded input_box2">
                        <label class="short_label">电子邮箱</label>
                        <input custom_keybord="0" id="set_emial" type="text" class="ui input" placeholder="请输入电子邮箱" maxlength="25"/>
                    </div>
                </div>
            </div>
            <div class="grid_02 mt20">
                <a href="javascript:void(0);" id="submit" class="ui button block rounded btn_register pop in">确定</a>
            </div>
            <!-- FORGET_PSD END -->
        </article>
    </section>
</div>
