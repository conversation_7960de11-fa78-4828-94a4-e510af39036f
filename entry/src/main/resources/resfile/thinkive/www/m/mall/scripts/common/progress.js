/**
 * 进度条事件
 */
define((require, exports, module)=> {
	const progress ={
        download:(url, filename) =>{
            progress.getBlob(url).then(blob => {
                // console.log(blob)
                progress.saveAs(blob, filename);
            });
        },
        //生成十一位随机数文件名
        rand: (m)=> {
            m = m > 16 ? 16 : m;
            var num = Math.random().toString();
            if(num.substr(num.length - m, 1) === '0') {
                return progress.rand(m);
            }
            return num.substring(num.length - m)
        },
        getBlob: (url)=> {
            return new Promise(resolve => {
                let that = this; // 创建XMLHttpRequest，会让this指向XMLHttpRequest，所以先接收一下this
                const xhr = new XMLHttpRequest();
                xhr.open("GET", url, true);
                //监听进度事件
                xhr.addEventListener(
                    "progress",
                    function(evt) {
                        // console.log(evt)
                        if (evt.lengthComputable) {
                            let percentComplete = evt.loaded / evt.total;
                            //获取进度百分比
                            that.percentage = parseInt(percentComplete * 100)
                            //渲染进度条
                            progress.move(that.percentage)
                        }
                    },
                    false
                );
        
                xhr.responseType = "blob";
                xhr.onload = () => {
                    if (xhr.status === 200) {
                        resolve(xhr.response);
                    }
                };
                xhr.onreadystatechange = ()=> {  
                    if (xhr.readyState === 4) {  
                        if (xhr.status === 200) {  
                        } else {
                            // 错误处理
                            //    console.log("Error", xhr.statusText);  
                            return $('#updateBtn').text("获取文件资源失败，请检查服务器配置");
                        }  
                
                    }  
                
                }
                xhr.send();
            });
        },
        /**
         * 保存
         * @param  {Blob} blob
         * @param  {String} filename 想要保存的文件名称
         */
        saveAs:(blob,filename)=> {
            // ie的下载
            if (window.navigator.msSaveOrOpenBlob) {
                navigator.msSaveBlob(blob, filename);
            } else {
                // 非ie的下载
                const link = document.createElement("a");
                const body = document.querySelector("body");
                link.href = window.URL.createObjectURL(blob);
                link.download = filename;
                // fix Firefox
                link.style.display = "none";
                body.appendChild(link);
                link.click();
                body.removeChild(link);
                window.URL.revokeObjectURL(link.href);
            }
        },
        /**
         * 进度条展示
         * @param {num} 进度值
         */
        move:(num)=> {
            let elem = document.getElementById("myBar");   
            elem.style.width = num+'%'; 
            elem.innerHTML = num * 1  + '%';
            if(num >= 100){
                return $('#updateBtn').text("已完成");
            }
        },
        /**
         * 点击下载 针对安卓手机下载apk包
        */
        new_download:(url,rand_num)=>{
            progress.download(url,rand_num+'.apk')
        }
    };
    // 暴露对外的接口
    module.exports = progress;
})
