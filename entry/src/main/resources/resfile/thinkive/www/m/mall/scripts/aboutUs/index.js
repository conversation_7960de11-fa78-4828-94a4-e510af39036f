// 关于我们
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        _pageId = "#aboutUs_index ";

    function init() {

    }

    function bindPageEvent() {
        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });
        //公司简介
        appUtils.bindEvent(_pageId + " #gsjj", function () {
            appUtils.pageInit("aboutUs/index", "aboutUs/gsjj");
        });
        //人员公示
        appUtils.bindEvent(_pageId + " #rygs", function () {
            appUtils.pageInit("aboutUs/index", "aboutUs/rygs");
        });
        //联系我们
        appUtils.bindEvent(_pageId + " #lxwm", function () {
            appUtils.pageInit("aboutUs/index", "aboutUs/lxwm");
        });
        //资质荣誉
        appUtils.bindEvent(_pageId + " #zzry", function () {
            appUtils.pageInit("aboutUs/index", "aboutUs/zzry");
        });
    }

    function destroy() {

    }

    function pageBack() {
        appUtils.pageBack();
    }

    var aboutJJS = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = aboutJJS;
});