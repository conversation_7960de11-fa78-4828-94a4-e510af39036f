// 手机注册
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#redPack_index ";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var external = require("external");
    var ut = require("../common/userUtil");
    var activitiesType;

    function init() {
        activitiesType = appUtils.getPageParam("activitiesType") || "";
        // queryNum();
    }

    //绑定事件
    function bindPageEvent() {

        //红包
        appUtils.bindEvent($(_pageId + " #redPackage"), function () {
            appUtils.pageInit("redPack/index", "redPack/redPackage", {"activitiesType": activitiesType});
        });

        //点击返回
        appUtils.bindEvent(_pageId + " .icon_back", function () {
            pageBack();
        });

    }

    //0 红包 3体验金 4现金券
    function queryNum() {
        var param = {"state": "0", "reward_code": "0"};
        service.queryRedPackage(param, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + " #redPackage i").html(data.results.length);
            } else {
                layerUtils.iAlert(error_info);
            }
        });

        var params = {"state": "0", "reward_code": "3"};
        service.queryRedPackage(params, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + " #tyj i").html(data.results.length);
            } else {
                layerUtils.iAlert(error_info);
            }
        });

        var paramsr = {"state": "0", "reward_code": "4"};
        service.queryRedPackage(paramsr, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                $(_pageId + " #cashVolume i").html(data.results.length);
            } else {
                layerUtils.iAlert(error_info);
            }
        });

    }

    function pageBack() {
        appUtils.pageBack();
        // var activitiesType = appUtils.getPageParam("activitiesType") || "";
        // var cardno = ut.getUserInf().bankAcct;
        // if (cardno == null || cardno == "") {
        //     appUtils.pageInit("redPack/index", "account/myAccountNoBind", {});
        // } else if (activitiesType) {
        //     appUtils.pageInit("redPack/index", "numberBenefits/benefitsList", {"activitiesType": activitiesType});
        // } else {
        //     appUtils.pageInit("redPack/index", "account/myAccount", {});
        // }
    }

    function destroy() {
        $(_pageId + " #redPackage i").html("");
        $(_pageId + " #tyj i").html("");
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
