// 交易记录
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        _pageId = "#inclusive_transactionDetailsSell ";
    var ut = require("../common/userUtil");
    var tools = require("../common/tools");
    var remarkObj = {
        "12402": "主动赎回到卡",// 主动赎回到卡
        "12404": "主动赎回到宝",//主动赎回到宝
        "12406": "到期自动赎回", // 到期自动赎回到宝
        "12408": "到期自动赎回", // 到期自动赎回到卡
    }

    function init() {
        var param = appUtils.getPageParam();
        reqFun102066(param);
    }

    function bindPageEvent() {
        //返回
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

    }

    function reqFun102066(param) {
        service.reqFun102066(param, function (data) {
            if (data.error_no != "0") {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];
            if (!results || results.length == 0) {
                return;
            }
            //数据处理 空 和 --
            results = tools.FormatNull(results);

            //交易份额
            var trans_vol = tools.fmoney(results.trans_vol);
            trans_vol = trans_vol + "份";
            //交易状态
            var trans_status = results.trans_status;
            var trans_status_name = tools.fundDataDict(trans_status, "pub_redeem_status_name");

            //待确定，暂时展示三个成功状态
            var item0 = $(_pageId + " .rule_box .item")[0];
            var item1 = $(_pageId + " .rule_box .item")[1];
            var item2 = $(_pageId + " .rule_box .item")[2];
            // if (trans_status != "8") {
            //     //显示一个成功状态
            //     $(item0).find(".name").removeClass("undone");
            //     $(item1).find(".name").addClass("undone");
            //     $(item2).find(".name").addClass("undone");
            // } else if (trans_status == "8") {
            //     //显示两个成功状态
            //     $(item0).find(".name").removeClass("undone");
            //     $(item1).find(".name").removeClass("undone");
            //     $(item2).find(".name").addClass("undone");
            // }

            //产品名称
            var prod_name = results.prod_name;
            //产品代码
            var prod_code = "(" + results.prod_code + ")";
            //赎回日期（交易日期）
            var trans_date = results.trans_date;
            if (trans_date != "--" && trans_date) {
                trans_date = tools.FormatDateText(trans_date.substring(4, 8));
            }
            //确认日期
            var ack_date = results.ack_date;
            if (ack_date != "--" && ack_date) {
                ack_date = tools.FormatDateText(ack_date.substring(4, 8));
            }
            //赎回到账日期
            var to_account_date = results.to_account_date;
            if (to_account_date != "--" && to_account_date) {
                to_account_date = tools.FormatDateText(to_account_date.substring(4, 8));

                // var comparator_time = results.to_account_date+ "150000";
                // comparator_time = tools.ftime(comparator_time);
                // comparator_time = new Date(comparator_time);
                // comparator_time = comparator_time.getTime();

                // var now_time = new Date();
                // now_time = now_time.getTime();
                // if(comparator_time<now_time){
                // 	//显示三个成功状态
                // 	$(item0).find(".name").removeClass("undone");
                // 	$(item1).find(".name").removeClass("undone");
                // 	$(item2).find(".name").removeClass("undone");
                // }
            }
            //显示三个成功状态
            $(item0).find(".name").removeClass("undone");
            $(item1).find(".name").removeClass("undone");
            $(item2).find(".name").removeClass("undone");


            var sell_info = "--";
            var sub_busi_code = results.sub_busi_code;
            //12402  赎回到卡    12404到宝
            if (sub_busi_code == "12402" || sub_busi_code == "12408") {
                sell_info = "银行卡";
            } else if (sub_busi_code == "12404" || sub_busi_code == "12406") {
                sell_info = "晋金宝";
            }
            //备注 12406 到期自动赎回到宝 12408 到期自动赎回到卡
            if (sub_busi_code == "12406" || sub_busi_code == "12408" ) {
                $(_pageId + " #remark").html(remarkObj[sub_busi_code]);
                $(_pageId + " .remark_box").show();
            } else {
                $(_pageId + " .remark_box").hide();
            }
            //交易流水号
            var trans_serno = results.trans_serno;
            //确认份额
            var ack_vol = tools.fmoney(results.ack_vol);
            ack_vol = ack_vol + "份";
            //确认金额
            var ack_amt = tools.fmoney(results.ack_amt);
            ack_amt = ack_amt + "元";
            //确认净值
            var ack_nav = tools.fmoney(results.ack_nav, 4);
            ack_nav = ack_nav + "元";
            //手续费
            var feet_amt = tools.fmoney(results.feet_amt);
            feet_amt = feet_amt + "元";
            //交易时间
            var trans_time = results.trans_time;
            trans_time = tools.ftime(trans_time);


            $(_pageId + " .trans_vol").html(trans_vol);
            $(_pageId + " #trans_status").html(trans_status_name);
            $(_pageId + " #prod_name").html(prod_name);
            $(_pageId + " #prod_code").html(prod_code);
            $(_pageId + " #trans_date").html(trans_date);
            $(_pageId + " #ack_date").html(ack_date);
            $(_pageId + " #to_account_date").html(to_account_date);
            $(_pageId + " .sell_info").html(sell_info);
            $(_pageId + " #trans_serno").html(trans_serno);
            $(_pageId + " #ack_vol").html(ack_vol);
            $(_pageId + " #ack_amt").html(ack_amt);
            $(_pageId + " #ack_nav").html(ack_nav);
            $(_pageId + " #feet_amt").html(feet_amt);
            $(_pageId + " #trans_time").html(trans_time);
           
        });
    }

    function pageBack() {
        appUtils.pageBack();
    }


    function destroy() {
        $(_pageId + " .trans_vol").html("--");
        $(_pageId + " #trans_status").html("--");
        $(_pageId + " #prod_name").html("--");
        $(_pageId + " #prod_code").html("--");
        $(_pageId + " #trans_date").html("--");
        $(_pageId + " #ack_date").html("--");
        $(_pageId + " #to_account_date").html("--");
        $(_pageId + " .sell_info").html("--");
        $(_pageId + " #trans_serno").html("--");
        $(_pageId + " #ack_vol").html("--");
        $(_pageId + " #ack_amt").html("--");
        $(_pageId + " #ack_nav").html("--");
        $(_pageId + " #feet_amt").html("--");
        $(_pageId + " #trans_time").html("--");
        $(_pageId + " .remark_box").hide();
    }
    


    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
