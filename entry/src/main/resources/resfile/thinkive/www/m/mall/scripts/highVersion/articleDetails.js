// 产品解读
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        tools = require("../common/tools"),
        gconfig = require("gconfig"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        service = require("mobileService"),
        _pageId = "#highVersion_articleDetails ",
        _page_code = "highVersion/articleDetails";
    var global = gconfig.global;
    var invest_teach_id, title;
    var startTime, timer = null;
    var info;
    var vipBenefitsTaskData; // 新手/月底投教活动数据
    function init() {
        vipBenefitsTaskData = appUtils.getPageParam();
        console.log(vipBenefitsTaskData)
        //获取文章详情
        info = appUtils.getPageParam();      
      
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        invest_teach_id = info.invest_teach_id;
        info.essay_id = info.invest_teach_id ? info.invest_teach_id : info.essay_id;
        sessionStorage.invest_teach_id = invest_teach_id;
        tools.initPagePointData({
            essayId: info.essay_id, // 文章id埋点
            catalogId: 'catalog_id' in info ? info.catalog_id : '' // 栏目id埋点
        });
        getMessageDetails(info.essay_id);
        if (vipBenefitsTaskData && vipBenefitsTaskData.activity_id) {
            var readingTime = vipBenefitsTaskData.duration && parseFloat(vipBenefitsTaskData.duration) * 1000;
            startTime = Date.now();
            if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3')) {
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, readingTime)
                }
            }
        }
        // console.log(invest_teach_id)
        // if(info.invest_teach_id) ;
        getPageShareStatus();
    }
    function getMessageDetails(article_id){
        service.reqFun181011({article_id:article_id}, (datas) => {
            if (datas.error_no != "0") return layerUtils.iAlert(datas.error_info);
            let res = datas.results[0];
            if(!res) return;
            $(_pageId + " .liveBroadcast_newsDetatil_title").text(res.title ? res.title : '');
            $(_pageId + " .liveBroadcast_newsDetatil_name").text(res.catalog_name ? res.catalog_name : '');
            $(_pageId + " .liveBroadcast_newsDetatil_time").text(res.upd_date ? tools.formatDateString(res.upd_date) : tools.formatDateString(res.crt_date));
            $(_pageId + " .list").html(res.html_content);
            
        })
    }
    //获取分享状态
    function getPageShareStatus() {
        let data = {
            busi_id: invest_teach_id ? invest_teach_id : info.essay_id,
            invest_teach_id: invest_teach_id ? invest_teach_id : '',
            page_type: '6',
            pageId: _pageId,
            pageCode: _page_code
        }
        if(vipBenefitsTaskData && vipBenefitsTaskData.activity_id) data.activity_id = vipBenefitsTaskData.activity_id;
        tools.isShowShare(data, '1', vipBenefitsTaskData, startTime)
    }
    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
    }
    function destroy() {
        $(_pageId + " #share").hide();
        //进页面首页清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        startTime = '';
        vipBenefitsTaskData = null;
        $(_pageId + " #share").removeAttr("has-share");
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .list").html('');
        $(_pageId + " .liveBroadcast_newsDetatil_name").html('');
        $(_pageId + " .liveBroadcast_newsDetatil_time").html('');
        $(_pageId + " .liveBroadcast_newsDetatil_title").text('');
    }
    //安卓物理返回键会调用该方法
    function pageBack() {
        // appUtils.pageBack();
        //清除定时器
        if (timer) {
            clearTimeout(timer);
            timer = null;
        }
        // 同行好友投教活动退出机制，判断浏览时长
        if (vipBenefitsTaskData && vipBenefitsTaskData.task_id && (vipBenefitsTaskData.task_type == '1' || vipBenefitsTaskData.task_type == '3') && !tools.getStayTime(startTime, vipBenefitsTaskData.duration)) {
            var remainTime = tools.getRemainTime(startTime, vipBenefitsTaskData.duration);
            layerUtils.iConfirm("您浏览的太快了，请再看看", function () {
                startTime = Date.now();
                vipBenefitsTaskData.duration = remainTime / 1000;
                getPageShareStatus();
                if (!timer || timer == null) {
                    timer = setTimeout(() => {
                        if (vipBenefitsTaskData.task_type == '1') { // 仅阅读
                            tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                        } else {
                            // 判断是否已经分享，已经分享的则完成任务
                            if ($(_pageId + " #share").attr("has-share") && vipBenefitsTaskData.is_help != '1') {
                                tools.vipTjActivityIn({ activity_id: vipBenefitsTaskData.activity_id, task_id: vipBenefitsTaskData.task_id });
                            }
                        }
                    }, remainTime)
                }
            }, function () {
                appUtils.pageInit(_page_code, "vipBenefits/index", {})
            }, "继续浏览", "返回");
            return;
        } else {
            appUtils.pageBack();
        }
    }

    var highVersion_articleDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = highVersion_articleDetails;
});
