// 业绩表现
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        cfdUtils = require("cfdUtils"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        tools = require("../common/tools"),
        common = require("common"),
        vIscroll = {
            "scroll": null,
            "_init": false
        },
        _pageId = "#inclusive_performanceList";
    var ut = require("../common/userUtil");
    var _pageCode = "inclusive/performanceList";
    var _fund_code = "";
    var productInfo;
    var isShowRank, isShowAnnualized,series_info,pageInfo,buttonFlag;
    var is_special_pro; //是否是投顾产品

    function init() {
        pageInfo = appUtils.getPageParam()?appUtils.getPageParam():{};
        buttonFlag = pageInfo.button
        _fund_code = appUtils.getSStorageInfo("fund_code");
        //页面埋点初始化
        tools.initPagePointData({fundCode:_fund_code});
        is_special_pro = appUtils.getSStorageInfo("is_special_pro") ? appUtils.getSStorageInfo("is_special_pro") : '0';//判断当前产品是否为投顾产品特殊
        productInfo = appUtils.getSStorageInfo("productInfo");
        series_info = appUtils.getSStorageInfo("series_info")?appUtils.getSStorageInfo("series_info"):{};
        isShowRank = appUtils.getSStorageInfo("_isShowBank")
        isShowAnnualized = appUtils.getSStorageInfo("_isShowAnnualized");   //是否展示年化收益率
        if (productInfo && productInfo.prod_source == '2') {
            getCombPerformance();
            // tools.initCombFundBtn(productInfo, _pageId);
            //是否为投顾系列产品
            let isSeriesComb = appUtils.getSStorageInfo("isSeriesComb");
            if(isSeriesComb != '1') tools.initCombFundBtn(productInfo, _pageId);
        } else {
            //获取业绩表现
            getPerformance();
            if (productInfo && productInfo.prod_sub_type2 == "200" && buttonFlag != '2') {
                tools.initFundBtn(productInfo, _pageId);
            }
        }
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //获取投顾业绩表现
    function getCombPerformance() {
        var prams = {
            comb_code: productInfo.comb_code,
        }
        service.reqFun102170(prams, function (data) {
            if (data.error_no == 0) {
                if (data.results.length == 0) {
                    $(_pageId + " #performanceContent .list_content").html('<div class="nodata">暂无数据</div>');
                    return;
                }
                var results = data.results[0];
                var numData = [];
                var dataArr
                if(is_special_pro == '1'){
                    //投顾
                    dataArr = ["近一月", "近三月", "近半年", "近一年", "近两年", "近三年", "上线以来"];
                }else{
                    dataArr = ["近一月", "近三月", "近半年", "近一年", "近两年", "近三年", "成立以来"];
                }
                //列表数据重构
                var numData = [
                    {
                        //月涨跌幅
                        riseRange:results.month,
                        //月年化收益
                        annualizedIncome:results.month_annualized
                    },
                    {
                        //三月涨跌幅
                        riseRange:results.season,
                        //月年化收益
                        annualizedIncome:results.season_annualized
                    },
                    {
                        //半年涨跌幅
                        riseRange:results.six_month,
                        //半年年化收益
                        annualizedIncome:results.six_month_annualized
                    },
                    {
                        //一年涨跌幅
                        riseRange:results.year,
                        //一年年化收益
                        annualizedIncome:results.year_annualized
                    },
                    {
                        //两年涨跌幅
                        riseRange:results.two_year,
                        //两年年化收益
                        annualizedIncome:results.two_year_annualized
                    },
                    {
                        //三年涨跌幅
                        riseRange:results.three_year,
                        //三年年化收益
                        annualizedIncome:results.three_year_annualized
                    },
                    {
                        //区分上线成立

                        //涨跌幅
                        riseRange:is_special_pro == '1' ? results.online : results.found,
                        //年年化收益
                        annualizedIncome:is_special_pro == '1' ? results.online_annualized : results.found_annualized
                    }
                ];
                // numData.push(results.month);
                // numData.push(results.season);
                // numData.push(results.six_month);
                // numData.push(results.year);
                // numData.push(results.two_year);
                // numData.push(results.three_year);
                // if(is_special_pro != '1') numData.push(results.five_year);
                // if(is_special_pro == '1'){
                //     numData.push(results.online);
                // }else{
                //     numData.push(results.found);
                // }
                
                var html = "";
                if (isShowAnnualized) {
                    $(_pageId + " #annualized").show()
                } else {
                    $(_pageId + " #annualized").hide()
                }
                for (var i = 0; i < numData.length; i++) {
                    //涨跌幅据处理
                    numData[i].riseRange = FormatNull(numData[i].riseRange);    //空数据
                    var rateClass = "";

                    if (!numData[i].riseRange) {
                        break;
                    }
                    if (numData[i].riseRange < 0) {
                        rateClass = "text_green";
                    } else if (numData[i].riseRange > 0 || numData[i].riseRange == '--') {
                        rateClass = "text_red";
                    } else {
                        rateClass = "text_gray"
                    }
                    // if (numData[i].riseRange < 0) {
                    //     rateClass = "minus";
                    // } else {
                    //     rateClass = "add";
                    // }

                    if (numData[i].riseRange != "--") {
                        numData[i].riseRange = (+numData[i].riseRange).toFixed(2) + "%";
                    }
                    //年化收益率处理
                    numData[i].annualizedIncome = FormatNull(numData[i].annualizedIncome);    //空数据
                    var annualizedIncomeClass = "";
                    let annualizedIncome = numData[i].annualizedIncome
                    if (!annualizedIncome) {
                        break;
                    }
                    if (annualizedIncome < 0) {
                        annualizedIncomeClass = "text_green";
                    } else if (annualizedIncome > 0 || annualizedIncome == '--') {
                        annualizedIncomeClass = "text_red";
                    } else {
                        annualizedIncomeClass = "text_gray"
                    }
                    // if (annualizedIncome < 0) {
                    //     annualizedIncomeClass = "minus";
                    // } else {
                    //     annualizedIncomeClass = "add";
                    // }

                    if (annualizedIncome != "--") {
                        annualizedIncome = (+annualizedIncome).toFixed(2) + "%";
                    }
                    //模板渲染
                    html += '<div class="item">' +
                        '<span>' + dataArr[i] + '</span>' +
                        '<span class=' + rateClass + '>' + numData[i].riseRange + '</span>' +
                        `${isShowAnnualized ? `<span id="m_width_50" class='${annualizedIncomeClass}'>${annualizedIncome}</span>` : ''}` +
                        '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //获取业绩表现
    function getPerformance() {
        var prams = {
            fund_code: _fund_code,
        }
        service.reqFun102007(prams, function (data) {
            if (data.error_no == 0) {
                if (data.results.length == 0) {
                    $(_pageId + " #performanceContent .list_content").html('<div class="nodata">暂无数据</div>');
                    return;
                }

                var results = data.results[0];
                var dateName = {
                    "week": "近一周",
                    "month": "近一月",
                    "season": "近三月",
                    "six_month": "近半年",
                    "year": "近一年",
                    "two_year": "近两年",
                    "three_year": "近三年",
                    "this_year": "今年以来",
                    "found": "成立以来",
                }
                var numData = [];
                if (_fund_code == '006627') {
                    var dataArr = ["近一月", "近三月", "近半年", "近一年", "近两年", "近三年", "今年以来", "成立以来"];
                } else {
                    var dataArr = ["近一周", "近一月", "近三月", "近半年", "近一年", "近两年", "近三年", "今年以来", "成立以来"];
                    numData.push(results.week);
                }
                numData.push(results.month);
                numData.push(results.season);
                numData.push(results.six_month);
                numData.push(results.year);
                numData.push(results.two_year);
                numData.push(results.three_year);
                numData.push(results.this_year);
                numData.push(results.found);
                var html = "";
                if (isShowRank) {
                    $(_pageId + " #rank").show()
                } else {
                    $(_pageId + " #rank").hide()
                }
                if (isShowAnnualized) {
                    $(_pageId + " #annualized").show()
                } else {
                    $(_pageId + " #annualized").hide()
                }
                for (var i = 0; i < numData.length; i++) {
                    //空数据处理
                    numData[i] = FormatNull(numData[i]);
                    var rateClass = "", simiRateClass = "";

                    if (!numData[i]) {
                        break;
                    }

                    if (numData[i].rate < 0) {
                        rateClass = "minus";
                    } else {
                        rateClass = "add";
                    }

                    if (numData[i].simi_rate < 0) {
                        simiRateClass = "minus";
                    } else {
                        simiRateClass = "add";
                    }

                    if (numData[i].rate != "--") {
                        numData[i].rate = numData[i].rate;
                        numData[i].rate = (+numData[i].rate).toFixed(2) + "%";
                    }

                    if (numData[i].simi_rate != "--") {
                        numData[i].simi_rate = numData[i].simi_rate;
                        numData[i].simi_rate = (+numData[i].simi_rate).toFixed(2) + "%";
                    }

                    html += '<div class="item">' +
                        '<span>' + dataArr[i] + '</span>' +
                        '<span class=' + rateClass + '>' + numData[i].rate + '</span>' +
                        `${isShowAnnualized ? `<span class=${simiRateClass}>${numData[i].simi_rate}</span>` : ''}` +
                        `${isShowRank ? `<span class="m_paddingRight_10"><div class="m_text_right">${numData[i].simi_ranking}</div><div class="m_text_right">${numData[i].rank_desc}</div></span>` : ''}` +
                        '</div>';
                }
                $(_pageId + " #performanceContent .list_content").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " #rank").hide();
        $(_pageId + " #annualized").hide();
        $(_pageId + " .thfundBtn").hide();
    }

    function FormatNull(param) {
        if (!param) {
            return "--";
        } else if (param == "-") {
            return "--";
        } else if (Array.isArray(param)) {
            for (var i = 0; i < param.length; i++) {
                if (!param[i] || param[i] == "-") {
                    param[i] = "--";
                }
            }
        } else if (param instanceof Object) {
            for (var key in param) {
                if (!param[key] || param[key] == "-") {
                    param[key] = "--";
                }
            }
        }
        return param;
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
