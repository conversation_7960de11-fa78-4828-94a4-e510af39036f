define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#thfund_gatherBuy ";
    var gconfig = require("gconfig");
    var ut = require("../common/userUtil");
    var _pageCode = "thfund/gatherBuy";
    var validatorUtil = require("validatorUtil");
    var tools = require("../common/tools");
    var get_pdf_file = require("../common/StrongHintPdf")
    var fund_code;
    var userInfo;
    var monkeywords = require("../common/moneykeywords");
    var threshold_amount;
    var jymm;
    var addition_amt;//追加金额
    var step_amt; // 递增金额
    var _available_vol;
    var product_risk_level;
    var buyflag; //风险等级是否匹配  1 不匹配
    var buy_state; //1 购买、2 预约
    var isFirstPurchase;// 是否首次购买产品
    var _first_max_amt;  //单笔最高限额
    var productInfo;
    function init() {
        userInfo = ut.getUserInf();
        fund_code = appUtils.getSStorageInfo("fund_code");
        //可用份额
        reqFun101901();
        //获取时间
        //reqFun102008();
        //查询该产品是否首次购买
        //        reqFun102072();
        reqFun102103();
        $(_pageId + " #inputspanid span").addClass("unable");//默认输入框失去焦点
        common.systemKeybord(); // 解禁系统键盘
        //PDF相关，走公共方法
        is_show_paf()
    }
    function is_show_paf() {
        get_pdf_file.get_file(fund_code, _pageId)
    }
    function bindPageEvent() {
        //晋金所晋金宝转入
        appUtils.bindEvent($(_pageId + " .jjs_into"), function () {
            // appUtils.pageInit(_pageCode, "jjsfund/toJJCF");
            tools.intercommunication(_pageCode);
        });
        appUtils.bindEvent($(_pageId + " .icon_gray"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
        //进入产品详情页
        appUtils.bindEvent($(_pageId + " .product_content"), function () {
            appUtils.pageInit(_pageCode, "thfund/gatherDetail");
        });
        //输入金额弹出数字键盘
        appUtils.bindEvent($(_pageId + " #inputspanid"), function (event) {
            event.stopPropagation();
            $(_pageId + " #czje").val('');
            //键盘事件
            moneyboardEvent();
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_gatherBuy";
            param["eleId"] = "czje";
            param["doneLable"] = "确定";
            param["keyboardType"] = "3";
            require("external").callMessage(param);
        });
        //打开协议弹框
        appUtils.bindEvent($(_pageId + " #agreement_btn"), function () {
            guanbi();
            $(_pageId + " #agreementList").show();
            $(_pageId + " .pop_layer").show();
        });
        //关闭协议弹框
        appUtils.bindEvent($(_pageId + " #agree_closeBtn"), function () {
            $(_pageId + " #agreementList").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭交易密码输入框
        appUtils.bindEvent($(_pageId + " #close"), function () {
            guanbi();
            $(_pageId + " .password_box").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //关闭支付弹框
        appUtils.bindEvent($(_pageId + " #close_payMethod"), function () {
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .pop_layer").hide();
        });
        //点击交易规则
        appUtils.bindEvent($(_pageId + " .trade_rule .rule"), function () {
            appUtils.pageInit(_pageCode, "thfund/gatherDetailRule");
        });
        //关闭数字键盘
        appUtils.bindEvent($(_pageId), function () {
            monkeywords.close();
        });
        // 打开pdf协议
        appUtils.bindEvent($(_pageId + " .xy"), function () {
            var url = $(this).attr("url");
            let title = '内容查看'
            let statusColor = '#2F4D80'
            let titleColor = '#ffffff'
            var param = {};
            param["funcNo"] = "50240";
            param["title"] = title
            param["url"] = url;
            param["statusColor"] = statusColor;
            param["titleColor"] = titleColor;
            require("external").callMessage(param);
        });
        //风险承受能力确认
        appUtils.bindEvent($(_pageId + " .agreement1"), function () {
            if ($(this).find("i").hasClass("active")) {
                $(this).find("i").removeClass("active")
            } else {
                $(this).find("i").addClass("active")
            }
        });
        //跳转晋金宝充值页面
        appUtils.bindEvent($(_pageId + " .recharge"), function () {
            appUtils.pageInit(_pageCode, "thfund/inputRechargePwd");
        });
        //显示购买弹框
        appUtils.bindEvent($(_pageId + " .buy"), function () {
            if ((userInfo.riskLevel == '00' || userInfo.riskLevel == 'C0') && (productInfo.risk_level != 'R1')) return tools.is_show_c0(_pageCode)
            var moneys = $(_pageId + " #czje").val().replace(/,/g, "");
            var isverify = $(_pageId + " .agreement").attr("isverify");
            // if (!$(_pageId + " .agreement1 i").hasClass("active") && isverify == "true") {
            //     layerUtils.iAlert("请勾选风险说明");
            //     return;
            // }
            if (!$(_pageId + " .agreement2 i").hasClass("active")) {
                layerUtils.iAlert("请阅读协议并同意签署");
                return;
            }
            if (moneys <= 0 || !moneys) {
                layerUtils.iAlert("请输入买入金额");
                return;
            }
            if (threshold_amount && parseFloat(moneys) < parseFloat(threshold_amount) && (productInfo.prod_sub_type2 == "25" || isFirstPurchase)) { // 首次购买 || 日鑫产品 需大于起购金额
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            } else if (addition_amt && parseFloat(moneys) < parseFloat(addition_amt) && !isFirstPurchase) {
                layerUtils.iAlert("购买金额不能低于起购金额");
                return;
            }
            if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) return //是否满足递增
            if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) return //是否满足递增
            if (_first_max_amt && moneys > parseFloat(_first_max_amt)) {
                layerUtils.iAlert("超过单笔最高限额");
                return;
            }
            if (buyflag == "1") {
                layerUtils.iConfirm("<span style='text-align: left;padding:0;display:inline-block'>您的风险承受能力为" + userInfo.riskName + "，此产品超过了您的风险承受能力，若仍然选择投资，则表明在上述情况下，您仍自愿投资该产品，并愿意承担可能由此产生的风险</span>", function () {
                    appUtils.pageInit(_pageCode, "safety/riskQuestion");
                }, function funcNo() {
                    $(_pageId + " .pop_layer").show();
                    $(_pageId + " #payMethod").show();
                    //是否可以购买
                    isCanBuy();
                }, "重新测评", "继续购买");
                return;
            }
            $(_pageId + " .pop_layer").show();
            $(_pageId + " #payMethod").show();
            //是否可以购买
            isCanBuy();
        });

        //切换购买状态
        //        appUtils.bindEvent($(_pageId + " .pay_method"), function () {
        //            var buy_money = $(_pageId + " #czje").val().replace(/,/g, "");
        //            if (totalMoney < buy_money) {
        //                $(_pageId + " .pay_method").removeClass("active").filter(".bank_pay").addClass("active");
        //                return;
        //            }
        //            $(_pageId + " .pay_method").removeClass("active").filter(this).addClass("active");
        //        });

        //显示交易密码
        appUtils.bindEvent($(_pageId + " .model_bottom"), function () {
            if ($(this).hasClass("noactive")) {
                return;
            }
            $(_pageId + " #payMethod").hide();
            $(_pageId + " .password_box").show();
            //输入交易密码时的提示
            setRechargeInfo();
            passboardEvent();
            monkeywords.flag = 0;
            //键盘事件
            var param = {};
            param["moduleName"] = "mall";
            param["funcNo"] = "50210";
            param["pageId"] = "thfund_gatherBuy";
            param["eleId"] = "jymm";
            param["doneLable"] = "确定";
            param["keyboardType"] = "4";
            require("external").callMessage(param);
        });
        //调用支付接口
        appUtils.bindEvent($(_pageId + " #queDing"), function () {
            $(_pageId + " .pop_layer").hide();
            $(_pageId + " .password_box").hide();
            var jymm1 = $(_pageId + " #jymm").val();
            if (jymm1.length != 6) {
                layerUtils.iAlert("请确定您的交易密码格式正确");
                return;
            }
            guanbi();
            //进行充值
            var trans_amt = $(_pageId + " #czje").val();
            trans_amt = trans_amt.replace(/,/g, "");
            var param = {
                app_amt: trans_amt, //交易金额
                trans_pwd: jymm1, //交易密码
                fund_code: $(_pageId + " .fund_code").html(),
                buyflag: buyflag,
                agreement_sign_no: $(_pageId + " .agreement2").attr("agreement_sign_no"),//协议签署流水号
                period: productInfo.period
            };
            //交易密码加密
            service.getRSAKey({}, function (data) {
                if (data.error_no != "0") {
                    layerUtils.iLoading(false);
                    return;
                }
                var modulus = data.results[0].modulus;
                var publicExponent = data.results[0].publicExponent;
                var endecryptUtils = require("endecryptUtils");
                param.trans_pwd = endecryptUtils.rsaEncrypt(modulus, publicExponent, param.trans_pwd);
                service.reqFun106014(param, function (resultVo) {
                    if (resultVo.error_no == "0") {
                        if (buy_state == "1") { //购买
                            purchase(param);
                        } else if (buy_state == "2") { // 预约
                            apponit(param);
                        }
                    } else {
                        layerUtils.iLoading(false);
                        layerUtils.iAlert(resultVo.error_info);
                    }
                }, { isLastReq: false });
            }, { isLastReq: false });
        });
    }

    // 产品详情查询
    function getFundInfo() {
        var params = {
            fund_code: fund_code
        }
        service.reqFun102028(params, function (data) {
            if (data.error_no == 0) {
                var result = data.results[0];
                $(_pageId + " .prod_sname").html(result.prod_sname);//产品简称
                $(_pageId + " .header_inner h1").text(result.prod_sname);
                $(_pageId + " .fund_code").html(result.fund_code);//产品编码
                $(_pageId + " .risk_level_name").html(result.risk_level_name);//风险
                product_risk_level = result.risk_level;
                $(_pageId + " .fund_type_name").html(result.fund_type_name);//产品类型
                threshold_amount = result.threshold_amount;//起购金额
                addition_amt = result.addition_amt;//追加金额
                step_amt = result.step_amt;//递增金额
                var redconfirm_days_text_obj = {
                    "0": "自动滚入下一期",
                    "1": "自动赎回到银行卡",
                    "2": "自动赎回到晋金宝",
                }
                $(_pageId + " .back_way_default").html(redconfirm_days_text_obj[result.back_way_default]);//默认到期方式
                compareRiskLevel();
                var str = "";
                if (threshold_amount && isFirstPurchase) {
                    str += (threshold_amount >= 10000 ? (threshold_amount / 10000 + "万") : tools.fmoney(threshold_amount)) + '元起购';
                } else if (addition_amt && !isFirstPurchase) {
                    str += (addition_amt >= 10000 ? (addition_amt / 10000 + "万") : tools.fmoney(addition_amt)) + '元起购';
                }

                if (step_amt && step_amt > 0) {
                    str += "，" + (step_amt >= 10000 ? (step_amt / 10000 + '万') :
                        tools.fmoney(step_amt + ''))
                        + "元递增"
                }
                if (str) {
                    $(_pageId + " #inputspanid span").text(str);
                    $(_pageId + " #inputspanid span").attr("text", str);
                } else {
                    $(_pageId + " #inputspanid span").text("请输入购买金额");
                    $(_pageId + " #inputspanid span").attr("text", "请输入购买金额");
                }
                //单笔最高限额
                _first_max_amt = result.first_max_amt;
                buy_state = result.buy_state;
                productInfo = result;
                tools.getPdf("prod", fund_code, buy_state); //获取协议
                appUtils.setSStorageInfo("productInfo", result);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    //可用份额
    function reqFun101901() {
        service.reqFun101901({}, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            var results = data.results[0];

            //可用份额
            _available_vol = results.available_vol;
            var html = '可用金额：<em class="money">' + tools.fmoney(_available_vol + "") + '</em>元';
            $(_pageId + " .pay_bank").html(html);
        })
    }

    function guanbi() {
        $(_pageId + " #jymm").val("");
        jymm = "";
        for (var i = 0; i < 6; i++) {
            var idspan = "#span0" + i
            $(_pageId + " " + idspan).removeAttr("class", "point");
        }
        var param = {};
        param["funcNo"] = "50211";
        require("external").callMessage(param);
    }

    //现金宝认购、申购产品
    function purchase(param) {
        service.reqFun106003(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iLoading(false);
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "thfund/gatherBuyResult", data.results[0]);
        })
    }

    // 现金宝预约产品
    function apponit(param) {
        service.reqFun106008(param, function (data) {
            if (data.error_no != 0) {
                layerUtils.iAlert(data.error_info);
                return;
            }
            appUtils.pageInit(_pageCode, "thfund/gatherBuyResult", data.results[0]);
        })
    }

    //金额键盘事件
    function moneyboardEvent() {
        monkeywords.open({
            _pageId: _pageId,
            domid: $(_pageId + " #czje"),
            endcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                var moneys = curVal.replace(/,/g, "");
                if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) return //是否满足递增
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) return //是否满足递增
                if (moneys) {
                    moneys = tools.fmoney(moneys);
                }
                $(_pageId + " #czje").val(moneys);
            },
            inputcallback: function () {
                var curVal = $(_pageId + " #czje").val();
                curVal = curVal.replace(/,/g, "");
                if (!(/^\d{0,8}(\.\d{0,2})?$/.test(curVal))) {
                    $(_pageId + " #czje").val(curVal.substring(0, curVal.length - 1));
                }
                if (parseFloat(curVal) > parseFloat(productInfo.prod_per_max_amt)) {
                    $(_pageId + " #czje").val(productInfo.prod_per_max_amt);
                }
            },
            keyBoardHide: function () {
                var curVal = $(_pageId + " #czje").val();
                if (!curVal) return
                var moneys = curVal.replace(/,/g, "");
                if (moneys) {
                    $(_pageId + " #czje").val(tools.fmoney(moneys).replace(/,/g, ""));
                    $(_pageId + " #inputspanid span").html(tools.fmoney(moneys));
                }
                if (isFirstPurchase && tools.isMatchAddAmt(moneys, threshold_amount, step_amt)) return //是否满足递增
                if (!isFirstPurchase && tools.isMatchAddAmt(moneys, addition_amt, step_amt)) return //是否满足递增
            }
        })
    }

    //键盘事件
    function passboardEvent() {
        window.customKeyboardEvent = {
            keyBoardFinishFunction: function () {
            }, // 键盘完成按钮的事件
            keyBoardInputFunction: function () {
                var jymm = $(_pageId + " #jymm").val();
                var len = jymm.length;
                if (len <= 6) {
                    for (var i = 0; i < 6; i++) {
                        var idspan = "#span0" + i
                        if (i < len) {
                            $(_pageId + " " + idspan).attr("class", "point");
                            continue;
                        }

                        $(_pageId + " " + idspan).removeAttr("class", "point");
                    }
                } else {
                    jymm = jymm.substring(0, 6);
                    $(_pageId + " #jymm").val(jymm);
                }
            } // 键盘的输入事件
        };
    }

    //是否可以购买
    function isCanBuy() {
        //查询是否晋金所转入白名单用户
        tools.whiteList(_pageId);
        var trans_amt = $(_pageId + " #czje").val();
        trans_amt = trans_amt.replace(/,/g, "");
        trans_amt = (+trans_amt);
        _available_vol = (+_available_vol);
        if (trans_amt <= _available_vol) {
            $(_pageId + " #insufficient_fund").hide();
            $(_pageId + " #amount_enough").show();
            $(_pageId + " .model_bottom").removeClass("noactive");
        } else {
            $(_pageId + " #insufficient_fund").show();
            $(_pageId + " #amount_enough").hide();
            $(_pageId + " .model_bottom").addClass("noactive");
        }
    }

    //查询产品是否首次购买
    function reqFun102103() {
        var param = {
            fund_code: fund_code,
        }
        service.reqFun102103(param, function (data) {
            if (data.error_no == 0) {
                var results = data.results[0];
                if (results.is_can_add == 0) {
                    isFirstPurchase = false;
                } else {
                    isFirstPurchase = true;
                }
            } else {
                isFirstPurchase = true;
                layerUtils.iAlert(data.error_info);
            }
            //获取产品详情
            getFundInfo();
        }, { isLastReq: false })
    }

    //比较风险等级
    function compareRiskLevel() {
        var userRiskLevel = userInfo.riskLevel;
        userRiskLevel = +(userRiskLevel.substr(-1));
        product_risk_level = +(product_risk_level.substr(-1));
        if (product_risk_level == 1) return;
        if (product_risk_level > userRiskLevel) {
            $(_pageId + " .agreement1").show();
            $(_pageId + " .agreement").attr("isVerify", "true");
            buyflag = "1";
        } else {
            $(_pageId + " .agreement1").hide();
            $(_pageId + " .agreement").attr("isVerify", "false");
        }
    }

    //输入交易密码时的提示
    function setRechargeInfo() {
        //产品简称
        var prod_sname = $(_pageId + " .prod_sname").html();
        $(_pageId + " #recharge_name").html(prod_sname);
        $(_pageId + " #recharge_money").html($(_pageId + " #czje").val());
    }

    function destroy() {
        get_pdf_file.clearTime()
        guanbi();
        $(_pageId + " .prod_sname").html("--");//产品全称
        $(_pageId + " .fund_code").html("--");//产品编码
        $(_pageId + " .risk_level_name").html("--");//产品编码
        $(_pageId + " .fund_type_name").html("--");//产品编码
        $(_pageId + " #czje").val("");
        $(_pageId + " #jymm").val("");
        $(_pageId + " .pop_layer").hide();
        $(_pageId + " .action_sheet_wrapper").hide();
        $(_pageId + " #inputspanid span").text("").css({ color: "rgb(153, 153, 153)" });
        $(_pageId + " .password_box").hide();
        $(_pageId + " .van-overlay").hide();
        buyflag = "";
        buy_state = "";
        _first_max_amt;
        monkeywords.destroy();
        $(_pageId + " .jjs_yue").hide();


    }

    function pageBack() {
        appUtils.pageBack();
    }


    var jjbDetails = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjbDetails;
});
