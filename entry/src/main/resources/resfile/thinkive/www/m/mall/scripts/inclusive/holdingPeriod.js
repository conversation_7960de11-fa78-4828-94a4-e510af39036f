//持有期收益率
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        service = require("mobileService"),
        common = require("common"),
        _pageId = "#inclusive_holdingPeriod ";
    var tools = require("../common/tools");
    var _pageCode = "inclusive/holdingPeriod";
    var ut = require("../common/userUtil");
    var _fund_code = "";
    var dividend_status;
    var sold_state = "";
    var completed_flag;
    var is_lock_time;
    var params;
    let getData;
    let custno;
    let userInfo;
    function init() {
        getData = appUtils.getSStorageInfo("productInfo");
        $(_pageId + " .holding_yield_term").html(getData.holding_yield_term ? getData.holding_yield_term : '--')
        userInfo = ut.getUserInf();
        setData()
    }
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
            // appUtils.pageInit(_pageCode, "customerService/onlineCustomerService");
        });
    }

    function setData() {
        let queuest = {
            fundcode: getData.fund_code,
            custno: userInfo.custNo,
            virfundcode: getData.vir_fundcode
        }
        let holding_yield_term = getData.holding_yield_term ? getData.holding_yield_term : null
        service.reqFun120116(queuest, function (data) {
            if (data.error_no == '0') {
                if (data.results[0].updatetime) {
                    $(_pageId + " .holdingPeriod_tip .holdUpdateTime").html(data.results[0].updatetime)
                } else {
                    $(_pageId + " .holdingPeriod_tip .holdUpdateTime").html('--')
                }

                if (data.results[0].data.length > 0) {
                    let arr = data.results[0].data
                    let html = ''
                    for (let i = 0; i < arr.length; i++) {
                        let holdincome
                        if (arr[i].holdincome == '--' || !arr[i].holdincome) {
                            holdincome = '--'
                        } else {
                            holdincome = arr[i].holdincome + '%'
                        }
                        if (holding_yield_term && (arr[i].holddays * 1) < (holding_yield_term * 1)) {
                            holdincome = '--'
                        }
                        let holddays
                        if ((arr[i].holddays * 1) <= 0) {
                            holddays = '--'
                        } else {
                            holddays = arr[i].holddays + '天'
                        }

                        html += '<ul class="flex"><li class="m_width_35">' + arr[i].totalvol + '</li><li class="m_width_25">' + arr[i].registerdate + '</li><li class="m_width_20">' + holddays + '</li><li class="m_width_20">' + holdincome + '</li> </ul>'
                    }
                    $(_pageId + " .holdingList").html(html)
                } else {
                    $(_pageId + " .holdingPeriod_remark").show();
                }
            } else {
                $(_pageId + " .holdingPeriod_remark").show();
            }
        })
    }
    function destroy() {
        $(_pageId + " .holdingPeriod_remark").hide();
        $(_pageId + " .holding_yield_term").html('')
    }
    function pageBack() {
        appUtils.pageBack();
    }

    var bondFixMochikura = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = bondFixMochikura;
});
