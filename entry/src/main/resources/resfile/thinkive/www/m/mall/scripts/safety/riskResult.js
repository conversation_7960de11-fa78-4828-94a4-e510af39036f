// 风险测评结果
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        validatorUtil = require("validatorUtil"),
        service = require("mobileService"),
        serviceConstants = require("constants"),
        _pageId = "#safety_riskResult";
    var ut = require("../common/userUtil");

    function init() {
        var param = appUtils.getPageParam();
        var userInfo = ut.getUserInf();
        userInfo.riskName = param.cust_risk_desc;
        userInfo.riskLevel = param.cust_risk_level;
        userInfo.custRiskInvalidDate = param.invalid_date;
        ut.saveUserInf(userInfo);
        $(_pageId + " #rRisk").html(param.cust_risk_desc);
        //渲染适合购买的风险等级产品
        setCustRiskInvalidDate()
    }
    function setCustRiskInvalidDate(){
        let info = ut.getUserInf();
        let riskLevel = info.riskLevel ? info.riskLevel.split(0)[1] : '';
        riskLevel = riskLevel == '' ? '0' : riskLevel;
        if(riskLevel && riskLevel.length) riskLevel = tools.getRiskLevel(riskLevel);
        $(_pageId + " .riskLevel").html(riskLevel);
    }
    function bindPageEvent() {

        //确定按钮
        appUtils.bindEvent(_pageId + " #queding", function () {
            pageBack();
        });

        //点击返回
        appUtils.bindEvent(_pageId + " #getBack", function () {
            pageBack();
        });
    }

    function destroy() {
        $(_pageId + " #rRisk").html("");
    }

    function pageBack() {
        var routerList = appUtils.getSStorageInfo("routerList");
        routerList.splice(-2);
        appUtils.pageInit("account/riskResult", routerList[routerList.length - 1]);

    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = myAccount;
});
