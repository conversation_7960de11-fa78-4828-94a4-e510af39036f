// 历史净值
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = { "scroll": null, "_init": false },
        _pageId = "#inclusive_historyValueList";
    var _pageCode = "inclusive/historyValueList";
    var ut = require("../common/userUtil");
    var _fund_code;
    var productInfo;
    var isEnd = false;
    var _cur_page = 1;
    var _num_per_page = "20";

    function init() {
        vIscroll = { "scroll": null, "_init": false },
        _fund_code = appUtils.getSStorageInfo("fund_code");
        //页面埋点初始化
        tools.initPagePointData({fundCode:_fund_code});
        productInfo = appUtils.getSStorageInfo("productInfo");
        //获取历史净值
        getHistory(false)
        
        if(productInfo.prod_source == '2'){
            //是否为投顾系列产品
            let isSeriesComb = appUtils.getSStorageInfo("isSeriesComb");
            if(isSeriesComb != '1') tools.initCombFundBtn(productInfo, _pageId);
            // tools.initCombFundBtn(productInfo, _pageId);
        }else{
            tools.initFundBtn(productInfo, _pageId);
        }
    }

    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });
        appUtils.bindEvent($(_pageId + " #kefu"), function () {
            tools.saveAlbum(_pageCode)
        });
    }
    //获取历史净值
    function getHistory(isAppendFlag) {
        isEnd = false;
        $(_pageId + " .new_none").hide();
        var params = {
            fund_code: _fund_code,
            cur_page: _cur_page + "",
            num_per_page: _num_per_page + "",
        }
        var callback = function (resultVo) {
            if (resultVo.error_no == "0") {
                var results = productInfo.prod_source == '2'? resultVo.results[0].data : resultVo.results;
                var html = "";
                if (results && results.length > 0) {
                    for (var i = 0; i < results.length; i++) {
                        //空数据处理
                        results[i] = FormatNull(results[i]);
                        var end_date = results[i].end_date;
                        if (end_date != "--") {
                            end_date = FormatDate(end_date.substring(0, 8));
                        }

                        //单位净值 
                        var nav = results[i].nav;
                        if (nav != "--") {
                            nav = (+nav).toFixed(4);
                        }

                        //累计净值
                        var accunav = results[i].accunav;
                        if (accunav != "--") {
                            accunav = (+accunav).toFixed(4);
                        }

                        //日涨跌幅
                        var rateClass = "add";
                        var daily_return = results[i].daily_return;
                        if (daily_return != "--") {
                            rateClass = addMinusClass(daily_return);
                            daily_return = tools.fmoney(daily_return) + "%";
                        }


                        html += '<div class="item">' +
                            '<span>' + end_date + '</span>' +
                            '<span class="">' + nav + '</span>' +
                            '<span class="">' + accunav + '</span>' +
                            '<span class=' + rateClass + '>' + daily_return + '</span>' +
                            '</div>';
                    }
                } else if (!results) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }

                if (results && results.length < _num_per_page) {
                    isEnd = true;
                    $(_pageId + " .new_none").show();
                }
                // $(_pageId + " #historyContent .list_content").html(html);
            } else {
                layerUtils.iAlert(resultVo.error_info);
            }

            if (isAppendFlag) {
                $(_pageId + " #concent").append(html);
                $(_pageId + " .visc_pullUpIcon").hide();
                $(_pageId + " .visc_pullUpDiv").hide();
            } else {
                $(_pageId + " #concent").html(html);
            }
            pageScrollInit();
        }
        if(productInfo.prod_source == '2'){
            params.comb_code = productInfo.comb_code;
            service.reqFun102193(params, callback);
        }else{
            service.reqFun102006(params, callback);
        }
        
        
    }


    //上下滑动事件
    function pageScrollInit() {
        let height = $(_pageId + " #v_container_productList").offset().top;
        let height2;
        if(productInfo.prod_source == '2'){
            height2 = $(window).height() - height - 50 + 44;
        }else{
            height2 = $(window).height() - height - 50 + 44 - 90;
        }
        if (!vIscroll._init) {
            var config = {
                "isPagingType": false,
                "visibleHeight": height2, //这个是中间数据的高度
                "container": $(_pageId + " #v_container_productList"),
                "wrapper": $(_pageId + " #v_wrapper_productList"),
                "downHandle": function () {
                    _cur_page = 1;
                    getHistory(false);
                    $(_pageId + " .visc_pullUp").show();
                    $(_pageId + " .visc_pullUpIcon").hide();
                    $(_pageId + " .visc_pullUpDiv").hide();
                },
                "upHandle": function () {
                    if (!isEnd) {
                        _cur_page += 1;
                        $(_pageId + " .visc_pullUpIcon").show();
                        $(_pageId + " .visc_pullUpDiv").show();
                        getHistory(true);
                    } else {
                        $(_pageId + " .new_none").show();
                    }
                },
                "wrapperObj": null
            };

            vIscroll.scroll = new VIscroll(config); //初始化
            vIscroll._init = true;
        } else {
            vIscroll.scroll.refresh();
        }
        if (isEnd) {//可能有当前页为1总页数为0的情况
            $(_pageId + " .visc_pullUp").hide();
        } else {
            $(_pageId + " .visc_pullUp").show();
        }
    }

    function FormatDate(date, separator) {
        separator = separator || "-"
        var year = date.substring(0, 4);
        var month = Number(date.substring(4, 6));
        var day = Number(date.substring(6, 8));
        var resultDate = year + separator + month + separator + day;
        return resultDate;
    }

    function addMinusClass(str) {
        var numClass = "add";

        if (str < 0) {
            numClass = "minus";
        } else if (str > 0) {
            numClass = "add";
        } else {
            numClass = "text_grey";
        }
        return numClass;
    }

    function FormatNull(param) {
        if (!param) {
            return "--";
        } else if (param == "-") {
            return "--";
        } else if (Array.isArray(param)) {
            for (var i = 0; i < param.length; i++) {
                if (!param[i] || param[i] == "-") {
                    param[i] = "--";
                }
            }
        } else if (param instanceof Object) {
            for (var key in param) {
                if (!param[key] || param[key] == "-") {
                    param[key] = "--";
                }
            }
        }
        return param;
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        _cur_page = 1;
        $(_pageId + " .new_none").hide();
        $(_pageId + " .thfundBtn").hide();
        isEnd = false;
        $(_pageId + " #concent").html("");
    }

    function pageBack() {
        appUtils.pageBack();
    }

    var jjThirtyDetail = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = jjThirtyDetail;
});
