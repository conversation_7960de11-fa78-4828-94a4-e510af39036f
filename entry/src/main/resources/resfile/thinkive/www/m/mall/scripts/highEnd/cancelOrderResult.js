// 晋金高端列表页
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        layerUtils = require("layerUtils"),
        common = require("common"),
        tools = require("../common/tools"),
        service = require("mobileService"),
        VIscroll = require("vIscroll"),
        vIscroll = {"scroll": null, "_init": false},
        _page_code = "highEnd/cancelOrderResult",
        _pageId = "#highEnd_cancelOrderResult ";
    var ut = require("../common/userUtil");

    function init() {


    }

    //绑定事件
    function bindPageEvent() {
        appUtils.bindEvent($(_pageId + " .icon_back"), function () {
            pageBack();
        });

        //申请撤单
        appUtils.bindEvent($(_pageId + " #next"), function () {
            var routerList = appUtils.getSStorageInfo("routerList")
            routerList.splice(-1);
            appUtils.setSStorageInfo("routerList",routerList)
            appUtils.pageBack();
        });
    }

    function destroy() {

    }

    //安卓物理返回键会调用该方法
    function pageBack() {
        appUtils.pageBack();
    }

    var thcancelOrderResult = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy,
        "pageBack": pageBack
    };
    // 暴露对外的接口
    module.exports = thcancelOrderResult;
});
