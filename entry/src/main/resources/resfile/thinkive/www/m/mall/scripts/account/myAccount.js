// 我的
//@版本: 2.0
define(function (require, exports, module) {
    var appUtils = require("appUtils"),
        dateUtils = require("dateUtils"),
        layerUtils = require("layerUtils"),
        validatorUtil = require("validatorUtil"),
        serviceConstants = require("constants"),
        service = require("mobileService"),
        common = require("common"),
        _pageUrl = "account/myAccount",
        _pageId = "#account_myAccount";
    var gconfig = require("gconfig");
    var global = gconfig.global;
    var lcjer = "";
    var external = require("external");
    var tools = require("../common/tools");//升级
    var ut = require("../common/userUtil");
    var userInfo;
    let secondaryPageData;
    let source;//来源
    //初始化版本相关数据
    let userChooseVerson,scene_code,mobileWhole
    let openFlag; // 晋金所开户状态
    function init() {
        //获取当前用户选中的版本
        userChooseVerson = common.getLocalStorage("userChooseVerson"); //判断用户是否登陆过
        //获取当前用户初始版本
        scene_code = common.getLocalStorage("scene_code"); //页面版本类型 1标准版 X版
        mobileWhole = common.getLocalStorage("mobileWhole"); //页面版本类型 1标准版 X版
        userChooseVerson = userChooseVerson ? userChooseVerson : '';
        scene_code = scene_code ? scene_code : '';
        mobileWhole = mobileWhole ? mobileWhole : '';
        // 获取用户当前切换的版本
        // let chooseVerson = appUtils.getSStorageInfo("chooseVerson") ? appUtils.getSStorageInfo("chooseVerson") : '';
        // scene_code = '1'
        if(userChooseVerson && userChooseVerson !=''){
            if(userChooseVerson == '3'){
                $(_pageId).addClass("high");
                $(_pageId + " .other_myAccount").hide();
                $(_pageId + " .high_myAccount").show();
            }else{
                $(_pageId).removeClass("high");
                $(_pageId + " .high_myAccount").hide();
                $(_pageId + " .other_myAccount").show();
            }
        }else{
            if(scene_code == '3'){
                $(_pageId).addClass("high");
                $(_pageId + " .other_myAccount").hide();
                $(_pageId + " .high_myAccount").show();
            }else{
                $(_pageId).removeClass("high");
                $(_pageId + " .high_myAccount").hide();
                $(_pageId + " .other_myAccount").show();
            }
        }
        //页面埋点初始化
        tools.initPagePointData();
        source = '3';//主动邀请
        //新版本开发 资产查询
        getList()
        userInfo = ut.getUserInf();
        if (ut.getUserInf().custLabelCnlCode != "" && ut.getUserInf().custLabelCnlCode != "yh_jjdx") {
            appUtils.pageInit("", "yuanhui/myAccount", {});
            return;
        }
        tools.footerShow(_pageId,userChooseVerson,scene_code,mobileWhole);
        $(_pageId + " #phoneNum").html("");
        getUserIsShowCombProd();//TODO: 上线需要删除
        nameOrPhone();
        //添加头像
        testHead();
        //资产查询
        // getAssetInfo();
        celarBankInfo();// 清空银行卡信息
        // queryNum();//查询可用红包数量
        initRisk();//风险测评提示
        tools.hasNewMsg(_pageId); // 是否有新消息
        qryWhiteList(); //查询白名单信息
        id_card_info()
        hgSoonInvalidState();
    }
	//去测评
	function pageTo_evaluation() {
	    let operationId = 'riskAssessment'
	    layerUtils.iAlert("您的风险测评已到期，请重新测评", () => { }, () => {
	        appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
	    }, '', '确定',operationId)
	}
    // 合格投资人认证状态
    function hgSoonInvalidState() {
        if (!appUtils.getSStorageInfo("user")) return
        // 合格投资人认证状态  4:已过期 6:临期 其他:不提示
        var hgSoonInvalidState = appUtils.getSStorageInfo("user").hgSoonInvalidState;
        if (hgSoonInvalidState == "4") {
            $(_pageId + ' .exprie_tip .tip_mainText').text("您的合格投资者认证已到期，请尽快").show();
            $(_pageId + " .exprie_tip").css("display", 'block');
        } else if (hgSoonInvalidState == "6") {
            $(_pageId + ' .exprie_tip .tip_mainText').text("您的合格投资者认证即将到期，请尽快").show();
            $(_pageId + " .exprie_tip").css("display", 'block');
        }
    }

    // 查询用户是否展示"我的"页面投顾入口
    function getUserIsShowCombProd() {
        service.reqFun102173({}, function (data) {
            if (data.error_no == "0") {
                if (data.results[0].tg_entrance == "1") {
                    $(_pageId + " #myCombPro").show();
                } else {
                    $(_pageId + " #myCombPro").hide();
                }
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //获取我的页面列表
    function getList() {
        service.reqFun101932({}, function (data) {
            if (data.error_no == "0") {
                let res = data.results[0];
                let total_assets = res.total_assets ? common.fmoney(res.total_assets, 2) : "--";//总资产
                let arr = res.data;
                let html = '';
                for (let i = 0; i < arr.length; i++) {
                    let classify_desc = arr[i].financial_prod_type == '01' ? '晋金宝' : arr[i].classify_desc
                    let total_amt = common.fmoney(arr[i].total_amt)
                    if ((arr[i].financial_prod_type != '05') || (arr[i].financial_prod_type == '05' && (arr[i].total_amt * 1) > 0)) {
                        html += '<p><a operationType="1" operationName="'+ classify_desc +'" operationId="item_'+ arr[i].financial_prod_type +'" class="item" href="javascript:void(0)" classify_desc="' + arr[i].classify_desc + '" financial_prod_type="' + arr[i].financial_prod_type + '" clickName="' + arr[i].financial_prod_type + '" id="' + arr[i].financial_prod_type + '" >' + '<i style="font-style: normal;">' + classify_desc + '</i>' + '<span><em>' + total_amt + '</em> </span></a></p>'
                    }
                }
                html = '<p class="total_assets">总资产：<span><em>' + total_assets + '</em></span></p>' + html
                $(_pageId + " .topTitleList").html(html);
            } else {
                layerUtils.iAlert(data.error_info);
            }
        })
    }
    //身份证认证提示
    function id_card_info() {
        if (!appUtils.getSStorageInfo("user")) return
        let perfect_info = appUtils.getSStorageInfo("user").perfect_info  //0:未完善 1:已完善 2:证件到期3:到期前3个月 4:到期后3个月
        let parentHtml = $(_pageId + ' .tip') //主节点
        let tip_mainText = $(_pageId + ' .tip' + ' .tip_mainText') //文案
        switch (perfect_info) {
            case '3':
                parentHtml.css("display", 'block')
                break;
            case '2':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display", 'block')
                break;
            case '4':
                tip_mainText.text('您的身份证照片已到期，请尽快').show()
                parentHtml.css("display", 'block')
                break;
            default:
                break;
        }
    }
    //切换用户版本
    function changeUserVersion(param) {
        service.reqFun101095(param, function (data) {
            if (data.error_no == "0") {
                if (!common.loginInter()) return;
                appUtils.setSStorageInfo("plan_type",'');
                //当前用户选中的版本，不重置
                common.setLocalStorage("userChooseRefresh",'0');
                common.setLocalStorage("userChooseVerson",param.scene_code);
                common.setLocalStorage("scene_code",param.scene_code);
                appUtils.pageInit(_pageUrl, "login/userIndexs");
            } else {
                $(_pageId + " .high_msg_num").hide();
            }
        });
    }
    //绑定事件
    function bindPageEvent() {
        //跳转资产列表页面
        appUtils.preBindEvent($(_pageId + " .topTitleList"), ".item", function (e) {
            let financial_prod_type = $(this).attr('financial_prod_type')
            // tools.clickPoint(_pageId, _pageUrl, financial_prod_type)
            if (financial_prod_type == '01') {    //01跳转晋金宝
                // tools.clickPoint(_pageId, _pageUrl, 'thfund')
                appUtils.pageInit(_pageUrl, "thfund/myProfit");
            } else if (financial_prod_type == '05') {
                appUtils.pageInit(_pageUrl, "bank/bankDeposit");
            } else {
                appUtils.setSStorageInfo("financial_prod_type", financial_prod_type)
                appUtils.pageInit(_pageUrl, "template/positionList");
            }

        }, "click");
        //认证身份证
        appUtils.bindEvent($(_pageId + " .tip .uploadIDCard"), () => {
            // tools.clickPoint(_pageId, _pageUrl, 'expirationReplacementIdCard')
            appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
        })

        //合格投资人认证
        appUtils.bindEvent($(_pageId + " .exprie_tip #decertification"), () => {
            appUtils.setSStorageInfo("productInfo", { prod_sub_type2: '81' });
            appUtils.pageInit(_pageUrl, "highEnd/qualifiedInvestor1");
        })
        //消息
        appUtils.bindEvent($(_pageId + " #message_img"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'message_img')
            appUtils.pageInit(_pageUrl, "moreDetails/msg");
        });
        //个人中心
        appUtils.bindEvent($(_pageId + " .my_account"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'my_account')
            appUtils.pageInit(_pageUrl, "account/personMessage")
        });
        //晋金宝
        appUtils.bindEvent($(_pageId + " #thfund"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'thfund')
            appUtils.pageInit(_pageUrl, "thfund/myProfit");
        });
        /**新页面  开始*/

        //类固收
        appUtils.bindEvent($(_pageId + " #bankDeposit"), function () {
            let secondaryPageData = {
                title: "类固收",
                index: 1
            }
            appUtils.setSStorageInfo("secondaryPageData", secondaryPageData)
            // tools.clickPoint(_pageId, _pageUrl, 'bankDeposit')
            appUtils.pageInit(_pageUrl, "inclusive/hold");
        });
        //稳健理财
        appUtils.bindEvent($(_pageId + " #jjInclusive"), function () {
            let secondaryPageData = {
                title: "固收增强",
                index: 2
            }
            appUtils.setSStorageInfo("secondaryPageData", secondaryPageData)
            // tools.clickPoint(_pageId, _pageUrl, 'jjInclusive')
            appUtils.pageInit(_pageUrl, "inclusive/hold");
        });
        //进阶理财
        appUtils.bindEvent($(_pageId + " #jjHighEnd"), function () {
            let secondaryPageData = {
                title: "长期理财",
                index: 3
            }
            appUtils.setSStorageInfo("secondaryPageData", secondaryPageData)
            // tools.clickPoint(_pageId, _pageUrl, 'jjHighEnd')
            // appUtils.pageInit(_pageUrl, "highEnd/hold");
            appUtils.pageInit(_pageUrl, "inclusive/hold");
        });


        /**新页面  结束 */
        //在途资金
        appUtils.bindEvent($(_pageId + " #fundsInTransit"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'fundsInTransit')
            appUtils.pageInit(_pageUrl, "account/theWayRecord");
        });
        //同行好友
        appUtils.bindEvent($(_pageId + " #vipBenefits"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'vipBenefits')
            appUtils.pageInit(_pageUrl, "vipBenefits/index");
        });
        //邀请好友
        appUtils.bindEvent($(_pageId + " #InviteFriends"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'InviteFriends')
            appUtils.pageInit(_pageUrl, "vipBenefits/friendInvitation", { source: source });    //主动邀请
        });
        //邀请奖励
        appUtils.bindEvent($(_pageId + " #InviteRewards"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'InviteRewards')
            appUtils.pageInit(_pageUrl, "spokesperson/inviteRewardsPage");
        });
        //安全中心
        appUtils.bindEvent($(_pageId + " #passWordManage"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'passWordManage')
            appUtils.pageInit(_pageUrl, "safety/passwordManage");
        });
        //理财账单
        appUtils.bindEvent($(_pageId + " #financialBill"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'financialBill')
            appUtils.pageInit(_pageUrl, "activity/financialBill");
        });

        //转让
        appUtils.bindEvent($(_pageId + " #myTransfer"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'myTransfer');
            appUtils.setSStorageInfo("_cust_fund_type", '0');
            appUtils.pageInit(_pageUrl, "myTransfer/index");
        });
        //定投
        appUtils.bindEvent($(_pageId + " #myInvestment"), function () {
            appUtils.setSStorageInfo("productInfo", {});
            // tools.clickPoint(_pageId, _pageUrl, 'myInvestment');
            appUtils.setSStorageInfo("fixed_investment_list", '0');
            appUtils.setSStorageInfo("singlePlan", '0');
            appUtils.pageInit(_pageUrl, "fixedInvestment/investmentList");
        });
        //投顾
        appUtils.bindEvent($(_pageId + " #myCombPro"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'myCombPro')
            // appUtils.setSStorageInfo("productType", '06') //存储类型
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageUrl)) return;
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    let operationId = 'riskAssessment'
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                appUtils.pageInit(_pageUrl, "combProduct/combProdList", {});
            });
            return;
        });
        //持仓详情
        appUtils.bindEvent($(_pageId + " #holdHeighDetail"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'holdHeighDetail')
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(_pageUrl, "template/holdHeighDetail");
        });
        //产品详情
        appUtils.bindEvent($(_pageId + " #heighEndProduct"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'heighEndProduct')
            sessionStorage.vip_buttonShow = true;
            appUtils.pageInit(_pageUrl, "template/heighEndProduct");
        });
		//产品详情
		appUtils.bindEvent($(_pageId + " #publicHave"), function () {
		    sessionStorage.vip_buttonShow = true;
		    appUtils.pageInit(_pageUrl, "template/publicHoldHeightDetail");
		});

        //财富微信版
        appUtils.bindEvent($(_pageId + " #wxfund"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'wxfund')
            service.reqFun101051({}, function (data) {
                if (data.error_no == "0") {
                    var results = data.results;
                    if (results.length > 0) {
                        appUtils.pageInit(_pageUrl, "wxfund/index", { token: results[0].token });
                        return;
                    }
                    layerUtils.iAlert("网络繁忙，请稍后重试")
                } else {
                    layerUtils.iAlert(data.error_info);
                }
            })
        });

        // 晋金所资产-去查看
        appUtils.bindEvent($(_pageId + " #jjsAsset"), function () {
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageUrl)) return;
            //校验用户是否上传过身份证照片
            if(!ut.getUploadStatus()){
                let operationId = 'noUploadIdCard'
                appUtils.setSStorageInfo("noUploadIdCard", '1');
                return layerUtils.iConfirm("您还未上传身份证照片", function () {
                    appUtils.pageInit(_page_code, "account/uploadIDCard", {});
                }, function () {
                }, "去上传", "取消",operationId);
            }
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag
            var perfect_info = appUtils.getSStorageInfo("user").perfect_info;
            //到期3个月后提示
            if (perfect_info == 4) {
                let operationId = 'replaceIdCard'
                return layerUtils.iConfirm("您的身份证照片已到期，请先更换", () => { }, () => {
                    appUtils.pageInit(_pageUrl, "account/uploadIDCard", {});
                }, "取消", "更换",operationId);
            }
            common.changeCardInter(function () {
                if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                    let operationId = 'riskAssessment'
                    layerUtils.iConfirm("您还未进行风险测评", function () {
                        appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
                    }, function () {
                    }, "去测评", "取消",operationId);
                    return;
                } else if (invalidFlag == '1') {
                    pageTo_evaluation()
                    return
                }
                //调用107003接口 
                service.reqFun177003({
                    bank_leave_phone: userInfo.bankReservedMobile,
                    card_no: userInfo.bankAcct,
                    user_name: userInfo.name,
                    identity_num: userInfo.identityNum
                }, function (data) {
                    if (data.error_no != 0) {
                        layerUtils.iAlert(data.error_info);
                        return;
                    }
                    var result = data.results[0];
                    appUtils.setSStorageInfo("jjs_cust_no", result.jjsCustNo);
                    layerUtils.iLoading(false)
                    // tools.clickPoint(_pageId, _pageUrl, 'jjsAsset')
                    //缓存流程所用晋金所客户号
                    appUtils.setSStorageInfo("jjsCustNo",result.jjsCustNo);
                    appUtils.setSStorageInfo("jjsShowData",result);
                    appUtils.pageInit(_pageUrl, "account/jjsAssets");
                })
            });
            
        });
        //首页
        appUtils.bindEvent($(_pageId + " #shouye"), function () {
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageUrl, "login/userIndexs", {});
        });
        //跳转到学投资页面
        appUtils.bindEvent($(_pageId + " #Learn"), function () {
            if (!common.loginInter()) return;
            //判断缓存中时间戳 < 当前时间戳 500 可以跳转否则不能跳转
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageUrl, "liveBroadcast/index", {});
        });
        // 点击跳转到 更多页面
        appUtils.bindEvent($(_pageId + " #gengduo"), function () {
            if (localStorage.toPageTime && (new Date().getTime() - localStorage.toPageTime < 500)) return
            //页面跳转前存储当前时间戳
            localStorage.toPageTime = new Date().getTime();
            appUtils.pageInit(_pageUrl, "moreDetails/more", {});
        });
        //风险测评
        appUtils.bindEvent($(_pageId + " #risk"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'risk')
            appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
        });
        appUtils.bindEvent($(_pageId + " #wealthAdvisor"), function () {
            scene_code = common.getLocalStorage("scene_code") ? common.getLocalStorage("scene_code") : ''; //页面版本类型 1标准版 X版
            if(scene_code == '3') return appUtils.pageInit(_pageUrl, "highVersion/adviser", {});
            appUtils.pageInit(_pageUrl, "account/wealthAdvisor", {});
        });
        //切换高端
        appUtils.bindEvent($(_pageId + " #switchingHigh"), function () {
            changeUserVersion({scene_code:'3'})
        });
        //切换标准
        appUtils.bindEvent($(_pageId + " #switchingStandards"), function () {
            changeUserVersion({scene_code:'1'})
        });
        //切换标准
        appUtils.bindEvent($(_pageId + " #transferZone"), function () {
            // tools.clickPoint(_pageId, _pageUrl, 'transferPro')
            tools.setPageToUrl('login/listMorePage', '2');
            appUtils.setSStorageInfo("productType", '06') //存储类型
            if (!common.loginInter()) return;
            if (!ut.hasBindCard(_pageUrl)) return;
            var invalidFlag = appUtils.getSStorageInfo("user").invalidFlag;
            if (validatorUtil.isEmpty(ut.getUserInf().riskLevel)) {
                let operationId = 'riskAssessment'
                layerUtils.iConfirm("您还未进行风险测评", function () {
                    // tools.recordEventData('1','riskQuestion','风险测评');
                    appUtils.pageInit(_pageUrl, "safety/riskQuestion", {});
                }, function () {
                }, "去测评", "取消",operationId);
                return;
            } else if (invalidFlag == '1') {
                pageTo_evaluation()
                return
            }

            appUtils.pageInit(_pageUrl, "login/listMorePage", {});
        });
        
        
    }

    function destroy() {
        tools.recordEventData('4','destroy','页面销毁');
        $(_pageId + " .other_myAccount").hide();
        $(_pageId + " .staffFlag").hide();
        $(_pageId + " .high_myAccount").hide();
        $(_pageId + " #riskShow").hide();
        $(_pageId + " #wealth_advisor").hide();
        $(_pageId + " #wdyy").hide();
        $(_pageId + " .tip").hide();
        $(_pageId + " .total_assets em").html("");
        $(_pageId + " #thfund em").html("");
        $(_pageId + " #bankDeposit em").html("");
        $(_pageId + " #jjInclusive em").html("");
        $(_pageId + " #jjHighEnd em").html("");
        $(_pageId + " #fundsInTransit em").html("");
        $(_pageId + " .wxfund_box").hide();
        $(_pageId + " .jjs_box").hide();
        $(_pageId + " #myCombPro").hide();
        $(_pageId + " .exprie_tip").hide();
        // $(_pageId + " .bankDeposit_region").hide();
    }

    /****************自定义方法，写到destroy后面**********************/

    //添加头像
    function testHead() {
        service.reqFun1100007({}, function (data) {
            var error_no = data.error_no,
                error_info = data.error_info;
            if (error_no == "0") {
                var result = (data.results)[0];
                var photo_url = result.photoUrl;//用户头像
                //var photo_url = data.results[0].photo_url;//用户头像
                if (photo_url) {
                    $(_pageId + " #headPortrait img").attr("src", global.oss_url + photo_url + "?key=" + new Date().getTime());
                    var params = {
                        funcNo: "50042",
                        key: "photo_url",
                        isEncrypt: "1",
                        value: global.oss_url + photo_url + "?key=" + new Date().getTime(),
                    };
                    external.callMessage(params);
                } else {
                    $(_pageId + " #headPortrait img").attr("src", "./images/highEnd/high_headerImg.png");
                    var params = {
                        funcNo: "50042",
                        key: "photo_url",
                        value: "",
                        isEncrypt: "1"
                    };
                    external.callMessage(params);
                }
            } else {
                layerUtils.iAlert(error_info);
            }
        });
    }

    //取名称作为我的第一列展示，如果没有就去手机号码
    function nameOrPhone() {
        var nameCheck = userInfo.name;
        if (nameCheck) {
            var name_Check = nameCheck.substr(0, 1);
            for (var i = 2; i <= nameCheck.length; i++) {
                name_Check += "*";
            }
            $(_pageId + " #phoneNum").html(name_Check);
        } else {
            $(_pageId + " #phoneNum").html(userInfo.mobile);
        }
    }

    //清空银行卡信息
    function celarBankInfo() {
        // 清空银行卡信息
        $("#account_setBankCardInfo #oneMoney").html("");
        $("#account_setBankCardInfo #drxe").html("");
        $("#account_setBankCardInfo #bankname").html("");  // 银行名称
        $("#account_setBankCardInfo input").val(""); // 清空所有的记录
    }

    //风险测评提示
    function initRisk() {
        //风险测评设置
        if (userInfo.riskName && userInfo.riskLevel) {
            $(_pageId + " #riskShow").hide();
        } else {
            $(_pageId + " #riskShow").show();
        }
    }

    //基金用户资产查询
    function getAssetInfo() {
        service.reqFun101999({}, function (data) {
            if (data.error_no == "0") {
                var result = data.results[0];
                var totalAssets = result.totalAssets ? common.fmoney(result.totalAssets, 2) : "--";//总资产
                if (result.bankfundAssets && result.bankfundAssets != "--") {
                    var bankfundAssets = result.bankfundAssets ? common.fmoney(result.bankfundAssets, 2) : "--"; //银行
                } else {
                    var bankfundAssets = "--"
                }
                var prifundAssets = result.prifundAssets ? common.fmoney(result.prifundAssets, 2) : "--"; //私募
                var pubfundAssets = result.pubfundAssets ? common.fmoney(result.pubfundAssets, 2) : "--"; //公募
                var mfundAssets = result.mfundAssets ? common.fmoney(result.mfundAssets, 2) : "--";//现金宝
                var fromFundAssets = result.fromFundAssets ? common.fmoney(result.fromFundAssets, 2) : "--";//在途
                var flag = result.flag; //是否已开银行户  Y开  N未开
                if (flag == "Y") {
                    $(_pageId + " .bankDeposit_region").show();
                } else {
                    $(_pageId + " .bankDeposit_region").hide();
                }
                appUtils.setSStorageInfo("pubfundAssets", pubfundAssets);
                $(_pageId + " .total_assets em").html(totalAssets + "元");
                $(_pageId + " #thfund em").html(mfundAssets + "元");
                $(_pageId + " #bankDeposit em").html(bankfundAssets + "元");
                $(_pageId + " #jjInclusive em").html(pubfundAssets + "元");
                $(_pageId + " #jjHighEnd em").html(prifundAssets + "元");
                $(_pageId + " #fundsInTransit em").html(fromFundAssets + "元");

            } else {
                $(_pageId + " .total_assets em").html("--");
                $(_pageId + " #bankDeposit em").html("--");
                $(_pageId + " #jjInclusive em").html("--");
                $(_pageId + " #jinjibao em").html("--");
                $(_pageId + " #jjHighEnd em").html("--");
                $(_pageId + " #fundsInTransit em").html("--");
                $(_pageId + " .bankDeposit_region").hide();
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    // 查询白名单列表 我的页面菜单是否展示
    function qryWhiteList() {
        service.reqFun102127({}, function (data) {
            if (data.error_no == "0") {
                var result = data.results ? data.results[0] : {};
                if (result.wxbFlag == "1") {
                    $(_pageId + " .wxfund_box").show();
                } else {
                    $(_pageId + " .wxfund_box").hide();
                }
                if (result.financialFlag == "0") {
                    $(_pageId + " #financialBill").show();
                } else {
                    $(_pageId + " #financialBill").hide();
                }
                if (result.staffFlag == "1") {
                    $(_pageId + " .staffFlag").show();
                } else {
                    $(_pageId + " .staffFlag").hide();
                }
                if (result.jjsFlag == '1') {
                    openFlag = result.openFlag; // 晋金所开户状态
                    $(_pageId + " .jjs_box").show();
                } else {
                    $(_pageId + " .jjs_box").hide();
                }
                // TODO;邀请奖励
                if (result.inviteRewardFlag == "1") {
                    $(_pageId + " #InviteRewards").show();
                } else {
                    $(_pageId + " #InviteRewards").hide();
                }
                if (result.transferFlag == "1") {
                    $(_pageId + " #transferZone").show();
                } else {
                    $(_pageId + " #transferZone").hide();
                }
                //是否展示财富顾问
                if (result.cfgwFlag == "1") {
                    $(_pageId + " .wealth_advisor").show();
                } else {
                    $(_pageId + " .wealth_advisor").hide();
                }
            } else {
                $(_pageId + " .wxfund_box").hide();
                layerUtils.iAlert(data.error_info);
            }
        })
    }

    var myAccount = {
        "init": init,
        "bindPageEvent": bindPageEvent,
        "destroy": destroy
    };
    // 暴露对外的接口
    module.exports = myAccount;
})