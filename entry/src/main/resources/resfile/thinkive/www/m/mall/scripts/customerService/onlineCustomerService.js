// 2017-7-11
// jiaxr
// 在线客服
define(function(require, exports, module){
	var _pageId = "#customerService_onlineCustomerService ",
		appUtils = require("appUtils");
	var backPageUrl = "";//用于确定返回页面
	function init(){
		backPageUrl = appUtils.getPageParam("backPageUrl");
		//设置页面高度
		setHeight();

	}

	//设置页面高度
	function setHeight(){
		$(_pageId+" iframe").css("overflow-y","auto");
		$(_pageId+" iframe").css("height",
			window.innerHeight-
			$(_pageId+" header").height()
		);
	}

	//绑定事件
	function bindPageEvent(){

		//返回
		appUtils.bindEvent($(_pageId+" .icon_back"),function(){
			pageBack();
		});



	}
	function destroy(){

	}
	function pageBack() {
		appUtils.pageBack();
	}
	var onlineCustomerService = {
		"init": init,
		"bindPageEvent": bindPageEvent,
		"destroy": destroy,
		"pageBack": pageBack
	};
	// 暴露对外的接口
	module.exports = onlineCustomerService ;
});
