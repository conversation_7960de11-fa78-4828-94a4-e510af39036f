{"HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {}, "APIS": [], "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "BUILD_ID": "202508041848396180", "ERROR_MESSAGE": [{"CODE": "00303010", "MESSAGE": "The property of module should be an array in root project %s. At file: %s", "SOLUTIONS": ["Check the project-level build-profile.json5 file and ensure that the module's option is an array."], "TIMESTAMP": "1754304519621"}], "TOTAL_TIME": 5546917}}