[2025-08-01T13:57:11.183] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-01T13:57:10.968] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-01T13:57:10.981] [DEBUG] debug-file - env: daemon=true
[2025-08-01T13:57:10.969] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-01T14:13:01.213] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-01T14:13:00.998] [DEBUG] debug-file - env: daemon=true
[2025-08-01T14:13:00.984] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-01T14:13:00.985] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-01T14:49:07.652] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-01T14:49:07.438] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-01T14:49:07.450] [DEBUG] debug-file - env: daemon=true
[2025-08-01T14:49:07.439] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-01T15:51:59.931] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-01T15:51:59.736] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-01T15:51:59.748] [DEBUG] debug-file - env: daemon=true
[2025-08-01T15:51:59.737] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-01T16:12:34.283] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-01T16:12:34.076] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-01T16:12:34.089] [DEBUG] debug-file - env: daemon=true
[2025-08-01T16:12:34.077] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T15:05:18.548] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T15:05:18.290] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T15:05:18.304] [DEBUG] debug-file - env: daemon=true
[2025-08-04T15:05:18.291] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T15:45:50.686] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T15:45:50.398] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T15:45:50.412] [DEBUG] debug-file - env: daemon=true
[2025-08-04T15:45:50.399] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T15:53:43.438] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T15:53:43.209] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T15:53:43.222] [DEBUG] debug-file - env: daemon=true
[2025-08-04T15:53:43.209] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T16:06:59.190] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T16:06:58.968] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T16:06:58.981] [DEBUG] debug-file - env: daemon=true
[2025-08-04T16:06:58.969] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T16:28:55.955] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T16:28:55.383] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T16:28:55.402] [DEBUG] debug-file - env: daemon=true
[2025-08-04T16:28:55.385] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T18:46:40.506] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T18:46:39.991] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T18:46:40.005] [DEBUG] debug-file - env: daemon=true
[2025-08-04T18:46:39.992] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T18:47:22.162] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T18:47:21.889] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T18:47:21.904] [DEBUG] debug-file - env: daemon=true
[2025-08-04T18:47:21.890] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T18:47:33.488] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T18:47:33.156] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T18:47:33.171] [DEBUG] debug-file - env: daemon=true
[2025-08-04T18:47:33.157] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T18:48:39.351] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T18:48:39.623] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-08-04T18:48:39.352] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T18:48:39.623] [WARN] debug-file - [31m00303010 Configuration Error
Error Message: The property of module should be an array in root project build-profile.json5. At file: /Users/<USER>/Desktop/kh_HarmonyOS/project/jjcf/JJCFProject/entry/build-profile.json5

* Try the following:
  > Check the project-level build-profile.json5 file and ensure that the module's option is an array.
[39m
[2025-08-04T18:48:39.637] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-08-04T18:48:39.352] [DEBUG] debug-file - env: daemon=false
[2025-08-04T18:48:39.623] [DEBUG] debug-file - ERROR: stacktrace = AdaptorError: [31m00303010 Configuration Error
Error Message: The property of module should be an array in root project build-profile.json5. At file: /Users/<USER>/Desktop/kh_HarmonyOS/project/jjcf/JJCFProject/entry/build-profile.json5

* Try the following:
  > Check the project-level build-profile.json5 file and ensure that the module's option is an array.
[39m
    at HvigorLogger.printErrorExit (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/log/hvigor-log.js:1:5062)
    at checkProjectConfigFile (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3292)
    at new HvigorConfig (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1465)
    at hvigorConfigInit (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3708)
    at init (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/internal/lifecycle/init.js:1:2867)
    at start (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/boot/index.js:1:2518)
    at boot (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/boot/index.js:1:1818)
    at startHvigorBuild (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/cli/main/cli.js:1:7585)
    at /Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/bin/hvigor.js:50:64
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[2025-08-04T18:48:39.638] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-08-04T18:48:39.352] [DEBUG] debug-file - no-daemon, use the default node options
[2025-08-04T18:48:39.623] [ERROR] debug-file - AdaptorError: [31m00303010 Configuration Error
Error Message: The property of module should be an array in root project build-profile.json5. At file: /Users/<USER>/Desktop/kh_HarmonyOS/project/jjcf/JJCFProject/entry/build-profile.json5

* Try the following:
  > Check the project-level build-profile.json5 file and ensure that the module's option is an array.
[39m
    at HvigorLogger.printErrorExit (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/log/hvigor-log.js:1:5062)
    at checkProjectConfigFile (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3292)
    at new HvigorConfig (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1465)
    at hvigorConfigInit (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3708)
    at init (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/internal/lifecycle/init.js:1:2867)
    at start (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/boot/index.js:1:2518)
    at boot (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/boot/index.js:1:1818)
    at startHvigorBuild (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/cli/main/cli.js:1:7585)
    at /Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/bin/hvigor.js:50:64
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[2025-08-04T18:48:39.640] [DEBUG] debug-file - Current worker pool is stopped or closed.
[2025-08-04T18:48:39.625] [WARN] debug-file - BUILD FAILED in 6 ms 
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Clear worker 0.
[2025-08-04T18:48:39.636] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Worker 0 has been cleared.
[2025-08-04T18:48:39.636] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Current idle worker size: 1.
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Current busy worker size: 0.
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Clear worker 1.
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Worker 1 has been cleared.
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Current idle worker size: 0.
[2025-08-04T18:48:39.641] [DEBUG] debug-file - Current busy worker size: 0.
[2025-08-04T18:48:39.642] [DEBUG] debug-file - hvigor build process will be closed.
[2025-08-04T18:48:39.648] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2025-08-04T18:48:39.649] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2025-08-04T18:48:39.649] [DEBUG] debug-file - Current worker pool is terminated.
[2025-08-04T19:04:55.477] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T19:04:55.224] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T19:04:55.242] [DEBUG] debug-file - env: daemon=true
[2025-08-04T19:04:55.226] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T19:04:56.771] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T19:04:56.594] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T19:04:56.605] [DEBUG] debug-file - env: daemon=true
[2025-08-04T19:04:56.595] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-04T19:05:19.342] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-04T19:05:19.138] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-04T19:05:19.150] [DEBUG] debug-file - env: daemon=true
[2025-08-04T19:05:19.139] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-05T11:02:48.601] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-05T11:02:48.378] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-05T11:02:48.393] [DEBUG] debug-file - env: daemon=true
[2025-08-05T11:02:48.379] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-05T11:03:42.982] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-05T11:03:42.702] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-05T11:03:42.714] [DEBUG] debug-file - env: daemon=true
[2025-08-05T11:03:42.703] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
[2025-08-05T11:11:49.109] [DEBUG] debug-file - java daemon tryConnect success
[2025-08-05T11:11:48.891] [DEBUG] debug-file - env: nodejsVersion=v18.14.1
[2025-08-05T11:11:48.905] [DEBUG] debug-file - env: daemon=true
[2025-08-05T11:11:48.892] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  properties: { 'hvigor.dependency.useNpm': true },
  modelVersion: '',
  dependencies: {}
}
