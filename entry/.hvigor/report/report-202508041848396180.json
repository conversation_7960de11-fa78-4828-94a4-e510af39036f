{"version": "2.0", "ppid": 34493, "events": [{"head": {"id": "abd230d3-7d3a-433b-89cc-58f76fcebec2", "name": "env: nodejsVersion=v18.14.1", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048502645083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973c42b7-9f09-492f-a28b-a40fcc344e22", "name": "env: hvigor-config.json5 content = {\n  properties: { 'hvigor.dependency.useNpm': true },\n  modelVersion: '',\n  dependencies: {}\n}", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048503912625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57897e58-8287-4059-80d5-224ea032a235", "name": "env: daemon=false", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048504726083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30a5dc21-9e61-49b0-b75d-4022d8bb7ee9", "name": "no-daemon, use the default node options", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048504775250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8417c6dc-a3c5-4506-ac53-d56da8c37b40", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048771324166}, "additional": {"children": ["2d8a4efc-a8fc-479c-be05-efc971d97ef3", "7f4e7ba4-9422-4b98-81ec-0838d9c48b0a", "5a42fc0f-8801-4abd-93ed-067acafa4868", "89301083-c697-46e9-9567-7f346078cf8a"], "state": "running", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d8a4efc-a8fc-479c-be05-efc971d97ef3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048771331583}, "additional": {"children": [], "state": "running", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8417c6dc-a3c5-4506-ac53-d56da8c37b40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb59f620-ebee-40fb-a036-0d9cbdca7107", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048774760041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b579ff-6bb8-44b8-990a-cabaad133fca", "name": "\u001b[31m00303010 Configuration Error\nError Message: The property of module should be an array in root project build-profile.json5. At file: /Users/<USER>/Desktop/kh_HarmonyOS/project/jjcf/JJCFProject/entry/build-profile.json5\n\n* Try the following:\n  > Check the project-level build-profile.json5 file and ensure that the module's option is an array.\n\u001b[39m", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048775010333}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "964fb2fa-badd-429d-854a-77566990e095", "name": "ERROR: stacktrace = AdaptorError: \u001b[31m00303010 Configuration Error\nError Message: The property of module should be an array in root project build-profile.json5. At file: /Users/<USER>/Desktop/kh_HarmonyOS/project/jjcf/JJCFProject/entry/build-profile.json5\n\n* Try the following:\n  > Check the project-level build-profile.json5 file and ensure that the module's option is an array.\n\u001b[39m\n    at HvigorLogger.printErrorExit (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/log/hvigor-log.js:1:5062)\n    at checkProjectConfigFile (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3292)\n    at new HvigorConfig (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1465)\n    at hvigorConfigInit (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3708)\n    at init (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/internal/lifecycle/init.js:1:2867)\n    at start (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/boot/index.js:1:2518)\n    at boot (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/base/boot/index.js:1:1818)\n    at startHvigorBuild (/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/cli/main/cli.js:1:7585)\n    at /Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/bin/hvigor.js:50:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048775738166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ebf6842-b790-49e9-a115-863d086ff9b5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048776189416, "endTime": 19048776271916}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69d10d37-b2a0-453d-b73b-175da63e3248", "logId": "8b96bfe1-fa07-4ab4-a6d7-0e630d6cfb9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b96bfe1-fa07-4ab4-a6d7-0e630d6cfb9a", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048776189416, "endTime": 19048776271916}, "additional": {"logType": "info", "children": [], "durationId": "2ebf6842-b790-49e9-a115-863d086ff9b5"}}, {"head": {"id": "2e081c3f-94e3-4085-872d-c7f083657f78", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048770799791, "endTime": 19048777271833}, "additional": {"time": {"year": 2025, "month": 8, "day": 4, "hour": 18, "minute": 48, "second": 39}, "completeCommand": "{\"prop\":[],\"daemon\":false,\"_\":[\"assembleHap\"]};assembleHap --no-daemon", "hvigorVersion": "5.19.5", "markType": "history", "nodeVersion": "v18.14.1", "category": "build", "state": "failed"}}, {"head": {"id": "22e4462d-8fc2-431a-9b0a-5955fec4dd1d", "name": "BUILD FAILED in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 34684, "tid": "Main Thread", "startTime": 19048777408666}, "additional": {"logType": "error", "children": []}}]}