{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_JJCFProject_MB7uwbhjtSOldk2avja94NThMtoUp5_8BViEwnrLxIc=.cer",
          "storePassword": "0000001B8C84657F1FCBA62D7DC39A7EFD95A5CBEC8F63D9B8D44A60DDE6E42DF7BC6647D717FE6202FD52",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B1DDEBCCE671AE0144E36B848F98AD0847878A12E2FAAA4F4A241E570EBB2DFB45AC2713D329D2F",
          "profile": "/Users/<USER>/.ohos/config/default_JJCFProject_MB7uwbhjtSOldk2avja94NThMtoUp5_8BViEwnrLxIc=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_JJCFProject_MB7uwbhjtSOldk2avja94NThMtoUp5_8BViEwnrLxIc=.p12"
        }
      },
      {
        "name": "sign_dev",
        "type": "HarmonyOS",
        "material": {
          "storePassword": "0000001808985AB15A49086355EA06BF263FD63B876D2A431BF1ECC26EEB56E620280CFED9990182",
          "certpath": "./sign/晋金财富调试证书20250312.cer",
          "keyAlias": "a",
          "keyPassword": "00000018BCEAAFB098202C7CA230581CD285FA05635E0D97FA900782E8674ABAB7AF8744DE949326",
          "profile": "./sign/晋金财富调试Debug.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "./sign/jjcftiaoshi20250312.p12"
        }
      },
      {
        "name": "sign_pro",
        "type": "HarmonyOS",
        "material": {
          "storePassword": "00000018240EA6F845EBAFA94B9D05A26C6C4583AB860B108CF18A7C71F485BB8114C16658436620",
          "certpath": "./sign/晋金财富发布证书.cer",
          "keyAlias": "a",
          "keyPassword": "000000188ADD355D80BE5676821E5D574A537A96E3FD584A2E4975EF3CA3F21F1B01DF41BB33BB48",
          "profile": "./sign/jjcfharmonyfbRelease.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "./sign/jjcfharmonyfabu.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      },
      {
        "name": "jjcf_dev",
        "signingConfig": "sign_dev",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      },
      {
        "name": "jjcf_pro",
        "signingConfig": "sign_pro",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default",
            "jjcf_dev",
            "jjcf_pro"
          ]
        }
      ]
    },
    {
      "name": "ShareSDK",
      "srcPath": "./ShareSDK"
    }
  ]
}