#!/bin/bash

# HarmonyOS Hvigor Wrapper Script
# This script attempts to run hvigor build commands

echo "Starting HarmonyOS build process..."

# Check if ohpm is available
if ! command -v ohpm &> /dev/null; then
    echo "Error: ohpm is not installed or not in PATH"
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
ohpm install

# Try to find and run hvigor
if [ -f "./node_modules/.bin/hvigor" ]; then
    echo "Running hvigor assembleHap..."
    ./node_modules/.bin/hvigor assembleHap
elif [ -f "./oh_modules/.bin/hvigor" ]; then
    echo "Running hvigor assembleHap..."
    ./oh_modules/.bin/hvigor assembleHap
else
    echo "Warning: hvigor not found in standard locations"
    echo "Attempting to use npx..."
    npx hvigor assembleHap 2>/dev/null || {
        echo "Error: Could not find hvigor build tool"
        echo "Please ensure DevEco Studio and HarmonyOS SDK are properly installed"
        exit 1
    }
fi

echo "Build process completed"
