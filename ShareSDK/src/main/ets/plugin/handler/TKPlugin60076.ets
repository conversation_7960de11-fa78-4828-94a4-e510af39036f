import { T<PERSON>BasePlugin, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON>bjectHelper, TKResultVO, TKStringHelper } from '@thinkive/tk-harmony-base'
import { TKDialogHelper } from '@thinkive/tk-harmony-base/Index'
import { TKShare } from '../../manager/TKShare'
import {
  TKOpenMiniProgramParam,
  TKSharePluginMiniProgramParam } from '../../model/TKShareParam'

export class TKPlugin60076 extends TKBasePlugin {
  serverInvoke(v126: Record<string, Object>): TKResultVO {
    const resultVO = new TKResultVO()
    const param = TKObjectHelper.toObject(v126, TKSharePluginMiniProgramParam) as TKSharePluginMiniProgramParam

    // const appId: string = param.appId ?? ''
    // if (TKStringHelper.isEmpty(appId)) {
    //   resultVO.errorNo = -6007601
    //   resultVO.errorInfo = 'appId不能为空'
    //   return resultVO
    // }

    let shareParam: TKOpenMiniProgramParam = new TKOpenMiniProgramParam(param)
    TKShare.openMiniProgram(shareParam).then((result) => {
      if (result.flag == '1') {
        this.harmonyCallJS(result)
      } else {  // result.flag == '0' 取消不处理
        TKDialogHelper.showToast(result.info as string)
      }
    }).catch((reason: string) => {
      TKDialogHelper.showToast(reason)
    })
    return resultVO
  }
}