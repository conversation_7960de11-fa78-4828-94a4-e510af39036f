import { T<PERSON>BasePlugin, T<PERSON><PERSON>ormatHelper, <PERSON><PERSON><PERSON><PERSON>, TKResultVO, TKStringHelper } from '@thinkive/tk-harmony-base'
import { TKShare, TKShareConfig } from '../../manager/TKShare'
import { T<PERSON>hareParam, TKSharePluginPlatformListParam } from '../../model/TKShareParam'

export class TKPlugin50230 extends TKBasePlugin {
  serverInvoke(v126: Record<string, Object>): TKResultVO {
    const resultVO = new TKResultVO()
    const param = v126 as TKSharePluginPlatformListParam
    const shareTypeList: string = param.shareTypeList ?? ''
    if (TKStringHelper.isEmpty(shareTypeList)) {
      resultVO.errorNo = -5023001
      resultVO.errorInfo = '分享的平台不能为空！'
      return resultVO
    }
    let imageUrl = param.imgUrl
    if (TKStringHelper.isNotEmpty(param.base64Img)) {
      imageUrl = param.base64Img
    }
    if (param.imageType == 'base64') {
      if (!TKFormatHelper.isFormatRegex(TKShareConfig.RegularExpresion.base64, imageUrl as string)) {
        imageUrl = 'data:image/png;base64,' + imageUrl
      }
    }
    TKLog.debug(`=======================分享imageUrl:${imageUrl}======================`)
    if (imageUrl == undefined || (typeof imageUrl == 'string' && TKStringHelper.isEmpty(imageUrl))) {
      resultVO.errorNo = -5023005
      resultVO.errorInfo = '分享的图标不能访问或为空'
      return resultVO
    }
    let shareParam: TKShareParam | undefined = undefined
    if (param.imageShare == '1') {
      shareParam = { image: imageUrl }
    } else {
      if (TKStringHelper.isEmpty(param.content)) {
        resultVO.errorNo = -5023002
        resultVO.errorInfo = '分享的内容不能为空！'
      } else if (TKStringHelper.isEmpty(param.title)) {
        resultVO.errorNo = -5023003
        resultVO.errorInfo = '分享的标题不能为空！'
      } else if (TKStringHelper.isEmpty(param.link)) {
        resultVO.errorNo = -5023004
        resultVO.errorInfo = '分享的连接不能为空！'
      } else {
        shareParam = {
          title: param.title!,
          content: param.content!,
          url: param.link!,
          description: param.description!,
          image: imageUrl
        }
      }
    }
    if (shareParam != undefined) {
        TKShare.showShareMenuActionSheet(shareParam, shareTypeList, (result) => {
          this.harmonyCallJS(result)
        })
    }
    return resultVO
  }
}