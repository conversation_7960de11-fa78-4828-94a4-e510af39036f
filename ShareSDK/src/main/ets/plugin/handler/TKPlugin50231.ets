import { T<PERSON><PERSON><PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON>bjectHelper, TKResultVO, TKStringHelper } from '@thinkive/tk-harmony-base'
import { TKDialogHelper } from '@thinkive/tk-harmony-base/Index'
import { TKShare, TKShareConfig } from '../../manager/TKShare'
import {
  TKShareMiniProgramParam,
  TKShareParam, TKSharePluginPlatformParam } from '../../model/TKShareParam'

export class TKPlugin50231 extends TKBasePlugin {
  serverInvoke(v126: Record<string, Object>): TKResultVO {
    let resultVO = new TKResultVO()
    const param = TKObjectHelper.toObject(v126, TKSharePluginPlatformParam) as TKSharePluginPlatformParam

    // 调试代码
    // param.imageType = 'base64'

    const shareType: string = param.shareType ?? ''
    let shareContentType: string = param.shareContentType ?? ''
    if (T<PERSON>tringHelper.isEmpty(shareType)) {
      resultVO.errorNo = -5023101
      resultVO.errorInfo = '分享的平台不能为空！'
      return resultVO
    }
    let imageUrl = param.imgUrl
    if (TKStringHelper.isNotEmpty(param.base64Img)) {
      imageUrl = param.base64Img
    }
    if (param.imageType == 'base64') {
      if (!TKFormatHelper.isFormatRegex(TKShareConfig.RegularExpresion.base64, imageUrl as string)) {
        imageUrl = 'data:image/png;base64,' + imageUrl
      }
    }
    if (imageUrl == undefined || (typeof imageUrl == 'string' && TKStringHelper.isEmpty(imageUrl))) {
      resultVO.errorNo = -5023105
      resultVO.errorInfo = '分享的图标不能访问或为空'
      return resultVO
    }
    let shareParam: TKShareParam | undefined = undefined
    if (param.imageShare == '1') {
      shareParam = { image: imageUrl }
    } else {
      if (TKStringHelper.isEmpty(param.content)) {
        resultVO.errorNo = -5023102
        resultVO.errorInfo = '分享的内容不能为空！'
      } else if (TKStringHelper.isEmpty(param.title)) {
        resultVO.errorNo = -5023103
        resultVO.errorInfo = '分享的标题不能为空！'
      } else if (TKStringHelper.isEmpty(param.link)) {
        resultVO.errorNo = -5023104
        resultVO.errorInfo = '分享的连接不能为空！'
      } else {
        shareParam = {
          title: param.title!,
          content: param.content!,
          url: param.link!,
          description: param.description!,
          image: imageUrl
        }

        // 分享小程序
        if (shareContentType.length) {
          if (TKStringHelper.isEmpty(param.username)) {
            resultVO.errorNo = -5023106
            resultVO.errorInfo = '微信小程序必传参数不能为空'
            return resultVO
          } else if (TKStringHelper.isEmpty(param.webpageUrl)) {
            resultVO.errorNo = -5023106
            resultVO.errorInfo = '微信小程序必传参数不能为空'
            return resultVO
          }

          let miniProgramParamParam = shareParam as TKShareMiniProgramParam
          v126.path ? (miniProgramParamParam.path = v126.path as string) : undefined
          v126.path ? (miniProgramParamParam.webpageUrl = v126.webpageUrl as string) : undefined
          v126.path ? (miniProgramParamParam.username = v126.username as string) : undefined
          v126.path ? (miniProgramParamParam.userName = v126.userName as string) : undefined
        }
      }
    }
    if (shareParam != undefined) {
      TKShare.shareContent(shareParam, shareType).then((result) => {
        if (result.flag == '1') {
          this.harmonyCallJS(result)
        } else {  // result.flag == '0' 取消不处理
          TKDialogHelper.showToast(result.info as string)
        }
      }).catch((reason: string) => {
        // TKDialogHelper.showToast(reason)
        TKDialogHelper.showToast('暂未开放此功能') // 晋金财富需求提示文案
      })
    }
    return resultVO
  }
}