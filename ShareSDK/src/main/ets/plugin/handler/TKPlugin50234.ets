import { T<PERSON><PERSON>asePlugin, TKO<PERSON><PERSON>elper, TKResultVO, T<PERSON>tringHelper } from '@thinkive/tk-harmony-base'
import { TKDialogHelper } from '@thinkive/tk-harmony-base/Index'
import { TKShare } from '../../manager/TKShare'
import {
  TKOpenMiniProgramParam,
  TKSharePluginMiniProgramParam } from '../../model/TKShareParam'

export class TKPlugin50234 extends TKBasePlugin {
  serverInvoke(v126: Record<string, Object>): TKResultVO {
    const resultVO = new TKResultVO()
    const param = TKObjectHelper.toObject(v126, TKSharePluginMiniProgramParam) as TKSharePluginMiniProgramParam
    const username: string = param.username ?? ''
    if (TKStringHelper.isEmpty(username)) {
      resultVO.errorNo = -5023101
      resultVO.errorInfo = '小程序原始id不能为空！'
      return resultVO
    }
    let type = param.type
    if (type == undefined) {
      resultVO.errorNo = -5023101
      resultVO.errorInfo = '小程序原始id不能为空！'
      return resultVO
    }
    let shareParam: TKOpenMiniProgramParam = new TKOpenMiniProgramParam(param)
    TKShare.openMiniProgram(shareParam).then((result) => {
      this.harmonyCallJS(result)
    }).catch((reason: string) => {
      TKDialogHelper.showToast(reason)
    })
    return resultVO
  }
}