export interface TKSharePluginParamInterface {
  imageShare?: string
  imgUrl?: PixelMap | ResourceStr
  base64Img?: string
  title?: string
  link?: string
  content?: string
  description?: string
}

export class TKSharePluginPlatformListParam implements TKSharePluginParamInterface {
  shareTypeList?: string //22-微信好友 23-微信朋友圈 24-qq好友 1-新浪微博 6-qq空间
  imageShare?: string
  imageType?: string
  imgUrl?: PixelMap | ResourceStr
  base64Img?: string
  title?: string
  link?: string
  content?: string
  description?: string
}

export class TKSharePluginPlatformParam implements TKSharePluginParamInterface {
  shareType?: string //22-微信好友 23-微信朋友圈 24-qq好友 1-新浪微博 6-qq空间
  shareContentType?: string //3.6.3 小程序分享(暂时仅微信 QQ可用)
  imageShare?: string
  imageType?: string
  imgUrl?: string
  base64Img?: string
  title?: string
  link?: string
  content?: string
  description?: string

  // 小程序
  username?: string
  type?: number
  path?: string
  webpageUrl?: string

  public set userName(userName: string | undefined) {
    this.username = userName
  }

  public get userName(): string | undefined {
    return this.username
  }
}

export class TKSharePluginMiniProgramParam {
  username?: string
  type?: number
  path?: string
  webpageUrl?: string
  extData?: string

  public set userName(userName: string | undefined) {
    this.username = userName
  }

  public get userName(): string | undefined {
    return this.username
  }

  public set miniprogramType(miniprogramType: number) {
    this.type = miniprogramType
  }

  public get miniprogramType(): number {
    return this.type ?? 0
  }
}

export class TKShareLinkParam {
  title: string = ''
  url: string = ''
  image: PixelMap | ResourceStr = ''
  content: string = ''
  description: string = ''
}

export class TKShareImageParam {
  image: PixelMap | ResourceStr = ''
}

export class TKShareMiniProgramParam {
  title: string = ''
  url: string = ''
  image: PixelMap | ResourceStr = ''
  content: string = ''
  description: string = ''

  username?: string
  userName?: string
  type?: number
  path?: string
  webpageUrl?: string
  isShareTicket: boolean = false
}

export class TKOpenMiniProgramParam {

  username?: string
  type: number
  path?: string
  webpageUrl?: string
  constructor(param: TKSharePluginMiniProgramParam) {
    this.username = param.username ?? ''
    this.type = param.type ?? 0
    this.path = param.path
    this.webpageUrl = param.webpageUrl
  }

  public set userName(userName: string | undefined) {
    this.username = userName
  }

  public get userName(): string | undefined {
    return this.username
  }

  public set miniprogramType(miniprogramType: number) {
    this.type = miniprogramType
  }

  public get miniprogramType(): number {
    return this.type ?? 0
  }
}

export type TKShareParam = TKShareMiniProgramParam | TKShareLinkParam | TKShareImageParam

