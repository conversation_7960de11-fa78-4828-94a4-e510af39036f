import { TKSharePlatform } from "../manager/TKShare"

export interface TKShareListItemViewModelInterface {
  image: ResourceStr
  title: string
  type: string
  platform: TKSharePlatform
}

export class TKShareListItemViewModel implements TKShareListItemViewModelInterface {
  image: ResourceStr
  title: string
  type: string
  platform: TKSharePlatform

  constructor(model: TKShareListItemViewModelInterface) {
    this.image = model.image
    this.title = model.title
    this.type = model.type
    this.platform = model.platform
  }

}