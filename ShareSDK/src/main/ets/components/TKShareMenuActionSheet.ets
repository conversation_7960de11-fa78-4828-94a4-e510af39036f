import { T<PERSON><PERSON><PERSON>po<PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>elper } from "@thinkive/tk-harmony-base"
import { TKShareListItemViewModel } from "../model/TKShareListItemViewModel"
import { TKShareParam } from "../model/TKShareParam"
import { TKShare } from "../manager/TKShare";

export interface TKShareMenuActionSheetOption {
  shareList: TKShareListItemViewModel[]
  shareParam: TKShareParam
  completion: (result: Record<string, Object>) => void
}

class TKShareMenuActionSheetOptionModel implements TKShareMenuActionSheetOption {
  shareList: TKShareListItemViewModel[]
  shareParam: TKShareParam
  completion: (result: Record<string, Object>) => void

  constructor(model: TKShareMenuActionSheetOption) {
    this.shareList = model.shareList
    this.shareParam = model.shareParam
    this.completion = model.completion
  }
}

export interface TKShareMenuActionSheetContainerOption {
  option?: TKS<PERSON>MenuActionSheetOption
  dialog?: TKComponentContent<TKShareMenuActionSheetContainerOption>
}

@Builder
export function TKShareMenuActionSheetBuilder(option: TKShareMenuActionSheetContainerOption) {
  TKShareMenuActionSheet({option: option})
}

@Component
struct TKShareMenuActionSheet {
  @State option: TKShareMenuActionSheetContainerOption = {}
  @State private model: TKShareMenuActionSheetOptionModel | undefined = undefined

  aboutToAppear(): void {
    if (this.option.option != undefined) {
      this.model = new TKShareMenuActionSheetOptionModel(this.option.option)
    }
  }

  aboutToReuse(params: Record<string, Object>): void {
    if (TKMapHelper.getBoolean(params, 'isDisappear') == true) {
      return
    }
    const option = TKMapHelper.getObject(params, 'option') as TKShareMenuActionSheetOption | undefined
    this.option.option = option
    if (option != undefined) {
      this.model = new TKShareMenuActionSheetOptionModel(option)
    }
  }

  build() {
    Column() {
      Text('分享至')
        .fontSize(14)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .margin({top: 15, bottom: 15})
      Grid() {
        ForEach(this.model!.shareList, (model: TKShareListItemViewModel) => {
          GridItem() {
            Column() {
              Row() {
                Image(model.image)
                  .size({width: 37, height: 37})
              }
              .size({width: 50, height: 50})
              .backgroundColor('#FFFFFF')
              .borderRadius(8)
              .justifyContent(FlexAlign.Center)
              .margin({top: 5})
              Text(model.title)
                .fontColor('#999999')
                .fontSize(11)
                .margin({top: 6})
            }
            .width('100%')
            .height('100%')
          }
          .width(80)
          .height(81)
          .onClick(async () => {
            this.option.dialog?.close()
            const result = await TKShare.share(this.model!.shareParam, model)
            this.model?.completion(result)
          })
        })
      }
      .columnsTemplate('1fr '.repeat(4))
      .columnsGap(10)
      .rowsGap(10)
      .width('100%')
      .height(this.getHeight())
      Divider()
        .strokeWidth(0.5)
        .color('#EDEDED')
      Row() {
        Text('取消')
          .fontSize(13)
          .fontColor('#333333')
          .fontWeight(FontWeight.Regular)
          .margin({top: 15, bottom: 15})
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .onClick(() => {
        this.option.dialog?.close()
      })
    }
    .width('100%')
    .borderRadius({topLeft: 10, topRight: 10})
    .backgroundColor('#F5F6FA')
    .padding({bottom: px2vp(TKWindowHelper.getNavigationBottomBarHeightSync())})
  }

  getHeight(): number {
    const row = Math.floor((this.model!.shareList.length + 3) / 4)
    return 81 * row + 10 * (row - 1)
  }

}