import { common, Want } from "@kit.AbilityKit";
import {
  T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>mage<PERSON>el<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TKMapHelper,
  TKPluginInvokeCenter,
  TKStringHelper,
  TKURLRequestHelper,
  TKURLRequestVO,
} from "@thinkive/tk-harmony-base";
import mobShare from "@zztsdk/sharesdk";
import { ZztSDK } from "@zztsdk/zztcore";
import { HashMap, JSON } from "@kit.ArkTS";
import * as TKSharePlugin from '../plugin/TKSharePlugin'
import { TKShareLinkParam, TKShareImageParam, TKShareParam, TKOpenMiniProgramParam,
  TKShareMiniProgramParam, } from "../model/TKShareParam";
import { TKShareListItemViewModel } from "../model/TKShareListItemViewModel";
import {
  TKShareMenuActionSheetBuilder,
  TKShareMenuActionSheetContainerOption
} from "../components/TKShareMenuActionSheet";
import { WXAPIFactory } from "@tencent/wechat_open_sdk";
import { QQOpenApiFactory } from "@tencent/qq-open-sdk";
import { image } from "@kit.ImageKit";
import { fileUri } from "@kit.CoreFileKit";

export enum TKSharePlatform {
  wechat,
  wechatTimeLine,
  qq,
  qqZone,
  sina,
  unkown
}

export namespace TKShareConfig {

  export namespace Platform {
    export const sina: string = 'SinaWeibo'

    export const wechat: string = 'WeChat'

    export const qq: string = 'QQ'
  }

  export namespace ShareSDK {
    export const appKey: string = 'shareSDK.appKey'

    export const appSecret: string = 'shareSDK.appSecret'

    export const platform: string = 'shareSDK.platform'
  }

  export namespace Sina {
    export const appKey: string = 'SinaWeibo.AppKey'

    export const appSecret: string = 'SinaWeibo.AppSecret'

    export const redirectUri: string = 'SinaWeibo.RedirectUri'

    export const ability: string = 'SinaWeibo.CallBackAbility'
  }

  export namespace Wechat {
    export const appID: string = 'WeChat.AppId'

    export const appSecret: string = 'WeChat.AppSecret'
  }

  export namespace QQ {
    export const appID: string = 'QQ.AppId'

    export const appKey: string = 'QQ.AppKey'
  }

  export namespace RegularExpresion {
    export const base64: string = '(data:image/)(png|jpeg|jpg|bmp|webp|heif)(;base64,)'
  }

  export namespace NonSupport {
    export const platforms: TKSharePlatform[] = [TKSharePlatform.wechatTimeLine, TKSharePlatform.qqZone, TKSharePlatform.qq]
  }
}

export namespace TKShare {

  export let _shareMap: Map<string, string> | undefined = undefined

  export let _context: common.UIAbilityContext | undefined = undefined

  export let platformDevInfos: TKSharePlatform[] = []
  /**
   * SDK初始化
   * @param context
   */
  export async function builtInitial(context: common.UIAbilityContext) {
    _context = context
    TKPluginInvokeCenter.shareInstance().registerPlugin(TKSharePlugin)
    await mobShare.ShareSDK.getInstance().setUIAbilityContext(context)
    ZztSDK.init(context, getConfig(TKShareConfig.ShareSDK.appKey), getConfig(TKShareConfig.ShareSDK.appSecret))
    const platform: string = getConfig(TKShareConfig.ShareSDK.platform)
    const platforms: string[] = TKStringHelper.split(platform, '|')
    platforms.forEach((platform) => {
      initialPlatform(platform)
    })
  }

  /**
   * 是否同意隐私协议
   * @param state
   */
  export function updatePolicy(state: boolean) {
    ZztSDK.submitPolicyGrantResult(state)
    if (true) {
      const platform: string = getConfig(TKShareConfig.ShareSDK.platform)
      const platforms: string[] = TKStringHelper.split(platform, '|')
      platforms.forEach((platform) => {
        initialPlatform(platform)
      })
    }
  }

  export function handleWant(want: Want, context: common.UIAbilityContext) {
    mobShare.ShareSDK.getInstance().handlerWant(want, context)
  }

  /**
   * 弹出分享弹窗
   * @param param
   * @param shareTypeList
   * @param completion
   * @returns
   */
  export async function showShareMenuActionSheet(param: TKShareParam,
    shareTypeList: string, completion: (result: Record<string, Object>) => void = () => {}): Promise<TKComponentContent<TKShareMenuActionSheetContainerOption> | undefined> {
    const models = getShareTypeList(shareTypeList)
    let tempOption: TKShareMenuActionSheetContainerOption =
      { option: { shareList: models, shareParam: param, completion: completion }, dialog: undefined }
    const dialog =
      TKDialogHelper.createCommonDialog(wrapBuilder<[TKShareMenuActionSheetContainerOption]>(TKShareMenuActionSheetBuilder),
        tempOption)
    tempOption.dialog = dialog
    dialog.open({
      maskColor: 0x33000000,
      alignment: DialogAlignment.Bottom,
      onWillDismiss: (action) => {
        action.dismiss()
      },
      autoCancel: true
    })
    return dialog
  }

  /**
   * 分享到单独平台
   * @param param
   * @param platform
   * @returns
   */
  export function shareContent(param: TKShareParam, platform: string): Promise<Record<string, Object>> {
    return new Promise(async (resolve, reject) => {
      const shareList: TKShareListItemViewModel[] = getShareTypeList(platform)
      const model = shareList.shift()
      if (model == undefined) {
        reject('平台不存在或未安装')
      }
      const result = await share(param, model!)
      resolve(result)
    })
  }


  export async function openMiniProgram(param: TKOpenMiniProgramParam): Promise<Record<string, Object>> {
    return new Promise(async (resolve) => {
      let result: Record<string, Object> = {}
      let plat = await mobShare.ShareSDK.getInstance().getPlatformAsync(mobShare.Platform.Wechat)
      let receive: mobShare.PlatformActionListener = {
        onComplete: (platform: mobShare.IPlatform, action: number, res: Map<string, Object>) => {
          // 成功回调，可通过platform.getDb().exportData()获取结果
          //成功回调
          result['flag'] = '1'
          result['info'] = '打开小程序成功'
          resolve(result)
        },
        onError: (platform: mobShare.IPlatform, action: number, error: Error) => {
          // 失败回调，可通过打印error.message获取错误信息
          //成功回调
          result['flag'] = '-1'
          result['info'] = `打开小程序失败，错误码：${error.name}，错误描述：${error.message}`
          resolve(result)
        },
        onCancel: (platform: mobShare.IPlatform, action: number) => {
          // 取消回调，可通过platform.getName()获取平台信息
          //成功回调
          result['flag'] = '0'
          result['info'] = '用户取消操作'
          resolve(result)
        }
      }
      let records: Array<mobShare.SharedParam> = new Array()
      records.push({
        utd: mobShare.ShareType.OPEN_WXMINIPROGRAM,
        miniProgramUserName: param.username,
        miniProgramPath: param.path,
        miniProgramType: param.type
      })
      plat.setPlatformActionListener(receive)
      plat.share(records, _context)
    })
  }

  /**
   * 调用sharesdk分享
   * @param param
   * @param model
   * @returns
   */
  export async function share(param: TKShareParam, model: TKShareListItemViewModel): Promise<Record<string, Object>> {
    return new Promise(async (resolve) => {
      const platform = model.platform
      let records: Array<mobShare.SharedParam> = new Array()

      if (isMiniProgramParam(param)) {
        const miniProgramParam = param as TKShareMiniProgramParam
        records.push({utd:mobShare.ShareType.SHARE_WXMINIPROGRAM,
          miniProgramUserName: miniProgramParam.userName,
          miniProgramPath: miniProgramParam.path,
          miniProgramType: miniProgramParam.type,
          withShareTicket: miniProgramParam.isShareTicket,
          imageData: await image2Uint8Array(miniProgramParam.image),
          title: miniProgramParam.title,
          description: miniProgramParam.description
        })
      } else if (isLinkParam(param)) {
        const linkParam = param as TKShareLinkParam
        if (TKStringHelper.isEmpty(linkParam.description)) {
          linkParam.description = linkParam.content
        }
        if (platform == TKSharePlatform.sina) {
          const content = linkParam.content + linkParam.url
          records.push({ utd: mobShare.ShareType.TEXT, content: content})
        } else {
          let array = await image2Uint8Array(linkParam.image)
          let tempArray = array.length ? array : undefined  // Uint8Array数组为空，分享会有异常，没有任何效果。要传undefined
          records.push({ utd: mobShare.ShareType.WEBPAGE, title: linkParam.title, url: linkParam.url, description: linkParam.content, imageData: tempArray})
        }
      } else if (isImageParam(param)) {
        TKLog.debug(`===========================imageParam:platform(${platform})======================`)
        if (platform == TKSharePlatform.sina) {
          const fileUrl = await image2FileUrl(param.image)
          records.push({ utd: mobShare.ShareType.IMAGE, imageUris: [fileUrl]})
        } else {
          const record = await image2ShareRecord(param.image)
          records.push(record)
        }
      }

      let result: Record<string, Object> = {'funcNo': '50232'}
      let receive: mobShare.PlatformActionListener = {
        onComplete: (platform: mobShare.IPlatform, action: number, res: Map<string, Object>) => {
          //成功回调
          result['flag'] = '1'
          result['info'] = '分享成功'
          resolve(result)
        },
        onError: (platform: mobShare.IPlatform, action: number, error: Error) => {
          //异常回调
          result['flag'] = '0'
          result['info'] = `分享失败，错误码：${error.name}，错误描述：${error.message}`
          resolve(result)
        },
        onCancel: (platform: mobShare.IPlatform, action: number) => {
          //取消回调
          result['flag'] = '0'
          result['info'] = '用户取消'
          resolve(result)
        }
      }
      const mobPlatform = getPlatform(platform)
      let plat = await mobShare.ShareSDK.getInstance().getPlatformAsync(mobPlatform)
      plat.setPlatformActionListener(receive)
      plat.share(records, _context, {
        previewMode: mobShare.SharePreviewMode.DEFAULT,
        selectionMode: mobShare.SelectionMode.SINGLE
      })
    })
  }

  function isLinkParam(param: TKShareParam): boolean {
    const temp = param as TKShareLinkParam
    if (temp.url == undefined) {
      return false
    }
    return true
  }

  function isImageParam(param: TKShareParam): boolean {
    const temp = param as TKShareImageParam
    if (temp.image == undefined) {
      return false
    }
    return true
  }

  function isMiniProgramParam(param: TKShareParam): boolean {
    const temp = param as TKShareMiniProgramParam
    if (temp.path == undefined) {
      return false
    }
    return true
  }

  function isResource(obj: Object): boolean {
    const resource = obj as Resource
    if (resource.bundleName != undefined && resource.moduleName != undefined && resource.id != undefined) {
      return true
    }
    return false
  }

  function isHttpUrl(url: string): boolean {
    return TKStringHelper.isHttpUrl(url)
  }

  function isFileUrl(url: string): boolean {
    return TKStringHelper.isFileUrl(url)
  }

  async function image2Uint8Array(image: PixelMap | ResourceStr): Promise<Uint8Array> {
    let array: Uint8Array = new Uint8Array()
    if (typeof image === 'string') {
      if (isHttpUrl(image)) {
        array = await http2Uint8Array(image)
      } else if (isFileUrl(image)) {
        array = TKFileHelper.readFile(image)
      } else if (TKFormatHelper.isFormatRegex(TKShareConfig.RegularExpresion.base64, image)) {
        array = base64String2Uint8Array(image)
      } else {
        array = await imageString2Uint8Array(image)
      }
    } else if (typeof image === 'object') {
      if (isResource(image)) {
        array = await resource2Uint8Array(image as Resource)
      } else {
        array = await pixel2Uint8Array(image as PixelMap)
      }
    }

    return array
  }

  async function image2FileUrl(image: PixelMap | ResourceStr): Promise<string> {
    if (typeof image === 'string') {
      if (isFileUrl(image)) {
        return image
      }
    }
    const array = await image2Uint8Array(image)
    const pixelMap = await uint8Array2Pixel(array)
    const pathDir = TKFileHelper.fileDir() + '/images/share'
    const imagename = 'temp.png'
    const file = await TKImageHelper.savePixelMap(pixelMap, pathDir, imagename, 'image/png')
    return fileUri.getUriFromPath(file)
  }

  async function image2ShareRecord(image: PixelMap | ResourceStr): Promise<mobShare.SharedParam> {
    if (typeof image === 'string') {
      if (isHttpUrl(image)) {
        return {utd:mobShare.ShareType.IMAGE, imageUrl: image}
      } else if (isFileUrl(image)) {
        return {utd:mobShare.ShareType.IMAGE, imagePath: image}
      }
    }
    const array = await image2Uint8Array(image)
    TKLog.debug(`===========================imageParam:image2ShareRecord(${array.byteLength})======================`)
    return {utd:mobShare.ShareType.IMAGE, imageData: array}
  }

  async function imageString2Uint8Array(image: string): Promise<Uint8Array> {
    const resource = TKImageHelper.getImageResource(image)
    return resource2Uint8Array(resource)
  }

  function base64String2Uint8Array(image: string): Uint8Array {
    let array = TKBase64Helper.dataWithBase64Decode(image)
    return array
  }

  async function resource2Uint8Array(image: Resource): Promise<Uint8Array> {
    const pixel = await TKImageHelper.getPixelMapFromResource(image)
    return await pixel2Uint8Array(pixel)
  }

  async function pixel2Uint8Array(image: PixelMap): Promise<Uint8Array> {
    const arrayBuffer = await TKImageHelper.packingFromPixelMap(image, { format: 'image/png', quality: 100 })
    return TKDataHelper.arrayBufferToUint8Array(arrayBuffer)
  }

  async function uint8Array2Pixel(buffer: Uint8Array): Promise<image.PixelMap> {
    let imageSource = image.createImageSource(buffer.buffer.slice(0, buffer.buffer.byteLength))
    let createPixelMap: image.PixelMap = await imageSource.createPixelMap({
      desiredPixelFormat: image.PixelMapFormat.RGBA_8888
    })
    await imageSource.release()
    return createPixelMap
  }

  async function http2Uint8Array(image: string): Promise<Uint8Array> {
    return new Promise<Uint8Array>((resolve) => {
      TKDialogHelper.createLoadingDialog().open()
      const requestVO = new TKURLRequestVO()
      requestVO.url = image
      requestVO.isEncodeUrl = true
      requestVO.timeout = 30
      TKURLRequestHelper.getRequest(requestVO, (responseVO) => {
        TKDialogHelper.createLoadingDialog().close()
        resolve(responseVO.result)
      })
    })
  }

  function getPlatform(platform: TKSharePlatform): string | undefined {
    let temp: string | undefined = undefined
    switch (platform) {
      case TKSharePlatform.wechat:
        temp = mobShare.Platform.Wechat
        break;

      case TKSharePlatform.qq:
        temp = mobShare.Platform.QQ
        break;

      case TKSharePlatform.sina:
        temp = mobShare.Platform.SinaWeibo
        break;

      default:
        break;
    }
    return temp
  }

  function getShareTypeList(shareType: string): TKShareListItemViewModel[] {
    const array: string[] = TKStringHelper.split(shareType, ',')
    let models: TKShareListItemViewModel[] = []
    array.forEach((type) => {
      const model: TKShareListItemViewModel = getShareListItemViewModel(type)
      let isValid = clientIsInstalled(model.platform)
      if (isValid) {
        models.push(model)
      }
    })
    return models
  }

  function getShareListItemViewModel(shareType: string): TKShareListItemViewModel {
    //22-微信好友 23-微信朋友圈 24-qq好友 1-新浪微博 6-qq空间
    let platform: TKSharePlatform = TKSharePlatform.unkown
    let title: string = ''
    let image: ResourceStr = ''
    switch (shareType) {
      case '22':
        platform = TKSharePlatform.wechat
        title = '微信好友'
        image = $r('app.media.tk_share_wechat')
        break;

      case '23':
        platform = TKSharePlatform.wechatTimeLine
        title = '微信朋友圈'
        image = $r('app.media.tk_share_wechatTimeLine')
        break;

      case '24':
        platform = TKSharePlatform.qq
        title = 'QQ'
        image = $r('app.media.tk_share_QQ')
        break;

      case '1':
        platform = TKSharePlatform.sina
        title = '新浪微博'
        image = $r('app.media.tk_share_sina')
        break;

      case '6':
        platform = TKSharePlatform.qqZone
        title = 'QQ空间'
        image = $r('app.media.tk_share_QQZone')
        break;

      default:
        break;
    }
    return new TKShareListItemViewModel({ title: title, image: image, platform: platform, type: shareType })
  }

  function getConfig(key: string): string {
    if (_shareMap == undefined) {
      _shareMap = TKFileHelper.readSystemConfig('share/Share.xml', _context)
    }
    return TKMapHelper.getString(_shareMap, key)
  }

  function initialPlatform(platform: string) {
    switch (platform) {
      case TKShareConfig.Platform.sina:
        sina()
        break;

      case TKShareConfig.Platform.wechat:
        wechat()
        break;

      case TKShareConfig.Platform.qq:
        qq()
        break;

      default:
        break;
    }
  }

  function clientIsInstalled(platform: TKSharePlatform) {
    let installed: boolean = false
    switch (platform) {
      case TKSharePlatform.wechat:
      case TKSharePlatform.wechatTimeLine:
        installed = WXAPIFactory.createWXAPI('').isWXAppInstalled()
        break;

      case TKSharePlatform.qq:
      case TKSharePlatform.qqZone:
        installed = QQOpenApiFactory.createApi(0).isQQInstalled()
        break;

      case TKSharePlatform.sina:
        installed = true
        break;
    }
    if (TKShareConfig.NonSupport.platforms.includes(platform)) {
      installed = false
    }
    return installed
  }

  async function sina() {
    if (platformDevInfos.includes(TKSharePlatform.sina)) {
      return
    }
    let map = new HashMap<string, Object>()
    map.set(mobShare.Platform_Info.APP_KEY, getConfig(TKShareConfig.Sina.appKey))
    map.set(mobShare.Platform_Info.APP_SECRET, getConfig(TKShareConfig.Sina.appSecret))
    map.set(mobShare.Platform_Info.CALLBACK_ABILITY_NAME, getConfig(TKShareConfig.Sina.ability))
    let isSuccess = await mobShare.ShareSDK.getInstance().setPlatformDevInfoAsync(mobShare.Platform.SinaWeibo, map)
    if (isSuccess) {
      platformDevInfos.push(TKSharePlatform.sina)
    }
  }

  async function qq() {
    if (platformDevInfos.includes(TKSharePlatform.qq)) {
      return
    }
    let map = new HashMap<string, Object>()
    map.set(mobShare.Platform_Info.APP_ID, getConfig(TKShareConfig.QQ.appID))
    map.set(mobShare.Platform_Info.APP_KEY, getConfig(TKShareConfig.QQ.appKey))
    let isSuccess = await mobShare.ShareSDK.getInstance().setPlatformDevInfoAsync(mobShare.Platform.QQ, map)
    if (isSuccess) {
      platformDevInfos.push(TKSharePlatform.qq)
    }
  }

  export async function wechat() {
    if (platformDevInfos.includes(TKSharePlatform.wechat)) {
      return
    }
    let map = new HashMap<string, Object>()
    map.set(mobShare.Platform_Info.APP_KEY, getConfig(TKShareConfig.Wechat.appID))
    map.set(mobShare.Platform_Info.APP_SECRET, getConfig(TKShareConfig.Wechat.appSecret))
    let isSuccess = await mobShare.ShareSDK.getInstance().setPlatformDevInfoAsync(mobShare.Platform.Wechat, map)
    if (isSuccess) {
      platformDevInfos.push(TKSharePlatform.wechat)
    }
  }

}