package com.xinhua.fundsales.message.handler;

import android.content.Context;
import android.os.Build;

import com.android.thinkive.framework.message.AppMessage;
import com.android.thinkive.framework.message.IMessageHandler;
import com.android.thinkive.framework.message.MessageManager;
import com.android.thinkive.framework.view.MyWebView;
import com.xinhua.fundsales.fingerprintRecognition.FingerManager;

import org.json.JSONObject;

/**
 * Created by HUAWEI on 2022/1/5.
 * @version V1.0
 * @Description: 同步指纹验证
 */

public class Message80321 implements IMessageHandler {

    private Context context;
    private MyWebView mWebView;

    @Override
    public String handlerMessage(Context context, AppMessage appMessage) {
        JSONObject content = appMessage.getContent();
        this.mWebView = appMessage.getWebView();
        this.context = context;
        syncPrint();
        return MessageManager.getInstance(context).buildMessageReturn(1, null, null);
    }


    /**
     * 同步指纹数据
     */
    private void syncPrint() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                FingerManager.updateFingerData(context);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
