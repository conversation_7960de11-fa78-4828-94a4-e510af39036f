package com.xinhua.fundsales.message.handler;

import android.content.Context;
import android.os.Build;

import com.android.thinkive.framework.message.AppMessage;
import com.android.thinkive.framework.message.IMessageHandler;
import com.android.thinkive.framework.message.MessageManager;
import com.android.thinkive.framework.view.MyWebView;
import com.xinhua.fundsales.fingerprintRecognition.FingerManager;
import com.xinhua.fundsales.fingerprintRecognition.callback.SimpleFingerCallback;

import org.json.JSONException;
import org.json.JSONObject;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Created by HUAWEI on 2022/1/5.
 * @version V1.0
 * @Description: 指纹验证
 */

public class Message80319 implements IMessageHandler {

    private Context context;
    private MyWebView mWebView;

    @Override
    public String handlerMessage(Context context, AppMessage appMessage) {
        JSONObject content = appMessage.getContent();
        this.mWebView = appMessage.getWebView();
        this.context = context;
        check();
        return MessageManager.getInstance(context).buildMessageReturn(1, null, null);
    }


    /**
     * 验证指纹
     */
    private void check() {
        try {
            final JSONObject jsonObject = new JSONObject();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                switch (FingerManager.checkSupport(context)) {
                    case DEVICE_UNSUPPORTED:
                        jsonObject.put("errorInfo", "设备不支持指纹");
                        jsonObject.put("errorNo", "-1");
                        send80319Msg(jsonObject);
                        break;
                    case SUPPORT_WITHOUT_KEYGUARD:
                        //设备支持但未处于安全保护中（你的设备必须是使用屏幕锁保护的，这个屏幕锁可以是password，PIN或者图案都行）
                        jsonObject.put("errorInfo", "未录制屏幕保护");
                        jsonObject.put("errorNo", "-2");
                        send80319Msg(jsonObject);
                        break;
                    case SUPPORT_WITHOUT_DATA:
                        jsonObject.put("errorInfo", "未录入指纹");
                        jsonObject.put("errorNo", "-3");
                        send80319Msg(jsonObject);
                        break;
                    case SUPPORT:
                        if (FingerManager.hasFingerprintChang(context)) {
                            try {
                                jsonObject.put("errorInfo", "指纹数据发生变化");
                                jsonObject.put("errorNo", "-5");
                                send80319Msg(jsonObject);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else {
                            FingerManager.build().setApplication(context)
                                    .setTitle("指纹验证")
                                    .setDes("请按下指纹")
                                    .setNegativeText("取消")
                                    //.setFingerDialogApi23(new MyFingerDialog())//如果你需要自定义android P 以下系统弹窗就设置,注意需要继承BaseFingerDialog，不设置会使用默认弹窗
                                    .setFingerCallback(new SimpleFingerCallback() {
                                        @Override
                                        public void onSucceed() {
                                            try {
                                                jsonObject.put("errorInfo", "验证成功");
                                                jsonObject.put("errorNo", "0");
                                                send80319Msg(jsonObject);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }

                                        @Override
                                        public void onFailed() {
                                            try {
                                                jsonObject.put("errorInfo", "指纹无法识别");
                                                jsonObject.put("errorNo", "-4");
                                                send80319Msg(jsonObject);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }

                                        @Override
                                        public void onChange() {
                                            // FingerManager.updateFingerData(context);
                                            // check();
                                            try {
                                                jsonObject.put("errorInfo", "指纹数据发生变化");
                                                jsonObject.put("errorNo", "-5");
                                                send80319Msg(jsonObject);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }

                                        @Override
                                        public void onBack() {
                                            try {
                                                jsonObject.put("errorInfo", "指纹返回");
                                                jsonObject.put("errorNo", "-7");
                                                send80319Msg(jsonObject);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }

                                        @Override
                                        public void onCancel() {
                                           // super.onCancel();
                                            try {
                                                jsonObject.put("errorInfo", "取消指纹登录");
                                                jsonObject.put("errorNo", "-6");
                                                send80319Msg(jsonObject);
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    })
                                    .create()
                                    .startListener((AppCompatActivity) context);
                        }
                        break;
                    default:
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private void send80319Msg(JSONObject param) {
        if (param != null || mWebView != null) {
            if (param != null) {
                try {
                    param.put("funcNo", "80320");
                } catch (JSONException var4) {
                    var4.printStackTrace();
                }
                mWebView.loadUrl("javascript:callMessage(" + param.toString() + ")");
            }
        }
    }
}
